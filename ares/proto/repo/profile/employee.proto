syntax = "proto3";

package repo_profile_employee;
import "service/domains/common.proto";
import "goff.proto";
import "service/domains/patient_profile_common.proto";
import "app/admin/admin_app.proto";

option go_package = "git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee";

message EmployeeProfile {
    option (goff.model) = true;
    option (goff.collection_name) = "employee_profile";
    option (goff.is_tenant) = true;

    goff.UUID id = 1;
    string firstName = 5 [(goff.custom) = 'validate:"trim"'];
    string lastName = 6 [(goff.custom) = 'validate:"trim"'];
    int64 dob = 7 [(goff.custom) = 'bson:"omitempty"', (goff.nullable) = true];
    patient_profile_common.Salutation Salutation = 8 [(goff.custom) = 'bson:"omitempty"', (goff.nullable) = true];
    string phone = 9 [(goff.custom) = 'bson:"omitempty"', (goff.nullable) = true];
    string zipCode = 10 [(goff.custom) = 'bson:"omitempty"'];
    string street = 11 [(goff.custom) = 'bson:"omitempty"'];
    string email = 12 [(goff.custom) = 'bson:"omitempty"', (goff.nullable) = true];
    string fax = 13 [(goff.custom) = 'bson:"omitempty"'];
    string mobilePhone = 14 [(goff.custom) = 'bson:"omitempty"'];
    string address = 15 [(goff.custom) = 'bson:"omitempty"', (goff.nullable) = true];
    string title = 16 [(goff.nullable) = true];
    string lanr = 17 [(goff.nullable) = true];
    string bsnr = 18;
    string havgId = 19 [(goff.nullable) = true];
    string havgVpId = 20 [(goff.nullable) = true];
    string mediverbundId = 21 [(goff.nullable) = true];
    string mediverbundVpId = 22 [(goff.nullable) = true];
    repeated string areaOfExpertise = 23 [(goff.nullable) = true];
    int64 createdDate = 24;
    string okv = 25 [(goff.nullable) = true];
    bool hasHzvContracts = 26;
    bool hasFavContracts = 27;
    patient_profile_common.AdditionalName AdditionalName = 28 [ (goff.nullable) = true ];
    patient_profile_common.IntendWord IntendWord = 29 [ (goff.nullable) = true ];
    string Initial = 30;
    repeated string DmpPrograms = 31 [ (goff.nullable) = true ];
    string JobDescription = 32 [ (goff.nullable) = true ];
    bool MarkAsBillingDoctor = 33;
    goff.UUID BsnrId = 34 [ (goff.nullable) = true ];
    string pseudoLanr = 35 [(goff.nullable) = true];
    repeated string teamNumbers = 36 [(goff.nullable) = true];
    string DoctorStamp = 37;
    string BsnrPracticeStamp = 38;
    repeated common.BankInformation BankInformations = 39;
    bool markAsEmployedDoctor = 40;
    goff.UUID ResponsibleDoctorId = 41 [ (goff.nullable) = true ];
    goff.UUID RepresentativeDoctorId = 42 [ (goff.nullable) = true ];
    string bsnrName = 43 [ (goff.nullable) = true ];
    string bsnrStreet = 44;
    string bsnrNumber = 45;
    string bsnrPostCode = 46;
    string bsnrPhoneNumber = 47;
    string bsnrFaxNumber = 48;
    string ExternalId = 49;
    bool IsParticipationActive = 50;
    string BsnrFacilityType = 51;
    repeated common.UserType Types = 52;
    goff.UUID deviceId = 53;
    string UserName = 54;
    string HpmEndpoint = 56;
    repeated app_admin.Contract hzvContracts = 57;
    repeated app_admin.Contract favContracts = 58;
    repeated goff.UUID BsnrIds = 59 [ (goff.nullable) = true ];
    repeated string bsnrs = 60;
    common.EHKSType EHKSType = 61 [(goff.nullable) = true];
    bool IsDoctor = 62;
}

message Contract {
    string contractId = 1;
    int64 startDate = 2;
    int64 endDate = 3 [(goff.nullable) = true];
}
