syntax = "proto3";

package repo_encounter;

import "goff.proto";
import "repo/mvz/medicine_common.proto";
import "repo/mvz/bmp_common.proto";
import "repo/mvz/himi_common.proto";
import "service/domains/patient_profile_common.proto";
import "service/domains/common.proto";
import "service/domains/lab_common.proto";
import "service/domains/form_common.proto";
import "service/domains/waiting_room_common.proto";
import "repo/mvz/prescribed_heimi_common.proto";
import "repo/admin/doctor_letter_common.proto";
import "service/domains/master_data_common.proto";
import "service/domains/appointment_common.proto";
import "service/domains/document_management_common.proto";
import "repo/mvz/document_type_common.proto";
import "service/domains/catalog_sdav_common.proto";
import "service/domains/arriba_common.proto";

option go_package = "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter";

message DoctorLetterTimeline {
    goff.UUID id = 1 [(goff.custom) = 'bson:"id"', (goff.nullable) = true];
    double sortOrder = 2[(goff.custom) = 'bson:"sortOrder"'];
    string body = 3[(goff.custom) = 'bson:"body"'];
    goff.UUID templateId = 4[(goff.custom) = 'bson:"templateId"'];
    doctor_letter_common.Sender sender = 5 [(goff.custom) = 'bson:"sender"'];
    doctor_letter_common.Receiver receiver = 6 [(goff.custom) = 'bson:"receiver"'];
    repeated doctor_letter_common.Variable Variables = 7;
}

message EncounterNoteTimeline {
    goff.UUID id = 1 [(goff.custom) = 'bson:"id"', (goff.nullable) = true];
    double sortOrder = 2[(goff.custom) = 'bson:"sortOrder"'];
    bool fromUnknownRow = 3 [(goff.custom) = 'bson:"fromUnknownRow"', (goff.nullable) = true];
    string note = 4[(goff.custom) = 'bson:"note"'];
    string command = 5[(goff.custom) = 'bson:"command"'];
    Sources sources = 6 [(goff.custom) = 'bson:"sources"', (goff.nullable) = true];
    EncounterNoteType type = 7 [(goff.custom) = 'bson:"type"', (goff.nullable) = true];
    common.AuditLog auditLog = 8 [(goff.nullable) = true];
    repeated goff.UUID schienIds = 9 [(goff.custom) = 'bson:"schienIds"', (goff.nullable) = true];
}

message EncounterCalendarTimeline {
    waiting_room_common.WaitingRoomPatient waitingRoomPatient = 4 [(goff.custom) = 'bson:"waitingRoomPatient"', (goff.nullable) = true];
    string waitingRoomName = 5 [(goff.custom) = 'bson:"waitingRoomName"', (goff.nullable) = true];
    int64 acceptableWaitingTimeInMinutes = 6 [(goff.custom) = 'bson:"acceptableWaitingTimeInMinutes"', (goff.nullable) = true];
    bool activeTimeMeasurement = 7 [(goff.custom) = 'bson:"activeTimeMeasurement"', (goff.nullable) = true];
}

enum EncounterServiceTimelinePsychotherapyStatus {
    IsApproval = 0;
    IsCompleted = 1;
    HasBeenRemoveApproval = 2;
    NotAcceptedByKV = 3;
}

enum PreParticipateType {
    UHU35 = 0;
    KJP4a = 1;
}

message EncounterServiceTimeline {
    string code = 1;
    string description = 2;
    ReferralDoctorInfo referralDoctorInfo = 3 [(goff.custom) = 'bson:"referralDoctorInfo"', (goff.nullable) = true];
    MaterialCosts materialCosts = 4 [(goff.custom) = 'bson:"materialCosts"', (goff.nullable) = true];
    CareFacility careFacility = 5 [(goff.custom) = 'bson:"careFacility"', (goff.nullable) = true];
    string freeText = 6 [(goff.custom) = 'bson:"freeText"'];
    repeated repo_encounter.EncounterItemError errors = 7 [(goff.nullable) = true];
    string command = 8;
    goff.UUID patientId = 9 [(goff.custom) = 'bson:"patientId"', (goff.nullable) = true];
    repo_encounter.Sources sources = 10 [(goff.nullable) = true];
    bool isPreParticipate = 11 [(goff.custom) = 'bson:"isPreParticipate"'];
    common.AuditLog auditLog = 13 [(goff.custom) = 'bson:"auditLog"', (goff.nullable) = true];
    repeated common.ScheinWithMainGroup scheins = 14 [(goff.custom) = 'bson:"scheins"', (goff.nullable) = true];
    common.MainGroup serviceMainGroup = 15 [(goff.custom) = 'bson:"serviceMainGroup"', (goff.nullable) = true];
    repeated repo_encounter.AdditionalInfoParent additionalInfos = 16 [(goff.custom) = 'bson:"additionalInfos"', (goff.nullable) = true];
    string additionalInfosRaw = 17 [(goff.custom) = 'bson:"additionalInfosRaw"', (goff.nullable) = true];
    string ChargeSystemId = 18 [(goff.nullable) = true];
    // isApprovedPyschotherapy
    EncounterServiceTimelinePsychotherapyStatus ApprovalStatus = 19 [(goff.custom) = 'bson:"approvalStatus"', (goff.nullable) = true];
    goff.UUID TimelineServiceSuggestionId = 20 [(goff.nullable) = true];
    // eab service code
    goff.UUID eabId = 21 [(goff.nullable) = true];
    PreParticipateType PreParticipateType = 22 [(goff.custom) = 'bson:"preParticipateType"', (goff.nullable) = true];
    bool IsSubmitPreParticipateSucsess = 23 [(goff.nullable) = true];
    goff.UUID participateId = 24 [(goff.nullable) = true];
    goff.UUID serviceId = 25 [(goff.nullable) = true];
    double price = 26 [(goff.nullable) = true];
}
message Diagnose {
    string code = 1;
    string description = 2;
    repo_encounter.Certainty certainty = 3;
    goff.UUID timelineId = 4 [(goff.custom) = 'bson:"timelineId"', (goff.nullable) = true]; // null is new, not null can be edit or delete
}

message DiagnoseSuggestion {
    repo_encounter.SuggestionType suggestionType = 1[(goff.custom) = 'bson:"suggestionType"'];
    string proposal = 2;
    repeated Diagnose diagnoses= 3;
    string ruleId = 4[(goff.custom) = 'bson:"ruleId"'];
    bool applied = 5;
    string hint = 6;
    masterdata_common.CheckTime checkTime = 7;
}

message EncounterDiagnoseTimeline {
    string code = 1;
    string description = 2;
    repo_encounter.DiagnoseType type = 3;
    int64 startDate = 4  [(goff.nullable) = true];
    int64 validUntil = 5 [(goff.custom) = 'bson:"validUntil"', (goff.nullable) = true];
    repo_encounter.Certainty certainty = 6 [(goff.nullable) = true];
    repo_encounter.Laterality laterality = 7 [(goff.nullable) = true];
    bool hib = 8;
    bool mrsa = 9;
    bool imported = 10;
    string freeText = 11 [(goff.custom) = 'bson:"freeText"'];
    repeated repo_encounter.EncounterItemError errors = 12 [(goff.nullable) = true];
    string command = 13;
    bool group = 14;
    repo_encounter.Sources sources = 15 [(goff.nullable) = true];
    MorbiRsa morbiRsa = 16 [(goff.custom) = 'bson:"morbiRsa"', (goff.nullable) = true];
    common.AuditLog auditLog = 18 [(goff.custom) = 'bson:"auditLog"', (goff.nullable) = true];
    repeated common.ScheinWithMainGroup scheins = 19 [(goff.custom) = 'bson:"schiens"', (goff.nullable) = true];
    repeated DiagnoseSuggestion diagnoseSuggestions = 20 [(goff.custom) = 'bson:"diagnoseSuggestions"', (goff.nullable) = true];
    RunSdkrwEnum runSdkrw = 21 [(goff.custom) = 'bson:"runSdkrw"'];
    string exception = 22;
    string explanation = 23;
    repeated string sdvaRefs = 24 [(goff.custom) = 'bson:"sdvaRefs"'];
    bool MarkedTreatmentRelevant = 25;
}

enum Laterality {
    U = 0;
    L = 1;
    R = 2;
    B = 3;
}
enum RunSdkrwEnum {
    RUNSDKRWENUM_DEFAULT = 0 [(goff.value) = "DEFAULT"];
    RUNSDKRWENUM_RUNNING = 1 [(goff.value) = "RUNNING"];
    RUNSDKRWENUM_CANCELLED = 2 [(goff.value) = "CANCELLED"];
    RUNSDKRWENUM_DONE = 3 [(goff.value) = "DONE"];
}

enum Certainty {
    G = 0;
    V = 1;
    Z = 2;
    A = 3;
}

enum EncounterNoteType {
    ANAMNESE = 0;
    FINDING = 1;
    THERAPY = 2;
    NOTE = 3;
}

enum SuggestionType {
    SUGGESTIONTYPE_ADD = 0;
    SUGGESTIONTYPE_DELETE = 1;
    SUGGESTIONTYPE_REPLACE = 2;
}

enum DiagnoseType {
    DIAGNOSETYPE_PERMANENT = 0 [(goff.value) = "PERMANENT"];
    DIAGNOSETYPE_ACUTE = 1 [(goff.value) = "ACUTE"];
    DIAGNOSETYPE_ANAMNESTIC = 2 [(goff.value) = "ANAMNESTIC"];
}

enum DiagnoseCommand {
    PERMANENT_DIAGNOSE_COMMAND_DD = 0 [(goff.value) = "DD"];
    PERMANENT_DIAGNOSE_COMMAND_DA = 1 [(goff.value) = "DA"];
    ACUTE_DIAGNOSE_COMMAND = 2 [(goff.value) = "D"];
    ANAMNESTIC_DIAGNOSE_COMMAND = 3 [(goff.value) = "AD"];
}

enum ValidationLevel {
    ValidationLevelEntry = 0;
    ValidationLevelEncounter = 1;
    ValidationLevelQuarter = 2;
    ValidationLevelTimeline = 3;
}

message EncounterItemError {
    EncounterItemErrorType type = 1;
    string message = 2;
    string akaFunction = 3;
    string ErrorCode = 4; 
	map<string,string> MetaData = 5; 
	ValidationLevel ValidationLevel = 6;
    repeated string ErrorParams = 7;
}

message QuarterEncounterItemError {
    EncounterItemErrorType type = 1;
    string message = 2;
    string code = 3;
}

enum EncounterItemErrorType {
    EncounterItemErrorType_error = 0 [(goff.value) = "error"];
    EncounterItemErrorType_warning = 1 [(goff.value) = "warning"];
    EncounterItemErrorType_info = 2 [(goff.value) = "info"];
}

enum Sources {
    Imported = 0;
    Composer = 1;
    Timeline = 2;
    EAB = 3;
}

enum EncounterCase {
    AB = 0;
    PB = 1;
    NOT = 2;
    PRE_ENROLLMENT = 3;
}

enum TreatmentCase {
    TreatmentCaseCustodian = 0;
    TreatmentCaseDelegate = 1;
    TreatmentCaseDeputy = 2;
    TreatmentCasePreParticipate = 3;
}

message MorbiRsa {
    bool chronic = 1;
    bool krhNumber = 2;
    bool krhLabel = 3;
}

message PrescriptionDates {
    int64 createdDate = 1 ;
    int64 changedDate = 2 [(goff.nullable) = true];
    int64 printedDate = 3 [(goff.nullable) = true];
    int64 deletedDate = 4 [(goff.nullable) = true];
}

message Drug {
    string drugId = 1;
    bool autIdem = 2;
    int32 amount = 3;

}

message ReceiptKvDetails {
    bool documentRequired = 1;
    bool himi = 2;
    bool vaccine = 3;
}

message Delegate {
    string havgNumber = 1;
    string mediId = 2;
    string lanr = 3;
}

message AdditionalInfoParent {
    string FK = 1 [(goff.custom) = 'bson:"fK"'];
    string Value = 2 [(goff.custom) = 'bson:"value"'];
    repeated AdditionalInfoChild Children = 3 [(goff.custom) = 'bson:"children"'];
}

message AdditionalInfoChild {
    string fK = 1 [(goff.custom) = 'bson:"fK"'];
    string Value = 2 [(goff.custom) = 'bson:"value"'];
}

enum EnrollmentPrintFormStatus {
    EnrollmentPrintFormStatus_Generated = 0 [(goff.value) = "Generated"];
    EnrollmentPrintFormStatus_Printed_Created = 1 [(goff.value) = "PrintedCreated"];
    EnrollmentPrintFormStatus_Printed_Succesfully = 2 [(goff.value) = "PrintedSuccesfully"];
    EnrollmentPrintFormStatus_Printed_Failed = 3 [(goff.value) = "PrintedFailed"];
    EnrollmentPrintFormStatus_Incorrect = 4 [(goff.value) = "Incorrect"];
    EnrollmentPrintFormStatus_Handed_Over = 5 [(goff.value) = "HandedOver"];
    EnrollmentPrintFormStatus_Received = 6 [(goff.value) = "Received"];
}

message EncounterService {
    string code = 1[(goff.custom) = 'bson:"code"'];
    string description = 2[(goff.custom) = 'bson:"description"'];
    ReferralDoctorInfo referralDoctorInfo = 3 [(goff.custom) = 'bson:"referralDoctorInfo"',(goff.nullable) = true];
    MaterialCosts materialCosts = 4 [(goff.custom) = 'bson:"materialCosts"',(goff.nullable) = true];
    CareFacility careFacility = 5 [(goff.custom) = 'bson:"careFacility"',(goff.nullable) = true];
    string freeText = 6[(goff.custom) = 'bson:"freeText"'];
    repeated repo_encounter.EncounterItemError errors = 7 [(goff.custom) = 'bson:"errors"',(goff.nullable) = true];
    string command = 8[(goff.custom) = 'bson:"command"'];
    goff.UUID patientId = 9 [(goff.custom) = 'bson:"patientId"',(goff.nullable) = true];
    repo_encounter.Sources sources = 10 [(goff.custom) = 'bson:"sources"',(goff.nullable) = true];
    bool isPreParticipate = 11[(goff.custom) = 'bson:"isPreParticipate"'];
    repeated common.ScheinWithMainGroup scheins = 12 [(goff.custom) = 'bson:"schiens"', (goff.nullable) = true];
    common.MainGroup serviceMainGroup = 13 [(goff.custom) = 'bson:"serviceMainGroup"', (goff.nullable) = true];
    repeated AdditionalInfoParent AdditionalInfos = 14 [(goff.custom) = 'bson:"additionalInfos"', (goff.nullable) = true];
    string AdditionalInfosRaw = 25 [(goff.custom) = 'bson:"additionalInfosRaw"', (goff.nullable) = true];
}

message ReferralDoctorInfo {
    string bsnr = 1 [(goff.custom) = 'bson:"bsnr"', (goff.nullable) = true];
    string lanr = 2 [(goff.custom) = 'bson:"lanr"', (goff.nullable) = true];
    bool requiredLanr = 3 [(goff.custom) = 'bson:"requiredLanr"', (goff.nullable) = true];
    bool requiredBsnr = 4 [(goff.custom) = 'bson:"requiredBsnr"', (goff.nullable) = true];
}

message MaterialCosts {
    bool required = 1 [(goff.custom) = 'bson:"required"', (goff.nullable) = true];
    repeated MaterialCostsItemList materialCostsItemList = 2 [(goff.custom) = 'bson:"materialCostsItemList"', (goff.nullable) = true];
}

message MaterialCostsItemList {
    goff.UUID id = 1 [(goff.custom) = 'bson:"id"', (goff.nullable) = true];
    string amount = 2 [(goff.custom) = 'bson:"amount"'];
    string description = 3[(goff.custom) = 'bson:"description"'];
    bool required = 4 [(goff.custom) = 'bson:"required"', (goff.nullable) = true];
}

message CareFacility {
    string name = 1[(goff.custom) = 'bson:"name"'];
    string ort = 2[(goff.custom) = 'bson:"ort"'];
    bool required = 3 [(goff.custom) = 'bson:"required"', (goff.nullable) = true];
}

message EncounterDiagnose {
    string code = 1[(goff.custom) = 'bson:"code"'];
    string description = 2[(goff.custom) = 'bson:"description"'];
    repo_encounter.DiagnoseType type = 3[(goff.custom) = 'bson:"type"'];
    int64 validUntil = 4 [(goff.custom) = 'bson:"validUntil"', (goff.nullable) = true];
    repo_encounter.Certainty certainty = 5[(goff.custom) = 'bson:"certainty"', (goff.nullable) = true];
    repo_encounter.Laterality laterality = 6 [(goff.custom) = 'bson:"laterality"', (goff.nullable) = true];
    bool hib = 7[(goff.custom) = 'bson:"hib"'];
    bool mrsa = 8[(goff.custom) = 'bson:"mrsa"'];
    bool imported = 9[(goff.custom) = 'bson:"imported"'];
    string freeText = 10[(goff.custom) = 'bson:"freeText"'];
    repeated repo_encounter.EncounterItemError errors = 11 [(goff.custom) = 'bson:"errors"', (goff.nullable) = true];
    string command = 12[(goff.custom) = 'bson:"command"'];
    bool group = 13[(goff.custom) = 'bson:"group"'];
    repo_encounter.Sources sources = 14 [(goff.custom) = 'bson:"sources"', (goff.nullable) = true];
    MorbiRsa morbiRsa = 15 [(goff.custom) = 'bson:"morbiRsa"', (goff.nullable) = true];
    repeated common.ScheinWithMainGroup scheins = 16 [(goff.custom) = 'bson:"schiens"', (goff.nullable) = true];
    string exception = 17;
    string explanation = 18;
    repeated string sdvaRefs = 19;
}

message EncounterNote {
    goff.UUID id = 1 [(goff.custom) = 'bson:"id"', (goff.nullable) = true];
    double sortOrder = 2[(goff.custom) = 'bson:"sortOrder"'];
    bool fromUnknownRow = 3 [(goff.custom) = 'bson:"fromUnknownRow"', (goff.nullable) = true];
    string note = 4[(goff.custom) = 'bson:"note"'];
    string command = 5[(goff.custom) = 'bson:"command"'];
    Sources sources = 6 [(goff.custom) = 'bson:"sources"', (goff.nullable) = true];
    EncounterNoteType type = 7 [(goff.custom) = 'bson:"type"', (goff.nullable) = true];
    repeated common.ScheinWithMainGroup scheins = 8 [(goff.custom) = 'bson:"schiens"', (goff.nullable) = true];
}

message EncounterMedicinePrescription {
    goff.UUID Id = 1;
    goff.UUID MedicinePrescriptionId = 2;
    repeated repo_medicine_common.FormInfo FormInfos = 3;
    common.BillingInfo BillingInfo = 4;
    common.AuditLog AuditLog = 5 [(goff.nullable) = true];
    double sortOrder = 6[(goff.custom) = 'bson:"sortOrder"'];
    goff.UUID PatientId = 7;
    goff.UUID EncounterId = 8;
}

message EncounterHimiPrescription {
    goff.UUID Id = 1;
    goff.UUID HimiPrescriptionId = 2;
    himi_model_common.FormInfo FormInfo = 3;
    himi_model_common.AdditionalForm AdditionalForm = 4;
    common.BillingInfo BillingInfo = 5;
    common.AuditLog AuditLog = 6 [(goff.nullable) = true];
    goff.UUID PatientId = 7;
    goff.UUID EncounterId = 8;
    int64 PrintDate = 9 [(goff.nullable) = true];
    int64 PrescribeDate = 10 [(goff.nullable) = true];
    int64 CreatedDate = 11;
    string DiagnoseCode = 12;
    string SecondaryDiagnore = 13 [(goff.nullable) = true];
}

message HeimiForm {
    goff.UUID HeimiPrescriptionId = 1;
    repo_prescribed_heimi_common.Prescription Prescription = 2;
}

message EncounterHeimiPrescription {
    goff.UUID Id = 1;
    HeimiForm HeimiForm = 2;
    goff.UUID PatientId = 3;
    goff.UUID EncounterId = 4;
    common.BillingInfo BillingInfo = 5;
    double sortOrder = 6[(goff.custom) = 'bson:"sortOrder"'];
    common.AuditLog AuditLog = 7 [(goff.nullable) = true];
}

message EncounterForm {
    goff.UUID Id = 1;
    form_common.Prescribe Prescribe = 2;
    goff.UUID PatientId = 3;
    goff.UUID EncounterId = 4;
    common.BillingInfo BillingInfo = 5;
    double sortOrder = 6[(goff.custom) = 'bson:"sortOrder"'];
    common.AuditLog AuditLog = 7 [(goff.nullable) = true];
    bool IsEnrollmentForm = 8;
    goff.UUID EnrollmentId = 9 [(goff.nullable) = true];
    string EnrollmentFormType = 10 [(goff.nullable) = true];
    EnrollmentPrintFormStatus EnrollmentPrintFormStatus = 11 [(goff.nullable) = true];
    doctor_letter_common.BgInvoice BgInvoice = 22 [(goff.nullable) = true];
}

message EncounterLab {
    goff.UUID Id = 1;
    lab_common.LabForm LabForm = 2;
    goff.UUID PatientId = 3;
    goff.UUID EncounterId = 4;
    common.BillingInfo BillingInfo = 5;
    double sortOrder = 6[(goff.custom) = 'bson:"sortOrder"'];
    common.AuditLog AuditLog = 7 [(goff.nullable) = true];
    lab_common.LabResultStatus LabResultStatus = 8 [(goff.nullable) = true]; 
}

message EncounterPatientMedicalData {
    goff.UUID Id = 1;
    double sortOrder = 2[(goff.custom) = 'bson:"sortOrder"'];
    patient_profile_common.PatientMedicalData old = 3;
    patient_profile_common.PatientMedicalData new = 4 [(goff.nullable) = true];
    goff.UUID PatientId = 5;
    goff.UUID EncounterId = 6;
    common.AuditLog AuditLog = 7 [(goff.nullable) = true];
}

message EncounterMedicinePlanHistory {
    goff.UUID Id = 1;
    goff.UUID MedicationPlanId = 2;
    string ActionType = 3;
    repo_bmp_common.MedicationInformation MedicineInfo = 4;
    repo_bmp_common.MedicationInformation BeforeMedicineInfo = 5 [(goff.nullable) = true];
    common.AuditLog AuditLog = 6 [(goff.nullable) = true];
}

message EncounterEHIC{
    string FormSetting = 1 [(goff.nullable)=true];
	string Language = 2 [(goff.nullable)=true];
	common.EuropeanHealthInsuranceStatus Status = 3 [(goff.nullable)=true];
}

enum EncounterPsychotherapyStatus {
    INPROGRESS = 0 [(goff.value) = "INPROGRESS"];
    HAS_BEEN_TAKE_OVER = 1 [(goff.value) = "HAS_BEEN_TAKE_OVER"];
    COMPLETED = 2 [(goff.value) = "COMPLETED"];
    BILLED = 3 [(goff.value) = "BILLED"];
    READY_TO_BILL = 4 [(goff.value) = "READY_TO_BILL"];
}

message ServiceCodeApproval {
    repeated goff.UUID EntryIds = 1;
    int32 AmountBilled = 2;
    int32 AmountApproval = 3 [(goff.nullable) = true];
    goff.UUID TerminalId = 4 [(goff.nullable) = true];
}
message EncounterPsychotherapy {
    int64 RequestDate = 1 [(goff.nullable) = true, (goff.custom) = 'bson:"requestDate"'];
    int64 ApprovalDate = 2 [(goff.custom) = 'bson:"approvalDate"'];
    int32 AmountApproval = 3 [(goff.custom) = 'bson:"amountApproval"'];
    int32 AmountBilled = 4 [(goff.custom) = 'bson:"amountBilled"'];
    repeated string ServiceCodes = 5 [(goff.custom) = 'bson:"serviceCodes"'];
    goff.UUID ScheinId = 6 [(goff.custom) = 'bson:"scheinId"'];
    EncounterPsychotherapyStatus Status = 7;
    goff.UUID TerminateServiceId = 9 [(goff.nullable) = true, (goff.custom) = 'bson:"terminateServiceId"'];
    goff.UUID TakeOverId = 10 [(goff.nullable) = true, (goff.custom) = 'bson:"takeOverId"'];
    map<string, ServiceCodeApproval> Entries = 11 [(goff.custom) = 'bson:"entries"'];
    repeated string ReferenceServiceCodes = 12 [(goff.custom) = 'bson:"referenceServiceCodes"'];
    int32 ReferenceAmountApproval = 13 [(goff.custom) = 'bson:"referenceAmountApproval"'];
}

message EncounterGoaService {
    string code = 1 [ (goff.custom) = 'validate:"required"' ];
    string description = 2 [(goff.custom) = 'validate:"required"'];
    string freeText = 3 ;
    double factor = 4  [(goff.custom) = 'validate:"required"'];
    double quantity = 5  [(goff.custom) = 'validate:"required"'];
    MaterialCosts materialCosts = 7 [(goff.nullable) = true]; // preview this field is use or not
    repeated repo_encounter.EncounterItemError errors = 8 [(goff.nullable) = true];
    double price = 9 [(goff.nullable) = true];
    repeated repo_encounter.AdditionalInfoParent additionalInfos = 6 [(goff.nullable) = true];
    string additionalInfosRaw = 10 [(goff.custom) = 'bson:"additionalInfosRaw"', (goff.nullable) = true];
    string command = 11;
    repeated common.ScheinWithMainGroup scheins = 12 [(goff.custom) = 'bson:"schiens"', (goff.nullable) = true];
    bool isChangeDefault = 13;
    goff.UUID serviceId = 14 [(goff.nullable) = true];
    double originalPrice = 15 [(goff.nullable) = true];
}

message EncounterUvGoaService {
    string code = 1 [ (goff.custom) = 'validate:"required"' ];
    string description = 2 [(goff.custom) = 'validate:"required"'];
    string freeText = 3 ;
    MaterialCosts materialCosts = 7 [(goff.nullable) = true]; 
    repeated repo_encounter.EncounterItemError errors = 8 [(goff.nullable) = true];
    double price = 9 [(goff.nullable) = true];
    repeated repo_encounter.AdditionalInfoParent additionalInfos = 6 [(goff.nullable) = true];
    string additionalInfosRaw = 10 [(goff.custom) = 'bson:"additionalInfosRaw"', (goff.nullable) = true];
    string command = 11;
    repeated common.ScheinWithMainGroup scheins = 12 [(goff.custom) = 'bson:"schiens"', (goff.nullable) = true];
    bool isGeneral = 13;
    goff.UUID serviceId = 14 [(goff.nullable) = true];
}

message EncounterAppointmentTimeline {
    string appointmentId = 1 [(goff.custom) = 'bson:"appointmentId" validate:"required"'];
    TimelineAppointmentContent appointmentContent = 2 [(goff.custom) = 'bson:"appointmentContent"'];
    common.AuditLog auditLog = 3 [(goff.custom) = 'bson:"auditLog"', (goff.nullable) = true];
    appointment_common.UpdatedAppointmentDateTime updatedAppointmentDateTime = 4 [(goff.custom) = 'bson:"updatedAppointmentDateTime"', (goff.nullable) = true];
    appointment_common.UpdatedTreatingPerson updatedTreatingDoctor = 5 [(goff.custom) = 'bson:"updatedTreatingDoctor"', (goff.nullable) = true];
    appointment_common.UpdatedTreatingPerson updatedTreatingMFA = 6 [(goff.custom) = 'bson:"updatedTreatingMFA"', (goff.nullable) = true];
    appointment_common.UpdatedStringValue updatedTodoTypeName = 7 [(goff.custom) = 'bson:"updatedTodoTypeName"', (goff.nullable) = true];
    appointment_common.UpdatedStringValue updatedMedicalDeviceName = 8 [(goff.custom) = 'bson:"updatedMedicalDeviceName"', (goff.nullable) = true];
    AppointmentAction appointmentAction = 9 [(goff.custom) = 'bson:"appointmentAction"'];
}

enum AppointmentAction {
	CREATE = 0;
	RESCHEDULE = 1;
	CANCEL = 2;
	REMOVE = 3;
}

message TimelineAppointmentContent {
    appointment_common.TreatingPerson treatingPerson = 1 [(goff.custom) = 'bson:"treatingPerson"'];
    string note = 2 [(goff.custom) = 'bson:"note"'];
    int64 startTime = 3 [(goff.custom) = 'bson:"startTime"'];
    int64 endTime = 4 [(goff.custom) = 'bson:"endTime"'];
    string todoTypeName = 5 [(goff.custom) = 'bson:"todoTypeName"'];
    string medicalDeviceName = 6 [(goff.custom) = 'bson:"medicalDeviceName"'];
}

message EncounterDocumentManagement {
    goff.UUID Id = 1 [(goff.custom) = 'bson:"id", validate:"required"'];
    int64 CompanionFileId = 2 [(goff.custom) = 'bson:"companionfileid"'];
    string CompanionFilePath = 3 [(goff.custom) = 'bson:"companionfilepath"'];
    document_management_common.Patient Patient = 4 [ (goff.custom) = 'validate:"required"' ];
    catalog_sdav_common.SdavCatalog Sender = 5 [(goff.nullable) = true];
    string DocumentName = 6 [(goff.custom) = 'bson:"documentname"'];
    document_type_common.DocumentType DocumentType = 7 [(goff.custom) = 'bson:"documenttype"', (goff.nullable) = true];
    string Description = 8 [(goff.custom) = 'bson:"description"', (goff.nullable) = true];
    document_management_common.DocumentManagementStatus Status = 9 [(goff.custom) = 'bson:"status"'];
    int64 ImportedDate = 11 [(goff.custom) = 'bson:"importeddate"'];
    document_management_common.ReadBy ReadBy = 12 [(goff.custom) = 'bson:"readBy"', (goff.nullable) = true];
}

message EncounterArriba {
    goff.UUID SessionId = 1 [(goff.custom) = 'bson:"sessionId"'];
    repeated string CompanionFilePaths = 2 [(goff.custom) = 'bson:"companionfilepaths"'];
    arriba_common.ArribaStatus Status = 3 [(goff.custom) = 'bson:"status"'];
    goff.UUID ArribaId = 4 [(goff.custom) = 'bson:"arribaId"'];
}

// TODO: Need to implement more field adapt with settings and base on requirement for dynamic fields
message EncounterCustomize {
    goff.UUID DocumentTypeId = 1 [(goff.custom) = 'bson:"documentTypeId"'];
    string Command = 2;
    string Description = 3;
    double sortOrder = 4[(goff.custom) = 'bson:"sortOrder"'];
}

message EncounterGDT {
    string filePath = 1;
    string fileName = 2;
    string bucketName = 3;
    string note = 4;
    goff.UUID gdtImportSettingId = 5;
    string gdtImportSettingName = 6;
    goff.UUID documentManagementId = 7;
    string command = 8;
    repeated ArchiveFile archiveFiles = 9;
    string labOrderId = 10;
}

enum EncounterLDTType {
    LDT_RESULT = 0 [(goff.value) = "LDT_RESULT"];
    LDT_ORDER = 1 [(goff.value) = "LDT_ORDER"];
}

message EncounterLDT {
    string filePath = 1;
    string fileName = 2;
    string bucketName = 3;
    string note = 4;
    goff.UUID ldtImportSettingId = 5;
    string ldtImportSettingName = 6;
    goff.UUID documentManagementId = 7;
    string command = 8;
    EncounterLDTType type = 9;
    string labOrderId = 10;
}

message ArchiveFile {
    string fileName = 1;
    string objectName = 2;
}


