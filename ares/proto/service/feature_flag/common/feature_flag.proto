syntax = "proto3";

package feature_flag;

import "goff.proto";

option go_package = "git.tutum.dev/medi/tutum/ares/service/feature_flag/common";
option (goff.pointer_strict_mode) = true;

// Default feature flag keys is false
enum FeatureFlagKey {
  option (goff.create_array) = true;
  FeatureFlagKey_SV = 0 [ (goff.value) = "FF_MVZ_SV" ];
  FeatureFlagKey_SystemDate = 1 [ (goff.value) = "FF_ADMIN_SYSTEMDATE" ];
  FeatureFlagKey_1ClickBilling = 2  [ (goff.value) = "FF_MVZ_1CLICKBILLING" ];
  FeatureFlagKey_HZV_PTV_TESTMODE = 3 [ (goff.value) = "FF_MVZ_HZV_PTV_TESTMODE" ];
  FeatureFlagKey_HPM_TESTMODE = 4 [ (goff.value) = "FF_HPM_TESTMODE" ];
  FeatureFlagKey_REPORT = 5 [ (goff.value) = "FF_REPORT" ];
  FeatureFlagKey_EHKS = 6 [ (goff.value) = "FF_EHKS" ];
  FeatureFlagKey_PVS_BILLING = 7 [ (goff.value) = "FF_PVS_BILLING" ];
  FeatureFlagKey_PREVENTIVE_BILLING = 8 [ (goff.value) = "FF_PREVENTIVE_BILLING" ];
}
