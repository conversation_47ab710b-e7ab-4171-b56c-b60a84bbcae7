syntax = "proto3";
package patient_profile_common;

import "goff.proto";
import "service/domains/catalog_utils_common.proto";
import "service/domains/common.proto";
import "service/domains/catalog_sdkt_common.proto";
import "service/domains/insurance_common.proto";
import "service/domains/error_code.proto";

option go_package = "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common";
option (goff.pointer_strict_mode) = true;

enum TypeOfInsurance {
  Public = 0;
  Private = 1;
  BG = 2;
}

enum Gender {
  M = 0;
  W = 1;
  X = 2;
  D = 3;
  U = 4;
}

enum Salutation {
  Salutation_Herr = 0 [ (goff.value) = "Herr" ];
  Salutation_Frau = 1 [ (goff.value) = "Frau" ];
  Salutation_Keine = 2 [ (goff.value) = "Keine" ];
}

enum CreatePatientProfileErrorCode {
  DuplicatedInsuranceNumber = 0;
  MultipleActiveCostUnits = 1;
  EndDateOutOfDate = 2;
  LHMUnique = 3;
  CostUnitIsNotAvailableInKvRegion = 4;
  CostUnitHasExpired = 5;
  DuplicateLHMItem = 6;
  IKNumberHasExpired = 7;
  CanNotChangeInsuranceType = 8;
  NotFoundInsurance = 9;
  InvalidInsuranceDate = 10;
  NotFoundActiveInsurance = 11;
}

enum SpecialGroupDescription {
  SpecialGroup_00 = 0 [ (goff.value) = "00" ];
  SpecialGroup_04 = 1 [ (goff.value) = "04" ];
  SpecialGroup_06 = 2 [ (goff.value) = "06" ];
  SpecialGroup_07 = 3 [ (goff.value) = "07" ];
  SpecialGroup_08 = 4 [ (goff.value) = "08" ];
  SpecialGroup_09 = 5 [ (goff.value) = "09" ];
}

enum AdditionalName {
  AdditionalName_Bar = 0 [ (goff.value) = "Bar" ];
  AdditionalName_Baron = 1 [ (goff.value) = "Baron" ];
  AdditionalName_Baroness = 2 [ (goff.value) = "Baroness" ];
  AdditionalName_Baronesse = 3 [ (goff.value) = "Baronesse" ];
  AdditionalName_Baronin = 4 [ (goff.value) = "Baronin" ];
  AdditionalName_Brand = 5 [ (goff.value) = "Brand" ];
  AdditionalName_Burggraf = 6 [ (goff.value) = "Burggraf" ];
  AdditionalName_Burggrafin = 7 [ (goff.value) = "Burggräfin" ];
  AdditionalName_Condesa = 8 [ (goff.value) = "Condesa" ];
  AdditionalName_Earl = 9 [ (goff.value) = "Earl" ];
  AdditionalName_Edle = 10 [ (goff.value) = "Edle" ];
  AdditionalName_Edler = 11 [ (goff.value) = "Edler" ];
  AdditionalName_Erbgraf = 12 [ (goff.value) = "Erbgraf" ];
  AdditionalName_Erbgrafin = 13 [ (goff.value) = "Erbgräfin" ];
  AdditionalName_Erbprinz = 14 [ (goff.value) = "Erbprinz" ];
  AdditionalName_Erbprinzessin = 15 [ (goff.value) = "Erbprinzessin" ];
  AdditionalName_Ffr = 16 [ (goff.value) = "Ffr" ];
  AdditionalName_Freifr = 17 [ (goff.value) = "Freifr" ];
  AdditionalName_Freifraulein = 18 [ (goff.value) = "Freifräulein" ];
  AdditionalName_Freifrau = 19 [ (goff.value) = "Freifrau" ];
  AdditionalName_Freih = 20 [ (goff.value) = "Freih" ];
  AdditionalName_Freiherr = 21 [ (goff.value) = "Freiherr" ];
  AdditionalName_Freiin = 22 [ (goff.value) = "Freiin" ];
  AdditionalName_Frf = 23 [ (goff.value) = "Frf" ];
  AdditionalName_FrfDot = 24 [ (goff.value) = "Frf." ];
  AdditionalName_Frfr = 25 [ (goff.value) = "Frfr" ];
  AdditionalName_FrfrDot = 26 [ (goff.value) = "Frfr." ];
  AdditionalName_Frh = 27 [ (goff.value) = "Frh" ];
  AdditionalName_FrhDot = 28 [ (goff.value) = "Frh." ];
  AdditionalName_Frhr = 29 [ (goff.value) = "Frhr" ];
  AdditionalName_FrhrDot = 30 [ (goff.value) = "Frhr." ];
  AdditionalName_Fst = 31 [ (goff.value) = "Fst" ];
  AdditionalName_FstDot = 32 [ (goff.value) = "Fst." ];
  AdditionalName_Fstn = 33 [ (goff.value) = "Fstn" ];
  AdditionalName_FstnDot = 34 [ (goff.value) = "Fstn." ];
  AdditionalName_Furst = 35 [ (goff.value) = "Fürst" ];
  AdditionalName_Furstin = 36 [ (goff.value) = "Fürstin" ];
  AdditionalName_Gr = 37 [ (goff.value) = "Gr" ];
  AdditionalName_Graf = 38 [ (goff.value) = "Graf" ];
  AdditionalName_Grafin = 39 [ (goff.value) = "Gräfin" ];
  AdditionalName_Grf = 40 [ (goff.value) = "Grf" ];
  AdditionalName_Grfn = 41 [ (goff.value) = "Grfn" ];
  AdditionalName_Grossherzog = 42 [ (goff.value) = "Grossherzog" ];
  AdditionalName_GroBherzog = 43 [ (goff.value) = "Großherzog" ];
  AdditionalName_Grossherzogin = 44 [ (goff.value) = "Grossherzogin" ];
  AdditionalName_GroBherzogin = 45 [ (goff.value) = "Großherzogin" ];
  AdditionalName_Herzog = 46 [ (goff.value) = "Herzog" ];
  AdditionalName_Herzogin = 47 [ (goff.value) = "Herzogin" ];
  AdditionalName_Jhr = 48 [ (goff.value) = "Jhr" ];
  AdditionalName_JhrDot = 49 [ (goff.value) = "Jhr." ];
  AdditionalName_Jonkheer = 50 [ (goff.value) = "Jonkheer" ];
  AdditionalName_Junker = 51 [ (goff.value) = "Junker" ];
  AdditionalName_Landgraf = 52 [ (goff.value) = "Landgraf" ];
  AdditionalName_Landgrafin = 53 [ (goff.value) = "Landgräfin" ];
  AdditionalName_Markgraf = 54 [ (goff.value) = "Markgraf" ];
  AdditionalName_Markgrafin = 55 [ (goff.value) = "Markgräfin" ];
  AdditionalName_Marques = 56 [ (goff.value) = "Marques" ];
  AdditionalName_Marquis = 57 [ (goff.value) = "Marquis" ];
  AdditionalName_Marschall = 58 [ (goff.value) = "Marschall" ];
  AdditionalName_Ostoja = 59 [ (goff.value) = "Ostoja" ];
  AdditionalName_Prinz = 60 [ (goff.value) = "Prinz" ];
  AdditionalName_Prinzessin = 61 [ (goff.value) = "Prinzessin" ];
  AdditionalName_Przin = 62 [ (goff.value) = "Przin" ];
  AdditionalName_Rabe = 63 [ (goff.value) = "Rabe" ];
  AdditionalName_Reichsgraf = 64 [ (goff.value) = "Reichsgraf" ];
  AdditionalName_Reichsgrafin = 65 [ (goff.value) = "Reichsgräfin" ];
  AdditionalName_Ritter = 66 [ (goff.value) = "Ritter" ];
  AdditionalName_Rr = 67 [ (goff.value) = "Rr" ];
  AdditionalName_Truchsess = 68 [ (goff.value) = "Truchsess" ];
  AdditionalName_TruchseB = 69 [ (goff.value) = "Truchseß" ];
}

enum IntendWord {
  IntendWord_A = 0 [ (goff.value) = "a" ];
  IntendWord_Aande = 1 [ (goff.value) = "aan de" ];
  IntendWord_Aanden = 2 [ (goff.value) = "aan den" ];
  IntendWord_Al = 3 [ (goff.value) = "al" ];
  IntendWord_Am = 4 [ (goff.value) = "am" ];
  IntendWord_An = 5 [ (goff.value) = "an" ];
  IntendWord_Ander = 6 [ (goff.value) = "an der" ];
  IntendWord_Auf = 7 [ (goff.value) = "auf" ];
  IntendWord_Aufdem = 8 [ (goff.value) = "auf dem" ];
  IntendWord_Aufder = 9 [ (goff.value) = "auf der" ];
  IntendWord_Aufmspace = 10 [ (goff.value) = "auf m" ];
  IntendWord_Aufm = 11 [ (goff.value) = "aufm" ];
  IntendWord_Auffm = 12 [ (goff.value) = "auff m" ];
  IntendWord_Aus = 13 [ (goff.value) = "aus" ];
  IntendWord_Ausdem = 14 [ (goff.value) = "aus dem" ];
  IntendWord_Ausden = 15 [ (goff.value) = "aus den" ];
  IntendWord_Ausder = 16 [ (goff.value) = "aus der" ];
  IntendWord_B = 17 [ (goff.value) = "b" ];
  IntendWord_Be = 18 [ (goff.value) = "be" ];
  IntendWord_Bei = 19 [ (goff.value) = "bei" ];
  IntendWord_Beider = 20 [ (goff.value) = "bei der" ];
  IntendWord_Beim = 21 [ (goff.value) = "beim" ];
  IntendWord_Ben = 22 [ (goff.value) = "ben" ];
  IntendWord_Bey = 23 [ (goff.value) = "bey" ];
  IntendWord_Beyder = 24 [ (goff.value) = "bey der" ];
  IntendWord_Che = 25 [ (goff.value) = "che" ];
  IntendWord_Cid = 26 [ (goff.value) = "cid" ];
  IntendWord_D = 27 [ (goff.value) = "d" ];
  IntendWord_DDot = 28 [ (goff.value) = "d." ];
  IntendWord_DQuote = 29 [ (goff.value) = "d'" ];
  IntendWord_Da = 30 [ (goff.value) = "da" ];
  IntendWord_Dacosta = 31 [ (goff.value) = "da costa" ];
  IntendWord_Dalas = 32 [ (goff.value) = "da las" ];
  IntendWord_Dasilva = 33 [ (goff.value) = "da silva" ];
  IntendWord_Dal = 34 [ (goff.value) = "dal" ];
  IntendWord_Dall = 35 [ (goff.value) = "dall" ];
  IntendWord_Dallquote = 36 [ (goff.value) = "dall'" ];
  IntendWord_Dalla = 37 [ (goff.value) = "dalla" ];
  IntendWord_Dalle = 38 [ (goff.value) = "dalle" ];
  IntendWord_Dallo = 39 [ (goff.value) = "dallo" ];
  IntendWord_Das = 40 [ (goff.value) = "das" ];
  IntendWord_De = 41 [ (goff.value) = "de" ];
  IntendWord_Degli = 42 [ (goff.value) = "degli" ];
  IntendWord_Dei = 43 [ (goff.value) = "dei" ];
  IntendWord_Den = 44 [ (goff.value) = "den" ];
  IntendWord_Delquote = 45 [ (goff.value) = "de l '" ];
  IntendWord_Dela = 46 [ (goff.value) = "de la" ];
  IntendWord_Delas = 47 [ (goff.value) = "de las" ];
  IntendWord_Dele = 48 [ (goff.value) = "de le" ];
  IntendWord_Delos = 49 [ (goff.value) = "de los" ];
  IntendWord_Del = 50 [ (goff.value) = "del" ];
  IntendWord_Delcoz = 51 [ (goff.value) = "del coz" ];
  IntendWord_Deli = 52 [ (goff.value) = "deli" ];
  IntendWord_Dell = 53 [ (goff.value) = "dell" ];
  IntendWord_Dellquote = 54 [ (goff.value) = "dell'" ];
  IntendWord_Della = 55 [ (goff.value) = "della" ];
  IntendWord_Delle = 56 [ (goff.value) = "delle" ];
  IntendWord_Delli = 57 [ (goff.value) = "delli" ];
  IntendWord_Dello = 58 [ (goff.value) = "dello" ];
  IntendWord_Der = 59 [ (goff.value) = "der" ];
  IntendWord_Des = 60 [ (goff.value) = "des" ];
  IntendWord_Di = 61 [ (goff.value) = "di" ];
  IntendWord_Dit = 62 [ (goff.value) = "dit" ];
  IntendWord_Do = 63 [ (goff.value) = "do" ];
  IntendWord_Doceu = 64 [ (goff.value) = "do ceu" ];
  IntendWord_Don = 65 [ (goff.value) = "don" ];
  IntendWord_Donle = 66 [ (goff.value) = "don le" ];
  IntendWord_Dos = 67 [ (goff.value) = "dos" ];
  IntendWord_Dossantos = 68 [ (goff.value) = "dos santos" ];
  IntendWord_Du = 69 [ (goff.value) = "du" ];
  IntendWord_Dy = 70 [ (goff.value) = "dy" ];
  IntendWord_El = 71 [ (goff.value) = "el" ];
  IntendWord_G = 72 [ (goff.value) = "g" ];
  IntendWord_Gen = 73 [ (goff.value) = "gen" ];
  IntendWord_Gil = 74 [ (goff.value) = "gil" ];
  IntendWord_Gli = 75 [ (goff.value) = "gli" ];
  IntendWord_Grosse = 76 [ (goff.value) = "grosse" ];
  IntendWord_GroBe = 77 [ (goff.value) = "große" ];
  IntendWord_I = 78 [ (goff.value) = "i" ];
  IntendWord_Im = 79 [ (goff.value) = "im" ];
  IntendWord_In = 80 [ (goff.value) = "in" ];
  IntendWord_Inde = 81 [ (goff.value) = "in de" ];
  IntendWord_Inden = 82 [ (goff.value) = "in den" ];
  IntendWord_Inder = 83 [ (goff.value) = "in der" ];
  IntendWord_Inhet = 84 [ (goff.value) = "in het" ];
  IntendWord_Intquote = 85 [ (goff.value) = "in't" ];
  IntendWord_Kl = 86 [ (goff.value) = "kl" ];
  IntendWord_Kleine = 87 [ (goff.value) = "kleine" ];
  IntendWord_L = 88 [ (goff.value) = "l" ];
  IntendWord_Ldot = 89 [ (goff.value) = "l." ];
  IntendWord_Lquote = 90 [ (goff.value) = "l'" ];
  IntendWord_La = 91 [ (goff.value) = "la" ];
  IntendWord_Le = 92 [ (goff.value) = "le" ];
  IntendWord_Lee = 93 [ (goff.value) = "lee" ];
  IntendWord_Li = 94 [ (goff.value) = "li" ];
  IntendWord_Lo = 95 [ (goff.value) = "lo" ];
  IntendWord_M = 96 [ (goff.value) = "m" ];
  IntendWord_Mc = 97 [ (goff.value) = "mc" ];
  IntendWord_Mac = 98 [ (goff.value) = "mac" ];
  IntendWord_N = 99 [ (goff.value) = "n" ];
  IntendWord_O = 100 [ (goff.value) = "o" ];
  IntendWord_Oquote = 101 [ (goff.value) = "o'" ];
  IntendWord_Op = 102 [ (goff.value) = "op" ];
  IntendWord_Opde = 103 [ (goff.value) = "op de" ];
  IntendWord_Opden = 104 [ (goff.value) = "op den" ];
  IntendWord_Opgen = 105 [ (goff.value) = "op gen" ];
  IntendWord_Ophet = 106 [ (goff.value) = "op het" ];
  IntendWord_Opte = 107 [ (goff.value) = "op te" ];
  IntendWord_Opten = 108 [ (goff.value) = "op ten" ];
  IntendWord_Oude = 109 [ (goff.value) = "oude" ];
  IntendWord_Pla = 110 [ (goff.value) = "pla" ];
  IntendWord_Pro = 111 [ (goff.value) = "pro" ];
  IntendWord_S = 112 [ (goff.value) = "s" ];
  IntendWord_Stdot = 113 [ (goff.value) = "st." ];
  IntendWord_T = 114 [ (goff.value) = "t" ];
  IntendWord_Te = 115 [ (goff.value) = "te" ];
  IntendWord_Ten = 116 [ (goff.value) = "ten" ];
  IntendWord_Ter = 117 [ (goff.value) = "ter" ];
  IntendWord_Thi = 118 [ (goff.value) = "thi" ];
  IntendWord_Tho = 119 [ (goff.value) = "tho" ];
  IntendWord_Thom = 120 [ (goff.value) = "thom" ];
  IntendWord_Thor = 121 [ (goff.value) = "thor" ];
  IntendWord_Thum = 122 [ (goff.value) = "thum" ];
  IntendWord_To = 123 [ (goff.value) = "to" ];
  IntendWord_Tom = 124 [ (goff.value) = "tom" ];
  IntendWord_Tor = 125 [ (goff.value) = "tor" ];
  IntendWord_Tu = 126 [ (goff.value) = "tu" ];
  IntendWord_Tum = 127 [ (goff.value) = "tum" ];
  IntendWord_Unten = 128 [ (goff.value) = "unten" ];
  IntendWord_Unter = 129 [ (goff.value) = "unter" ];
  IntendWord_Unterm = 130 [ (goff.value) = "unterm" ];
  IntendWord_Vdot = 131 [ (goff.value) = "v." ];
  IntendWord_Vddot = 132 [ (goff.value) = "v. d." ];
  IntendWord_Vdem = 133 [ (goff.value) = "v. dem" ];
  IntendWord_Vden = 134 [ (goff.value) = "v. den" ];
  IntendWord_Vder = 135 [ (goff.value) = "v. der" ];
  IntendWord_Vd = 136 [ (goff.value) = "v.d." ];
  IntendWord_Vdemdot = 137 [ (goff.value) = "v.dem" ];
  IntendWord_Vdendot = 138 [ (goff.value) = "v.den" ];
  IntendWord_Vderdot = 139 [ (goff.value) = "v.der" ];
  IntendWord_Van = 140 [ (goff.value) = "van" ];
  IntendWord_Vandespace = 141 [ (goff.value) = "van de" ];
  IntendWord_Vandemspace = 142 [ (goff.value) = "van dem" ];
  IntendWord_Vandenspace = 143 [ (goff.value) = "van den" ];
  IntendWord_Vanderspace = 144 [ (goff.value) = "van der" ];
  IntendWord_Vande = 145 [ (goff.value) = "vande" ];
  IntendWord_Vandem = 146 [ (goff.value) = "vandem" ];
  IntendWord_Vanden = 147 [ (goff.value) = "vanden" ];
  IntendWord_Vander = 148 [ (goff.value) = "vander" ];
  IntendWord_Vangen = 149 [ (goff.value) = "van gen" ];
  IntendWord_Vanhet = 150 [ (goff.value) = "van het" ];
  IntendWord_Vant = 151 [ (goff.value) = "van t" ];
  IntendWord_Ven = 152 [ (goff.value) = "ven" ];
  IntendWord_Vender = 153 [ (goff.value) = "ven der" ];
  IntendWord_Ver = 154 [ (goff.value) = "ver" ];
  IntendWord_Vo = 155 [ (goff.value) = "vo" ];
  IntendWord_Vom = 156 [ (goff.value) = "vom" ];
  IntendWord_Vomundzu = 157 [ (goff.value) = "vom und zu" ];
  IntendWord_Von = 158 [ (goff.value) = "von" ];
  IntendWord_Vonundzu = 159 [ (goff.value) = "von und zu" ];
  IntendWord_Vonundzuder = 160 [ (goff.value) = "von und zu der" ];
  IntendWord_Vonundzur = 161 [ (goff.value) = "von und zur" ];
  IntendWord_Vondespace = 162 [ (goff.value) = "von de" ];
  IntendWord_Vondemspace = 163 [ (goff.value) = "von dem" ];
  IntendWord_Vondenspace = 164 [ (goff.value) = "von den" ];
  IntendWord_Vonderspace = 165 [ (goff.value) = "von der" ];
  IntendWord_Vonla = 166 [ (goff.value) = "von la" ];
  IntendWord_Vonzu = 167 [ (goff.value) = "von zu" ];
  IntendWord_Vonzum = 168 [ (goff.value) = "von zum" ];
  IntendWord_Vonzur = 169 [ (goff.value) = "von zur" ];
  IntendWord_Vonde = 170 [ (goff.value) = "vonde" ];
  IntendWord_Vonden = 171 [ (goff.value) = "vonden" ];
  IntendWord_Vondem = 172 [ (goff.value) = "vondem" ];
  IntendWord_Vonder = 173 [ (goff.value) = "vonder" ];
  IntendWord_Voneinem = 174 [ (goff.value) = "von einem" ];
  IntendWord_Vonmast = 175 [ (goff.value) = "von mast" ];
  IntendWord_Vor = 176 [ (goff.value) = "vor" ];
  IntendWord_Vordem = 177 [ (goff.value) = "vor dem" ];
  IntendWord_Vorden = 178 [ (goff.value) = "vor den" ];
  IntendWord_Vorder = 179 [ (goff.value) = "vor der" ];
  IntendWord_Vorm = 180 [ (goff.value) = "vorm" ];
  IntendWord_Vorn = 181 [ (goff.value) = "vorn" ];
  IntendWord_Y = 182 [ (goff.value) = "y" ];
  IntendWord_Ydel = 183 [ (goff.value) = "y del" ];
  IntendWord_Zu = 184 [ (goff.value) = "zu" ];
  IntendWord_Zum = 185 [ (goff.value) = "zum" ];
  IntendWord_Zur = 186 [ (goff.value) = "zur" ];
}

enum InsuranceStatus {
  Mitglied = 0 [ (goff.value) = '1' ];
  Familienmitglied = 1 [ (goff.value) = '3' ];
  Rentner = 2 [ (goff.value) = '5' ];
}

enum WorkActivity1 {
  Physical = 0;
  Mental = 1;
}

enum WorkActivity2 {
  Standing = 0;
  Sitting = 1;
}

enum FromCardType {
  FromCardType_EGK = 0 [(goff.value) = 'EGK'];
  FromCardType_KVK = 1 [(goff.value) = 'KVK'];
  FromCardType_Mobile = 2 [(goff.value) = 'Mobile'];
}

message AddressInsurance {
  string Street = 1 [ (goff.nullable) = true ];
  string Number = 2 [ (goff.nullable) = true ];
  string PostCode = 3 [ (goff.nullable) = true ];
  string City = 4 [ (goff.nullable) = true ];
  string Country = 5 [ (goff.nullable) = true ];
}

message InsuranceInfo {
  goff.UUID Id = 1;
  string InsuranceCompanyId = 2;
  string InsuranceCompanyName = 3;
  repeated string LocationNames = 4;
  catalog_utils_common.Validity Validity = 5;
  int32 IkNumber = 6;
  string InsuranceNumber = 7 [ (goff.nullable) = true ];
  InsuranceStatus InsuranceStatus = 8;
  int64 StartDate = 9 [ (goff.nullable) = true ];
  int64 EndDate = 10 [ (goff.nullable) = true ];
  SpecialGroupDescription SpecialGroup = 11;
  string DMPLabeling = 12;
  bool HaveCoPaymentExemptionTill = 13;
  bool HavePatientReceipt = 14;
  bool HaveHeimiLongTermApproval = 15;
  int64 CopaymentExemptionTillDate = 16 [ (goff.nullable) = true ];
  // todo remove later if this prop belongs to schein
  string wop = 17 [ (goff.custom) = 'bson:"wop,omitempty"' ];
  repeated LHM LHMs = 18 [ (goff.nullable) = true ];
  int32 FeeSchedule = 19;
  InsuranceInfo AcquiredInsuranceInfo = 20 [ (goff.nullable) = true ];
  catalog_sdkt_common.FeeCatalogue FeeCatalogue = 21;
  TypeOfInsurance InsuranceType = 22;
  MMYYYY ValidUntil = 23 [ (goff.nullable) = true ];
  repeated ReadCardModel ReadCardDatas = 24 [ (goff.nullable) = true ];
  bool IsActive = 25;
  AddressInsurance Address = 26 [ (goff.nullable) = true ];
  string tel = 27 [ (goff.nullable) = true ];
  string fax = 28 [ (goff.nullable) = true ];
  bool isTerminated = 29 [ (goff.nullable) = true ];
}

message VSDStatus {
  string Status = 1;
  int64 Timestamp = 2;
  string Version = 3;
}

message ReadCardModel {
  FromCardType FromCardType = 1;
  ProofOfInsurance proofOfInsurance = 2 [(goff.nullable) = true]; //only exist with EGK
  string RegistrationNumber = 3 [(goff.nullable) = true]; //only exist with Mobile
  int64 ReadCardDate = 4;
  string CdmVersion = 5;
  VSDStatus VSDStatus = 6 [(goff.nullable) = true]; //only exist with EGK
}

message ProofOfInsurance {
  int64 OnlineCheckDate = 1;
  int32 ResultCode = 2;
  string CheckDigit = 3 [ (goff.nullable) = true ];
  int32 ErrorCode = 4 [ (goff.nullable) = true ];
}

message InsuranceRequestCreate {
  string InsuranceCompanyId = 2;
  string InsuranceCompanyName = 3;
  repeated string LocationNames = 4;
  catalog_utils_common.Validity Validity = 5;
  int32 IkNumber = 6;
  string InsuranceNumber = 7 [ (goff.nullable) = true ];
  InsuranceStatus InsuranceStatus = 8;
  int64 StartDate = 9 [ (goff.nullable) = true ];
  int64 EndDate = 10 [ (goff.nullable) = true ];
  SpecialGroupDescription SpecialGroup = 11;
  string DMPLabeling = 12;
  bool HaveCoPaymentExemptionTill = 13;
  bool HavePatientReceipt = 14;
  bool HaveHeimiLongTermApproval = 15;
  int64 CopaymentExemptionTillDate = 16 [ (goff.nullable) = true ];
  // todo remove later if this prop belongs to schein
  string wop = 17 [ (goff.custom) = 'bson:"omitempty"' ];
  repeated LHM LHMs = 18 [ (goff.nullable) = true ];
  bool IsActive = 19;
  int32 FeeSchedule = 20;
  insurance_common.FromCardType FromCardType = 21 [ (goff.nullable) = true ];
  insurance_common.InsuranceInfo AcquiredInsuranceInfo = 22 [ (goff.nullable) = true ];
  catalog_sdkt_common.FeeCatalogue FeeCatalogue = 23;
  TypeOfInsurance InsuranceType = 24;
  MMYYYY ValidUntil = 25 [ (goff.nullable) = true ];
}

message MMYYYY {
  int32 Year = 1 [ (goff.custom) = 'validate:"omitempty,gte=1900"', (goff.nullable) = true ];
  int32 Month = 2 [ (goff.custom) = 'validate:"omitempty,gte=1,lte=12"',(goff.nullable) = true ];
}

enum CardReadInStatus {
  CardReadInStatus_CardReadin = 0 [ (goff.value) = 'CardReadin' ]; // read in
  CardReadInStatus_ManualEntry = 1
      [ (goff.value) = 'ManualEntry' ];                          // manual entry
  CardReadInStatus_NotReadin = 2 [ (goff.value) = 'NotReadin' ]; // not reading
  CardReadInStatus_OnlineCheckFail = 3 [ (goff.value) = 'OnlineCheckFail' ];
}

enum PatientType {
  PatientType_Private = 0 [ (goff.value) = 'Private' ];
  PatientType_Public = 1 [ (goff.value) = 'Public' ];
}

message GenericInfo {
  PatientType PatientType = 1;
  int64 LastManualEntryDate = 2 [ (goff.nullable) = true ];
  int64 LastCardReadinDate = 3 [ (goff.nullable) = true ];
}

message DateOfBirth {
  int32 Year = 1 [ (goff.custom) = 'validate:"omitempty,gte=1900,lte=2100"', (goff.nullable) = true ];
  int32 Month = 2 [ (goff.custom) = 'validate:"omitempty,gte=1,lte=12"',(goff.nullable) = true ];
  int32 Date = 3 [ (goff.custom) = 'validate:"omitempty,gte=1,lte=31"',(goff.nullable) = true ];
  bool IsValidDOB = 21;
}

message PersonalInfo {
  string FirstName = 1 [(goff.custom) = 'validate:"trim"'];
  string LastName = 2 [(goff.custom) = 'validate:"trim"'];;
  string Title = 3 [ (goff.nullable) = true ];
  repeated AdditionalName AdditionalNames = 4 [ (goff.nullable) = true ];
  IntendWord IntendWord = 5 [ (goff.nullable) = true ];
  Gender Gender = 6;
  int64 DOB = 7;
  DateOfBirth DateOfBirth = 8 [(goff.custom) = 'validate:"required"'];
  int64 DateOfDeath = 10 [ (goff.nullable) = true ];
  string BirthName = 11 [ (goff.nullable) = true ];
  string Status = 12 [ (goff.nullable) = true ];
}

message Address {
  string Street = 1 [ (goff.nullable) = true ];
  string Number = 2 [ (goff.nullable) = true ];
  string PostCode = 3;
  string City = 4 [ (goff.nullable) = true ];
  string CountryCode = 5 [ (goff.nullable) = true ];
  string AdditionalAddressInfo = 6 [ (goff.nullable) = true ];
  float Distance = 7 [ (goff.nullable) = true ];
}

// P.O. BoxPLZ
// Postleitzahl - Postal code

// P.O. BoxCity
// Ort - location, place

// PO Box
// Postfach - Post office box

// P.O. BoxResidence country code
// Land - country code
message PostalAddress {
  string PostCode = 1;
  string OfficeBox = 2;
  string CityBox = 3;
  string CountryCode = 4;
}

message BillingAddress {
  string Street = 1 [ (goff.nullable) = true ];
  string Number = 2 [ (goff.nullable) = true ];
  string PostCode = 3;
  string City = 4 [ (goff.nullable) = true ];
  string CountryCode = 5 [ (goff.nullable) = true ];
  string FirstName = 6;
  string LastName = 7;
  string Title = 8 [ (goff.nullable) = true ];
  repeated AdditionalName AdditionalNames = 9 [ (goff.nullable) = true ];
  IntendWord IntendWord = 10 [ (goff.nullable) = true ];
}

message CompanyAddress {
  string Street = 1 [ (goff.nullable) = true ];
  string Number = 2 [ (goff.nullable) = true ];
  string PostCode = 3 [ (goff.nullable) = true ];
  string City = 4 [ (goff.nullable) = true ];
  string CountryCode = 5 [ (goff.nullable) = true ];
  string Employer = 6 [ (goff.nullable) = true ];
}

message AddressInfo {
  Address Address = 1;
  BillingAddress BillingAddress = 2;
  PostalAddress PostalAddress = 3 [ (goff.nullable) = true ];
}

message ContactPerson {
  string Name = 1 [ (goff.nullable) = true ];
  string PhoneNumber = 2 [ (goff.nullable) = true ];
  string Email = 3 [ (goff.nullable) = true ];
  string Relationship = 4 [ (goff.nullable) = true ];
}

message ContactInfo {
  string PrimaryContactNumber = 1 [ (goff.nullable) = true ];
  repeated string FurtherContactNumber = 2 [ (goff.nullable) = true ];
  string EmailAddress = 3 [ (goff.nullable) = true ];
  bool HaveDeclarationOfAgreementToContact = 4;
  repeated ContactPerson ContactPersons = 5 [ (goff.nullable) = true ];
  FileInfo ContactAgreementFile = 6 [ (goff.nullable) = true ];
  string Address = 7 [ (goff.nullable) = true ];
}

message DoctorInfo {
  repeated goff.UUID GeneralPractitionerDoctorId = 1;
  repeated goff.UUID SpecialistDoctorId = 2;
  goff.UUID TreatmentDoctorId = 3 [ (goff.nullable) = true ];
}

message FileInfo {
  string FileName = 1;
  string FileUrl = 2;
}

message OtherInfo {
  string Cave = 1;
  int64 PatientSinceDate = 2 [ (goff.nullable) = true ];
  int64 LastTreatmentDate = 3 [ (goff.nullable) = true ];
  bool HaveMedicalHistoryFormCompleted = 4;
  bool IsLivingWillAvailable = 5;
  bool IsAgreeWithBillingViaPVS = 6;
  bool IsPrivacyPolicySigned = 7;
  FileInfo MedicalHistoryFileUrl = 8 [ (goff.nullable) = true ];
  FileInfo LivingWillFileUrl = 9 [ (goff.nullable) = true ];
  FileInfo BillingFileUrl = 10 [ (goff.nullable) = true ];
  FileInfo PrivacyPolicyFileUrl = 11 [ (goff.nullable) = true ];
}

message VisitInfo {
  goff.UUID TreatmentDoctorId = 1 [ (goff.nullable) = true ];
  string AdditionalVisitInfo = 2 [ (goff.nullable) = true ];
}
message PostOfficeBox {
  string PostCode = 1;
  string PlaceOfResidence = 2;
  string OfficeBox = 3;
  string CountryCode = 4;
}

message EuropeanHealthInsurance {
  bool HasEuropeanHealthInsuranceCard = 1;
  string FormSetting = 2 [ (goff.nullable) = true, (goff.custom) = 'validate:"omitempty,trimjson"' ];
  string Language = 3 [ (goff.nullable) = true ];
  common.EuropeanHealthInsuranceStatus Status = 4 [ (goff.nullable) = true ];
  goff.UUID TreatmentDoctorId = 5;
}

message PatientInfo {
  int32 PatientNumber = 1;
  GenericInfo GenericInfo = 2;
  PersonalInfo PersonalInfo = 3;
  AddressInfo AddressInfo = 4;
  ContactInfo ContactInfo = 5;
  repeated InsuranceInfo InsuranceInfos = 6;
  DoctorInfo DoctorInfo = 7;
  OtherInfo OtherInfo = 8;
  VisitInfo VisitInfo = 9;
  EmploymentInfo EmploymentInfo = 11;
  PostOfficeBox PostOfficeBox = 12;
  EuropeanHealthInsurance EuropeanHealthInsurance = 13;
  // Like DTO
  goff.UUID PatientId = 14 [(goff.nullable) = true, (goff.custom) = 'bson:"-"'];

  /*
    After reading card we will save a card raw
    This field is using for store a latest card raw has been read
  */
  goff.UUID CardRawId = 15 [ (goff.nullable) = true];
}

enum JobStatus {
  JobStatus_Employees = 0 [ (goff.value) = "Angestellte (erwerbstätig=Y)" ];
  JobStatus_Executives = 1
      [ (goff.value) = "Leitende Angestellte (erwerbstätig=Y)" ];
  JobStatus_ManagingDirector = 2
      [ (goff.value) = "Geschäftsfühhrter (erwerbstätig=Y)" ];
  JobStatus_CivilServant = 3
      [ (goff.value) = "Zivilbediensteter/Diener (erwerbstätig=Y)" ];
  JobStatus_JobSeeker = 4 [ (goff.value) = "Arbeitssuchend (erwerbstätig=N)" ];
}

message EmploymentInfo {
  JobStatus JobStatus = 1 [ (goff.nullable) = true ];
  string Occupation = 2;
  string SpecialProblemAtWork = 4;
  float workingHourInWeek = 5;
  WorkActivity1 workActivity1 = 6;
  WorkActivity2 workActivity2 = 7;
  CompanyAddress CompanyAddress = 8;
  bool IsEmployed = 9;
}

enum CompareStatus {
  NotExist = 0;
  PartialMatch = 1;
  ExactMatch = 2;
  ExactInsuranceNumber = 3;
}

message Allergy {
  string Allergy = 1;
  bool IsPrescriptionRelated = 2;
}

enum Pulse {
  Pulse_Rhythmic = 0;
  Pulse_Irregular = 1;
}

message PatientMedicalData {
  double Weight = 1 [ (goff.nullable) = true ];
  double Height = 2 [ (goff.nullable) = true ];
  string BloodPressure = 3 [ (goff.nullable) = true ];
  int64 HeartFrequency = 4 [ (goff.nullable) = true ];
  repeated Allergy Allergies = 5 [ (goff.nullable) = true ];
  double Creatinine = 6 [ (goff.nullable) = true ];
  int32 AmountOfChildren = 7 [ (goff.nullable) = true ];
  bool IsPregnant = 8 [ (goff.nullable) = true ];
  int64 DateOfPlannedBirth = 9 [ (goff.nullable) = true ];
  int64 AmountOfBirth = 10 [ (goff.nullable) = true ];
  int64 AmountOfPregnancies = 11 [ (goff.nullable) = true ];
  bool IsBreastfeeding = 12 [ (goff.nullable) = true ];
  int32 CareLevel = 13 [ (goff.nullable) = true ];
  string AdditionalNote = 14 [ (goff.nullable) = true ];
  bool IsSmoker = 15 [ (goff.nullable) = true ];
  int64 CigarettesPerDay = 16 [ (goff.nullable) = true ];
  repeated Allergy AllergiesFor4205 = 17 [ (goff.nullable) = true ];
  double BodyTemperature = 18 [ (goff.nullable) = true ];
  double PulseOxiMetric = 19 [ (goff.nullable) = true ];
  Pulse Pulse = 20 [ (goff.nullable) = true ];
}

message LHM {
  goff.UUID Id = 1 [ (goff.nullable) = true ];
  string FirstICDCode = 2 [ (goff.custom) = 'validate:"required"' ];
  string SecondICDCode = 3 [ (goff.nullable) = true ];
  string DiagnosisGroupCode = 4;
  bool IsStandardCombination = 5;
  repeated string PrimaryRemediesPosition = 6 [ (goff.nullable) = true ];
  repeated string ComplementaryRemediesPosition = 7 [ (goff.nullable) = true ];
  int64 ValidUntilDate = 8 [ (goff.nullable) = true ];
  string Note = 9 [ (goff.nullable) = true ];
}

message PatientPrepared {
  PatientInfo PatientInfo = 1;
  CompareStatus CompareStatus = 2;
  error_code.ErrorCode ValidationError = 3 [ (goff.nullable) = true ];
  bool IsCreateShown = 4;
}

message PatientDeleteRequest {
    goff.UUID PatientId = 1 [(goff.custom) = 'validate:"required"'];
}

enum EventName {
  EventName_UpdateMedicalData = 0;
  EventName_UpdatePatientProfile = 1;
  EventName_UpdateLHM = 2;
  EventName_CreatedPatient = 3;
  EventName_DeletedPatient = 4;
}

message EventPatientProfileChange {
  EventName EventName = 1;
  goff.UUID PatientId = 2;
  patient_profile_common.PatientMedicalData PatientMedicalData = 3
      [ (goff.nullable) = true ];
  patient_profile_common.PatientInfo PatientInfo = 4 [ (goff.nullable) = true ];
  int64 MedicalDataUpdatedAt = 5 [ (goff.nullable) = true ];
  int64 EmploymentInfoUpdatedAt = 6 [ (goff.nullable) = true ];
  bool ShouldCreateNewEHIC = 7;
  patient_profile_common.InsuranceInfo CardInsurance = 8 [ (goff.nullable) = true ];

  /*
    if patient created by card -> reading date = card reading date
    if patient created by ev -> reading date = time now
  */
  int64 ReadingDate = 9;
  bool ShouldRunTimelineValidation = 10;
}

message GetInsurancesByPatientIdRequest {
  goff.UUID PatientId = 1;
}

message GetInsurancesByPatientIdResponse {
  repeated patient_profile_common.InsuranceInfo InsuranceInfos = 1 [(goff.custom) = 'validate:"required"'];
}
