syntax = "proto3";

package service_domains_profile;

import "goff.proto";
import "service/domains/patient_profile_common.proto";
import "service/domains/common.proto";
import "app/admin/admin_app.proto";

option go_package = "git.tutum.dev/medi/tutum/ares/service/domains/api/profile";

option (goff.nats_subject) = "api.service.domains";

enum WorkActivity1 {
  Physical = 0;
  Mental = 1;
}

enum WorkActivity2 {
  Standing = 0;
  Sitting = 1;
}

enum IsEmploymentAnswer {
  Yes = 0;
  No = 1;
}

message GetByIdsRequest {
  repeated goff.UUID originalIds = 1 [ (goff.custom) = 'validate:"required"' ];
}
message GetByLanrIDRequest { string lanr = 1; }

message GetByHzvIDRequest { string havgId = 1; }

message GetByMediIDRequest { string mediId = 1; }

message GetByBsnrIdRequest { goff.UUID bsnrId = 1 [ (goff.custom) = 'validate:"required"' ];}

// Employee Service

message Contract {
  string contractId = 1;
  int64 startDate = 2;
  int64 endDate = 3 [ (goff.nullable) = true ];
}

message EmployeeProfileResponse {
  goff.UUID id = 1;
  string fullName = 2;
  string firstName = 3;
  string lastName = 4;
  string title = 5 [ (goff.nullable) = true ];
  int64 dob = 6 [ (goff.nullable) = true ];
  patient_profile_common.Salutation Salutation = 7 [(goff.nullable) = true];
  string email = 8 [ (goff.nullable) = true ];
  string phone = 9 [ (goff.nullable) = true ];
  string address = 10 [ (goff.nullable) = true ];
  string lanr = 11 [ (goff.nullable) = true ];
  string bsnr = 12;
  string havgId = 13 [ (goff.nullable) = true ];
  string havgVpId = 14 [ (goff.nullable) = true ];
  string mediverbundId = 15 [ (goff.nullable) = true ];
  string mediverbundVpId = 16 [ (goff.nullable) = true ];
  repeated string areaOfExpertise = 17 [ (goff.nullable) = true ];
  string mobilePhone = 18 [ (goff.custom) = 'bson:"omitempty"' ];
  string okv = 19 [ (goff.nullable) = true ];
  bool hasHzvContracts = 20;
  bool hasFavContracts = 21;
  string zipCode = 22;
  string street = 23;
  string fax = 24;
  patient_profile_common.AdditionalName AdditionalName = 25 [ (goff.nullable) = true ];
  patient_profile_common.IntendWord IntendWord = 26 [ (goff.nullable) = true ];
  string Initial = 27;
  repeated string DmpPrograms = 28 [ (goff.nullable) = true ];
  string jobDescription = 29 [(goff.nullable) = true];
  bool markAsBillingDoctor = 30;
  goff.UUID bsnrId = 31;
  goff.UUID EmployeeProfileId = 32 [(goff.nullable) = true];
  string pseudoLanr = 33 [(goff.nullable) = true];
  repeated string teamNumbers = 34 [(goff.nullable) = true];
  string doctorStamp = 35;
  string BsnrCity = 36;
  string BsnrPracticeStamp = 37;
  repeated common.BankInformation BankInformations = 38;
  bool markAsEmployedDoctor = 39;
  goff.UUID ResponsibleDoctorId = 40 [ (goff.nullable) = true ];
  goff.UUID RepresentativeDoctorId = 41 [ (goff.nullable) = true ];
  string bsnrName = 42 [ (goff.nullable) = true ];
  string bsnrStreet = 43;
  string bsnrNumber = 44;
  string bsnrPostCode = 45;
  string bsnrPhoneNumber = 46;
  string bsnrFaxNumber = 47;
  bool isParticipationActive = 48;
  repeated common.UserType Types = 49;
  string externalId = 50;
  string BsnrFacilityType = 51;
  goff.UUID deviceId = 52 [ (goff.nullable) = true ];
  string UserName = 53;
  string OrgId = 54;
  string HpmEndpoint = 55;
  int64 CreatedDate = 56;
  common.EmployeeStatus status = 57;
  repeated app_admin.Contract hzvContracts = 58;
  repeated app_admin.Contract favContracts = 59;
  bool IsDoctor = 60;
  repeated string bsnrs = 62;
  repeated goff.UUID bsnrIds = 63;
  common.EHKSType EHKSType = 64 [(goff.nullable) = true];
}

message EmployeeProfilesResponse {
  repeated EmployeeProfileResponse profiles = 1;
}

message EmployeeProfileDeleteResponse { int64 result = 1; }
message EmployeeProfileDeleteRequest {
  goff.UUID originalId = 1 [ (goff.custom) = 'validate:"required"' ];
}

message EmployeeProfileGetRequest {
  goff.UUID originalId = 1 [ (goff.custom) = 'validate:"required"' ];
  goff.UUID bsnrId = 2 [ (goff.nullable) = true ];
}

message EmployeeProfileRequest {
  goff.UUID originalId = 1 [ (goff.custom) = 'validate:"required"' ];
  string firstName = 2 [(goff.custom) = 'validate:"trim"'];
  string lastName = 3 [(goff.custom) = 'validate:"trim"'];
  string title = 4 [ (goff.nullable) = true ];
  int64 dob = 5 [ (goff.nullable) = true ];
  patient_profile_common.Salutation Salutation = 6 [(goff.nullable) = true];
  string email = 7 [ (goff.nullable) = true ];
  string phone = 8 [ (goff.nullable) = true ];
  string address = 9 [ (goff.nullable) = true ];
  string lanr = 10 [ (goff.nullable) = true ];
  string bsnr = 11;
  string havgId = 12 [ (goff.nullable) = true ];
  string havgVpId = 13 [ (goff.nullable) = true ];
  string mediverbundId = 14 [ (goff.nullable) = true ];
  string mediverbundVpId = 15 [ (goff.nullable) = true ];
  repeated string areaOfExpertise = 16 [ (goff.nullable) = true ];
  string mobilePhone = 17 [ (goff.custom) = 'bson:"omitempty"' ];
  string okv = 18 [ (goff.nullable) = true ];
  bool hasHzvContracts = 19;
  bool hasFavContracts = 20;
  patient_profile_common.AdditionalName AdditionalName = 21 [ (goff.nullable) = true ];
  patient_profile_common.IntendWord IntendWord = 22 [ (goff.nullable) = true ];
  string Initial = 23;
  repeated string DmpPrograms = 24 [ (goff.nullable) = true ];
  string jobDescription = 25 [(goff.nullable) = true];
  bool markAsBillingDoctor = 26;
  goff.UUID bsnrId = 27 [(goff.nullable) = true];
  string pseudoLanr = 28 [(goff.nullable) = true];
  repeated string teamNumbers = 29 [(goff.nullable) = true];
  string doctorStamp = 30;
  string bsnrPracticeStamp = 31;
  repeated common.BankInformation BankInformations = 32;
  bool markAsEmployedDoctor = 33;
  goff.UUID ResponsibleDoctorId = 34 [ (goff.nullable) = true ];
  goff.UUID RepresentativeDoctorId = 35 [ (goff.nullable) = true ];
  string bsnrName = 36 [ (goff.nullable) = true ];
  string bsnrStreet = 37;
  string bsnrNumber = 38;
  string bsnrPostCode = 39;
  string password = 40;
  bool isParticipationActive = 41;
  repeated common.UserType Types = 42;
  goff.UUID deviceId = 43 [ (goff.nullable) = true ];
  string UserName = 44;
  string ExternalId = 45;
  string HpmEndpoint = 46;
  repeated app_admin.Contract hzvContracts = 47;
  repeated app_admin.Contract favContracts = 48;
  repeated string bsnrs = 49;
  repeated goff.UUID bsnrIds = 50 [(goff.nullable) = true];
  common.EHKSType EHKSType = 51 [(goff.nullable) = true];
  bool IsDoctor = 52;
}

message GetAllInitialResponse {
  repeated string Data = 1;
}

message GetEmployeeByExternalIdRequest {
  string ExternalId = 1 [(goff.custom) = 'validate:"required"'];
}

message UpdateDeviceRequest {
 goff.UUID deviceId = 1 [(goff.custom) = 'validate:"required"'];
}

message GetEmployeeByDeviceIdRequest {
  goff.UUID deviceId = 1 [(goff.custom) = 'validate:"required"'];
}

message GetByCareProviderIdRequest {
  goff.UUID careProviderId = 1 [(goff.custom) = 'validate:"required"'];
}

message SearchEmployeeByNameRequest {
  string Name = 1;
  goff.UUID careProviderId = 2 [(goff.custom) = 'validate:"required"'];
  common.PaginationRequest PaginationRequest = 3;
}

message SearchEmployeeByNameResponse {
  repeated EmployeeProfileResponse profiles = 1;
  common.PaginationResponse PaginationResponse = 2;
}

message UpdateEmployeeStatusRequest {
  string ExternalId = 1 [(goff.custom) = 'validate:"required"'];
  common.EmployeeStatus status = 2;
}

message ResetEmployeePasswordRequest {
  string ExternalId = 1 [(goff.custom) = 'validate:"required"'];
}

message ResetEmployeePasswordResponse {
  string Password = 1 [(goff.custom) = 'validate:"required"'];
}

message ResetEmployee2FARequest {
  string ExternalId = 1 [(goff.custom) = 'validate:"required"'];
}

service EmployeeProfileService {
  rpc GetAll (GetByCareProviderIdRequest) returns (EmployeeProfilesResponse){
    option (goff.secured) = "SYS_ADMIN";
  };
  rpc SearchEmployeeByName (SearchEmployeeByNameRequest) returns (SearchEmployeeByNameResponse){
    option (goff.secured) = "CARE_PROVIDER_MEMBER_PRE_SWITCH, CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN";
  };
  rpc GetEmployeeByExternalId (GetEmployeeByExternalIdRequest) returns (EmployeeProfileResponse){
    option (goff.secured) = "IS_AUTHENTICATED";
  }
  rpc GetAllInitial (goff.Empty) returns (GetAllInitialResponse){
    option (goff.secured) = "CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN";
  }
  rpc getMyEmployeeProfile(goff.Empty) returns (EmployeeProfileResponse) {
    option (goff.secured) =
        "CARE_PROVIDER_MEMBER_PRE_SWITCH, CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN";
  }
  rpc getEmployeeProfileByLanrId(GetByLanrIDRequest)
      returns (EmployeeProfileResponse) {
    option (goff.secured) =
        "PATIENT, CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN, "
        "CARE_PROVIDER_MEMBER_PRE_SWITCH";
  }
  rpc getEmployeeProfilesByBsnrId(GetByBsnrIdRequest) returns (EmployeeProfilesResponse){
    option (goff.secured) =
        "PATIENT, CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN, "
        "CARE_PROVIDER_MEMBER_PRE_SWITCH";
  }
  rpc getEmployeeProfileByHzvId(GetByHzvIDRequest)
      returns (EmployeeProfileResponse) {
    option (goff.secured) =
        "PATIENT, CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN, "
        "CARE_PROVIDER_MEMBER_PRE_SWITCH";
  }
  rpc getEmployeeProfileByMediId(GetByMediIDRequest)
      returns (EmployeeProfileResponse) {
    option (goff.secured) =
        "PATIENT, CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN, "
        "CARE_PROVIDER_MEMBER_PRE_SWITCH";
  }
  rpc getEmployeeProfileById(EmployeeProfileGetRequest)
      returns (EmployeeProfileResponse) {
    option (goff.secured) = "PATIENT, CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN";
  }
  rpc getEmployeeProfileByIds(GetByIdsRequest)
      returns (EmployeeProfilesResponse) {
    option (goff.secured) =
        "PATIENT, CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN, "
        "CARE_PROVIDER_MEMBER_PRE_SWITCH";
  }
  rpc createEmployeeProfile(EmployeeProfileRequest)
      returns (EmployeeProfileResponse) {
    option (goff.secured) = "SYS_ADMIN, CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN";
  };
  rpc CreateEmployeeProfileWithoutAuth(EmployeeProfileRequest)
      returns (EmployeeProfileResponse) {
    option (goff.secured) = "SYS_ADMIN, CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN";
  };
  rpc updateEmployeeProfile(EmployeeProfileRequest)
      returns (EmployeeProfileResponse) {
    option (goff.secured) = "CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN";
  }
  rpc updateEmployeeStatus(UpdateEmployeeStatusRequest)
      returns (goff.Empty) {
    option (goff.secured) = "CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN";
  }
  rpc deleteEmployeeProfileById(EmployeeProfileDeleteRequest)
      returns (EmployeeProfileDeleteResponse) {
    option (goff.secured) = "CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN";
  }
  rpc GetEmployeeByIds(GetByIdsRequest)returns (EmployeeProfilesResponse) {
    option (goff.secured) =
        "PATIENT, CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN, "
        "CARE_PROVIDER_MEMBER_PRE_SWITCH";
  }
  rpc UpdateDevice (UpdateDeviceRequest) returns (goff.Empty){
      option (goff.secured) = "CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN";
  };
  rpc GetEmployeeByDeviceId (GetEmployeeByDeviceIdRequest) returns (EmployeeProfilesResponse){
      option (goff.secured) = "CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN";
  };
  rpc ResetEmployeePassword (ResetEmployeePasswordRequest) returns (ResetEmployeePasswordResponse){
      option (goff.secured) = "CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN";
  };
  rpc ResetEmployee2FA (ResetEmployee2FARequest) returns (goff.Empty){
      option (goff.secured) = "CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN";
  };
  rpc UpdateDefaultBsnrOfEmployee(app_admin.UpdateDefaultBsnrOfEmployeeRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED";
  }
   rpc HandleEventDeviceChange(app_admin.EventDeviceChange) returns (goff.Empty) {
    option (goff.secured) = "CARE_PROVIDER_ADMIN";
    option (goff.subject) = "api.app.admin.AppAdmin.DeviceChange";
  }
}

// patient profile service

message GetByIdRequest { goff.UUID patientId = 1; }

message PatientProfileCreateRequest {
  goff.UUID patientId = 1;
  string title = 2;
  string lastName = 3 [ (goff.custom) = 'validate:"notblank"' ];
  string firstName = 4 [ (goff.custom) = 'validate:"notblank"' ];
  int64 dateOfBirth = 5
      [ (goff.nullable) = true, (goff.custom) = 'validate:"required"' ];
  patient_profile_common.Gender gender = 6
      [ (goff.custom) = 'validate:"required"' ];
  string street = 7 [ (goff.custom) = 'validate:"notblank"' ];
  string houseNumber = 8 [ (goff.custom) = 'validate:"notblank"' ];
  string postCode = 9 [ (goff.custom) = 'validate:"notblank"' ];
  string cityState = 10 [ (goff.custom) = 'validate:"notblank"' ];
  string country = 11;
  string primaryContact = 12;
  string secondaryContact = 13;
  string contactPerson = 14;
  string email = 15;
  string intendWords = 17;
}

message PatientProfileUpdateRequest {
  goff.UUID patientId = 1 [ (goff.custom) = 'validate:"required"' ];
  string title = 2;
  string firstName = 3 [ (goff.custom) = 'validate:"notblank"' ];
  string lastName = 4 [ (goff.custom) = 'validate:"notblank"' ];
  int64 dateOfBirth = 5
      [ (goff.nullable) = true, (goff.custom) = 'validate:"required"' ];
  patient_profile_common.Gender gender = 6
      [ (goff.custom) = 'validate:"required"' ];
  string street = 7 [ (goff.custom) = 'validate:"notblank"' ];
  string houseNumber = 8 [ (goff.custom) = 'validate:"notblank"' ];
  string postCode = 9 [ (goff.custom) = 'validate:"notblank"' ];
  string cityState = 10 [ (goff.custom) = 'validate:"notblank"' ];
  string country = 11;
  string primaryContact = 12;
  string secondaryContact = 13;
  string contactPerson = 14;
  string email = 15;
  string intendWords = 16;
  string additionalName = 17;
  string job = 18;
  string company = 19;
  string previousDoctor = 20;
  string additionalAddress = 21;
  string postfach = 22;
  string plzPostfach = 23;
  string stadtPostfach = 24;
  IsEmploymentAnswer isEmployee = 26;
  string jobStatus = 27;
  float workingHourInWeek = 28;
  WorkActivity1 workActivity1 = 29;
  WorkActivity2 workActivity2 = 30;
  int64 employmentInformationLastUpdated = 31;
  string specialProblemAtWork = 32;
}
message UpdatePatientMedicalDataRequest {
  goff.UUID patientId = 1 [ (goff.custom) = 'validate:"required"' ];
  patient_profile_common.PatientMedicalData PatientMedicalData = 2;
}

message UpdatePatientMedicalDataResponse {
  goff.UUID MedicalDataHistoryId = 1;
  patient_profile_common.PatientMedicalData PatientMedicalData = 2;
}

message PatientProfile {
  goff.UUID id = 1;
  string firstName = 5;
  string lastName = 6;
  int64 dateOfBirth = 7
      [ (goff.nullable) = true, (goff.custom) = 'validate:"required"' ];
  repeated HpmInformation hpmInformation = 8;
  patient_profile_common.PatientMedicalData patientMedicalData = 9;

  // v2 infor here
  patient_profile_common.PatientInfo PatientInfo = 10;
  int64 EmploymentInfoUpdatedAt = 16 [ (goff.nullable) = true ];
  int64 MedicalDataUpdatedAt = 17 [ (goff.nullable) = true ];
}

message PatientResponse {
  goff.UUID id = 1;
  goff.UUID accountId = 2;
  goff.UUID careProviderId = 3;
}

enum HpmInformationStatus {
  HpmInformationStatus_Active = 0 [ (goff.value) = 'Active' ];
  HpmInformationStatus_InActive = 1 [ (goff.value) = 'InActive' ];
  HpmInformationStatus_Error = 2 [ (goff.value) = 'Error' ];
}

message HpmInformation {
  int64 checkedDate = 1;
  string contractId = 2;
  HpmInformationStatus status = 3;
}

message UpdateHpmInformationRequest {
  goff.UUID patientId = 1 [ (goff.custom) = 'validate:"required"' ];
  repeated HpmInformation hpmInformation = 2
      [ (goff.custom) = 'validate:"required"' ];
}



message GetProfileByIdsRequest { repeated goff.UUID Ids = 1; }

message GetProfileByIdsResponse { repeated PatientProfile PatientProfiles = 1; }

service PatientProfileService {
  rpc CreateProfile(PatientProfileCreateRequest) returns (PatientProfile) {
    option (goff.secured) = "IS_AUTHENTICATED";
  };
  rpc UpdateProfile(PatientProfileUpdateRequest) returns (PatientProfile) {
    option (goff.secured) = "IS_AUTHENTICATED";
  };
  rpc CreatePatientMedicalData(UpdatePatientMedicalDataRequest)
      returns (UpdatePatientMedicalDataResponse) {
    option (goff.secured) = "IS_AUTHENTICATED";
  };
  rpc GetProfileById(GetByIdRequest) returns (PatientProfile) {
    option (goff.secured) = "IS_AUTHENTICATED";
  };
  rpc GetProfileByIds(GetProfileByIdsRequest)
      returns (GetProfileByIdsResponse) {
    option (goff.secured) = "IS_AUTHENTICATED";
  };
  rpc UpdateHpmInformation(UpdateHpmInformationRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED";
  }
}

message EventHealthInsuranceChange {
  string InsuranceNumber = 1;
  int32 IkNumber = 2;
  goff.UUID PatientId = 3;
}
