syntax = "proto3";

package document_management_common;

import "goff.proto";
import "service/domains/patient_profile_common.proto";
import "service/domains/catalog_sdav_common.proto";
import "repo/mvz/document_type_common.proto";

option (goff.pointer_strict_mode) = true;

option go_package = "git.tutum.dev/medi/tutum/ares/service/domains/api/document_management/common";

enum DocumentManagementStatus {
  DocumentManagementStatus_New = 0 [ (goff.value) = "NEW" ];
  DocumentManagementStatus_ReImport = 1 [ (goff.value) = "RE_IMPORT" ];
  DocumentManagementStatus_InProgress = 2 [ (goff.value) = "IN_PROGRESS" ];
  DocumentManagementStatus_Completed = 3 [ (goff.value) = "COMPLETED" ];
  DocumentManagementStatus_Failed = 4 [ (goff.value) = "FAILED" ];
}

enum DocumentNotificationType {
  DocumentNotificationType_LDT = 0 [ (goff.value) = "LDT" ];
  DocumentNotificationType_GDT = 1 [ (goff.value) = "GDT" ];
}

enum MetaDataKey {
  MetaDataKey_PatientLabOrder = 0 [ (goff.value) = "patient_lab_order" ];
  MetaDataKey_PatientLabResult = 1 [ (goff.value) = "patient_lab_result" ];
  MetaDataKey_FileNameUploaded = 2 [ (goff.value) = "file_name_uploaded" ];
  MetaDataKey_LabResultChunkFileName = 3 [ (goff.value) = "lab_result_chunk_file_name" ];
}

message Patient {
    goff.UUID Id = 1;
    string FirstName = 2;
    string LastName = 3;
    patient_profile_common.DateOfBirth DateOfBirth = 4;
    int64 PatientNumber = 5;
    string Gender = 6;
    string FullName = 7;
}

message DocumentManagementSender {
  string Bsnr = 1;
  string Lanr = 2;
}

message DocumentManagementItem {
  goff.UUID Id = 1;
  int64 CompanionFileId = 2 [(goff.nullable) = true];
  string CompanionFilePath = 3 [(goff.nullable) = true];
  Patient Patient = 4 [(goff.nullable) = true];
  catalog_sdav_common.SdavCatalog Sender = 5 [(goff.nullable) = true];
  string DocumentName = 6;
  document_type_common.DocumentType DocumentType = 7 [(goff.nullable) = true];
  string Description = 8;
  DocumentManagementStatus Status = 9;
  int64 ImportedDate = 10;
  string DocumentDirPath = 11;
  string GdtSenderName = 12;
  map<string, string> MetaData = 13 [(goff.nullable) = true];
}

message DocumentManagementModel {
  int64 CompanionFileId = 1 [(goff.nullable) = true];
  string CompanionFilePath = 2 [(goff.nullable) = true];
  goff.UUID PatientId = 3 [(goff.nullable) = true];
  string SenderId = 4 [(goff.nullable) = true];
  document_type_common.DocumentType DocumentType = 5 [(goff.nullable) = true];
  string DocumentName = 6;
  string Description = 7;
  DocumentManagementStatus Status = 8;
  int64 ImportedDate = 9;
  goff.UUID DocumentSettingId = 10 [(goff.nullable) = true];
  int64 GdtImportModTime = 11;
  map<string, string> MetaData = 12 [(goff.nullable) = true];
}

message ReadBy {
  goff.UUID Id = 1;
  string Name = 2;
  int64 ReadAt = 3;
}

// enum DocumentType {
//   DocumentType_GdtImport = 0 [ (goff.value) = "gdt_import" ];
//   DocumentType_GdtExport = 1 [ (goff.value) = "gdt_export" ];
//   DocumentType_ExternalDocument = 2 [ (goff.value) = "external_document" ];
// }

message FolderState {
  map<string, int64> currentState = 1;
  string DirPath = 2;
}
