syntax = "proto3";

package ptv_import_common;

import "goff.proto";
import "service/domains/common.proto";
import "service/domains/patient_profile_common.proto";
option (goff.pointer_strict_mode) = true;

option go_package = "git.tutum.dev/medi/tutum/ares/service/domains/ptv_import/common";

option (goff.nats_subject) = "api.service.domains";

enum ImportParticipantsType {
    ImportType_Auto = 0 [(goff.value) = "autoImportParticipants"];
    ImportType_Conflict = 1 [(goff.value) = "conflictParticipants"];
    ImportType_Missing = 2 [(goff.value) = "missingParticipants"];
}

message GetCodePtvImportByDoctorRequest {
    goff.UUID doctorId = 1  [(goff.custom) = 'validate:"required"'];
    int64 year = 2  [(goff.custom) = 'validate:"required"'];
    int64 quarter = 3  [(goff.custom) = 'validate:"required"'];
}

message GetCodePtvImportByDoctorResponse {
    string code = 1;
}

message GetPtvContractByDoctorRequest {
    goff.UUID doctorId = 1  [(goff.custom) = 'validate:"required"'];
    int64 year = 2  [(goff.custom) = 'validate:"required"'];
    int64 quarter = 3  [(goff.custom) = 'validate:"required"'];
    string code = 4  [(goff.custom) = 'validate:"required"'];
}

message GetPtvContractByDoctorResponse {
    repeated ImportContract contracts = 1;
}

enum ImportContractStatus {
    ImportContractStatus_New = 0 [(goff.value) = "NEW"];
    ImportContractStatus_InProgress = 1 [(goff.value) = "INPROGRESS"];
    ImportContractStatus_Done = 2 [(goff.value) = "DONE"];
    ImportContractStatus_Pending = 3  [(goff.value) = "PENDING"];
}

message ImportContract {
    string contractId = 1 [(goff.custom) = 'bson:"contractId"'];
    string documentId = 2 [(goff.custom) = 'bson:"documentId"'];
    int64 version = 3;
    ImportContractStatus status = 4;
    FileInfo fileInfo = 5 [(goff.nullable) = true, (goff.custom) = 'bson:"fileInfo"'];
    int64 year = 6;
    int64 quarter = 7;
}

message PtvImport {
    goff.UUID DoctorId = 1 [(goff.custom) = 'bson:"doctorId"'];
    string RetrievalCode = 2 [(goff.custom) = 'bson:"retrievalCode"'];
    // int64 Year = 3;
    // int64 Quarter = 4;
    // ImportContract Contract = 5 [(goff.nullable) = true];
}

message GetParticipantsByDoctorRequest {
    goff.UUID doctorId = 1;
    string code = 2;
    string documentId = 3;
    int64 year = 4;
    int64 quarter = 5;
    int64 version = 6;
    string ContractId = 7;
    bool NewSession = 8;
}

message GetParticipantsByDoctorResponse {
    goff.UUID id = 1;
    goff.UUID doctorId = 2;
    string contractId = 3;
    string documentId = 4;
    repeated ParticipantDecision autoImportParticipants = 5;
    repeated ParticipantDecision conflictParticipants = 6;
    repeated ParticipantDecision missingParticipants = 7;
    int64 beforeParticipantCount = 8;
    int64 afterParticipantCount = 9;
    int64 year = 10;
    int64 quarter = 11;
}

message ParticipantDecision {
    goff.UUID id = 1;
    goff.UUID patientId = 2 [(goff.custom) = 'bson:"patientId"'];
    IkNumber ikNumber = 3 [(goff.custom) = 'bson:"ikNumber"'];
    InsuranceNumber insuranceNumber = 4 [(goff.custom) = 'bson:"insuranceNumber"'];
    Status status = 5;
    FirstName firstName = 6 [(goff.custom) = 'bson:"firstName"'];
    LastName lastName = 7 [(goff.custom) = 'bson:"lastName"'];
    Reason reason = 8;
    Gender gender = 9;
    Dob dob = 10;
    ContractBeginDate contractBeginDate = 11 [(goff.custom) = 'bson:"contractBeginDate"'];
    ContractEndDate contractEndDate = 12 [(goff.custom) = 'bson:"contractEndDate"'];
    bool MarkAsDone = 13 [(goff.custom) = 'bson:"markAsDone"'];
    TypeGroupDecision typeGroupDecision = 14 [(goff.custom) = 'bson:"typeGroupDecision"'];
    bool isProccessing = 15 [(goff.custom) = 'bson:"isProccessing"'];
    goff.UUID ppId = 16 [(goff.nullable) = true, (goff.custom) = 'bson:"ppId"'];
    bool conflictResolved = 17 [(goff.custom) = 'bson:"conflictResolved"'];
    repeated string hints = 18 [(goff.nullable) = true, (goff.custom) = 'bson:"hints"'];
}

message IkNumber {
    NumberSelection localIkNumber = 1 [(goff.custom) = 'bson:"localIkNumber"'];
    NumberSelection hpmIkNumber = 2 [(goff.custom) = 'bson:"hpmIkNumber"'];
}

message InsuranceNumber {
    StringSelection localInsuranceNumber = 1 [(goff.custom) = 'bson:"localInsuranceNumber"'];
    StringSelection hpmInsuranceNumber = 2 [(goff.custom) = 'bson:"hpmInsuranceNumber"'];
}

message Status {
    StatusSelection localStatus = 1 [(goff.custom) = 'bson:"localStatus"'];
    StatusSelection hpmStatus = 2 [(goff.custom) = 'bson:"hpmStatus"'];
}

message FirstName {
    StringSelection localFirstName = 1 [(goff.custom) = 'bson:"localFirstName"'];
    StringSelection hpmFirstName = 2 [(goff.custom) = 'bson:"hpmFirstName"'];
}

message LastName {
    StringSelection localLastName = 1 [(goff.custom) = 'bson:"localLastName"'];
    StringSelection hpmLastName = 2 [(goff.custom) = 'bson:"hpmLastName"'];
}

message Reason {
    StringSelection localReason = 1 [(goff.custom) = 'bson:"localReason"'];
    StringSelection hpmReason = 2 [(goff.custom) = 'bson:"hpmReason"'];
}

message Gender {
    GenderSelection localGender = 1 [(goff.custom) = 'bson:"localGender"'];
    GenderSelection hpmGender = 2 [(goff.custom) = 'bson:"hpmGender"'];
}

message Dob {
    NumberSelection localDOB = 1 [(goff.custom) = 'bson:"localDOB"'];
    NumberSelection hpmDOB = 2 [(goff.custom) = 'bson:"hpmDOB"'];
}

message ContractBeginDate {
    NumberSelection localContractBeginDate = 1 [(goff.custom) = 'bson:"localContractBeginDate"'];
    NumberSelection hpmContractBeginDate = 2 [(goff.custom) = 'bson:"hpmContractBeginDate"'];
}

message ContractEndDate {
    NumberSelection localContractEndDate = 1 [(goff.custom) = 'bson:"localContractEndDate"'];
    NumberSelection hpmContractEndDate = 2 [(goff.custom) = 'bson:"hpmContractEndDate"'];
}

enum PTVImportType {
    ImportType_Basic = 0 [(goff.value) = "basic"];
    ImportType_Full = 1 [(goff.value) = "full"];
}

message ImportParticipantsRequest {
    goff.UUID id = 1;
    goff.UUID doctorId = 2;
    string contractId = 3;
    string documentId = 4;
    repeated ParticipantDecision autoImportParticipants = 5;
    repeated ParticipantDecision conflictParticipants = 6;
    repeated ParticipantDecision missingParticipants = 7;
    int64 year = 8;
    int64 quarter = 9;
    PTVImportType importType = 10;
}

message GetListPtvImportHistoryRequest {
    common.Pagination pagination = 1 [(goff.custom) = 'validate:"required"'];
}

message GetListPtvImportHistoryResponse {
    repeated PtvImportHistory data = 1;
    int64 total = 2;
}

message PtvImportHistory {
    goff.UUID id = 1 [(goff.custom) = 'bson:"_id"'];
    goff.UUID doctorId = 2 [(goff.custom) = 'bson:"doctorId"'];
    string contractId = 3 [(goff.custom) = 'bson:"contractId"'];
    string documentId = 4 [(goff.custom) = 'bson:"documentId"'];
    repeated ParticipantDecision autoImportParticipants = 5 [(goff.custom) = 'bson:"autoImportParticipants"'];
    repeated ParticipantDecision conflictParticipants = 6 [(goff.custom) = 'bson:"conflictParticipants"'];
    repeated ParticipantDecision missingParticipants = 7 [(goff.custom) = 'bson:"missingParticipants"'];
    // string protocolFileUrl = 8[(goff.nullable) = true, (goff.custom) = 'bson:"protocolFileUrl"'];
    goff.UUID importerId = 9 [(goff.custom) = 'bson:"importerId"'];
    int64 createTime = 10 [(goff.custom) = 'bson:"createTime"'];
    int64 updateTime = 11 [(goff.custom) = 'bson:"updateTime"'];
    int64 year = 12;
    int64 quarter = 13;
    ImportContractStatus status = 14 [(goff.custom) = 'bson:"status"'];
    int64 beforeParticipantCount = 15;
    int64 afterParticipantCount = 16;
}

message ImportTestDataRequest {
    goff.UUID doctorId = 1;
    string xmlData = 2;
}

enum TypeGroupDecision {
    GeneralGroupUnchanged = 0 [(goff.value) = "Unchanged"];
    GeneralGroupNew = 1 [(goff.value) = "New"];
    GeneralGroupTerminated = 2 [(goff.value) = "Terminated"];
    GeneralGroupRequested = 3 [(goff.value) = "Requested"];
    GeneralGroupRejected = 4 [(goff.value) = "Rejected"];
    SpecialGroup = 5 [(goff.value) = "SpecialGroup"];
    MissingGroupPTV = 6 [(goff.value) = "MissingGroupPTV"];
    MissingGroupIV = 7 [(goff.value) = "MissingGroupIV"];
}

message StringSelection {
    string value = 1;
    bool selected = 2;
}

message NumberSelection {
    int64 value = 1 [(goff.nullable) = true];
    bool selected = 2;
}

message GenderSelection {
    patient_profile_common.Gender value = 1;
    bool selected = 2;
}

message StatusSelection {
    PatientParticipationStatus value = 1;
    bool selected = 2;
}

message FileInfo {
    string BucketName = 1 [(goff.custom) = 'bson:"bucketName"'];
	string ObjectName = 2 [(goff.custom) = 'bson:"objectName"'];
	int64 CreatedDate = 3 [(goff.custom) = 'bson:"createdDate"'];
}

enum PatientParticipationStatus {
    PatientParticipation_Requested = 0 [(goff.value) = "REQUESTED"];
    PatientParticipation_Rejected = 1 [(goff.value) = "REJECTED"];
    PatientParticipation_Active = 2 [(goff.value) = "ACTIVE"];
    PatientParticipation_Cancelled = 3 [(goff.value) = "CANCELLED"];
    PatientParticipation_Terminated = 4 [(goff.value) = "TERMINATED"];
    PatientParticipation_Faulty = 5 [(goff.value) = "FAULTY"];
}