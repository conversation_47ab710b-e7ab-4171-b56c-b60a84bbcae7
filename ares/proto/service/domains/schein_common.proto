syntax = "proto3";
package schein_common;

import "goff.proto";
import "service/domains/patient_profile_common.proto";
import "service/domains/patient_participation.proto";
import "service/domains/common.proto";
option go_package = "git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common";
option (goff.pointer_strict_mode) = true;

enum MainGroup {
  HZV = 0;
  FAV = 1;
  KV = 2;
  BG = 3; // incident
  PRIVATE = 4;
  IGEL = 5;
}

enum EncounterCase {
  AB = 0;
  PB = 1;
  NOT = 2;
  PRE_ENROLLMENT = 3;
}

enum TreatmentCase {
  TreatmentCaseCustodian = 0;
  TreatmentCaseDelegate = 1;
  TreatmentCaseDeputy = 2;
  TreatmentCasePreParticipate = 3;
}

enum Laterality {
  U = 0;
  L = 1;
  R = 2;
  B = 3;
}

enum Certainty {
  G = 0;
  V = 1;
  Z = 2;
  A = 3;
}

enum Sources {
  Imported = 0;
  Composer = 1;
  Timeline = 2;
}

enum TreatmentCaseNames {
  TCKvOutpatient = 0 [ (goff.value) = "0101" ];
  TCKvReferral = 1 [ (goff.value) = "0102" ];
  TCKvHospital = 2 [ (goff.value) = "0103" ];
  TCKvEmergency = 3 [ (goff.value) = "0104" ];
  TCKvSpaMedical = 4 [ (goff.value) = "0109" ];
  TCKvSadtOutpatient = 5 [ (goff.value) = "sadt1" ];
  TCKvSadtReferral = 6 [ (goff.value) = "sadt2" ];
  TCKvSadtHospital = 7 [ (goff.value) = "sadt3" ];

  TCBgCase = 8 [ (goff.value) = "BG" ];
  TCPrivateCase = 9 [ (goff.value) = "PRIVATE" ];
}

enum ValidationType {
  ScheinValidation = 0 [ (goff.value) = "ScheinValidation" ];
  ServiceValidation = 1 [ (goff.value) = "ServiceValidation" ];
  DiagnoseValidation = 2 [ (goff.value) = "DiagnoseValidation" ];
}

enum CreateScheinErrorCode {
  CostUnitIsNotAvailableInKvRegion = 0;
  CostUnitHasExpired = 1;
  CostUnitIsTerminated = 2;
  TheBillingAreaIsOutOfValidityDateRange = 3;
  WarningKvx3SKTAddtional = 4;
  TheBillingAreaIsInvalid = 5;
}

message PatientSnapshot {
  goff.UUID PatientId = 1;
  patient_profile_common.PatientInfo PatientInfo = 2;
  int64 UpdatedAt = 3;
}

message ReferralDoctor {
  string Bsnr = 1;
  string Lanr = 2;
}

message MarkAsReferralRequest {
  goff.UUID ScheinId = 1;
  ReferralDoctor ReferralDoctor = 2;
}

message RemoveReferralRequest { goff.UUID ScheinId = 1; }

message Schein {
  string ScheinMainGroup = 1 [ (goff.custom) = 'bson:"scheinMainGroup"' ];
  string KvScheinSubGroup = 2 [ (goff.custom) = 'bson:"kvScheinSubGroup"', (goff.nullable) = true ];
  string KvTreatmentCase = 3 [ (goff.custom) = 'bson:"kvTreatmentCase"' ];
  int32 G4101Quarter = 4  [ (goff.custom) = 'bson:"g4101Quarter"', (goff.nullable) = true ];
  int32 G4101Year = 5 [ (goff.custom) = 'bson:"g4101Year"', (goff.nullable) = true ];
  string TariffType = 6 [ (goff.custom) = 'bson:"tariffType"', (goff.nullable) = true ];
  string BgType = 7 [ (goff.custom) = 'bson:"bgType"', (goff.nullable) = true ];
  int64 BgAccidentDate = 8  [ (goff.custom) = 'bson:"bgAccidentDate"', (goff.nullable) = true ];
  string BgAccidentTime = 9 [ (goff.custom) = 'bson:"bgAccidentTime"', (goff.nullable) = true ];
  string BgWorkingTimeFrom = 10 [ (goff.custom) = 'bson:"bgWorkingTimeFrom"', (goff.nullable) = true ];
  string BgWorkingTimeTo = 11 [ (goff.custom) = 'bson:"bgWorkingTimeTo"', (goff.nullable) = true ];
  string BgEmployerName = 12  [ (goff.custom) = 'bson:"bgEmployerName"', (goff.nullable) = true];
  string BgEmployerStreet = 13  [ (goff.custom) = 'bson:"bgEmployerStreet"', (goff.nullable) = true];
  string BgEmployerHousenumber = 14 [(goff.custom) = 'bson:"bgEmployerHousenumber"',(goff.nullable) = true];
  string BgEmployerPostcode = 15  [ (goff.custom) = 'bson:"bgEmployerPostcode"', (goff.nullable) = true ];
  string BgEmployerCity = 16  [ (goff.custom) = 'bson:"bgEmployerCity"', (goff.nullable) = true ];
  string BgEmployerCountry = 17 [(goff.custom) = 'bson:"bgEmployerCountry"', (goff.nullable) = true ];
  goff.UUID InsuranceId = 18 [(goff.custom) = 'validate:"required"'];
  int32 IkNumber = 19 [ (goff.nullable) = true ];
  PatientSnapshot PatientSnapshot = 20 [(goff.custom) = 'fake:"skip"', (goff.nullable) = true];
}

message ScheinDetail {
  string G4122 = 1 [ (goff.nullable) = true ];
  string G4106 = 5 [ (goff.nullable) = true ];
  int64 G4102 = 6 [ (goff.nullable) = true ];
  int64 Ad4206 = 7 [ (goff.nullable) = true ];
  int64 Ad4125From = 8
      [ (goff.custom) = 'bson:"ad4125From"', (goff.nullable) = true ];
  int64 Ad4125To = 9
      [ (goff.custom) = 'bson:"ad4125To"', (goff.nullable) = true ];
  string Ad4126 = 10 [ (goff.nullable) = true ];
  string Ad4124 = 11 [ (goff.nullable) = true ];
  bool Ad4204 = 12 [ (goff.nullable) = true ];
  bool Ad4202 = 13 [ (goff.nullable) = true ];
  string Re4241 = 14 [(goff.custom) = 'validate:"omitempty,ne=888888800"', (goff.nullable) = true ];
  string Re4248 = 15 [ (goff.nullable) = true ];
  string Re4249 = 16 [ (goff.nullable) = true ];
  string Re4242 = 17 [ (goff.custom) = 'validate:"omitempty,ne=888888800"',(goff.nullable) = true ];
  string Re4225 = 18 [ (goff.nullable) = true ];
  repeated DateTimeFromTo Re4233 = 19
      [ (goff.custom) = 'bson:"re4233"', (goff.nullable) = true ];
  string Re4226 = 21 [ (goff.nullable) = true ];
  string Re4221 = 22 [ (goff.nullable) = true ];
  string Re4219 = 23 [ (goff.nullable) = true ];
  string Re4218 = 24 [ (goff.nullable) = true ];
  string Re4217 = 25 [ (goff.nullable) = true ];
  string Re4209 = 26 [ (goff.nullable) = true ];
  string Re4208 = 27 [ (goff.nullable) = true ];
  string Re4207 = 28 [ (goff.nullable) = true ];
  string Re4205 = 29 [ (goff.nullable) = true ];
  string Re4220 = 30 [ (goff.nullable) = true ];
  string Re4229 = 31 [ (goff.nullable) = true ];
  int32 Ps4257 = 32 [ (goff.nullable) = true ];
  int32 Ps4299 = 33 [ (goff.custom) = 'validate:"omitempty,ne=888888800"',(goff.nullable) = true ];
  repeated string Ps4256 = 34;
  repeated string Ps4253 = 35;
  int32 Ps4254 = 36 [ (goff.nullable) = true ];
  int32 Ps4255 = 37 [ (goff.nullable) = true ];
  int32 Ps4252 = 38 [ (goff.nullable) = true ];
  int32 Ps4251 = 39 [ (goff.nullable) = true ];
  int64 Ps4247 = 40 [ (goff.nullable) = true ];
  repeated string Ps4244 = 41;
  string Ps4245 = 42 [ (goff.nullable) = true ];
  string Ps4246 = 43 [ (goff.nullable) = true ];
  int64 Ps4235 = 44 [ (goff.nullable) = true ];
  bool Ps4236 = 45 [ (goff.nullable) = true ]; // Keep it
  bool Ps4234 = 46 [ (goff.nullable) = true ]; // Keep it
  bool Ps4250 = 47 [ (goff.nullable) = true ];
  string Re4243 = 48 [ (goff.nullable) = true ];
  int32 G4104 = 49 [ (goff.nullable) = true ];
  string TsvgContactType = 51
      [ (goff.custom) = 'bson:"tsvgContactType"', (goff.nullable) = true ];
  string TsvgInfor = 52
      [ (goff.custom) = 'bson:"tsvgInfor"', (goff.nullable) = true ];
  string TsvgTranferCode = 53
      [ (goff.custom) = 'bson:"tsvgTranferCode"', (goff.nullable) = true ];
  int64 TsvgContactDate = 54
      [ (goff.custom) = 'bson:"tsvgContactDate"', (goff.nullable) = true ];
  int64 PausingStartDate = 55 [ (goff.custom) = 'bson:"pausingStartDate"', (goff.nullable) = true ];
  int64 PausingEndDate = 56 [ (goff.custom) = 'bson:"pausingEndDate"', (goff.nullable) = true ];
  bool IsInsuranceInformedTherapy = 57 [ (goff.custom) = 'bson:"isInsuranceInformedTherapy"', (goff.nullable) = true ];
  string Ad4123 = 58 [(goff.nullable) = true ];
  int64 Re4214 = 59 [ (goff.nullable) = true ];
  repeated Psychotherapy Psychotherapy = 61;
}

message DateTimeFromTo {
  int64 from = 1;
  int64 to = 2;
}

message GroupServicesCode {
  string ServiceCode = 1;
  int32 AmountBilled = 2;
}

message GroupServicesCodeBefore2017 {
  string ServiceCode = 1;
  int32 AmountApproval = 2;
  int32 AmountBilled = 3;
}

message Psychotherapy {
  int64 Ps4235 = 1 [ (goff.nullable) = true, (goff.custom) = 'validate:"required"' ];
  int64 Ps4247 = 2 [ (goff.nullable) = true ];
  int64 Ps4245 = 4 [ (goff.nullable) = true ];
  string Ps4246 = 5 [ (goff.nullable) = true ];
  int64 PausingStartDate = 6 [ (goff.custom) = 'bson:"pausingStartDate"', (goff.nullable) = true ];
  int64 PausingEndDate = 7 [ (goff.custom) = 'bson:"pausingEndDate"', (goff.nullable) = true ];
  bool IsInsuranceInformedTherapy = 8 [ (goff.custom) = 'bson:"isInsuranceInformedTherapy"', (goff.nullable) = true ];
  int32 Ps4251 = 9 [ (goff.nullable) = true ];
  bool Ps4250 = 10 [ (goff.nullable) = true ];
  int32 Ps4299 = 11 [ (goff.custom) = 'validate:"omitempty,ne=888888800"',(goff.nullable) = true ];
  int32 Ps4254 = 13 [ (goff.nullable) = true ];
  int32 Ps4257 = 15 [ (goff.nullable) = true ];
  int32 Ps4255 = 16 [ (goff.nullable) = true ];
  int32 Ps4252 = 17[ (goff.nullable) = true ];
  repeated GroupServicesCode GroupServicesCode = 18 [ (goff.custom) = 'bson:"groupServicesCode"'];
  repeated GroupServicesCode GroupCareGiver = 19 [ (goff.custom) = 'bson:"groupCareGiver"'];
  bool IsReason = 20;
  goff.UUID Id = 21 [(goff.nullable) = true ];
  goff.UUID TakeOverId = 22 [(goff.nullable) = true ];
  repeated GroupServicesCodeBefore2017 GroupServicesCodeBefore2017 = 23 [ (goff.custom) = 'bson:"groupServicesCodeBefore2017"'];
}

message UpdateScheinRequest {
  goff.UUID ScheinId = 1;
  goff.UUID PatientId = 2;
  goff.UUID DoctorId = 3;
  MainGroup ScheinMainGroup = 4;
  TreatmentCaseNames KvTreatmentCase = 5;
  string KvScheinSubGroup = 6 [ (goff.nullable) = true ];
  int32 G4101Year = 7 [ (goff.nullable) = true ];
  int32 G4101Quarter = 8 [ (goff.nullable) = true ];

  string TariffType = 9 [ (goff.nullable) = true ];
  string BgType = 10 [ (goff.nullable) = true ];

  int64 BgAccidentDate = 11 [ (goff.nullable) = true ];
  string BgAccidentTime = 12 [ (goff.nullable) = true ];
  string BgWorkingTimeFrom = 13 [ (goff.nullable) = true ];
  string BgWorkingTimeTo = 14 [ (goff.nullable) = true ];
  string BgEmployerName = 15 [ (goff.nullable) = true ];
  string BgEmployerStreet = 16 [ (goff.nullable) = true ];
  string BgEmployerHousenumber = 17 [ (goff.nullable) = true ];
  string BgEmployerPostcode = 18 [ (goff.nullable) = true ];
  string BgEmployerCity = 19 [ (goff.nullable) = true ];
  string BgEmployerCountry = 20 [ (goff.nullable) = true ];

  string HzvContractId = 21 [ (goff.nullable) = true ];

  ScheinDetail ScheinDetails = 22 [ (goff.nullable) = true ];

  goff.UUID InsuranceId = 23;
  bool ExcludeFromBilling = 24;
  goff.UUID AssignedToBsnrId = 25 [(goff.nullable) = true];
}

message CheckEmptyScheinRequest { goff.UUID scheinId = 1; }

message CheckExistKVScheinCurrentQuarterRequest {
  goff.UUID patientId = 1;
  int32 quarter = 2 [ (goff.nullable) = true ];
  int32 year = 3 [ (goff.nullable) = true ];
}

message CheckExistKVScheinCurrentQuarterResponse { bool isExist = 1; }

message CheckEmptyScheinResponse { bool isEmpty = 1; }

message DeleteScheinRequest {
  goff.UUID scheinId = 1;
  goff.UUID patientId = 2;
}

message GetScheinDetailRequest { goff.UUID patientId = 1; }

message GetScheinDetailByIdRequest { goff.UUID scheinId = 1; }
message GetScheinDetailByIdsRequest { repeated goff.UUID scheinIds = 1; }

message GetScheinDetailByIdResponse {
  goff.UUID PatientId = 1;
  goff.UUID DoctorId = 2;
  MainGroup ScheinMainGroup = 3;
  TreatmentCaseNames KvTreatmentCase = 4;
  string KvScheinSubGroup = 5 [ (goff.nullable) = true ];
  int32 G4101Year = 6 [ (goff.nullable) = true ];
  int32 G4101Quarter = 7 [ (goff.nullable) = true ];

  string TariffType = 8 [ (goff.nullable) = true ];
  string BgType = 9 [ (goff.nullable) = true ];

  int64 BgAccidentDate = 10 [ (goff.nullable) = true ];
  string BgAccidentTime = 11 [ (goff.nullable) = true ];
  string BgWorkingTimeFrom = 12 [ (goff.nullable) = true ];
  string BgWorkingTimeTo = 13 [ (goff.nullable) = true ];
  string BgEmployerName = 14 [ (goff.nullable) = true ];
  string BgEmployerStreet = 15 [ (goff.nullable) = true ];
  string BgEmployerHousenumber = 16 [ (goff.nullable) = true ];
  string BgEmployerPostcode = 17 [ (goff.nullable) = true ];
  string BgEmployerCity = 18 [ (goff.nullable) = true ];
  string BgEmployerCountry = 19 [ (goff.nullable) = true ];

  string HzvContractId = 20 [ (goff.nullable) = true ];

  ScheinDetail ScheinDetails = 21 [ (goff.nullable) = true ];
  goff.UUID scheinId = 22;
  goff.UUID InsuranceId = 23;
  bool ExcludeFromBilling = 24;
  bool MarkedAsBilled = 25;

  string ChargeSystemId = 26 [ (goff.nullable) = true ];
  SvScheinDetail svScheinDetail = 27 [ (goff.nullable) = true ];
  goff.UUID AssignedToBsnrId = 28 [ (goff.nullable) = true ];
}

message GetScheinDetailByIdsResponse {
  repeated GetScheinDetailByIdResponse Data = 1;
}

message GetScheinDetailResponse { repeated goff.UUID scheinIds = 1; }

message ScheinItem {
  goff.UUID ScheinId = 1;
  MainGroup ScheinMainGroup = 2;
  TreatmentCaseNames KvTreatmentCase = 3;
  string KvScheinSubGroup = 4 [ (goff.nullable) = true ];
  int32 G4101Year = 5 [ (goff.nullable) = true ];
  int32 G4101Quarter = 6 [ (goff.nullable) = true ];
  string HzvContractId = 7 [ (goff.nullable) = true ];
  int64 createdTime = 8;
  bool MarkedAsBilled = 9;
  int64 G4110 = 10 [ (goff.nullable) = true ];
  goff.UUID InsuranceId = 11;
  bool ExcludeFromBilling = 12;
  goff.UUID UpdatedBy = 13 [(goff.nullable) = true];
  int64 UpdatedAt = 14 [(goff.nullable) = true];
  string ChargeSystemId = 15 [ (goff.nullable) = true ];
  string TsvgContactType = 16 [ (goff.nullable) = true ];
  string TsvgInfor = 17 [ (goff.nullable) = true ];
  string TsvgTranferCode = 18 [ (goff.nullable) = true ];
  int64 TsvgContactDate = 19 [ (goff.nullable) = true ];
  ScheinDetail ScheinDetail = 20;
  bool IsTechnicalSchein = 21 [ (goff.nullable) = true ];
  PatientSnapshot PatientSnapshot = 22 [(goff.nullable) = true];
  goff.UUID DoctorId = 23;
  int64 IssueDate = 24 [(goff.nullable) = true];
  ScheinStatus ScheinStatus = 25 [(goff.nullable) = true];
  string InvoiceNumber = 26 [(goff.nullable) = true];
  ReferralDoctor ReferralDoctor = 27 [(goff.nullable) = true];
  bool IsGeneral = 28 [(goff.nullable) = true];
  int64 AccidentDate = 29 [(goff.nullable) = true];
  string JobOccupation = 30 [(goff.nullable) = true];
  int64 ArrivalDate = 31 [(goff.nullable) = true];
  int64 WorkingTimeStart = 32;
  int64 WorkingTimeEnd = 33;
  patient_profile_common.CompanyAddress CompanyAddress = 34;
  BgScheinDetail BgScheinDetail = 35 [(goff.nullable) = true];
}

message GetScheinsOverviewRequest {
  goff.UUID patientId = 1[(goff.custom) = 'validate:"required"'];
  common.YearQuarter Quarter = 2 [(goff.nullable) = true];
  MainGroup MainGroup = 3 [(goff.nullable) = true];
}
message GetScheinsOverviewResponse { repeated ScheinItem ScheinItems = 1;   repeated patient_profile_common.InsuranceInfo InsuranceInfo = 2; }

message MarkNotBilledRequest { goff.UUID scheinId = 1; }
message ScheinFields {
  goff.UUID scheinId = 1;
  EncounterCase hzvEncounterCase = 2
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  TreatmentCase hzvTreatmentCase = 3
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
}

message PatientDiagnose {
  goff.UUID id = 1;
  string code = 2;
  string diagnoseDescription = 3;
  bool chronicDiagnose = 4;
  int64 validUntil = 5
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  Certainty certainty = 6
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  Laterality laterality = 7
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  goff.UUID patientId = 8;
  string freeText = 9;
  string command = 10;
  Sources source = 11;
  goff.UUID treatmentDoctorId = 12;
  int64 createdTime = 13;
  goff.UUID createdUser = 14;
  int64 updatedTime = 15;
  goff.UUID updatedUser = 16;
  int64 encounterDate = 17;
}

message ScheinDiagnose {
  goff.UUID id = 1;
  string code = 2;
  string diagnoseDescription = 3;
  bool chronicDiagnose = 4;
  int64 validUntil = 5
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  Certainty certainty = 6
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  Laterality laterality = 7
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  goff.UUID patientId = 8;
  string freeText = 9;
  string command = 10;
  Sources source = 11;
  goff.UUID treatmentDoctorId = 12;
  int64 createdTime = 13;
  goff.UUID createdUser = 14;
  int64 updatedTime = 15;
  goff.UUID updatedUser = 16;
  int64 encounterDate = 17;
  goff.UUID patientDiagnoseId = 18;
  goff.UUID scheinId = 19;
  EncounterCase hzvEncounterCase = 20;
  TreatmentCase hzvTreatmentCase = 21;
}

message CreateDiagnoseRequest {
  string code = 1;
  string diagnoseDescription = 2;
  bool chronicDiagnose = 3;
  int64 validUntil = 4
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  Certainty certainty = 5
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  Laterality laterality = 6
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  goff.UUID patientId = 7;
  string freeText = 8;
  string command = 9;
  Sources source = 10;
  goff.UUID treatmentDoctorId = 11;
  repeated ScheinFields scheinDiagnoseFields = 12
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  goff.UUID id = 13;
}

message CreateDiagnoseResponse { goff.UUID diagnoseId = 1; }

message UpdateDiagnoseRequest {
  string code = 1;
  string diagnoseDescription = 2;
  bool chronicDiagnose = 3;
  int64 validUntil = 4
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  Certainty certainty = 5
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  Laterality laterality = 6
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  goff.UUID patientId = 7;
  string freeText = 8;
  string command = 9;
  Sources source = 10;
  goff.UUID diagnoseId = 11;
  goff.UUID treatmentDoctorId = 12;
  repeated ScheinFields scheinDiagnoseFields = 13
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
}

message DeleteDiagnoseRequest { goff.UUID diagnoseId = 1; }

message GetDiagnoseByIdRequest { goff.UUID diagnoseId = 1; }

message GetDiagnoseByIdResponse { PatientDiagnose diagnose = 1; }

message GetScheinDiagnoseByDiagnoseIdRequest { goff.UUID diagnoseId = 1; }

message GetScheinDiagnoseByDiagnoseIdResponse {
  repeated ScheinDiagnose scheinDiagnoses = 1;
}

message PatientService {
  goff.UUID id = 1;
  string code = 2;
  string serviceDescription = 3;
  bool isPreParticipate = 4
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string hzvReferralDoctorBsnr = 5
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string hzvReferralDoctorLanr = 6
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string hzvCareDoctorFacilityName = 7
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string hzvCareDoctorFacilityOrt = 8
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  goff.UUID patientId = 9;
  string freeText = 10;
  string command = 11;
  Sources source = 12;
  goff.UUID treatmentDoctorId = 13;
  goff.UUID createdUser = 14;
  int64 createdTime = 15;
  goff.UUID updatedUser = 16;
  int64 updatedTime = 17;
  repeated ScheinFields scheinServiceFields = 18
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
}

message CreateServiceRequest {
  string code = 1;
  string serviceDescription = 2;
  bool isPreParticipate = 3
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string hzvReferralDoctorBsnr = 4
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string hzvReferralDoctorLanr = 5
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string hzvCareDoctorFacilityName = 6
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string hzvCareDoctorFacilityOrt = 7
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  goff.UUID patientId = 8;
  string freeText = 9;
  string command = 10;
  Sources source = 11;
  goff.UUID treatmentDoctorId = 12;
  repeated ScheinFields scheinServiceFields = 13
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  goff.UUID id = 14;
}

message CreateServiceResponse { goff.UUID serviceId = 1; }

message UpdateServiceRequest {
  goff.UUID id = 1;
  string code = 2;
  string serviceDescription = 3;
  bool isPreParticipate = 4
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string hzvReferralDoctorBsnr = 5
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string hzvReferralDoctorLanr = 6
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string hzvCareDoctorFacilityName = 7
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string hzvCareDoctorFacilityOrt = 8
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string freeText = 9;
  string command = 10;
  Sources source = 11;
  goff.UUID treatmentDoctorId = 12;
  repeated ScheinFields scheinServiceFields = 13
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
}

message DeleteServiceRequest { goff.UUID serviceId = 1; }

message GetPatientServiceByIdRequest { goff.UUID patientServiceId = 1; }

message GetPatientServiceByIdResponse { PatientService patientService = 1; }

message RunValidationForScheinsRequest {
  repeated goff.UUID scheinIds = 1
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  goff.UUID serviceId = 2
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  goff.UUID diagnoseId = 3
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  ValidationType type = 4;
}

enum Assigndiagnosis {
  AssigndiagnosisAll = 0 [ (goff.value) = "all" ];
  AssigndiagnosisPermanent = 1 [ (goff.value) = "permanent" ];
  AssigndiagnosisManually = 2 [ (goff.value) = "manually" ];
}

message SaveSettingRequest {
  Assigndiagnosis AssignDiagnosisCreatedschein = 1;
  bool PreviouslySelected = 2;
  string DefaultTreatmentcase = 3 [ (goff.nullable) = true ];
  string DefaultSubgroup = 4 [ (goff.nullable) = true ];
  bool AutoCreatekvscheinHzvfav = 5;
  bool ComposerAutoSelectscheinDiagnosis = 6;
  string ShowWhenPatientCardIsRead = 7;
  bool IncludeCurrentPatient = 8;
  bool ShowHintSpecialGroup09 = 9;
  bool HideHintForTfSG = 10;
}

message GetSettingResponse {
  Assigndiagnosis AssignDiagnosisCreatedschein = 1;
  bool PreviouslySelected = 2;
  string DefaultTreatmentcase = 3 [ (goff.nullable) = true ];
  string DefaultSubgroup = 4 [ (goff.nullable) = true ];
  bool AutoCreatekvscheinHzvfav = 5;
  bool ComposerAutoSelectscheinDiagnosis = 6;
  string ShowWhenPatientCardIsRead = 7;
  bool IncludeCurrentPatient = 8;
  bool ShowHintSpecialGroup09 = 9;
  bool HideHintForTfSG = 10;
}

message OrderValue {
  string Name = 1;
  string Value = 2;
}
message GetOrderListResponse { repeated OrderValue OrderValues = 1; }

message SaveOrderListRequest { repeated OrderValue OrderValues = 1; }

message GetSelectedTreatmentCaseSubgroupRequest { goff.UUID patientId = 1; }
message GetSelectedTreatmentCaseSubgroupResponse {
  TreatmentCaseNames KvTreatmentCase = 1;
  string KvScheinSubGroup = 2 [ (goff.nullable) = true ];
}

message GetFieldsRequest {
  TreatmentCaseNames treatmentCase = 1;
  goff.UUID PatientId = 2 [ (goff.nullable) = true ];
}
enum FieldValidationType {
  FieldValidationType_Required = 0 [ (goff.value) = "required" ];
  FieldValidationType_Optional = 1 [ (goff.value) = "optional" ];
}
message FieldValidation {
  string name = 1;
  FieldValidationType validationType = 2;
}

message SubGroupRules {
  string Code = 1;
  repeated string Rules = 2;
}
message CaseFields {
  string Code = 1;
  repeated FieldValidation Fields = 2;
  repeated SubGroupRules SubGroups = 3;
  repeated string specialRules = 4;
}
message GetFieldsResponse { CaseFields CaseFields = 1; }

message CreateSvScheinRequest {
  goff.UUID PatientId = 1;
  goff.UUID DoctorId = 2;
  repeated service_domains_patient_participation.PatientParticipation
      patientParticipations = 3;
  patient_profile_common.InsuranceInfo InsuranceInfo = 4;
  int64 StartDate = 5 [ (goff.nullable) = true ];
  int64 EndDate = 6 [ (goff.nullable) = true ];
}

message OmimG {
  string id = 1;
  string code = 2;
  string genName = 3;
}

message GetOmimGResponse { repeated OmimG omimGs = 1; }

message OmimP {
  string id = 1;
  string code = 2;
  string typeOfIllness = 3;
  int32 pmk = 4;
}

message GetOmimPResponse { repeated OmimP omimPs = 1; }

message OmimGChain {
  goff.UUID id = 1;
  string name = 2;
  repeated string omimGIds = 3;
}

message GetOmimGChainResponse { repeated OmimGChain omimGChains = 1; }

message InsertOmimGChainRequest {
  string name = 1;
  repeated string omimGIds = 2;
}

message UpdateOmimGChainRequest {
  goff.UUID id = 1;
  string name = 2;
  repeated string omimGIds = 3;
}

message DeleteOmimGChainRequest { goff.UUID id = 1; }

message InsertOmimPChainRequest {
  string name = 1;
  repeated string omimPIds = 2;
}

message UpdateOmimPChainRequest {
  goff.UUID id = 1;
  string name = 2;
  repeated string omimPIds = 3;
}

message DeleteOmimPChainRequest { goff.UUID id = 1; }

message GetOmimPChainResponse { repeated OmimPChain omimPChains = 1; }

message OmimPChain {
  goff.UUID id = 1;
  string name = 2;
  repeated string omimPIds = 3;
}

message GroupTherapy {
  repeated string ServiceCode = 1;
  int32 AmountApprovalTherapy = 2;
  int32 AmountDocumentedTherapy = 3;
}

message PyschoTherapy {
  bool IsCheckingSomaticSymptom = 1;
  bool IsApproval = 2;
  int64 DeadlineApproval = 3 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string Lanr = 4 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  int64 DateApproval = 5 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  repeated string ServiceCodeApproval = 6 [ (goff.custom) = 'json:"omitempty"'];
  int64 AmountApprovedTherapy = 7 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  int64 AmountBilledTherapy = 8 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  int64 PausingStartDate = 9 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  int64 PausingEndDate = 10 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  bool IsInsuranceInformedTherapy = 11 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  bool IsCombinatedTreatment = 12 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  int32 TypeOfCombinatedTreatment = 13 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  GroupTherapy Patient = 14 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  GroupTherapy OtherPerson = 15 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
}

message CreateSvScheinAutomaticlyRequest {
  repeated goff.UUID ReferenceScheinIds = 1;
  int64 SelectedDate = 2;
}

message CreateSvScheinAutomaticlyResponse {
  repeated goff.UUID ScheinIds = 1;
}


message CreateSvScheinFromReferenceRequest {
  goff.UUID referenceScheinId = 1;
  goff.UUID doctorId = 2;
  string chargeSystemId = 3;
  int64 selectedDate = 4;
  int64 startDate = 5 [ (goff.nullable) = true ];
  int64 endDate = 6 [ (goff.nullable) = true ];
}

message CreateSvScheinFromReferenceResponse {
  goff.UUID ScheinId = 1;
}

message CreateSvScheinManuallyRequest {
  goff.UUID PatientId = 1;
  goff.UUID DoctorId = 2;
  goff.UUID InsuranceId = 3;
  int64 SelectedDate = 4;
  string ContractId = 5;
  string ChargeSystemId = 6;
  int64 StartDate = 7 [ (goff.nullable) = true ];
  int64 EndDate = 8 [ (goff.nullable) = true ];
  schein_common.MainGroup ScheinMainGroup = 9;
  goff.UUID AssignedToBsnrId = 10 [ (goff.nullable) = true ];
}

message CreateSvScheinManuallyResponse {
  goff.UUID ScheinId = 1;
}

message UpdateSvScheinRequest {
  goff.UUID scheinId = 1;
  goff.UUID doctorId = 2;
  string chargeSystemId = 3;
  int64 selectedDate = 4;
  int64 startDate = 5 [ (goff.nullable) = true ];
  int64 endDate = 6 [ (goff.nullable) = true ];
  goff.UUID assignedToBsnrId = 7 [ (goff.nullable) = true ];
}

message UpdateSvScheinResponse {
  goff.UUID ScheinId = 1;
}

enum ScheinStatus {
  ScheinStatus_Normal = 0;
  ScheinStatus_Printed = 1;
  ScheinStatus_Billed = 2;
  ScheinStatus_Canceled = 3;
}

enum BGType {
  GereralAction = 0;
  SpecialAction = 1;
}

message BgScheinItem {
  goff.UUID ScheinId = 1;
  goff.UUID InsuranceId = 2 [(goff.nullable) = true];
  goff.UUID DoctorId = 3 [(goff.custom) = 'validate:"required"'];
  goff.UUID PatientId = 4 [(goff.custom) = 'validate:"required"'];
  int64 CreatedOn = 5 [(goff.custom) = 'validate:"required"'];
  int64 EndDate = 6 [(goff.nullable) = true];
  bool PersonalAccident = 7;
  int64 AccidentDate = 8[(goff.custom) = 'validate:"required"'];
  int64 ArrivalDate = 9 [(goff.nullable) = true];
  int64 FileNumber = 10 [(goff.nullable) = true];
  bool ExcludeFromBilling = 11;
  bool MarkedAsBilled = 12 ;
  schein_common.MainGroup ScheinMainGroup = 13;
  int64 WorkingTimeStart = 14;
  int64 WorkingTimeEnd = 15;
  ScheinStatus ScheinStatus = 16 [(goff.nullable) = true];
  BGType  BGType = 17;
  patient_profile_common.EmploymentInfo EmploymentInfo = 18;
  goff.UUID AssignedToBsnrId = 19 [(goff.nullable) = true];
  int64 EmploymentInfoUpdatedAt = 20 [ (goff.nullable) = true ];
  string FileNumberStr = 21 [(goff.nullable) = true];
  int32 IkNumber = 22 [(goff.nullable) = true];
}

message BgScheinDetail {
  int64 CreatedOn = 1 [(goff.custom) = 'validate:"required"'];
  int64 EndDate = 2 [(goff.nullable) = true];
  bool PersonalAccident = 3;
  int64 AccidentDate = 4[(goff.custom) = 'validate:"required"'];
  int64 ArrivalDate = 5 [(goff.nullable) = true];
  int64 FileNumber = 6 [(goff.nullable) = true];
  int64 WorkingTimeStart = 7;
  int64 WorkingTimeEnd = 8;
  BGType  BGType = 9;
  string InvoiceNumber = 10 [(goff.nullable) = true];
  patient_profile_common.EmploymentInfo EmploymentInfo = 11;
  int64 EmploymentInfoUpdatedAt = 12 [ (goff.nullable) = true ];
  string FileNumberStr = 13 [(goff.nullable) = true];
}

message SvScheinDetail {
  int64 startDate =1 [(goff.nullable) = true];
  int64 endDate = 2 [(goff.nullable) = true];
  int64 onlineParticipatedCheckDate = 3 [(goff.nullable) = true];
}