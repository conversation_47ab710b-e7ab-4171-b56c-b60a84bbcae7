syntax = "proto3";

package form_common;
import "goff.proto";
import "service/domains/common.proto";
import "service/domains/eau_common.proto";
import "service/domains/qes_common.proto";

option go_package = "git.tutum.dev/medi/tutum/ares/service/domains/form/common";

enum FormCategory {
    FormCategory_A4_portrait = 0 [ (goff.value) = "A4_portrait" ];
    FormCategory_A4_landscape = 1 [ (goff.value) = "A4_landscape" ];
    FormCategory_A5_portrait = 2 [ (goff.value) = "A5_portrait" ];
    FormCategory_A5_landscape = 3 [ (goff.value) = "A5_landscape" ];
    FormCategory_A6_portrait = 4 [ (goff.value) = "A6_portrait" ];
    FormCategory_A6_landscape = 5 [ (goff.value) = "A6_landscape" ];
}



enum FormType {
    FormType_medication = 0 [ (goff.value) = "medication" ];
    FormType_heimi = 1 [ (goff.value) = "heimi" ];
    FormType_himi = 2 [ (goff.value) = "himi" ];
    FormType_lab = 3 [ (goff.value) = "lab" ];
    FormType_generic_form = 4 [ (goff.value) = "generic_form" ];
    FormType_public_document = 5 [ (goff.value) = "public_document" ];
    FormType_public_contract_text = 6 [ (goff.value) = "public_contract_text" ];
    FormType_contract_hint = 7 [ (goff.value) = "contract_hint" ];
    FormType_diga = 8 [ (goff.value) = "diga" ];
}

enum FormSize {
    FormSize_A4 = 0 [ (goff.value) = "A4" ];
    FormSize_A5 = 1 [ (goff.value) = "A5" ];
    FormSize_A6 = 2 [ (goff.value) = "A6" ];
}

enum FormOrientation {
    PrintOrientation_portrait = 0 [ (goff.value) = "portrait" ];
    PrintOrientation_landscape = 1 [ (goff.value) = "landscape" ];
}

enum PrintType {
    PrintType_formPrint = 0 [ (goff.value) = "formPrint" ];
    PrintType_fullPrint = 1 [ (goff.value) = "fullPrint" ];
  }

message Form {
    string Id = 1;
    bool FormTab = 2;
    FormSize Size = 3;
    FormOrientation Orientation = 4;
    repeated string AdditionalDocs = 5 [(goff.nullable)=true];
    repeated FormAction Actions = 6;
    FormType FormType = 7 [(goff.nullable)=true];
    string Title = 8;
    string FileNameWithVersion = 9;
    bool IsDuplex = 10;
    repeated PrintType printTypes = 11;
    bool HasRefill = 12;
    bool IsHzvFav = 13;
}

message Prescribe {
    goff.UUID Id = 1 [(goff.nullable) = true];
    goff.UUID DoctorId = 2 [(goff.custom) = 'validate:"required"'];
    goff.UUID TreatmentDoctorId = 3 [(goff.custom) = 'validate:"required"'];
    goff.UUID PatientId = 4 [(goff.custom) = 'validate:"required"'];
    int64 CreatedDate = 5;
    int64 UpdatedDate = 6  [(goff.nullable) = true];
    int64 PrintedDate = 7 [(goff.nullable) = true];
    string Payload = 8 [(goff.custom) = 'validate:"required,trimjson"'];
    FormName FormName = 9 [(goff.custom) = 'validate:"required"'];
    string EncounterCase = 10;
    common.ContractType ContractType = 11;
    string ContractId = 12 [(goff.nullable) = true];
    goff.UUID ScheinId = 13 [(goff.nullable) = true];
    int64 PrescribeDate = 14;
    goff.UUID UpdatedBy = 15 [(goff.nullable) = true];
    repeated string PdfUrl = 16 [(goff.nullable) = true];
    qes_common.DocumentStatus EAUStatus = 17 [(goff.nullable) = true];
    eau_common.EAUSetting EAUSetting = 18 [(goff.nullable) = true];
    string FormTitle = 19 [(goff.nullable) = true];
    string EAUBundleUrl = 20 [(goff.nullable) = true];
    bool IsImported = 21 [(goff.nullable) = true];
    bool IsTerminated = 22 [(goff.nullable) = true];
    string EAUCancellationBundleUrl = 23 [(goff.nullable) = true];
    goff.UUID AssignedToBsnrId = 24 [ (goff.nullable) = true ];
}

enum FormAction {
    FormAction_PrintFull = 0;
    FormAction_PrintHeader = 1;
    FormAction_PrintWithoutContent = 2;
    FormAction_OpenNewTab = 3;
    FormAction_PrintOnly = 4;
    FormAction_PrintWithBSNRAndLANR = 5;
}

message PrintOption {
    int64 DateOfPrint = 1;
    bool PdfWithBackground = 2;
    FormAction FormAction = 3;
    bool PreventAddToTimeline = 4 [(goff.nullable) = true];
}

enum EAUFormType {
    FormType_V = 0 [ (goff.value) = "v" ];
    FormType_KK = 1 [ (goff.value) = "kk" ];
    FormType_AG = 2 [ (goff.value) = "ag" ];
}


enum FormName {
    Muster_8 = 0  [(goff.value) = "Muster_8"];
    Muster_8A = 1 [(goff.value) = "Muster_8A"];
    Muster_16 = 2 [(goff.value) = "Muster_16"];
    Muster_15 = 3 [(goff.value) = "Muster_15"];
    Muster_1 = 4 [(goff.value) = "Muster_1"];
    Muster_6 = 5 [(goff.value) = "Muster_6"];
    Muster_10 = 6 [(goff.value) = "Muster_10"];
    Muster_10A = 7 [(goff.value) = "Muster_10A"];
    Muster_13 = 8 [(goff.value) = "Muster_13"];
    Muster_39A = 9 [(goff.value) = "Muster_39A"];
    Muster_2B = 10 [(goff.value) = "Muster_2B"];
    Muster_4 = 11 [(goff.value) = "Muster_4"];
    Muster_2A = 12 [(goff.value) = "Muster_2A"];
    KREZ = 13 [(goff.value) = "Muster_16"];
    GREZ = 14 [(goff.value) = "Gruenes_Rezept"];
    BTM = 15 [(goff.value) = "Btm_Rezept_Print"];
    TPrescription = 16 [(goff.value) = "T-Rezept-Muster"];
    Private = 17 [(goff.value) = "Blaues_Rezept"];
    Muster_6_cover_letter = 18 [(goff.value) = "Muster_6_cover_letter"];
    Muster_2C = 19 [(goff.value) = "Muster_2C"];
    Muster_3A = 22 [(goff.value) = "Muster_3A"];
    Muster_3B = 23 [(goff.value) = "Muster_3B"];
    Muster_5 = 24 [(goff.value) = "Muster_5"];
    Muster_9 = 25 [(goff.value) = "Muster_9"];
    Muster_21 = 28 [(goff.value) = "Muster_21"];
    Muster_19A = 29 [(goff.value) = "Muster_19A"];
    Muster_36_E_2017_07 = 31 [(goff.value) = "Muster_36_E_2017_07"];
    Muster_12A = 32 [(goff.value) = "Muster_12A"];
    Muster_28A = 33 [(goff.value) = "Muster_28A"];
    Muster_28B = 34 [(goff.value) = "Muster_28B"];
    Muster_28C = 35 [(goff.value) = "Muster_28C"];
    Muster_52_0_V2 = 41 [(goff.value) = "Muster_52_0_V2"];
    Muster_12B = 42 [(goff.value) = "Muster_12B"];
    Muster_12C = 43 [(goff.value) = "Muster_12C"];
    Muster_55 = 44 [(goff.value) = "Muster_55"];
    Muster_10C = 45 [(goff.value) = "Muster_10C"];
    Muster_20A = 46 [(goff.value) = "Muster_20A"];
    Muster_20B = 47 [(goff.value) = "Muster_20B"];
    Muster_20C = 48 [(goff.value) = "Muster_20C"];
    Muster_20D = 49 [(goff.value) = "Muster_20D"];
    Muster_65A = 50 [(goff.value) = "Muster_65A"];
    Muster_65B = 51 [(goff.value) = "Muster_65B"];
    Muster_70 = 53 [(goff.value) = "Muster_70"];
    Muster_70_B = 54 [(goff.value) = "Muster_70_B"];
    Muster_70A = 55 [(goff.value) = "Muster_70A"];
    Muster_70A_B = 56 [(goff.value) = "Muster_70A_B"];
    Muster_N63A = 57 [(goff.value) = "Muster_N63A"];
    Muster_N63B = 58 [(goff.value) = "Muster_N63B"];
    Muster_N63C = 59 [(goff.value) = "Muster_N63C"];
    Muster_N63D = 60 [(goff.value) = "Muster_N63D"];
    Muster_61A = 61 [(goff.value) = "Muster_61A"];
    Muster_61B = 62 [(goff.value) = "Muster_61B"];
    Muster_64 = 63 [(goff.value) = "Muster_64"];
    Muster_64B = 64 [(goff.value) = "Muster_64B"];
    Muster_19B = 65 [(goff.value) = "Muster_19B"];
    Muster_19C = 66 [(goff.value) = "Muster_19C"];
    Muster_56 = 67 [(goff.value) = "Muster_56"];
    Muster_61 = 75 [(goff.value) = "Muster_61"];
    Muster_PTV_11A = 76 [(goff.value) = "Muster_PTV_11A"];
    Muster_PTV_11B = 77 [(goff.value) = "Muster_PTV_11B"];
    Muster_PTV_3 = 78 [(goff.value) = "Muster_PTV_3"];
    Muster_PTV_10 = 79 [(goff.value) = "Muster_PTV_10"];
    G81_EHIC_Bulgarisch = 80 [(goff.value) = "G81_EHIC_Bulgarisch"];
    G81_EHIC_Danisch = 81 [(goff.value) = "G81_EHIC_Danisch"];
    G81_EHIC_Englisch = 82 [(goff.value) = "G81_EHIC_Englisch"];
    G81_EHIC_Franzosisch = 83 [(goff.value) = "G81_EHIC_Franzosisch"];
    G81_EHIC_Griechisch = 84 [(goff.value) = "G81_EHIC_Griechisch"];
    G81_EHIC_Italienisch = 85 [(goff.value) = "G81_EHIC_Italienisch"];
    G81_EHIC_Kroatisch = 86 [(goff.value) = "G81_EHIC_Kroatisch"];
    G81_EHIC_Niederlandisch = 87 [(goff.value) = "G81_EHIC_Niederlandisch"];
    G81_EHIC_Polnisch = 88 [(goff.value) = "G81_EHIC_Polnisch"];
    G81_EHIC_Rumanisch = 89 [(goff.value) = "G81_EHIC_Rumanisch"];
    G81_EHIC_Spanisch = 90 [(goff.value) = "G81_EHIC_Spanisch"];
    G81_EHIC_Tschechisch = 91 [(goff.value) = "G81_EHIC_Tschechisch"];
    G81_EHIC_Ungarisch = 92 [(goff.value) = "G81_EHIC_Ungarisch"];
    Muster_16A = 93 [(goff.value) = "Muster_16A"];
    Muster_16A_Bay = 94 [(goff.value) = "Muster_16a_bay"];
    G81_EHIC_All = 95 [(goff.value) = "G81_EHIC_All"];
    DMP_Enrollment_Form = 96 [(goff.value) = "DMP_Enrollment_Form"];
    PHQ_9_Q3_2023 = 97 [(goff.value) = "PHQ_9_Q3_2023"];
    Muster_7 = 98  [(goff.value) = "Muster_7"];
    Muster_11 = 99  [(goff.value) = "Muster_11"];
    Muster_50 = 100 [(goff.value) = "Muster_50"];
    Muster_51 = 101 [(goff.value) = "Muster_51"];
    G81_EHIC_Finnisch = 102  [(goff.value) = "G81_EHIC_Finnisch"];
    G81_EHIC_Estnisch = 103  [(goff.value) = "G81_EHIC_Estnisch"];
    G81_EHIC_Slowenisch = 104  [(goff.value) = "G81_EHIC_Slowenisch"];
    G81_EHIC_Slowakisch = 105  [(goff.value) = "G81_EHIC_Slowakisch"];
    G81_EHIC_Schwedisch = 106  [(goff.value) = "G81_EHIC_Schwedisch"];
    G81_EHIC_Portugiesisch = 107  [(goff.value) = "G81_EHIC_Portugiesisch"];
    G81_EHIC_Litauisch = 108  [(goff.value) = "G81_EHIC_Litauisch"];
    G81_EHIC_Lettisch = 109  [(goff.value) = "G81_EHIC_Lettisch"];
    Muster_22A = 110 [(goff.value) = "Muster_22A"];
    Muster_22B = 111 [(goff.value) = "Muster_22B"];
    Muster_22C = 112 [(goff.value) = "Muster_22C"];
    Muster_22D = 113 [(goff.value) = "Muster_22D"];
    Muster_26A = 114 [(goff.value) = "Muster_26A"];
    Muster_26B = 115 [(goff.value) = "Muster_26B"];
    Muster_26C = 116 [(goff.value) = "Muster_26C"];
    Muster_27A = 117 [(goff.value) = "Muster_27A"];
    Muster_27B = 118 [(goff.value) = "Muster_27B"];
    Muster_27C = 119 [(goff.value) = "Muster_27C"];
    Muster_PTV_1A = 120 [(goff.value) = "Muster_PTV_1A"];
    Muster_PTV_1B = 121 [(goff.value) = "Muster_PTV_1B"];
    Muster_PTV_1C = 122 [(goff.value) = "Muster_PTV_1C"];
    Muster_PTV_12A = 123 [(goff.value) = "Muster_PTV_12A"];
    Muster_PTV_12B = 124 [(goff.value) = "Muster_PTV_12B"];  
    Muster_PTV_2A = 125 [(goff.value) = "Muster_PTV_2A"];
    Muster_PTV_2B = 126 [(goff.value) = "Muster_PTV_2B"];
    Muster_PTV_2C = 127 [(goff.value) = "Muster_PTV_2C"];
    Muster_39B = 128 [(goff.value) = "Muster_39B"];
    Muster_61C = 129 [(goff.value) = "Muster_61C"];
    Muster_61D = 130 [(goff.value) = "Muster_61D"];
    AOK_FA_NPPP_BW = 144 [(goff.value) = "AOK_FA_NPPP_BW"];
    BKK_BOSCH_FA_BW = 145 [(goff.value) = "BKK_BOSCH_FA_BW"];
    MEDI_FA_PT_BW = 146 [(goff.value) = "MEDI_FA_PT_BW"];
    HIMI_QUESTION_NAME = 147 [(goff.value) = "HIMI_QUESTION_NAME"];
    // All SV Forms
    BKK_VAG_BW_Schnellinformation_Patientenbegleitung_V4 = 148 [(goff.value) = "BKK_VAG_BW_Schnellinformation_Patientenbegleitung_V4"];
    BKK_VAG_FA_BW_Versichertenteilnahmeerklaerung_V5 = 149 [(goff.value) = "BKK_VAG_FA_BW_Versichertenteilnahmeerklaerung_V5"];
    Muster_52_2_V3 = 150 [(goff.value) = "Muster_52_2_V3"];
    HIMIFB0399051_V3 = 151 [(goff.value) = "HIMIFB0399051_V3"];
    HIMIFB0399054_V3 = 152 [(goff.value) = "HIMIFB0399054_V3"];
    HIMIFB039906_V3 = 153 [(goff.value) = "HIMIFB039906_V3"];
    HIMIFB0440_V3 = 154 [(goff.value) = "HIMIFB0440_V3"];
    HIMIFB1129_V3 = 155 [(goff.value) = "HIMIFB1129_V3"];
    HIMIFB1424_V3 = 156 [(goff.value) = "HIMIFB1424_V3"];
    HIMIFB1525192_V3 = 157 [(goff.value) = "HIMIFB1525192_V3"];
    HIMIFB1846ER_V3 = 158 [(goff.value) = "HIMIFB1846ER_V3"];
    HIMIFB1865_V3 = 159 [(goff.value) = "HIMIFB1865_V3"];
    HIMIFB1940_V3 = 160 [(goff.value) = "HIMIFB1940_V3"];
    HIMIFB213401_V4 = 161 [(goff.value) = "HIMIFB213401_V4"];  
    HIMIFB31_V3 = 162 [(goff.value) = "HIMIFB31_V3"];
    BKK_VAG_FA_BW_TE_HepCModul_V2 = 163 [(goff.value) = "BKK_VAG_FA_BW_TE_HepCModul_V2"];
    TK_HZV_Versichertenteilnahmeerklaerung_V9 = 164 [(goff.value) = "TK_HZV_Versichertenteilnahmeerklaerung_V9"];
    HZV_Beleg_Muster_V3 = 165 [(goff.value) = "HZV_Beleg_Muster_V3"];
    SI_IKK_HZV_Versichertenteilnahmeerklaerung_V7 = 166 [(goff.value) = "SI_IKK_HZV_Versichertenteilnahmeerklaerung_V7"];
    RV_KBS_HZV_Versichertenteilnahmeerklaerung_V4 = 167 [(goff.value) = "RV_KBS_HZV_Versichertenteilnahmeerklaerung_V4"];
    Ueberleitungsbogen_AOK_KBS_NO_WL_V2 = 168 [(goff.value) = "Ueberleitungsbogen_AOK_KBS_NO_WL_V2"];
    RV_KBS_SN_HZV_Ueberleitungsmanagement_Ueberleitungsbogen_V3 = 169 [(goff.value) = "RV_KBS_SN_HZV_Ueberleitungsmanagement_Ueberleitungsbogen_V3"];
    RV_KBS_BW_HZV_Versichertenteilnahmeerklaerung_V5 = 170 [(goff.value) = "RV_KBS_BW_HZV_Versichertenteilnahmeerklaerung_V5"];
    BKK_VAG_FA_PT_BW_Versichertenteilnahmeerklaerung_V17 = 171 [(goff.value) = "BKK_VAG_FA_PT_BW_Versichertenteilnahmeerklaerung_V17"];
    BKK_VAG_FA_PT_BW_GDK_Antragsformular_V3 = 172 [(goff.value) = "BKK_VAG_FA_PT_BW_GDK_Antragsformular_V3"];
    BKK_VAG_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2 = 173 [(goff.value) = "BKK_VAG_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2"];
    BKK_VAG_FA_PT_BW_Ausschreibeformular_V5 = 174 [(goff.value) = "BKK_VAG_FA_PT_BW_Ausschreibeformular_V5"];
    BKK_GWQ_FA_PT_BW_Ausschreibeformular_V2 = 175 [(goff.value) = "BKK_GWQ_FA_PT_BW_Ausschreibeformular_V2"];
    BKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2 = 176 [(goff.value) = "BKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2"];
    BKK_GWQ_FA_PT_BW_GDK_Antragsformular_V2 = 177 [(goff.value) = "BKK_GWQ_FA_PT_BW_GDK_Antragsformular_V2"];
    BKK_GWQ_FA_PT_BW_Versichertenteilnahmeerklaerung_V2 = 178 [(goff.value) = "BKK_GWQ_FA_PT_BW_Versichertenteilnahmeerklaerung_V2"];
    LKK_Teilnahme_und_Einwilligungserklaerung_V7 = 179 [(goff.value) = "LKK_Teilnahme-und Einwilligungserklaerung_V7"];
    LKK_BY_HZV_Muster_Versicherteneinschreibebeleg_V3 = 180 [(goff.value) = "LKK_BY_HZV_Muster_Versicherteneinschreibebeleg_V3"];
    Praxisuebergabe_V1 = 181 [(goff.value) = "Praxisuebergabe_V1"];
    LKK_BY_Teilnahme_und_Einwilligungserklaerung_V4 = 182 [(goff.value) = "LKK_BY_Teilnahme-und Einwilligungserklaerung_V4"];
    LKK_BW_HZV_VersichertenTeilnahmeerklaerung_V15 = 183 [(goff.value) = "LKK_BW_HZV_VersichertenTeilnahmeerklaerung_V15"];
    IKK_CL_BW_HZV_Versichertenteilnahmeerklaerung_V12 = 184 [(goff.value) = "IKK_CL_BW_HZV_Versichertenteilnahmeerklaerung_V12"];
    HKK_HZV_NORD_Versichertenteilnahmeerklaerung_V4 = 185 [(goff.value) = "HKK_HZV_NORD_Versichertenteilnahmeerklaerung_V4"];
    EK_WL_HZV_Versichertenteilnahmeerklaerung_V2 = 186 [(goff.value) = "EK_WL_HZV_Versichertenteilnahmeerklaerung_V2"];
    Ueberleitungsbogen_EK_BKK_NO_WL_V1 = 187 [(goff.value) = "Ueberleitungsbogen_EK_BKK_NO_WL_V1"];
    EK_SN_HZV_Versichertenteilnahmeerklaerung_V3 = 188 [(goff.value) = "EK_SN_HZV_Versichertenteilnahmeerklaerung_V3"];
    EK_HZV_BARMER_DAK_Versichertenteilnahmeerklaerung_V3 = 189 [(goff.value) = "EK_HZV_BARMER_DAK_Versichertenteilnahmeerklaerung_V3"];
    EK_RLP_HZV_Versichertenteilnahmeerklaerung_V5 = 190 [(goff.value) = "EK_RLP_HZV_Versichertenteilnahmeerklaerung_V5"];
    EK_NO_HZV_Versichertenteilnahmeerklaerung_V2 = 191 [(goff.value) = "EK_NO_HZV_Versichertenteilnahmeerklaerung_V2"];
    EK_FA_DIA_BW_Versichertenteilnahmeerklaerung_V1 = 192 [(goff.value) = "EK_FA_DIA_BW_Versichertenteilnahmeerklaerung_V1"];
    EK_BY_HZV_S12_Versichertenteilnahmeerklaerung_V7 = 193 [(goff.value) = "EK_BY_HZV_S12_Versichertenteilnahmeerklaerung_V7"];
    Versichertenteilnahmeerklaerung_Online_EK_BW_HZV_V5 = 194 [(goff.value) = "Versichertenteilnahmeerklaerung_Online_EK_BW_HZV_V5"];
    EK_BLN_HZV_Versichertenteilnahmeerklaerung_V4 = 195 [(goff.value) = "EK_BLN_HZV_Versichertenteilnahmeerklaerung_V4"];
    DAK_HZV_VersichertenTeilnahmeerklaerung_V4 = 196 [(goff.value) = "DAK_HZV_VersichertenTeilnahmeerklaerung_V4"];
    Versichertenteilnahmeerklaerung_Online_BKK_GWQ_HZV_V8 = 197 [(goff.value) = "Versichertenteilnahmeerklaerung_Online_BKK_GWQ_HZV_V8"];
    BKK_VAG_HE_Versichertenteilnahmeerklaerung_V4 = 198 [(goff.value) = "BKK_VAG_HE_Versichertenteilnahmeerklaerung_V4"];
    BKK_VAG_HE_Schnellinformation_Patientenbegleitung_V1 = 199 [(goff.value) = "BKK_VAG_HE_Schnellinformation_Patientenbegleitung_V1"];
    BKK_BOSCH_VAG_BW_Praeventionsverordnung_V1 = 200 [(goff.value) = "BKK_BOSCH_VAG_BW_Praeventionsverordnung_V1"];
    Begleitschreiben_FaV_V4 = 201 [(goff.value) = "Begleitschreiben_FaV_V4"];
    Versichertenteilnahmeerklaerung_Online_Variante_A_V11 = 202 [(goff.value) = "Versichertenteilnahmeerklaerung_Online_Variante_A_V11"];
    Versichertenteilnahmeerklaerung_Online_BKK_SPECTRUM_HZV_V5 = 203 [(goff.value) = "Versichertenteilnahmeerklaerung_Online_BKK_SPECTRUM_HZV_V5"];
    Ambulantes_Operieren_V1 = 204 [(goff.value) = "Ambulantes_Operieren_V1"];
    AOK_FA_URO_BW_BKK_FA_URO_BW_Uebertragung_Honorar_Anaesthesist_V4 = 205 [(goff.value) = "AOK_FA_URO_BW_BKK_FA_URO_BW_Uebertragung_Honorar_Anaesthesist_V4"];
    BKK_BOSCH_FA_BW_Versichertenteilnahmeerklaerung_V11 = 206 [(goff.value) = "BKK_BOSCH_FA_BW_Versichertenteilnahmeerklaerung_V11"];
    BKK_BOSCH_BW_Schnellinfo_Patientenbegleitung_V6 = 207 [(goff.value) = "BKK_BOSCH_BW_Schnellinfo_Patientenbegleitung_V6"];
    AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Arthrose_V3 = 208 [(goff.value) = "AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Arthrose_V3"];
    AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_entzuendliche_Gelenkerkrankungen_V3 = 209 [(goff.value) = "AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_entzuendliche_Gelenkerkrankungen_V3"];
    AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Grundversorgung_V3 = 210 [(goff.value) = "AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Grundversorgung_V3"];
    AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_V3 = 211 [(goff.value) = "AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_V3"];
    AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Rueckenschmerz_V3 = 212 [(goff.value) = "AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Rueckenschmerz_V3"];
    AOK_FA_OC_BW_BKK_FA_OC_BW_Uebertragung_Honorar_Anaesthesist_V5 = 213 [(goff.value) = "AOK_FA_OC_BW_BKK_FA_OC_BW_Uebertragung_Honorar_Anaesthesist_V5"];
    BKK_BOSCH_Beratungsbogen_Einbindung_PBG_V10 = 214 [(goff.value) = "BKK_BOSCH_Beratungsbogen_Einbindung_PBG_V10"];
    BKK_BOSCH_FA_TE_HepCModul_V4 = 215 [(goff.value) = "BKK_BOSCH_FA_TE_HepCModul_V4"];
    BKK_BOSCH_FA_GASTRO_BW_Maviret_Teilnahmeerklaerung_V3 = 216 [(goff.value) = "BKK_BOSCH_FA_GASTRO_BW_Maviret_Teilnahmeerklaerung_V3"];
    BKK_BOSCH_FA_GASTRO_BW_Epclusa_Teilnahmeerklaerung_V2 = 217 [(goff.value) = "BKK_BOSCH_FA_GASTRO_BW_Epclusa_Teilnahmeerklaerung_V2"];
    Versichertenteilnahmeerklaerung_Online_BKK_BY_HZV_V6 = 218 [(goff.value) = "Versichertenteilnahmeerklaerung_Online_BKK_BY_HZV_V6"];
    BKK_BY_HZV_Notfallplan_geriatrischer_Patient_V1 = 219 [(goff.value) = "BKK_BY_HZV_Notfallplan_geriatrischer_Patient_V1"];
    BKK_BY_HZV_Schnellinfo_Patientenbegleitung_V6 = 220 [(goff.value) = "BKK_BY_HZV_Schnellinfo_Patientenbegleitung_V6"];
    BKK_BW_HZV_Versichertenteilnahmeerklaerung_V9 = 221 [(goff.value) = "BKK_BW_HZV_Versichertenteilnahmeerklaerung_V9"];
    BKK_BOSCH_FA_BW_GDK_Antragsformular_V4 = 222 [(goff.value) = "BKK_BOSCH_FA_BW_GDK_Antragsformular_V4"];
    AWH_01_Checkliste_Somatik_V1 = 223 [(goff.value) = "AWH_01_Checkliste_Somatik_V1"];
    AWH_01_Checkliste_Psychosomatik_V1 = 224 [(goff.value) = "AWH_01_Checkliste_Psychosomatik_V1"];
    AWH_01_Kurzantrag_HZV_KinderReha_V1 = 225 [(goff.value) = "AWH_01_Kurzantrag_HZV-KinderReha_V1"];
    AWH_01_BVKJ_Anlage_7b_Osteopathie_V2 = 226 [(goff.value) = "AWH_01_BVKJ_Anlage_7b_Osteopathie_V2"];
    AWH_01_Patientenfragebogen_AOK_Check_18_V2 = 227 [(goff.value) = "AWH_01_Patientenfragebogen_AOK-Check 18+_V2"];
    AWH_01_Versichertenteilnahmeerklaerung_Volldruck_V12 = 228 [(goff.value) = "AWH_01_Versichertenteilnahmeerklaerung_Volldruck_V12"];
    AOK_BW_Beratungsbogen_Einbindung_SD_V7 = 229 [(goff.value) = "AOK_BW_Beratungsbogen_Einbindung_SD_V7"];
    AOK_FaV_Versichertenteilnahmeerklaerung_Volldruck_V9 = 230 [(goff.value) = "AOK_FaV_Versichertenteilnahmeerklaerung_Volldruck_V9"];
    AOK_BW_IV_P_Versichertenteilnahmeerklaerung_Formulardruck_V3 = 231 [(goff.value) = "AOK_BW_IV_P_Versichertenteilnahmeerklaerung_Formulardruck_V3"];
    AOK_WL_HZV_Versichertenteilnahmeerklaerung_V8 = 232 [(goff.value) = "AOK_WL_HZV_Versichertenteilnahmeerklaerung_V8"];
    AOK_SH_HZV_Ueberleitungsmanagement_V3 = 233 [(goff.value) = "AOK_SH_HZV_Ueberleitungsmanagement_V3"];
    AOK_SH_HZV_Versichertenteilnahmeerklaerung_V7 = 234 [(goff.value) = "AOK_SH_HZV_Versichertenteilnahmeerklaerung_V7"];
    AOK_RP_HZV_Versichertenteilnahmeerklaerung_V4 = 235 [(goff.value) = "AOK_RP_HZV_Versichertenteilnahmeerklaerung_V4"];
    AOK_PLUS_Versichertenteilnahmeerklaerung_V6 = 236 [(goff.value) = "AOK_PLUS_Versichertenteilnahmeerklaerung_V6"];
    AOK_NO_HH_Versichertenteilnahmeerklaerung_V5 = 237 [(goff.value) = "AOK_NO_HH_Versichertenteilnahmeerklaerung_V5"];
    AOK_IKK_BLN_HZV_Versichertenteilnahmeerklaerung_V11 = 238 [(goff.value) = "AOK_IKK_BLN_HZV_Versichertenteilnahmeerklaerung_V11"];
    AOK_IKK_BLN_HZV_Muster_Versicherteneinschreibebeleg_V2 = 239 [(goff.value) = "AOK_IKK_BLN_HZV_Muster_Versicherteneinschreibebeleg_V2"];
    AOK_HH_HZV_Ueberleitungsbogen_V2 = 240 [(goff.value) = "AOK_HH_HZV_Ueberleitungsbogen_V2"];
    AOK_HE_HZV_Versichertenteilnahmeerklaerung_V12 = 241 [(goff.value) = "AOK_HE_HZV_Versichertenteilnahmeerklaerung_V12"];
    AOK_FA_NPPP_BW_GDK_KJPY_Antragsformular_V3 = 242 [(goff.value) = "AOK_FA_NPPP_BW_GDK_KJPY_Antragsformular_V3"];
    AOK_FA_NPPP_BW_GDK_Antragsformular_V6 = 243 [(goff.value) = "AOK_FA_NPPP_BW_GDK_Antragsformular_V6"];
    AOK_FA_BW_GDK_Antragsformular_DF_V4 = 244 [(goff.value) = "AOK_FA_BW_GDK_Antragsformular_DF_V4"];
    AOK_FA_BW_TE_HepCModul_V3 = 245 [(goff.value) = "AOK_FA_BW_TE_HepCModul_V3"];
    AOK_FA_GASTRO_BW_Maviret_Teilnahmeerklaerung_V2 = 246 [(goff.value) = "AOK_FA_GASTRO_BW_Maviret_Teilnahmeerklaerung_V2"];
    AOK_FA_GASTRO_BW_Epclusa_Teilnahmeerklaerung_V2 = 247 [(goff.value) = "AOK_FA_GASTRO_BW_Epclusa_Teilnahmeerklaerung_V2"];
    AOK_SL_HZV_Versichertenteilnahmeerklaerung_V2 = 248 [(goff.value) = "AOK_SL_HZV_Versichertenteilnahmeerklaerung_V2"];
    BKK_BAHN_HZV_Versichertenteilnahmeerklaerung_V4 = 249 [(goff.value) = "BKK_BAHN_HZV_Versichertenteilnahmeerklaerung_V4"];
    AOKNordwet = 250 [(goff.value) = "AOK_Nordwet"];
    AOKBremen = 251 [(goff.value) = "AOK_Bremen_impfstoff"];
    Muster16aBay = 252 [(goff.value) = "Muster_16a_bay"];
    Versichertenteilnahmeerklaerung_Online_BKK_BY_HZV_V8 = 253 [(goff.value) = "Versichertenteilnahmeerklaerung_Online_BKK_BY_HZV_V8"];
    F1050 = 254 [(goff.value) = "F1050"];
    F9990 = 255 [(goff.value) = "F9990"];
    F2100 = 256 [(goff.value) = "F2100"];
    F1000 = 257 [(goff.value) = "F1000"];
    BKK_BY_HZV_Einwilligungserklaerung_Telemedizinisches_Facharztkonsil = 258 [(goff.value) = "BKK_BY_HZV_Einwilligungserklaerung_Telemedizinisches_Facharztkonsil"];
    Muster_eRezept = 259 [(goff.value) = "Muster_eRezept"];
    AOK_FA_OC_BW_Antrag_AOK_Sports_V3 = 260 [(goff.value) = "AOK_FA_OC_BW_Antrag_AOK_Sports_V3"];
    AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_Anleitung_Bedruckung_V2 = 261 [(goff.value) = "AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_Anleitung_Bedruckung_V2"];
    Muster_4_A5 = 262 [(goff.value) = "Muster_4_print_preview_n"];
    EK_NO_WL_HZV_Versichertenteilnahmeerklaerung_V3 = 263 [(goff.value) = "EK_NO_WL_HZV_Versichertenteilnahmeerklaerung_V3"];
    AOK_SL_HZV_Versichertenteilnahmeerklaerung_V3 = 264 [(goff.value) = "AOK_SL_HZV_Versichertenteilnahmeerklaerung_V3"];
    LabResults_Landscape = 265 [(goff.value) = "LabResults_Landscape"];
    LabResults_Portrait = 266 [(goff.value) = "LabResults_Portrait"];
}

message PrintResult {
    FormName FormName = 1;
    string FormUrl = 2; // presignedUrl
}