syntax = "proto3";

package edmp_common;

import "goff.proto";
import "service/domains/edmp_sdda_common.proto";
import "service/domains/patient_profile_common.proto";
import "service/domains/schein_common.proto";

option go_package = "git.tutum.dev/medi/tutum/ares/service/domains/edmp/common";
option (goff.pointer_strict_mode) = true;

//https://www.notion.so/silenteer/38e5a1a4dc8042208fc1b7bcb0c70e5c?v=4aa009ca3b004446802b59f61fff1095
message DMPLabeling {
  string Name = 1;
  string GermanName = 2;
  string ParticipationLetter = 3;
  string Value = 4;
  string ICDCode = 5;
}

enum ParticipationFormsStatus {
  ParticipationFormsStatus_Print = 0;
  ParticipationFormsStatus_Save = 1;
  ParticipationFormsStatus_Created = 2;
}

enum DocumentType {
  DocumentType_ED = 0;
  DocumentType_FD = 1;
  DocumentType_PD = 2;
  DocumentType_PHQ9_ED = 3;
  DocumentType_PHQ9_FD = 4;
  DocumentType_EHKS_D = 5;
  DocumentType_EHKS_ND = 6;
  DocumentType_EHKS_D_EV = 7;
  DocumentType_EHKS_ND_EV = 8;
}

enum DocumentStatus {
  DocumentStatus_Saved = 0;
  DocumentStatus_Finished = 1;
  DocumentStatus_Billed = 2;
  DocumentStatus_Printed = 3;
}

message ParticipationForm {
  string DMPLabelingValue = 1;
  ParticipationFormsStatus ParticipationFormsStatus = 2;
  goff.UUID TreatmentDoctorId = 3;
  EnrollStatus EnrollStatus = 4 [(goff.nullable) = true];
  string FormSetting = 5 [
    (goff.nullable) = true,
    (goff.custom) = 'validate:"omitempty,trimjson"'
  ];
}

enum EnrollStatus {
  StatusActivated = 0 [(goff.value) = "Activated"];
  StatusPotential = 1 [(goff.value) = "Potential"];
  StatusTerminated = 2 [(goff.value) = "Terminated"];
}

enum DoctorRelationType {
  DoctorRelationType_Treatment = 0;
  DoctorRelationType_Deputy = 1;
  DoctorRelationType_DoctorCharge = 2;
}

enum XPMFileType {
  XPMFileType_Protocol = 0;
  XPMFileType_Statistic = 1;
}

message XPMErrorContent {
  string ErrorNo = 1;
  string Message = 2;
  int32 LineNumberError = 3;
  string HeaderName = 4;
  string FieldName = 5;
  ErrorType ErrorType = 6;
  string BillingFileName = 7;
}

message XPMErrorResponse {
  string DPMCaseNumber = 1;
  repeated XPMErrorContent XPMErrorContents = 2;
  XPMFileType XPMFileType = 3;
}

message XPMResult {
  string Status = 1;
  DMPBillingFile StatisticFile = 2; //PDF
  DMPBillingFile ProtocolFile = 3; //PDF
  repeated XPMErrorResponse XPMErrorResponse = 4;
}

message PHQ9 {
  string FileName = 1;
  repeated int32 Scores = 2;
  repeated int32 Totals = 3;
  int32 PreviousScore = 4 [(goff.nullable) = true];
}

enum AdditionalContractsEnum {
  AdditionalContractsEnum_Yes = 0 [ (goff.value) = "yes" ];
  AdditionalContractsEnum_No = 1 [ (goff.value) = "no" ];
}

message DocumentationOverview {
  goff.UUID EnrollmentId = 1;
  DocumentType DocumentType = 2;
  goff.UUID ScheinId = 3 [(goff.custom) = 'validate:"required"'];
  string DMPLabelingValue = 4 [(goff.custom) = 'validate:"required"'];
  goff.UUID TreatmentDoctorId = 5 [(goff.custom) = 'validate:"required"'];
  goff.UUID DoctorId = 6 [(goff.custom) = 'validate:"required"'];
  DocumentStatus DocumentStatus = 7 [(goff.nullable) = true];
  EnrollStatus EnrollStatus = 8 [(goff.nullable) = true];
  goff.UUID PatientId = 9 [(goff.nullable) = true];
  repeated Field Fields = 10 [(goff.nullable) = true];
  DoctorRelationType DoctorRelationType = 11;
  goff.UUID DocumentationOverviewId = 12 [(goff.nullable) = true];
  int64 DocumentDate = 13 [(goff.custom) = 'validate:"required"'];
  DMPBillingFile DMPBillingFile = 14;
  PHQ9 PHQ9 = 15 [(goff.nullable) = true];
  string DMPCaseNumber = 16 [(goff.custom) = 'validate:"required"'];
  string BsnrCode = 17 [(goff.nullable) = true];
  goff.UUID BsnrId = 18 [(goff.nullable) = true];
  AdditionalContractsEnum AdditionalContracts = 19 [(goff.nullable) = true];
}

message PatientFrequency {
  bool IsEveryQuarter = 1;
  bool IsEveryTwoQuarter = 2;
}

message InsuranceProgrammes {
  bool SmokingCessation = 1;
  bool NutritionalCounseling = 2;
  bool PhysicalTraining = 3;
}

message PatientMedicalData {
  float Height = 1;
  int32 Weight = 2;
  string BloodPressureDiastolisch = 3;
  string BloodPressureSystolisch = 4;
  bool Smoker = 5;
}

enum EventEnrollType {
  EventEnrollType_Print_Save_Form = 0;
  EventEnrollType_Terminated = 1;
  EventEnrollType_CreateDocument = 2;
  EventEnrollType_Enroll = 3;
  EventEnrollType_SaveDocument = 4;
  EventEnrollType_FinishDocument = 5;
  EventEnrollType_PrintDocument = 6;
  EventEnrollType_BilledDocument = 7;
  EventEnrollType_FetchEnroll = 8;
}

message ActivatedTime {
  int64 StartDate = 1 [(goff.nullable) = true];
  int64 EndDate = 2 [(goff.nullable) = true];
}

message EnrollmentInfo {
  goff.UUID DoctorId = 1 [(goff.custom) = 'validate:"required"'];
  goff.UUID PatientId = 2 [(goff.custom) = 'validate:"required"'];
  string DMPCaseNumber = 3 [(goff.custom) = 'validate:"required"'];
  ParticipationForm ParticipationForm = 4 [(goff.custom) = 'validate:"required"'];
  PatientFrequency PatientFrequency = 5 [(goff.nullable) = true];
  InsuranceProgrammes InsuranceProgrammes = 6 [(goff.nullable) = true];
  PatientMedicalData PatientMedicalData = 7 [(goff.custom) = 'validate:"required"'];
  goff.UUID TreatmentDoctorId = 8 [(goff.custom) = 'validate:"required"'];
  ActivatedTime ActivatedTime = 9 [(goff.nullable) = true];
  patient_profile_common.InsuranceInfo InsuranceInfo = 10 [(goff.nullable) = true];
}

message EnrollmentInfoRequest {
  goff.UUID DoctorId = 1 [(goff.custom) = 'validate:"required"'];
  goff.UUID PatientId = 2 [(goff.custom) = 'validate:"required"'];
  string DMPCaseNumber = 3 [(goff.custom) = 'validate:"required"'];
  repeated ParticipationForm ParticipationForms = 4 [(goff.custom) = 'validate:"required"'];
  PatientFrequency PatientFrequency = 5 [(goff.nullable) = true];
  InsuranceProgrammes InsuranceProgrammes = 6 [(goff.nullable) = true];
  PatientMedicalData PatientMedicalData = 7 [(goff.custom) = 'validate:"required"'];
  goff.UUID TreatmentDoctorId = 8 [(goff.custom) = 'validate:"required"'];
}

message EnrollmentWithDocumentModel {
  EnrollmentInfoModel EnrollmentInfoModel = 1;
  repeated EnrollmentDocumentInfoModel EnrollmentDocumentInfoModel = 2;
}

message EnrollmentInfoModel {
  goff.UUID Id = 1;
  EnrollmentInfo EnrollmentInfo = 2;
}

message EnrollmentDocumentInfoModel {
  goff.UUID Id = 1;
  DocumentationOverview DocumentationOverview = 2;
  Patient Patient = 3;
  Doctor Doctor = 4;
  schein_common.Schein Schein = 5;
  int64 CreatedAt = 13 [(goff.nullable) = true];
}

message Patient {
  goff.UUID PatientId = 1;
  string FirstName = 2;
  string LastName = 3;
  patient_profile_common.DateOfBirth DateOfBirth = 4;
  int64 patientNumber = 5;
  string FullName = 6;
  patient_profile_common.InsuranceInfo ActiveInsurance = 7 [(goff.nullable) = true];
}

message Doctor {
  goff.UUID DoctorId = 1;
  string FirstName = 2;
  string LastName = 3;
  string Initial = 4;
  string Title = 5;
  string FullName = 6;
}

message DmpProgram {
  string DMPLabelingValue = 1;
  EnrollStatus EnrollStatus = 2;
  int64 StartDate = 3 [(goff.nullable) = true];
  Doctor Doctor = 4 [(goff.nullable) = true];
  Total TotalED = 5;
  Total TotalFD = 6;
  Total TotalPED = 7 [(goff.nullable) = true];
  Total TotalPHQ9_ED = 8 [(goff.nullable) = true];
  Total TotalPHQ9_FD = 9 [(goff.nullable) = true];
}

message Total {
  int32 Complete = 1;
  int32 InComplete = 2;
  int32 Submitted = 3;
}

message DmpPatientInfo {
  Patient Patient = 1;
  string InsuranceNumber = 2 [(goff.nullable) = true];
  repeated DmpProgram DmpProgram = 3;
}

message EdokuPatientOverview {
  goff.UUID Id = 1;
  Patient Patient = 2;
  Doctor Doctor = 3;
  DocumentationOverview Document = 4;
  int64 CreatedAt = 5 [(goff.nullable) = true];
}

enum FieldType {
  FieldType_Text = 0;
  FieldType_Number = 1;
  FieldType_Date = 2;
  FieldType_Checkbox = 3;
  FieldType_Radio = 4;
  FieldType_Nested = 5;
  FieldType_Unknown = 6;
}

message Option {
  string Name = 1;
  string Label = 2;
  string Placeholder = 3;
  FieldType FieldType = 4 [(goff.nullable) = true];
  string Unit = 5 [(goff.nullable) = true];
  bool IsFloat = 6 [(goff.nullable) = true];
  int32 DecimalDigits = 7 [(goff.nullable) = true];
  int32 MinLength = 8 [(goff.nullable) = true];
  int32 MaxLength = 9 [(goff.nullable) = true];
  repeated string HiddenFieldNames = 10;
  repeated string HiddenHeaderNames = 11;
}

message FieldValue {
  string Value = 1;
  FieldType FieldType = 2;
  string ValueUnit = 3 [(goff.nullable) = true];
  string Name = 4;
}

message Position {
  int32 P = 1;
  int32 E = 2;
}

message Field {
  string Name = 1;
  string Label = 2;
  bool IsRequire = 3;
  repeated FieldValue Values = 4 [(goff.nullable) = true];
  FieldType FieldType = 5;
  repeated Option Options = 6;
  bool IsVertical = 7;
  DocumentType DocumentType = 8;
  repeated Field Fields = 9;
  int32 PositionNoNumber = 11;
  string PositionValue = 12;
  string Header = 13;
  string Placeholder = 14;
  string Unit = 15 [(goff.nullable) = true];
  HeaderStatus HeaderStatus = 16 [(goff.nullable) = true];
  repeated string NestedFieldNames = 17 [(goff.nullable) = true];
  Position Position = 18;
  int32 MinLength = 19 [(goff.nullable) = true];
  int32 MaxLength = 20 [(goff.nullable) = true];
  bool IsFloat = 21 [(goff.nullable) = true];
  int32 DecimalDigits = 22 [(goff.nullable) = true];
  bool ReadOnly = 23 [(goff.nullable) = true];
  bool EmptyDateFormat = 24 [(goff.nullable) = true];
  string DisplayHeader = 25 [(goff.nullable) = true];
  bool LiveCheck = 26 [(goff.nullable) = true];
}

enum HeaderStatus {
  HeaderStatus_NotFilled = 0;
  HeaderStatus_Completed = 1;
  HeaderStatus_Incomplete = 2;
}

message DMP {
  string DMPLabelingValue = 1;
  repeated Field Fields = 2;
  HeaderStatus HeaderStatus = 3 [(goff.nullable) = true];
  string HeaderName = 4;
  Position Position = 5;
  string DisplayHeader = 6 [(goff.nullable) = true];
}

message Header {
  string Name = 1;
  repeated DMP DMPs = 2;
}

message DMPDocument {
  repeated Header HeadersED = 1;
  repeated Header HeadersFD = 2;
  repeated Header HeadersPD = 3;
  DMPValueEnum DMPValue = 4;
}

message EDOKUDocument {
  repeated Header Headers = 1;
  DMPValueEnum DMPValue = 2;
}

message QuarterToDateRange {
  int64 Quarter = 1 [(goff.custom) = 'validate:"required"'];
  int64 Year = 2 [(goff.custom) = 'validate:"required"'];
}

enum DMPValueEnum {
  DMPValueEnum_MellitusType1 = 0 [(goff.value) = "04"];
  DMPValueEnum_MellitusType2 = 1 [(goff.value) = "01"];
  DMPValueEnum_Brustkrebs = 2 [(goff.value) = "02"];
  DMPValueEnum_CoronaryArteryDisease = 3 [(goff.value) = "03"];
  DMPValueEnum_AsthmaBronchiale = 4 [(goff.value) = "05"];
  DMPValueEnum_COPD = 5 [(goff.value) = "06"];
  DMPValueEnum_ChronicHeartFailure = 6 [(goff.value) = "07"];
  DMPValueEnum_Depression = 7 [(goff.value) = "08"];
  DMPValueEnum_BackPain = 8 [(goff.value) = "09"];
  DMPValueEnum_EDO_SkinCancer = 9 [(goff.value) = "10"];
}

enum FileTypeEnum {
  FileTypeEnum_XML = 0;
  FileTypeEnum_PDF = 1;
}

enum EdmpTestModuleResultEnum {
  EdmpTestModuleResultEnum_Correct = 0;
  EdmpTestModuleResultEnum_Incorrect = 1;
}

enum DMPBillingHistoryFileType {
  DMPBillingHistoryFileType_Protocol = 0;
  DMPBillingHistoryFileType_Statistic = 1;
  DMPBillingHistoryFileType_Companion = 2;
  DMPBillingHistoryFileType_Billing = 3;
  DMPBillingHistoryFileType_TransferLetter = 4;
}

message DMPBillingFile {
  string FileName = 1;
  string FilePath = 2;
  string DisplayName = 3 [(goff.nullable) = true];
  DMPBillingHistoryFileType FileType = 4;
  string SddaIkNumber = 5 [(goff.nullable) = true];
}
enum DmpBillingStatus {
  DmpBillingStatus_Sent = 0 [(goff.value) = "Sent"];
  DmpBillingStatus_Confirmed = 1 [(goff.value) = "Confirmed"];
  DmpBillingStatus_Feedback_Available = 2 [(goff.value) = "Feedback Available"];
  DmpBillingStatus_Empty = 3 [(goff.value) = "Empty"];
  DmpBillingStatus_Sending = 4 [(goff.value) = "Sending"];
  DmpBillingStatus_Sending_Failed = 5 [(goff.value) = "Sending Failed"];
  DmpBillingStatus_Timeout_ReceiveEmail = 6 [(goff.value) = "Timeout Receive Email"];
  DmpBillingStatus_ReceiveEmail_Failed = 7 [(goff.value) = "Timeout Receive Email"];
}

message DmpBillingError {
  string Code = 1;
  string Message = 2;
}

enum TypeOfBilling {
  TypeOfBilling_Send_As_Real_Billing = 0;
  TypeOfBilling_Send_As_Real_Test_Billing = 1;
}

message BillingData {
  goff.UUID EnrollmentId = 1;
  edmp_sdda_common.DataCenter DataCenter = 2;
  repeated DMPBillingFile BillingFiles = 3;
  goff.UUID MessageId = 4 [(goff.nullable) = true];
  DmpBillingStatus DmpBillingStatus = 5 [(goff.nullable) = true];
}

message DMPBillingHistoryInfo {
  int32 Quarter = 1;
  int32 Year = 2;
  goff.UUID DoctorId = 3;
  goff.UUID SubmittedBy = 4;
  int64 SubmittedTime = 5 [(goff.nullable) = true];
  TypeOfBilling TypeOfBilling = 6;
  bool MarkAsCompletedBilling = 7;
  repeated BillingData BillingsData = 8;
  DmpBillingStatus DmpBillingStatus = 9;
  DmpBillingError DmpBillingError = 10 [(goff.nullable) = true];
  goff.UUID KvConnectId = 11;
  string BsnrCode = 12;
  repeated DMPBillingFile TransferLetters = 13;
}

message DMPDocumentationType {
  repeated goff.UUID DocumentationIds = 1;
  DMPDocumentationStatus DocumentationStatus = 2;
  int32 Total = 3;
  DocumentType DocumentType = 4;
}

enum DMPDocumentationStatus {
  DMPDocumentationStatus_Complete = 0;
  DMPDocumentationStatus_Incomplete = 1;
}

message EnrollmentWithDataCenter {
  goff.UUID EnrollmentId = 1 [(goff.custom) = 'validate:"required"'];
  edmp_sdda_common.DataCenter DataCenter = 2 [(goff.custom) = 'validate:"required"'];
}

enum FieldValidationResultType {
  FieldValidationResultType_XPMCheck = 0;
  FieldValidationResultType_SelfCheck = 1;
}

message Script {
  string Version = 1;
  string Name = 2;
  string Content = 3;
}

enum ErrorType {
  ErrorType_Error = 0;
  ErrorType_Warning = 1;
  ErrorType_Schema = 2; // TODO: enhance later
}

message FieldValidationResult {
  string FieldName = 1;
  string ErrorCode = 2;
  string ErrorMessage = 3;
  string HeaderName = 4 [(goff.nullable) = true];
  FieldValidationResultType FieldValidationResultType = 5;
  Script Script = 6 [(goff.nullable) = true];
  ErrorType ErrorType = 7;
  string BillingFileName = 8;
}

message DMPBillingFieldsValidationResult {
  goff.UUID DocumentId = 1;
  repeated FieldValidationResult FieldValidationResults = 2;
  bool IsPlausibility = 3;
  DMPBillingFile XPMResultFile = 4;
}

enum SequenceType {
  SequenceType_Billing = 0;
  SequenceType_Conpanion = 1;
  SequenceType_XKM = 2;
}
