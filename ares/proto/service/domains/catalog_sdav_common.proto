syntax = "proto3";

package catalog_sdav_common;
import "service/domains/catalog_utils_common.proto";
import "service/domains/common.proto";

import "goff.proto";

option go_package = "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdav_common";
option (goff.pointer_strict_mode) = true;

message GetSdavRequest {
    string Query = 1 [(goff.nullable) = true];
    string Filter = 2 [(goff.nullable) = true];
    int64 SelectedDate = 3;
    common.PaginationRequest Pagination = 4;
    catalog_sdav_common.SdavSearchType SearchType = 5 [(goff.nullable) = true];
}  

message AreaOfExpertise {
    string V = 1;
    string DN = 2;
}

enum SortField {
    Bsnr = 0 [ (goff.value) = "bsnr" ];
    Lanr = 1 [ (goff.value) = "lanr" ];
    AddressName = 2 [ (goff.value) = "addressName" ];
    Lastname = 3 [ (goff.value) = "lastName" ];
    Nbsnr = 4 [ (goff.value) = "nbsnr" ];
}

message SdavCatalog {
    string SdavId = 1;
    GeneralInfo GeneralInfo = 2; 
    DoctorInfo DoctorInfo = 3; 
    ContactInfo ContactInfo = 4;  
    catalog_utils_common.SourceType Source = 6;
    goff.UUID BsnrId = 7 [(goff.nullable) = true];
}
  
enum AddressType {
    Practice = 0 [(goff.value) = "Practice"];
    Hospital = 1 [(goff.value) = "Hospital"];
    Other = 2 [(goff.value) = "Other"];
}

message GeneralInfo {
    string Bsnr = 1 [(goff.custom) = 'validate:"required_unless=AddressType Other,Hospital"'];
    repeated string Nbsnr = 2;
    AddressType AddressType = 3 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
    string AddressName = 4 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
    string Street = 5 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
    string Number = 6 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
    string PostCode = 7 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
    string City = 8 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
}

message DoctorInfo {
    string Lanr = 1 [(goff.custom) = 'validate:"omitempty,len=9"'];
    repeated string TeamNumbers = 2 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
    string Title = 3 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
    string FirstName = 4 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
    string LastName = 5 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
    string Salutation = 6 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
    string IntendWord = 7 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
    string AdditionalName = 8 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
    repeated AreaOfExpertise AreaOfExpertises = 9 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
}

message ContactInfo {
    string PhoneNumber = 1 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
    string MobilePhoneNumber = 2 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
    string ContactPerson = 3 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
    string Email = 4 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
    string Fax = 5 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
    string Salutation = 6 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
}

enum SdavSearchType {
    SdavSearchType_BSNR = 0 [ (goff.value) = "SEARCH_TYPE_BSNR" ];
    SdavSearchType_LANR = 1 [ (goff.value) = "SEARCH_TYPE_LANR" ];
    SdavSearchType_NBSNR = 2 [ (goff.value) = "SEARCH_TYPE_NBSNR" ];
    SdavSearchType_ADDRESS_NAME = 3 [ (goff.value) = "SEARCH_TYPE_ADDRESS_NAME" ];
    SdavSearchType_NAME = 4 [ (goff.value) = "SEARCH_TYPE_NAME" ];
    SdavSearchType_BSNR_AND_LANR = 5 [ (goff.value) = "SEARCH_TYPE_BSNR_AND_LANR" ];
}