syntax = "proto3";

package error_code;
import "goff.proto";
option go_package = "git.tutum.dev/medi/tutum/ares/service/domains/error_code";

message CommonException {
    int32 Status = 1;
    string Message = 2;
    string ServerError = 3;
    repeated string ServerErrorParam = 4;
  }

enum ErrorCode {
    ErrorCode_ValidationError_NotActive_InsuranceInfo = 0;
    ErrorCode_CardReader_NotAvailable = 1;
    ErrorCode_CardReader_Error = 2;
    ErrorCode_Setting_TI_Not_Found = 3;
    ErrorCode_Patient_Not_Found = 4;
    ErrorCode_Cost_Unit_Not_Found = 5;
    ErrorCode_Doctor_Not_Found = 6;
    ErrorCode_Schein_Not_Found = 7;
    ErrorCode_SDEBM_Rule_Not_Found = 8;
    ErrorCode_Employee_Not_Found = 9;
    ErrorCode_Prescribe_Not_Found = 10;
    ErrorCode_Form_Not_Found = 11;
    ErrorCode_PrinterProfile_Not_Found = 12;
    ErrorCode_Login_Invalid_Verify_Password = 13;
    ErrorCode_Login_Invalid_New_Password = 14;
    ErrorCode_Login_Invalid_Current_Password = 15;
    ErrorCode_SDEBM_Service_Code_Existed = 16;
    ErrorCode_SDKT_Duplicated_VKNR = 17;
    ErrorCode_SDKT_Required_CatalogId = 18;
    ErrorCode_SDKT_Create_Failed = 19;
    ErrorCode_SDKT_Not_Found = 20;
    ErrorCode_SDEBM_Cannot_Deleted = 21;
    ErrorCode_BSNR_NOT_FOUND = 22;
    ErrorCode_NO_SCHEIN_BILLING_FOUND = 23;
    ErrorCode_NO_SCHEIN_BILLING_IN_QUARTER_FOUND = 24;
    ErrorCode_NO_DIAGNOSIS_BILLING_FOUND = 25;
    ErrorCode_NO_SERVICES_BILLING_FOUND = 26;
    ErrorCode_CAN_NOT_CREATE_ERROR_HISTORY = 27;
    ErrorCode_WAITING_ROOM_NOT_FOUND = 28;
    ErrorCode_SERVICE_CODE_USED_IN_TIMELINE = 29;
    ErrorCode_Value_Is_Required = 30;
    ErrorCode_Waiting_Room_Have_Patient = 31;
    ErrorCode_Exist_Patient_In_A_Waiting_Room = 32;
    ErrorCode_Contract_Not_Found = 33;
    ErrorCode_Record_Not_Found = 34;
    ErrorCode_Data_Invalid = 35;
    ErrorCode_Validation_MessageABRD613 = 36;
    ErrorCode_Validation_MessageABRD969 = 37;
    ErrorCode_Validation_MessageABRD514 = 38;
    ErrorCode_Validation_MessageABRD612 = 39;
    ErrorCode_Validation_MessageABRD786 = 40;
    ErrorCode_Validation_MessageP10470MM = 41;
    ErrorCode_Validation_MessageP10470MF = 42;
    ErrorCode_Validation_MessageP10470KM = 43;
    ErrorCode_Validation_MessageP10470KF = 44;
    ErrorCode_Validation_MessageP10480MM = 45;
    ErrorCode_Validation_MessageP10480MR = 46;
    ErrorCode_Validation_MessageP10480MZ = 47;
    ErrorCode_Validation_MessageP10480KM = 48;
    ErrorCode_Validation_MessageP10480KR = 49;
    ErrorCode_Validation_MessageABRD456 = 50;
    ErrorCode_Validation_MessageABRD887 = 51;
    ErrorCode_Validation_MessageABRD970 = 52;
    ErrorCode_Validation_MessageFieldIsRequiredFormat = 53;
    ErrorCode_Validation_MessageMedicineTypeRequireFormat = 54;
    ErrorCode_Validation_MessageMappingError = 55;
    ErrorCode_Validation_MessageNotBillingAble = 56;
    ErrorCode_Validation_MessageNotFillingForBilling = 57;
    ErrorCode_Validation_MessageRareDiseaseEu = 58;
    ErrorCode_Validation_MessageIfSG = 59;
    ErrorCode_Validation_MessageNotSuitablePermanent = 60;
    ErrorCode_Validation_MessageNeedPrimaryCode = 61;
    ErrorCode_CardReader_Validity_Card = 62;
    ErrorCode_CardReader_UnknownType_Card = 63;
    ErrorCode_CardReader_Error_VSDService = 64;
    ErrorCode_TIConnector_Error_Version_Not_Support = 65;
    ErrorCode_TIConnector_Error_Service_Not_Found = 66;
    ErrorCode_CardReader_Confirm_PN_Code_No_Successful = 67;
    ErrorCode_CardReader_Confirm_PN_Code_Successful = 68;
    ErrorCode_CardReader_Insurance_EndDate_Before_Current_Date = 69;
    ErrorCode_CardReader_Insurance_StartDate_After_Current_Date = 70;
    ErrorCode_CardReader_SDKT_Vknr_Invalid = 71;
    ErrorCode_CardReader_Proof_Of_Insurance_Invalid = 72;
    Warning_CardReader_PnResultCode_1 = 73;
    Warning_CardReader_PnResultCode_2 = 74;
    Warning_CardReader_PnResultCode_3 = 75;
    Warning_CardReader_PnResultCode_4 = 76;
    Warning_CardReader_PnResultCode_5 = 77;
    Warning_CardReader_PnResultCode_6 = 78;
    Warning_CardReader_PnErrorCode_114 = 79;
    Warning_CardReader_PnErrorCode_106 = 80;
    Warning_CardReader_PnErrorCode_107 = 81;
    Warning_CardReader_PnErrorCode_113 = 82;
    Warning_CardReader_PnErrorCode_4192 = 83;
    Warning_CardReader_PnErrorCode_102 = 84;
    Warning_CardReader_PnErrorCode_103 = 85;
    Warning_CardReader_PnErrorCode_104 = 86;
    Warning_CardReader_PnErrorCode_109 = 87;
    Warning_CardReader_PnErrorCode_110 = 88;
    Warning_CardReader_PnErrorCode_112 = 89;
    Warning_CardReader_PnErrorCode_4147 = 90;
    Warning_CardReader_PnErrorCode_12999 = 91;
    Warning_CardReader_PnErrorCode_101 = 92;
    Warning_CardReader_PnErrorCode_111 = 93;
    Warning_CardReader_PnErrorCode_4093 = 94;
    Warning_CardReader_PnErrorCode_3001 = 95;
    Warning_CardReader_PnErrorCode_12105 = 96;
    ErrorCode_CardReader_ErrorPn35WithErrorCode12103 = 97;
    ErrorCode_CardReader_ErrorPn6 = 98;
    ErrorCode_CardReader_ErrorPnNotEqual4AndListErrorCode = 99;
    ErrorCode_CardReader_PN4WithErrorCode = 100;
    ErrorCode_CardReader_ErrorCode300112105 = 101;
    ErrorCode_CardReader_ErrorCode30403039 = 102;
    ErrorCode_Patient_Not_Found_In_Con_File_Result = 103;
    ErrorCode_CardReader_Confirm_PN_Code_Invalid = 104;
    ErrorCode_SDAV_Cannot_Modified = 105;
    ErrorCode_Template_Is_Exist = 106;
    ErrorCode_Validation_Document_88130 = 107;
    ErrorCode_KV_Connect_Account_Not_Found = 108;
    ErrorCode_Validation_EDMPSuggestion = 109 [ (goff.value) = "EDMPSuggestion" ];
    ErrorCode_TIConnector_Not_Found = 110;
    ErrorCode_TI_Status_Not_Found = 111;
    ErrorCode_HeaderFooter_Is_Exist = 112;
    ErrorCode_TI_Can_Not_Check_Certificate_Expire = 113;
    ErrorCode_TI_Certificate_Expire_Not_Found = 114;
    ErrorCode_Erezept_Invalid_Request = 115;
    ErrorCode_Device_Not_Found = 116;
    ErrorCode_EDMP_Case_Number_Invalid = 117;
    ErrorCode_EDMP_Enrollment_Not_Found = 118;
    ErrorCode_EDMP_Not_Allowed = 119;
    ErrorCode_EREZEPT_TRAINING_DOCTOR_AUTHORIZATION = 120;
    ErrorCode_Validation_ICD_Code_Not_In_Master_Data = 121;
    ErrorCode_CANNOT_SEARCH_SERVICE_CODE = 122;
    ErrorCode_CERTAINTY_IS_REQUIRED = 123;
    ErrorCode_Email_Not_Found = 124;
    ErrorCode_EDMP_ED_Exist = 125;
    ErrorCode_Validation_Missing_Treatment_Time = 126;
    ErrorCode_Missing_Referral_Doctor = 127;
    ErrorCode_EDMP_ED_Not_Exist = 128;
    ErrorCode_Validation_Missing_Pseudo_GNR = 129;
    ErrorCode_kvConnect_CertificateOlderThanOnKvServer = 130;
    ErrorCode_empty = 131;
    ErrorCode_CardReader_InsHasBeenTerminated = 132;
    ErrorCode_CardReader_InsHasBeenDeactive = 133;
    ErrorCode_CardReader_InsHasBeenRestrictArea = 134;
    ErrorCode_CardReader_IK_Invalid_Expired = 135;
    ErrorCode_Common_DataExisting = 136;
    ErrorCode_EDMP_Get_Document_Not_Found = 137;
    ErrorCode_ValidationError_ScheinHasTimeline = 138;
    ErrorCode_Real_Billing_Already_Sent = 139;
    ErrorCode_Cannot_Correct_Billing_Before_Real_Billing = 140;
    ErrorCode_IK_NotFound = 141;
    ErrorCode_MissICDCodeToBilling = 142;
    ErrorCode_InvalidServiceCode = 143;
    ErrorCode_ServiceValidationError = 144;
    ErrorCode_Service_Timeline_Not_Found = 145;
    ErrorCode_SearchSdkt = 146;
    ErrorCode_Validation_Missing_ScheinId = 147;
    ErrorCode_kvconnect_invalidSignature = 148;
    ErrorCode_kvconnect_tss_fail = 149;
    ErrorCode_kvconnect_serverCert_invalid = 150;
    ErrorCode_kvconnect_login = 151;
    ErrorCode_kvconnect_SendCSR = 152;
    ErrorCode_Validation_Invalid_Pseudo_GNR = 153;
    ErrorCode_Validation_RangeAge = 154;
    ErrorCode_kvconnect_serverCert_expired = 155;
    ErrorCode_kvconnect_tss_timeout = 156;
    ErrorCode_Empty_InsuranceInfo = 157;
    ErrorCode_Validation_ReplacedWithServiceCodeWhenBilling = 158 [(goff.value) = "ReplacedWithServiceCodeWhenBilling"];
    ErrorCode_Cannot_Delete_Schein = 159;
    ErrorCode_ServerError_KvBilling = 160;
    ErrorCode_ValidationError_InsuranceInfo_Not_Valid_In_Quarter = 161;
    ErrorCode_ValidationError_ScheinHasActiveDocuments = 162;
    ErrorCode_ValidationError_OpsMustInList = 163;
    ErrorCode_ValidationError_GnrMustInList = 164;
    ErrorCode_Validation_Must_Not_Present_Treatment_Time = 165;
    ErrorCode_ValidationError_TSS_Surcharge_Acute = 166 [(goff.value) = "tss_surcharge_acute_error"];
    ErrorCode_ValidationError_TSS_Surcharge_Routine = 167 [(goff.value) = "tss_surcharge_routine_error"];
    ErrorCode_ValidationError_TSS_Surcharge_Common = 168 [(goff.value) = "tss_surcharge_error_common"];
    ErrorCode_ValidationError_TSS_Suggestion = 169 [(goff.value) = "tss_surcharge_suggestion"];
    ErrorCode_ValidationError_VknrIsRequired = 170;
    ErrorCode_ValidationError_BSNR_NOT_VALID_IN_QUARTER = 171;
    ErrorCode_ValidationError_MultipleActive_InsuranceInfo = 172;
    ErrorCode_ValidationError_Form_IcdIsRequired = 173;
    ErrorCode_CardReader_Not_Found_Patient_Card = 174;
    ErrorCode_GOA_Existed = 175;
    ErrorCode_Create_Goa_Catalog_Failed = 176;
    ErrorCode_ValidationError_EDMP_Not_Supported_Version = 177;
    ErrorCode_CardReader_UnknownType_Card_Status = 178;
    ErrorCode_Sdik_Existed = 179;
    ErrorCode_Patient_Missing_Gender = 180;
    ErrorCode_Insurance_InValid_For_PrivateSchein = 181;
    ErrorCode_PrivateContractGroup_NotFound = 182;
    ErrorCode_CardReader_ErrorCode105 = 183;
    ErrorCode_LDAP_AddressBookNotFound = 184;
    ErrorCode_LDAP_MailNotFound = 185;
    ErrorCode_CardReader_Not_Found_Doctor_Card = 186;
    ErrorCode_Validation_Ad4125_InValid = 187;
    ErrorCode_CardReader_Invalid_Vsd_TimeStamp = 188;
    ErrorCode_CardReader_Invalid_Insurance = 189;
    ErrorCode_CardReader_Cannot_Delete_Insurance = 190;
    ErrorCode_GoaService_ExcludedCode = 191;
    ErrorCode_Companion_NotReady = 192;
    ErrorCode_EDMP_Gender_Not_Allowed = 193;
    ErrorCode_Sdkt_Invalid_Validity = 194;
    ErrorCode_Sdkt_Invalid_Restrict_Region = 195;
    ErrorCode_ValidationError_MustHaveRVSA = 196;
    ErrorCode_ValidationError_RequireLanr = 197 [(goff.value) = "InVertretungFuer_LANR"];
    ErrorCode_ValidationError_RequireBsnr = 198 [(goff.value) = "InVertretungFuer_BSNR"];
    ErrorCode_Schein_Validation_Patient_Insurance_Not_Found = 199;
    ErrorCode_Sdik_Assigned_To_Schein = 200;
    ErrorCode_Validation_MessageVERT647 = 201;
    ErrorCode_TI_Card_Not_Found = 202;
    ErrorCode_SMCB_Card_Not_Found = 203;
    ErrorCode_CardReader_ReadCardDate_Not_Found = 204;
    ErrorCode_EAU_NotTransmitted = 205;
    ErrorCode_Record_Exist = 206;
    ErrorCode_HpmFunction_Not_Available = 207;
    ErrorCode_Zitadel_Resource_Already_Exits = 208 [(goff.value) = "Resource_Already_Exits"];
    ErrorCode_Zitadel_Resource_Not_Found = 209 [(goff.value) = "Resource_Not_Found"];
    ErrorCode_Zitadel_Resource_Not_Changed = 210 [(goff.value) = "Resource_Not_Changed"];
    ErrorCode_CardReader_Invalid_CardType = 211;
    ErrorCode_GoaService_GoaNumber_Not_Found = 212;
    ErrorCode_GoaService_Invalid_GOA =  213;
    ErrorCode_DoctorLetter_Timeline_Not_Found = 215;
    ErrorCode_ValidationError_Attachment = 216;
    ErrorCode_CardReader_Invalid_Vknr = 217;
    ErrorCode_CardReader_Invalid_Sdik = 218;
    ErrorCode_EAU_SendPrintOutToInsurance = 219;
    ErrorCode_EAB_RecordNotFound = 220;
    ErrorCode_EAB_SettingNotFound = 221;
    ErrorCode_ServiceCodeInMasterDataNotFound = 222;
    ErrorCode_Validation_MessageABRG669 = 223;
    ErrorCode_Diga_PznAlreadyExists = 224;
    ErrorCode_Diga_PznExpired = 225;
    ErrorCode_EAB_Mail_Invalid = 226;
    ErrorCode_EAB_History_RecordNotFound = 227;
    ErrorCode_Cannot_Delete_Timeline_Entry = 228;
    ErrorCode_Add_Organization_Failed = 229;
    ErrorCode_Organization_Exists = 230;
    ErrorCode_GrantPRO_Failed = 231;
    ErrorCode_GrantCAL_Failed = 232;
    ErrorCode_Organization_Deactivated = 233;
    ErrorCode_Invalid_IK_Number = 234;
    ErrorCode_MobileCard_SettingNotFound = 235;
    ErrorCode_Validation_Abrd1564 = 236;
    ErrorCode_WarningForGroupDoctorValidate = 237;
    Hint_ABRD1062 = 238;
    ErrorCode_Arriba_Not_Eligible = 239;
    ErrorCode_PrinterProfileGroup_Not_Found = 240;
    ErrorCode_LDAPConnection_Failed = 241;
    ErrorCode_Arriba_Session_Not_Finished = 242;
    ErrorCode_Device_Wrong_Format = 243;
    ErrorCode_KIM_Account_Invalid = 244;
    ErrorCode_TI_Setting_Not_Found = 245;
    // ErrorCode_UniqueFileAndFolder_GdtExport = 246;
    ErrorCode_UniqueFileAndFolder_GdtImport = 247;
    ErrorCode_Forbidden = 248;
    ErrorCode_Unauthorized = 249;
    ErrorCode_Insurance_Has_Assign_Schein = 250;
    ErrorCode_Duplicate_LHMItem = 251;
    ErrorCode_Cost_Unit_Has_Expired = 252;
    ErrorCode_Cost_Unit_IsNot_Available_In_KvRegion = 253;
    ErrorCode_CanNot_Change_InsuranceType = 254;
    ErrorCode_Missmatch5005And5050 = 255;
    ErrorCode_UV_GOA_Existed = 256;
    ErrorCode_DocumentType_NameExisted = 257;
    ErrorCode_DocumentType_AbbrExisted = 258;
    ErrorCode_CardReader_Invalid_KVK = 259;
    ErrorCode_ExportReportFailed = 260;
    ErrorCode_Insurance_InValid_For_BgSchein = 261; 
    ErrorCode_PrinterHost_Not_Found = 262;
    ErrorCode_PrinterSetting_IsInvalid = 263;
    ErrorCode_Printer_Connection_Failed = 264;
    ErrorCode_Validation_Abrg1565 = 265;
    ErrorCode_Goa_Not_Found = 266;
    ErrorCode_Sdebm_Catalog_Existed = 267; 
    ErrorCode_Sdebm_Catalog_Not_Found = 268;
    ErrorCode_Sdik_NotFound = 269;
    ErrorCode_UV_GOA_Not_Found = 270;
    ErrorCode_Masterdata_GenerateApiKey_Failed = 271;
    ErrorCode_PTV_Import_Older_Than_Latest = 272;
}
