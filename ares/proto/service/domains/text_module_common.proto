syntax = "proto3";

package text_module_common;

import "goff.proto";
import "repo/mvz/encounter_common.proto";

option go_package = "git.tutum.dev/medi/tutum/ares/service/domains/api/text_module_common";
option (goff.pointer_strict_mode) = true;

enum TextModuleUseFor {
  TextModuleUseFor_Anamnesis = 0;
  TextModuleUseFor_Cave = 1;
  TextModuleUseFor_Findings = 2;
  TextModuleUseFor_Note = 3;
  TextModuleUseFor_Therapy = 4;
  TextModuleUseFor_OmimGChain = 5;
  TextModuleUseFor_Form = 6;
  TextModuleUseFor_BMP = 7;
  TextModuleUseFor_Doctorletter = 8;
  TextModuleUseFor_HGNC = 9;
}

enum ModuleType {
  ModuleType_FreeText = 0;
  ModuleType_Questionnaire = 1;
}

enum TextModuleStatus {
  TextModuleStatus_Active = 0;
  TextModuleNodeType_Deactive = 1;
}

message TextModuleContent {
  string Text = 1;
  repeated TextModuleNode Data = 2;
}

message TextModuleNode {
  TextModuleNodeType Type = 1;
  repeated TextModuleNode Children = 2;
  // data for each type
  TextModuleTextNode Text = 3 [ (goff.nullable) = true ];
  TextModulePlaceholderNode Placeholder = 4 [ (goff.nullable) = true ];
  TextModuleQuestionnaireNode Questionnaire = 5 [ (goff.nullable) = true ];
  TextModuleVariableNode Variable = 6 [ (goff.nullable) = true ];
  repo_encounter.AdditionalInfoParent OmimG = 7 [ (goff.nullable) = true ];
}

enum TextModuleNodeType {
  TextModuleNodeType_Text = 0;
  TextModuleNodeType_Placeholder = 1;
  TextModuleNodeType_Questionnaire = 2;
  TextModuleNodeType_Variable = 3;
  TextModuleNodeType_AdditionalInfo = 4;
  TextModuleNodeType_LineBreak = 5;
}

message TextModuleTextNode { string Value = 1; }

message TextModulePlaceholderNode {
  string Label = 1;
  string Value = 2;
}

message TextModuleQuestionnaireNode {
  string Label = 1;
  QuestionnaireQuestionType QuestionType = 2;
  repeated Answer Answers = 3;
  string Value = 4 [ (goff.nullable) = true ];
}

enum QuestionnaireQuestionType {
  QuestionnaireQuestionType_SingleSelection = 0;
  QuestionnaireQuestionType_MultipleSelection = 1;
  QuestionnaireQuestionType_Freetext = 2;
}

message Answer {
  string Label = 1;
  AnswerType AnswerType = 2;
  string Value = 3;
}

enum AnswerType {
  AnswerType_Select = 0;
  AnswerType_Others = 1;
}

message TextModuleVariableNode {}

message TextModule {
  goff.UUID Id = 1 [ (goff.nullable) = true, (goff.custom) = 'bson:"omitempty"' ];
  repeated TextModuleUseFor UseFor = 2 [ (goff.custom) = 'validate:"required"' ];
  string TextShortcut = 3 [ (goff.custom) = 'validate:"required"' ];
  ModuleType ModuleType = 4 [ (goff.custom) = 'validate:"required"' ];
  TextModuleContent Content = 5 [ (goff.custom) = 'validate:"required"' ];
  TextModuleStatus Status = 6 [ (goff.custom) = 'validate:"required"' ];
  goff.UUID BsnrId = 7 [ (goff.nullable) = true ];
}

message TextModulePaginationRequest {
  string Query = 1;
  int64 Page = 2 [ (goff.nullable) = true ];
  int64 PageSize = 3 [ (goff.nullable) = true ];
  repeated text_module_common.TextModuleUseFor UseFors = 4;
  goff.UUID BsnrId = 5 [ (goff.nullable) = true ];
}

message TextModulePaginationResponse {
  repeated TextModule TextModules = 1;
  int64 Page = 2;
  int64 Total = 3;
}
