syntax = "proto3";

package app_profile;

import "goff.proto";
import "service/domains/common.proto";
import "service/domains/patient_profile_common.proto";
import "app/admin/admin_app.proto";
import "service/domains/bsnr_common.proto";

option go_package = "git.tutum.dev/medi/tutum/ares/app/profile/api/profile_bff";

option (goff.nats_subject) = "api.app.profile";

message EmployeeProfileResponse {
    goff.UUID id = 1;
    string firstName = 2;
    string lastName = 3;
    string title = 4;
    patient_profile_common.Salutation Salutation = 5 [(goff.nullable) = true];
    int64 dob = 6 [(goff.nullable) = true];
    string phone = 7 [(goff.nullable) = true];
    string email = 8 [(goff.nullable) = true];
    string address = 9 [(goff.nullable) = true];
    bool hasHzvContracts = 10;
    bool hasFavContracts = 11;
    string bsnr = 12;
    string havgId = 13;
    string mediId = 14;
    string lanr = 15;
    patient_profile_common.AdditionalName AdditionalName = 16 [ (goff.nullable) = true ];
    patient_profile_common.IntendWord IntendWord = 17 [ (goff.nullable) = true ];
    string Initial = 18;
    repeated string DmpPrograms = 19 [ (goff.nullable) = true ];
    bool markAsBillingDoctor = 20;
    string pseudoLanr = 21 [(goff.nullable) = true];
    repeated string teamNumbers = 22 [(goff.nullable) = true];
    string DoctorStamp = 23;
    string BsnrCity = 24;
    string BsnrPracticeStamp = 25;
    repeated common.BankInformation BankInformations = 26;
    bool markAsEmployedDoctor = 27;
    goff.UUID ResponsibleDoctorId = 28 [ (goff.nullable) = true ];
    goff.UUID RepresentativeDoctorId = 29 [ (goff.nullable) = true ];
    string bsnrName = 30 [ (goff.nullable) = true ];
    string bsnrStreet = 31;
    string bsnrNumber = 32;
    string bsnrPostCode = 33;
    string bsnrPhoneNumber = 34;
    string bsnrFaxNumber = 35;
    repeated string areaOfExpertise = 36 [(goff.nullable) = true];
    string OrgId = 37;
    bool isParticipationActive = 38;
    repeated common.UserType types = 39;
    string HavgVpId = 40;
    string MediVpId = 41;
    string BsnrFacilityType = 42;
    string HpmEndpoint = 43;
    repeated app_admin.Contract hzvContracts = 44;
    repeated app_admin.Contract favContracts = 45;
    string UserName = 46;
    goff.UUID DeviceId = 47;
    bool IsDoctor = 48;
    string JobDescription = 49;
    string Okv = 50;
    repeated goff.UUID bsnrIds = 51;
    repeated string bsnrs = 52;
    goff.UUID bsnrId = 53 [(goff.nullable) = true];
    common.EHKSType EHKSType = 54 [(goff.nullable) = true];
}

message MyEmployeeProfileResponse {
    goff.UUID id = 1;
    string firstName = 2;
    string lastName = 3;
    string title = 4;
    patient_profile_common.Salutation Salutation = 5 [(goff.nullable) = true];
    int64 dob = 6 [(goff.nullable) = true];
    string phone = 7 [(goff.nullable) = true];
    string email = 8 [(goff.nullable) = true];
    string address = 9 [(goff.nullable) = true];
    bool hasHzvContracts = 10;
    bool hasFavContracts = 11;
    string bsnr = 12;
    string havgId = 13;
    string mediId = 14;
    string lanr = 15;
    repeated app_admin.Contract hzvContracts = 16;
    repeated app_admin.Contract favContracts = 17;

    patient_profile_common.AdditionalName AdditionalName = 18 [ (goff.nullable) = true ];
    patient_profile_common.IntendWord IntendWord = 19 [ (goff.nullable) = true ];
    string Initial = 20;
    repeated string DmpPrograms = 21 [ (goff.nullable) = true ];
    bool markAsBillingDoctor = 22;
    bool markAsEmployedDoctor = 23;
    goff.UUID ResponsibleDoctorId = 24 [ (goff.nullable) = true ];
    goff.UUID RepresentativeDoctorId = 25 [ (goff.nullable) = true ];
    string JobDescription = 26 [ (goff.nullable) = true ];
    string bsnrName = 27 [ (goff.nullable) = true ];
    string DoctorStamp = 28;
    bool isParticipationActive = 29;
    repeated common.UserType Types = 30;
    goff.UUID DeviceId = 31 [ (goff.nullable) = true ];
    string externalId = 32;
    string userName = 33;
    string OrgId = 34;
    string HpmEndpoint = 35;
    repeated common.BankInformation BankInformations = 36;
    repeated string AreaOfExpertise = 37 [ (goff.nullable) = true ];
    goff.UUID bsnrId = 38 [ (goff.nullable) = true,(goff.custom) = 'bson:"omitempty"'];
    repeated string bsnrs = 39;
    repeated goff.UUID bsnrIds = 40 [(goff.nullable) = true];
    common.EHKSType EHKSType = 41 [(goff.nullable) = true];
    bool IsDoctor = 42;
}

message Practice {
    string bsnr = 1;
    string name = 2;
    int64 endDate = 3;
    int64 startDate = 4;
}

message EmployeeProfilesResponse {
    repeated EmployeeProfileResponse profiles = 1;
}

message GetByIdsRequest {
    repeated goff.UUID originalIds = 1 [(goff.custom) = 'validate:"required"'];
}

message GetByBsnrRequest {
    goff.UUID BsnrId = 1 [(goff.custom) = 'validate:"required"'];
}

message GetByLanrIDRequest {
    string lanr = 1;
}

message GetByHzvIDRequest {
    string havgId = 1;
}

message GetByMediIDRequest {
    string mediId = 1;
}

message GetAllInitialResponse {
    repeated string Data = 1;
}

message CareProvider {
    goff.UUID Id = 1;
    string Name = 2;
    repeated bsnr_common.BSNR Bsnrs = 3;
}

message GetListBsnrOfEmployeeResponse {
   repeated CareProvider CareProviders = 1;
}

service ProfileBff {
    option (goff.name) = "";
    rpc GetAllInitial (goff.Empty) returns (GetAllInitialResponse){
        option (goff.secured) = "CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN";
      }
    rpc getMyEmployeeProfile (goff.Empty) returns (MyEmployeeProfileResponse) {
        option (goff.secured) = "CARE_PROVIDER_MEMBER_PRE_SWITCH, CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN";
    }
    rpc getEmployeeProfileByIds (GetByIdsRequest) returns (EmployeeProfilesResponse){
        option (goff.secured) = "PATIENT, CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN, CARE_PROVIDER_MEMBER_PRE_SWITCH";
    }
    rpc getEmployeeProfilesByBsnrId (GetByBsnrRequest) returns (EmployeeProfilesResponse){
        option (goff.secured) = "PATIENT, CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN, CARE_PROVIDER_MEMBER_PRE_SWITCH";
    }
    rpc getByLanrID (GetByLanrIDRequest) returns (EmployeeProfileResponse){
        option (goff.secured) = "CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN, CARE_PROVIDER_MEMBER_PRE_SWITCH";
    }
    rpc getByHzvID (GetByHzvIDRequest) returns (EmployeeProfileResponse){
        option (goff.secured) = "CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN, CARE_PROVIDER_MEMBER_PRE_SWITCH";
    }
    rpc getByMediID (GetByMediIDRequest) returns (EmployeeProfileResponse){
        option (goff.secured) = "CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN, CARE_PROVIDER_MEMBER_PRE_SWITCH";
    }
    rpc getEmployeeByIds (GetByIdsRequest) returns (EmployeeProfilesResponse){
        option (goff.secured) = "PATIENT, CARE_PROVIDER_MEMBER, CARE_PROVIDER_ADMIN, CARE_PROVIDER_MEMBER_PRE_SWITCH";
    }
    rpc GetListBsnrOfEmployee (goff.Empty) returns (GetListBsnrOfEmployeeResponse) {
       option (goff.secured) = "CARE_PROVIDER_MEMBER";
    }
}
