syntax = "proto3";

package companion_dm_app;

import "goff.proto";

option go_package = "git.tutum.dev/medi/tutum/ares/app/companion/dm_app";
option (goff.nats_subject) = "api.app.companion";
option (goff.pointer_strict_mode) = true;

enum SourceTypeEnum {
  SourceTypeEnum_LOCAL = 0 [ (goff.value) = "local" ];
  SourceTypeEnum_FTP = 1 [ (goff.value) = "ftp" ];
  SourceTypeEnum_SMB = 2 [ (goff.value) = "smb" ];
}

enum CharacterEncoding {
  CharacterEncoding_IBM437 = 0 [ (goff.value) = "IBM437" ];
  CharacterEncoding_7Bit = 1 [ (goff.value) = "7-bit" ];
  CharacterEncoding_ISO8859 = 2 [ (goff.value) = "ISO8859-1 ANSI CP 1252" ];
}

message GetFolderStateRequest {
  string Folder = 1 [ (goff.custom) = 'validate:"required"' ];
  string Host = 2 [(goff.nullable) = true];
  string Username = 3 [(goff.nullable) = true];
  string Password = 4 [(goff.nullable) = true];
  SourceTypeEnum SourceType = 5;
  goff.UUID settingId = 6;
  bool recursive = 7;
}

message GetFolderStateResponse {
  map<string, int64> folderState = 1;
}

message FileUploadInfo {
  string FilePath = 1;
  string PresignedUrl = 2;
}

message UploadFilesRequest {
  repeated FileUploadInfo listFileUploadInfo = 1;
  string Folder = 2;
  string Host = 3 [(goff.nullable) = true];
  string Username = 4 [(goff.nullable) = true];
  string Password = 5 [(goff.nullable) = true];
  SourceTypeEnum SourceType = 6;
  bool deleteAfterUpload = 7;
}

message WatchFolderRequest {
  string Folder = 1 [ (goff.custom) = 'validate:"required"' ];
  string Host = 2 [(goff.nullable) = true];
  string Username = 3 [(goff.nullable) = true];
  string Password = 4 [(goff.nullable) = true];
  SourceTypeEnum SourceType = 5;
  goff.UUID settingId = 6;
  bool recursive = 7;
}

message ExportGdtDocumentRequest {
  string PresignUrl = 1;
  string FileName = 2;
  string Folder = 3;
  string Host = 4 [(goff.nullable) = true];
  string Username = 5 [(goff.nullable) = true];
  string Password = 6 [(goff.nullable) = true];
  SourceTypeEnum SourceType = 7;
  string ExternalAppFilePath = 8 [(goff.nullable) = true];
}

message CheckGdtImportDocumentRequest {
  string Filename = 1 [ (goff.custom) = 'validate:"required"' ];
  string Folder = 2 [ (goff.custom) = 'validate:"required"' ];
  string Host = 3 [(goff.nullable) = true];
  string Username = 4 [(goff.nullable) = true];
  string Password = 5 [(goff.nullable) = true];
  SourceTypeEnum SourceType = 6;
  int64 LastModifyTime = 7;
  goff.UUID SettingId = 8;
  CharacterEncoding CharacterEncoding = 9 [ (goff.custom) = 'validate:"required"' ];
}

message RemoveGdtDocumentRequest {
  string Filename = 1 [ (goff.custom) = 'validate:"required"' ];
  string Folder = 2;
  string Host = 3 [(goff.nullable) = true];
  string Username = 4 [(goff.nullable) = true];
  string Password = 5 [(goff.nullable) = true];
  SourceTypeEnum SourceType = 6;
  string pdfFileName = 7 [(goff.nullable) = true];
}

message CheckGdtImportDocumentResponse {
  int64 ModifyTime = 1;
  bool IsChanged = 2;
  string GdtDocumentContent = 3;
}

message StopWatchFolderRequest {
  SourceTypeEnum SourceType = 1;
  goff.UUID settingId = 2;
}

message HandleDocumentRequest {
  string Filename = 1;
  string Folder = 2 [ (goff.custom) = 'validate:"required"' ];
  string Host = 3 [(goff.nullable) = true];
  string Username = 4 [(goff.nullable) = true];
  string Password = 5 [(goff.nullable) = true];
  SourceTypeEnum SourceType = 6;
  int64 LastModifyTime = 7;
  goff.UUID SettingId = 8;
  CharacterEncoding CharacterEncoding = 9 [ (goff.custom) = 'validate:"required"' ];
  string FileExt = 10;
}

message CheckExistedDocumentResponse {
  bool IsExisted = 1;
}

service DmApp {
  rpc GetFolderState(GetFolderStateRequest) returns (GetFolderStateResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc UploadFiles(UploadFilesRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc WatchFolder(WatchFolderRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc ExportGdtDocument(ExportGdtDocumentRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc CheckGdtImportDocument(CheckGdtImportDocumentRequest) returns (CheckGdtImportDocumentResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc StopWatchFolder(StopWatchFolderRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc RemoveGdtDocument(RemoveGdtDocumentRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc CheckExistedDocument(HandleDocumentRequest) returns (CheckExistedDocumentResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc HandleGdtImportDocument(HandleDocumentRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
}
