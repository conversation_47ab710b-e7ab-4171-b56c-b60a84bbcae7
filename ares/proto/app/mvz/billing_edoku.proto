syntax = "proto3";

package app_mvz_billing_edoku;

import "service/domains/billing_history_common.proto";
import "service/domains/billing_edoku_common.proto";
import "service/domains/common.proto";
import "goff.proto";
import "service/domains/edmp_common.proto";

option go_package = "git.tutum.dev/medi/tutum/ares/app/mvz/api/billing_edoku";
option (goff.nats_subject) = "api.app.mvz";

message GetValidationListRequest {
  common.YearQuarter Quarter = 1 [(goff.custom) = 'validate:"required"'];
  goff.UUID BsnrId = 2 [(goff.custom) = 'validate:"required"'];
  string DocumentType = 3 [(goff.custom) = 'validate:"required"'];
  bool OpenPreviousQuarter = 4;
}
message GetValidationListResponse {
  repeated billing_edoku_common.BillingValidationListModel BillingValidationList = 1;
  int32 TotalPatient = 2;
}

message TroubleshootRequest {}
message TroubleshootResponse {}

message CreateBillingRequest {
  repeated goff.UUID DocumentIds = 1 [(goff.custom) = 'validate:"required"'];
  common.YearQuarter Quarter = 2 [(goff.custom) = 'validate:"required"'];
  string Bsnr = 3 [(goff.custom) = 'validate:"required"'];
  edmp_common.DMPValueEnum DMPValue = 4 [(goff.custom) = 'validate:"required"'];
  bool IsOpenPreviousQuarter = 5;
}
message CreateBillingResponse {
  bool Status = 1;
  goff.UUID DMPBillingHistoryId = 2;
  repeated edmp_common.DMPBillingFieldsValidationResult DMPBillingFieldsValidationResults = 3;
}

message GetBillingSummaryRequest {}
message GetBillingSummaryResponse {}

message PrepareForShippingRequest {
  goff.UUID BillingHistoryId = 1 [(goff.custom) = 'validate:"required"'];
}

message SendMailRequest {
  goff.UUID BillingHistoryId = 1 [(goff.custom) = 'validate:"required"'];
  goff.UUID SenderMailSettingId = 2 [(goff.custom) = 'validate:"required"'];
  string TestMailTo = 3 [(goff.nullable) = true];
}

message DownloadBillingFileRequest {}
message DownloadBillingFileResponse {}

message UndoSubmissionRequest {
  goff.UUID BillingHistoryId = 1 [(goff.custom) = 'validate:"required"'];
}

message GetDispatchListRequest {
  string Query = 1 [(goff.nullable) = true];
}
message GetDispatchListResponse {
  repeated billing_edoku_common.BillingHistoryModel BillingHistories = 1;
}

message GetEdokuBillingSelectionResponse {
  repeated common.YearQuarter Quarters = 1;
  repeated billing_history_common.BSNR Bsnrs = 2;
  repeated string DocumentTypes = 3;
}

message CheckForValidationRequest {
  repeated goff.UUID DocumentIds = 1 [(goff.custom) = 'validate:"required"'];
  common.YearQuarter Quarter = 2 [(goff.custom) = 'validate:"required"'];
  string Bsnr = 3 [(goff.custom) = 'validate:"required"'];
}

message CheckForValidationResponse {
  bool Status = 1;
  repeated edmp_common.DMPBillingFieldsValidationResult DMPBillingFieldsValidationResults = 2;
}

message GetBillingHistoryRequest {
  goff.UUID BillingHistoryId = 1 [(goff.custom) = 'validate:"required"'];
}

message GetBillingHistoryResponse {
  billing_edoku_common.BillingHistoryModel BillingHistory = 1;
}

message EventBillingEDokuStatusChanged {
  goff.UUID BillingHistoryId = 1;
  billing_edoku_common.BillingStatus Status = 2;
}

message GetEdokuDocumentByIdsRequest{
  repeated goff.UUID DocumentIds = 1 [(goff.custom) = 'validate:"required"'];
}

message GetEdokuDocumentByIdsResponse{
  repeated edmp_common.DocumentationOverview Documents = 1;
}
  
service BillingEDokuApp {
  rpc Troubleshoot(TroubleshootRequest) returns (TroubleshootResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetValidationList(GetValidationListRequest) returns (GetValidationListResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc CreateBilling(CreateBillingRequest) returns (CreateBillingResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetBillingSummary(GetBillingSummaryRequest) returns (GetBillingSummaryResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc PrepareForShipping(PrepareForShippingRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc SendMail(SendMailRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc DownloadBillingFile(DownloadBillingFileRequest) returns (DownloadBillingFileResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc UndoSubmission(UndoSubmissionRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetDispatchList(GetDispatchListRequest) returns (GetDispatchListResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetEdokuBillingSelection(goff.Empty) returns (GetEdokuBillingSelectionResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc CheckForValidation(CheckForValidationRequest) returns (CheckForValidationResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetBillingHistory(GetBillingHistoryRequest) returns (GetBillingHistoryResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc GetEdokuDocumentByIds(GetEdokuDocumentByIdsRequest) returns (GetEdokuDocumentByIdsResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
}