syntax = "proto3";

package app_mvz_schein;

import "goff.proto";
import "service/domains/common.proto";
import "service/domains/schein_common.proto";
import "service/domains/catalog_sdkt_common.proto";
import "service/domains/patient_profile_common.proto";
import "service/domains/timeline_common.proto";
// import "service/domains/billing.proto";
import "service/domains/private_schein_common.proto";

option (goff.pointer_strict_mode) = true;
option go_package = "git.tutum.dev/medi/tutum/ares/app/mvz/api/schein";
option (goff.nats_subject) = "api.app.mvz";

enum EventName {
  EventName_UpdateSchein = 0;
}

message ScheinChangedResponse { goff.UUID patientId = 1; goff.UUID ScheinId = 2; }

message EventScheinChanged { ScheinChangedResponse data = 1; EventName EventName = 2; }

message EventScheinChangedResponse { EventScheinChanged data = 1; }

message EventCreateRemoveSchein { ScheinChangedResponse data = 1; }

message IsValidRequest {
  CreateScheinRequest CreateScheinRequest = 1;
  repeated patient_profile_common.InsuranceInfo Insurances = 2;
}

message IsValidResponse {
  map<string, common.FieldError> Error = 1 [ (goff.nullable) = true ];
}

message GetKTABsRequest {
  string VKNR = 1 [(goff.custom) = 'validate:"required"'];
  patient_profile_common.SpecialGroupDescription SpecialGroup = 2;
  goff.UUID PatientId = 3 [(goff.custom) = 'validate:"required"'];
  int32 Quarter = 4 [(goff.custom) = 'validate:"required"']; 
  int32 Year = 5 [(goff.custom) = 'validate:"required"'];
  string Bsnr = 6 [(goff.custom) = 'validate:"required"'];
}

message GetKTABsResponse {
  repeated catalog_sdkt_common.KTABValue KTABValue = 1;
}

message MarkBillRequest {
  string ContractId = 1 [ (goff.nullable) = true ];
  schein_common.MainGroup MainGroup = 2;
  repeated goff.UUID ExcludeScheinIds = 3;
  repeated goff.UUID ValidScheinsIds = 4;
}

message TakeOverDiagnoseInfo {
  goff.UUID Id = 1;
  bool IsTreatmentRelevant = 2;
}

message CreateScheinRequest {
  goff.UUID ScheinId = 1 [ (goff.nullable) = true ];
  goff.UUID PatientId = 2;
  goff.UUID DoctorId = 3 [ (goff.nullable) = true ];
  schein_common.MainGroup ScheinMainGroup = 4;
  schein_common.TreatmentCaseNames KvTreatmentCase = 5;
  string KvScheinSubGroup = 6 [ (goff.nullable) = true ];
  int32 G4101Year = 7 [ (goff.custom) = 'validate:"required"' ];
  int32 G4101Quarter = 8 [ (goff.custom) = 'validate:"required"' ];
  string TariffType = 9 [ (goff.nullable) = true ];
  string BgType = 10 [ (goff.nullable) = true ];
  int64 BgAccidentDate = 11 [ (goff.nullable) = true ];
  string BgAccidentTime = 12 [ (goff.nullable) = true ];
  string BgWorkingTimeFrom = 13 [ (goff.nullable) = true ];
  string BgWorkingTimeTo = 14 [ (goff.nullable) = true ];
  string BgEmployerName = 15 [ (goff.nullable) = true ];
  string BgEmployerStreet = 16 [ (goff.nullable) = true ];
  string BgEmployerHousenumber = 17 [ (goff.nullable) = true ];
  string BgEmployerPostcode = 18 [ (goff.nullable) = true ];
  string BgEmployerCity = 19 [ (goff.nullable) = true ];
  string BgEmployerCountry = 20 [ (goff.nullable) = true ];
  string HzvContractId = 21 [ (goff.nullable) = true ];
  schein_common.ScheinDetail ScheinDetails = 22 [ (goff.nullable) = true ];
  goff.UUID InsuranceId = 23;
  bool ExcludeFromBilling = 24;
  repeated TakeOverDiagnoseInfo TakeOverDiagnoseInfos = 25;
  bool SkipFields = 26 [ (goff.nullable) = true ];
  string G4122 = 27 [ (goff.nullable) = true ];
  string G4101 = 28;
  repeated timeline_common.TimelineModel NewTakeOverDiagnosis = 29 [(goff.nullable) = true];
  goff.UUID AssignedToBsnrId = 30 [(goff.nullable) = true]; 
}

message CreateScheinResponse {
  schein_common.ScheinItem ScheinItem = 1 [ (goff.nullable) = true ];
  map<string, common.FieldError> FieldErrors = 2;
}

message GetSubGroupFromMasterDataRequest {
  string Bsnr = 1;
}

message GetSubGroupFromMasterDataResponse {
  repeated string Keys = 1;
}

message GetBillingAreaFromMasterDataRequest {
  string Bsnr = 1;
  string Subgroup = 2;
}

message GetBillingAreaFromMasterDataResponse {
  repeated string Keys = 1;
}

message Rezidiv {
  string Code = 1;
  string Content = 2;
}

message GetRezidivListResponse {
  repeated Rezidiv Data = 1;
}

message GetPsychotherapyByIdRequest {
  goff.UUID PsychotherapyId = 1;
}

message GetPsychotherapyByIdResponse {
  schein_common.Psychotherapy Data = 1;
}

message GetScheinByInsuranceIdRequest {
  goff.UUID InsuranceId = 1 [(goff.custom) = 'validate:"required"'];
}

message GetScheinByInsuranceIdResponse {
  schein_common.ScheinItem ScheinItem = 1;
}

message GetScheinsByInsuranceIdResponse {
  repeated schein_common.ScheinItem ScheinItems = 1;
}

message RevertTechnicalScheinRequest {
  goff.UUID ScheinId = 1;
}

// --- Private Schein --- //
message CreatePrivateScheinRequest {
  private_schein_common.PrivateScheinItem Schein = 1;
  repeated TakeOverDiagnoseInfo TakeOverDiagnoseInfos = 2;
  repeated timeline_common.TimelineModel NewTakeOverDiagnosis = 3 [(goff.nullable) = true];
}

message UpdatePrivateScheinRequest {
  private_schein_common.PrivateScheinItem Schein = 1;
}

message DeletePrivateScheinRequest {
  goff.UUID ScheinId = 1;
  goff.UUID PatientId = 2;
}

message IsValidPrivateScheinRequest {
  private_schein_common.PrivateScheinItem Schein = 1;
}

message GetPrivateScheinByIdRequest {
  goff.UUID ScheinId = 1;
}

message GetGoaFactorValueRequest {
  goff.UUID scheinId = 1 [(goff.custom) = 'validate:"required"'];
  string GoaNumber = 2 [(goff.custom) = 'validate:"required"'];
}

message GetGoaFactorValueResponse {
  double value = 1;
}

message TakeOverScheinDiagnisToRequest{
  goff.UUID ScheinId = 1;
  repeated TakeOverDiagnoseInfo TakeOverDiagnoseInfos = 2;
  repeated timeline_common.TimelineModel NewTakeOverDiagnosis = 3 [(goff.nullable) = true];
}
  
message TakeOverDiagnosisByScheinIdRequest {
  goff.UUID ScheinId = 1 [(goff.custom) = 'validate:"required"'];
  repeated TakeOverDiagnoseInfo TakeOverDiagnoseInfos = 2 [(goff.custom) = 'validate:"required"'];
  repeated timeline_common.TimelineModel NewTakeOverDiagnosis = 3 [(goff.nullable) = true];
}

message GetScheinItemByIdRequest {
  goff.UUID ScheinId = 1 [(goff.custom) = 'validate:"required"'];
}

message GetScheinItemByIdResponse {
  schein_common.ScheinItem ScheinItems = 1;
}

message GetScheinItemByIdsRequest {
  repeated goff.UUID ScheinIds = 1 [(goff.custom) = 'validate:"required"'];
}

message GetScheinItemByIdsResponse {
  repeated schein_common.ScheinItem ScheinItems = 1;
}

// --- BG Schein --- //
message CreateBgScheinRequest {
  schein_common.BgScheinItem Schein = 1;
  repeated TakeOverDiagnoseInfo TakeOverDiagnoseInfos = 2;
  repeated timeline_common.TimelineModel NewTakeOverDiagnosis = 3 [(goff.nullable) = true];
}

message UpdateBgScheinRequest {
  schein_common.BgScheinItem Schein = 1;
}

message DeleteBgScheinRequest {
  goff.UUID ScheinId = 1;
  goff.UUID PatientId = 2;
}

message IsValidBgScheinRequest {
  schein_common.BgScheinItem Schein = 1;
  patient_profile_common.InsuranceInfo Insurances = 2;
}

message GetBgScheinByIdRequest {
  goff.UUID ScheinId = 1;
}

message CheckDummyVknrRequest {
  goff.UUID ScheinId = 1 [ (goff.custom) = 'validate:"required"' ];
}

message CheckDummyVknrResponse {
  bool IsDummy = 1;
}

message GetTotalScheinsRequest {
  goff.UUID PatientId = 1 [(goff.custom) = 'validate:"required"'];
}

message GetTotalScheinsResponse {
  int32 TotalScheins = 1;
}

service ScheinApp {
  rpc CreateSvScheins(
      schein_common.CreateSvScheinRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc IsValid(IsValidRequest) returns (IsValidResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc createSchein(CreateScheinRequest)
      returns (CreateScheinResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc takeOverScheinDiagnosis(TakeOverScheinDiagnisToRequest)
      returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc getScheinDetailById(schein_common.GetScheinDetailByIdRequest)
      returns (schein_common.GetScheinDetailByIdResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc getScheinDetailByIds(schein_common.GetScheinDetailByIdsRequest)
      returns (schein_common.GetScheinDetailByIdsResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc updateSchein(schein_common.UpdateScheinRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc markNotBilled(schein_common.MarkNotBilledRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc checkExistKVScheinCurrentQuarter(
      schein_common.CheckExistKVScheinCurrentQuarterRequest)
      returns (schein_common.CheckExistKVScheinCurrentQuarterResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc deleteSchein(schein_common.DeleteScheinRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc getScheinDetail(schein_common.GetScheinDetailRequest)
      returns (schein_common.GetScheinDetailResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc getFields(schein_common.GetFieldsRequest)
      returns (schein_common.GetFieldsResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc getScheinsOverview(schein_common.GetScheinsOverviewRequest)
      returns (schein_common.GetScheinsOverviewResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc handleEventScheinChanged(EventScheinChanged) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  };

  rpc handleEventCreateRemoveSchein(EventCreateRemoveSchein) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  
  rpc getSetting(goff.Empty) returns (schein_common.GetSettingResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc saveSetting(schein_common.SaveSettingRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc getSelectedTreatmentCaseSubgroup(
      schein_common.GetSelectedTreatmentCaseSubgroupRequest)
      returns (schein_common.GetSelectedTreatmentCaseSubgroupResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc getOrderList(goff.Empty) returns (schein_common.GetOrderListResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc saveOrderList(schein_common.SaveOrderListRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc MarkBill (MarkBillRequest) returns (goff.Empty){
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc GetKTABs (GetKTABsRequest) returns (GetKTABsResponse){
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc GetSubGroupFromMasterData(GetSubGroupFromMasterDataRequest) returns (GetSubGroupFromMasterDataResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc GetBillingAreaFromMasterData(GetBillingAreaFromMasterDataRequest) returns (GetBillingAreaFromMasterDataResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc GetRezidivList(goff.Empty) returns (GetRezidivListResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc GetPsychotherapyById(GetPsychotherapyByIdRequest) returns (GetPsychotherapyByIdResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc GetScheinByInsuranceId(GetScheinByInsuranceIdRequest) returns (GetScheinByInsuranceIdResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  
  rpc GetScheinsByInsuranceId(GetScheinByInsuranceIdRequest) returns (GetScheinsByInsuranceIdResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc RevertTechnicalSchein(RevertTechnicalScheinRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc CreatePrivateSchein(CreatePrivateScheinRequest) returns (private_schein_common.PrivateScheinItem) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc UpdatePrivateSchein(UpdatePrivateScheinRequest) returns (private_schein_common.PrivateScheinItem) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc DeletePrivateSchein(DeletePrivateScheinRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc IsValidPrivateSchein(IsValidPrivateScheinRequest) returns (IsValidResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc GetPrivateScheinById(GetPrivateScheinByIdRequest) returns (private_schein_common.PrivateScheinItem) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc GetGoaFactorValue(GetGoaFactorValueRequest) returns (GetGoaFactorValueResponse){
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  };
  
  rpc TakeOverDiagnosisByScheinId(TakeOverDiagnosisByScheinIdRequest) returns (goff.Empty){
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  };

  rpc CreateSvScheinAutomaticly(schein_common.CreateSvScheinAutomaticlyRequest) returns (schein_common.CreateSvScheinAutomaticlyResponse) {
    option (goff.secured) = "IS_AUTHENTICATED";
  }

  rpc CreateSvScheinFromReference(schein_common.CreateSvScheinFromReferenceRequest) returns (schein_common.CreateSvScheinFromReferenceResponse) {
    option (goff.secured) = "IS_AUTHENTICATED";
  }

  rpc CreateSvScheinManually(schein_common.CreateSvScheinManuallyRequest) returns (schein_common.CreateSvScheinManuallyResponse) {
    option (goff.secured) = "IS_AUTHENTICATED";
  }

  rpc UpdateSvSchein(schein_common.UpdateSvScheinRequest) returns (schein_common.UpdateSvScheinResponse) {
    option (goff.secured) = "IS_AUTHENTICATED";
  }
  
  rpc MarkAsReferral(schein_common.MarkAsReferralRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc RemoveReferral(schein_common.RemoveReferralRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc GetScheinItemById(GetScheinItemByIdRequest) returns (GetScheinItemByIdResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc GetScheinItemByIds(GetScheinItemByIdsRequest) returns (GetScheinItemByIdsResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc CreateBgSchein(CreateBgScheinRequest) returns (schein_common.BgScheinItem) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc UpdateBgSchein(UpdateBgScheinRequest) returns (schein_common.BgScheinItem) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc DeleteBgSchein(DeleteBgScheinRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc GetBgScheinById(GetBgScheinByIdRequest) returns (schein_common.BgScheinItem) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc IsValidBgSchein(IsValidBgScheinRequest) returns (IsValidResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc CheckDummyVknr(CheckDummyVknrRequest) returns (CheckDummyVknrResponse) {
    option (goff.secured) = "IS_AUTHENTICATED";
  }

  rpc GetTotalScheins(GetTotalScheinsRequest) returns (GetTotalScheinsResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
}