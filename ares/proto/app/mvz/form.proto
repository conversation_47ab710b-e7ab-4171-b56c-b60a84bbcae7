syntax = "proto3";

package app_mvz_form;

import "goff.proto";
import "service/domains/form_common.proto";
import "service/domains/eau_common.proto";
import "service/domains/schein_common.proto";
option go_package = "git.tutum.dev/medi/tutum/ares/app/mvz/api/form";
option (goff.nats_subject) = "api.app.mvz";
option (goff.pointer_strict_mode) = true;

message GetFormRequest {
  string Id = 1 [ (goff.nullable) = true ];
  string OKV = 2 [ (goff.nullable) = true ];
  int32 IkNumber = 3 [ (goff.nullable) = true ];
  string ContractId = 4 [ (goff.nullable) = true ];
}

message GetFormResponse { form_common.Form Form = 1; }

message PrescribeRequest {
  form_common.Prescribe Prescribe = 1;
  form_common.PrintOption PrintOption = 2 [ (goff.nullable) = true ];
  eau_common.EAUSetting EAUSetting = 3 [ (goff.nullable) = true ];
}

message PrescribeResponse {
  form_common.Prescribe Prescribe = 1;
  string PdfBase64 = 2;
  repeated string PdfBase64List = 3 [ (goff.nullable) = true ];
}

message BuildBundleAndValidationRequest {
  form_common.Prescribe Prescribe = 1;
  form_common.PrintOption PrintOption = 2 [ (goff.nullable) = true ];
}

message BuildBundleAndValidationResponse {
  eau_common.EAUValidation EAUValidation = 1 [ (goff.nullable) = true ];
}

message GetPrescribeRequest { goff.UUID PrescribeId = 1; }

message GetPrescribeResponse { form_common.Prescribe Prescribe = 1; }

message GetFormsResponse { repeated form_common.Form Forms = 1; }

message PrintRequest {
  goff.UUID PrescribeId = 1;
  form_common.PrintOption PrintOption = 2;
  eau_common.EAUSetting EAUSetting = 3 [ (goff.nullable) = true ];
}

message PrintData {
  repeated string FormUrls = 1;
}

message PrintResponse { repeated PrintData PrintData = 1; }

message GetFormsRequest {
  string OKV = 1;
  int32 IkNumber = 2;
  string ContractId = 3;
  string ChargeSystemId = 4;
  string ModuleChargeSystemId = 5 [ (goff.nullable) = true ];
  schein_common.MainGroup ScheinMainGroup = 6 [ (goff.nullable) = true ];
}

message PrintPlainPdfRequest {
  string FormSetting = 1 [ (goff.custom) = 'validate:"trimjson"' ];
  string FormName = 2;
  goff.UUID TreatmentDoctorId = 3 [ (goff.nullable) = true ];
  bool IsRemoveBackground = 4 [ (goff.nullable) = true ];
  string ContractId = 5 [ (goff.nullable) = true ];
}

message PrintPlainPdfResponse { string FormUrl = 1; }

message GetFileUrlRequest { string FileName = 1; }
message GetFileUrlResponse { string FileUrl = 1; }
message PrescribeResponseV2 {
  repeated form_common.PrintResult PrintInfos = 1;
  goff.UUID TimelineId = 2;
}

message GetIndicatorActiveIngredientsRequest {
  string ContractId = 1 [(goff.custom) = 'validate:"required"'];
}

message AtcDiagnoseCode {
  string AtcCode = 1 [ (goff.nullable) = true ];
  string DiagnoseCode = 2 [ (goff.nullable) = true ];
}
message GetIndicatorActiveIngredientsResponse {
  repeated AtcDiagnoseCode Data = 1;
}

message GetIcdFormRequest { string ContractId = 1; }

message GetIcdFormResponse { repeated string IcdCodes = 1; }

service FormAPP {
  // Obsoleted
  rpc GetForm(GetFormRequest) returns (GetFormResponse) {
    option (goff.secured) = "IS_AUTHENTICATED";
  };
  rpc GetForms(GetFormsRequest) returns (GetFormsResponse) {
    option (goff.secured) = "IS_AUTHENTICATED";
  };
  rpc GetIcdForm(GetIcdFormRequest) returns (GetIcdFormResponse) {
    option (goff.secured) = "IS_AUTHENTICATED";
  };
  rpc GetPrescribe(GetPrescribeRequest) returns (GetPrescribeResponse) {
    option (goff.secured) = "CARE_PROVIDER_ADMIN, CARE_PROVIDER_MEMBER";
  }
  rpc Print(PrintRequest) returns (PrintResponse) {
    option (goff.secured) = "CARE_PROVIDER_ADMIN, CARE_PROVIDER_MEMBER";
  }

  rpc PrintPlainPdf(PrintPlainPdfRequest) returns (PrintPlainPdfResponse) {
    option (goff.secured) = "CARE_PROVIDER_ADMIN, CARE_PROVIDER_MEMBER";
  }
  rpc GetAllForms(goff.Empty) returns (GetFormsResponse) {
    option (goff.secured) = "IS_AUTHENTICATED";
  }
  rpc GetFileUrl(GetFileUrlRequest) returns (GetFileUrlResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  };
  rpc BuildBundleAndValidation(BuildBundleAndValidationRequest)
      returns (BuildBundleAndValidationResponse) {
    option (goff.secured) = "IS_USER";
  }
  rpc PrescribeV2(PrescribeRequest) returns (PrescribeResponseV2) {
    option (goff.secured) = "IS_USER, IS_AUTHENTICATED";
  }
  rpc GetIndicatorActiveIngredients(GetIndicatorActiveIngredientsRequest)
      returns (GetIndicatorActiveIngredientsResponse) {
    option (goff.secured) = "CARE_PROVIDER_MEMBER, IS_AUTHENTICATED";
  }
}
/* TODO: hard code for now */
enum KBV_PRF_NR {
  KBV_PRF_LABEL = 0 [ (goff.value) = "label_prf_nr" ];
  KBV_PRF_VALUE = 1 [ (goff.value) = "Y/9/2407/36/001" ];
}

enum EHIC_PRF_NR {
  EHIC_PRF_LABEL = 0 [ (goff.value) = "label_ehic_prf_nr" ];
  EHIC_PRF_VALUE = 1 [ (goff.value) = "Y/1/2404/36/701" ];
}
