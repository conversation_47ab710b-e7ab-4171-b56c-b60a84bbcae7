syntax = "proto3";

package app_mvz_patient_combination;

import "goff.proto";

option (goff.pointer_strict_mode) = true;

option go_package = "git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_combination";

option (goff.nats_subject) = "api.app.mvz";

message CombinePatientsRequest {
    goff.UUID TargetPatientId = 1 [(goff.custom) = 'validate:"required"'];
    repeated goff.UUID DuplicatedPatientIds = 2 [(goff.custom) = 'validate:"required"'];
}
  
service PatientCombinationApp {
    rpc CombinePatients(CombinePatientsRequest) returns (goff.Empty){
        option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
    }
}