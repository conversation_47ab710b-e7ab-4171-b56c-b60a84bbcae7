syntax = "proto3";

package app_mvz_medicine;

import "goff.proto";
import "service/domains/common.proto";
import "service/domains/bmp_medication_plan.proto";
import "service/domains/form_common.proto";
import "service/domains/qes_common.proto";
import "repo/mvz/medicine_common.proto";
import "app/mvz/timeline.proto";

option go_package = "git.tutum.dev/medi/tutum/ares/app/mvz/api/medicine";
option (goff.nats_subject) = "api.app.mvz";
option (goff.pointer_strict_mode) = true;

message DrugInformation {
  string ATC = 1;
  string ATCA = 2;
  string ATCName = 3;
  string Effectiveness = 4;
  int64 NoOfSubstances = 5;
  repeated Component Components = 6;
}

message Component {
  repeated Substance Substances = 1;
  int64 ComponentNumber = 2;
  string ComponentName = 3;
  int64 PhysicalForm = 4;
  string AbsoluteUnit = 5;
  string AbsoluteAmount = 6;
  string RelativeUnit = 7;
  string RelativeAmount = 8;
  string RelativeForm = 9;
  string EthanolPercentage = 10;
  int64 ReleaseBehavior = 11;
  int64 GalenicBasicForm = 12;
}

message Substance {
  int64 SubstanceType = 1;
  int64 Rank = 2;
  string Unit = 3;
  double Amount = 4;
  int64 EquivalentSubstance = 5;
  string Suffix = 6;
  string Name = 7;
  string GbaUrl = 8;
  int64 Id = 9;
}

message HintsAndWarning {
  string standardIndex = 1;
  string text = 2;
}

message LegalNote {
  int64 type = 1;
  string text = 2;
}

message Medicine {
  string id = 1 [ (goff.custom) = 'json:"id"' ];
  string pzn = 2 [ (goff.custom) = 'json:"pzn"' ];
  string search = 3;
  ProductInformation productInformation = 4
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  repeated HintsAndWarning hintsAndWarnings = 5
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  DrugInformation drugInformation = 6
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  PackagingInformation packagingInformation = 7
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  PriceInformation priceInformation = 8
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  TextInformation textInformation = 9
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  ColorCategory ColorCategory = 10
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  MedicationPlanInformation MedicationPlanInformation = 11
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  repo_medicine_common.PackageExtend PackageExtend = 12 [(goff.nullable) = true ];
}

message ColorCategory {
  int32 Sorting = 1;
  string DrugCategory = 2;
  bool IsInPriscusList = 3 [
    (goff.custom) = 'json:"inPriscusList,omitempty"',
    (goff.nullable) = true
  ];
}

message PackageSize {
  int64 PackingComponent = 1;
  int64 Classification = 2;
  string Nop = 3;
}

message PackagingInformation {
  string Quantity = 1;
  string Unit = 2;
  PackageSize PackageSize = 3;
  string amountText = 4;
  int32 QuantityNumber = 5 [(goff.nullable) = true];
  string NameRecipe = 6;
}

message PriceList {
  string PriceType = 1 [ (goff.nullable) = true ];
  double Value = 2 [ (goff.nullable) = true ];
}

message PriceInformation {
  double PharmacySalePrice = 1;
  map<string, DiscountDetail> Discount = 2;
  double CopaymentSorting = 3;
  double Copayment = 4;
  int64 TotalPayment = 5;
  double TotalCoPayment = 6 [ (goff.nullable) = true ];
  int64 AdditionalPayment = 7;
  double AdditionalCost = 8 [ (goff.nullable) = true ];
  int32 PriceComparisonGroup1 = 9;
  int32 PriceComparisonGroup2 = 10;
  bool IsFreeCopayment = 11 [ (goff.nullable) = true ];
  repeated PriceList Pricelist = 12;
  int64 FixedAmount = 13  [(goff.nullable) = true];
}

message DiscountDetail {
  int64 AdditionalPaymentIndicator = 1;
  int64 DiscountFactor = 2;
  int64 PreferAutIdem = 3;
}

message Divisible {
  string DIVISIBILITYTYPECODE = 1;
  int32 DIVISIBLE2_FLAG = 2;
  int32 DIVISIBLE3_FLAG = 3;
  int32 DIVISIBLE4_FLAG = 4;
  int32 DIVISIBLE_FLAG = 5;
}

message BENIFIT_ASSESSMENT_PATIENT_GROUP_LIST {
	string ID = 1;                                       
	int64 DOCUMENT_ID = 2;                               
	string DATUM_BE_VOM = 3;                            
	string DATUM_BE_BIS = 4;                               
	string NAME_PAT_GR = 5;                                      
}

message GPA {
  string AWG = 1;
  int32 ID = 2;
  string ID_BE_AKZ = 3;
  int32 QS_ATMP = 4;
  string REG_NB = 5;
  int32 SOND_ZUL_ATMP = 6;
  int32 SOND_ZUL_AUSN = 7;
  int32 SOND_ZUL_BESOND = 8;
  int32 SOND_ZUL_ORPHAN = 9;
  string UES_BE = 10;
  string URL = 11;
  string URL_QS_ATMP = 12;
  string URL_QS_ATMP_TEXT = 13;
  string URL_TEXT = 14;
  repeated int32 DocumentIds = 15;
  repeated BENIFIT_ASSESSMENT_PATIENT_GROUP_LIST BENIFIT_ASSESSMENT_PATIENT_GROUP_LIST = 16;
}

message ARMDocument {
  string FileName = 1;
  string AmrTypeCode = 2;
}

message ProductInformation {
  string shortName = 1;
  string name = 2;
  string provider = 3;
  string dosageForm = 4;
  int64 tReceipt = 6;
  int64 negativeList = 7;
  bool importReimport = 8;
  int64 documentRequired = 9;
  int64 lifeStyle = 10;
  int64 prescribable = 11;
  bool isOTC = 12;
  int64 conditionalReimbursement = 13;
  bool isExcludedFromAppendixIII = 14;
  bool isExistingInGBA = 15;
  int64 pharmaciesAvailability = 16;
  int64 productGroup = 17;
  int64 copaymentExemption = 18;
  int64 copaymentExemptionForBloodAndUrineTest = 19;
  repeated LegalNote legalNotes = 20;
  FormType FormType = 21;
  repeated string ActiveIngredients = 22;
  int64 OTXFlag = 23;
  repeated string OrderComponents = 24;
  int64 TransfusionlawFlag = 25;
  int64 MedicineProductFlag = 26;
  bool KReceipt = 27;
  int32 AmrMessagesCount = 28;
  int32 ImportProductFlag = 29;
  int32 SampleProductFlag = 30;
  bool PharmacyRequired = 31;
  repeated GPA GPAs = 32;
  Divisible Divisible = 33;
  int64 techInformationId = 35;
  int64 id = 36;
  int64 providerID = 37;
  string dosageFormCode = 38;
  string pharmFormCodeIFA = 39;
  repeated string RegulationTypeCodes = 40;
  int64 HasAmr3ConstraintFlag = 41;
  int64 HasAmr3ExclusionFlag = 42;
  string DispensingTypeCode = 43;
  int64 MedicineProductExceptionFlag = 44;
  ARMDocument PraxisbesonderheitDocument = 45 [(goff.nullable) = true];
  int64 HasAmr1Flag = 46;
  bool IsDigaFlag = 47;
  bool IsNegativeList = 48;
  bool IsLifeStyle = 49;
  bool IsBandage = 50;
}

message Substances {
  string substanceType = 1;
  string rank = 2;
  string unit = 3;
  double amount = 4;
  string equivalentSubstance = 5;
  string suffix = 6;
  string name = 7;
}

message TextInfomationItem {
  string CodeName = 1;
  string Name = 2;
  string Content = 3;
  int64 Order = 4;
}

message TextInformation {
  string updatedDate = 1;
  repeated TextInfomationItem Items = 2;
}

message Medicines { repeated Medicine Medicines = 1; }

message Sort {
  SortField Field = 1;
  common.Order Order = 2;
}

enum SortField {
  Size = 0;
  Price = 1;
  TotalPayment = 3;
}

message MedicationPlanInformation {
  string MedicationPlanUnitCode = 1;
}

message AddToShoppingBagRequest {
  goff.UUID PatientId = 1 [ (goff.nullable) = true ];;
  goff.UUID DoctorId = 2 [ (goff.nullable) = true ];
  string ContractId = 3 [ (goff.nullable) = true ];
  int32 IkNumber = 4 [ (goff.nullable) = true ];
  MedicineShoppingBagInfo Medicine = 5 [ (goff.custom) = 'validate:"required"' ];
  string Bsnr = 6 [ (goff.nullable) = true ]; 
  goff.UUID AssignedToBsnrId = 7 [ (goff.nullable) = true ];
}

message MedicineShoppingBagInfo {
  goff.UUID Id = 1 [ (goff.nullable) = true ];
  MedicineType Type = 2 [ (goff.custom) = 'validate:"required"' ];
  string Pzn = 3 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string Name = 4;
  int32 Quantity = 5 [ (goff.custom) = 'validate:"min=1"' ];
  PackagingInformation PackagingInformation = 6
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  ProductInformation ProductInformation = 7
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  goff.UUID PatientId = 8
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  goff.UUID DoctorId = 9
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  FormType CurrentFormType = 10;
  IntakeInterval IntakeInterval = 11
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string FormSetting = 12
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  bool AutIdem = 13
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  bool AsNeeded = 14
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string FurtherInformation = 15
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  repeated AvailableSize AvailableSizes = 16
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  DrugInformation DrugInformation = 17
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  TextInformation TextInformation = 18
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  PriceInformation priceInformation = 19
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  ColorCategory ColorCategory = 20
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  bool SubstitutionPrescription = 21 [ (goff.nullable) = true ];
  repeated SpecialExceeding SpecialExceedings = 22 [ (goff.nullable) = true ];
  int32 KBVMedicineId = 23 [ (goff.nullable) = true ];
  bool IsEPrescription = 24 [ (goff.nullable) = true ];
  MedicationPlanInformation MedicationPlanInformation = 25 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  bool IsArtificialInsemination = 26 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string Bsnr = 27 [ (goff.nullable) = true ];
  bool Vaccinate = 28 [ (goff.nullable) = true ];
  string DrugFormInformation = 29 [ (goff.nullable) = true ];
}

enum SpecialExceeding {
  A = 0;
  N = 1;
  S = 2;
  SZ = 3;
  ST = 4;
}

message AvailableSize {
  string Pzn = 1;
  string Size = 2;
  string Unit = 3;
  string Quantity = 4;
  int64 PackingComponent = 5;
  int64 Classification = 6;
}

message IntakeInterval {
  double Morning = 1
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  double Evening = 2
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  double Afternoon = 3
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  double Night = 4
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string Freetext = 5
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  bool DJ = 6 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
}

enum FormType {
  KREZ = 0;
  GREZ = 1;
  BTM = 2;
  TPrescription = 3;
  Private = 4;
  AOKNordwet = 5;
  AOKBremen = 6;
  Muster16aBay = 7;
}

enum MedicineType {
  FreeText = 0;
  HPM = 1;
  KBV = 2;
}

message ShoppingBagResponse {
  MedicineShoppingBagInfo Medicine = 1;
}

message UpdateShoppingBagQuantityRequest {
  goff.UUID PatientId = 1 [ (goff.nullable) = true ];
  goff.UUID DoctorId = 2 [ (goff.nullable) = true ];
  goff.UUID MedicineId = 3 [ (goff.custom) = 'validate:"required"' ];
  string ContractId = 4 [ (goff.nullable) = true ];
  int32 Quantity = 5 [ (goff.custom) = 'validate:"min=1"' ];
  string Bsnr = 6 [ (goff.nullable) = true ];
}

message RemoveFromShoppingBagRequest {
  goff.UUID PatientId = 1 [ (goff.nullable) = true ];
  goff.UUID DoctorId = 2 [ (goff.nullable) = true ];
  goff.UUID MedicineId = 3
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string ContractId = 4 [ (goff.nullable) = true ];
  goff.UUID ShoppingBagId = 5
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string Bsnr = 6 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true]; 
}

message GetShoppingBagRequest {
  goff.UUID PatientId = 1 [ (goff.nullable) = true ];
  goff.UUID DoctorId = 2 [ (goff.nullable) = true ];
  string ContractId = 3 [ (goff.nullable) = true ];
  string Bsnr = 4 [ (goff.nullable) = true ];
  goff.UUID AssignedToBsnrId = 5 [ (goff.nullable) = true ];
}

message GetShoppingBagRequestResponse {
  goff.UUID ShoppingBagId = 1;
  goff.UUID PatientId = 2;
  goff.UUID DoctorId = 3;
  goff.UUID TreatmentDoctorId = 4;
  repeated MedicineShoppingBagInfo Medicines = 5;
  string Bsnr = 6;
  goff.UUID AssignedToBsnrId = 7 [ (goff.nullable) = true ];
}

message GetSubsitutionResponse {
  repeated SubsitutionResponse Subsitutions = 1;
}

message SubsitutionResponse {
  string Key = 1;
  int32 Total = 2;
  string ATCA = 3;
  string ATCName = 4;
  repeated Medicine Medicines = 5;
}

message EventShoppingBagRequest {
  ShoppingBagEventType Type = 1;
  MedicineShoppingBagInfo Payload = 2;
  goff.UUID TreatmentDoctorId = 3 [ (goff.nullable) = true ];
  string ContractId = 4 [ (goff.nullable) = true ];
  repeated goff.UUID MedicineDeletedIds = 5 [ (goff.nullable) = true ];
  goff.UUID AssignedToBsnrId = 6 [ (goff.nullable) = true ];
}

enum ShoppingBagEventType {
  Add = 0;
  Update = 1;
  Delete = 2;
  UpdateForm = 3;
  UpdateDoctorId = 4;
  DeleteShoppingBag = 5;
}

message CheckMissingDiagnoseResponse { bool ShowWarning = 1; }

message AvailableSizeRequest {
  string ShortName = 1;
  string Provider = 2;
}

message UpdateFormRequest {
  goff.UUID PatientId = 1 [ (goff.custom) = 'validate:"required"' ];
  goff.UUID DoctorId = 2 [ (goff.custom) = 'validate:"required"' ];
  repeated goff.UUID MedicineIDs = 3 [ (goff.custom) = 'validate:"required"' ];
  FormType CurrentFormType = 4 [ (goff.custom) = 'validate:"required"' ];
  string ContractId = 5 [ (goff.nullable) = true ];
}

message PrescribeRequest {
  goff.UUID PatientId = 1 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  goff.UUID DoctorId = 2 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  repeated FormInfo FormInfos = 3 [ (goff.custom) = 'validate:"required"' ];
  string ContractId = 4 [ (goff.nullable) = true ];
  goff.UUID TreatmentDoctorId = 5 [ (goff.nullable) = true ];
  string EncounterCase = 6 [ (goff.nullable) = true ];
  repeated goff.UUID MedicineAutIdemIds = 7;
  goff.UUID ScheinId = 8 [ (goff.nullable) = true ];
  string Bsnr = 9 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  bool HasSupportForm907 = 10 [ (goff.nullable) = true ];
  goff.UUID AssignedToBsnrId = 11 [ (goff.nullable) = true ]; 
  bool PreventGetPatientProfile = 12 [ (goff.nullable) = true ];
}

enum SpecialIdentifier {
  SpecialIdentifierA = 0
      [ (goff.value) = "A - Überschreitung der Höchstverschreibungsmenge "
                       "innerhalb von 30 Tagen" ];
  SpecialIdentifierN = 1 [ (goff.value) = "N - Notfall Verschreibung" ];
  SpecialIdentifierS = 2
      [ (goff.value) = "S - Substitutionsmittel Verschreibung" ];
  SpecialIdentifierSZ = 3 [ (goff.value) = "SZ - § 5 Absatz 8 BtMVV" ];
  SpecialIdentifierST = 4 [ (goff.value) = "ST - § 5 Absatz 9 BtMVV" ];
}

message DateRange {
  int64 StartDate = 1;
  int64 EndDate = 2 [ (goff.nullable) = true ];
}

message FormInfo {
  repeated goff.UUID MedicineIDs = 1 [ (goff.custom) = 'validate:"required"' ];
  string formSetting = 2 [(goff.custom) = 'validate:"trimjson"'];
  FormType CurrentFormType = 3;
  int64 PrescribeDate = 4 [ (goff.nullable) = true ];
  bool IsShowFavHint = 5;
  int64 PrintDate = 6 [ (goff.nullable) = true ];
  repo_medicine_common.EPrescription EPrescription = 7 [ (goff.nullable) = true ];
  form_common.PrintOption PrintOption = 8 [(goff.nullable) = true];
  string BundleUrl = 9 [(goff.nullable) = true];
  string PdfUrl = 10 [(goff.nullable) = true];
  bool HasChangedSprechsundenbedarf = 11 [ (goff.nullable) = true ];
}

message FormInfoResponse {
  goff.UUID Id = 1;
  repeated MedicineShoppingBagInfo FormInfoResponse = 2;
  string FormSetting = 3;
  FormType CurrentFormType = 4;
  int64 PrescribeDate = 5 [ (goff.nullable) = true ];
  bool IsNotPicked = 6;
  bool IsShowFavHint = 7;
  repo_medicine_common.EPrescription EPrescription = 8 [ (goff.nullable) = true ];
  string BundleUrl = 9 [ (goff.nullable) = true ];
  bool HasChangedSprechsundenbedarf = 10 [ (goff.nullable) = true ];
  qes_common.DocumentStatus ERezeptStatus = 11 [ (goff.nullable) = true ];
}

message PrintResult {
  FormType CurrentFormType = 1;
  string FormUrl = 2;
}

message PrescribeResponse {
  goff.UUID Id = 1;
  goff.UUID PatientId = 2;
  goff.UUID DoctorId = 3;
  repeated FormInfoResponse FormInfoResponses = 4;
  int64 PrintDate = 5;
  string ContractId = 7;
  goff.UUID TreatmentDoctorId = 8;
  int64 CreatedDate = 9;
  goff.UUID EncounterId = 10;
  repeated PrintResult PrintResults = 11;
}

message UpdateShoppingBagInformationRequest {
  goff.UUID PatientId = 1 [ (goff.nullable) = true ];
  goff.UUID DoctorId = 2 [ (goff.nullable) = true ];
  string ContractId = 3 [ (goff.nullable) = true ];
  goff.UUID MedicineId = 4 [
    (goff.custom) =
        'validate:"required_with=FurtherInformation IntakeInterval PackageSizeRequest AsNeeded"',
    (goff.nullable) = true
  ];
  string FurtherInformation = 5 [ (goff.nullable) = true ];
  IntakeInterval IntakeInterval = 6 [ (goff.nullable) = true ];
  PackageSizeRequest PackageSizeRequest = 7 [ (goff.nullable) = true ];
  goff.UUID TreatmentDoctorId = 8 [ (goff.nullable) = true ];
  bool AsNeeded = 9 [ (goff.nullable) = true ];
  string FreeText = 10 [ (goff.nullable) = true ];
  bool SubstitutionPrescription = 11 [ (goff.nullable) = true ];
  repeated SpecialExceeding SpecialExceedings = 12 [ (goff.nullable) = true ];
  bool IsEPrescription = 13 [ (goff.nullable) = true ];
  bool IsArtificialInsemination = 14 [ (goff.nullable) = true ];
  string Bsnr = 15 [ (goff.nullable) = true ];
  bool Vaccinate = 16 [ (goff.nullable) = true ];
  string DrugFormInformation = 17 [ (goff.nullable) = true ];
  goff.UUID AssignedToBsnrId = 18 [ (goff.nullable) = true ];
}

message PackageSizeRequest { string Pzn = 1; }

enum SortFieldConsultation {
  PrescribeDate = 0;
  Tradename = 1;
  SizeMed = 2;
  PrescribedBy = 3;
  Status = 4;
}

message GetMedicationPrescribeRequest {
  goff.UUID PatientId = 1 [ (goff.nullable) = true ];
  string Bsnr = 2 [ (goff.nullable) = true ];
  SortFieldConsultation SortField = 3;
  common.Order Order = 4;
}

message MedicationPrescribeResponse {
  goff.UUID Id = 1;
  MedicineType Type = 2;
  string Pzn = 3 [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string Name = 4;
  PackagingInformation PackagingInformation = 5
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  ProductInformation ProductInformation = 6
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  IntakeInterval IntakeInterval = 7;
  string FurtherInformation = 8;
  DrugInformation DrugInformation = 9
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  int64 PrescribeDate = 10;
  goff.UUID TreatmentDoctorId = 11;
  TextInformation TextInformation = 12
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  goff.UUID MedicationPlanId = 13 [ (goff.nullable) = true ];
  bool AsNeeded = 14
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  string ContractId = 15 [ (goff.nullable) = true ];
  PriceInformation PriceInformation = 16
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  ColorCategory ColorCategory = 17
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  bool AutIdem = 18;
  int32 Quantity = 19;
  FormType CurrentFormType = 20;
  goff.UUID FormInfoId = 21;
  bool SubstitutionPrescription = 22 [ (goff.nullable) = true ];
  repeated SpecialExceeding SpecialExceedings = 23 [ (goff.nullable) = true ];
  int32 KBVMedicineId = 24 [ (goff.nullable) = true ];
  MedicationPlanInformation MedicationPlanInformation = 25
      [ (goff.custom) = 'json:"omitempty"', (goff.nullable) = true ];
  bool IsEPrescription = 26;
  int64 PrintDate = 27 [(goff.nullable) = true]; 
  int64 FixedAmount = 28 [(goff.nullable) = true];
  bool IsFavourite = 29;
  FormInfoResponse FormInfo = 30;
  goff.UUID AssignedToBsnrId = 31 [(goff.nullable) = true];
  bool IsArtificialInsemination = 32;
  bool Vaccinate = 33;
}

message GetMedicationPrescribeResponse {
  goff.UUID PatientId = 1;
  string Bsnr = 2;
  repeated MedicationPrescribeResponse MedicationPrescribeResponses = 3;
}

enum EventMedicationPrescribeType {
  Remove = 0;
  PrescribeSuccess = 1;
  RemovePrescribeMedicationPlan = 2;
  CreateMedicationPlanSuccess = 3;
}

message EventMedicationPrescribe {
  EventMedicationPrescribeType EventType = 1;
  MedicationPrescribeResponse Payload = 2;
  goff.UUID PatientId = 3;
  goff.UUID DoctorId = 4;
  string ContractId = 5 [ (goff.nullable) = true ];
  string Bsnr = 6 [ (goff.nullable) = true ];
}

message DeleteMedicationPrescribeRequest {
  goff.UUID Id = 1 [ (goff.custom) = 'validate:"required"' ];
  goff.UUID PatientId = 2 [ (goff.custom) = 'validate:"required"' ];
}

message DeleteMedicationPrescribeResponse {
  goff.UUID Id = 1;
  goff.UUID PatientId = 2;
}

message CreateMedicationPlanRequest {
  goff.UUID PrescribedMedicationId = 1
      [ (goff.custom) = 'validate:"required"' ];
  string Hint = 2;
}

enum EventMedicationPlanChangedType {
  RemoveMedicationPlan = 0;
  CreateMedicationPlan = 1;
  UpdateMedicationPlan = 2;
}

message EventMedicationPlanChanged {
  EventMedicationPlanChangedType EventType = 1;
  service_domains_bmp.EntryResponse Payload = 2;
  string EncounterCase = 3;
  common.ContractType ContractType = 4 [ (goff.nullable) = true ];
  goff.UUID PatientId = 5;
}

message DeleteMedicationPlanRequest {
  goff.UUID MedicationPlanId = 1 [ (goff.custom) = 'validate:"required"' ];
  goff.UUID PatientId = 2;
  goff.UUID DoctorId = 3;
  string ContractId = 4 [ (goff.nullable) = true ];
  string EncounterCase = 5;
}

message DeleteMedicationPlanResponse {
  goff.UUID Id = 1;
  goff.UUID PatientId = 2;
}
message CheckMissingDiagnosesRequest {
  goff.UUID PatientId = 1 [ (goff.custom) = 'validate:"required"' ];
  goff.UUID DoctorId = 2 [ (goff.custom) = 'validate:"required"' ];
  string ContractId = 3 [ (goff.nullable) = true ];
  int32 IkNumber = 4 [ (goff.nullable) = true ];
  repeated string Pzns = 5 [ (goff.custom) = 'validate:"required"' ];
}

message CheckMissingDiagnosesResponse { repeated string Pzns = 1; }

message EventRefillMedicine {
  goff.UUID PatientId = 1;
  repeated MedicineShoppingBagInfo MedicineInfos = 4;
}

message ViewMedicationForm {
  goff.UUID TreatmentDoctorId = 1;
  FormInfoResponse FormInfo = 2;
  goff.UUID AssignedToBsnrId = 3;
}

enum ViewMedicationFormType {
  ViewForm = 0;
  ChangeTreatmentDoctor = 1;
}

message EventViewMedicationForm {
  goff.UUID PatientId = 1;
  ViewMedicationForm ViewMedicationForm = 2;
  ViewMedicationFormType EventType = 3;
}

message UpdateTreatmentDoctorMedicationFormRequest {
  goff.UUID MedicationFormId = 1 [ (goff.custom) = 'validate:"required"' ];
  goff.UUID TreatmentDoctorId = 2 [ (goff.custom) = 'validate:"required"' ];
  goff.UUID PatientId = 3 [ (goff.custom) = 'validate:"required"' ];
  goff.UUID AssignedToBsnrId = 4 [ (goff.nullable) = true ];
}

message PrintFormRequest {
  goff.UUID FormId = 1 [ (goff.custom) = 'validate:"required"' ];
  form_common.PrintOption PrintOption = 2 [ (goff.custom) = 'validate:"required"' ];
  bool HasSupportForm907 = 3 [ (goff.nullable) = true ];
  goff.UUID PatientId = 4;
  bool PreventGetPatientProfile = 5 [ (goff.nullable) = true ];
}

message PrintFormResponse {
  repeated PrintResult PrintResults = 1;
}

service MedicineApp {
  rpc HandleEventShoppingBagChanged(EventShoppingBagRequest)
      returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  };
  rpc HandleEventRefillMedicine(EventRefillMedicine) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetShoppingBag(GetShoppingBagRequest)
      returns (GetShoppingBagRequestResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  };
  rpc AddToShoppingBag(AddToShoppingBagRequest) returns (ShoppingBagResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  };

  rpc CheckMissingDiagnose(AddToShoppingBagRequest)
      returns (CheckMissingDiagnoseResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  };

  rpc UpdateShoppingBagQuantity(UpdateShoppingBagQuantityRequest)
      returns (ShoppingBagResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  };

  rpc UpdateShoppingBagInformation(UpdateShoppingBagInformationRequest)
      returns (ShoppingBagResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc RemoveFromShoppingBag(RemoveFromShoppingBagRequest)
      returns (ShoppingBagResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  };
  rpc UpdateForm(UpdateFormRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  };
  rpc Prescribe(PrescribeRequest) returns (PrescribeResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  };
  rpc GetMedicationPrescribe(GetMedicationPrescribeRequest)
      returns (GetMedicationPrescribeResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc DeleteMedicationPrescribe(DeleteMedicationPrescribeRequest)
      returns (DeleteMedicationPrescribeResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc CheckMissingDiagnoses(CheckMissingDiagnosesRequest)
      returns (CheckMissingDiagnosesResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  };
  rpc HandleEventMedicationPrescribeChanged(EventMedicationPrescribe)
      returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  };
  rpc CreateMedicationPlan(CreateMedicationPlanRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc HandleEventMedicationPlanChanged(EventMedicationPlanChanged)
      returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc DeleteMedicationPlan(DeleteMedicationPlanRequest)
      returns (DeleteMedicationPlanResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc HandleEventViewMedicationForm(EventViewMedicationForm)
      returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc UpdateTreatmentDoctorMedicationForm(
      UpdateTreatmentDoctorMedicationFormRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc PrintForm (PrintFormRequest) returns (PrintFormResponse){
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc OnTimelineHardDelete (app_mvz_timeline.EventTimelineHardRemove) returns (goff.Empty){
    option (goff.subject) = "api.app.mvz.AppMvzTimeline.TimelineHardRemove";
    option (goff.queue) = "medicine_queue";
  };
}