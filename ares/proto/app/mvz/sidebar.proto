syntax = "proto3";

package app_mvz_sidebar;

import "goff.proto";

option (goff.pointer_strict_mode) = true;
option go_package = "git.tutum.dev/medi/tutum/ares/app/mvz/api/sidebar";
option (goff.nats_subject) = "api.app.mvz";

message GetDefaultInformationResponse {
  bool HasNotSubmittedEnrollment = 1;
  bool HasPendingPtvImport = 2;
  // Dynamic items map for extensibility
  map<string, bool> Items = 3;
}

message EventSidebarRepsonse {
  goff.UUID UserId = 1;
  bool HasNotSubmittedEnrollment = 2;
  bool HasPendingPtvImport = 3;
}

service SidebarApp {
  rpc GetDefaultInformation(goff.Empty)
      returns (GetDefaultInformationResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc HandleEventSidebar (EventSidebarRepsonse) returns (goff.Empty){
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc HandleEventForPatientOverview (EventSidebarRepsonse) returns (goff.Empty){
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
}
