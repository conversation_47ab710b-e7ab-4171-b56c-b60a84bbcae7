
syntax = "proto3";

package bg_billing;

import "goff.proto";
import "service/domains/bg_billing_common.proto";
import "service/domains/common.proto";
import "service/domains/timeline_common.proto";
import "service/domains/patient_profile_common.proto";

option go_package = "git.tutum.dev/medi/tutum/ares/app/mvz/api/bg_billing";
option (goff.nats_subject) = "api.app.mvz";

message CreateBgBillingRequest {
    bg_billing_common.BillingRecord item = 1;
}

message GetBgBillingsRequest {
   string search = 1 [ (goff.nullable) = true ];
   common.PaginationRequest pagination = 2;
   bg_billing_common.BgBillingFilter filter = 3
   [ (goff.nullable) = true, (goff.custom) = 'bson:"omitempty"' ];
}

message GetBgBillingsResponse {
    repeated bg_billing_common.BgBillingItem items = 1;
    int64 total = 2;
}

message GetBgBillingByScheinIdRequest {
    goff.UUID scheinId = 1;
}

message GetBgBillingByScheinIdResponse {
    bg_billing_common.BgBillingItem item = 1;
}

message GetBgBillingByIdRequest {
    goff.UUID id = 1 [(goff.custom) = 'validate:"required"'];
}

message GetBgBillingByIdResponse {
    bg_billing_common.BgBillingItem item = 1;
}

message GetUvGoaServiceCodeRequest {
    goff.UUID id = 1 [(goff.custom) = 'validate:"required"'];
}

message GetUvGoaServiceCodeByIdsRequest {
    repeated goff.UUID ids = 1 [(goff.custom) = 'validate:"required"'];
}

message UvGoaServiceTimelineData {
    goff.UUID billingId = 1;
    repeated timeline_common.TimelineModel TimelineModels = 2;
    bg_billing_common.InvoiceInfo InvoiceInfo = 3;
}

message GetUvGoaServiceCodeResponse {
    UvGoaServiceTimelineData Data = 1;
}

message GetUvGoaServiceCodeByIdsResponse {
    repeated UvGoaServiceTimelineData Data = 1;
}

message GetPrintedInvoicesRequest {
    goff.UUID patientId = 1 [(goff.custom) = 'validate:"required"'];
    goff.UUID BillingId = 2 [(goff.custom) = 'validate:"required"'];
}

message GetPrintedInvoicesResponse {
    repeated timeline_common.TimelineModel TimelineModels = 1;
}

message MarkBgBillingPaidRequest {
    goff.UUID PatientId = 1;
    goff.UUID BillingId = 2;
    string InvoiceNumber = 3;
}

message MarkBgBillingPaidResponse {
    timeline_common.TimelineModel TimelineModel = 1;
}

message MarkBgBillingUnpaidRequest {
    goff.UUID PatientId = 1;
    goff.UUID BillingId = 2;
}

message MarkBgBillingUnpaidResponse {
    bg_billing_common.BgBillingItem item = 1;
}

message MarkBgBillingCancelledRequest {
    goff.UUID PatientId = 1;
    goff.UUID BillingId = 2;
}

message MarkBgBillingCancelledResponse {
    bg_billing_common.BgBillingItem item = 1;
}

message GetListDoctorResponse {
    repeated bg_billing_common.Doctor Doctors = 1;
}

message GetListInsuranceResponse {
    repeated  patient_profile_common.InsuranceInfo Insurances = 1;
}

service BgBillingApp {
    rpc GetBgBillingByScheinId(GetBgBillingByScheinIdRequest) returns (GetBgBillingByScheinIdResponse) {
        option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
    }
    rpc GetBgBilling(GetBgBillingsRequest) returns (GetBgBillingsResponse) {
        option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
    }
    rpc getUvGoaServiceCode(GetUvGoaServiceCodeRequest) returns (GetUvGoaServiceCodeResponse) {
        option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
    }
    rpc getUvGoaServiceCodeByIds(GetUvGoaServiceCodeByIdsRequest) returns (GetUvGoaServiceCodeByIdsResponse) {
        option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
    }
    rpc GetBgBillingById(GetBgBillingByIdRequest) returns (GetBgBillingByIdResponse) {
        option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
    }
    rpc GetPrintedInvoices(GetPrintedInvoicesRequest) returns (GetPrintedInvoicesResponse) {
        option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
    }
    rpc MarkBgBillingPaid(MarkBgBillingPaidRequest) returns (MarkBgBillingPaidResponse){
        option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
    };
    rpc MarkBgBillingUnpaid(MarkBgBillingUnpaidRequest) returns (MarkBgBillingUnpaidResponse) {
        option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
    }
    rpc MarkBgBillingCancelled(MarkBgBillingCancelledRequest) returns (MarkBgBillingCancelledResponse) {
        option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
    }
    rpc GetListDoctor(goff.Empty) returns (GetListDoctorResponse) {
        option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
    }
    rpc GetListInsurance(goff.Empty) returns (GetListInsuranceResponse) {
        option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
    }
}
