syntax = "proto3";

package app_mvz_timeline;

import "goff.proto";
import "repo/mvz/encounter_common.proto";
import "service/domains/common.proto";
import "service/domains/eab_service_history_common.proto";
import "service/domains/form_common.proto";
import "service/domains/schein_common.proto";
import "service/domains/timeline_common.proto";
import "service/domains/timeline_validation.proto";

option go_package = "git.tutum.dev/medi/tutum/ares/app/mvz/api/timeline";
option (goff.nats_subject) = "api.app.mvz";
option (goff.pointer_strict_mode) = true;

message GetRequest {
  goff.UUID PatientId = 1;
  string ContractId = 2 [(goff.nullable) = true];
  int64 CreatedDate = 3 [(goff.nullable) = true];
  string EncounterCase = 4 [(goff.nullable) = true];
  goff.UUID TreatmentDoctorId = 5 [(goff.nullable) = true];
  repeated timeline_common.TimelineEntityType Types = 6 [(goff.nullable) = true];
}

message GetResponse {
  repeated timeline_common.TimelineModel TimelineModels = 1;
}

message CreateRequest {
  timeline_common.TimelineModel TimelineModel = 1;
}

message CreateResponse {
  timeline_common.TimelineModel TimelineModel = 1;
}

message RemoveRequest {
  bool hasHardDelete = 1 [(goff.nullable) = true];
  goff.UUID TimelineId = 3 [(goff.nullable) = true];
  bool isChain = 4 [(goff.nullable) = true];
}

message RemoveResponse {}

message EditRequest {
  timeline_common.TimelineModel TimelineModel = 1;
}

message EditResponse {
  timeline_common.TimelineModel TimelineModel = 1;
}

message UpdatePrintDateRequest {
  timeline_common.TimelineEntityType Type = 1;
  goff.UUID TimelineId = 2 [(goff.nullable) = true];
  goff.UUID FormId = 3 [(goff.nullable) = true];
  int64 PrintDate = 4 [(goff.nullable) = true];
}

message UpdateSuggestionRuleAppliedRequest {
  goff.UUID TimelineId = 1 [(goff.nullable) = true];
  string RuleId = 2;
}
message UpdatePrintDateResponse {}

message EventTimelineHardRemove {
  goff.UUID PatientId = 1;
  timeline_common.TimelineModel TimelineModel = 2;
  goff.UUID TimelineId = 3;
}

message EventTimelineRemove {
  goff.UUID PatientId = 1;
  timeline_common.TimelineModel TimelineModel = 2;
}

message EventTimelineRestore {
  timeline_common.TimelineModel TimelineModel = 1;
}

message EventTimelineCreate {
  goff.UUID PatientId = 1;
  timeline_common.TimelineModel TimelineModel = 2;
}

message EventTimelineUpdate {
  goff.UUID PatientId = 1;
  timeline_common.TimelineModel TimelineModel = 2;
  timeline_common.TimelineModel OldTimelineModel = 3 [(goff.nullable) = true];
  bool SkipUpdateEndDatePermanentDiagnoses = 4;
  goff.UUID AuditLogId = 5 [(goff.nullable) = true];
  form_common.FormName PrescribeFormName = 6 [(goff.nullable) = true];
}

message EventAutoAction {
  goff.UUID PatientId = 1;
  string NotificationCode = 2;
}

message ITimelineEntityType {
  timeline_common.TimelineEntityType TimelineEntityType = 1;
  string Command = 2  [(goff.nullable) = true];
}

message GroupByQuarterRequest {
  goff.UUID PatientId = 1;
  int64 FromDate = 2 [(goff.nullable) = true];
  int64 ToDate = 3 [(goff.nullable) = true];
  repeated ITimelineEntityType TimelineEntityTypes = 4 [(goff.nullable) = true];
  bool IsSortByCategory = 5;
  bool IsHistoryMode = 6;
  goff.UUID ScheinId = 7 [(goff.nullable) = true];
  string Keyword = 8 [(goff.nullable) = true];
  common.PaginationRequest Pagination = 9 [(goff.nullable) = true];
  int32 Year = 10 [(goff.nullable) = true];
  int32 Quarter = 11 [(goff.nullable) = true];
}

message GroupByQuarter {
  int32 Year = 1;
  int32 Quarter = 2;
  repeated timeline_common.TimelineModel TimelineModels = 3;
}

message GroupByQuarterResponse {
  repeated GroupByQuarter GroupByQuarters = 1;
  int32 TotalPage = 2;
  repeated string MatchedTokens = 3;
}

message EventTimelineValidation {
  goff.UUID PatientId = 1;
  repeated timeline_common.TimelineModel TimelineModels = 2;
}

message GetByIdRequest {
  goff.UUID TimelineId = 1;
}

message GetByIdsRequest {
  repeated goff.UUID TimelineIds = 1;
}

message GetByIdResponse {
  timeline_common.TimelineModel TimelineModel = 1 [(goff.nullable) = true];
}

message GetDiagnoseRequest {
  goff.UUID PatientId = 1;
  repeated string Codes = 2 [(goff.nullable) = true];
}

message GetDiagnoseResponse {
  repeated repo_encounter.EncounterDiagnoseTimeline EncounterDiagnoseTimeline = 1;
}

message IgnoreSdkrwRuleRequest {
  goff.UUID patientId = 1;
  string ruleId = 2;
  int64 encounterDate = 3;
}

message UpdateErezeptStatusRequest {
  goff.UUID MedicineId = 1;
  string Status = 2;
}

message DeleteErezeptRequest {
  goff.UUID MedicineId = 1;
  goff.UUID PatientId = 2 [(goff.custom) = 'validate:"required"'];
}

message GetTherapiesRequest {
  goff.UUID PatientId = 1;
  goff.UUID ScheinId = 2;
}

message TherapiesResponse {
  schein_common.Psychotherapy Pyschotherapy = 1;
  timeline_common.TimelineModel TimelineModel = 2;
}

message GetTherapiesResponse {
  repeated TherapiesResponse Pyschotherapies = 1;
}

message GetAmountServiceCodeRequest {
  goff.UUID PatientId = 1;
  repeated string ServiceCodes = 2;
  int32 Year = 3;
  int32 Quarter = 4;
}

message GetAmountServiceCodeResponse {
  map<string, int64> AmountServiceCode = 1;
}

message MarkNotApprovedPyschotherapyRequest {
  repeated goff.UUID TimelineId = 1;
  goff.UUID PatientId = 2;
}

message RestoreEntryHistoryRequest {
  goff.UUID AuditLogId = 1;
}

message RestoreEntryHistoryResponse {}

message ValidateDiagnoseRequest {
  repeated string IcdCode = 1 [(goff.custom) = 'validate:"required"'];
  repeated timeline_common.IcdErrorTypeCheck TypeCheck = 2 [(goff.custom) = 'validate:"required"'];
}

message ValidateDiagnoseResponse {
  repeated timeline_common.ValidationDiagnoseResult Results = 1;
}

message ReRunValidateServiceRequest {
  goff.UUID PatientId = 1;
  string ContractId = 2 [(goff.nullable) = true];
}

message GetTimelineEntryByIdsRequest {
  repeated goff.UUID EntryIds = 1;
}

message GetTimelineEntryIdsResponse {
  repeated timeline_common.TimelineModel TimelineModels = 1;
}

message GetPreParticipationServiceCodesRequest {
  goff.UUID PatientId = 1;
}

message Document88130Request {
  timeline_common.TimelineModel TimelineModel = 1;
  goff.UUID ServiceEntryId = 2;
}

message Document88130Response {
  repeated timeline_common.TimelineModel TakeoverDiagnosis = 1;
  goff.UUID ScheinId = 2 [(goff.nullable) = true];
  goff.UUID ServiceId = 3;
}

message GetPsychotherapyTakeOverRequest {
  goff.UUID PatientId = 1;
}

message GetPsychotherapyTakeOverResponse {
  repeated timeline_common.TimelineModel PsychotherapyEntry = 1;
}

message TakeoverServiceTerminalApprovalRequest {
  goff.UUID TimelineId = 1;
  goff.UUID ScheinId = 2;
}

message GetTakeOverDiagnosisRequest {
  goff.UUID PatientId = 1 [(goff.custom) = 'validate:"required"'];
  int64 FromDate = 2 [(goff.nullable) = true];
  int64 ToDate = 3 [(goff.nullable) = true];
  string Query = 4 [(goff.nullable) = true];
  goff.UUID ScheinId = 5 [(goff.nullable) = true];
}

message GetTakeOverDiagnosisResponse {
  repeated timeline_common.TakeOverDiagnosisGroup TakeOverDiagnosisGroup = 1;
}

message GetPsychotherapyBefore2020 {
  timeline_common.TimelineModel TimelineModel = 1;
  service_domains_validation_timeline.ServiceErrorCode ErrorCode = 2;
  timeline_common.TimelineModel ServiceEntry = 3;
}

message GetPsychotherapyBefore2020Request {
  goff.UUID PatientId = 1;
}

message GetPsychotherapyBefore2020Response {
  repeated GetPsychotherapyBefore2020 Data = 1;
}

message UpdateManyRequest {
  repeated timeline_common.TimelineModel TimelineModels = 1;
}

message TakeOverDiagnosisWithScheinIdRequest {
  goff.UUID ScheinId = 1;
  repeated goff.UUID TimelineModelIds = 2;
  repeated timeline_common.TimelineModel NewDiagnosis = 3;
  map<string, bool> MappingTreatmentRelevent = 4;
}

message MarkTreatmentRelevantRequest {
  goff.UUID TimelineId = 1 [(goff.custom) = 'validate:"required"'];
}

message UpdateEncounterCaseForServiceEntriesRequest {
  goff.UUID TimelineId = 1 [(goff.custom) = 'validate:"required"'];
}

message MarkAcceptedByKVRequest {
  goff.UUID TimelineId = 1;
}

message DocumentSuggestionRequest {
  goff.UUID TimelineId = 1 [(goff.custom) = 'validate:"required"'];
  string SuggestionCode = 2 [(goff.custom) = 'validate:"required"'];
  map<string, string> SuggestionData = 3 [(goff.nullable) = true];
}

message RollbackDocumentTerminateServiceRequest {
  goff.UUID TerminateServiceId = 1;
  goff.UUID TehcnicalScheinId = 2;
  goff.UUID PatientId = 3 [(goff.custom) = 'validate:"required"'];
}

message GetLastDocumentedQuarterRequest {
  goff.UUID PatientId = 1 [(goff.custom) = 'validate:"required"'];
  int32 Year = 2 [(goff.custom) = 'validate:"required"'];
  int32 Quarter = 3 [(goff.custom) = 'validate:"required"'];
  timeline_common.TimelineEntityType TimelineEntityType = 4 [(goff.nullable) = true];
}

message GetLastDocumentedQuarterResponse {
  int32 Year = 1;
  int32 Quarter = 2;
}

message GetTimelineByEnrollmentIdRequest {
  goff.UUID EnrollmentId = 1;
  goff.UUID PatientId = 2 [(goff.custom) = 'validate:"required"'];
}

message GetTimelineByEnrollmentIdResponse {
  timeline_common.TimelineModel TimelineModel = 1;
}

message ReSendEABMailRequest {
  goff.UUID TimelineId = 1 [(goff.custom) = 'validate:"required"'];
}

message DocumentEABServiceCodeRequest {
  goff.UUID ScheinId = 1 [(goff.custom) = 'validate:"required"'];
  goff.UUID PatientId = 2 [(goff.custom) = 'validate:"required"'];
  string BsnrCode = 3 [(goff.custom) = 'validate:"required"'];
}

message DocumentEABServiceCodeResponse {
  repeated eab_service_history_common.EABServiceCode ServiceCodes = 1;
}

message FindLatesTimelineEntryRequest {
  goff.UUID PatientId = 1;
  timeline_common.TimelineEntityType Type = 2;
  string ContractId = 3;
}

message FindLatesTimelineEntryResponse {
  timeline_common.TimelineModel TimelineModel = 1;
}

message CheckIsVSST785Request {
  string ContractId = 1;
  goff.UUID PatientId = 2;
  repeated string AtcCodes = 3;
  int32 IkNumber = 4 [(goff.nullable) = true, (goff.custom) = 'json:"omitempty"'];
}

message CheckIsVSST785Response {
  bool IsVSST785 = 1;
}

message GetDoctorLetterByIdRequest {
  goff.UUID DoctorLetterId = 1;
}
message GetDoctorLetterByIdResponse {
  timeline_common.TimelineModel TimelineModel = 1 [(goff.nullable) = true];
}

message GetActionChainDiagnoseByCodesRequest {
  repeated string Codes = 1;
  int32 Year = 2;
}
message GetActionChainDiagnoseByCodesResponse {
  repeated string ValidItems = 1;
  repeated string InValidItems = 2;
}

service TimelineApp {
  rpc DocumentSuggestion(DocumentSuggestionRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc MarkTreatmentRelevant(MarkTreatmentRelevantRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc UpdateEncounterCaseForServiceEntries(UpdateEncounterCaseForServiceEntriesRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetDiagnose(GetDiagnoseRequest) returns (GetDiagnoseResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc HandleEventTimelineValidation(EventTimelineValidation) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc HandleEventTimelineHardRemove(EventTimelineHardRemove) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc HandleEventTimelineRemove(EventTimelineRemove) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc HandleEventTimelineCreate(EventTimelineCreate) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc HandleEventTimelineUpdate(EventTimelineUpdate) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc HandleEventAutoAction(EventAutoAction) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc GetById(GetByIdRequest) returns (GetByIdResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetByIds(GetByIdsRequest) returns (GetResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc Get(GetRequest) returns (GetResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc Create(CreateRequest) returns (CreateResponse) {
    option (goff.secured) = "IS_USER";
  }
  rpc Edit(EditRequest) returns (EditResponse) {
    option (goff.secured) = "IS_USER";
  }
  rpc UpdateMany(UpdateManyRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED";
  }
  rpc Remove(RemoveRequest) returns (RemoveResponse) {
    option (goff.secured) = "IS_USER";
  }
  rpc GroupByQuarter(GroupByQuarterRequest) returns (GroupByQuarterResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc UpdatePrintDate(UpdatePrintDateRequest) returns (UpdatePrintDateResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc UpdateSuggestionRuleApplied(UpdateSuggestionRuleAppliedRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc IgnoreSdkrwRule(IgnoreSdkrwRuleRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc DeleteErezept(DeleteErezeptRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetTherapies(GetTherapiesRequest) returns (GetTherapiesResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetAmountServiceCode(GetAmountServiceCodeRequest) returns (GetAmountServiceCodeResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc MarkNotApprovedPyschotherapy(MarkNotApprovedPyschotherapyRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc RestoreEntryHistory(RestoreEntryHistoryRequest) returns (RestoreEntryHistoryResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc ValidateDiagnose(ValidateDiagnoseRequest) returns (ValidateDiagnoseResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc ReRunValidateService(ReRunValidateServiceRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetTimelineEntryByIds(GetTimelineEntryByIdsRequest) returns (GetTimelineEntryIdsResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetPreParticipationServiceCodes(GetPreParticipationServiceCodesRequest) returns (GetTimelineEntryIdsResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc Document88130(Document88130Request) returns (Document88130Response) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetPsychotherapyTakeOver(GetPsychotherapyTakeOverRequest) returns (GetPsychotherapyTakeOverResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc TakeoverServiceTerminalApproval(TakeoverServiceTerminalApprovalRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetTakeOverDiagnosis(GetTakeOverDiagnosisRequest) returns (GetTakeOverDiagnosisResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetPsychotherapyBefore2020(GetPsychotherapyBefore2020Request) returns (GetPsychotherapyBefore2020Response) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc TakeOverDiagnosisWithScheinId(TakeOverDiagnosisWithScheinIdRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc MarkAcceptedByKV(MarkAcceptedByKVRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc RollbackDocumentTerminateService(RollbackDocumentTerminateServiceRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetLastDocumentedQuarter(GetLastDocumentedQuarterRequest) returns (GetLastDocumentedQuarterResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetTimelineByEnrollmentId(GetTimelineByEnrollmentIdRequest) returns (GetTimelineByEnrollmentIdResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc ReSendEABMail(ReSendEABMailRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc DocumentEABServiceCode(DocumentEABServiceCodeRequest) returns (DocumentEABServiceCodeResponse) {
    option (goff.secured) = "IS_AUTHENTICATED";
  }
  rpc FindLatestTimelineEntry(FindLatesTimelineEntryRequest) returns (FindLatesTimelineEntryResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc CheckIsVSST785(CheckIsVSST785Request) returns (CheckIsVSST785Response) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetDoctorLetterById(GetDoctorLetterByIdRequest) returns (GetDoctorLetterByIdResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetActionChainDiagnoseByCodes(GetActionChainDiagnoseByCodesRequest) returns (GetActionChainDiagnoseByCodesResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
}
