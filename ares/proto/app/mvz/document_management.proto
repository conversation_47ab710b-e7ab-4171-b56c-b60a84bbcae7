syntax = "proto3";

package app_mvz_document_management;

import "goff.proto";
import "repo/mvz/document_type_common.proto";
import "service/domains/common.proto";
import "service/domains/document_management_common.proto";
import "service/domains/patient_profile_common.proto";

option go_package = "git.tutum.dev/medi/tutum/ares/app/mvz/api/document_management";
option (goff.nats_subject) = "api.app.mvz";
option (goff.pointer_strict_mode) = true;

message EventDocumentManagementChange {}

enum LabResultsPDFMode {
  PORTRAIT = 0 [ (goff.value) = "portrait" ];
  LANDSCAPE = 1 [ (goff.value) = "landscape" ];
}

message EventGdtExportResult {
  bool result = 1;
  string error = 2;
}

message EventGdtImportResult {
  bool result = 1;
  string patientName = 2;
  string error = 3;
  document_management_common.DocumentNotificationType type = 4;
}

message CreateDocumentManagementRequest {
  string DocumentName = 1 [(goff.custom) = 'validate:"required"'];
  int64 CompanionFileId = 2 [(goff.custom) = 'validate:"required"'];
  string CompanionFilePath = 3 [(goff.custom) = 'validate:"required"'];
  document_management_common.DocumentManagementStatus Status = 4 [(goff.custom) = 'validate:"required"'];
}

message CreateDocumentManagementResponse {
  goff.UUID Id = 1;
  document_management_common.DocumentManagementModel DocumentManagementModel = 2;
  string PresignedUrl = 3;
}

message UpdateDocumentManagementStatusRequest {
  goff.UUID Id = 1 [(goff.custom) = 'validate:"required"'];
  document_management_common.DocumentManagementStatus Status = 2 [(goff.custom) = 'validate:"required"'];
  int64 ImportedDate = 3 [(goff.custom) = 'validate:"required"'];
}

message UpdateDocumentManagementStatusResponse {
  goff.UUID Id = 1;
  document_management_common.DocumentManagementModel DocumentManagementModel = 2;
}

message ListDocumentManagementRequest {
  string Value = 1 [(goff.nullable) = true];
  bool IsNotAssigned = 2;
  common.PaginationRequest Pagination = 3 [(goff.nullable) = true];
  int64 FromDate = 4 [(goff.nullable) = true];
  int64 ToDate = 5 [(goff.nullable) = true];
  document_management_common.DocumentManagementStatus Status = 6 [(goff.nullable) = true];
  repeated goff.UUID PatientIds = 7 [(goff.nullable) = true];
  repeated string SenderIds = 8 [(goff.nullable) = true];
}

message ListDocumentManagementResponse {
  repeated document_management_common.DocumentManagementItem Data = 1;
  common.PaginationResponse Pagination = 2 [(goff.nullable) = true];
}

message AssignPatientDocumentRequest {
  goff.UUID Id = 1 [(goff.custom) = 'validate:"required"'];
  goff.UUID PatientId = 2 [(goff.nullable) = true];
  document_management_common.DocumentManagementSender Sender = 3 [(goff.nullable) = true];
  document_type_common.DocumentType DocumentType = 6 [(goff.nullable) = true];
  string Description = 7 [(goff.nullable) = true];
}

message GetDocumentManagementRequest {
  goff.UUID Id = 1 [(goff.custom) = 'validate:"required"'];
}

message GetDocumentManagementResponse {
  document_management_common.DocumentManagementItem Data = 1;
}

message DeleteDocumentManagementRequest {
  goff.UUID Id = 1 [(goff.custom) = 'validate:"required"'];
  goff.UUID PatientId = 2 [(goff.nullable) = true];
}

message DeleteFailDocumentManagementRequest {
  repeated goff.UUID Ids = 1 [(goff.custom) = 'validate:"required"'];
  bool IsAll = 2;
}

message MarkReadDocumentManagementRequest {
  goff.UUID Id = 1 [(goff.custom) = 'validate:"required"'];
  goff.UUID PatientId = 2 [(goff.nullable) = true];
}

message UploadFile {
  string FileName = 1 [(goff.custom) = 'validate:"required"'];
  string ObjectId = 2 [(goff.custom) = 'validate:"required"'];
}

message UploadDocumentManagementRequest {
  repeated UploadFile Files = 1 [(goff.custom) = 'validate:"required"'];
}

message GetDocumentBadgeResponse {
  int64 unassignedCount = 1;
  int64 failCount = 2;
  int64 totalCount = 3;
}

message ReImportFailDocumentRequest {
  repeated goff.UUID Ids = 1 [(goff.custom) = 'validate:"required"'];
  bool IsAll = 2;
}

message ExportGdtDocumentRequest {
  goff.UUID GdtExportSettingId = 1 [(goff.custom) = 'validate:"required"'];
  int64 TreatmentDate = 2 [(goff.nullable) = true];
  int64 ReadingDate = 3 [(goff.nullable) = true];
  goff.UUID patientId = 4;
  goff.UUID scheinId = 5 [(goff.nullable) = true];
  int64 TreatmentTime = 6 [(goff.nullable) = true];
  int64 ReadingTime = 7 [(goff.nullable) = true];
}

message LabParameter {
  string Name = 1;
  string Min = 2;
  string Max = 3;
  string Unit = 4;
}

message LabResultItem {
  string Name = 1;
  string Value = 2;
  string Icon = 3;
}

message LabResultOverview {
  int64 Date = 1;
  string LabOrderId = 2;
  repeated LabResultItem Items = 3;
}

message GetLabResultsRequest {
  goff.UUID PatientId = 1;
  int64 Results = 2 [(goff.nullable)=true];
  int64 FromDate = 3 [(goff.nullable)=true];
  int64 ToDate = 4 [(goff.nullable)=true];
  repeated string FieldNames = 5 [(goff.nullable)=true];
  bool isOnlyPathologicalResults = 6 [(goff.nullable)=true];
}

message GetLabResultsResponse {
  repeated LabParameter LabParameters = 1;
  repeated LabParameter AvailableLabParameters = 2;
  repeated LabResultOverview LabResults = 3;
}

message GetLabResultsPDFResponse {
  bytes Pdf = 1;
  LabResultsPDFMode Mode = 2;
}

message ExportGdtDocumentResponse {}

service DocumentManagementApp {
  rpc CreateDocumentManagement(CreateDocumentManagementRequest) returns (CreateDocumentManagementResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc UpdateDocumentManagementStatus(UpdateDocumentManagementStatusRequest) returns (UpdateDocumentManagementStatusResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc ListDocumentManagement(ListDocumentManagementRequest) returns (ListDocumentManagementResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc AssignPatientDocument(AssignPatientDocumentRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc GetDocumentManagement(GetDocumentManagementRequest) returns (GetDocumentManagementResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc MarkReadDocumentManagement(MarkReadDocumentManagementRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc DeleteDocumentManagement(DeleteDocumentManagementRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc DeleteFailDocumentManagement(DeleteFailDocumentManagementRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc HandleEventDocumentManagementChange(EventDocumentManagementChange) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc UploadDocumentManagement(UploadDocumentManagementRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc GetDocumentBadge(goff.Empty) returns (GetDocumentBadgeResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc ReImportFailDocument(ReImportFailDocumentRequest) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc ProcessDocumentUpload(goff.Empty) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc ExportGdtDocument(ExportGdtDocumentRequest) returns (ExportGdtDocumentResponse) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc ProcessImportGdtDocuments(goff.Empty) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc ProcessImportLdtDocuments(goff.Empty) returns (goff.Empty) {
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }

  rpc OnPatientUpdate(patient_profile_common.EventPatientProfileChange) returns (goff.Empty) {
    option (goff.subject) = "api.app.mvz.AppMvzPatientProfile.PatientProfileChange";
  }

  // ===================== Labv2App ==========================
  rpc GetLabResults (GetLabResultsRequest) returns (GetLabResultsResponse){
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  }
  rpc GetLabResultsPDF (GetLabResultsRequest) returns (GetLabResultsPDFResponse){
    option (goff.secured) = "IS_AUTHENTICATED, CARE_PROVIDER_MEMBER";
  };
  // ===================== EndLabv2App ==========================
}
