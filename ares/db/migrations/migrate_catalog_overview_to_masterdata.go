package migrations

import (
	"context"
	"fmt"

	masterdata_model "git.tutum.dev/medi/tutum/ares/pkg/masterdata/model"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	catalog_overview_common "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_overview_common"
	bsnr_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_repo"
	uv_goa_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/bg_service_code"
	goa_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/goa"
	sdebm_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/sdebm"
	sdik_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/sdik"
	sdkt_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/sdkt"
	catalog_overview_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/catalog_overview"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"

	"go.mongodb.org/mongo-driver/mongo"
)

var CatalogType = []string{
	"Sdkt",
	"Sdebm",
	"Goa",
	"UvGoa",
	"Sdik",
	"BGInsurance",
}

type MigrateCatalogOverviewToMasterdata struct {
	CatalogOverviewRepo catalog_overview_repo.CatalogOverviewsDefaultRepository
	SdktRepo            *sdkt_repo.SdktRepo
	SdebmRepo           *sdebm_repo.SdebmRepo
	GoaRepo             *goa_repo.GoaRepo
	SdikRepo            *sdik_repo.SdikRepo
	UvGoaRepo           *uv_goa_repo.BGServiceCodeRepo
	BsnrRepo            *bsnr_repo.BSNRDefaultRepository
}

func (m *MigrateCatalogOverviewToMasterdata) Down(ctx context.Context, mongoClient *mongo.Client) error {
	return nil
}

func (m *MigrateCatalogOverviewToMasterdata) Name() string {
	return "MigrateCatalogOverviewToMasterdata"
}

func (m *MigrateCatalogOverviewToMasterdata) FindDataToMigrate(ctx *titan.Context, mongoClient *mongo.Client) ([]catalog_overview_repo.CatalogOverviews, error) {
	now := util.NowUnixMillis(ctx)
	return m.CatalogOverviewRepo.Find(ctx, bson.M{
		catalog_overview_repo.Field_CatalogType: bson.M{
			"$in": CatalogType,
		},
		"$or": bson.A{
			bson.M{
				catalog_overview_repo.Field_FromDate: bson.M{
					"$lte": now,
				},
				catalog_overview_repo.FieldToDate: nil,
			},
			bson.M{
				catalog_overview_repo.Field_FromDate: bson.M{
					"$lte": now,
				},
				catalog_overview_repo.FieldToDate: bson.M{
					"$gte": now,
				},
			},
			bson.M{
				catalog_overview_repo.Field_FromDate: nil,
				catalog_overview_repo.FieldToDate:    nil,
			},
		},
		catalog_overview_repo.Field_IsDeleted: false,
	})
}

func (m *MigrateCatalogOverviewToMasterdata) migrateSdkt(ctx *titan.Context, item catalog_overview_repo.CatalogOverviews) error {
	if item.Data.Sdkt == nil {
		return nil
	}

	itemId := item.Id.String()
	item.Data.Sdkt.SdktId = itemId
	model, err := m.SdktRepo.TransformedToSdktModel(ctx, item.Data.Sdkt)
	if err != nil {
		return errors.WithMessage(err, "failed to transform sdkt model")
	}

	res, err := m.SdktRepo.Upsert(ctx, model)
	if err != nil {
		return errors.WithMessage(err, "failed to upsert sdkt")
	}

	if res == nil {
		return errors.New("sdkt not found")
	}

	if res.Id != itemId {
		return errors.New("sdkt id is not match")
	}

	return nil
}

func (m *MigrateCatalogOverviewToMasterdata) migrateSdebm(ctx *titan.Context, item catalog_overview_repo.CatalogOverviews) error {
	if item.Data.Ebm == nil {
		return nil
	}

	itemId := item.Id.String()
	item.Data.Ebm.SdebmId = itemId
	model, err := m.SdebmRepo.TransformToSdebmModel(ctx, item.Data.Ebm, masterdata_model.NATIONAL_KV_REGION)
	if err != nil {
		return errors.WithMessage(err, "failed to transform sdebm model")
	}

	res, err := m.SdebmRepo.Upsert(ctx, model)
	if err != nil {
		return errors.WithMessage(err, "failed to upsert sdebm")
	}

	if res == nil {
		return errors.New("sdebm not found")
	}

	if res.Id != itemId {
		return errors.New("sdebm id is not match")
	}

	return nil
}

func (m *MigrateCatalogOverviewToMasterdata) migrateGoa(ctx *titan.Context, item catalog_overview_repo.CatalogOverviews) error {
	if item.Data.Goa == nil {
		return nil
	}

	itemId := item.Id.String()
	item.Data.Goa.GoaId = itemId
	model, err := m.GoaRepo.TransformToGoaModel(ctx, item.Data.Goa)
	if err != nil {
		return errors.WithMessage(err, "failed to transform goa model")
	}

	res, err := m.GoaRepo.Upsert(ctx, model)
	if err != nil {
		return errors.WithMessage(err, "failed to upsert goa")
	}

	if res == nil {
		return errors.New("goa not found")
	}

	if res.Id != itemId {
		return errors.New("goa id is not match")
	}

	return nil
}

func (m *MigrateCatalogOverviewToMasterdata) migrateUvGoa(ctx *titan.Context, item catalog_overview_repo.CatalogOverviews) error {
	if item.Data.UvGoa == nil {
		return nil
	}

	itemId := item.Id.String()
	item.Data.UvGoa.UvGoaId = itemId

	model, err := m.UvGoaRepo.TransformToBgServiceCode(ctx, item.Data.UvGoa)
	if err != nil {
		return errors.WithMessage(err, "failed to transform uv goa model")
	}

	res, err := m.UvGoaRepo.Upsert(ctx, model)
	if err != nil {
		return errors.WithMessage(err, "failed to upsert uv goa")
	}

	if res == nil {
		return errors.New("uv goa not found")
	}

	if res.Id != itemId {
		return errors.New("uv goa id is not match")
	}

	return nil
}

func (m *MigrateCatalogOverviewToMasterdata) migrateSdik(ctx *titan.Context, item catalog_overview_repo.CatalogOverviews) error {
	if item.Data.Sdik == nil {
		return nil
	}

	itemId := item.Id.String()
	item.Data.Sdik.SdikId = itemId

	model, err := m.SdikRepo.TransformToSdikModel(ctx, item.Data.Sdik)
	if err != nil {
		return errors.WithMessage(err, "failed to transform sdik model")
	}

	res, err := m.SdikRepo.Upsert(ctx, model)
	if err != nil {
		return errors.WithMessage(err, "failed to upsert sdik")
	}

	if res == nil {
		return errors.New("sdik not found")
	}

	if res.Id != itemId {
		return errors.New("sdik id is not match")
	}

	return nil
}

func (m *MigrateCatalogOverviewToMasterdata) Up(ctx context.Context, mongoClient *mongo.Client) error {
	// Migrate catalog overview to masterdata
	ctxs, err := mongodb_repo.GetContextsFromMongoClient(mongoClient, "tutum_mvz_db", "catalog_overview", "migration")
	if err != nil {
		return errors.WithMessage(err, "failed to get contexts from mongo client")
	}

	if len(ctxs) == 0 {
		return nil
	}

	for _, ctx := range ctxs {
		bsnrList, err := m.BsnrRepo.Find(ctx, bson.M{})
		if err != nil {
			return errors.WithMessage(err, "failed to find bsnr")
		}

		if len(bsnrList) == 0 {
			continue
		}

		careProviderId := ctx.UserInfo().CareProviderId.String()
		if careProviderId == "" {
			return errors.New("care provider id is empty")
		}

		res, err := m.FindDataToMigrate(ctx, mongoClient)
		if err != nil {
			return errors.WithMessage(err, "failed to find data to migrate")
		}

		if len(res) == 0 {
			continue
		}

		migratedIds := []uuid.UUID{}
		for _, bsnr := range bsnrList {
			ctx.UserInfo().BsnrId = bsnr.Id
			for _, item := range res {
				if item.Id == nil {
					continue
				}

				var err error
				switch item.CatalogType {
				case catalog_overview_common.Sdkt:
					err = m.migrateSdkt(ctx, item)
				case catalog_overview_common.Sdebm:
					err = m.migrateSdebm(ctx, item)
				case catalog_overview_common.Goa:
					err = m.migrateGoa(ctx, item)
				case catalog_overview_common.UvGoa:
					err = m.migrateUvGoa(ctx, item)
				case catalog_overview_common.Sdik:
					err = m.migrateSdik(ctx, item)
				default:
					continue
				}

				if err != nil {
					return errors.WithMessage(err, "failed to migrate catalog overview")
				}

				itemId := *item.Id
				if slice.Contains(migratedIds, itemId) {
					continue
				}

				migratedIds = append(migratedIds, itemId)
			}
		}

		for _, id := range migratedIds {
			if err := m.CatalogOverviewRepo.DeleteSoftById(ctx, id); err != nil {
				return errors.WithMessage(err, "failed to delete catalog overview")
			}
		}

		catalogAfterUpdated, err := m.FindDataToMigrate(ctx, mongoClient)
		if err != nil {
			return errors.WithMessage(err, "failed to find data to migrate")
		}

		if len(catalogAfterUpdated) != 0 {
			return errors.New(fmt.Sprintf("some catalog overview not migrated: %s", careProviderId))
		}
	}

	return nil
}
