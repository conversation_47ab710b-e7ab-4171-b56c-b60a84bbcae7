package migrations

import (
	"context"
	"log"

	"git.tutum.dev/medi/tutum/ares/pkg/repo"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/patient_info_template"
	action_chain_repos "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/action_chain"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/header_footer"
	doctor_letter_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/letter_template"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/settings"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/text_module_repo"
	setting_common "git.tutum.dev/medi/tutum/ares/service/domains/settings/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/settings/settings_service"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

/*
Migrate those settings:
- Text module
- Action Chain
- Deletion Settings
- Template Overview ( template, header/footer, patient info )
*/
type UpdateSettingMultiBsnr struct {
	settingsRepo            *repo.Repo[*settings.Settings[map[string]string]]
	bsnrRepo                bsnr_repo.BSNRDefaultRepository
	patientInfoTemplateRepo patient_info_template.PatientInfoTemplateRepo
	letterTemplateRepo      doctor_letter_repo.LetterTemplatesEntityRepo
	headerFooterRepo        header_footer.HeaderFooterEntityRepo
	actionChainRepo         action_chain_repos.ActionChainDefaultRepository
	textModuleRepo          text_module_repo.TextModuleDefaultRepository
}

var UpdateSettingMultiBsnrMod = submodule.Make[*UpdateSettingMultiBsnr](func(
	settingRepo *repo.Repo[*settings.Settings[map[string]string]],
	patientInfoTemplateRepo patient_info_template.PatientInfoTemplateRepo,
) *UpdateSettingMultiBsnr {
	return &UpdateSettingMultiBsnr{
		settingsRepo:            settingRepo,
		bsnrRepo:                bsnr_repo.NewBSNRDefaultRepository(),
		patientInfoTemplateRepo: patientInfoTemplateRepo,
		letterTemplateRepo:      doctor_letter_repo.NewLetterTemplatesEntityRepo(),
		headerFooterRepo:        header_footer.NewHeaderFooterEntityRepo(),
		actionChainRepo:         action_chain_repos.NewActionChainDefaultRepository(),
		textModuleRepo:          text_module_repo.NewTextModuleDefaultRepository(),
	}
},
	settings_service.SettingRepoMod,
	patient_info_template.PatientInfoTemplateRepoMod,
)

func (c *UpdateSettingMultiBsnr) Name() string {
	return "UpdateSettingMultiBsnr"
}

func (c *UpdateSettingMultiBsnr) Up(ctx context.Context, mongoClient *mongo.Client) error {
	ctxs, err := mongodb_repo.GetContextsFromMongoClient(mongoClient, "tutum_mvz_db", "settings", "migration")
	if err != nil {
		return errors.WithMessage(err, "failed to get contexts from mongo client")
	}
	if len(ctxs) == 0 {
		log.Println("No settings found")
		return nil
	}

	for _, ctx := range ctxs {
		bsnrs, err := c.bsnrRepo.Find(ctx, bson.M{})
		if err != nil {
			return err
		}
		if len(bsnrs) == 0 {
			log.Printf("no bsnr found in careproviderId: %s", ctx.UserInfo().CareProviderUUID().String())
			continue
		}
		if err := c.migrateActionChains(ctx, bsnrs); err != nil {
			return err
		}

		if err := c.migrateTextModules(ctx, bsnrs); err != nil {
			return err
		}

		if err := c.migrateDeletionSettings(ctx, bsnrs); err != nil {
			return err
		}

		if err := c.migratePatientTemplates(ctx, bsnrs); err != nil {
			return err
		}

		if err := c.migrateHeaderFooterAndLetterTemplates(ctx, bsnrs); err != nil {
			return err
		}
	}
	return nil
}

func (c *UpdateSettingMultiBsnr) Down(ctx context.Context, mongoClient *mongo.Client) error {
	return nil
}

func (c *UpdateSettingMultiBsnr) migrateDeletionSettings(ctx *titan.Context,
	bsnrs []bsnr_repo.BSNR) error {
	// Handle deletion settings
	filterDeletionSetting := bson.M{
		settings.Field_BsnrId:  nil,
		settings.Field_Feature: setting_common.SettingsFeatures_Timeline_Retention_Period,
	}
	deletionSettings, err := c.settingsRepo.Find(ctx, filterDeletionSetting)
	if err != nil {
		return err
	}
	// Handle deletion settings
	if len(deletionSettings) == 1 {
		setting := deletionSettings[0]
		// Assign first BSNR to existing deletion setting
		setting.BsnrId = bsnrs[0].Id
		_, err = c.settingsRepo.Update(ctx, setting)
		if err != nil {
			return err
		}

		// Create duplicate deletion settings for remaining BSNRs in bulk
		var newDeletionSettings []*settings.Settings[map[string]string]
		for _, bsnr := range bsnrs[1:] {
			ns := settings.Settings[map[string]string]{}
			err = copier.Copy(&ns, setting)
			if err != nil {
				return err
			}
			ns.Id = util.NewUUID()
			ns.BsnrId = bsnr.Id
			newDeletionSettings = append(newDeletionSettings, &ns)
		}

		_, err = c.settingsRepo.CreateMany(ctx, newDeletionSettings)
		if err != nil {
			return err
		}

		log.Printf("Migrate deletion settings successfully for careProviderId: %s", ctx.UserInfo().CareProviderUUID().String())
	} else {
		log.Printf("No deletion settings found for careProviderId: %s", ctx.UserInfo().CareProviderUUID().String())
	}
	return nil
}

func (c *UpdateSettingMultiBsnr) migratePatientTemplates(ctx *titan.Context, bsnrs []bsnr_repo.BSNR) error {
	patientTemps, err := c.patientInfoTemplateRepo.Find(ctx, bson.M{patient_info_template.Field_BsnrId: nil})
	if err != nil {
		return err
	}
	// Update existing templates with first BSNR
	for _, patientTemp := range patientTemps {
		tmp := *patientTemp
		tmp.AssignedToBsnrId = bsnrs[0].Id
		_, err = c.patientInfoTemplateRepo.Update(ctx, &tmp)
		if err != nil {
			return err
		}
	}

	// Create duplicates for remaining BSNRs
	var newTemplates []*patient_info_template.PatientInfoTemplateEntity
	for _, bsnr := range bsnrs[1:] {
		for _, patientTemp := range patientTemps {
			newTemplate := *patientTemp
			newTemplate.Id = util.NewUUID()
			newTemplate.AssignedToBsnrId = bsnr.Id
			newTemplates = append(newTemplates, &newTemplate)
		}
	}
	_, err = c.patientInfoTemplateRepo.CreateMany(ctx, newTemplates)
	if err != nil {
		return err
	}
	log.Printf("Migrate patient templates successfully for careProviderId: %s", ctx.UserInfo().CareProviderUUID().String())
	return nil
}

func (c *UpdateSettingMultiBsnr) migrateHeaderFooterAndLetterTemplates(ctx *titan.Context, bsnrs []bsnr_repo.BSNR) error {
	letterTemplates, err := c.letterTemplateRepo.Find(ctx, bson.M{doctor_letter_repo.Field_BsnrId: nil})
	if err != nil {
		return err
	}
	headerFooters, err := c.headerFooterRepo.Find(ctx, bson.M{header_footer.Field_BsnrId: nil})
	if err != nil {
		return err
	}
	// key is header footer id + bsnr id: new header footer id of another bsnr
	var mapHeaderFooterIDBsnrID = make(map[string]*uuid.UUID)
	for _, headerFooter := range headerFooters {
		tmp := *headerFooter
		bsnr := bsnrs[0]
		tmp.AssignedToBsnrId = bsnr.Id
		_, err = c.headerFooterRepo.Update(ctx, &tmp)
		if err != nil {
			return err
		}
		mapHeaderFooterIDBsnrID[headerFooter.Id.String()+bsnr.Id.String()] = tmp.Id
	}
	var tmpUpdatedHeaderFooters []*header_footer.HeaderFooterEntity
	for _, headerFooter := range headerFooters {
		for _, bsnr := range bsnrs[1:] {
			tmp := *headerFooter
			tmp.AssignedToBsnrId = bsnr.Id
			tmp.Id = util.NewUUID()
			tmpUpdatedHeaderFooters = append(tmpUpdatedHeaderFooters, &tmp)
			mapHeaderFooterIDBsnrID[headerFooter.Id.String()+bsnr.Id.String()] = tmp.Id
		}
	}
	_, err = c.headerFooterRepo.CreateMany(ctx, tmpUpdatedHeaderFooters)
	if err != nil {
		return err
	}
	log.Printf("Migrate header footer successfully for careProviderId: %s", ctx.UserInfo().CareProviderUUID().String())

	// update existing letter templates
	for _, letterTemplate := range letterTemplates {
		tmp := *letterTemplate
		bsnr := bsnrs[0]
		tmp.AssignedToBsnrId = bsnr.Id
		if tmp.HeaderFooterID != nil {
			tmp.HeaderFooterID = mapHeaderFooterIDBsnrID[tmp.HeaderFooterID.String()+bsnr.Id.String()]
		}
		_, err = c.letterTemplateRepo.Update(ctx, &tmp)
		if err != nil {
			return err
		}
	}
	// create remaining letter templates
	var newLetterTemplates []*doctor_letter_repo.LetterTemplatesEntity
	for _, letterTemplate := range letterTemplates {
		for _, bsnr := range bsnrs[1:] {
			tmp := *letterTemplate
			tmp.AssignedToBsnrId = bsnr.Id
			tmp.Id = util.NewUUID()
			if tmp.HeaderFooterID != nil {
				tmp.HeaderFooterID = mapHeaderFooterIDBsnrID[tmp.HeaderFooterID.String()+bsnr.Id.String()]
			}
			newLetterTemplates = append(newLetterTemplates, &tmp)
		}
	}
	_, err = c.letterTemplateRepo.CreateMany(ctx, newLetterTemplates)
	if err != nil {
		return err
	}

	log.Printf("Migrate letter templates successfully for careProviderId: %s", ctx.UserInfo().CareProviderUUID().String())
	return nil
}

func (c *UpdateSettingMultiBsnr) migrateActionChains(ctx *titan.Context, bsnrs []bsnr_repo.BSNR) error {
	actionChains, err := c.actionChainRepo.Find(ctx, bson.M{action_chain_repos.Field_BsnrId: nil})
	if err != nil {
		return err
	}
	// Update existing templates with first BSNR
	for _, actionChain := range actionChains {
		actionChain.BsnrId = bsnrs[0].Id
		_, err = c.actionChainRepo.Update(ctx, actionChain)
		if err != nil {
			return err
		}
	}

	// Create duplicates for remaining BSNRs
	var newActionChains []action_chain_repos.ActionChain
	for _, bsnr := range bsnrs[1:] {
		for _, actionChain := range actionChains {
			newActionChain := actionChain
			newActionChain.Id = util.NewUUID()
			newActionChain.BsnrId = bsnr.Id
			newActionChains = append(newActionChains, newActionChain)
		}
	}
	if len(newActionChains) > 0 {
		_, err = c.actionChainRepo.CreateMany(ctx, newActionChains)
		if err != nil {
			return err
		}
	}
	log.Printf("Migrate action chains successfully for careProviderId: %s", ctx.UserInfo().CareProviderUUID().String())
	return nil
}

func (c *UpdateSettingMultiBsnr) migrateTextModules(ctx *titan.Context, bsnrs []bsnr_repo.BSNR) error {
	textModules, err := c.textModuleRepo.Find(ctx, bson.M{text_module_repo.Field_BsnrId: nil})
	if err != nil {
		return err
	}
	// Update existing templates with first BSNR
	for _, textModule := range textModules {
		textModule.BsnrId = bsnrs[0].Id
		_, err = c.textModuleRepo.Update(ctx, textModule)
		if err != nil {
			return err
		}
	}
	// Create duplicates for remaining BSNRs
	var newTextModules []text_module_repo.TextModule
	for _, bsnr := range bsnrs[1:] {
		for _, textModule := range textModules {
			newTextModule := textModule
			newTextModule.Id = util.NewUUID()
			newTextModule.BsnrId = bsnr.Id
			newTextModules = append(newTextModules, newTextModule)
		}
	}
	if len(newTextModules) > 0 {
		_, err = c.textModuleRepo.CreateMany(ctx, newTextModules)
		if err != nil {
			return err
		}
	}
	log.Printf("Migrate text modules successfully for careProviderId: %s", ctx.UserInfo().CareProviderUUID().String())
	return nil
}
