package repo

import "git.tutum.dev/medi/tutum/ares/service/timeline_document_type/model"

const (
	FIELD_ID           = "_id"
	FIELD_NAME         = "name"
	FIELD_ABBREVIATION = "abbreviation"
	FIELD_BSNRS        = "bsnrs"
	FIELD_IS_ACTIVE    = "isactive"
	FIELD_IS_CUSTOM    = "iscustom"
	FIELD_IS_DELETED   = "isDeleted"
)

var builtInCommands = []model.TimelineDocumentType{
	{
		Name:         "Anamnese", // anamnese
		Abbreviation: "A",
		Bsnrs:        []string{},
		Description:  "",
		IsActive:     true,
		IsCustom:     false,
	},
	{
		Name:         "Anamnestische Diagnose", // anamnesticDiagnose
		Abbreviation: "AD",
		Bsnrs:        []string{},
		Description:  "",
		IsActive:     true,
		IsCustom:     false,
	},
	{
		Name:         "Befund",
		Abbreviation: "B",
		Bsnrs:        []string{},
		Description:  "",
		IsActive:     true,
		IsCustom:     false,
	},
	{
		Name:         "Brief",
		Abbreviation: "BR",
		Bsnrs:        []string{},
		Description:  "",
		IsActive:     true,
		IsCustom:     false,
	},
	{
		Name:         "Akutdiagnose", // Diagnose
		Abbreviation: "D",
		Bsnrs:        []string{},
		Description:  "",
		IsActive:     true,
		IsCustom:     false,
	},
	{
		Name:         "Dauerdiagnose", // permanantDiagnose
		Abbreviation: "DD",
		Bsnrs:        []string{},
		Description:  "",
		IsActive:     true,
		IsCustom:     false,
	},
	// {
	// 	Name:         "Leistung", // service
	// 	Abbreviation: "L",
	// 	Bsnrs:        []string{},
	// 	Description:  "",
	// 	IsActive:     true,
	// 	IsCustom:     false,
	// },
	{
		Name:         "Leistungskette", // service chain
		Abbreviation: "L",
		Bsnrs:        []string{},
		Description:  "",
		IsActive:     true,
		IsCustom:     false,
	},
	{
		Name:         "Therapie", // therapie
		Abbreviation: "T",
		Bsnrs:        []string{},
		Description:  "",
		IsActive:     true,
		IsCustom:     false,
	},
	{
		Name:         "Notiz", // note
		Abbreviation: "N",
		Bsnrs:        []string{},
		Description:  "",
		IsActive:     true,
		IsCustom:     false,
	},
	{
		Name:         "Aktionsketten", // action chain
		Abbreviation: "AK",
		Bsnrs:        []string{},
		Description:  "",
		IsActive:     true,
		IsCustom:     false,
	},
	{
		Name:         "DMP",
		Abbreviation: "DMP",
		Bsnrs:        []string{},
		Description:  "",
		IsActive:     true,
		IsCustom:     false,
	},
	{
		Name:         "Elektronisches Hautkrebsscreening",
		Abbreviation: "EHKS",
		Bsnrs:        []string{},
		Description:  "",
		IsActive:     true,
		IsCustom:     false,
	},
}
