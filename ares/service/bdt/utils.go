package bdt_service

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"git.tutum.dev/medi/tutum/ares/pkg/xdt/bdt"
	bdt_model "git.tutum.dev/medi/tutum/ares/pkg/xdt/bdt/model"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/text_module_common"
	medicine_common "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/prescribed"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/prescription"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/shoppingbag"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	timeline_common "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"gitlab.com/silenteer-oss/titan"
)

var customizeTimelineCommandMap = map[string]string{
	"Recall":  "REC",
	"Impfung": "IMP",
}

func getKvTreatmentCase(scheinType string) string {
	var kvTreatmentCase = slice.FindOne(bdt.KVCodes, func(sType string) bool {
		return sType == scheinType
	})

	return util.GetPointerValue(kvTreatmentCase)
}

func getCurrentFormType(code, medicationType string) medicine_common.FormType {
	if medicationType == "1" {
		return medicine_common.BTM
	}

	switch code {
	case "1":
		return medicine_common.Private
	default:
		return medicine_common.KREZ
	}
}

func getMapperMedicationType(code string) map[string]bool {
	return map[string]bool{
		TOGGLE_7_HILFSMITTEL: code == "3",
		TOGGLE_8_IMPFSTOFF:   code == "4",
		TOGGLE_9_BEDARF:      code == "5",
	}
}

// almost of boolean field in bdt will be true in case the value is 1
func getBoolValue(value string) bool {
	return value == "1"
}

func concatSliceToString(slice reflect.Value) string {
	// Ensure that the input is a slice
	if slice.Kind() != reflect.Slice {
		return ""
	}

	// Create a string slice to store string representations of each item
	var strItems []string

	// Iterate over the slice items
	for i := 0; i < slice.Len(); i++ {
		strItems = append(strItems, slice.Index(i).String())
	}

	return strings.Join(strItems, "\n")
}

/* return command, encounter note type, note content */
func getInfoOfNoteType(command, content string) (commandOutput string, noteType patient_encounter.EncounterNoteType, noteContent string) {
	noteContent = content
	// switch command {
	// case "A":
	// 	noteType = patient_encounter.ANAMNESE
	// case "B":
	// 	noteType = patient_encounter.FINDING
	// case "T":
	// 	noteType = patient_encounter.THERAPY
	// default:
	// 	commandOutput = "N"
	// 	noteType = patient_encounter.NOTE
	// }
	// cause bdt file includes both code and description then we use description for now to detect type of timeline
	switch command {
	case "Anamnese":
		commandOutput = "A"
		noteType = patient_encounter.ANAMNESE
	case "Befund":
		commandOutput = "B"
		noteType = patient_encounter.FINDING
	case "Therapie":
		commandOutput = "T"
		noteType = patient_encounter.THERAPY
	case "Notizen":
		commandOutput = "N"
		noteType = patient_encounter.NOTE
	default:
		commandOutput = "N"
		noteType = patient_encounter.NOTE
		noteContent = command + " - " + noteContent
	}

	return commandOutput, noteType, noteContent
}

// check if value not empty then add new item in additionalInfos
func addAdditionalInfoIfExistValue(additionalInfos []*patient_encounter.AdditionalInfoParent, key, value string) []*patient_encounter.AdditionalInfoParent {
	if value != "" {
		additionalInfos = append(additionalInfos, &patient_encounter.AdditionalInfoParent{
			FK:    key,
			Value: value,
		})
	}

	return additionalInfos
}

type CreateMedicineTimelineRequest struct {
	formType         medicine_common.FormType
	patientId        *uuid.UUID
	createdById      uuid.UUID
	bsnrId           *uuid.UUID
	bsnr             *string
	medi             bdt_model.Medicine
	selectedDate     int64
	selectedDateTime time.Time
	config           bdt_model.Config
	scheinId         *uuid.UUID
}

type MedicineTimelineResult struct {
	TimelineMedicine     timeline_repo.TimelineEntity[patient_encounter.EncounterMedicinePrescription]
	MedicinePrescription prescription.MedicinePrescription
	PrescribedMedication prescribed.PrescribedMedication
}

func createMedicineTimeline(ctx *titan.Context, req CreateMedicineTimelineRequest) MedicineTimelineResult {
	medi := req.medi
	config := req.config
	patientId := req.patientId
	createdById := req.createdById
	bsnrId := req.bsnrId
	selectedDateTime := req.selectedDateTime
	selectedDate := req.selectedDate
	createdAtMilli, _ := util.ConvertStringToMillisecond(medi.CreatedAt, config.DateTimeFormat)
	quantityNumber, _ := strconv.ParseInt(medi.Quantity, 10, 32)
	// default value of quantity
	if quantityNumber == 0 {
		quantityNumber = 1
	}
	intakeInterval := medicine_common.IntakeInterval{
		Freetext: medi.IntakeIntervalText,
		DJ:       function.If(medi.IntakeIntervalText != "", util.NewBool(false), util.NewBool(true)),
	}

	price, _ := strconv.Atoi(medi.Price)
	mapperMedicationType := getMapperMedicationType(medi.MedicationType)
	formSettingMap := map[string]any{
		"checkbox_gebuhrfrei": getBoolValue(medi.Isfreecopayment),
		"checkbox_gebpfl":     !getBoolValue(medi.Isfreecopayment),
		TOGGLE_7_HILFSMITTEL:  mapperMedicationType[TOGGLE_7_HILFSMITTEL],
		TOGGLE_8_IMPFSTOFF:    mapperMedicationType[TOGGLE_8_IMPFSTOFF],
		TOGGLE_9_BEDARF:       mapperMedicationType[TOGGLE_9_BEDARF],
		"checkbox_autIdem1":   getBoolValue(medi.AutIdem1),
		// T-Rezept
		"checkbox_alle":                  getBoolValue(medi.Un0814),
		"checkbox_dem":                   getBoolValue(medi.Un0815),
		"checkbox_behandlung_erfolgt_in": medi.Un0816 == "1",
		"checkbox_behandlung_erfolgt_au": medi.Un0816 == "2",
		// K-Rezept
	}

	formSettingByte, err := json.Marshal(formSettingMap)
	if err != nil {
		ctx.Logger().Error("Failed to marshal formSettingMap", "err", err)
	}
	scheinIds := []uuid.UUID{}
	if req.scheinId != nil {
		scheinIds = append(scheinIds, *req.scheinId)
	}

	medicines := []medicine_common.MedicineInfo{
		{
			Id:       *util.NewUUID(),
			Type:     medicine_common.KBV,
			Pzn:      medi.Pzn,
			Name:     medi.Name,
			Quantity: int32(quantityNumber),
			PackagingInformation: medicine_common.PackagingInformation{
				PackageSize: medicine_common.PackageSize{
					Nop: medi.Nop,
				},
			},
			ProductInformation: medicine_common.ProductInformation{
				DosageForm: medi.Dosageform,
				FormType:   req.formType,
			},
			CurrentFormType: getCurrentFormType(medi.CurrentFormType, medi.MedicationType),
			CreatedDate:     createdAtMilli,
			IntakeInterval:  &intakeInterval,
			PriceInformation: medicine_common.PriceInformation{
				PharmacySalePrice: float64(price),
				IsFreeCopayment:   util.NewPointer(medi.Isfreecopayment == "1"),
			},
			AutIdem: getBoolValue(medi.AutIdem1),
		},
	}

	var shoppingbagMedicine []shoppingbag.MedicineInfo
	if err := copier.Copy(&shoppingbagMedicine, medicines[0]); err != nil {
		ctx.Logger().Error("Failed to copy medicine to shopping bag medicine", "err", err)
	}

	formId := util.NewUUID()
	medicineId := util.NewUUID()

	timelineMedicine := timeline_repo.TimelineEntity[patient_encounter.EncounterMedicinePrescription]{
		Id:                util.NewUUID(),
		SelectedDate:      selectedDateTime,
		IsImported:        true,
		TreatmentDoctorId: createdById,
		BillingDoctorId:   &createdById,
		PatientId:         *patientId,
		ScheinIds:         scheinIds,
		EncounterCase:     util.NewPointer(patient_encounter.AB),
		Type:              timeline_common.TimelineEntityType_MedicinePrescription,
		Payload: patient_encounter.EncounterMedicinePrescription{
			Id:                     medicineId,
			MedicinePrescriptionId: medicineId,
			PatientId:              patientId,
			FormInfos: []*medicine_common.FormInfo{
				{
					Medicines:         medicines,
					FormSetting:       string(formSettingByte),
					CurrentFormType:   getCurrentFormType(medi.CurrentFormType, medi.MedicationType),
					PrescribeDate:     &selectedDate,
					PrintDate:         &selectedDate,
					Id:                *formId,
					TreatmentDoctorId: createdById,
				},
			},
		},
		CreatedBy:        createdById,
		AssignedToBsnrId: bsnrId,
	}

	medicinePrescription := prescription.MedicinePrescription{
		Id:        medicineId,
		PatientId: *patientId,
		DoctorId:  createdById,
		FormInfos: []prescription.FormInfo{
			{
				Id:                           *formId,
				Medicines:                    shoppingbagMedicine,
				FormSetting:                  string(formSettingByte),
				CurrentFormType:              getCurrentFormType(medi.CurrentFormType, medi.MedicationType),
				PrescribeDate:                &selectedDate,
				PrintDate:                    &selectedDate,
				IsShowFavHint:                false,
				EPrescription:                nil,
				BundleUrl:                    nil,
				PdfUrl:                       nil,
				HasChangedSprechsundenbedarf: util.NewBool(false),
			},
		},
		CreatedDate:       selectedDate,
		ContractId:        nil,
		CreatedBy:         createdById,
		TreatmentDoctorId: createdById,
		EncounterCase:     "AB",
		ScheinId:          util.GetPointerValue(req.scheinId),
		Bsnr:              req.bsnr,
		AssignedToBsnrId:  bsnrId,
	}

	prescribedMedication := prescribed.PrescribedMedication{
		Id:                     shoppingbagMedicine[0].Id,
		MedicinePrescriptionId: *medicineId,
		PatientId:              patientId,
		DoctorId:               &createdById,
		Medicine:               shoppingbagMedicine[0],
		PrintDate:              &selectedDate,
		CreatedDate:            selectedDate,
		ContractId:             nil,
		CreatedBy:              createdById,
		TreatmentDoctorId:      createdById,
		PrescribeDate:          &selectedDate,
		MedicationPlanId:       nil,
		EncounterCase:          "AB",
		FormInfoId:             *formId,
		Bsnr:                   *req.bsnr,
		ScheinMainGroup:        nil,
		FormInfo:               medicinePrescription.FormInfos[0],
		AssignedToBsnrId:       bsnrId,
	}

	return MedicineTimelineResult{
		TimelineMedicine:     timelineMedicine,
		MedicinePrescription: medicinePrescription,
		PrescribedMedication: prescribedMedication,
	}
}

func getBGType(code string) schein_common.BGType {
	switch code {
	case "40":
		return schein_common.GereralAction
	case "41":
		return schein_common.SpecialAction
	}
	return schein_common.GereralAction
}

func getTextModuleType(tmType string) []text_module_common.TextModuleUseFor {
	if tmType == "Befund" {
		return []text_module_common.TextModuleUseFor{text_module_common.TextModuleUseFor_Findings}
	} else if tmType == "Text" {
		// all of text module type with exclude OMIM-G
		return []text_module_common.TextModuleUseFor{
			text_module_common.TextModuleUseFor_Anamnesis,
			text_module_common.TextModuleUseFor_Cave,
			text_module_common.TextModuleUseFor_Findings,
			text_module_common.TextModuleUseFor_Note,
			text_module_common.TextModuleUseFor_Therapy,
			text_module_common.TextModuleUseFor_Form,
			text_module_common.TextModuleUseFor_BMP,
			text_module_common.TextModuleUseFor_Doctorletter,
		}
	}
	return []text_module_common.TextModuleUseFor{}
}

type newCustomizeTimelineReq struct {
	selectedDateTime time.Time
	patientId        *uuid.UUID
	employeeId       *uuid.UUID
	createdById      uuid.UUID
	bsnrId           *uuid.UUID
	command          string
	description      string
}

func newCustomizeTimeline(req newCustomizeTimelineReq) *timeline_repo.TimelineEntity[patient_encounter.EncounterCustomize] {
	command, ok := customizeTimelineCommandMap[req.command]
	if !ok {
		return nil
	}

	return &timeline_repo.TimelineEntity[patient_encounter.EncounterCustomize]{
		SelectedDate:      req.selectedDateTime,
		IsImported:        true,
		Type:              timeline_common.TimelineEntityType_Customize,
		PatientId:         *req.patientId,
		BillingDoctorId:   req.employeeId,
		TreatmentDoctorId: util.GetPointerValue(req.employeeId),
		ScheinIds:         []uuid.UUID{},
		Payload: patient_encounter.EncounterCustomize{
			Command:     command,
			Description: req.description,
		},
		EncounterCase:    util.NewPointer(patient_encounter.AB),
		TreatmentCase:    util.NewPointer(patient_encounter.TreatmentCaseCustodian),
		CreatedBy:        req.createdById,
		AssignedToBsnrId: req.bsnrId,
		RecentAuditLogs: []timeline_repo.AuditLog{
			{
				AuditLogId: util.NewUUID(),
				UserId:     &req.createdById,
				Date:       req.selectedDateTime,
				ActionType: timeline_common.Add,
			},
		},
	}
}

// there are some cases that selectedDate include hh:mm:ss
func parseSelectedDate(ctx *titan.Context, selectedDateString, dateFormat string) (time.Time, error) {
	var selectedDate time.Time
	var err error
	if len(selectedDateString) > 8 {
		selectedDate, err = time.ParseInLocation(fmt.Sprintf("%s %s", dateFormat, util.HH_MM_SS), selectedDateString, ctx.RequestTimeZone())
	} else {
		selectedDate, err = time.ParseInLocation(dateFormat, selectedDateString, ctx.RequestTimeZone())
	}
	if err != nil {
		return time.Time{}, err
	}
	return selectedDate, nil
}
