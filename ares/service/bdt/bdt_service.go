package bdt_service

import (
	"bufio"
	"bytes"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"runtime/debug"
	"slices"
	"strconv"
	"strings"
	"time"

	"emperror.dev/errors"
	employeebff "git.tutum.dev/medi/tutum/ares/app/admin/api/admin_bff"
	"git.tutum.dev/medi/tutum/ares/app/admin/api/bsnr"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/bg_billing"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_bg_insurance"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_sdav"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/form"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/heimi"
	patient_profile_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_profile"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/private_billing"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/app/mvz/patient_profile"
	"git.tutum.dev/medi/tutum/ares/pkg/config/hpm"
	"git.tutum.dev/medi/tutum/ares/pkg/formkey"
	hpm_rest_client "git.tutum.dev/medi/tutum/ares/pkg/hpm_rest/client"
	"git.tutum.dev/medi/tutum/ares/pkg/minio"
	"git.tutum.dev/medi/tutum/ares/pkg/operator"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_copy"
	"git.tutum.dev/medi/tutum/ares/pkg/xdt"
	"git.tutum.dev/medi/tutum/ares/pkg/xdt/bdt"
	bdt_model "git.tutum.dev/medi/tutum/ares/pkg/xdt/bdt/model"
	"git.tutum.dev/medi/tutum/ares/pkg/zitadel/management_client"
	contract_service "git.tutum.dev/medi/tutum/ares/service/contract/contract"
	"git.tutum.dev/medi/tutum/ares/service/contract/contract/model"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/action_chain_common"
	admin_service_employee "git.tutum.dev/medi/tutum/ares/service/domains/api/admin_service"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_bg_insurance_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdav_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_utils_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common/patientfile"
	dm_common "git.tutum.dev/medi/tutum/ares/service/domains/api/document_management/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/medicine_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/private_schein_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/text_module_common"
	bg_common "git.tutum.dev/medi/tutum/ares/service/domains/bg_billing/common"
	bg_billing_repo "git.tutum.dev/medi/tutum/ares/service/domains/bg_billing/repo"
	catalog_bg_insurance_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_bg_insurance"
	catalog_sdav_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_sdav"
	form_common "git.tutum.dev/medi/tutum/ares/service/domains/form/common"
	private_common "git.tutum.dev/medi/tutum/ares/service/domains/private_billing/common"
	priv_billing_repo "git.tutum.dev/medi/tutum/ares/service/domains/private_billing/repo"
	private_contract_group_common "git.tutum.dev/medi/tutum/ares/service/domains/private_contract_group/common"
	private_contract_group_service "git.tutum.dev/medi/tutum/ares/service/domains/private_contract_group/service"
	qes_common "git.tutum.dev/medi/tutum/ares/service/domains/qes/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bdt_log_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_common"
	action_chain_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/action_chain"
	medicine_repo_common "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/prescribed"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/prescription"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_participation"
	scheinRepo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	text_module_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/text_module"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/sdicd/sdicd_service"
	timeline_common "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	timeline_document_type_model "git.tutum.dev/medi/tutum/ares/service/timeline_document_type/model"
	"git.tutum.dev/medi/tutum/ares/service/timeline_document_type/repo"
	"git.tutum.dev/medi/tutum/ares/share"
	share_config "git.tutum.dev/medi/tutum/ares/share/config"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/spf13/viper"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"golang.org/x/text/transform"
)

type (
	BDTService struct {
		bsnrAppClient                    bsnr.BsnrServiceApp
		patientProfileBff                *patient_profile.PatientProfileBffImpl
		patientProfileServiceClient      *profile.PatientProfileServiceClient
		scheinRepo                       scheinRepo.ScheinRepoDefaultRepository
		employeeManagementService        EmployeeManagementService
		catalogSdavService               *catalog_sdav_service.CatalogSdavService
		formAppClient                    *form.FormAPPClient
		heimiAppClient                   *heimi.HeimiAppClient
		timelineDiagnoseRepo             timeline_repo.TimelineEntityRepo[patient_encounter.EncounterDiagnoseTimeline]
		timelineServiceRepo              timeline_repo.TimelineEntityRepo[patient_encounter.EncounterServiceTimeline]
		timelineNoteRepo                 timeline_repo.TimelineEntityRepo[patient_encounter.EncounterNoteTimeline]
		timelineMedicinePrescriptionRepo timeline_repo.TimelineEntityRepo[patient_encounter.EncounterMedicinePrescription]
		timelineMedicineRepo             timeline_repo.TimelineEntityRepo[medicine_common.Medicine]
		timelineCustomizeRepo            timeline_repo.TimelineEntityRepo[patient_encounter.EncounterCustomize]
		timelineFormRepo                 timeline_repo.TimelineEntityRepo[patient_encounter.EncounterForm]
		timelineDMRepo                   timeline_repo.TimelineEntityRepo[patient_encounter.EncounterDocumentManagement]
		xdtExtractor                     *xdt.XDTExtractor
		patientProfileRepo               *patient_profile.PatientProfileRepository
		bdtLogRepo                       *bdt_log_repo.BdtLogRepo
		minioClient                      *minio.Minio
		bucketBdt                        string
		bucketDmCompanion                string
		importingProcess                 map[string]bool
		privateContractGroupService      *private_contract_group_service.Service
		privBillingRepo                  *priv_billing_repo.PrivateBillingRepo
		hpmRestService                   hpm_rest_client.ServiceRest
		contractService                  *contract_service.Service
		bgBillingRepo                    *bg_billing_repo.BgBillingRepo
		actionChainRepo                  action_chain_repo.ActionChainDefaultRepository
		textModuleRepo                   text_module_repo.TextModuleDefaultRepository
		medicinePrescriptionRepo         prescription.MedicinePrescriptionDefaultRepository
		prescribedMedicationRepo         prescribed.PrescribedMedicationDefaultRepository
		catalogBgInsuranceService        *catalog_bg_insurance_service.CatalogBGInsuranceService
		timelineTypeRepo                 *repo.TimelineDocumentTypeRepo
		adminService                     admin_service_employee.EmployeeAdminService
		employeeProfileRepo              employee.EmployeeProfileDefaultRepository
		patientParticipationRepo         patient_participation.PatientParticipationDefaultRepository
	}
	EmployeeManagementService interface {
		CreateEmployee(ctx *titan.Context, newEmployee *employeebff.UpsertEmployeeRequest) (*employeebff.EmployeeResponse, error)
		GetById(ctx *titan.Context, request *employeebff.EmployeeGetByIdRequest) (*employeebff.EmployeeResponse, error)
	}
	handleImportScheinRequest struct {
		DataContext *bdt_model.DataContext
		PatientInfo *patient_profile_common.PatientInfo
		PatientId   *uuid.UUID
		BdtNumber   string
	}
	HandleImportPatient struct {
		DataContext *bdt_model.DataContext
		PatientData *bdt_model.PatientData
	}
	ExtractScheinRequest struct {
		DataContext *bdt_model.DataContext
		ScheinData  *bdt_model.Schein
	}
	ExtractPrivateScheinRequest struct {
		DataContext       *bdt_model.DataContext
		PrivateScheinData *bdt_model.PrivateSchein
	}
	ExtractBGScheinRequest struct {
		DataContext  *bdt_model.DataContext
		BGScheinData *bdt_model.BGSchein
	}
	ImportScheinRequest struct {
		BdtSchein   []bdt_model.Schein
		DataContext *bdt_model.DataContext
		PatientId   *uuid.UUID
		PatientInfo *patient_profile_common.PatientInfo
	}
	ImportTreatmentRequest struct {
		DataContext *bdt_model.DataContext
		Treatment   *bdt_model.Treatment
	}
	CreateHeilmittelFormRequest struct {
		template     string    //nolint:unused
		selectedDate int64     //nolint:unused
		employeeId   uuid.UUID //nolint:unused
		patientId    uuid.UUID //nolint:unused
		patientDob   int64     //nolint:unused
	}
	CreateHeilmittelFromFormMapperRequest struct {
		formMapper   FormMapper
		selectedDate int64
		patientId    uuid.UUID
		employeeId   uuid.UUID
		patientDob   int64
		bsnrId       *uuid.UUID
	}
	UpdatePatientMedicalDataRequest struct {
		treatment      *bdt_model.Treatment
		patientProfile *patient.PatientProfile
	}
	ImportPrivateScheinRequest struct {
		DataContext   *bdt_model.DataContext
		PrivateSchein []bdt_model.PrivateSchein
		PatientId     *uuid.UUID
		PatientInfo   *patient_profile_common.PatientInfo
	}
	ImportBGScheinRequest struct {
		DataContext *bdt_model.DataContext
		BGSchein    []bdt_model.BGSchein
		PatientId   *uuid.UUID
		PatientInfo *patient_profile_common.PatientInfo
	}
	CreatePatientMedicalDataRequest struct {
		PatientId   *uuid.UUID
		PatientData *bdt_model.PatientData
	}
)

var BaseDir = filepath.Join(os.TempDir(), "bdt")

var EmployeeManagementServiceMod = submodule.Make[EmployeeManagementService](func(
	titanClient *titan.Client,
) EmployeeManagementService {
	adminAppClient := employeebff.NewEmployeeManagementServiceClient(titanClient)
	return adminAppClient
}, config.GetDefaultTitanClientMod)

var BDTServiceMod = submodule.Make[*BDTService](
	newBDTService,
	config.GetDefaultTitanClientMod,
	patient_profile.PatientProfileServiceMod,
	EmployeeManagementServiceMod,
	catalog_sdav_service.CatalogSdavServiceMod,
	sdicd_service.SdicdServiceMod,
	xdt.XDTExtractorMod,
	patient_profile.PatientProfileRepositoryMod,
	bdt_log_repo.BdtLogRepoMod,
	minio.MinioMod,
	share_config.AdminConfigMod,
	private_contract_group_service.PrivateContractServiceMod,
	hpm_rest_client.HpmRestServiceMod,
	contract_service.ContractServiceMod,
	catalog_bg_insurance_service.CatalogBGInsuranceServiceMod,
	repo.TimelineDocumentTypeRepoMod,
	share.EmployeeAdminServiceMod,
)

func newBDTService(
	defaultClient *titan.Client,
	patientProfileBff *patient_profile.PatientProfileBffImpl,
	employeeManagementService EmployeeManagementService,
	catalogSdavService *catalog_sdav_service.CatalogSdavService,
	xdtExtractor *xdt.XDTExtractor,
	patientProfileRepo *patient_profile.PatientProfileRepository,
	bdtLogRepo *bdt_log_repo.BdtLogRepo,
	minioClient *minio.Minio,
	adminConfig *share_config.AdminAppConfigs,
	privateContractGroupService *private_contract_group_service.Service,
	hpmRestService hpm_rest_client.ServiceRest,
	contractService *contract_service.Service,
	catalogBgInsuranceService *catalog_bg_insurance_service.CatalogBGInsuranceService,
	timelineTypeRepo *repo.TimelineDocumentTypeRepo,
	adminService admin_service_employee.EmployeeAdminService,
) *BDTService {
	bsnrAppClient := bsnr.NewBsnrServiceAppClient(defaultClient)
	patientProfileServiceClient := profile.NewPatientProfileServiceClient(defaultClient)
	formAppClient := form.NewFormAPPClient(defaultClient)
	heimiAppClient := heimi.NewHeimiAppClient(defaultClient)

	return &BDTService{
		bsnrAppClient:                    bsnrAppClient,
		patientProfileBff:                patientProfileBff,
		patientProfileServiceClient:      patientProfileServiceClient,
		scheinRepo:                       scheinRepo.NewScheinRepoDefaultRepository(),
		employeeManagementService:        employeeManagementService,
		catalogSdavService:               catalogSdavService,
		formAppClient:                    formAppClient,
		heimiAppClient:                   heimiAppClient,
		timelineDiagnoseRepo:             timeline_repo.NewTimelineRepoDefaultRepository[patient_encounter.EncounterDiagnoseTimeline](),
		timelineServiceRepo:              timeline_repo.NewTimelineRepoDefaultRepository[patient_encounter.EncounterServiceTimeline](),
		timelineNoteRepo:                 timeline_repo.NewTimelineRepoDefaultRepository[patient_encounter.EncounterNoteTimeline](),
		timelineMedicinePrescriptionRepo: timeline_repo.NewTimelineRepoDefaultRepository[patient_encounter.EncounterMedicinePrescription](),
		timelineMedicineRepo:             timeline_repo.NewTimelineRepoDefaultRepository[medicine_common.Medicine](),
		timelineCustomizeRepo:            timeline_repo.NewTimelineRepoDefaultRepository[patient_encounter.EncounterCustomize](),
		timelineFormRepo:                 timeline_repo.NewTimelineRepoDefaultRepository[patient_encounter.EncounterForm](),
		timelineDMRepo:                   timeline_repo.NewTimelineRepoDefaultRepository[patient_encounter.EncounterDocumentManagement](),
		xdtExtractor:                     xdtExtractor,
		patientProfileRepo:               patientProfileRepo,
		bdtLogRepo:                       bdtLogRepo,
		minioClient:                      minioClient,
		bucketBdt:                        adminConfig.MinioClientConfig.BucketXBdt,
		bucketDmCompanion:                adminConfig.MinioClientConfig.BucketDmCompanion,
		importingProcess:                 make(map[string]bool),
		privateContractGroupService:      privateContractGroupService,
		privBillingRepo:                  priv_billing_repo.NewPrivateBillingRepo(),
		bgBillingRepo:                    bg_billing_repo.NewBgBillingRepo(),
		hpmRestService:                   hpmRestService,
		contractService:                  contractService,
		actionChainRepo:                  action_chain_repo.NewActionChainDefaultRepository(),
		textModuleRepo:                   text_module_repo.NewTextModuleDefaultRepository(),
		medicinePrescriptionRepo:         prescription.NewMedicinePrescriptionDefaultRepository(),
		prescribedMedicationRepo:         prescribed.NewPrescribedMedicationDefaultRepository(),
		catalogBgInsuranceService:        catalogBgInsuranceService,
		timelineTypeRepo:                 timelineTypeRepo,
		adminService:                     adminService,
		employeeProfileRepo:              employee.NewEmployeeProfileDefaultRepository(),
		patientParticipationRepo:         patient_participation.NewPatientParticipationDefaultRepository(),
	}
}

func (srv *BDTService) Import(ctx *titan.Context, fileName string) (*bdt_log_repo.BdtLogEntity, error) {
	currentProcess, err := srv.bdtLogRepo.GetCurrentProcess(ctx)
	if err != nil {
		return nil, err
	}

	key := ctx.UserInfo().CareProviderId.String()
	if srv.getImport(key) {
		return currentProcess, nil
	}

	ctx = ctx.Clone()
	go func() {
		defer func() {
			delete(srv.importingProcess, key)
			if err := recover(); err != nil {
				errMsg := fmt.Sprintf("panic: %v\n\n%s", err, string(debug.Stack()))
				ctx.Logger().Error(errMsg)
			}
		}()
		if err := srv.importBdt(ctx, fileName, currentProcess); err != nil {
			ctx.Logger().Error("ImportBdt failed", "err", err)
		}
	}()

	return currentProcess, nil
}

func (srv *BDTService) getImport(key string) bool {
	_, ok := srv.importingProcess[key]
	if !ok {
		srv.importingProcess[key] = true
		return false
	}

	return srv.importingProcess[key]
}

func (*BDTService) Remove(ctx *titan.Context) error {
	return os.RemoveAll(filepath.Join(BaseDir, ctx.UserInfo().CareProviderId.String()))
}

func (srv *BDTService) importBdt(ctx *titan.Context, fileName string, currentProcess *bdt_log_repo.BdtLogEntity) error {
	res, err := srv.extract(ctx, fileName, currentProcess)
	if err != nil {
		return err
	}

	if res == nil {
		return errors.New("can not found download file")
	}

	if res.ExtractDir == "" {
		return errors.New("Extracted folder is empty")
	}

	if err := srv.process(ctx, res.ExtractDir, currentProcess); err != nil {
		return err
	}

	if err := srv.uploadLog(ctx); err != nil {
		return err
	}

	if err := srv.cleanUp(ctx, res.ExtractDir); err != nil {
		return err
	}

	ctx.Logger().Info("Successfully import bdt file", "fileName", fileName, "careproviderId", ctx.UserInfo().CareProviderId.String())

	return nil
}

func (srv *BDTService) cleanUp(ctx *titan.Context, extractDir string) error {
	currentProcess, err := srv.bdtLogRepo.GetCurrentProcess(ctx)
	if err != nil {
		return err
	}
	if err := srv.checkIfCustomTimelineTypeExistAndAddNewType(ctx); err != nil {
		ctx.Logger().Error("checkIfCustomTimelineTypeExistAndAddNewType failed", "err", err)
	}
	if err := srv.updateContractsOfEmployee(ctx, currentProcess); err != nil {
		return fmt.Errorf("update employee contract failed: %w", err)
	}
	if currentProcess == nil || currentProcess.ReadRaws != currentProcess.TotalRaws {
		return nil
	}

	if err := os.RemoveAll(extractDir); err != nil {
		return err
	}

	res, err := srv.bdtLogRepo.MarkAsDone(ctx)
	if err != nil {
		return err
	}

	if res == nil {
		return errors.New("can not mark as done")
	}

	// if err := srv.minioClient.RemoveObject(ctx, srv.bucketBdt, res.FileName, minio.RemoveObjectOptions{}); err != nil {
	// 	return err
	// }

	return nil
}

func (srv *BDTService) updateContractsOfEmployee(ctx *titan.Context, currentProcess *bdt_log_repo.BdtLogEntity) error {
	dataContext, _ := currentProcess.ToBdtDataContext()
	for _, emp := range dataContext.Employees {
		contractMap := make([]*admin_service_employee.UpdateDoctorParticipate, 0)
		hzvContract := make([]*admin_service_employee.UpdateDoctorParticipate, 0)
		for _, c := range emp.Contracts {
			contract := &admin_service_employee.UpdateDoctorParticipate{
				ContractId:            c.ContractId,
				ChargeSystemId:        c.ChargeSystemId,
				StartDate:             &c.StartDate,
				Type:                  c.Type,
				EndDate:               c.EndDate,
				EnrollmentType:        c.EnrollmentType,
				EnrollmentTypeOptions: c.EnrollmentTypeOptions,
			}
			contractMap = append(contractMap, contract)
			if c.Type == common.ContractType_HouseDoctorCare {
				hzvContract = append(hzvContract, contract)
			}
		}

		if len(contractMap) > 0 {
			_, err := srv.adminService.UpdateContractEmployee(ctx, &admin_service_employee.UpdateDoctorParticipateRequest{
				DoctorId:  &emp.Id,
				Contracts: contractMap,
			})
			if err != nil {
				return err
			}
		}
		if len(hzvContract) > 0 {
			_, err := srv.employeeProfileRepo.FindOneAndUpdate(ctx, bson.M{
				employee.Field_Id: emp.Id,
			}, bson.M{
				operator.Set: bson.M{
					employee.Field_HzvContracts:    hzvContract,
					employee.Field_HasHzvContracts: true,
				},
			})
			if err != nil {
				return err
			}
		}
	}
	return nil
}

// checkIfCustomTimelineTypeExistAndAddNewType checks if the customize timeline type exists, if not, add new timeline type
func (srv *BDTService) checkIfCustomTimelineTypeExistAndAddNewType(ctx *titan.Context) error {
	for n, c := range customizeTimelineCommandMap {
		exists, err := srv.timelineCustomizeRepo.IsExistCommand(ctx, c)
		if err != nil {
			return err
		}
		if exists {
			docTypeModel := &timeline_document_type_model.TimelineDocumentType{
				Name:         n,
				Abbreviation: c,
				IsActive:     true,
				IsCustom:     true,
			}
			_, err = srv.timelineTypeRepo.CreateTimelineDocumentType(ctx, docTypeModel)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (srv *BDTService) extract(ctx *titan.Context, fileName string, currentProcess *bdt_log_repo.BdtLogEntity) (*xdt.ExtractResponse, error) {
	readRaws := int64(0)
	if currentProcess != nil {
		readRaws = currentProcess.ReadRaws
	}
	res, err := srv.xdtExtractor.Extract(ctx, xdt.ExtractRequest{
		BaseDir:  BaseDir,
		FileName: fileName,
		ReadRaws: readRaws,
	})
	if err != nil {
		return nil, err
	}

	if res == nil {
		return nil, errors.New("can not found download file")
	}

	if res.TotalRaws > 0 {
		if _, err := srv.bdtLogRepo.UpdateTotalRaws(ctx, res.TotalRaws); err != nil {
			return nil, err
		}
	}

	return res, nil
}

func (srv *BDTService) process(ctx *titan.Context, extractedFolder string, currentProcess *bdt_log_repo.BdtLogEntity) error {
	dataContext, readRaws := currentProcess.ToBdtDataContext()
	var reader *os.File
	defer reader.Close()

	return filepath.Walk(extractedFolder, func(filePath string, fileInfo os.FileInfo, walkErr error) error {
		if walkErr != nil {
			return walkErr
		}
		ext := filepath.Ext(fileInfo.Name())
		if fileInfo.IsDir() || ext != ".bdt" {
			return nil
		}

		file, err := os.Open(filePath)
		if err != nil {
			return err
		}
		defer file.Close()
		reader := transform.NewReader(bufio.NewReader(file), dataContext.Config.Decoder)
		scanner := bufio.NewScanner(reader)
		fields := bdt.XdtModelSlice{}
		previousField := &xdt.XdtModel{}
		for i := 0; scanner.Scan(); i++ {
			line := scanner.Text()
			if line == "" {
				continue
			}

			xdtModel, err := bdt.ToXdtModel(line, dataContext.Config, previousField)
			if err != nil {
				return err
			}

			readRaws++
			if xdtModel == nil {
				continue
			}

			if previousField != nil && previousField.FieldId != "" {
				previousField = &xdt.XdtModel{}
			}

			fields = append(fields, *xdtModel)
		}

		if len(fields) == 0 {
			return nil
		}

		if err := srv.importData(ctx, fields, dataContext); err != nil {
			return err
		}

		if err := srv.updateContext(ctx, dataContext, readRaws); err != nil {
			return err
		}

		if err := os.Remove(filePath); err != nil {
			return err
		}

		return nil
	})
}

func (srv *BDTService) updateContext(ctx *titan.Context, dataContext *bdt_model.DataContext, readRaws int64) error {
	var data bdt_log_repo.BdtDataContext
	err := copier.Copy(&data, &bdt_model.DataContext{
		HeaderData:       dataContext.HeaderData,
		ScheinMap:        dataContext.ScheinMap,
		PrivateScheinMap: dataContext.PrivateScheinMap,
		BGScheinMap:      dataContext.BGScheinMap,
		Employees:        dataContext.Employees,
		PatientIdMap:     dataContext.PatientIdMap,
		ScheinNumberMap:  dataContext.ScheinNumberMap,
		ScheinIdMap:      dataContext.ScheinIdMap,
		BsnrIds:          dataContext.BsnrIds,
	})
	if err != nil {
		return err
	}

	if _, err := srv.bdtLogRepo.UpdateDataContext(ctx, &data, readRaws); err != nil {
		return err
	}

	return nil
}

func (srv *BDTService) importData(ctx *titan.Context, fields bdt.XdtModelSlice, dataContext *bdt_model.DataContext) error {
	if fields.IsActionChain() {
		actionChain := bdt_model.ActionChain{}
		xdt.FillData(&actionChain, fields, 0)
		return srv.importActionChain(ctx, actionChain)
	}

	if fields.IsTextModule() {
		textModule := bdt_model.TextModule{}
		xdt.FillData(&textModule, fields, 0)
		return srv.importTextModule(ctx, textModule)
	}

	if fields.IsPractice() {
		practice := bdt_model.BesaPractice{}
		xdt.FillData(&practice, fields, 0)
		employeeProfiles, err := srv.importBesaPractice(ctx, practice, dataContext)
		if err != nil {
			return err
		}

		dataContext.Employees = employeeProfiles
		return nil
	}

	if fields.IsPatient() {
		patient := bdt_model.PatientData{}
		xdt.FillData(&patient, fields, 0)
		return srv.handleImportPatient(ctx, &HandleImportPatient{
			DataContext: dataContext,
			PatientData: &patient,
		})
	}

	if fields.IsHeader() {
		header := bdt_model.BDTHeader{}
		xdt.FillData(&header, fields, 0)
		dataContext.Config = header.SetUpConfig()
		dataContext.HeaderData = &header
		return nil
	}

	if fields.IsScheinData() {
		schein := bdt_model.Schein{}
		xdt.FillData(&schein, fields, 0)
		return extractSchein(&ExtractScheinRequest{
			DataContext: dataContext,
			ScheinData:  &schein,
		})
	}

	if fields.IsAddressMaster() {
		addressMaster := bdt_model.AddressMasterData{}
		xdt.FillData(&addressMaster, fields, 0)
		return srv.importAddressMasterData(ctx, &addressMaster, dataContext)
	}

	if fields.IsTreatment() {
		treatment := bdt_model.Treatment{}
		xdt.FillData(&treatment, fields, 0)
		return srv.importTreatmentData(ctx, &ImportTreatmentRequest{
			DataContext: dataContext,
			Treatment:   &treatment,
		})
	}

	if fields.IsPrivateSchein() {
		privateSchein := bdt_model.PrivateSchein{}
		xdt.FillData(&privateSchein, fields, 0)
		return extractPrivateSchein(&ExtractPrivateScheinRequest{
			DataContext:       dataContext,
			PrivateScheinData: &privateSchein,
		})
	}

	if fields.IsBGSchein() {
		bgSchein := bdt_model.BGSchein{}
		xdt.FillData(&bgSchein, fields, 0)
		return extractBGSchein(&ExtractBGScheinRequest{
			DataContext:  dataContext,
			BGScheinData: &bgSchein,
		})
	}

	return nil
}

func (srv *BDTService) importBesaPractice(ctx *titan.Context, bdtRaw bdt_model.BesaPractice, dataContext *bdt_model.DataContext) ([]bdt_model.BDTEmployee, error) {
	var bdtEmployees []bdt_model.BDTEmployee
	now := time.Now()
	empRequests := []employeebff.UpsertEmployeeRequest{}
	defer func() {
		ctx.Logger().Info("importBesaPractice",
			"duration", time.Since(now).String())
	}()
	bsnrs := []bsnr_common.BSNR{}
	for _, bsnrRaw := range bdtRaw.Bsnrs {
		streetInfo := strings.Split(bsnrRaw.Street, " ")
		var bsnrStreet = bsnrRaw.Street
		var bsnrNumber string
		if len(streetInfo) > 1 {
			bsnrStreet = strings.Join(streetInfo[0:len(streetInfo)-1], " ")
			bsnrNumber = streetInfo[len(streetInfo)-1]
		}
		temp := bsnr.BsnrRequest{
			// note
			// ComdoxxId
			Code:         bsnrRaw.Code,
			Name:         bsnrRaw.BsnrName,
			Street:       bsnrStreet,
			Number:       bsnrNumber,
			PostCode:     bsnrRaw.PostCode,
			City:         bsnrRaw.City,
			Country:      "D",
			PhoneNumber:  bsnrRaw.PhoneNumber,
			Fax:          bsnrRaw.Fax,
			Email:        bsnrRaw.Email,
			FacilityType: bsnr_common.FacilityType_Practice,
			HpmConfig: bsnr_common.HpmConfig{
				Endpoint: viper.GetString(hpm.HPM_URL),
			},
		}
		newBSNR, err := srv.bsnrAppClient.ImportBSNR(ctx, temp)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if newBSNR.Bsnr == nil {
			return nil, errors.New("can not  bsnr")
		}
		bsnrs = append(bsnrs, *newBSNR.Bsnr)
		for _, v := range bsnrRaw.Lanrs {
			// add unique initial for zitadel accounts
			result, err := xdt.GetDoctorInfo(v.DoctorFirstName, v.DoctorLastName)
			if err != nil {
				return nil, err
			}
			contracts, havgVpId := srv.getContractsAndHavgVpIdOfEmployee(ctx, v.HavgId, bsnrRaw.Code)
			newEmployee := employeebff.UpsertEmployeeRequest{
				Types:          []common.UserType{common.DOCTOR},
				FirstName:      result.FirstName,
				LastName:       result.LastName,
				UserName:       result.UserName,
				Initial:        result.Initial,
				Title:          &v.Title,
				Lanr:           &v.Code,
				AdditionalName: (*patient_profile_common.AdditionalName)(&v.DoctorMidName),
				TeamNumbers:    &v.AsvTeamNumbers,
				// AreaOfExpertise: ,
				HavgId:          &v.HavgId,
				HasHzvContracts: function.If(v.HavgId != "", true, false),
				HavgVpId:        &havgVpId,
				Contracts:       &contracts,
				// ComdoxxId
				MediverbundId: &v.MediVerbundId,
				// Modules
				Bsnrs:    []string{newBSNR.Bsnr.Code},
				BsnrIds:  []uuid.UUID{*newBSNR.Bsnr.Id},
				IsDoctor: true,
				Password: management_client.DefaultPassword,
			}
			tmprs, i := slice.FindOneIndex(empRequests, func(e employeebff.UpsertEmployeeRequest) bool {
				return util.GetPointerValue(e.Lanr) == util.GetPointerValue(newEmployee.Lanr) &&
					e.FirstName == newEmployee.FirstName &&
					e.LastName == newEmployee.LastName
			})
			if tmprs == nil {
				empRequests = append(empRequests, newEmployee)
			} else {
				empRequests[i].BsnrIds = slice.Uniq(append(tmprs.BsnrIds, *newBSNR.Bsnr.Id))
				empRequests[i].Bsnrs = slice.Uniq(append(tmprs.Bsnrs, newBSNR.Bsnr.Code))
			}
		}

		for _, v := range bsnrRaw.PseudoLanrs {
			// add unique initial for zitadel accounts
			result, err := xdt.GetDoctorInfo(v.DoctorFirstName, v.DoctorLastName)
			if err != nil {
				return nil, err
			}
			contracts, havgVpId := srv.getContractsAndHavgVpIdOfEmployee(ctx, v.HavgId, bsnrRaw.Code)
			newEmployee := employeebff.UpsertEmployeeRequest{
				Types:          []common.UserType{common.DOCTOR},
				FirstName:      result.FirstName,
				LastName:       result.LastName,
				Initial:        result.Initial,
				UserName:       result.UserName,
				Title:          &v.Title,
				PseudoLanr:     &v.Code,
				AdditionalName: (*patient_profile_common.AdditionalName)(&v.DoctorMidName),
				TeamNumbers:    &v.AsvTeamNumbers,
				// AreaOfExpertise: ,
				HavgId:          &v.HavgId,
				HasHzvContracts: function.If(v.HavgId != "", true, false),
				HavgVpId:        &havgVpId,
				Contracts:       &contracts,
				// ComdoxxId
				MediverbundId: &v.MediVerbundId,
				// Modules
				Bsnrs:    []string{newBSNR.Bsnr.Code},
				BsnrIds:  []uuid.UUID{*newBSNR.Bsnr.Id},
				IsDoctor: true,
				Password: management_client.DefaultPassword,
			}

			tmprs, i := slice.FindOneIndex(empRequests, func(e employeebff.UpsertEmployeeRequest) bool {
				return util.GetPointerValue(e.Lanr) == util.GetPointerValue(newEmployee.Lanr) &&
					e.FirstName == newEmployee.FirstName &&
					e.LastName == newEmployee.LastName
			})
			if tmprs == nil {
				empRequests = append(empRequests, newEmployee)
			} else {
				empRequests[i].BsnrIds = slice.Uniq(append(tmprs.BsnrIds, *newBSNR.Bsnr.Id))
				empRequests[i].Bsnrs = slice.Uniq(append(tmprs.Bsnrs, newBSNR.Bsnr.Code))
			}
		}
	}

	bsnrIds := []*uuid.UUID{}
	for _, bsnr := range bsnrs {
		if bsnr.Id == nil {
			continue
		}
		bsnrIds = append(bsnrIds, bsnr.Id)
	}
	dataContext.BsnrIds = bsnrIds

	for _, e := range empRequests {
		employeeRes, err := srv.employeeManagementService.CreateEmployee(ctx, &e)
		if err != nil {
			return nil, err
		}
		bdtEmployees = append(bdtEmployees, bdt_model.BDTEmployee{
			Id:        util.GetPointerValue(employeeRes.Id),
			FirstName: e.FirstName,
			LastName:  e.LastName,
			Bsnrs: slice.Filter(bsnrs, func(bsnr bsnr_common.BSNR) bool {
				return slice.Contains(e.BsnrIds, util.GetPointerValue(bsnr.Id))
			}),
			Lanr:      util.GetPointerValue(e.Lanr),
			Initial:   e.Initial,
			Title:     util.GetPointerValue(e.Title),
			Contracts: util.GetPointerValue(e.Contracts),
		})
	}

	return bdtEmployees, nil
}

func extractSchein(req *ExtractScheinRequest) error {
	if req.ScheinData == nil {
		return nil
	}

	bdtScheinRaw := *req.ScheinData
	req.DataContext.ScheinMap[bdtScheinRaw.PatientNumber] = append(req.DataContext.ScheinMap[bdtScheinRaw.PatientNumber], bdtScheinRaw)
	return nil
}

func extractPrivateSchein(req *ExtractPrivateScheinRequest) error {
	if req.PrivateScheinData == nil {
		return nil
	}

	bdtPrivateScheinRaw := *req.PrivateScheinData
	req.DataContext.PrivateScheinMap[bdtPrivateScheinRaw.PatientNumber] = append(req.DataContext.PrivateScheinMap[bdtPrivateScheinRaw.PatientNumber], bdtPrivateScheinRaw)
	return nil
}

func extractBGSchein(req *ExtractBGScheinRequest) error {
	if req.BGScheinData == nil {
		return nil
	}

	bdtBGScheinRaw := *req.BGScheinData
	if req.DataContext.BGScheinMap == nil {
		req.DataContext.BGScheinMap = make(map[string][]bdt_model.BGSchein)
	}

	req.DataContext.BGScheinMap[bdtBGScheinRaw.PatientNumber] = append(
		req.DataContext.BGScheinMap[bdtBGScheinRaw.PatientNumber],
		bdtBGScheinRaw,
	)
	return nil
}

// Import schein and timeline for patient
func (srv *BDTService) importSchein(ctx *titan.Context, req *ImportScheinRequest) error {
	now := time.Now()
	if req == nil || len(req.BdtSchein) == 0 {
		ctx.Logger().Error("importSchein", "err", "req is nil or len(req.BdtSchein) == 0")
		return nil
	}
	patientNumber := req.BdtSchein[0].PatientNumber
	if req.DataContext == nil {
		ctx.Logger().Error("importSchein", "err", "req.DataContext is required")
		return nil
	}
	defer func() {
		ctx.Logger().Info("importSchein",
			"patientBdtNumber", patientNumber,
			"patientId", req.PatientId.String(),
			"duration", time.Since(now).String())
	}()
	timelineDiagnosis := []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{}
	timeLineServices := []timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{}
	kvScheins := []scheinRepo.ScheinRepo{}
	patientId := req.PatientId
	patientInfo := req.PatientInfo
	dataContext := req.DataContext
	tmap := make(map[string]*uuid.UUID)
	dateFormat := dataContext.Config.DateTimeFormat
	patientParticipations := []patient_participation.PatientParticipation{}

	for _, bdtScheinRaw := range req.BdtSchein {
		var employeeId uuid.UUID
		treatmentDoctor := dataContext.SearchTreatmentDoctor(bdtScheinRaw.TreatmentDoctor)
		if treatmentDoctor != nil {
			employeeId = treatmentDoctor.Id
		}

		var createdById uuid.UUID
		createdBy := dataContext.SearchDoctorByLanr(bdtScheinRaw.Lanr)
		if createdBy != nil {
			createdById = createdBy.Id
		}
		var contractId *string
		bsnrId := createdBy.GetBsnrIdByCode(bdtScheinRaw.BsnrCode)
		var G4101Year int64
		var G4101Quarter int64
		var err error
		if bdtScheinRaw.YearQuarter != "" {
			G4101Quarter, err = strconv.ParseInt(bdtScheinRaw.YearQuarter[:1], 10, 32)
			if err != nil {
				ctx.Logger().Error("Failed to ParseInt", "err", err.Error())
			}
			G4101Year, err = strconv.ParseInt(bdtScheinRaw.YearQuarter[1:], 10, 32)
			if err != nil {
				ctx.Logger().Error("Failed to ParseInt", "err", err.Error())
			}
		}

		var G4102 int64
		if bdtScheinRaw.G4102 != "" {
			G4102, err = util.ConvertStringToMillisecond(bdtScheinRaw.G4102, dateFormat)
			if err != nil {
				ctx.Logger().Error("Failed to ConvertStringToMillisecond", "err", err.Error())
			}
		}

		var G4104 int64
		if bdtScheinRaw.G4104 != "" {
			G4104, err = strconv.ParseInt(bdtScheinRaw.G4104, 10, 32)
			if err != nil {
				ctx.Logger().Error("Failed to ParseInt", "err", err.Error())
			}
		}

		var Ad4125From int64
		var Ad4125To int64

		if bdtScheinRaw.Ad4125FromTo != "" {
			Ad4125FromStr, Ad4125ToStr := bdtScheinRaw.Ad4125FromTo[:8], bdtScheinRaw.Ad4125FromTo[8:]
			Ad4125From, err = util.ConvertStringToMillisecond(Ad4125FromStr, dateFormat)
			if err != nil {
				ctx.Logger().Error("Failed to ConvertStringToMillisecond", "err", err.Error())
			}
			Ad4125To, err = util.ConvertStringToMillisecond(Ad4125ToStr, dateFormat)
			if err != nil {
				ctx.Logger().Error("Failed to ConvertStringToMillisecond", "err", err.Error())
			}
		}
		if bdtScheinRaw.Ad4125From != "" {
			if v, err := util.ConvertStringToMillisecond(bdtScheinRaw.Ad4125From, dateFormat); err == nil {
				Ad4125From = v
			} else {
				ctx.Logger().Error("failed to parse string to timestamp", "err", err.Error())
			}
		}

		if bdtScheinRaw.Ad4125To != "" {
			if v, err := util.ConvertStringToMillisecond(bdtScheinRaw.Ad4125To, dateFormat); err == nil {
				Ad4125To = v
			} else {
				ctx.Logger().Error("failed to parse string to timestamp", "err", err.Error())
			}
		}

		Ad4202 := bdtScheinRaw.Ad4202 == "1"
		Ad4204 := bdtScheinRaw.Ad4204 == "1"
		var Ad4206 int64
		if bdtScheinRaw.Ad4206 != "" {
			Ad4206, err = util.ConvertStringToMillisecond(bdtScheinRaw.Ad4206, dateFormat)
			if err != nil {
				ctx.Logger().Error("Failed to ConvertStringToMillisecond", "err", err.Error())
			}
		}

		Re4207 := strings.Join(bdtScheinRaw.Re4207, "")
		Re4208 := strings.Join(bdtScheinRaw.Re4208, "")
		Re4209 := strings.Join(bdtScheinRaw.Re4209, "")
		// only get Re4241 when their parent has value
		Re4241 := function.If(bdtScheinRaw.Re4217.Re4217 != "", bdtScheinRaw.Re4217.Re4241, "")
		Re4241 = function.If(bdtScheinRaw.Re4225.Re4225 != "", bdtScheinRaw.Re4225.Re4241, Re4241)
		Re4242 := function.If(bdtScheinRaw.Re4218.Re4242 != "", bdtScheinRaw.Re4218.Re4242, "")
		Re4242 = function.If(bdtScheinRaw.Re4226.Re4242 != "", bdtScheinRaw.Re4226.Re4242, Re4242)
		Ps4234 := bdtScheinRaw.Ps4234.Ps4234 == "1"
		Ps4236 := bdtScheinRaw.Ps4236 == "1"

		var Re4233 []schein_common.DateTimeFromTo
		for _, v := range bdtScheinRaw.Re4233 {
			var from, to int64
			fromStr, toStr := v[:8], v[8:]
			from, err = util.ConvertStringToMillisecond(fromStr, dateFormat)
			if err != nil {
				ctx.Logger().Error("Failed to ConvertStringToMillisecond", "err", err.Error())
			}
			to, err = util.ConvertStringToMillisecond(toStr, dateFormat)
			if err != nil {
				ctx.Logger().Error("Failed to ConvertStringToMillisecond", "err", err.Error())
			}
			Re4233 = append(Re4233, schein_common.DateTimeFromTo{
				From: from,
				To:   to,
			})
		}

		var TsvgContactDate int64
		if bdtScheinRaw.TsvgContact.TsvgContactDate != "" {
			TsvgContactDate, err = util.ConvertStringToMillisecond(bdtScheinRaw.TsvgContact.TsvgContactDate, dateFormat)
			if err != nil {
				ctx.Logger().Error("Failed to ConvertStringToMillisecond TsvgContactDate", "err", err.Error())
			}
		}

		PsychotherapyArr := []schein_common.Psychotherapy{}
		for _, Ps4235Obj := range bdtScheinRaw.Ps4234.Ps4235 {
			Ps4235, _ := util.ConvertStringToMillisecond(Ps4235Obj.Ps4235, dateFormat)
			Ps4299, _ := strconv.ParseInt(Ps4235Obj.Ps4299, 10, 32)
			Ps4247, _ := util.ConvertStringToMillisecond(Ps4235Obj.Ps4247, dateFormat)
			Ps4250 := Ps4235Obj.Ps4250.Ps4250 == "1"
			Ps4251, _ := strconv.ParseInt(Ps4235Obj.Ps4250.Ps4251, 10, 32)
			Ps4252, _ := strconv.ParseInt(Ps4235Obj.Ps4252, 10, 32)
			Ps4255, _ := strconv.ParseInt(Ps4235Obj.Ps4255, 10, 32)
			groupServicesCodes := []schein_common.GroupServicesCode{}

			for _, groupServicesCode := range Ps4235Obj.GroupServiceCodes {
				amountBilled, err := strconv.ParseInt(groupServicesCode.AmountBilled, 10, 32)
				if err != nil {
					return errors.WithStack(err)
				}

				groupServicesCodes = append(groupServicesCodes, schein_common.GroupServicesCode{
					ServiceCode:  groupServicesCode.GroupServiceCode,
					AmountBilled: int32(amountBilled),
				})
			}
			groupCareGivers := []schein_common.GroupServicesCode{}

			for _, careGiverServiceCode := range Ps4235Obj.CareGiverServiceCodes {
				amountBilled, err := strconv.ParseInt(careGiverServiceCode.AmountBilled, 10, 32)
				if err != nil {
					return errors.WithStack(err)
				}

				groupCareGivers = append(groupCareGivers, schein_common.GroupServicesCode{
					ServiceCode:  careGiverServiceCode.CareGiverServiceCode,
					AmountBilled: int32(amountBilled),
				})
			}
			groupServicesCodeBefore2017s := []schein_common.GroupServicesCodeBefore2017{}

			for _, obj := range Ps4235Obj.GroupServicesCodeBefore2017s {
				amountBilled, err := strconv.ParseInt(obj.AmountBilled, 10, 32)
				if err != nil {
					ctx.Logger().Error("Failed to ParseInt", "err", err.Error())
				}
				amountApproval, err := strconv.ParseInt(obj.AmountApproval, 10, 32)
				if err != nil {
					ctx.Logger().Error("Failed to ParseInt", "err", err.Error())
				}

				groupServicesCodeBefore2017s = append(groupServicesCodeBefore2017s, schein_common.GroupServicesCodeBefore2017{
					ServiceCode:    obj.ServiceCode,
					AmountBilled:   int32(amountBilled),
					AmountApproval: int32(amountApproval),
				})
			}

			Psychotherapy := schein_common.Psychotherapy{
				Id:                          util.NewUUID(),
				Ps4235:                      function.If(Ps4235Obj.Ps4235 != "", util.NewPointer(Ps4235), nil),
				Ps4299:                      function.If(Ps4235Obj.Ps4299 != "", util.NewPointer(int32(Ps4299)), nil),
				Ps4247:                      function.If(Ps4235Obj.Ps4247 != "", util.NewPointer(Ps4247), nil),
				Ps4250:                      function.If(Ps4235Obj.Ps4250.Ps4250 != "", &Ps4250, nil),
				Ps4251:                      function.If(Ps4235Obj.Ps4250.Ps4251 != "", util.NewPointer(int32(Ps4251)), nil),
				Ps4252:                      function.If(Ps4235Obj.Ps4252 != "", util.NewPointer(int32(Ps4252)), nil),
				GroupServicesCode:           groupServicesCodes,
				Ps4255:                      function.If(Ps4235Obj.Ps4255 != "", util.NewPointer(int32(Ps4255)), nil),
				GroupCareGiver:              groupCareGivers,
				GroupServicesCodeBefore2017: groupServicesCodeBefore2017s,
			}
			PsychotherapyArr = append(PsychotherapyArr, Psychotherapy)
		}

		var insuranceId uuid.UUID
		var ikNumber *int32
		var insuranceNumber *string
		if insurance := bdtScheinRaw.GetInsuranceInfo(ctx, *dataContext); insurance != nil {
			if existingInsurance, index := patientInfo.GetInsuranceIndexByStatusIkNumberSpecialGroup(
				insurance.IkNumber,
				insurance.SpecialGroup,
				insurance.InsuranceStatus,
			); existingInsurance != nil {
				insuranceId = existingInsurance.Id
				ikNumber = &existingInsurance.IkNumber
				insuranceNumber = existingInsurance.InsuranceNumber
				if existingInsurance.EndDate == nil {
					existingInsurance.EndDate = insurance.EndDate
					patientInfo.InsuranceInfos[index] = *existingInsurance
				} else if insurance.EndDate != nil && *existingInsurance.EndDate < *insurance.EndDate {
					insurance.ReadCardDatas = existingInsurance.ReadCardDatas
					insurance.Id = existingInsurance.Id
					existingInsurance = insurance
					patientInfo.InsuranceInfos[index] = *existingInsurance
				}

				// check if read card is already exist in existing insurance if not then append new one
				existingReadCard := slice.FindOne(existingInsurance.ReadCardDatas, func(readCard patient_profile_common.ReadCardModel) bool {
					return len(insurance.ReadCardDatas) > 0 &&
						readCard.ReadCardDate == insurance.ReadCardDatas[0].ReadCardDate &&
						readCard.CdmVersion == insurance.ReadCardDatas[0].CdmVersion &&
						readCard.RegistrationNumber == insurance.ReadCardDatas[0].RegistrationNumber
				})
				_, index := slice.FindOneIndex(patientInfo.InsuranceInfos, func(i patient_profile_common.InsuranceInfo) bool {
					return i.Id == existingInsurance.Id
				})
				if index != -1 && existingReadCard == nil {
					patientInfo.InsuranceInfos[index].ReadCardDatas = append(patientInfo.InsuranceInfos[index].ReadCardDatas, insurance.ReadCardDatas...)
				}
			} else {
				// at least one insurance is active
				if len(patientInfo.InsuranceInfos) == 0 {
					insurance.IsActive = true
				}
				patientInfo.InsuranceInfos = append(patientInfo.InsuranceInfos, *insurance)
				insuranceId = insurance.Id
				insuranceNumber = insurance.InsuranceNumber
				ikNumber = &insurance.IkNumber
				patientInfo.GenericInfo.PatientType = patient_profile_common.PatientType_Public
			}
		}

		if createdBy != nil && bdtScheinRaw.ContractId != "" {
			dc := srv.contractService.GetContractDetailById(bdtScheinRaw.ContractId)
			if dc == nil {
				ctx.Logger().Info("GetContractDetailById return nil", "contract", bdtScheinRaw.ContractId)
			}
			contract := employeebff.Contract{
				ContractId:            bdtScheinRaw.ContractId,
				ChargeSystemId:        bdtScheinRaw.ContractId,
				Type:                  common.ContractType_HouseDoctorCare,
				EnrollmentTypeOptions: dc.GetEnrollmentTypeOptions(),
			}
			createdBy.UpsertContract(&contract)
			dataContext.UpsertEmployee(*createdBy)
			contractId = &bdtScheinRaw.ContractId
			patientParticipations = append(patientParticipations, patient_participation.PatientParticipation{
				Id:                    util.NewUUID(),
				DoctorId:              &employeeId,
				PatientId:             *patientId,
				ContractId:            bdtScheinRaw.ContractId,
				IkNumber:              util.GetPointerValue(ikNumber),
				HashedInsuranceNumber: util.GetPointerValue(insuranceNumber),
				Status:                patient_participation.PatientParticipationStatus_Active,
				CreatedDate:           util.NowUnixMillis(ctx),
				CreatedBy:             createdById,
				DoctorFunctionType:    patient_participation.DoctorFunctionType_Custodian,
				ContractType:          model.ContractType_HouseDoctorCare,
				ChargeSystemId:        bdtScheinRaw.ContractId,
			})
		}

		scheinRepo := scheinRepo.ScheinRepo{
			Id:             util.NewUUID(),
			PatientId:      *patientId,
			ContractId:     contractId,
			DoctorId:       employeeId,
			ChargeSystemId: contractId,
			IsBilled:       true,
			Schein: schein_common.Schein{
				G4101Quarter:     function.If(G4101Quarter != 0, util.NewPointer(int32(G4101Quarter)), nil),
				G4101Year:        function.If(G4101Year != 0, util.NewPointer(int32(G4101Year)), nil),
				KvScheinSubGroup: &bdtScheinRaw.KvScheinSubGroup,
				ScheinMainGroup:  bdt.GetScheinMainGroup(bdtScheinRaw.Type),
				KvTreatmentCase:  getKvTreatmentCase(bdtScheinRaw.Type),
				InsuranceId:      insuranceId,
				PatientSnapshot: &schein_common.PatientSnapshot{
					PatientId:   *patientId,
					PatientInfo: *patientInfo,
					UpdatedAt:   util.NowUnixMillis(ctx),
				},
				IkNumber: ikNumber,
			},
			ScheinDetail: schein_common.ScheinDetail{
				G4102:           function.If(G4102 != 0, util.NewPointer(G4102), nil),
				G4104:           function.If(G4104 != 0, util.NewPointer(int32(G4104)), nil),
				G4106:           function.If(bdtScheinRaw.G4106 != "", &bdtScheinRaw.G4106, util.NewPointer("02")),
				G4122:           function.If(bdtScheinRaw.G4122 != "", &bdtScheinRaw.G4122, util.NewPointer("02")),
				Ad4123:          &bdtScheinRaw.Ad4123,
				Ad4124:          &bdtScheinRaw.Ad4124,
				Ad4125From:      function.If(Ad4125From != 0, &Ad4125From, nil),
				Ad4125To:        function.If(Ad4125To != 0, &Ad4125To, nil),
				Ad4126:          function.If(bdtScheinRaw.Ad4126 != "", &bdtScheinRaw.Ad4126, nil),
				Ad4202:          function.If(bdtScheinRaw.Ad4202 != "", &Ad4202, nil),
				Ad4204:          function.If(bdtScheinRaw.Ad4204 != "", &Ad4204, nil),
				Re4205:          function.If(bdtScheinRaw.Re4205 != "", &bdtScheinRaw.Re4205, nil),
				Ad4206:          function.If(bdtScheinRaw.Ad4206 != "", &Ad4206, nil),
				Re4207:          function.If(Re4207 != "", &Re4207, nil),
				Re4208:          function.If(Re4208 != "", &Re4208, nil),
				Re4209:          function.If(Re4209 != "", &Re4209, nil),
				Re4217:          function.If(bdtScheinRaw.Re4217.Re4217 != "", &bdtScheinRaw.Re4217.Re4217, nil),
				Re4225:          function.If(bdtScheinRaw.Re4225.Re4225 != "", &bdtScheinRaw.Re4225.Re4225, nil),
				Re4241:          function.If(Re4241 != "", &Re4241, nil),
				Re4218:          function.If(bdtScheinRaw.Re4218.Re4218 != "", &bdtScheinRaw.Re4218.Re4218, nil),
				Re4226:          function.If(bdtScheinRaw.Re4226.Re4226 != "", &bdtScheinRaw.Re4226.Re4226, nil),
				Re4242:          function.If(Re4242 != "", &Re4242, nil),
				Re4249:          function.If(bdtScheinRaw.Re4226.Re4249 != "", &bdtScheinRaw.Re4226.Re4249, nil),
				Re4219:          function.If(bdtScheinRaw.Re4219 != "", &bdtScheinRaw.Re4219, nil),
				Re4220:          function.If(bdtScheinRaw.Re4220 != "", &bdtScheinRaw.Re4220, nil),
				Re4221:          function.If(bdtScheinRaw.Re4221 != "", &bdtScheinRaw.Re4221, nil),
				Re4229:          function.If(bdtScheinRaw.Re4229 != "", &bdtScheinRaw.Re4229, nil),
				Re4233:          function.If(bdtScheinRaw.Re4233 != nil, Re4233, nil),
				Ps4234:          &Ps4234,
				Psychotherapy:   PsychotherapyArr,
				Ps4236:          &Ps4236,
				TsvgContactType: function.If(bdtScheinRaw.TsvgContact.TsvgContactType != "", &bdtScheinRaw.TsvgContact.TsvgContactType, nil),
				TsvgTranferCode: function.If(bdtScheinRaw.TsvgContact.TsvgTranferCode != "", &bdtScheinRaw.TsvgContact.TsvgTranferCode, nil),
				TsvgContactDate: function.If(bdtScheinRaw.TsvgContact.TsvgContactDate != "", &TsvgContactDate, nil),
				TsvgInfor:       function.If(bdtScheinRaw.TsvgContact.TsvgInfor != "", &bdtScheinRaw.TsvgContact.TsvgInfor, nil),
				Re4243:          function.If(bdtScheinRaw.Re4243 != "", &bdtScheinRaw.Re4243, nil),
			},
			CreatedAt:        util.NowUnixMillis(ctx),
			CreatedBy:        util.GetPointerValue(ctx.UserInfo().UserUUID()),
			AssignedToBsnrId: bsnrId,
		}

		tmap[bdtScheinRaw.ScheinNumber] = scheinRepo.Id
		kvScheins = append(kvScheins, scheinRepo)
		for _, Re5000 := range bdtScheinRaw.Di5000s {
			selectedDate, err := parseSelectedDate(ctx, Re5000.SelectedDate, dateFormat)
			if err != nil {
				ctx.Logger().Error("Failed to parse selectedDate", "err", err.Error())
			}
			for _, Di5001Obj := range Re5000.Di5001 {
				additionalInfos := []*patient_encounter.AdditionalInfoParent{}

				Di5002 := strings.Join(Di5001Obj.Di5002, "")
				Di5003 := strings.Join(Di5001Obj.Di5003, "")
				Di5009 := strings.Join(Di5001Obj.Di5009, "")
				Di5015 := strings.Join(Di5001Obj.Di5015, "")
				Di5016 := strings.Join(Di5001Obj.Di5016, "")
				Di5017 := strings.Join(Di5001Obj.Di5017, "")
				Di5019 := strings.Join(Di5001Obj.Di5019, "")
				Di5036 := strings.Join(Di5001Obj.Di5036, "")
				Di5038 := strings.Join(Di5001Obj.Di5038, "")

				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5002", Di5002)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5003", Di5003)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5005", Di5001Obj.Di5005)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5006", Di5001Obj.Di5006)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5008", Di5001Obj.Di5008)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5009", Di5009)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5010", Di5001Obj.Di5010)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5013", Di5001Obj.Di5013)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5015", Di5015)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5016", Di5016)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5017", Di5017)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5018", Di5001Obj.Di5018)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5019", Di5019)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5023", Di5001Obj.Di5023)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5024", Di5001Obj.Di5024)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5025", Di5001Obj.Di5025)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5026", Di5001Obj.Di5026)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5034", Di5001Obj.Di5034)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5036", Di5036)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5037", Di5001Obj.Di5037)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5038", Di5038)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5040", Di5001Obj.Di5040)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5098", Di5001Obj.Di5098)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5099", Di5001Obj.Di5099)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5101", Di5001Obj.Di5101)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5100", Di5001Obj.Di5100)
				if len(Di5001Obj.Di5012) != 0 {
					newAdditionalInfoChildren := []*patient_encounter.AdditionalInfoChild{}
					for _, Di5012Obj := range Di5001Obj.Di5012 {
						Di5011Value := strings.Join(Di5012Obj.Di5011, "")
						newAdditionalInfoChildren = append(newAdditionalInfoChildren, &patient_encounter.AdditionalInfoChild{
							FK:    "5011",
							Value: Di5011Value,
						}, &patient_encounter.AdditionalInfoChild{
							FK:    "5074",
							Value: Di5012Obj.Di5074,
						}, &patient_encounter.AdditionalInfoChild{
							FK:    "5075",
							Value: Di5012Obj.Di5075,
						})

						newAdditionalInfo := patient_encounter.AdditionalInfoParent{
							FK:       "5012",
							Value:    Di5012Obj.Di5012,
							Children: newAdditionalInfoChildren,
						}
						additionalInfos = append(additionalInfos, &newAdditionalInfo)
					}
				}
				if Di5001Obj.Di5020.Di5020 != "" {
					newAdditionalInfoChildren := []*patient_encounter.AdditionalInfoChild{
						{
							FK:    "5021",
							Value: Di5001Obj.Di5020.Di5021,
						},
					}
					newAdditionalInfo := patient_encounter.AdditionalInfoParent{
						FK:       "5020",
						Value:    Di5001Obj.Di5020.Di5020,
						Children: newAdditionalInfoChildren,
					}
					additionalInfos = append(additionalInfos, &newAdditionalInfo)
				}
				if len(Di5001Obj.Di5035) != 0 {
					newAdditionalInfoChildren := []*patient_encounter.AdditionalInfoChild{}
					for _, Di5035Obj := range Di5001Obj.Di5035 {
						newAdditionalInfoChildren = append(newAdditionalInfoChildren, &patient_encounter.AdditionalInfoChild{
							FK:    "5041",
							Value: Di5035Obj.Di5041,
						})
						newAdditionalInfo := patient_encounter.AdditionalInfoParent{
							FK:       "5035",
							Value:    Di5035Obj.Di5035,
							Children: newAdditionalInfoChildren,
						}
						additionalInfos = append(additionalInfos, &newAdditionalInfo)
					}
				}
				if Di5001Obj.Di5042.Di5042 != "" {
					newAdditionalInfoChildren := []*patient_encounter.AdditionalInfoChild{
						{
							FK:    "5043",
							Value: Di5001Obj.Di5042.Di5043,
						},
					}
					newAdditionalInfo := patient_encounter.AdditionalInfoParent{
						FK:       "5042",
						Value:    Di5001Obj.Di5042.Di5042,
						Children: newAdditionalInfoChildren,
					}
					additionalInfos = append(additionalInfos, &newAdditionalInfo)
				}
				if len(Di5001Obj.Di5070) != 0 {
					newAdditionalInfoChildren := []*patient_encounter.AdditionalInfoChild{}
					for _, Di5070Obj := range Di5001Obj.Di5070 {
						Di5072Value := strings.Join(Di5070Obj.Di5072, "")
						newAdditionalInfoChildren = append(newAdditionalInfoChildren, &patient_encounter.AdditionalInfoChild{
							FK:    "5072",
							Value: Di5072Value,
						})
						newAdditionalInfo := patient_encounter.AdditionalInfoParent{
							FK:       "5070",
							Value:    Di5070Obj.Di5070,
							Children: newAdditionalInfoChildren,
						}
						additionalInfos = append(additionalInfos, &newAdditionalInfo)
					}
				}
				if len(Di5001Obj.Di5071) != 0 {
					newAdditionalInfoChildren := []*patient_encounter.AdditionalInfoChild{}
					for _, Di5071Obj := range Di5001Obj.Di5071 {
						Di5073Value := strings.Join(Di5071Obj.Di5073, "")
						newAdditionalInfoChildren = append(newAdditionalInfoChildren, &patient_encounter.AdditionalInfoChild{
							FK:    "5073",
							Value: Di5073Value,
						})
						newAdditionalInfo := patient_encounter.AdditionalInfoParent{
							FK:       "5071",
							Value:    Di5071Obj.Di5071,
							Children: newAdditionalInfoChildren,
						}
						additionalInfos = append(additionalInfos, &newAdditionalInfo)
					}
				}

				timeLineServices = append(timeLineServices, timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
					Id:                util.NewUUID(),
					SelectedDate:      selectedDate,
					IsImported:        true,
					PatientId:         *patientId,
					BillingDoctorId:   &employeeId,
					TreatmentDoctorId: employeeId,
					ScheinIds:         []uuid.UUID{util.GetPointerValue(scheinRepo.Id)},
					Type:              timeline_common.TimelineEntityType_Service,
					EncounterCase:     util.NewPointer(patient_encounter.AB),
					TreatmentCase:     util.NewPointer(patient_encounter.TreatmentCaseCustodian),
					Payload: patient_encounter.EncounterServiceTimeline{
						Code:            Di5001Obj.Di5001,
						FreeText:        fmt.Sprintf("(%s)", Di5001Obj.Di5001),
						AdditionalInfos: &additionalInfos,
						Scheins: &[]*common.ScheinWithMainGroup{
							{ScheinId: scheinRepo.Id, Group: common.MainGroup(scheinRepo.Schein.ScheinMainGroup)},
						},
						ServiceMainGroup: (*common.MainGroup)(&scheinRepo.Schein.ScheinMainGroup),
						Command:          "L",
					},
					CreatedBy:        createdById,
					AssignedToBsnrId: bsnrId,
					RecentAuditLogs: []timeline_repo.AuditLog{
						{
							AuditLogId: util.NewUUID(),
							UserId:     &createdById,
							Date:       selectedDate,
							ActionType: timeline_common.Add,
						},
					},
				})
			}
		}
		for _, Di6200Obj := range bdtScheinRaw.Di6200s {
			selectedDate, err := parseSelectedDate(ctx, Di6200Obj.SelectedDate, dateFormat)
			if err != nil {
				ctx.Logger().Error("Failed to parse selectedDate", "err", err.Error())
			}

			if Di6200Obj.Di6001.Di6001 != "" {
				timelineDiagnosis = append(timelineDiagnosis, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
					Id:                util.NewUUID(),
					SelectedDate:      selectedDate,
					IsImported:        true,
					Type:              timeline_common.TimelineEntityType_Diagnose,
					PatientId:         *patientId,
					BillingDoctorId:   &employeeId,
					TreatmentDoctorId: employeeId,
					ScheinIds:         []uuid.UUID{util.GetPointerValue(scheinRepo.Id)},
					Payload: patient_encounter.EncounterDiagnoseTimeline{
						Code:        Di6200Obj.Di6001.Di6001,
						Description: Di6200Obj.Di6000,
						FreeText:    fmt.Sprintf("(%s) %s", Di6200Obj.Di6001.Di6001, Di6200Obj.Di6000),
						Certainty:   (*patient_encounter.Certainty)(&Di6200Obj.Di6001.Di6003),
						Laterality:  (*patient_encounter.Laterality)(&Di6200Obj.Di6001.Di6004),
						Explanation: strings.Join(Di6200Obj.Di6001.Di6006, "\n"),
						Exception:   strings.Join(Di6200Obj.Di6001.Di6008, "\n"),
						Command:     "D",
						Type:        patient_encounter.DIAGNOSETYPE_ACUTE,
					},
					CreatedBy:        createdById,
					AssignedToBsnrId: bsnrId,
					RecentAuditLogs: []timeline_repo.AuditLog{
						{
							AuditLogId: util.NewUUID(),
							UserId:     &createdById,
							Date:       selectedDate,
							ActionType: timeline_common.Add,
						},
					},
				})
			}
			if Di6200Obj.Di3673.Di3673 != "" {
				timelineDiagnosis = append(timelineDiagnosis, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
					Id:                util.NewUUID(),
					SelectedDate:      selectedDate,
					IsImported:        true,
					Type:              timeline_common.TimelineEntityType_Diagnose,
					PatientId:         *patientId,
					BillingDoctorId:   &employeeId,
					TreatmentDoctorId: employeeId,
					ScheinIds:         []uuid.UUID{util.GetPointerValue(scheinRepo.Id)},
					Payload: patient_encounter.EncounterDiagnoseTimeline{
						Code:        Di6200Obj.Di3673.Di3673,
						Description: Di6200Obj.Di3672,
						FreeText:    fmt.Sprintf("(%s) %s", Di6200Obj.Di3673.Di3673, Di6200Obj.Di3672),
						Certainty:   (*patient_encounter.Certainty)(&Di6200Obj.Di3673.Di3674),
						Laterality:  (*patient_encounter.Laterality)(&Di6200Obj.Di3673.Di3675),
						Explanation: strings.Join(Di6200Obj.Di3673.Di3676, "\n"),
						Exception:   strings.Join(Di6200Obj.Di3673.Di3677, "\n"),
						Command:     function.If(Di6200Obj.Di3678 == "2", "AD", "DD"),
						Type:        patient_encounter.DIAGNOSETYPE_PERMANENT,
					},
					CreatedBy:        createdById,
					AssignedToBsnrId: bsnrId,
					RecentAuditLogs: []timeline_repo.AuditLog{
						{
							AuditLogId: util.NewUUID(),
							UserId:     &createdById,
							Date:       selectedDate,
							ActionType: timeline_common.Add,
						},
					},
				})
			}
		}
	}

	var errorGroup function.ErrorGroupRecover

	errorGroup.Go(
		func() error {
			_, err := srv.patientProfileRepo.UpdatePatientProfileV2(ctx, *patientId, *patientInfo)
			return errors.WithMessage(err, "Error updating patient profile")
		},
		func() error {
			_, err := srv.scheinRepo.CreateMany(ctx, kvScheins)
			if err != nil {
				return errors.WithMessage(err, "Error creating new schein to repo")
			}
			for scheinNumber, scheinId := range tmap {
				dataContext.ScheinNumberMap[patientNumber] = append(dataContext.ScheinNumberMap[patientNumber], scheinNumber)
				dataContext.ScheinIdMap[scheinNumber] = scheinId
			}
			return errors.WithMessage(err, "Error creating new schein to repo")
		},

		func() error {
			_, err := srv.timelineDiagnoseRepo.CreateMany(ctx, timelineDiagnosis)
			return errors.WithMessage(err, "Error creating timeline diagnosis")
		},

		func() error {
			_, err := srv.timelineServiceRepo.CreateMany(ctx, timeLineServices)
			return errors.WithMessage(err, "Error creating timeline service")
		},

		func() error {
			if len(patientParticipations) == 0 {
				return nil
			}
			patientParticipations = slice.UniqBy(patientParticipations, func(p patient_participation.PatientParticipation) string {
				var doctorIdStr string
				if p.DoctorId != nil {
					doctorIdStr = p.DoctorId.String()
				}
				return p.PatientId.String() + doctorIdStr + p.ContractId + strconv.Itoa(int(p.IkNumber))
			})
			_, err := srv.patientParticipationRepo.CreateMany(ctx, patientParticipations)
			return err
		},
	)
	if err := errorGroup.Wait(); err != nil {
		return errors.WithMessage(err, "error in group")
	}

	return nil
}

func (srv *BDTService) importAddressMasterData(ctx *titan.Context, addressMasterData *bdt_model.AddressMasterData, dataContext *bdt_model.DataContext) error {
	if addressMasterData == nil {
		return errors.New("addressMasterData is nil")
	}
	now := time.Now()
	defer func() {
		ctx.Logger().Info("importAddressMasterData",
			"duration", time.Since(now).String())
	}()

	bsnrIds := dataContext.BsnrIds
	if len(bsnrIds) == 0 {
		return nil
	}

	for _, bsnrId := range bsnrIds {
		for _, masterData := range addressMasterData.MasterDatas {
			// TODO: In bdt they have many value for those field
			var phoneNumber, mobilePhoneNumber, email, fax *string
			if len(masterData.PhoneNumber) > 1 {
				phoneNumber = &masterData.PhoneNumber[0]
				mobilePhoneNumber = &masterData.PhoneNumber[1]
			} else if len(masterData.PhoneNumber) == 1 {
				phoneNumber = &masterData.PhoneNumber[0]
			}
			if len(masterData.Email) > 0 {
				email = &masterData.Email[0]
			}
			if len(masterData.Fax) > 0 {
				fax = &masterData.Fax[0]
			}

			doctorSalutation := patient_profile_common.Salutation_Keine
			if strings.Contains(masterData.Salutation, string(patient_profile_common.Salutation_Frau)) {
				doctorSalutation = patient_profile_common.Salutation_Frau
			} else if strings.Contains(masterData.Salutation, string(patient_profile_common.Salutation_Herr)) {
				doctorSalutation = patient_profile_common.Salutation_Herr
			}

			request := &catalog_sdav.CreateSdavRequest{
				SdavCatalog: &catalog_sdav_common.SdavCatalog{
					Source: catalog_utils_common.SelfCreated,
					GeneralInfo: catalog_sdav_common.GeneralInfo{
						Bsnr:        masterData.BsnrCode,
						AddressType: util.NewPointer(masterData.GetAddressType()),
						AddressName: &masterData.Name,
						City:        &masterData.City,
						// Number:      &masterData.Number,
						PostCode: &masterData.PostalCode,
						Street:   &masterData.Street,
					},
					DoctorInfo: catalog_sdav_common.DoctorInfo{
						Lanr: masterData.LanrCode,
						// TeamNumbers: ,
						AdditionalName: &masterData.AdditionalName,
						FirstName:      function.If(masterData.FirstName != "", &masterData.FirstName, nil),
						LastName:       function.If(masterData.FirstName != "", &masterData.LastName, nil),
						// IntendWord:     &masterData.IntendWord,
						Salutation: util.NewString(string(doctorSalutation)),
						Title:      &masterData.Title,
					},
					ContactInfo: catalog_sdav_common.ContactInfo{
						PhoneNumber:       phoneNumber,
						MobilePhoneNumber: mobilePhoneNumber,
						ContactPerson:     &masterData.ContactPerson,
						Email:             email,
						Fax:               fax,
						Salutation:        util.NewString(string(patient_profile_common.Salutation_Keine)), // default value
					},
					BsnrId: bsnrId,
				},
			}
			_, err := srv.catalogSdavService.CreateSdav(ctx, request)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func (srv *BDTService) importPatient(ctx *titan.Context, patientInfo *patient_profile_common.PatientInfo) (*patient_profile_api.CreatePatientProfileV2Response, error) {
	createdPatientRes, err := srv.patientProfileBff.CreatePatientProfileV2(ctx, &patient_profile_api.CreatePatientProfileV2Request{
		PatientInfo: patientInfo,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if createdPatientRes == nil || createdPatientRes.Id == nil {
		return nil, errors.New("failed to create patient")
	}

	return createdPatientRes, nil
}

func (srv *BDTService) handleImportSchein(ctx *titan.Context, req *handleImportScheinRequest) error {
	if req.PatientInfo == nil {
		return nil
	}
	now := time.Now()
	defer func() {
		ctx.Logger().Info("importPatient",
			"patientBdtNumber", req.BdtNumber,
			"duration", time.Since(now).String())
	}()

	patientId := req.PatientId
	patientInfo := req.PatientInfo
	bdtPatientNumber := req.BdtNumber
	scheins, ok := req.DataContext.ScheinMap[bdtPatientNumber]
	if ok || len(scheins) != 0 {
		ctxCloned := ctx.Clone()
		if err := srv.importSchein(ctxCloned, &ImportScheinRequest{
			BdtSchein:   scheins,
			DataContext: req.DataContext,
			PatientId:   patientId,
			PatientInfo: patientInfo,
		}); err != nil {
			ctxCloned.Logger().Error("failed to import schein", "err", err.Error())
		}
		delete(req.DataContext.ScheinMap, bdtPatientNumber)
	}

	privateScheins, ok := req.DataContext.PrivateScheinMap[bdtPatientNumber]
	if ok || len(privateScheins) != 0 {
		ctxCloned := ctx.Clone()
		if err := srv.importPrivateSchein(ctxCloned, &ImportPrivateScheinRequest{
			PrivateSchein: privateScheins,
			DataContext:   req.DataContext,
			PatientInfo:   patientInfo,
			PatientId:     patientId,
		}); err != nil {
			ctxCloned.Logger().Error("failed to import private schein", "err", err.Error())
		}
		delete(req.DataContext.PrivateScheinMap, bdtPatientNumber)
	}
	bgScheins, ok := req.DataContext.BGScheinMap[bdtPatientNumber]
	if ok || len(bgScheins) != 0 {
		ctxCloned := ctx.Clone()
		if err := srv.importBGSchein(ctxCloned, ImportBGScheinRequest{
			BGSchein:    bgScheins,
			DataContext: req.DataContext,
			PatientInfo: patientInfo,
			PatientId:   patientId,
		}); err != nil {
			ctxCloned.Logger().Error("failed to import bg schein", "err", err.Error())
		}
		delete(req.DataContext.BGScheinMap, bdtPatientNumber)
	}

	return nil
}

func (srv *BDTService) updatePatientMedicalData(ctx *titan.Context, req *UpdatePatientMedicalDataRequest) error {
	treatment := req.treatment
	if treatment == nil || treatment.Height == "" && treatment.Weight == "" {
		return nil
	}

	patientMedicalData := req.patientProfile.PatientMedicalData
	if patientMedicalData == nil {
		patientMedicalData = &patient_profile_common.PatientMedicalData{}
	}

	// update medical data of patient
	heightFloat, heightErr := strconv.ParseFloat(treatment.Height, 64)
	weightFloat, weightErr := strconv.ParseFloat(treatment.Weight, 64)
	height := function.If(heightErr == nil, util.NewPointer(heightFloat), nil)
	weight := function.If(weightErr == nil, util.NewPointer(weightFloat), nil)
	if height != nil {
		patientMedicalData.Height = height
	}

	if weight != nil {
		patientMedicalData.Weight = weight
	}

	if _, err := srv.patientProfileRepo.UpdatePatientMedicalData(ctx, patient_profile_api.UpdatePatientMedicalHistoryDataRequest{
		PatientId:          req.patientProfile.Id,
		PatientMedicalData: patientMedicalData,
	}); err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (srv *BDTService) importTreatmentData(ctx *titan.Context, req *ImportTreatmentRequest) error {
	if req.Treatment == nil {
		return nil
	}
	now := time.Now()
	timelineDiagnosis := []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{}
	timelineNotes := []timeline_repo.TimelineEntity[patient_encounter.EncounterNoteTimeline]{}
	prescribedMedications := []prescribed.PrescribedMedication{}
	medicinePrescriptions := []prescription.MedicinePrescription{}
	timelineMedicinePrescriptions := []timeline_repo.TimelineEntity[patient_encounter.EncounterMedicinePrescription]{}
	timelineMedicines := []timeline_repo.TimelineEntity[medicine_common.Medicine]{}
	timelineForms := []timeline_repo.TimelineEntity[patient_encounter.EncounterForm]{}
	timelineDMs := []timeline_repo.TimelineEntity[patient_encounter.EncounterDocumentManagement]{}
	timelineCustomizes := []timeline_repo.TimelineEntity[patient_encounter.EncounterCustomize]{}

	treatmentDataRaw := req.Treatment
	dataContext := req.DataContext
	config := dataContext.Config

	patientId, ok := req.DataContext.PatientIdMap[req.Treatment.PatientNumber]
	if !ok || patientId == nil {
		return errors.New("patientId not found")
	}

	patientProfile, err := srv.patientProfileRepo.GetPatientProfileById(ctx, *patientId)
	if err != nil {
		return errors.WithStack(err)
	}

	if patientProfile == nil {
		return errors.New("patient profile not found")
	}
	employeeId := patientProfile.PatientInfo.DoctorInfo.TreatmentDoctorId

	defer func() {
		ctx.Logger().Info("importTreatmentData",
			"patientBdtNumber", req.Treatment.PatientNumber,
			"patientId", patientProfile.Id.String(),
			"duration", time.Since(now).String())
	}()

	if err := srv.updatePatientMedicalData(ctx, &UpdatePatientMedicalDataRequest{
		patientProfile: patientProfile,
		treatment:      treatmentDataRaw,
	}); err != nil {
		ctx.Logger().Error("failed to update patient medical data", "err", err.Error())
	}

	for _, Un6200Obj := range treatmentDataRaw.Un6200 {
		notePayloads := []patient_encounter.EncounterNoteTimeline{}
		selectedDateTime, err := parseSelectedDate(ctx, Un6200Obj.Un6200, config.DateTimeFormat)
		if err != nil {
			ctx.Logger().Error("Failed to parse selectedDate", "err", err.Error())
		}
		selectedDate := selectedDateTime.UnixMilli()
		var createdById uuid.UUID
		var bsnrId *uuid.UUID
		createdBy := req.DataContext.SearchDoctorByLanr(Un6200Obj.Lanr)

		if createdBy != nil {
			createdById = createdBy.Id
			employeeId = &createdBy.Id
			bsnrId = createdBy.GetBsnrIdByCode(Un6200Obj.BsnrCode)
		}

		if len(Un6200Obj.ActualDiagnosis) > 0 {
			description := strings.Join(Un6200Obj.ActualDiagnosis, "\n")
			timelineDiagnosis = append(timelineDiagnosis, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
				Id:                util.NewUUID(),
				SelectedDate:      selectedDateTime,
				IsImported:        true,
				Type:              timeline_common.TimelineEntityType_Diagnose,
				PatientId:         *patientId,
				BillingDoctorId:   employeeId,
				TreatmentDoctorId: util.GetPointerValue(employeeId),
				ScheinIds:         []uuid.UUID{},
				Payload: patient_encounter.EncounterDiagnoseTimeline{
					Code:        Un6200Obj.Di6001.Di6001,
					Description: description,
					FreeText:    fmt.Sprintf("(%s) %s", Un6200Obj.Di6001.Di6001, description),
					Certainty:   (*patient_encounter.Certainty)(&Un6200Obj.Di6001.Di6003),
					Laterality:  (*patient_encounter.Laterality)(&Un6200Obj.Di6001.Di6004),
					Explanation: strings.Join(Un6200Obj.Di6001.Di6006, "\n"),
					Exception:   strings.Join(Un6200Obj.Di6001.Di6008, "\n"),
					Command:     "D",
					Type:        patient_encounter.DIAGNOSETYPE_ACUTE,
				},
				CreatedBy:        createdById,
				AssignedToBsnrId: bsnrId,
				RecentAuditLogs: []timeline_repo.AuditLog{
					{
						AuditLogId: util.NewUUID(),
						UserId:     &createdById,
						Date:       selectedDateTime,
						ActionType: timeline_common.Add,
					},
				},
			})
		}

		for _, v := range Un6200Obj.Un6280 {
			fullText := strings.Join(v.Un6280, "\n")
			commandText := "Überweisung Inhalt"
			if strings.HasPrefix(fullText, "an:") {
				fullText = strings.Replace(fullText, "an:", "", 1)
				commandText = "Überweisung an"
			} else if strings.HasPrefix(fullText, "wg:") {
				fullText = strings.Replace(fullText, "wg:", "", 1)
				commandText = "Auftrag/Diagnose"
			}
			command, noteType, noteContent := getInfoOfNoteType(commandText, fullText)
			notePayloads = append(notePayloads, patient_encounter.EncounterNoteTimeline{
				Note:    noteContent,
				Command: command,
				Type:    &noteType,
			})
		}

		for _, v := range Un6200Obj.Un6285 {
			if len(v.Un6285) < 16 {
				continue
			}
			from, to := v.Un6285[:8], v.Un6285[8:]
			formMillis, _ := util.ConvertStringToMillisecond(from, config.DateTimeFormat)
			toMillis, _ := util.ConvertStringToMillisecond(to, config.DateTimeFormat)
			mapPayload := map[formkey.FormKey_MUSTER_1]any{
				formkey.MUSTER_1_CHECKBOX_ERSTBES_0:  false,
				formkey.MUSTER_1_CHECKBOX_FOLGEBES_0: false,
			}
			filterICDValues := v.Un6286
			if slice.Contains(v.Un6286, "Folge-AU") {
				mapPayload[formkey.MUSTER_1_CHECKBOX_FOLGEBES_0] = true
				filterICDValues = slice.Filter(v.Un6286, func(v string) bool {
					return v != "Folge-AU"
				})
			} else {
				mapPayload[formkey.MUSTER_1_CHECKBOX_ERSTBES_0] = true
			}
			// filter out data like "Erstausstellung durch Dr. Krikeli"
			filterICDValues = slice.Filter(filterICDValues, func(v string) bool {
				return !strings.Contains(v, " ")
			})
			icdLabels := []formkey.FormKey_MUSTER_1{
				formkey.MUSTER_1_LABEL_ICD10_CODE_1_0,
				formkey.MUSTER_1_LABEL_ICD10_CODE_2_0,
				formkey.MUSTER_1_LABEL_ICD10_CODE_3_0,
				formkey.MUSTER_1_LABEL_ICD10_CODE_4_0,
				formkey.MUSTER_1_LABEL_ICD10_CODE_5_0,
				formkey.MUSTER_1_LABEL_ICD10_CODE_6_0,
			}
			for i := range min(len(filterICDValues), len(icdLabels)) {
				mapPayload[icdLabels[i]] = filterICDValues[i]
			}
			if formMillis != 0 && mapPayload[formkey.MUSTER_1_CHECKBOX_ERSTBES_0].(bool) {
				mapPayload[formkey.MUSTER_1_DATE_ARBEIT_0] = formMillis
			}
			if toMillis != 0 {
				mapPayload[formkey.MUSTER_1_DATE_VORAU_0] = toMillis
			}
			payloadBytes, err := json.Marshal(mapPayload)
			if err != nil {
				ctx.Logger().Error("error marshalling payload", "err", err.Error())
				continue
			}
			newUUID := util.NewUUID()
			timelineForms = append(timelineForms, timeline_repo.TimelineEntity[patient_encounter.EncounterForm]{
				Id:                newUUID,
				SelectedDate:      selectedDateTime,
				IsImported:        true,
				Type:              timeline_common.TimelineEntityType_Form,
				PatientId:         *patientId,
				BillingDoctorId:   employeeId,
				TreatmentDoctorId: util.GetPointerValue(employeeId),
				EncounterCase:     util.NewPointer(patient_encounter.AB),
				TreatmentCase:     util.NewPointer(patient_encounter.TreatmentCaseCustodian),
				ScheinIds:         []uuid.UUID{},
				Payload: patient_encounter.EncounterForm{
					Id: newUUID,
					Prescribe: &form_common.Prescribe{
						Id:                newUUID,
						DoctorId:          employeeId,
						TreatmentDoctorId: employeeId,
						PatientId:         patientId,
						CreatedDate:       selectedDate,
						PrintedDate:       &selectedDate,
						Payload:           string(payloadBytes),
						FormName:          form_common.Muster_1,
						EncounterCase:     string(patient_encounter.AB),
						ContractType:      common.ContractType_KvContract,
						PrescribeDate:     selectedDate,
						AssignedToBsnrId:  bsnrId,
						EAUStatus:         util.NewPointer(qes_common.Status_Printed),
					},
				},
				CreatedBy:        createdById,
				AssignedToBsnrId: bsnrId,
				RecentAuditLogs: []timeline_repo.AuditLog{
					{
						AuditLogId: util.NewUUID(),
						UserId:     &createdById,
						Date:       selectedDateTime,
						ActionType: timeline_common.Add,
					},
				},
			})
		}

		for _, v := range Un6200Obj.Un6290 {
			command, noteType, noteContent := getInfoOfNoteType(v.Un6290, strings.Join(v.Un6291, "\n"))
			notePayloads = append(notePayloads, patient_encounter.EncounterNoteTimeline{
				Note:    noteContent,
				Command: command,
				Type:    &noteType,
			})
		}

		for _, obj6295 := range Un6200Obj.Un6295 {
			formMapper := newFormMapper(obj6295.Un6296, obj6295.Un6295, config.DateTimeFormat, &selectedDateTime)
			if formMapper == nil {
				ctx.Logger().Warn("No form mapper was found", "formName", obj6295.Un6295)
				continue
			}
			payload, err := formMapper.Map()
			if err != nil {
				ctx.Logger().Warn("failed to get payload of form", "formName", obj6295.Un6295)
				continue
			}
			if formMapper.GetName() == form_common.Muster_13 {
				err = srv.createHeilmittelFromFormMapper(ctx, &CreateHeilmittelFromFormMapperRequest{
					formMapper:   formMapper,
					selectedDate: selectedDate,
					patientId:    *patientId,
					employeeId:   util.GetPointerValue(employeeId),
					patientDob:   patientProfile.PatientInfo.PersonalInfo.DOB,
					bsnrId:       bsnrId,
				})
				if err != nil {
					ctx.Logger().Error("failed to import heilmittelForm", "err", err.Error())
					continue
				}
			}
			newUUID := util.NewUUID()
			timelineForms = append(timelineForms, timeline_repo.TimelineEntity[patient_encounter.EncounterForm]{
				Id:                newUUID,
				SelectedDate:      selectedDateTime,
				IsImported:        true,
				Type:              timeline_common.TimelineEntityType_Form,
				PatientId:         *patientId,
				BillingDoctorId:   employeeId,
				TreatmentDoctorId: util.GetPointerValue(employeeId),
				EncounterCase:     util.NewPointer(patient_encounter.AB),
				TreatmentCase:     util.NewPointer(patient_encounter.TreatmentCaseCustodian),
				ScheinIds:         []uuid.UUID{},
				Payload: patient_encounter.EncounterForm{
					Id: newUUID,
					Prescribe: &form_common.Prescribe{
						Id:                newUUID,
						DoctorId:          employeeId,
						TreatmentDoctorId: employeeId,
						PatientId:         patientId,
						CreatedDate:       selectedDate,
						PrintedDate:       &selectedDate,
						Payload:           payload,
						FormName:          formMapper.GetName(),
						EncounterCase:     string(patient_encounter.AB),
						ContractType:      common.ContractType_KvContract,
						PrescribeDate:     selectedDate,
						AssignedToBsnrId:  bsnrId,
					},
				},
				CreatedBy:        createdById,
				AssignedToBsnrId: bsnrId,
				RecentAuditLogs: []timeline_repo.AuditLog{
					{
						AuditLogId: util.NewUUID(),
						UserId:     &createdById,
						Date:       selectedDateTime,
						ActionType: timeline_common.Add,
					},
				},
			})
		}
		// import document management timeline
		for _, obj6310 := range Un6200Obj.Un6310s {
			v, err := srv.createDMTimeline(ctx, obj6310, patientProfile, selectedDate, employeeId, req.Treatment.PatientNumber, bsnrId)
			if err != nil {
				ctx.Logger().Error("Failed to create DM timeline", "err", err)
			}
			timelineDMs = append(timelineDMs, *v)
		}

		un6200ObjValue := reflect.ValueOf(Un6200Obj)
		un6200ObjType := un6200ObjValue.Type()
		// Iterate over the struct fields 6334 - 6397
		for i := 0; i < un6200ObjValue.NumField(); i++ {
			field := un6200ObjType.Field(i)
			tagValue := field.Tag.Get("field")
			if value, err := strconv.Atoi(tagValue); err != nil || value%2 != 0 {
				continue
			}
			if tagValue >= "6330" && tagValue <= "6397" {
				value := un6200ObjValue.Field(i)
				if value.IsZero() {
					continue
				}
				if value.Kind() == reflect.Slice {
					for j := 0; j < value.Len(); j++ {
						if value.Index(j).Field(1).IsZero() {
							continue
						}
						if customizeTimeline := newCustomizeTimeline(newCustomizeTimelineReq{
							selectedDateTime: selectedDateTime,
							patientId:        patientId,
							employeeId:       employeeId,
							createdById:      createdById,
							bsnrId:           bsnrId,
							command:          value.Index(j).Field(0).String(),
							description:      concatSliceToString(value.Index(j).Field(1)),
						}); customizeTimeline != nil {
							timelineCustomizes = append(timelineCustomizes, *customizeTimeline)
							continue
						}
						command, noteType, noteContent := getInfoOfNoteType(value.Index(j).Field(0).String(), concatSliceToString(value.Index(j).Field(1)))
						notePayloads = append(notePayloads, patient_encounter.EncounterNoteTimeline{
							Note:    noteContent,
							Command: command,
							Type:    &noteType,
						})
					}
				} else {
					if customizeTimeline := newCustomizeTimeline(newCustomizeTimelineReq{
						selectedDateTime: selectedDateTime,
						patientId:        patientId,
						employeeId:       employeeId,
						createdById:      createdById,
						bsnrId:           bsnrId,
						command:          value.String(),
						description:      concatSliceToString(un6200ObjValue.Field(i + 1)),
					}); customizeTimeline != nil {
						timelineCustomizes = append(timelineCustomizes, *customizeTimeline)
						continue
					}
					command, noteType, noteContent := getInfoOfNoteType(value.String(), concatSliceToString(un6200ObjValue.Field(i+1)))
					notePayloads = append(notePayloads, patient_encounter.EncounterNoteTimeline{
						Note:    noteContent,
						Command: command,
						Type:    &noteType,
					})
				}
			}
		}

		for _, medi := range Un6200Obj.Medicines {
			scheinId := req.DataContext.ScheinIdMap[medi.ScheinNumber]
			if scheinId == nil {
				scheinIdRes, err := srv.getOnlyOneScheinIdInQuarter(ctx, *patientId, selectedDate)
				if err != nil {
					ctx.Logger().Warn("fail to getOnlyOneScheinIdInQuarter", "err", err)
				}
				scheinId = scheinIdRes
			}
			res := createMedicineTimeline(ctx, CreateMedicineTimelineRequest{
				formType:         medicine_repo_common.KREZ,
				medi:             medi,
				config:           config,
				patientId:        patientId,
				createdById:      createdById,
				bsnrId:           bsnrId,
				selectedDate:     selectedDate,
				selectedDateTime: selectedDateTime,
				bsnr:             &Un6200Obj.BsnrCode,
				scheinId:         scheinId,
			})
			timelineMedicinePrescriptions = append(timelineMedicinePrescriptions, res.TimelineMedicine)
			medicinePrescriptions = append(medicinePrescriptions, res.MedicinePrescription)
			prescribedMedications = append(prescribedMedications, res.PrescribedMedication)
		}

		for _, m := range Un6200Obj.MedicinesGRez {
			medi := pkg_copy.CloneTo[bdt_model.Medicine](m)
			scheinId := req.DataContext.ScheinIdMap[medi.ScheinNumber]
			if scheinId == nil {
				scheinIdRes, err := srv.getOnlyOneScheinIdInQuarter(ctx, *patientId, selectedDate)
				if err != nil {
					ctx.Logger().Warn("fail to getOnlyOneScheinIdInQuarter", "err", err)
				}
				scheinId = scheinIdRes
			}
			res := createMedicineTimeline(ctx, CreateMedicineTimelineRequest{
				formType:         medicine_repo_common.GREZ,
				medi:             medi,
				config:           config,
				patientId:        patientId,
				createdById:      createdById,
				bsnrId:           bsnrId,
				selectedDate:     selectedDate,
				selectedDateTime: selectedDateTime,
				bsnr:             &Un6200Obj.BsnrCode,
				scheinId:         scheinId,
			})
			timelineMedicinePrescriptions = append(timelineMedicinePrescriptions, res.TimelineMedicine)
			medicinePrescriptions = append(medicinePrescriptions, res.MedicinePrescription)
			prescribedMedications = append(prescribedMedications, res.PrescribedMedication)
		}

		for _, m := range Un6200Obj.MedicinesTPrescription {
			medi := pkg_copy.CloneTo[bdt_model.Medicine](m)
			scheinId := req.DataContext.ScheinIdMap[medi.ScheinNumber]
			if scheinId == nil {
				scheinIdRes, err := srv.getOnlyOneScheinIdInQuarter(ctx, *patientId, selectedDate)
				if err != nil {
					ctx.Logger().Warn("fail to getOnlyOneScheinIdInQuarter", "err", err)
				}
				scheinId = scheinIdRes
			}
			res := createMedicineTimeline(ctx, CreateMedicineTimelineRequest{
				formType:         medicine_repo_common.TPrescription,
				medi:             medi,
				config:           config,
				patientId:        patientId,
				createdById:      createdById,
				bsnrId:           bsnrId,
				selectedDate:     selectedDate,
				selectedDateTime: selectedDateTime,
				bsnr:             &Un6200Obj.BsnrCode,
				scheinId:         scheinId,
			})
			timelineMedicinePrescriptions = append(timelineMedicinePrescriptions, res.TimelineMedicine)
			medicinePrescriptions = append(medicinePrescriptions, res.MedicinePrescription)
			prescribedMedications = append(prescribedMedications, res.PrescribedMedication)
		}

		for _, m := range Un6200Obj.MedicinesKRez_6215 {
			scheinId := req.DataContext.ScheinIdMap[m.ScheinNumber]
			if scheinId == nil {
				scheinIdRes, err := srv.getOnlyOneScheinIdInQuarter(ctx, *patientId, selectedDate)
				if err != nil {
					ctx.Logger().Warn("fail to getOnlyOneScheinIdInQuarter", "err", err)
				}
				scheinId = scheinIdRes
			}
			scheinIds := []uuid.UUID{}
			if scheinId != nil {
				scheinIds = append(scheinIds, *scheinId)
			}
			price, _ := strconv.ParseFloat(m.Price, 64)
			quantity, _ := strconv.ParseInt(m.Quantity, 10, 32)
			timelineMedicine := timeline_repo.TimelineEntity[medicine_common.Medicine]{
				Id:                util.NewUUID(),
				TreatmentDoctorId: util.GetPointerValue(employeeId),
				PatientId:         *patientId,
				Payload: medicine_common.Medicine{
					Pzn: m.Pzn,
					PriceInformation: &medicine_common.PriceInformation{
						PharmacySalePrice: price,
					},

					PackagingInformation: &medicine_common.PackagingInformation{
						Quantity:       m.Quantity,
						QuantityNumber: util.NewPointer(int32(quantity)),
						PackageSize: medicine_common.PackageSize{
							Nop: m.Nop,
						},
					},
					ProductInformation: &medicine_common.ProductInformation{
						DosageForm:        m.Dosageform,
						SampleProductFlag: 1,
						Name:              m.Name,
					},
				},
				CreatedBy:        createdById,
				AssignedToBsnrId: bsnrId,
				SelectedDate:     selectedDateTime,
				IsImported:       true,
				Type:             timeline_common.TimelineEntityType_Medicine,
				ScheinIds:        scheinIds,
				RecentAuditLogs: []timeline_repo.AuditLog{
					{
						AuditLogId: util.NewUUID(),
						UserId:     &createdById,
						Date:       selectedDateTime,
						ActionType: timeline_common.Add,
					},
				},
			}
			timelineMedicines = append(timelineMedicines, timelineMedicine)
		}

		for _, Un6398Obj := range Un6200Obj.Un6398 {
			// ignore in case object without content
			if Un6398Obj.Un6399 == nil {
				continue
			}

			if strings.Contains(Un6398Obj.Un6398, HEILMITTEL_FORM_DETAIL_NAME) {
				continue
			} else if slices.Contains(MusterFormDetailNames, Un6398Obj.Un6398) && isValidFormDate(Un6398Obj.Un6398, selectedDateTime) {
				// only use specific form to import for now
				continue
			} else if Un6398Obj.Un6398 != "" {
				command, noteType, noteContent := getInfoOfNoteType(Un6398Obj.Un6398, strings.Join(Un6398Obj.Un6399, "\n"))
				notePayloads = append(notePayloads, patient_encounter.EncounterNoteTimeline{
					Note:    noteContent,
					Command: command,
					Type:    &noteType,
				})
			}
		}

		tmp := slice.Map(notePayloads, func(p patient_encounter.EncounterNoteTimeline) timeline_repo.TimelineEntity[patient_encounter.EncounterNoteTimeline] {
			return timeline_repo.TimelineEntity[patient_encounter.EncounterNoteTimeline]{
				Id:                util.NewUUID(),
				SelectedDate:      selectedDateTime,
				IsImported:        true,
				Type:              timeline_common.TimelineEntityType_Note,
				PatientId:         *patientId,
				BillingDoctorId:   employeeId,
				TreatmentDoctorId: util.GetPointerValue(employeeId),
				EncounterCase:     util.NewPointer(patient_encounter.AB),
				TreatmentCase:     util.NewPointer(patient_encounter.TreatmentCaseCustodian),
				ScheinIds:         []uuid.UUID{},
				Payload:           p,
				CreatedBy:         createdById,
				AssignedToBsnrId:  bsnrId,
				RecentAuditLogs: []timeline_repo.AuditLog{
					{
						AuditLogId: util.NewUUID(),
						UserId:     &createdById,
						Date:       selectedDateTime,
						ActionType: timeline_common.Add,
					},
				},
			}
		})
		timelineNotes = append(timelineNotes, tmp...)
	}

	var errorGroup function.ErrorGroupRecover
	errorGroup.Go(
		func() error {
			timelineDiagnosis = srv.filterDiagnosis(ctx, timelineDiagnosis)
			_, err = srv.timelineDiagnoseRepo.CreateMany(ctx, timelineDiagnosis)
			return errors.WithMessage(err, "failed to create diagnoses")
		},
		func() error {
			_, err := srv.timelineNoteRepo.CreateMany(ctx, timelineNotes)
			return errors.WithMessage(err, "failed to create notes")
		},
		func() error {
			_, err := srv.timelineFormRepo.CreateMany(ctx, timelineForms)
			return errors.WithMessage(err, "failed to create forms")
		},
		func() error {
			_, err := srv.timelineDMRepo.CreateMany(ctx, timelineDMs)
			return errors.WithMessage(err, "failed to create DMs")
		},
		func() error {
			_, err := srv.timelineMedicinePrescriptionRepo.CreateMany(ctx, timelineMedicinePrescriptions)
			return errors.WithMessage(err, "failed to create medicine prescriptions timeline")
		},
		func() error {
			if len(medicinePrescriptions) > 0 {
				_, err := srv.medicinePrescriptionRepo.CreateMany(ctx, medicinePrescriptions)
				return errors.WithMessage(err, "failed to create medicine prescriptions")
			}
			return nil
		},
		func() error {
			if len(prescribedMedications) > 0 {
				_, err := srv.prescribedMedicationRepo.CreateMany(ctx, prescribedMedications)
				return errors.WithMessage(err, "failed to create prescribed medications")
			}
			return nil
		},
		func() error {
			_, err := srv.timelineMedicineRepo.CreateMany(ctx, timelineMedicines)
			return errors.WithMessage(err, "failed to create medicines")
		},
		func() error {
			_, err := srv.timelineCustomizeRepo.CreateMany(ctx, timelineCustomizes)
			if err != nil {
				return errors.WithMessage(err, "failed to create customize timeline")
			}
			return nil
		},
	)
	if err := errorGroup.Wait(); err != nil {
		return err
	}

	delete(req.DataContext.PatientIdMap, req.Treatment.PatientNumber)
	for _, scheinNumber := range req.DataContext.ScheinNumberMap[req.Treatment.PatientNumber] {
		delete(req.DataContext.ScheinIdMap, scheinNumber)
	}
	delete(req.DataContext.ScheinNumberMap, req.Treatment.PatientNumber)
	return nil
}

// Deprecated: createHeilmittelForm is use for regex extract.
// func (srv *BDTService) createHeilmittelForm(ctx *titan.Context, req *CreateHeilmittelFormRequest) error { //nolint:unused
// 	template := req.template
// 	patientId := req.patientId
// 	employeeId := req.employeeId
// 	selectedDate := req.selectedDate
// 	dob := req.patientDob

// 	formName := form_common.Muster_13
// 	var areaContent string
// 	var keySymptoms []heimi.KeySymptoms
// 	payload, form13Data, err := mapMusterForm13(template)
// 	if err != nil {
// 		return err
// 	}
// 	payloadBytes, err := json.Marshal(payload)
// 	if err != nil {
// 		return err
// 	}
// 	if form13Data.Leitsymptomatik_a {
// 		keySymptoms = append(keySymptoms, heimi.KeySymptoms{
// 			Value: "a",
// 		})
// 	} else if form13Data.Leitsymptomatik_b {
// 		keySymptoms = append(keySymptoms, heimi.KeySymptoms{
// 			Value: "b",
// 		})
// 	} else if form13Data.Leitsymptomatik_c {
// 		keySymptoms = append(keySymptoms, heimi.KeySymptoms{
// 			Value: "c",
// 		})
// 	}
// 	if form13Data.Physiotherapie {
// 		areaContent = "I. Maßnahmen der Physiotherapie"
// 	} else if form13Data.Podologische {
// 		areaContent = "II. Maßnahmen der Podologischen Therapie"
// 	} else if form13Data.Stimm {
// 		areaContent = "III. Maßnahmen der Stimm-, Sprech-, Sprach- und Schlucktherapie"
// 	} else if form13Data.Ergotherapie {
// 		areaContent = "IV. Maßnahmen der Ergotherapie"
// 	} else if form13Data.Ernahrungstherapie {
// 		areaContent = "V. Maßnahmen der Ernährungstherapie"
// 	}
// 	quantity, _ := strconv.ParseInt(form13Data.Quantity_1, 10, 32)
// 	// default value of quanity
// 	if quantity == 0 {
// 		quantity = 1
// 	}
// 	splitedICDText := strings.Split(form13Data.ICDText, ";")
// 	diagnoseName := splitedICDText[0]
// 	var secondaryDiagnoseName string
// 	var secondaryDiagnose *heimi.Diagnose
// 	if len(splitedICDText) > 1 {
// 		secondaryDiagnoseName = splitedICDText[1]
// 	}
// 	if secondaryDiagnoseName != "" || form13Data.ICD2 != "" {
// 		secondaryDiagnose = &heimi.Diagnose{
// 			Code: form13Data.ICD2,
// 			Name: secondaryDiagnoseName,
// 		}
// 	}

//		heimiRequest := heimi.PrescribleRequest{
//			PatientId:         patientId,
//			PatientAge:        int32(util.Age(ctx, util.ConvertMillisecondsToTime(dob), ctx.RequestTimeZone())),
//			DoctorId:          employeeId,
//			TreatmentDoctorId: employeeId,
//			Prescription: heimi.Prescription{
//				ScheinId: uuid.New(),
//				Area: heimi.Diagnose{
//					Code: areaContent,
//					Name: areaContent,
//				},
//				ZuzahLungsFrei:    form13Data.Gebuhrenfrei,
//				ZuzahLungsPflicht: form13Data.Gebuhrenpflichtig,
//				UnfallFolgen:      form13Data.Unfallfolgen,
//				Bvg:               form13Data.BVG,
//				TheRapyReport:     form13Data.Therapiebericht,
//				HomeVisit:         form13Data.Hausbesuch_ja,
//				UrgentTreatment:   form13Data.Dringlicher,
//				DiagnoseGroup: heimi.Diagnose{
//					Code: form13Data.Diagnosegruppe,
//				},
//				Diagnose: heimi.Diagnose{
//					Code: form13Data.ICD1,
//					Name: diagnoseName,
//				},
//				SecondaryDiagnose: secondaryDiagnose,
//				KeySymptom:        keySymptoms,
//				FormName:          string(formName),
//				PrintDate:         &selectedDate,
//				PrescribeDate:     selectedDate,
//				EncounterCase:     string(patientfile.AB),
//				ContractType:      common.ContractType_KvContract,
//				FormSetting:       string(payloadBytes),
//				Remedies: []heimi.ProductDetail{
//					{
//						Quantity: int32(quantity),
//						Name:     form13Data.Heilmittel_1,
//					},
//				},
//				ComplementaryRemedies: []heimi.ProductDetail{},
//				TherapyFrequency: heimi.TherapyFrequency{
//					Name: form13Data.Therapiefrequenz,
//				},
//			},
//			IsImported: util.NewPointer(true),
//		}
//		_, err = srv.heimiAppClient.Prescribe(ctx, heimiRequest)
//		if err != nil {
//			return err
//		}
//		return nil
//	}

func parseQuantity(s string) int32 {
	q, err := strconv.ParseInt(s, 10, 32)
	if err != nil || q == 0 {
		return 1
	}
	return int32(q)
}

func (srv *BDTService) createHeilmittelFromFormMapper(ctx *titan.Context, req *CreateHeilmittelFromFormMapperRequest) error {
	formMapper := req.formMapper
	patientId := req.patientId
	employeeId := req.employeeId
	selectedDate := req.selectedDate
	dob := req.patientDob

	formName := formMapper.GetName()
	var areaContent string
	var keySymptoms []heimi.KeySymptoms
	payload, err := formMapper.Map()
	if err != nil {
		return err
	}
	form13Data, ok := formMapper.GetFormData().(MusterForm13)
	if !ok {
		ctx.Logger().Warn("Failed to GetFormData Heilmittel form")
		return nil
	}

	if form13Data.Leitsymptomatik_a {
		keySymptoms = append(keySymptoms, heimi.KeySymptoms{
			Value: "a",
		})
	} else if form13Data.Leitsymptomatik_b {
		keySymptoms = append(keySymptoms, heimi.KeySymptoms{
			Value: "b",
		})
	} else if form13Data.Leitsymptomatik_c {
		keySymptoms = append(keySymptoms, heimi.KeySymptoms{
			Value: "c",
		})
	}
	if form13Data.Physiotherapie {
		areaContent = "I. Maßnahmen der Physiotherapie"
	} else if form13Data.Podologische {
		areaContent = "II. Maßnahmen der Podologischen Therapie"
	} else if form13Data.Stimm {
		areaContent = "III. Maßnahmen der Stimm-, Sprech-, Sprach- und Schlucktherapie"
	} else if form13Data.Ergotherapie {
		areaContent = "IV. Maßnahmen der Ergotherapie"
	} else if form13Data.Ernahrungstherapie {
		areaContent = "V. Maßnahmen der Ernährungstherapie"
	}
	splitedICDText := strings.Split(form13Data.ICDText, ";")
	diagnoseName := splitedICDText[0]
	var secondaryDiagnoseName string
	var secondaryDiagnose *heimi.Diagnose
	if len(splitedICDText) > 1 {
		secondaryDiagnoseName = splitedICDText[1]
	}
	if secondaryDiagnoseName != "" || form13Data.ICD2 != "" {
		secondaryDiagnose = &heimi.Diagnose{
			Code: form13Data.ICD2,
			Name: secondaryDiagnoseName,
		}
	}

	remedies := []heimi.ProductDetail{}
	if form13Data.Heilmittel_1 != "" {
		remedies = append(remedies, heimi.ProductDetail{
			Name:     form13Data.Heilmittel_1,
			Quantity: parseQuantity(form13Data.Quantity_1),
		})
	}
	if form13Data.Heilmittel_2 != "" {
		remedies = append(remedies, heimi.ProductDetail{
			Name:     form13Data.Heilmittel_2,
			Quantity: parseQuantity(form13Data.Quantity_2),
		})
	}
	if form13Data.Heilmittel_3 != "" {
		remedies = append(remedies, heimi.ProductDetail{
			Name:     form13Data.Heilmittel_3,
			Quantity: parseQuantity(form13Data.Quantity_3),
		})
	}
	if form13Data.Heilmittel_4 != "" {
		remedies = append(remedies, heimi.ProductDetail{
			Name:     form13Data.Heilmittel_4,
			Quantity: parseQuantity(form13Data.Quantity_4),
		})
	}

	heimiRequest := heimi.PrescribleRequest{
		PatientId:         patientId,
		PatientAge:        int32(util.Age(ctx, util.ConvertMillisecondsToTime(dob), ctx.RequestTimeZone())),
		DoctorId:          employeeId,
		TreatmentDoctorId: employeeId,
		Prescription: heimi.Prescription{
			ScheinId: uuid.New(),
			Area: heimi.Diagnose{
				Code: areaContent,
				Name: areaContent,
			},
			ZuzahLungsFrei:    form13Data.Gebuhrenfrei,
			ZuzahLungsPflicht: form13Data.Gebuhrenpflichtig,
			UnfallFolgen:      form13Data.Unfallfolgen,
			Bvg:               form13Data.BVG,
			TheRapyReport:     form13Data.Therapiebericht,
			HomeVisit:         form13Data.Hausbesuch_ja,
			UrgentTreatment:   form13Data.Dringlicher,
			DiagnoseGroup: heimi.Diagnose{
				Code: form13Data.Diagnosegruppe,
			},
			Diagnose: heimi.Diagnose{
				Code: form13Data.ICD1,
				Name: diagnoseName,
			},
			SecondaryDiagnose:     secondaryDiagnose,
			KeySymptom:            keySymptoms,
			FormName:              string(formName),
			PrintDate:             &selectedDate,
			PrescribeDate:         selectedDate,
			EncounterCase:         string(patientfile.AB),
			ContractType:          common.ContractType_KvContract,
			FormSetting:           payload,
			Remedies:              remedies,
			ComplementaryRemedies: []heimi.ProductDetail{},
			TherapyFrequency: heimi.TherapyFrequency{
				Name: form13Data.Therapiefrequenz,
			},
			TheRapyFreeText: form13Data.TherapiezieleText,
		},
		IsImported:       util.NewPointer(true),
		AssignedToBsnrId: req.bsnrId,
		SelectedDate:     util.NewPointer(util.ConvertMillisecondsToTime(selectedDate, ctx.RequestTimeZone())),
	}
	_, err = srv.heimiAppClient.Prescribe(ctx, heimiRequest)
	if err != nil {
		return err
	}
	return nil
}

// check if diagnosis timeline already imported by another object
func (srv *BDTService) filterDiagnosis(ctx *titan.Context, diagnosisTimelines []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline] {
	memoize := function.Memoize[[]timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline], string]()

	for i, v := range diagnosisTimelines {
		yearQuarter := util.ToYearQuarter(util.ConvertTimeToMiliSecond(v.SelectedDate, ctx.RequestTimeZone()))
		timelines, err := memoize(
			func() string {
				return fmt.Sprintf("%s_%d_%d", v.PatientId.String(), int(yearQuarter.Quarter), int(yearQuarter.Year))
			},
			func() ([]timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline], error) {
				return srv.timelineDiagnoseRepo.GetDiagnosesByQuarter(ctx, v.PatientId, yearQuarter)
			},
		)
		if err != nil {
			ctx.Logger().Error("Failed to get timeline diagnose by GetByPatientIdAndQuarter", "err", err.Error())
			continue
		}
		matchedTimeLine := slice.FindOne(timelines, func(d timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) bool {
			return util.IsSameDate(d.SelectedDate, v.SelectedDate) && v.Payload.IsDuplicated(&d.Payload)
		})
		// hides timeline records already imported from another object
		if matchedTimeLine != nil {
			diagnosisTimelines[i].IsDeleted = true
		}
	}

	return diagnosisTimelines
}

func (srv *BDTService) createDMTimeline(ctx *titan.Context,
	obj6310 bdt_model.Un6310,
	patientProfile *patient.PatientProfile,
	selectedDate int64,
	employeeId *uuid.UUID,
	bdtNumber string,
	bsnrId *uuid.UUID,
) (*timeline_repo.TimelineEntity[patient_encounter.EncounterDocumentManagement], error) {
	now := time.Now()
	defer func() {
		ctx.Logger().Info("createDMTimeline",
			"patientBdtNumber", bdtNumber,
			"patientId", patientProfile.Id.String(),
			"duration", time.Since(now).String())
	}()
	selectedDateTime := util.ConvertMillisecondsToTime(selectedDate, ctx.RequestTimeZone())
	normalizedPath := filepath.FromSlash(strings.ReplaceAll(obj6310.Un6321, "\\", "/"))
	absPathFile := filepath.Join(BaseDir, ctx.UserInfo().CareProviderId.String(), normalizedPath)
	DMUUID := util.NewUUID()
	var fileName, description, fileNameOriginal string
	var fileSize int64

	file, err := os.Open(absPathFile)
	if err != nil {
		ctx.Logger().Error(fmt.Sprintf("Failed to open file %s: %v", absPathFile, err),
			"patientBdtNumber", bdtNumber,
			"patientId", patientProfile.Id.String())
		fileNameOriginal = filepath.Base(absPathFile) // Fallback to extracted file name
	} else {
		defer file.Close()

		// Get file info for size and metadata
		fileStat, statErr := file.Stat()
		if statErr != nil {
			ctx.Logger().Error(fmt.Sprintf("Failed to get file info: %v", statErr))
		} else {
			fileSize = fileStat.Size()
			fileNameOriginal = fileStat.Name()
		}

		ext := filepath.Ext(normalizedPath)
		fileName = DMUUID.String() + ext

		if _, uploadErr := srv.minioClient.PutObject(
			ctx,
			srv.bucketDmCompanion,
			fmt.Sprintf("%s/%s", ctx.UserInfo().CareProviderId.String(), fileName),
			file,
			fileSize,
			minio.PutObjectOptions{},
		); uploadErr != nil {
			ctx.Logger().Error("Failed to upload file to MinIO", "err", uploadErr.Error())
		} else {
			description = function.If(obj6310.Un6310 != "", fmt.Sprintf("%s - %s", obj6310.Un6310, obj6310.Un6320), obj6310.Un6320)

			// Attempt to remove the file after a successful upload
			if err := os.Remove(absPathFile); err != nil {
				ctx.Logger().Error(fmt.Sprintf("Failed to delete file %s: %v", absPathFile, err))
			}
		}
	}

	// Construct the timeline entity regardless of errors
	timelineEntity := &timeline_repo.TimelineEntity[patient_encounter.EncounterDocumentManagement]{
		Id:                util.NewUUID(),
		SelectedDate:      selectedDateTime,
		IsImported:        true,
		Type:              timeline_common.TimelineEntityType_DocumentManagement,
		PatientId:         *patientProfile.PatientInfo.PatientId,
		BillingDoctorId:   employeeId,
		TreatmentDoctorId: util.GetPointerValue(employeeId),
		ScheinIds:         []uuid.UUID{},
		Payload: patient_encounter.EncounterDocumentManagement{
			Id: DMUUID,
			Patient: &dm_common.Patient{
				Id:            *patientProfile.PatientInfo.PatientId,
				PatientNumber: int64(patientProfile.PatientInfo.PatientNumber),
				FirstName:     patientProfile.PatientInfo.PersonalInfo.FirstName,
				LastName:      patientProfile.PatientInfo.PersonalInfo.LastName,
				DateOfBirth:   patientProfile.PatientInfo.PersonalInfo.DateOfBirth,
				Gender:        string(patientProfile.PatientInfo.PersonalInfo.Gender),
				FullName:      patientProfile.PatientInfo.PersonalInfo.GetFullName(),
			},
			DocumentName: fileNameOriginal, // Use original name as fallback
			Description:  &description,
			Status:       dm_common.DocumentManagementStatus_Completed,
			ImportedDate: selectedDate,
		},
		CreatedBy:        util.GetPointerValue(employeeId),
		AssignedToBsnrId: bsnrId,
		RecentAuditLogs: []timeline_repo.AuditLog{
			{
				AuditLogId: util.NewUUID(),
				UserId:     employeeId,
				Date:       selectedDateTime,
				ActionType: timeline_common.Add,
			},
		},
	}

	// Return the timeline entity with an error if something failed
	if err != nil {
		return timelineEntity, fmt.Errorf("one or more operations failed: %w", err)
	}

	return timelineEntity, nil
}

func (srv *BDTService) importPrivateSchein(ctx *titan.Context, req *ImportPrivateScheinRequest) error {
	if req == nil || len(req.PrivateSchein) == 0 {
		return errors.New("private schein is empty")
	}
	now := time.Now()
	patientId := util.GetPointerValue(req.PatientId)
	defer func() {
		ctx.Logger().Info("importPrivateSchein",
			"patientBdtNumber", req.PrivateSchein[0].PatientNumber,
			"patientId", patientId,
			"duration", time.Since(now).String())
	}()
	dataContext := req.DataContext
	patientInfo := req.PatientInfo
	dateFormat := dataContext.Config.DateTimeFormat
	timelineDiagnosis := []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{}
	timeLineServices := []timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{}
	privateBillings := []private_billing.CreatePrivateBillingRequest{}
	privateScheins := []scheinRepo.ScheinRepo{}
	privateContactMemo := function.Memoize[*private_contract_group_common.PrivateContractGroupItem, int64]()

	for _, rawData := range req.PrivateSchein {
		var createdById uuid.UUID
		var treatmentDoctorId uuid.UUID
		treatmentDoctor := req.DataContext.SearchTreatmentDoctor(rawData.TreatmentDoctor)
		if treatmentDoctor != nil {
			treatmentDoctorId = treatmentDoctor.Id
		}
		createdBy := req.DataContext.SearchDoctorByLanr(rawData.Lanr)
		if createdBy != nil {
			createdById = createdBy.Id
			treatmentDoctorId = createdBy.Id
			treatmentDoctor = createdBy
		}
		bsnrId := createdBy.GetBsnrIdByCode(rawData.BsnrCode)

		var issueDate, _ = util.ConvertStringToMillisecond(rawData.ScheinStart, dateFormat)
		var yearQuarter util.YearQuarter
		if rawData.YearQuarter != "" {
			G4101Quarter, err := strconv.ParseInt(rawData.YearQuarter[:1], 10, 64)
			yearQuarter.Quarter = int32(G4101Quarter)
			if err != nil {
				ctx.Logger().Error("Failed to ParseInt", "err", err.Error())
			}
			G4101Year, err := strconv.ParseInt(rawData.YearQuarter[1:], 10, 32)
			if err != nil {
				ctx.Logger().Error("Failed to ParseInt", "err", err.Error())
			}
			yearQuarter.Year = int32(G4101Year)
		} else {
			yearQuarter = util.ToYearQuarter(issueDate)
		}
		invoiceDate, _ := util.ConvertStringToMillisecond(rawData.InvoiceDate, dateFormat)
		scheinStatus := schein_common.ScheinStatus_Normal
		privateBillingStatus := private_common.PrivBillingStatus_NoInvoice
		if rawData.InvoiceDate != "" {
			scheinStatus = schein_common.ScheinStatus_Printed
			privateBillingStatus = private_common.PrivBillingStatus_UnPaid
		}
		if rawData.BillingDate != "" {
			scheinStatus = schein_common.ScheinStatus_Billed
			privateBillingStatus = private_common.PrivBillingStatus_Paid
		}
		feeAmount, _ := strconv.ParseFloat(rawData.FeeAmount, 64)

		var pInsurance private_common.InsuranceInfo
		if insuranceInfo := rawData.GetInsuranceInfo(ctx, rawData.Kassenname); insuranceInfo != nil {
			if existingInsurance := req.PatientInfo.GetInsuranceByName(insuranceInfo.InsuranceCompanyName); existingInsurance != nil {
				pInsurance = private_common.InsuranceInfo{
					Id:                   existingInsurance.Id,
					IkNumber:             existingInsurance.IkNumber,
					InsuranceCompanyName: existingInsurance.InsuranceCompanyName,
				}
			} else {
				pInsurance = private_common.InsuranceInfo{
					Id:                   insuranceInfo.Id,
					IkNumber:             insuranceInfo.IkNumber,
					InsuranceCompanyName: insuranceInfo.InsuranceCompanyName,
				}
				req.PatientInfo.InsuranceInfos = append(req.PatientInfo.InsuranceInfos, *insuranceInfo)
			}
		}

		// InvoiceExtra 4635[] where to import
		var privateContractGroupId uuid.UUID
		if contactnumber, err := strconv.ParseInt(rawData.InvoiceType, 10, 32); err == nil {
			privateContract, err := privateContactMemo(
				func() int64 {
					return contactnumber
				},
				func() (*private_contract_group_common.PrivateContractGroupItem, error) {
					return srv.privateContractGroupService.GetPrivateContractGroupByContractNumber(ctx, int32(contactnumber))
				},
			)
			if err != nil {
				ctx.Logger().Error("GetPrivateContractGroupByContractNumber failed", "err", err)
			}
			if privateContract == nil {
				ctx.Logger().Warn("No private contract group found", "contactnumber", contactnumber)
			} else {
				privateContractGroupId = privateContract.Id
			}
		}
		scheinId := util.NewUUID()

		newSchein := scheinRepo.ScheinRepo{
			Schein: schein_common.Schein{
				ScheinMainGroup: string(schein_common.PRIVATE),
				G4101Quarter:    util.NewPointer(yearQuarter.Quarter),
				G4101Year:       util.NewPointer(yearQuarter.Year),
				InsuranceId:     pInsurance.Id,
				IkNumber:        &pInsurance.IkNumber,
			},
			Id:        scheinId,
			DoctorId:  treatmentDoctorId,
			PatientId: patientId,
			PrivateScheinDetail: private_schein_common.PrivateScheinDetail{
				IssueDate:              issueDate,
				PrivateContractGroupId: privateContractGroupId,
				// Discount: ,
				DiscountUnit:       function.If(rawData.DiscountEuroUnit == "1", private_schein_common.DiscountUnit_Euro, private_schein_common.DiscountUnit_Percent),
				InvoiceSendingType: private_schein_common.InvoiceSendingType_Address,
				// ScheinMainGroup:     schein_common.PRIVATE,
				// MarkedAsBilled:      true, // assumed
				InvoiceNumber: &rawData.InvoiceNumber,
				// IsVat                 ,
				// IkNumber              ,
			},
			CreatedAt:        util.NowUnixMillis(ctx),
			CreatedBy:        util.GetPointerValue(ctx.UserInfo().UserUUID()),
			ScheinStatus:     scheinStatus,
			AssignedToBsnrId: bsnrId,
		}
		privateScheins = append(privateScheins, newSchein)

		createPrivateBillingRequest := private_billing.CreatePrivateBillingRequest{
			Item: &private_common.PrivateBillingRecord{
				Patient: private_common.Patient{
					PatientId:     patientId,
					FirstName:     patientInfo.GetFirstName(),
					LastName:      patientInfo.GetLastName(),
					DateOfBirth:   patientInfo.PersonalInfo.DateOfBirth,
					PatientNumber: patientInfo.GetPatientNumber(),
					FullName:      util.GetPatientName(patientInfo),
				},
				Doctor: private_common.Doctor{
					DoctorId:  treatmentDoctor.Id,
					FirstName: treatmentDoctor.FirstName,
					LastName:  treatmentDoctor.LastName,
					Initial:   treatmentDoctor.Initial,
					Title:     treatmentDoctor.Title,
					FullName:  util.GetDoctorName(treatmentDoctor),
				},
				PrivScheinId:  *scheinId,
				FeeAmount:     feeAmount,
				Reviewed:      false,
				Status:        privateBillingStatus,
				InvoiceDate:   &invoiceDate,
				InsuranceInfo: pInsurance,
				InvoiceNumber: rawData.InvoiceNumber,
				IsImported:    true,
			},
		}

		privateBillings = append(privateBillings, createPrivateBillingRequest)
		for _, Re5000 := range rawData.Di5000s {
			selectedDate, err := parseSelectedDate(ctx, Re5000.SelectedDate, dateFormat)
			if err != nil {
				ctx.Logger().Error("Failed to parse selectedDate", "err", err.Error())
			}

			for _, Di5001Obj := range Re5000.Di5001 {
				additionalInfos := []*patient_encounter.AdditionalInfoParent{}

				Di5002 := strings.Join(Di5001Obj.Di5002, "")
				Di5003 := strings.Join(Di5001Obj.Di5003, "")
				Di5009 := strings.Join(Di5001Obj.Di5009, "")
				Di5015 := strings.Join(Di5001Obj.Di5015, "")
				Di5017 := strings.Join(Di5001Obj.Di5017, "")

				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5002", Di5002)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5003", Di5003)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5005", Di5001Obj.Di5005)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5006", Di5001Obj.Di5006)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5008", Di5001Obj.Di5008)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5009", Di5009)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5010", Di5001Obj.Di5010)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5013", Di5001Obj.Di5013)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5015", Di5015)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5017", Di5017)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5098", Di5001Obj.Di5098)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5099", Di5001Obj.Di5099)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5101", Di5001Obj.Di5101)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5100", Di5001Obj.Di5100)
				if len(Di5001Obj.Di5012) != 0 {
					newAdditionalInfoChildren := []*patient_encounter.AdditionalInfoChild{}
					for _, Di5012Obj := range Di5001Obj.Di5012 {
						Di5011Value := strings.Join(Di5012Obj.Di5011, "")
						newAdditionalInfoChildren = append(newAdditionalInfoChildren, &patient_encounter.AdditionalInfoChild{
							FK:    "5011",
							Value: Di5011Value,
						})

						newAdditionalInfo := patient_encounter.AdditionalInfoParent{
							FK:       "5012",
							Value:    Di5012Obj.Di5012,
							Children: newAdditionalInfoChildren,
						}
						additionalInfos = append(additionalInfos, &newAdditionalInfo)
					}
				}

				timeLineServices = append(timeLineServices, timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
					Id:                util.NewUUID(),
					SelectedDate:      selectedDate,
					IsImported:        true,
					PatientId:         patientId,
					BillingDoctorId:   &treatmentDoctor.Id,
					TreatmentDoctorId: treatmentDoctor.Id,
					ScheinIds:         []uuid.UUID{util.GetPointerValue(newSchein.Id)},
					Type:              timeline_common.TimelineEntityType_Service,
					EncounterCase:     util.NewPointer(patient_encounter.AB),
					TreatmentCase:     util.NewPointer(patient_encounter.TreatmentCaseCustodian),
					Payload: patient_encounter.EncounterServiceTimeline{
						Code:            Di5001Obj.Di5001,
						FreeText:        fmt.Sprintf("(%s)", Di5001Obj.Di5001),
						AdditionalInfos: &additionalInfos,
						Scheins: &[]*common.ScheinWithMainGroup{
							{ScheinId: newSchein.Id, Group: common.MainGroup(newSchein.Schein.ScheinMainGroup)},
						},
						ServiceMainGroup: (*common.MainGroup)(&newSchein.Schein.ScheinMainGroup),
						Command:          "L",
					},
					CreatedBy:        createdById,
					AssignedToBsnrId: bsnrId,
					RecentAuditLogs: []timeline_repo.AuditLog{
						{
							AuditLogId: util.NewUUID(),
							UserId:     &createdById,
							Date:       selectedDate,
							ActionType: timeline_common.Add,
						},
					},
				})
			}
		}
		for _, Di6200Obj := range rawData.Di6200s {
			selectedDate, err := parseSelectedDate(ctx, Di6200Obj.SelectedDate, dateFormat)
			if err != nil {
				ctx.Logger().Error("Failed to parse selectedDate", "err", err.Error())
			}

			if Di6200Obj.Di6001.Di6001 != "" {
				timelineDiagnosis = append(timelineDiagnosis, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
					Id:                util.NewUUID(),
					SelectedDate:      selectedDate,
					IsImported:        true,
					Type:              timeline_common.TimelineEntityType_Diagnose,
					PatientId:         patientId,
					BillingDoctorId:   &treatmentDoctor.Id,
					TreatmentDoctorId: treatmentDoctor.Id,
					ScheinIds:         []uuid.UUID{util.GetPointerValue(newSchein.Id)},
					Payload: patient_encounter.EncounterDiagnoseTimeline{
						Code:        Di6200Obj.Di6001.Di6001,
						Description: Di6200Obj.Di6000,
						FreeText:    fmt.Sprintf("(%s) %s", Di6200Obj.Di6001.Di6001, Di6200Obj.Di6000),
						Certainty:   (*patient_encounter.Certainty)(&Di6200Obj.Di6001.Di6003),
						Laterality:  (*patient_encounter.Laterality)(&Di6200Obj.Di6001.Di6004),
						Explanation: strings.Join(Di6200Obj.Di6001.Di6006, "\n"),
						Exception:   strings.Join(Di6200Obj.Di6001.Di6008, "\n"),
						Command:     "D",
						Type:        patient_encounter.DIAGNOSETYPE_ACUTE,
					},
					CreatedBy:        createdById,
					AssignedToBsnrId: bsnrId,
					RecentAuditLogs: []timeline_repo.AuditLog{
						{
							AuditLogId: util.NewUUID(),
							UserId:     &createdById,
							Date:       selectedDate,
							ActionType: timeline_common.Add,
						},
					},
				})
			}
		}
	}
	var errorGroup function.ErrorGroupRecover
	errorGroup.Go(
		func() error {
			_, err := srv.patientProfileRepo.UpdatePatientProfileV2(ctx, patientId, *req.PatientInfo)
			return errors.WithMessage(err, "Error updating patient info")
		},

		func() error {
			_, err := srv.scheinRepo.CreateMany(ctx, privateScheins)
			return errors.WithMessage(err, "Error creating private schein")
		},

		func() error {
			_, err := srv.privBillingRepo.CreateManyPrivateBillingWithRawData(ctx, privateBillings)
			return errors.WithMessage(err, "Error creating private billing")
		},

		func() error {
			_, err := srv.timelineDiagnoseRepo.CreateMany(ctx, timelineDiagnosis)
			return errors.WithMessage(err, "Error creating timeline diagnose")
		},

		func() error {
			_, err := srv.timelineServiceRepo.CreateMany(ctx, timeLineServices)
			return errors.WithMessage(err, "Error creating timeline services")
		},
	)
	if err := errorGroup.Wait(); err != nil {
		return errors.WithMessage(err, "error in group")
	}
	return nil
}

func (srv *BDTService) importBGSchein(ctx *titan.Context, req ImportBGScheinRequest) error {
	if len(req.BGSchein) == 0 {
		return errors.New("bg schein is empty")
	}
	now := time.Now()
	patientId := util.GetPointerValue(req.PatientId)
	defer func() {
		ctx.Logger().Info("importBGSchein",
			"patientBdtNumber", req.BGSchein[0].PatientNumber,
			"patientId", patientId,
			"duration", time.Since(now).String())
	}()
	dataContext := req.DataContext
	patientInfo := req.PatientInfo
	dateFormat := dataContext.Config.DateTimeFormat
	timelineDiagnosis := []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{}
	timeLineServices := []timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{}
	BGBillings := []bg_billing.CreateBgBillingRequest{}
	BGScheins := []scheinRepo.ScheinRepo{}

	for _, rawData := range req.BGSchein {
		var createdById uuid.UUID
		createdBy := req.DataContext.SearchDoctorByLanr(rawData.Lanr)
		if createdBy != nil {
			createdById = createdBy.Id
		}
		bsnrId := createdBy.GetBsnrIdByCode(rawData.BsnrCode)

		var issueDate, _ = util.ConvertStringToMillisecond(rawData.ScheinStart, dateFormat)
		var yearQuarter util.YearQuarter
		if rawData.YearQuarter != "" {
			G4101Quarter, err := strconv.ParseInt(rawData.YearQuarter[:1], 10, 64)
			yearQuarter.Quarter = int32(G4101Quarter)
			if err != nil {
				ctx.Logger().Error("Failed to ParseInt", "err", err.Error())
			}
			G4101Year, err := strconv.ParseInt(rawData.YearQuarter[1:], 10, 32)
			if err != nil {
				ctx.Logger().Error("Failed to ParseInt", "err", err.Error())
			}
			yearQuarter.Year = int32(G4101Year)
		} else {
			yearQuarter = util.ToYearQuarter(issueDate)
		}

		var endDate, _ = util.ConvertStringToMillisecond(rawData.ScheinEnd, dateFormat)
		accidentTimeInt, _ := util.ConvertStringToMillisecond(rawData.AccidentDate+rawData.AccidentTime, dateFormat+util.HHMM, ctx.RequestTimeZone())
		arrivalTimeInt, _ := util.ConvertStringToMillisecond(rawData.ArrivedDate+rawData.ArrivedTime, dateFormat+util.HHMM, ctx.RequestTimeZone())
		fileNumberStr := rawData.FileNumberStr
		invoiceDate, _ := util.ConvertStringToMillisecond(rawData.InvoiceDate, dateFormat)
		workingTimeStart, _ := util.ConvertStringToMillisecond(rawData.StartOfWorkingTime, util.HHMM, ctx.RequestTimeZone())
		workingTimeEnd, _ := util.ConvertStringToMillisecond(rawData.EndWorkingTime, util.HHMM, ctx.RequestTimeZone())
		scheinStatus := schein_common.ScheinStatus_Normal
		if rawData.InvoiceDate != "" {
			scheinStatus = schein_common.ScheinStatus_Printed
		}
		if rawData.BillingDate != "" {
			scheinStatus = schein_common.ScheinStatus_Billed
		}
		feeAmount, _ := strconv.ParseFloat(rawData.FeeAmount, 64)
		bgBillingStatus := bg_common.BillingStatus_NoInvoice
		if rawData.InvoiceDate != "" {
			scheinStatus = schein_common.ScheinStatus_Printed
			bgBillingStatus = bg_common.BillingStatus_UnPaid
		}
		if rawData.BillingDate != "" {
			scheinStatus = schein_common.ScheinStatus_Billed
			bgBillingStatus = bg_common.BillingStatus_Paid
		}

		scheinId := util.NewUUID()
		var treatmentDoctorId uuid.UUID
		treatmentDoctor := req.DataContext.SearchTreatmentDoctor(rawData.TreatmentDoctor)
		if treatmentDoctor != nil {
			treatmentDoctorId = treatmentDoctor.Id
			treatmentDoctor = &bdt_model.BDTEmployee{}
		}

		ikNumber := rawData.IkNumber
		if ikNumber == "" {
			ikNumber = BG_IK_NUMBER_EMPTY
		}

		bgInsurance, err := srv.createBgInsurance(ctx, ikNumber)
		if err != nil {
			return errors.WithMessage(err, "Failed to create bg insurance")
		}

		if bgInsurance == nil {
			return errors.New("bg insurance is nil")
		}

		insuranceId, err := uuid.Parse(bgInsurance.BgInsuranceId)
		if err != nil {
			return errors.WithMessage(err, "Failed to parse insurance id")
		}

		ikNumberInt64, err := strconv.ParseInt(bgInsurance.IkNumber, 10, 32)
		if err != nil {
			return errors.WithMessage(err, "Failed to parse ik number")
		}

		ikNumberInt32 := int32(ikNumberInt64)
		newSchein := scheinRepo.ScheinRepo{
			Schein: schein_common.Schein{
				InsuranceId:     insuranceId,
				ScheinMainGroup: string(schein_common.BG),
				G4101Quarter:    util.NewPointer(yearQuarter.Quarter),
				G4101Year:       util.NewPointer(yearQuarter.Year),
				IkNumber:        util.NewPointer(ikNumberInt32),
			},
			Id:        scheinId,
			DoctorId:  treatmentDoctorId,
			PatientId: patientId,
			BgScheinDetail: schein_common.BgScheinDetail{
				CreatedOn:        issueDate,
				EndDate:          &endDate,
				AccidentDate:     accidentTimeInt,
				ArrivalDate:      &arrivalTimeInt,
				FileNumberStr:    &fileNumberStr,
				WorkingTimeStart: workingTimeStart,
				WorkingTimeEnd:   workingTimeEnd,
				BGType:           getBGType(rawData.InvoiceType),
				InvoiceNumber:    &rawData.InvoiceNumber,
				EmploymentInfo:   patientInfo.EmploymentInfo,
			},
			CreatedAt:        util.NowUnixMillis(ctx),
			CreatedBy:        treatmentDoctor.Id,
			ScheinStatus:     scheinStatus,
			AssignedToBsnrId: bsnrId,
		}
		BGScheins = append(BGScheins, newSchein)

		insuranceInfo := &patient_profile_common.InsuranceInfo{
			Id:                   insuranceId,
			InsuranceType:        patient_profile_common.BG,
			InsuranceNumber:      &rawData.InsuranceNumber,
			IkNumber:             ikNumberInt32,
			InsuranceCompanyName: bgInsurance.InsuranceName,
		}

		existedInsurance := slice.FindOne(patientInfo.InsuranceInfos, func(insurance patient_profile_common.InsuranceInfo) bool {
			return util.GetPointerValue(insurance.InsuranceNumber) == rawData.InsuranceNumber
		})
		if existedInsurance == nil {
			patientInfo.InsuranceInfos = append(patientInfo.InsuranceInfos, *insuranceInfo)
		}

		createBgBillingRequest := bg_billing.CreateBgBillingRequest{
			Item: &bg_common.BillingRecord{
				Patient: bg_common.Patient{
					PatientId:     patientId,
					FirstName:     patientInfo.GetFirstName(),
					LastName:      patientInfo.GetLastName(),
					DateOfBirth:   patientInfo.PersonalInfo.DateOfBirth,
					PatientNumber: patientInfo.GetPatientNumber(),
					FullName:      util.GetPatientName(patientInfo),
				},
				Doctor: bg_common.Doctor{
					DoctorId:  treatmentDoctorId,
					FirstName: treatmentDoctor.FirstName,
					LastName:  treatmentDoctor.LastName,
					Initial:   treatmentDoctor.Initial,
					Title:     treatmentDoctor.Title,
					FullName:  util.GetDoctorName(treatmentDoctor),
				},
				ScheinId:      *scheinId,
				Status:        bgBillingStatus,
				InvoiceDate:   &invoiceDate,
				InsuranceInfo: insuranceInfo,
				PaidAmount:    feeAmount,
				IsImported:    true,
			},
		}

		BGBillings = append(BGBillings, createBgBillingRequest)
		for _, Re5000 := range rawData.Di5000s {
			selectedDate, err := parseSelectedDate(ctx, Re5000.SelectedDate, dateFormat)
			if err != nil {
				ctx.Logger().Error("Failed to parse selectedDate", "err", err.Error())
			}

			for _, Di5001Obj := range Re5000.Di5001 {
				additionalInfos := []*patient_encounter.AdditionalInfoParent{}

				Di5002 := strings.Join(Di5001Obj.Di5002, "")
				Di5003 := strings.Join(Di5001Obj.Di5003, "")
				Di5009 := strings.Join(Di5001Obj.Di5009, "")
				Di5015 := strings.Join(Di5001Obj.Di5015, "")
				Di5017 := strings.Join(Di5001Obj.Di5017, "")

				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5002", Di5002)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5003", Di5003)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5005", Di5001Obj.Di5005)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5006", Di5001Obj.Di5006)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5008", Di5001Obj.Di5008)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5009", Di5009)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5010", Di5001Obj.Di5010)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5013", Di5001Obj.Di5013)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5015", Di5015)
				additionalInfos = addAdditionalInfoIfExistValue(additionalInfos, "5017", Di5017)
				if len(Di5001Obj.Di5012) != 0 {
					newAdditionalInfoChildren := []*patient_encounter.AdditionalInfoChild{}
					for _, Di5012Obj := range Di5001Obj.Di5012 {
						Di5011Value := strings.Join(Di5012Obj.Di5011, "")
						newAdditionalInfoChildren = append(newAdditionalInfoChildren, &patient_encounter.AdditionalInfoChild{
							FK:    "5011",
							Value: Di5011Value,
						})

						newAdditionalInfo := patient_encounter.AdditionalInfoParent{
							FK:       "5012",
							Value:    Di5012Obj.Di5012,
							Children: newAdditionalInfoChildren,
						}
						additionalInfos = append(additionalInfos, &newAdditionalInfo)
					}
				}

				timeLineServices = append(timeLineServices, timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
					Id:                util.NewUUID(),
					SelectedDate:      selectedDate,
					IsImported:        true,
					PatientId:         patientId,
					BillingDoctorId:   &treatmentDoctor.Id,
					TreatmentDoctorId: treatmentDoctor.Id,
					ScheinIds:         []uuid.UUID{util.GetPointerValue(newSchein.Id)},
					Type:              timeline_common.TimelineEntityType_Service,
					EncounterCase:     util.NewPointer(patient_encounter.AB),
					TreatmentCase:     util.NewPointer(patient_encounter.TreatmentCaseCustodian),
					Payload: patient_encounter.EncounterServiceTimeline{
						Code:            Di5001Obj.Di5001,
						FreeText:        fmt.Sprintf("(%s)", Di5001Obj.Di5001),
						AdditionalInfos: &additionalInfos,
						Scheins: &[]*common.ScheinWithMainGroup{
							{ScheinId: newSchein.Id, Group: common.MainGroup(newSchein.Schein.ScheinMainGroup)},
						},
						ServiceMainGroup: (*common.MainGroup)(&newSchein.Schein.ScheinMainGroup),
						Command:          "L",
					},
					CreatedBy:        createdById,
					AssignedToBsnrId: bsnrId,
					RecentAuditLogs: []timeline_repo.AuditLog{
						{
							AuditLogId: util.NewUUID(),
							UserId:     &createdById,
							Date:       selectedDate,
							ActionType: timeline_common.Add,
						},
					},
				})
			}
		}
		for _, Di6200Obj := range rawData.Di6200s {
			selectedDate, err := time.ParseInLocation(dateFormat, Di6200Obj.SelectedDate, ctx.RequestTimeZone())
			if err != nil {
				ctx.Logger().Error("Failed to parse selectedDate", "err", err.Error())
			}

			if Di6200Obj.Di6001.Di6001 != "" {
				timelineDiagnosis = append(timelineDiagnosis, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
					Id:                util.NewUUID(),
					SelectedDate:      selectedDate,
					IsImported:        true,
					Type:              timeline_common.TimelineEntityType_Diagnose,
					PatientId:         patientId,
					BillingDoctorId:   &treatmentDoctor.Id,
					TreatmentDoctorId: treatmentDoctor.Id,
					ScheinIds:         []uuid.UUID{util.GetPointerValue(newSchein.Id)},
					Payload: patient_encounter.EncounterDiagnoseTimeline{
						Code:        Di6200Obj.Di6001.Di6001,
						Description: Di6200Obj.Di6000,
						FreeText:    fmt.Sprintf("(%s) %s", Di6200Obj.Di6001.Di6001, Di6200Obj.Di6000),
						Certainty:   (*patient_encounter.Certainty)(&Di6200Obj.Di6001.Di6003),
						Laterality:  (*patient_encounter.Laterality)(&Di6200Obj.Di6001.Di6004),
						Explanation: strings.Join(Di6200Obj.Di6001.Di6006, "\n"),
						Exception:   strings.Join(Di6200Obj.Di6001.Di6008, "\n"),
						Command:     "D",
						Type:        patient_encounter.DIAGNOSETYPE_ACUTE,
					},
					CreatedBy:        createdById,
					AssignedToBsnrId: bsnrId,
					RecentAuditLogs: []timeline_repo.AuditLog{
						{
							AuditLogId: util.NewUUID(),
							UserId:     &createdById,
							Date:       selectedDate,
							ActionType: timeline_common.Add,
						},
					},
				})
			}
		}
	}
	var errorGroup function.ErrorGroupRecover
	errorGroup.Go(
		func() error {
			_, err := srv.patientProfileRepo.UpdatePatientProfileV2(ctx, patientId, *patientInfo)
			return errors.WithMessage(err, "Error updating patient profile")
		},

		func() error {
			_, err := srv.scheinRepo.CreateMany(ctx, BGScheins)
			return errors.WithMessage(err, "Error creating private schein")
		},

		func() error {
			_, err := srv.bgBillingRepo.CreateManyBgBillingWithRawData(ctx, BGBillings)
			return errors.WithMessage(err, "Error creating bg billing")
		},

		func() error {
			_, err := srv.timelineDiagnoseRepo.CreateMany(ctx, timelineDiagnosis)
			return errors.WithMessage(err, "Error creating timeline diagnose")
		},

		func() error {
			_, err := srv.timelineServiceRepo.CreateMany(ctx, timeLineServices)
			return errors.WithMessage(err, "Error creating timeline services")
		},
	)
	if err := errorGroup.Wait(); err != nil {
		return errors.WithMessage(err, "error in group")
	}
	return nil
}

func (srv *BDTService) FindExistedPatient(ctx *titan.Context, insuranceNumber string, patientInfo *patient_profile_common.PatientInfo) (*patient.PatientProfile, error) {
	existedPatient, err := srv.patientProfileBff.GetPatientProfileByInsuranceNumberV2(ctx, insuranceNumber)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patient profile by insurance number")
	}

	// Should exists only 1 patient with the same insurance number
	if len(existedPatient) != 1 {
		return nil, nil
	}

	p := existedPatient[0]
	if p.PatientInfo == nil {
		return nil, nil
	}

	comparingPatientInfo := p.PatientInfo
	matchedInsurance := slice.FindOne(comparingPatientInfo.InsuranceInfos, func(insurance patient_profile_common.InsuranceInfo) bool {
		return util.GetPointerValue(insurance.InsuranceNumber) == insuranceNumber
	})
	if matchedInsurance == nil {
		return nil, nil
	}

	isReadCardInCurrentQuarter := matchedInsurance.GetCurrentReadCardModel(ctx) != nil
	if isReadCardInCurrentQuarter {
		return nil, nil
	}

	if !patientInfo.IsMatchedDOB(comparingPatientInfo.PersonalInfo.DateOfBirth) {
		return nil, nil
	}

	if !patientInfo.IsMatchedFirstNameAndLastName(comparingPatientInfo.PersonalInfo) {
		return nil, nil
	}

	return &p, nil
}

func (srv *BDTService) handleImportPatient(ctx *titan.Context, req *HandleImportPatient) error {
	insuranceNumber := req.PatientData.InsuranceNumber
	bdtNumber := req.PatientData.PatientNumber
	patientInfo, err := req.PatientData.ToPatientInfo(ctx, req.DataContext)
	if err != nil {
		return errors.WithMessage(err, "failed to convert to patient info")
	}

	if patientInfo == nil {
		return errors.New("patient info is empty")
	}

	patientId := req.DataContext.PatientIdMap[bdtNumber]
	// In case of more than 1 bdt, we need to check if the patient already exists
	if insuranceNumber != "" {
		existedPatient, err := srv.FindExistedPatient(ctx, insuranceNumber, patientInfo)
		if err != nil {
			return errors.WithMessage(err, "failed to get patient profile by insurance number")
		}
		if existedPatient != nil {
			req.DataContext.PatientIdMap[bdtNumber] = existedPatient.Id
			patientId = existedPatient.Id
			patientInfo = existedPatient.PatientInfo
			if err := writeLog(ctx, []*patient_profile_common.PatientInfo{
				existedPatient.PatientInfo,
				patientInfo,
			}); err != nil {
				return errors.WithStack(err)
			}
		}
	}

	if patientId == nil {
		res, err := srv.importPatient(ctx, patientInfo)
		if err != nil {
			return err
		}
		patientInfo = res.PatientInfo
		patientId = res.Id
		req.DataContext.PatientIdMap[bdtNumber] = res.Id
	}

	if err := srv.handleImportSchein(ctx, &handleImportScheinRequest{
		DataContext: req.DataContext,
		PatientInfo: patientInfo,
		PatientId:   patientId,
		BdtNumber:   bdtNumber,
	}); err != nil {
		return errors.WithStack(err)
	}

	if err := srv.createPatientMedicalData(ctx, &CreatePatientMedicalDataRequest{
		PatientId:   patientId,
		PatientData: req.PatientData,
	}); err != nil {
		return errors.WithStack(err)
	}

	// import timeline diagnose DD
	dateFormat := req.DataContext.Config.DateTimeFormat
	timelineDiagnosis := []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{}
	for _, Di3649Obj := range req.PatientData.Di3649 {
		var createdById uuid.UUID
		createdBy := req.DataContext.SearchDoctorByLanr(Di3649Obj.Lanr)
		if createdBy != nil {
			createdById = createdBy.Id
		}
		bsnrId := createdBy.GetBsnrIdByCode(Di3649Obj.BsnrCode)
		selectedDate, err := parseSelectedDate(ctx, Di3649Obj.SelectedDate, dateFormat)
		if err != nil {
			ctx.Logger().Error("Failed to parse selectedDate", "err", err.Error())
		}

		timelineDiagnosis = append(timelineDiagnosis, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
			Id:                util.NewUUID(),
			SelectedDate:      selectedDate,
			IsImported:        true,
			Type:              timeline_common.TimelineEntityType_Diagnose,
			PatientId:         *patientId,
			BillingDoctorId:   &createdById,
			TreatmentDoctorId: createdById,
			ScheinIds:         []uuid.UUID{},
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Code:        Di3649Obj.Di6001.Di6001,
				Description: Di3649Obj.Di3650,
				FreeText:    fmt.Sprintf("(%s) %s", Di3649Obj.Di6001.Di6001, Di3649Obj.Di3650),
				Certainty:   (*patient_encounter.Certainty)(&Di3649Obj.Di6001.Di6003),
				Laterality:  (*patient_encounter.Laterality)(&Di3649Obj.Di6001.Di6004),
				Explanation: strings.Join(Di3649Obj.Di6001.Di6006, "\n"),
				Exception:   strings.Join(Di3649Obj.Di6001.Di6008, "\n"),
				Command:     function.If(Di3649Obj.Un0830 == "2", "AD", "DD"),
				Type:        patient_encounter.DIAGNOSETYPE_PERMANENT,
			},
			CreatedBy:        createdById,
			AssignedToBsnrId: bsnrId,
			RecentAuditLogs: []timeline_repo.AuditLog{
				{
					AuditLogId: util.NewUUID(),
					UserId:     &createdById,
					Date:       selectedDate,
					ActionType: timeline_common.Add,
				},
			},
		})
	}
	if _, err := srv.timelineDiagnoseRepo.CreateMany(ctx, timelineDiagnosis); err != nil {
		ctx.Logger().Error("Failed to create timeline diagnose", "err", err.Error())
	}

	return nil
}

func (srv *BDTService) createPatientMedicalData(ctx *titan.Context, req *CreatePatientMedicalDataRequest) error {
	patientMedicalData := req.PatientData.ToPatientMedicalData()
	if patientMedicalData == nil {
		return nil
	}

	patientMedicalDataRequest := &profile.UpdatePatientMedicalDataRequest{
		PatientId:          req.PatientId,
		PatientMedicalData: patientMedicalData,
	}
	if _, err := srv.patientProfileServiceClient.CreatePatientMedicalData(ctx, patientMedicalDataRequest); err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func fileExists(filename string) bool {
	info, err := os.Stat(filename)
	if os.IsNotExist(err) {
		return false
	}
	return !info.IsDir()
}

type PatientDataDuplicated struct {
	FirstName        string
	LastName         string
	DOB              int64
	InsuranceNumbers []string
}

func writeLog(ctx *titan.Context, patientInfos []*patient_profile_common.PatientInfo) error {
	careProviderId := ctx.UserInfo().CareProviderId.String()
	logFileName := fmt.Sprintf("%s_duplicated_patients_data", careProviderId)
	logPath := filepath.Join(BaseDir, logFileName)
	if !fileExists(logPath) {
		if err := os.MkdirAll(BaseDir, os.ModePerm); err != nil {
			return errors.WithMessage(err, "failed to create directory")
		}

		if _, err := os.Create(logPath); err != nil {
			return errors.WithMessage(err, "failed to create file")
		}
	}

	file, err := os.OpenFile(logPath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return errors.WithMessage(err, "failed to open file")
	}

	defer file.Close()

	patientsDataDuplicated := []PatientDataDuplicated{}
	for _, patientInfo := range patientInfos {
		patientsDataDuplicated = append(patientsDataDuplicated, PatientDataDuplicated{
			FirstName: patientInfo.PersonalInfo.FirstName,
			LastName:  patientInfo.PersonalInfo.LastName,
			DOB:       patientInfo.PersonalInfo.DOB,
			InsuranceNumbers: slice.Map(patientInfo.InsuranceInfos, func(insurance patient_profile_common.InsuranceInfo) string {
				return util.GetPointerValue(insurance.InsuranceNumber)
			}),
		})
	}
	writer := bufio.NewWriter(file)

	// encode json data
	encoder := json.NewEncoder(writer)
	if err := encoder.Encode(patientsDataDuplicated); err != nil {
		return errors.WithMessage(err, "failed to encode json data")
	}

	// write to file
	if err := writer.Flush(); err != nil {
		return errors.WithMessage(err, "failed to write to file")
	}

	return nil
}

func (srv *BDTService) uploadLog(ctx *titan.Context) error {
	files, err := os.ReadDir(BaseDir)
	if err != nil {
		return err
	}

	if len(files) == 0 {
		return nil
	}

	for _, file := range files {
		if file.IsDir() {
			continue
		}

		fileName := file.Name()
		if !strings.Contains(fileName, "duplicated_patients_data") {
			continue
		}

		filePath := filepath.Join(BaseDir, file.Name())
		fileContent, err := os.ReadFile(filePath)
		if err != nil {
			return err
		}

		if len(fileContent) == 0 {
			continue
		}

		if _, err := srv.minioClient.PutObject(ctx, "bucket-tmp", file.Name(), bytes.NewReader(fileContent), int64(len(fileContent)), minio.PutObjectOptions{}); err != nil {
			return err
		}

		if err := os.Remove(filePath); err != nil {
			return err
		}
	}

	return nil
}

func (srv *BDTService) getContractsAndHavgVpIdOfEmployee(ctx *titan.Context, havgId, bsnrCode string) (contracts []*employeebff.Contract, havgVpId string) {
	if havgId != "" {
		havgVpIdResponse, err := srv.hpmRestService.GetHavgVpId(ctx, &hpm_rest_client.GetHavgVpIdRequest{
			HavgId: havgId,
		})
		if err != nil {
			ctx.Logger().Error("Failed to GetHavgVpId", "err", err.Error())
		}

		if havgVpIdResponse != nil {
			havgVpId = havgVpIdResponse.HavgVpId
			res, err := srv.hpmRestService.GetParticipationContracts(ctx, havgVpId, bsnrCode)
			if err != nil {
				ctx.Logger().Error("Failed to GetParticipationContracts", "err", err.Error())
			}

			if res != nil {
				contractIds := slice.Map(res.Data, func(i hpm_rest_client.ParticipationContract) model.ContractId {
					return i.ContractId
				})
				tmpContracts := srv.contractService.GetContractByIds(contractIds)
				contracts = slice.Map(res.Data, func(i hpm_rest_client.ParticipationContract) *employeebff.Contract {
					contract := slice.FindOne(tmpContracts, func(c model.Contract) bool {
						return c.Id == i.ContractId
					})

					return &employeebff.Contract{
						ContractId:            i.ContractId,
						ChargeSystemId:        i.ChargeSystemId,
						Type:                  common.ContractType_HouseDoctorCare,
						StartDate:             i.StartDate,
						EndDate:               &i.EndDate,
						EnrollmentTypeOptions: contract.GetEnrollmentTypeOptions(),
					}
				})
			}
		}
	}

	return contracts, havgVpId
}

func (srv *BDTService) importActionChain(ctx *titan.Context, raw bdt_model.ActionChain) error {
	now := time.Now()
	defer func() {
		ctx.Logger().Info("importActionChain",
			"duration", time.Since(now).String())
	}()
	var actionChain []action_chain_repo.ActionChain
	for _, v := range raw.Abbreviation {
		actionChain = append(actionChain, action_chain_repo.ActionChain{
			Id:          util.NewUUID(),
			Name:        v.Name,
			Description: strings.Join(v.Diagnoses, "\n"),
			Status:      action_chain_common.ActionChainStatus_Active,
			Steps: []action_chain_common.ActionChainStep{
				{
					Diagnose: action_chain_common.DiagnoseStep{
						Diagnose: &patientfile.DiagnoseResponse{
							Code:        v.ICD.Code,
							Type:        patientfile.DIAGNOSETYPE_ACUTE,
							Certainty:   util.NewPointer(patientfile.Certainty(v.ICD.Certainty)),
							Laterality:  util.NewPointer(patientfile.Laterality(v.ICD.Laterality)),
							RunSdkrw:    patientfile.RUNSDKRWENUM_DEFAULT,
							Command:     "D",
							Explanation: strings.Join(v.ICD.Explain, "\n"),
						},
					},
					StepCategory: action_chain_common.ActionChainCategory_AcuteDiagnose,
				},
			},
		})
	}
	if len(actionChain) == 0 {
		ctx.Logger().Info("No action chain was found")
		return nil
	}
	if _, err := srv.actionChainRepo.CreateMany(ctx, actionChain); err != nil {
		ctx.Logger().Error("Failed to CreateMany action chain", "err", err.Error())
		return err
	}

	return nil
}

func (srv *BDTService) importTextModule(ctx *titan.Context, raw bdt_model.TextModule) error {
	now := time.Now()
	defer func() {
		ctx.Logger().Info("importTextModule",
			"duration", time.Since(now).String())
	}()
	var textModules []text_module_repo.TextModule
	mShortCut := make(map[string]int)
	for _, v := range raw.ShortCuts {
		shortCutText := v.ShortCutText
		if v, ok := mShortCut[v.ShortCutText]; ok {
			shortCutText = fmt.Sprintf("%s_v%d", shortCutText, v+1)
		}

		mShortCut[v.ShortCutText]++
		content := strings.ReplaceAll(strings.Join(v.Contents, "\n"), "#", "%param%")
		textModules = append(textModules, text_module_repo.TextModule{
			Id:           util.NewPointer(uuid.New()),
			UseFor:       getTextModuleType(v.Type),
			TextShortcut: shortCutText,
			ModuleType:   text_module_common.ModuleType_FreeText,
			Content: text_module_common.TextModuleContent{
				Data: []text_module_common.TextModuleNode{
					{
						Type: text_module_common.TextModuleNodeType_Text,
						Text: &text_module_common.TextModuleTextNode{
							Value: content,
						},
					},
				},
				Text: content,
			},
			Status: text_module_common.TextModuleStatus_Active,
		})
	}

	if len(textModules) == 0 {
		ctx.Logger().Info("No text module was found")
		return nil
	}

	if _, err := srv.textModuleRepo.CreateMany(ctx, textModules); err != nil {
		ctx.Logger().Error("Failed to CreateMany text module", "err", err.Error())
		return err
	}

	return nil
}

func (srv *BDTService) getOnlyOneScheinIdInQuarter(ctx *titan.Context, patientId uuid.UUID, selectedDate int64) (*uuid.UUID, error) {
	scheins, err := srv.scheinRepo.GetScheinInQuarterBySelectedDate(ctx, selectedDate, patientId)
	if err != nil {
		return nil, err
	}

	if len(scheins) > 1 {
		return nil, errors.New("more than one schein in quarter")
	} else if len(scheins) == 0 {
		return nil, errors.New("no schein in quarter")
	}
	return scheins[0].Id, nil
}

func (srv *BDTService) createBgInsurance(ctx *titan.Context, ikNumber string) (*catalog_bg_insurance_common.BGInsuranceCatalog, error) {
	foundBgInsurance, err := srv.catalogBgInsuranceService.GetBGInsuranceByIkNumber(ctx, &catalog_bg_insurance.GetBGInsuranceByIkNumberRequest{
		IkNumber: ikNumber,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to get bg insurance by ik number")
	}

	payload := &catalog_bg_insurance_common.BGInsuranceCatalog{}
	if foundBgInsurance != nil && foundBgInsurance.BgInsurance != nil {
		if foundBgInsurance.BgInsurance.Source == catalog_utils_common.SelfCreated {
			return foundBgInsurance.BgInsurance, nil
		}

		err := copier.Copy(payload, foundBgInsurance.BgInsurance)
		if err != nil {
			return nil, errors.WithMessage(err, "Failed to copy bg insurance")
		}
	} else {
		payload.IkNumber = ikNumber
	}

	payload.BgInsuranceId = uuid.NewString()
	payload.Source = catalog_utils_common.SelfCreated
	res, err := srv.catalogBgInsuranceService.CreateBGInsurance(ctx, &catalog_bg_insurance.CreateBGInsuranceRequest{
		BgInsurance: payload,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to create bg insurance")
	}

	if res == nil {
		return nil, errors.New("bg insurance is empty")
	}

	return res.BgInsurance, nil
}
