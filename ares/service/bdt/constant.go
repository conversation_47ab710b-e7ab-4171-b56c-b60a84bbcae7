package bdt_service

const MAX_PATIENT_ID_LENGTH = 10

const GESCHLECHT_M = "M"
const GESCHLECHT_W = "W"

// form code name
const MUSTER_FORM_10A_NAME = "ANFLABOR"
const MUSTER_FORM_4_2019_NAME = "KRBEFOERD_2019"
const MUSTER_FORM_4_NAME = "KRBEFOERD_2020"
const MUSTER_FORM_63_NAME = "SAPV"
const MUSTER_FORM_56_NAME = "REHASPORT"
const MUSTER_FORM_39_NAME = "KREBSMANN"
const HEILMITTEL_FORM_NAME = "HEILMITTEL"
const PHYSIK_HEILMITTEL_FORM_NAME = "HMPHYSIK"
const ERGO_HEILMITTEL_FORM_NAME = "HMERGO"
const STIMME_HEILMITTEL_FORM_NAME = "HMSTIMME"
const PTV1_FORM_NAME = "PTV1"
const MUSTER_FORM_10_NAME = "UEBLABOR"
const MUSTER_FORM_36_NAME = "EMPFEHLUNG"
const MUSTER_FORM_52_NAME = "AUWEITER"
const MUSTER_FORM_55_NAME = "CHRONERKRANK"
const MUSTER_FORM_61_NAME = "REHABERATUNG"
const MUSTER_FORM_61_B_NAME = "REHAVERORDNUNG"
const MUSTER_FORM_64_NAME = "VORSORGEMV"
const MUSTER_FORM_22_NAME = "KONSILIARPT"
const MUSTER_FORM_65_NAME = "ATTESTKIND"
const MUSTER_FORM_20_NAME = "EINGLPLAN"
const MUSTER_FORM_12_2020 = "HKPFLEGE_2020"
const MUSTER_FORM_12_2017 = "HKPFLEGE_2017"
const MUSTER_FORM_19_NAME = "NOTFALL"
const MUSTER_FORM_F1050 = "AARZT"

// description of form name ( we use for now cause the version of xbdt file return both code and description)
const MUSTER_FORM_10A_DETAIL_NAME = "Anforderungsschein für Laboruntersuchungen bei Laborgemeinschaften"
const MUSTER_FORM_4_DETAIL_NAME = "Verordnung einer Krankenbeförderung"
const MUSTER_FORM_63_DETAIL_NAME = "Verordnung spezialisierter ambulanter Palliativversorgung (SAPV)"
const MUSTER_FORM_56_DETAIL_NAME = "Antrag auf Kostenübernahme"
const MUSTER_FORM_39_DETAIL_NAME = "Krebsfrüherkennung Männer"
const HEILMITTEL_FORM_DETAIL_NAME = "Heilmittelverordnung"
const PTV1_FORM_DETAIL_NAME = "PTV 1 - Antrag des Versicherten auf Psychotherapie"
const MUSTER_FORM_10_DETAIL_NAME = "Überweisungsschein für Labor als Auftragsleistung"
const MUSTER_FORM_36_DETAIL_NAME = "Empfehlung zur verhaltensbezogenen Primärprävention"
const MUSTER_FORM_52_DETAIL_NAME = "Bericht für die Krankenkasse bei Fortbestehen der Arbeitsunfähigkeit"
const MUSTER_FORM_55_DETAIL_NAME = "Bescheinigung einer schwerwiegenden chronischen Erkrankung"
const MUSTER_FORM_61_DETAIL_NAME = "Bescheinigung einer schwerwiegenden chronischen Erkrankung"
const MUSTER_FORM_61_B_DETAIL_NAME = "Verordnung von medizinischer Rehabilitation"
const MUSTER_FORM_64_DETAIL_NAME = "Verordnung von medizinischer Vorsorge für Mütter oder Väter"
const MUSTER_FORM_22_DETAIL_NAME = "Konsiliarbericht vor Aufnahme einer Psychotherapie"
const MUSTER_FORM_65_DETAIL_NAME = "Ärztliches Attest Kind M 65"
const MUSTER_FORM_20_DETAIL_NAME = "Maßnahmen zur stufenweisen Wiedereingliederung in das Erwerbsleben"
const MUSTER_FORM_12_DETAIL_NAME = "Verordnung häuslicher Krankenpflege"
const MUSTER_FORM_19_DETAIL_NAME = "Notfall- / Vertretungsschein"
const MUSTER_FORM_F1050_DETAIL_NAME = "Ärztliche Unfallmeldung"

// BG IKNumber
const BG_IK_NUMBER_EMPTY = "000000000"

var MusterFormNames = []string{
	MUSTER_FORM_4_NAME,
	MUSTER_FORM_4_2019_NAME,
	MUSTER_FORM_10A_NAME,
	MUSTER_FORM_63_NAME,
	MUSTER_FORM_56_NAME,
	MUSTER_FORM_39_NAME,
	PTV1_FORM_NAME,
	MUSTER_FORM_10A_NAME,
	MUSTER_FORM_36_NAME,
	MUSTER_FORM_52_NAME,
	MUSTER_FORM_55_NAME,
	MUSTER_FORM_61_NAME,
	MUSTER_FORM_61_B_NAME,
	MUSTER_FORM_22_NAME,
	MUSTER_FORM_65_NAME,
	MUSTER_FORM_20_NAME,
	MUSTER_FORM_12_2020,
	MUSTER_FORM_12_2017,
	MUSTER_FORM_19_NAME,
	MUSTER_FORM_F1050,
}

var MusterFormDetailNames = []string{
	MUSTER_FORM_4_DETAIL_NAME,
	MUSTER_FORM_10A_DETAIL_NAME,
	MUSTER_FORM_63_DETAIL_NAME,
	MUSTER_FORM_56_DETAIL_NAME,
	MUSTER_FORM_39_DETAIL_NAME,
	PTV1_FORM_DETAIL_NAME,
	MUSTER_FORM_10_DETAIL_NAME,
	MUSTER_FORM_36_DETAIL_NAME,
	MUSTER_FORM_52_DETAIL_NAME,
	MUSTER_FORM_55_DETAIL_NAME,
	MUSTER_FORM_61_DETAIL_NAME,
	MUSTER_FORM_61_B_DETAIL_NAME,
	MUSTER_FORM_22_DETAIL_NAME,
	MUSTER_FORM_65_DETAIL_NAME,
	MUSTER_FORM_20_DETAIL_NAME,
	MUSTER_FORM_19_DETAIL_NAME,
	MUSTER_FORM_F1050_DETAIL_NAME,
}
