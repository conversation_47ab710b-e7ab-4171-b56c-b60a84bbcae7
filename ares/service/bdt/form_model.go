package bdt_service

type MusterForm4 struct {
	Zuzahlungsfrei        bool   `regroup:"Zuzahlungsfrei,exists"`
	Zuzahlungspflichtig   bool   `regroup:"Zuzahlungspflichtig,exists"`
	Unfallfolgen          bool   `regroup:"Unfallfolgen,exists"`
	Arbeitsunfall         bool   `regroup:"Arbeitsunfall,exists"`
	BVG                   bool   `regroup:"BVG,exists"`
	Hinfahrt              bool   `regroup:"Hinfahrt,exists"`
	Ruckfahrtm            bool   `regroup:"Ruckfahrtm,exists"`
	Voll                  bool   `regroup:"Voll,exists"`
	Vor                   bool   `regroup:"Vor,exists"`
	Ambulante             bool   `regroup:"Ambulante,exists"`
	AndererGrund          bool   `regroup:"AndererGrund,exists"`
	AndererGrundText      string `regroup:"AndererGrundText"`
	Dialyse               bool   `regroup:"Dialyse,exists"`
	Vergleichbarer        bool   `regroup:"<PERSON><PERSON>gle<PERSON>bar<PERSON>,exists"`
	<PERSON>uer<PERSON>e            bool   `regroup:"<PERSON><PERSON><PERSON><PERSON>,exists"`
	AndererGrundFur       bool   `regroup:"AndererGrundFur,exists"`
	FromDate              string `regroup:"FromDate"`
	Frequency             string `regroup:"Frequency"`
	ToDate                string `regroup:"ToDate"`
	Behandlung            string `regroup:"Behandlung"`
	Taxi                  bool   `regroup:"Taxi,exists"`
	Rollstuhl             bool   `regroup:"Rollstuhl,exists"`
	Tragestuhl            bool   `regroup:"Tragestuhl,exists"`
	Liegend               bool   `regroup:"Liegend,exists"`
	Ktw                   bool   `regroup:"Ktw,exists"`
	Rtw                   bool   `regroup:"Rtw,exists"`
	Naw                   bool   `regroup:"Naw,exists"`
	AndereEinrichtung     bool   `regroup:"AndereEinrichtung,exists"`
	AndereEinrichtungText string `regroup:"AndereEinrichtungText"`
	Sonstiges             string `regroup:"Sonstiges"`
	Einrichtung           string `regroup:"Einrichtung"`
}

type MusterForm10A struct {
	Geschlecht             string `regroup:"Geschlecht"`
	Kurativ                bool   `regroup:"Kurativ,exists"`
	Praventiv              bool   `regroup:"Praventiv,exists"`
	Behandlung             bool   `regroup:"Behandlung,exists"`
	Unfallfolgen           bool   `regroup:"Unfallfolgen,exists"`
	Knappschaftskennziffer string `regroup:"Knappschaftskennziffer"`
	SSW                    string `regroup:"SSW"`
	Auftragsnummer         string `regroup:"Auftragsnummer"`
	Abnahmedatum           string `regroup:"Abnahmedatum"`
	Abnahmezeit            string `regroup:"Abnahmezeit"`
	Befund                 bool   `regroup:"Befund,exists"`
	GroBes                 bool   `regroup:"GroBes,exists"`
	Glukose1               bool   `regroup:"Glukose1,exists"`
	Kleines                bool   `regroup:"Kleines,exists"`
	Glukose2               bool   `regroup:"Glukose2,exists"`
	HbA1c                  bool   `regroup:"HbA1c,exists"`
	Glukose3               bool   `regroup:"Glukose3,exists"`
	Retikulozyten          bool   `regroup:"Retikulozyten,exists"`
	Glukose4               bool   `regroup:"Glukose4,exists"`
	BlutSenkung            bool   `regroup:"BlutSenkung,exists"`
	Status                 bool   `regroup:"Status,exists"`
	Harnstreifentest       bool   `regroup:"Harnstreifentest,exists"`
	Mikroalbumin           bool   `regroup:"Mikroalbumin,exists"`
	Nuchternplasmaglukose  bool   `regroup:"Nuchternplasmaglukose,exists"`
	GlukoseUrin            bool   `regroup:"GlukoseUrin,exists"`
	Lipidprofil            bool   `regroup:"Lipidprofil,exists"`
	Sediment               bool   `regroup:"Sediment,exists"`
	Quick                  bool   `regroup:"Quick,exists"`
	Thrombinzeit           bool   `regroup:"Thrombinzeit,exists"`
	QuickUnter             bool   `regroup:"QuickUnter,exists"`
	PTT                    bool   `regroup:"PTT,exists"`
	Alkalische             bool   `regroup:"Alkalische,exists"`
	Gamma                  bool   `regroup:"Gamma,exists"`
	LDL                    bool   `regroup:"LDL,exists"`
	Glukose                bool   `regroup:"Glukose,exists"`
	Lipase                 bool   `regroup:"Lipase,exists"`
	Amylase                bool   `regroup:"Amylase,exists"`
	GOT                    bool   `regroup:"GOT,exists"`
	Natrium                bool   `regroup:"Natrium,exists"`
	ASL                    bool   `regroup:"ASL,exists"`
	GPT                    bool   `regroup:"GPT,exists"`
	OP                     bool   `regroup:"OP,exists"`
	BilirubinDir           bool   `regroup:"BilirubinDir,exists"`
	Harnsaure              bool   `regroup:"Harnsaure,exists"`
	BilirubinGes           bool   `regroup:"BilirubinGes,exists"`
	Harnstoff              bool   `regroup:"Harnstoff,exists"`
	Phosphat               bool   `regroup:"Phosphat,exists"`
	Calcium                bool   `regroup:"Calcium,exists"`
	HDL                    bool   `regroup:"HDL,exists"`
	Cholesterin            bool   `regroup:"Cholesterin,exists"`
	IgA                    bool   `regroup:"IgA,exists"`
	Transferrin            bool   `regroup:"Transferrin,exists"`
	Cholinesterase         bool   `regroup:"Cholinesterase,exists"`
	IgG                    bool   `regroup:"IgG,exists"`
	Triglyceride           bool   `regroup:"Triglyceride,exists"`
	CK                     bool   `regroup:"CK,exists"`
	IgM                    bool   `regroup:"IgM,exists"`
	TSH                    bool   `regroup:"TSH,exists"`
	CRP                    bool   `regroup:"CRP,exists"`
	Kalium                 bool   `regroup:"Kalium,exists"`
	Eisen                  bool   `regroup:"Eisen,exists"`
	Kreatinin              bool   `regroup:"Kreatinin,exists"`
	Elektro                bool   `regroup:"Elektro,exists"`
	Clearance              bool   `regroup:"Clearance,exists"`
	Gesamt                 bool   `regroup:"Gesamt,exists"`
	LDH                    bool   `regroup:"LDH,exists"`
}

type MusterForm63 struct {
	Erstverordnung     bool   `regroup:"Erstverordnung,exists"`
	Folgeverordnung    bool   `regroup:"Folgeverordnung,exists"`
	Unfall             bool   `regroup:"Unfall,exists"`
	FromDate           string `regroup:"FromDate"`
	ToDate             string `regroup:"ToDate"`
	ICD                string `regroup:"ICD"`
	Schmerzsymptomatik bool   `regroup:"Schmerzsymptomatik,exists"`
	Urogenitale        bool   `regroup:"Urogenitale,exists"`
	Respiratorische    bool   `regroup:"Respiratorische,exists"`
	Gastrointestinale  bool   `regroup:"Gastrointestinale,exists"`
	Ulzerierende       bool   `regroup:"Ulzerierende,exists"`
	Neurologische      bool   `regroup:"Neurologische,exists"`
	Sonstiges          bool   `regroup:"Sonstiges,exists"`
	Beratung           bool   `regroup:"Beratung,exists"`
	Arztes             bool   `regroup:"Arztes,exists"`
	Pflegefachkraft    bool   `regroup:"Pflegefachkraft,exists"`
	Patienten          bool   `regroup:"Patienten,exists"`
	Koordination       bool   `regroup:"Koordination,exists"`
	Additiv            bool   `regroup:"Additiv,exists"`
	Vollstandige       bool   `regroup:"Vollstandige,exists"`
}

type MusterForm56 struct {
	Rehabilitationssport bool `regroup:"Rehabilitationssport,exists"`
	Funktionstraining    bool `regroup:"Funktionstraining,exists"`
	Diagnose             string
	Schadigung           string
	ZielDes              string
	Prowoche1            bool
	Prowoche2            bool
	Prowoche3            bool
}

type MusterForm13 struct {
	Gebuhrenpflichtig     bool   `regroup:"Gebuhrenpflichtig,exists"`
	Gebuhrenfrei          bool   `regroup:"Gebuhrenfrei,exists"`
	Unfallfolgen          bool   `regroup:"Unfallfolgen,exists"`
	BVG                   bool   `regroup:"BVG,exists"`
	Physiotherapie        bool   `regroup:"Physiotherapie,exists"`
	Podologische          bool   `regroup:"Podologische,exists"`
	Stimm                 bool   `regroup:"Stimm,exists"`
	Ergotherapie          bool   `regroup:"Ergotherapie,exists"`
	Ernahrungstherapie    bool   `regroup:"Ernahrungstherapie,exists"`
	ICD1                  string `regroup:"ICD1"`
	ICD2                  string `regroup:"ICD2"`
	ICDText               string `regroup:"ICDText"`
	Diagnosegruppe        string `regroup:"Diagnosegruppe"`
	Leitsymptomatik_a     bool   `regroup:"Leitsymptomatik_a,exists"`
	Leitsymptomatik_b     bool   `regroup:"Leitsymptomatik_b,exists"`
	Leitsymptomatik_c     bool   `regroup:"Leitsymptomatik_c,exists"`
	Patientenindividuelle bool   `regroup:"Patientenindividuelle,exists"`
	LeitsymptomatikText   string `regroup:"LeitsymptomatikText"`
	Heilmittel_1          string `regroup:"Heilmittel_1"`
	Heilmittel_2          string `regroup:"Heilmittel_2"`
	Heilmittel_3          string `regroup:"Heilmittel_3"`
	Heilmittel_4          string `regroup:"Heilmittel_4"`
	Quantity_1            string `regroup:"Quantity_1"`
	Quantity_2            string `regroup:"Quantity_2"`
	Quantity_3            string `regroup:"Quantity_3"`
	Quantity_4            string `regroup:"Quantity_4"`
	Therapiefrequenz      string `regroup:"Therapiefrequenz"`
	Therapiebericht       bool   `regroup:"Therapiebericht,exists"`
	Hausbesuch_ja         bool   `regroup:"Hausbesuch_ja,exists"`
	Hausbesuch_nein       bool   `regroup:"Hausbesuch_nein,exists"`
	Dringlicher           bool   `regroup:"Dringlicher,exists"`
	TherapiezieleText     string `regroup:"TherapiezieleText"`
}

type FormPTV1 struct {
	AddressContent         string
	BeiMir                 bool
	Analytische            bool
	Systemische            bool
	Tiefenpsychologisch    bool
	Verhaltenstherapie     bool
	Einzeltherapie         bool
	Gruppentherapie        bool
	Kombinationsbehandlung bool
	Erstantrag             bool
	Folgeantrag            bool
	WurdenJa               bool
	WurdenNein             bool
	DateZwar               string
	DateGgf                string
	WarenJa                bool
	WarenNein              bool
	WurdeJa                bool
	WurdeNein              bool
	DateDatum              string
}

type MusterForm10 struct {
	Kurativ          bool
	Praventiv        bool
	Behandlung       bool
	Unfall           bool
	Knappschaft      string
	Quarter          string
	Year             string
	Kontrolun        bool
	Gender           string
	Ser              bool
	Eignes           bool
	Empfang          bool
	Beitriebsstatten string
	Arzt             string
	Abnahmedatum     string
	Abnahmezeit      string
	BefundEilt       bool
	Telefon          bool
	Fax              bool
	Nr               string
	SSW              string
	Diagnose         string
	Befund           string
	Auftrag          string
}

type MusterForm36 struct {
	Bewegung         bool
	Ernahrung        bool
	StressManagement bool
	Sucht            bool
	Sontiges         string
	Hinweise         string
}

type MusterForm52 struct {
	ICDs                   []string
	ErwerbCheckbox         bool
	ErwerbText             string
	VersicherteCheckbox    bool
	VersicherteText        string
	KannJa                 bool
	KannNein               bool
	IstJa                  bool
	IstNein                bool
	WiederDate             string
	Konservativ            string
	Operativ               string
	WeitereBehandelnde     string
	Keine                  bool
	Innerbetri             bool
	Stufen                 bool
	Medizinische           bool
	Psychotherapeu         bool
	Leistung               bool
	WelcheSonstigeCheckbox bool
	WelcheSonstigeText     string
	GibtNein               bool
	GibtJa                 bool
	GibtEsBei              string
	BestehtJa              bool
	BestehtNein            bool
	SonstigeBemerkungen    string
	DateDatum              string
}

type MusterForm55 struct {
	DateSeit                  string
	ICD1                      string
	ICD2                      string
	ICD3                      string
	EndeJa                    bool
	VorauJa                   bool
	KontinNein                bool
	AngabenName               string
	AngabenKranken            string
	KrankenversichertenNumber string
	DateDatum                 string
}

type MusterForm61 struct {
	Diagnose1    string
	Diagnose2    string
	Diagnose3    string
	Diagnose4    string
	Diagnose5    string
	Diagnose6    string
	IcdCode1     string
	IcdCode2     string
	IcdCode3     string
	IcdCode4     string
	IcdCode5     string
	IcdCode6     string
	Beratung     bool
	Prufung      bool
	WeitereLine1 string
	WeitereLine2 string
	DateDatum    string
}

type MusterForm64 struct {
	Vorsorgerelevante_a_line1          string
	Vorsorgerelevante_a_line2          string
	Vorsorgerelevante_b_line1          string
	Vorsorgerelevante_b_line2          string
	Vorsorgerelevante_b_line3          string
	Icd1                               string
	Icd2                               string
	Icd3                               string
	Vorsorgebedurftigkeit_a_line1      string
	Vorsorgebedurftigkeit_a_line2      string
	Vorsorgebedurftigkeit_a_line3      string
	Vorsorgebedurftigkeit_b_line1      string
	Vorsorgebedurftigkeit_b_line2      string
	Vorsorgebedurftigkeit_b_line3      string
	KontextfaktorenMehrfachbelastung   bool
	KontextfaktorenMangelnde           bool
	KontextfaktorenErziehungs          bool
	KontextfaktorenSchwierigkeiten     bool
	KontextfaktorenMangelndeGrund      bool
	KontextfaktorenErhohter            bool
	KontextfaktorenFehlende            bool
	KontextfaktorenPartner             bool
	KontextfaktorenPflege              bool
	KontextfaktorenTod                 bool
	KontextfaktorenTeenager            bool
	KontextfaktorenSoziale             bool
	KontextfaktorenFinanzielle         bool
	KontextfaktorenStandiger           bool
	KontextfaktorenAllein              bool
	KontextfaktorenBeeintrachtigte     bool
	KontextfaktorenUberforderungssitua bool
	KontextfaktorenSprachliche         bool
	KontextfaktorenBesonders           bool
	KontextfaktorenDrohender           bool
	KontextfaktorenNicht               bool
	AndereKontextLine1                 string
	KrankenbehandlungALine1            string
	KrankenbehandlungHeilmittel        bool
	KrankenbehandlungBLine1            string
	VorsorgezieleLine1                 string
	VorsorgezieleLine2                 string
	Zuweisungsempfohlence              bool
	ZuweisungsemVater                  bool
	ZuweisungsemMutter                 bool
	ZuweisungsemVaterKind              bool
	ZuweisungsemNameDesKindes1         string
	ZuweisungsemNameDesKindes2         string
	ZuweisungsemNameDesKindes3         string
	Geburtsdatum1                      string
	Geburtsdatum2                      string
	Geburtsdatum3                      string
	ZuweisungsemAttestLine1            bool
	ZuweisungsemAttestLine2            bool
	ZuweisungsemAttestLine3            bool
	ZuweisungsemBelasteteLine1         bool
	ZuweisungsemBelasteteLine2         bool
	ZuweisungsemBelasteteLine3         bool
	ZuweisungsemPsychosoziableLine1    bool
	ZuweisungsemPsychosoziableLine2    bool
	ZuweisungsemPsychosoziableLine3    bool
	SonstigeAJa                        bool
	SonstigeAText                      string
	SonstigeBJa                        bool
	SonstigeBText                      string
	SonstigeCSonstigesLine1            string
	SonstigeDRuckruf                   string
	Datum1                             string
}

type MusterForm22 struct {
	Therapeuten       string
	Arztnummer        string
	Betrieb           string
	Angaben           string
	Erforderlich      bool
	Erforderlic       bool
	Erfolgt           bool
	Veranlasst        bool
	Sind              string
	Aufgrund          bool
	Arztliche         bool
	Textbox_Arztliche string
	Date_Datum        string
}

type MusterForm65 struct {
	Kurze_Angaben   string
	Vorliegende_Ges string
	Diagnose1       string
	ICD1            string
	Diagnose2       string
	ICD2            string
	Diagnose3       string
	ICD3            string
	Vorliegende_Wei string
	Bisherige       string
	Empfohlene      string
	Ja_Folgende     bool
	Hinweise        string
	Date_Datum      string
}

type MusterForm20 struct {
	TextboxZuletztLine1   string
	TextboxZuletztLine2   string
	TextboxZuletztLine3   string
	TextboxZuletztStunden string

	TextboxTatigkeitLine1 string
	TextboxTatigkeitLine2 string
	TextboxTatigkeitLine3 string
	TextboxTatigkeitLine4 string

	TextboxTaglichLine1 string
	TextboxTaglichLine2 string
	TextboxTaglichLine3 string
	TextboxTaglichLine4 string

	DateVom1 string
	DateBis1 string
	DateVom2 string
	DateBis2 string
	DateVom3 string
	DateBis3 string
	DateVom4 string
	DateBis4 string
}

type MusterForm61B struct {
	Checkbox_esHandelt_1                                bool
	Checkbox_versjcherte_1                              bool
	Textbox_kurzeAngaben_line1_1                        string
	Textbox_kurzeAngaben_line2_1                        string
	Textbox_kurzeAngaben_line3_1                        string
	Textbox_rehabilitationsrelevante_line1_1            string
	Textbox_rehabilitationsrelevante_line2_1            string
	Textbox_mobilitatTug_1                              string
	Textbox_bisherigeArztliche_line1_1                  string
	Textbox_bisherigeArztliche_line2_1                  string
	Textbox_mobilitatChairRise_1                        string
	Textbox_mobilitatHand_1                             string
	Textbox_mobilitatOder_1                             string
	Textbox_kognitionMmst_1                             string
	Textbox_kognitionGds15_1                            string
	Textbox_kognitionUhren_1                            string
	Textbox_mobilitatDemmi_1                            string
	Textbox_mobilitatTinetti_1                          string
	Textbox_schmerz_1                                   string
	Textbox_herzErgometrie_1                            string
	Textbox_herzFev1_1                                  string
	Textbox_herzVk_1                                    string
	Textbox_herzNyha_1                                  string
	Checkbox_heilmittel_1                               bool
	Checkbox_rehabilitationsrelevanteNein_1             bool
	Checkbox_rehabilitationsrelevanteJa_1               bool
	Textbox_rehabilitationsrelevante_1                  string
	Checkbox_lernen2_2                                  bool
	Checkbox_lernen3_2                                  bool
	Checkbox_lernen4_2                                  bool
	Checkbox_allgemeine2_2                              bool
	Checkbox_allgemeine3_2                              bool
	Checkbox_allgemeine4_2                              bool
	Checkbox_kommunikation1_2                           bool
	Checkbox_kommunikation2_2                           bool
	Checkbox_kommunikation3_2                           bool
	Checkbox_kommunikation4_2                           bool
	Checkbox_mobilitat_2                                bool
	Checkbox_mobilitatTransfer1_2                       bool
	Checkbox_mobilitatTransfer2_2                       bool
	Checkbox_mobilitatTransfer3_2                       bool
	Checkbox_mobilitatTransfer4_2                       bool
	Checkbox_mobilitatStehen1_2                         bool
	Checkbox_mobilitatStehen2_2                         bool
	Checkbox_mobilitatStehen3_2                         bool
	Checkbox_mobilitatStehen4_2                         bool
	Checkbox_mobilitatTreppensteigen1_2                 bool
	Checkbox_mobilitatTreppensteigen2_2                 bool
	Checkbox_mobilitatTreppensteigen3_2                 bool
	Checkbox_mobilitatTreppensteigen4_2                 bool
	Checkbox_mobilitatSonstige1_2                       bool
	Checkbox_mobilitatSonstige2_2                       bool
	Checkbox_mobilitatSonstige3_2                       bool
	Checkbox_selbstversorgung_2                         bool
	Checkbox_selbstversorgungEssen1_2                   bool
	Checkbox_selbstversorgungEssen2_2                   bool
	Checkbox_selbstversorgungEssen3_2                   bool
	Checkbox_selbstversorgungEssen4_2                   bool
	Checkbox_selbstversorgungAn1_2                      bool
	Checkbox_selbstversorgungAn2_2                      bool
	Checkbox_selbstversorgungAn3_2                      bool
	Checkbox_selbstversorgungAn4_2                      bool
	Checkbox_selbstversorgungWaschen1_2                 bool
	Checkbox_selbstversorgungWaschen2_2                 bool
	Checkbox_selbstversorgungWaschen3_2                 bool
	Checkbox_selbstversorgungWaschen4_2                 bool
	Checkbox_selbstversorgungBaden2_2                   bool
	Checkbox_selbstversorgungBaden3_2                   bool
	Checkbox_selbstversorgungBaden4_2                   bool
	Checkbox_selbstversorgungBaden1_2                   bool
	Checkbox_selbstversorgungToilettenbenutzung1_2      bool
	Checkbox_selbstversorgungToilettenbenutzung2_2      bool
	Checkbox_selbstversorgungToilettenbenutzung3_2      bool
	Checkbox_selbstversorgungToilettenbenutzung4_2      bool
	Checkbox_selbstversorgungSonstige1_2                bool
	Checkbox_selbstversorgungSonstige2_2                bool
	Checkbox_selbstversorgungSonstige3_2                bool
	Checkbox_hausliches1_2                              bool
	Checkbox_hausliches2_2                              bool
	Checkbox_hausliches3_2                              bool
	Checkbox_hausliches4_2                              bool
	Checkbox_interpersonelle1_2                         bool
	Checkbox_interpersonelle2_2                         bool
	Checkbox_interpersonelle3_2                         bool
	Checkbox_interpersonelle4_2                         bool
	Checkbox_bedeutende1_2                              bool
	Checkbox_bedeutende2_2                              bool
	Checkbox_bedeutende3_2                              bool
	Checkbox_bedeutende4_2                              bool
	Checkbox_gemeinschaftsleben1_2                      bool
	Checkbox_gemeinschaftsleben2_2                      bool
	Checkbox_gemeinschaftsleben3_2                      bool
	Checkbox_gemeinschaftsleben4_2                      bool
	Textbox_mobilitatSonstige_2                         string
	Textbox_sekbstversorgungSonstige_2                  string
	Textbox_rehabilitationsrelevanteUmwelt_line1_2      string
	Textbox_rehabilitationsrelevanteUmwelt_line2_2      string
	Textbox_rehabilitationsrelevanteUmwelt_line3_2      string
	Checkbox_risikofaktorenBewegungsmangel_2            bool
	Checkbox_risikofaktorenSonstiges_2                  bool
	Checkbox_risikofaktorenFehl_2                       bool
	Checkbox_risikofaktorenMedikamente_2                bool
	Checkbox_risikofaktorenNikotin_2                    bool
	Textbox_risikofaktorenSonstiges_line1_2             string
	Textbox_risikofaktorenSonstiges_line2_2             string
	Textbox_risikofaktorenBmi_2                         string
	Checkbox_angabenJa_2                                bool
	Checkbox_angabenNein_2                              bool
	Textbox_rehabilitationsziele_line1_3                string
	Textbox_rehabilitationsziele_line2_3                string
	Textbox_rehabilitationszieleAusSicht_3              string
	Checkbox_unterBerucksichtigungJa_3                  bool
	Checkbox_unterBerucksichtigungEingeschrankt_3       bool
	Textbox_unterBerucksichtigung_line2_3               string
	Textbox_unterBerucksichtigung_line1_3               string
	Checkbox_zuweisungsempfehlungenAmbulant_3           bool
	Checkbox_zuweisungsempfehlungenMutterLeistung_3     bool
	Checkbox_zuweisungsempfehlungenAmbulantMobil_3      bool
	Checkbox_zuweisungsempfehlungenVaterLeistung_3      bool
	Checkbox_zuweisungsempfehlungenStationar_3          bool
	Checkbox_zuweisungsempfehlungenMutterKindLeistung_3 bool
	Checkbox_zuweisungsempfehlungenVaterKindLeistung_3  bool
	Checkbox_zuweisungsempfehlungenGeriatr_3            bool
	Textbox_zuweisungsempfehlungenInhaltliche_3         string
	Textbox_zuweisungsempfehlungenWeitereBemerkungen_3  string
	Checkbox_zuweisungsempfehlungenAndere_3             bool
	Checkbox_zuweisungsempfehlungenPflegende_3          bool
	Checkbox_sonstigeAngabenNeuer_3                     bool
	Checkbox_sonstigeAngabenDieZeitweise_3              bool
	Checkbox_sonstigeAngabenVerschlimmerung_3           bool
	Checkbox_sonstigeAngabenImFalle_3                   bool
	Checkbox_sonstigeAngabenMitaufnahme_3               bool
	Checkbox_sonstigeAngabenKoordination_3              bool
	Checkbox_sonstigeAngabenBegleitperson_3             bool
	Checkbox_sonstigeAngabenOffentliche_3               bool
	Checkbox_sonstigeAngabenPkw_3                       bool
	Textbox_sonstigesBesondere_line1_3                  string
	Textbox_sonstigesBesondere_line2_3                  string
	Checkbox_erteilteJa2_4                              bool
	Checkbox_erteilteNein2_4                            bool
	Checkbox_erteilteJa1_4                              bool
	Checkbox_erteilteNein1_4                            bool
	Textbox_nameVorname1_4                              string
	Textbox_plz1_4                                      string
	Textbox_ort1_4                                      string
	Textbox_strabe1_4                                   string
	Textbox_nameVorname2_4                              string
	Textbox_plz2_4                                      string
	Textbox_ort2_4                                      string
	Textbox_strabe2_4                                   string
	Textbox_nameVorname3_4                              string
	Textbox_plz3_4                                      string
	Textbox_ort3_4                                      string
	Textbox_strabe3_4                                   string
	Checkbox_allgemeine1_2                              bool
	Checkbox_lernen1_2                                  bool
	Textbox_icd10_code1_1                               string
	Textbox_icd10_code2_1                               string
	Textbox_icd10_code3_1                               string
	Textbox_icd10_code4_1                               string
	Textbox_icd10_code5_1                               string
	Textbox_icd10_code6_1                               string
	Textbox_diagnose1_1                                 string
	Textbox_diagnose2_1                                 string
	Textbox_diagnose3_1                                 string
	Textbox_diagnose4_1                                 string
	Textbox_diagnose5_1                                 string
	Textbox_diagnose6_1                                 string
	Textbox_ursache1_1                                  string
	Textbox_ursache2_1                                  string
	Textbox_ursache3_1                                  string
	Textbox_ursache4_1                                  string
	Textbox_ursache5_1                                  string
	Textbox_ursache6_1                                  string
	Textbox_ursache1_0                                  string
	Textbox_ursache2_0                                  string
	Textbox_ursache3_0                                  string
	Textbox_ursache4_0                                  string
	Textbox_ursache5_0                                  string
	Textbox_ursache6_0                                  string
	Date_label_custom_datum_4                           string
}
type MusterForm12 struct {
	ICD1                  string
	ICD2                  string
	ICD3                  string
	ICD4                  string
	EinscharekugenLine1   string
	EinscharekugenLine2   string
	Erst                  bool
	Folge                 bool
	Unfall                bool
	Ser                   bool
	VomDate               string
	BisDate               string
	Medikamentengabe      bool
	MedikamentengabeLine1 string
	MedikamentengabeLine2 string
	MedikamentengabeWtl   string
	MedikamentengabeTgl   string
	MedikamentengabeVom   string
	MedikamentengabeBis   string
	HerrichtenDer         bool
	HerrichtenWtl         string
	HerrichtenTgl         string
	HerrichtenVom         string
	HerrichtenBis         string
	Injektionen           bool
	Herrichten            bool
	Intramuskular         bool
	Subkutan              bool
	ErstOder              bool
	BeiIntensivierter     bool
	Rechts                bool
	Links                 bool
	Beidseits             bool

	Anziehen   bool
	Auszihen   bool
	Komprsttgl string
	Komprstwo  string
	Komprstvom string
	Komprstbis string

	Anlegen           bool
	Abnehmen          bool
	Stutzende         bool
	StutzendeText     string
	Unterstutzung     bool
	Krankenhau        bool
	Grundpflege       bool
	GrundpflegeTgl    string
	GrundpflegeWtl    string
	GrundpflegeVom    string
	GrundpflegeBis    string
	Hauswirtschaft    bool
	HauswirtschaftWtl string
	HauswirtschaftTgl string
	HauswirtschaftVom string
	HauswirtschaftBis string
	Hinweise          string
}

type MusterFormF1050 struct {
	Textbox_auswahl_0                     string
	Textbox_lfd_0                         string
	Textbox_unfallversicher_0             string
	Date_label_custom_arrive_date_0       string
	Label_time_arrive_time_0              string
	Label_lastname_0                      string
	Label_firstname_0                     string
	Label_date_of_birth_0                 string
	Label_active_insurance_name_0         string
	Checkbox_familien_nein_0              bool
	Checkbox_familien_ja_0                bool
	Textbox_family_insured_person_0       string
	Label_address_0                       string
	Textbox_insurance_of_another_person_0 string
	Textbox_job_0                         string
	Date_label_custom_start_datum_0       string
	Label_date_label_custom_start_datum_0 string
	Textbox_tel_0                         string
	Textbox_nationality_0                 string
	Textbox_gender_0                      string
	Textbox_company_address_0             string
	Date_label_custom_accident_day_0      string
	Label_time_accident_time_0            string
	Textbox_accident_place_0              string
	Label_time_start_time_0               string
	Label_time_end_time_0                 string
	Checkbox_die_versicherte_0            bool
	Date_die_versicherte_0                string
	Textbox_die_versicherte_0             string
	Checkbox_die_unfall_0                 bool
	Checkbox_die_behand_0                 bool
	Checkbox_die_verordnung_0             bool
	Checkbox_eine_wiederer_0              bool
	Checkbox_eine_vorstellung_0           bool
	Area_angaben_0                        string
	Area_beschwerden_0                    string
	Area_kurze_angabe_0                   string
	Area_diagnose_0                       string
	Area_art_der_0                        string
	Checkbox_ist_weitere_nein_0           bool
	Checkbox_ist_weitere_ja_0             bool
	Checkbox_durch_mich_0                 bool
	Checkbox_durch_andere_0               bool
	Area_durch_andere_0                   string
	Label_bsnr_date_0                     string
	Label_city_bsnr_0                     string
	Label_doctor_stamp                    string
	Label_patient_fullname_1              string
	Label_date_of_birth_1                 string
	Label_custom_accident_day_1           string
	Label_lfd_1                           string
	Textbox_uv_goa_1                      string
	Textbox_nach_nr0_1                    string
	Textbox_nach_nr1_1                    string
	Textbox_nach_nr2_1                    string
	Textbox_nach_nr3_1                    string
	Textbox_nach_nr4_1                    string
	Textbox_nach_nr5_1                    string
	Textbox_nach_nr6_1                    string
	Textbox_nach_nr7_1                    string
	Textbox_nach_nr8_1                    string
	Textbox_nach_nr9_1                    string
	Textbox_rechnung0_1                   string
	Textbox_rechnung1_1                   string
	Textbox_rechnung2_1                   string
	Textbox_rechnung3_1                   string
	Textbox_rechnung4_1                   string
	Textbox_rechnung5_1                   string
	Textbox_rechnung6_1                   string
	Textbox_rechnung7_1                   string
	Textbox_rechnung8_1                   string
	Textbox_rechnung9_1                   string
	Textbox_kosten0_1                     string
	Textbox_kosten1_1                     string
	Textbox_kosten2_1                     string
	Textbox_kosten3_1                     string
	Textbox_kosten4_1                     string
	Textbox_kosten5_1                     string
	Textbox_kosten6_1                     string
	Textbox_kosten7_1                     string
	Textbox_kosten8_1                     string
	Textbox_kosten9_1                     string
	Label_rechnung10_1                    string
	Textbox_rechnung11_1                  string
	Label_sum_eur_1                       string
	Textbox_rechnungsnummer_1             string
	Label_ik_number_cost_unit_1           string
}

type Muster39 struct {
}

type MusterForm19 struct {
	Checkbox_Notfalldienst bool
	Chexkbox_Vertretung    bool
	Chexkbox_Notfall       bool
	Chexkbox_Unfall        bool
	Textbox_Quarter        string
	Textbox_Year           string
	Textbox_Geschlecht_M   string
	Textbox_Geschlecht_W   string
	Textbox_Diagnose       string
	Textbox_Befund         string
	Textbox_Aubis          string
	Weiterarzt             string
}
