package bdt_service

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"
	"unicode"

	"git.tutum.dev/medi/tutum/ares/pkg/formkey"
	bdt_model "git.tutum.dev/medi/tutum/ares/pkg/xdt/bdt/model"
	form_common "git.tutum.dev/medi/tutum/ares/service/domains/form/common"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/util"
)

// var regexMuster4 = regroup.MustCompile(`\[(?P<Zuzahlungsfrei>X)?\s?\]\s+Zuzahlungsfrei\s+\[(?P<Zuzahlungspflichtig>X)?\s?\]\s+Zuzahlungspflichtig
// \[(?P<Unfallfolgen>X)?\s?\]\s+Unfallfolgen\s+\[(?P<Arbeitsunfall>X)?\s?\]\s+Arbeitsunfall/Berufskrankheit\s+\[(?P<BVG>X)?\s?\]\s+BVG
// \[(?P<Hinfahrt>X)?\s?\]\s+Hinfahrt\s+\[(?P<Ruckfahrtm>X)?\s?\]\s+Rückfahrt
// .*
// .*
// a\)\s+\[(?P<Voll>X)?\s?\]\s+voll- oder teilstationäre Krankenhausbehandlung
// \s*\[(?P<Vor>X)?\s?\]\s+vor- oder nachstationäre Behandlung
// b\) \[(?P<Ambulante>X)?\s?\] ambulante Behandlung
// c\) \[(?P<AndererGrund>X)?\s?\] anderer Grund:\s*(?P<AndererGrundText>.*\S|)\s*
// .*
// d\).*
// \s*\[(?P<Dialyse>X)?\s?\] Dialyse, onkol. Chemo- oder Strahlentherapie
// \s*\[(?P<Vergleichbarer>X)?\s?\] vergleichbarer Ausnahmefall
// e\) \[(?P<Dauerhafte>X)?\s?\] dauerhafte Mobilitätseinschränkung
// f\) \[(?P<AndererGrundFur>X)?\s?\] anderer Grund für Fahrt mit KTW
// .*
// vom/am\s*(?P<FromDate>\d*)\s*/\s*(?P<Frequency>.*)\s*x pro Woche, bis voraussichtlich\s*(?P<ToDate>\d*)\s*
// Behandlungsstätte:\s*(?P<Behandlung>.*\S|)\s*
// .*
// \[(?P<Taxi>X)?\s?\] Taxi/Mietwagen  \[(?P<Rollstuhl>X)?\s?\] Rollstuhl  \[(?P<Tragestuhl>X)?\s?\] Tragestuhl  \[(?P<Liegend>X)?\s?\] liegend
// \[(?P<Ktw>X)?\s?\] KTW, da medizinisch-fachliche Betreuung und/oder
// \s*Einrichtung notwendig ist wegen:\s*(?P<Einrichtung>.*\S|)\s*
// \[(?P<Rtw>X)?\s?\] RTW  \[(?P<Naw>X)?\s?\] NAW/NEF  \[(?P<AndereEinrichtung>X)?\s?\] andere:\s*(?P<AndereEinrichtungText>.*\S|)\s*
// Begründung/Sonstiges:\s*(?P<Sonstiges>.*\S|)\s*`)

// var regexMuster10A = regroup.MustCompile(`Geschlecht:\s*(?P<Geschlecht>.*\S|)\s*
// \[(?P<Kurativ>X)?\s?\] Kurativ   \[(?P<Praventiv>X)?\s?\] Präventiv
// \[(?P<Behandlung>X)?\s?\] bei belegärztl\. Behandlung   \[(?P<Unfallfolgen>X)?\s?\] Unfallfolgen
// Knappschaftskennziffer:\s*(?P<Knappschaftskennziffer>.*\S|)\s* SSW:\s*(?P<SSW>.*\S|)\s*
// Auftragsnummer des Labors:\s*(?P<Auftragsnummer>.*\S|)\s*
// Abnahmedatum:\s*(?P<Abnahmedatum>.*\S|)\s* Abnahmezeit:\s*(?P<Abnahmezeit>.*\S|)\s*
// \[(?P<Befund>X)?\s?\] Befund eilt
// .*
// \[(?P<GroBes>X)?\s?\] großes Blutbild                     \[(?P<Glukose1>X)?\s?\] Glukose 1
// \[(?P<Kleines>X)?\s?\] kleines Blutbild                    \[(?P<Glukose2>X)?\s?\] Glukose 2
// \[(?P<HbA1c>X)?\s?\] HbA1c                               \[(?P<Glukose3>X)?\s?\] Glukose 3
// \[(?P<Retikulozyten>X)?\s?\] Retikulozyten                       \[(?P<Glukose4>X)?\s?\] Glukose 4
// \[(?P<BlutSenkung>X)?\s?\] BlutSenkung                         Urin
// Gesundheitsuntersuchungen               \[(?P<Status>X)?\s?\] Status
// \[(?P<Harnstreifentest>X)?\s?\] Harnstreifentest \(32880\)            \[(?P<Mikroalbumin>X)?\s?\] Mikroalbumin
// \[(?P<Nuchternplasmaglukose>X)?\s?\] Nüchternplasmaglukose \(32881\)       \[(?P<GlukoseUrin>X)?\s?\] Glukose
// \[(?P<Lipidprofil>X)?\s?\] Lipidprofil \(32882\)                 \[(?P<Sediment>X)?\s?\] Sediment
// .*
// \[(?P<Quick>X)?\s?\] Quick                               \[(?P<Thrombinzeit>X)?\s?\] Thrombinzeit
// \[(?P<QuickUnter>X)?\s?\] Quick unter Marcumar-Therapie       \[(?P<PTT>X)?\s?\] PTT
// Serum / Plasma / Vollblut
// \[(?P<Alkalische>X)?\s?\] alkalische      \[(?P<Gamma>X)?\s?\] Gamma GT        \[(?P<LDL>X)?\s?\] LDL-Cholesterin
//     Phosphatase     \[(?P<Glukose>X)?\s?\] Glukose         \[(?P<Lipase>X)?\s?\] Lipase
// \[(?P<Amylase>X)?\s?\] Amylase         \[(?P<GOT>X)?\s?\] GOT / ASAT      \[(?P<Natrium>X)?\s?\] Natrium
// \[(?P<ASL>X)?\s?\] ASL             \[(?P<GPT>X)?\s?\] GPT / ALAT      \[(?P<OP>X)?\s?\] OP-Vorbereitung
// \[(?P<BilirubinDir>X)?\s?\] Bilirubin dir\.  \[(?P<Harnsaure>X)?\s?\] Harnsäure           \(32125\)
// \[(?P<BilirubinGes>X)?\s?\] Bilirubin ges\.  \[(?P<Harnstoff>X)?\s?\] Harnstoff       \[(?P<Phosphat>X)?\s?\] Phosphat
// \[(?P<Calcium>X)?\s?\] Calcium         \[(?P<HDL>X)?\s?\] HDL-Cholesterin     anorganisches
// \[(?P<Cholesterin>X)?\s?\] Cholesterin     \[(?P<IgA>X)?\s?\] IgA             \[(?P<Transferrin>X)?\s?\] Transferrin
// \[(?P<Cholinesterase>X)?\s?\] Cholinesterase  \[(?P<IgG>X)?\s?\] IgG             \[(?P<Triglyceride>X)?\s?\] Triglyceride
// \[(?P<CK>X)?\s?\] CK              \[(?P<IgM>X)?\s?\] IgM             \[(?P<TSH>X)?\s?\] TSH
// \[(?P<CRP>X)?\s?\] CRP             \[(?P<Kalium>X)?\s?\] Kalium
// \[(?P<Eisen>X)?\s?\] Eisen           \[(?P<Kreatinin>X)?\s?\] Kreatinin
// \[(?P<Elektro>X)?\s?\] Eiweiß Elektro\. \[(?P<Clearance>X)?\s?\] Kreatinin Clearance
// \[(?P<Gesamt>X)?\s?\] Eiweiß gesamt   \[(?P<LDH>X)?\s?\] LDH`)

// var regexMuster63 = regroup.MustCompile(`\[(?P<Erstverordnung>X)?\s?\] Erstverordnung\s*\[(?P<Folgeverordnung>X)?\s?\] Folgeverordnung\s*\[(?P<Unfall>X)?\s?\] Unfall, Unfallfolgen
// vom\s*(?P<FromDate>.*\S|)\s*bis\s*(?P<ToDate>.*\S|)\s*
// .*
// \s*(?P<ICD>.*\S|)\s*
// .*
// \[(?P<Schmerzsymptomatik>X)?\s?\] ausgeprägte Schmerzsymptomatik
// \[(?P<Urogenitale>X)?\s?\] ausgeprägte urogenitale Symptomatik
// \[(?P<Respiratorische>X)?\s?\] ausgeprägte respiratorische/kardiale Symptomatik
// \[(?P<Gastrointestinale>X)?\s?\] ausgeprägte gastrointestinale Symptomatik
// \[(?P<Ulzerierende>X)?\s?\] ausgeprägte ulzerierende/exulzerierende Wunden oder Tumore
// \[(?P<Neurologische>X)?\s?\] ausgeprägte neurologische/psych. Symptomatik
// \[(?P<Sonstiges>X)?\s?\] sonstiges komplexes Symptomgeschehen
// .*
// .*
// Folgende Maßnahmen sind notwendig:
// \[(?P<Beratung>X)?\s?\] Beratung  \[(?P<Arztes>X)?\s?\] a. des behandelnden Arztes
// \s*\[(?P<Pflegefachkraft>X)?\s?\] b. der behandelnden Pflegefachkraft
// \s*\[(?P<Patienten>X)?\s?\] c. des Patienten / der Angehörigen
// \[(?P<Koordination>X)?\s?\] Koordination der Palliativversorgung
// .*
// \[(?P<Additiv>X)?\s?\] additiv unterstützende Teilversorgung
// \[(?P<Vollstandige>X)?\s?\] Vollständige Versorgung`)

// var regexMuster56 = regroup.MustCompile(`\[(?P<Rehabilitationssport>X)?\s?\] für Rehabilitationssport
// \[(?P<Funktionstraining>X)?\s?\] für Funktionstraining`)

// var regexMuster13 = regroup.MustCompile(`\[(?P<Gebuhrenpflichtig>X)?\s?\] gebührenpflichtig  \[(?P<Gebuhrenfrei>X)?\s?\] gebührenfrei
// \[(?P<Unfallfolgen>X)?\s?\] Unfallfolgen       \[(?P<BVG>X)?\s?\] Versorgungsleiden/BVG
// \[(?P<Physiotherapie>X)?\s?\] Physiotherapie
// \[(?P<Podologische>X)?\s?\] Podologische Therapie
// \[(?P<Stimm>X)?\s?\] Stimm-\, Sprech-\, Sprach- und Schlucktherapie
// \[(?P<Ergotherapie>X)?\s?\] Ergotherapie
// \[(?P<Ernahrungstherapie>X)?\s?\] Ernährungstherapie
// .*
// ICD-10-Code: \s*(?P<ICD1>\S*|)\s*(?P<ICD2>\S*|)\s*
// \s*(?P<ICDText>(.|\n)*\S|)\s*
// Diagnosegruppe: \s*(?P<Diagnosegruppe>.*\S|)\s*
// Leitsymptomatik: \[(?P<Leitsymptomatik_a>X)?\s?\] a   \[(?P<Leitsymptomatik_b>X)?\s?\] b   \[(?P<Leitsymptomatik_c>X)?\s?\] c
// \[(?P<Patientenindividuelle>X)?\s?\] patientenindividuelle Leitsymptomatik:
// \s*(?P<LeitsymptomatikText>(.|\n)*\S|)\s*
// Heilmittel:
// \s*(?P<Heilmittel_1>.*\S|)\s*   Einheiten:\s*(?P<Quantity_1>.*\S|)\s*(
// \s*(?P<Heilmittel_2>.*\S|)\s*   Einheiten:\s*(?P<Quantity_2>.*\S|)\s*)?(
// \s*(?P<Heilmittel_3>.*\S|)\s*   Einheiten:\s*(?P<Quantity_3>.*\S|)\s*)?(
// Ergänzendes Heilmittel:
// \s*(?P<Heilmittel_4>.*\S|)\s*   Einheiten:\s*(?P<Quantity_4>.*\S|)\s*)?
// Therapiefrequenz:\s*(?P<Therapiefrequenz>.*\S|)\s*
// \[(?P<Therapiebericht>X)?\s?\] Therapiebericht    Hausbesuch: \[(?P<Hausbesuch_ja>X)?\s?\] ja   \[(?P<Hausbesuch_nein>X)?\s?\] nein
// \[(?P<Dringlicher>X)?\s?\] Dringlicher Behandlungsbedarf innerhalb von 14 Tagen(
// .*
// \s*(?P<TherapiezieleText>(.|\n)*\S|)\s*)?`)

// label key of medication form
var TOGGLE_7_HILFSMITTEL = "toggle_7_hilfsmittel"
var TOGGLE_8_IMPFSTOFF = "toggle_8_impfstoff"
var TOGGLE_9_BEDARF = "toggle_9_bedarf"

type FormMapperType interface {
	MusterForm10A | MusterForm56 | MusterForm63 | MusterForm4
}
type FormMapper interface {
	Map() (string, error)
	GetName() form_common.FormName
	GetFormData() any
	SetData(dateFormat string, rawData []bdt_model.Un6296)
}

type baseData struct {
	dateFormat string
	rawData    []bdt_model.Un6296
}

func (b *baseData) SetData(dateFormat string, rawData []bdt_model.Un6296) {
	b.dateFormat = dateFormat
	b.rawData = rawData
}

// form with specific fields exported from xbdt setting
type SpecificForm[T any] struct {
	formName               form_common.FormName
	mapFormHandler         func(t *T, dateFormat string) any // map form struct to payload json
	extractFormDataHandler func(t []bdt_model.Un6296) *T     // extract rawData to form struct
	formData               *T
	baseData
}

func (f *SpecificForm[T]) Map() (string, error) {
	f.formData = f.extractFormDataHandler(f.rawData)
	payload := f.mapFormHandler(f.formData, f.dateFormat)
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return "", err
	}

	return string(payloadBytes), nil
}

func (f *SpecificForm[T]) GetName() form_common.FormName {
	return f.formName
}

func (f *SpecificForm[T]) GetFormData() any {
	if f.formData == nil {
		f.formData = f.extractFormDataHandler(f.rawData)
	}
	return *f.formData
}

var formMap = map[string]FormMapper{
	MUSTER_FORM_10A_NAME: &SpecificForm[MusterForm10A]{
		formName:               form_common.Muster_10A,
		extractFormDataHandler: extractForm10A,
		mapFormHandler:         mapMuster10A,
	},
	MUSTER_FORM_56_NAME: &SpecificForm[MusterForm56]{
		formName:               form_common.Muster_56,
		extractFormDataHandler: extractForm56,
		mapFormHandler:         mapMuster56,
	},
	MUSTER_FORM_63_NAME: &SpecificForm[MusterForm63]{
		formName:               form_common.Muster_N63A,
		extractFormDataHandler: extractForm63,
		mapFormHandler:         mapMuster63,
	},
	MUSTER_FORM_4_NAME: &SpecificForm[MusterForm4]{
		formName:               form_common.Muster_4,
		extractFormDataHandler: extractForm4,
		mapFormHandler:         mapMuster4,
	},
	MUSTER_FORM_4_2019_NAME: &SpecificForm[MusterForm4]{
		formName:               form_common.Muster_4,
		extractFormDataHandler: extractForm4,
		mapFormHandler:         mapMuster4,
	},
	HEILMITTEL_FORM_NAME: &SpecificForm[MusterForm13]{
		formName:               form_common.Muster_13,
		extractFormDataHandler: extractHeilmittelForm,
		mapFormHandler:         mapHeilmittelForm,
	},
	PHYSIK_HEILMITTEL_FORM_NAME: &SpecificForm[MusterForm13]{
		formName:               form_common.Muster_13,
		extractFormDataHandler: extractPhysikHeilmittelForm,
		mapFormHandler:         mapHeilmittelForm,
	},
	ERGO_HEILMITTEL_FORM_NAME: &SpecificForm[MusterForm13]{
		formName:               form_common.Muster_13,
		extractFormDataHandler: extractErgoHeilmittelForm,
		mapFormHandler:         mapHeilmittelForm,
	},
	STIMME_HEILMITTEL_FORM_NAME: &SpecificForm[MusterForm13]{
		formName:               form_common.Muster_13,
		extractFormDataHandler: extractStimmeHeilmittelForm,
		mapFormHandler:         mapHeilmittelForm,
	},
	PTV1_FORM_NAME: &SpecificForm[FormPTV1]{
		formName:               form_common.Muster_PTV_1A,
		extractFormDataHandler: extractMusterFormPTV1,
		mapFormHandler:         mapMusterFormPTV1,
	},
	MUSTER_FORM_10_NAME: &SpecificForm[MusterForm10]{
		formName:               form_common.Muster_10,
		extractFormDataHandler: extractMusterForm10,
		mapFormHandler:         mapMusterForm10,
	},
	MUSTER_FORM_36_NAME: &SpecificForm[MusterForm36]{
		formName:               form_common.Muster_36_E_2017_07,
		extractFormDataHandler: extractMusterForm36,
		mapFormHandler:         mapMusterForm36,
	},
	MUSTER_FORM_52_NAME: &SpecificForm[MusterForm52]{
		formName:               form_common.Muster_52_0_V2,
		extractFormDataHandler: extractMusterForm52,
		mapFormHandler:         mapMusterForm52,
	},
	MUSTER_FORM_55_NAME: &SpecificForm[MusterForm55]{
		formName:               form_common.Muster_55,
		extractFormDataHandler: extractMusterForm55,
		mapFormHandler:         mapMusterForm55,
	},
	MUSTER_FORM_61_NAME: &SpecificForm[MusterForm61]{
		formName:               form_common.Muster_61,
		extractFormDataHandler: extractMusterForm61,
		mapFormHandler:         mapMusterForm61,
	},
	MUSTER_FORM_61_B_NAME: &SpecificForm[MusterForm61B]{
		formName:               form_common.Muster_61,
		extractFormDataHandler: extractMusterForm61B,
		mapFormHandler:         mapMusterForm61B,
	},
	MUSTER_FORM_64_NAME: &SpecificForm[MusterForm64]{
		formName:               form_common.Muster_64,
		extractFormDataHandler: extractMusterForm64,
		mapFormHandler:         mapMusterForm64,
	},
	MUSTER_FORM_22_NAME: &SpecificForm[MusterForm22]{
		formName:               form_common.Muster_22A,
		extractFormDataHandler: extractMusterForm22,
		mapFormHandler:         mapMusterForm22,
	},
	MUSTER_FORM_65_NAME: &SpecificForm[MusterForm65]{
		formName:               form_common.Muster_22A,
		extractFormDataHandler: extractMusterForm65,
		mapFormHandler:         mapMusterForm65,
	},
	MUSTER_FORM_20_NAME: &SpecificForm[MusterForm20]{
		formName:               form_common.Muster_20A,
		extractFormDataHandler: extractMusterForm20,
		mapFormHandler:         mapMusterForm20,
	},
	MUSTER_FORM_12_2020: &SpecificForm[MusterForm12]{
		formName:               form_common.Muster_12A,
		extractFormDataHandler: extractMusterForm12,
		mapFormHandler:         mapMusterForm12,
	},
	MUSTER_FORM_12_2017: &SpecificForm[MusterForm12]{
		formName:               form_common.Muster_12A,
		extractFormDataHandler: extractMusterForm12,
		mapFormHandler:         mapMusterForm12,
	},
	MUSTER_FORM_19_NAME: &SpecificForm[MusterForm19]{
		formName:               form_common.Muster_19B,
		extractFormDataHandler: extractMusterForm19,
		mapFormHandler:         mapMusterForm19,
	},
	MUSTER_FORM_F1050: &SpecificForm[MusterFormF1050]{
		formName:               form_common.F1050,
		extractFormDataHandler: extractMusterFormF1050,
		mapFormHandler:         mapMusterFormF1050,
	},
}

// valid form from time
var validTimeFormMapper = map[string]time.Time{
	MUSTER_FORM_39_NAME:        time.Date(2021, 1, 1, 0, 0, 0, 0, time.Local),
	MUSTER_FORM_39_DETAIL_NAME: time.Date(2021, 1, 1, 0, 0, 0, 0, time.Local),
}

// isValidFormDate checks if a form should be created based on its name and date
func isValidFormDate(formName string, createdDate time.Time) bool {
	if v, ok := validTimeFormMapper[formName]; ok {
		if createdDate.Before(v) {
			return false
		}
	}
	return true
}

// create form factory
func newFormMapper(rawData []bdt_model.Un6296, name, dateFormat string, createdDate *time.Time) FormMapper {
	for formName, form := range formMap {
		if strings.Contains(name, formName) {
			// Check if form should be created based on name and date
			if createdDate != nil && !isValidFormDate(formName, *createdDate) {
				return nil
			}

			form.SetData(dateFormat, rawData)
			return form
		}
	}
	return nil
}

/* ----------map form by using specific fields------------- */
func extractForm10A(rawData []bdt_model.Un6296) *MusterForm10A {
	form10A := MusterForm10A{}
	for _, value := range rawData {
		if strings.Contains(value.Un6296, "Geschlecht") {
			form10A.Geschlecht = value.Un6297[0]
		} else if strings.Contains(value.Un6296, "Kurativ") {
			form10A.Kurativ = getBoolValue(value.Un6297[0])
		} else if strings.Contains(value.Un6296, "Praeventiv") {
			form10A.Praventiv = getBoolValue(value.Un6297[0])
		} else if strings.Contains(value.Un6296, "Grblutbild") {
			form10A.GroBes = getBoolValue(value.Un6297[0])
		} else if strings.Contains(value.Un6296, "Glukose1") {
			form10A.Glukose1 = getBoolValue(value.Un6297[0])
		} else if strings.Contains(value.Un6296, "Urinstatus") {
			form10A.Status = getBoolValue(value.Un6297[0])
		} else if strings.Contains(value.Un6296, "Guglukose") {
			form10A.Nuchternplasmaglukose = getBoolValue(value.Un6297[0])
		} else if strings.Contains(value.Un6296, "Bilirubing") {
			form10A.BilirubinGes = getBoolValue(value.Un6297[0])
		} else if strings.Contains(value.Un6296, "Harnstoff") {
			form10A.Harnstoff = getBoolValue(value.Un6297[0])
		}
	}
	return &form10A
}

func extractForm4(rawData []bdt_model.Un6296) *MusterForm4 {
	form4 := MusterForm4{}
	for _, value := range rawData {
		content := strings.Join(value.Un6297, "")
		if strings.Contains(value.Un6296, "Gebpflicht") {
			form4.Zuzahlungspflichtig = getBoolValue(content)
		} else if strings.Contains(value.Un6296, "GebfreiJ") {
			form4.Zuzahlungsfrei = getBoolValue(content)
		} else if strings.Contains(value.Un6296, "Krhstation") {
			form4.Voll = getBoolValue(content)
		} else if strings.Contains(value.Un6296, "HinfahrtJ") {
			form4.Hinfahrt = getBoolValue(content)
		} else if strings.Contains(value.Un6296, "RueckfahrtJ") {
			form4.Ruckfahrtm = getBoolValue(content)
		} else if strings.Contains(value.Un6296, "AmbbehandJ") {
			form4.Ambulante = getBoolValue(content)
		} else if strings.Contains(value.Un6296, "Dauer6monJ") {
			form4.Dauerhafte = getBoolValue(content)
		} else if strings.Contains(value.Un6296, "Transprtvom") {
			form4.FromDate = content
		} else if strings.Contains(value.Un6296, "Anzwoche") {
			form4.Frequency = content
		} else if strings.Contains(value.Un6296, "Transprtbis") {
			form4.ToDate = content
		} else if strings.Contains(value.Un6296, "Behandort") {
			form4.Behandlung = content
		} else if strings.Contains(value.Un6296, "TaxiJ") {
			form4.Taxi = getBoolValue(content)
		} else if strings.Contains(value.Un6296, "Sonstiges") {
			form4.Sonstiges = content
		}
	}
	return &form4
}

func extractHeilmittelForm(rawData []bdt_model.Un6296) *MusterForm13 {
	form := MusterForm13{}
	for _, value := range rawData {
		if strings.Contains(value.Un6296, "Gebpflicht") {
			form.Gebuhrenpflichtig = getBoolValue(value.Un6297[0])
		} else if strings.Contains(value.Un6296, "Thergo") {
			form.Ergotherapie = getBoolValue(value.Un6297[0])
		} else if strings.Contains(value.Un6296, "Thpodologie") {
			form.Podologische = getBoolValue(value.Un6297[0])
		} else if strings.Contains(value.Un6296, "Thphysio") {
			form.Physiotherapie = getBoolValue(value.Un6297[0])
		} else if strings.Contains(value.Un6296, "Icd10_1") {
			form.ICD1 = value.Un6297[0]
		} else if strings.Contains(value.Un6296, "Icd10_2") {
			form.ICD2 = value.Un6297[0]
		} else if strings.Contains(value.Un6296, "Diagnose") {
			form.ICDText = strings.Join(value.Un6297, "\n")
		} else if strings.Contains(value.Un6296, "Diaggruppe") {
			form.Diagnosegruppe = value.Un6297[0]
		} else if strings.Contains(value.Un6296, "Ltsymptoma") {
			form.Leitsymptomatik_a = getBoolValue(value.Un6297[0])
		} else if strings.Contains(value.Un6296, "Ltsymptomb") {
			form.Leitsymptomatik_b = getBoolValue(value.Un6297[0])
		} else if strings.Contains(value.Un6296, "Ltsymptomp") {
			form.LeitsymptomatikText = strings.Join(value.Un6297, " ")
		} else if strings.Contains(value.Un6296, "Verordnung1") {
			form.Heilmittel_1 = value.Un6297[0]
		} else if strings.Contains(value.Un6296, "Verordnung2") {
			form.Heilmittel_2 = value.Un6297[0]
		} else if strings.Contains(value.Un6296, "Menge2") {
			form.Quantity_2 = value.Un6297[0]
		} else if strings.Contains(value.Un6296, "Verordnung3") {
			form.Heilmittel_3 = value.Un6297[0]
		} else if strings.Contains(value.Un6296, "Menge3") {
			form.Quantity_3 = value.Un6297[0]
		} else if strings.Contains(value.Un6296, "Verordnung4") {
			form.Heilmittel_4 = value.Un6297[0]
		} else if strings.Contains(value.Un6296, "Menge4") {
			form.Quantity_4 = value.Un6297[0]
		} else if strings.Contains(value.Un6296, "Menge") {
			form.Quantity_1 = value.Un6297[0]
		} else if strings.Contains(value.Un6296, "Anzwoche1") {
			if len(value.Un6297[0]) > 1 {
				lastChar := value.Un6297[0][len(value.Un6297[0])-1]
				if !unicode.IsDigit(rune(lastChar)) {
					form.Heilmittel_1 = form.Heilmittel_1 + " " + value.Un6297[0]
				} else {
					form.Heilmittel_1 = form.Heilmittel_1 + " " + value.Un6297[0] + "wöch."
				}
			}
		} else if strings.Contains(value.Un6296, "Anzwoche2") {
			if len(value.Un6297[0]) > 1 {
				lastChar := value.Un6297[0][len(value.Un6297[0])-1]
				if !unicode.IsDigit(rune(lastChar)) {
					form.Heilmittel_2 = form.Heilmittel_2 + " " + value.Un6297[0]
				} else {
					form.Heilmittel_2 = form.Heilmittel_2 + " " + value.Un6297[0] + "wöch."
				}
			}
		} else if strings.Contains(value.Un6296, "Anzwoche3") {
			if len(value.Un6297[0]) > 1 {
				lastChar := value.Un6297[0][len(value.Un6297[0])-1]
				if !unicode.IsDigit(rune(lastChar)) {
					form.Heilmittel_3 = form.Heilmittel_3 + " " + value.Un6297[0]
				} else {
					form.Heilmittel_3 = form.Heilmittel_3 + " " + value.Un6297[0] + "wöch."
				}
			}
		} else if strings.Contains(value.Un6296, "Anzwoche4") {
			if len(value.Un6297[0]) > 1 {
				lastChar := value.Un6297[0][len(value.Un6297[0])-1]
				if !unicode.IsDigit(rune(lastChar)) {
					form.Heilmittel_4 = form.Heilmittel_4 + " " + value.Un6297[0]
				} else {
					form.Heilmittel_4 = form.Heilmittel_4 + " " + value.Un6297[0] + "wöch."
				}
			}
		} else if value.Un6296 == "Anzwoche" {
			form.Therapiefrequenz = value.Un6297[0]
		} else if strings.Contains(value.Un6296, "HausbesuchN") {
			form.Hausbesuch_nein = getBoolValue(value.Un6297[0])
		} else if strings.Contains(value.Un6296, "HausbesuchJ") {
			form.Hausbesuch_ja = getBoolValue(value.Un6297[0])
		} else if strings.Contains(value.Un6296, "DringendJ") {
			form.Dringlicher = getBoolValue(value.Un6297[0])
		} else if strings.Contains(value.Un6296, "ThberichtJ") {
			form.Therapiebericht = getBoolValue(value.Un6297[0])
		} else if strings.Contains(value.Un6296, "ThberichtN") {
			form.Therapiebericht = !getBoolValue(value.Un6297[0])
		} else if strings.Contains(value.Un6296, "Theraziele") {
			form.TherapiezieleText = strings.Join(value.Un6297, "\n")
		} else if strings.Contains(value.Un6296, "Indikschl") { // following fields will appear only old heilmittel
			if len(value.Un6297[0]) > 1 {
				raw := value.Un6297[0]
				i := len(raw) - 1
				for i >= 0 {
					ch := raw[i]
					if ch == 'a' || ch == 'b' || ch == 'c' {
						i--
					} else {
						break
					}
				}
				form.Diagnosegruppe = raw[:i+1]
				suffix := raw[i+1:]
				for _, ch := range suffix {
					switch ch {
					case 'a':
						form.Leitsymptomatik_a = true
					case 'b':
						form.Leitsymptomatik_b = true
					case 'c':
						form.Leitsymptomatik_c = true
					}
				}
			} else if len(value.Un6297[0]) == 1 {
				form.Diagnosegruppe = value.Un6297[0]
			}
		} else if strings.HasSuffix(value.Un6296, "Icd10") {
			form.ICD1 = value.Un6297[0]
		}
	}
	return &form
}

func extractPhysikHeilmittelForm(rawData []bdt_model.Un6296) *MusterForm13 {
	form13 := extractHeilmittelForm(rawData)
	form13.Physiotherapie = true

	return form13
}

func extractErgoHeilmittelForm(rawData []bdt_model.Un6296) *MusterForm13 {
	form13 := extractHeilmittelForm(rawData)
	form13.Ergotherapie = true

	return form13
}

func extractStimmeHeilmittelForm(rawData []bdt_model.Un6296) *MusterForm13 {
	form13 := extractHeilmittelForm(rawData)
	form13.Stimm = true
	return form13
}

func extractForm56(rawData []bdt_model.Un6296) *MusterForm56 {
	form56 := MusterForm56{}
	for _, value := range rawData {
		content := strings.Join(value.Un6297, "")
		switch {
		case strings.Contains(value.Un6296, "Rehasport"):
			form56.Rehabilitationssport = getBoolValue(content)
		case strings.Contains(value.Un6296, "Diagnose"):
			form56.Diagnose = strings.Join(value.Un6297, ", ")
		case strings.Contains(value.Un6296, "Schaedigung"):
			form56.Schadigung = strings.Join(value.Un6297, ", ")
		case strings.Contains(value.Un6296, "Rehafktziel"):
			form56.ZielDes = strings.Join(value.Un6297, ", ")
		case strings.Contains(value.Un6296, "1prowoche"):
			form56.Prowoche1 = getBoolValue(content)
		case strings.Contains(value.Un6296, "2prowoche"):
			form56.Prowoche2 = getBoolValue(content)
		case strings.Contains(value.Un6296, "3prowoche"):
			form56.Prowoche3 = getBoolValue(content)
		}
	}
	return &form56
}

func extractForm63(rawData []bdt_model.Un6296) *MusterForm63 {
	form := MusterForm63{}
	for _, value := range rawData {
		if strings.Contains(value.Un6296, "Erst") {
			form.Erstverordnung = getBoolValue(value.Un6297[0])
		} else if strings.Contains(value.Un6296, "Diagnose") {
			// TODO add data for line 23
			form.ICD = value.Un6297[0]
		}
	}
	return &form
}

func extractMusterFormPTV1(rawData []bdt_model.Un6296) *FormPTV1 {
	form := FormPTV1{}
	for _, v := range rawData {
		content := strings.Join(v.Un6297, "")

		if strings.Contains(v.Un6296, "Kkname1") {
			form.AddressContent += strings.Join(v.Un6297, " ")
		} else if strings.Contains(v.Un6296, "Kkstrasse") {
			form.AddressContent += fmt.Sprintf("\n%s", strings.Join(v.Un6297, " "))
		} else if strings.Contains(v.Un6296, "Kkplzort") {
			form.AddressContent += fmt.Sprintf("\n%s", strings.Join(v.Un6297, " "))
		} else if strings.Contains(v.Un6296, "Einzelther") {
			form.Einzeltherapie = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Tiefenpt") {
			form.Tiefenpsychologisch = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Erst") {
			form.Erstantrag = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Ptsprech1") {
			form.DateZwar = content
		} else if strings.Contains(v.Un6296, "Ptsprech2") {
			form.DateGgf = content
		} else if strings.Contains(v.Un6296, "PtsprechJ") {
			form.WurdenJa = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "PtsprechN") {
			form.WurdenNein = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "PtstatrehaJ") {
			form.WarenJa = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "PtstatrehaN") {
			form.WarenNein = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Pt2jahreJ") {
			form.WurdeJa = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Pt2jahreN") {
			form.WurdeNein = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Ausstellung") {
			form.DateDatum = content
		}
	}

	return &form
}

func extractMusterForm10(rawData []bdt_model.Un6296) *MusterForm10 {
	form := MusterForm10{}
	for _, v := range rawData {
		content := strings.Join(v.Un6297, "")

		if strings.Contains(v.Un6296, "Quartal") {
			form.Quarter = content
		} else if strings.Contains(v.Un6296, "Jahr") {
			form.Year = content
		} else if strings.Contains(v.Un6296, "Geschlecht") {
			form.Gender = content
		} else if strings.Contains(v.Un6296, "Kurativ") {
			form.Kurativ = getBoolValue(content)
			// will save labId later
			// } else if strings.Contains(v.Un6296, "Auftragnr") {
			// form.Kurativ = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Diagnose") {
			form.Diagnose = strings.Join(v.Un6297, ", ")
		} else if strings.Contains(v.Un6296, "Befund") {
			form.Befund = strings.Join(v.Un6297, ", ")
		} else if strings.Contains(v.Un6296, "Auftrag") {
			form.Auftrag = strings.Join(v.Un6297, ", ")
		}
	}

	return &form
}

func extractMusterForm36(rawData []bdt_model.Un6296) *MusterForm36 {
	form := MusterForm36{}
	for _, v := range rawData {
		content := strings.Join(v.Un6297, "")

		if strings.Contains(v.Un6296, "ErnaehrungJ") {
			form.Bewegung = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Hinweise") {
			form.Hinweise = strings.Join(v.Un6297, ", ")
		}
	}

	return &form
}

func extractMusterForm52(rawData []bdt_model.Un6296) *MusterForm52 {
	form := MusterForm52{}
	for _, v := range rawData {
		content := strings.Join(v.Un6297, "")

		if strings.Contains(v.Un6296, "Icd") {
			form.ICDs = v.Un6297
		} else if strings.Contains(v.Un6296, "Erwerbals") {
			form.ErwerbText = strings.Join(v.Un6297, ", ")
		} else if strings.Contains(v.Un6296, "WiederafN") {
			form.IstNein = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "WiederafJ") {
			form.IstJa = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "cMasskonserv") {
			form.Konservativ = content
		} else if strings.Contains(v.Un6296, "Weiterarzt") {
			form.WeitereBehandelnde = content
		} else if strings.Contains(v.Un6296, "MassnpsychJ") {
			form.Psychotherapeu = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "ProblemeJ") {
			form.GibtJa = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "ProblemeN") {
			form.GibtNein = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "cProbleme") {
			form.GibtEsBei = content
		} else if strings.Contains(v.Un6296, "MinderungN") {
			form.BestehtNein = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "MinderungJ") {
			form.BestehtJa = getBoolValue(content)
		}
	}

	return &form
}

func extractMusterForm55(rawData []bdt_model.Un6296) *MusterForm55 {
	form := MusterForm55{}
	for _, v := range rawData {
		content := strings.Join(v.Un6297, "")

		if strings.Contains(v.Un6296, "Behandseit") {
			form.DateSeit = content
		} else if strings.Contains(v.Un6296, "Icd1") {
			form.ICD1 = content
		} else if strings.Contains(v.Un6296, "Icd2") {
			form.ICD2 = content
		} else if strings.Contains(v.Un6296, "Icd3") {
			form.ICD3 = content
		} else if strings.Contains(v.Un6296, "MedversorgJ") {
			form.EndeJa = getBoolValue(content)
		}
	}

	return &form
}

func extractMusterForm61(rawData []bdt_model.Un6296) *MusterForm61 {
	form := MusterForm61{}
	for _, v := range rawData {
		content := strings.Join(v.Un6297, "")

		if strings.Contains(v.Un6296, "Grunddiag1") {
			form.Diagnose1 = content
		} else if strings.Contains(v.Un6296, "Grunddiag2") {
			form.Diagnose2 = content
		} else if strings.Contains(v.Un6296, "Grunddiag3") {
			form.Diagnose3 = content
		} else if strings.Contains(v.Un6296, "Grundicd1") {
			form.IcdCode1 = content
		} else if strings.Contains(v.Un6296, "Grundicd2") {
			form.IcdCode2 = content
		} else if strings.Contains(v.Un6296, "Grundicd3") {
			form.IcdCode3 = content
		} else if strings.Contains(v.Un6296, "Relevdiag1") {
			form.Diagnose4 = content
		} else if strings.Contains(v.Un6296, "Relevdiag2") {
			form.Diagnose5 = content
		} else if strings.Contains(v.Un6296, "Relevdiag3") {
			form.Diagnose6 = content
		} else if strings.Contains(v.Un6296, "Relevicd1") {
			form.IcdCode4 = content
		} else if strings.Contains(v.Un6296, "Relevicd2") {
			form.IcdCode5 = content
		} else if strings.Contains(v.Un6296, "Relevicd3") {
			form.IcdCode6 = content
		} else if strings.Contains(v.Un6296, "KkberatungJ") {
			form.Beratung = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Kkanmerkung") {
			if len(v.Un6297) > 1 {
				form.WeitereLine1 = v.Un6297[0]
				form.WeitereLine2 = v.Un6297[1]
			} else if len(v.Un6297) > 0 {
				form.WeitereLine2 = v.Un6297[0]
			}
		} else if strings.Contains(v.Un6296, "Ausstellung") {
			form.DateDatum = content
		}
	}

	return &form
}

func extractMusterForm61B(rawData []bdt_model.Un6296) *MusterForm61B {
	form := MusterForm61B{}
	for _, v := range rawData {
		content := strings.Join(v.Un6297, "")

		switch {
		case strings.Contains(v.Un6296, "MinderwerbN"):
			form.Checkbox_esHandelt_1 = getBoolValue(content)
		case strings.Contains(v.Un6296, "MedrehagkvJ"):
			form.Checkbox_versjcherte_1 = getBoolValue(content)
		case strings.Contains(v.Un6296, "Grunddiag1"):
			form.Textbox_diagnose1_1 = content
		case strings.Contains(v.Un6296, "Grunddiag2"):
			form.Textbox_diagnose2_1 = content
		case strings.Contains(v.Un6296, "Grunddiag3"):
			form.Textbox_diagnose3_1 = content
		case strings.Contains(v.Un6296, "Grundicd1"):
			form.Textbox_icd10_code1_1 = content
		case strings.Contains(v.Un6296, "Grundicd2"):
			form.Textbox_icd10_code2_1 = content
		case strings.Contains(v.Un6296, "Grundicd3"):
			form.Textbox_icd10_code3_1 = content
		case strings.Contains(v.Un6296, "Relevdiag1"):
			form.Textbox_diagnose4_1 = content
		case strings.Contains(v.Un6296, "Relevdiag2"):
			form.Textbox_diagnose5_1 = content
		case strings.Contains(v.Un6296, "Relevdiag3"):
			form.Textbox_diagnose6_1 = content
		case strings.Contains(v.Un6296, "Relevicd1"):
			form.Textbox_icd10_code4_1 = content
		case strings.Contains(v.Un6296, "Relevicd2"):
			form.Textbox_icd10_code5_1 = content
		case strings.Contains(v.Un6296, "Relevicd3"):
			form.Textbox_icd10_code6_1 = content
		case strings.Contains(v.Un6296, "Anamnese"):
			fields := []*string{
				&form.Textbox_kurzeAngaben_line1_1,
				&form.Textbox_kurzeAngaben_line2_1,
				&form.Textbox_kurzeAngaben_line3_1,
			}
			for i := 0; i < len(v.Un6297) && i < len(fields); i++ {
				*fields[i] = v.Un6297[i]
			}
		case strings.Contains(v.Un6296, "Befund"):
			form.Textbox_rehabilitationsrelevante_line1_1 = strings.Join(v.Un6297, ", ")
		case strings.Contains(v.Un6296, "Intervent"):
			form.Textbox_bisherigeArztliche_line1_1 += strings.Join(v.Un6297, ", ") + "; "
		case strings.Contains(v.Un6296, "Heilmittel"):
			form.Textbox_bisherigeArztliche_line1_1 += strings.Join(v.Un6297, ", ")
		case strings.Contains(v.Un6296, "HmreichenN"):
			form.Checkbox_heilmittel_1 = getBoolValue(content)
		case strings.Contains(v.Un6296, "HilfmittelJ"):
			form.Checkbox_rehabilitationsrelevanteJa_1 = getBoolValue(content)
		case strings.Contains(v.Un6296, "cHilfmittel"):
			form.Textbox_rehabilitationsrelevante_1 = content
		case strings.Contains(v.Un6296, "LerneneiJ"):
			form.Checkbox_lernen1_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "LerneneiN"):
			form.Checkbox_lernen2_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "AufgabeeiJ"):
			form.Checkbox_allgemeine1_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "AufgabeeiN"):
			form.Checkbox_allgemeine2_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "KommuniJ"):
			form.Checkbox_kommunikation1_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "KommuniN"):
			form.Checkbox_kommunikation1_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "TransfreiJ"):
			form.Checkbox_mobilitatTransfer1_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "TransfreiN"):
			form.Checkbox_mobilitatTransfer2_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "StehgeheiJ"):
			form.Checkbox_mobilitatStehen1_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "StehgeheiN"):
			form.Checkbox_mobilitatStehen2_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "TreppeneiJ"):
			form.Checkbox_mobilitatTreppensteigen1_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "TreppeneiN"):
			form.Checkbox_mobilitatTreppensteigen2_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "NahrungJ"):
			form.Checkbox_selbstversorgungEssen1_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "NahrungN"):
			form.Checkbox_selbstversorgungEssen2_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "KleideneiJ"):
			form.Checkbox_selbstversorgungAn1_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "KleideneiN"):
			form.Checkbox_selbstversorgungAn2_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "WaschenJ"):
			form.Checkbox_selbstversorgungWaschen1_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "WaschenN"):
			form.Checkbox_selbstversorgungWaschen2_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "DuscheneiJ"):
			form.Checkbox_selbstversorgungBaden1_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "DuscheneiN"):
			form.Checkbox_selbstversorgungBaden2_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "ToilettJ"):
			form.Checkbox_selbstversorgungToilettenbenutzung1_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "ToilettN"):
			form.Checkbox_selbstversorgungToilettenbenutzung2_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "HauslebeiJ"):
			form.Checkbox_hausliches1_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "HauslebeiN"):
			form.Checkbox_hausliches2_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "PersaktJ"):
			form.Checkbox_interpersonelle1_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "PersaktN"):
			form.Checkbox_interpersonelle2_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "BereicheiJ"):
			form.Checkbox_bedeutende1_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "BereicheiN"):
			form.Checkbox_bedeutende2_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "SozialJ"):
			form.Checkbox_gemeinschaftsleben1_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "SozialN"):
			form.Checkbox_gemeinschaftsleben2_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "Rehafaktor"):
			fields := []*string{
				&form.Textbox_rehabilitationsrelevanteUmwelt_line1_2,
				&form.Textbox_rehabilitationsrelevanteUmwelt_line2_2,
				&form.Textbox_rehabilitationsrelevanteUmwelt_line3_2,
			}
			for i := 0; i < len(v.Un6297) && i < len(fields); i++ {
				*fields[i] = v.Un6297[i]
			}
		case strings.Contains(v.Un6296, "Rfbewmangel"):
			form.Checkbox_risikofaktorenBewegungsmangel_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "RehafaehigJ"):
			form.Checkbox_angabenJa_2 = getBoolValue(content)
		case strings.Contains(v.Un6296, "Rehazielobj"):
			fields := []*string{
				&form.Textbox_rehabilitationsziele_line1_3,
				&form.Textbox_rehabilitationsziele_line2_3,
			}
			for i := 0; i < len(v.Un6297) && i < len(fields); i++ {
				*fields[i] = v.Un6297[i]
			}
		case strings.Contains(v.Un6296, "lRehaprognoJ"):
			form.Checkbox_unterBerucksichtigungJa_3 = getBoolValue(content)
		case strings.Contains(v.Un6296, "lRehaprognoN"):
			form.Checkbox_unterBerucksichtigungEingeschrankt_3 = getBoolValue(content)
		case strings.Contains(v.Un6296, "RehastatioN"):
			form.Checkbox_zuweisungsempfehlungenStationar_3 = getBoolValue(content)
		case strings.Contains(v.Un6296, "Indikation"):
			form.Textbox_zuweisungsempfehlungenInhaltliche_3 = strings.Join(v.Un6297, ", ")
		case strings.Contains(v.Un6296, "lVerschlimmJ"):
			form.Checkbox_sonstigeAngabenVerschlimmerung_3 = getBoolValue(content)
		case strings.Contains(v.Un6296, "dAusstellung"):
			form.Date_label_custom_datum_4 = content
		}
	}

	return &form
}

func extractMusterForm64(rawData []bdt_model.Un6296) *MusterForm64 {
	form := MusterForm64{}
	for _, v := range rawData {
		content := strings.Join(v.Un6297, "")
		if strings.Contains(v.Un6296, "Gesundstoer") {
			form.Vorsorgerelevante_a_line1 = content
		} else if strings.Contains(v.Un6296, "Erkrankung1") {
			form.Vorsorgerelevante_b_line1 = content
		} else if strings.Contains(v.Un6296, "Erkrankung2") {
			form.Vorsorgerelevante_b_line2 = content
		} else if strings.Contains(v.Un6296, "Erkrankung3") {
			form.Vorsorgerelevante_b_line3 = content
		} else if strings.Contains(v.Un6296, "Icd1") {
			form.Icd1 = content
		} else if strings.Contains(v.Un6296, "Icd2") {
			form.Icd2 = content
		} else if strings.Contains(v.Un6296, "Icd3") {
			form.Icd3 = content
		} else if strings.Contains(v.Un6296, "Anamnese") {
			if len(v.Un6297) > 0 {
				form.Vorsorgebedurftigkeit_a_line1 = v.Un6297[0]
			}
			if len(v.Un6297) > 1 {
				form.Vorsorgebedurftigkeit_a_line2 = v.Un6297[1]
			}
			if len(v.Un6297) > 2 {
				form.Vorsorgebedurftigkeit_a_line3 = v.Un6297[2]
			}
		} else if strings.Contains(v.Un6296, "Beeintraech") {
			if len(v.Un6297) > 0 {
				form.Vorsorgebedurftigkeit_b_line1 = v.Un6297[0]
			}
			if len(v.Un6297) > 1 {
				form.Vorsorgebedurftigkeit_b_line2 = v.Un6297[1]
			}
			if len(v.Un6297) > 2 {
				form.Vorsorgebedurftigkeit_b_line2 = v.Un6297[2]
			}
		} else if strings.Contains(v.Un6296, "Mehrfachbel") {
			form.KontextfaktorenMehrfachbelastung = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Beziehung") {
			form.KontextfaktorenBeeintrachtigte = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Erziehung") {
			form.KontextfaktorenErziehungs = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Alltagsprob") {
			form.KontextfaktorenSchwierigkeiten = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Interventio") {
			form.KrankenbehandlungALine1 = content
		} else if strings.Contains(v.Un6296, "Massnahmen") {
			form.KrankenbehandlungBLine1 = content
		} else if strings.Contains(v.Un6296, "Vorsorgziel") {
			if len(v.Un6297) > 1 {
				form.VorsorgezieleLine2 = v.Un6297[1]
			}
			if len(v.Un6297) > 0 {
				form.VorsorgezieleLine1 = v.Un6297[0]
			}
		} else if strings.Contains(v.Un6296, "Lstmuttkind") {
			form.ZuweisungsemMutter = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Namekind1") {
			form.ZuweisungsemNameDesKindes1 = content
		} else if strings.Contains(v.Un6296, "Namekind2") {
			form.ZuweisungsemNameDesKindes2 = content
		} else if strings.Contains(v.Un6296, "Namekind3") {
			form.ZuweisungsemNameDesKindes3 = content
		} else if strings.Contains(v.Un6296, "Geburtkind1") {
			form.Geburtsdatum1 = content
		} else if strings.Contains(v.Un6296, "Geburtkind2") {
			form.Geburtsdatum2 = content
		} else if strings.Contains(v.Un6296, "Geburtkind3") {
			form.Geburtsdatum3 = content
		} else if strings.Contains(v.Un6296, "lBeziehkind1") {
			form.ZuweisungsemBelasteteLine1 = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "lBeziehkind2") {
			form.ZuweisungsemBelasteteLine2 = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "lBeziehkind3") {
			form.ZuweisungsemBelasteteLine3 = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "lAnforderung") {
			form.SonstigeAJa = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "cAnforderung") {
			form.SonstigeAText = content
		} else if strings.Contains(v.Un6296, "dAusstellung") {
			form.Datum1 = content
		}
	}

	return &form
}

func extractMusterForm22(rawData []bdt_model.Un6296) *MusterForm22 {
	form := MusterForm22{}
	for _, v := range rawData {
		content := strings.Join(v.Un6297, "")

		if strings.Contains(v.Un6296, "Therapeut") {
			form.Therapeuten = content
		} else if strings.Contains(v.Un6296, "Lanr") {
			form.Arztnummer = content
		} else if strings.Contains(v.Un6296, "Bsnr") {
			form.Betrieb = content
		} else if strings.Contains(v.Un6296, "Diagnose") {
			form.Angaben = content
		} else if strings.Contains(v.Un6296, "AbklerfolgJ") {
			form.Erfolgt = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "MitbehandJ") {
			form.Arztliche = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Mitbehand") {
			form.Textbox_Arztliche = content
		} else if strings.Contains(v.Un6296, "Ausstellung") {
			form.Date_Datum = content
		}
	}

	return &form
}

func extractMusterForm65(rawData []bdt_model.Un6296) *MusterForm65 {
	form := MusterForm65{}
	for _, v := range rawData {
		content := strings.Join(v.Un6297, "")

		if strings.Contains(v.Un6296, "Ausstellung") {
			form.Date_Datum = content
		}
	}

	return &form
}

func extractMusterForm20(rawData []bdt_model.Un6296) *MusterForm20 {
	form := MusterForm20{}
	for _, v := range rawData {
		content := strings.Join(v.Un6297, "")

		if strings.Contains(v.Un6296, "Vortaetig") {
			if len(v.Un6297) > 0 {
				form.TextboxZuletztLine1 = v.Un6297[0]
			}
			if len(v.Un6297) > 1 {
				form.TextboxZuletztLine2 = v.Un6297[1]
			}
			if len(v.Un6297) > 2 {
				form.TextboxZuletztLine3 = v.Un6297[2]
			}
		} else if strings.Contains(v.Un6296, "Vorstunden") {
			form.TextboxZuletztStunden = content
		} else if strings.Contains(v.Un6296, "Taetigvom1") {
			form.DateVom1 = content
		} else if strings.Contains(v.Un6296, "Taetigvom2") {
			form.DateVom2 = content
		} else if strings.Contains(v.Un6296, "Taetigvom3") {
			form.DateVom3 = content
		} else if strings.Contains(v.Un6296, "Taetigvom4") {
			form.DateVom4 = content
		} else if strings.Contains(v.Un6296, "Taetigbis1") {
			form.DateBis1 = content
		} else if strings.Contains(v.Un6296, "Taetigbis2") {
			form.DateBis2 = content
		} else if strings.Contains(v.Un6296, "Taetigbis3") {
			form.DateBis3 = content
		} else if strings.Contains(v.Un6296, "Taetigbis4") {
			form.DateBis4 = content
		} else if strings.Contains(v.Un6296, "Taetigstd1") {
			form.TextboxTaglichLine1 = content
		} else if strings.Contains(v.Un6296, "Taetigstd2") {
			form.TextboxTaglichLine2 = content
		} else if strings.Contains(v.Un6296, "Taetigstd3") {
			form.TextboxTaglichLine3 = content
		} else if strings.Contains(v.Un6296, "Taetigstd4") {
			form.TextboxTaglichLine4 = content
		}
	}

	return &form
}

func extractMusterForm12(rawData []bdt_model.Un6296) *MusterForm12 {
	form := MusterForm12{}
	for _, v := range rawData {
		content := strings.Join(v.Un6297, "")
		if strings.Contains(v.Un6296, "Icd1") {
			form.ICD1 = content
		} else if strings.Contains(v.Un6296, "Icd2") {
			form.ICD2 = content
		} else if strings.Contains(v.Un6296, "Icd3") {
			form.ICD3 = content
		} else if strings.Contains(v.Un6296, "Icd4") {
			form.ICD4 = content
		} else if strings.Contains(v.Un6296, "ErstJ") {
			form.Erst = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "FolgeJ") {
			form.Folge = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Verordvom") {
			form.VomDate = content
		} else if strings.Contains(v.Un6296, "Verordbis") {
			form.BisDate = content
		} else if strings.Contains(v.Un6296, "Praepmed") {
			if len(v.Un6297) > 0 {
				form.MedikamentengabeLine1 = v.Un6297[0]
			}
			if len(v.Un6297) > 1 {
				form.MedikamentengabeLine2 = v.Un6297[1]
			}
		} else if strings.Contains(v.Un6296, "MedherrichJ") {
			form.HerrichtenDer = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Medherwo") {
			form.HerrichtenTgl = content
		} else if strings.Contains(v.Un6296, "Medhertgl") {
			form.HerrichtenWtl = content
		} else if strings.Contains(v.Un6296, "Medhervom") {
			form.HerrichtenVom = content
		} else if strings.Contains(v.Un6296, "Medherbis") {
			form.HerrichtenBis = content
		} else if strings.Contains(v.Un6296, "MedgebenJ") {
			form.Medikamentengabe = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Medgebentgl") {
			form.MedikamentengabeTgl = content
		} else if strings.Contains(v.Un6296, "Medgebenwo") {
			form.MedikamentengabeWtl = content
		} else if strings.Contains(v.Un6296, "Medgebenvom") {
			form.MedikamentengabeVom = content
		} else if strings.Contains(v.Un6296, "Medgebenbis") {
			form.MedikamentengabeBis = content
		} else if strings.Contains(v.Un6296, "KomprbehbdJ") {
			form.Beidseits = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "KomprstanJ") {
			form.Anziehen = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Komprsttgl") {
			form.Komprsttgl = content
		} else if strings.Contains(v.Un6296, "Komprstwo") {
			form.Komprstwo = content
		} else if strings.Contains(v.Un6296, "Komprstvom") {
			form.Komprstvom = content
		} else if strings.Contains(v.Un6296, "Komprstbis") {
			form.Komprstbis = content
		} else if strings.Contains(v.Un6296, "KhvermeidJ") {
			form.Krankenhau = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "GrundpflJ") {
			form.Grundpflege = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Grundpfltgl") {
			form.GrundpflegeTgl = content
		} else if strings.Contains(v.Un6296, "Grundpflwo") {
			form.GrundpflegeWtl = content
		} else if strings.Contains(v.Un6296, "Grundpflvom") {
			form.GrundpflegeVom = content
		} else if strings.Contains(v.Un6296, "Grundpflbis") {
			form.GrundpflegeBis = content
		} else if strings.Contains(v.Un6296, "HauswirtJ") {
			form.Hauswirtschaft = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Hauswirttgl") {
			form.HauswirtschaftTgl = content
		} else if strings.Contains(v.Un6296, "Hauswirtwo") {
			form.HauswirtschaftWtl = content
		} else if strings.Contains(v.Un6296, "Hauswirtvom") {
			form.HauswirtschaftVom = content
		} else if strings.Contains(v.Un6296, "Hauswirtbis") {
			form.HauswirtschaftBis = content
		}
	}
	return &form
}

func extractMusterForm19(rawData []bdt_model.Un6296) *MusterForm19 {
	form := MusterForm19{}
	for _, v := range rawData {
		content := strings.Join(v.Un6297, "")
		if strings.Contains(v.Un6296, "Quartal") {
			form.Textbox_Quarter = content
		} else if strings.Contains(v.Un6296, "Jahr") {
			form.Textbox_Year = content
		} else if strings.Contains(v.Un6296, "WeiblichJ") {
			if getBoolValue(content) {
				form.Textbox_Geschlecht_W = GESCHLECHT_W
			}
		} else if strings.Contains(v.Un6296, "MaennlichJ") {
			if getBoolValue(content) {
				form.Textbox_Geschlecht_M = GESCHLECHT_M
			}
		} else if strings.Contains(v.Un6296, "NotfallJ") {
			form.Chexkbox_Notfall = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "NotfalldienstJ") {
			form.Checkbox_Notfalldienst = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "VertretungJ") {
			form.Chexkbox_Vertretung = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "UnfallJ") {
			form.Chexkbox_Unfall = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "Diagnose") {
			form.Textbox_Diagnose = content
		} else if strings.Contains(v.Un6296, "Befund") {
			form.Textbox_Befund = content
		} else if strings.Contains(v.Un6296, "Aubis") {
			form.Textbox_Aubis = content
		} else if strings.Contains(v.Un6296, "Weiterarzt") {
			form.Weiterarzt = content
		}
	}
	return &form
}

func extractMusterFormF1050(rawData []bdt_model.Un6296) *MusterFormF1050 {
	form := MusterFormF1050{}
	for _, v := range rawData {
		content := strings.Join(v.Un6297, "")
		if strings.Contains(v.Un6296, "cBgkasse") {
			form.Textbox_unfallversicher_0 = content
		} else if strings.Contains(v.Un6296, "cLfdnr") {
			form.Textbox_lfd_0 = content
		} else if strings.Contains(v.Un6296, "cKassenname") {
			form.Label_active_insurance_name_0 = content
		} else if strings.Contains(v.Un6296, "cVersname") {
			form.Label_lastname_0 = content
			form.Label_patient_fullname_1 = content
		} else if strings.Contains(v.Un6296, "cBeschals") {
			form.Textbox_job_0 = content
		} else if strings.Contains(v.Un6296, "cBeschseit") {
			form.Label_date_label_custom_start_datum_0 = content
		} else if strings.Contains(v.Un6296, "cVersadresse") {
			form.Label_address_0 = content
		} else if strings.Contains(v.Un6296, "cStaat") {
			form.Textbox_nationality_0 = content
		} else if strings.Contains(v.Un6296, "cGeschlecht") {
			form.Textbox_gender_0 = content
		} else if strings.Contains(v.Un6296, "cUnfbetrieb") {
			form.Textbox_company_address_0 = content
		} else if strings.Contains(v.Un6296, "dEintreffam") {
			form.Date_label_custom_arrive_date_0 = content
		} else if strings.Contains(v.Un6296, "cEintreffum") {
			form.Label_time_arrive_time_0 = content
		} else if strings.Contains(v.Un6296, "dUnfalltag") {
			form.Date_label_custom_accident_day_0 = content
		} else if strings.Contains(v.Un6296, "cUnfallzeit") {
			form.Label_time_accident_time_0 = content
		} else if strings.Contains(v.Un6296, "cUnfallort") {
			form.Textbox_accident_place_0 = content
		} else if strings.Contains(v.Un6296, "cArbzeitanf") {
			form.Label_time_start_time_0 = content
		} else if strings.Contains(v.Un6296, "cArbzeitende") {
			form.Label_time_end_time_0 = content
		} else if strings.Contains(v.Un6296, "lZudarztJ") {
			form.Checkbox_die_versicherte_0 = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "lZudarztN") {
			form.Checkbox_eine_vorstellung_0 = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "dZudarztam") {
			form.Date_die_versicherte_0 = content
		} else if strings.Contains(v.Un6296, "cBehdarztadr") {
			form.Textbox_die_versicherte_0 = content
		} else if strings.Contains(v.Un6296, "lAuweiterJ") {
			form.Checkbox_die_unfall_0 = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "cHergang") {
			form.Area_angaben_0 = content
		} else if strings.Contains(v.Un6296, "cKlagen") {
			form.Area_beschwerden_0 = content
		} else if strings.Contains(v.Un6296, "cBefund") {
			form.Area_kurze_angabe_0 = content
		} else if strings.Contains(v.Un6296, "cDiagnose") {
			form.Area_diagnose_0 = content
		} else if strings.Contains(v.Un6296, "lAllghbN") {
			form.Checkbox_ist_weitere_nein_0 = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "lAllghbJ") {
			form.Checkbox_ist_weitere_ja_0 = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "lAllghbmichJ") {
			form.Checkbox_durch_mich_0 = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "lAllghbarztJ") {
			form.Checkbox_durch_andere_0 = getBoolValue(content)
		} else if strings.Contains(v.Un6296, "cBeharztadr") {
			form.Area_durch_andere_0 = content
		}
	}
	return &form
}

func mapMuster4(form *MusterForm4, dateFormat string) any {
	fromDate, fromDateErr := util.ConvertStringToMillisecond(form.FromDate, dateFormat)
	toDate, toDateErr := util.ConvertStringToMillisecond(form.ToDate, dateFormat)
	payload := map[formkey.FormKey_MUSTER_4]any{
		formkey.MUSTER_4_CHECKBOX_ZUZAH_LUNGS_PFLICHT:                       form.Zuzahlungspflichtig,
		formkey.MUSTER_4_CHECKBOX_ZUZAH_FREI:                                form.Zuzahlungsfrei,
		formkey.MUSTER_4_CHECKBOX_UNFALL_UNFALLFOLGE:                        form.Unfallfolgen,
		formkey.MUSTER_4_CHECKBOX_ARBEITSUNFALL_BERUFSKRANKHEIT:             form.Arbeitsunfall,
		formkey.MUSTER_4_CHECKBOX_VERSORGUNGSLEIDEN:                         form.BVG,
		formkey.MUSTER_4_CHECKBOX_HINFAHRT:                                  form.Hinfahrt,
		formkey.MUSTER_4_CHECKBOX_RUCKFAHRT:                                 form.Ruckfahrtm,
		formkey.MUSTER_4_CHECKBOX_VOLL_TEILSTATIONARE_KRANKENHAUSBEHANDLUNG: form.Voll,
		formkey.MUSTER_4_CHECKBOX_VOR_NACHSTATIONARE_BEHANDLUNG:             form.Vor,
		formkey.MUSTER_4_CHECKBOX_AMBULANTE_BEHANDLUNG:                      form.Ambulante,
		formkey.MUSTER_4_CHECKBOX_ANDERER_GRUND:                             form.AndererGrund,
		formkey.MUSTER_4_TEXTBOX_FAHRTEN_ZU_HOSPIZEN:                        form.AndererGrundText,
		formkey.MUSTER_4_CHECKBOX_HOCHFREQUENTE_BEHANDLUNG:                  form.Dialyse,
		formkey.MUSTER_4_CHECKBOX_VERGLEICHBARER_AUSNAHMEFALL:               form.Vergleichbarer,
		formkey.MUSTER_4_CHECKBOX_DAUERHAFTE_MOBILITATSBEEINTRACHTIGUNG:     form.Dauerhafte,
		formkey.MUSTER_4_CHECKBOX_ANDERER_GRUND_FUR_FAHRT:                   form.AndererGrundFur,
		formkey.MUSTER_4_DATE_LABEL_CUSTOM_VOM:                              function.If(fromDateErr == nil, fromDate, 0),
		formkey.MUSTER_4_TEXTBOX_PRO_WOCHE:                                  form.Frequency,
		formkey.MUSTER_4_DATE_LABEL_CUSTOM_BIS:                              function.If(toDateErr == nil, toDate, 0),
		formkey.MUSTER_4_TEXTBOX_BEHANDLUNGSSTATTE:                          form.Behandlung,
		formkey.MUSTER_4_CHECKBOX_TAXI_MIETWAGEN:                            form.Taxi,
		formkey.MUSTER_4_CHECKBOX_ROLLSTUHL:                                 form.Rollstuhl,
		formkey.MUSTER_4_CHECKBOX_TRAGESTUHL:                                form.Tragestuhl,
		formkey.MUSTER_4_CHECKBOX_LIEGEND:                                   form.Liegend,
		formkey.MUSTER_4_CHECKBOX_KTW_DA_MEDIZINISCH:                        form.Ktw,
		formkey.MUSTER_4_TEXTBOX_ART_UND_AUSSTATTUNG_1:                      form.Einrichtung,
		formkey.MUSTER_4_CHECKBOX_RTW:                                       form.Rtw,
		formkey.MUSTER_4_CHECKBOX_NAW_NEF:                                   form.Naw,
		formkey.MUSTER_4_CHECKBOX_ANDERE:                                    form.AndereEinrichtung,
		formkey.MUSTER_4_TEXTBOX_ANDERE:                                     form.AndereEinrichtungText,
		formkey.MUSTER_4_TEXTBOX_BEGRUNDUNG_SONSTIGES:                       form.Sonstiges,
	}
	return payload
}

func mapHeilmittelForm(form *MusterForm13, _ string) any {
	payload := map[formkey.FormKey_MUSTER_13]any{
		formkey.MUSTER_13_CHECKBOX_PHYSIOTHERAPIE_0:        form.Physiotherapie,
		formkey.MUSTER_13_CHECKBOX_PODOLOGISCHE_0:          form.Podologische,
		formkey.MUSTER_13_CHECKBOX_SCHLUCKTHERAPIE_0:       form.Stimm,
		formkey.MUSTER_13_CHECKBOX_ERGOTHERAPIE_0:          form.Ergotherapie,
		formkey.MUSTER_13_CHECKBOX_ERNAHRUNGSTHERAPIE_0:    form.Ernahrungstherapie,
		formkey.MUSTER_13_CHECKBOX_ZUZAHLUNGSFREI_0:        form.Gebuhrenfrei,
		formkey.MUSTER_13_CHECKBOX_ZUZAHLUNGPFLICHT_0:      form.Gebuhrenpflichtig,
		formkey.MUSTER_13_CHECKBOX_UNFALLFOLGEN_0:          form.Unfallfolgen,
		formkey.MUSTER_13_CHECKBOX_BVG_0:                   form.BVG,
		formkey.MUSTER_13_LABEL_ICD_CODE10_LINE1_0:         form.ICD1,
		formkey.MUSTER_13_LABEL_ICD_CODE10_LINE2_0:         form.ICD2,
		formkey.MUSTER_13_AREA_TEXTBOX_ICD_CODE10_0:        form.ICDText,
		formkey.MUSTER_13_LABEL_DIAGNOSE_GRUPPE_0:          form.Diagnosegruppe,
		formkey.MUSTER_13_CHECKBOX_LEITSYMPTOMATIK_A_0:     form.Leitsymptomatik_a,
		formkey.MUSTER_13_CHECKBOX_LEITSYMPTOMATIK_B_0:     form.Leitsymptomatik_b,
		formkey.MUSTER_13_CHECKBOX_LEITSYMPTOMATIK_C_0:     form.Leitsymptomatik_c,
		formkey.MUSTER_13_CHECKBOX_PATIENTENINDIVIDUELLE_0: form.Leitsymptomatik_c,
		formkey.MUSTER_13_AREA_TEXTBOX_LEITSYMTOMATIK_0:    form.LeitsymptomatikText,
		formkey.MUSTER_13_TEXTBOX_HEILMITTEL_LINE1_0:       form.Heilmittel_1,
		formkey.MUSTER_13_TEXTBOX_HEILMITTEL_LINE2_0:       form.Heilmittel_2,
		formkey.MUSTER_13_TEXTBOX_HEILMITTEL_LINE3_0:       form.Heilmittel_3,
		formkey.MUSTER_13_TEXTBOX_BEHANDLUNG_LINE1_0:       form.Quantity_1,
		formkey.MUSTER_13_TEXTBOX_BEHANDLUNG_LINE2_0:       form.Quantity_2,
		formkey.MUSTER_13_TEXTBOX_BEHANDLUNG_LINE3_0:       form.Quantity_3,
		formkey.MUSTER_13_TEXTBOX_ERGANZENDES_0:            form.Heilmittel_4,
		formkey.MUSTER_13_TEXTBOX_BEHANDLUNG_LINE4_0:       form.Quantity_4,
		formkey.MUSTER_13_CHECKBOX_THERAPIEBERICHT_0:       form.Therapiebericht,
		formkey.MUSTER_13_CHECKBOX_HAUSBESUCH_JA_0:         form.Hausbesuch_ja,
		formkey.MUSTER_13_CHECKBOX_HAUSBESUCH_NEIN_0:       form.Hausbesuch_nein,
		formkey.MUSTER_13_LABEL_THERAPIE_FREQUENZ_0:        form.Therapiefrequenz,
		formkey.MUSTER_13_CHECKBOX_DRINGLICHER_14_TAGEN_0:  form.Dringlicher,
		formkey.MUSTER_13_AREA_TEXTBOX_THERAPIEZIELE_0:     form.TherapiezieleText,
	}
	return payload
}

func mapMuster56(form *MusterForm56, _ string) any {
	payload := map[formkey.FormKey_MUSTER_56]any{
		formkey.MUSTER_56_CHECKBOX_56_FUR_REHABILITATIONSSPORT_0: form.Rehabilitationssport,
		formkey.MUSTER_56_CHECKBOX_56_FUR_FUNKTIONSTRAINING_0:    form.Funktionstraining,
	}
	return payload
}

func mapMuster10A(form10A *MusterForm10A, dateFormat string) any {
	abnahmedatumMilis, abnahmedatumMilisErr := util.ConvertStringToMillisecond(form10A.Abnahmedatum, dateFormat)
	abnahmezeitMilis, abnahmezeitMilisErr := util.ConvertStringToMillisecond(form10A.Abnahmedatum+form10A.Abnahmezeit, dateFormat+"1504")
	payload := map[formkey.FormKey_MUSTER_10A_N]any{
		formkey.MUSTER_10A_N_LABEL_GENDER:                    form10A.Geschlecht,
		formkey.MUSTER_10A_N_CHECKBOX_KURATIV:                form10A.Kurativ,
		formkey.MUSTER_10A_N_CHECKBOX_PRAVENTIV:              form10A.Praventiv,
		formkey.MUSTER_10A_N_CHECKBOX_BEHANDL:                form10A.Behandlung,
		formkey.MUSTER_10A_N_CHECKBOX_UNFALL:                 form10A.Unfallfolgen,
		formkey.MUSTER_10A_N_TEXTBOX_KNAPPSCHAFTS_KENNZIFFER: form10A.Knappschaftskennziffer,
		formkey.MUSTER_10A_N_TEXTBOX_SSW:                     form10A.SSW,
		formkey.MUSTER_10A_N_TEXTBOX_ZUSAT:                   form10A.Auftragsnummer,
		formkey.MUSTER_10A_N_DATE_LABEL_CUSTOM_ABNAHMEDATUM:  function.If(abnahmedatumMilisErr == nil, abnahmedatumMilis, 0),
		formkey.MUSTER_10A_N_DATE_ABNAHMEZEIT:                function.If(abnahmezeitMilisErr == nil, abnahmezeitMilis, 0),
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_1:           form10A.Befund,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_2:           form10A.GroBes,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_3:           form10A.Kleines,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_4:           form10A.HbA1c,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_5:           form10A.Retikulozyten,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_6:           form10A.BlutSenkung,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_51:          form10A.Glukose1,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_52:          form10A.Glukose2,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_53:          form10A.Glukose3,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_54:          form10A.Glukose4,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_62:          form10A.Harnstreifentest,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_63:          form10A.Nuchternplasmaglukose,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_64:          form10A.Lipidprofil,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_55:          form10A.Status,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_56:          form10A.Mikroalbumin,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_58:          form10A.GlukoseUrin,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_60:          form10A.Sediment,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_8:           form10A.Quick,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_9:           form10A.QuickUnter,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_10:          form10A.Thrombinzeit,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_11:          form10A.PTT,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_13:          form10A.Alkalische,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_14:          form10A.Amylase,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_15:          form10A.ASL,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_16:          form10A.BilirubinDir,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_17:          form10A.BilirubinGes,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_18:          form10A.Calcium,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_19:          form10A.Cholesterin,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_20:          form10A.Cholinesterase,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_21:          form10A.CK,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_23:          form10A.CRP,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_24:          form10A.Eisen,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_25:          form10A.Elektro,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_26:          form10A.Gesamt,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_27:          form10A.Gamma,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_28:          form10A.Glukose,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_29:          form10A.GOT,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_30:          form10A.GPT,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_31:          form10A.Harnsaure,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_32:          form10A.Harnstoff,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_34:          form10A.HDL,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_35:          form10A.IgA,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_36:          form10A.IgG,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_37:          form10A.IgM,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_38:          form10A.Kalium,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_39:          form10A.Kreatinin,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_40:          form10A.Clearance,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_41:          form10A.LDH,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_42:          form10A.LDL,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_43:          form10A.Lipase,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_44:          form10A.Natrium,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_45:          form10A.OP,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_46:          form10A.Phosphat,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_47:          form10A.Transferrin,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_48:          form10A.Triglyceride,
		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_49:          form10A.TSH,
	}

	return payload
}

func mapMuster63(form *MusterForm63, dateFormat string) any {
	fromDate, fromDateErr := util.ConvertStringToMillisecond(form.FromDate, dateFormat)
	toDate, toDateErr := util.ConvertStringToMillisecond(form.ToDate, dateFormat)
	payload := map[formkey.FormKey_MUSTER_N63A]any{
		formkey.MUSTER_N63A_CHECKBOX_63_ERSTVERORDNUNG_0:             form.Erstverordnung,
		formkey.MUSTER_N63A_CHECKBOX_63_FOLGEVERORDNUNG_0:            form.Folgeverordnung,
		formkey.MUSTER_N63A_CHECKBOX_63_UNFALL_UNFALLFOLGEN_0:        form.Unfall,
		formkey.MUSTER_N63A_DATE_LABEL_CUSTOM_VOM_0:                  function.If(fromDateErr == nil, fromDate, 0),
		formkey.MUSTER_N63A_DATE_LABEL_CUSTOM_BIS_0:                  function.If(toDateErr == nil, toDate, 0),
		formkey.MUSTER_N63A_TEXTBOX_DIAGNOSE_LINE1_0:                 form.ICD,
		formkey.MUSTER_N63A_CHECKBOX_63_AUS_SCHMERZ_0:                form.Schmerzsymptomatik,
		formkey.MUSTER_N63A_CHECKBOX_63_AUS_URO_0:                    form.Urogenitale,
		formkey.MUSTER_N63A_CHECKBOX_63_AUS_RESPIRATORIS_0:           form.Respiratorische,
		formkey.MUSTER_N63A_CHECKBOX_63_AUS_GASTRO_0:                 form.Gastrointestinale,
		formkey.MUSTER_N63A_CHECKBOX_63_AUS_ULZERIE_0:                form.Ulzerierende,
		formkey.MUSTER_N63A_CHECKBOX_63_AUS_NEUROLOGISCHE_0:          form.Neurologische,
		formkey.MUSTER_N63A_CHECKBOX_63_AUS_KOMPLEXES_0:              form.Sonstiges,
		formkey.MUSTER_N63A_CHECKBOX_63_BERATUNG_0:                   form.Beratung,
		formkey.MUSTER_N63A_CHECKBOX_63_DES_BEH_ARZTES_0:             form.Arztes,
		formkey.MUSTER_N63A_CHECKBOX_63_DES_BEH_PFLEGEFACHKRAFT_0:    form.Pflegefachkraft,
		formkey.MUSTER_N63A_CHECKBOX_63_DES_PAT_DER_ANGEHORIGEN_0:    form.Patienten,
		formkey.MUSTER_N63A_CHECKBOX_63_KOORDINATION_0:               form.Koordination,
		formkey.MUSTER_N63A_CHECKBOX_63_ADDITIV_UNT_TEILVERSORGUNG_0: form.Additiv,
		formkey.MUSTER_N63A_CHECKBOX_63_VOL_VERSORGUNG_0:             form.Vollstandige,
	}

	return payload
}

func mapMusterFormPTV1(form *FormPTV1, dateFormat string) any {
	fromDate, _ := util.ConvertStringToMillisecond(form.DateZwar, dateFormat)
	toDate, _ := util.ConvertStringToMillisecond(form.DateGgf, dateFormat)
	dateDatum, _ := util.ConvertStringToMillisecond(form.DateDatum, dateFormat)
	payload := map[formkey.FormKey_MUSTER_PTV_1]any{
		formkey.MUSTER_PTV_1_AREA_TEXTBOX_ADDRESS:            form.AddressContent,
		formkey.MUSTER_PTV_1_CHECKBOX_BEI_MIR:                form.BeiMir,
		formkey.MUSTER_PTV_1_CHECKBOX_ANALYTISCHE:            form.Analytische,
		formkey.MUSTER_PTV_1_CHECKBOX_SYSTEMISCHE:            form.Systemische,
		formkey.MUSTER_PTV_1_CHECKBOX_TIEFENPSYCHOLOGISCH:    form.Tiefenpsychologisch,
		formkey.MUSTER_PTV_1_CHECKBOX_VERHALTENSTHERAPIE:     form.Verhaltenstherapie,
		formkey.MUSTER_PTV_1_CHECKBOX_EINZELTHERAPIE:         form.Einzeltherapie,
		formkey.MUSTER_PTV_1_CHECKBOX_GRUPPENTHERAPIE:        form.Gruppentherapie,
		formkey.MUSTER_PTV_1_CHECKBOX_KOMBINATIONSBEHANDLUNG: form.Kombinationsbehandlung,
		formkey.MUSTER_PTV_1_CHECKBOX_FOLGEANTRAG:            form.Folgeantrag,
		formkey.MUSTER_PTV_1_CHECKBOX_ERSTANTRAG:             form.Erstantrag,
		formkey.MUSTER_PTV_1_CHECKBOX_JA:                     form.WurdenJa,
		formkey.MUSTER_PTV_1_CHECKBOX_NEIN:                   form.WurdenNein,
		formkey.MUSTER_PTV_1_CHECKBOX_WAREN_JA:               form.WarenJa,
		formkey.MUSTER_PTV_1_CHECKBOX_WAREN_NEIN:             form.WarenNein,
		formkey.MUSTER_PTV_1_CHECKBOX_WURDE_JA:               form.WurdeJa,
		formkey.MUSTER_PTV_1_CHECKBOX_WURDE_NEIN:             form.WurdeNein,
		formkey.MUSTER_PTV_1_DATE_LABEL_CUSTOM_ZWAR:          fromDate,
		formkey.MUSTER_PTV_1_DATE_LABEL_CUSTOM_GGF:           toDate,
		formkey.MUSTER_PTV_1_DATE_LABEL_CUSTOM_DATUM:         dateDatum,
	}

	return payload
}

func mapMusterForm10(form *MusterForm10, _ string) any {
	quarter, qErr := strconv.ParseInt(form.Quarter, 10, 32)
	var quartalInt int64
	if form.Year != "" && qErr == nil {
		var month string
		switch quarter {
		case 1:
			month = "02"
		case 2:
			month = "05"
		case 3:
			month = "08"
		default:
			month = "11"
		}
		quartalInt, _ = util.ConvertStringToMillisecond(month+form.Year, util.MMYY)
	}

	payload := map[formkey.FormKey_MUSTER_10_N]any{
		formkey.MUSTER_10_N_CHECKBOX_KURATIV:                form.Kurativ,
		formkey.MUSTER_10_N_CHECKBOX_PRAVENTIV:              form.Praventiv,
		formkey.MUSTER_10_N_CHECKBOX_BEHANDL:                form.Behandlung,
		formkey.MUSTER_10_N_CHECKBOX_UNFALL:                 form.Unfall,
		formkey.MUSTER_10_N_TEXTBOX_KNAPPSCHAFTS_KENNZIFFER: form.Knappschaft,
		formkey.MUSTER_10_N_DATE_QUARTAL:                    quartalInt,
		formkey.MUSTER_10_N_CHECKBOX_KONTROLLUNTERSUCHUNG:   form.Kontrolun,
		formkey.MUSTER_10_N_LABEL_GENDER:                    form.Gender,
		formkey.MUSTER_10_N_CHECKBOX_SER:                    form.Ser,
		formkey.MUSTER_10_N_CHECKBOX_EINGESCHRANKTER:        form.Eignes,
		formkey.MUSTER_10_N_CHECKBOX_EMPFANGNISREGELUNG:     form.Empfang,
		formkey.MUSTER_10_N_TEXTBOX_BETRI:                   form.Beitriebsstatten,
		formkey.MUSTER_10_N_TEXTBOX_ARZT:                    form.Arzt,
		formkey.MUSTER_10_N_DATE_LABEL_CUSTOM_ABNAHMEDATUM:  form.Abnahmedatum,
		formkey.MUSTER_10_N_DATE_ABNAHMEZEIT:                form.Abnahmezeit,
		formkey.MUSTER_10_N_CHECKBOX_BEFUND_EILT:            form.BefundEilt,
		formkey.MUSTER_10_N_CHECKBOX_TELEFON:                form.Telefon,
		formkey.MUSTER_10_N_CHECKBOX_FAX:                    form.Fax,
		formkey.MUSTER_10_N_TEXTBOX_NR:                      form.Nr,
		formkey.MUSTER_10_N_TEXTBOX_SSW:                     form.SSW,
		formkey.MUSTER_10_N_TEXTBOX_DIAGNOSE:                form.Diagnose,
		formkey.MUSTER_10_N_TEXTBOX_BEFUND_LINE1:            form.Befund,
		formkey.MUSTER_10_N_TEXTBOX_AUFTRAG_LINE1:           form.Auftrag,
	}

	return payload
}

func mapMusterForm36(form *MusterForm36, _ string) any {
	payload := map[formkey.FormKey_MUSTER_36]any{
		formkey.MUSTER_36_CHECKBOX_BEWEGUNGSGEWOHNHEITEN: form.Bewegung,
		formkey.MUSTER_36_CHECKBOX_ERNAHRUNG:             form.Ernahrung,
		formkey.MUSTER_36_CHECKBOX_STRESSMANAGEMENT:      form.StressManagement,
		formkey.MUSTER_36_CHECKBOX_SUCHTMITTELKONSUM:     form.Sucht,
		formkey.MUSTER_36_TEXTBOX_SONSTIGES:              form.Sontiges,
		formkey.MUSTER_36_TEXTBOX_36_HINWEISE_LINE1:      form.Hinweise,
	}

	return payload
}

func mapMusterForm52(form *MusterForm52, dateFormat string) any {
	dateWeider, _ := util.ConvertStringToMillisecond(form.WiederDate, dateFormat)
	dateDatum, _ := util.ConvertStringToMillisecond(form.DateDatum, dateFormat)
	payload := map[formkey.FormKey_MUSTER_52]any{}
	icdKeys := []formkey.FormKey_MUSTER_52{
		formkey.MUSTER_52_TEXTBOX_WEGEN_ICD0,
		formkey.MUSTER_52_TEXTBOX_WEGEN_ICD1,
		formkey.MUSTER_52_TEXTBOX_WEGEN_ICD2,
		formkey.MUSTER_52_TEXTBOX_WEGEN_ICD3,
		formkey.MUSTER_52_TEXTBOX_WEGEN_ICD4,
		formkey.MUSTER_52_TEXTBOX_WEGEN_ICD5,
		formkey.MUSTER_52_TEXTBOX_WEGEN_ICD6,
		formkey.MUSTER_52_TEXTBOX_WEGEN_ICD7,
		formkey.MUSTER_52_TEXTBOX_WEGEN_ICD8,
		formkey.MUSTER_52_TEXTBOX_WEGEN_ICD9,
		formkey.MUSTER_52_TEXTBOX_WEGEN_ICD10,
		formkey.MUSTER_52_TEXTBOX_WEGEN_ICD11,
	}

	for i, v := range form.ICDs {
		payload[icdKeys[i]] = v
	}

	payload[formkey.MUSTER_52_CHECKBOX_ERWERBSTATIG] = form.ErwerbCheckbox
	payload[formkey.MUSTER_52_TEXTBOX_ERWERBSTATIG] = form.ErwerbText
	payload[formkey.MUSTER_52_CHECKBOX_DER_VERSICHERTE] = form.VersicherteCheckbox
	payload[formkey.MUSTER_52_TEXTBOX_DER_VERSICHERTE] = form.VersicherteText
	payload[formkey.MUSTER_52_CHECKBOX_KANNDER_EMPFANGER_JA] = form.KannJa
	payload[formkey.MUSTER_52_CHECKBOX_KANNDER_EMPFANGER_NEIN] = form.KannNein
	payload[formkey.MUSTER_52_CHECKBOX_ZEITPUNKT_JA] = form.IstJa
	payload[formkey.MUSTER_52_CHECKBOX_ZEITPUNKT_NEIN] = form.IstNein
	payload[formkey.MUSTER_52_DATE_LABEL_FULL_DATE_WEIDEREINTRITTS] = dateWeider
	payload[formkey.MUSTER_52_TEXTBOX_WELCHE_KONSERVATIV] = form.Konservativ
	payload[formkey.MUSTER_52_TEXTBOX_WELCHE_OPERATIV] = form.Operativ
	payload[formkey.MUSTER_52_TEXTBOX_WEITERE_BEHANDELNDE] = form.WeitereBehandelnde
	payload[formkey.MUSTER_52_CHECKBOX_WELCHE_KEINE] = form.Keine
	payload[formkey.MUSTER_52_CHECKBOX_WELCHE_INNERBETRIEBLICHER] = form.Innerbetri
	payload[formkey.MUSTER_52_CHECKBOX_WELCHE_STUFENWEISE] = form.Stufen
	payload[formkey.MUSTER_52_CHECKBOX_WELCHE_MEDIZINISCHE] = form.Medizinische
	payload[formkey.MUSTER_52_CHECKBOX_WELCHE_PSYCHOTHERAPEUTISCHE] = form.Psychotherapeu
	payload[formkey.MUSTER_52_CHECKBOX_WELCHE_LEISTUNG] = form.Leistung
	payload[formkey.MUSTER_52_CHECKBOX_WELCHE_SONSTIGE] = form.WelcheSonstigeCheckbox
	payload[formkey.MUSTER_52_TEXTBOX_WELCHE_SONSTIGE] = form.WelcheSonstigeText
	payload[formkey.MUSTER_52_CHECKBOX_GIBT_NEIN] = form.GibtNein
	payload[formkey.MUSTER_52_CHECKBOX_GIBT_JA_FOLGENDE] = form.GibtJa
	payload[formkey.MUSTER_52_TEXTBOX_GIBT_ES_BEI_LINE1] = form.GibtEsBei
	payload[formkey.MUSTER_52_CHECKBOX_BESTEHT_JA] = form.BestehtJa
	payload[formkey.MUSTER_52_CHECKBOX_BESTEHT_NEIN] = form.BestehtNein
	payload[formkey.MUSTER_52_TEXTBOX_SONSTIGES_BEMERKUNGEN_LINE1] = form.SonstigeBemerkungen
	payload[formkey.MUSTER_52_DATE_LABEL_CUSTOM_DATUM] = dateDatum

	return payload
}

func mapMusterForm55(form *MusterForm55, dateFormat string) any {
	dateSeit, _ := util.ConvertStringToMillisecond(form.DateSeit, dateFormat)
	dateDatum, _ := util.ConvertStringToMillisecond(form.DateDatum, dateFormat)
	payload := map[formkey.FormKey_MUSTER_55]any{
		formkey.MUSTER_55_DATE_LABEL_CUSTOM_SEIT:                      dateSeit,
		formkey.MUSTER_55_TEXTBOX_ICD1:                                form.ICD1,
		formkey.MUSTER_55_TEXTBOX_ICD2:                                form.ICD2,
		formkey.MUSTER_55_TEXTBOX_ICD3:                                form.ICD3,
		formkey.MUSTER_55_CHECKBOX_KONTINUIERLICHE_JA_ENDE:            form.EndeJa,
		formkey.MUSTER_55_CHECKBOX_KONTINUIERLICHE_JA_VORAUSSICHTLICH: form.VorauJa,
		formkey.MUSTER_55_CHECKBOX_KONTINUIERLICHE_NEIN:               form.KontinNein,
		formkey.MUSTER_55_TEXTBOX_VORNAME:                             form.AngabenName,
		formkey.MUSTER_55_TEXTBOX_KRANKENKASSE:                        form.AngabenKranken,
		formkey.MUSTER_55_TEXTBOX_KRANKENVERSICHERTEN_NUMBER:          form.KrankenversichertenNumber,
		formkey.MUSTER_55_DATE_LABEL_CUSTOM_DATUM:                     dateDatum,
	}
	return payload
}

func mapMusterForm61(form *MusterForm61, dateFormat string) any {
	dateDatum, _ := util.ConvertStringToMillisecond(form.DateDatum, dateFormat)
	payload := map[formkey.FormKey_MUSTER_61]any{
		formkey.MUSTER_61_TEXTBOX_DIAGNOSE1_0:       form.Diagnose1,
		formkey.MUSTER_61_TEXTBOX_DIAGNOSE2_0:       form.Diagnose2,
		formkey.MUSTER_61_TEXTBOX_DIAGNOSE3_0:       form.Diagnose3,
		formkey.MUSTER_61_TEXTBOX_DIAGNOSE4_0:       form.Diagnose4,
		formkey.MUSTER_61_TEXTBOX_DIAGNOSE5_0:       form.Diagnose5,
		formkey.MUSTER_61_TEXTBOX_DIAGNOSE6_0:       form.Diagnose6,
		formkey.MUSTER_61_TEXTBOX_ICD10_CODE1_0:     form.IcdCode1,
		formkey.MUSTER_61_TEXTBOX_ICD10_CODE2_0:     form.IcdCode2,
		formkey.MUSTER_61_TEXTBOX_ICD10_CODE3_0:     form.IcdCode3,
		formkey.MUSTER_61_TEXTBOX_ICD10_CODE4_0:     form.IcdCode4,
		formkey.MUSTER_61_TEXTBOX_ICD10_CODE5_0:     form.IcdCode5,
		formkey.MUSTER_61_TEXTBOX_ICD10_CODE6_0:     form.IcdCode6,
		formkey.MUSTER_61_CHECKBOX_BERATUNG_0:       form.Beratung,
		formkey.MUSTER_61_CHECKBOX_PRUFUNG_0:        form.Prufung,
		formkey.MUSTER_61_TEXTBOX_WEITERE_LINE1_0:   form.WeitereLine1,
		formkey.MUSTER_61_TEXTBOX_WEITERE_LINE2_0:   form.WeitereLine2,
		formkey.MUSTER_61_DATE_LABEL_CUSTOM_DATUM_0: dateDatum,
	}
	return payload
}

func mapMusterForm61B(form *MusterForm61B, dateFormat string) any {
	datum, _ := util.ParseStringToTime(form.Date_label_custom_datum_4, dateFormat)
	payload := map[formkey.FormKey_MUSTER_61B]any{
		formkey.MUSTER_61B_CHECKBOX_ESHANDELT_1:                                form.Checkbox_esHandelt_1,
		formkey.MUSTER_61B_CHECKBOX_VERSJCHERTE_1:                              form.Checkbox_versjcherte_1,
		formkey.MUSTER_61B_TEXTBOX_KURZEANGABEN_LINE1_1:                        form.Textbox_kurzeAngaben_line1_1,
		formkey.MUSTER_61B_TEXTBOX_KURZEANGABEN_LINE2_1:                        form.Textbox_kurzeAngaben_line2_1,
		formkey.MUSTER_61B_TEXTBOX_KURZEANGABEN_LINE3_1:                        form.Textbox_kurzeAngaben_line3_1,
		formkey.MUSTER_61B_TEXTBOX_REHABILITATIONSRELEVANTE_LINE1_1:            form.Textbox_rehabilitationsrelevante_line1_1,
		formkey.MUSTER_61B_TEXTBOX_REHABILITATIONSRELEVANTE_LINE2_1:            form.Textbox_rehabilitationsrelevante_line2_1,
		formkey.MUSTER_61B_TEXTBOX_MOBILITATTUG_1:                              form.Textbox_mobilitatTug_1,
		formkey.MUSTER_61B_TEXTBOX_BISHERIGEARZTLICHE_LINE1_1:                  form.Textbox_bisherigeArztliche_line1_1,
		formkey.MUSTER_61B_TEXTBOX_BISHERIGEARZTLICHE_LINE2_1:                  form.Textbox_bisherigeArztliche_line2_1,
		formkey.MUSTER_61B_TEXTBOX_MOBILITATCHAIRRISE_1:                        form.Textbox_mobilitatChairRise_1,
		formkey.MUSTER_61B_TEXTBOX_MOBILITATHAND_1:                             form.Textbox_mobilitatHand_1,
		formkey.MUSTER_61B_TEXTBOX_MOBILITATODER_1:                             form.Textbox_mobilitatOder_1,
		formkey.MUSTER_61B_TEXTBOX_KOGNITIONMMST_1:                             form.Textbox_kognitionMmst_1,
		formkey.MUSTER_61B_TEXTBOX_KOGNITIONGDS15_1:                            form.Textbox_kognitionGds15_1,
		formkey.MUSTER_61B_TEXTBOX_KOGNITIONUHREN_1:                            form.Textbox_kognitionUhren_1,
		formkey.MUSTER_61B_TEXTBOX_MOBILITATDEMMI_1:                            form.Textbox_mobilitatDemmi_1,
		formkey.MUSTER_61B_TEXTBOX_MOBILITATTINETTI_1:                          form.Textbox_mobilitatTinetti_1,
		formkey.MUSTER_61B_TEXTBOX_SCHMERZ_1:                                   form.Textbox_schmerz_1,
		formkey.MUSTER_61B_TEXTBOX_HERZERGOMETRIE_1:                            form.Textbox_herzErgometrie_1,
		formkey.MUSTER_61B_TEXTBOX_HERZFEV1_1:                                  form.Textbox_herzFev1_1,
		formkey.MUSTER_61B_TEXTBOX_HERZVK_1:                                    form.Textbox_herzVk_1,
		formkey.MUSTER_61B_TEXTBOX_HERZNYHA_1:                                  form.Textbox_herzNyha_1,
		formkey.MUSTER_61B_CHECKBOX_HEILMITTEL_1:                               form.Checkbox_heilmittel_1,
		formkey.MUSTER_61B_CHECKBOX_REHABILITATIONSRELEVANTENEIN_1:             form.Checkbox_rehabilitationsrelevanteNein_1,
		formkey.MUSTER_61B_CHECKBOX_REHABILITATIONSRELEVANTEJA_1:               form.Checkbox_rehabilitationsrelevanteJa_1,
		formkey.MUSTER_61B_TEXTBOX_REHABILITATIONSRELEVANTE_1:                  form.Textbox_rehabilitationsrelevante_1,
		formkey.MUSTER_61B_CHECKBOX_LERNEN2_2:                                  form.Checkbox_lernen2_2,
		formkey.MUSTER_61B_CHECKBOX_LERNEN3_2:                                  form.Checkbox_lernen3_2,
		formkey.MUSTER_61B_CHECKBOX_LERNEN4_2:                                  form.Checkbox_lernen4_2,
		formkey.MUSTER_61B_CHECKBOX_ALLGEMEINE2_2:                              form.Checkbox_allgemeine2_2,
		formkey.MUSTER_61B_CHECKBOX_ALLGEMEINE3_2:                              form.Checkbox_allgemeine3_2,
		formkey.MUSTER_61B_CHECKBOX_ALLGEMEINE4_2:                              form.Checkbox_allgemeine4_2,
		formkey.MUSTER_61B_CHECKBOX_KOMMUNIKATION1_2:                           form.Checkbox_kommunikation1_2,
		formkey.MUSTER_61B_CHECKBOX_KOMMUNIKATION2_2:                           form.Checkbox_kommunikation2_2,
		formkey.MUSTER_61B_CHECKBOX_KOMMUNIKATION3_2:                           form.Checkbox_kommunikation3_2,
		formkey.MUSTER_61B_CHECKBOX_KOMMUNIKATION4_2:                           form.Checkbox_kommunikation4_2,
		formkey.MUSTER_61B_CHECKBOX_MOBILITAT_2:                                form.Checkbox_mobilitat_2,
		formkey.MUSTER_61B_CHECKBOX_MOBILITATTRANSFER1_2:                       form.Checkbox_mobilitatTransfer1_2,
		formkey.MUSTER_61B_CHECKBOX_MOBILITATTRANSFER2_2:                       form.Checkbox_mobilitatTransfer2_2,
		formkey.MUSTER_61B_CHECKBOX_MOBILITATTRANSFER3_2:                       form.Checkbox_mobilitatTransfer3_2,
		formkey.MUSTER_61B_CHECKBOX_MOBILITATTRANSFER4_2:                       form.Checkbox_mobilitatTransfer4_2,
		formkey.MUSTER_61B_CHECKBOX_MOBILITATSTEHEN1_2:                         form.Checkbox_mobilitatStehen1_2,
		formkey.MUSTER_61B_CHECKBOX_MOBILITATSTEHEN2_2:                         form.Checkbox_mobilitatStehen2_2,
		formkey.MUSTER_61B_CHECKBOX_MOBILITATSTEHEN3_2:                         form.Checkbox_mobilitatStehen3_2,
		formkey.MUSTER_61B_CHECKBOX_MOBILITATSTEHEN4_2:                         form.Checkbox_mobilitatStehen4_2,
		formkey.MUSTER_61B_CHECKBOX_MOBILITATTREPPENSTEIGEN1_2:                 form.Checkbox_mobilitatTreppensteigen1_2,
		formkey.MUSTER_61B_CHECKBOX_MOBILITATTREPPENSTEIGEN2_2:                 form.Checkbox_mobilitatTreppensteigen2_2,
		formkey.MUSTER_61B_CHECKBOX_MOBILITATTREPPENSTEIGEN3_2:                 form.Checkbox_mobilitatTreppensteigen3_2,
		formkey.MUSTER_61B_CHECKBOX_MOBILITATTREPPENSTEIGEN4_2:                 form.Checkbox_mobilitatTreppensteigen4_2,
		formkey.MUSTER_61B_CHECKBOX_MOBILITATSONSTIGE1_2:                       form.Checkbox_mobilitatSonstige1_2,
		formkey.MUSTER_61B_CHECKBOX_MOBILITATSONSTIGE2_2:                       form.Checkbox_mobilitatSonstige2_2,
		formkey.MUSTER_61B_CHECKBOX_MOBILITATSONSTIGE3_2:                       form.Checkbox_mobilitatSonstige3_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNG_2:                         form.Checkbox_selbstversorgung_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGESSEN1_2:                   form.Checkbox_selbstversorgungEssen1_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGESSEN2_2:                   form.Checkbox_selbstversorgungEssen2_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGESSEN3_2:                   form.Checkbox_selbstversorgungEssen3_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGESSEN4_2:                   form.Checkbox_selbstversorgungEssen4_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGAN1_2:                      form.Checkbox_selbstversorgungAn1_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGAN2_2:                      form.Checkbox_selbstversorgungAn2_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGAN3_2:                      form.Checkbox_selbstversorgungAn3_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGAN4_2:                      form.Checkbox_selbstversorgungAn4_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGWASCHEN1_2:                 form.Checkbox_selbstversorgungWaschen1_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGWASCHEN2_2:                 form.Checkbox_selbstversorgungWaschen2_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGWASCHEN3_2:                 form.Checkbox_selbstversorgungWaschen3_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGWASCHEN4_2:                 form.Checkbox_selbstversorgungWaschen4_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGBADEN2_2:                   form.Checkbox_selbstversorgungBaden2_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGBADEN3_2:                   form.Checkbox_selbstversorgungBaden3_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGBADEN4_2:                   form.Checkbox_selbstversorgungBaden4_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGBADEN1_2:                   form.Checkbox_selbstversorgungBaden1_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGTOILETTENBENUTZUNG1_2:      form.Checkbox_selbstversorgungToilettenbenutzung1_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGTOILETTENBENUTZUNG2_2:      form.Checkbox_selbstversorgungToilettenbenutzung2_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGTOILETTENBENUTZUNG3_2:      form.Checkbox_selbstversorgungToilettenbenutzung3_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGTOILETTENBENUTZUNG4_2:      form.Checkbox_selbstversorgungToilettenbenutzung4_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGSONSTIGE1_2:                form.Checkbox_selbstversorgungSonstige1_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGSONSTIGE2_2:                form.Checkbox_selbstversorgungSonstige2_2,
		formkey.MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGSONSTIGE3_2:                form.Checkbox_selbstversorgungSonstige3_2,
		formkey.MUSTER_61B_CHECKBOX_HAUSLICHES1_2:                              form.Checkbox_hausliches1_2,
		formkey.MUSTER_61B_CHECKBOX_HAUSLICHES2_2:                              form.Checkbox_hausliches2_2,
		formkey.MUSTER_61B_CHECKBOX_HAUSLICHES3_2:                              form.Checkbox_hausliches3_2,
		formkey.MUSTER_61B_CHECKBOX_HAUSLICHES4_2:                              form.Checkbox_hausliches4_2,
		formkey.MUSTER_61B_CHECKBOX_INTERPERSONELLE1_2:                         form.Checkbox_interpersonelle1_2,
		formkey.MUSTER_61B_CHECKBOX_INTERPERSONELLE2_2:                         form.Checkbox_interpersonelle2_2,
		formkey.MUSTER_61B_CHECKBOX_INTERPERSONELLE3_2:                         form.Checkbox_interpersonelle3_2,
		formkey.MUSTER_61B_CHECKBOX_INTERPERSONELLE4_2:                         form.Checkbox_interpersonelle4_2,
		formkey.MUSTER_61B_CHECKBOX_BEDEUTENDE1_2:                              form.Checkbox_bedeutende1_2,
		formkey.MUSTER_61B_CHECKBOX_BEDEUTENDE2_2:                              form.Checkbox_bedeutende2_2,
		formkey.MUSTER_61B_CHECKBOX_BEDEUTENDE3_2:                              form.Checkbox_bedeutende3_2,
		formkey.MUSTER_61B_CHECKBOX_BEDEUTENDE4_2:                              form.Checkbox_bedeutende4_2,
		formkey.MUSTER_61B_CHECKBOX_GEMEINSCHAFTSLEBEN1_2:                      form.Checkbox_gemeinschaftsleben1_2,
		formkey.MUSTER_61B_CHECKBOX_GEMEINSCHAFTSLEBEN2_2:                      form.Checkbox_gemeinschaftsleben2_2,
		formkey.MUSTER_61B_CHECKBOX_GEMEINSCHAFTSLEBEN3_2:                      form.Checkbox_gemeinschaftsleben3_2,
		formkey.MUSTER_61B_CHECKBOX_GEMEINSCHAFTSLEBEN4_2:                      form.Checkbox_gemeinschaftsleben4_2,
		formkey.MUSTER_61B_TEXTBOX_MOBILITATSONSTIGE_2:                         form.Textbox_mobilitatSonstige_2,
		formkey.MUSTER_61B_TEXTBOX_SEKBSTVERSORGUNGSONSTIGE_2:                  form.Textbox_sekbstversorgungSonstige_2,
		formkey.MUSTER_61B_TEXTBOX_REHABILITATIONSRELEVANTEUMWELT_LINE1_2:      form.Textbox_rehabilitationsrelevanteUmwelt_line1_2,
		formkey.MUSTER_61B_TEXTBOX_REHABILITATIONSRELEVANTEUMWELT_LINE2_2:      form.Textbox_rehabilitationsrelevanteUmwelt_line2_2,
		formkey.MUSTER_61B_TEXTBOX_REHABILITATIONSRELEVANTEUMWELT_LINE3_2:      form.Textbox_rehabilitationsrelevanteUmwelt_line3_2,
		formkey.MUSTER_61B_CHECKBOX_RISIKOFAKTORENBEWEGUNGSMANGEL_2:            form.Checkbox_risikofaktorenBewegungsmangel_2,
		formkey.MUSTER_61B_CHECKBOX_RISIKOFAKTORENSONSTIGES_2:                  form.Checkbox_risikofaktorenSonstiges_2,
		formkey.MUSTER_61B_CHECKBOX_RISIKOFAKTORENFEHL_2:                       form.Checkbox_risikofaktorenFehl_2,
		formkey.MUSTER_61B_CHECKBOX_RISIKOFAKTORENMEDIKAMENTE_2:                form.Checkbox_risikofaktorenMedikamente_2,
		formkey.MUSTER_61B_CHECKBOX_RISIKOFAKTORENNIKOTIN_2:                    form.Checkbox_risikofaktorenNikotin_2,
		formkey.MUSTER_61B_TEXTBOX_RISIKOFAKTORENSONSTIGES_LINE1_2:             form.Textbox_risikofaktorenSonstiges_line1_2,
		formkey.MUSTER_61B_TEXTBOX_RISIKOFAKTORENSONSTIGES_LINE2_2:             form.Textbox_risikofaktorenSonstiges_line2_2,
		formkey.MUSTER_61B_TEXTBOX_RISIKOFAKTORENBMI_2:                         form.Textbox_risikofaktorenBmi_2,
		formkey.MUSTER_61B_CHECKBOX_ANGABENJA_2:                                form.Checkbox_angabenJa_2,
		formkey.MUSTER_61B_CHECKBOX_ANGABENNEIN_2:                              form.Checkbox_angabenNein_2,
		formkey.MUSTER_61B_TEXTBOX_REHABILITATIONSZIELE_LINE1_3:                form.Textbox_rehabilitationsziele_line1_3,
		formkey.MUSTER_61B_TEXTBOX_REHABILITATIONSZIELE_LINE2_3:                form.Textbox_rehabilitationsziele_line2_3,
		formkey.MUSTER_61B_TEXTBOX_REHABILITATIONSZIELEAUSSICHT_3:              form.Textbox_rehabilitationszieleAusSicht_3,
		formkey.MUSTER_61B_CHECKBOX_UNTERBERUCKSICHTIGUNGJA_3:                  form.Checkbox_unterBerucksichtigungJa_3,
		formkey.MUSTER_61B_CHECKBOX_UNTERBERUCKSICHTIGUNGEINGESCHRANKT_3:       form.Checkbox_unterBerucksichtigungEingeschrankt_3,
		formkey.MUSTER_61B_TEXTBOX_UNTERBERUCKSICHTIGUNG_LINE2_3:               form.Textbox_unterBerucksichtigung_line2_3,
		formkey.MUSTER_61B_TEXTBOX_UNTERBERUCKSICHTIGUNG_LINE1_3:               form.Textbox_unterBerucksichtigung_line1_3,
		formkey.MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENAMBULANT_3:           form.Checkbox_zuweisungsempfehlungenAmbulant_3,
		formkey.MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENMUTTERLEISTUNG_3:     form.Checkbox_zuweisungsempfehlungenMutterLeistung_3,
		formkey.MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENAMBULANTMOBIL_3:      form.Checkbox_zuweisungsempfehlungenAmbulantMobil_3,
		formkey.MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENVATERLEISTUNG_3:      form.Checkbox_zuweisungsempfehlungenVaterLeistung_3,
		formkey.MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENSTATIONAR_3:          form.Checkbox_zuweisungsempfehlungenStationar_3,
		formkey.MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENMUTTERKINDLEISTUNG_3: form.Checkbox_zuweisungsempfehlungenMutterKindLeistung_3,
		formkey.MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENVATERKINDLEISTUNG_3:  form.Checkbox_zuweisungsempfehlungenVaterKindLeistung_3,
		formkey.MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENGERIATR_3:            form.Checkbox_zuweisungsempfehlungenGeriatr_3,
		formkey.MUSTER_61B_TEXTBOX_ZUWEISUNGSEMPFEHLUNGENINHALTLICHE_3:         form.Textbox_zuweisungsempfehlungenInhaltliche_3,
		formkey.MUSTER_61B_TEXTBOX_ZUWEISUNGSEMPFEHLUNGENWEITEREBEMERKUNGEN_3:  form.Textbox_zuweisungsempfehlungenWeitereBemerkungen_3,
		formkey.MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENANDERE_3:             form.Checkbox_zuweisungsempfehlungenAndere_3,
		formkey.MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENPFLEGENDE_3:          form.Checkbox_zuweisungsempfehlungenPflegende_3,
		formkey.MUSTER_61B_CHECKBOX_SONSTIGEANGABENNEUER_3:                     form.Checkbox_sonstigeAngabenNeuer_3,
		formkey.MUSTER_61B_CHECKBOX_SONSTIGEANGABENDIEZEITWEISE_3:              form.Checkbox_sonstigeAngabenDieZeitweise_3,
		formkey.MUSTER_61B_CHECKBOX_SONSTIGEANGABENVERSCHLIMMERUNG_3:           form.Checkbox_sonstigeAngabenVerschlimmerung_3,
		formkey.MUSTER_61B_CHECKBOX_SONSTIGEANGABENIMFALLE_3:                   form.Checkbox_sonstigeAngabenImFalle_3,
		formkey.MUSTER_61B_CHECKBOX_SONSTIGEANGABENMITAUFNAHME_3:               form.Checkbox_sonstigeAngabenMitaufnahme_3,
		formkey.MUSTER_61B_CHECKBOX_SONSTIGEANGABENKOORDINATION_3:              form.Checkbox_sonstigeAngabenKoordination_3,
		formkey.MUSTER_61B_CHECKBOX_SONSTIGEANGABENBEGLEITPERSON_3:             form.Checkbox_sonstigeAngabenBegleitperson_3,
		formkey.MUSTER_61B_CHECKBOX_SONSTIGEANGABENOFFENTLICHE_3:               form.Checkbox_sonstigeAngabenOffentliche_3,
		formkey.MUSTER_61B_CHECKBOX_SONSTIGEANGABENPKW_3:                       form.Checkbox_sonstigeAngabenPkw_3,
		formkey.MUSTER_61B_TEXTBOX_SONSTIGESBESONDERE_LINE1_3:                  form.Textbox_sonstigesBesondere_line1_3,
		formkey.MUSTER_61B_TEXTBOX_SONSTIGESBESONDERE_LINE2_3:                  form.Textbox_sonstigesBesondere_line2_3,
		formkey.MUSTER_61B_CHECKBOX_ERTEILTEJA2_4:                              form.Checkbox_erteilteJa2_4,
		formkey.MUSTER_61B_CHECKBOX_ERTEILTENEIN2_4:                            form.Checkbox_erteilteNein2_4,
		formkey.MUSTER_61B_CHECKBOX_ERTEILTEJA1_4:                              form.Checkbox_erteilteJa1_4,
		formkey.MUSTER_61B_CHECKBOX_ERTEILTENEIN1_4:                            form.Checkbox_erteilteNein1_4,
		formkey.MUSTER_61B_TEXTBOX_NAMEVORNAME1_4:                              form.Textbox_nameVorname1_4,
		formkey.MUSTER_61B_TEXTBOX_PLZ1_4:                                      form.Textbox_plz1_4,
		formkey.MUSTER_61B_TEXTBOX_ORT1_4:                                      form.Textbox_ort1_4,
		formkey.MUSTER_61B_TEXTBOX_STRABE1_4:                                   form.Textbox_strabe1_4,
		formkey.MUSTER_61B_TEXTBOX_NAMEVORNAME2_4:                              form.Textbox_nameVorname2_4,
		formkey.MUSTER_61B_TEXTBOX_PLZ2_4:                                      form.Textbox_plz2_4,
		formkey.MUSTER_61B_TEXTBOX_ORT2_4:                                      form.Textbox_ort2_4,
		formkey.MUSTER_61B_TEXTBOX_STRABE2_4:                                   form.Textbox_strabe2_4,
		formkey.MUSTER_61B_TEXTBOX_NAMEVORNAME3_4:                              form.Textbox_nameVorname3_4,
		formkey.MUSTER_61B_TEXTBOX_PLZ3_4:                                      form.Textbox_plz3_4,
		formkey.MUSTER_61B_TEXTBOX_ORT3_4:                                      form.Textbox_ort3_4,
		formkey.MUSTER_61B_TEXTBOX_STRABE3_4:                                   form.Textbox_strabe3_4,
		formkey.MUSTER_61B_CHECKBOX_ALLGEMEINE1_2:                              form.Checkbox_allgemeine1_2,
		formkey.MUSTER_61B_CHECKBOX_LERNEN1_2:                                  form.Checkbox_lernen1_2,
		formkey.MUSTER_61B_TEXTBOX_ICD10_CODE1_1:                               form.Textbox_icd10_code1_1,
		formkey.MUSTER_61B_TEXTBOX_ICD10_CODE2_1:                               form.Textbox_icd10_code2_1,
		formkey.MUSTER_61B_TEXTBOX_ICD10_CODE3_1:                               form.Textbox_icd10_code3_1,
		formkey.MUSTER_61B_TEXTBOX_ICD10_CODE4_1:                               form.Textbox_icd10_code4_1,
		formkey.MUSTER_61B_TEXTBOX_ICD10_CODE5_1:                               form.Textbox_icd10_code5_1,
		formkey.MUSTER_61B_TEXTBOX_ICD10_CODE6_1:                               form.Textbox_icd10_code6_1,
		formkey.MUSTER_61B_TEXTBOX_DIAGNOSE1_1:                                 form.Textbox_diagnose1_1,
		formkey.MUSTER_61B_TEXTBOX_DIAGNOSE2_1:                                 form.Textbox_diagnose2_1,
		formkey.MUSTER_61B_TEXTBOX_DIAGNOSE3_1:                                 form.Textbox_diagnose3_1,
		formkey.MUSTER_61B_TEXTBOX_DIAGNOSE4_1:                                 form.Textbox_diagnose4_1,
		formkey.MUSTER_61B_TEXTBOX_DIAGNOSE5_1:                                 form.Textbox_diagnose5_1,
		formkey.MUSTER_61B_TEXTBOX_DIAGNOSE6_1:                                 form.Textbox_diagnose6_1,
		formkey.MUSTER_61B_TEXTBOX_URSACHE1_1:                                  form.Textbox_ursache1_1,
		formkey.MUSTER_61B_TEXTBOX_URSACHE2_1:                                  form.Textbox_ursache2_1,
		formkey.MUSTER_61B_TEXTBOX_URSACHE3_1:                                  form.Textbox_ursache3_1,
		formkey.MUSTER_61B_TEXTBOX_URSACHE4_1:                                  form.Textbox_ursache4_1,
		formkey.MUSTER_61B_TEXTBOX_URSACHE5_1:                                  form.Textbox_ursache5_1,
		formkey.MUSTER_61B_TEXTBOX_URSACHE6_1:                                  form.Textbox_ursache6_1,
		formkey.MUSTER_61B_TEXTBOX_URSACHE1_0:                                  form.Textbox_ursache1_0,
		formkey.MUSTER_61B_TEXTBOX_URSACHE2_0:                                  form.Textbox_ursache2_0,
		formkey.MUSTER_61B_TEXTBOX_URSACHE3_0:                                  form.Textbox_ursache3_0,
		formkey.MUSTER_61B_TEXTBOX_URSACHE4_0:                                  form.Textbox_ursache4_0,
		formkey.MUSTER_61B_TEXTBOX_URSACHE5_0:                                  form.Textbox_ursache5_0,
		formkey.MUSTER_61B_TEXTBOX_URSACHE6_0:                                  form.Textbox_ursache6_0,
		formkey.MUSTER_61B_DATE_LABEL_CUSTOM_DATUM_4:                           datum,
	}

	return payload
}

func mapMusterForm64(form *MusterForm64, dateFormat string) any {
	geburtsDatum1, _ := util.ConvertStringToMillisecond(form.Geburtsdatum1, dateFormat)
	geburtsDatum2, _ := util.ConvertStringToMillisecond(form.Geburtsdatum2, dateFormat)
	geburtsDatum3, _ := util.ConvertStringToMillisecond(form.Geburtsdatum3, dateFormat)
	dateDatum1, _ := util.ConvertStringToMillisecond(form.Datum1, dateFormat)
	payload := map[formkey.FormKey_MUSTER_64]any{
		formkey.MUSTER_64_TEXTBOX_VORSORGERELEVANTE_A_LINE1_0:            form.Vorsorgerelevante_a_line1,
		formkey.MUSTER_64_TEXTBOX_VORSORGERELEVANTE_A_LINE2_0:            form.Vorsorgerelevante_a_line2,
		formkey.MUSTER_64_TEXTBOX_SINGLE_DIAGNOSE1_0:                     form.Vorsorgerelevante_b_line1,
		formkey.MUSTER_64_TEXTBOX_SINGLE_DIAGNOSE2_0:                     form.Vorsorgerelevante_b_line2,
		formkey.MUSTER_64_TEXTBOX_SINGLE_DIAGNOSE3_0:                     form.Vorsorgerelevante_b_line3,
		formkey.MUSTER_64_TEXTBOX_ICD1_0:                                 form.Icd1,
		formkey.MUSTER_64_TEXTBOX_ICD2_0:                                 form.Icd2,
		formkey.MUSTER_64_TEXTBOX_ICD3_0:                                 form.Icd3,
		formkey.MUSTER_64_TEXTBOX_VORSORGEBEDURFTIGKEIT_A_LINE1_0:        form.Vorsorgebedurftigkeit_a_line1,
		formkey.MUSTER_64_TEXTBOX_VORSORGEBEDURFTIGKEIT_A_LINE2_0:        form.Vorsorgebedurftigkeit_a_line2,
		formkey.MUSTER_64_TEXTBOX_VORSORGEBEDURFTIGKEIT_A_LINE3_0:        form.Vorsorgebedurftigkeit_a_line3,
		formkey.MUSTER_64_TEXTBOX_VORSORGEBEDURFTIGKEIT_B_LINE1_0:        form.Vorsorgebedurftigkeit_b_line1,
		formkey.MUSTER_64_TEXTBOX_VORSORGEBEDURFTIGKEIT_B_LINE2_0:        form.Vorsorgebedurftigkeit_b_line2,
		formkey.MUSTER_64_TEXTBOX_VORSORGEBEDURFTIGKEIT_B_LINE3_0:        form.Vorsorgebedurftigkeit_b_line3,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_MEHRFACHBELASTUNG_0:   form.KontextfaktorenMehrfachbelastung,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_MANGELNDE_0:           form.KontextfaktorenMangelnde,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_ERZIEHUNGS_0:          form.KontextfaktorenErziehungs,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_SCHWIERIGKEITEN_0:     form.KontextfaktorenSchwierigkeiten,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_MANGELNDE_GRUND_0:     form.KontextfaktorenMangelndeGrund,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_ERHOHTER_0:            form.KontextfaktorenErhohter,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_FEHLENDE_0:            form.KontextfaktorenFehlende,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_PARTNER_0:             form.KontextfaktorenPartner,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_PFLEGE_0:              form.KontextfaktorenPflege,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_TEENAGER_0:            form.KontextfaktorenTeenager,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_SOZIALE_0:             form.KontextfaktorenSoziale,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_FINANZIELLE_0:         form.KontextfaktorenFinanzielle,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_STANDIGER_0:           form.KontextfaktorenStandiger,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_ALLEIN_0:              form.KontextfaktorenAllein,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_TOD_0:                 form.KontextfaktorenTod,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_BEEINTRACHTIGTE_0:     form.KontextfaktorenBeeintrachtigte,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_UBERFORDERUNGSSITUA_0: form.KontextfaktorenUberforderungssitua,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_SPRACHLICHE_0:         form.KontextfaktorenSprachliche,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_BESONDERS_0:           form.KontextfaktorenBesonders,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_DROHENDER_0:           form.KontextfaktorenDrohender,
		formkey.MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_NICHT_0:               form.KontextfaktorenNicht,
		formkey.MUSTER_64_TEXTBOX_ANDERE_KONTEXT_LINE1_0:                 form.AndereKontextLine1,
		formkey.MUSTER_64_TEXTBOX_KRANKENBEHANDLUNG_A_LINE1_1:            form.KrankenbehandlungALine1,
		formkey.MUSTER_64_CHECKBOX_KRANKENBEHANDLUNG_HEILMITTEL_1:        form.KrankenbehandlungHeilmittel,
		formkey.MUSTER_64_TEXTBOX_KRANKENBEHANDLUNG_B_LINE1_1:            form.KrankenbehandlungBLine1,
		formkey.MUSTER_64_TEXTBOX_VORSORGEZIELE_LINE1_1:                  form.VorsorgezieleLine1,
		formkey.MUSTER_64_TEXTBOX_VORSORGEZIELE_LINE2_1:                  form.VorsorgezieleLine2,
		formkey.MUSTER_64_CHECKBOX_ZUWEISUNGSEM_EMPFOHLENCE_1:            form.Zuweisungsempfohlence,
		formkey.MUSTER_64_CHECKBOX_ZUWEISUNGSEM_VATER_1:                  form.ZuweisungsemVater,
		formkey.MUSTER_64_CHECKBOX_ZUWEISUNGSEM_MUTTER_1:                 form.ZuweisungsemMutter,
		formkey.MUSTER_64_CHECKBOX_ZUWEISUNGSEM_VATER_KIND_1:             form.ZuweisungsemVaterKind,
		formkey.MUSTER_64_TEXTBOX_ZUWEISUNGSEM_NAME_DES_KINDES1_1:        form.ZuweisungsemNameDesKindes1,
		formkey.MUSTER_64_TEXTBOX_ZUWEISUNGSEM_NAME_DES_KINDES2_1:        form.ZuweisungsemNameDesKindes2,
		formkey.MUSTER_64_TEXTBOX_ZUWEISUNGSEM_NAME_DES_KINDES3_1:        form.ZuweisungsemNameDesKindes3,
		formkey.MUSTER_64_DATE_LABEL_CUSTOM_GEBURTSDATUM1_1:              geburtsDatum1,
		formkey.MUSTER_64_DATE_LABEL_CUSTOM_GEBURTSDATUM2_1:              geburtsDatum2,
		formkey.MUSTER_64_DATE_LABEL_CUSTOM_GEBURTSDATUM3_1:              geburtsDatum3,
		formkey.MUSTER_64_CHECKBOX_ZUWEISUNGSEM_ATTEST_LINE1_1:           form.ZuweisungsemAttestLine1,
		formkey.MUSTER_64_CHECKBOX_ZUWEISUNGSEM_ATTEST_LINE2_1:           form.ZuweisungsemAttestLine2,
		formkey.MUSTER_64_CHECKBOX_ZUWEISUNGSEM_ATTEST_LINE3_1:           form.ZuweisungsemAttestLine3,
		formkey.MUSTER_64_CHECKBOX_ZUWEISUNGSEM_BELASTETE_LINE1_1:        form.ZuweisungsemBelasteteLine1,
		formkey.MUSTER_64_CHECKBOX_ZUWEISUNGSEM_BELASTETE_LINE2_1:        form.ZuweisungsemBelasteteLine2,
		formkey.MUSTER_64_CHECKBOX_ZUWEISUNGSEM_BELASTETE_LINE3_1:        form.ZuweisungsemBelasteteLine3,
		formkey.MUSTER_64_CHECKBOX_ZUWEISUNGSEM_PSYCHOSOZIABLE_LINE1_1:   form.ZuweisungsemPsychosoziableLine1,
		formkey.MUSTER_64_CHECKBOX_ZUWEISUNGSEM_PSYCHOSOZIABLE_LINE2_1:   form.ZuweisungsemPsychosoziableLine2,
		formkey.MUSTER_64_CHECKBOX_ZUWEISUNGSEM_PSYCHOSOZIABLE_LINE3_1:   form.ZuweisungsemPsychosoziableLine3,
		formkey.MUSTER_64_CHECKBOX_SONSTIGE_A_JA_1:                       form.SonstigeAJa,
		formkey.MUSTER_64_TEXTBOX_SONSTIGE_A_JA_1:                        form.SonstigeAText,
		formkey.MUSTER_64_CHECKBOX_SONSTIGE_B_JA_1:                       form.SonstigeBJa,
		formkey.MUSTER_64_TEXTBOX_SONSTIGE_B_JA_1:                        form.SonstigeBText,
		formkey.MUSTER_64_TEXTBOX_SONSTIGE_C_SONSTIGES_LINE1_1:           form.SonstigeCSonstigesLine1,
		formkey.MUSTER_64_TEXTBOX_SONSTIGE_D_RUCKRUF_1:                   form.SonstigeDRuckruf,
		formkey.MUSTER_64_DATE_LABEL_CUSTOM_DATUM_1:                      dateDatum1,
	}
	return payload
}

func mapMusterForm22(form *MusterForm22, dateFormat string) any {
	dateDatum, _ := util.ConvertStringToMillisecond(form.Date_Datum, dateFormat)
	payload := map[formkey.FormKey_MUSTER_22A_N]any{
		formkey.MUSTER_22A_N_TEXTBOX_NAME_DES_THERAPEUTEN: form.Therapeuten,
		formkey.MUSTER_22A_N_TEXTBOX_CUSTOM_ARZTNUMMER:    form.Arztnummer,
		formkey.MUSTER_22A_N_TEXTBOX_CUSTOM_BETRIEB:       form.Betrieb,
		formkey.MUSTER_22A_N_AREA_TEXTBOX_ANGABEN:         form.Angaben,
		formkey.MUSTER_22A_N_CHECKBOX_ERFORDERLICH:        form.Erforderlich,
		formkey.MUSTER_22A_N_CHECKBOX_NICHT_ERFORDERLICH:  form.Erforderlic,
		formkey.MUSTER_22A_N_CHECKBOX_ERFOLGT:             form.Erfolgt,
		formkey.MUSTER_22A_N_CHECKBOX_VERANLASST:          form.Veranlasst,
		formkey.MUSTER_22A_N_TEXTBOX_SIND_LINE1:           form.Sind,
		formkey.MUSTER_22A_N_CHECKBOX_AUFGRUND:            form.Aufgrund,
		formkey.MUSTER_22A_N_CHECKBOX_ARZTLICHE:           form.Arztliche,
		formkey.MUSTER_22A_N_TEXTBOX_ARZTLICHE_LINE1:      form.Textbox_Arztliche,
		formkey.MUSTER_22A_N_DATE_LABEL_CUSTOM_DATUM:      dateDatum,
	}

	return payload
}

func mapMusterForm65(form *MusterForm65, dateFormat string) any {
	dateDatum, _ := util.ConvertStringToMillisecond(form.Date_Datum, dateFormat)
	payload := map[formkey.FormKey_MUSTER_65A]any{
		formkey.MUSTER_65A_TEXTBOX_65_KURZE_ANGABEN_LINE1_0:   form.Kurze_Angaben,
		formkey.MUSTER_65A_TEXTBOX_65_VORLIEGENDE_GES_LINE1_0: form.Vorliegende_Ges,
		formkey.MUSTER_65A_TEXTBOX_SINGLE_DIAGNOSE_LINE1_0:    form.Diagnose1,
		formkey.MUSTER_65A_TEXTBOX_ICD1_0:                     form.ICD1,
		formkey.MUSTER_65A_TEXTBOX_SINGLE_DIAGNOSE_LINE2_0:    form.Diagnose2,
		formkey.MUSTER_65A_TEXTBOX_ICD2_0:                     form.ICD2,
		formkey.MUSTER_65A_TEXTBOX_SINGLE_DIAGNOSE_LINE3_0:    form.Diagnose3,
		formkey.MUSTER_65A_TEXTBOX_ICD3_0:                     form.ICD3,
		formkey.MUSTER_65A_TEXTBOX_65_VORLIEGENDE_WEI_LINE1_0: form.Vorliegende_Wei,
		formkey.MUSTER_65A_TEXTBOX_65_BISHERIGE_LINE1_0:       form.Bisherige,
		formkey.MUSTER_65A_TEXTBOX_65_EMPFOHLENE_LINE1_0:      form.Empfohlene,
		formkey.MUSTER_65A_CHECKBOX_JA_FOLGENDE_0:             form.Ja_Folgende,
		formkey.MUSTER_65A_TEXTBOX_65_HINWEISE_LINE1_0:        form.Hinweise,
		formkey.MUSTER_65A_DATE_LABEL_CUSTOM_DATUM:            dateDatum,
	}

	return payload
}

func mapMusterForm20(form *MusterForm20, dateFormat string) any {
	dateVom1, _ := util.ConvertStringToMillisecond(form.DateVom1, dateFormat)
	dateBis1, _ := util.ConvertStringToMillisecond(form.DateBis1, dateFormat)
	dateVom2, _ := util.ConvertStringToMillisecond(form.DateVom2, dateFormat)
	dateBis2, _ := util.ConvertStringToMillisecond(form.DateBis2, dateFormat)
	dateVom3, _ := util.ConvertStringToMillisecond(form.DateVom3, dateFormat)
	dateBis3, _ := util.ConvertStringToMillisecond(form.DateBis3, dateFormat)
	dateVom4, _ := util.ConvertStringToMillisecond(form.DateVom4, dateFormat)
	dateBis4, _ := util.ConvertStringToMillisecond(form.DateBis4, dateFormat)
	payload := map[formkey.FormKey_MUSTER_20]any{
		formkey.MUSTER_20_TEXTBOX_ZULETZT_LINE1:   form.TextboxZuletztLine1,
		formkey.MUSTER_20_TEXTBOX_ZULETZT_LINE2:   form.TextboxZuletztLine2,
		formkey.MUSTER_20_TEXTBOX_ZULETZT_LINE3:   form.TextboxZuletztLine3,
		formkey.MUSTER_20_TEXTBOX_ZULETZT_STUNDEN: form.TextboxZuletztStunden,
		formkey.MUSTER_20_DATE_LABEL_CUSTOM_VOM1:  dateVom1,
		formkey.MUSTER_20_DATE_LABEL_CUSTOM_VOM2:  dateVom2,
		formkey.MUSTER_20_DATE_LABEL_CUSTOM_VOM3:  dateVom3,
		formkey.MUSTER_20_DATE_LABEL_CUSTOM_VOM4:  dateVom4,

		formkey.MUSTER_20_DATE_LABEL_CUSTOM_BIS1: dateBis1,
		formkey.MUSTER_20_DATE_LABEL_CUSTOM_BIS2: dateBis2,
		formkey.MUSTER_20_DATE_LABEL_CUSTOM_BIS3: dateBis3,
		formkey.MUSTER_20_DATE_LABEL_CUSTOM_BIS4: dateBis4,

		formkey.MUSTER_20_TEXTBOX_TATIGKEIT_LINE1: form.TextboxTatigkeitLine1,
		formkey.MUSTER_20_TEXTBOX_TATIGKEIT_LINE2: form.TextboxTatigkeitLine2,
		formkey.MUSTER_20_TEXTBOX_TATIGKEIT_LINE3: form.TextboxTatigkeitLine3,
		formkey.MUSTER_20_TEXTBOX_TATIGKEIT_LINE4: form.TextboxTatigkeitLine4,

		formkey.MUSTER_20_TEXTBOX_TAGLICH_LINE1: form.TextboxTaglichLine1,
		formkey.MUSTER_20_TEXTBOX_TAGLICH_LINE2: form.TextboxTaglichLine2,
		formkey.MUSTER_20_TEXTBOX_TAGLICH_LINE3: form.TextboxTaglichLine3,
		formkey.MUSTER_20_TEXTBOX_TAGLICH_LINE4: form.TextboxTaglichLine4,
	}

	return payload
}

func mapMusterForm12(form *MusterForm12, dateFormat string) any {
	// bdt date format is 02.01. should we add year behind then parse
	vomDate, _ := util.ConvertStringToMillisecond(form.VomDate, dateFormat)
	bisDate, _ := util.ConvertStringToMillisecond(form.BisDate, dateFormat)
	var year string
	if vomDate != 0 {
		if tmp, err := util.ParseStringToTime(form.VomDate, dateFormat); err == nil {
			year = strconv.Itoa(tmp.Year())
		}
	}

	herrichtenVom, _ := util.ConvertStringToMillisecond(form.HerrichtenVom+year, util.FORMAT_DATE_COMPARE)
	herrichtenBis, _ := util.ConvertStringToMillisecond(form.HerrichtenBis+year, util.FORMAT_DATE_COMPARE)
	medikamentengabeVom, _ := util.ConvertStringToMillisecond(form.MedikamentengabeVom+year, util.FORMAT_DATE_COMPARE)
	medikamentengabeBis, _ := util.ConvertStringToMillisecond(form.MedikamentengabeBis+year, util.FORMAT_DATE_COMPARE)
	grundpflegeVom, _ := util.ConvertStringToMillisecond(form.GrundpflegeVom+year, util.FORMAT_DATE_COMPARE)
	grundpflegeBis, _ := util.ConvertStringToMillisecond(form.GrundpflegeBis+year, util.FORMAT_DATE_COMPARE)
	hauswirtschaftVom, _ := util.ConvertStringToMillisecond(form.HauswirtschaftVom+year, util.FORMAT_DATE_COMPARE)
	hauswirtschaftBis, _ := util.ConvertStringToMillisecond(form.HauswirtschaftBis+year, util.FORMAT_DATE_COMPARE)
	komprstvom, _ := util.ConvertStringToMillisecond(form.Komprstvom+year, util.FORMAT_DATE_COMPARE)
	komprstbis, _ := util.ConvertStringToMillisecond(form.Komprstbis+year, util.FORMAT_DATE_COMPARE)

	payload := map[formkey.FormKey_MUSTER_12A]any{
		formkey.MUSTER_12A_TEXTBOX_ICD10_CODE1_0: form.ICD1,
		formkey.MUSTER_12A_TEXTBOX_ICD10_CODE2_0: form.ICD2,
		formkey.MUSTER_12A_TEXTBOX_ICD10_CODE3_0: form.ICD3,
		formkey.MUSTER_12A_TEXTBOX_ICD10_CODE4_0: form.ICD4,

		formkey.MUSTER_12A_TEXTBOX_EINSCHRANKUNGEN_LINE1_0: form.EinscharekugenLine1,
		formkey.MUSTER_12A_TEXTBOX_EINSCHRANKUNGEN_LINE2_0: form.EinscharekugenLine2,
		formkey.MUSTER_12A_CHECKBOX_ERST_0:                 form.Erst,
		formkey.MUSTER_12A_CHECKBOX_FOLGE_0:                form.Folge,
		formkey.MUSTER_12A_CHECKBOX_UNFALL_0:               form.Unfall,
		formkey.MUSTER_12A_CHECKBOX_SER_0:                  form.Ser,
		formkey.MUSTER_12A_DATE_LABEL_CUSTOM_VOM_0:         vomDate,
		formkey.MUSTER_12A_DATE_LABEL_CUSTOM_BIS_0:         bisDate,
		formkey.MUSTER_12A_TEXTBOX_MEDIKAMENTENGABE1_0:     form.MedikamentengabeLine1,
		formkey.MUSTER_12A_TEXTBOX_MEDIKAMENTENGABE2_0:     form.MedikamentengabeLine2,

		formkey.MUSTER_12A_CHECKBOX_HERRICHTENDERMEDIKAMENTENBOX_0: form.HerrichtenDer,
		formkey.MUSTER_12A_TEXTBOX_WTL_LINE2_0:                     form.HerrichtenWtl,
		formkey.MUSTER_12A_TEXTBOX_TGL_LINE2_0:                     form.HerrichtenTgl,
		formkey.MUSTER_12A_DATE_DAUERVOM_LINE2_0:                   herrichtenVom,
		formkey.MUSTER_12A_DATE_DAUERBIS_LINE2_0:                   herrichtenBis,

		formkey.MUSTER_12A_CHECKBOX_MEDIKAMENTENGABE_0: form.Medikamentengabe,
		formkey.MUSTER_12A_TEXTBOX_WTL_LINE1_0:         form.MedikamentengabeWtl,
		formkey.MUSTER_12A_TEXTBOX_TGL_LINE1_0:         form.MedikamentengabeTgl,
		formkey.MUSTER_12A_DATE_DAUERVOM_LINE1_0:       medikamentengabeVom,
		formkey.MUSTER_12A_DATE_DAUERBIS_LINE1_0:       medikamentengabeBis,

		formkey.MUSTER_12A_CHECKBOX_INJEKTIONEN_0:                      form.Injektionen,
		formkey.MUSTER_12A_CHECKBOX_HERRICHTEN_0:                       form.Herrichten,
		formkey.MUSTER_12A_CHECKBOX_INTRAMUSKULAR_0:                    form.Intramuskular,
		formkey.MUSTER_12A_CHECKBOX_SUBKUTAN_0:                         form.Subkutan,
		formkey.MUSTER_12A_CHECKBOX_ERSTODERNEUEINSTELLUNG_0:           form.ErstOder,
		formkey.MUSTER_12A_CHECKBOX_BEIINTENSIVIERTERINSULINTHERAPIE_0: form.BeiIntensivierter,
		formkey.MUSTER_12A_CHECKBOX_RECHTS_0:                           form.Rechts,
		formkey.MUSTER_12A_CHECKBOX_LINKS_0:                            form.Links,
		formkey.MUSTER_12A_CHECKBOX_BEIDSEITS_0:                        form.Beidseits,
		formkey.MUSTER_12A_CHECKBOX_AUSZIEHEN1_0:                       form.Anziehen,
		formkey.MUSTER_12A_CHECKBOX_AUSZIHEN2_0:                        form.Auszihen,
		formkey.MUSTER_12A_TEXTBOX_TGL_LINE5_0:                         form.Komprsttgl,
		formkey.MUSTER_12A_TEXTBOX_WTL_LINE5_0:                         form.Komprstwo,
		formkey.MUSTER_12A_DATE_DAUERVOM_LINE5_0:                       komprstvom,
		formkey.MUSTER_12A_DATE_DAUERBIS_LINE5_0:                       komprstbis,

		formkey.MUSTER_12A_CHECKBOX_ABNEHMEN1_0:                    form.Anlegen,
		formkey.MUSTER_12A_CHECKBOX_ABNEHMEN2_0:                    form.Abnehmen,
		formkey.MUSTER_12A_CHECKBOX_STUTZENDE_0:                    form.Stutzende,
		formkey.MUSTER_12A_TEXTBOX_WUNDVERSORGUNG_0:                form.StutzendeText,
		formkey.MUSTER_12A_CHECKBOX_UNTERSTUTZUNGSPFLEGE_0:         form.Unterstutzung,
		formkey.MUSTER_12A_CHECKBOX_KRANKENHAUSVERMEIDUNGSPFLEGE_0: form.Krankenhau,

		formkey.MUSTER_12A_CHECKBOX_GRUNDPFLEGE_0: form.Grundpflege,
		formkey.MUSTER_12A_TEXTBOX_WTL_LINE12_0:   form.GrundpflegeWtl,
		formkey.MUSTER_12A_TEXTBOX_TGL_LINE12_0:   form.GrundpflegeTgl,
		formkey.MUSTER_12A_DATE_DAUERVOM_LINE12_0: grundpflegeVom,
		formkey.MUSTER_12A_DATE_DAUERBIS_LINE12_0: grundpflegeBis,

		formkey.MUSTER_12A_CHECKBOX_HAUSWIRTSCHAFTLICHE_0: form.Hauswirtschaft,
		formkey.MUSTER_12A_TEXTBOX_WTL_LINE13_0:           form.HauswirtschaftWtl,
		formkey.MUSTER_12A_TEXTBOX_TGL_LINE13_0:           form.HauswirtschaftTgl,
		formkey.MUSTER_12A_DATE_DAUERVOM_LINE13_0:         hauswirtschaftVom,
		formkey.MUSTER_12A_DATE_DAUERBIS_LINE13_0:         hauswirtschaftBis,

		formkey.MUSTER_12A_TEXTBOX_WEITERE_LINE1_0: form.Hinweise,
	}

	return payload
}

func mapMusterForm19(form *MusterForm19, dateFormat string) any {
	quarter, qErr := strconv.ParseInt(form.Textbox_Quarter, 10, 32)
	var quartalInt int64
	if form.Textbox_Year != "" && qErr == nil {
		var month string
		switch quarter {
		case 1:
			month = "02"
		case 2:
			month = "05"
		case 3:
			month = "08"
		default:
			month = "11"
		}
		quartalInt, _ = util.ConvertStringToMillisecond(month+form.Textbox_Year, util.MMYY)
	}

	var aubisTime time.Time
	if form.Textbox_Aubis != "" {
		var err error
		aubisTime, err = time.Parse(dateFormat, form.Textbox_Aubis)
		if err != nil {
			return err
		}
	}

	payload := map[formkey.FormKey_MUSTER_19A]any{
		formkey.MUSTER_19A_DATE_QUARTAL:                            quartalInt,
		formkey.MUSTER_19A_CHECKBOX_BEHANDLUNG:                     form.Checkbox_Notfalldienst,
		formkey.MUSTER_19A_CHECKBOX_BELEGARZTL:                     form.Chexkbox_Vertretung,
		formkey.MUSTER_19A_CHECKBOX_UNFALL:                         form.Chexkbox_Notfall,
		formkey.MUSTER_19A_CHECKBOX_ABKLARUNG_SOMATISCHER_URSACHEN: form.Chexkbox_Unfall,
		formkey.MUSTER_19B_LABEL_M:                                 form.Textbox_Geschlecht_M,
		formkey.MUSTER_19B_LABEL_W:                                 form.Textbox_Geschlecht_W,
		formkey.MUSTER_19A_TEXTBOX_BEFUNDE_LINE_1:                  form.Textbox_Befund,
		formkey.MUSTER_19A_TEXTBOX_DIAGNOSE_LINE1:                  form.Textbox_Diagnose,
		formkey.MUSTER_19A_TEXTBOX_WEITERARZT:                      form.Weiterarzt,
	}

	if !aubisTime.IsZero() {
		payload[formkey.MUSTER_19A_DATE_LABEL_CUSTOM_ARBEITSUNFAHIGKEIT] = aubisTime.Unix()
	}

	return payload
}

func formatTimeHH_MM_SS(value string) (int64, error) {
	str := fmt.Sprintf("1970-01-01T%s:00", value)

	return util.ConvertStringToMillisecond(str, util.YYYY_MM_DDThh_mm_ss)
}

func mapMusterFormF1050(form *MusterFormF1050, _ string) any {
	arriveDate, _ := util.ConvertStringToMillisecond(form.Date_label_custom_arrive_date_0, util.YYYYMMDD)
	arriveTime, _ := formatTimeHH_MM_SS(form.Label_time_arrive_time_0)
	accidentDay, _ := util.ConvertStringToMillisecond(form.Date_label_custom_accident_day_0, util.YYYYMMDD)
	accidentTime, _ := formatTimeHH_MM_SS(form.Label_time_accident_time_0)
	startTime, _ := formatTimeHH_MM_SS(form.Label_time_start_time_0)
	endTime, _ := formatTimeHH_MM_SS(form.Label_time_end_time_0)
	dieVersicherte, _ := util.ConvertStringToMillisecond(form.Date_die_versicherte_0, util.YYYYMMDD)

	payload := map[formkey.FormKey_F1050]any{
		formkey.F1050_TEXTBOX_UNFALLVERSICHER_0:             form.Textbox_unfallversicher_0,
		formkey.F1050_TEXTBOX_LFD_0:                         form.Textbox_lfd_0,
		formkey.F1050_LABEL_ACTIVE_INSURANCE_NAME_0:         form.Label_active_insurance_name_0,
		formkey.F1050_LABEL_LASTNAME_0:                      form.Label_lastname_0,
		formkey.F1050_LABEL_FIRSTNAME_0:                     form.Label_firstname_0,
		formkey.F1050_TEXTBOX_JOB_0:                         form.Textbox_job_0,
		formkey.F1050_LABEL_DATE_LABEL_CUSTOM_START_DATUM_0: form.Label_date_label_custom_start_datum_0,
		formkey.F1050_LABEL_ADDRESS_0:                       form.Label_address_0,
		formkey.F1050_TEXTBOX_NATIONALITY_0:                 form.Textbox_nationality_0,
		formkey.F1050_TEXTBOX_GENDER_0:                      form.Textbox_gender_0,
		formkey.F1050_TEXTBOX_COMPANY_ADDRESS_0:             form.Textbox_company_address_0,
		formkey.F1050_DATE_LABEL_CUSTOM_ARRIVE_DATE_0:       arriveDate,
		formkey.F1050_LABEL_TIME_ARRIVE_TIME_0:              arriveTime,
		formkey.F1050_DATE_LABEL_CUSTOM_ACCIDENT_DAY_0:      accidentDay,
		formkey.F1050_LABEL_TIME_ACCIDENT_TIME_0:            accidentTime,
		formkey.F1050_LABEL_TIME_START_TIME_0:               startTime,
		formkey.F1050_LABEL_TIME_END_TIME_0:                 endTime,
		formkey.F1050_CHECKBOX_DIE_VERSICHERTE_0:            form.Checkbox_die_versicherte_0,
		formkey.F1050_CHECKBOX_EINE_VORSTELLUNG_0:           form.Checkbox_eine_vorstellung_0,
		formkey.F1050_DATE_DIE_VERSICHERTE_0:                dieVersicherte,
		formkey.F1050_TEXTBOX_DIE_VERSICHERTE_0:             form.Textbox_die_versicherte_0,
		formkey.F1050_CHECKBOX_DIE_UNFALL_0:                 form.Checkbox_die_unfall_0,
		formkey.F1050_AREA_ANGABEN_0:                        form.Area_angaben_0,
		formkey.F1050_AREA_KURZE_ANGABE_0:                   form.Area_kurze_angabe_0,
		formkey.F1050_AREA_BESCHWERDEN_0:                    form.Area_beschwerden_0,
		formkey.F1050_AREA_DIAGNOSE_0:                       form.Area_diagnose_0,
		formkey.F1050_CHECKBOX_IST_WEITERE_NEIN_0:           form.Checkbox_ist_weitere_nein_0,
		formkey.F1050_CHECKBOX_IST_WEITERE_JA_0:             form.Checkbox_ist_weitere_ja_0,
		formkey.F1050_CHECKBOX_DURCH_ANDERE_0:               form.Checkbox_durch_andere_0,
		formkey.F1050_CHECKBOX_DURCH_MICH_0:                 form.Checkbox_durch_mich_0,
		formkey.F1050_AREA_DURCH_ANDERE_0:                   form.Area_durch_andere_0,

		formkey.F1050_LABEL_PATIENT_FULLNAME_1: form.Label_patient_fullname_1,
	}

	return payload
}

/* ----------map form by using regex------------- */
// func mapMusterForm4(template, dateFormat string) (map[formkey.FormKey_MUSTER_4]any, error) {
// 	form4 := &MusterForm4{}

// 	err := regexMuster4.MatchToTarget(template, form4)
// 	if err != nil {
// 		return nil, err
// 	}

// 	fromDate, fromDateErr := util.ConvertStringToMillisecond(form4.FromDate, dateFormat)
// 	toDate, toDateErr := util.ConvertStringToMillisecond(form4.ToDate, dateFormat)
// 	payload := map[formkey.FormKey_MUSTER_4]any{
// 		formkey.MUSTER_4_CHECKBOX_ZUZAH_LUNGS_PFLICHT:                       form4.Zuzahlungspflichtig,
// 		formkey.MUSTER_4_CHECKBOX_ZUZAH_FREI:                                form4.Zuzahlungsfrei,
// 		formkey.MUSTER_4_CHECKBOX_UNFALL_UNFALLFOLGE:                        form4.Unfallfolgen,
// 		formkey.MUSTER_4_CHECKBOX_ARBEITSUNFALL_BERUFSKRANKHEIT:             form4.Arbeitsunfall,
// 		formkey.MUSTER_4_CHECKBOX_VERSORGUNGSLEIDEN:                         form4.BVG,
// 		formkey.MUSTER_4_CHECKBOX_HINFAHRT:                                  form4.Hinfahrt,
// 		formkey.MUSTER_4_CHECKBOX_RUCKFAHRT:                                 form4.Ruckfahrtm,
// 		formkey.MUSTER_4_CHECKBOX_VOLL_TEILSTATIONARE_KRANKENHAUSBEHANDLUNG: form4.Voll,
// 		formkey.MUSTER_4_CHECKBOX_VOR_NACHSTATIONARE_BEHANDLUNG:             form4.Vor,
// 		formkey.MUSTER_4_CHECKBOX_AMBULANTE_BEHANDLUNG:                      form4.Ambulante,
// 		formkey.MUSTER_4_CHECKBOX_ANDERER_GRUND:                             form4.AndererGrund,
// 		formkey.MUSTER_4_TEXTBOX_FAHRTEN_ZU_HOSPIZEN:                        form4.AndererGrundText,
// 		formkey.MUSTER_4_CHECKBOX_HOCHFREQUENTE_BEHANDLUNG:                  form4.Dialyse,
// 		formkey.MUSTER_4_CHECKBOX_VERGLEICHBARER_AUSNAHMEFALL:               form4.Vergleichbarer,
// 		formkey.MUSTER_4_CHECKBOX_DAUERHAFTE_MOBILITATSBEEINTRACHTIGUNG:     form4.Dauerhafte,
// 		formkey.MUSTER_4_CHECKBOX_ANDERER_GRUND_FUR_FAHRT:                   form4.AndererGrundFur,
// 		formkey.MUSTER_4_DATE_LABEL_CUSTOM_VOM:                              function.If(fromDateErr == nil, fromDate, 0),
// 		formkey.MUSTER_4_TEXTBOX_PRO_WOCHE:                                  form4.Frequency,
// 		formkey.MUSTER_4_DATE_LABEL_CUSTOM_BIS:                              function.If(toDateErr == nil, toDate, 0),
// 		formkey.MUSTER_4_TEXTBOX_BEHANDLUNGSSTATTE:                          form4.Behandlung,
// 		formkey.MUSTER_4_CHECKBOX_TAXI_MIETWAGEN:                            form4.Taxi,
// 		formkey.MUSTER_4_CHECKBOX_ROLLSTUHL:                                 form4.Rollstuhl,
// 		formkey.MUSTER_4_CHECKBOX_TRAGESTUHL:                                form4.Tragestuhl,
// 		formkey.MUSTER_4_CHECKBOX_LIEGEND:                                   form4.Liegend,
// 		formkey.MUSTER_4_CHECKBOX_KTW_DA_MEDIZINISCH:                        form4.Ktw,
// 		formkey.MUSTER_4_TEXTBOX_ART_UND_AUSSTATTUNG_1:                      form4.Einrichtung,
// 		formkey.MUSTER_4_CHECKBOX_RTW:                                       form4.Rtw,
// 		formkey.MUSTER_4_CHECKBOX_NAW_NEF:                                   form4.Naw,
// 		formkey.MUSTER_4_CHECKBOX_ANDERE:                                    form4.AndereEinrichtung,
// 		formkey.MUSTER_4_TEXTBOX_ANDERE:                                     form4.AndereEinrichtungText,
// 		formkey.MUSTER_4_TEXTBOX_BEGRUNDUNG_SONSTIGES:                       form4.Sonstiges,
// 	}

// 	return payload, nil
// }

// func mapMusterForm10A(template, dateFormat string) (map[formkey.FormKey_MUSTER_10A_N]any, error) {
// 	form10A := &MusterForm10A{}
// 	err := regexMuster10A.MatchToTarget(template, form10A)
// 	if err != nil {
// 		return nil, err
// 	}

// 	abnahmedatumMilis, abnahmedatumMilisErr := util.ConvertStringToMillisecond(form10A.Abnahmedatum, dateFormat)
// 	abnahmezeitMilis, abnahmezeitMilisErr := util.ConvertStringToMillisecond(form10A.Abnahmedatum+form10A.Abnahmezeit, dateFormat+"1504")
// 	payload := map[formkey.FormKey_MUSTER_10A_N]any{
// 		formkey.MUSTER_10A_N_LABEL_GENDER:                    form10A.Geschlecht,
// 		formkey.MUSTER_10A_N_CHECKBOX_KURATIV:                form10A.Kurativ,
// 		formkey.MUSTER_10A_N_CHECKBOX_PRAVENTIV:              form10A.Praventiv,
// 		formkey.MUSTER_10A_N_CHECKBOX_BEHANDL:                form10A.Behandlung,
// 		formkey.MUSTER_10A_N_CHECKBOX_UNFALL:                 form10A.Unfallfolgen,
// 		formkey.MUSTER_10A_N_TEXTBOX_KNAPPSCHAFTS_KENNZIFFER: form10A.Knappschaftskennziffer,
// 		formkey.MUSTER_10A_N_TEXTBOX_SSW:                     form10A.SSW,
// 		formkey.MUSTER_10A_N_TEXTBOX_ZUSAT:                   form10A.Auftragsnummer,
// 		formkey.MUSTER_10A_N_DATE_LABEL_CUSTOM_ABNAHMEDATUM:  function.If(abnahmedatumMilisErr == nil, abnahmedatumMilis, 0),
// 		formkey.MUSTER_10A_N_DATE_ABNAHMEZEIT:                function.If(abnahmezeitMilisErr == nil, abnahmezeitMilis, 0),
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_1:           form10A.Befund,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_2:           form10A.GroBes,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_3:           form10A.Kleines,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_4:           form10A.HbA1c,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_5:           form10A.Retikulozyten,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_6:           form10A.BlutSenkung,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_51:          form10A.Glukose1,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_52:          form10A.Glukose2,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_53:          form10A.Glukose3,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_54:          form10A.Glukose4,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_62:          form10A.Harnstreifentest,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_63:          form10A.Nuchternplasmaglukose,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_64:          form10A.Lipidprofil,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_55:          form10A.Status,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_56:          form10A.Mikroalbumin,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_58:          form10A.GlukoseUrin,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_60:          form10A.Sediment,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_8:           form10A.Quick,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_9:           form10A.QuickUnter,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_10:          form10A.Thrombinzeit,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_11:          form10A.PTT,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_13:          form10A.Alkalische,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_14:          form10A.Amylase,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_15:          form10A.ASL,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_16:          form10A.BilirubinDir,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_17:          form10A.BilirubinGes,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_18:          form10A.Calcium,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_19:          form10A.Cholesterin,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_20:          form10A.Cholinesterase,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_21:          form10A.CK,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_23:          form10A.CRP,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_24:          form10A.Eisen,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_25:          form10A.Elektro,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_26:          form10A.Gesamt,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_27:          form10A.Gamma,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_28:          form10A.Glukose,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_29:          form10A.GOT,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_30:          form10A.GPT,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_31:          form10A.Harnsaure,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_32:          form10A.Harnstoff,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_34:          form10A.HDL,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_35:          form10A.IgA,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_36:          form10A.IgG,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_37:          form10A.IgM,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_38:          form10A.Kalium,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_39:          form10A.Kreatinin,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_40:          form10A.Clearance,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_41:          form10A.LDH,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_42:          form10A.LDL,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_43:          form10A.Lipase,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_44:          form10A.Natrium,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_45:          form10A.OP,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_46:          form10A.Phosphat,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_47:          form10A.Transferrin,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_48:          form10A.Triglyceride,
// 		formkey.MUSTER_10A_N_LABEL_CHECKBOX_EXAM_49:          form10A.TSH,
// 	}

// 	return payload, nil
// }

// func mapMusterForm63(template, dateFormat string) (map[formkey.FormKey_MUSTER_N63A]any, error) {
// 	form63 := &MusterForm63{}

// 	err := regexMuster63.MatchToTarget(template, form63)
// 	if err != nil {
// 		return nil, err
// 	}

// 	fromDate, fromDateErr := util.ConvertStringToMillisecond(form63.FromDate, dateFormat)
// 	toDate, toDateErr := util.ConvertStringToMillisecond(form63.ToDate, dateFormat)
// 	payload := map[formkey.FormKey_MUSTER_N63A]any{
// 		formkey.MUSTER_N63A_CHECKBOX_63_ERSTVERORDNUNG_0:             form63.Erstverordnung,
// 		formkey.MUSTER_N63A_CHECKBOX_63_FOLGEVERORDNUNG_0:            form63.Folgeverordnung,
// 		formkey.MUSTER_N63A_CHECKBOX_63_UNFALL_UNFALLFOLGEN_0:        form63.Unfall,
// 		formkey.MUSTER_N63A_DATE_LABEL_CUSTOM_VOM_0:                  function.If(fromDateErr == nil, fromDate, 0),
// 		formkey.MUSTER_N63A_DATE_LABEL_CUSTOM_BIS_0:                  function.If(toDateErr == nil, toDate, 0),
// 		formkey.MUSTER_N63A_TEXTBOX_DIAGNOSE_LINE1_0:                 form63.ICD,
// 		formkey.MUSTER_N63A_CHECKBOX_63_AUS_SCHMERZ_0:                form63.Schmerzsymptomatik,
// 		formkey.MUSTER_N63A_CHECKBOX_63_AUS_URO_0:                    form63.Urogenitale,
// 		formkey.MUSTER_N63A_CHECKBOX_63_AUS_RESPIRATORIS_0:           form63.Respiratorische,
// 		formkey.MUSTER_N63A_CHECKBOX_63_AUS_GASTRO_0:                 form63.Gastrointestinale,
// 		formkey.MUSTER_N63A_CHECKBOX_63_AUS_ULZERIE_0:                form63.Ulzerierende,
// 		formkey.MUSTER_N63A_CHECKBOX_63_AUS_NEUROLOGISCHE_0:          form63.Neurologische,
// 		formkey.MUSTER_N63A_CHECKBOX_63_AUS_KOMPLEXES_0:              form63.Sonstiges,
// 		formkey.MUSTER_N63A_CHECKBOX_63_BERATUNG_0:                   form63.Beratung,
// 		formkey.MUSTER_N63A_CHECKBOX_63_DES_BEH_ARZTES_0:             form63.Arztes,
// 		formkey.MUSTER_N63A_CHECKBOX_63_DES_BEH_PFLEGEFACHKRAFT_0:    form63.Pflegefachkraft,
// 		formkey.MUSTER_N63A_CHECKBOX_63_DES_PAT_DER_ANGEHORIGEN_0:    form63.Patienten,
// 		formkey.MUSTER_N63A_CHECKBOX_63_KOORDINATION_0:               form63.Koordination,
// 		formkey.MUSTER_N63A_CHECKBOX_63_ADDITIV_UNT_TEILVERSORGUNG_0: form63.Additiv,
// 		formkey.MUSTER_N63A_CHECKBOX_63_VOL_VERSORGUNG_0:             form63.Vollstandige,
// 	}

// 	return payload, nil
// }

// func mapMusterForm56(template, _ string) (map[formkey.FormKey_MUSTER_56]any, error) {
// 	form56 := &MusterForm56{}
// 	err := regexMuster56.MatchToTarget(template, form56)
// 	if err != nil {
// 		return nil, err
// 	}
// 	payload := map[formkey.FormKey_MUSTER_56]any{
// 		formkey.MUSTER_56_CHECKBOX_56_FUR_REHABILITATIONSSPORT_0: form56.Rehabilitationssport,
// 		formkey.MUSTER_56_CHECKBOX_56_FUR_FUNKTIONSTRAINING_0:    form56.Funktionstraining,
// 		formkey.MUSTER_56_TEXTBOX_SINGLE_DIAGNOSE1_0:             form56.Diagnose,
// 		formkey.MUSTER_56_TEXTBOX_56_SCHADIGUNG_0:                form56.Schadigung,
// 		formkey.MUSTER_56_TEXTBOX_56_ZIELDES_0:                   form56.ZielDes,
// 		formkey.MUSTER_56_CHECKBOX_REHABILITATIONSSPORT_1X_1:     form56.Prowoche1,
// 		formkey.MUSTER_56_CHECKBOX_REHABILITATIONSSPORT_2X_1:     form56.Prowoche2,
// 		formkey.MUSTER_56_CHECKBOX_REHABILITATIONSSPORT_3X_1:     form56.Prowoche3,
// 	}
// 	return payload, nil
// }

// func mapMusterForm13(template string) (map[formkey.FormKey_MUSTER_13]any, *MusterForm13, error) {
// 	form13 := &MusterForm13{}
// 	err := regexMuster13.MatchToTarget(template, form13)
// 	if err != nil {
// 		return nil, nil, err
// 	}
// 	payload := map[formkey.FormKey_MUSTER_13]any{
// 		formkey.MUSTER_13_CHECKBOX_PHYSIOTHERAPIE_0:        form13.Physiotherapie,
// 		formkey.MUSTER_13_CHECKBOX_PODOLOGISCHE_0:          form13.Podologische,
// 		formkey.MUSTER_13_CHECKBOX_SCHLUCKTHERAPIE_0:       form13.Stimm,
// 		formkey.MUSTER_13_CHECKBOX_ERGOTHERAPIE_0:          form13.Ergotherapie,
// 		formkey.MUSTER_13_CHECKBOX_ERNAHRUNGSTHERAPIE_0:    form13.Ernahrungstherapie,
// 		formkey.MUSTER_13_CHECKBOX_ZUZAHLUNGSFREI_0:        form13.Gebuhrenfrei,
// 		formkey.MUSTER_13_CHECKBOX_ZUZAHLUNGPFLICHT_0:      form13.Gebuhrenpflichtig,
// 		formkey.MUSTER_13_CHECKBOX_UNFALLFOLGEN_0:          form13.Unfallfolgen,
// 		formkey.MUSTER_13_CHECKBOX_BVG_0:                   form13.BVG,
// 		formkey.MUSTER_13_LABEL_ICD_CODE10_LINE1_0:         form13.ICD1,
// 		formkey.MUSTER_13_LABEL_ICD_CODE10_LINE2_0:         form13.ICD2,
// 		formkey.MUSTER_13_AREA_TEXTBOX_ICD_CODE10_0:        form13.ICDText,
// 		formkey.MUSTER_13_LABEL_DIAGNOSE_GRUPPE_0:          form13.Diagnosegruppe,
// 		formkey.MUSTER_13_CHECKBOX_LEITSYMPTOMATIK_A_0:     form13.Leitsymptomatik_a,
// 		formkey.MUSTER_13_CHECKBOX_LEITSYMPTOMATIK_B_0:     form13.Leitsymptomatik_b,
// 		formkey.MUSTER_13_CHECKBOX_LEITSYMPTOMATIK_C_0:     form13.Leitsymptomatik_c,
// 		formkey.MUSTER_13_CHECKBOX_PATIENTENINDIVIDUELLE_0: form13.Leitsymptomatik_c,
// 		formkey.MUSTER_13_AREA_TEXTBOX_LEITSYMTOMATIK_0:    form13.LeitsymptomatikText,
// 		formkey.MUSTER_13_TEXTBOX_HEILMITTEL_LINE1_0:       form13.Heilmittel_1,
// 		formkey.MUSTER_13_TEXTBOX_HEILMITTEL_LINE2_0:       form13.Heilmittel_2,
// 		formkey.MUSTER_13_TEXTBOX_HEILMITTEL_LINE3_0:       form13.Heilmittel_3,
// 		formkey.MUSTER_13_TEXTBOX_BEHANDLUNG_LINE1_0:       form13.Quantity_1,
// 		formkey.MUSTER_13_TEXTBOX_BEHANDLUNG_LINE2_0:       form13.Quantity_2,
// 		formkey.MUSTER_13_TEXTBOX_BEHANDLUNG_LINE3_0:       form13.Quantity_3,
// 		formkey.MUSTER_13_TEXTBOX_ERGANZENDES_0:            form13.Heilmittel_4,
// 		formkey.MUSTER_13_TEXTBOX_BEHANDLUNG_LINE4_0:       form13.Quantity_4,
// 		formkey.MUSTER_13_CHECKBOX_THERAPIEBERICHT_0:       form13.Therapiebericht,
// 		formkey.MUSTER_13_CHECKBOX_HAUSBESUCH_JA_0:         form13.Hausbesuch_ja,
// 		formkey.MUSTER_13_CHECKBOX_HAUSBESUCH_NEIN_0:       form13.Hausbesuch_nein,
// 		formkey.MUSTER_13_LABEL_THERAPIE_FREQUENZ_0:        form13.Therapiefrequenz,
// 		formkey.MUSTER_13_CHECKBOX_DRINGLICHER_14_TAGEN_0:  form13.Dringlicher,
// 		formkey.MUSTER_13_AREA_TEXTBOX_THERAPIEZIELE_0:     form13.TherapiezieleText,
// 	}

// 	return payload, form13, nil
// }
