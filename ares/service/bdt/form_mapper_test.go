package bdt_service

import (
	"strings"
	"testing"

	bdt_model "git.tutum.dev/medi/tutum/ares/pkg/xdt/bdt/model"
	"git.tutum.dev/medi/tutum/pkg/util"

	"github.com/stretchr/testify/require"
)

// func Test_map_form4_sample(t *testing.T) {
// 	form4Data := "1:Verordnung einer Krankenbeförderung\n[ ] Zuzahlungsfrei   [X] Zuzahlungspflichtig\n[ ] Unfallfolgen     [ ] Arbeitsunfall/Berufskrankheit   [X] BVG\n[ ] Hinfahrt         [ ] Rückfahrt\n1. Grund der Beförderung\nGenehmigungsfreie Fahrten\na) [X] voll- oder teilstationäre Krankenhausbehandlung\n   [ ] vor- oder nachstationäre Behandlung\nb) [ ] ambulante Behandlung\nc) [X] anderer Grund: anderer  1234    \nGenehmigungspflichtige Fahrten zu ambulanten Behandlungen\nd) hochfrequente Behandlung\n   [X] Dialyse, onkol. Chemo- oder Strahlentherapie\n   [ ] vergleichbarer Ausnahmefall\ne) [ ] dauerhafte Mobilitätseinschränkung\nf) [ ] anderer Grund für Fahrt mit KTW\n2. Behandlungstag/-frequenz und Behandlungsstätte\nvom/am  06062024          / 5 x pro Woche, bis voraussichtlich 07072024 \nBehandlungsstätte: Behandlungsstätte \n3. Art der Beförderung\n[ ] Taxi/Mietwagen  [ ] Rollstuhl  [ ] Tragestuhl  [X] liegend\n[X] KTW, da medizinisch-fachliche Betreuung und/oder\n    Einrichtung notwendig ist wegen: Einrichtung\n[X] RTW  [ ] NAW/NEF  [ ] andere: andere, text #$%#$@   \nBegründung/Sonstiges:"
// 	payload, err := mapMusterForm4(form4Data, util.DDMMYYYY)
// 	t.Logf("%+v\n", payload)
// 	require.NoError(t, err)
// }
// func Test_map_form10A_sample(t *testing.T) {
// 	form10AData := `Geschlecht: M
// [X] Kurativ   [ ] Präventiv
// [ ] bei belegärztl. Behandlung   [ ] Unfallfolgen
// Knappschaftskennziffer:    12 SSW:3333
// Auftragsnummer des Labors: 2323
// Abnahmedatum:   12122024            Abnahmezeit: 1506
// [ ] Befund eilt
// EDTA-Blut                               Glukose-Profil
// [X] großes Blutbild                     [X] Glukose 1
// [ ] kleines Blutbild                    [ ] Glukose 2
// [ ] HbA1c                               [ ] Glukose 3
// [ ] Retikulozyten                       [ ] Glukose 4
// [ ] BlutSenkung                         Urin
// Gesundheitsuntersuchungen               [X] Status
// [ ] Harnstreifentest (32880)            [ ] Mikroalbumin
// [X] Nüchternplasmaglukose (32881)       [ ] Glukose
// [ ] Lipidprofil (32882)                 [ ] Sediment
// Citrat-Blut
// [ ] Quick                               [ ] Thrombinzeit
// [ ] Quick unter Marcumar-Therapie       [ ] PTT
// Serum / Plasma / Vollblut
// [ ] alkalische      [ ] Gamma GT        [ ] LDL-Cholesterin
//     Phosphatase     [ ] Glukose         [ ] Lipase
// [ ] Amylase         [ ] GOT / ASAT      [ ] Natrium
// [ ] ASL             [ ] GPT / ALAT      [ ] OP-Vorbereitung
// [ ] Bilirubin dir.  [ ] Harnsäure           (32125)
// [X] Bilirubin ges.  [X] Harnstoff       [ ] Phosphat
// [ ] Calcium         [ ] HDL-Cholesterin     anorganisches
// [ ] Cholesterin     [ ] IgA             [ ] Transferrin
// [ ] Cholinesterase  [ ] IgG             [ ] Triglyceride
// [ ] CK              [ ] IgM             [ ] TSH
// [ ] CRP             [ ] Kalium
// [ ] Eisen           [ ] Kreatinin
// [ ] Eiweiß Elektro. [ ] Kreatinin Clearance
// [ ] Eiweiß gesamt   [ ] LDH`
// 	payload, err := mapMusterForm10A(form10AData, util.DDMMYYYY)

// 	t.Logf("%+v\n", payload)
// 	require.NoError(t, err)
// }

// func Test_map_form63_sample(t *testing.T) {
// 	form63Data := `[X] Erstverordnung   [ ] Folgeverordnung   [ ] Unfall, Unfallfolgen
// vom  19052024           bis 19072024
// Verordnungsrelevante Diagnose(n)/ICD:
// C43.5G, J09.G
// komplexes Symptomgeschehen:
// [ ] ausgeprägte Schmerzsymptomatik
// [ ] ausgeprägte urogenitale Symptomatik
// [ ] ausgeprägte respiratorische/kardiale Symptomatik
// [ ] ausgeprägte gastrointestinale Symptomatik
// [ ] ausgeprägte ulzerierende/exulzerierende Wunden oder Tumore
// [ ] ausgeprägte neurologische/psych. Symptomatik
// [ ] sonstiges komplexes Symptomgeschehen
// Nähere Beschreibung des komplexen Symptomgeschehens
// Aktuelle Medikation (ggf. einschließlich BtM)
// Folgende Maßnahmen sind notwendig:
// [ ] Beratung  [ ] a. des behandelnden Arztes
//   [ ] b. der behandelnden Pflegefachkraft
//       [ ] c. des Patienten / der Angehörigen
// [ ] Koordination der Palliativversorgung
// mit folgender inhaltlicher Ausrichtung:
// [ ] additiv unterstützende Teilversorgung
// [ ] Vollständige Versorgung
// Nähere Angaben zu den notwendigen Maßnahmen:`
// 	payload, err := mapMusterForm63(form63Data, util.DDMMYYYY)

// 	t.Logf("%+v\n", payload)
// 	require.NoError(t, err)
// }

// func Test_map_form_56(t *testing.T) {
// 	form56Data := `[X] für Rehabilitationssport
// [ ] für Funktionstraining`

// 	payload, err := mapMusterForm56(form56Data, util.DDMMYYYY)
// 	t.Logf("%+v\n", payload)
// 	require.NoError(t, err)
// }

// func Test_map_form13_sample(t *testing.T) {
// 	form13Data := "1:Heilmittelverordnung\n[X] gebührenpflichtig  [ ] gebührenfrei\n[ ] Unfallfolgen       [ ] Versorgungsleiden/BVG\n[ ] Physiotherapie\n[ ] Podologische Therapie\n[ ] Stimm-, Sprech-, Sprach- und Schlucktherapie\n[X] Ergotherapie\n[ ] Ernährungstherapie\nBehandlungsrelevante Diagnose(n):\nICD-10-Code: C43.5   J09.\nBösartiges Melanom des Rumpfes;Grippe durch zoonotische oder\npandemische nachgewiesene Influenzaviren\nDiagnosegruppe: PS4\nLeitsymptomatik: [X] a   [X] b   [ ] c\n[ ] patientenindividuelle Leitsymptomatik:\nSchädigung der globalen mentalen Funktionen;Schädigung der spezifisch^\nen\nmentalen Funktionen;\nHeilmittel:\nkmt   Einheiten: 2\nkg   Einheiten: 3\nTherapiefrequenz: 1-2x wöch.\n[ ] Therapiebericht    Hausbesuch: [X] ja   [ ] nein\n[X] Dringlicher Behandlungsbedarf innerhalb von 14 Tagen\nggf. Therapieziele / weitere med. Befunde und Hinweise:\ntest random text\ntest random line 2\nline 3\nline 4"
// 	payload, _, err := mapMusterForm13(form13Data)
// 	t.Logf("%+v\n", payload)
// 	require.NoError(t, err)
// }

// func Test_map_form13_with_therapieziele_sample(t *testing.T) {
// 	form13Data := "Heilmittelverordnung\n[X] gebührenpflichtig  [ ] gebührenfrei\n[ ] Unfallfolgen       [ ] Versorgungsleiden/BVG\n[X] Physiotherapie\n[ ] Podologische Therapie\n[ ] Stimm-, Sprech-, Sprach- und Schlucktherapie\n[ ] Ergotherapie\n[ ] Ernährungstherapie\nBehandlungsrelevante Diagnose(n):\nICD-10-Code: J35.0   C23\nChronische Tonsillitis;add random text her tBösartige Neubildung\nder Gallenblase\nDiagnosegruppe: EX\nLeitsymptomatik: [ ] a   [X] b   [ ] c\n[ ] patientenindividuelle Leitsymptomatik:\ntttttttt random; Schädigung/Störung der Muskelfunktion;\nHeilmittel:\nkmt   Einheiten: 2\nkg   Einheiten: 3\nErgänzendes Heilmittel:\nwarmtherapie   Einheiten: 1\nTherapiefrequenz: 1-2x wöch.\n[X] Therapiebericht    Hausbesuch: [X] ja   [ ] nein\n[X] Dringlicher Behandlungsbedarf innerhalb von 14 Tagen\nggf. Therapieziele / weitere med. Befunde und Hinweise:\ntest random text\ntest random line 2\nline 3\nline 4"
// 	payload, _, err := mapMusterForm13(form13Data)
// 	t.Logf("%+v\n", payload)
// 	require.NoError(t, err)
// }

// extract form data from bdt file
func parseData(data string) bdt_model.Un6295 {
	lines := strings.Split(data, "\n")
	var result bdt_model.Un6295
	var currentUn6296 *bdt_model.Un6296 // pointer to keep track of current Un6296 entry

	for _, line := range lines {
		if len(line) < 7 {
			continue // skip lines that are too short to contain meaningful data
		}

		fieldCode := line[3:7] // extract field code (ignore third character)
		content := line[7:]    // extract content after field code

		switch fieldCode {
		case "6295":
			result.Un6295 = content
		case "6296":
			// Start a new Un6296 section
			currentUn6296 = &bdt_model.Un6296{Un6296: content}
			result.Un6296 = append(result.Un6296, *currentUn6296)
		case "6297":
			// Append to the last Un6296's Un6297 field if it exists
			if currentUn6296 != nil {
				result.Un6296[len(result.Un6296)-1].Un6297 = append(result.Un6296[len(result.Un6296)-1].Un6297, content)
			}
		}
	}
	return result
}

var muster52 = `
0866295AUWEITER#Bericht für die Krankenkasse bei Fortbestehen der Arbeitsunfähigkeit
0136296cIcd
0156297F43.0G
0196296cErwerbals
0106297X
0176297Arbeiter
0196296lWiederafN
01062971
0206296cWeiterarzt
0536297Garrio GmbH,Garriostraße 12,70565 Stuttgart ,
0216296lMassnpsychJ
01062971
0196296lProblemeJ
01062971
0206296lMinderungN
01062971
`

var muster523 = `
0866295AUWEITER#Bericht für die Krankenkasse bei Fortbestehen der Arbeitsunfähigkeit
0136296cIcd
0156297F43.0G
0156297F43.9G
0156297F32.8G
0196296cErwerbals
0106297X
0176297Arbeiter
0196296lWiederafN
01062971
0216296lMassnpsychJ
01062971
0196296lProblemeJ
01062971
0186296cProbleme
0306297bevorstehende Hüft-OP
0206296lMinderungN
01062971
0216296cMasskonserv
0236297Psychotherapie
`

var muster55 = `
0806295CHRONERKRANK#Bescheinigung einer schwerwiegenden chronischen Erkrankung
0206296dBehandseit
017629720220902
0146296cIcd1
0156297G81.0G
0146296cIcd2
0156297G83.6G
0146296cIcd3
0156297R26.0G
0216296lMedversorgJ
01062971
`

var muster61 = `
0626295REHABERATUNG#Beratung zu medizinischer Rehabilitation
0206296cGrunddiag1
0326297mehrere Rippenfrakturen
0196296cGrundicd1
0166297S22.40G
0206296cGrunddiag2
0256297Commotio Cerebri
0196296cGrundicd2
0156297S06.0G
0206296cRelevdiag1
0306297Insulinpfl.Diab.mell.
0196296cRelevicd1
0156297E11.90
0206296cRelevdiag2
0126297KHK
0196296cRelevicd2
0156297I25.11
0206296cRelevdiag3
0226297Niereninsuff.
0196296cRelevicd3
0146297N18.4
0216296lKkberatungJ
01062971
0216296cKkanmerkung
0376297Reha zur Wiedererlangung der
0416297Selbständigkeit in Betreutem Woh
0216296dAusstellung
017629720210302
`

var muster22 = `
0716295KONSILIARPT#Konsiliarbericht vor Aufnahme einer Psychotherapie
0196296cTherapeut
0396297Dipl.-Psych. Katharina Neumann
0146296cLanr
0186297015100268
0146296cBsnr
0186297617019600
0206296cPatchiffre
0166297H240970
0186296cDiagnose
0786297Seit länger rezidivierende depressive Episoden, aktuell mittelgradig.
0376297Generalisierte Angststörung.
0626297Probleme bei der Arbeit.Psychosomatische Beschwerden.
0216296lAbklerfolgJ
01062971
0206296lMitbehandJ
01062971
0196296cMitbehand
0336297Mitbehandlng Psychiatrie
0216296dAusstellung
017629720200717
`

var muster65 = `
0426295ATTESTKIND#Ärztliches Attest Kind
0216296dAusstellung
017629720240426
`

var muster64 = `
0796295VORSORGEMV#Verordnung von medizinischer Vorsorge für Mütter oder Väter
0216296cGesundstoer
0596297Zunehmende Erschöpfung, verminderte Belastbarkeit.
0216296cErkrankung1
0636297Rezidivierende depressive Störung Spannungskopfschmerz
0146296cIcd1
0156297F33.1G
0216296cErkrankung2
0506297Krankheit der Wirbelsäule und des Rückens
0146296cIcd2
0166297M53.90G
0216296cErkrankung3
0396297Allergisches Asthma bronchiale
0146296cIcd3
0156297J45.0G
0186296cAnamnese
0726297Seit Jahren zunehmend ERschöpfung, Schlafprobleme. Reizbarkeit.
0216296cBeeintraech
0766297Vermindert auf eigene Gesundheit achten, Schwierigkeiten mit Stress
0666297umzugehen. zunehmend Vernachlässigung eigener Interessen.
0216296lMehrfachbel
01062971
0196296lBeziehung
01062971
0196296lErziehung
01062971
0216296lAlltagsprob
01062971
0216296cInterventio
0256297Familientherapie
0206296cMassnahmen
0326297Psychologische Beratung
0216296cVorsorgziel
0796297Stärkung der psychologischen Stabilität, Steigerung der Kompetenzen in
0736297Bezug auf Stresssituationen, Steigerung des Familienzusamenhalts
0216296lLstmuttkind
01062971
0196296cNamekind1
0306297Kalaitzidis, Nikolaos
0216296dGeburtkind1
017629720160521
0216296lBeziehkind1
01062971
0196296cNamekind2
0306297Kalaitzidis, Georgios
0216296dGeburtkind2
017629720190610
0216296lAnforderung
01062971
0216296cAnforderung
0686297Bad Schönborn-Mikina-Fachklinik, Lenzkirch Fachklinik Ursee
0216296dAusstellung
017629720220223
`
var muster20 = `
0856295EINGLPLAN#Maßnahmen zur stufenweisen Wiedereingliederung in das Erwerbsleben
0196296cVortaetig
0276297Betriebshandwerker
0206296cVorstunden
01062977
0206296dTaetigvom1
017629720230306
0206296dTaetigbis1
017629720230319
0206296cTaetigstd1
01062974
0206296dTaetigvom2
017629720230320
0206296dTaetigbis2
017629720230402
0206296cTaetigstd2
01062976
`

var muster4 = `
0596295KRBEFOERD_2020#Verordnung einer Krankenbeförderung
0186296lGebfreiJ
01062971
0196296lHinfahrtJ
01062971
0206296lAmbbehandJ
01062971
0216296lRueckfahrtJ
01062971
0206296lDauer6monJ
01062971
0216296dTransprtvom
017629720221207
0216296dTransprtbis
017629720221207
0196296cBehandort
0536297Garrio GmbH,Garriostraße 12,70565 Stuttgart ,
0156296lTaxiJ
01062971
0196296cSonstiges
0286297Mobilitätsminderung
`
var muster12 = `
0586295HKPFLEGE_2020#Verordnung häuslicher Krankenpflege
0146296cIcd1
0156297F01.9G
0146296cIcd2
0166297I70.29G
0146296cIcd3
0156297R29.6G
0146296cIcd4
0146297R54.G
0156296lErstJ
01062971
0196296dVerordvom
017629720221031
0196296dVerordbis
017629720221113
0186296cPraepmed
0596297Bezafibrat, ASS, Pantoprazol, Enalapril, Metformin
0296297Memantin, Risperidon
0216296lMedherrichJ
01062971
0186296cMedherwo
01062971
0196296cMedhervom
*********.10.
0196296cMedherbis
*********.11.
0196296lMedgebenJ
01062971
0216296cMedgebentgl
01062972
0216296cMedgebenvom
*********.10.
0216296cMedgebenbis
*********.11.
0206296lKhvermeidJ
01062971
0196296lGrundpflJ
01062971
0216296cGrundpfltgl
01062971
0216296cGrundpflvom
*********.10.
0216296cGrundpflbis
*********.11.
0196296lHauswirtJ
01062971
0206296cHauswirtwo
01062971
0216296cHauswirtvom
*********.10.
0216296cHauswirtbis
*********.11.
0186296cHinweise
0316297Berthel-Index: 40 Pkt.
0456297Grundpflege: 08:00 Uhr bis 10:00 Uhr
0606297hauswirtschaftliche Versorgung: 10:00 bis 12:00 Uhr
`

var muster61B = `
0676295REHAVERORDNUNG#Verordnung von medizinischer Rehabilitation
0216296lMinderwerbN
01062971
0216296lMedrehagkvJ
01062971
0206296cGrunddiag1
0326297mehrere Rippenfrakturen
0196296cGrundicd1
0166297S22.40G
0206296cGrunddiag2
0256297Commotio Cerebri
0196296cGrundicd2
0156297S06.0G
0206296cRelevdiag1
0306297Insulinpfl.Diab.mell.
0196296cRelevicd1
0156297E11.90
0206296cRelevdiag2
0126297KHK
0196296cRelevicd2
0156297I25.11
0206296cRelevdiag3
0226297Niereninsuff.
0196296cRelevicd3
0146297N18.4
0186296cAnamnese
0676297Sturz am 03.02.21,Krankenhausaufenthalt vom 3.2.-17.2.2021
0166296cBefund
0656297Einschränkung von Kraft und Beweglichkeit,Schmerzsyndrom
0196296cIntervent
0346297derzeit in Kurzzeitpflege
0206296cHeilmittel
0476297bisher selbständig in Betreutem Wohnen
0206296lHmreichenN
01062971
0216296lHilfmittelJ
01062971
0206296cHilfmittel
0406297KG,Beweglichk.-und Krafttrainig
0206296cMassnahmen
0536297Funktionstraining,Förderung der Koordination
0196296lLerneneiN
01062971
0206296lAufgabeeiN
01062971
0186296lKommuniJ
01062971
0206296lTransfreiN
01062971
0206296lStehgeheiN
01062971
0206296lTreppeneiN
01062971
0186296lNahrungJ
01062971
0206296lKleideneiN
01062971
0186296lWaschenJ
01062971
0206296lDuscheneiN
01062971
0186296lToilettJ
01062971
0206296lHauslebeiN
01062971
0186296lPersaktJ
01062971
0206296lBereicheiN
01062971
0176296lSozialJ
01062971
0206296cRehafaktor
0756297Ziel der Patientin:wieder Selbständigkeit mit Unterstützung in Be-
0236297treutem Wohnen
0216296lRfbewmangel
01062971
0216296lRehafaehigJ
01062971
0216296cRehazielobj
0566297Verbesserung der Beweglichkeit und Koordination
0206297Kraftaufbau
0216296lRehaprognoJ
01062971
0216296lRehastatioN
01062971
0206296cIndikation
0156297s.oben
0216296lVerschlimmJ
01062971
0216296dAusstellung
017629720210302
`

// test to get payload of timeline
func Test_payload_form(t *testing.T) {
	res := parseData(muster61B)
	formMapper := newFormMapper(res.Un6296, res.Un6295, util.YYYYMMDD, nil)
	require.NotNil(t, formMapper)
	_, err := formMapper.Map()
	require.NoError(t, err)
}

func Test_Map_Multiple_Form_Same_Name(t *testing.T) {
	res := parseData(muster52)
	formMapper := newFormMapper(res.Un6296, res.Un6295, util.YYYYMMDD, nil)
	require.NotNil(t, formMapper)
	payload, err := formMapper.Map()
	require.NoError(t, err)
	res1 := parseData(muster523)
	formMapper = newFormMapper(res1.Un6296, res1.Un6295, util.YYYYMMDD, nil)
	require.NotNil(t, formMapper)
	payload1, err := formMapper.Map()
	require.NoError(t, err)
	require.False(t, strings.EqualFold(payload, payload1)) // should return new payload1
}
