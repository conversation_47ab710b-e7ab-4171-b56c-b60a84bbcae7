package gdt

import (
	"bytes"
	"os"
	"testing"

	"git.tutum.dev/medi/tutum/ares/service/domains/api/document_setting_common"
	field_transfer "git.tutum.dev/medi/tutum/pkg/field_transfer"
	"github.com/stretchr/testify/assert"
)

func TestDecodePatientReportFile(t *testing.T) {
	// Open the test LDT file
	file, err := os.Open("testdata/ldt/auftrag.LDT.ldt")
	if err != nil {
		t.Fatalf("Failed to open test file: %v", err)
	}
	defer file.Close()

	// Decode the file
	report, err := DecodePatientDataFile(file)
	assert.NoError(t, err)
	assert.NotNil(t, report)

	// Test header fields (8230)
	assert.Equal(t, "8230", report.Header.RecordType)

	// Test patient record fields (8218)
	assert.Equal(t, "8218", report.PatientRecord.RecordType)

	// Test footer fields (8231)
	assert.Equal(t, "8231", report.Footer.RecordType)
	assert.Equal(t, "011234567", report.Header.FacilityNumber)
	assert.Equal(t, "K", report.PatientRecord.BillingType)

	t.Logf("Successfully decoded LDT file with header type %s, patient type %s, footer type %s",
		report.Header.RecordType, report.PatientRecord.RecordType, report.Footer.RecordType)

	// Log some decoded values for verification
	t.Logf("Header - Facility Number: %s, Facility Name: %s", report.Header.FacilityNumber, report.Header.FacilityName)
	t.Logf("Patient - Last Name: %s, First Name: %s, Billing Type: %s",
		report.PatientRecord.LastName, report.PatientRecord.FirstName, report.PatientRecord.BillingType)
	t.Logf("Footer - Total Package Length: %s", report.Footer.TotalDataPackageLength)
}

func TestEncodeThenDecode(t *testing.T) {
	patientFile := PatientDataFile{
		Header: Sentence8230{
			FieldAttr:      field_transfer.FieldAttr("8230"),
			RecordType:     "8230",
			RecordLength:   "00000",
			FacilityNumber: "011234567",
			FacilityName:   "Test Facility",
		},
		PatientRecord: Sentence8218{
			FieldAttr:    field_transfer.FieldAttr("8218"),
			RecordType:   "8218",
			RecordLength: "00000",
			LastName:     "Doe",
			FirstName:    "John",
			BillingType:  "K",
		},
		Footer: Sentence8231{
			FieldAttr:              field_transfer.FieldAttr("8231"),
			RecordType:             "8231",
			RecordLength:           "00000",
			TotalDataPackageLength: "00000",
		},
	}

	SetSentenceLengthByTag8100(&patientFile.Header)
	SetSentenceLengthByTag8100(&patientFile.PatientRecord)
	SetSentenceLengthByTag8100(&patientFile.Footer)

	encoder := getEncoder(document_setting_common.CharacterEncoding_7Bit)
	encoded := StructToByteArrayWithEncoder(patientFile, encoder)

	decodedStruct, err := DecodePatientDataFile(bytes.NewReader(encoded))
	assert.NoError(t, err)
	assert.Equal(t, patientFile, *decodedStruct)

}

func TestGenericDecodeLDTFile(t *testing.T) {
	// Open the test LDT file
	file, err := os.Open("testdata/ldt/auftrag.LDT.ldt")
	if err != nil {
		t.Fatalf("Failed to open test file: %v", err)
	}
	defer file.Close()

	// Test 1: Decode as PatientDataFile using the generic function
	t.Run("Generic decode to PatientDataFile", func(t *testing.T) {
		file.Seek(0, 0) // Reset file position

		var patientData PatientDataFile
		err := DecodeLDTFile(file, &patientData)
		assert.NoError(t, err)

		assert.Equal(t, "8230", patientData.Header.RecordType)
		assert.Equal(t, "8218", patientData.PatientRecord.RecordType)
		assert.Equal(t, "8231", patientData.Footer.RecordType)
		assert.Equal(t, "011234567", patientData.Header.FacilityNumber)

		t.Logf("PatientDataFile decoded successfully with generic function")
	})

}
