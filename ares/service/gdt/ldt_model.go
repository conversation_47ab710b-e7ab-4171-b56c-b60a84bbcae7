package gdt

import (
	"fmt"
	"io"
	"reflect"
	"strings"

	"git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"
	field_transfer "git.tutum.dev/medi/tutum/pkg/field_transfer"
	"git.tutum.dev/medi/tutum/pkg/util"
)

// Sentence8220 represents an LDT order record containing laboratory order information
type Sentence8220 struct {
	// Record metadata (mandatory fields)
	field_transfer.FieldAttr `field:"8000"` // Embedded FieldAttr for advanced usage
	RecordType               string         `field:"8000"` // Type of the record (8220)
	RecordLength             string         `field:"8100"` // Total length of all fields in the record in bytes
	LDTVersion               string         `field:"9212"` // Binding version: LDT1014.01

	// Facility/Practice information
	FacilityNumber string `field:"0201"` // Main/Subsidiary Facility Number (BSNR/NBSNR)
	FacilityName   string `field:"0203"` // Name or designation of the facility/subsidiary
	FacilityStreet string `field:"0205"` // Street address of the facility or subsidiary
	FacilityZIP    string `field:"0215"` // ZIP code of the facility or subsidiary
	FacilityCity   string `field:"0216"` // City of the facility or subsidiary

	// Physician information (hierarchy level 1)
	PhysicianID   []string `field:"0212"` // Lifetime Physician ID (LANR), default: 999999900
	PhysicianName []string `field:"0211"` // Name of the physician

	// ASV Team information (hierarchy level 2)
	ASVTeamNumber []string `field:"0222"` // ASV Team Number (can occur multiple times)

	// Pseudo-LANR for hospital physicians
	PseudoLANR []string `field:"0223"` // Temporary identifier for hospital physicians in ASV billing

	// Laboratory information
	Laboratory       string `field:"8300"` // Laboratory identifier
	LaboratoryName   string `field:"8320"` // Name of the laboratory
	LaboratoryStreet string `field:"8321"` // Street address of the laboratory
	LaboratoryZIP    string `field:"8322"` // ZIP code of the laboratory
	LaboratoryCity   string `field:"8323"` // City of the laboratory

	// Verification and system information
	KBVVerificationNumber string `field:"0101"` // Verification number assigned by KBV
	CharacterSet          string `field:"9106"` // Encoding/character set applied to the data
	CustomerNumber        string `field:"8312"` // Identifier for the physician/customer
	CreationDate          string `field:"9103"` // Creation date

	// General information and security
	GeneralInformation []string `field:"9472"` // General messages from the laboratory to the sender
	Checksum           string   `field:"9300"` // Checksum/electronic signature
	CryptoKey          string   `field:"9301"` // Crypto key
}

// Sentence8221 represents an LDT order footer record containing data package summary information
type Sentence8221 struct {
	// Record metadata (mandatory fields)
	field_transfer.FieldAttr `field:"8000"` // Embedded FieldAttr for advanced usage
	RecordType               string         `field:"8000"` // Identifier for the record type, here: 8221
	RecordLength             string         `field:"8100"` // Sum of all field lengths of the record in bytes

	// Data package information
	TotalDataPackageLength string `field:"9202"` // Total of all record lengths of the entire data package in bytes
}

// Sentence8230 represents an LDT order header record containing comprehensive laboratory and facility information
type Sentence8230 struct {
	// Record metadata (mandatory fields)
	field_transfer.FieldAttr `field:"8000"` // Embedded FieldAttr for advanced usage
	RecordType               string         `field:"8000"` // Type of the record (8230)
	RecordLength             string         `field:"8100"` // Total length of all fields in the record in bytes
	LDTVersion               string         `field:"9212"` // Binding version: LDT1014.01

	// Facility/Practice information
	FacilityNumber string `field:"0201"` // Main/Subsidiary Facility Number (BSNR/NBSNR)
	FacilityName   string `field:"0203"` // Name or designation of the facility/subsidiary
	FacilityStreet string `field:"0205"` // Street address of the facility or subsidiary
	FacilityZIP    string `field:"0215"` // ZIP code of the facility or subsidiary
	FacilityCity   string `field:"0216"` // City of the facility or subsidiary

	// Physician information (hierarchy level 1)
	PhysicianID   []string `field:"0212"` // Lifetime Physician ID (LANR), default: 999999900
	PhysicianName []string `field:"0211"` // Name of the physician

	// ASV Team information (hierarchy level 2)
	ASVTeamNumber []string `field:"0222"` // ASV Team Number (can occur multiple times)

	// Pseudo-LANR for hospital physicians
	PseudoLANR []string `field:"0223"` // Temporary identifier for hospital physicians in ASV billing

	// Laboratory information
	Laboratory       string `field:"8300"` // Laboratory identifier
	LaboratoryName   string `field:"8320"` // Name of the laboratory
	LaboratoryStreet string `field:"8321"` // Street address of the laboratory
	LaboratoryZIP    string `field:"8322"` // ZIP code of the laboratory
	LaboratoryCity   string `field:"8323"` // City of the laboratory

	// Verification and system information
	KBVVerificationNumber string `field:"0101"` // Verification number assigned by KBV
	CharacterSet          string `field:"9106"` // Encoding/character set applied to the data
	CustomerNumber        string `field:"8312"` // Identifier for the physician/customer
	CreationDate          string `field:"9103"` // Creation date

	// General information and security
	GeneralInformation []string `field:"9472"` // General messages from the laboratory to the sender
	Checksum           string   `field:"9300"` // Checksum/electronic signature
	CryptoKey          string   `field:"9301"` // Crypto key
}

// Sentence8231 represents an LDT order footer record containing data package summary information
type Sentence8231 struct {
	// Record metadata (mandatory fields)
	field_transfer.FieldAttr `field:"8000"` // Embedded FieldAttr for advanced usage
	RecordType               string         `field:"8000"` // Identifier for the record type, here: 8231
	RecordLength             string         `field:"8100"` // Sum of all field lengths of the record in bytes

	// Data package information
	TotalDataPackageLength string `field:"9202"` // Total of all record lengths of the entire data package in bytes
}

// FeeInfo represents fee-related information (hierarchy level 3)
type FeeInfo struct {
	field_transfer.FieldAttr `field:"5001"` // Embedded FieldAttr for advanced usage
	FeeNumber                string         `field:"5001"` // Fee Number (GNR)
	CostInCent               string         `field:"8406"` // Cost in Cent
	Multiplier               string         `field:"5005"` // Multiplier
	BillingBy                string         `field:"8614"` // Billing By (1 = Lab, 2 = Referrer)
}

// TestInfo represents test-related information (hierarchy level 2)
type TestInfo struct {
	field_transfer.FieldAttr    `field:"8410"` // Embedded FieldAttr for advanced usage
	TestID                      string         `field:"8410"` // Test ID
	TestDescription             string         `field:"8411"` // Test Description
	TestStatus                  string         `field:"8418"` // Test Status
	SampleMaterialID            string         `field:"8428"` // Sample Material ID
	SampleMaterialIndex         string         `field:"8429"` // Sample Material Index
	SampleMaterialDescription   string         `field:"8430"` // Sample Material Description
	SampleMaterialSpecification []string       `field:"8431"` // Sample Material Specification (can occur multiple times)
	SamplingDate                string         `field:"8432"` // Sampling Date
	SamplingTime                string         `field:"8433"` // Sampling Time
	ResultValue                 string         `field:"8420"` // Result Value
	Unit                        string         `field:"8421"` // Unit
	ResultText                  []string       `field:"8480"` // Result Text (can occur multiple times)
	TestRelatedNotes            []string       `field:"8470"` // Test-related Notes (can occur multiple times)
	ReferenceValueText          []string       `field:"8460"` // Reference Value Text (can occur multiple times)
	ReferenceLowerLimit         string         `field:"8461"` // Reference Lower Limit
	ReferenceUpperLimit         string         `field:"8462"` // Reference Upper Limit
	ThresholdIndicator          string         `field:"8422"` // Threshold Indicator
	Fees                        []FeeInfo      `field:"5001"` // Fee information (can occur multiple times)
}

// Sentence8201 represents an LDT test result record with comprehensive patient and test information
type Sentence8201 struct {
	// Record metadata (mandatory fields)
	field_transfer.FieldAttr `field:"8000"` // Embedded FieldAttr for advanced usage
	RecordType               string         `field:"8000"` // Record Type (8201)
	RecordLength             string         `field:"8100"` // Sum of all fields in the record (bytes)

	// Order information
	RequestID        string `field:"8310"` // Request ID
	LabOrderNumber   string `field:"8311"` // Lab Order Number ("Day number")
	OrderReceiptDate string `field:"8301"` // Order Receipt Date (Lab)
	ReportDate       string `field:"8302"` // Report Date
	ReportTime       string `field:"8303"` // Report Time

	// Patient information
	Suffix      string `field:"3100"` // Suffix
	Prefix      string `field:"3120"` // Prefix
	LastName    string `field:"3101"` // Last Name
	FirstName   string `field:"3102"` // First Name
	DateOfBirth string `field:"3103"` // Date of Birth
	Title       string `field:"3104"` // Title
	Gender      string `field:"3110"` // Gender

	// Medical information
	DiagnosisSuspectedDiagnosis []string `field:"4207"` // Diagnosis / Suspected Diagnosis (can occur multiple times)

	// Billing and order details
	TypeOfFindings     string   `field:"8401"` // Type of Findings
	BillingType        string   `field:"8609"` // Billing Type
	Client             string   `field:"8615"` // Client (LANR/Physician ID)
	FeeSchedule        string   `field:"8403"` // Fee Schedule
	PatientInformation string   `field:"8405"` // Patient Information
	OrderRelatedNotes  []string `field:"8490"` // Order-related Notes (can occur multiple times)

	// Test information (hierarchy level 2)
	Tests []TestInfo `field:"8410"` // Test information (can occur multiple times)
}

// FeeInfo8202 represents fee-related information for sentence 8202 (hierarchy level 3)
type FeeInfo8202 struct {
	field_transfer.FieldAttr `field:"5001"` // Embedded FieldAttr for advanced usage
	FeeNumber                string         `field:"5001"` // Fee Number (GNR)
	CostInCent               string         `field:"8406"` // Cost in Cent
	ExaminationType          string         `field:"5002"` // Art der Untersuchung (Type of Examination)
	Multiplier               string         `field:"5005"` // Multiplier
	FreeJustificationText    string         `field:"5009"` // freier Begründungstext (Free Justification Text)
	BillingBy                string         `field:"8614"` // Billing By (1 = Lab, 2 = Referrer)
}

// TestInfo8202 represents test-related information for sentence 8202 (hierarchy level 2)
type TestInfo8202 struct {
	field_transfer.FieldAttr    `field:"8410"` // Embedded FieldAttr for advanced usage
	TestID                      string         `field:"8410"` // Test ID
	TestDescription             string         `field:"8411"` // Test Description
	TestStatus                  string         `field:"8418"` // Test Status
	SampleMaterialID            string         `field:"8428"` // Sample Material ID
	SampleMaterialIndex         string         `field:"8429"` // Sample Material Index
	SampleMaterialDescription   string         `field:"8430"` // Sample Material Description
	SampleMaterialSpecification string         `field:"8431"` // Sample Material Specification
	ResultValue                 string         `field:"8420"` // Result Value
	Unit                        string         `field:"8421"` // Unit
	ResultText                  []string       `field:"8480"` // Result Text (can occur multiple times)
	TestRelatedNotes            []string       `field:"8470"` // Test-related Notes (can occur multiple times)
	ReferenceValueText          []string       `field:"8460"` // Reference Value Text (can occur multiple times)
	ReferenceLowerLimit         string         `field:"8461"` // Reference Lower Limit
	ReferenceUpperLimit         string         `field:"8462"` // Reference Upper Limit
	ThresholdIndicator          string         `field:"8422"` // Threshold Indicator
	Fees                        []FeeInfo8202  `field:"5001"` // Fee information (can occur multiple times)
}

// Sentence8202 represents an LDT test result record with minimal patient information and comprehensive test/billing data
type Sentence8202 struct {
	// Record metadata (mandatory fields)
	field_transfer.FieldAttr `field:"8000"` // Embedded FieldAttr for advanced usage
	RecordType               string         `field:"8000"` // Record Type (8202)
	RecordLength             string         `field:"8100"` // Sum of all fields in the record (bytes)

	// Order information
	RequestID        string `field:"8310"` // Request ID
	LabOrderNumber   string `field:"8311"` // Lab Order Number ("Day number")
	OrderReceiptDate string `field:"8301"` // Order Receipt Date (Lab)
	ReportDate       string `field:"8302"` // Report Date
	ReportTime       string `field:"8303"` // Report Time

	// Patient information (minimal)
	DateOfBirth string `field:"3103"` // Date of Birth
	Gender      string `field:"3110"` // Gender

	// Billing and order details
	TypeOfFindings     string `field:"8401"` // Type of Findings
	BillingType        string `field:"8609"` // Billing Type
	Client             string `field:"8615"` // Client (LANR/Physician ID)
	FeeSchedule        string `field:"8403"` // Fee Schedule
	PatientInformation string `field:"8405"` // Patient Information
	OrderRelatedNotes  string `field:"8490"` // Order-related Notes

	// Test information (hierarchy level 2)
	Tests []TestInfo8202 `field:"8410"` // Test information (can occur multiple times)
}

// FeeInfo8204 represents fee-related information for sentence 8204 (hierarchy level 3)
type FeeInfo8204 struct {
	field_transfer.FieldAttr `field:"5001"` // Embedded FieldAttr for advanced usage
	FeeNumber                string         `field:"5001"` // Fee Number (GNR)
	CostInCent               string         `field:"8406"` // Cost in Cent
	Multiplier               string         `field:"5005"` // Multiplier
	BillingBy                string         `field:"8614"` // Billing By (1 = Lab, 2 = Referrer)
}

// RequestInfo8204 represents request-related information for sentence 8204 (hierarchy level 2)
type RequestInfo8204 struct {
	field_transfer.FieldAttr    `field:"8434"` // Embedded FieldAttr for advanced usage
	RequestID                   string         `field:"8434"` // Request ID
	TestStatus                  string         `field:"8418"` // Test Status
	SampleMaterialID            string         `field:"8428"` // Sample Material ID
	SampleMaterialIndex         string         `field:"8429"` // Sample Material Index
	SampleMaterialDescription   string         `field:"8430"` // Sample Material Description
	SampleMaterialSpecification []string       `field:"8431"` // Sample Material Specification (can occur multiple times)
	SamplingDate                string         `field:"8432"` // Sampling Date
	SamplingTime                string         `field:"8433"` // Sampling Time
	ResultValue                 string         `field:"8420"` // Result Value
	Unit                        string         `field:"8421"` // Unit
	ResultText                  []string       `field:"8480"` // Result Text (can occur multiple times)
	TestRelatedNotes            []string       `field:"8470"` // Test-related Notes (can occur multiple times)
	ReferenceValueText          []string       `field:"8460"` // Reference Value Text (can occur multiple times)
	ReferenceLowerLimit         string         `field:"8461"` // Reference Lower Limit
	ReferenceUpperLimit         string         `field:"8462"` // Reference Upper Limit
	ThresholdIndicator          string         `field:"8422"` // Threshold Indicator
	Fees                        []FeeInfo8204  `field:"5001"` // Fee information (can occur multiple times)
}

// TestInfo8204 represents test-related information for sentence 8204 (hierarchy level 2)
type TestInfo8204 struct {
	field_transfer.FieldAttr    `field:"8410"` // Embedded FieldAttr for advanced usage
	TestID                      string         `field:"8410"` // Test ID
	TestDescription             string         `field:"8411"` // Test Description
	TestStatus                  string         `field:"8418"` // Test Status
	SampleMaterialID            string         `field:"8428"` // Sample Material ID
	SampleMaterialIndex         string         `field:"8429"` // Sample Material Index
	SampleMaterialDescription   string         `field:"8430"` // Sample Material Description
	SampleMaterialSpecification []string       `field:"8431"` // Sample Material Specification (can occur multiple times)
	SamplingDate                string         `field:"8432"` // Sampling Date
	SamplingTime                string         `field:"8433"` // Sampling Time
	ResultValue                 string         `field:"8420"` // Result Value
	Unit                        string         `field:"8421"` // Unit
	ResultText                  []string       `field:"8480"` // Result Text (can occur multiple times)
	TestRelatedNotes            []string       `field:"8470"` // Test-related Notes (can occur multiple times)
	ReferenceValueText          []string       `field:"8460"` // Reference Value Text (can occur multiple times)
	ReferenceLowerLimit         string         `field:"8461"` // Reference Lower Limit
	ReferenceUpperLimit         string         `field:"8462"` // Reference Upper Limit
	ThresholdIndicator          string         `field:"8422"` // Threshold Indicator
	Fees                        []FeeInfo8204  `field:"5001"` // Fee information (can occur multiple times)
}

// Sentence8204 represents an LDT record with comprehensive patient information and both requests and tests
type Sentence8204 struct {
	// Record metadata (mandatory fields)
	field_transfer.FieldAttr `field:"8000"` // Embedded FieldAttr for advanced usage
	RecordType               string         `field:"8000"` // Record Type (8204)
	RecordLength             string         `field:"8100"` // Sum of all fields in the record (bytes)

	// Order information
	RequestID        string `field:"8310"` // Request ID
	LabOrderNumber   string `field:"8311"` // Lab Order Number ("Day number")
	OrderReceiptDate string `field:"8301"` // Order Receipt Date (Lab)
	ReportDate       string `field:"8302"` // Report Date
	ReportTime       string `field:"8303"` // Report Time

	// Patient information (complete)
	Suffix      string `field:"3100"` // Suffix
	Prefix      string `field:"3120"` // Prefix
	LastName    string `field:"3101"` // Last Name
	FirstName   string `field:"3102"` // First Name
	DateOfBirth string `field:"3103"` // Date of Birth
	Title       string `field:"3104"` // Title
	Gender      string `field:"3110"` // Gender

	// Billing and order details
	TypeOfFindings     string   `field:"8401"` // Type of Findings
	BillingType        string   `field:"8609"` // Billing Type
	Client             string   `field:"8615"` // Client (LANR/Physician ID)
	FeeSchedule        string   `field:"8403"` // Fee Schedule
	PatientInformation string   `field:"8405"` // Patient Information
	OrderRelatedNotes  []string `field:"8490"` // Order-related Notes (can occur multiple times)

	// Request and test information (hierarchy level 2)
	Requests []RequestInfo8204 `field:"8434"` // Request information (can occur multiple times)
	Tests    []TestInfo8204    `field:"8410"` // Test information (can occur multiple times)
}

// InitiatingPhysicianInfo8218 represents initiating physician information (hierarchy level 2)
type InitiatingPhysicianInfo8218 struct {
	field_transfer.FieldAttr `field:"4241"` // Embedded FieldAttr for advanced usage
	LANR                     string         `field:"4241"` // Lifetime physician number of the initiating physician
	PseudoLANR               string         `field:"4248"` // Pseudo-LANR for hospital physicians in ASV billing
}

// ReferringPhysicianInfo8218 represents referring physician information (hierarchy level 2)
type ReferringPhysicianInfo8218 struct {
	field_transfer.FieldAttr `field:"4242"` // Embedded FieldAttr for advanced usage
	LANR                     string         `field:"4242"` // Lifetime physician number of the referring physician
	PseudoLANR               string         `field:"4249"` // Pseudo-LANR for hospital physicians in ASV billing
}

// PregnancyInfo8218 represents pregnancy-related information (hierarchy level 2)
type PregnancyInfo8218 struct {
	field_transfer.FieldAttr `field:"8511"` // Embedded FieldAttr for advanced usage
	Duration                 string         `field:"8511"` // Duration of pregnancy
}

// AdditionalResultPathwayInfo8218 represents additional result pathway information (hierarchy level 2)
type AdditionalResultPathwayInfo8218 struct {
	field_transfer.FieldAttr `field:"8611"` // Embedded FieldAttr for advanced usage
	Pathway                  string         `field:"8611"` // Additional result pathway
	PhoneNumber              string         `field:"8612"` // Phone number (hierarchy level 3)
}

// SampleQuantityInfo8218 represents sample material quantity information (hierarchy level 3)
type SampleQuantityInfo8218 struct {
	field_transfer.FieldAttr `field:"8520"` // Embedded FieldAttr for advanced usage
	Quantity                 string         `field:"8520"` // Quantity of sample material
	Unit                     string         `field:"8521"` // Unit of measurement
	CollectionTime           string         `field:"8522"` // Collection time of sample material
}

// RequirementInfo8218 represents requirement information (hierarchy level 2)
type RequirementInfo8218 struct {
	field_transfer.FieldAttr    `field:"8434"`           // Embedded FieldAttr for advanced usage
	Requirement                 string                   `field:"8434"` // Requirement
	SampleMaterialID            string                   `field:"8428"` // Sample material ID
	SampleMaterialIndex         string                   `field:"8429"` // Sample material index
	SampleMaterialDesignation   string                   `field:"8430"` // Sample material designation
	SampleMaterialSpecification []string                 `field:"8431"` // Sample material specification (can occur multiple times)
	CollectionDate              string                   `field:"8432"` // Collection date
	CollectionTime              string                   `field:"8433"` // Collection time
	SampleQuantity              []SampleQuantityInfo8218 `field:"8520"` // Sample quantity information (can occur multiple times)
}

// TestInfo8218 represents test information (hierarchy level 2)
type TestInfo8218 struct {
	field_transfer.FieldAttr    `field:"8410"`           // Embedded FieldAttr for advanced usage
	TestID                      string                   `field:"8410"` // Test ID
	TestName                    string                   `field:"8411"` // Test name
	SampleMaterialID            string                   `field:"8428"` // Sample material ID
	SampleMaterialIndex         string                   `field:"8429"` // Sample material index
	SampleMaterialName          string                   `field:"8430"` // Sample material name
	SampleMaterialSpecification []string                 `field:"8431"` // Sample material specification (can occur multiple times)
	CollectionDate              string                   `field:"8432"` // Collection date
	CollectionTime              string                   `field:"8433"` // Collection time
	SampleQuantity              []SampleQuantityInfo8218 `field:"8520"` // Sample quantity information (can occur multiple times)
}

// Sentence8218 represents a comprehensive LDT patient and order record with complete medical and billing information
type Sentence8218 struct {
	// Record metadata
	field_transfer.FieldAttr `field:"8000"` // Embedded FieldAttr for advanced usage
	RecordType               string         `field:"8000"` // Record type (8218)
	RecordLength             string         `field:"8100"` // Sum of all field lengths of the sentence in bytes

	// Order information
	RequestIdentifier string `field:"8310"` // Request identifier
	FollowUpRequest   string `field:"8313"` // Follow-up request

	// Billing information
	BillingType           string `field:"8609"` // Billing type
	BillingResponsibility string `field:"8614"` // Billing responsibility
	OrderingEntity        string `field:"8615"` // Ordering entity (LANR/physician number)

	// Patient personal information
	NameSuffix      string `field:"3100"` // Name suffix
	Prefix          string `field:"3120"` // Prefix
	LastName        string `field:"3101"` // Last name
	FirstName       string `field:"3102"` // First name
	DateOfBirth     string `field:"3103"` // Date of birth
	Title           string `field:"3104"` // Title
	InsuranceNumber string `field:"3105"` // Insurance number
	InsuredPersonID string `field:"3119"` // Insured person ID
	Gender          string `field:"3110"` // Gender

	// Patient address information
	Street                    string `field:"3107"` // Street
	HouseNumber               string `field:"3109"` // House number
	ZIPCode                   string `field:"3112"` // ZIP code
	ResidenceCountryCode      string `field:"3114"` // Residence country code
	City                      string `field:"3113"` // City
	POBoxZIP                  string `field:"3121"` // P.O. box ZIP code
	POBoxCity                 string `field:"3122"` // P.O. box city
	POBox                     string `field:"3123"` // P.O. box
	POBoxResidenceCountryCode string `field:"3124"` // P.O. box residence country code
	WOP                       string `field:"3116"` // WOP

	// Insurance information
	InsuranceInstitution   string `field:"3108"` // Insurance institution
	PatientInformation     string `field:"8405"` // Patient information
	HealthInsuranceName    string `field:"2002"` // Health insurance name
	BillingVKNR            string `field:"4104"` // Billing VKNR
	CostUnitBillingArea    string `field:"4106"` // Cost unit billing area (KTAB)
	LastCardReadDay        string `field:"4109"` // Last read day of the insurance card in the quarter
	InsuranceCoverageStart string `field:"4133"` // Insurance coverage start date
	InsuranceCoverageEnd   string `field:"4110"` // Insurance coverage end date
	CostUnitIdentification string `field:"4111"` // Cost unit identification
	SpecialPopulationGroup string `field:"4131"` // Special population group
	DMPDesignation         string `field:"4132"` // DMP designation
	SKTAdditionalInfo      string `field:"4124"` // SKT additional information

	// Medical information
	FeeSchedule                 string   `field:"8403"` // Fee schedule
	AccidentConsequences        string   `field:"4202"` // Accident, accident consequences
	BillingRegion               string   `field:"4122"` // Billing region
	LimitedBenefitEntitlement   string   `field:"4204"` // Limited entitlement to benefits under § 16 (3a) SGB V
	Orders                      []string `field:"4205"` // Order (can occur multiple times)
	DiagnosisSuspectedDiagnosis []string `field:"4207"` // Diagnosis/suspected diagnosis (can occur multiple times)
	FindingMedication           []string `field:"4208"` // Finding/medication (can occur multiple times)

	// Physician information
	InitiatingPhysicianBSNR string                        `field:"4217"` // (N)BSNR of the initiating physician
	InitiatingPhysicianASV  string                        `field:"4225"` // ASV team number of the initiating physician
	InitiatingPhysician     []InitiatingPhysicianInfo8218 `field:"4241"` // Initiating physician details (hierarchy level 2)
	ReferringPhysicianBSNR  string                        `field:"4218"` // (N)BSNR of the referring physician
	ReferringPhysicianASV   string                        `field:"4226"` // ASV team number of the referring physician
	ReferringPhysician      []ReferringPhysicianInfo8218  `field:"4242"` // Referring physician details (hierarchy level 2)

	// Referral information
	ReferralFromOtherPhysicians string `field:"4219"` // Referral from other physicians
	ReferralTo                  string `field:"4220"` // Referral to
	SubgroupIdentifier          string `field:"4239"` // Subgroup identifier
	CurativePreventiveESS       string `field:"4221"` // Curative / Preventive / ESS / in hospital-based care
	ExceptionIndication         string `field:"4229"` // Exception indication
	FollowUpExamination         string `field:"4231"` // Follow-up examination of a known infection

	// Sample collection information
	CollectionDate string `field:"8432"` // Collection date
	CollectionTime string `field:"8433"` // Collection time

	// Private billing information
	PrivateTariff             string `field:"8610"` // Private tariff
	InvoiceRecipientName      string `field:"8601"` // Name of invoice recipient
	InvoiceRecipientTitleName string `field:"8602"` // Title, first name of invoice recipient
	InvoiceRecipientResidence string `field:"8606"` // Residence of invoice recipient
	InvoiceRecipientStreet    string `field:"8607"` // Street of invoice recipient
	CommentFileNumber         string `field:"8608"` // Comment/file number

	// Medical status information
	Infectious        string              `field:"8503"` // Infectious
	Pregnancy         string              `field:"8510"` // Pregnancy
	PregnancyDetails  []PregnancyInfo8218 `field:"8511"` // Pregnancy details (hierarchy level 2)
	MedicationIntake  []string            `field:"8504"` // Medication intake at time of sample collection (can occur multiple times)
	FirstDayLastCycle string              `field:"8512"` // First day of last cycle
	PatientHeight     string              `field:"3622"` // Patient height
	PatientWeight     string              `field:"3623"` // Patient weight
	UrgencyStatus     string              `field:"8501"` // Urgency status

	// Additional communication
	AdditionalResultPathways []AdditionalResultPathwayInfo8218 `field:"8611"` // Additional result pathways (hierarchy level 2)
	AdditionalRecipients     []string                          `field:"8613"` // Additional recipients (can occur multiple times)

	// Requirements and tests (hierarchy level 2)
	Requirements []RequirementInfo8218 `field:"8434"` // Requirements (can occur multiple times)
	Tests        []TestInfo8218        `field:"8410"` // Tests (can occur multiple times)
}

// =============================================================================
// Generic LDT Decoder
// =============================================================================
//
// This package provides a generic decoder for LDT (Laboratory Data Transfer) files
// that can automatically decode multi-record structures using reflection.
//
// Usage Examples:
//
//   // Option 1: Using the generic function directly
//   var order LDTOrder
//   err := DecodeLDTFile(reader, &order)
//
//   // Option 2: Using specific wrapper functions
//   order, err := DecodeLDTOrder(reader)
//   report, err := DecodeLDTReport(reader)
//   patientData, err := DecodePatientDataFile(reader)
//
// The generic decoder works with any struct that has fields of types matching the pattern "SentenceXXXX"
// where XXXX is a 4-digit record type (e.g., Sentence8230, Sentence8218, etc.)
//
// Supported struct patterns:
//   type MyLDTStruct struct {
//       Header  Sentence8230  // Will decode record type "8230"
//       Data    Sentence8218  // Will decode record type "8218"
//       Footer  Sentence8231  // Will decode record type "8231"
//   }

// DecodeLDTFile is a generic function that can decode any LDT file into a struct
// with multiple sentence fields. It uses reflection to inspect the target struct
// and automatically maps record types to the appropriate fields.
func DecodeLDTFile[T any](reader io.Reader, target *T) error {
	fieldReader := field_transfer.NewFieldReader(reader)

	// Read all fields from the LDT file
	err := fieldReader.ReadField()
	if err != nil {
		return fmt.Errorf("failed to read LDT fields: %w", err)
	}

	fields := fieldReader.GetFields()
	if len(fields) == 0 {
		return fmt.Errorf("no fields found in LDT file")
	}

	// Group fields by record type
	recordFields := groupFieldsByRecordType(fields)

	// Use reflection to decode each field in the target struct
	return decodeStructFields(recordFields, target)
}

// groupFieldsByRecordType groups fields by their record type (8000 field value)
func groupFieldsByRecordType(fields []field_transfer.Field) map[string][]field_transfer.Field {
	recordFields := make(map[string][]field_transfer.Field)
	currentRecordType := ""

	for _, field := range fields {
		if field.Name == "8000" {
			currentRecordType = field.Value
		}
		if currentRecordType != "" {
			recordFields[currentRecordType] = append(recordFields[currentRecordType], field)
		}
	}

	return recordFields
}

// groupFieldsByRecordInstances groups fields of the same record type into separate instances
// For example, if there are multiple 8202 records, this will separate them into individual groups
func groupFieldsByRecordInstances(fields []field_transfer.Field) [][]field_transfer.Field {
	var instances [][]field_transfer.Field
	var currentInstance []field_transfer.Field

	for _, field := range fields {
		// When we encounter a record type field (8000), it marks the start of a new instance
		if field.Name == "8000" {
			// If we have a current instance, add it to instances
			if len(currentInstance) > 0 {
				instances = append(instances, currentInstance)
			}
			// Start new instance
			currentInstance = []field_transfer.Field{field}
		} else {
			// Add field to current instance
			currentInstance = append(currentInstance, field)
		}
	}

	// Add the last instance if it exists
	if len(currentInstance) > 0 {
		instances = append(instances, currentInstance)
	}

	return instances
}

// decodeStructFields uses reflection to decode record fields into struct fields
func decodeStructFields(recordFields map[string][]field_transfer.Field, target any) error {
	targetValue := reflect.ValueOf(target)
	if targetValue.Kind() != reflect.Ptr || targetValue.Elem().Kind() != reflect.Struct {
		return fmt.Errorf("target must be a pointer to a struct")
	}

	targetStruct := targetValue.Elem()
	targetType := targetStruct.Type()

	// Iterate through all fields in the target struct
	for i := range targetStruct.NumField() {
		field := targetStruct.Field(i)
		fieldType := targetType.Field(i)

		var recordType string
		var isSlice bool

		// Handle slice fields (e.g., []Sentence8202)
		if fieldType.Type.Kind() == reflect.Slice {
			elemType := fieldType.Type.Elem()
			recordType = extractRecordTypeFromTypeName(elemType.Name())
			isSlice = true
		} else {
			// Handle single struct fields (e.g., Sentence8230)
			recordType = extractRecordTypeFromTypeName(fieldType.Type.Name())
			isSlice = false
		}

		if recordType == "" {
			continue // Skip fields that don't match the pattern
		}

		// Check if we have fields for this record type
		if recordFieldsForType, exists := recordFields[recordType]; exists {
			if isSlice {
				// Handle slice of structs - group by record instances
				sliceInstances := groupFieldsByRecordInstances(recordFieldsForType)

				// Create slice value
				sliceValue := reflect.MakeSlice(fieldType.Type, 0, len(sliceInstances))

				for _, instanceFields := range sliceInstances {
					// Create new element
					elemValue := reflect.New(fieldType.Type.Elem()).Elem()
					elemPtr := elemValue.Addr().Interface()

					if err := decodeFieldsWithFieldTransfer(instanceFields, elemPtr); err != nil {
						return fmt.Errorf("failed to decode slice element %s (record type %s): %w", fieldType.Name, recordType, err)
					}

					sliceValue = reflect.Append(sliceValue, elemValue)
				}

				field.Set(sliceValue)
			} else {
				// Handle single struct
				fieldPtr := field.Addr().Interface()

				if err := decodeFieldsWithFieldTransfer(recordFieldsForType, fieldPtr); err != nil {
					return fmt.Errorf("failed to decode %s (record type %s): %w", fieldType.Name, recordType, err)
				}
			}
		}
	}

	return nil
}

// extractRecordTypeFromTypeName extracts the record type from type names like "Sentence8230"
func extractRecordTypeFromTypeName(typeName string) string {
	// Look for pattern like "Sentence8230" and extract "8230"
	if strings.HasPrefix(typeName, "Sentence") && len(typeName) >= 12 {
		recordType := typeName[8:] // Remove "Sentence" prefix
		// Validate that it's a 4-digit number
		if len(recordType) == 4 {
			for _, r := range recordType {
				if r < '0' || r > '9' {
					return ""
				}
			}
			return recordType
		}
	}
	return ""
}

func MapScheinDetailToLDT(scheinDetail *schein_common.ScheinDetail) *Sentence8218 {
	if scheinDetail == nil {
		return nil
	}

	ldt := &Sentence8218{
		ReferringPhysicianBSNR: util.GetValueFromStringPointer(scheinDetail.Re4218),
		ReferringPhysicianASV:  util.GetValueFromStringPointer(scheinDetail.Re4226),
		ReferringPhysician: []ReferringPhysicianInfo8218{
			{
				LANR:       util.GetValueFromStringPointer(scheinDetail.Re4242),
				PseudoLANR: util.GetValueFromStringPointer(scheinDetail.Re4249),
			},
		},
		ReferralFromOtherPhysicians: util.GetValueFromStringPointer(scheinDetail.Re4219),
		ReferralTo:                  util.GetValueFromStringPointer(scheinDetail.Re4220),
		CurativePreventiveESS:       util.GetValueFromStringPointer(scheinDetail.Re4221),
		ExceptionIndication:         util.GetValueFromStringPointer(scheinDetail.Re4229),
		InitiatingPhysicianBSNR:     util.GetValueFromStringPointer(scheinDetail.Re4217),
		InitiatingPhysicianASV:      util.GetValueFromStringPointer(scheinDetail.Re4225),
		InitiatingPhysician: []InitiatingPhysicianInfo8218{
			{
				LANR:       util.GetValueFromStringPointer(scheinDetail.Re4241),
				PseudoLANR: util.GetValueFromStringPointer(scheinDetail.Re4248),
			},
		},

		BillingRegion:               util.GetValueFromStringPointer(scheinDetail.G4122),
		CostUnitBillingArea:         util.GetValueFromStringPointer(scheinDetail.G4106),
		BillingVKNR:                 util.NumberToString(scheinDetail.G4104),
		LimitedBenefitEntitlement:   util.NumberToString(scheinDetail.Ad4204),
		AccidentConsequences:        util.NumberToString(scheinDetail.Ad4202),
		SKTAdditionalInfo:           util.GetValueFromStringPointer(scheinDetail.Ad4124),
		Orders:                      []string{util.GetValueFromStringPointer(scheinDetail.Re4205)},
		DiagnosisSuspectedDiagnosis: []string{util.GetValueFromStringPointer(scheinDetail.Re4207)},
		FindingMedication:           []string{util.GetValueFromStringPointer(scheinDetail.Re4208)},
	}

	return ldt
}

// decodeFieldsWithFieldTransfer creates a mock field data and uses field_transfer.Decode
func decodeFieldsWithFieldTransfer(fields []field_transfer.Field, target any) error {
	// Create a string representation of the fields in the format expected by FieldReader
	var lines []string
	for _, field := range fields {
		// Format: lengthFieldNameValue (e.g., 01380008230)
		line := fmt.Sprintf("%03d%s%s", len(field.Name)+len(field.Value)+3, field.Name, field.Value)
		lines = append(lines, line)
	}

	// Create a reader from the formatted lines
	data := strings.Join(lines, "\n")
	reader := strings.NewReader(data)

	// Create a new FieldReader and decode
	fieldReader := field_transfer.NewFieldReader(reader)
	if err := fieldReader.ReadField(); err != nil {
		return fmt.Errorf("failed to read fields: %w", err)
	}

	return fieldReader.Decode(target)
}
