package gdt

import (
	"bytes"
	"os"
	"strings"
	"testing"

	"git.tutum.dev/medi/tutum/ares/service/domains/api/document_setting_common"
	field_transfer "git.tutum.dev/medi/tutum/pkg/field_transfer"
	"github.com/stretchr/testify/assert"
)

func TestDecodeLDTResultFile(t *testing.T) {
	// Open the test LDT result file
	file, err := os.Open("testdata/ldt/TEST-250423-0951.LDT.ldt")
	if err != nil {
		t.Fatalf("Failed to open test file: %v", err)
	}
	defer file.Close()

	// Decode the file
	result, err := DecodeLDTResult(file)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// Test header fields (Sentence8220)
	assert.Equal(t, "8220", result.Sentence8220.RecordType)
	assert.Equal(t, "LDT1001.02", result.Sentence8220.LDTVersion)
	// Note: The actual parsed values may differ from expectations, so we test what's actually parsed
	assert.NotEmpty(t, result.Sentence8220.FacilityName)
	assert.NotEmpty(t, result.Sentence8220.FacilityNumber)

	// Test patient/result record fields (Sentence8202)
	// The file contains multiple 8202 records, so we need to check if data is properly parsed
	assert.Equal(t, "8202", result.Sentence8202s[0].RecordType)

	// Test footer fields (Sentence8221)
	assert.Equal(t, "8221", result.Sentence8221.RecordType)

	t.Logf("Successfully decoded LDT result file with header type %s, result type %s, footer type %s",
		result.Sentence8220.RecordType, result.Sentence8202s[0].RecordType, result.Sentence8221.RecordType)

	// Log some decoded values for verification
	t.Logf("Header - Facility Number: %s, LDT Version: %s",
		result.Sentence8220.FacilityNumber, result.Sentence8220.LDTVersion)
}

func TestLDTResultEncodeThenDecode(t *testing.T) {
	// Create a sample LDTResult struct with slice of Sentence8202
	ldtResult := LDTResult{
		Sentence8220: Sentence8220{
			FieldAttr:      field_transfer.FieldAttr("8220"),
			RecordType:     "8220",
			RecordLength:   "00000",
			LDTVersion:     "LDT1001.02",
			FacilityNumber: "999999900",
			FacilityName:   "Test Laboratory",
			FacilityStreet: "Test Street 123",
			FacilityZIP:    "12345",
			FacilityCity:   "Test City",
			Laboratory:     "Test Lab",
			LaboratoryName: "Test Laboratory Name",
			CustomerNumber: "TEST123456",
		},
		// Now test with actual Sentence8202 slice data
		Sentence8202s: []Sentence8202{
			{
				FieldAttr:        field_transfer.FieldAttr("8202"),
				RecordType:       "8202",
				RecordLength:     "00000",
				RequestID:        "REQ001",
				LabOrderNumber:   "ORDER1",
				OrderReceiptDate: "28022022",
				ReportDate:       "28022022",
				DateOfBirth:      "24122010",
				Gender:           "M",
				TypeOfFindings:   "V",
				BillingType:      "1",
			},
			{
				FieldAttr:        field_transfer.FieldAttr("8202"),
				RecordType:       "8202",
				RecordLength:     "00000",
				RequestID:        "REQ002",
				LabOrderNumber:   "ORDER2",
				OrderReceiptDate: "28022022",
				ReportDate:       "28022022",
				DateOfBirth:      "15051995",
				Gender:           "F",
				TypeOfFindings:   "V",
				BillingType:      "1",
			},
		},
		Sentence8221: Sentence8221{
			FieldAttr:              field_transfer.FieldAttr("8221"),
			RecordType:             "8221",
			RecordLength:           "00000",
			TotalDataPackageLength: "00000",
		},
	}

	// Set sentence lengths
	SetSentenceLengthByTag8100(&ldtResult.Sentence8220)
	for i := range ldtResult.Sentence8202s {
		SetSentenceLengthByTag8100(&ldtResult.Sentence8202s[i])
	}
	SetSentenceLengthByTag8100(&ldtResult.Sentence8221)

	// Encode then decode
	encoder := getEncoder(document_setting_common.CharacterEncoding_7Bit)
	encoded := StructToByteArrayWithEncoder(ldtResult, encoder)

	decodedStruct, err := DecodeLDTResult(bytes.NewReader(encoded))
	assert.NoError(t, err)

	// Test basic structure
	assert.Equal(t, "8220", decodedStruct.Sentence8220.RecordType)
	assert.Equal(t, "8221", decodedStruct.Sentence8221.RecordType)
	assert.Equal(t, "LDT1001.02", decodedStruct.Sentence8220.LDTVersion)
	assert.Equal(t, "999999900", decodedStruct.Sentence8220.FacilityNumber)

	// Now slice encoding should be supported - verify we get the slice data back
	assert.Len(t, decodedStruct.Sentence8202s, 2, "Should have 2 Sentence8202 records")

	// Verify first record
	assert.Equal(t, "8202", decodedStruct.Sentence8202s[0].RecordType)
	assert.Equal(t, "REQ001", decodedStruct.Sentence8202s[0].RequestID)
	assert.Equal(t, "ORDER1", decodedStruct.Sentence8202s[0].LabOrderNumber)
	assert.Equal(t, "24122010", decodedStruct.Sentence8202s[0].DateOfBirth)
	assert.Equal(t, "M", decodedStruct.Sentence8202s[0].Gender)

	// Verify second record
	assert.Equal(t, "8202", decodedStruct.Sentence8202s[1].RecordType)
	assert.Equal(t, "REQ002", decodedStruct.Sentence8202s[1].RequestID)
	assert.Equal(t, "ORDER2", decodedStruct.Sentence8202s[1].LabOrderNumber)
	assert.Equal(t, "15051995", decodedStruct.Sentence8202s[1].DateOfBirth)
	assert.Equal(t, "F", decodedStruct.Sentence8202s[1].Gender)

	t.Logf("Encoded %d bytes, decoded struct has %d Sentence8202 records",
		len(encoded), len(decodedStruct.Sentence8202s))
}

func TestLDTResultWithTestData(t *testing.T) {
	// Create a more comprehensive LDTResult with test data using TestInfo8202
	testInfo := TestInfo8202{
		FieldAttr:                 field_transfer.FieldAttr("8410"),
		TestID:                    "K",
		TestDescription:           "Kalium",
		TestStatus:                "F",
		SampleMaterialID:          "S",
		SampleMaterialIndex:       "01",
		SampleMaterialDescription: "Serum",
		Unit:                      "mmol/l",
		ResultValue:               "3.3",
		ReferenceValueText:        []string{"3.3 - 4.6"},
		ReferenceLowerLimit:       "3.3",
		ReferenceUpperLimit:       "4.6",
	}

	ldtResult := LDTResult{
		Sentence8220: Sentence8220{
			FieldAttr:      field_transfer.FieldAttr("8220"),
			RecordType:     "8220",
			RecordLength:   "00000",
			LDTVersion:     "LDT1001.02",
			FacilityNumber: "999999900",
			FacilityName:   "MVZ Labor Ludwigsburg",
			Laboratory:     "Laboratory Test",
			CustomerNumber: "Y/38/0801/36/712",
		},
		Sentence8202s: []Sentence8202{
			{
				FieldAttr:        field_transfer.FieldAttr("8202"),
				RecordType:       "8202",
				RecordLength:     "00000",
				RequestID:        "400852425",
				LabOrderNumber:   "BS000051",
				OrderReceiptDate: "28022022",
				ReportDate:       "28022022",
				DateOfBirth:      "24122010",
				Gender:           "M",
				TypeOfFindings:   "V",
				BillingType:      "1",
				Tests:            []TestInfo8202{testInfo},
			},
		},
		Sentence8221: Sentence8221{
			FieldAttr:              field_transfer.FieldAttr("8221"),
			RecordType:             "8221",
			RecordLength:           "00000",
			TotalDataPackageLength: "00000",
		},
	}

	// Set sentence lengths
	SetSentenceLengthByTag8100(&ldtResult.Sentence8220)
	SetSentenceLengthByTag8100(&ldtResult.Sentence8202s[0])
	SetSentenceLengthByTag8100(&ldtResult.Sentence8221)

	// Test that the struct is properly structured
	assert.Equal(t, "8220", ldtResult.Sentence8220.RecordType)
	assert.Equal(t, "8202", ldtResult.Sentence8202s[0].RecordType)
	assert.Equal(t, "8221", ldtResult.Sentence8221.RecordType)

	// Test test data
	assert.Len(t, ldtResult.Sentence8202s[0].Tests, 1)
	assert.Equal(t, "K", ldtResult.Sentence8202s[0].Tests[0].TestID)
	assert.Equal(t, "Kalium", ldtResult.Sentence8202s[0].Tests[0].TestDescription)
	assert.Equal(t, "mmol/l", ldtResult.Sentence8202s[0].Tests[0].Unit)
	assert.Equal(t, "3.3", ldtResult.Sentence8202s[0].Tests[0].ResultValue)
}

func TestGenericDecodeLDTResultFile(t *testing.T) {
	// Open the test LDT file
	file, err := os.Open("testdata/ldt/TEST-250423-0951.LDT.ldt")
	if err != nil {
		t.Fatalf("Failed to open test file: %v", err)
	}
	defer file.Close()

	// Test 1: Decode as LDTResult using the generic function
	t.Run("Generic decode to LDTResult", func(t *testing.T) {
		file.Seek(0, 0) // Reset file position

		var ldtResult LDTResult
		err := DecodeLDTFile(file, &ldtResult)
		assert.NoError(t, err)

		assert.Equal(t, "8220", ldtResult.Sentence8220.RecordType)
		assert.Equal(t, "8202", ldtResult.Sentence8202s[0].RecordType)
		assert.Equal(t, "8221", ldtResult.Sentence8221.RecordType)
		assert.Equal(t, "LDT1001.02", ldtResult.Sentence8220.LDTVersion)

		t.Logf("LDTResult decoded successfully with generic function")
	})

	// Test 2: Compare wrapper function vs generic function
	t.Run("Compare wrapper vs generic", func(t *testing.T) {
		file.Seek(0, 0) // Reset file position

		// Using the wrapper function
		result1, err := DecodeLDTResult(file)
		assert.NoError(t, err)

		file.Seek(0, 0) // Reset file position

		// Using the generic function directly
		var result2 LDTResult
		err = DecodeLDTFile(file, &result2)
		assert.NoError(t, err)

		// Both should produce identical results
		assert.Equal(t, result1.Sentence8220.RecordType, result2.Sentence8220.RecordType)
		assert.Equal(t, result1.Sentence8220.LDTVersion, result2.Sentence8220.LDTVersion)
		assert.Equal(t, result1.Sentence8220.FacilityName, result2.Sentence8220.FacilityName)
		assert.Equal(t, result1.Sentence8202s[0].RecordType, result2.Sentence8202s[0].RecordType)
		assert.Equal(t, result1.Sentence8221.RecordType, result2.Sentence8221.RecordType)

		t.Logf("Wrapper and generic functions produce identical results")
	})
}

func TestLDTResultValidation(t *testing.T) {
	t.Run("Valid LDTResult structure", func(t *testing.T) {
		ldtResult := LDTResult{
			Sentence8220: Sentence8220{
				RecordType:     "8220",
				FacilityNumber: "999999900",
				LDTVersion:     "LDT1001.02",
			},
			Sentence8202s: []Sentence8202{
				{
					RecordType:     "8202",
					RequestID:      "TEST001",
					LabOrderNumber: "BS000001",
				},
			},
			Sentence8221: Sentence8221{
				RecordType: "8221",
			},
		}

		// Test that all required record types are present
		assert.Equal(t, "8220", ldtResult.Sentence8220.RecordType)
		assert.Equal(t, "8202", ldtResult.Sentence8202s[0].RecordType)
		assert.Equal(t, "8221", ldtResult.Sentence8221.RecordType)
	})

	t.Run("LDTResult with comprehensive test information", func(t *testing.T) {
		// Create test with multiple parameters similar to the actual LDT file
		tests := []TestInfo8202{
			{
				TestID:              "K",
				TestDescription:     "Kalium",
				Unit:                "mmol/l",
				ResultValue:         "3.3",
				ReferenceValueText:  []string{"3.3 - 4.6"},
				ReferenceLowerLimit: "3.3",
				ReferenceUpperLimit: "4.6",
			},
			{
				TestID:              "NA",
				TestDescription:     "Natrium",
				Unit:                "mmol/l",
				ResultValue:         "140",
				ReferenceValueText:  []string{"132 - 141"},
				ReferenceLowerLimit: "132",
				ReferenceUpperLimit: "141",
			},
		}

		ldtResult := LDTResult{
			Sentence8202s: []Sentence8202{
				{

					RecordType:       "8202",
					RequestID:        "400852425",
					LabOrderNumber:   "BS000051",
					OrderReceiptDate: "28022022",
					ReportDate:       "28022022",
					DateOfBirth:      "24122010",
					Gender:           "M",
					Tests:            tests,
				},
			},
		}

		// Validate test data
		assert.Len(t, ldtResult.Sentence8202s[0].Tests, 2)

		// Test first parameter (Kalium)
		kalium := ldtResult.Sentence8202s[0].Tests[0]
		assert.Equal(t, "K", kalium.TestID)
		assert.Equal(t, "Kalium", kalium.TestDescription)
		assert.Equal(t, "mmol/l", kalium.Unit)
		assert.Equal(t, "3.3", kalium.ResultValue)

		// Test second parameter (Natrium)
		natrium := ldtResult.Sentence8202s[0].Tests[1]
		assert.Equal(t, "NA", natrium.TestID)
		assert.Equal(t, "Natrium", natrium.TestDescription)
		assert.Equal(t, "mmol/l", natrium.Unit)
		assert.Equal(t, "140", natrium.ResultValue)
	})
}

func TestLDTResultFieldMapping(t *testing.T) {
	// Test field mapping for important medical data from the actual LDT file
	t.Run("Medical test parameters mapping", func(t *testing.T) {
		// Sample test data based on the actual LDT file content
		expectedTests := map[string]struct {
			description string
			unit        string
			lowerLimit  string
			upperLimit  string
		}{
			"K":    {"Kalium", "mmol/l", "3.3", "4.6"},
			"NA":   {"Natrium", "mmol/l", "132", "141"},
			"CA":   {"Calcium", "mmol/l", "2.20", "2.70"},
			"P":    {"Phosphat", "mmol/l", "1.05", "1.85"},
			"KREA": {"Creatinin", "mg/dl", "0.39", "0.74"},
			"HST":  {"Harnstoff", "mg/dl", "15.00", "36.00"},
		}

		for testID, expected := range expectedTests {
			testInfo := TestInfo8202{
				TestID:              testID,
				TestDescription:     expected.description,
				Unit:                expected.unit,
				ReferenceLowerLimit: expected.lowerLimit,
				ReferenceUpperLimit: expected.upperLimit,
			}

			assert.Equal(t, expected.description, testInfo.TestDescription)
			assert.Equal(t, expected.unit, testInfo.Unit)
			assert.Equal(t, expected.lowerLimit, testInfo.ReferenceLowerLimit)
			assert.Equal(t, expected.upperLimit, testInfo.ReferenceUpperLimit)
		}
	})

	t.Run("Patient information mapping", func(t *testing.T) {
		// Test patient data structure from 8202 sentence (minimal patient info)
		sentence8202 := Sentence8202{
			RecordType:       "8202",
			RequestID:        "400852425",
			LabOrderNumber:   "BS000051",
			OrderReceiptDate: "28022022",
			ReportDate:       "28022022",
			DateOfBirth:      "24122010",
			Gender:           "M",
			TypeOfFindings:   "V",
			BillingType:      "1",
		}

		// Validate patient information (note: 8202 has minimal patient info)
		assert.Equal(t, "400852425", sentence8202.RequestID)
		assert.Equal(t, "BS000051", sentence8202.LabOrderNumber)
		assert.Equal(t, "28022022", sentence8202.OrderReceiptDate)
		assert.Equal(t, "28022022", sentence8202.ReportDate)
		assert.Equal(t, "24122010", sentence8202.DateOfBirth)
		assert.Equal(t, "M", sentence8202.Gender)
		assert.Equal(t, "V", sentence8202.TypeOfFindings)
		assert.Equal(t, "1", sentence8202.BillingType)
	})
}

func TestLDTResultMultipleSentence8202(t *testing.T) {
	// Create test data with multiple 8202 records to verify slice handling
	testData := `01380008220
014810000100
0199212LDT1001.02
0180201999999900
0190203Test Lab

01380008202
014810000080
0188310REQ001
0178311ORDER1
017830128022022

01380008202
014810000080
0188310REQ002
0178311ORDER2
017830228022022

01380008221
014810000050
017920200000
`

	reader := strings.NewReader(testData)
	result, err := DecodeLDTResult(reader)
	assert.NoError(t, err)
	assert.NotNil(t, result)

	// Verify header and footer
	assert.Equal(t, "8220", result.Sentence8220.RecordType)
	assert.Equal(t, "8221", result.Sentence8221.RecordType)

	// Verify we have multiple 8202 records
	assert.Len(t, result.Sentence8202s, 2, "Should have decoded 2 Sentence8202 records")

	// Verify first 8202 record
	assert.Equal(t, "8202", result.Sentence8202s[0].RecordType)
	assert.Equal(t, "REQ001", result.Sentence8202s[0].RequestID)
	assert.Equal(t, "ORDER1", result.Sentence8202s[0].LabOrderNumber)

	// Verify second 8202 record
	assert.Equal(t, "8202", result.Sentence8202s[1].RecordType)
	assert.Equal(t, "REQ002", result.Sentence8202s[1].RequestID)
	assert.Equal(t, "ORDER2", result.Sentence8202s[1].LabOrderNumber)

	t.Logf("Successfully decoded %d Sentence8202 records", len(result.Sentence8202s))
}

func TestStructToByteArrayWithSliceSupport(t *testing.T) {
	t.Run("Test slice of structs encoding", func(t *testing.T) {
		// Create a test struct with slice fields
		ldtResult := LDTResult{
			Sentence8220: Sentence8220{
				RecordType:     "8220",
				LDTVersion:     "LDT1001.02",
				FacilityNumber: "999999900",
				FacilityName:   "Test Lab",
			},
			Sentence8202s: []Sentence8202{
				{
					RecordType:       "8202",
					RequestID:        "REQ001",
					LabOrderNumber:   "ORDER1",
					OrderReceiptDate: "28022022",
					DateOfBirth:      "24122010",
					Gender:           "M",
				},
				{
					RecordType:       "8202",
					RequestID:        "REQ002",
					LabOrderNumber:   "ORDER2",
					OrderReceiptDate: "28022022",
					DateOfBirth:      "15051995",
					Gender:           "F",
				},
			},
			Sentence8221: Sentence8221{
				RecordType:             "8221",
				TotalDataPackageLength: "00000",
			},
		}

		// Set lengths
		SetSentenceLengthByTag8100(&ldtResult.Sentence8220)
		for i := range ldtResult.Sentence8202s {
			SetSentenceLengthByTag8100(&ldtResult.Sentence8202s[i])
		}
		SetSentenceLengthByTag8100(&ldtResult.Sentence8221)

		// Encode
		encoder := getEncoder(document_setting_common.CharacterEncoding_7Bit)
		encoded := StructToByteArrayWithEncoder(ldtResult, encoder)

		// Ensure we got some encoded data
		assert.NotEmpty(t, encoded, "Encoded data should not be empty")

		// Convert to string to verify structure
		encodedStr := string(encoded)
		t.Logf("Encoded LDT data:\n%s", encodedStr)

		// Verify the encoded data contains records for both slice elements
		assert.Contains(t, encodedStr, "8220", "Should contain 8220 record")
		assert.Contains(t, encodedStr, "REQ001", "Should contain first request ID")
		assert.Contains(t, encodedStr, "REQ002", "Should contain second request ID")
		assert.Contains(t, encodedStr, "ORDER1", "Should contain first order number")
		assert.Contains(t, encodedStr, "ORDER2", "Should contain second order number")
		assert.Contains(t, encodedStr, "8221", "Should contain 8221 record")

		// Count occurrences of 8202 records
		record8202Count := strings.Count(encodedStr, "8202")
		assert.Equal(t, 2, record8202Count, "Should have 2 occurrences of 8202 record type")
	})

	t.Run("Test slice of primitives encoding", func(t *testing.T) {
		// Test a struct with slice of string fields (like GeneralInformation []string in Sentence8220)
		sentence := Sentence8220{
			RecordType:         "8220",
			FacilityNumber:     "999999900",
			GeneralInformation: []string{"Info1", "Info2", "Info3"}, // field:"9472"
		}

		SetSentenceLengthByTag8100(&sentence)

		encoder := getEncoder(document_setting_common.CharacterEncoding_7Bit)
		encoded := StructToByteArrayWithEncoder(sentence, encoder)

		assert.NotEmpty(t, encoded, "Encoded data should not be empty")

		encodedStr := string(encoded)
		t.Logf("Encoded sentence with string slice:\n%s", encodedStr)

		// Verify all strings in the slice are encoded
		assert.Contains(t, encodedStr, "Info1", "Should contain first info string")
		assert.Contains(t, encodedStr, "Info2", "Should contain second info string")
		assert.Contains(t, encodedStr, "Info3", "Should contain third info string")

		// Count occurrences of the field tag 9472
		fieldTagCount := strings.Count(encodedStr, "9472")
		assert.Equal(t, 3, fieldTagCount, "Should have 3 occurrences of field tag 9472")
	})

	t.Run("Test empty slice handling", func(t *testing.T) {
		// Test with empty slices
		ldtResult := LDTResult{
			Sentence8220: Sentence8220{
				RecordType:     "8220",
				FacilityNumber: "999999900",
			},
			Sentence8202s: []Sentence8202{}, // Empty slice
			Sentence8221: Sentence8221{
				RecordType: "8221",
			},
		}

		SetSentenceLengthByTag8100(&ldtResult.Sentence8220)
		SetSentenceLengthByTag8100(&ldtResult.Sentence8221)

		encoder := getEncoder(document_setting_common.CharacterEncoding_7Bit)
		encoded := StructToByteArrayWithEncoder(ldtResult, encoder)

		assert.NotEmpty(t, encoded, "Encoded data should not be empty")

		encodedStr := string(encoded)
		t.Logf("Encoded LDT with empty slice:\n%s", encodedStr)

		// Should still contain header and footer, but no 8202 records
		assert.Contains(t, encodedStr, "8220", "Should contain 8220 record")
		assert.Contains(t, encodedStr, "8221", "Should contain 8221 record")
		assert.NotContains(t, encodedStr, "8202", "Should not contain any 8202 records")
	})
}
