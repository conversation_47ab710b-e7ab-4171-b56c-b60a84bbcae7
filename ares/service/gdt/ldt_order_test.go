package gdt

import (
	"bytes"
	"os"
	"strings"
	"testing"

	"git.tutum.dev/medi/tutum/ares/service/domains/api/document_setting_common"
	field_transfer "git.tutum.dev/medi/tutum/pkg/field_transfer"
	"github.com/stretchr/testify/assert"
)

func TestDecodeLDTOrderFile(t *testing.T) {
	// Open the test LDT order file
	file, err := os.Open("testdata/ldt/L01D250404A1464_AT_FA0.LDT.ldt")
	if err != nil {
		t.Fatalf("Failed to open test file: %v", err)
	}
	defer file.Close()

	// Decode the file
	order, err := DecodeLDTOrder(file)
	assert.NoError(t, err)
	assert.NotNil(t, order)

	// Test header fields (Sentence8230)
	assert.Equal(t, "8230", order.Sentence8230.RecordType)
	assert.Equal(t, "LDT1014.01", order.Sentence8230.LDTVersion)
	assert.NotEmpty(t, order.Sentence8230.CustomerNumber)

	// Test patient/order record fields (Sentence8218)
	// The file contains at least one 8218 record, so we need to check if data is properly parsed
	assert.Len(t, order.Sentence8218s, 1, "Should have at least one Sentence8218 record")
	assert.Equal(t, "8218", order.Sentence8218s[0].RecordType)

	// Test footer fields (Sentence8231)
	assert.Equal(t, "8231", order.Sentence8231.RecordType)

	t.Logf("Successfully decoded LDT order file with header type %s, order type %s, footer type %s",
		order.Sentence8230.RecordType, order.Sentence8218s[0].RecordType, order.Sentence8231.RecordType)

	// Log some decoded values for verification
	t.Logf("Header - Customer Number: %s, LDT Version: %s",
		order.Sentence8230.CustomerNumber, order.Sentence8230.LDTVersion)
	t.Logf("Order - Billing Type: %s, Patient Last Name: %s",
		order.Sentence8218s[0].BillingType, order.Sentence8218s[0].LastName)
}

func TestLDTOrderEncodeThenDecode(t *testing.T) {
	// Create a sample LDTOrder struct with slice of Sentence8218
	ldtOrder := LDTOrder{
		Sentence8230: Sentence8230{
			FieldAttr:      field_transfer.FieldAttr("8230"),
			RecordType:     "8230",
			RecordLength:   "00000",
			LDTVersion:     "LDT1014.01",
			FacilityNumber: "011234567",
			FacilityZIP:    "zip",
			PhysicianID:    []string{"123", "456"},
			PhysicianName:  []string{"Doctor", "Doctor2"},
			CharacterSet:   "hahhaha",
			CreationDate:   "Create",
			FacilityName:   "Test Laboratory",
		},
		// Test with actual Sentence8218 slice data
		Sentence8218s: []Sentence8218{
			{
				FieldAttr:             field_transfer.FieldAttr("8218"),
				RecordType:            "8218",
				RecordLength:          "00000",
				RequestIdentifier:     "000014640",
				BillingType:           "K",
				LastName:              "Test",
				FirstName:             "Patient",
				DateOfBirth:           "19970514",
				Gender:                "M",
				InsuranceNumber:       "123456601",
				OrderingEntity:        "011234567",
				BillingResponsibility: "042025",
			},
			{
				FieldAttr:             field_transfer.FieldAttr("8218"),
				RecordType:            "8218",
				RecordLength:          "00000",
				RequestIdentifier:     "000014641",
				BillingType:           "K",
				LastName:              "Another",
				FirstName:             "Patient",
				DateOfBirth:           "19850315",
				Gender:                "F",
				InsuranceNumber:       "987654321",
				OrderingEntity:        "011234567",
				BillingResponsibility: "042025",
			},
		},
		Sentence8231: Sentence8231{
			FieldAttr:              field_transfer.FieldAttr("8231"),
			RecordType:             "8231",
			RecordLength:           "00000",
			TotalDataPackageLength: "00000",
		},
	}

	// Set sentence lengths
	SetSentenceLengthByTag8100(&ldtOrder.Sentence8230)
	for i := range ldtOrder.Sentence8218s {
		SetSentenceLengthByTag8100(&ldtOrder.Sentence8218s[i])
	}
	SetSentenceLengthByTag8100(&ldtOrder.Sentence8231)

	// Encode then decode
	encoder := getEncoder(document_setting_common.CharacterEncoding_7Bit)
	encoded := StructToByteArrayWithEncoder(ldtOrder, encoder)

	decodedStruct, err := DecodeLDTOrder(bytes.NewReader(encoded))
	assert.NoError(t, err)

	// Test basic structure
	assert.Equal(t, "8230", decodedStruct.Sentence8230.RecordType)
	assert.Equal(t, "8231", decodedStruct.Sentence8231.RecordType)
	assert.Equal(t, "LDT1014.01", decodedStruct.Sentence8230.LDTVersion)
	assert.Equal(t, "011234567", decodedStruct.Sentence8230.FacilityNumber)
	assert.Equal(t, "00186", decodedStruct.Sentence8230.RecordLength)

	// Verify we get the slice data back
	assert.Len(t, decodedStruct.Sentence8218s, 2, "Should have 2 Sentence8218 records")

	// Verify first record
	assert.Equal(t, "8218", decodedStruct.Sentence8218s[0].RecordType)
	assert.Equal(t, "000014640", decodedStruct.Sentence8218s[0].RequestIdentifier)
	assert.Equal(t, "K", decodedStruct.Sentence8218s[0].BillingType)
	assert.Equal(t, "Test", decodedStruct.Sentence8218s[0].LastName)
	assert.Equal(t, "Patient", decodedStruct.Sentence8218s[0].FirstName)
	assert.Equal(t, "19970514", decodedStruct.Sentence8218s[0].DateOfBirth)
	assert.Equal(t, "M", decodedStruct.Sentence8218s[0].Gender)
	assert.Equal(t, "00162", decodedStruct.Sentence8218s[0].RecordLength)

	// Verify second record
	assert.Equal(t, "8218", decodedStruct.Sentence8218s[1].RecordType)
	assert.Equal(t, "000014641", decodedStruct.Sentence8218s[1].RequestIdentifier)
	assert.Equal(t, "K", decodedStruct.Sentence8218s[1].BillingType)
	assert.Equal(t, "Another", decodedStruct.Sentence8218s[1].LastName)
	assert.Equal(t, "Patient", decodedStruct.Sentence8218s[1].FirstName)
	assert.Equal(t, "19850315", decodedStruct.Sentence8218s[1].DateOfBirth)
	assert.Equal(t, "F", decodedStruct.Sentence8218s[1].Gender)
	assert.Equal(t, "00165", decodedStruct.Sentence8218s[1].RecordLength)

	t.Logf("Encoded %d bytes, decoded struct has %d Sentence8218 records",
		len(encoded), len(decodedStruct.Sentence8218s))
}

func TestLDTOrderWithTestData(t *testing.T) {
	// Create a more comprehensive LDTOrder with test data using TestInfo8218
	testInfo := TestInfo8218{
		TestID:                      "FA: QUIN/C",
		TestName:                    "FA: Quick",
		SampleMaterialID:            "KLBT/E",
		SampleMaterialName:          "Blutbild (klein)",
		CollectionDate:              "20250404",
		SampleMaterialSpecification: []string{"Routineuntersuchung"},
	}

	cholesterolTest := TestInfo8218{
		FieldAttr:                   field_transfer.FieldAttr("8410"),
		TestID:                      "CHOL",
		TestName:                    "Cholesterin",
		SampleMaterialID:            "SER",
		SampleMaterialName:          "Serum",
		CollectionDate:              "20250404",
		SampleMaterialSpecification: []string{"Lipidprofil"},
	}

	ldtOrder := LDTOrder{
		Sentence8230: Sentence8230{
			FieldAttr:      field_transfer.FieldAttr("8230"),
			RecordType:     "8230",
			RecordLength:   "00000",
			LDTVersion:     "LDT1014.01",
			FacilityNumber: "011234567",
			FacilityName:   "Test Laboratory",
			CustomerNumber: "TESTJP",
			CreationDate:   "20250404",
		},
		Sentence8218s: []Sentence8218{
			{
				FieldAttr:             field_transfer.FieldAttr("8218"),
				RecordType:            "8218",
				RecordLength:          "00000",
				RequestIdentifier:     "000014640",
				BillingType:           "K",
				LastName:              "Test",
				FirstName:             "Patient",
				DateOfBirth:           "19970514",
				Gender:                "M",
				InsuranceNumber:       "123456601",
				OrderingEntity:        "011234567",
				BillingResponsibility: "042025",
				Tests:                 []TestInfo8218{testInfo, cholesterolTest},
			},
		},
		Sentence8231: Sentence8231{
			FieldAttr:              field_transfer.FieldAttr("8231"),
			RecordType:             "8231",
			RecordLength:           "00000",
			TotalDataPackageLength: "00000",
		},
	}

	// Set sentence lengths
	SetSentenceLengthByTag8100(&ldtOrder.Sentence8230)
	SetSentenceLengthByTag8100(&ldtOrder.Sentence8218s[0])
	SetSentenceLengthByTag8100(&ldtOrder.Sentence8231)

	// Test that the struct is properly structured
	assert.Equal(t, "8230", ldtOrder.Sentence8230.RecordType)
	assert.Equal(t, "8218", ldtOrder.Sentence8218s[0].RecordType)
	assert.Equal(t, "8231", ldtOrder.Sentence8231.RecordType)

	// Test test data
	assert.Len(t, ldtOrder.Sentence8218s[0].Tests, 2)
	assert.Equal(t, "FA: QUIN/C", ldtOrder.Sentence8218s[0].Tests[0].TestID)
	assert.Equal(t, "FA: Quick", ldtOrder.Sentence8218s[0].Tests[0].TestName)
	assert.Equal(t, "KLBT/E", ldtOrder.Sentence8218s[0].Tests[0].SampleMaterialID)
	assert.Equal(t, "Blutbild (klein)", ldtOrder.Sentence8218s[0].Tests[0].SampleMaterialName)

	assert.Equal(t, "CHOL", ldtOrder.Sentence8218s[0].Tests[1].TestID)
	assert.Equal(t, "Cholesterin", ldtOrder.Sentence8218s[0].Tests[1].TestName)
}

func TestGenericDecodeLDTOrderFile(t *testing.T) {
	// Open the test LDT file
	file, err := os.Open("testdata/ldt/L01D250404A1464_AT_FA0.LDT.ldt")
	if err != nil {
		t.Fatalf("Failed to open test file: %v", err)
	}
	defer file.Close()

	// Test 1: Decode as LDTOrder using the generic function
	t.Run("Generic decode to LDTOrder", func(t *testing.T) {
		file.Seek(0, 0) // Reset file position

		var ldtOrder LDTOrder
		err := DecodeLDTFile(file, &ldtOrder)
		assert.NoError(t, err)

		assert.Equal(t, "8230", ldtOrder.Sentence8230.RecordType)
		assert.Equal(t, "8218", ldtOrder.Sentence8218s[0].RecordType)
		assert.Equal(t, "8231", ldtOrder.Sentence8231.RecordType)
		assert.Equal(t, "LDT1014.01", ldtOrder.Sentence8230.LDTVersion)

		t.Logf("LDTOrder decoded successfully with generic function")
	})

	// Test 2: Compare wrapper function vs generic function
	t.Run("Compare wrapper vs generic", func(t *testing.T) {
		file.Seek(0, 0) // Reset file position

		// Using the wrapper function
		order1, err := DecodeLDTOrder(file)
		assert.NoError(t, err)

		file.Seek(0, 0) // Reset file position

		// Using the generic function directly
		var order2 LDTOrder
		err = DecodeLDTFile(file, &order2)
		assert.NoError(t, err)

		// Both should produce identical results
		assert.Equal(t, order1.Sentence8230.RecordType, order2.Sentence8230.RecordType)
		assert.Equal(t, order1.Sentence8230.LDTVersion, order2.Sentence8230.LDTVersion)
		assert.Equal(t, order1.Sentence8230.CustomerNumber, order2.Sentence8230.CustomerNumber)
		assert.Equal(t, order1.Sentence8218s[0].RecordType, order2.Sentence8218s[0].RecordType)
		assert.Equal(t, order1.Sentence8231.RecordType, order2.Sentence8231.RecordType)

		t.Logf("Wrapper and generic functions produce identical results")
	})
}

func TestLDTOrderValidation(t *testing.T) {
	t.Run("Valid LDTOrder structure", func(t *testing.T) {
		ldtOrder := LDTOrder{
			Sentence8230: Sentence8230{
				RecordType:     "8230",
				FacilityNumber: "011234567",
				LDTVersion:     "LDT1014.01",
			},
			Sentence8218s: []Sentence8218{
				{
					RecordType:        "8218",
					RequestIdentifier: "000014640",
					BillingType:       "K",
				},
			},
			Sentence8231: Sentence8231{
				RecordType: "8231",
			},
		}

		// Test that all required record types are present
		assert.Equal(t, "8230", ldtOrder.Sentence8230.RecordType)
		assert.Equal(t, "8218", ldtOrder.Sentence8218s[0].RecordType)
		assert.Equal(t, "8231", ldtOrder.Sentence8231.RecordType)
	})

	t.Run("LDTOrder with comprehensive test information", func(t *testing.T) {
		// Create test with multiple parameters similar to the actual LDT file
		tests := []TestInfo8218{
			{
				TestID:             "FA: QUIN/C",
				TestName:           "FA: Quick",
				SampleMaterialID:   "KLBT/E",
				SampleMaterialName: "Blutbild (klein)",
				CollectionDate:     "20250404",
			},
			{
				TestID:             "CHOL",
				TestName:           "Cholesterin",
				SampleMaterialID:   "SER",
				SampleMaterialName: "Serum",
				CollectionDate:     "20250404",
			},
		}

		ldtOrder := LDTOrder{
			Sentence8218s: []Sentence8218{
				{
					RecordType:            "8218",
					RequestIdentifier:     "000014640",
					BillingType:           "K",
					LastName:              "Test",
					FirstName:             "Patient",
					DateOfBirth:           "19970514",
					Gender:                "M",
					OrderingEntity:        "011234567",
					BillingResponsibility: "042025",
					Tests:                 tests,
				},
			},
		}

		// Validate test data
		assert.Len(t, ldtOrder.Sentence8218s[0].Tests, 2)

		// Test first parameter (FA: Quick)
		faQuick := ldtOrder.Sentence8218s[0].Tests[0]
		assert.Equal(t, "FA: QUIN/C", faQuick.TestID)
		assert.Equal(t, "FA: Quick", faQuick.TestName)
		assert.Equal(t, "KLBT/E", faQuick.SampleMaterialID)
		assert.Equal(t, "Blutbild (klein)", faQuick.SampleMaterialName)

		// Test second parameter (Cholesterin)
		cholesterol := ldtOrder.Sentence8218s[0].Tests[1]
		assert.Equal(t, "CHOL", cholesterol.TestID)
		assert.Equal(t, "Cholesterin", cholesterol.TestName)
		assert.Equal(t, "SER", cholesterol.SampleMaterialID)
		assert.Equal(t, "Serum", cholesterol.SampleMaterialName)
	})
}

func TestLDTOrderFieldMapping(t *testing.T) {
	// Test field mapping for important medical data from the actual LDT file
	t.Run("Medical test parameters mapping", func(t *testing.T) {
		// Sample test data based on the actual LDT file content
		expectedTests := map[string]struct {
			name         string
			materialID   string
			materialName string
		}{
			"FA: QUIN/C": {"FA: Quick", "KLBT/E", "Blutbild (klein)"},
			"CHOL":       {"Cholesterin", "SER", "Serum"},
			"GLUC":       {"Glucose", "SER", "Serum"},
			"UREA":       {"Harnstoff", "SER", "Serum"},
		}

		for testID, expected := range expectedTests {
			testInfo := TestInfo8218{
				TestID:             testID,
				TestName:           expected.name,
				SampleMaterialID:   expected.materialID,
				SampleMaterialName: expected.materialName,
			}

			assert.Equal(t, expected.name, testInfo.TestName)
			assert.Equal(t, expected.materialID, testInfo.SampleMaterialID)
			assert.Equal(t, expected.materialName, testInfo.SampleMaterialName)
		}
	})

	t.Run("Patient information mapping", func(t *testing.T) {
		// Test patient data structure from 8218 sentence
		sentence8218 := Sentence8218{
			RecordType:            "8218",
			RequestIdentifier:     "000014640",
			BillingType:           "K",
			LastName:              "Abrechnung",
			FirstName:             "Kasse",
			DateOfBirth:           "19970514",
			Gender:                "M",
			InsuranceNumber:       "123456601",
			OrderingEntity:        "011234567",
			BillingResponsibility: "042025",
		}

		// Validate patient information
		assert.Equal(t, "000014640", sentence8218.RequestIdentifier)
		assert.Equal(t, "K", sentence8218.BillingType)
		assert.Equal(t, "Abrechnung", sentence8218.LastName)
		assert.Equal(t, "Kasse", sentence8218.FirstName)
		assert.Equal(t, "19970514", sentence8218.DateOfBirth)
		assert.Equal(t, "M", sentence8218.Gender)
		assert.Equal(t, "123456601", sentence8218.InsuranceNumber)
		assert.Equal(t, "011234567", sentence8218.OrderingEntity)
		assert.Equal(t, "042025", sentence8218.BillingResponsibility)
	})
}

func TestLDTOrderMultipleSentence8218(t *testing.T) {
	// Create test data with multiple 8218 records to verify slice handling
	testData := `01380008230
0158312TESTJP
0199212LDT1014.01

01380008218
0188310000014640
0108609K
0193101Patient1
0143102John

01380008218
0188310000014641
0108609K
0193101Patient2
0143102Jane

01380008231
017920200000
`

	reader := strings.NewReader(testData)
	order, err := DecodeLDTOrder(reader)
	assert.NoError(t, err)
	assert.NotNil(t, order)

	// Verify header and footer
	assert.Equal(t, "8230", order.Sentence8230.RecordType)
	assert.Equal(t, "8231", order.Sentence8231.RecordType)

	// Verify we have multiple 8218 records
	assert.Len(t, order.Sentence8218s, 2, "Should have decoded 2 Sentence8218 records")

	// Verify first 8218 record
	assert.Equal(t, "8218", order.Sentence8218s[0].RecordType)
	assert.Equal(t, "000014640", order.Sentence8218s[0].RequestIdentifier)
	assert.Equal(t, "K", order.Sentence8218s[0].BillingType)
	assert.Equal(t, "Patient1", order.Sentence8218s[0].LastName)
	assert.Equal(t, "John", order.Sentence8218s[0].FirstName)

	// Verify second 8218 record
	assert.Equal(t, "8218", order.Sentence8218s[1].RecordType)
	assert.Equal(t, "000014641", order.Sentence8218s[1].RequestIdentifier)
	assert.Equal(t, "K", order.Sentence8218s[1].BillingType)
	assert.Equal(t, "Patient2", order.Sentence8218s[1].LastName)
	assert.Equal(t, "Jane", order.Sentence8218s[1].FirstName)

	t.Logf("Successfully decoded %d Sentence8218 records", len(order.Sentence8218s))
}

func TestStructToByteArrayWithSliceSupportForOrder(t *testing.T) {
	t.Run("Test slice of structs encoding for LDTOrder", func(t *testing.T) {
		// Create a test struct with slice fields
		ldtOrder := LDTOrder{
			Sentence8230: Sentence8230{
				RecordType:     "8230",
				LDTVersion:     "LDT1014.01",
				FacilityNumber: "011234567",
				FacilityName:   "Test Lab",
				CustomerNumber: "TESTJP",
			},
			Sentence8218s: []Sentence8218{
				{
					RecordType:        "8218",
					RequestIdentifier: "000014640",
					BillingType:       "K",
					LastName:          "Patient1",
					FirstName:         "John",
					DateOfBirth:       "19970514",
					Gender:            "M",
				},
				{
					RecordType:        "8218",
					RequestIdentifier: "000014641",
					BillingType:       "K",
					LastName:          "Patient2",
					FirstName:         "Jane",
					DateOfBirth:       "19850315",
					Gender:            "F",
				},
			},
			Sentence8231: Sentence8231{
				RecordType:             "8231",
				TotalDataPackageLength: "00000",
			},
		}

		// Set lengths
		SetSentenceLengthByTag8100(&ldtOrder.Sentence8230)
		for i := range ldtOrder.Sentence8218s {
			SetSentenceLengthByTag8100(&ldtOrder.Sentence8218s[i])
		}
		SetSentenceLengthByTag8100(&ldtOrder.Sentence8231)

		// Encode
		encoder := getEncoder(document_setting_common.CharacterEncoding_7Bit)
		encoded := StructToByteArrayWithEncoder(ldtOrder, encoder)

		// Ensure we got some encoded data
		assert.NotEmpty(t, encoded, "Encoded data should not be empty")

		// Convert to string to verify structure
		encodedStr := string(encoded)
		t.Logf("Encoded LDT order data:\n%s", encodedStr)

		// Verify the encoded data contains records for both slice elements
		assert.Contains(t, encodedStr, "8230", "Should contain 8230 record")
		assert.Contains(t, encodedStr, "000014640", "Should contain first request ID")
		assert.Contains(t, encodedStr, "000014641", "Should contain second request ID")
		assert.Contains(t, encodedStr, "Patient1", "Should contain first patient name")
		assert.Contains(t, encodedStr, "Patient2", "Should contain second patient name")
		assert.Contains(t, encodedStr, "8231", "Should contain 8231 record")

		// Count occurrences of 8218 records
		record8218Count := strings.Count(encodedStr, "8218")
		assert.Equal(t, 2, record8218Count, "Should have 2 occurrences of 8218 record type")
	})

	t.Run("Test empty slice handling for LDTOrder", func(t *testing.T) {
		// Test with empty slices
		ldtOrder := LDTOrder{
			Sentence8230: Sentence8230{
				RecordType:     "8230",
				FacilityNumber: "011234567",
				CustomerNumber: "TESTJP",
			},
			Sentence8218s: []Sentence8218{}, // Empty slice
			Sentence8231: Sentence8231{
				RecordType: "8231",
			},
		}

		SetSentenceLengthByTag8100(&ldtOrder.Sentence8230)
		SetSentenceLengthByTag8100(&ldtOrder.Sentence8231)

		encoder := getEncoder(document_setting_common.CharacterEncoding_7Bit)
		encoded := StructToByteArrayWithEncoder(ldtOrder, encoder)

		assert.NotEmpty(t, encoded, "Encoded data should not be empty")

		encodedStr := string(encoded)
		t.Logf("Encoded LDT order with empty slice:\n%s", encodedStr)

		// Should still contain header and footer, but no 8218 records
		assert.Contains(t, encodedStr, "8230", "Should contain 8230 record")
		assert.Contains(t, encodedStr, "8231", "Should contain 8231 record")
		assert.NotContains(t, encodedStr, "8218", "Should not contain any 8218 records")
	})
}

func TestActualLDTOrderFileStructure(t *testing.T) {
	// Test using the actual provided LDT file to verify structure understanding
	file, err := os.Open("testdata/ldt/L01D250404A1464_AT_FA0.LDT.ldt")
	if err != nil {
		t.Fatalf("Failed to open test file: %v", err)
	}
	defer file.Close()

	order, err := DecodeLDTOrder(file)
	assert.NoError(t, err)
	assert.NotNil(t, order)

	// Verify the actual structure based on the file content
	assert.Equal(t, "8230", order.Sentence8230.RecordType)
	assert.Equal(t, "LDT1014.01", order.Sentence8230.LDTVersion)
	assert.Equal(t, "TESTJP", order.Sentence8230.CustomerNumber)

	// Test patient record
	assert.Len(t, order.Sentence8218s, 1)
	patientRecord := order.Sentence8218s[0]
	assert.Equal(t, "8218", patientRecord.RecordType)
	assert.Equal(t, "K", patientRecord.BillingType)
	assert.Equal(t, "**********", patientRecord.RequestIdentifier) // Note: Actual decoded value
	assert.Equal(t, "Abrechnung", patientRecord.LastName)
	assert.Equal(t, "Kasse", patientRecord.FirstName)
	assert.Equal(t, "19970514", patientRecord.DateOfBirth)
	// Note: Some fields are empty in the actual file, so we test for empty values
	assert.Equal(t, "", patientRecord.OrderingEntity)
	assert.Equal(t, "", patientRecord.InsuranceNumber)
	assert.Equal(t, "", patientRecord.BillingResponsibility)
	assert.Equal(t, "", patientRecord.Gender)

	// Test footer
	assert.Equal(t, "8231", order.Sentence8231.RecordType)

	// Log the complete decoded structure for verification
	t.Logf("Decoded LDT Order:")
	t.Logf("  Header: %s, Version: %s, Customer: %s",
		order.Sentence8230.RecordType, order.Sentence8230.LDTVersion, order.Sentence8230.CustomerNumber)
	t.Logf("  Patient: %s %s, DOB: %s, Gender: '%s', Billing: %s",
		patientRecord.FirstName, patientRecord.LastName, patientRecord.DateOfBirth,
		patientRecord.Gender, patientRecord.BillingType)
	t.Logf("  Footer: %s", order.Sentence8231.RecordType)
}
