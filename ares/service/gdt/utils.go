package gdt

import (
	"bytes"
	"fmt"
	"io"
	"reflect"
	"strconv"
	"strings"

	"git.tutum.dev/medi/tutum/ares/service/domains/api/document_setting_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	profile_service "git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	"git.tutum.dev/medi/tutum/pkg/util"
	"golang.org/x/text/encoding"
	"golang.org/x/text/encoding/charmap"
	"golang.org/x/text/transform"
)

func getGenderValue(g patient_profile_common.Gender) int {
	switch g {
	case patient_profile_common.M:
		return 1
	case patient_profile_common.W:
		return 2
	default:
		return 0
	}
}

// Function to convert struct to byte array with encoding
func StructToByteArrayWithEncoder(data any, encoder *encoding.Encoder) []byte {
	var buf bytes.Buffer

	// Validate that `data` is a struct or a pointer to a struct
	val := reflect.ValueOf(data)
	if val.Kind() == reflect.Ptr {
		val = val.Elem()
	}
	if val.Kind() != reflect.Struct {
		return nil // Return nil if `data` is not a struct
	}
	typ := val.Type()

	// Create a writer, with or without an encoder
	var writer io.Writer = &buf
	if encoder != nil {
		writer = transform.NewWriter(writer, encoder)
		defer func() {
			if closer, ok := writer.(io.Closer); ok {
				closer.Close()
			}
		}()
	}

	// Iterate over the struct fields
	for i := range val.NumField() {
		field := val.Field(i)
		fieldType := typ.Field(i)

		// Handle embedded structs recursively
		if field.Kind() == reflect.Struct {
			nestedBytes := StructToByteArrayWithEncoder(field.Interface(), nil) // no need to encode nested struct fields we will encode only one
			_, err := writer.Write(nestedBytes)
			if err != nil {
				return nil
			}
			continue
		}

		// Handle slice fields
		if field.Kind() == reflect.Slice {
			// Iterate through each element in the slice
			for j := range field.Len() {
				element := field.Index(j)

				// If the slice element is a struct, encode it recursively
				if element.Kind() == reflect.Struct {
					nestedBytes := StructToByteArrayWithEncoder(element.Interface(), nil)
					_, err := writer.Write(nestedBytes)
					if err != nil {
						return nil
					}
				} else {
					// Handle slice of primitive types (strings, numbers)
					fieldTag := fieldType.Tag.Get("field")
					if fieldTag == "" {
						continue // Skip fields without a `field` tag
					}

					var value string
					switch element.Kind() {
					case reflect.String:
						value = element.String()
					case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
						if element.Int() != 0 {
							value = strconv.FormatInt(element.Int(), 10)
						}
					}

					// Skip if the value is empty
					if value == "" {
						continue
					}

					if err := writeLine(writer, fieldTag, value); err != nil {
						return nil
					}
				}
			}
			continue
		}

		// Skip FieldAttr embedded fields (they're used by field_transfer package but shouldn't be encoded)
		if fieldType.Type.String() == "field_transfer.FieldAttr" {
			continue
		}

		// Get the `field` tag
		fieldTag := fieldType.Tag.Get("field")
		if fieldTag == "" {
			continue // Skip fields without a `field` tag
		}

		// Get the field value as a string
		var value string
		switch field.Kind() {
		case reflect.String:
			value = field.String()
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			if field.Int() != 0 {
				value = strconv.FormatInt(field.Int(), 10)
			}
		}

		// Skip if the value is empty
		if value == "" {
			continue
		}

		if err := writeLine(writer, fieldTag, value); err != nil {
			return nil
		}
	}

	return buf.Bytes()
}

func writeLine(writer io.Writer, fieldTag, value string) error {
	line := fmt.Sprintf("%03d%s%s\r\n", util.CountChar(value)+FixLength, fieldTag, value)
	_, err := writer.Write([]byte(line))
	return err
}

func TestDataTransferToTimelineContent(data TestDataTransfer) string {
	var builder strings.Builder

	// Patient Information
	builder.WriteString(fmt.Sprintf("Patient: ID %s, %s %s %s %s\n", data.PatientNumber, data.Title, data.FirstName, data.Prefix, data.LastName))
	builder.WriteString("-----------------------------\n")
	if data.Height != 0 {
		builder.WriteString(fmt.Sprintf("Größe des Patienten: %d\n", data.Height))
	}
	if data.Weight != 0 {
		builder.WriteString(fmt.Sprintf("Gewicht des Patienten: %d\n", data.Weight))
	}
	if data.DeviceSpecificField != "" {
		builder.WriteString(fmt.Sprintf("Geräte und verfahrensspezifisches Kennfeld: %s\n", data.DeviceSpecificField))
	}
	if data.TreatmentDate != "" {
		builder.WriteString(fmt.Sprintf("Tag der Erhebung von Behandlungsdaten: %s\n", data.TreatmentDate))
	}
	if data.TreatmentTime != "" {
		builder.WriteString(fmt.Sprintf("Uhrzeit der Erhebung von Behandlungsdaten: %s\n", data.TreatmentTime))
	}

	// Diagnoses and Results
	if len(data.CurrentDiagnosis) > 0 {
		builder.WriteString("Aktuelle Diagnose:\n")
		builder.WriteString(strings.Join(data.CurrentDiagnosis, "\n") + "\n")
	}
	if len(data.Results) > 0 {
		builder.WriteString("Befund:\n")
		builder.WriteString(strings.Join(data.Results, "\n") + "\n")
	}
	if len(data.ThirdPartyResults) > 0 {
		builder.WriteString("Fremdbefund:\n")
		builder.WriteString(strings.Join(data.ThirdPartyResults, "\n") + "\n")
	}
	if len(data.Comments) > 0 {
		builder.WriteString("Kommentar:\n")
		builder.WriteString(strings.Join(data.Comments, "\n") + "\n")
	}
	if len(data.ResultsTexts) != 0 {
		builder.WriteString("Ergebnistabellentext:\n")
		for _, v := range data.ResultsTexts {
			builder.WriteString(v + "\n")
		}
		builder.WriteString("\n")
	}

	// Categories
	for _, category := range data.NameCategories {
		for k, v := range category.FreeText {
			builder.WriteString(fmt.Sprintf("Kategorie: %s\nInhalt: %s\n", k, v))
		}
		for k, v := range category.Content.FreeText {
			builder.WriteString(fmt.Sprintf("Kategorie: %s\nInhalt: %s\n", k, v))
		}
	}
	for _, v := range data.Tests {
		if v.TestID != "" {
			builder.WriteString(fmt.Sprintf("Test-Ident: %s\n", v.TestID))
		}
		if v.TestName != "" {
			builder.WriteString(fmt.Sprintf("Testbezeichnung: %s\n", v.TestName))
		}
		if v.TestMaterialID != "" {
			builder.WriteString(fmt.Sprintf("Probenmaterialindex: %s\n", v.TestMaterialID))
			builder.WriteString(fmt.Sprintf("Probenmaterial-Ident: %s\n", v.TestMaterialID))
		}
		if v.TestMaterialIndex != 0 {
			builder.WriteString(fmt.Sprintf("Probenmaterial-Index: %d\n", v.TestMaterialIndex))
		}
		if v.TestMaterialLabel.TestMaterialLabel != "" {
			builder.WriteString(fmt.Sprintf("Probenmaterial-Bezeichnung: %s\n", v.TestMaterialLabel.TestMaterialLabel))
		}
		if len(v.TestMaterialLabel.TestMaterialSpecification) != 0 {
			builder.WriteString(fmt.Sprintf("Probenmaterial-Spezifikation: %s\n", strings.Join(v.TestMaterialLabel.TestMaterialSpecification, "\n")))
		}
		if v.UnitForDataStream != "" {
			builder.WriteString(fmt.Sprintf("Einheiten für Datenstrom: %s\n", v.UnitForDataStream))
		}
		if len(v.DataStream) != 0 {
			builder.WriteString(fmt.Sprintf("Datenstrom: %s\n", strings.Join(v.DataStream, "\n")))
		}
		if v.TestStatus != "" {
			builder.WriteString(fmt.Sprintf("Teststatus: %s\n", v.TestStatus))
		}
		if v.ResultValue != "" {
			builder.WriteString(fmt.Sprintf("Ergebnis-Wert: %s\n", v.ResultValue))
		}
		if v.Unit != "" {
			builder.WriteString(fmt.Sprintf("Einheit: %s\n", v.Unit))
		}
		if v.ReadingDate != "" {
			builder.WriteString(fmt.Sprintf("Abnahmedatum: %s\n", v.ReadingDate))
		}
		if v.ReadingTime != "" {
			builder.WriteString(fmt.Sprintf("Abnahmezeit: %s\n", v.ReadingTime))
		}
		if v.StandardValueText != "" {
			builder.WriteString(fmt.Sprintf("Normalwert-Text: %s\n", v.StandardValueText))
		}
		if v.LowerThreshold != "" {
			builder.WriteString(fmt.Sprintf("Normalwert untere Grenze: %s\n", v.LowerThreshold))
		}
		if v.UpperThreshold != "" {
			builder.WriteString(fmt.Sprintf("Normalwert obere Grenze: %s\n", v.UpperThreshold))
		}
		if len(v.Comments) != 0 {
			builder.WriteString(fmt.Sprintf("Anmerkung: %s\n", strings.Join(v.Comments, "\n")))
		}
		if len(v.ResultsText) != 0 {
			builder.WriteString(fmt.Sprintf("Ergebnis-Text: %s\n", strings.Join(v.ResultsText, "\n")))
		}
	}

	// Signature
	if data.Signature != "" {
		builder.WriteString(fmt.Sprintf("Signatur: %s\n", data.Signature))
	}

	return builder.String()
}

func get3106Content(a patient_profile_common.Address) string {
	return strings.TrimSpace(fmt.Sprintf("%s %s", a.PostCode, util.GetPointerValue(a.City)))
}

func get3107Content(a patient_profile_common.Address) string {
	return strings.TrimSpace(fmt.Sprintf("%s %s", util.GetPointerValue(a.Street), util.GetPointerValue(a.Number)))
}

// calculateStructLength calculates the total length of a struct, including field tags, values, and newline characters.
func calculateStructLength(obj any) int {
	if obj == nil {
		return 0
	}

	val := reflect.ValueOf(obj)
	for val.Kind() == reflect.Ptr {
		if val.IsNil() {
			return 0
		}
		val = val.Elem()
	}

	switch val.Kind() {
	case reflect.Struct:
		length := 0
		typ := val.Type()
		for i := 0; i < val.NumField(); i++ {
			field := val.Field(i)
			fieldType := typ.Field(i)

			// Skip unexported fields
			if !field.CanInterface() {
				continue
			}

			// Skip FieldAttr embedded fields (they're used by field_transfer package but shouldn't be encoded)
			if fieldType.Type.String() == "field_transfer.FieldAttr" {
				continue
			}

			// Handle nested structs recursively
			if field.Kind() == reflect.Struct || (field.Kind() == reflect.Ptr && field.Elem().Kind() == reflect.Struct) {
				length += calculateStructLength(field.Interface())
				continue
			}

			// Get the field tag (if any)
			fieldTag := fieldType.Tag.Get("field")
			if fieldTag == "" {
				continue // Skip fields without a `field` tag
			}

			// Get the field value as a string
			var value string
			switch field.Kind() {
			case reflect.String:
				value = field.String()
			case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
				if field.Int() != 0 {
					value = strconv.FormatInt(field.Int(), 10)
				}
			case reflect.Slice:
				for i := 0; i < field.Len(); i++ {
					length += calculateStructLength(field.Index(i).Interface())
				}
				continue
			}
			// Skip if the value is empty
			if value == "" {
				continue
			}

			// Add the length of the field tag, value, and newline character
			length += util.CountChar(value) + FixLength
		}
		return length
	case reflect.String:
		// If called directly with a string, just count its chars
		return util.CountChar(val.String()) + FixLength
	case reflect.Slice:
		length := 0
		for i := 0; i < val.Len(); i++ {
			length += calculateStructLength(val.Index(i).Interface())
		}
		return length
	}
	// For unsupported types, return 0
	return 0
}

// SetSentenceLength sets the SentenceLength field of the Header struct.
func SetSentenceLength(obj any) {
	val := reflect.ValueOf(obj)
	if val.Kind() == reflect.Ptr {
		val = val.Elem() // Dereference if it's a pointer
	}

	// Find the Header field
	headerField := val.FieldByName("Header")
	if !headerField.IsValid() {
		return // No Header field found
	}

	// Find the SentenceLength field within the Header
	sentenceLengthField := headerField.FieldByName("SentenceLength")
	if !sentenceLengthField.IsValid() {
		return // No SentenceLength field found
	}

	// Calculate the total length of the struct
	totalLength := calculateStructLength(obj)

	// Set the SentenceLength field
	sentenceLengthField.SetString(util.LeftPad(strconv.Itoa(totalLength), 5, '0'))
}

func SetSentenceLengthByTag8100(obj any) {
	val := reflect.ValueOf(obj)
	if val.Kind() == reflect.Ptr {
		val = val.Elem() // Dereference if it's a pointer
	}

	// Find the field with tag "8100" using reflection
	fieldWithTag8100 := findFieldByTag(val, "8100")
	if !fieldWithTag8100.IsValid() || !fieldWithTag8100.CanSet() {
		return // No field with tag "8100" found or field is not settable
	}

	// Calculate the total length of the struct
	totalLength := calculateStructLength(obj)

	// Set the field value
	fieldWithTag8100.SetString(util.LeftPad(strconv.Itoa(totalLength), 5, '0'))
}

// findFieldByTag recursively searches for a field with the specified tag
func findFieldByTag(val reflect.Value, targetTag string) reflect.Value {
	if val.Kind() != reflect.Struct {
		return reflect.Value{}
	}

	typ := val.Type()
	for i := 0; i < val.NumField(); i++ {
		field := val.Field(i)
		fieldType := typ.Field(i)

		// Skip unexported fields
		if !field.CanInterface() {
			continue
		}

		// Check if this field has the target tag
		fieldTag := fieldType.Tag.Get("field")
		if fieldTag == targetTag && field.Kind() == reflect.String {
			return field
		}

		// If this is a struct, search recursively
		if field.Kind() == reflect.Struct {
			result := findFieldByTag(field, targetTag)
			if result.IsValid() {
				return result
			}
		}
	}

	return reflect.Value{}
}

func getPrefixPatient(p profile_service.PatientProfile) string {
	return strings.TrimSpace(fmt.Sprintf("%s %s", p.PatientInfo.PersonalInfo.GetAdditionalNames(), p.GetIntentWord()))
}

func getEncoder(characterEncoding document_setting_common.CharacterEncoding) *encoding.Encoder {
	switch characterEncoding {
	case document_setting_common.CharacterEncoding_IBM437:
		return charmap.CodePage437.NewEncoder()
	case document_setting_common.CharacterEncoding_ISO8859:
		return charmap.ISO8859_1.NewEncoder()
	default:
		return charmap.ISO8859_15.NewEncoder()
	}
}

func getEncoderHeader(characterEncoding document_setting_common.CharacterEncoding) string {
	switch characterEncoding {
	case document_setting_common.CharacterEncoding_IBM437:
		return "2"
	case document_setting_common.CharacterEncoding_ISO8859:
		return "3"
	default:
		return "1"
	}
}

func getPregnancyContent(patientProfile profile_service.PatientProfile) string {
	if patientProfile.PatientMedicalData != nil && patientProfile.PatientMedicalData.IsPregnant != nil && *patientProfile.PatientMedicalData.IsPregnant {
		return "1"
	}
	return "0"
}

func getPatientWeight(patientProfile profile_service.PatientProfile) string {
	if patientProfile.PatientMedicalData != nil && patientProfile.PatientMedicalData.Weight != nil {
		return util.NumberToString(patientProfile.PatientMedicalData.Weight)
	}
	return ""
}

func getPatientHeight(patientProfile profile_service.PatientProfile) string {
	if patientProfile.PatientMedicalData != nil && patientProfile.PatientMedicalData.Height != nil {
		return util.NumberToString(patientProfile.PatientMedicalData.Height)
	}
	return ""
}
