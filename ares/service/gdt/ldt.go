package gdt

import (
	"errors"

	"git.tutum.dev/medi/tutum/ares/service/domains/api/document_setting_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	profile_service "git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"
	employeeRepo "git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee"
	field_transfer "git.tutum.dev/medi/tutum/pkg/field_transfer"
	"git.tutum.dev/medi/tutum/pkg/util"
	"gitlab.com/silenteer-oss/titan"
)

// given the gdt struct, we need to create a ldt struct

const (
	LdtVersionHeader = "LDT1014.01"
)

type LDTBuilder struct {
	PatientProfile profile_service.PatientProfile
	LdtExport      *document_setting_common.LdtExport
	Schein         *schein_common.GetScheinDetailByIdResponse
	Employee       *employeeRepo.EmployeeProfile
}

type LDTInsurance struct {
	InsuranceNumber     string
	HealthInsuranceName string
}

func NewLDTBuilder(payload LDTBuilder) *LDTBuilder {
	return &LDTBuilder{
		PatientProfile: payload.PatientProfile,
		LdtExport:      payload.LdtExport,
		Schein:         payload.Schein,
		Employee:       payload.Employee,
	}
}
func (s *LDTBuilder) GetCurrentInsuranceStatus() string {
	in := s.getCurrentInsurance()
	if in == nil {
		return ""
	}
	return string(in.InsuranceStatus)
}

func (s *LDTBuilder) getCurrentInsurance() *patient_profile_common.InsuranceInfo {
	if s.Schein == nil {
		return nil
	}
	return s.PatientProfile.PatientInfo.GetInsurance(s.Schein.InsuranceId)
}

func (s *LDTBuilder) getCurrentInsuranceNumber() string {
	in := s.getCurrentInsurance()
	return util.GetValueFromStringPointer(in.InsuranceNumber)
}

func (s *LDTBuilder) getBillingType() string {
	in := s.getCurrentInsurance()
	switch in.InsuranceType {
	case patient_profile_common.Public:
		return "K"
	case patient_profile_common.Private:
		return "P"
	case patient_profile_common.BG:
		return "X"
	default:
		return ""
	}
}

func (s *LDTBuilder) getASVTeamNumber() []string {
	if s.Employee.TeamNumbers == nil {
		return []string{}
	}
	return *s.Employee.TeamNumbers
}

func (s *LDTBuilder) getLastCardReadDay(ctx *titan.Context) string {
	currentYearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	cardReadDate := s.getCurrentInsurance().GetCardByQuarter(currentYearQuarter)
	if cardReadDate == nil {
		return ""
	}
	return util.FormatMillisecondsToString(cardReadDate.GetReadCardDate(), util.YYYYMMDD)
}

func (s *LDTBuilder) Build(ctx *titan.Context) ([]byte, error) {
	insurance := s.getCurrentInsurance()
	if insurance == nil {
		return nil, errors.New("insurance information not found for patient")
	}

	if s.Employee == nil {
		return nil, errors.New("employee information not found")
	}

	ldtSentenceFromSchein := MapScheinDetailToLDT(s.Schein.ScheinDetails)

	if ldtSentenceFromSchein == nil {
		return nil, errors.New("ldt sentence from schein not found")
	}

	patientDataFile := PatientDataFile{
		Header: Sentence8230{
			FieldAttr:      field_transfer.FieldAttr("8230"),
			RecordType:     "8230",
			RecordLength:   "00000",
			LDTVersion:     LdtVersionHeader,
			FacilityNumber: s.Employee.Bsnr,
			FacilityName:   util.GetPointerValue(s.Employee.BsnrName),
			PhysicianID:    []string{util.GetValueFromStringPointer(s.Employee.Lanr)},
			PhysicianName:  []string{util.GetDoctorName(s.Employee)},
			ASVTeamNumber:  s.getASVTeamNumber(),
			FacilityStreet: s.Employee.BsnrStreet,
			FacilityZIP:    s.Employee.BsnrPostCode,
			FacilityCity:   util.GetValueFromStringPointer(s.Employee.Address),
			CharacterSet:   getEncoderHeader(s.LdtExport.CharacterEncoding),
			CreationDate:   util.Now(ctx).Format(util.YYYYMMDD),
		},
		PatientRecord: Sentence8218{
			FieldAttr:                   field_transfer.FieldAttr("8218"),
			RecordType:                  "8218",
			RecordLength:                "00000",
			BillingType:                 s.getBillingType(),
			NameSuffix:                  s.PatientProfile.PatientInfo.PersonalInfo.GetAdditionalNames(),
			Prefix:                      s.PatientProfile.PatientInfo.PersonalInfo.IntendWord.ToStringValue(),
			LastName:                    s.PatientProfile.GetLastName(),
			FirstName:                   s.PatientProfile.GetFirstName(),
			DateOfBirth:                 s.PatientProfile.PatientInfo.PersonalInfo.DateOfBirthString(),
			Title:                       s.PatientProfile.GetTitle(),
			InsuranceNumber:             s.getCurrentInsuranceNumber(),
			InsuredPersonID:             s.getCurrentInsuranceNumber(),
			Street:                      util.GetPointerValue(s.PatientProfile.PatientInfo.AddressInfo.Address.Street),
			HouseNumber:                 util.GetValueFromStringPointer(s.PatientProfile.PatientInfo.AddressInfo.Address.Number),
			ZIPCode:                     s.PatientProfile.PatientInfo.AddressInfo.Address.PostCode,
			ResidenceCountryCode:        util.GetValueFromStringPointer(s.PatientProfile.PatientInfo.AddressInfo.Address.CountryCode),
			City:                        util.GetValueFromStringPointer(s.PatientProfile.PatientInfo.AddressInfo.Address.City),
			POBoxZIP:                    s.PatientProfile.PatientInfo.PostOfficeBox.PostCode,
			POBoxCity:                   s.PatientProfile.PatientInfo.PostOfficeBox.PlaceOfResidence,
			POBox:                       s.PatientProfile.PatientInfo.PostOfficeBox.OfficeBox,
			POBoxResidenceCountryCode:   s.PatientProfile.PatientInfo.PostOfficeBox.CountryCode,
			WOP:                         insurance.Wop,
			InsuranceInstitution:        s.GetCurrentInsuranceStatus(),
			Gender:                      s.PatientProfile.GetGenderString(),
			HealthInsuranceName:         s.getCurrentInsurance().InsuranceCompanyName,
			BillingVKNR:                 insurance.GetInsuranceCompanyId(),
			CostUnitBillingArea:         ldtSentenceFromSchein.CostUnitBillingArea,
			LastCardReadDay:             s.getLastCardReadDay(ctx),
			InsuranceCoverageStart:      util.FormatMillisecondsToString(insurance.StartDate, util.YYYYMMDD),
			InsuranceCoverageEnd:        util.FormatMillisecondsToString(insurance.EndDate, util.YYYYMMDD),
			CostUnitIdentification:      insurance.GetIkNumberString(),
			SpecialPopulationGroup:      string(insurance.SpecialGroup),
			DMPDesignation:              insurance.DMPLabeling,
			SKTAdditionalInfo:           ldtSentenceFromSchein.SKTAdditionalInfo,
			FeeSchedule:                 string(insurance.FeeCatalogue),
			AccidentConsequences:        ldtSentenceFromSchein.AccidentConsequences,
			BillingRegion:               ldtSentenceFromSchein.BillingRegion,
			LimitedBenefitEntitlement:   ldtSentenceFromSchein.LimitedBenefitEntitlement,
			Orders:                      ldtSentenceFromSchein.Orders,
			DiagnosisSuspectedDiagnosis: ldtSentenceFromSchein.DiagnosisSuspectedDiagnosis,
			FindingMedication:           ldtSentenceFromSchein.FindingMedication,
			InitiatingPhysicianBSNR:     ldtSentenceFromSchein.InitiatingPhysicianBSNR,
			InitiatingPhysician:         ldtSentenceFromSchein.InitiatingPhysician,
			InitiatingPhysicianASV:      ldtSentenceFromSchein.InitiatingPhysicianASV,
			ReferringPhysicianBSNR:      ldtSentenceFromSchein.ReferringPhysicianBSNR,
			ReferringPhysician:          ldtSentenceFromSchein.ReferringPhysician,
			ReferringPhysicianASV:       ldtSentenceFromSchein.ReferringPhysicianASV,
			ReferralFromOtherPhysicians: ldtSentenceFromSchein.ReferralFromOtherPhysicians,
			ReferralTo:                  ldtSentenceFromSchein.ReferralTo,
			SubgroupIdentifier:          util.GetValueFromStringPointer(s.Schein.KvScheinSubGroup),
			CurativePreventiveESS:       ldtSentenceFromSchein.CurativePreventiveESS,
			ExceptionIndication:         ldtSentenceFromSchein.ExceptionIndication,
			Pregnancy:                   getPregnancyContent(s.PatientProfile),
			PatientHeight:               getPatientHeight(s.PatientProfile),
			PatientWeight:               getPatientWeight(s.PatientProfile),
		},
		Footer: Sentence8231{
			FieldAttr:    field_transfer.FieldAttr("8231"),
			RecordType:   "8231",
			RecordLength: "00000",
		},
	}

	SetSentenceLengthByTag8100(&patientDataFile.Header)
	SetSentenceLengthByTag8100(&patientDataFile.PatientRecord)
	SetSentenceLengthByTag8100(&patientDataFile.Footer)

	encoder := getEncoder(s.LdtExport.CharacterEncoding)
	encoded := StructToByteArrayWithEncoder(patientDataFile, encoder)

	return encoded, nil
}
