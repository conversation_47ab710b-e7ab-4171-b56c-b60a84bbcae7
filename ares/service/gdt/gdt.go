package gdt

import (
	"strconv"
	"time"

	"git.tutum.dev/medi/tutum/ares/service/domains/api/document_setting_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	profile_service "git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"
	"git.tutum.dev/medi/tutum/pkg/util"
	"golang.org/x/text/encoding"
	"golang.org/x/text/encoding/charmap"
)

var (
	FixLength = 9 // length of line (first 3rd chars of line) 3 + 2 characters of new line (CRLF) + 4 length of field tag
)

type GDTBuilder struct {
	characterEncoding document_setting_common.CharacterEncoding
	patientProfile    profile_service.PatientProfile
	gdtExport         *document_setting_common.GdtExport
	garrioProVersion  string
	schein            *schein_common.Schein
}

func NewGDTBuilder(
	characterEncoding document_setting_common.CharacterEncoding,
	patientProfile profile_service.PatientProfile,
	gdtExport *document_setting_common.GdtExport,
	garrioProVersion string,
) *GDTBuilder {
	return &GDTBuilder{
		characterEncoding: characterEncoding,
		patientProfile:    patientProfile,
		gdtExport:         gdtExport,
		garrioProVersion:  garrioProVersion,
	}
}

func (s *GDTBuilder) SetSchein(schein *schein_common.Schein) {
	if s == nil {
		return
	}
	s.schein = schein
}

func (s *GDTBuilder) getCurrentInsurance() *patient_profile_common.InsuranceInfo {
	if s == nil || s.schein == nil {
		return nil
	}
	return s.patientProfile.PatientInfo.GetInsurance(s.schein.InsuranceId)
}

func (s *GDTBuilder) GetCurrentInsuranceNumber() string {
	in := s.getCurrentInsurance()
	if in == nil {
		return ""
	}
	return util.GetPointerValue(in.InsuranceNumber)
}

func (s *GDTBuilder) GetCurrentInsuranceStatus() string {
	in := s.getCurrentInsurance()
	if in == nil {
		return ""
	}
	return string(in.InsuranceStatus)
}

func (s *GDTBuilder) GetEncoderString() string {
	switch s.characterEncoding {
	case document_setting_common.CharacterEncoding_ISO8859:
		return "3"
	case document_setting_common.CharacterEncoding_IBM437:
		return "2"
	default:
		return "1"
	}
}

func (s *GDTBuilder) GetEncoder() *encoding.Encoder {
	switch s.characterEncoding {
	case document_setting_common.CharacterEncoding_ISO8859:
		return charmap.ISO8859_1.NewEncoder()
	case document_setting_common.CharacterEncoding_IBM437:
		return charmap.CodePage437.NewEncoder()
	default:
		return charmap.ISO8859_15.NewEncoder()
	}
}

func (s *GDTBuilder) Build6301() []byte {
	medicalData := util.GetPointerValue(s.patientProfile.PatientMedicalData)
	patientNumber := strconv.Itoa(int(s.patientProfile.PatientInfo.PatientNumber))
	obj6301 := RootDataRequest{
		Header: Header{
			SentenceID:     "6301",
			SentenceLength: "00000",
			Receiver:       s.gdtExport.ReceiverName,
			Sender:         s.gdtExport.SenderName,
			SetType:        s.GetEncoderString(),
			Version:        string(s.gdtExport.GdtVersion),
		},
		PatientInfo: PatientInfo{
			PatientNumber:   patientNumber,
			Prefix:          getPrefixPatient(s.patientProfile),
			LastName:        s.patientProfile.GetLastName(),
			FirstName:       s.patientProfile.GetFirstName(),
			BirthDate:       s.patientProfile.PatientInfo.PersonalInfo.DateOfBirth_DDMMYYYY(),
			Title:           s.patientProfile.PatientInfo.GetTitle(),
			InsuranceNumber: s.GetCurrentInsuranceNumber(),
			Residence:       get3106Content(s.patientProfile.PatientInfo.AddressInfo.Address),
			Street:          get3107Content(s.patientProfile.PatientInfo.AddressInfo.Address),
			InsuranceStatus: s.GetCurrentInsuranceStatus(),
			Sex:             getGenderValue(s.patientProfile.PatientInfo.PersonalInfo.Gender),
			Height:          int(util.GetPointerValue(medicalData.Height)),
			Weight:          int(util.GetPointerValue(medicalData.Weight)),
		},
	}
	SetSentenceLength(&obj6301)

	return StructToByteArrayWithEncoder(obj6301, s.GetEncoder())
}

func (s *GDTBuilder) Build6302() []byte {
	medicalData := util.GetPointerValue(s.patientProfile.PatientMedicalData)
	obj6302 := NewTestRequest{
		Header: Header{
			SentenceID:      "6302",
			SentenceLength:  "00000",
			Receiver:        s.gdtExport.ReceiverName,
			Sender:          s.gdtExport.SenderName,
			SetType:         s.GetEncoderString(),
			Version:         string(s.gdtExport.GdtVersion),
			Responsible:     "garrio GmbH",
			Software:        "garrioPRO",
			SoftwareVersion: s.garrioProVersion,
		},
		PatientInfo: PatientInfo{
			PatientNumber:   strconv.Itoa(int(s.patientProfile.PatientInfo.PatientNumber)),
			Prefix:          getPrefixPatient(s.patientProfile),
			LastName:        s.patientProfile.GetLastName(),
			FirstName:       s.patientProfile.GetFirstName(),
			BirthDate:       s.patientProfile.PatientInfo.PersonalInfo.DateOfBirth_DDMMYYYY(),
			Title:           s.patientProfile.PatientInfo.GetTitle(),
			InsuranceNumber: s.GetCurrentInsuranceNumber(),
			Residence:       get3106Content(s.patientProfile.PatientInfo.AddressInfo.Address),
			Street:          get3107Content(s.patientProfile.PatientInfo.AddressInfo.Address),
			InsuranceStatus: s.GetCurrentInsuranceStatus(),
			Sex:             getGenderValue(s.patientProfile.PatientInfo.PersonalInfo.Gender),
			Height:          int(util.GetPointerValue(medicalData.Height)),
			Weight:          int(util.GetPointerValue(medicalData.Weight)),
		},
		DeviceSpecificField: util.GetPointerValue(s.gdtExport.DeviceMethodField),
		TestID:              util.GetPointerValue(s.gdtExport.TestId),
	}
	SetSentenceLength(&obj6302)

	return StructToByteArrayWithEncoder(obj6302, s.GetEncoder())
}

func (s *GDTBuilder) Build6311(
	treatmentDate time.Time,
	readingDate time.Time,
	treatmentTime *time.Time,
	readingTime *time.Time,
) []byte {
	var treatmentDateString, treatmentTimeHourString, readingDateString, readingTimeHourString string
	if !treatmentDate.IsZero() {
		treatmentDateString = treatmentDate.Format(util.DDMMYYYY)
	}
	if treatmentTime != nil {
		treatmentTimeHourString = treatmentTime.Format(util.HHMMSS)
	}
	if !readingDate.IsZero() {
		readingDateString = readingDate.Format(util.DDMMYYYY)
	}
	if readingTime != nil {
		readingTimeHourString = readingTime.Format(util.HHMMSS)
	}

	obj6311 := TestDataDisplay{
		Header: Header{
			SentenceID:     "6311",
			SentenceLength: "00000",
			Receiver:       s.gdtExport.ReceiverName,
			Sender:         s.gdtExport.SenderName,
			SetType:        s.GetEncoderString(),
			Version:        string(s.gdtExport.GdtVersion),
		},
		PatientInfo: PatientInfo{
			PatientNumber: strconv.Itoa(int(s.patientProfile.PatientInfo.PatientNumber)),
			Prefix:        getPrefixPatient(s.patientProfile),
			LastName:      s.patientProfile.GetLastName(),
			FirstName:     s.patientProfile.GetFirstName(),
			BirthDate:     s.patientProfile.PatientInfo.PersonalInfo.DateOfBirth_DDMMYYYY(),
			Title:         s.patientProfile.PatientInfo.GetTitle(),
		},
		TreatmentDate:       treatmentDateString,
		TreatmentTime:       treatmentTimeHourString,
		DeviceSpecificField: util.GetPointerValue(s.gdtExport.DeviceMethodField),
		ReadingDate:         readingDateString,
		ReadingTime:         readingTimeHourString,
	}
	SetSentenceLength(&obj6311)

	return StructToByteArrayWithEncoder(obj6311, s.GetEncoder())
}
