package gdt

import (
	field_transfer "git.tutum.dev/medi/tutum/pkg/field_transfer"
)

type ObjectType string

const (
	ObjectTypeRootDataRequest  ObjectType = "RootDataRequest"
	ObjectTypeRootDataTransfer ObjectType = "RootDataTransfer"
	ObjectTypeNewTestRequest   ObjectType = "NewTestRequest"
	ObjectTypeTestDataTransfer ObjectType = "TestDataTransfer"
	ObjectTypeTestDataDisplay  ObjectType = "TestDataDisplay"
)

type Header struct {
	field_transfer.FieldAttr `field:"8000"`
	SentenceID               string `field:"8000"`
	SentenceLength           string `field:"8100"`
	ReceiverID               string `field:"8315"`
	SenderID                 string `field:"8316"`
	Receiver                 string `field:"8315"`
	Sender                   string `field:"8316"`
	SetType                  string `field:"9206"`
	Version                  string `field:"9218"`
	LabOrderId               string `field:"8310"`
	ResultId                 string `field:"8311"`
	Responsible              string `field:"0102"`
	Software                 string `field:"0103"`
	SoftwareVersion          string `field:"0132"`
}

type PatientInfo struct {
	field_transfer.FieldAttr `field:"3000"`
	PatientNumber            string `field:"3000"` // M: Patient Number / Label (0 if unavailable) = patient Number
	Prefix                   string `field:"3100"` // K: Prefix / Additional Name (e.g., "Lord")
	LastName                 string `field:"3101"` // K: Name of Patient
	FirstName                string `field:"3102"` // K: First Name of Patient
	BirthDate                string `field:"3103"` // K: Birth Date (DDMMYYYY)
	Title                    string `field:"3104"` // K: Patient Title
	InsuranceNumber          string `field:"3105"` // K: Patient Insurance Number
	Residence                string `field:"3106"` // K: Resid(PLZ ORT)
	Street                   string `field:"3107"` // K: Street and House Number
	InsuranceStatus          string `field:"3108"` // K: Insurance Status (1=Member, 3=Family Member, 5=Retired)
	Sex                      int    `field:"3110"` // K: Sex (1=Male, 2=female)
	Height                   int    `field:"3622"` // K: Height in cm
	Weight                   int    `field:"3623"` // K: Weight in kg
	FirstLanguage            string `field:"3628"` // K: First Language of Patient
}

type RootDataRequest struct {
	Header
	PatientInfo
}

type RootDataTransfer struct {
	Header
	PatientInfo
}

// 8402 1 Device and method specific field K Requested Test Type, e.g.. EKG01
// 8410 1 Test-ID K
type NewTestRequest struct {
	Header
	PatientInfo
	DeviceSpecificField string `field:"8402"` // K: Device and method specific field (e.g., Test Type, EKG01)
	TestID              string `field:"8410"` // K: Test-ID
}

type TestDataTransfer struct {
	Header
	PatientInfo
	DeviceSpecificField     string        `field:"8402"` // K: Device and method specific field (e.g., Test Type, EKG01)
	TreatmentDate           string        `field:"6200" validate:"regex=^(0[1-9]|[12][0-9]|3[01])(0[1-9]|1[0-2])\\d{4}$" description:"Day treatment data were saved (DDMMYYYY)"`
	TreatmentTime           string        `field:"6201" validate:"regex=^(0[0-9]|1[0-9]|2[0-3])([0-5][0-9])([0-5][0-9])$" description:"Time treatment data were saved (HHMMSS)"`
	CurrentDiagnosis        []string      `field:"6205"` // K: Current Diagnosis
	Results                 []string      `field:"6220"` // K: Results
	ThirdPartyResults       []string      `field:"6221"` // K: Third Party Results (e.g., automatically generated by the device)
	Comments                []string      `field:"6227"` // K: Comments
	FollowingLinesCount6228 int           `field:"6226"` // K: Number of following lines of the Identifier 6228
	ResultsTexts            []string      `field:"6228"`
	ArchiveFiles            []ArchiveFile `field:"6302"`
	NameCategories          []Category    `field:"6330"`
	Tests                   []Test        `field:"8410"`
	Signature               string        `field:"6330"`
}

type FreeText map[string]string

type Category struct {
	field_transfer.FieldAttr `field:"6330"`
	FreeText                 FreeText `decode:"map" field:"6332>6398"`
	Content                  NameCategoryChild
}

type NameCategoryChild struct {
	field_transfer.FieldAttr `field:"6331"`
	FreeText                 FreeText `decode:"map" field:"6333>6399"`
}

type ArchiveFile struct {
	FileName        string `field:"6302"`
	FileFormat      string `field:"6303"` // M: Description of file format (e.g., PDF)
	FileContentInfo string `field:"6304"` // M: Description of the file content
	FileReference   string `field:"6305"` // M: Uniform Resource Locator (WWW address or UNC Path)
}
type TestMaterialLabel struct {
	TestMaterialLabel         string   `field:"8430"`
	TestMaterialSpecification []string `field:"8431"`
	UnitForDataStream         string   `field:"8437"` // M: Unit(s) for Data Stream (e.g., Hz, dBA)
	DataStream                []string `field:"8438"` // K: Data Stream (e.g., (50,-30),(100,-20),...)
	TestStatus                string   `field:"8418"` // K: Test Status (B=reported, K=corrected/missing)
	ResultValue               string   `field:"8420"` // K: Result Value
	Unit                      string   `field:"8421"` // M: Unit for Result Value
	ReadingDate               string   `field:"8432"` // K: Reading Date (DDMMYYYY)
	ReadingTime               string   `field:"8439"` // K: Reading Time (HHMMSS)
	StandardValueText         string   `field:"8460"` // K: Standard Value Text
	LowerThreshold            string   `field:"8461"` // K: Lower Threshold Standard Value (transmitted in Unit 8421)
	UpperThreshold            string   `field:"8462"` // K: Upper Threshold Standard Value (transmitted in Unit 8421)
	Comments                  []string `field:"8470"` // K: Test Notes (n)
	ResultsText               []string `field:"8480"` // K: Results Text (n)
}

type Test struct {
	TestID            string `field:"8410"`
	TestName          string `field:"8411"` // K: Test Name
	TestMaterialID    string `field:"8428"` // K: Test Material ID
	TestMaterialIndex int    `field:"8429"` // K: Test Material Index
	TestMaterialLabel
}

type TestDataDisplay struct {
	Header
	PatientInfo
	TreatmentDate       string `field:"6200" validate:"regex=^(0[1-9]|[12][0-9]|3[01])(0[1-9]|1[0-2])\\d{4}$" description:"Day treatment data were saved (DDMMYYYY)"`
	TreatmentTime       string `field:"6201" validate:"regex=^(0[0-9]|1[0-9]|2[0-3])([0-5][0-9])([0-5][0-9])$" description:"Time treatment data were saved (HHMMSS)"`
	DeviceSpecificField string `field:"8402"` // K: Device and method specific field (e.g., Test Type, EKG01)
	ReadingDate         string `field:"8432"` // K: Reading Date (DDMMYYYY)
	ReadingTime         string `field:"8439"` // K: Reading Time (HHMMSS)
}
