package gdt

import (
	"bytes"
	"fmt"
	"os"
	"testing"
	"time"

	"git.tutum.dev/medi/tutum/ares/service/domains/api/document_setting_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	"git.tutum.dev/medi/tutum/pkg/field_transfer"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/stretchr/testify/require"
	"golang.org/x/text/encoding/charmap"
)

func Test_StructToByteArrayWithEncoder(t *testing.T) {
	sample := RootDataRequest{
		Header: Header{
			SentenceID:      "6301",
			SentenceLength:  "1",
			ReceiverID:      "1",
			SenderID:        "1",
			Receiver:        "1",
			Sender:          "1",
			SetType:         "1",
			Version:         "1",
			Responsible:     "1",
			Software:        "1",
			SoftwareVersion: "1",
		},
		PatientInfo: PatientInfo{
			PatientNumber:   "1",
			Prefix:          "1",
			LastName:        "1",
			FirstName:       "Hansjörg",
			BirthDate:       "1",
			Title:           "1",
			InsuranceNumber: "1",
			Residence:       "1",
			Street:          "1",
			InsuranceStatus: "1",
			Sex:             1,
			Height:          1,
			Weight:          1,
		},
	}
	result := StructToByteArrayWithEncoder(sample, charmap.ISO8859_15.NewEncoder())
	s := string(result)
	fmt.Printf("s: %v\n", s)
	require.NotEmpty(t, s)
}

func Test_GDTBuilder_Build6311(t *testing.T) {
	builder := &GDTBuilder{
		gdtExport: &document_setting_common.GdtExport{
			ReceiverName:      "Receiver",
			SenderName:        "Sender",
			GdtVersion:        "GDT_Version",
			DeviceMethodField: util.NewPointer("DeviceField"),
			TestId:            util.NewPointer("TestID"),
		},
		patientProfile: profile.PatientProfile{
			PatientInfo: &patient_profile_common.PatientInfo{
				PatientNumber: 123,
				PersonalInfo: patient_profile_common.PersonalInfo{
					Gender: "M",
					DateOfBirth: patient_profile_common.DateOfBirth{
						Date:  util.NewPointer[int32](28),
						Month: util.NewPointer[int32](2),
						Year:  util.NewPointer[int32](1919),
					},
				},
				AddressInfo: patient_profile_common.AddressInfo{
					Address: patient_profile_common.Address{
						Street:      util.NewString("street"),
						Number:      util.NewString("Number"),
						PostCode:    "PostCode",
						City:        util.NewString("City"),
						CountryCode: util.NewString("CountryCode"),
					},
				},
			},
		},
		garrioProVersion: "1.0",
	}
	result := builder.Build6311(time.Now(), time.Time{}, nil, nil)
	require.NotEmpty(t, result)
	fmt.Printf("result: %v\n", string(result))
}

func Test_Decode_TestDataTransfer(t *testing.T) {
	file, err := os.Open("./testdata/BGA.gdt")
	require.NoError(t, err)
	defer file.Close()
	reader := field_transfer.NewFieldReader(file)
	if err := reader.ReadField(); err != nil {
		panic(err)
	}

	var TestDataTransfer TestDataTransfer
	require.NoError(t, reader.Decode(&TestDataTransfer))

	// Test header fields
	require.Equal(t, "6310", TestDataTransfer.SentenceID)
	require.Equal(t, "01223", TestDataTransfer.SentenceLength)
	require.Equal(t, "Generic-", TestDataTransfer.ReceiverID)
	require.Equal(t, "RapidSer", TestDataTransfer.SenderID)
	require.Equal(t, "2", TestDataTransfer.SetType)
	require.Equal(t, "02.10", TestDataTransfer.Version)

	// Test device specific fields
	require.Equal(t, "ALLG00", TestDataTransfer.DeviceSpecificField)
	require.Equal(t, "10092024", TestDataTransfer.TreatmentDate)
	require.Equal(t, "000000", TestDataTransfer.TreatmentTime)

	// Test test data
	require.Len(t, TestDataTransfer.Tests, 20)

	// Check first test
	firstTest := TestDataTransfer.Tests[0]
	require.Equal(t, "mpH", firstTest.TestID)
	require.Equal(t, "mpH", firstTest.TestName)
	require.Equal(t, "7.399", firstTest.ResultValue)
	require.Equal(t, "", firstTest.Unit)

	// Check last test
	lastTest := TestDataTransfer.Tests[19]
	require.Equal(t, "cHct", lastTest.TestID)
	require.Equal(t, "cHct", lastTest.TestName)
	require.Equal(t, "50", lastTest.ResultValue)
	require.Equal(t, "%", lastTest.Unit)
}

func Test_StructToByteArrayWithEncoder_HasCRLF(t *testing.T) {
	sample := RootDataRequest{
		Header: Header{
			SentenceID:      "6301",
			SentenceLength:  "1",
			ReceiverID:      "1",
			SenderID:        "1",
			Receiver:        "1",
			Sender:          "1",
			SetType:         "1",
			Version:         "1",
			Responsible:     "1",
			Software:        "1",
			SoftwareVersion: "1",
		},
		PatientInfo: PatientInfo{
			PatientNumber:   "1",
			Prefix:          "1",
			LastName:        "1",
			FirstName:       "Hansjörg",
			BirthDate:       "1",
			Title:           "1",
			InsuranceNumber: "1",
			Residence:       "1",
			Street:          "1",
			InsuranceStatus: "1",
			Sex:             1,
			Height:          1,
			Weight:          1,
		},
	}
	result := StructToByteArrayWithEncoder(sample, charmap.ISO8859_15.NewEncoder())
	require.True(t, hasCRLF(result))
}

func hasCRLF(b []byte) bool {
	return bytes.Contains(b, []byte{'\r', '\n'})
}

func TestLengthOfTransferData(t *testing.T) {
	var testDataTransfer TestDataTransfer = TestDataTransfer{
		Header: Header{
			SentenceID:      "6310",
			SentenceLength:  "00000",
			ReceiverID:      "1",
			SenderID:        "1",
			Receiver:        "1",
			Sender:          "1",
			SetType:         "1",
			Version:         "1",
			Responsible:     "1",
			Software:        "1",
			SoftwareVersion: "1",
		},
	}
	SetSentenceLength(&testDataTransfer)
	require.Equal(t, "00117", testDataTransfer.SentenceLength)
}
