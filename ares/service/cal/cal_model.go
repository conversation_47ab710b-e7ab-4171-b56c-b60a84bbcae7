package cal_service

import (
	"net/http"

	catalog_sdav_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_sdav"

	"git.tutum.dev/medi/tutum/ares/service/domains/settings/settings_service"
	"github.com/google/uuid"
)

type STAFF_STATUS string

const (
	STAFF_STATUS_ACTIVE     STAFF_STATUS = "ACTIVE"
	STAFF_STATUS_DEACTIVATE STAFF_STATUS = "DEACTIVATE"
)

type CALService struct {
	BaseUrl               string
	netClient             *http.Client
	getEnableCALFeatureFn settings_service.GetEnableCALFeatureFn
	catalogSdavService    *catalog_sdav_service.CatalogSdavService
}

type CreateOrUpdateRoomRequest struct {
	Id             uuid.UUID `json:"id"`
	CareProviderId string    `json:"careProviderId"`
	Name           string    `json:"name"`
	StaffId        uuid.UUID `json:"staffId"`
	PracticeId     uuid.UUID `json:"practiceId"`
}

type DeleteRoomRequest struct {
	Id             uuid.UUID `json:"id"`
	CareProviderId string    `json:"careProviderId"`
}

type CreateOrUpdateStaffRequest struct {
	Id              *uuid.UUID `json:"id"`
	CareProviderId  string     `json:"careProviderId"`
	FirstName       string     `json:"firstName"`
	LastName        string     `json:"lastName"`
	Role            *string    `json:"role"`
	PracticeIds     []string   `json:"practiceIds"`
	Initial         string     `json:"initial"`
	AreaOfExpertise []string   `json:"areaOfExpertise"`
	PortalOrgId     string     `json:"portalOrgId"`
	PortalUserId    string     `json:"portalUserId"`
	Salutation      string     `json:"salutation"`
	Title           string     `json:"title"`
	AdditionalName  string     `json:"additionalName"`
	IntendWord      string     `json:"intendWord"`
	Lanr            string     `json:"lanr"`
}

type CreateOrUpdatePracticeRequest struct {
	Id             *uuid.UUID `json:"id"`
	CareProviderId string     `json:"careProviderId"`
	Name           string     `json:"name"`
	Bsnr           string     `json:"bsnr"`
	Street         string     `json:"street"`
	Number         string     `json:"number"`
	PostalCode     string     `json:"postalCode"`
	City           string     `json:"city"`
	Country        string     `json:"country"`
	Phone          string     `json:"phone"`
	Fax            string     `json:"fax"`
}

type DeletePracticeRequest struct {
	Id             uuid.UUID `json:"id"`
	CareProviderId string    `json:"careProviderId"`
}

type DeletePatientRequest struct {
	Id             uuid.UUID `json:"id"`
	CareProviderId string    `json:"careProviderId"`
}

type UpdateStaffStatusRequest struct {
	Id             string       `json:"id"`
	CareProviderId string       `json:"careProviderId"`
	Status         STAFF_STATUS `json:"status"`
}
