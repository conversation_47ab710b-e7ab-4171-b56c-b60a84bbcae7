package himi

import (
	"fmt"

	"emperror.dev/errors"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/timeline"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common/patientfile"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/himi"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/himi/himi_model"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/himi/himi_model/prescription"
	patient_encounter_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/timeline_service"
	"git.tutum.dev/medi/tutum/ares/service/himi/api"
	"git.tutum.dev/medi/tutum/ares/service/himi/common"
	util_pkg "git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"gitlab.com/silenteer-oss/hestia/infrastructure/util"
	"gitlab.com/silenteer-oss/titan"
)

type HimiService struct {
	himiRepo          *himi.HimiRepo
	himiPrescribeRepo prescription.HimiPrescriptionDefaultRepository
	timelineService   *timeline_service.TimelineService[patient_encounter_repo.EncounterHimiPrescription]
	himiVersion       string
}

func NewHimiService(
	himiRepo *himi.HimiRepo,
	himiVersion string,
) api.HimiApp {
	timelineService, err := timeline_service.TimelineServiceHimiPrescriptionMod.SafeResolve()
	if err != nil {
		panic(fmt.Errorf("failed to get timeline service: %w", err))
	}
	return &HimiService{
		himiRepo:          himiRepo,
		himiVersion:       himiVersion,
		himiPrescribeRepo: prescription.NewHimiPrescriptionDefaultRepository(),
		timelineService:   timelineService,
	}
}

// GetPrescribe implements api.HimiApp
func (srv *HimiService) GetPrescribe(ctx *titan.Context, request api.GetPrescribeRequest) (*api.GetPrescribeResponse, error) {
	result, err := srv.timelineService.GetPrescribeHimi(ctx, request.PrescriptionId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to find prescribe")
	}
	if result == nil {
		return nil, nil
	}
	// map to response
	himiForm := patientfile.HimiForm{
		Id:                 result.Id,
		HimiPrescriptionId: result.Payload.HimiPrescriptionId,
		SortOrder:          0,
		FormInfo:           result.Payload.FormInfo,
		AdditionalForm:     result.Payload.AdditionalForm,
		PatientId:          &result.PatientId,
		EncounterId:        nil,
		AuditLog:           result.Payload.AuditLog,
		PrintDate:          result.Payload.PrintDate,
		PrescribeDate:      result.Payload.PrescribeDate,
		CreatedDate:        result.Payload.CreatedDate,
		DiagnoseCode:       result.Payload.DiagnoseCode,
		SecondaryDiagnore:  result.Payload.SecondaryDiagnore,
	}
	if len(result.ScheinIds) > 0 {
		himiForm.ScheinId = util_pkg.NewPointer(result.ScheinIds[0])
	}
	return &api.GetPrescribeResponse{
		Prescription: himiForm,
	}, nil
}
func (srv *HimiService) SearchControllableHimi(ctx *titan.Context, request api.SearchControllableHimiRequest) (*api.SearchControllableHimiResponse, error) {
	result, err := srv.himiRepo.SearchControllableHimi(ctx, himi_model.SearchControllableHimiRequest{
		Base:    himi_model.Base(request.Base),
		ArtId:   request.ArtId,
		Version: "q2_25_2",
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var response api.SearchControllableHimiResponse
	if err := copier.Copy(&response, result); err != nil {
		return nil, err
	}
	return &response, nil
}

func (srv *HimiService) SearchUnterByGruppeOrt(ctx *titan.Context, request api.SearchUnterByGruppeOrtRequest) (*api.SearchUnterByGruppeOrtResponse, error) {
	result, err := srv.himiRepo.SearchUnterByGruppeOrt(ctx, himi_model.SearchUnterByGruppeOrtRequest{
		GruppeId: request.GruppeId,
		OrtId:    request.OrtId,
		Version:  srv.himiVersion,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var response api.SearchUnterByGruppeOrtResponse
	if err := copier.Copy(&response, result); err != nil {
		return nil, err
	}
	return &response, nil
}

func (srv *HimiService) SearchOrtByGruppe(ctx *titan.Context, request api.SearchOrtByGruppeRequest) (*api.SearchOrtByGruppeResponse, error) {
	result, err := srv.himiRepo.SearchOrtByGruppe(ctx, himi_model.SearchOrtByGruppeRequest{
		GruppeId: request.GruppeId,
		Version:  srv.himiVersion,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var response api.SearchOrtByGruppeResponse
	if err := copier.Copy(&response, result); err != nil {
		return nil, errors.WithStack(err)
	}
	return &response, nil
}

func (srv *HimiService) SearchGruppe(ctx *titan.Context, request api.SearchGruppeRequest) (*api.SearchGruppeResponse, error) {
	results, err := srv.himiRepo.SearchGruppe(ctx, himi_model.SearchGruppeRequest{
		Id:          request.Id,
		Bezeichnung: request.Bezeichnung,
		Version:     srv.himiVersion,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var gruppes []common.Gruppe
	if err := copier.Copy(&gruppes, results); err != nil {
		return nil, errors.WithStack(err)
	}
	return &api.SearchGruppeResponse{
		Gruppes: gruppes,
		Version: srv.himiVersion,
	}, nil
}

func (srv *HimiService) SearchOrt(ctx *titan.Context, request api.SearchOrtRequest) (*api.SearchOrtResponse, error) {
	results, err := srv.himiRepo.SearchOrt(ctx, himi_model.SearchOrtRequest{
		Id:          request.Id,
		Bezeichnung: request.Bezeichnung,
		Version:     srv.himiVersion,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var orts []common.Ort
	if err := copier.Copy(&orts, results); err != nil {
		return nil, errors.WithStack(err)
	}
	return &api.SearchOrtResponse{
		Orts:    orts,
		Version: srv.himiVersion,
	}, nil
}

func (srv *HimiService) SearchArt(ctx *titan.Context, request api.SearchArtRequest) (*api.SearchArtResponse, error) {
	searchFreetextRequest := himi_model.SearchArtRequest{
		SearchValue:   request.SearchValue,
		Version:       srv.himiVersion,
		SearchArtType: himi_model.SearchArtType(request.SearchArtType),
	}
	if request.SearchBaseFreeText != nil {
		searchFreetextRequest.SearchBaseFreeText = &himi_model.SearchBaseFreeText{
			Gruppe: request.SearchBaseFreeText.Gruppe,
			Ort:    request.SearchBaseFreeText.Ort,
			Unter:  request.SearchBaseFreeText.Unter,
			ArtId:  request.SearchBaseFreeText.ArtId,
		}
	}

	results, err := srv.himiRepo.SearchArt(ctx, searchFreetextRequest)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var orts []common.Art
	if err := copier.Copy(&orts, results); err != nil {
		return nil, errors.WithStack(err)
	}
	return &api.SearchArtResponse{
		Arts:    orts,
		Version: srv.himiVersion,
	}, nil
}

func (srv *HimiService) SearchHimiMatchingTable(ctx *titan.Context, request api.SearchHimiMatchingTableRequest) (*api.SearchHimiMatchingTableResponse, error) {
	result, err := srv.himiRepo.SearchHimiMatchingTable(ctx, himi_model.SearchHimiMatchingTableRequest{
		ContractId: request.ContractId,
		Version:    srv.himiVersion,
		Base:       himi_model.Base(request.Base),
		ArtId:      request.ArtId,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if result == nil {
		return nil, nil
	}
	var searchhimimatchingtableresponse api.SearchHimiMatchingTableResponse
	if err := copier.Copy(&searchhimimatchingtableresponse, result); err != nil {
		return nil, errors.WithStack(err)
	}
	return &searchhimimatchingtableresponse, nil
}

func (srv *HimiService) SearchProductByBaseAndArt(ctx *titan.Context, request api.SearchProductByBaseAndArtRequest) (*api.SearchProductByBaseAndArtResponse, error) {
	result, err := srv.himiRepo.SearchProductByBaseAndArt(ctx, himi_model.SearchProductByBaseAndArtRequest{
		Gruppe:  request.Gruppe,
		Ort:     request.Ort,
		Unter:   request.Unter,
		ArtId:   request.ArtId,
		Version: srv.himiVersion,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var response api.SearchProductByBaseAndArtResponse
	if err := copier.Copy(&response, result); err != nil {
		return nil, err
	}
	return &response, nil
}

func (srv *HimiService) Prescribe(ctx *titan.Context, request api.PrescribeRequest) (*api.PrescribeResponse, error) {
	var newHimiPrescription prescription.HimiPrescription
	if err := copier.Copy(&newHimiPrescription, request); err != nil {
		return nil, errors.WithMessage(err, "could not copy request Prescribe")
	}
	newId := uuid.New()
	formInforId := uuid.New()
	additionalFormId := uuid.New()

	newHimiPrescription.Id = &newId
	newHimiPrescription.FormInfo.Id = &formInforId
	newHimiPrescription.CreatedDate = util.NowUnixMillis(ctx)
	if newHimiPrescription.AdditionalForm != nil {
		newHimiPrescription.AdditionalForm.Id = &additionalFormId
	}
	_, err := srv.himiPrescribeRepo.Create(ctx, newHimiPrescription)
	if err != nil {
		return nil, errors.WithMessage(err, "could not create Prescribe")
	}

	_, err = srv.timelineService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter_repo.EncounterHimiPrescription]{
		Id:                util_pkg.NewPointer(newId),
		ContractId:        request.ContractId,
		TreatmentDoctorId: request.TreatmentDoctorId,
		BillingDoctorId:   &request.TreatmentDoctorId,
		PatientId:         request.PatientId,
		EncounterCase:     util_pkg.NewPointer(patient_encounter_repo.EncounterCase(request.EncounterCase)),
		Payload: patient_encounter_repo.EncounterHimiPrescription{
			Id:                 &newId,
			HimiPrescriptionId: &newId,
			FormInfo:           &request.FormInfo,
			AdditionalForm:     request.AdditionalForm,
			PatientId:          &request.PatientId,
			PrintDate:          request.PrintDate,
			PrescribeDate:      request.PrescribeDate,
			CreatedDate:        util.NowUnixMillis(ctx),
			DiagnoseCode:       request.DiagnoseCode,
			SecondaryDiagnore:  request.SecondaryDiagnore,
		},
		ScheinIds:        []uuid.UUID{request.ScheinId},
		AssignedToBsnrId: request.AssignedToBsnrId,
	},
	)
	if err != nil {
		return nil, errors.WithMessage(err, "could not create timeline")
	}
	if request.TimelineId != nil {
		err = srv.timelineService.Remove(ctx, *request.TimelineId, false)
		if err != nil {
			return nil, errors.WithMessage(err, "could not remove timeline")
		}
	}
	return &api.PrescribeResponse{
		PrescriptionId: newId,
	}, nil
}

func (srv *HimiService) OnTimelineHardDelete(ctx *titan.Context, request timeline.EventTimelineHardRemove) error {
	_, err := srv.himiPrescribeRepo.DeleteById(ctx, util_pkg.GetPointerValue(request.TimelineModel.Id))
	return err
}
