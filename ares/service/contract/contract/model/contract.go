package model

import (
	"fmt"
	"strconv"
	"time"

	"emperror.dev/errors"
	enrollment_common "git.tutum.dev/medi/tutum/ares/service/domains/enrollment/common"
	okv_util "git.tutum.dev/medi/tutum/ares/service/domains/pkg/okv"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	pkg_utils "git.tutum.dev/medi/tutum/pkg/util"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"gitlab.com/silenteer-oss/titan"
)

// Contract is the detail information from a specific contract file like AOK_BY_HZV_S15_VSW_Selektivvertragsdefinition_Q4-22-1.xml
type Contract struct {
	Id                 ContractId
	ContractDefinition Selektivvertragsdefinition
}

type ICheckExistAnforderung struct {
	ComplianceIds []string
	CheckTime     int64
	IkGroup       *int32
	KvRegion      *string
	IkNumber      *int32
	// TODO: Data doesn't exist
	// IkGroupPrimary *int32
}

type ContractAttachmentId struct {
	Id           string `json:"id"`
	AttachmentID string `json:"anlageId"`
}

func (contract *Contract) OriginalId() ContractId {
	return contract.ContractDefinition.Vertrag.ID
}

func (contract *Contract) GetKey() string {
	if contractType := contract.GetContractType(); contractType != nil {
		return contractType.KENNUNG
	}
	return ""
}

func (contract *Contract) Insurance() *Kostentraeger {
	if contract == nil {
		return nil
	}
	return contract.ContractDefinition.Kostentraeger
}

func (contract *Contract) HasValidAtLeastUkv(ukvs []string, checkTime time.Time) bool {
	okvs := slice.Map(ukvs, func(ukv string) string {
		return pkg_utils.GetOKVByUKV(ukv)
	})
	return slice.FindOne(contract.ContractDefinition.Kvregionen.Kvregion, func(kv Kvregion) bool {
		return slice.Contains(okvs, kv.OKV) &&
			pkg_utils.InRangeNums(
				pkg_utils.ConvertTimeToMiliSecond(checkTime),
				pkg_utils.ConvertTimeToMiliSecond(kv.GueltigAbReferenzdatum.Time),
				function.Do(func() int64 {
					if kv.GueltigBisReferenzdatum == nil {
						return pkg_utils.ConvertTimeToMiliSecond(checkTime) + 1
					}
					return pkg_utils.ConvertTimeToMiliSecond(kv.GueltigBisReferenzdatum.Time)
				}),
			)
	}) != nil
}

func (contract *Contract) MainOfficeInsurances() *Kostentraeger {
	if contract.ContractDefinition.Kostentraeger != nil {
		return contract.ContractDefinition.Kostentraeger
	}
	return nil
}

func (contract *Contract) IsFavContract() bool {
	return string(contract.ContractDefinition.Vertrag.TYP) == string(ContractType_SpecialistCare)
}
func (contract *Contract) IsHzvContract() bool {
	return string(contract.ContractDefinition.Vertrag.TYP) == string(ContractType_HouseDoctorCare)
}
func (contract *Contract) GetPatientParticipatingContractGroups() *PatiententeilnahmeVertragsgruppen {
	return contract.ContractDefinition.PatiententeilnahmeVertragsgruppen
}

func (contract *Contract) GetPatientParticipationRequirement() *TeilnahmevoraussetzungPatient {
	if contract.ContractDefinition.Vertrag != nil {
		return contract.ContractDefinition.Vertrag.TeilnahmevoraussetzungPatient
	}
	return nil
}

func (contract *Contract) GetFavPatientParticipationContractGroup() []ContractId {
	if !contract.IsFavContract() {
		return []ContractId{}
	}
	groups := contract.GetPatientParticipatingContractGroups()
	if groups == nil {
		return []ContractId{contract.Id}
	}
	var result []ContractId
	for _, group := range groups.PatiententeilnahmeVertragsgruppe {
		for _, patientParticipationContract := range group.PatiententeilnahmeVertrag {
			id := patientParticipationContract.ID
			result = append(result, id)
		}
	}
	return result
}

func (contract *Contract) GetPrerequisiteForParticipation() *TeilnahmeAnVertrag {
	if contract.ContractDefinition.Vertrag != nil &&
		contract.ContractDefinition.Vertrag.TeilnahmevoraussetzungPatient != nil {
		return contract.ContractDefinition.Vertrag.TeilnahmevoraussetzungPatient.TeilnahmeAnVertrag
	}
	return nil
}

func (contract *Contract) GetContractType() *VertragTyp {
	return contract.ContractDefinition.Vertrag
}

func (contract *Contract) GetInsureRegistrationInfo(referenceDate int64, okvs []string) []MetainformationTyp {
	metaInformation := contract.ContractDefinition.Vertrag.Metainformationen
	if metaInformation == nil || len(metaInformation.Metainformation) == 0 {
		return []MetainformationTyp{}
	}
	metaList := make([]MetainformationTyp, 0, len(metaInformation.Metainformation))
	for _, meta := range metaInformation.Metainformation {
		condition := meta.Bedingungen
		if meta.U == MetaInformationType_InsureRegistration &&
			meta.IsRefDateValid(referenceDate) &&
			(condition == nil || condition.IsKvRegionValid(okvs, referenceDate)) {
			metaList = append(metaList, meta)
		}
	}
	return metaList
}

func (contract *Contract) IsEligibleToEnrolOffline(referenceDate int64, okvs []string) bool {
	return !contract.IsEligibleToEnrolOnline(referenceDate, okvs)
}

func (contract *Contract) IsEligibleToEnrolOnline(referenceDate int64, okvs []string) bool {
	for _, meta := range contract.GetInsureRegistrationInfo(referenceDate, okvs) {
		if meta.IstOnline {
			return true
		}
	}
	return false
}

func (contract *Contract) HasFunction(functionId FunctionId, timeCheckMs int64, okvs []string, ikNumber int32) (bool, error) {
	result, err := contract.HasAnyFunctions(contract.GetContractSoftwareFunction(), []FunctionId{functionId}, timeCheckMs, okvs, ikNumber)
	if err != nil {
		return false, errors.WithStack(err)
	}
	return result, nil
}

func (contract *Contract) HasAnyFunctions(contractFunctions []AnforderungTyp, functionIds []FunctionId, timeCheckMs int64, okvs []string, ikNumber int32) (bool, error) {
	if len(contractFunctions) == 0 {
		return false, nil
	}
	checkFunctionMap := map[string]struct{}{}
	for _, functionId := range functionIds {
		checkFunctionMap[string(functionId)] = struct{}{}
	}
	mainInsurance := contract.Insurance().GetInsuranceOffice(ikNumber, timeCheckMs, okvs)
	if mainInsurance == nil {
		return false, nil
	}

	for _, contractFunction := range contractFunctions {
		if _, ok := checkFunctionMap[contractFunction.ID]; ok {
			// Check valid
			isValid := contractFunction.Bedingungen.IsContractFunctionConditionsValid(okvs, timeCheckMs, mainInsurance.IkgruppeRef, ikNumber)
			if isValid {
				return true, nil
			}
		}
	}
	return false, nil
}

func (contract *Contract) GetContractSoftwareFunction() []AnforderungTyp {
	if contract.ContractDefinition.Vertrag == nil {
		return nil
	}
	return contract.ContractDefinition.Vertrag.Anforderungen.Anforderung
}

func (contract *Contract) GetModuleContractSoftwareFunction() []AnforderungTyp {
	if contract.ContractDefinition.Modulvertrag == nil {
		return nil
	}
	if len(contract.ContractDefinition.Modulvertrag) == 0 {
		return nil
	}

	return contract.ContractDefinition.Modulvertrag[0].Anforderungen.Anforderung
}

type GetFilesModel struct {
	Attachment Anlage
	Files      []Anlageobjekt
}

func GetOriginalFromDefinition(contractDefinition Selektivvertragsdefinition) ContractId {
	return contractDefinition.Vertrag.ID
}

func (contract *Contract) GetChargeSystems() []HonoraranlageTyp {
	if contract.ContractDefinition.Vertrag != nil && contract.ContractDefinition.Vertrag.Honoraranlagen != nil {
		if contract.Id == AWH_01 {
			return contract.ContractDefinition.Vertrag.Honoraranlagen.Honoraranlage
		}
		chargeSystems := contract.ContractDefinition.Vertrag.Honoraranlagen.Honoraranlage
		for i := range chargeSystems {
			chargeSystems[i].Bezeichnung = contract.ContractDefinition.Vertrag.NAME
		}
		return chargeSystems
	}
	return []HonoraranlageTyp{}
}

func (contract *Contract) GetEffectiveChargeSystem(
	chargeSystemId string,
) *HonoraranlageTyp {
	chargeSystems := contract.ContractDefinition.Vertrag.Honoraranlagen.Honoraranlage

	for _, chargeSystem := range chargeSystems {
		if chargeSystem.HonoraranlageIdentifikator == chargeSystemId {
			tmp := chargeSystem
			return &tmp
		}
	}

	return nil
}

func (contract *Contract) GetModuleChargeSystems() []HonoraranlageTyp {
	if len(contract.ContractDefinition.Modulvertrag) == 0 {
		return nil
	}
	var moduleChargeSystems []HonoraranlageTyp
	for _, module := range contract.ContractDefinition.Modulvertrag {
		if module.Honoraranlagen != nil {
			moduleChargeSystems = append(moduleChargeSystems, module.Honoraranlagen.Honoraranlage...)
		}
	}
	return moduleChargeSystems
}

func (contract *Contract) GetContractInfomationUrl() string {
	if contract.ContractDefinition.Vertrag == nil && len(contract.ContractDefinition.Vertrag.Anlagen.Anlage) == 0 {
		return ""
	}
	for _, anlage := range contract.ContractDefinition.Vertrag.Anlagen.Anlage {
		if anlage.Name == "Vertragstext" && anlage.Anlageobjekt != nil && len(anlage.Anlageobjekt) > 0 {
			return anlage.Anlageobjekt[0].URL
		}
	}
	return ""
}

func (contractRaw *Contract) getPrintContract(
	contract *VertragTyp,
	checkTimeMillis int64,
	okvs []string,
	ikNumber int32,
	aka *AKA,
	documentType AttachmentType,
) ([]PrintContract, error) {
	if aka == nil {
		return nil, nil
	}
	if contract == nil {
		return nil, nil
	}
	mainInsurance := contractRaw.Insurance().GetInsuranceOffice(ikNumber, checkTimeMillis, okvs)
	if mainInsurance == nil {
		return nil, errors.New("not found office insurance")
	}

	var printContracts []PrintContract

	for _, attachment := range contract.Anlagen.Anlage {
		printContract := PrintContract{
			Id: attachment.ID,
		}
		for _, attachmentObject := range attachment.Anlageobjekt {
			if attachmentObject.Bedingungen != nil { // Conditions
				isFound := attachmentObject.Bedingungen.IsKvRegionValid(okvs, checkTimeMillis) &&
					attachmentObject.Bedingungen.IsIkNumberValid(ikNumber, checkTimeMillis) &&
					attachmentObject.Bedingungen.IsIkGroupValid(checkTimeMillis, mainInsurance.IkgruppeRef)
				if !isFound {
					continue
				}
			}

			attachment := aka.GetFiles(cast.ToInt16(attachment.ID), pkg_utils.NewPointer(cast.ToInt16(attachmentObject.ID)), documentType)
			if attachment == nil {
				continue
			}
			printContract.Description = attachment.Attachment.Titel
			files := slice.Filter(attachment.Files, func(t Anlageobjekt) bool {
				return t.Freigabe == FileSharingType_Public
			})
			fileType := PrintContractFileType_form
			switch documentType {
			case AttachmentType_Document:
				fileType = PrintContractFileType_public_document
			case AttachmentType_ContractTexts:
				fileType = PrintContractFileType_public_contract_text
			default:
				fileType = PrintContractFileType_form
			}

			printContract.Files = append(printContract.Files, slice.Map(files, func(t Anlageobjekt) PrintContractFile {
				return PrintContractFile{
					Id:          t.AnlageobjektID,
					Path:        fmt.Sprintf("Anlagen/%s/%s", attachment.Attachment.Typ, t.Datei),
					PrintStyles: attachment.Attachment.GetPrintStyles(),
					FileName:    t.Datei,
					FileType:    fileType,
					Title:       t.Titel,
				}
			})...)
		}

		if len(printContract.Files) == 0 {
			continue
		}
		printContracts = append(printContracts, printContract)
	}
	return printContracts, nil
}

func getAttachmentObjectsFromAka(aka *AKA, validAttachmentFiles []AnlageobjektTyp, attachmentId string) []Anlageobjekt {
	if aka == nil || len(validAttachmentFiles) == 0 {
		return nil
	}
	detailAttachmentObjectFiles := []Anlageobjekt{}
	for _, attachmentObject := range validAttachmentFiles {
		detailAttachmentFile := aka.GetDetailAttachmentFile(cast.ToInt16(attachmentId), cast.ToInt16(attachmentObject.ID))
		if detailAttachmentFile != nil {
			detailAttachmentObjectFiles = append(detailAttachmentObjectFiles, *detailAttachmentFile)
		}
	}
	return detailAttachmentObjectFiles
}

func (contractRaw *Contract) GetValidAttachments(
	checkTimeMillis int64,
	okvs []string,
	ikNumber int32,
	aka *AKA,
) ([]Anlage, error) {
	if aka == nil {
		return nil, errors.New("AKA is not provided")
	}

	contract := contractRaw.ContractDefinition.Vertrag
	moduleContracts := contractRaw.ContractDefinition.Modulvertrag
	mainInsurance := contractRaw.Insurance().GetInsuranceOffice(ikNumber, pkg_utils.NowUnixMillis(nil), okvs)
	if mainInsurance == nil {
		return nil, errors.New("cannot get mainInsurance")
	}

	attachmentIDsOfContract := slice.Map(contract.Anlagen.Anlage, func(a AnlageTyp) int16 { return cast.ToInt16(a.ID) })
	attachmentIDsOfModuleContract := slice.Map(moduleContracts, func(moduleContract VertragTyp) []int16 {
		return slice.Map(moduleContract.Anlagen.Anlage, func(a AnlageTyp) int16 { return cast.ToInt16(a.ID) })
	})
	for _, attachmentIds := range attachmentIDsOfModuleContract {
		attachmentIDsOfContract = append(attachmentIDsOfContract, attachmentIds...)
	}
	allDetailAttachmentsOfContract := aka.GetDetailAttachments(attachmentIDsOfContract)
	mapDetail := lo.SliceToMap(allDetailAttachmentsOfContract, func(t Anlage) (int16, *Anlage) {
		return t.AnlageID, pkg_utils.NewPointer(t)
	})

	for _, attachment := range contract.Anlagen.Anlage {
		// filter out invalid attachments
		validAttachmentFiles := slice.Filter(attachment.Anlageobjekt, func(attachmentObject AnlageobjektTyp) bool {
			if attachmentObject.Bedingungen == nil { // Conditions
				return true
			}
			isValidFile := attachmentObject.Bedingungen.IsKvRegionValid(okvs, checkTimeMillis) &&
				attachmentObject.Bedingungen.IsIkNumberValid(ikNumber, checkTimeMillis) &&
				attachmentObject.Bedingungen.IsIkGroupValid(checkTimeMillis, mainInsurance.IkgruppeRef)
			return isValidFile
		})

		detailAttachmentObjectFiles := getAttachmentObjectsFromAka(aka, validAttachmentFiles, attachment.ID)
		if len(detailAttachmentObjectFiles) == 0 {
			continue
		}
		detailAttachment := mapDetail[cast.ToInt16(attachment.ID)]
		// update valid object file to result
		detailAttachment.Anlageobjekt = detailAttachmentObjectFiles
	}
	result := lo.MapToSlice(mapDetail, func(_ int16, v *Anlage) Anlage {
		return *v
	})
	return result, nil
}

func (contractRaw *Contract) GetAllAttachments(aka *AKA) ([]Anlage, error) {
	if aka == nil {
		return nil, errors.New("AKA is not provided")
	}

	contract := contractRaw.ContractDefinition.Vertrag

	attachmentIDsOfContract := slice.Map(contract.Anlagen.Anlage, func(a AnlageTyp) int16 { return cast.ToInt16(a.ID) })
	allDetailAttachmentsOfContract := aka.GetDetailAttachments(attachmentIDsOfContract)
	mapDetail := lo.SliceToMap(allDetailAttachmentsOfContract, func(t Anlage) (int16, *Anlage) {
		return t.AnlageID, pkg_utils.NewPointer(t)
	})

	for _, attachment := range contract.Anlagen.Anlage {
		// filter out invalid attachments
		validAttachmentFiles := attachment.Anlageobjekt
		detailAttachmentObjectFiles := getAttachmentObjectsFromAka(aka, validAttachmentFiles, attachment.ID)
		if len(detailAttachmentObjectFiles) == 0 {
			continue
		}
		detailAttachment := mapDetail[cast.ToInt16(attachment.ID)]
		// update valid object file to result
		detailAttachment.Anlageobjekt = detailAttachmentObjectFiles
	}
	result := lo.MapToSlice(mapDetail, func(_ int16, v *Anlage) Anlage {
		return *v
	})
	return result, nil
}

// GetAllAttachments1 filters and returns all valid attachments for a given AKA based on specific conditions.
func (cd *Contract) GetAllAttachmentsByCondition(
	aka *AKA,
	checkTime int64,
	okvs []string,
	ikNumber int32,
) ([]Anlage, error) {
	if aka == nil {
		return nil, errors.New("AKA is not provided")
	}

	mainInsurance := cd.Insurance().GetInsuranceOffice(ikNumber, checkTime, okvs)
	if mainInsurance == nil {
		return nil, nil
	}

	contract := cd.ContractDefinition.Vertrag
	attachmentIDs := slice.Map(contract.Anlagen.Anlage, func(a AnlageTyp) int16 {
		return cast.ToInt16(a.ID)
	})
	attachmentsAka := slice.Filter(aka.Anlagen.Anlage, func(aa Anlage) bool {
		return slice.Contains(attachmentIDs, aa.AnlageID)
	})
	mapAttachmentsAka := lo.SliceToMap(attachmentsAka, func(t Anlage) (int16, *Anlage) {
		return t.AnlageID, pkg_utils.NewPointer(t)
	})

	// filter & update valid attachmentObjects
	for _, attachment := range contract.Anlagen.Anlage {
		validAttachmentFiles := slice.Filter(attachment.Anlageobjekt, func(ao AnlageobjektTyp) bool {
			if ao.Bedingungen == nil {
				return true
			}

			return ao.Bedingungen.IsKvRegionValid(okvs, checkTime) &&
				ao.Bedingungen.IsIkNumberValid(ikNumber, checkTime) &&
				ao.Bedingungen.IsIkGroupValid(checkTime, mainInsurance.IkgruppeRef)
		})
		attachmentObjectsFromAka := getAttachmentObjectsFromAka(aka, validAttachmentFiles, attachment.ID)
		attachmentAka := mapAttachmentsAka[cast.ToInt16(attachment.ID)]
		attachmentAka.Anlageobjekt = attachmentObjectsFromAka
	}
	result := lo.MapToSlice(mapAttachmentsAka, func(_ int16, v *Anlage) Anlage {
		return *v
	})
	return result, nil
}

func (contractFile *Contract) GetForms(contract *VertragTyp, checkTimeMillis int64, okvs []string, ikNumber int32, aka *AKA) ([]PrintContract, error) {
	return contractFile.getPrintContract(contract, checkTimeMillis, okvs, ikNumber, aka, AttachmentType_Form)
}

func (*Contract) GetIcdCsvFiles(contract *VertragTyp, aka *AKA) []Anlageobjekt {
	result := []Anlageobjekt{}
	for _, attachment := range contract.Anlagen.Anlage {
		for _, attachmentObject := range attachment.Anlageobjekt {
			attachmentFile := aka.GetDetailAttachmentFile(cast.ToInt16(attachment.ID), cast.ToInt16(attachmentObject.ID))
			if attachmentFile != nil && attachmentFile.Typ == string(AttachmentObjectType_Diagnoseliste) {
				result = append(result, *attachmentFile)
			}
		}
	}
	return result
}

func (contractFile *Contract) GetDocuments(contract *VertragTyp, checkTimeMillis int64, okvs []string, ikNumber int32, aka *AKA) ([]PrintContract, error) {
	results := []PrintContract{}

	docs, err := contractFile.getPrintContract(contract, checkTimeMillis, okvs, ikNumber, aka, AttachmentType_Document)
	if err != nil {
		return results, err
	}
	results = append(results, docs...)

	contractTexts, err := contractFile.getPrintContract(contract, checkTimeMillis, okvs, ikNumber, aka, AttachmentType_ContractTexts)
	if err != nil {
		return results, err
	}
	results = append(results, contractTexts...)
	return results, nil
}

func (chargeSystem *HonoraranlageTyp) GetServiceDefinition(
	serviceCode string,
	serviceDate int64,
) (*LeistungTyp, error) {
	if !(chargeSystem != nil && chargeSystem.Leistungen != nil) {
		return nil, nil
	}
	services := chargeSystem.Leistungen.Leistung

	var foundService *LeistungTyp

	for _, service := range services {
		if serviceCode == service.Ziffer {
			tmp := service
			foundService = &tmp
			break
		}
	}

	if foundService == nil {
		return nil, errors.Errorf("service code %s not found", serviceCode)
	}

	if foundService.Zifferart == ZifferartTyp_BLANKOZIFFER {
		return foundService, nil
	}

	if !foundService.IsRefDateValid(serviceDate) {
		return nil, errors.Errorf("Service code %s no longer valid", serviceCode)
	}

	return foundService, nil
}

func (contract *Contract) GetDiseaseDescriptionFromCode(
	ctx *titan.Context,
	diseaseGroup string,
	year int32,
	quarter int32,
) (*string, error) {
	if contract.ContractDefinition.Krankheitsbilder == nil {
		return nil, errors.Errorf("Can not look up for disease description in the contract: %s", contract.Id)
	}

	from, to, err := getYearQuarterTimeRange(ctx, year, quarter)

	if err != nil {
		return nil, err
	}

	for _, record := range contract.ContractDefinition.Krankheitsbilder.Krankheitsbild {
		if record.KrankheitsbildCode == diseaseGroup &&
			record.IsRefDateValid(from) &&
			record.IsRefDateValid(to) {
			return &record.Bezeichnung, nil
		}
	}

	return nil, errors.Errorf("Disease code [%s] cannot be found in the contract %s", diseaseGroup, contract.Id)
}

func getYearQuarterTimeRange(ctx *titan.Context, year, quarter int32) (from, to int64, err error) {
	if quarter == 1 {
		from := time.Date(int(year), time.January, 1, 0, 0, 0, 0, pkg_utils.Now(ctx).Location()).Unix()
		to := time.Date(int(year), time.April, 0, 0, 0, 0, 0, pkg_utils.Now(ctx).Location()).Unix()
		return from * 1000, to * 1000, nil
	}

	if quarter == 2 {
		from := time.Date(int(year), time.April, 1, 0, 0, 0, 0, pkg_utils.Now(ctx).Location()).Unix()
		to := time.Date(int(year), time.July, 0, 0, 0, 0, 0, pkg_utils.Now(ctx).Location()).Unix()
		return from * 1000, to * 1000, nil
	}

	if quarter == 3 {
		from := time.Date(int(year), time.July, 1, 0, 0, 0, 0, pkg_utils.Now(ctx).Location()).Unix()
		to := time.Date(int(year), time.September, 0, 0, 0, 0, 0, pkg_utils.Now(ctx).Location()).Unix()
		return from * 1000, to * 1000, nil
	}

	if quarter == 4 {
		from := time.Date(int(year), time.September, 1, 0, 0, 0, 0, pkg_utils.Now(ctx).Location()).Unix()
		to := time.Date(int(year)+1, time.January, 0, 0, 0, 0, 0, pkg_utils.Now(ctx).Location()).Unix()
		return from * 1000, to * 1000, nil
	}

	return 0, 0, errors.Errorf("Invalid quarter: %d", quarter)
}

func (contract *Contract) DoesSupportAMM(referenceDate int64) bool {
	return contract.doesSupportMetaInformationType(MetaInformationType_MedicineModule, referenceDate)
}

func (contract *Contract) doesSupportMetaInformationType(metaType MetaInformationType, referenceDate int64) bool {
	metaInformation := contract.ContractDefinition.Vertrag.Metainformationen
	if metaInformation == nil || len(metaInformation.Metainformation) == 0 {
		return false
	}

	for _, i := range metaInformation.Metainformation {
		if i.U == metaType && i.IsRefDateValid(referenceDate) {
			return true
		}
	}
	return false
}

func (contract *Contract) GetContact(ctx *titan.Context, ikNumber string) (phone, fax string) {
	if contract == nil {
		return "", ""
	}
	resultPhone := map[string]string{}
	resultFax := map[string]string{}
	now := pkg_utils.NowUnixMillis(ctx)
	form1410 := contract.CheckExistAnforderung(ICheckExistAnforderung{
		ComplianceIds: []string{"FORM1410"},
		CheckTime:     now,
	})
	form1413 := contract.CheckExistAnforderung(ICheckExistAnforderung{
		ComplianceIds: []string{"FORM1413"},
		CheckTime:     now,
	})

	for _, mainContact := range contract.ContractDefinition.Kostentraeger.Hauptkasse {
		mainIk := mainContact.IK
		for _, subContact := range mainContact.Kassen.Kasse {
			ikString := strconv.FormatInt(int64(subContact.IK), 10)
			phone := ""
			fax := ""

			if subContact.Kontakte != nil && len(subContact.Kontakte.Kontakt) > 0 {
				phone = subContact.Kontakte.Kontakt[0].Telefon

				if form1410 && !form1413 {
					ambulantesOperierenKontakt := slice.FindOne(subContact.Kontakte.Kontakt, func(kontakt Kontakt) bool {
						return kontakt.Verwendung == KontaktVerwendungsartTyp_AMBULANTES_OPERIEREN
					})

					if ambulantesOperierenKontakt != nil {
						fax = ambulantesOperierenKontakt.Telefax
					} else {
						fax = subContact.Kontakte.Kontakt[0].Telefax
					}
				} else {
					fax = subContact.Kontakte.Kontakt[0].Telefax
				}
			} else if mainIk == subContact.IK && mainContact.Kontakte != nil && len(mainContact.Kontakte.Kontakt) > 0 {
				phone = mainContact.Kontakte.Kontakt[0].Telefon

				if form1410 && !form1413 {
					ambulantesOperierenKontakt := slice.FindOne(mainContact.Kontakte.Kontakt, func(kontakt Kontakt) bool {
						return kontakt.Verwendung == KontaktVerwendungsartTyp_AMBULANTES_OPERIEREN
					})

					if ambulantesOperierenKontakt != nil {
						fax = ambulantesOperierenKontakt.Telefax
					} else {
						fax = mainContact.Kontakte.Kontakt[0].Telefax
					}
				} else {
					fax = mainContact.Kontakte.Kontakt[0].Telefax
				}
			}
			if phone != "" {
				resultPhone[ikString] = phone
			}

			if fax != "" {
				resultFax[ikString] = fax
			}
		}
	}

	return resultPhone[ikNumber], resultFax[ikNumber]
}

func (contract *Contract) CheckExistAnforderung(req ICheckExistAnforderung) bool {
	ikGroupRefs := []int32{}
	if contract != nil {
		if req.IkNumber != nil && contract.ContractDefinition.Kostentraeger != nil {
			for _, h := range contract.ContractDefinition.Kostentraeger.Hauptkasse {
				if h.Kassen == nil {
					continue
				}
				if !h.IsRefDateValid(req.CheckTime) {
					continue
				}
				if slice.Any(h.Kassen.Kasse, func(k Kasse) bool {
					if !k.IsRefDateValid(req.CheckTime) {
						return false
					}
					return *req.IkNumber == k.IK
				}) {
					ikGroupRefs = append(
						ikGroupRefs,
						slice.Map(
							slice.Filter(
								h.IkgruppeRef,
								func(i IkgrupperefTyp) bool {
									return i.IsRefDateValid(req.CheckTime)
								},
							),
							func(i IkgrupperefTyp) int32 {
								return i.ID
							},
						)...,
					)
				}
			}
		}
		if contract.ContractDefinition.Vertrag != nil && contract.ContractDefinition.Vertrag.Anforderungen != nil {
			isVsst515 := slice.FindOne(contract.ContractDefinition.Vertrag.Anforderungen.Anforderung, func(t AnforderungTyp) bool {
				ikGroups := function.Do(func() []IkgruppeEinschlussTyp {
					if t.Bedingungen != nil && t.Bedingungen.IkgruppeEinschlussBedingung != nil {
						return t.Bedingungen.IkgruppeEinschlussBedingung.IkgruppeEinschluss
					}
					return []IkgruppeEinschlussTyp{}
				})
				kvRegions := function.Do(func() []KvregionEinschlussTyp {
					if t.Bedingungen != nil && t.Bedingungen.KvregionEinschlussBedingung != nil {
						return t.Bedingungen.KvregionEinschlussBedingung.KvregionEinschluss
					}
					return []KvregionEinschlussTyp{}
				})

				if req.IkGroup != nil && len(ikGroups) > 0 {
					if !slice.Any(ikGroups, func(ikGroup IkgruppeEinschlussTyp) bool {
						return ikGroup.IsRefDateValid(req.CheckTime) && ikGroup.ID == *req.IkGroup
					}) {
						return false
					}
				}
				if req.KvRegion != nil && len(kvRegions) > 0 {
					if !slice.Any(kvRegions, func(kvRegion KvregionEinschlussTyp) bool {
						return kvRegion.IsRefDateValid(req.CheckTime) && kvRegion.OKV == okv_util.ConvertUKVToOKV(*req.KvRegion)
					}) {
						return false
					}
				}
				if req.IkNumber != nil && len(ikGroupRefs) > 0 && t.Bedingungen != nil && t.Bedingungen.IkgruppeEinschlussBedingung != nil {
					if !slice.Any(t.Bedingungen.IkgruppeEinschlussBedingung.IkgruppeEinschluss, func(ik IkgruppeEinschlussTyp) bool {
						return ik.IsRefDateValid(req.CheckTime) && slice.Contains(ikGroupRefs, ik.ID)
					}) {
						return false
					}
				}
				return slice.Contains(req.ComplianceIds, t.ID)
			})
			return isVsst515 != nil
		}
	}
	return false
}

func (contract *Contract) CheckModuleContractComplianceExist(moduleContractId string, requirementes []string) bool {
	var moduleContract *VertragTyp
	if contract != nil && contract.ContractDefinition.Modulvertrag != nil {
		moduleContract = slice.FindOne(contract.ContractDefinition.Modulvertrag, func(t VertragTyp) bool {
			return t.ID == moduleContractId
		})
	}
	if moduleContract != nil && moduleContract.Anforderungen != nil {
		return slice.Any(moduleContract.Anforderungen.Anforderung, func(t AnforderungTyp) bool {
			return slice.Contains(requirementes, t.ID)
		})
	}
	return false
}

func (contract *Contract) CheckModuleContractExistFunction(moduleContractId, functionId string) bool {
	moduleContract := slice.FindOne(contract.ContractDefinition.Modulvertrag, func(item VertragTyp) bool {
		return item.ID == moduleContractId
	})
	if moduleContract == nil {
		return false
	}
	return slice.Any(moduleContract.Anforderungen.Anforderung, func(function AnforderungTyp) bool {
		return function.ID == functionId
	})
}

func (contract *Contract) GetServiceCode(code string, checkTime time.Time) *LeistungTyp {
	if contract.ContractDefinition.Vertrag != nil && contract.ContractDefinition.Vertrag.Honoraranlagen != nil {
		for _, h := range contract.ContractDefinition.Vertrag.Honoraranlagen.Honoraranlage {
			if h.Leistungen != nil {
				for _, l := range h.Leistungen.Leistung {
					startDate, endDate := l.GetEffectiveFromTo()
					checkTimeStamp := checkTime.UnixMilli()
					if checkTimeStamp < startDate {
						continue
					}
					if endDate != nil && checkTimeStamp > *endDate {
						continue
					}
					if l.GetCode() == code {
						return &l
					}
				}
			}
		}
	}
	return nil
}

func (contract *Contract) GetPatientParticipationRequestContract() []PatiententeilnahmeanfrageVertrag {
	if contract == nil ||
		contract.ContractDefinition.VertragsuebergreifendePatiententeilnahmeanfrage == nil ||
		contract.ContractDefinition.VertragsuebergreifendePatiententeilnahmeanfrage.PatiententeilnahmeanfrageVertrag == nil {
		return nil
	}
	return contract.ContractDefinition.VertragsuebergreifendePatiententeilnahmeanfrage.PatiententeilnahmeanfrageVertrag
}

func (contract *Contract) CheckAvailableHPMFunction(function HpmFunktionSimpleTyp, checkDate int64) bool {
	if contract != nil {
		hpmFunctions := contract.GetAvailableHpmFunctions(checkDate)
		return slice.Contains(hpmFunctions, function)
	}
	return false
}

func (c *Contract) GetAvailableHpmFunctions(checkDate int64) []HpmFunktionSimpleTyp {
	result := []HpmFunktionSimpleTyp{}
	if c == nil {
		return result
	}
	for _, m := range c.ContractDefinition.Vertrag.Metainformationen.Metainformation {
		validTo := m.GueltigBisReferenzdatum.Time.UnixMilli()
		validFrom := m.GueltigAbReferenzdatum.Time.UnixMilli()
		if validTo < 0 {
			validTo = checkDate
		}
		if checkDate >= validFrom && checkDate <= validTo && m.HpmFunktionen != nil {
			result = append(result, m.HpmFunktionen.HpmFunktion...)
		}
	}
	return result
}

func (contract *Contract) CheckValidIkGroup(ikNumber int32, checkTime int64) bool {
	if contract == nil || contract.ContractDefinition.Kostentraeger == nil || contract.ContractDefinition.Kostentraeger.Hauptkasse == nil {
		return false
	}

	for _, hauptkasse := range contract.ContractDefinition.Kostentraeger.Hauptkasse {
		if hauptkasse.IkgruppeRef == nil {
			continue
		}

		if hauptkasse.IK == ikNumber && isValidIkGroup(hauptkasse, checkTime) {
			return true
		}
	}
	return false
}

func isValidIkGroup(hauptkasse Hauptkasse, checkTime int64) bool {
	return slice.EveryBy(hauptkasse.IkgruppeRef, func(ikGroup IkgrupperefTyp) bool {
		return ikGroup.IsRefDateValid(checkTime)
	})
}

func (contract *Contract) GetGroupIdListIkMap() map[int64][]int64 {
	ikGroupMap := make(map[int64][]int64)
	for _, hauptkasse := range contract.MainOfficeInsurances().Hauptkasse {
		for _, ikGroup := range hauptkasse.IkgruppeRef {
			groupId := int64(ikGroup.ID)
			ikGroupMap[groupId] = append(ikGroupMap[groupId], int64(hauptkasse.IK))
		}
	}
	return ikGroupMap
}

func (contract *Contract) GetKvServiceCodes(checkDate int64) []string {
	result := []string{}
	if contract.ContractDefinition.Vertrag == nil {
		return result
	}
	if contract.ContractDefinition.Vertrag.Honoraranlagen == nil {
		return result
	}
	for _, h := range contract.ContractDefinition.Vertrag.Honoraranlagen.Honoraranlage {
		if h.Ziffernkraenze == nil {
			continue
		}
		for _, z := range h.Ziffernkraenze.Ziffernkranz {
			for _, e := range z.Ebmziffer {
				startDate := pkg_utils.ConvertTimeToMiliSecond(e.GueltigAbReferenzdatum.Time)
				var endDate *int64
				if e.GueltigBisReferenzdatum != nil && e.GueltigBisReferenzdatum.Time.Format(pkg_utils.DDMMYYYY) != "31129999" {
					endDate = pkg_utils.NewPointer(pkg_utils.ConvertTimeToMiliSecond(e.GueltigBisReferenzdatum.Time))
				}
				if startDate > checkDate {
					continue
				}
				if endDate != nil && *endDate < checkDate {
					continue
				}
				result = append(result, e.Ziffer)
			}
		}
	}
	return result
}

func (contract *Contract) DoesContractHasAttachmentWithProperty(attachmentID, propertyID string) bool {
	if contract.ContractDefinition.Vertrag == nil ||
		contract.ContractDefinition.Vertrag.Anlagen == nil {
		return false
	}
	attachment := slice.FindOne(contract.ContractDefinition.Vertrag.Anlagen.Anlage, func(attachment AnlageTyp) bool {
		return attachment.ID == attachmentID
	})
	if attachment == nil {
		return false
	}
	property := slice.FindOne(attachment.Anlageobjekt, func(property AnlageobjektTyp) bool {
		return property.ID == propertyID
	})
	return property != nil
}

func (contract *Contract) GetEnrollmentTypeOptions() []enrollment_common.EnrollmentType {
	if contract == nil {
		return nil
	}
	s := []string{}
	if contract.CheckExistAnforderung(ICheckExistAnforderung{
		ComplianceIds: []string{"VERE1559"},
		CheckTime:     time.Now().UnixMilli(),
	}) {
		s = append(s, "VERE1559")
	}
	if contract.CheckExistAnforderung(ICheckExistAnforderung{
		ComplianceIds: []string{"VERE554"},
		CheckTime:     time.Now().UnixMilli(),
	}) {
		s = append(s, "VERE554")
	}
	return enrollment_common.GetContractEnrollmentTypeOptions(s)
}

func (contract *Contract) GetCrossParticipationContract() []string {
	if contract.ContractDefinition.VertragsuebergreifendePatiententeilnahmeanfrage == nil {
		return nil
	}

	return slice.Map(contract.ContractDefinition.VertragsuebergreifendePatiententeilnahmeanfrage.PatiententeilnahmeanfrageVertrag, func(item PatiententeilnahmeanfrageVertrag) string {
		return item.ID
	})
}
