// This code was autogenerated from service/domains/ptv_import_common.proto, do not edit.

package common

import (
	"encoding/json"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	common "git.tutum.dev/medi/tutum/ares/service/domains/api/common"

	patient_profile_common "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type GetCodePtvImportByDoctorRequest struct {
	DoctorId uuid.UUID `json:"doctorId" validate:"required"`
	Year     int64     `json:"year" validate:"required"`
	Quarter  int64     `json:"quarter" validate:"required"`
}

type GetCodePtvImportByDoctorResponse struct {
	Code string `json:"code"`
}

type GetPtvContractByDoctorRequest struct {
	DoctorId uuid.UUID `json:"doctorId" validate:"required"`
	Year     int64     `json:"year" validate:"required"`
	Quarter  int64     `json:"quarter" validate:"required"`
	Code     string    `json:"code" validate:"required"`
}

type GetPtvContractByDoctorResponse struct {
	Contracts []ImportContract `json:"contracts"`
}

type ImportContract struct {
	ContractId string               `json:"contractId" bson:"contractId"`
	DocumentId string               `json:"documentId" bson:"documentId"`
	Version    int64                `json:"version"`
	Status     ImportContractStatus `json:"status"`
	FileInfo   *FileInfo            `json:"fileInfo" bson:"fileInfo"`
	Year       int64                `json:"year"`
	Quarter    int64                `json:"quarter"`
}

type PtvImport struct {
	DoctorId      uuid.UUID `json:"doctorId" bson:"doctorId"`
	RetrievalCode string    `json:"retrievalCode" bson:"retrievalCode"`
}

type GetParticipantsByDoctorRequest struct {
	DoctorId   uuid.UUID `json:"doctorId"`
	Code       string    `json:"code"`
	DocumentId string    `json:"documentId"`
	Year       int64     `json:"year"`
	Quarter    int64     `json:"quarter"`
	Version    int64     `json:"version"`
	ContractId string    `json:"contractId"`
	NewSession bool      `json:"newSession"`
}

type GetParticipantsByDoctorResponse struct {
	Id                     uuid.UUID             `json:"id"`
	DoctorId               uuid.UUID             `json:"doctorId"`
	ContractId             string                `json:"contractId"`
	DocumentId             string                `json:"documentId"`
	AutoImportParticipants []ParticipantDecision `json:"autoImportParticipants"`
	ConflictParticipants   []ParticipantDecision `json:"conflictParticipants"`
	MissingParticipants    []ParticipantDecision `json:"missingParticipants"`
	BeforeParticipantCount int64                 `json:"beforeParticipantCount"`
	AfterParticipantCount  int64                 `json:"afterParticipantCount"`
	Year                   int64                 `json:"year"`
	Quarter                int64                 `json:"quarter"`
}

type ParticipantDecision struct {
	Id                uuid.UUID         `json:"id"`
	PatientId         uuid.UUID         `json:"patientId" bson:"patientId"`
	IkNumber          IkNumber          `json:"ikNumber" bson:"ikNumber"`
	InsuranceNumber   InsuranceNumber   `json:"insuranceNumber" bson:"insuranceNumber"`
	Status            Status            `json:"status"`
	FirstName         FirstName         `json:"firstName" bson:"firstName"`
	LastName          LastName          `json:"lastName" bson:"lastName"`
	Reason            Reason            `json:"reason"`
	Gender            Gender            `json:"gender"`
	Dob               Dob               `json:"dob"`
	ContractBeginDate ContractBeginDate `json:"contractBeginDate" bson:"contractBeginDate"`
	ContractEndDate   ContractEndDate   `json:"contractEndDate" bson:"contractEndDate"`
	MarkAsDone        bool              `json:"markAsDone" bson:"markAsDone"`
	TypeGroupDecision TypeGroupDecision `json:"typeGroupDecision" bson:"typeGroupDecision"`
	IsProccessing     bool              `json:"isProccessing" bson:"isProccessing"`
	PpId              *uuid.UUID        `json:"ppId" bson:"ppId"`
	ConflictResolved  bool              `json:"conflictResolved" bson:"conflictResolved"`
	Hints             []string          `json:"hints" bson:"hints"`
}

type IkNumber struct {
	LocalIkNumber NumberSelection `json:"localIkNumber" bson:"localIkNumber"`
	HpmIkNumber   NumberSelection `json:"hpmIkNumber" bson:"hpmIkNumber"`
}

type InsuranceNumber struct {
	LocalInsuranceNumber StringSelection `json:"localInsuranceNumber" bson:"localInsuranceNumber"`
	HpmInsuranceNumber   StringSelection `json:"hpmInsuranceNumber" bson:"hpmInsuranceNumber"`
}

type Status struct {
	LocalStatus StatusSelection `json:"localStatus" bson:"localStatus"`
	HpmStatus   StatusSelection `json:"hpmStatus" bson:"hpmStatus"`
}

type FirstName struct {
	LocalFirstName StringSelection `json:"localFirstName" bson:"localFirstName"`
	HpmFirstName   StringSelection `json:"hpmFirstName" bson:"hpmFirstName"`
}

type LastName struct {
	LocalLastName StringSelection `json:"localLastName" bson:"localLastName"`
	HpmLastName   StringSelection `json:"hpmLastName" bson:"hpmLastName"`
}

type Reason struct {
	LocalReason StringSelection `json:"localReason" bson:"localReason"`
	HpmReason   StringSelection `json:"hpmReason" bson:"hpmReason"`
}

type Gender struct {
	LocalGender GenderSelection `json:"localGender" bson:"localGender"`
	HpmGender   GenderSelection `json:"hpmGender" bson:"hpmGender"`
}

type Dob struct {
	LocalDOB NumberSelection `json:"localDOB" bson:"localDOB"`
	HpmDOB   NumberSelection `json:"hpmDOB" bson:"hpmDOB"`
}

type ContractBeginDate struct {
	LocalContractBeginDate NumberSelection `json:"localContractBeginDate" bson:"localContractBeginDate"`
	HpmContractBeginDate   NumberSelection `json:"hpmContractBeginDate" bson:"hpmContractBeginDate"`
}

type ContractEndDate struct {
	LocalContractEndDate NumberSelection `json:"localContractEndDate" bson:"localContractEndDate"`
	HpmContractEndDate   NumberSelection `json:"hpmContractEndDate" bson:"hpmContractEndDate"`
}

type ImportParticipantsRequest struct {
	Id                     uuid.UUID             `json:"id"`
	DoctorId               uuid.UUID             `json:"doctorId"`
	ContractId             string                `json:"contractId"`
	DocumentId             string                `json:"documentId"`
	AutoImportParticipants []ParticipantDecision `json:"autoImportParticipants"`
	ConflictParticipants   []ParticipantDecision `json:"conflictParticipants"`
	MissingParticipants    []ParticipantDecision `json:"missingParticipants"`
	Year                   int64                 `json:"year"`
	Quarter                int64                 `json:"quarter"`
	ImportType             PTVImportType         `json:"importType"`
}

type GetListPtvImportHistoryRequest struct {
	Pagination common.Pagination `json:"pagination" validate:"required"`
}

type GetListPtvImportHistoryResponse struct {
	Data  []PtvImportHistory `json:"data"`
	Total int64              `json:"total"`
}

type PtvImportHistory struct {
	Id                     uuid.UUID             `json:"id" bson:"_id"`
	DoctorId               uuid.UUID             `json:"doctorId" bson:"doctorId"`
	ContractId             string                `json:"contractId" bson:"contractId"`
	DocumentId             string                `json:"documentId" bson:"documentId"`
	AutoImportParticipants []ParticipantDecision `json:"autoImportParticipants" bson:"autoImportParticipants"`
	ConflictParticipants   []ParticipantDecision `json:"conflictParticipants" bson:"conflictParticipants"`
	MissingParticipants    []ParticipantDecision `json:"missingParticipants" bson:"missingParticipants"`
	ImporterId             uuid.UUID             `json:"importerId" bson:"importerId"`
	CreateTime             int64                 `json:"createTime" bson:"createTime"`
	UpdateTime             int64                 `json:"updateTime" bson:"updateTime"`
	Year                   int64                 `json:"year"`
	Quarter                int64                 `json:"quarter"`
	Status                 ImportContractStatus  `json:"status" bson:"status"`
	BeforeParticipantCount int64                 `json:"beforeParticipantCount"`
	AfterParticipantCount  int64                 `json:"afterParticipantCount"`
}

type ImportTestDataRequest struct {
	DoctorId uuid.UUID `json:"doctorId"`
	XmlData  string    `json:"xmlData"`
}

type StringSelection struct {
	Value    string `json:"value"`
	Selected bool   `json:"selected"`
}

type NumberSelection struct {
	Value    *int64 `json:"value"`
	Selected bool   `json:"selected"`
}

type GenderSelection struct {
	Value    patient_profile_common.Gender `json:"value"`
	Selected bool                          `json:"selected"`
}

type StatusSelection struct {
	Value    PatientParticipationStatus `json:"value"`
	Selected bool                       `json:"selected"`
}

type FileInfo struct {
	BucketName  string `json:"bucketName" bson:"bucketName"`
	ObjectName  string `json:"objectName" bson:"objectName"`
	CreatedDate int64  `json:"createdDate" bson:"createdDate"`
}

// enum definitions
type ImportParticipantsType string

const (
	ImportType_Auto     ImportParticipantsType = "autoImportParticipants"
	ImportType_Conflict ImportParticipantsType = "conflictParticipants"
	ImportType_Missing  ImportParticipantsType = "missingParticipants"
)

type ImportContractStatus string

const (
	ImportContractStatus_New        ImportContractStatus = "NEW"
	ImportContractStatus_InProgress ImportContractStatus = "INPROGRESS"
	ImportContractStatus_Done       ImportContractStatus = "DONE"
	ImportContractStatus_Pending    ImportContractStatus = "PENDING"
)

type PTVImportType string

const (
	ImportType_Basic PTVImportType = "basic"
	ImportType_Full  PTVImportType = "full"
)

type TypeGroupDecision string

const (
	GeneralGroupUnchanged  TypeGroupDecision = "Unchanged"
	GeneralGroupNew        TypeGroupDecision = "New"
	GeneralGroupTerminated TypeGroupDecision = "Terminated"
	GeneralGroupRequested  TypeGroupDecision = "Requested"
	GeneralGroupRejected   TypeGroupDecision = "Rejected"
	SpecialGroup           TypeGroupDecision = "SpecialGroup"
	MissingGroupPTV        TypeGroupDecision = "MissingGroupPTV"
	MissingGroupIV         TypeGroupDecision = "MissingGroupIV"
)

type PatientParticipationStatus string

const (
	PatientParticipation_Requested  PatientParticipationStatus = "REQUESTED"
	PatientParticipation_Rejected   PatientParticipationStatus = "REJECTED"
	PatientParticipation_Active     PatientParticipationStatus = "ACTIVE"
	PatientParticipation_Cancelled  PatientParticipationStatus = "CANCELLED"
	PatientParticipation_Terminated PatientParticipationStatus = "TERMINATED"
	PatientParticipation_Faulty     PatientParticipationStatus = "FAULTY"
)

// Define constants
const NATS_SUBJECT = "api.service.domains" // nats subject this service will listen to

// service event constants

// message event constants

// Define service interface -------------------------------------------------------------

// Define service proxy -------------------------------------------------------------------

// Define service router -----------------------------------------------------------------

// Define client ----------------------------------------------------------------------------------------------

// Define event Notifier -----------------------------------------------------------------------------------------
type CommonNotifier struct {
	client *titan.Client
}

func NewCommonNotifier() *CommonNotifier {
	client := titan.GetDefaultClient()
	return &CommonNotifier{client}
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type CommonSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewCommonSocketNotifier(socket *socket_api.SocketServiceClient) *CommonSocketNotifier {
	return &CommonSocketNotifier{socket}
}

// -----------------------------------------------
// Test event listener
type CommonEventListener struct {
	mux sync.Mutex
}

func (listener *CommonEventListener) Reset() {
	listener.mux.Lock()
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *CommonEventListener) Subscribe(s *titan.MessageSubscriber) {
}
