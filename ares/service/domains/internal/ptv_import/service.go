package ptv_import

import (
	"bytes"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	patient_service "git.tutum.dev/medi/tutum/ares/service/domains/internal/patient"
	patient_profile_service "git.tutum.dev/medi/tutum/ares/service/domains/internal/profile/patient"
	ptv_import_internal "git.tutum.dev/medi/tutum/ares/service/domains/internal/ptv_import/internal"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	feature_flag_service "git.tutum.dev/medi/tutum/ares/service/feature_flag"
	"git.tutum.dev/medi/tutum/ares/service/schein"
	"git.tutum.dev/medi/tutum/ares/share/config"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/jinzhu/copier"
	"github.com/spf13/cast"
	"github.com/submodule-org/submodule.go/v2"

	hpm_rest_hzv_client "git.tutum.dev/medi/tutum/ares/pkg/hpm_rest/hzv"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	"github.com/google/uuid"

	"emperror.dev/errors"
	patient_profile_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_profile"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/sidebar"
	"git.tutum.dev/medi/tutum/ares/app/mvz/patient_profile"
	hpm_rest_client "git.tutum.dev/medi/tutum/ares/pkg/hpm_rest/client"
	"git.tutum.dev/medi/tutum/ares/pkg/minio"
	doctor_participation_service "git.tutum.dev/medi/tutum/ares/service/domains/api/doctor_participate"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_participation"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	ptv_import_api "git.tutum.dev/medi/tutum/ares/service/domains/api/ptv_import"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/internal/doctor_participate"
	patient_participation_service "git.tutum.dev/medi/tutum/ares/service/domains/internal/patient_participation"
	"git.tutum.dev/medi/tutum/ares/service/domains/internal/profile/employee"
	ptv_import_common "git.tutum.dev/medi/tutum/ares/service/domains/ptv_import/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/ptv_import"
	feature_flag_common "git.tutum.dev/medi/tutum/ares/service/feature_flag/common"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"gitlab.com/silenteer-oss/titan"
)

var (
	ErrNoSelectedValue = errors.New("NO_SELECTED_VALUE_TO_IMPORT")
	CreateNewPatient   = false // This field is used for test purpose, DO NOT CHANGE THE VALUE
)

type Service struct {
	patientProfileBff          *patient_profile.PatientProfileBffImpl
	patientProfileService      profile.PatientProfileService
	employeeProfileService     profile.EmployeeProfileService
	patientParticipateService  patient_participation.PatientParticipationService
	minioClient                *minio.Minio
	bucketName                 string
	repo                       *Repository
	doctorParticipationService doctor_participation_service.DoctorParticipateService
	hpmRest                    *hpm_rest_client.ServiceRest
	featureFlagService         *feature_flag_service.FeatureFlagService
	patientProfileRepo         *patient_profile.PatientProfileRepository
	scheinService              *schein.ScheinService
	ptvDeleteService           *ptv_import_internal.PTVDeleteService
	sidebarNotifier            *sidebar.SidebarNotifier
}

var PtvImportServiceMod = submodule.Make[ptv_import_api.PtvImportService](func(
	patientProfileBff *patient_profile.PatientProfileBffImpl,
	patientProfileService profile.PatientProfileService,
	employeeProfileService profile.EmployeeProfileService,
	patientParticipateService patient_participation.PatientParticipationService,
	minioClient *minio.Minio,
	config *config.DomainServiceConfigs,
	repo *Repository,
	doctorParticipationService *doctor_participate.Service,
	hpmRestService *hpm_rest_client.ServiceRest,
	featureFlagDomainService *feature_flag_service.FeatureFlagService,
	patientProfileRepo *patient_profile.PatientProfileRepository,
	scheinService *schein.ScheinService,
	ptvDeleteService *ptv_import_internal.PTVDeleteService,
) ptv_import_api.PtvImportService {
	sidebarNotifier := sidebar.NewSidebarNotifier()

	return &Service{
		patientProfileBff:          patientProfileBff,
		patientProfileService:      patientProfileService,
		employeeProfileService:     employeeProfileService,
		patientParticipateService:  patientParticipateService,
		minioClient:                minioClient,
		bucketName:                 config.MinioClientConfig.BucketPtvImport,
		repo:                       repo,
		doctorParticipationService: doctorParticipationService,
		hpmRest:                    hpmRestService,
		featureFlagService:         featureFlagDomainService,
		patientProfileRepo:         patientProfileRepo,
		scheinService:              scheinService,
		ptvDeleteService:           ptvDeleteService,
		sidebarNotifier:            sidebarNotifier,
	}
},
	patient_profile.PatientProfileServiceMod,
	patient_service.PatientServiceMod,
	patient_profile_service.PatientProfileServiceMod,
	employee.ProfileServiceWithAuthServiceManagementMod,
	patient_participation_service.PatientParticipationServiceMod,
	minio.MinioMod,
	config.DomainServiceConfigsMod,
	RepositoryMod,
	doctor_participate.DoctorParticipateServiceAPIMod,
	hpm_rest_client.HpmRestServiceMod,
	feature_flag_service.FeatureFlagServiceMod,
	patient_profile.PatientProfileRepositoryMod,
	schein.ScheinServiceMod,
	ptv_import_internal.PTVDeleteServiceMod,
)

func (srv *Service) GetCodePtvByDoctor(ctx *titan.Context, request ptv_import_common.GetCodePtvImportByDoctorRequest) (*ptv_import_common.GetCodePtvImportByDoctorResponse, error) {
	result, err := srv.repo.GetPtvImportByDoctorId(ctx, request.DoctorId)

	if err != nil {
		return nil, fmt.Errorf("failed to get PTV import by doctor ID: %w", err)
	}
	icode := ""
	if len(result) > 0 {
		icode = result[0].PtvImport.RetrievalCode
	}

	return &ptv_import_common.GetCodePtvImportByDoctorResponse{
		Code: icode,
	}, nil
}

// hpm return list of contracts with documentId
func (srv *Service) GetPtvContractByDoctor(ctx *titan.Context, request ptv_import_common.GetPtvContractByDoctorRequest) (*ptv_import_common.GetPtvContractByDoctorResponse, error) {
	empRes, err := srv.employeeProfileService.GetEmployeeProfileById(ctx, &profile.EmployeeProfileGetRequest{
		OriginalId: &request.DoctorId,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get employee profile by ID: %w", err)
	}
	timeNow := util.Now(ctx).UnixNano() / int64(time.Millisecond)
	contractDoctor := employee.ResponseToContractDoctor(empRes)

	// NOTE: this is for test mode
	isTestMode, err := srv.featureFlagService.IsFeatureEnabled(ctx, feature_flag_common.FeatureFlagKey_HZV_PTV_TESTMODE)
	if err != nil {
		return nil, fmt.Errorf("failed to check if feature is enabled: %w", err)
	}

	// NOTE: test mode flow
	if isTestMode {
		ptvImportResult, err := srv.repo.GetPtvImportByDoctorId(ctx, request.DoctorId)
		if err != nil {
			return nil, fmt.Errorf("failed to get PTV import by doctor ID: %w", err)
		}
		return &ptv_import_common.GetPtvContractByDoctorResponse{
			Contracts: slice.Map(ptvImportResult, func(i ptv_import.PtvImport) ptv_import_common.ImportContract {
				return ptv_import_common.ImportContract{
					ContractId:  i.Contract.ContractId,
					DocumentId:  i.Contract.DocumentId,
					Version:     i.Contract.Version,
					Status:      i.Contract.Status,
					Year:        i.Contract.Year,
					Quarter:     i.Contract.Quarter,
					PtvImportId: *i.Id,
				}
			}),
		}, nil
	}

	hpmContracts, err := srv.hpmRest.GetPTVFoldersByDoctor(ctx, hpm_rest_client.GetPtvFoldersaByDoctorRequest{
		Icode:                     request.Code,
		Bsnr:                      contractDoctor.Bsnr,
		ContractPartnerIdentifier: contractDoctor.HavgVpId,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get PTV folders by doctor: %w", err)
	}
	doctorContracts, err := srv.doctorParticipationService.GetContractsByDoctor(ctx, &doctor_participation_service.GetContractsByDoctorRequest{
		DoctorId: &request.DoctorId,
		Time:     &timeNow,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get contracts by doctor: %w", err)
	}

	missingContracts := []string{}
	hpmContractIds := slice.Map(hpmContracts, func(c hpm_rest_hzv_client.PTVContractFolder) string { return c.ContractId })
	dbContractIds := doctorContracts.ContractIds
	for _, hpmContractId := range hpmContractIds {
		if !slice.Contains(dbContractIds, hpmContractId) {
			missingContracts = append(missingContracts, hpmContractId)
		}
	}
	if len(missingContracts) > 0 {
		return nil, fmt.Errorf("you should add missing contract for this doctor. %v ", strings.Join(missingContracts, ", "))
	}

	ptvImportDb, err := srv.repo.GetPtvImportByDoctorId(ctx, request.DoctorId)
	if err != nil {
		return nil, fmt.Errorf("failed to get PTV import by doctor ID: %w", err)
	}
	if ptvImportDb == nil {
		return srv.createPtvImport(ctx, request, hpmContracts)
	}

	mapImportRepoContract := slice.MapToMap(ptvImportDb, func(i ptv_import.PtvImport) (string, ptv_import.PtvImport) {
		return i.Contract.ContractId, i
	})
	// handle data changed from previous
	tmpResContracts := []ptv_import_common.ImportContract{}
	for _, hpmContract := range hpmContracts {
		dbContract, ok := mapImportRepoContract[hpmContract.ContractId]
		if !ok {
			continue
		}
		if dbContract.Contract.Version < int64(hpmContract.Version) {
			dbContract.Contract.DocumentId = hpmContract.DocumentId
			dbContract.Contract.ContractId = hpmContract.ContractId
			dbContract.Contract.Status = ptv_import_common.ImportContractStatus_New
			dbContract.Contract.Version = int64(hpmContract.Version)
			dbContract.Contract.Year = int64(hpmContract.Year)
			dbContract.Contract.Quarter = int64(hpmContract.Quarter)
			_, err = srv.repo.PtvImportRepo.Update(ctx, &dbContract)
			if err != nil {
				return nil, fmt.Errorf("failed to update PTV import repo: %w", err)
			}
		}
		dbContract.Contract.PtvImportId = *dbContract.Id
		tmp := ptv_import_common.ImportContract{}
		err = copier.Copy(&tmp, dbContract.Contract)
		if err != nil {
			return nil, fmt.Errorf("failed to copy contract data: %w", err)
		}
		tmpResContracts = append(tmpResContracts, tmp)
	}
	return &ptv_import_common.GetPtvContractByDoctorResponse{
		Contracts: tmpResContracts,
	}, nil
}

func (*Service) hashXmlContent(_ *titan.Context,
	doctorId uuid.UUID,
	year, quarter int64,
	xmlContent string,
) string {
	hash := sha256.New()
	fmt.Fprintf(hash, "%s%d%d%s", doctorId, year, quarter, xmlContent)
	return hex.EncodeToString(hash.Sum(nil))
}

// parse and import a test data to prv_import and ptv_import_history
func (srv *Service) ImportTestData(ctx *titan.Context, request ptv_import_common.ImportTestDataRequest) error {
	// parse xml data
	data, err := ParsePatientParticipationListDocument(request.XmlData)
	if err != nil {
		return fmt.Errorf("failed to parse patient participation list document: %w", err)
	}
	patientPtv, err := data.ToPTVPatientParticipation(ctx.RequestTimeZone())
	if err != nil {
		return fmt.Errorf("failed to convert data to PTV patient participation: %w", err)
	}
	hpmParticipantBytes, err := json.Marshal(patientPtv)
	if err != nil {
		return fmt.Errorf("failed to marshal patient participation data: %w", err)
	}

	// add data to ptv_import
	date, err := time.Parse(util.YYYYMMDDhhmmss, data.EffectiveTime.Low.Value)
	if err != nil {
		return fmt.Errorf("failed to parse date: %w", err)
	}
	quarter, year := util.GetCurrentQuarter(date)
	version := cast.ToInt32(data.VersionNumber.Value)

	// map to partipation json upload minio
	documentId := data.ID.Extension
	contractId := data.Component.Act.ID.Extension
	ptvContractFolders := []hpm_rest_hzv_client.PTVContractFolder{
		{
			DocumentId: documentId,
			Year:       int32(year),
			Quarter:    int32(quarter),
			Version:    version,
			ContractId: contractId,
		},
	}

	hash := srv.hashXmlContent(ctx, request.DoctorId, int64(year), int64(quarter), string(hpmParticipantBytes))
	ptvImportUploading, err := srv.repo.GetPtvImportByHash(ctx, hash)
	if err != nil {
		return fmt.Errorf("failed to get PTV import by hash: %w", err)
	}

	isPtvImportExists := ptvImportUploading != nil

	if isPtvImportExists {
		ptvImportLatest, err := srv.repo.GetPtvImportByDoctorIdAndContractId(ctx, request.DoctorId, contractId)
		if err != nil {
			return fmt.Errorf("failed to get PTV import by doctor ID: %w", err)
		}
		uploadingTime := year*4 + quarter
		latestTime := ptvImportLatest[0].Contract.Year*4 + ptvImportLatest[0].Contract.Quarter
		if int64(uploadingTime) < latestTime {
			return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_PTV_Import_Older_Than_Latest, "current year quarter is older than ptvImport result, please upload the latest year quarter")
		}
	}

	if !isPtvImportExists {
		createPtvImportRequest := ptv_import_common.GetPtvContractByDoctorRequest{
			DoctorId: request.DoctorId,
			Year:     int64(year),
			Quarter:  int64(quarter),
			Code:     data.Code.Code,
		}
		_, err = srv.createPtvImport(ctx, createPtvImportRequest, ptvContractFolders)
		if err != nil {
			return fmt.Errorf("failed to create PTV import: %w", err)
		}

		// update fileInfo to ptv_import

		fileInfoToUpdate, err := srv.uploadFileJson(ctx, hpmParticipantBytes, documentId, hash)
		if err != nil {
			return fmt.Errorf("failed to upload JSON file: %w", err)
		}
		_, err = srv.repo.UpdateFileInfoPtvImport(ctx, request.DoctorId, documentId, fileInfoToUpdate)
		if err != nil {
			return fmt.Errorf("failed to update file info in PTV import: %w", err)
		}
	}

	_ = srv.sidebarNotifier.NotifySidebarRepsonse(ctx, &sidebar.EventSidebarRepsonse{
		UserId: util.GetPointerValue(ctx.UserInfo().UserUUID()),
	})

	return nil
}

// GetParticipantsByDoctor retrieves participants from HPM and local system
// and categorizes them for import
func (srv *Service) GetParticipantsByDoctor(ctx *titan.Context, request ptv_import_common.GetParticipantsByDoctorRequest) (*ptv_import_common.GetParticipantsByDoctorResponse, error) {
	ptvImport, err := srv.repo.GetPtvImportById(ctx, request.PtvImportId)
	if err != nil {
		if errors.Is(err, errors.New("mongo: no documents in result")) {
			return createEmptyParticipantsResponse(), nil
		}
		return nil, fmt.Errorf("failed to get import contract: %w", err)
	}

	if ptvImport == nil {
		return createEmptyParticipantsResponse(), nil
	}
	importContract := ptvImport.Contract

	ptvImportLatest, err := srv.repo.GetPtvImportByDoctorIdAndContractId(ctx, request.DoctorId, request.ContractId)
	if err != nil {
		return nil, fmt.Errorf("failed to get PTV import by doctor ID: %w", err)
	}
	uploadingTime := request.Year*4 + request.Quarter
	latestTime := ptvImportLatest[0].Contract.Year*4 + ptvImportLatest[0].Contract.Quarter
	if uploadingTime < latestTime {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_PTV_Import_Older_Than_Latest, "current year quarter is older than ptvImport result, please upload the latest year quarter")
	}

	ptvImportHistory, err := srv.repo.GetPtvImportHistoryByPtvImportId(ctx, request.PtvImportId)
	if err != nil {
		return nil, fmt.Errorf("failed to get PTV import history: %w", err)
	}

	// 2. Initialize history tracker
	tracker := NewImportHistoryTracker(
		srv.repo,
		request.DocumentId,
		request.DoctorId,
		importContract.ContractId,
		request.Year,
		request.Quarter,
		ptvImport.PtvImport.RetrievalCode,
		importContract.Version,
		request.PtvImportId,
		ptvImportHistory,
	)

	if err := tracker.InitializeHistory(ctx); err != nil {
		return nil, err
	}

	// 3. Get participants from HPM and local system
	hpmParticipants, err := srv.getPatientParticipationHPM(ctx, request, importContract.FileInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to get HPM participants: %w", err)
	}
	// get all insurance numbers
	insuranceNumbers := slice.Map(hpmParticipants, func(p ptv_import_common.ParticipantDecision) string {
		return p.InsuranceNumber.HpmInsuranceNumber.Value
	})

	// get all patients by insurance numbers
	patientProfiles, err := srv.patientProfileRepo.GetPatientsProfileByInsuranceNumbers(ctx, insuranceNumbers)
	if err != nil {
		if isRecordNotFoundError, _ := titan.IsError(err, string(error_code.ErrorCode_Record_Not_Found)); !isRecordNotFoundError {
			return nil, fmt.Errorf("failed to get patients profile by insurance numbers: %w", err)
		}
	}

	localParticipants, localAllParticipants, err := srv.getPatientParticipationLocal(ctx, importContract.ContractId, request.DoctorId, patientProfiles)
	if err != nil {
		return nil, fmt.Errorf("failed to get local participants: %w", err)
	}

	// 4. Classify participants
	classifier := NewParticipantClassifier(request.Year, request.Quarter)
	classifier.Classify(localAllParticipants, hpmParticipants, localParticipants)

	// 5. Save classified participants
	if err := tracker.SaveClassifiedParticipants(ctx, classifier); err != nil {
		return nil, err
	}

	// 6. Prepare response
	importHistory := tracker.GetImportHistory()
	response := &ptv_import_common.GetParticipantsByDoctorResponse{}

	if err := copier.Copy(&response, importHistory); err != nil {
		return nil, fmt.Errorf("failed to copy response data: %w", err)
	}

	_ = srv.sidebarNotifier.NotifySidebarRepsonse(ctx, &sidebar.EventSidebarRepsonse{
		UserId: util.GetPointerValue(ctx.UserInfo().UserUUID()),
	})

	return response, nil
}

// ImportParticipants processes participants for import
func (srv *Service) ImportParticipants(ctx *titan.Context, request ptv_import_common.ImportParticipantsRequest) error {
	// 1. Initialize tracker and mark as in progress
	ptvImportHistory, err := srv.repo.GetPtvImportHistoryByPtvImportId(ctx, request.PtvImportId)
	if err != nil {
		return fmt.Errorf("failed to get PTV import history: %w", err)
	}
	tracker := NewImportHistoryTracker(
		srv.repo,
		request.DocumentId,
		request.DoctorId,
		request.ContractId,
		0, // Year not needed for this operation
		0, // Quarter not needed for this operation,
		ptvImportHistory.Code,
		ptvImportHistory.Version,
		request.PtvImportId,
		ptvImportHistory,
	)

	if err := tracker.BeginImport(ctx); err != nil {
		return err
	}

	// 2. Process auto-import participants
	if len(request.AutoImportParticipants) > 0 {
		// Set selections for auto-import
		for i := range request.AutoImportParticipants {
			request.AutoImportParticipants[i].SetHpmSelected(true)
			request.AutoImportParticipants[i].SetLocalSelected(false)
		}

		if err := srv.BatchProcessParticipants(
			ctx,
			request.AutoImportParticipants,
			request.Id,
			request.DoctorId,
			request.ContractId,
			ptv_import_common.ImportType_Auto,
		); err != nil {
			return err
		}
	}

	// 3. Process conflict participants (with user selections)
	hasConflictUnresolved := slice.Any(request.ConflictParticipants, func(p ptv_import_common.ParticipantDecision) bool {
		return !p.ConflictResolved
	})
	request.ConflictParticipants = slice.Filter(request.ConflictParticipants, func(p ptv_import_common.ParticipantDecision) bool {
		return p.ConflictResolved
	})

	if len(request.ConflictParticipants) > 0 {
		if err := srv.BatchProcessParticipants(
			ctx,
			request.ConflictParticipants,
			request.Id,
			request.DoctorId,
			request.ContractId,
			ptv_import_common.ImportType_Conflict,
		); err != nil {
			return err
		}
	}

	// 4. Process missing participants
	var hasUnassignedMissingParticipants bool
	if len(request.MissingParticipants) > 0 {
		// Split missing participants into new and existing patients
		var newPatients []ptv_import_common.ParticipantDecision
		var existingPatients []ptv_import_common.ParticipantDecision

		for _, participant := range request.MissingParticipants {
			if participant.TypeGroupDecision == ptv_import_common.MissingGroupIV {
				continue
			}
			if participant.PatientId == uuid.Nil {
				hasUnassignedMissingParticipants = true
				// No PatientId means this is a new patient that needs to be created
				newPatients = append(newPatients, participant)
			} else {
				// Has PatientId means this patient exists and needs to be updated
				existingPatients = append(existingPatients, participant)
			}
		}

		// Process new patients (create profiles)
		if len(newPatients) > 0 && CreateNewPatient {
			// Temporarily update request with only new patients
			originalMissingParticipants := request.MissingParticipants
			request.MissingParticipants = newPatients

			if err := srv.processNewPatientParticipations(ctx, request); err != nil {
				return fmt.Errorf("failed to process new patients: %w", err)
			}

			// Restore original list
			request.MissingParticipants = originalMissingParticipants
		}

		// Process existing patients (update profiles)
		if len(existingPatients) > 0 {
			// Temporarily update request with only existing patients
			originalMissingParticipants := request.MissingParticipants
			request.MissingParticipants = existingPatients

			if err := srv.processExistingPatientParticipations(ctx, request); err != nil {
				return fmt.Errorf("failed to process existing patients: %w", err)
			}

			// Restore original list
			request.MissingParticipants = originalMissingParticipants
		}
	}

	// 5. Mark import as complete
	isPending := hasConflictUnresolved || hasUnassignedMissingParticipants

	_ = srv.sidebarNotifier.NotifySidebarRepsonse(ctx, &sidebar.EventSidebarRepsonse{
		UserId: util.GetPointerValue(ctx.UserInfo().UserUUID()),
	})

	return tracker.CompleteImport(ctx, isPending)
}

func (srv *Service) GetListPtvImportHistory(ctx *titan.Context, request ptv_import_common.GetListPtvImportHistoryRequest) (*ptv_import_common.GetListPtvImportHistoryResponse, error) {
	result, err := srv.repo.GetPtvImportHistory(ctx, &request.Pagination)
	if err != nil {
		return nil, fmt.Errorf("failed to get PTV import history: %w", err)
	}
	totalCount, err := srv.repo.CountPtvImportHistory(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to count PTV import history: %w", err)
	}

	// get all ptv import
	// ptvImports, err := srv.repo.GetPtvImports(ctx)
	// if err != nil {
	// 	return nil, fmt.Errorf("failed to get PTV imports: %w", err)
	// }
	// m := slice.MapToMap(ptvImports, func(p ptv_import.PtvImport) (string, ptv_import.PtvImport) {
	// 	return p.Contract.DocumentId, p
	// })

	var ptvImportHistories []ptv_import_common.PtvImportHistory
	err = copier.Copy(&ptvImportHistories, result)
	if err != nil {
		return nil, fmt.Errorf("failed to copy PTV import histories: %w", err)
	}

	// update ptvImport status to history
	// for i := range ptvImportHistories {
	// 	if ptvImport, ok := m[ptvImportHistories[i].DocumentId]; ok {
	// 		ptvImportHistories[i].Status = ptvImport.Contract.Status
	// 	}
	// }

	return &ptv_import_common.GetListPtvImportHistoryResponse{
		Data:  ptvImportHistories,
		Total: totalCount,
	}, nil
}

func (srv *Service) createPtvImport(
	ctx *titan.Context,
	request ptv_import_common.GetPtvContractByDoctorRequest,
	hpmContracts []hpm_rest_hzv_client.PTVContractFolder,
) (*ptv_import_common.GetPtvContractByDoctorResponse, error) {
	response := &ptv_import_common.GetPtvContractByDoctorResponse{}
	rs := make([]ptv_import.PtvImport, 0, len(hpmContracts))
	for _, hpmContract := range hpmContracts {
		id := util.NewPointer(uuid.New())
		ptvImportResult := ptv_import.PtvImport{
			BaseEntity: repos.BaseEntity{
				Id:        id,
				CreatedAt: util.Now(ctx),
				CreatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
			},
			PtvImport: ptv_import_common.PtvImport{
				RetrievalCode: request.Code,
				DoctorId:      request.DoctorId,
				// Quarter:       request.Quarter,
				// Year:          request.Year,
			},
			Contract: ptv_import_common.ImportContract{
				ContractId:  hpmContract.ContractId,
				DocumentId:  hpmContract.DocumentId,
				Version:     int64(hpmContract.Version),
				Status:      ptv_import_common.ImportContractStatus_New,
				Year:        int64(hpmContract.Year),
				Quarter:     int64(hpmContract.Quarter),
				PtvImportId: *id,
			},
		}
		rs = append(rs, ptvImportResult)

		response.Contracts = append(response.Contracts, ptv_import_common.ImportContract{
			ContractId:  hpmContract.ContractId,
			DocumentId:  hpmContract.DocumentId,
			Version:     int64(hpmContract.Version),
			Status:      ptv_import_common.ImportContractStatus_New,
			Year:        int64(hpmContract.Year),
			Quarter:     int64(hpmContract.Quarter),
			PtvImportId: *id,
		})
	}
	_, err := srv.repo.PtvImportRepo.CreateMany(ctx, slice.ToPointerType(rs))
	if err != nil {
		return nil, fmt.Errorf("failed to create PTV import repo: %w", err)
	}
	return response, nil
}

// missing case: has patient profile but not have participation data yet in garrioPro system
func (srv *Service) getPatientParticipationLocal(
	ctx *titan.Context,
	contractId string,
	doctorId uuid.UUID,
	patients []patient.PatientProfile,
) ([]ptv_import_common.ParticipantDecision, []patient_participation.PatientParticipationPTVImport, error) {
	ppaItems, err := srv.patientParticipateService.GetPatientParticipationForPTVImport(ctx,
		patient_participation.GetPatientParticipationForPTVImportRequest{
			ContractId: contractId,
			DoctorId:   doctorId,
		})
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get patient participation for PTV import: %w", err)
	}
	patientIds := slice.Map(ppaItems.ParticipationPTVImports, func(p patient_participation.PatientParticipationPTVImport) uuid.UUID {
		return *p.PatientId
	})
	allPatientsFound, err := srv.patientProfileRepo.GetByIds(ctx, patientIds)
	if err != nil {
		if isRecordNotFoundError, _ := titan.IsError(err, string(error_code.ErrorCode_Record_Not_Found)); !isRecordNotFoundError {
			return nil, nil, fmt.Errorf("failed to get profile by IDs: %w", err)
		}
	}

	// append patient which not have participation data yet
	for _, p := range patients {
		foundPatient := slice.Any(allPatientsFound, func(pp *patient.PatientProfile) bool {
			return *pp.Id == *p.Id
		})
		if !foundPatient {
			allPatientsFound = append(allPatientsFound, &p)
		}
	}

	localParticipantDecisions := make([]ptv_import_common.ParticipantDecision, 0, len(ppaItems.ParticipationPTVImports))
	mParticipationPTVImports := slice.MapToMap(ppaItems.ParticipationPTVImports, func(p patient_participation.PatientParticipationPTVImport) (uuid.UUID, patient_participation.PatientParticipationPTVImport) {
		return *p.PatientId, p
	})
	for _, foundPatient := range allPatientsFound {
		dobV := ptv_import_common.Dob{}
		generV := ptv_import_common.Gender{}
		currentInsurance := *foundPatient.PatientInfo.GetActiveInsurance()
		if foundPatient.PatientInfo != nil {
			if foundPatient.PatientInfo.PersonalInfo.DateOfBirth.IsValidDOB {
				dobV.LocalDOB = ptv_import_common.NumberSelection{
					Value: &foundPatient.PatientInfo.PersonalInfo.DOB,
				}
			}
			generV.LocalGender = ptv_import_common.GenderSelection{
				Value: foundPatient.PatientInfo.PersonalInfo.Gender,
			}
		}
		ikNumber := int64(currentInsurance.IkNumber)
		localParticipantDecision := ptv_import_common.ParticipantDecision{
			PatientId: *foundPatient.Id,
			IkNumber:  ptv_import_common.IkNumber{LocalIkNumber: ptv_import_common.NumberSelection{Value: &ikNumber}},
			InsuranceNumber: ptv_import_common.InsuranceNumber{
				LocalInsuranceNumber: ptv_import_common.StringSelection{Value: *currentInsurance.InsuranceNumber},
			},
			Status:            ptv_import_common.Status{LocalStatus: ptv_import_common.StatusSelection{Value: ""}},
			ContractBeginDate: ptv_import_common.ContractBeginDate{LocalContractBeginDate: ptv_import_common.NumberSelection{Value: nil}},
			ContractEndDate:   ptv_import_common.ContractEndDate{LocalContractEndDate: ptv_import_common.NumberSelection{Value: nil}},
			FirstName:         ptv_import_common.FirstName{LocalFirstName: ptv_import_common.StringSelection{Value: foundPatient.FirstName}},
			LastName:          ptv_import_common.LastName{LocalLastName: ptv_import_common.StringSelection{Value: foundPatient.LastName}},
			Gender:            generV,
			Dob:               dobV,
			PpId:              nil,
		}
		if ppp, ok := mParticipationPTVImports[*foundPatient.Id]; ok {
			localParticipantDecision.IkNumber = ptv_import_common.IkNumber{LocalIkNumber: ptv_import_common.NumberSelection{Value: &ppp.IkNumber}}
			localParticipantDecision.InsuranceNumber = ptv_import_common.InsuranceNumber{
				LocalInsuranceNumber: ptv_import_common.StringSelection{Value: ppp.InsuranceNumber},
			}

			localParticipantDecision.Status = ptv_import_common.Status{LocalStatus: ptv_import_common.StatusSelection{Value: ptv_import_common.PatientParticipationStatus(ppp.Status)}}
			localParticipantDecision.ContractBeginDate = ptv_import_common.ContractBeginDate{LocalContractBeginDate: ptv_import_common.NumberSelection{Value: ppp.StartDate}}
			localParticipantDecision.ContractEndDate = ptv_import_common.ContractEndDate{LocalContractEndDate: ptv_import_common.NumberSelection{Value: ppp.EndDate}}
			localParticipantDecision.PpId = ppp.PpId
			localParticipantDecision.TreatmentType = ptv_import_common.TreatmentType{LocalTreatmentType: ptv_import_common.StringSelection{Value: ppp.TreatmentType}}
		}
		localParticipantDecisions = append(localParticipantDecisions, localParticipantDecision)
	}
	return localParticipantDecisions, ppaItems.ParticipationPTVImports, nil
}

// getPatientParticipationHPM retrieves patient participation data from HPM.
// It first attempts to load data from a previously cached file (if fileInfo is provided).
// If no cached data exists, it retrieves the data from the HPM REST API and caches it.
//
// Parameters:
//   - ctx: The titan context for the request
//   - request: The request containing doctor and document information
//   - fileInfo: Optional file info for cached data
//
// Returns:
//   - A slice of participant decisions mapped from HPM data
//   - An error if the operation fails
func (srv *Service) getPatientParticipationHPM(ctx *titan.Context, request ptv_import_common.GetParticipantsByDoctorRequest, fileInfo *ptv_import_common.FileInfo) ([]ptv_import_common.ParticipantDecision, error) {
	var hpmParticipants []hpm_rest_hzv_client.PTVPatientParticipation
	if fileInfo != nil {
		hpmParticipantBytes, err := srv.minioClient.DownloadFile(ctx, fileInfo.BucketName, fileInfo.ObjectName, minio.GetObjectOptions{})
		if err != nil {
			return nil, fmt.Errorf("failed to download file from minio: %w", err)
		}
		ctx.Logger().Debug("Downloaded HPM participants from Minio", string(hpmParticipantBytes))
		err = json.Unmarshal(hpmParticipantBytes, &hpmParticipants)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal HPM participants: %w", err)
		}
	} else {
		empRes, err := srv.employeeProfileService.GetEmployeeProfileById(ctx, &profile.EmployeeProfileGetRequest{
			OriginalId: &request.DoctorId,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to get employee profile: %w", err)
		}

		contractDoctor := employee.ResponseToContractDoctor(empRes)
		hpmParticipants, err = srv.hpmRest.GetPTVContractsByDoctor(ctx, hpm_rest_client.GetPtvContractsByDoctorRequest{
			Icode:                     request.Code,
			Bsnr:                      contractDoctor.Bsnr,
			ContractPartnerIdentifier: contractDoctor.HavgVpId,
			DocumentIdentifier:        request.DocumentId,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to get PTV contracts by doctor: %w", err)
		}
		hpmParticipantBytes, err := json.Marshal(hpmParticipants)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal HPM participants: %w", err)
		}
		hash := srv.hashXmlContent(ctx, request.DoctorId, request.Year, request.Quarter, string(hpmParticipantBytes))
		fileInfoToUpdate, err := srv.uploadFileJson(ctx, hpmParticipantBytes, request.DocumentId, hash)
		if err != nil {
			return nil, fmt.Errorf("failed to upload JSON file: %w", err)
		}
		_, err = srv.repo.UpdateFileInfoPtvImport(ctx, request.DoctorId, request.DocumentId, fileInfoToUpdate)
		if err != nil {
			return nil, fmt.Errorf("failed to update file info in PTV import: %w", err)
		}
	}

	return slice.Map(hpmParticipants, func(p hpm_rest_hzv_client.PTVPatientParticipation) ptv_import_common.ParticipantDecision {
		return mapPTVPatientParticipation2ParticipantDecision(p)
	}), nil
}

func (srv *Service) uploadFileJson(
	ctx *titan.Context,
	fileData []byte,
	objectName, hash string,
) (*ptv_import_common.FileInfo, error) {
	if len(fileData) == 0 {
		return nil, errors.New("data length is 0")
	}
	_, err := srv.minioClient.PutObject(
		ctx,
		srv.bucketName,
		objectName,
		bytes.NewReader(fileData),
		int64(len(fileData)),
		minio.PutObjectOptions{},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to put object in Minio: %w", err)
	}
	return &ptv_import_common.FileInfo{
		CreatedDate: util.NowUnixMillis(nil),
		BucketName:  srv.bucketName,
		ObjectName:  objectName,
		Hash:        hash,
	}, nil
}

// createPatientParticipantsFromHpmData creates patient profiles and participations for the given participant decisions.
// It returns a list of successfully imported participant decisions.
// set selected for HPM data
func (srv *Service) createPatientParticipantsFromHpmData(ctx *titan.Context,
	doctorId uuid.UUID,
	contractId string,
	participantDecisionsRequest []ptv_import_common.ParticipantDecision,
	participantDecisionsDb []ptv_import_common.ParticipantDecision,
) ([]ptv_import_common.ParticipantDecision, error) {
	imported := []ptv_import_common.ParticipantDecision{}
	err := function.NewPool[ptv_import_common.ParticipantDecision, *ptv_import_common.ParticipantDecision](
		function.EarlyReturn(),
		function.WithContext(ctx),
		function.WithSize(10),
	).WithFunc(func(participantDecision ptv_import_common.ParticipantDecision) (*ptv_import_common.ParticipantDecision, error) {
		participantDecision.SetLocalSelected(false)
		participantDecision.SetHpmSelected(true)
		if getParticipantDecisionById(participantDecisionsDb, participantDecision.Id).MarkAsDone {
			return nil, nil
		}
		patientId, err := srv.createPatientProfile(ctx, participantDecision)
		if err != nil {
			return nil, fmt.Errorf("failed to create patient profile: %w", err)
		}
		createPatientParticipationRequest := patient_participation.UpdatePatientParticipationForPTVImportRequest{
			DoctorId:        doctorId,
			ContractId:      contractId,
			PatientId:       *patientId,
			IkNumber:        int32(*participantDecision.IkNumber.SelectedValue()),
			InsuranceNumber: participantDecision.InsuranceNumber.SelectedValue(),
			StartDate:       participantDecision.ContractBeginDate.SelectedValue(),
			EndDate:         participantDecision.ContractEndDate.SelectedValue(),
			Status:          patient_participation.PatientParticipationStatus(participantDecision.Status.SelectedValue()),
		}
		ppResponse, err := srv.patientParticipateService.UpdatePatientParticipationForPTVImport(ctx, createPatientParticipationRequest)
		if err != nil {
			return nil, fmt.Errorf("failed to create patient participation for PTV import: %w", err)
		}
		// assign first participation id to participant decision
		if len(ppResponse.Participations) == 0 {
			return nil, errors.New("no participation created")
		}
		err = srv.CreateSchein(ctx, ppResponse.Participations[0])
		if err != nil {
			return nil, fmt.Errorf("failed to create schein: %w", err)
		}
		participantDecision.PpId = ppResponse.Participations[0].Id
		participantDecision.MarkAsDone = true
		participantDecision.PatientId = *patientId
		return &participantDecision, nil
	}).Process(participantDecisionsRequest, func(participantDecision *ptv_import_common.ParticipantDecision, err error) {
		if err != nil {
			ctx.Logger().Error("failed to process participant decision, error: ", err)
			return
		}
		if participantDecision == nil { // DONE case
			return
		}
		imported = append(imported, *participantDecision)
	})
	// for _, participantDecision := range participantDecisionsRequest {
	// 	participantDecision.SetLocalSelected(false)
	// 	participantDecision.SetHpmSelected(true)
	// 	if getParticipantDecisionById(participantDecisionsDb, participantDecision.Id).MarkAsDone {
	// 		continue
	// 	}
	// 	patientId, err := srv.createPatientProfile(ctx, participantDecision)
	// 	if err != nil {
	// 		return nil, fmt.Errorf("failed to create patient profile: %w", err)
	// 	}
	// 	createPatientParticipationRequest := patient_participation.UpdatePatientParticipationForPTVImportRequest{
	// 		DoctorId:        doctorId,
	// 		ContractId:      contractId,
	// 		PatientId:       *patientId,
	// 		IkNumber:        int32(*participantDecision.IkNumber.SelectedValue()),
	// 		InsuranceNumber: participantDecision.InsuranceNumber.SelectedValue(),
	// 		StartDate:       participantDecision.ContractBeginDate.SelectedValue(),
	// 		EndDate:         participantDecision.ContractEndDate.SelectedValue(),
	// 		Status:          patient_participation.PatientParticipationStatus(participantDecision.Status.SelectedValue()),
	// 	}
	// 	ppResponse, err := srv.patientParticipateService.UpdatePatientParticipationForPTVImport(ctx, createPatientParticipationRequest)
	// 	if err != nil {
	// 		return nil, fmt.Errorf("failed to create patient participation for PTV import: %w", err)
	// 	}
	// 	// assign first participation id to participant decision
	// 	if len(ppResponse.Participations) == 0 {
	// 		return nil, errors.New("no participation created")
	// 	}
	// 	err = srv.CreateSchein(ctx, ppResponse.Participations[0])
	// 	if err != nil {
	// 		return nil, fmt.Errorf("failed to create schein: %w", err)
	// 	}
	// 	participantDecision.PpId = ppResponse.Participations[0].Id
	// 	participantDecision.MarkAsDone = true
	// 	participantDecision.PatientId = *patientId
	// 	imported = append(imported, participantDecision)
	// }
	if err != nil {
		return nil, fmt.Errorf("failed to create patient participants: %w", err)
	}
	return imported, nil
}

func (srv *Service) CreateSchein(ctx *titan.Context, pp patient_participation.PatientParticipation) error {
	if pp.Status == patient_participation.PatientParticipation_Active {
		patientProfile, err := srv.patientProfileRepo.GetPatientProfileById(ctx, pp.PatientId)
		if err != nil {
			return err
		}
		activeInsurance := patientProfile.PatientInfo.GetActiveInsurance()
		if activeInsurance == nil {
			activeInsurance = &patientProfile.PatientInfo.InsuranceInfos[0]
		}
		err = srv.scheinService.CreateSvScheins(ctx, schein_common.CreateSvScheinRequest{
			PatientId:             pp.PatientId,
			DoctorId:              *pp.DoctorId,
			PatientParticipations: []patient_participation.PatientParticipation{pp},
			InsuranceInfo:         *activeInsurance,
			StartDate:             pp.StartDate,
			EndDate:               pp.EndDate,
		})
		if err != nil {
			return err
		}
	}
	return nil
}

func (srv *Service) createPatientProfile(ctx *titan.Context, request ptv_import_common.ParticipantDecision) (*uuid.UUID, error) {
	patientInfo := request.ToPatientInfo(ctx)
	createPatientRequest := patient_profile_api.CreatePatientProfileV2Request{
		PatientInfo: &patientInfo,
	}
	patientData, err := srv.patientProfileBff.CreatePatientProfileV2(ctx, &createPatientRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to create patient: %w", err)
	}

	return patientData.Id, nil
}

// updateExistingPatientParticipantsFromHpmData updates existing patient profiles and participations for the given participant decisions.
// It returns a list of successfully updated participant decisions.
// This is similar to createPatientParticipantsFromHpmData but updates instead of creates
func (srv *Service) updateExistingPatientParticipantsFromHpmData(ctx *titan.Context,
	doctorId uuid.UUID,
	contractId string,
	participantDecisionsRequest []ptv_import_common.ParticipantDecision,
	participantDecisionsDb []ptv_import_common.ParticipantDecision,
) ([]ptv_import_common.ParticipantDecision, error) {
	updated := []ptv_import_common.ParticipantDecision{}
	err := function.NewPool[ptv_import_common.ParticipantDecision, *ptv_import_common.ParticipantDecision](
		function.EarlyReturn(),
		function.WithContext(ctx),
		function.WithSize(10),
	).WithFunc(func(participantDecision ptv_import_common.ParticipantDecision) (*ptv_import_common.ParticipantDecision, error) {
		participantDecision.SetLocalSelected(false)
		participantDecision.SetHpmSelected(true)
		if getParticipantDecisionById(participantDecisionsDb, participantDecision.Id).MarkAsDone {
			return nil, nil
		}

		// Update patient profile with HPM data
		err := srv.updatePatientProfileFromHpm(ctx, participantDecision)
		if err != nil {
			return nil, fmt.Errorf("failed to update patient profile: %w", err)
		}

		// Update patient participation
		updatePatientParticipationRequest := patient_participation.UpdatePatientParticipationForPTVImportRequest{
			DoctorId:        doctorId,
			ContractId:      contractId,
			PatientId:       participantDecision.PatientId,
			IkNumber:        int32(*participantDecision.IkNumber.SelectedValue()),
			InsuranceNumber: participantDecision.InsuranceNumber.SelectedValue(),
			StartDate:       participantDecision.ContractBeginDate.SelectedValue(),
			EndDate:         participantDecision.ContractEndDate.SelectedValue(),
			Status:          patient_participation.PatientParticipationStatus(participantDecision.Status.SelectedValue()),
			PpId:            participantDecision.PpId,
			TreatmentType:   participantDecision.TreatmentType.SelectedValue(),
		}
		ppResponse, err := srv.patientParticipateService.UpdatePatientParticipationForPTVImport(ctx, updatePatientParticipationRequest)
		if err != nil {
			return nil, fmt.Errorf("failed to update patient participation for PTV import: %w", err)
		}
		// assign first participation id to participant decision
		if len(ppResponse.Participations) == 0 {
			return nil, errors.New("no participation created")
		}
		err = srv.CreateSchein(ctx, ppResponse.Participations[0])
		if err != nil {
			return nil, fmt.Errorf("failed to create schein: %w", err)
		}
		participantDecision.PpId = ppResponse.Participations[0].Id
		participantDecision.MarkAsDone = true
		return &participantDecision, nil
	}).Process(participantDecisionsRequest, func(participantDecision *ptv_import_common.ParticipantDecision, err error) {
		if err != nil {
			ctx.Logger().Error("failed to process participant decision for update, error: ", err)
			return
		}
		if participantDecision == nil { // DONE case
			return
		}
		updated = append(updated, *participantDecision)
	})
	if err != nil {
		return nil, fmt.Errorf("failed to update patient participants: %w", err)
	}
	return updated, nil
}

// updatePatientProfileFromHpm updates an existing patient profile with data from HPM
func (srv *Service) updatePatientProfileFromHpm(ctx *titan.Context, participantDecision ptv_import_common.ParticipantDecision) error {
	// Only update if there's a valid patient ID
	if participantDecision.PatientId == uuid.Nil {
		return errors.New("patient ID is required for update")
	}

	// First, get the existing patient profile to merge with HPM data
	existingPatient, err := srv.patientProfileBff.GetPatientProfileById(ctx, &patient_profile_api.GetById{
		Id: participantDecision.PatientId.String(),
	})
	if err != nil {
		return fmt.Errorf("failed to get existing patient profile: %w", err)
	}

	// Update patient info fields with HPM data if available
	patientInfo := existingPatient.PatientInfo
	if patientInfo == nil {
		return errors.New("patient info not found in existing profile")
	}

	// Update personal info with HPM data
	if participantDecision.FirstName.HpmFirstName.Value != "" {
		patientInfo.PersonalInfo.FirstName = participantDecision.FirstName.HpmFirstName.Value
	}
	if participantDecision.LastName.HpmLastName.Value != "" {
		patientInfo.PersonalInfo.LastName = participantDecision.LastName.HpmLastName.Value
	}
	if participantDecision.Dob.HpmDOB.Value != nil {
		patientInfo.PersonalInfo.DOB = *participantDecision.Dob.HpmDOB.Value
		patientInfo.PersonalInfo.DateOfBirth.IsValidDOB = true
	}
	if participantDecision.Gender.HpmGender.Value != "" {
		patientInfo.PersonalInfo.Gender = participantDecision.Gender.HpmGender.Value
	}

	// Update insurance information if available from HPM
	if participantDecision.InsuranceNumber.HpmInsuranceNumber.Value != "" && participantDecision.IkNumber.HpmIkNumber.Value != nil {
		// Create new insurance info from HPM data
		newInsurance := patient_profile_common.InsuranceInfo{
			Id:              uuid.New(),
			IkNumber:        int32(*participantDecision.IkNumber.HpmIkNumber.Value),
			InsuranceNumber: &participantDecision.InsuranceNumber.HpmInsuranceNumber.Value,
			IsActive:        true,                                               // Set as active since it's coming from HPM
			InsuranceType:   patient_profile_common.Public,                      // Public insurance from HPM/PTV
			InsuranceStatus: patient_profile_common.InsuranceStatus("Mitglied"), // Member status
			SpecialGroup:    patient_profile_common.SpecialGroup_00,             // Standard special group
		}

		// Check if this insurance already exists (by insurance number and IK number)
		insuranceExists := false
		for _, existingInsurance := range patientInfo.InsuranceInfos {
			if existingInsurance.InsuranceNumber != nil &&
				*existingInsurance.InsuranceNumber == participantDecision.InsuranceNumber.HpmInsuranceNumber.Value &&
				int64(existingInsurance.IkNumber) == *participantDecision.IkNumber.HpmIkNumber.Value {
				insuranceExists = true
				break
			}
		}

		// Only append if it doesn't already exist
		if !insuranceExists {
			// Deactivate other insurances if this is being set as active
			for i := range patientInfo.InsuranceInfos {
				patientInfo.InsuranceInfos[i].IsActive = false
			}

			// Append the new insurance
			patientInfo.InsuranceInfos = append(patientInfo.InsuranceInfos, newInsurance)
		}
	}

	// Update patient profile using BFF
	updateRequest := &patient_profile_api.UpdatePatientProfileV2Request{
		Id:          &participantDecision.PatientId,
		PatientInfo: patientInfo,
	}

	_, err = srv.patientProfileBff.UpdatePatientProfileV2(ctx, updateRequest)
	if err != nil {
		return fmt.Errorf("failed to update patient profile: %w", err)
	}

	// Update insurance information separately if needed
	if len(patientInfo.InsuranceInfos) > 0 {
		// Convert insurance infos to pointers
		insuranceInfoPtrs := make([]*patient_profile_common.InsuranceInfo, len(patientInfo.InsuranceInfos))
		for i := range patientInfo.InsuranceInfos {
			insuranceInfoPtrs[i] = &patientInfo.InsuranceInfos[i]
		}

		updateInsurancesRequest := &patient_profile_api.UpdateInsurancesRequest{
			PatientId:             &participantDecision.PatientId,
			InsuranceInfos:        insuranceInfoPtrs,
			InsuranceInfosDeleted: []*patient_profile_common.InsuranceInfo{},
		}

		_, err = srv.patientProfileBff.UpdateInsurances(ctx, updateInsurancesRequest)
		if err != nil {
			// Log error but don't fail the entire update
			ctx.Logger().Error("failed to update insurance information", err)
		}
	}

	return nil
}

// processExistingPatientParticipations processes participants that need updates to existing patient profiles
// This can be used as an alternative to processNewPatientParticipations when you want to update instead of create
func (srv *Service) processExistingPatientParticipations(ctx *titan.Context, request ptv_import_common.ImportParticipantsRequest) error {
	// Get participant decisions from database
	participantDecisionsDb, err := srv.repo.GetParticipantDecisionByIds(
		ctx,
		request.Id,
		string(ptv_import_common.ImportType_Missing),       // Use existing import type
		extractParticipantIds(request.MissingParticipants), // Use existing field
	)
	if err != nil {
		return fmt.Errorf("failed to get participant decisions: %w", err)
	}

	// Update existing patients with HPM data
	updatedPatients, err := srv.updateExistingPatientParticipantsFromHpmData(
		ctx,
		request.DoctorId,
		request.ContractId,
		request.MissingParticipants, // Use existing field
		participantDecisionsDb,
	)
	if err != nil {
		return fmt.Errorf("failed to update patient participants: %w", err)
	}

	participantDecisions := make([]ptv_import_common.ParticipantDecision, 0, len(updatedPatients))
	// Update the decision records to mark them as processed
	for _, participant := range updatedPatients {
		var participantRepo ptv_import_common.ParticipantDecision
		if err := copier.Copy(&participantRepo, participant); err != nil {
			return fmt.Errorf("failed to copy participant data: %w", err)
		}
		participantDecisions = append(participantDecisions, participantRepo)
	}

	if err := srv.repo.UpdateParticipantDecision(
		ctx,
		request.Id,
		string(ptv_import_common.ImportType_Missing), // Use existing import type
		participantDecisions,
	); err != nil {
		return fmt.Errorf("failed to update participant decision: %w", err)
	}
	return nil
}

// processNewPatientParticipations processes missing participants that need new patient profiles
func (srv *Service) processNewPatientParticipations(ctx *titan.Context, request ptv_import_common.ImportParticipantsRequest) error {
	participantDecisionsDb, err := srv.repo.GetParticipantDecisionByIds(
		ctx,
		request.Id,
		string(ptv_import_common.ImportType_Missing),
		extractParticipantIds(request.MissingParticipants),
	)
	if err != nil {
		return fmt.Errorf("failed to get participant decisions: %w", err)
	}

	// Create new patients from HPM data
	importedPatients, err := srv.createPatientParticipantsFromHpmData(
		ctx,
		request.DoctorId,
		request.ContractId,
		request.MissingParticipants,
		participantDecisionsDb,
	)
	if err != nil {
		return fmt.Errorf("failed to create patient participants: %w", err)
	}

	participantDecisions := make([]ptv_import_common.ParticipantDecision, 0, len(importedPatients))
	// Update the decision records to mark them as processed
	for _, participant := range importedPatients {
		var participantRepo ptv_import_common.ParticipantDecision
		if err := copier.Copy(&participantRepo, participant); err != nil {
			return fmt.Errorf("failed to copy participant data: %w", err)
		}
		participantDecisions = append(participantDecisions, participantRepo)
	}

	if err := srv.repo.UpdateParticipantDecision(
		ctx,
		request.Id,
		string(ptv_import_common.ImportType_Missing),
		participantDecisions,
	); err != nil {
		return fmt.Errorf("failed to update participant decision: %w", err)
	}
	return nil
}

func validateParticipandDecision(importParticipant ptv_import_common.ParticipantDecision) error {
	var validationError error
	if _, validationError = getSelectedValueNumber(importParticipant.IkNumber.HpmIkNumber, importParticipant.IkNumber.LocalIkNumber); validationError != nil {
		return validationError
	}
	if _, validationError = getSelectedValueString(importParticipant.InsuranceNumber.HpmInsuranceNumber, importParticipant.InsuranceNumber.LocalInsuranceNumber); validationError != nil {
		return validationError
	}
	if _, validationError = getSelectedValueStatus(importParticipant.Status.HpmStatus, importParticipant.Status.LocalStatus); validationError != nil {
		return validationError
	}
	if _, validationError = getSelectedValueString(importParticipant.FirstName.HpmFirstName, importParticipant.FirstName.LocalFirstName); validationError != nil {
		return validationError
	}
	if _, validationError = getSelectedValueString(importParticipant.LastName.HpmLastName, importParticipant.LastName.LocalLastName); validationError != nil {
		return validationError
	}
	if _, validationError = getSelectedValueGender(importParticipant.Gender.HpmGender, importParticipant.Gender.LocalGender); validationError != nil {
		return validationError
	}
	if _, validationError = getSelectedValueNumber(importParticipant.Dob.HpmDOB, importParticipant.Dob.LocalDOB); validationError != nil {
		return validationError
	}
	if _, validationError = getSelectedValueNumber(importParticipant.ContractBeginDate.HpmContractBeginDate, importParticipant.ContractBeginDate.LocalContractBeginDate); validationError != nil {
		return validationError
	}
	if _, validationError = getSelectedValueNumber(importParticipant.ContractEndDate.HpmContractEndDate, importParticipant.ContractEndDate.LocalContractEndDate); validationError != nil {
		return validationError
	}
	return nil
}

func getSelectedValueString(s1, s2 ptv_import_common.StringSelection) (string, error) { //nolint: unparam
	if s1.Selected {
		return s1.Value, nil
	}
	if s2.Selected {
		return s2.Value, nil
	}
	if s1.Value == s2.Value {
		return s1.Value, nil
	}
	return "", ErrNoSelectedValue
}

func getSelectedValueGender(s1, s2 ptv_import_common.GenderSelection) (patient_profile_common.Gender, error) { //nolint: unparam
	if s1.Selected {
		return s1.Value, nil
	}
	if s2.Selected {
		return s2.Value, nil
	}
	if s1.Value == s2.Value {
		return s1.Value, nil
	}
	return "", ErrNoSelectedValue
}

func getSelectedValueStatus(s1, s2 ptv_import_common.StatusSelection) (string, error) { //nolint: unparam
	if s1.Selected {
		return string(s1.Value), nil
	}
	if s2.Selected {
		return string(s2.Value), nil
	}
	if s1.Value == s2.Value {
		return string(s1.Value), nil
	}
	return "", ErrNoSelectedValue
}

func getSelectedValueNumber(s1, s2 ptv_import_common.NumberSelection) (*int64, error) { //nolint: unparam
	if s1.Selected {
		return s1.Value, nil
	}
	if s2.Selected {
		return s2.Value, nil
	}
	if util.GetPointerValue(s1.Value) == util.GetPointerValue(s2.Value) {
		return s1.Value, nil
	}
	return nil, ErrNoSelectedValue
}

// createEmptyParticipantsResponse creates an empty response for the GetParticipantsByDoctor method
func createEmptyParticipantsResponse() *ptv_import_common.GetParticipantsByDoctorResponse {
	return &ptv_import_common.GetParticipantsByDoctorResponse{
		AutoImportParticipants: []ptv_import_common.ParticipantDecision{},
		ConflictParticipants:   []ptv_import_common.ParticipantDecision{},
		MissingParticipants:    []ptv_import_common.ParticipantDecision{},
		Year:                   int64(0),
		Quarter:                int64(0),
	}
}

func getParticipantDecisionById(ps []ptv_import_common.ParticipantDecision, findingId uuid.UUID) (ret ptv_import_common.ParticipantDecision) {
	for _, p := range ps {
		if p.Id == findingId {
			return p
		}
	}
	return
}

func isTreatmentTypeChanged(hpm, local ptv_import_common.ParticipantDecision) bool {
	if hpm.Status.HpmStatus.Value == ptv_import_common.PatientParticipation_Active &&
		local.Status.LocalStatus.Value == ptv_import_common.PatientParticipation_Active &&
		hpm.TreatmentType.HpmTreatmentType.Value != local.TreatmentType.LocalTreatmentType.Value {
		return true
	}
	return false
}

func isUnchanged(hpm, local ptv_import_common.ParticipantDecision) bool {
	hpmBeginDate := hpm.ContractBeginDate.HpmContractBeginDate.GetStartOfDay()
	localBeginDate := local.ContractBeginDate.LocalContractBeginDate.GetStartOfDay()

	hpmEndDate := hpm.ContractEndDate.HpmContractEndDate.GetEndOfDay()
	localEndDate := local.ContractEndDate.LocalContractEndDate.GetEndOfDay()

	if hpm.Status.HpmStatus.Value == local.Status.LocalStatus.Value &&
		hpmBeginDate.Equal(localBeginDate) &&
		hpmEndDate.Equal(localEndDate) {
		return true
	}
	return false
}

func isRegistered(hpm, local ptv_import_common.ParticipantDecision) bool {
	if hpm.Status.HpmStatus.Value == ptv_import_common.PatientParticipation_Active &&
		isRequestedOrNa(local.Status.LocalStatus) {
		return true
	}
	return false
}

func isTerminated(hpm, local ptv_import_common.ParticipantDecision) bool {
	isHpmTerminated := hpm.Status.HpmStatus.Value == ptv_import_common.PatientParticipation_Terminated
	isLocalActive := local.Status.LocalStatus.Value == ptv_import_common.PatientParticipation_Active
	isLocalEmpty := local.Status.LocalStatus.Value == ""

	if isHpmTerminated && (isLocalEmpty || isLocalActive) {
		return true
	}

	return false
}

func isRequested(hpm, local ptv_import_common.ParticipantDecision) bool {
	if hpm.Status.HpmStatus.Value == ptv_import_common.PatientParticipation_Requested &&
		isRequestedOrNa(local.Status.LocalStatus) {
		return true
	}
	return false
}

func isRejected(hpm, local ptv_import_common.ParticipantDecision) bool {
	if hpm.Status.HpmStatus.Value == ptv_import_common.PatientParticipation_Rejected &&
		isRequestedOrNa(local.Status.LocalStatus) {
		return true
	}
	return false
}

func isRequestedOrNa(p ptv_import_common.StatusSelection) bool {
	return p.Value == ptv_import_common.PatientParticipation_Requested ||
		p.Value == "" ||
		p.Value == ptv_import_common.PatientParticipation_Cancelled
}

func mapPTVPatientParticipation2ParticipantDecision(participation hpm_rest_hzv_client.PTVPatientParticipation) ptv_import_common.ParticipantDecision {
	ikNumber, _ := strconv.ParseInt(participation.IkNumber, 10, 64)
	return ptv_import_common.ParticipantDecision{
		IkNumber: ptv_import_common.IkNumber{HpmIkNumber: ptv_import_common.NumberSelection{Value: &ikNumber}},
		InsuranceNumber: ptv_import_common.InsuranceNumber{
			HpmInsuranceNumber: ptv_import_common.StringSelection{Value: participation.InsuranceNumber},
		},
		Status: ptv_import_common.Status{HpmStatus: ptv_import_common.StatusSelection{Value: convertStatus(participation.Status)}},
		TreatmentType: ptv_import_common.TreatmentType{HpmTreatmentType: ptv_import_common.StringSelection{
			Value: "Custodian",
		}},
		FirstName: ptv_import_common.FirstName{HpmFirstName: ptv_import_common.StringSelection{
			Value: participation.Names.FirstName,
		}},
		LastName: ptv_import_common.LastName{HpmLastName: ptv_import_common.StringSelection{
			Value: participation.Names.LastName,
		}},
		Reason:            ptv_import_common.Reason{HpmReason: ptv_import_common.StringSelection{Value: participation.Reason}},
		Dob:               ptv_import_common.Dob{HpmDOB: ptv_import_common.NumberSelection{Value: &participation.Dob}},
		ContractBeginDate: ptv_import_common.ContractBeginDate{HpmContractBeginDate: ptv_import_common.NumberSelection{Value: &participation.ContractBeginDate}},
		ContractEndDate:   ptv_import_common.ContractEndDate{HpmContractEndDate: ptv_import_common.NumberSelection{Value: participation.ContractEndDate}},
		Gender:            ptv_import_common.Gender{HpmGender: ptv_import_common.GenderSelection{Value: convertGender(participation.Gender)}},
	}
}

func convertStatus(status hpm_rest_hzv_client.ContractStatus) ptv_import_common.PatientParticipationStatus {
	switch status {
	case hpm_rest_hzv_client.ContractStatus_Requested:
		return ptv_import_common.PatientParticipation_Requested
	case hpm_rest_hzv_client.ContractStatus_Rejected:
		return ptv_import_common.PatientParticipation_Rejected
	case hpm_rest_hzv_client.ContractStatus_Active:
		return ptv_import_common.PatientParticipation_Active
	case hpm_rest_hzv_client.ContractStatus_Cancelled:
		return ptv_import_common.PatientParticipation_Cancelled
	case hpm_rest_hzv_client.ContractStatus_Terminated:
		return ptv_import_common.PatientParticipation_Terminated
	default:
		return ""
	}
}

func convertGender(gender hpm_rest_hzv_client.Gender) patient_profile_common.Gender {
	switch gender {
	case hpm_rest_hzv_client.Male:
		return patient_profile_common.M
	case hpm_rest_hzv_client.Female:
		return patient_profile_common.W
	case hpm_rest_hzv_client.Undetermined:
		return patient_profile_common.X
	default:
		return patient_profile_common.U
	}
}
