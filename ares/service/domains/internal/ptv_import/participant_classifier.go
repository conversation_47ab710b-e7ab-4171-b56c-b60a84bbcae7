package ptv_import

import (
	"fmt"
	"time"

	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_participation"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	ptv_import_common "git.tutum.dev/medi/tutum/ares/service/domains/ptv_import/common"
	"git.tutum.dev/medi/tutum/pkg/function"
	time_util "git.tutum.dev/medi/tutum/pkg/util"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/google/uuid"
	"gitlab.com/silenteer-oss/hestia/infrastructure/util"
	"gitlab.com/silenteer-oss/titan"
)

// ParticipantClassifier groups and categorizes participant decisions based on business rules
type ParticipantClassifier struct {
	AutoImport             []ptv_import_common.ParticipantDecision
	Conflict               []ptv_import_common.ParticipantDecision
	Missing                []ptv_import_common.ParticipantDecision
	timeData               time_util.YearQuarter
	BeforeParticipantCount int
	AfterParticipantCount  int
}

// NewParticipantClassifier creates a new classifier for participants
func NewParticipantClassifier(year, quarter int64) *ParticipantClassifier {
	return &ParticipantClassifier{
		AutoImport: []ptv_import_common.ParticipantDecision{},
		Conflict:   []ptv_import_common.ParticipantDecision{},
		Missing:    []ptv_import_common.ParticipantDecision{},
		timeData: time_util.YearQuarter{
			Year:     int32(year),
			Quarter:  int32(quarter),
			Location: time.UTC,
		},
		BeforeParticipantCount: 0,
		AfterParticipantCount:  0,
	}
}

// Make sure this func is called after `Classify`
func (pc *ParticipantClassifier) countParticipants(
	localAllParticipants []patient_participation.PatientParticipationPTVImport) {
	currentActiveCount := slice.CountBy(localAllParticipants, func(_ int, item patient_participation.PatientParticipationPTVImport) bool {
		return item.Status == patient_participation.PatientParticipation_Active
	})

	mergedHpmData := append(pc.AutoImport, pc.Conflict...)
	mergedHpmData = append(mergedHpmData, pc.Missing...)

	newActiveCount := slice.CountBy(mergedHpmData, func(_ int, item ptv_import_common.ParticipantDecision) bool {
		return item.Status.HpmStatus.Value == ptv_import_common.PatientParticipation_Active &&
			item.Status.LocalStatus.Value != ptv_import_common.PatientParticipation_Active
	})

	pc.BeforeParticipantCount = currentActiveCount
	pc.AfterParticipantCount = currentActiveCount + newActiveCount
}

// Classify runs the classification algorithm on HPM and local data
func (pc *ParticipantClassifier) Classify(
	localAllParticipants []patient_participation.PatientParticipationPTVImport,
	hpmData, localData []ptv_import_common.ParticipantDecision) {
	// Map data for efficient lookup
	mapHpm := createParticipantMap(hpmData, true)
	mapLocal := createLocalParticipantMap(localData)

	// Process matches between HPM and local data
	for insuranceNumber, hpmParticipant := range mapHpm {
		if localParticipants, ok := mapLocal[insuranceNumber]; ok {
			if len(localParticipants) > 1 {
				// Multiple local participants match one HPM participant
				pc.processMultipleLocalMatches(hpmParticipant)
			} else {
				// Single match
				pc.processMatchingParticipant(hpmParticipant, localParticipants[0])
			}
			delete(mapLocal, insuranceNumber)
		} else {
			pc.processMissingLocalParticipant(hpmParticipant)
		}
	}

	// Process remaining local participants (not in HPM)
	for _, localParticipants := range mapLocal {
		for _, localParticipant := range localParticipants {
			pc.processMissingHpmParticipant(localParticipant)
		}
	}

	pc.countParticipants(localAllParticipants)
}

// Create map of participants keyed by insurance number for efficient lookup
func createParticipantMap(participants []ptv_import_common.ParticipantDecision, isHpm bool) map[string]ptv_import_common.ParticipantDecision {
	result := make(map[string]ptv_import_common.ParticipantDecision, len(participants))

	for _, p := range participants {
		key := ""
		if isHpm {
			key = p.InsuranceNumber.HpmInsuranceNumber.Value
		} else {
			key = p.InsuranceNumber.LocalInsuranceNumber.Value
		}
		result[key] = p
	}

	return result
}

// createLocalParticipantMap creates a map that can store multiple participants per insurance number
func createLocalParticipantMap(participants []ptv_import_common.ParticipantDecision) map[string][]ptv_import_common.ParticipantDecision {
	result := make(map[string][]ptv_import_common.ParticipantDecision)

	for _, p := range participants {
		key := p.InsuranceNumber.LocalInsuranceNumber.Value
		result[key] = append(result[key], p)
	}

	return result
}

// processMatchingParticipant analyzes matching participants and categorizes them
func (pc *ParticipantClassifier) processMatchingParticipant(hpm, local ptv_import_common.ParticipantDecision) {
	// Create merged decision record with data from both sources
	mergedDecision := createMergedDecision(hpm, local)

	// Check if this is a true match (same insurance, DOB, and IK)
	isBasicMatch := isBasicIdentityMatch(hpm, local)

	if isBasicMatch {
		if classifyToAutoImport(hpm, local, mergedDecision) {
			pc.AutoImport = append(pc.AutoImport, *mergedDecision)
		} else {
			mergedDecision.TypeGroupDecision = ptv_import_common.SpecialGroup
			pc.Conflict = append(pc.Conflict, *mergedDecision)
		}
	} else {
		mergedDecision.TypeGroupDecision = ptv_import_common.SpecialGroup
		pc.Conflict = append(pc.Conflict, *mergedDecision)
	}
}

// processMissingLocalParticipant handles participants in HPM but not in local system
func (pc *ParticipantClassifier) processMissingLocalParticipant(hpm ptv_import_common.ParticipantDecision) {
	hpm.Id = uuid.New()
	hpm.TypeGroupDecision = ptv_import_common.MissingGroupPTV
	hpm.Hints = []string{"Dieser Patient kann nicht ermittelt werden, rufen Sie den Support der HÄVG für weitere Informationen an."}
	pc.Missing = append(pc.Missing, hpm)
}

// processMissingHpmParticipant handles participants in local system but not in HPM
func (pc *ParticipantClassifier) processMissingHpmParticipant(local ptv_import_common.ParticipantDecision) {
	local.Id = uuid.New()
	local.MarkAsDone = true // Don't process these automatically
	local.TypeGroupDecision = ptv_import_common.MissingGroupIV
	local.Hints = []string{"Dieser Patient existiert nicht im Patiententeilnehmerverzeichnis, bitte beachten Sie das dass zu Abrechnungsproblemen führen kann. Wenn dieser Patient kein aktiver Teilnehmer ist, beenden Sie bitte die Teilnahme."}
	pc.Missing = append(pc.Missing, local)
}

// isBasicIdentityMatch checks if two participant records refer to the same person
func isBasicIdentityMatch(hpm, local ptv_import_common.ParticipantDecision) bool {
	// Must match on these key identifiers
	return hpm.InsuranceNumber.HpmInsuranceNumber.Value == local.InsuranceNumber.LocalInsuranceNumber.Value
}

// createMergedDecision creates a new decision record combining HPM and local data
func createMergedDecision(hpm, local ptv_import_common.ParticipantDecision) *ptv_import_common.ParticipantDecision {
	return &ptv_import_common.ParticipantDecision{
		Id:                uuid.New(),
		PatientId:         local.PatientId,
		PpId:              local.PpId,
		IkNumber:          ptv_import_common.IkNumber{HpmIkNumber: hpm.IkNumber.HpmIkNumber, LocalIkNumber: local.IkNumber.LocalIkNumber},
		InsuranceNumber:   ptv_import_common.InsuranceNumber{HpmInsuranceNumber: hpm.InsuranceNumber.HpmInsuranceNumber, LocalInsuranceNumber: local.InsuranceNumber.LocalInsuranceNumber},
		Status:            ptv_import_common.Status{HpmStatus: hpm.Status.HpmStatus, LocalStatus: local.Status.LocalStatus},
		FirstName:         ptv_import_common.FirstName{HpmFirstName: hpm.FirstName.HpmFirstName, LocalFirstName: local.FirstName.LocalFirstName},
		LastName:          ptv_import_common.LastName{HpmLastName: hpm.LastName.HpmLastName, LocalLastName: local.LastName.LocalLastName},
		Reason:            ptv_import_common.Reason{HpmReason: hpm.Reason.HpmReason},
		Gender:            ptv_import_common.Gender{HpmGender: hpm.Gender.HpmGender, LocalGender: local.Gender.LocalGender},
		Dob:               ptv_import_common.Dob{HpmDOB: hpm.Dob.HpmDOB, LocalDOB: local.Dob.LocalDOB},
		ContractBeginDate: ptv_import_common.ContractBeginDate{HpmContractBeginDate: hpm.ContractBeginDate.HpmContractBeginDate, LocalContractBeginDate: local.ContractBeginDate.LocalContractBeginDate},
		ContractEndDate:   ptv_import_common.ContractEndDate{HpmContractEndDate: hpm.ContractEndDate.HpmContractEndDate, LocalContractEndDate: local.ContractEndDate.LocalContractEndDate},
	}
}

// classifyToAutoImport determines if a participant can be auto-imported based on business rules
func classifyToAutoImport(hpm, local ptv_import_common.ParticipantDecision, result *ptv_import_common.ParticipantDecision) bool {
	if hpm.Status.HpmStatus.Value != local.Status.LocalStatus.Value {
		result.Hints = []string{"Es sind widersprüchliche Angaben im Patiententeilnehmerverzeichnis und ihrer Software. Bitte führen Sie eine Onlineteilnahmeprüfung durch, um sicherzustellen, dass Sie korrekt abrechnen."}
	}

	if isUnchanged(hpm, local) {
		result.TypeGroupDecision = ptv_import_common.GeneralGroupUnchanged
		return true
	}

	if isTerminated(hpm, local) {
		result.Hints = []string{}
		result.TypeGroupDecision = ptv_import_common.GeneralGroupTerminated
		return true
	}

	if isRequested(hpm, local) {
		result.TypeGroupDecision = ptv_import_common.GeneralGroupRequested
		return true
	}

	if isRejected(hpm, local) {
		result.TypeGroupDecision = ptv_import_common.GeneralGroupRejected
		return true
	}

	if isRegistered(hpm, local) {
		result.TypeGroupDecision = ptv_import_common.GeneralGroupNew
		return true
	}

	return false
}

// BatchProcessParticipants processes a batch of participants for import
// It handles validation, patient profile updates, and participation updates
func (srv *Service) BatchProcessParticipants(
	ctx *titan.Context,
	participants []ptv_import_common.ParticipantDecision,
	ptvImportHistoryId uuid.UUID,
	doctorId uuid.UUID,
	contractId string,
	importType ptv_import_common.ImportParticipantsType,
) error {
	// Get all participant decisions from database
	participantDecisions, err := srv.repo.GetParticipantDecisionByIds(
		ctx,
		ptvImportHistoryId,
		string(importType),
		extractParticipantIds(participants),
	)
	if err != nil {
		return fmt.Errorf("failed to get participant decisions: %w", err)
	}

	// Create map for efficient lookup
	participantMap := make(map[uuid.UUID]ptv_import_common.ParticipantDecision)
	for _, pd := range participantDecisions {
		participantMap[pd.Id] = pd
	}

	updatedParticipantDecisions := make([]ptv_import_common.ParticipantDecision, 0, len(participants))
	err = function.NewPool[ptv_import_common.ParticipantDecision, *ptv_import_common.ParticipantDecision](
		function.EarlyReturn(),
		function.WithContext(ctx),
		function.WithSize((10)),
	).WithFunc(func(participant ptv_import_common.ParticipantDecision) (*ptv_import_common.ParticipantDecision, error) {
		dbParticipant, exists := participantMap[participant.Id]
		if !exists || dbParticipant.MarkAsDone {
			return nil, nil
		}

		if err := srv.processParticipant(ctx, participant, dbParticipant, doctorId, contractId); err != nil {
			return nil, err
		}

		dbParticipant.IkNumber = participant.IkNumber
		// Mark as done
		dbParticipant.MarkAsDone = true
		return &dbParticipant, nil
	}).Process(participants, func(participant *ptv_import_common.ParticipantDecision, err error) {
		if err != nil {
			return
		}
		if participant == nil {
			return
		}
		updatedParticipantDecisions = append(updatedParticipantDecisions, *participant)
	})
	if err != nil {
		return err
	}

	if err := srv.repo.UpdateParticipantDecision(ctx, ptvImportHistoryId, string(importType), updatedParticipantDecisions); err != nil {
		return fmt.Errorf("failed to update participant decision: %w", err)
	}

	return nil
}

// processParticipant handles validation and updating for a single participant
func (srv *Service) processParticipant(
	ctx *titan.Context,
	participant ptv_import_common.ParticipantDecision,
	dbParticipant ptv_import_common.ParticipantDecision,
	doctorId uuid.UUID,
	contractId string,
) error {
	// Validate selections
	if err := validateParticipandDecision(participant); err != nil {
		return err
	}

	// Update patient profile
	if _, err := srv.updatePatientProfile(ctx, participant); err != nil {
		return err
	}

	// Update patient participation
	err := srv.updatePatientParticipation(ctx, participant, dbParticipant, doctorId, contractId)
	if err != nil {
		return err
	}

	return nil
}

// updatePatientProfile updates the patient profile with selected data
func (srv *Service) updatePatientProfile(ctx *titan.Context, participant ptv_import_common.ParticipantDecision) (bool, error) {
	_, err := srv.patientProfileService.UpdateProfile(ctx, &profile.PatientProfileUpdateRequest{
		PatientId:   &participant.PatientId,
		FirstName:   participant.FirstName.SelectedValue(),
		LastName:    participant.LastName.SelectedValue(),
		DateOfBirth: participant.Dob.SelectedValue(),
		Gender:      patient_profile_common.Gender(participant.Gender.SelectedValue()),
	})
	if err != nil {
		return false, fmt.Errorf("failed to update patient profile: %w", err)
	}

	return true, nil
}

// updatePatientParticipation updates the patient participation data
func (srv *Service) updatePatientParticipation(
	ctx *titan.Context,
	participant ptv_import_common.ParticipantDecision,
	dbParticipant ptv_import_common.ParticipantDecision,
	doctorId uuid.UUID,
	contractId string,
) error {
	ikNumberValue := participant.IkNumber.SelectedValue()
	if ikNumberValue == nil {
		return fmt.Errorf("IK number is required but not selected")
	}

	request := patient_participation.UpdatePatientParticipationForPTVImportRequest{
		DoctorId:        doctorId,
		ContractId:      contractId,
		PatientId:       dbParticipant.PatientId,
		IkNumber:        int32(time_util.GetPointerValue(ikNumberValue)),
		InsuranceNumber: participant.InsuranceNumber.SelectedValue(),
		Status:          patient_participation.PatientParticipationStatus(participant.Status.SelectedValue()),
		StartDate:       participant.ContractBeginDate.SelectedValue(),
		EndDate:         participant.ContractEndDate.SelectedValue(),
		Reason:          time_util.NewPointer(participant.Reason.SelectedValue()),
		PpId:            dbParticipant.PpId,
	}
	rs, err := srv.patientParticipateService.UpdatePatientParticipationForPTVImport(ctx, request)
	if err != nil {
		return fmt.Errorf("failed to update patient participation: %w", err)
	}
	if len(rs.Participations) == 0 {
		return fmt.Errorf("no participation created")
	}
	err = srv.CreateSchein(ctx, rs.Participations[0])
	if err != nil {
		return err
	}
	return nil
}

// extractParticipantIds extracts UUIDs from participant decisions
func extractParticipantIds(participants []ptv_import_common.ParticipantDecision) []uuid.UUID {
	ids := make([]uuid.UUID, 0, len(participants))
	for _, p := range participants {
		ids = append(ids, p.Id)
	}
	return ids
}

// ImportHistoryTracker handles tracking and reporting of import operations
type ImportHistoryTracker struct {
	Id            uuid.UUID
	repo          *Repository
	documentId    string
	doctorId      uuid.UUID
	contractId    string
	year          int64
	quarter       int64
	importHistory *ptv_import_common.PtvImportHistory
}

// NewImportHistoryTracker creates a new tracker for import operations
func NewImportHistoryTracker(
	repo *Repository,
	documentId string,
	doctorId uuid.UUID,
	contractId string,
	year, quarter int64,
	historyId uuid.UUID,
) *ImportHistoryTracker {
	return &ImportHistoryTracker{
		Id:         historyId,
		repo:       repo,
		documentId: documentId,
		doctorId:   doctorId,
		contractId: contractId,
		year:       year,
		quarter:    quarter,
	}
}

// BeginImport starts an import operation and marks it as in-progress
func (t *ImportHistoryTracker) BeginImport(ctx *titan.Context) error {
	err := t.repo.UpdatePtvImportHistoryStatus(ctx, t.Id, string(ptv_import_common.ImportContractStatus_InProgress))
	if err != nil {
		return fmt.Errorf("failed to update contract status to in-progress: %w", err)
	}
	return nil
}

// CompleteImport marks an import as complete
func (t *ImportHistoryTracker) CompleteImport(ctx *titan.Context, hasConflictUnresolved bool) error {
	status := ptv_import_common.ImportContractStatus_Done
	if hasConflictUnresolved {
		status = ptv_import_common.ImportContractStatus_Pending
	}
	err := t.repo.UpdatePtvImportHistoryStatus(ctx, t.Id, string(status))
	if err != nil {
		return fmt.Errorf("failed to update contract status to done: %w", err)
	}
	return nil
}

// InitializeHistory creates a new import history record
func (t *ImportHistoryTracker) InitializeHistory(ctx *titan.Context) error {
	historyId, err := uuid.NewUUID()
	if err != nil {
		return fmt.Errorf("failed to generate UUID: %w", err)
	}

	now := util.Now(ctx).UnixNano() / int64(time.Millisecond)
	importerId := uuid.MustParse(ctx.UserInfo().UserId.String())

	t.importHistory = &ptv_import_common.PtvImportHistory{
		Id:                     historyId,
		DoctorId:               t.doctorId,
		ContractId:             t.contractId,
		DocumentId:             t.documentId,
		Year:                   t.year,
		Quarter:                t.quarter,
		BeforeParticipantCount: 0,
		AfterParticipantCount:  0,
		CreateTime:             now,
		UpdateTime:             now,
		ImporterId:             importerId,
		Status:                 ptv_import_common.ImportContractStatus_New,
	}

	return nil
}

// SaveClassifiedParticipants saves classified participants to the import history
func (t *ImportHistoryTracker) SaveClassifiedParticipants(ctx *titan.Context, classifier *ParticipantClassifier) error {
	if t.importHistory == nil {
		return fmt.Errorf("import history not initialized")
	}

	t.importHistory.BeforeParticipantCount = int64(classifier.BeforeParticipantCount)
	t.importHistory.AfterParticipantCount = int64(classifier.AfterParticipantCount)
	t.importHistory.AutoImportParticipants = classifier.AutoImport
	t.importHistory.ConflictParticipants = classifier.Conflict
	t.importHistory.MissingParticipants = classifier.Missing

	_, err := t.repo.CreatePtvImportHistory(ctx, *t.importHistory)
	if err != nil {
		return fmt.Errorf("failed to create import history: %w", err)
	}

	return nil
}

// GetImportHistory returns the current import history record
func (t *ImportHistoryTracker) GetImportHistory() *ptv_import_common.PtvImportHistory {
	return t.importHistory
}

// processMultipleLocalMatches handles cases where one HPM participant matches multiple local participants
func (pc *ParticipantClassifier) processMultipleLocalMatches(hpm ptv_import_common.ParticipantDecision) {
	// Mark HPM participant as MissingGroupPTV when it matches multiple local participants
	hpm.Id = uuid.New()
	hpm.TypeGroupDecision = ptv_import_common.MissingGroupPTV
	hpm.Hints = []string{"Diese Versicherungsnummer existiert mehrmals in ihrer Software, bitte weisen Sie den richtigen Patienten zu."}
	pc.Missing = append(pc.Missing, hpm)
}
