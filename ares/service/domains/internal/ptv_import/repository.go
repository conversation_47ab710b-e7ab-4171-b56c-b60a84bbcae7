package ptv_import

import (
	"fmt"
	"strings"

	"git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/pkg/slice"

	ptv_import_common "git.tutum.dev/medi/tutum/ares/service/domains/ptv_import/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/ptv_import"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/ptv_import_history"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Repository struct {
	ptv_import.PtvImportRepo
	ptv_import_history.PtvImportHistoryDefaultRepository
}

var RepositoryMod = submodule.Make[*Repository](NewPTVImportRepository)

func NewPTVImportRepository() *Repository {
	return &Repository{
		ptv_import.NewPtvImportDefaultRepository(),
		ptv_import_history.NewPtvImportHistoryDefaultRepository(),
	}
}

func (s *Repository) GetPtvImportByDoctorId(ctx *titan.Context, doctorId uuid.UUID) ([]ptv_import.PtvImport, error) {
	filter := bson.D{
		{
			Key:   ptv_import.Field_DoctorId,
			Value: doctorId,
		},
	}

	result, err := s.PtvImportRepo.FindOne(ctx, filter, options.FindOne().SetSort(bson.D{{Key: repos.Field_CreatedAt, Value: -1}}))
	if err != nil {
		return nil, err
	}
	if result == nil {
		return nil, nil
	}

	return slice.ToValueType([]*ptv_import.PtvImport{result}), nil
}

// func get ptvImport by doctor id, and contract id
func (s *Repository) GetPtvImportByDoctorIdAndContractId(ctx *titan.Context, doctorId uuid.UUID, contractId string) ([]ptv_import.PtvImport, error) {
	filter := bson.D{
		{Key: ptv_import.Field_DoctorId, Value: doctorId},
		{Key: ptv_import.Field_ImportContract_ContractId, Value: contractId},
	}

	result, err := s.PtvImportRepo.FindOne(ctx, filter, options.FindOne().SetSort(bson.D{{Key: repos.Field_CreatedAt, Value: -1}}))
	if err != nil {
		return nil, err
	}
	if result == nil {
		return nil, nil
	}

	return slice.ToValueType([]*ptv_import.PtvImport{result}), nil
}

// get all ptv import from PtvImportRepo
func (s *Repository) GetPtvImports(ctx *titan.Context) ([]ptv_import.PtvImport, error) {
	return slice.ToValueTypeWithError(s.PtvImportRepo.Find(ctx, bson.D{}))
}

func (s *Repository) GetPtvImportHistory(ctx *titan.Context, pagination *common.Pagination) ([]ptv_import_common.PtvImportHistory, error) {
	var offset, limit int64
	var sort bson.D
	option := options.Find()

	if pagination != nil {
		offset = pagination.Offset
		limit = pagination.Max
		if pagination.SortBy != "" {
			order := 1                                       // asc
			if strings.EqualFold(pagination.Order, "desc") { // there is already validating the value in proto
				order = -1
			}
			sort = bson.D{
				{
					Key:   pagination.SortBy,
					Value: order,
				},
			}
		}
	}
	option.SetSkip(offset)
	option.SetLimit(limit)
	option.SetSort(sort)

	return s.PtvImportHistoryDefaultRepository.Find(ctx, bson.D{}, option)
}

func (s *Repository) GetParticipantDecisionByIds(
	ctx *titan.Context,
	ptvImportHistoryId uuid.UUID,
	importType string,
	participantDecisionIds []uuid.UUID,
) ([]ptv_import_common.ParticipantDecision, error) {
	filterCondition := fmt.Sprintf("$$item.%s", ptv_import_history.Field_ParticipantDecision_Id)
	step := bson.A{
		bson.M{
			"$match": bson.M{
				ptv_import_history.Field_Id: ptvImportHistoryId,
			},
		},
		bson.M{
			"$project": bson.M{
				importType: bson.M{
					"$filter": bson.M{
						"input": fmt.Sprintf("$%s", importType),
						"as":    "item",
						"cond": bson.M{
							"$in": bson.A{filterCondition, participantDecisionIds},
						},
					},
				},
			},
		},
	}

	result, err := s.PtvImportHistoryDefaultRepository.Aggregate(ctx, step)
	if err != nil {
		return nil, err
	}

	if len(result) == 0 {
		return nil, nil
	}

	var participantDecisions []ptv_import_common.ParticipantDecision
	ptvImportHistory := result[0]
	switch importType {
	case string(ptv_import_history.Field_AutoImportParticipants):
		participantDecisions = ptvImportHistory.AutoImportParticipants
	case string(ptv_import_history.Field_ConflictParticipants):
		participantDecisions = ptvImportHistory.ConflictParticipants
	case string(ptv_import_history.Field_MissingParticipants):
		participantDecisions = ptvImportHistory.MissingParticipants
	}
	if len(participantDecisions) == 0 {
		return nil, nil
	}
	return participantDecisions, nil
}

func (s *Repository) CountPtvImportHistory(ctx *titan.Context) (int64, error) {
	filter := bson.D{}

	return s.PtvImportHistoryDefaultRepository.Count(ctx, filter)
}

func (s *Repository) CreatePtvImportHistory(ctx *titan.Context, entity ptv_import_common.PtvImportHistory) (*ptv_import_common.PtvImportHistory, error) {
	return s.PtvImportHistoryDefaultRepository.Create(ctx, entity)
}

func (s *Repository) GetImportContractByDocumentId(ctx *titan.Context, documentId string, doctorId uuid.UUID) (*ptv_import_common.ImportContract, error) {
	filter := bson.M{
		ptv_import.Field_DoctorId: doctorId,
		// ptv_import.Field_Year:                      year,
		// ptv_import.Field_Quarter:                   quarter,
		ptv_import.Field_ImportContract_DocumentId: documentId,
	}
	result, err := s.PtvImportRepo.FindOne(ctx, filter, options.FindOne().SetSort(bson.D{{Key: repos.Field_CreatedAt, Value: -1}}))
	if err != nil {
		return nil, err
	}

	if result == nil {
		return nil, nil
	}

	return &result.Contract, nil
}

func (s *Repository) UpdateFileInfoPtvImport(
	ctx *titan.Context,
	doctorId uuid.UUID,
	documentId string,
	fileInfo *ptv_import_common.FileInfo,
) (*ptv_import.PtvImport, error) {
	filter := bson.D{
		{Key: ptv_import.Field_DoctorId, Value: doctorId},
		{Key: ptv_import.Field_ImportContract_DocumentId, Value: documentId},
	}
	updateQuery := bson.D{
		{Key: "$set", Value: bson.D{{Key: ptv_import.Field_ImportContract_FileInfo, Value: fileInfo}}},
	}
	return s.PtvImportRepo.FindOneAndUpdate(ctx, filter, updateQuery, options.FindOneAndUpdate().SetSort(bson.D{{Key: repos.Field_CreatedAt, Value: -1}}))
}

func (s *Repository) UpdatePtvImportHistoryStatus(ctx *titan.Context, historyId uuid.UUID, status string) error {
	filter := bson.M{
		ptv_import_history.Field_Id: historyId,
	}
	updateQuery := bson.M{
		"$set": bson.M{
			ptv_import_history.Field_Status: status,
		},
	}
	_, err := s.PtvImportHistoryDefaultRepository.FindOneAndUpdate(ctx, filter, updateQuery)
	return err
}

func (s *Repository) UpdateParticipantDecision(
	ctx *titan.Context,
	ptvImportHistoryId uuid.UUID,
	importParticipantsType string,
	participantDecisions []ptv_import_common.ParticipantDecision,
) error {
	if len(participantDecisions) == 0 {
		return nil
	}

	// Collect all Ids and build a map for quick lookup
	ids := make([]uuid.UUID, 0, len(participantDecisions))
	decisionMap := make(map[uuid.UUID]ptv_import_common.ParticipantDecision)
	for _, pd := range participantDecisions {
		ids = append(ids, pd.Id)
		decisionMap[pd.Id] = pd
	}

	// Build the update document to set each matched element
	setUpdates := bson.D{}
	for i, id := range ids {
		setUpdates = append(setUpdates, bson.E{
			Key:   fmt.Sprintf("%s.$[elem%d]", importParticipantsType, i),
			Value: decisionMap[id],
		})
	}

	filter := bson.D{
		{Key: ptv_import_history.Field_Id, Value: ptvImportHistoryId},
	}

	// Build arrayFilters for each participant decision
	arrayFilters := make([]any, 0, len(ids))
	for i, id := range ids {
		arrayFilters = append(arrayFilters, bson.M{
			fmt.Sprintf("elem%d.%s", i, ptv_import_history.Field_ParticipantDecision_Id): id,
		})
	}

	updateQuery := bson.D{{Key: "$set", Value: setUpdates}}

	opts := options.FindOneAndUpdate().SetArrayFilters(options.ArrayFilters{Filters: arrayFilters})
	_, err := s.PtvImportHistoryDefaultRepository.FindOneAndUpdate(ctx, filter, updateQuery, opts)
	if err != nil {
		return err
	}

	return nil
}
