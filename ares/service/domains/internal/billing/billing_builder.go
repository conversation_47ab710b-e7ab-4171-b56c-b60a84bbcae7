package billing

import (
	"fmt"
	"strconv"
	"strings"

	"emperror.dev/errors"
	hpm_rest_hzv_client "git.tutum.dev/medi/tutum/ares/pkg/hpm_rest/hzv"
	hpm "git.tutum.dev/medi/tutum/ares/pkg/hpm_service/hpm_next"
	"git.tutum.dev/medi/tutum/ares/pkg/operator"
	"git.tutum.dev/medi/tutum/ares/service/billing_history/billing_history_common"
	"git.tutum.dev/medi/tutum/ares/service/contract/contract/model"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/billing"
	billing_common "git.tutum.dev/medi/tutum/ares/service/domains/api/billing_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	patient_participation_service "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_participation"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/internal/billing/hpm_next_builder"
	qes_common "git.tutum.dev/medi/tutum/ares/service/domains/qes/common"
	billing_service_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/billing"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/doctor_participate"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee"
	patient_profile_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	timeline_common "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/timeline_service"
	feature_flag_common "git.tutum.dev/medi/tutum/ares/service/feature_flag/common"
	scheinService "git.tutum.dev/medi/tutum/ares/service/schein"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	pkg_utils "git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"gitlab.com/silenteer-oss/hestia/infrastructure/util"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
)

type (
	contractIdsMap map[string]*model.Contract

	// doctorParticipateMaps map by doctorId_contractId
	doctorParticipateMaps map[string]doctor_participate.DoctorParticipate

	mappedDoctor         map[uuid.UUID]employee.EmployeeProfile
	mappedPatientProfile map[uuid.UUID]patient_profile_repo.PatientProfile
	mappedSchein         map[uuid.UUID]schein_common.GetScheinDetailByIdResponse

	refInfo struct {
		// mappedDoctor map by hashedId
		mappedDoctor mappedDoctor
		allDoctors   mappedDoctor
		// doctorParticipateMaps map by doctorId_contractId
		doctorParticipateMaps        doctorParticipateMaps
		contractIdsMap               contractIdsMap
		mappedPatientProfile         mappedPatientProfile
		doctorParticipateContractMap map[string][]doctor_participate.DoctorParticipate
		mappedSchein                 mappedSchein
	}

	billingRequest struct {
		Times                 []*billing_common.YearQuarter
		DoctorIds             []uuid.UUID
		ContractIds           []string
		TestRun               bool
		IgnoreBilling         []*billing.IgnoreBilling
		ReferenceId           *uuid.UUID
		IsOffline             bool
		IsPrescriptionOnly    bool
		IgnoreDocumentBilling *[]*billing.IgnoreBilling
		IsGetErrorTimeline    bool
	}

	billingBuilder struct {
		billingService *Service
		billingRequest *billingRequest
		// computed data
		refInfo                 *refInfo
		timelineMapItems        TimelineMap
		rawTimelines            []timeline_common.TimelineModel
		timelines               []timeline_common.TimelineModel
		scheins                 []schein.ScheinRepo
		patientIds              []uuid.UUID
		prescriptionOnlyScheins []uuid.UUID

		mapCreatedBy           map[uuid.UUID]employee.EmployeeProfile
		err                    error
		payloadBuilder         []payloadBuilderTimes
		pseudoServices         []timeline_common.TimelineModel
		patientParticipateMaps map[uuid.UUID]patient_participation_service.PatientParticipation
	}

	mapToBillingHistoryAndUploadProtocolRequest struct {
		year, quarter      int32
		doctorId           uuid.UUID
		protocol           []byte
		contractId         string
		transferId         *string
		DiagnoseIds        []uuid.UUID
		ServiceIds         []uuid.UUID
		PrescriptionIds    []uuid.UUID
		ReferenceId        uuid.UUID
		BillingHistoryType billing_history_common.BillingHistoryType
	}

	payloadBuilderData struct {
		Diagnosises           []timeline_common.TimelineModel
		Services              []timeline_common.TimelineModel
		MedicinePrescriptions []timeline_common.TimelineModel
	}

	payloadBuilderPatient struct {
		Patient patient_profile_repo.PatientProfile
		Data    payloadBuilderData
	}

	payloadBuilderDoctor struct {
		Doctor             employee.EmployeeProfile
		Patients           []payloadBuilderPatient
		DoctorHzvContracts []model.Contract
		DoctorFavContracts []model.Contract
	}

	// payloadBuilderContract = map[string]payloadBuilderDoctor
	payloadBuilderContract struct {
		Contract      string
		DoctorBuilder []payloadBuilderDoctor
	}

	// payloadBuilderTimes = map[billing_common.YearQuarter]payloadBuilderContract
	payloadBuilderTimes struct {
		YearQuater billing_common.YearQuarter
		Contracts  []payloadBuilderContract
	}
	ExtendHpmRequest struct {
		DoctorId   uuid.UUID
		HpmRequest hpm_rest_hzv_client.AbrechnungsContainer
	}
)

func (c contractIdsMap) getBy(contractId string) *model.Contract {
	if v, ok := c[contractId]; ok {
		return v
	}
	return nil
}

func (d doctorParticipateMaps) getBy(doctorId, contractId string) *doctor_participate.DoctorParticipate {
	if v, ok := d[fmt.Sprintf("%s.%s", doctorId, contractId)]; ok {
		return &v
	}
	return nil
}

func (m mappedDoctor) Get(id uuid.UUID) *employee.EmployeeProfile {
	if v, ok := m[id]; ok {
		return &v
	}
	return nil
}

func (m mappedSchein) Get(id uuid.UUID) *schein_common.GetScheinDetailByIdResponse {
	if v, ok := m[id]; ok {
		return &v
	}
	return nil
}

func (m mappedPatientProfile) Get(id uuid.UUID) *patient_profile_repo.PatientProfile {
	if v, ok := m[id]; ok {
		return &v
	}
	return nil
}

func newBillingBuilder(billingRequest *billingRequest, billingService *Service) *billingBuilder {
	return &billingBuilder{
		billingService:         billingService,
		billingRequest:         billingRequest,
		patientParticipateMaps: make(map[uuid.UUID]patient_participation_service.PatientParticipation),
	}
}

func (b *billingBuilder) prepare(ctx *titan.Context) *billingBuilder {
	doctors, err := b.billingService.repo.employeeProfileRepo.Find(ctx, bson.M{})
	if err != nil {
		b.err = errors.WithMessage(err, "get doctor profile failed")
		return b
	}
	allDoctors := mappedDoctor{}
	for _, d := range doctors {
		allDoctors[*d.Id] = d
	}
	doctors, err = b.billingService.repo.employeeProfileRepo.GetProfileByIds(ctx, b.billingRequest.DoctorIds)
	if err != nil {
		b.err = errors.WithMessage(err, "get doctor profile failed")
		return b
	}
	mappedDoctor := map[uuid.UUID]employee.EmployeeProfile{}
	for _, d := range doctors {
		mappedDoctor[*d.Id] = d
	}
	doctorParticipates, err := b.billingService.repo.doctorParticipateRepo.Find(ctx, bson.M{
		doctor_participate.Field_DoctorId: bson.M{
			"$in": b.billingRequest.DoctorIds,
		},
	})
	if err != nil {
		b.err = errors.WithMessage(err, "get doctor participate failed")
		return b
	}

	doctorParticipateMaps := map[string]doctor_participate.DoctorParticipate{}
	for _, d := range doctorParticipates {
		// Hotfix: always ignore AOK_BW_IV_P
		if d.ChargeSystemId == "AOK_BW_IV_P" {
			continue
		}
		if d.ContractId != nil && d.DoctorId != nil {
			doctor := slice.FindOne(doctors, func(ep employee.EmployeeProfile) bool {
				return *ep.Id == *d.DoctorId
			})
			if doctor == nil {
				continue
			}
			doctorParticipateMaps[fmt.Sprintf("%s.%s", doctor.Id.String(), *d.ContractId)] = d
		}
	}
	doctorParticipationContractMaps := slice.GroupBy(doctorParticipates, func(d doctor_participate.DoctorParticipate) string {
		doctor := slice.FindOne(doctors, func(ep employee.EmployeeProfile) bool {
			return *ep.Id == *d.DoctorId
		})
		if doctor == nil {
			return uuid.Nil.String()
		}
		return doctor.Id.String()
	})
	contractIdsMap := b.billingService.toContractsMap(slice.Map(b.billingRequest.ContractIds, func(t string) *string { return &t }))
	b.refInfo = &refInfo{
		mappedDoctor:                 mappedDoctor,
		allDoctors:                   allDoctors,
		doctorParticipateMaps:        doctorParticipateMaps,
		doctorParticipateContractMap: doctorParticipationContractMaps,
		contractIdsMap:               contractIdsMap,
		mappedSchein:                 map[uuid.UUID]schein_common.GetScheinDetailByIdResponse{},
	}
	b.prescriptionOnlyScheins = []uuid.UUID{}
	return b
}

func (b *billingBuilder) fetchScheins(ctx *titan.Context) *billingBuilder {
	if b.err != nil {
		return b
	}
	doctorIds := []uuid.UUID{}
	for id := range b.refInfo.mappedDoctor {
		doctorIds = append(doctorIds, id)
	}
	times := []billing_common.YearQuarter{}
	for _, t := range b.billingRequest.Times {
		if t != nil {
			times = append(times, *t)
		}
	}
	ignoreBillings := []billing.IgnoreBilling{}
	for _, ignore := range b.billingRequest.IgnoreBilling {
		if ignore != nil {
			ignoreBillings = append(ignoreBillings, *ignore)
		}
	}
	scheins, err := b.billingService.scheinService.GetSvScheinsByDoctorIds(ctx, scheinService.GetSvScheins{
		DoctorIds:      doctorIds,
		Times:          times,
		ContractIds:    b.billingRequest.ContractIds,
		IgnoreBillings: ignoreBillings,
	})
	if err != nil {
		b.err = errors.WithMessage(err, "get schein items failed")
		return b
	}
	if len(scheins) == 0 {
		b.err = errors.WithMessage(err, "no schein items billable")
		return b
	}
	for _, schein := range scheins {
		b.refInfo.mappedSchein[schein.ScheinId] = schein
	}
	return b
}

func (b *billingBuilder) fetchTimeline(ctx *titan.Context) *billingBuilder {
	if b.err != nil {
		return b
	}
	scheinIds := []uuid.UUID{}
	for id := range b.refInfo.mappedSchein {
		scheinIds = append(scheinIds, id)
	}
	timelines, entities, scheins, err := b.billingService.GetTimelineItems(ctx, scheinIds)
	if err != nil {
		b.err = errors.WithMessage(err, "get timeline items failed")
		return b
	}
	if timelines == nil {
		b.err = errors.New("empty timelines")
		return b
	}
	patientIds := make([]uuid.UUID, 0, len(b.timelineMapItems))
	for _, v := range entities {
		patientIds = append(patientIds, v.PatientId)
	}
	patientIds = slice.Uniq(patientIds)
	patientProfiles, err := b.billingService.repo.patientProfileRepo.GetByIds(ctx, patientIds)
	if err != nil {
		b.err = errors.WithMessage(err, "get patient profile failed")
		return b
	}
	validPatients := slice.Filter(patientProfiles, func(p *patient_profile_repo.PatientProfile) bool {
		activeInsurance := p.PatientInfo.GetActiveInsurance()
		return pkg_utils.GetPointerValue(activeInsurance.InsuranceNumber) != ""
	})
	validPatientIds := slice.Map(validPatients, func(p *patient_profile_repo.PatientProfile) uuid.UUID {
		return *p.Id
	})
	timelines.diagnoses = slice.Filter(timelines.diagnoses, func(t timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) bool {
		return slice.Contains(validPatientIds, t.PatientId)
	})
	timelines.services = slice.Filter(timelines.services, func(t timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) bool {
		return slice.Contains(validPatientIds, t.PatientId)
	})
	timelines.medicinePrescriptions = slice.Filter(timelines.medicinePrescriptions, func(t timeline_repo.TimelineEntity[patient_encounter.EncounterMedicinePrescription]) bool {
		return slice.Contains(validPatientIds, t.PatientId)
	})

	entities = slice.Filter(entities, func(t timeline_common.TimelineModel) bool {
		return slice.Contains(validPatientIds, t.PatientId)
	})
	rawEntities := slice.Map(entities, func(t timeline_common.TimelineModel) timeline_common.TimelineModel {
		return t
	})
	scheins = slice.Filter(scheins, func(s schein.ScheinRepo) bool {
		return slice.Contains(validPatientIds, s.PatientId)
	})
	if timelines.IsEmpty() {
		return b
	}
	b.mapCreatedBy = b.billingService.GetCreatedDoctors(ctx, entities)
	b.timelineMapItems = timelines.ToMap(ctx, b.refInfo.contractIdsMap, b.refInfo.mappedSchein)
	b.timelines = entities
	b.rawTimelines = rawEntities
	b.scheins = scheins
	return b
}

func (b *billingBuilder) addPseudoServiceTimeline(ctx *titan.Context) *billingBuilder {
	if b.err != nil {
		return b
	}
	if b.billingRequest.IsPrescriptionOnly {
		return b
	}
	for _, schein := range b.scheins {
		contractId := pkg_utils.GetPointerValue(schein.ContractId)
		contract := b.billingService.contractService.GetContractDetailById(contractId)
		yearQuarter := schein.GetYearQuarter()
		if contract == nil || !contract.CheckExistAnforderung(model.ICheckExistAnforderung{
			ComplianceIds: []string{"ABRG493"},
			CheckTime: function.Do(func() int64 {
				startQuarter := yearQuarter.StartTime()
				if startQuarter != nil {
					return *startQuarter
				}
				return util.NowUnixMillis(ctx)
			}),
		}) {
			continue
		}
		chargeSystemId := pkg_utils.GetPointerValue(schein.ChargeSystemId)
		if contractId != chargeSystemId && !contract.CheckModuleContractComplianceExist(chargeSystemId, []string{"ABRG493"}) {
			continue
		}

		hasAtLeastOneService := slice.Any(b.timelines, func(t timeline_common.TimelineModel) bool {
			return slice.Contains(t.ScheinIds, *schein.Id) && t.EncounterServiceTimeline != nil
		})
		if hasAtLeastOneService {
			continue
		}
		pseudoService := timeline_common.TimelineModel{
			Id:                pkg_utils.NewUUID(),
			PatientId:         schein.PatientId,
			TreatmentDoctorId: schein.DoctorId,
			BillingDoctorId:   pkg_utils.NewPointer(schein.DoctorId),
			EncounterServiceTimeline: &patient_encounter.EncounterServiceTimeline{
				Code:           "999999",
				Description:    "Informationsziffer Abrechnungsübermittlung",
				FreeText:       "(999999) Informationsziffer Abrechnungsübermittlung",
				Command:        "L",
				ChargeSystemId: schein.ChargeSystemId,
			},
			ScheinIds:    []uuid.UUID{*schein.Id},
			Year:         yearQuarter.Year,
			Quarter:      yearQuarter.Quarter,
			SelectedDate: util.NowUnixMillis(ctx),
			ContractId:   schein.ContractId,
			Type:         pkg_utils.NewPointer(timeline_common.TimelineEntityType_Service),
		}
		b.timelines = append(b.timelines, pseudoService)
		b.pseudoServices = append(b.pseudoServices, pseudoService)
	}
	return b
}

func (b *billingBuilder) CreatePseudoServiceTimeline(ctx *titan.Context) error {
	return b.billingService.CreatePseudoServiceTimeline(ctx, b.prescriptionOnlyScheins, b.pseudoServices)
}

func (b *billingBuilder) fetchProfiles(ctx *titan.Context) *billingBuilder {
	if b.err != nil {
		return b
	}
	b.patientIds = slice.Uniq(b.patientIds)

	for _, patientId := range b.patientIds {
		patientParticipations, err := b.billingService.patientParticipationService.GetActivePatientParticipationWithoutCheckDate(ctx, patient_participation_service.GetPatientParticipationWithoutCheckDateRequest{
			PatientId: patientId,
		})
		if err != nil {
			b.err = err
		}
		if len(patientParticipations.Participations) == 0 {
			continue
		}
		b.patientParticipateMaps[patientId] = patientParticipations.Participations[0]
	}

	patientProfiles, err := b.billingService.repo.patientProfileRepo.GetByIds(ctx, b.patientIds)
	if err != nil {
		b.err = errors.WithMessage(err, "get patient profile failed")
		return b
	}
	if len(patientProfiles) == 0 {
		return b
	}
	mappedPatientProfile := map[uuid.UUID]patient_profile_repo.PatientProfile{}
	for _, v := range patientProfiles {
		mappedPatientProfile[*v.Id] = *v
	}
	b.refInfo.mappedPatientProfile = mappedPatientProfile

	for k, v := range b.timelineMapItems {
		doctorId := k.getDoctorId()
		employeeProfile := b.refInfo.allDoctors.Get(doctorId)
		for patientId, timeline := range v {
			patientProfile := b.refInfo.mappedPatientProfile.Get(patientId)
			timeline.patientProfile = *patientProfile
			timeline.doctorProfile = *employeeProfile
			b.timelineMapItems[k][patientId] = timeline
		}
	}
	return b
}

func (b *billingBuilder) reFilterTimeline() *billingBuilder {
	if b.err != nil {
		return b
	}

	errorsTimelines := []timeline_common.TimelineModel{}

	ignoreDocumentBilling := []billing.IgnoreBilling{}
	if b.billingRequest.IgnoreDocumentBilling != nil {
		for _, ignore := range *b.billingRequest.IgnoreDocumentBilling {
			if ignore != nil {
				ignoreDocumentBilling = append(ignoreDocumentBilling, *ignore)
			}
		}
	}

	for _, v := range b.timelines {
		b.patientIds = append(b.patientIds, v.PatientId)
	}

	if b.billingRequest.IsPrescriptionOnly {
		b.timelines = slice.Filter(b.timelines, func(t timeline_common.TimelineModel) bool {
			return t.Type != nil && *t.Type == timeline_common.TimelineEntityType_MedicinePrescription
		})

		scheinIds := []uuid.UUID{}
		for _, t := range b.timelines {
			scheinIds = append(scheinIds, t.ScheinIds...)
		}
		b.scheins = slice.Filter(b.scheins, func(s schein.ScheinRepo) bool {
			return slice.Contains(scheinIds, *s.Id)
		})
	} else if !b.billingRequest.TestRun {
		for _, schein := range b.scheins {
			diagnosis := filterTimelineByTypeAndScheinId(b.timelines, timeline_common.TimelineEntityType_Diagnose, *schein.Id)
			services := filterTimelineByTypeAndScheinId(b.timelines, timeline_common.TimelineEntityType_Service, *schein.Id)

			if len(diagnosis) == 0 && len(services) == 0 {
				continue
			}

			diagnosis = filterNonErrorTimelines(diagnosis, ignoreDocumentBilling)
			services = filterNonErrorTimelines(services, ignoreDocumentBilling)

			hasDiagnoseError := timelinesHasError(diagnosis)
			hasServiceError := timelinesHasError(services)

			if (hasDiagnoseError || hasServiceError) && b.billingRequest.IsGetErrorTimeline {
				errorsTimelines = append(errorsTimelines, diagnosis...)
				errorsTimelines = append(errorsTimelines, services...)
			}

			if hasDiagnoseError || hasServiceError {
				b.timelines = slice.Filter(b.timelines, func(t timeline_common.TimelineModel) bool {
					if !slice.Contains(t.ScheinIds, *schein.Id) {
						return true
					}
					return t.Type != nil && *t.Type == timeline_common.TimelineEntityType_MedicinePrescription
				})
				b.prescriptionOnlyScheins = append(b.prescriptionOnlyScheins, *schein.Id)
			}
		}
	}

	if b.billingRequest.IsGetErrorTimeline {
		b.timelines = errorsTimelines
	}

	return b
}

func (b *billingBuilder) filterMedicinePrescription() *billingBuilder {
	b.timelines = slice.Filter(b.timelines, func(t timeline_common.TimelineModel) bool {
		if t.Type != nil && *t.Type != timeline_common.TimelineEntityType_MedicinePrescription {
			return true
		}
		schein := b.refInfo.mappedSchein.Get(t.ScheinIds[0])
		if schein == nil {
			return false
		}
		if schein.HzvContractId == nil {
			return true
		}
		contract := b.refInfo.contractIdsMap[*schein.HzvContractId]
		doctor := b.refInfo.mappedDoctor.Get(schein.DoctorId)
		patientProfile := b.refInfo.mappedPatientProfile.Get(schein.PatientId)

		insuranceInfos := slice.FindOne(patientProfile.PatientInfo.InsuranceInfos, func(t patient_profile_common.InsuranceInfo) bool {
			return t.Id == schein.InsuranceId
		})
		if insuranceInfos == nil {
			return false
		}
		ikNumber := insuranceInfos.IkNumber
		isExist := contract.CheckExistAnforderung(model.ICheckExistAnforderung{
			ComplianceIds: []string{"VSST650"},
			CheckTime:     pkg_utils.GetInt64Value(t.CreatedAt),
			KvRegion:      pkg_utils.NewPointer(doctor.Bsnr[:2]),
			IkNumber:      &ikNumber,
		})
		return isExist
	})
	return b
}

func (b *billingBuilder) buildPrepairePayload(_ *titan.Context) *billingBuilder {
	if b.err != nil {
		return b
	}

	b.payloadBuilder = []payloadBuilderTimes{}
	for _, t := range b.billingRequest.Times {
		payloadContracts := []payloadBuilderContract{}
		for _, c := range b.billingRequest.ContractIds {
			payloadDoctors := []payloadBuilderDoctor{}
			for _, d := range b.billingRequest.DoctorIds {
				doctor := b.refInfo.mappedDoctor.Get(d)
				if doctor == nil {
					continue
				}
				timelines := slice.Filter(b.timelines, func(tm timeline_common.TimelineModel) bool {
					return tm.TreatmentDoctorId == d && tm.ContractId != nil && *tm.ContractId == c && t.Quarter == tm.Quarter && t.Year == tm.Year
				})
				mappedPatients := slice.GroupBy(timelines, func(t timeline_common.TimelineModel) uuid.UUID {
					return t.PatientId
				})
				payloadPatients := []payloadBuilderPatient{}
				for pId, ts := range mappedPatients {
					patient := b.refInfo.mappedPatientProfile.Get(pId)
					if patient == nil {
						continue
					}
					payloadPatients = append(payloadPatients, payloadBuilderPatient{
						Patient: *patient,
						Data: payloadBuilderData{
							Diagnosises: slice.Filter(ts, func(tm timeline_common.TimelineModel) bool {
								return tm.Type != nil && *tm.Type == timeline_common.TimelineEntityType_Diagnose
							}),
							Services: slice.Filter(ts, func(tm timeline_common.TimelineModel) bool {
								return tm.Type != nil && *tm.Type == timeline_common.TimelineEntityType_Service
							}),
							MedicinePrescriptions: slice.Filter(ts, func(tm timeline_common.TimelineModel) bool {
								return tm.Type != nil && *tm.Type == timeline_common.TimelineEntityType_MedicinePrescription
							}),
						},
					})
				}
				if len(payloadPatients) > 0 {
					participates, ok := b.refInfo.doctorParticipateContractMap[doctor.Id.String()]
					hzvContracts := []model.Contract{}
					favContracts := []model.Contract{}
					if ok {
						for _, p := range participates {
							if p.ContractId == nil {
								continue
							}
							contract := b.billingService.contractService.GetContractDetailById(*p.ContractId)
							if contract == nil {
								continue
							}
							if p.Type == common.ContractType_HouseDoctorCare {
								hzvContracts = append(hzvContracts, *contract)
							} else if p.Type == common.ContractType_SpecialistCare {
								favContracts = append(favContracts, *contract)
							}
						}
					}
					payloadDoctors = append(payloadDoctors, payloadBuilderDoctor{
						Doctor:   *doctor,
						Patients: payloadPatients,
						DoctorHzvContracts: slice.UniqBy(hzvContracts, func(t model.Contract) string {
							return t.Id
						}),
						DoctorFavContracts: slice.UniqBy(favContracts, func(t model.Contract) string {
							return t.Id
						}),
					})
				}
			}
			if len(payloadDoctors) > 0 {
				payloadContracts = append(payloadContracts, payloadBuilderContract{
					Contract:      c,
					DoctorBuilder: payloadDoctors,
				})
			}
		}
		if len(payloadContracts) > 0 {
			b.payloadBuilder = append(b.payloadBuilder, payloadBuilderTimes{
				YearQuater: billing_common.YearQuarter{
					Year:    t.Year,
					Quarter: t.Quarter,
				},
				Contracts: payloadContracts,
			})
		}
	}
	return b
}

func (b *billingBuilder) build(ctx *titan.Context) ([]ExtendHpmRequest, []hpm_next_builder.PrescriptionBuilderV2, error) {
	if b.err != nil {
		return nil, nil, b.err
	}

	hpmTestMode, err := b.billingService.featureFlagService.IsFeatureEnabled(ctx, feature_flag_common.FeatureFlagKey_HPM_TESTMODE)
	if err != nil {
		return nil, nil, errors.WithMessage(err, "failed to get realtime config")
	}

	result := []ExtendHpmRequest{}
	resultPrescription := []hpm_next_builder.PrescriptionBuilderV2{}
	builderService := hpm_next_builder.BuilderService{}
	for _, p := range b.payloadBuilder {
		for _, c := range p.Contracts {
			contract := b.refInfo.contractIdsMap[c.Contract]
			if contract == nil {
				continue
			}
			for _, d := range c.DoctorBuilder {
				doctorParticipate := b.refInfo.doctorParticipateMaps.getBy(d.Doctor.Id.String(), c.Contract)
				if doctorParticipate == nil {
					continue
				}
				builder := builderService.BuildPayload(hpmTestMode, b.billingRequest.TestRun, b.billingRequest.IsOffline).
					AddCurrentContractParticipation(b.billingRequest.ContractIds).
					AddDoctorContracts(d.DoctorHzvContracts, d.DoctorFavContracts).
					AddContract(*contract, doctorParticipate.ChargeSystemId, pkg_utils.YearQuarter{Year: p.YearQuater.Year, Quarter: p.YearQuater.Quarter}).
					AddDoctorInfo(d.Doctor, b.refInfo.allDoctors).
					AddScheins(b.scheins)
				isExistsNotEncounterCase := false
				for _, patient := range d.Patients {
					builder.AddPatientWithDocuments(hpm_next_builder.PatientWithDocuments{
						Patient: hpm_next_builder.Patient(patient.Patient),
						Diagnoses: slice.Map(patient.Data.Diagnosises, func(t timeline_common.TimelineModel) hpm_next_builder.Diagnose {
							return hpm_next_builder.Diagnose(timeline_service.ConvertModelToEntity[patient_encounter.EncounterDiagnoseTimeline](t))
						}),
						Services: slice.Map(patient.Data.Services, func(t timeline_common.TimelineModel) hpm_next_builder.Service {
							if t.EncounterCase != nil && *t.EncounterCase == patient_encounter.NOT {
								isExistsNotEncounterCase = true
							}
							return hpm_next_builder.Service(timeline_service.ConvertModelToEntity[patient_encounter.EncounterServiceTimeline](t))
						}),
						Prescription: slice.Map(patient.Data.MedicinePrescriptions, func(t timeline_common.TimelineModel) hpm_next_builder.Prescription {
							return hpm_next_builder.Prescription(timeline_service.ConvertModelToEntity[patient_encounter.EncounterMedicinePrescription](t))
						}),
					})
				}
				encounterCase := patient_encounter.AB
				if isExistsNotEncounterCase {
					encounterCase = patient_encounter.NOT
				}
				starteAbrechnungs, starteVerordnungsdatenUebermittlungs := builder.
					AddEncounterCase(pkg_utils.NewPointer(encounterCase)).
					AddPatientParticipationMap(b.patientParticipateMaps).
					AddSoftwareInfomation(hpm.SoftwareInformation(b.billingService.softwareInformation)).BuildV2(ctx)
				if starteAbrechnungs != nil && d.Doctor.Id != nil {
					result = append(result, ExtendHpmRequest{
						DoctorId:   *d.Doctor.Id,
						HpmRequest: *starteAbrechnungs,
					})
				}
				if starteVerordnungsdatenUebermittlungs.Request != nil {
					resultPrescription = append(resultPrescription, starteVerordnungsdatenUebermittlungs)
				}
			}
		}
	}
	return result, resultPrescription, nil
}

func (b *billingBuilder) GetPatients() []patient_profile_repo.PatientProfile {
	result := []patient_profile_repo.PatientProfile{}
	for _, v := range b.refInfo.mappedPatientProfile {
		result = append(result, v)
	}
	return result
}

func (b *billingBuilder) GetPatientIds() []uuid.UUID {
	p := b.GetPatients()
	result := []uuid.UUID{}
	for _, v := range p {
		result = append(result, *v.Id)
	}
	return result
}

func (b *billingBuilder) GetDoctors() []employee.EmployeeProfile {
	result := []employee.EmployeeProfile{}
	for _, v := range b.refInfo.mappedDoctor {
		result = append(result, v)
	}
	return result
}

func (b *billingBuilder) GetErrorAndWarningTimelines() []timeline_common.TimelineModel {
	return slice.Filter(b.timelines, func(t timeline_common.TimelineModel) bool {
		if t.EncounterDiagnoseTimeline != nil && t.EncounterDiagnoseTimeline.Errors != nil && len(*t.EncounterDiagnoseTimeline.Errors) > 0 {
			return true
		}
		if t.EncounterServiceTimeline != nil && t.EncounterServiceTimeline.Errors != nil && len(*t.EncounterServiceTimeline.Errors) > 0 {
			return true
		}
		return false
	})
}

func (b *billingBuilder) GetNoErrorDiagnosis() []timeline_common.TimelineModel {
	return slice.Filter(b.timelines, func(t timeline_common.TimelineModel) bool {
		return t.EncounterDiagnoseTimeline != nil && t.EncounterDiagnoseTimeline.Errors != nil && len(*t.EncounterDiagnoseTimeline.Errors) == 0
	})
}

func (b *billingBuilder) GetNoErrorDiagnoseIds() []uuid.UUID {
	timelines := b.GetNoErrorDiagnosis()
	return slice.Map(timelines, func(t timeline_common.TimelineModel) uuid.UUID {
		return *t.Id
	})
}

func (b *billingBuilder) GetPrescriptionOnlyScheins() []uuid.UUID {
	return b.prescriptionOnlyScheins
}

func (b *billingBuilder) GetErrorTimelineIdsByType(errorType patient_encounter.EncounterItemErrorType) []uuid.UUID {
	timelines := b.GetErrorAndWarningTimelines()
	errorTimelines := slice.Filter(timelines, func(t timeline_common.TimelineModel) bool {
		var errors *[]*patient_encounter.EncounterItemError
		if t.EncounterDiagnoseTimeline != nil && t.EncounterDiagnoseTimeline.Errors != nil {
			errors = t.EncounterDiagnoseTimeline.Errors
		} else if t.EncounterServiceTimeline != nil && t.EncounterServiceTimeline.Errors != nil {
			errors = t.EncounterServiceTimeline.Errors
		}

		return errors != nil && len(*errors) > 0 && hasEncounterItemErrorsByType(*errors, errorType)
	})
	return slice.Map(errorTimelines, func(t timeline_common.TimelineModel) uuid.UUID {
		return *t.Id
	})
}

func (srv *Service) mapToBillingHistoryAndUploadProtocol(ctx *titan.Context, req mapToBillingHistoryAndUploadProtocolRequest) (*billing_service_repo.BillingHistory, error) {
	id := uuid.New()
	submittedDate := util.NowUnixMillis(nil)
	filename := fmt.Sprintf("%s.pdf", id.String())
	var (
		fileInfo *billing_service_repo.FileInfo
		err      error
	)
	if len(req.protocol) > 0 {
		fileInfo, err = srv.uploadPdf(ctx, req.protocol, filename)
		if err != nil {
			return nil, err
		}
		if fileInfo != nil {
			submittedDate = fileInfo.CreatedDate
		}
	}
	employee, _ := srv.repo.employeeProfileRepo.GetById(ctx, pkg_utils.GetPointerValue(ctx.UserInfo().UserUUID()))
	return &billing_service_repo.BillingHistory{
		Id:                 &id,
		BillingYear:        req.year,
		BillingQuarter:     req.quarter,
		BillingDoctorId:    &req.doctorId,
		BillingContractId:  &req.contractId,
		SubmittedDate:      submittedDate,
		FileInfo:           fileInfo,
		TransferId:         req.transferId,
		DiagnoseIds:        req.DiagnoseIds,
		ServiceIds:         req.ServiceIds,
		PrescriptionIds:    req.PrescriptionIds,
		ReferenceId:        &req.ReferenceId,
		BillingHistoryType: req.BillingHistoryType,
		SubmittedUserId: function.Do(func() *uuid.UUID {
			if employee == nil {
				return nil
			}
			return employee.Id
		}),
	}, nil
}

type GetTimelineItemsResponse struct {
	diagnoses             hpm.DiagnoseTimelines
	services              hpm.ServiceTimelines
	medicinePrescriptions hpm.MedicinePrescriptions
	patientProfile        patient_profile_repo.PatientProfile
	doctorProfile         employee.EmployeeProfile
}

func (g *GetTimelineItemsResponse) IsEmpty() bool {
	if g == nil {
		return true
	}
	return len(g.diagnoses) == 0 && len(g.services) == 0 && len(g.medicinePrescriptions) == 0
}

// map by quarter_doctor_contract
type TimelineMapKey string

const spliter = "*"

func (t TimelineMapKey) toKeys() (quarter, year int, doctorId uuid.UUID, contractId string) {
	values := strings.Split(string(t), spliter)
	if len(values) == 4 {
		quarter, _ = strconv.Atoi(values[0])
		year, _ = strconv.Atoi(values[1])
		doctorId, _ = uuid.Parse(values[2])
		contractId = values[3]
		return quarter, year, doctorId, contractId
	}
	panic("invalid timeline map key")
}

func (t TimelineMapKey) getContractId() string {
	_, _, _, contractId := t.toKeys()
	return contractId
}

func (t TimelineMapKey) getDoctorId() uuid.UUID {
	_, _, doctorId, _ := t.toKeys()
	return doctorId
}

func (t TimelineMapKey) getQuarter() (quarter, year int) {
	quarter, year, _, _ = t.toKeys()
	return quarter, year
}

type timelineMapWithPatientId map[uuid.UUID]GetTimelineItemsResponse

// map by quarter_doctor_contract
// the result is map[quarter_doctor_contract]map[patientId]GetTimelineItemsResponse.
type TimelineMap map[TimelineMapKey]timelineMapWithPatientId

// ToMap by quarter_doctor_contract with map by patientId.
// the result is map[quarter_doctor_contract]map[patientId]GetTimelineItemsResponse.
func (g *GetTimelineItemsResponse) ToMap(ctx *titan.Context, contractMap contractIdsMap, scheinMap mappedSchein) TimelineMap {
	m := map[TimelineMapKey]GetTimelineItemsResponse{}
	createKey := func(quarter, year int, treatmentDoctorId uuid.UUID, contractId string) TimelineMapKey {
		return TimelineMapKey(fmt.Sprintf("%d%s%d%s%s%s%s",
			quarter,
			spliter,
			year,
			spliter,
			treatmentDoctorId.String(),
			spliter,
			contractId,
		))
	}
	for _, v := range g.diagnoses {
		var schein *schein_common.GetScheinDetailByIdResponse
		if len(v.ScheinIds) > 0 {
			schein = pkg_utils.NewPointer(scheinMap[v.ScheinIds[0]])
		}
		if schein == nil {
			continue
		}
		key := createKey(v.Quarter, v.Year, v.TreatmentDoctorId, *schein.HzvContractId)
		if _, ok := m[key]; !ok {
			m[key] = GetTimelineItemsResponse{}
		}
		t := m[key]
		t.diagnoses = append(t.diagnoses, v)
		m[key] = t
	}
	for _, v := range g.services {
		var schein *schein_common.GetScheinDetailByIdResponse
		if len(v.ScheinIds) > 0 {
			schein = pkg_utils.NewPointer(scheinMap[v.ScheinIds[0]])
		}
		if schein == nil {
			continue
		}
		key := createKey(v.Quarter, v.Year, v.TreatmentDoctorId, *schein.HzvContractId)
		if _, ok := m[key]; !ok {
			m[key] = GetTimelineItemsResponse{}
		}
		t := m[key]
		t.services = append(t.services, v)
		m[key] = t
	}
	for _, v := range g.medicinePrescriptions {
		var schein *schein_common.GetScheinDetailByIdResponse
		if len(v.ScheinIds) > 0 {
			schein = pkg_utils.NewPointer(scheinMap[v.ScheinIds[0]])
		}
		if schein == nil {
			continue
		}
		contract := contractMap.getBy(*v.ContractId)
		if contract == nil {
			continue
		}
		canSubmitPrescription := contract.CheckExistAnforderung(model.ICheckExistAnforderung{
			ComplianceIds: []string{"VSST515", "VSST532"},
			CheckTime:     util.NowUnixMillis(ctx),
		})
		if !canSubmitPrescription {
			continue
		}
		key := createKey(v.Quarter, v.Year, v.TreatmentDoctorId, *schein.HzvContractId)
		if _, ok := m[key]; !ok {
			m[key] = GetTimelineItemsResponse{}
		}
		t := m[key]
		t.medicinePrescriptions = append(t.medicinePrescriptions, v)
		m[key] = t
	}
	timelineMap := TimelineMap{}
	timelineWithPatient := timelineMapWithPatientId{}
	for k, v := range m {
		for _, d := range v.diagnoses {
			if _, ok := timelineWithPatient[d.PatientId]; !ok {
				timelineWithPatient[d.PatientId] = GetTimelineItemsResponse{}
			}
			t := timelineWithPatient[d.PatientId]
			t.diagnoses = append(t.diagnoses, d)
			timelineWithPatient[d.PatientId] = t
		}
		for _, s := range v.services {
			if _, ok := timelineWithPatient[s.PatientId]; !ok {
				timelineWithPatient[s.PatientId] = GetTimelineItemsResponse{}
			}
			t := timelineWithPatient[s.PatientId]
			t.services = append(t.services, s)
			timelineWithPatient[s.PatientId] = t
		}
		for _, m := range v.medicinePrescriptions {
			if _, ok := timelineWithPatient[m.PatientId]; !ok {
				timelineWithPatient[m.PatientId] = GetTimelineItemsResponse{}
			}
			t := timelineWithPatient[m.PatientId]
			t.medicinePrescriptions = append(t.medicinePrescriptions, m)
			timelineWithPatient[m.PatientId] = t
		}
		timelineMap[k] = timelineWithPatient
	}
	return timelineMap
}

func (srv *Service) GetTimelineItems(ctx *titan.Context, scheinIds []uuid.UUID) (*GetTimelineItemsResponse, []timeline_common.TimelineModel, []schein.ScheinRepo, error) {
	normalRezeptFilter := pkg_utils.AllElemMatch(timeline_repo.Field_MedicineERezeptStatus, bson.M{operator.Eq: nil})
	sentERezeptFilter := pkg_utils.AllElemMatch(timeline_repo.Field_MedicineERezeptStatus, bson.M{operator.Eq: qes_common.Status_Sent})

	timelineFilter := bson.A{
		// According to PRO-12595, PRO-12190, we only count the medicine prescription that has been printed and not deleted , but still send deleted ones to HPM
		bson.M{
			timeline_repo.Field_timelineEntityType:             timeline_common.TimelineEntityType_MedicinePrescription,
			timeline_repo.Field_MedicinePrescription_PrintDate: bson.M{operator.Ne: nil},
			operator.And: bson.A{
				bson.M{
					operator.Or: bson.A{
						bson.M{timeline_repo.Field_MedicinePrescription_Medicines: normalRezeptFilter},
						bson.M{timeline_repo.Field_MedicinePrescription_Medicines: sentERezeptFilter},
					},
				},
				bson.M{
					operator.Or: bson.A{
						bson.M{timeline_repo.Field_IsDeleted: false,
							timeline_repo.Field_BillingInfo: bson.M{"$exists": true, "$eq": nil},
						},
						bson.M{
							operator.And: bson.A{
								bson.M{timeline_repo.Field_IsDeleted: true},
								bson.M{timeline_repo.Field_BillingInfo: bson.M{"$exists": true, "$ne": nil}},
							},
						},
					},
				},
			},
		},
	}
	timelineFilter = append(timelineFilter, bson.M{
		timeline_repo.Field_timelineEntityType: bson.M{
			operator.In: bson.A{
				timeline_common.TimelineEntityType_Diagnose,
				timeline_common.TimelineEntityType_Service,
			},
		},
		timeline_repo.Field_IsDeleted: false,
		timeline_repo.Field_EncounterCase: bson.M{
			operator.Ne: patient_encounter.PRE_ENROLLMENT,
		},
	})
	filter := bson.M{
		timeline_repo.Field_ScheinIds: bson.M{
			operator.In: scheinIds,
		},
		operator.And: bson.A{
			bson.M{
				operator.Or: timelineFilter,
			},
		},
	}
	entities := []timeline_common.TimelineModel{}
	entries, err := srv.timelineService.Find(ctx, filter)
	if err != nil {
		return nil, entities, nil, errors.WithMessage(err, "get timeline items failed")
	}
	if len(entries) == 0 {
		return nil, entities, nil, nil
	}
	schienIds := []uuid.UUID{}
	for _, t := range entries {
		schienIds = append(schienIds, t.ScheinIds...)
	}
	scheins, err := srv.scheinRepo.GetByIds(ctx, schienIds)
	if err != nil {
		return nil, entities, nil, errors.WithMessage(err, "get scheins failed")
	}

	diagnoses := []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{}
	services := []timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{}
	medicinePrescription := []timeline_repo.TimelineEntity[patient_encounter.EncounterMedicinePrescription]{}
	for _, e := range entries {
		model := timeline_service.ConvertEntityAnyToModel(e)
		entities = append(entities, model)
		switch e.Type {
		case timeline_common.TimelineEntityType_Diagnose:
			diagnoses = append(diagnoses, timeline_service.ConvertModelToEntity[patient_encounter.EncounterDiagnoseTimeline](model))
		case timeline_common.TimelineEntityType_Service:
			services = append(services, timeline_service.ConvertModelToEntity[patient_encounter.EncounterServiceTimeline](model))
		case timeline_common.TimelineEntityType_MedicinePrescription:
			medicinePrescription = append(medicinePrescription, timeline_service.ConvertModelToEntity[patient_encounter.EncounterMedicinePrescription](model))
		}
	}
	return &GetTimelineItemsResponse{
		diagnoses:             diagnoses,
		services:              services,
		medicinePrescriptions: medicinePrescription,
	}, entities, scheins, nil
}

func (srv *Service) CreatePseudoServiceTimeline(ctx *titan.Context, prescriptionOnlyScheins []uuid.UUID, pseudoServices []timeline_common.TimelineModel) error {
	pseudoServicesTimelines := []timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{}
	for _, model := range pseudoServices {
		entity := timeline_service.ConvertModelToEntity[patient_encounter.EncounterServiceTimeline](model)
		if slice.Contains(prescriptionOnlyScheins, entity.ScheinIds[0]) {
			continue
		}
		entity.BillingInfo = &common.BillingInfo{
			BillingStatus: pkg_utils.NewPointer(common.Billed),
			SubmittedDate: pkg_utils.NewPointer(util.NowUnixMillis(ctx)),
		}
		pseudoServicesTimelines = append(pseudoServicesTimelines, entity)
	}

	if _, err := srv.serviceTimelineService.CreateMany(ctx, pseudoServicesTimelines); err != nil {
		return errors.WithMessage(err, "create pseudo service timeline failed")
	}
	return nil
}
