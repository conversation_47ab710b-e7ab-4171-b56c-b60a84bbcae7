package hpm_next_builder

import (
	"time"

	"git.tutum.dev/medi/tutum/ares/pkg/formkey"
	hpm_rest_amm_client "git.tutum.dev/medi/tutum/ares/pkg/hpm_rest/amm"
	hpm_rest_hzv_client "git.tutum.dev/medi/tutum/ares/pkg/hpm_rest/hzv"
	"git.tutum.dev/medi/tutum/ares/pkg/hpm_service/hpm_next"
	"git.tutum.dev/medi/tutum/ares/service/contract/contract/model"
	patient_participation_service "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_participation"
	medicine_common "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee"
	patient_profile_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"gitlab.com/silenteer-oss/titan"
)

// Group by order contract -> doctor -> encountercase
type BuilderService struct{}

type (
	Diagnose     timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]
	Service      timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]
	Prescription timeline_repo.TimelineEntity[patient_encounter.EncounterMedicinePrescription]

	Patient              patient_profile_repo.PatientProfile
	PatientWithDocuments struct {
		Patient      Patient
		Diagnoses    []Diagnose
		Services     []Service
		Prescription []Prescription
	}
	builderStarteAbrechnung struct {
		contract                hpm_next.BillingContract
		contractIds             []string
		doctor                  employee.EmployeeProfile
		mapAllDoctor            map[uuid.UUID]employee.EmployeeProfile
		softwareInformation     hpm_next.SoftwareInformation
		isTestMode              bool
		isTestRun               bool
		isOffline               bool
		documents               []PatientWithDocuments
		encounterCase           *patient_encounter.EncounterCase
		doctorHzvContract       []model.Contract
		doctorFavContract       []model.Contract
		scheins                 []schein.ScheinRepo
		patientParticipationMap map[uuid.UUID]patient_participation_service.PatientParticipation
	}

	PrescriptionBuilder struct {
		Request             *hpm_next.StarteVerordnungsdatenUebermittlung
		DoctorId            uuid.UUID
		PrescriptionEntries []Prescription
	}

	PrescriptionBuilderV2 struct {
		Request             *hpm_rest_amm_client.VerordnungsContainer
		DoctorId            uuid.UUID
		PrescriptionEntries []Prescription
	}
)

func (*BuilderService) BuildPayload(testMode, isTestRun, isOffline bool) *builderStarteAbrechnung {
	return &builderStarteAbrechnung{
		isTestMode: testMode,
		isTestRun:  isTestRun,
		isOffline:  isOffline,
	}
}
func (b *builderStarteAbrechnung) AddEncounterCase(encounterCase *patient_encounter.EncounterCase) *builderStarteAbrechnung {
	b.encounterCase = encounterCase
	return b
}

func (b *builderStarteAbrechnung) AddPatientParticipationMap(patientParticipationMap map[uuid.UUID]patient_participation_service.PatientParticipation) *builderStarteAbrechnung {
	b.patientParticipationMap = patientParticipationMap
	return b
}

func (b *builderStarteAbrechnung) AddDoctorInfo(doctor employee.EmployeeProfile, mapCreatedBy map[uuid.UUID]employee.EmployeeProfile) *builderStarteAbrechnung {
	b.doctor = doctor
	b.mapAllDoctor = mapCreatedBy
	return b
}

func (b *builderStarteAbrechnung) AddContract(contract model.Contract, chargeSys string, yearQuarter util.YearQuarter) *builderStarteAbrechnung {
	b.contract = hpm_next.BillingContract{
		Contract:       &contract,
		ChargeSystemId: chargeSys,
		Year:           yearQuarter.Year,
		Quarter:        yearQuarter.Quarter,
	}
	return b
}

func (b *builderStarteAbrechnung) AddCurrentContractParticipation(contractIds []string) *builderStarteAbrechnung {
	b.contractIds = contractIds
	return b
}

func (b *builderStarteAbrechnung) AddSoftwareInfomation(softwareInformation hpm_next.SoftwareInformation) *builderStarteAbrechnung {
	b.softwareInformation = softwareInformation
	return b
}

func (b *builderStarteAbrechnung) AddScheins(scheins []schein.ScheinRepo) *builderStarteAbrechnung {
	b.scheins = scheins
	return b
}

func (b *builderStarteAbrechnung) AddPatientWithDocuments(document PatientWithDocuments) *builderStarteAbrechnung {
	if len(b.documents) == 0 {
		b.documents = []PatientWithDocuments{document}
		return b
	}
	_, idx := slice.FindOneIndex(b.documents, func(pwd PatientWithDocuments) bool {
		return pwd.Patient.Id.String() == document.Patient.Id.String()
	})
	if idx == -1 {
		b.documents = append(b.documents, document)
	} else {
		b.documents[idx].Services = append(b.documents[idx].Services, document.Services...)
		b.documents[idx].Diagnoses = append(b.documents[idx].Diagnoses, document.Diagnoses...)
	}
	return b
}

func (b *builderStarteAbrechnung) AddDoctorContracts(hzvContract, favContract []model.Contract) *builderStarteAbrechnung {
	b.doctorHzvContract = hzvContract
	b.doctorFavContract = favContract
	return b
}

// Consider to remove this function (zero references)
func (b *builderStarteAbrechnung) Build() (*hpm_next.StarteAbrechnung, PrescriptionBuilder) {
	doctor := b.doctor
	diagnoseCodeSystemName := hpm_next.ToHmpDiagnoseCodeSystemName(b.contract.Year)
	haevgNumber := function.Do(func() string {
		if doctor.HavgId != nil {
			return *doctor.HavgId
		}
		return ""
	})
	mediId := function.Do(func() string {
		if doctor.MediverbundId != nil {
			return *doctor.MediverbundId
		}
		return ""
	})
	lanr := function.Do(func() string {
		if doctor.Lanr != nil {
			return *doctor.Lanr
		}
		return ""
	})
	operations := []*hpm_next.Operation{}
	existPrescription := slice.FindOne(b.documents, func(pwd PatientWithDocuments) bool {
		return len(pwd.Prescription) > 0
	}) != nil
	prescriptionBuilder := PrescriptionBuilder{
		Request:  nil,
		DoctorId: *doctor.Id,
		PrescriptionEntries: function.Do(func() []Prescription {
			result := []Prescription{}
			for _, d := range b.documents {
				result = append(result, d.Prescription...)
			}
			return result
		}),
	}
	if existPrescription {
		prescriptionBuilder.Request = &hpm_next.StarteVerordnungsdatenUebermittlung{
			Container: &hpm_next.VerordnungsContainer{
				NurPrueflauf:      b.isTestRun,
				Testuebermittlung: b.isTestMode,
				VertragsContainer: &hpm_next.VertragsContainer{
					Vertragskontext: b.contract.ToVertragskontext(),
					Vertragsarzt: &hpm_next.Vertragsarzt{
						AkademischerGrad: function.Do(func() string {
							if doctor.Title != nil {
								return *doctor.Title
							}
							return ""
						}),
						Bsnr:                 doctor.Bsnr,
						HaevgMitgliedsNummer: haevgNumber,
						Hausnummer:           "",
						Lanr:                 lanr,
						MediId:               mediId,
						Nachname:             doctor.LastName,
						Ort: function.Do(func() string {
							if doctor.Address != nil {
								return *doctor.Address
							}
							return ""
						}),
						Plz:     doctor.ZipCode,
						Strasse: doctor.Street,
						Telefax: doctor.Fax,
						Telefon: function.Do(func() string {
							if doctor.Phone != nil {
								return *doctor.Phone
							}
							return ""
						}),
						TelefonMobil: doctor.MobilePhone,
						Vorname:      doctor.FirstName,
					},
					ContainerBasis: &hpm_next.ContainerBasis{
						ArztInformationsSystem: &b.softwareInformation,
					},
				},
				Dokumentationen: &hpm_next.ArrayOfVerordnungsDokumentation{
					VerordnungsDokumentation: slice.Map(b.documents, func(d PatientWithDocuments) *hpm_next.VerordnungsDokumentation {
						return &hpm_next.VerordnungsDokumentation{
							Patient: d.Patient.mappingPatient(b.contract.Contract.Id),
							Verordnungen: &hpm_next.Verordnungen{
								Rezepte: &hpm_next.ArrayOfRezept{
									Rezept: function.Do(func() []*hpm_next.Rezept {
										result := []*hpm_next.Rezept{}
										for _, p := range d.Prescription {
											result = append(result, p.mappingMedicinePrescriptionsHPMNext()...)
										}
										return result
									}),
								},
							},
						}
					}),
				},
			},
		}
	}
	totalDiagnosises := 0
	totalServices := 0
	for _, d := range b.documents {
		totalDiagnosises += len(d.Diagnoses)
		totalServices += len(d.Services)
	}
	if totalDiagnosises == 0 && totalServices == 0 {
		return nil, prescriptionBuilder
	}
	return &hpm_next.StarteAbrechnung{
		Container: &hpm_next.AbrechnungsContainer{
			NurPrueflauf:      b.isTestRun,
			Testuebermittlung: b.isTestMode,
			Dokumentationen: &hpm_next.ArrayOfAbrechnungsDokumentation{
				AbrechnungsDokumentation: slice.Map(b.documents, func(d PatientWithDocuments) *hpm_next.AbrechnungsDokumentation {
					return &hpm_next.AbrechnungsDokumentation{
						Patient: d.Patient.mappingPatient(b.contract.Contract.Id),
						Diagnosen: &hpm_next.ArrayOfDiagnose{
							Diagnose: slice.Map(d.Diagnoses, func(d Diagnose) *hpm_next.Diagnose {
								return d.mappingDiangose(diagnoseCodeSystemName, haevgNumber, mediId, lanr)
							}),
						},
						Leistungen: &hpm_next.ArrayOfLeistung{
							Leistung: slice.Map(d.Services, func(s Service) *hpm_next.Leistung {
								v, o := s.mappingService(haevgNumber, mediId, lanr)
								operations = append(operations, o...)
								return v
							}),
						},
						Praxisgebuehren:          nil,
						Ueberweisungen:           nil,
						UnfallkennzeichenGesetzt: b.encounterCase != nil && *b.encounterCase == patient_encounter.NOT,
						Operationen: &hpm_next.ArrayOfOperation{
							Operation: operations,
						},
					}
				}),
			},
			VertragsContainer: &hpm_next.VertragsContainer{
				Vertragskontext: b.contract.ToVertragskontext(),
				Vertragsarzt: &hpm_next.Vertragsarzt{
					AkademischerGrad: function.Do(func() string {
						if doctor.Title != nil {
							return *doctor.Title
						}
						return ""
					}),
					Bsnr:                 doctor.Bsnr,
					HaevgMitgliedsNummer: haevgNumber,
					Hausnummer:           "",
					Lanr:                 lanr,
					MediId:               mediId,
					Nachname:             doctor.LastName,
					Ort: function.Do(func() string {
						if doctor.Address != nil {
							return *doctor.Address
						}
						return ""
					}),
					Plz:     doctor.ZipCode,
					Strasse: doctor.Street,
					Telefax: doctor.Fax,
					Telefon: function.Do(func() string {
						if doctor.Phone != nil {
							return *doctor.Phone
						}
						return ""
					}),
					TelefonMobil: doctor.MobilePhone,
					Vorname:      doctor.FirstName,
				},
				ContainerBasis: &hpm_next.ContainerBasis{
					ArztInformationsSystem: &b.softwareInformation,
				},
			},
			Uebertragungsart: util.NewPointer(hpm_next.UebertragungsartOnline),
		},
	}, prescriptionBuilder
}

func (b *builderStarteAbrechnung) BuildV2(ctx *titan.Context) (*hpm_rest_hzv_client.AbrechnungsContainer, PrescriptionBuilderV2) {
	doctor := b.doctor
	prescriptionBuilder := PrescriptionBuilderV2{
		Request:  nil,
		DoctorId: *doctor.Id,
		PrescriptionEntries: function.Do(func() []Prescription {
			result := []Prescription{}
			for _, d := range b.documents {
				result = append(result, d.Prescription...)
			}
			return result
		}),
	}
	var participate *model.Contract
	participate = slice.FindOne(b.doctorHzvContract, func(c model.Contract) bool {
		return c.Id == b.contract.Contract.Id
	})
	if participate == nil {
		participate = slice.FindOne(b.doctorFavContract, func(c model.Contract) bool {
			return c.Id == b.contract.Contract.Id
		})
	}

	var vertragspartneridentifikator *string = nil

	contractType := participate.GetContractType().Type()
	if contractType != nil {
		if *contractType == model.ContractType_HouseDoctorCare {
			vertragspartneridentifikator = doctor.HavgVpId
		} else if *contractType == model.ContractType_SpecialistCare {
			vertragspartneridentifikator = doctor.MediverbundVpId
		}
	}

	diagnoseCodeSystemName := hpm_rest_hzv_client.ToDiagnoseCodeSystemName(b.contract.Year)
	dokumentationen := []hpm_rest_amm_client.VerordnungsDokumentation{}
	for _, d := range b.documents {
		participation, ok := b.patientParticipationMap[*d.Patient.Id]
		isDeputy := ok && participation.DoctorFunctionType == patient_participation_service.DoctorFunctionTypeDeputy
		patientHealthInsurance := d.Patient.PatientInfo.GetActiveInsurance()
		rezeptResult := []hpm_rest_amm_client.Rezept{}
		for _, p := range d.Prescription {
			for _, formInfo := range p.Payload.FormInfos {
				const strTrue = "true"
				muster6PayloadMap, _ := formkey.NewFormMap[formkey.FormKey_MUSTER_16](formInfo.FormSetting)
				bvg := function.If(muster6PayloadMap[formkey.MUSTER_16_TOGGLE_6_BVG] == strTrue, true, false)
				chargeable := function.If(muster6PayloadMap[formkey.MUSTER_16_CHECKBOX_GEBUHRFREI] == strTrue, true, false)
				unfall := function.If(muster6PayloadMap[formkey.MUSTER_16_CHECKBOX_UNFALL] == strTrue, true, false)
				// notfall := function.If(muster6PayloadMap[formkey.MUSTER_16_CHECKBOX_ARBEITSUNFALL] == strTrue, true, false) //accident at work

				sonstige := function.If(muster6PayloadMap[formkey.MUSTER_16_CHECKBOX_SONSTIGE] == strTrue, true, false)
				bedarf := function.If(muster6PayloadMap[formkey.MUSTER_16_TOGGLE_9_BEDARF] == strTrue, true, false)
				impfstoff := function.If(muster6PayloadMap[formkey.MUSTER_16_TOGGLE_8_IMPFSTOFF] == strTrue, true, false)
				hilfsmittel := function.If(muster6PayloadMap[formkey.MUSTER_16_TOGGLE_7_HILFSMITTEL] == strTrue, true, false)
				noctu := function.If(muster6PayloadMap[formkey.MUSTER_16_CHECKBOX_NOCTU] == strTrue, true, false)
				begr := function.If(muster6PayloadMap[formkey.MUSTER_16_CHECKBOX_BEGR] == strTrue, true, false)
				medicineInfos := slice.Map(formInfo.Medicines, func(medicine medicine_common.MedicineInfo) hpm_rest_amm_client.Arzneimittel {
					return hpm_rest_amm_client.Arzneimittel{
						ArzneimittelId: medicine.Id.String(),
						AutIdem:        medicine.AutIdem,
						Anzahl:         &medicine.Quantity,
						// Pzn:            medicine.Pzn,
						// Atc:            medicine.DrugInformation.ATC,
					}
				})
				prescribeDate := util.ConvertMillisecondsToTime(*formInfo.PrescribeDate, ctx.RequestTimeZone())

				var printedDate *time.Time
				if formInfo.PrintDate != nil {
					printedDate = util.NewPointer(util.ConvertMillisecondsToTime(*formInfo.PrintDate, ctx.RequestTimeZone()))
				} else {
					printedDate = util.NewPointer(util.Now(ctx))
				}
				rezept := hpm_rest_amm_client.Rezept{
					ArbeitsunfallDatum: nil, // accident date
					ArbeitsunfallOrt:   nil, // accident at work place
					Arzneimittel:       &medicineInfos,
					Bvg:                &bvg,
					Gebuehrenpflichtig: &chargeable,
					KvDetails: &hpm_rest_amm_client.RezeptKvDetails{
						Begruendungspflicht: &begr, // Begr.-Pflicht
						Hilfsmittel:         &hilfsmittel,
						Impfstoff:           &impfstoff,
					},
					Notfall:             &noctu, // emmergeci
					RezeptId:            formInfo.Id.String(),
					SonstigerKt:         &sonstige,
					Sprechstundenbedarf: &bedarf,
					Stellvertreter:      b.mapPrescriptionStellvertreter(p.CreatedBy, p.ScheinIds, isDeputy),
					Unfall:              &unfall, // normal accident
					VerordnungsZeitpunkte: hpm_rest_amm_client.VerordnungsZeitpunkte{
						Erstellt:  &prescribeDate, // created
						Geaendert: printedDate,    // updated, according to PRO-12189, use printed date for updated field
						Gedruckt:  printedDate,    // printed
						Geloescht: function.Do(func() *time.Time { // deleted
							removeAction := slice.FindOne(p.RecentAuditLogs, func(auditLog timeline_repo.AuditLog) bool {
								return auditLog.ActionType == common.Remove
							})
							if removeAction == nil {
								return nil
							}

							return util.NewPointer(removeAction.Date.UTC().In(ctx.RequestTimeZone()))
						}), // deleted
					},
				}
				rezeptResult = append(rezeptResult, rezept)
			}
		}
		if len(rezeptResult) > 0 {
			dokumentationen = append(dokumentationen, hpm_rest_amm_client.VerordnungsDokumentation{
				Patient: &hpm_rest_amm_client.Patient{
					Geburtsdatum: b.parseDoB(d.Patient.DateOfBirth),
					Geschlecht:   hpm_rest_amm_client.PatientGeschlecht(d.Patient.PatientInfo.PersonalInfo.Gender),
					Nachname:     d.Patient.LastName,
					PatientenId:  d.Patient.Id.String(),
					Versicherungsnachweis: hpm_rest_amm_client.Versicherungsnachweis{
						KrankenkassenIk: patientHealthInsurance.GetIkNumberString(),
						VersichertenNummer: function.Do(func() string {
							if patientHealthInsurance.InsuranceNumber != nil {
								return *patientHealthInsurance.InsuranceNumber
							}
							return ""
						}),
						VersichertenArt:         "1",
						BesonderePersonengruppe: util.NewString(string(patientHealthInsurance.SpecialGroup)),
						DmpKennzeichnung:        util.NewString(patientHealthInsurance.DMPLabeling),
					},
					Vorname: d.Patient.FirstName,
					AktuelleVertragsteilnahmen: util.NewPointer(slice.Map(b.contractIds, func(id string) hpm_rest_amm_client.Vertragsteilnahme {
						participation, ok := b.patientParticipationMap[*d.Patient.Id]
						isDeputy := ok && participation.DoctorFunctionType == patient_participation_service.DoctorFunctionTypeDeputy
						return hpm_rest_amm_client.Vertragsteilnahme{
							VertragsIdentifikator: id,
							IstVertreterteilnahme: isDeputy,
						}
					})),
				},
				Verordnungen: &hpm_rest_amm_client.Verordnungen{
					Rezepte: rezeptResult,
				},
			})
		}
	}

	if len(dokumentationen) > 0 {
		prescriptionBuilder.Request = &hpm_rest_amm_client.VerordnungsContainer{
			AbsenderBsnr: b.doctor.Bsnr,
			ArztInformationsSystem: hpm_rest_amm_client.SoftwareInformation{
				Name:         &b.softwareInformation.Name,
				Organisation: &b.softwareInformation.Organisation,
				SystemOid:    b.softwareInformation.SystemOid,
				Version:      b.softwareInformation.Version,
			},
			Dokumentationen:   dokumentationen,
			NurPrueflauf:      b.isTestRun,
			Testuebermittlung: b.isTestMode,
			Vertragskontext: hpm_rest_amm_client.Vertragskontext{
				AbrechnungsJahr:            b.contract.Year,
				AbrechnungsQuartal:         b.contract.Quarter,
				HonoraranlageIdentifikator: b.contract.ChargeSystemId,
				VertragsIdentifikator:      b.contract.Contract.Id,
			},
			Vertragspartneridentifikator: function.Do(func() string {
				if vertragspartneridentifikator == nil {
					return ""
				}
				return *vertragspartneridentifikator
			}),
		}
	}
	var resultPayload *hpm_rest_hzv_client.AbrechnungsContainer

	documents := slice.Filter(b.documents, func(d PatientWithDocuments) bool {
		return len(d.Services)+len(d.Diagnoses) > 0
	})

	if len(documents) > 0 {
		resultPayload = &hpm_rest_hzv_client.AbrechnungsContainer{
			AbsenderBsnr: b.doctor.Bsnr,
			ArztInformationsSystem: hpm_rest_hzv_client.SoftwareInformation{
				Name:         &b.softwareInformation.Name,
				Organisation: &b.softwareInformation.Organisation,
				SystemOid:    b.softwareInformation.SystemOid,
				Version:      b.softwareInformation.Version,
			},
			NurPrueflauf:      b.isTestRun,
			Testuebermittlung: b.isTestMode,
			Uebertragungsart: function.Do(func() hpm_rest_hzv_client.Uebertragungsart {
				yearQuarter := util.YearQuarter{
					Year:    b.contract.Year,
					Quarter: b.contract.Quarter,
				}
				startQuarter := function.Do(func() int64 {
					start := yearQuarter.StartTime()
					if start != nil {
						return *start
					}
					return util.NowUnixMillis(ctx)
				})
				isContractSupportOffline := b.contract.Contract.CheckExistAnforderung(model.ICheckExistAnforderung{
					ComplianceIds: []string{"ABRG386", "ABRG454", "ABRG927"},
					CheckTime:     startQuarter,
					KvRegion:      util.NewString(doctor.Bsnr[:2]),
				})
				submitType := map[bool]hpm_rest_hzv_client.Uebertragungsart{
					true:  hpm_rest_hzv_client.Datentraeger,
					false: hpm_rest_hzv_client.Online,
				}
				return submitType[isContractSupportOffline && b.isOffline]
			}),
			Vertragskontext: hpm_rest_hzv_client.Vertragskontext{
				AbrechnungsJahr:            b.contract.Year,
				AbrechnungsQuartal:         b.contract.Quarter,
				HonoraranlageIdentifikator: b.contract.ChargeSystemId,
				VertragsIdentifikator:      b.contract.Contract.Id,
			},
			Dokumentationen: slice.Map(documents, func(d PatientWithDocuments) hpm_rest_hzv_client.AbrechnungsDokumentation {
				patientHealthInsurance := d.Patient.PatientInfo.GetActiveInsurance()
				diagnoses := []hpm_rest_hzv_client.Diagnose{}
				leistungs := []hpm_rest_hzv_client.Leistung{}
				operations := []hpm_rest_hzv_client.Operation{}
				participation, ok := b.patientParticipationMap[*d.Patient.Id]
				isDeputy := ok && participation.DoctorFunctionType == patient_participation_service.DoctorFunctionTypeDeputy
				for _, dianogsis := range d.Diagnoses {
					diagnoses = append(diagnoses, hpm_rest_hzv_client.Diagnose{
						CodeSystemName:             diagnoseCodeSystemName,
						DiagnoseCode:               dianogsis.Payload.Code,
						DiagnoseId:                 dianogsis.Id.String(),
						Diagnoseausnahmetatbestand: nil,
						Diagnoseerlaeuterung:       nil,
						DokumentationsDatum:        dianogsis.SelectedDate,
						IstDauerDiagnose:           util.NewBool(dianogsis.Payload.Type == patient_encounter.DIAGNOSETYPE_PERMANENT),
						Seitenlokalisation: function.Do(func() hpm_rest_hzv_client.DiagnoseSeitenlokalisation {
							laterality := dianogsis.Payload.Laterality
							if laterality == nil || *laterality == "" {
								return hpm_rest_hzv_client.DiagnoseSeitenlokalisationU
							}
							return hpm_rest_hzv_client.DiagnoseSeitenlokalisation(*laterality)
						}),
						Sicherheit: function.Do(func() hpm_rest_hzv_client.DiagnoseSicherheit {
							if dianogsis.Payload.Certainty == nil {
								return hpm_rest_hzv_client.DiagnoseSicherheitKeineAngabe
							}
							return hpm_rest_hzv_client.DiagnoseSicherheit(*dianogsis.Payload.Certainty)
						}),
						Stellvertreter: b.mapStellvertreter(dianogsis.CreatedBy, dianogsis.ScheinIds, isDeputy), // delegateDoctorLanr
					})
				}
				for _, s := range d.Services {
					leistungs = append(leistungs, hpm_rest_hzv_client.Leistung{
						LeistungsId:           s.Id.String(),
						Leistungsdatum:        &s.SelectedDate,                                         // serviceDate required
						Leistungsziffer:       s.Payload.Code,                                          // serviceNumber required
						Anforderungszeitpunkt: nil,                                                     // requestTime
						Stellvertreter:        b.mapStellvertreter(s.CreatedBy, s.ScheinIds, isDeputy), // delegateDoctorLanr
						Sachkosten: function.Do(func() *[]hpm_rest_hzv_client.Sachkosten {
							if s.Payload.MaterialCosts == nil {
								return nil
							}
							if s.Payload.MaterialCosts.MaterialCostsItemList == nil {
								return nil
							}
							if len(*s.Payload.MaterialCosts.MaterialCostsItemList) == 0 {
								return nil
							}
							return util.NewPointer(slice.Map(*s.Payload.MaterialCosts.MaterialCostsItemList, func(m *patient_encounter.MaterialCostsItemList) hpm_rest_hzv_client.Sachkosten {
								return hpm_rest_hzv_client.Sachkosten{
									Betrag:        m.Amount,
									Bezeichnungen: []string{m.Description},
								}
							}))
						}),
						UeberweisenderArzt: function.Do(func() *hpm_rest_hzv_client.ArztIdentifikation {
							scheinWithReferralDoctor := slice.FindOne(b.scheins, func(schein schein.ScheinRepo) bool {
								return slice.Contains(s.ScheinIds, *schein.Id) && schein.ReferralDoctor != nil
							})
							if scheinWithReferralDoctor == nil {
								return nil
							}
							return &hpm_rest_hzv_client.ArztIdentifikation{
								Lanr: &scheinWithReferralDoctor.ReferralDoctor.Lanr,
								Bsnr: &scheinWithReferralDoctor.ReferralDoctor.Bsnr,
							}
						}),
						InVertretungFuer: function.Do(func() *hpm_rest_hzv_client.ArztIdentifikation {
							referralDoctorInfo := s.Payload.ReferralDoctorInfo
							if referralDoctorInfo == nil {
								return nil
							}
							lanr, bsnr := referralDoctorInfo.Lanr, referralDoctorInfo.Bsnr
							if lanr == nil && bsnr == nil {
								return nil
							}
							if (lanr != nil && *lanr == "") || (bsnr != nil && *bsnr == "") {
								return nil
							}
							return &hpm_rest_hzv_client.ArztIdentifikation{
								Lanr: lanr,
								Bsnr: bsnr,
							}
						}),
						Abrechnungsbegruendung: function.Do(func() *string {
							if s.Payload.AdditionalInfos != nil {
								billingReason := slice.FindOne(*s.Payload.AdditionalInfos, func(a *patient_encounter.AdditionalInfoParent) bool {
									if a != nil && a.FK == "5009" {
										return true
									}
									return false
								})
								if billingReason != nil {
									return util.NewPointer((*billingReason).Value)
								}
							}
							return nil
						}), // billingReason -> 5009
					})
				}
				return hpm_rest_hzv_client.AbrechnungsDokumentation{
					Praxisgebuehren:          nil,
					Ueberweisungen:           nil,
					UnfallkennzeichenGesetzt: b.encounterCase != nil && *b.encounterCase == patient_encounter.NOT,
					Patient: hpm_rest_hzv_client.Patient{
						Geburtsdatum: b.parseDoB(d.Patient.DateOfBirth),
						Geschlecht:   hpm_rest_hzv_client.PatientGeschlecht(d.Patient.PatientInfo.PersonalInfo.Gender),
						Nachname:     d.Patient.LastName,
						PatientenId:  d.Patient.Id.String(),
						Versicherungsnachweis: hpm_rest_hzv_client.Versicherungsnachweis{
							KrankenkassenIk: patientHealthInsurance.GetIkNumberString(),
							VersichertenNummer: function.Do(func() string {
								if patientHealthInsurance.InsuranceNumber != nil {
									return *patientHealthInsurance.InsuranceNumber
								}
								return ""
							}),
							VersichertenArt:         "1",
							BesonderePersonengruppe: util.NewString(string(patientHealthInsurance.SpecialGroup)),
							DmpKennzeichnung:        util.NewString(patientHealthInsurance.DMPLabeling),
						},
						Vorname: d.Patient.FirstName,
						AktuelleVertragsteilnahmen: util.NewPointer(slice.Map(b.contractIds, func(id string) hpm_rest_hzv_client.Vertragsteilnahme {
							return hpm_rest_hzv_client.Vertragsteilnahme{
								VertragsIdentifikator: id,
								IstVertreterteilnahme: isDeputy,
							}
						})),
					},
					Diagnosen:   &diagnoses,
					Leistungen:  &leistungs,
					Operationen: &operations,
				}
			}),
			Vertragspartneridentifikator: function.Do(func() string {
				if vertragspartneridentifikator == nil {
					return ""
				}
				return *vertragspartneridentifikator
			}),
		}
	}
	return resultPayload, prescriptionBuilder
}

func (*builderStarteAbrechnung) parseDoB(dateOfBirth int64) string {
	// NOTE: -1 means 00.00.0000 on UI, according PRO-12072, we send 1900-01-01
	if dateOfBirth == -1 {
		return "1900-01-01"
	}
	return util.ConvertMillisecondsToTime(dateOfBirth).Format(util.YYYY_MM_DD)
}
