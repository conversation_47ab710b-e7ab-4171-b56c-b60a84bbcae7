package billing

import (
	"bytes"
	"crypto/rand"
	"encoding/json"
	"fmt"
	"io"
	"sort"
	"strings"

	"git.tutum.dev/medi/tutum/ares/service/billing_history/billing_history_common"
	billing_common "git.tutum.dev/medi/tutum/ares/service/domains/api/billing_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	patient_overview_domain "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_overview"
	patient_participation_api "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_participation"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"

	"git.tutum.dev/medi/tutum/ares/service/domains/internal/patient_overview"
	"git.tutum.dev/medi/tutum/ares/service/domains/internal/patient_participation"
	"git.tutum.dev/medi/tutum/ares/service/domains/pkg/constant"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	user_settings_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/user_settings"

	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	feature_flag_common "git.tutum.dev/medi/tutum/ares/service/feature_flag/common"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/settings"
	"git.tutum.dev/medi/tutum/ares/pkg/operator"
	share_config "git.tutum.dev/medi/tutum/ares/share/config"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/sidebar"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/user_settings"
	"git.tutum.dev/medi/tutum/ares/pkg/config/software"
	hpm_rest_client "git.tutum.dev/medi/tutum/ares/pkg/hpm_rest/client"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	setting_common "git.tutum.dev/medi/tutum/ares/service/domains/settings/common"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"

	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/doctor_participate"

	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee"

	billing_service_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/billing"

	hpm_rest_hzv_client "git.tutum.dev/medi/tutum/ares/pkg/hpm_rest/hzv"
	patient_profile_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"

	"git.tutum.dev/medi/tutum/ares/pkg/hpm_service"
	"git.tutum.dev/medi/tutum/ares/pkg/hpm_service/hpm"
	"git.tutum.dev/medi/tutum/ares/pkg/hpm_service/hpm_next"
	pkg_minio "git.tutum.dev/medi/tutum/ares/pkg/minio"
	contract_resource "git.tutum.dev/medi/tutum/ares/service/contract/contract"
	"git.tutum.dev/medi/tutum/ares/service/contract/contract/model"
	billing_service "git.tutum.dev/medi/tutum/ares/service/domains/api/billing"
	"git.tutum.dev/medi/tutum/ares/service/domains/internal/billing/hpm_next_builder"
	billing_service_model "git.tutum.dev/medi/tutum/ares/service/domains/internal/billing/model"
	qes_common "git.tutum.dev/medi/tutum/ares/service/domains/qes/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/settings/settings_service"
	timeline_common "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/timeline_service"
	feature_flag_service "git.tutum.dev/medi/tutum/ares/service/feature_flag"
	scheinService "git.tutum.dev/medi/tutum/ares/service/schein"
	pkg_utils "git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/minio/minio-go/v7/pkg/encrypt"
	"github.com/pkg/errors"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/hestia/infrastructure/util"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
)

type Service struct {
	timelineRepo                timeline_repo.TimelineEntityRepo[any]
	repo                        *Repository
	notifier                    *billing_service.BillingNotifier
	contractService             *contract_resource.Service
	minioClient                 *pkg_minio.Minio
	bucketName                  string
	timelineService             *timeline_service.TimelineService[any]
	serviceTimelineService      *timeline_service.TimelineService[patient_encounter.EncounterServiceTimeline]
	patientOverview             *patient_overview.Repository
	sidebarNotifier             *sidebar.SidebarNotifier
	softwareInformation         hpm.SoftwareInformation
	patientParticipationService *patient_participation.Service
	hpmNextService              hpm_service.HpmNextService
	hpmNextSoftware             hpm_service.HpmSoftware
	scheinRepo                  schein.ScheinRepoDefaultRepository
	hpmRest                     hpm_rest_client.ServiceRest
	userSettingsRepo            user_settings_repo.UserSettingsDefaultRepository
	getSettingFn                settings_service.GetSettingFn
	scheinService               *scheinService.ScheinService
	featureFlagService          *feature_flag_service.FeatureFlagService
}

var ServiceMod = submodule.Make[billing_service.HpmBillingService](New,
	RepoMod,
	pkg_minio.MinioMod,
	share_config.DomainServiceConfigsMod,
	patient_overview.PatientOverviewRepoMod,
	timeline_service.TimelineServiceAnyMod,
	timeline_service.TimelineServiceEncounterMod,
	contract_resource.ContractServiceMod,
	patient_participation.PatientParticipationServiceMod,
	hpm_service.HPMNextServiceMod,
	hpm_rest_client.HpmRestServiceMod,
	settings_service.GetSettingFlow,
	scheinService.ScheinServiceMod,
	feature_flag_service.FeatureFlagServiceMod,
)

func New(
	repo *Repository,
	minioClient *pkg_minio.Minio,
	domainConfig *share_config.DomainServiceConfigs,
	patientOverview *patient_overview.Repository,
	timelineService *timeline_service.TimelineService[any],
	serviceTimelineService *timeline_service.TimelineService[patient_encounter.EncounterServiceTimeline],
	contractService *contract_resource.Service,
	patientParticipation *patient_participation.Service,
	hpmNextService hpm_service.HpmNextService,
	hpmRest hpm_rest_client.ServiceRest,
	getSettingFn settings_service.GetSettingFn,
	scheinService *scheinService.ScheinService,
	featureFlagService *feature_flag_service.FeatureFlagService,
) billing_service.HpmBillingService {
	notifier := billing_service.NewBillingNotifier()
	sidebarNotifier := sidebar.NewSidebarNotifier()

	hpmNextSoftware := hpm_service.HpmSoftware(software.GetSoftwareConfig())

	scheinRepo := schein.NewScheinRepoDefaultRepository()

	userSettingsRepo := user_settings_repo.NewUserSettingsDefaultRepository()
	timelineRepo := timeline_repo.NewTimelineRepoDefaultRepository[any]()

	return &Service{
		timelineRepo,
		repo,
		notifier,
		contractService,
		minioClient,
		domainConfig.MinioClientConfig.BucketBilling,
		timelineService,
		serviceTimelineService,
		patientOverview,
		sidebarNotifier,
		hpm_service.GetHpmSoftwareInformation(),
		patientParticipation,
		hpmNextService,
		hpmNextSoftware,
		scheinRepo,
		hpmRest,
		userSettingsRepo,
		getSettingFn,
		scheinService,
		featureFlagService,
	}
}

// SubmitBilling return success and error submission
func (srv *Service) SubmitBilling(
	ctx *titan.Context,
	request *billing_service.BillingSubmissionRequest,
) (*billing_service.BillingSubmissionResponse, error) {
	successBillingSubmission, errorBillingSubmission, err := srv.submitBilling(ctx, request, false, request.SubmitPrescription)
	if err != nil {
		ctx.Logger().Warn("FAILED_TO_SUBMIT_SV_BILLING. Err: " + err.Error())
		return nil, err
	}

	if successBillingSubmission != nil {
		err = srv.notifier.NotifyBillingSubmissionResponse(ctx, &billing_service.EventBillingSubmissionResponse{
			ReferenceId:              request.ReferenceId,
			Status:                   billing_service.BillingSubmissionResponseStatus_Ok,
			SuccessBillingSubmission: successBillingSubmission,
		})
		if err != nil {
			ctx.Logger().Warn("FAILED_TO_NOTIFY_BILLING_SUCCESS_SUBMISSION_RESPONSE. Err: " + err.Error())
		}
		err = srv.notifier.NotifyBillingHistoryChange(ctx, &billing_service.EventBillingHistoryChange{
			Data: &billing_service.BillingHistoriesResponse{
				BillingHistories: []*billing_service.BillingHistoryResponse{
					successBillingSubmission.BillingHistory,
				},
			},
		})

		if err != nil {
			ctx.Logger().Warn("FAILED_TO_NOTIFY_BILLING_HISTORY_CHANGE. Err: " + err.Error())
		}
	}

	if errorBillingSubmission != nil {
		err = srv.notifier.NotifyBillingSubmissionResponse(ctx, &billing_service.EventBillingSubmissionResponse{
			ReferenceId:            request.ReferenceId,
			Status:                 billing_service.BillingSubmissionResponseStatus_Error,
			ErrorBillingSubmission: errorBillingSubmission,
		})
		if err != nil {
			ctx.Logger().Warn("FAILED_TO_NOTIFY_BILLING_ERROR_SUBMISSION_RESPONSE. Err: " + err.Error())
		}
		if err != nil {
			ctx.Logger().Warn("FAILED_TO_NOTIFY_SIDEBAR. Err: " + err.Error())
		}
	}

	return &billing_service.BillingSubmissionResponse{
		ReferenceId:       request.ReferenceId,
		SuccessSubmission: successBillingSubmission,
		ErrorSubmission:   errorBillingSubmission,
	}, err
}

func (srv *Service) submitBilling(ctx *titan.Context,
	request *billing_service.BillingSubmissionRequest,
	isTest bool,
	submitPrescription bool,
) (
	submittedBillingSubmission *billing_service.SuccessBillingSubmission,
	errorSubmission *billing_service.ErrorBillingSubmission,
	unHandledError error,
) {
	billingSubmission := request.BillingSubmission
	billingDoctorId := billingSubmission.BillingDoctorId
	// billingYear := billingSubmission.BillingYear
	// billingQuarter := billingSubmission.BillingQuarter
	billingContractId := billingSubmission.ContractId

	billingTimelines := billingSubmission.TimelineModels

	patientIdSet := getBillingPatientIds(billingTimelines)

	// TODO: filter last quarter
	previousQuarterChronicDiagnoses, err := srv.getEffectivePreviousQuarterChronicDiagnoses(
		ctx,
		patientIdSet,
	)

	if err != nil {
		return nil, nil, err
	}

	billingTimelines = append(billingTimelines, previousQuarterChronicDiagnoses...)

	doctorIdSet := getBillingDoctorIds(*billingDoctorId, billingTimelines)
	contractIdSet := []*model.ContractId{&billingContractId}

	contractIdsMap := srv.toContractsMap(contractIdSet)
	billingDoctorsMap, err := srv.toBillingDoctorsMap(ctx, doctorIdSet)
	if err != nil {
		return nil, nil, err
	}

	patientProfilesMap, err := srv.toPatientProfilesMap(ctx, patientIdSet)
	if err != nil {
		return nil, nil, err
	}

	doctorParticipatesMap, err := srv.toDoctorParticipatesMap(ctx, doctorIdSet)
	if err != nil {
		return nil, nil, err
	}

	billingSubmission.TimelineModels = billingTimelines

	submittedBillingSubmission, errorSubmission = srv.submitBillingToHpm(
		ctx,
		isTest,
		submitPrescription,
		billingSubmission,
		billingDoctorsMap,
		doctorParticipatesMap,
		patientProfilesMap,
		contractIdsMap,
	)
	return submittedBillingSubmission, errorSubmission, unHandledError
}

func (srv *Service) TestSubmitBilling(
	ctx *titan.Context,
	request *billing_service.BillingSubmissionRequest,
) (*billing_service.BillingSubmissionResponse, error) {
	successBillingSubmission, errorBillingSubmission, err := srv.submitBilling(ctx, request, true, request.SubmitPrescription)
	return &billing_service.BillingSubmissionResponse{
		ReferenceId:       request.ReferenceId,
		SuccessSubmission: successBillingSubmission,
		ErrorSubmission:   errorBillingSubmission,
	}, err
}

func (srv *Service) ReSubmitBilling(
	ctx *titan.Context,
	request *billing_service.BillingSubmissionRequest,
) (*billing_service.BillingSubmissionResponse, error) {
	billingSubmission := request.BillingSubmission
	billingDoctorId := billingSubmission.BillingDoctorId
	billingContractId := billingSubmission.ContractId
	billingTimelines := billingSubmission.TimelineModels

	patientIdSet := getBillingPatientIds(billingTimelines)

	doctorIdSet := getBillingDoctorIds(*billingDoctorId, billingTimelines)
	contractIdSet := []*model.ContractId{&billingContractId}

	contractIdsMap := srv.toContractsMap(contractIdSet)
	billingDoctorsMap, err := srv.toBillingDoctorsMap(ctx, doctorIdSet)
	if err != nil {
		return nil, err
	}

	patientProfilesMap, err := srv.toPatientProfilesMap(ctx, patientIdSet)
	if err != nil {
		return nil, err
	}

	doctorParticipatesMap, err := srv.toDoctorParticipatesMap(ctx, doctorIdSet)
	if err != nil {
		return nil, err
	}

	billingSubmission.TimelineModels = billingTimelines

	successBillingSubmission, errorBillingSubmission := srv.submitBillingToHpm(
		ctx,
		false,
		request.SubmitPrescription,
		billingSubmission,
		billingDoctorsMap,
		doctorParticipatesMap,
		patientProfilesMap,
		contractIdsMap,
	)

	if successBillingSubmission != nil {
		err = srv.notifier.NotifyBillingSubmissionResponse(ctx, &billing_service.EventBillingSubmissionResponse{
			ReferenceId:              request.ReferenceId,
			Status:                   billing_service.BillingSubmissionResponseStatus_Ok,
			SuccessBillingSubmission: successBillingSubmission,
		})
		if err != nil {
			ctx.Logger().Warn("FAILED_TO_NOTIFY_BILLING_SUCCESS_SUBMISSION_RESPONSE. Err: " + err.Error())
		}
		if err != nil {
			ctx.Logger().Warn("FAILED_TO_NOTIFY_SIDEBAR. Err: " + err.Error())
		}
		err = srv.notifier.NotifyBillingHistoryChange(ctx, &billing_service.EventBillingHistoryChange{
			Data: &billing_service.BillingHistoriesResponse{
				BillingHistories: []*billing_service.BillingHistoryResponse{
					successBillingSubmission.BillingHistory,
				},
			},
		})
		if err != nil {
			ctx.Logger().Warn("FAILED_TO_NOTIFY_BILLING_HISTORY_CHANGE. Err: " + err.Error())
		}
	}

	if errorBillingSubmission != nil {
		err = srv.notifier.NotifyBillingSubmissionResponse(ctx, &billing_service.EventBillingSubmissionResponse{
			ReferenceId:            request.ReferenceId,
			Status:                 billing_service.BillingSubmissionResponseStatus_Error,
			ErrorBillingSubmission: errorBillingSubmission,
		})
		if err != nil {
			ctx.Logger().Warn("FAILED_TO_NOTIFY_BILLING_ERROR_SUBMISSION_RESPONSE. Err: " + err.Error())
		}
		if err != nil {
			ctx.Logger().Warn("FAILED_TO_NOTIFY_SIDEBAR. Err: " + err.Error())
		}
	}

	return &billing_service.BillingSubmissionResponse{
		ReferenceId:       request.ReferenceId,
		SuccessSubmission: successBillingSubmission,
		ErrorSubmission:   errorBillingSubmission,
	}, nil
}

func getBillingPatientIds(
	billingTimelines []*timeline_common.TimelineModel,
) []uuid.UUID {
	patientIdSet := make(map[uuid.UUID]uuid.UUID)

	for _, billingTimeline := range billingTimelines {
		patientId := billingTimeline.PatientId
		patientIdSet[patientId] = patientId
	}

	patientIds := make([]uuid.UUID, 0)
	for patientId := range patientIdSet {
		tmp := patientId
		patientIds = append(patientIds, tmp)
	}
	return patientIds
}

func getBillingDoctorIds(
	billingDoctorId uuid.UUID,
	billingTimelines []*timeline_common.TimelineModel,
) []*uuid.UUID {
	doctorIdSet := make(map[uuid.UUID]uuid.UUID)

	doctorIdSet[billingDoctorId] = billingDoctorId

	for _, billingTimeline := range billingTimelines {
		doctorId := billingTimeline.TreatmentDoctorId
		doctorIdSet[doctorId] = doctorId
	}

	doctorIds := make([]*uuid.UUID, 0)
	for patientId := range doctorIdSet {
		tmp := patientId
		doctorIds = append(doctorIds, &tmp)
	}

	return doctorIds
}

func (srv *Service) getEffectivePreviousQuarterChronicDiagnoses(
	ctx *titan.Context,
	patientIds []uuid.UUID,
) ([]*timeline_common.TimelineModel, error) {
	permanentDiagnoses, err := srv.timelineService.GetPermanentDiagnoses(ctx, patientIds)
	if err != nil {
		return nil, err
	}

	return slice.Map(permanentDiagnoses, func(
		lt timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline],
	) *timeline_common.TimelineModel {
		tlm := timeline_service.ConvertEntityToModel(lt)
		return &tlm
	}), nil
}

func (srv *Service) GetHpmStatus(ctx *titan.Context) (*billing_service.HpmStatusResponse, error) {
	hpmResponse, err := srv.hpmNextService.CheckHpmConnectivity(ctx)
	if err != nil {
		return nil, err
	}

	response := &billing_service.HpmStatusResponse{
		Status: billing_service.HpmStatus(hpmResponse.Status),
	}

	return response, nil
}

func convertPatientSliceToMap(patients []*billing_service.DiseaseAnalysisPatientRequest) map[uuid.UUID]billing_service.DiseaseAnalysisPatientRequest {
	resultMap := make(map[uuid.UUID]billing_service.DiseaseAnalysisPatientRequest)
	for _, patient := range patients {
		if patient == nil || patient.PatientId == nil {
			continue
		}
		resultMap[*patient.PatientId] = *patient
	}
	return resultMap
}

func (srv *Service) AnalyzeForP4Diseases(
	ctx *titan.Context,
	request *billing_service.P4DiseaseAnalysisRequest,
) (*billing_service.P4DiseaseAnalysisResponse, error) {
	response, err := srv.hpmNextService.AnalyzeForDiseases(
		ctx,
		&hpm_next.DiseaseAnalysisRequest{
			ContractId:        *request.ContractId,
			EvaluationContext: "P4",
			Year:              request.Year,
			Quarter:           request.Quarter,
			Patients:          srv.toDiseaseAnalysisPatients(request.Patients),
		},
	)
	if err != nil {
		return nil, err
	}

	contract := srv.contractService.GetContractDetailById(*request.ContractId)
	if contract == nil {
		return nil, pkg_errors.NewTitanCommonExceptionWithParams(error_code.ErrorCode_Contract_Not_Found, map[string]string{
			"contractId": *request.ContractId,
		})
	}

	patientsMap := convertPatientSliceToMap(request.Patients)

	patients := make([]*billing_service.DiseaseAnalysisPatientResponse, 0)
	for _, p := range response.Patients {
		diseaseGroups := make([]*billing_service.DiseaseGroup, 0)
		for _, g := range p.DiseaseGroups {
			description, err := contract.GetDiseaseDescriptionFromCode(ctx, g, request.Year, request.Quarter)

			if err != nil {
				return nil, err
			}

			diseaseGroups = append(diseaseGroups, &billing_service.DiseaseGroup{
				GroupCode: g,
				GroupName: *description,
			})
		}

		patients = append(patients, &billing_service.DiseaseAnalysisPatientResponse{
			PatientId:           p.PatientId,
			DiseaseGroups:       diseaseGroups,
			SpecialServiceCount: patientsMap[*p.PatientId].SpecialServiceCount,
		})
	}

	return &billing_service.P4DiseaseAnalysisResponse{
		ContractId: request.ContractId,
		Year:       request.Year,
		Quarter:    request.Quarter,
		Patients:   patients,
	}, nil
}

func (*Service) toDiseaseAnalysisPatients(patients []*billing_service.DiseaseAnalysisPatientRequest) []*hpm_next.DiseaseAnalysisPatientRequest {
	patientList := make([]*hpm_next.DiseaseAnalysisPatientRequest, 0)
	for _, p := range patients {
		patientList = append(patientList, &hpm_next.DiseaseAnalysisPatientRequest{
			PatientId:     p.PatientId,
			DiagnoseCodes: p.DiagnoseCodes,
		})
	}
	return patientList
}

// PreRefactor
// Skip this function
func (*Service) submitBillingToHpm(
	_ *titan.Context,
	_ bool,
	_ bool,
	_ *billing_service.BillingSubmission,
	_ map[uuid.UUID]*employee.EmployeeProfile,
	_ map[billing_service_model.DoctorIdContractIdAsKey]*doctor_participate.DoctorParticipate,
	_ map[uuid.UUID]*patient_profile_repo.PatientProfile,
	_ map[model.ContractId]*model.Contract) (
	*billing_service.SuccessBillingSubmission,
	*billing_service.ErrorBillingSubmission,
) {
	panic("wait to remove this one")
}

func (srv *Service) SubmitPreParticipateService(
	ctx *titan.Context,
	request *billing_service.PreParticipateServiceSubmissionRequest,
) (*billing_service.PreParticipateServiceSubmissionResponse, error) {
	hpmResponse, err := srv.submitPreParticipateService(ctx, &request.ContractId, *request.PatientId, request.EncounterDate, request.Code)
	if err != nil {
		return srv.createPreEnrollmentEncounterService(ctx, request, common.UnBilled, []patient_encounter.EncounterItemError{
			{
				Type:        "error",
				Message:     err.Error(),
				ErrorCode:   "PREPARTICIPATE_SERVICE_SUBMISSION_SERVER_ERROR",
				AkaFunction: "ABRG958",
			},
		})
	}

	if *hpmResponse.Status != hpm_rest_hzv_client.ResultatStatusOK {
		encounterItemErrors := srv.toEncounterItemErrors(hpmResponse.Meldungen)
		return srv.createPreEnrollmentEncounterService(ctx, request, common.UnBilled, encounterItemErrors)
	}
	return srv.createPreEnrollmentEncounterService(ctx, request, common.Billed, nil)
}

func (srv *Service) ReSubmitPreParticipateService(
	ctx *titan.Context,
	request *billing_service.PreParticipateServiceReSubmissionRequest,
) (*billing_service.PreParticipateServiceSubmissionResponse, error) {
	timeline, err := srv.timelineService.FindById(ctx, *request.ServiceId)
	if err != nil {
		return nil, err
	}
	timelineData := timeline_service.ConvertEntityToModel(*timeline)
	response, err := srv.submitPreParticipateService(ctx, timelineData.ContractId, timelineData.PatientId, timelineData.SelectedDate, timelineData.EncounterServiceTimeline.Code)
	if err != nil {
		return srv.updatePreEnrollmentEncounterService(ctx, *request.ServiceId, common.UnBilled)
	}

	if *response.Status != hpm_rest_hzv_client.ResultatStatusOK {
		return srv.updatePreEnrollmentEncounterService(ctx, *request.ServiceId, common.UnBilled)
	}
	return srv.updatePreEnrollmentEncounterService(ctx, *request.ServiceId, common.Billed)
}

func (*Service) toEncounterItemErrors(hpmErrors *[]hpm_rest_hzv_client.Meldung) []patient_encounter.EncounterItemError {
	var encounterItemErrors []patient_encounter.EncounterItemError

	if hpmErrors == nil {
		return encounterItemErrors
	}
	for _, hpmErr := range *hpmErrors {
		if hpmErr.Kategorie == nil || hpmErr.Code == nil || hpmErr.Nachricht == nil {
			continue
		}
		refMessages := make([]string, 0)
		refMsg := fmt.Sprintf("{Id: %s, Type: %s}", *hpmErr.Code, *hpmErr.Kategorie)
		refMessages = append(refMessages, refMsg)

		encounterItemErrorMsg := fmt.Sprintf("Message: %s, Refs: %s", *hpmErr.Nachricht, strings.Join(refMessages, ", "))
		encounterItemErrorCode := *hpmErr.Code
		encounterItemErrors = append(encounterItemErrors, patient_encounter.EncounterItemError{
			Type:        "error",
			Message:     encounterItemErrorMsg,
			ErrorCode:   encounterItemErrorCode,
			AkaFunction: "ABRG958",
		})
	}

	return encounterItemErrors
}

func (srv *Service) submitPreParticipateService(
	ctx *titan.Context,
	contractId *string,
	patientId uuid.UUID,
	encounterDate int64,
	serviceCode string,
) (*hpm_rest_client.SubmitPreParticipateServiceResponse, error) {
	contract := srv.contractService.GetContractDetailById(*contractId)
	if contract == nil {
		return nil, pkg_errors.NewTitanCommonExceptionWithParams(error_code.ErrorCode_Contract_Not_Found, map[string]string{
			"contractId": *contractId,
		})
	}

	ppResult, err := srv.patientParticipationService.GetPatientParticipation(ctx, patient_participation_api.GetPatientParticipationRequest{
		PatientId: patientId,
		CheckDate: encounterDate,
	})
	if err != nil {
		return nil, err
	}
	pp := slice.FindOne(ppResult.Participations, func(p patient_participation_api.PatientParticipation) bool {
		return p.Status == patient_participation_api.PatientParticipation_Requested && p.ContractId == *contractId
	})

	doctorId := pp.DoctorId
	doctorsMap, err := srv.toBillingDoctorsMap(ctx, []*uuid.UUID{doctorId})
	if err != nil {
		return nil, err
	}
	billingDoctor := doctorsMap[*doctorId]

	patientProfilesMap, err := srv.toPatientProfilesMap(ctx, []uuid.UUID{patientId})
	if err != nil {
		return nil, err
	}
	patientProfile := patientProfilesMap[patientId]
	patientHealthInsurance := patientProfile.PatientInfo.GetActiveInsurance()
	if patientHealthInsurance == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_NotActive_InsuranceInfo, "")
	}

	hpmTestMode, err := srv.featureFlagService.IsFeatureEnabled(ctx, feature_flag_common.FeatureFlagKey_HPM_TESTMODE)
	if err != nil {
		return nil, err
	}

	quarter, year := pkg_utils.GetCurrentQuarter(pkg_utils.ConvertMillisecondsToTime(encounterDate))
	serviceId := pkg_utils.NewUUID()
	payload := hpm_rest_hzv_client.PostApiAbrechnungenVoreinschreibeleistungenJSONRequestBody{
		AbsenderBsnr:      billingDoctor.Bsnr,
		NurPrueflauf:      false,
		Testuebermittlung: hpmTestMode,
		ArztInformationsSystem: hpm_rest_hzv_client.SoftwareInformation{
			Name:         &srv.softwareInformation.Name,
			Organisation: &srv.softwareInformation.Organisation,
			SystemOid:    srv.softwareInformation.SystemOid,
			Version:      srv.softwareInformation.Version,
		},
		Patient: hpm_rest_hzv_client.Patient{
			// AktuelleVertragsteilnahmen: &[]hpm_rest_hzv_client.Vertragsteilnahme{
			// 	{IstVertreterteilnahme: false, VertragsIdentifikator: contract.GetContractType().ID},
			// },
			AktuelleVertragsteilnahmen: nil,
			Geburtsdatum: function.Do(func() string {
				// NOTE: -1 means 00.00.0000 on UI, according PRO-12072, we send 1900-01-01
				if patientProfile.PatientInfo.PersonalInfo.DOB == -1 {
					return "1900-01-01"
				}
				return pkg_utils.ConvertMillisecondsToTime(patientProfile.PatientInfo.PersonalInfo.DOB).Format(pkg_utils.YYYY_MM_DD)
			}),
			Geschlecht:  hpm_rest_hzv_client.PatientGeschlecht(patientProfile.PatientInfo.PersonalInfo.Gender),
			Nachname:    patientProfile.PatientInfo.PersonalInfo.LastName,
			PatientenId: patientProfile.Id.String(),
			Versicherungsnachweis: hpm_rest_hzv_client.Versicherungsnachweis{
				KrankenkassenIk: patientHealthInsurance.GetIkNumberString(),
				VersichertenNummer: function.Do(func() string {
					if patientHealthInsurance.InsuranceNumber != nil {
						return *patientHealthInsurance.InsuranceNumber
					}
					return ""
				}),
				VersichertenArt:         "1",
				BesonderePersonengruppe: pkg_utils.NewString(string(patientHealthInsurance.SpecialGroup)),
				DmpKennzeichnung:        pkg_utils.NewString(patientHealthInsurance.DMPLabeling),
			},
			Vorname: patientProfile.PatientInfo.PersonalInfo.FirstName,
		},
		Vertragskontext: hpm_rest_hzv_client.Vertragskontext{
			AbrechnungsJahr:            int32(year),
			AbrechnungsQuartal:         int32(quarter),
			HonoraranlageIdentifikator: pp.ChargeSystemId,
			VertragsIdentifikator:      contract.Id,
		},
		Vertragspartneridentifikator: function.Do(func() string {
			contractType := contract.GetContractType().Type()
			if contractType == nil {
				return ""
			}
			if *contractType == model.ContractType_HouseDoctorCare {
				return *billingDoctor.HavgVpId
			} else if *contractType == model.ContractType_SpecialistCare {
				return *billingDoctor.MediverbundVpId
			}
			return ""
		}),
		VorEinschreibeLeistung: hpm_rest_hzv_client.VorEinschreibeLeistung{
			LeistungsId:     serviceId.String(),
			Leistungsdatum:  pkg_utils.NewPointer(pkg_utils.ConvertMillisecondsToTime(encounterDate)),
			Leistungsziffer: serviceCode,
		},
	}
	return srv.hpmRest.SubmitPreParticipateService(ctx, payload)
}

func (srv *Service) createPreEnrollmentEncounterService(
	ctx *titan.Context,
	request *billing_service.PreParticipateServiceSubmissionRequest,
	billingStatus common.BillingStatus,
	encounterItemErrors []patient_encounter.EncounterItemError,
) (*billing_service.PreParticipateServiceSubmissionResponse, error) {
	isSubmitPreParticipateSucsess := false
	statusSubmitUhu35 := patient_overview_domain.SubmitUhu35Status_Failed
	billingInfor := common.BillingInfo{
		BillingStatus:     &billingStatus,
		SubmittedDate:     pkg_utils.NewPointer(pkg_utils.NowUnixMillis(ctx)),
		LastUpdatedUserId: ctx.UserInfo().UserUUID(),
	}
	if billingStatus == common.Billed {
		isSubmitPreParticipateSucsess = true
		statusSubmitUhu35 = patient_overview_domain.SubmitUhu35Status_Success
	}
	dt := timeline_repo.TimelineEntity[any]{
		ContractId:        &request.ContractId,
		TreatmentDoctorId: *request.BillingDoctorId,
		BillingDoctorId:   request.BillingDoctorId,
		PatientId:         *request.PatientId,
		EncounterCase:     pkg_utils.NewPointer(patient_encounter.PRE_ENROLLMENT),
		TreatmentCase:     pkg_utils.NewPointer(patient_encounter.TreatmentCasePreParticipate),
		ScheinIds: function.Do(func() []uuid.UUID {
			if request.Schein == nil {
				return []uuid.UUID{}
			}
			if request.Schein.ScheinId == nil {
				return []uuid.UUID{}
			}
			return []uuid.UUID{*request.Schein.ScheinId}
		}),
		Payload: patient_encounter.EncounterServiceTimeline{
			Code:             request.Code,
			Description:      request.Description,
			FreeText:         request.FreeText,
			Command:          request.Command,
			PatientId:        request.PatientId,
			Sources:          (*patient_encounter.Sources)(request.Sources),
			IsPreParticipate: true,
			Scheins:          pkg_utils.NewPointer([]*common.ScheinWithMainGroup{request.Schein}),
			ServiceMainGroup: pkg_utils.NewPointer(request.Schein.Group),
			ChargeSystemId:   new(string),
			PreParticipateType: function.Do(func() *patient_encounter.PreParticipateType {
				if request.Code == string(patient_encounter.UHU35) {
					return pkg_utils.NewPointer(patient_encounter.UHU35)
				}
				if request.Code == string(patient_encounter.KJP4a) {
					return pkg_utils.NewPointer(patient_encounter.KJP4a)
				}
				return nil
			}),
			IsSubmitPreParticipateSucsess: pkg_utils.NewBool(isSubmitPreParticipateSucsess),
			ParticipateId:                 request.ParticipateId,
			Errors:                        pkg_utils.NewPointer(slice.Map(encounterItemErrors, func(e patient_encounter.EncounterItemError) *patient_encounter.EncounterItemError { return &e })),
		},
		Errors:      encounterItemErrors,
		BillingInfo: &billingInfor,
	}
	_, err := srv.timelineService.Create(ctx, dt)
	if err != nil {
		return nil, err
	}

	if request.Code == string(patient_encounter.UHU35) {
		err := srv.patientOverview.UpdatePatientOverviewUHU35(ctx, *request.PatientId, request.ContractId, statusSubmitUhu35)
		if err != nil {
			return nil, err
		}
	}

	return &billing_service.PreParticipateServiceSubmissionResponse{
		Status: billing_service.PreParticipateServiceSubmissionStatusOk,
	}, nil
}

func (srv *Service) updatePreEnrollmentEncounterService(
	ctx *titan.Context,
	serviceId uuid.UUID,
	billingStatus common.BillingStatus,
) (*billing_service.PreParticipateServiceSubmissionResponse, error) {
	_, err := srv.timelineService.UpdateByIdsWithCallback(ctx, []uuid.UUID{serviceId}, func(timelineEntities []timeline_repo.TimelineEntity[any]) []timeline_repo.TimelineEntity[any] {
		return slice.Map(timelineEntities, func(t timeline_repo.TimelineEntity[any]) timeline_repo.TimelineEntity[any] {
			t.BillingInfo = &common.BillingInfo{
				BillingStatus:     pkg_utils.NewPointer(billingStatus),
				SubmittedDate:     pkg_utils.NewPointer(pkg_utils.ConvertTimeToMiliSecond(pkg_utils.Now(ctx))),
				LastUpdatedUserId: ctx.UserInfo().UserUUID(),
			}
			return t
		})
	})
	if err != nil {
		return nil, err
	}
	rs := billing_service.PreParticipateServiceSubmissionResponse{
		Status: billing_service.PreParticipateServiceSubmissionStatusOk,
	}
	return &rs, nil
}

func (srv *Service) toDoctorParticipatesMap(
	ctx *titan.Context,
	doctorIds []*uuid.UUID,
) (map[billing_service_model.DoctorIdContractIdAsKey]*doctor_participate.DoctorParticipate, error) {
	doctorParticipateList, err := srv.repo.doctorParticipateRepo.Find(ctx, bson.M{
		doctor_participate.Field_DoctorId: bson.M{
			"$in": doctorIds,
		},
	})

	if err != nil {
		return nil, err
	}

	doctorParticipateDoctorIdsMap := make(map[billing_service_model.DoctorIdContractIdAsKey]*doctor_participate.DoctorParticipate)
	for _, p := range doctorParticipateList {
		tmp := p
		key := billing_service_model.DoctorIdContractIdAsKey{
			DoctorId:   *p.DoctorId,
			ContractId: *p.ContractId,
		}

		doctorParticipateDoctorIdsMap[key] = &tmp
	}

	return doctorParticipateDoctorIdsMap, nil
}

func (srv *Service) toPatientProfilesMap(
	ctx *titan.Context,
	patientIds []uuid.UUID,
) (map[uuid.UUID]*patient_profile_repo.PatientProfile, error) {
	patientProfiles, err := srv.repo.patientProfileRepo.Find(ctx, bson.M{
		patient_profile_repo.Field_Id: bson.M{
			"$in": patientIds,
		},
	})

	if err != nil {
		return nil, err
	}

	patientProfilesMap := make(map[uuid.UUID]*patient_profile_repo.PatientProfile)
	for _, pp := range patientProfiles {
		tmp := pp
		patientProfilesMap[*pp.Id] = tmp
	}

	return patientProfilesMap, nil
}

func (srv *Service) toContractsMap(
	contractIds []*string,
) map[model.ContractId]*model.Contract {
	contractIdsMap := make(map[model.ContractId]*model.Contract)
	for _, contractId := range contractIds {
		contract := srv.contractService.GetContractDetailById(*contractId)
		contractIdsMap[*contractId] = contract
	}
	return contractIdsMap
}

func (srv *Service) toBillingDoctorsMap(
	ctx *titan.Context,
	doctorIds []*uuid.UUID,
) (map[uuid.UUID]*employee.EmployeeProfile, error) {
	// doctor profile
	doctorHashedIds := make([]uuid.UUID, 0)
	doctorMapHashedIds := make(map[uuid.UUID]*uuid.UUID)
	for _, id := range doctorIds {
		if id != nil {
			mapId := uuid.MustParse(id.String())
			doctorMapHashedIds[*id] = &mapId
			doctorHashedIds = append(doctorHashedIds, *id)
		}
	}

	doctorProfileResponses, err := srv.repo.employeeProfileRepo.Find(ctx, bson.M{
		employee.Field_Id: bson.M{
			"$in": doctorHashedIds,
		},
	})

	if err != nil {
		return nil, err
	}

	billingDoctorsMap := make(map[uuid.UUID]*employee.EmployeeProfile)
	for _, dp := range doctorProfileResponses {
		tmp := dp
		originalDoctorId := doctorMapHashedIds[*dp.Id]
		billingDoctorsMap[*originalDoctorId] = &tmp
	}

	return billingDoctorsMap, nil
}

func (srv *Service) uploadPdf(
	ctx *titan.Context,
	pdf []byte,
	objectName string,
) (*billing_service_repo.FileInfo, error) {
	if len(pdf) == 0 {
		return nil, errors.New("data length is 0")
	}
	password := uuid.New().String()

	salt := make([]byte, 32)
	_, err := io.ReadFull(rand.Reader, salt)
	if err != nil {
		return nil, errors.WithMessage(err, "Error on create form salt")
	}
	encryption := encrypt.DefaultPBKDF([]byte(password), salt)
	_, err = srv.minioClient.PutObject(
		ctx,
		srv.bucketName,
		objectName,
		bytes.NewReader(pdf),
		int64(len(pdf)),
		pkg_minio.PutObjectOptions{ServerSideEncryption: encryption},
	)
	if err != nil {
		return nil, errors.WithMessage(err, "Error on sending pdf file to Minio")
	}
	return &billing_service_repo.FileInfo{
		CreatedDate: util.NowUnixMillis(nil),
		BucketName:  srv.bucketName,
		ObjectName:  objectName,
		Password:    password,
		Salt:        salt,
	}, nil
}

func (srv *Service) PreConditionSvBilling(ctx *titan.Context) (*billing_service.PreConditionSvBillingResponse, error) {
	quarterYears := pkg_utils.GetCurrentQuarters(pkg_utils.Now(ctx), 7)
	queryQuarterYears := bson.A{}
	for _, q := range quarterYears {
		queryQuarterYears = append(queryQuarterYears, bson.M{
			timeline_repo.Field_Quarter: q[0],
			timeline_repo.Field_Year:    q[1],
			timeline_repo.Field_contractId: bson.M{
				"$exists": true,
			},
			"$or": []bson.M{
				{
					timeline_repo.Field_timelineEntityType: timeline_common.TimelineEntityType_MedicinePrescription,
				},
				{
					timeline_repo.Field_IsDeleted: false,
					timeline_repo.Field_timelineEntityType: bson.M{
						"$in": []timeline_common.TimelineEntityType{
							timeline_common.TimelineEntityType_Diagnose,
							timeline_common.TimelineEntityType_Service,
						},
					},
				},
			},
		})
	}
	query := bson.M{
		"$or": queryQuarterYears,
	}
	queriedTimelines, err := srv.timelineService.Find(ctx, query)
	if err != nil {
		return nil, err
	}
	scheinIds := []uuid.UUID{}
	for _, t := range queriedTimelines {
		for _, s := range t.ScheinIds {
			if !slice.Contains(scheinIds, s) {
				scheinIds = append(scheinIds, s)
			}
		}
	}
	scheins, err := srv.scheinRepo.FindByIds(ctx, scheinIds)
	if err != nil {
		return nil, err
	}
	timelines := ReFilterPreCheckConditionSvBilling(queriedTimelines, slice.Map(scheins, func(s schein.ScheinRepo) schein_common.GetScheinDetailByIdResponse {
		return s.ToScheinDetail()
	}))
	result := billing_service.PreConditionSvBillingResponse{}
	nestedYear := map[int32]*billing_service.Quarters{}
	doctors, err := srv.repo.employeeProfileRepo.GetByIds(ctx, slice.Map(timelines, func(t timeline_repo.TimelineEntity[any]) uuid.UUID { return t.TreatmentDoctorId }))
	if err != nil {
		return nil, err
	}
	for _, timeline := range timelines {
		if timeline.ContractId != nil {
			result.ContractIds = append(result.ContractIds, *timeline.ContractId)
		}
		schein := slice.FindOne(scheins, func(s schein.ScheinRepo) bool {
			return s.Id != nil && slice.Contains(timeline.ScheinIds, *s.Id)
		})
		if schein == nil {
			continue
		}
		result.DoctorIds = append(result.DoctorIds, schein.DoctorId)
		if timeline.ContractId != nil {
			result.YearQuarters = append(result.YearQuarters, &billing_common.YearQuarter{
				Year:    int32(timeline.Year),
				Quarter: int32(timeline.Quarter),
			})
			_, ok := nestedYear[int32(timeline.Year)]
			if !ok {
				nestedYear[int32(timeline.Year)] = &billing_service.Quarters{
					Quarter: make(map[int32]*billing_service.DoctorIds),
				}
			}
			_, ok = nestedYear[int32(timeline.Year)].Quarter[int32(timeline.Quarter)]
			if !ok {
				nestedYear[int32(timeline.Year)].Quarter[int32(timeline.Quarter)] = &billing_service.DoctorIds{
					DoctorId: make(map[string]*billing_service.ContractIds),
				}
			}
			doctor := slice.FindOne(doctors, func(d employee.EmployeeProfile) bool {
				return d.Id.String() == timeline.TreatmentDoctorId.String()
			})
			_, ok = nestedYear[int32(timeline.Year)].Quarter[int32(timeline.Quarter)].DoctorId[timeline.TreatmentDoctorId.String()]
			if doctor != nil && !doctor.MarkAsBillingDoctor {
				continue
			}
			if !ok {
				nestedYear[int32(timeline.Year)].Quarter[int32(timeline.Quarter)].DoctorId[timeline.TreatmentDoctorId.String()] = &billing_service.ContractIds{
					ContractIds: []string{},
				}
			}
			nestedYear[int32(timeline.Year)].Quarter[int32(timeline.Quarter)].DoctorId[timeline.TreatmentDoctorId.String()].ContractIds = slice.Uniq(append(nestedYear[int32(timeline.Year)].Quarter[int32(timeline.Quarter)].DoctorId[timeline.TreatmentDoctorId.String()].ContractIds, *timeline.ContractId))
		}
	}

	result.ContractIds = slice.Uniq(result.ContractIds)
	result.DoctorIds = slice.UniqBy(result.DoctorIds, func(t uuid.UUID) string { return t.String() })
	result.YearQuarters = slice.UniqBy(result.YearQuarters, func(t *billing_common.YearQuarter) string { return fmt.Sprintf("%d.%d", t.Quarter, t.Year) })
	sort.Slice(result.YearQuarters, func(i, j int) bool {
		isSameYear := result.YearQuarters[i].Year == result.YearQuarters[j].Year
		isGreaterYear := result.YearQuarters[i].Year > result.YearQuarters[j].Year
		isGreaterQuarter := result.YearQuarters[i].Quarter > result.YearQuarters[j].Quarter
		return (isSameYear && isGreaterQuarter) || isGreaterYear
	})
	result.NestedYear = nestedYear
	return &result, nil
}

func (srv *Service) CalculateBillingSummary(ctx *titan.Context, request *billing_service.SubmitBillingToHpmRequest) (*billing_service.CalculateBillingSummaryResponse, error) {
	isOffline, err := srv.isOfflineSubmissionBilling(ctx)
	if err != nil {
		return nil, errors.WithMessage(err, "Error on get submission billing")
	}
	billingBuilder := newBillingBuilder(&billingRequest{
		DoctorIds:             request.DoctorIds,
		ContractIds:           request.ContractIds,
		Times:                 request.Times,
		TestRun:               request.TestRun,
		IgnoreBilling:         request.IgnoreBilling,
		ReferenceId:           request.ReferenceId,
		IsOffline:             isOffline,
		IsPrescriptionOnly:    request.IsPrescriptionOnly,
		IgnoreDocumentBilling: request.IgnoreDocumentBilling,
		IsGetErrorTimeline:    request.IsGetErrorTimeline,
	}, srv).prepare(ctx).
		fetchScheins(ctx).
		fetchTimeline(ctx).
		addPseudoServiceTimeline(ctx).
		reFilterTimeline().
		fetchProfiles(ctx).
		filterMedicinePrescription()

	doctors := billingBuilder.GetDoctors()
	scheins := slice.Map(billingBuilder.scheins, func(s schein.ScheinRepo) schein_common.GetScheinDetailByIdResponse {
		return s.ToScheinDetail()
	})
	mappedDoctor := map[uuid.UUID]employee.EmployeeProfile{}
	for _, d := range doctors {
		mappedDoctor[*d.Id] = d
	}
	timelines := []timeline_repo.TimelineEntity[any]{}
	for _, t := range billingBuilder.timelines {
		timelines = append(timelines, timeline_service.ConvertModelToEntity[any](t))
	}

	result := map[string]*billing_service.MapGroupCalculateByDoctor{}
	mappedPatientWithEntry := map[uuid.UUID][]timeline_repo.TimelineEntity[any]{}
	contractIdsMap := srv.toContractsMap(slice.Map(request.ContractIds, func(t string) *string { return &t }))
	for _, t := range request.Times {
		filteredScheins := slice.Filter(scheins, func(s schein_common.GetScheinDetailByIdResponse) bool {
			// yearQuarter := s.GetYearQuarter()
			return s.G4101Year != nil && s.G4101Quarter != nil && *s.G4101Year == t.Year && *s.G4101Quarter == t.Quarter
		})
		if len(filteredScheins) == 0 {
			continue
		}
		entries := ReFilterPreCheckConditionSvBilling(timelines, filteredScheins)
		if len(entries) == 0 {
			continue
		}
		for _, e := range entries {
			_, ok := mappedPatientWithEntry[e.PatientId]
			if !ok {
				mappedPatientWithEntry[e.PatientId] = []timeline_repo.TimelineEntity[any]{}
			}
			mappedPatientWithEntry[e.PatientId] = append(mappedPatientWithEntry[e.PatientId], e)
		}
		for _, d := range request.DoctorIds {
			_, ok := result[d.String()]
			if !ok {
				result[d.String()] = &billing_service.MapGroupCalculateByDoctor{}
			}

			// Mapping ContractContext and DoctorContract
			doctor, ok := mappedDoctor[d]
			if !ok {
				continue
			}
			doctorId := *doctor.Id
			for _, c := range request.ContractIds {
				filterdEntries := slice.Filter(entries, func(i timeline_repo.TimelineEntity[any]) bool {
					if i.Type == timeline_common.TimelineEntityType_MedicinePrescription {
						timelineModel := timeline_service.ConvertEntityToModel(i)

						return timelineModel.EncounterMedicinePrescription != nil && len(timelineModel.EncounterMedicinePrescription.FormInfos) > 0 &&
							timelineModel.EncounterMedicinePrescription.FormInfos[0] != nil &&
							timelineModel.EncounterMedicinePrescription.FormInfos[0].PrintDate != nil &&
							*timelineModel.ContractId == c &&
							i.TreatmentDoctorId.String() == doctorId.String() && i.ContractId != nil
					}

					return i.TreatmentDoctorId.String() == doctorId.String() && i.ContractId != nil && *i.ContractId == c
				})
				patientIds := []uuid.UUID{}
				countDiagnose := []uuid.UUID{}
				countService := []uuid.UUID{}
				countPrescription := []uuid.UUID{}
				encounterIds := []uuid.UUID{}
				for _, f := range filterdEntries {
					if f.Id != nil {
						encounterIds = append(encounterIds, *f.Id)
						foundPatientId := slice.FindOne(patientIds, func(t uuid.UUID) bool {
							return t.String() == f.PatientId.String()
						})
						if foundPatientId == nil {
							patientIds = append(patientIds, f.PatientId)
						}
						if f.Type == timeline_common.TimelineEntityType_Diagnose {
							countDiagnose = append(countDiagnose, *f.Id)
						} else if f.Type == timeline_common.TimelineEntityType_Service {
							countService = append(countService, *f.Id)
						} else if f.Type == timeline_common.TimelineEntityType_MedicinePrescription {
							countPrescription = append(countPrescription, *f.Id)
						}
					}
				}
				contract, ok := contractIdsMap[c]
				countPreventiveCase := 0
				for _, p := range patientIds {
					timelineEntries := slice.Filter(filterdEntries, func(t timeline_repo.TimelineEntity[any]) bool {
						return t.PatientId.String() == p.String() && t.Type == timeline_common.TimelineEntityType_Service
					})
					if ok && len(timelineEntries) > 0 {
						if checkPreventiveCase(ctx, slice.Map(timelineEntries, func(t timeline_repo.TimelineEntity[any]) timeline_common.TimelineModel {
							return timeline_service.ConvertEntityAnyToModel(t)
						}), contract) {
							countPreventiveCase += 1
						}
					}
				}
				if !(len(patientIds) == 0 && len(filterdEntries) == 0 && len(countDiagnose) == 0 && len(countService) == 0 && len(countPrescription) == 0) {
					patients := []*billing_service.DiseaseAnalysisPatientRequest{}
					for _, p := range patientIds {
						patients = append(patients, &billing_service.DiseaseAnalysisPatientRequest{
							PatientId: &p,
							DiagnoseCodes: slice.Map(slice.Filter(billingBuilder.rawTimelines, func(t timeline_common.TimelineModel) bool {
								if t.EncounterDiagnoseTimeline == nil || t.PatientId != p {
									return false
								}
								return t.EncounterDiagnoseTimeline.Certainty != nil && *t.EncounterDiagnoseTimeline.Certainty == patient_encounter.G
							}), func(t timeline_common.TimelineModel) string {
								return t.EncounterDiagnoseTimeline.Code
							}),
							SpecialServiceCount: function.Do(func() *int64 {
								services := slice.Filter(billingBuilder.rawTimelines, func(t timeline_common.TimelineModel) bool {
									if t.EncounterServiceTimeline == nil || t.PatientId != p {
										return false
									}
									return t.EncounterServiceTimeline.Code == "56544"
								})
								length := int64(len(services))
								return &length
							}),
						})
					}
					resultP4, err := srv.AnalyzeForP4Diseases(ctx, &billing_service.P4DiseaseAnalysisRequest{
						ContractId: &c,
						Year:       t.Year,
						Quarter:    t.Quarter,
						Patients:   patients,
					})
					if err != nil {
						continue
					}
					resultP4.Patients = slice.Filter(resultP4.Patients, func(p *billing_service.DiseaseAnalysisPatientResponse) bool {
						return len(p.DiseaseGroups) >= 3
					})
					mainGroup := common.MainGroup("")
					if contract.IsHzvContract() {
						mainGroup = common.HZV
					} else if contract.IsFavContract() {
						mainGroup = common.FAV
					}
					result[d.String()].GroupCalculateByDoctors = append(result[d.String()].GroupCalculateByDoctors, &billing_service.GroupCalculateByDoctor{
						YearQuarter: &billing_common.YearQuarter{
							Year:    t.Year,
							Quarter: t.Quarter,
						},
						MainGroup:           mainGroup,
						ContractId:          c,
						BilledPatients:      patientIds,
						Encounters:          encounterIds,
						Diagnoses:           countDiagnose,
						Services:            countService,
						Prescriptions:       countPrescription,
						P4DiseaseAnalysis:   resultP4,
						CountPreventiveCase: int32(countPreventiveCase),
					})
				}
			}
		}
	}
	newResult := map[string]*billing_service.MapGroupCalculateByDoctor{}
	for k, v := range result {
		if v != nil && len(v.GroupCalculateByDoctors) > 0 {
			newResult[k] = v
		}
	}
	return &billing_service.CalculateBillingSummaryResponse{
		Response: newResult,
	}, nil
}

type submitToHPMResponse struct {
	billingSuccess                 []uuid.UUID
	billingFail                    []uuid.UUID
	billingHistories               []billing_service_repo.BillingHistory
	payloadTrackingDocuments       []string
	payloadTrackingPrescriptions   []string
	medicationPrescriptionResponse []*hpm_next.StarteVerordnungsdatenUebermittlungV2Response
	submitionResponse              []*hpm_next.StarteAbrechnungV2Response
	rawResponses                   []hpm_next.RawResponse
	offlineBillingDataResponse     []*hpm_rest_hzv_client.DatentraegerTyp
}

func medicineIdFinder(medicinePrescriptionsTimelines []timeline_repo.TimelineEntity[patient_encounter.EncounterMedicinePrescription]) func(medicineId, formId *uuid.UUID) *uuid.UUID {
	return func(medicineId, formId *uuid.UUID) *uuid.UUID {
		for _, e := range medicinePrescriptionsTimelines {
			for _, f := range e.Payload.FormInfos {
				if formId != nil && formId.String() == f.Id.String() {
					return e.Id
				}
				for _, m := range f.Medicines {
					if medicineId != nil && medicineId.String() == m.Id.String() {
						return e.Id
					}
				}
			}
		}
		return nil
	}
}

func (srv *Service) isOfflineSubmissionBilling(ctx *titan.Context) (bool, error) {
	billingSubmissionKey := string(settings.SelectiveContracts_BillingSubmission)
	setting, err := srv.getSettingFn(ctx, settings.SettingsRequest{
		Feature:  setting_common.SettingsFeatures_SelectiveContacts,
		Settings: []string{billingSubmissionKey},
		Signal:   nil,
	})
	if err != nil {
		return false, errors.WithMessage(err, "Error on get setting")
	}
	value := setting.Settings[billingSubmissionKey]
	return value == string(setting_common.EnrollmentType_OFFLINE), nil
}

func (srv *Service) SubmitBillingToHpm(ctx *titan.Context, request *billing_service.SubmitBillingToHpmRequest) (*billing_service.SubmitBillingToHpmResponse, error) {
	isOffline, err := srv.isOfflineSubmissionBilling(ctx)
	if err != nil {
		return nil, errors.WithMessage(err, "Error on get submission billing")
	}

	if len(request.Times) == 0 || len(request.DoctorIds) == 0 || len(request.ContractIds) == 0 {
		return nil, errors.WithStack(errors.New("Precondition isn't enough"))
	}

	result := &billing_service.SubmitBillingToHpmResponse{
		PayloadTrackingDocuments:     []string{},
		PayloadTrackingPrescriptions: []string{},
	}
	billingBuilder := newBillingBuilder(&billingRequest{
		DoctorIds:             request.DoctorIds,
		ContractIds:           request.ContractIds,
		Times:                 request.Times,
		TestRun:               request.TestRun,
		IgnoreBilling:         request.IgnoreBilling,
		ReferenceId:           request.ReferenceId,
		IsOffline:             isOffline,
		IsPrescriptionOnly:    request.IsPrescriptionOnly,
		IgnoreDocumentBilling: request.IgnoreDocumentBilling,
		IsGetErrorTimeline:    request.IsGetErrorTimeline,
	}, srv)
	submissionRequests, medicationPrescriptionRequests, err := billingBuilder.
		prepare(ctx).
		fetchScheins(ctx).
		fetchTimeline(ctx).
		addPseudoServiceTimeline(ctx).
		reFilterTimeline().
		fetchProfiles(ctx).
		filterMedicinePrescription().
		buildPrepairePayload(ctx).
		build(ctx)
	if err != nil {
		return nil, errors.WithMessage(err, "Error on submit to HPM")
	}
	if submissionRequests == nil && medicationPrescriptionRequests == nil {
		return nil, errors.New("Error on build payload to prepair")
	}
	if request.IsPrescriptionOnly {
		submissionRequests = nil
	}

	resp, err := srv.submit(
		ctx,
		submissionRequests,
		medicationPrescriptionRequests,
		function.Do(func() uuid.UUID {
			if request.ReferenceId == nil {
				return uuid.New()
			}
			return *request.ReferenceId
		}),
		request.TestRun,
	)
	result.PayloadTrackingDocuments = resp.payloadTrackingDocuments
	result.PayloadTrackingPrescriptions = resp.payloadTrackingPrescriptions
	if err != nil {
		return result, errors.WithMessage(err, "Error on submit to HPM")
	}
	patients := billingBuilder.GetPatients()
	doctors := billingBuilder.GetDoctors()
	errorAndWarningTimelines := billingBuilder.GetErrorAndWarningTimelines()
	noErrorDiagnosis := billingBuilder.GetNoErrorDiagnosis()
	errorTimelineIds := billingBuilder.GetErrorTimelineIdsByType(patient_encounter.EncounterItemErrorType_error)
	warningTimelineIds := billingBuilder.GetErrorTimelineIdsByType(patient_encounter.EncounterItemErrorType_warning)
	noErrorDiagnoseIds := billingBuilder.GetNoErrorDiagnoseIds()
	skipErrors, err := srv.GetSkipErrorList(ctx)
	if err != nil {
		return result, errors.WithMessage(err, "Error on get skip error list")
	}
	resultSubmit, err := srv.CalculateBillingSummary(ctx, request)
	if err != nil {
		return result, errors.WithMessage(err, "Error to calculate result submit")
	}
	result.SuccessEntryId = resp.billingSuccess
	result.FailureEntryId = append(resp.billingFail, errorTimelineIds...)
	result.WarningEntryId = warningTimelineIds
	result.NoErrorDiagnoseId = noErrorDiagnoseIds
	result.GroupErrors = GroupError(resp.rawResponses, patients, doctors, errorAndWarningTimelines, noErrorDiagnosis, skipErrors)
	result.ResultSubmit = resultSubmit
	result.IsHasErrors = len(slice.Filter(resp.rawResponses, func(r hpm_next.RawResponse) bool {
		return r.ErrorType == hpm_next.Error
	})) > 0
	if !request.TestRun {
		if !request.IsPrescriptionOnly {
			err = billingBuilder.CreatePseudoServiceTimeline(ctx)
			if err != nil {
				return result, errors.WithMessage(err, "Error on create pseudo service timeline")
			}
		}
		isSubmitOnlyPrescription := true
		if len(resp.billingHistories) > 0 {
			if _, err := srv.repo.billingHistoryRepo.CreateMany(ctx, resp.billingHistories); err != nil {
				return result, errors.WithMessage(err, "Error on create billing history")
			}
		}
		timelineIds := []uuid.UUID{}
		timelineIds = append(timelineIds, resp.billingSuccess...)
		timelineIds = append(timelineIds, resp.billingFail...)
		timelines, err := srv.timelineService.FindByIdsNotFilterDelete(ctx, timelineIds)
		if err != nil {
			return result, errors.WithMessage(err, "Error on get timelines")
		}
		timelines = slice.Filter(timelines, func(t timeline_repo.TimelineEntity[any]) bool {
			if t.IsDeleted && t.Type == timeline_common.TimelineEntityType_MedicinePrescription {
				return true
			}
			if t.IsDeleted {
				return false
			}
			return true
		})
		timelineSuccess := []timeline_repo.TimelineEntity[any]{}
		timelineFailure := []timeline_repo.TimelineEntity[any]{}
		for _, t := range timelines {
			if t.Id == nil {
				continue
			}
			if slice.Contains(resp.billingSuccess, *t.Id) {
				if t.Type == timeline_common.TimelineEntityType_MedicinePrescription {
					if t.BillingInfo != nil && *t.BillingInfo.BillingStatus == common.BilledPrescription {
						if _, err := srv.timelineRepo.UpdateBillingInforForMedicinePrescription(ctx, *t.Id, true, nil); err != nil {
							return result, errors.WithMessage(err, "Error on update billing info for medicine prescription")
						}
					}
					if t.BillingInfo == nil {
						t.BillingInfo = &common.BillingInfo{
							BillingStatus:     pkg_utils.NewPointer(common.BilledPrescription),
							SubmittedDate:     pkg_utils.NewPointer(pkg_utils.ConvertTimeToMiliSecond(pkg_utils.Now(ctx))),
							LastUpdatedUserId: ctx.UserInfo().UserUUID(),
						}
						if _, err := srv.timelineRepo.UpdateBillingInforForMedicinePrescription(ctx, *t.Id, false, t.BillingInfo); err != nil {
							return result, errors.WithMessage(err, "Error on update billing info for medicine prescription")
						}
					}
				}
				timelineSuccess = append(timelineSuccess, t)
			}
			if slice.Contains(resp.billingFail, *t.Id) {
				timelineFailure = append(timelineFailure, t)
			}
		}

		successScheinIds := []uuid.UUID{}
		failureScheinIds := []uuid.UUID{}
		for _, t := range timelineFailure {
			failureScheinIds = append(failureScheinIds, t.ScheinIds...)
		}

		for _, t := range timelineSuccess {
			if t.Type != timeline_common.TimelineEntityType_MedicinePrescription {
				isSubmitOnlyPrescription = false
			}
			for _, s := range t.ScheinIds {
				if slice.Contains(failureScheinIds, s) {
					continue
				}
				successScheinIds = append(successScheinIds, t.ScheinIds...)
			}
		}
		markBilledScheinIds := slice.Filter(successScheinIds, func(s uuid.UUID) bool {
			return !slice.Contains(billingBuilder.GetPrescriptionOnlyScheins(), s)
		})

		if len(markBilledScheinIds) > 0 && !request.IsPrescriptionOnly && !isSubmitOnlyPrescription {
			if err := srv.scheinRepo.MarkBilledByIds(ctx, markBilledScheinIds); err != nil {
				return result, errors.WithMessage(err, "Error on update billed success")
			}
			if _, err := srv.scheinService.CreateSvScheinAutomaticly(ctx, schein_common.CreateSvScheinAutomaticlyRequest{
				ReferenceScheinIds: markBilledScheinIds,
				SelectedDate:       util.NowUnixMillis(ctx),
			}); err != nil {
				return result, errors.WithMessage(err, "Error on create sv schein automaticly")
			}
		}
		if len(failureScheinIds) > 0 && !request.IsPrescriptionOnly {
			if err := srv.scheinRepo.MarkBilledFailByIds(ctx, failureScheinIds); err != nil {
				return result, errors.WithMessage(err, "Error on update billed failure")
			}
		}
		if isOffline && len(submissionRequests) > 0 {
			// Currently, we support only 1 BSNR
			isoFileName := getCDImageName(submissionRequests[0].HpmRequest.AbsenderBsnr)
			isoFileBase64, err := buildBase64CDImage(isoFileName, resp.offlineBillingDataResponse)
			if err != nil {
				return result, errors.WithMessage(err, "Error on build base64 cd image")
			}
			if isoFileBase64 != "" {
				result.OfflineBillingData = &billing_service.OfflineBillingData{
					CDImageName:   isoFileName,
					CDImageBase64: isoFileBase64,
				}
			}
		}
	}
	return result, nil
}

func (srv *Service) validateABRD(ctx *titan.Context, submissionRequests []ExtendHpmRequest, akaFunc string) []hpm_next.RawResponse {
	errors := []hpm_next.RawResponse{}
	for _, request := range submissionRequests {
		contractId := request.HpmRequest.Vertragskontext.HonoraranlageIdentifikator
		for _, document := range request.HpmRequest.Dokumentationen {
			contract := srv.contractService.GetContractDetailById(contractId)
			if contract == nil || !contract.CheckExistAnforderung(model.ICheckExistAnforderung{
				ComplianceIds: []string{akaFunc},
				CheckTime:     util.NowUnixMillis(ctx),
			}) {
				continue
			}

			hasRequiredService := slice.Any(*document.Leistungen, func(s hpm_rest_hzv_client.Leistung) bool {
				return s.Leistungsziffer == "0000"
			})
			if hasRequiredService {
				continue
			}
			messageMap := map[string]string{
				"ABRD456":  constant.MessageABRD456,
				"ABRD1449": constant.MessageABRD1449,
			}

			errorCode, errorMessage := akaFunc, messageMap[akaFunc]
			errors = append(errors, hpm_next.RawResponse{
				ErrorCode:    errorCode,
				ErrorMessage: errorMessage,
				ErrorType:    hpm_next.Warning,
				PatientId:    pkg_utils.NewPointer(uuid.MustParse(document.Patient.PatientenId)),
				ContractId:   document.Patient.Nachname,
				DoctorId:     request.DoctorId,
				YearQuarter: billing_common.YearQuarter{
					Year:    request.HpmRequest.Vertragskontext.AbrechnungsJahr,
					Quarter: request.HpmRequest.Vertragskontext.AbrechnungsQuartal,
				},
			})
		}
	}
	return errors
}

func (srv *Service) validateSubmissionRequests(ctx *titan.Context, submissionRequests []ExtendHpmRequest) []hpm_next.RawResponse {
	errors := []hpm_next.RawResponse{}
	errors = append(errors, srv.validateABRD(ctx, submissionRequests, "ABRD456")...)
	errors = append(errors, srv.validateABRD(ctx, submissionRequests, "ABRD1449")...)
	return errors
}

func (srv *Service) submit(
	ctx *titan.Context,
	submissionRequests []ExtendHpmRequest,
	medicationPrescriptionRequests []hpm_next_builder.PrescriptionBuilderV2,
	referenceId uuid.UUID,
	isTestRun bool,
) (submitToHPMResponse, error) {
	var (
		submissionResponse             []*hpm_next.StarteAbrechnungV2Response
		billingHistories               []billing_service_repo.BillingHistory
		medicationPrescriptionResponse []*hpm_next.StarteVerordnungsdatenUebermittlungV2Response
	)
	rawResponses := []hpm_next.RawResponse{}
	result := submitToHPMResponse{
		payloadTrackingDocuments:     []string{},
		payloadTrackingPrescriptions: []string{},
		offlineBillingDataResponse:   []*hpm_rest_hzv_client.DatentraegerTyp{},
	}
	for _, s := range submissionRequests {
		request, _ := json.Marshal(s.HpmRequest)
		result.payloadTrackingDocuments = append(result.payloadTrackingDocuments, string(request))
		res, err := srv.hpmRest.SubmitBilling(ctx, s.HpmRequest, s.DoctorId)
		if err != nil {
			if err.Error() == string(error_code.ErrorCode_HpmFunction_Not_Available) {
				continue
			}
			return result, errors.WithMessage(err, "start billing failed")
		}
		if res == nil {
			return result, errors.WithMessage(err, "start billing failed")
		}
		result.offlineBillingDataResponse = append(result.offlineBillingDataResponse, res.OfflineBillingData)
		submitedBillingError := res.SubmitBillingError
		internalBillingError := srv.validateSubmissionRequests(ctx, submissionRequests)
		isExistError := slice.Any(internalBillingError, func(e hpm_next.RawResponse) bool {
			return e.ErrorType == hpm_next.Error
		})
		rawResponses = append(rawResponses, submitedBillingError...)
		rawResponses = append(rawResponses, internalBillingError...)
		submissionResponse = append(submissionResponse, res)
		if !isExistError {
			if !isTestRun {
				data := res.OfflineBillingData
				if data != nil {
					isoName := *data.DatentraegerID + ".iso"
					isoData := *data.IsoImage
					isoFileInfo, err := srv.uploadFileOffline(ctx, isoName, isoData)
					if err != nil {
						return result, errors.WithMessage(err, "upload iso file failed")
					}
					pdfName := *data.DatentraegerID + ".pdf"
					pdfData := *data.Begleitschreiben
					pdfFileInfo, err := srv.uploadFileOffline(ctx, pdfName, pdfData)
					if err != nil {
						return result, errors.WithMessage(err, "upload iso file failed")
					}
					billingHistory := billing_service_repo.BillingHistory{
						Id:                  pkg_utils.NewUUID(),
						BillingYear:         res.Year,
						BillingQuarter:      res.Quarter,
						BillingDoctorId:     &s.DoctorId,
						BillingContractId:   &res.BillingContractId,
						SubmittedUserId:     ctx.UserInfo().UserUUID(),
						SubmittedDate:       pkg_utils.NowUnixMillis(ctx),
						DiagnoseIds:         res.EntryIds.DiagnoseEntryIds,
						ServiceIds:          res.EntryIds.ServiceEntryIds,
						TransferId:          &res.TransferId,
						ReferenceId:         &referenceId,
						OfflineBillingFiles: []*billing_service_repo.FileInfo{isoFileInfo, pdfFileInfo},
						BillingHistoryType:  billing_history_common.BillingHistoryType_Offline,
					}
					billingHistories = append(billingHistories, billingHistory)
				}
			}
			historyEntity, err := srv.mapToBillingHistoryAndUploadProtocol(ctx, mapToBillingHistoryAndUploadProtocolRequest{
				year:               res.Year,
				quarter:            res.Quarter,
				doctorId:           s.DoctorId,
				protocol:           res.HPMResponse.ProtocolFile,
				contractId:         res.HPMResponse.BillingContractId,
				transferId:         &res.TransferId,
				ServiceIds:         res.EntryIds.ServiceEntryIds,
				DiagnoseIds:        res.EntryIds.DiagnoseEntryIds,
				ReferenceId:        referenceId,
				BillingHistoryType: billing_history_common.BillingHistoryType_Document,
			})
			if err != nil {
				return result, errors.WithMessage(err, "map to billing history failed")
			}
			billingHistories = append(billingHistories, *historyEntity)
		}
	}
	for _, s := range medicationPrescriptionRequests {
		if s.Request == nil {
			continue
		}
		request, _ := json.Marshal(s.Request)
		result.payloadTrackingPrescriptions = append(result.payloadTrackingPrescriptions, string(request))
		res, err := srv.hpmRest.SubmmitMedicinPrescription(ctx, *s.Request, s.DoctorId, func(medicineId, formId *uuid.UUID) *uuid.UUID {
			if medicineId == nil && formId == nil {
				return nil
			}
			result := medicineIdFinder(
				slice.Map(s.PrescriptionEntries, func(p hpm_next_builder.Prescription) timeline_repo.TimelineEntity[patient_encounter.EncounterMedicinePrescription] {
					return timeline_repo.TimelineEntity[patient_encounter.EncounterMedicinePrescription](p)
				}))(medicineId, formId)
			return result
		})
		if err != nil {
			if err.Error() == string(error_code.ErrorCode_HpmFunction_Not_Available) {
				continue
			}
			return result, errors.WithMessage(err, "start billing failed")
		}
		if res == nil {
			return result, errors.New("start billing failed")
		}
		if len(res.SubmitBillingError) > 0 {
			medicationPrescriptionResponse = append(medicationPrescriptionResponse, res)
			rawResponses = append(rawResponses, res.SubmitBillingError...)
		} else {
			entryIds := s.Request.GetEntryIds(slice.Map(s.PrescriptionEntries, func(p hpm_next_builder.Prescription) timeline_repo.TimelineEntity[patient_encounter.EncounterMedicinePrescription] {
				return timeline_repo.TimelineEntity[patient_encounter.EncounterMedicinePrescription](p)
			}))
			historyEntity, err := srv.mapToBillingHistoryAndUploadProtocol(ctx, mapToBillingHistoryAndUploadProtocolRequest{
				year:               res.Year,
				quarter:            res.Quarter,
				doctorId:           s.DoctorId,
				protocol:           res.HPMResponse.ProtocolFile,
				contractId:         res.HPMResponse.BillingContractId,
				PrescriptionIds:    entryIds,
				transferId:         res.TransferId,
				ReferenceId:        referenceId,
				BillingHistoryType: billing_history_common.BillingHistoryType_Prescription,
			})
			if err != nil {
				return result, errors.WithMessage(err, "map to billing history failed")
			}
			billingHistories = append(billingHistories, *historyEntity)
		}
	}
	var (
		billingSuccess []uuid.UUID
		billingFail    []uuid.UUID
	)

	for _, v := range billingHistories {
		billingSuccess = append(billingSuccess, v.DiagnoseIds...)
		billingSuccess = append(billingSuccess, v.ServiceIds...)
		billingSuccess = append(billingSuccess, v.PrescriptionIds...)
	}

	for _, v := range medicationPrescriptionResponse {
		billingFail = append(billingFail, v.ErrorEntryIds...)
	}

	for _, v := range submissionResponse {
		billingFail = append(billingFail, v.ErrorEntryIds...)
	}
	result.rawResponses = rawResponses
	result.submitionResponse = submissionResponse
	result.medicationPrescriptionResponse = medicationPrescriptionResponse
	result.billingSuccess = billingSuccess
	result.billingFail = billingFail
	result.billingHistories = billingHistories
	return result, nil
}

func (srv *Service) GetEntries(ctx *titan.Context, scheinIds []uuid.UUID) ([]timeline_repo.TimelineEntity[any], error) {
	normalRezeptFilter := pkg_utils.AllElemMatch(timeline_repo.Field_MedicineERezeptStatus, bson.M{operator.Eq: nil})
	sentERezeptFilter := pkg_utils.AllElemMatch(timeline_repo.Field_MedicineERezeptStatus, bson.M{operator.Eq: qes_common.Status_Sent})
	entries, err := srv.timelineService.Find(ctx, bson.M{
		timeline_repo.Field_ScheinIds: bson.M{
			operator.In: scheinIds,
		},
		operator.Or: bson.A{
			bson.M{
				timeline_repo.Field_timelineEntityType: bson.M{
					operator.In: bson.A{
						timeline_common.TimelineEntityType_Diagnose,
						timeline_common.TimelineEntityType_Service,
					},
				},
				timeline_repo.Field_IsDeleted: false,
			},
			// According to PRO-12595, PRO-12190, we only count the medicine prescription that has been printed and not deleted, but still send deleted ones to HPM
			bson.M{
				timeline_repo.Field_timelineEntityType:             timeline_common.TimelineEntityType_MedicinePrescription,
				timeline_repo.Field_MedicinePrescription_PrintDate: bson.M{operator.Ne: nil},
				timeline_repo.Field_IsDeleted:                      false,
				operator.Or: bson.A{
					bson.M{timeline_repo.Field_MedicinePrescription_Medicines: normalRezeptFilter},
					bson.M{timeline_repo.Field_MedicinePrescription_Medicines: sentERezeptFilter},
				},
			},
		},
	})
	if err != nil {
		return nil, err
	}
	return entries, nil
}

func (srv *Service) GetCreatedDoctors(ctx *titan.Context, timelines []timeline_common.TimelineModel) map[uuid.UUID]employee.EmployeeProfile {
	createdByDoctorIds := slice.Map(timelines, func(t timeline_common.TimelineModel) uuid.UUID {
		return *t.CreatedBy
	})
	createdByDoctorIds = slice.Uniq(createdByDoctorIds)
	doctors, err := srv.repo.employeeProfileRepo.GetMapProfileByIds(ctx, createdByDoctorIds)
	if err != nil {
		return nil
	}
	return doctors
}

func (srv *Service) GetSkipErrorList(ctx *titan.Context) ([]billing_service_model.ErrorSkipBilling, error) {
	userSettings, err := srv.userSettingsRepo.FindById(ctx, pkg_utils.GetPointerValue(ctx.UserInfo().UserUUID()))
	if err != nil {
		return nil, err
	}

	if value, ok := userSettings.Settings[string(user_settings.SettingsKey_ErrorKeyList)]; ok {
		var errorSkipBilling []billing_service_model.ErrorSkipBilling
		err = json.Unmarshal([]byte(value), &errorSkipBilling)
		if err != nil {
			return nil, err
		}
		return errorSkipBilling, nil
	}
	return nil, nil
}
