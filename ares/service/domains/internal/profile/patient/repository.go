package patient

import (
	"emperror.dev/errors"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
)

type Repository struct {
	patient.PatientProfileDefaultRepository
}

func (r *Repository) GetByIds(ctx *titan.Context, ids []uuid.UUID) ([]patient.PatientProfile, error) {
	filter := bson.D{
		{
			Key: patient.Field_Id,
			Value: bson.D{
				{
					Key:   "$in",
					Value: ids,
				},
			},
		},
		{
			Key:   repos.Field_IsDeleted,
			Value: bson.M{"$ne": true},
		},
	}

	data, err := slice.ToValueTypeWithError(r.Find(ctx, filter))
	if err != nil {
		return nil, errors.New(err.Error())
	}

	if len(data) == 0 {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Record_Not_Found, "")
	}
	return data, nil
}

func (r *Repository) GetById(ctx *titan.Context, id uuid.UUID) (*patient.PatientProfile, error) {
	return r.FindOne(ctx, bson.D{
		{Key: patient.Field_Id, Value: id},
		{Key: repos.Field_IsDeleted, Value: bson.M{"$ne": true}},
	})
}

var PatientProfileRepositoryMod = submodule.Make[*Repository](func() *Repository {
	return &Repository{patient.NewPatientProfileDefaultRepository()}
})
