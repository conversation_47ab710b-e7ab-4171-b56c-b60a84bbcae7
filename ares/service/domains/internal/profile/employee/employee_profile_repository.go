package employee

import (
	"fmt"

	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_repo"
	mvz_admin_employee "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/admin/employee"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Repository struct {
	employeeProfileRepo *employee.EmployeeProfileDefaultRepository
	employeeMVZRepo     *mvz_admin_employee.EmployeeDefaultRepository
}

func (r *Repository) FindById(ctx *titan.Context, id uuid.UUID) ([]employee.EmployeeProfile, error) {
	filter := bson.D{
		{
			Key:   employee.Field_Id,
			Value: id,
		},
	}
	return r.employeeProfileRepo.Find(ctx, filter)
}

func (r *Repository) FindByLanrId(ctx *titan.Context, lanrId string) (*employee.EmployeeProfile, error) {
	filter := bson.M{employee.Field_Lanr: lanrId}
	return r.employeeProfileRepo.FindOne(ctx, filter)
}

func (r *Repository) FindByHzvId(ctx *titan.Context, hzvId string) (*employee.EmployeeProfile, error) {
	filter := bson.D{
		{
			Key:   employee.Field_HavgId,
			Value: hzvId,
		},
	}
	return r.employeeProfileRepo.FindOne(ctx, filter)
}

func (r *Repository) FindByMediId(ctx *titan.Context, mediId string) (*employee.EmployeeProfile, error) {
	filter := bson.D{
		{
			Key:   employee.Field_MediverbundId,
			Value: mediId,
		},
	}
	return r.employeeProfileRepo.FindOne(ctx, filter)
}

func (r *Repository) FindByExternalId(ctx *titan.Context, externalId string) (*employee.EmployeeProfile, error) {
	filter := bson.D{
		{
			Key:   employee.Field_ExternalId,
			Value: externalId,
		},
	}
	return r.employeeProfileRepo.FindOne(ctx, filter)
}

func (r *Repository) FindByBsnrId(ctx *titan.Context, bsnrId uuid.UUID) ([]employee.EmployeeProfile, error) {
	filter := bson.D{
		{
			Key:   employee.Field_BsnrId,
			Value: bsnrId,
		},
	}
	return r.employeeProfileRepo.Find(ctx, filter)
}

func (r *Repository) FindByBsnrCode(ctx *titan.Context, bsnrCode string) ([]employee.EmployeeProfile, error) {
	filter := bson.D{
		{
			Key:   employee.Field_Bsnr,
			Value: bsnrCode,
		},
	}
	return r.employeeProfileRepo.Find(ctx, filter)
}

func (r *Repository) FindByIds(ctx *titan.Context, ids []uuid.UUID) ([]employee.EmployeeProfile, error) {
	filter := bson.D{
		{
			Key: employee.Field_Id,
			Value: bson.D{
				{
					Key:   "$in",
					Value: ids,
				},
			},
		},
	}
	return r.employeeProfileRepo.Find(ctx, filter)
}

func (r *Repository) UpdateById(ctx *titan.Context, emp employee.EmployeeProfile) (*employee.EmployeeProfile, error) {
	filter := bson.D{
		{
			Key:   employee.Field_Id,
			Value: emp.Id,
		},
	}
	dataUpdate := bson.M{"$set": bson.M{
		employee.Field_Title:                  emp.Title,
		employee.Field_AreaOfExpertise:        emp.AreaOfExpertise,
		employee.Field_FirstName:              emp.FirstName,
		employee.Field_LastName:               emp.LastName,
		employee.Field_MediverbundId:          emp.MediverbundId,
		employee.Field_MediverbundVpId:        emp.MediverbundVpId,
		employee.Field_HavgId:                 emp.HavgId,
		employee.Field_HavgVpId:               emp.HavgVpId,
		employee.Field_HasFavContracts:        emp.HasFavContracts,
		employee.Field_HasHzvContracts:        emp.HasHzvContracts,
		employee.Field_Salutation:             emp.Salutation,
		employee.Field_Phone:                  emp.Phone,
		employee.Field_AdditionalName:         emp.AdditionalName,
		employee.Field_Initial:                emp.Initial,
		employee.Field_DmpPrograms:            emp.DmpPrograms,
		employee.Field_IntendWord:             emp.IntendWord,
		employee.Field_JobDescription:         emp.JobDescription,
		employee.Field_MarkAsBillingDoctor:    emp.MarkAsBillingDoctor,
		employee.Field_PseudoLanr:             emp.PseudoLanr,
		employee.Field_TeamNumbers:            emp.TeamNumbers,
		employee.Field_DoctorStamp:            emp.DoctorStamp,
		employee.Field_BankInformations:       emp.BankInformations,
		employee.Field_MarkAsEmployedDoctor:   emp.MarkAsEmployedDoctor,
		employee.Field_ResponsibleDoctorId:    emp.ResponsibleDoctorId,
		employee.Field_RepresentativeDoctorId: emp.RepresentativeDoctorId,
		employee.Field_Okv:                    emp.Okv,
		employee.Field_Lanr:                   emp.Lanr,
		employee.Field_IsParticipationActive:  emp.IsParticipationActive,
		employee.Field_Types:                  emp.Types,
		employee.Field_DeviceId:               emp.DeviceId,
		employee.Field_UserName:               emp.UserName,
		employee.Field_HpmEndpoint:            emp.HpmEndpoint,
		employee.Field_HzvContracts:           emp.HzvContracts,
		employee.Field_FavContracts:           emp.FavContracts,
		employee.Field_Email:                  emp.Email,
		employee.Field_BsnrIds:                emp.BsnrIds,
		employee.Field_Bsnrs:                  emp.Bsnrs,
		employee.Field_EHKSType:               emp.EHKSType,
		employee.Field_IsDoctor:               emp.IsDoctor,
	}}
	opts := options.FindOneAndUpdate().SetReturnDocument(options.After)
	result, err := r.employeeProfileRepo.FindOneAndUpdate(ctx, filter, dataUpdate, opts)
	return result, err
}

func (r *Repository) DeleteByHashedId(ctx *titan.Context, id uuid.UUID) (int64, error) {
	filter := bson.D{
		{
			Key:   employee.Field_Id,
			Value: id,
		},
	}
	result, err := r.employeeProfileRepo.Db.Delete(ctx, filter)
	if err != nil {
		return int64(0), err
	}
	return result.DeletedCount, err
}

func (r *Repository) FindDoctorsHasHavgId(ctx *titan.Context, ids []uuid.UUID) ([]employee.EmployeeProfile, error) {
	filter := util.NotNullOrEmpty(employee.Field_HavgId)
	if len(ids) > 0 {
		filter = util.And(filter, util.In(employee.Field_Id, ids))
	}
	return r.employeeProfileRepo.Find(ctx, filter)
}

func (r *Repository) FindDoctorsHasMediId(ctx *titan.Context, ids []uuid.UUID) ([]employee.EmployeeProfile, error) {
	filter := util.NotNullOrEmpty(employee.Field_MediverbundId)
	if len(ids) > 0 {
		filter = util.And(filter, util.In(employee.Field_Id, ids))
	}
	return r.employeeProfileRepo.Find(ctx, filter)
}

func (r *Repository) FindParticipationActiveDoctors(ctx *titan.Context, ids []uuid.UUID) ([]employee.EmployeeProfile, error) {
	filter := bson.M{employee.Field_IsParticipationActive: true}
	if len(ids) > 0 {
		filter[employee.Field_Id] = bson.M{
			"$in": ids,
		}
	}
	return r.employeeProfileRepo.Find(ctx, filter)
}

func (r *Repository) UpdateDevice(ctx *titan.Context, emp employee.EmployeeProfile) (*employee.EmployeeProfile, error) {
	filter := bson.D{
		{
			Key:   employee.Field_Id,
			Value: emp.Id,
		},
	}
	dataUpdate := bson.M{"$set": bson.M{
		employee.Field_DeviceId: emp.DeviceId,
	}}
	result, err := r.employeeProfileRepo.FindOneAndUpdate(ctx, filter, dataUpdate)
	return result, err
}

func (r *Repository) FindByDeviceIds(ctx *titan.Context, ids []uuid.UUID) ([]employee.EmployeeProfile, error) {
	filter := bson.D{
		{
			Key: employee.Field_DeviceId,
			Value: bson.D{
				{
					Key:   "$in",
					Value: ids,
				},
			},
		},
	}
	return r.employeeProfileRepo.Find(ctx, filter)
}

func (r *Repository) UpdateDefaultBsnrOfEmployee(ctx *titan.Context, bsnr bsnr_repo.BSNR) (*employee.EmployeeProfile, error) {
	emp, err := r.employeeProfileRepo.FindById(ctx, util.GetPointerValue(ctx.UserInfo().UserUUID()))
	if err != nil {
		return nil, err
	}
	if emp == nil {
		return nil, fmt.Errorf("no employee profile was found")
	}

	filter := bson.D{
		{
			Key:   employee.Field_Id,
			Value: emp.Id,
		},
	}
	dataUpdate := bson.M{"$set": bson.M{
		employee.Field_Bsnr:              bsnr.Code,
		employee.Field_BsnrId:            bsnr.Id,
		employee.Field_BsnrPracticeStamp: bsnr.PracticeStamp,
		employee.Field_BsnrName:          bsnr.Name,
		employee.Field_BsnrStreet:        bsnr.Street,
		employee.Field_BsnrNumber:        bsnr.Number,
		employee.Field_BsnrPostCode:      bsnr.PostCode,
		employee.Field_BsnrPhoneNumber:   bsnr.PhoneNumber,
		employee.Field_BsnrFaxNumber:     bsnr.Fax,
		employee.Field_BsnrFacilityType:  bsnr.FacilityType,
		repos.Field_UpdatedAt:            util.Now(ctx),
		repos.Field_UpdatedBy:            ctx.UserInfo().UserUUID(),
	}}
	return r.employeeProfileRepo.FindOneAndUpdate(ctx, filter, dataUpdate)
}

var EmployeeRepositoryMod = submodule.Make[*Repository](func() *Repository {
	employeeProfileRepo := employee.NewEmployeeProfileDefaultRepository()
	employeeMVZRepo := mvz_admin_employee.NewEmployeeDefaultRepository()
	return &Repository{
		employeeProfileRepo: &employeeProfileRepo,
		employeeMVZRepo:     &employeeMVZRepo,
	}
})
