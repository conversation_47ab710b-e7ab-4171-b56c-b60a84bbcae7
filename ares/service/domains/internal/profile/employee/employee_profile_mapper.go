package employee

import (
	"git.tutum.dev/medi/tutum/ares/pkg/hpm_service/hpm"
	"git.tutum.dev/medi/tutum/ares/pkg/hpm_service/hpm_next"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"gitlab.com/silenteer-oss/hestia/infrastructure"
	"gitlab.com/silenteer-oss/hestia/infrastructure/util"
)

func ToContractDoctor(e *employee.EmployeeProfile) *hpm.ContractDoctor {
	d := &hpm.ContractDoctor{
		AcademicDegree: *e.Title,
		Bsnr:           e.Bsnr,
		Lanr:           *e.<PERSON>,
		FirstName:      e.FirstName,
		LastName:       e.LastName,
		ZipCode:        e.ZipCode,
		Street:         e.Street,
		Fax:            e.Fax,
		MobilePhone:    e.MobilePhone,
	}

	if e.Address != nil {
		d.Address = *e.Address
	}
	if e.Phone != nil {
		d.Phone = *e.Phone
	}
	if e.MediverbundId != nil {
		d.MediId = *e.MediverbundId
	}
	if e.HavgId != nil {
		d.HaevgId = *e.HavgId
	}
	if e.HavgVpId != nil {
		d.HavgVpId = *e.HavgVpId
	}
	return d
}

func ToContractDoctorNext(e *employee.EmployeeProfile) *hpm_next.ContractDoctor {
	d := &hpm_next.ContractDoctor{
		AcademicDegree: *e.Title,
		Bsnr:           e.Bsnr,
		Lanr:           *e.Lanr,
		FirstName:      e.FirstName,
		LastName:       e.LastName,
		ZipCode:        e.ZipCode,
		Street:         e.Street,
		Fax:            e.Fax,
		MobilePhone:    e.MobilePhone,
	}

	if e.Address != nil {
		d.Address = *e.Address
	}
	if e.Phone != nil {
		d.Phone = *e.Phone
	}
	if e.MediverbundId != nil {
		d.MediId = *e.MediverbundId
	}
	if e.HavgId != nil {
		d.HaevgId = *e.HavgId
	}
	return d
}

func ResponseToContractDoctor(e *profile.EmployeeProfileResponse) *hpm_next.ContractDoctor {
	d := &hpm_next.ContractDoctor{
		AcademicDegree: *e.Title,
		Bsnr:           e.Bsnr,
		FirstName:      e.FirstName,
		LastName:       e.LastName,
		ZipCode:        e.ZipCode,
		Street:         e.Street,
		Fax:            e.Fax,
		MobilePhone:    e.MobilePhone,
	}

	if e.Lanr != nil {
		d.Lanr = *e.Lanr
	} else if e.PseudoLanr != nil {
		d.Lanr = *e.PseudoLanr
	}
	if e.Address != nil {
		d.Address = *e.Address
	}
	if e.Phone != nil {
		d.Phone = *e.Phone
	}
	if e.MediverbundId != nil {
		d.MediId = *e.MediverbundId
	}
	if e.HavgId != nil {
		d.HaevgId = *e.HavgId
	}
	if e.HavgVpId != nil {
		d.HavgVpId = *e.HavgVpId
	}
	return d
}

func ResponseToContractDoctorHPMNext(e *profile.EmployeeProfileResponse) *hpm_next.ContractDoctor {
	d := &hpm_next.ContractDoctor{
		AcademicDegree: *e.Title,
		Bsnr:           e.Bsnr,
		FirstName:      e.FirstName,
		LastName:       e.LastName,
		ZipCode:        e.ZipCode,
		Street:         e.Street,
		Fax:            e.Fax,
		MobilePhone:    e.MobilePhone,
	}

	if e.Lanr != nil {
		d.Lanr = *e.Lanr
	} else if e.PseudoLanr != nil {
		d.Lanr = *e.PseudoLanr
	}
	if e.Address != nil {
		d.Address = *e.Address
	}
	if e.Phone != nil {
		d.Phone = *e.Phone
	}
	if e.MediverbundId != nil {
		d.MediId = *e.MediverbundId
	}
	if e.HavgId != nil {
		d.HaevgId = *e.HavgId
	}
	return d
}

func Response(e employee.EmployeeProfile) *profile.EmployeeProfileResponse {
	response := &profile.EmployeeProfileResponse{
		Id:                     e.Id,
		FullName:               e.FirstName + " " + e.LastName,
		Dob:                    e.Dob,
		Salutation:             e.Salutation,
		Phone:                  e.Phone,
		Email:                  e.Email,
		Address:                e.Address,
		Title:                  e.Title,
		FirstName:              e.FirstName,
		LastName:               e.LastName,
		HavgId:                 e.HavgId,
		HavgVpId:               e.HavgVpId,
		Lanr:                   e.Lanr,
		MobilePhone:            e.MobilePhone,
		MediverbundId:          e.MediverbundId,
		MediverbundVpId:        e.MediverbundVpId,
		Bsnr:                   e.Bsnr,
		AreaOfExpertise:        e.AreaOfExpertise,
		Okv:                    e.Okv,
		HasFavContracts:        e.HasFavContracts,
		HasHzvContracts:        e.HasHzvContracts,
		ZipCode:                e.ZipCode,
		Street:                 e.Street,
		Fax:                    e.Fax,
		Initial:                e.Initial,
		AdditionalName:         e.AdditionalName,
		IntendWord:             e.IntendWord,
		DmpPrograms:            e.DmpPrograms,
		JobDescription:         e.JobDescription,
		BsnrId:                 e.BsnrId,
		PseudoLanr:             e.PseudoLanr,
		TeamNumbers:            e.TeamNumbers,
		DoctorStamp:            e.DoctorStamp,
		BankInformations:       e.BankInformations,
		MarkAsEmployedDoctor:   e.MarkAsEmployedDoctor,
		ResponsibleDoctorId:    e.ResponsibleDoctorId,
		MarkAsBillingDoctor:    e.MarkAsBillingDoctor,
		RepresentativeDoctorId: e.RepresentativeDoctorId,
		IsParticipationActive:  e.IsParticipationActive,
		ExternalId:             e.ExternalId,
		Types:                  e.Types,
		DeviceId:               e.DeviceId,
		BsnrName:               e.BsnrName,
		UserName:               e.UserName,
		HpmEndpoint:            e.HpmEndpoint,
		CreatedDate:            e.CreatedDate,
		HzvContracts:           e.HzvContracts,
		FavContracts:           e.FavContracts,
		Bsnrs:                  e.Bsnrs,
		BsnrIds:                e.BsnrIds,
		EHKSType:               e.EHKSType,
		IsDoctor:               e.IsDoctor,
	}
	return response
}

func FromRequest(request profile.EmployeeProfileRequest) employee.EmployeeProfile {
	emp := employee.EmployeeProfile{
		Id:                     request.OriginalId,
		Dob:                    request.Dob,
		Salutation:             request.Salutation,
		Phone:                  request.Phone,
		Email:                  request.Email,
		Address:                request.Address,
		Title:                  request.Title,
		FirstName:              request.FirstName,
		LastName:               request.LastName,
		HavgId:                 request.HavgId,
		HavgVpId:               request.HavgVpId,
		Lanr:                   request.Lanr,
		MobilePhone:            request.MobilePhone,
		Bsnr:                   request.Bsnr,
		AreaOfExpertise:        request.AreaOfExpertise,
		MediverbundId:          request.MediverbundId,
		MediverbundVpId:        request.MediverbundVpId,
		HasFavContracts:        request.HasFavContracts,
		HasHzvContracts:        request.HasHzvContracts,
		CreatedDate:            util.NowUnixMillis(nil),
		Okv:                    request.Okv,
		Initial:                request.Initial,
		AdditionalName:         request.AdditionalName,
		IntendWord:             request.IntendWord,
		DmpPrograms:            request.DmpPrograms,
		JobDescription:         request.JobDescription,
		MarkAsBillingDoctor:    request.MarkAsBillingDoctor,
		BsnrId:                 request.BsnrId,
		PseudoLanr:             request.PseudoLanr,
		TeamNumbers:            request.TeamNumbers,
		DoctorStamp:            request.DoctorStamp,
		BsnrPracticeStamp:      request.BsnrPracticeStamp,
		BankInformations:       request.BankInformations,
		MarkAsEmployedDoctor:   request.MarkAsEmployedDoctor,
		ResponsibleDoctorId:    request.ResponsibleDoctorId,
		RepresentativeDoctorId: request.RepresentativeDoctorId,
		BsnrName:               request.BsnrName,
		IsParticipationActive:  request.IsParticipationActive,
		Types:                  request.Types,
		DeviceId:               request.DeviceId,
		UserName:               request.UserName,
		ExternalId:             request.ExternalId,
		HpmEndpoint:            request.HpmEndpoint,
		HzvContracts:           request.HzvContracts,
		FavContracts:           request.FavContracts,
		Bsnrs:                  request.Bsnrs,
		BsnrIds:                request.BsnrIds,
		EHKSType:               request.EHKSType,
		IsDoctor:               request.IsDoctor,
	}
	return emp
}

func getRolesFromTypes(employeeTypes []common.UserType) (roleKeys, roles []string) {
	if slice.Contains(employeeTypes, common.MANAGER) {
		roleKeys = append(roleKeys, infrastructure.ADMIN.String())
		roles = append(roles, "ORG_OWNER")
	}
	if slice.Contains(employeeTypes, common.MFA) || slice.Contains(employeeTypes, common.DOCTOR) {
		roleKeys = append(roleKeys, infrastructure.DOCTOR.String())
		roles = append(roles, "ORG_OWNER_VIEWER")
	}
	return roleKeys, roles
}
