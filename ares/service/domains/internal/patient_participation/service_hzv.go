package patient_participation

import (
	"time"

	"emperror.dev/errors"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	contract_model "git.tutum.dev/medi/tutum/ares/service/contract/contract/model"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	patient_participation_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_participation"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"github.com/google/uuid"
	"gitlab.com/silenteer-oss/hestia/infrastructure/util"
	"gitlab.com/silenteer-oss/titan"
)

// Temporarily removed the following functions, because HzV support multiple active participations (AWH_01)
// func (s *Service) ActiveHzvParticipation(ctx *titan.Context, participation patient_participation_repo.PatientParticipation, startDate int64, insuranceNumber string) (
// 	*patient_participation_repo.PatientParticipation, error) {
// 	now := util.NowUnixMillis(ctx)
// 	endDate := util.UnixMillis(time.Unix(0, int64(time.Millisecond)*startDate).Add(-time.Hour * 24))
// 	err := s.TerminateActiveHZVParticipationByTime(ctx, insuranceNumber, startDate, endDate)
// 	if err != nil {
// 		return nil, errors.WithStack(err)
// 	}
// 	participation.Status = patient_participation_repo.PatientParticipationStatus_Active
// 	participation.UpdateDate = &now
// 	participation.UpdatedBy = ctx.UserInfo().UserUUID()
// 	participation.StartDate = &startDate
// 	result, err := s.repo.Update(ctx, participation)
// 	if err != nil {
// 		return nil, errors.WithStack(err)
// 	}

// 	return result, nil
// }

func (s *Service) ActivateHzvParticipations(ctx *titan.Context,
	participations []patient_participation_repo.PatientParticipation,
	startDate int64, insuranceNumber string) ([]patient_participation_repo.PatientParticipation, error) {
	now := util.NowUnixMillis(ctx)
	patientIds := slice.Map(participations, func(participation patient_participation_repo.PatientParticipation) uuid.UUID {
		return participation.PatientId
	})
	patientIds = slice.Uniq(patientIds)
	err := s.CancelCurrentActiveHZVParticipations(ctx, insuranceNumber, patientIds)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	for i := 0; i < len(participations); i++ {
		participations[i].Status = patient_participation_repo.PatientParticipationStatus_Active
		participations[i].UpdateDate = &now
		participations[i].UpdatedBy = ctx.UserInfo().UserUUID()
		participations[i].StartDate = &startDate
		participations[i].DoctorFunctionType = patient_participation_repo.DoctorFunctionType_Custodian
	}
	result, err := s.repo.UpdateMany(ctx, participations)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}

// CreateHzvCustodianParticipation create HZV participation.
// if the contract is not HZV type, it will return error
// if the new participation is HZV, the function will terminate current active hzv contract before creating the new one.
// will send notify Patient's Participation Changed when finished.
func (s *Service) CreateHzvCustodianParticipation(ctx *titan.Context,
	request CreateParticipation,
) ([]patient_participation_repo.PatientParticipation, error) {
	if request.ContractType != contract_model.ContractType_HouseDoctorCare {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Data_Invalid)
	}
	if request.Status == patient_participation_repo.PatientParticipationStatus_Active && request.StartDate != nil {
		endDate := util.UnixMillis(time.Unix(0, int64(time.Millisecond)*(*request.StartDate)).Add(-time.Hour * 24))
		err := s.TerminateActiveHZVParticipationByTime(ctx, request.InsuranceNumber, *request.StartDate, endDate)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	participations, err := s.createParticipations(ctx, request)

	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(participations) == 0 {
		return nil, nil
	}

	// Notification is now handled by createParticipations via defer
	return participations, nil
}

func (s *Service) TerminateActiveHZVParticipationByTime(ctx *titan.Context, insuranceNumber string, checkTime, endDate int64) error {
	hzvType := contract_model.ContractType_HouseDoctorCare
	participations, err := s.repo.FindByFilter(ctx, patient_participation_repo.Filter{
		Statuses: []patient_participation_repo.PatientParticipationStatus{
			patient_participation_repo.PatientParticipationStatus_Active,
			patient_participation_repo.PatientParticipationStatus_Terminated,
		},
		HashedInsuranceNumber: &insuranceNumber,
		Time:                  &checkTime,
		PatientId:             nil,
		ContractType:          &hzvType,
	})
	if err == nil && len(participations) > 0 {
		for _, participation := range participations {
			err = s.terminateParticipation(ctx, &participation, endDate)
			if err != nil {
				break
			}
		}
	}
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *Service) CancelCurrentActiveHZVParticipations(ctx *titan.Context, insuranceNumber string, patientIds []uuid.UUID) error {
	hzvType := contract_model.ContractType_HouseDoctorCare
	participations, err := s.repo.FindByFilter(ctx, patient_participation_repo.Filter{
		Statuses: []patient_participation_repo.PatientParticipationStatus{
			patient_participation_repo.PatientParticipationStatus_Active,
		},
		HashedInsuranceNumber: &insuranceNumber,
		PatientIdList:         patientIds,
		ContractType:          &hzvType,
	})
	if err == nil && len(participations) > 0 {
		for _, participation := range participations {
			err = s.CancelPatientParticipation(ctx, &participation)
			if err != nil {
				break
			}
		}
	}
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}
