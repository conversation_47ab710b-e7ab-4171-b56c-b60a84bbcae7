package patient_participation

import (
	"emperror.dev/errors"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	contract_model "git.tutum.dev/medi/tutum/ares/service/contract/contract/model"
	doctor_participate_api "git.tutum.dev/medi/tutum/ares/service/domains/api/doctor_participate"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	patient_participation_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_participation"
	"git.tutum.dev/medi/tutum/pkg/slice"
	pkg_util "git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"gitlab.com/silenteer-oss/hestia/infrastructure/util"
	"gitlab.com/silenteer-oss/titan"
)

func (*Service) generateNewParticipations(
	ctx *titan.Context,
	contractIds []contract_model.ContractId,
	mapContractDoctor map[contract_model.ContractId]struct {
		DoctorId       uuid.UUID
		ChargeSystemId string
	},
	patientId uuid.UUID,
	insuranceNumber string,
	startDate, endDate *int64,
	ikNumber int32,
	status patient_participation_repo.PatientParticipationStatus,
	contractType contract_model.ContractType,
	doctorFunctionType patient_participation_repo.DoctorFunctionType,
	isChangingDoctor, isTransmittedHpm bool,
) (participations []patient_participation_repo.PatientParticipation) {
	now := util.NowUnixMillis(ctx)
	for _, id := range contractIds {
		contractId := id
		if contractDoctor, supported := mapContractDoctor[contractId]; supported {
			newDoctorId := contractDoctor.DoctorId
			chargeSystemId := contractDoctor.ChargeSystemId
			if chargeSystemId != "" {
				chargeSystemId = contractId
			}
			newId := uuid.New()

			participations = append(participations, patient_participation_repo.PatientParticipation{
				Id:                    &newId,
				StartDate:             startDate,
				EndDate:               endDate,
				DoctorId:              &newDoctorId,
				PatientId:             patientId,
				ContractId:            contractId,
				IkNumber:              ikNumber,
				HashedInsuranceNumber: insuranceNumber,
				Status:                status,
				TerminateHistory:      nil,
				CreatedDate:           now,
				CreatedBy:             pkg_util.GetPointerValue(ctx.UserInfo().UserUUID()),
				UpdateDate:            &now,
				UpdatedBy:             ctx.UserInfo().UserUUID(),
				ContractType:          contractType,
				DoctorFunctionType:    doctorFunctionType,
				IsChangingDoctor:      isChangingDoctor,
				IsTransmittedHpm:      isTransmittedHpm,
				ChargeSystemId:        chargeSystemId,
			})
		}
	}
	return participations
}

// ActivateFavContractGroup public of activateFavContract func
// send notifyPatientParticipationsChanged event
func (s *Service) ActivateFavContractGroup(ctx *titan.Context, patientId uuid.UUID, contractId contract_model.ContractId, startDate int64, doctorIds []uuid.UUID) (
	activatedParticipations []patient_participation_repo.PatientParticipation,
	err error,
) {
	updatedpParticipations, err := s.activateFavContract(ctx, patientId, contractId, startDate, doctorIds, false)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(updatedpParticipations) > 0 {
		patientProfile, err := s.patientProfileRepos.GetPatientProfileByPatientId(ctx, patientId)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		insurance := patientProfile.PatientInfo.GetActiveInsurance()
		if insurance == nil {
			return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_NotActive_InsuranceInfo, "")
		}

		if err != nil {
			return updatedpParticipations, errors.WithStack(err)
		}
		if insurance != nil {
			errNotification := s.NotifyPatientParticipationsChanged(ctx, pkg_util.GetStringValue(insurance.InsuranceNumber), updatedpParticipations)
			if errNotification != nil {
				ctx.Logger().Error(errNotification.Error())
			}
		}
	}
	return updatedpParticipations, nil
}

// activateFavContract active all same group sub contracts with contractId
// if current contract of sub contract is:
//
//	Terminated:
//		If Enddate >= start date, func will update it to active with endate is nil and its startdate is startDate.
//		Else func will create new participation.
//	Requested:
//		will update to active
//	Active
//		if the participation's start date is not equal to passed start date, func will update the startdate.
func (s *Service) activateFavContract(
	ctx *titan.Context,
	patientId uuid.UUID,
	contractId contract_model.ContractId,
	startDate int64,
	doctorIds []uuid.UUID,
	applyGroupContract bool,
) (
	activatedParticipations []patient_participation_repo.PatientParticipation,
	err error,
) {
	patientProfile, err := s.patientProfileRepos.GetPatientProfileByPatientId(ctx, patientId)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	insurance := patientProfile.PatientInfo.GetActiveInsurance()
	if insurance == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_NotActive_InsuranceInfo, "")
	}
	now := util.NowUnixMillis(ctx)
	sameGroupContractIds := make([]contract_model.ContractId, 0)

	if applyGroupContract {
		favContractType := contract_model.ContractType_SpecialistCare
		supportedContracts, err := s.GetSupportedContracts(ctx, insurance.IkNumber, &favContractType)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		for _, contract := range supportedContracts {
			id := contract.Id
			sameGroupContractIds = append(sameGroupContractIds, id)
		}
	} else {
		sameGroupContractIds = append(sameGroupContractIds, contractId)
	}
	sameGroupParticipations, err := s.getValidParticipationsByContractId(ctx, patientId, contractId, insurance.InsuranceNumber, insurance.IkNumber, applyGroupContract)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	sameGroupParticipations = slice.Filter(sameGroupParticipations, func(participation patient_participation_repo.PatientParticipation) bool {
		return participation.PatientId == patientId
	})
	updatingParticipations := make([]patient_participation_repo.PatientParticipation, 0)
	// updatedContractIds := make([]contract_model.ContractId, 0)
	creatingContractIds := make([]contract_model.ContractId, 0)

	for _, contractId := range sameGroupContractIds {
		needCreate := true
		needUpdate := false
		for _, currentParticipatingContract := range sameGroupParticipations {
			if contractId != currentParticipatingContract.ContractId {
				continue
			}

			// found first one and break
			participation := patient_participation_repo.PatientParticipation{}
			err := copier.Copy(&participation, currentParticipatingContract)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			switch participation.Status {
			case patient_participation_repo.PatientParticipationStatus_Active:
				needCreate = false
				if participation.StartDate == nil || *participation.StartDate != startDate {
					needUpdate = true
				}
			case patient_participation_repo.PatientParticipationStatus_Requested:
				needCreate = false
				needUpdate = true
			case patient_participation_repo.PatientParticipationStatus_Terminated:
				if participation.EndDate != nil && *participation.EndDate >= startDate {
					needCreate = false
					needUpdate = true
				} else {
					needCreate = true
					needUpdate = false
				}
			default:
				needCreate = true
			}
			if needUpdate {
				participation.Status = patient_participation_repo.PatientParticipationStatus_Active
				participation.UpdateDate = &now
				participation.UpdatedBy = ctx.UserInfo().UserUUID()
				participation.StartDate = &startDate
				updatingParticipations = append(updatingParticipations, participation)
				// updatedContractIds = append(updatedContractIds, contractId)
			}
			break
		}

		if needCreate {
			creatingContractIds = append(creatingContractIds, contractId)
		}
	}

	if len(updatingParticipations) > 0 {
		result, err := s.repo.UpdateMany(ctx, updatingParticipations)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		activatedParticipations = append(activatedParticipations, result...)
	}

	if len(creatingContractIds) > 0 {
		mapContractDoctorId, err := s.GetMapSupportedContractDoctors(ctx, creatingContractIds)
		if err != nil {
			return activatedParticipations, errors.WithStack(err)
		}
		if len(doctorIds) > 0 {
			for _, doctorId := range doctorIds {
				if _, ok := mapContractDoctorId[contractId]; !ok {
					mapContractDoctorId[contractId] = struct {
						DoctorId       uuid.UUID
						ChargeSystemId string
					}{DoctorId: doctorId, ChargeSystemId: contractId}
				}
			}
		}

		creatingParticipations := s.generateNewParticipations(ctx,
			creatingContractIds,
			mapContractDoctorId,
			patientId,
			pkg_util.GetStringValue(insurance.InsuranceNumber),
			&startDate,
			nil,
			insurance.IkNumber,
			patient_participation_repo.PatientParticipationStatus_Active,
			contract_model.ContractType_SpecialistCare,
			patient_participation_repo.DoctorFunctionType_Custodian,
			false,
			false,
		)
		if len(creatingParticipations) > 0 {
			result, err := s.repo.CreateMany(ctx, creatingParticipations)
			if err != nil {
				return activatedParticipations, errors.WithStack(err)
			}
			activatedParticipations = append(activatedParticipations, result...)
		}
	}

	return activatedParticipations, err
}

// createFavGroupContractParticipation create contract participations in same group with passed contract Id
// The func currently supports for Request and Active status. return error if data is invalid
// if contract is not found or contract is not fav, return error
// status:
//
//	PatientParticipationStatus_Requested: if there is not active and not pending contracts, this func will create pending (requested status) for those contracts.
//	PatientparticipationStatus_Active: if there is Pending same group contract, this func will active that pending.
func (s *Service) createFavGroupContractParticipation(ctx *titan.Context,
	contractId contract_model.ContractId,
	patientId uuid.UUID,
	status patient_participation_repo.PatientParticipationStatus,
	startDate *int64,
) (changedParticipations []patient_participation_repo.PatientParticipation, err error) {
	switch status {
	case patient_participation_repo.PatientParticipationStatus_Requested:
		changedParticipations, err = s.createRequestedGroupFavParticipation(ctx, contractId, patientId)
	case patient_participation_repo.PatientParticipationStatus_Active:
		if startDate == nil {
			err = pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Data_Invalid)
			break
		}
		changedParticipations, err = s.activateFavContract(ctx, patientId, contractId, *startDate, nil, true)
	default:
		err = pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Data_Invalid)
	}
	return changedParticipations, err
}

// GetMapSupportedContractDoctors get a map of doctors, CURRENTLY, supporting contractIds
// return map[contractId] {doctorId uuid.UUID, chargeSystemId string}
func (s *Service) GetMapSupportedContractDoctors(ctx *titan.Context, filterContracts []contract_model.ContractId) (
	map[contract_model.ContractId]struct {
		DoctorId       uuid.UUID
		ChargeSystemId string
	},
	error,
) {
	now := util.NowUnixMillis(ctx)
	resultGetGroupByContract, err := s.doctorParticipate.GetGroupByContract(ctx, &doctor_participate_api.GetGroupByContractRequest{
		Time:        &now,
		ContractIds: filterContracts,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if resultGetGroupByContract == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Record_Not_Found, "")
	}

	mapContractDoctorId := make(map[contract_model.ContractId]struct {
		DoctorId       uuid.UUID
		ChargeSystemId string
	})
	for _, doctorContract := range resultGetGroupByContract.DoctorParticipateContracts {
		if _, alreadyMap := mapContractDoctorId[doctorContract.ContractID]; !alreadyMap && len(doctorContract.Doctors) > 0 {
			doctorId := *doctorContract.Doctors[0].DoctorId
			chargeSystemId := doctorContract.Doctors[0].ChargeSystemId
			contractId := doctorContract.ContractID
			mapContractDoctorId[contractId] = struct {
				DoctorId       uuid.UUID
				ChargeSystemId string
			}{DoctorId: doctorId, ChargeSystemId: chargeSystemId}
		}
	}
	return mapContractDoctorId, nil
}

// CreateFavParticipation create fav participation
// requestCreateParticipation:
//
//	 Supported Statuses: Requested, Active
//		IsApplyForGroup: true currently only support for Requested and Active status.
//			The func will create participations which are:
//				In the same group of contract Id and have doctor participation.
//				the current status is not equal to passed status.
//
// return
//
//	err :
//		If contract is not fav
//		If passed status is not supported
//	list created participations
func (s *Service) CreateFavParticipation(ctx *titan.Context,
	request CreateParticipation,
) ([]patient_participation_repo.PatientParticipation, error) {
	contract := s.contractService.GetContractDetailById(request.ContractId)
	if contract == nil || contract.GetContractType().TYP != contract_model.VertragtypSimpleTyp_FACHAERZTLICHE_VERSORGUNG {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Data_Invalid)
	}
	if request.IsApplyForGroup != nil && *request.IsApplyForGroup &&
		(request.Status != patient_participation_repo.PatientParticipationStatus_Requested &&
			request.Status != patient_participation_repo.PatientParticipationStatus_Active) {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Data_Invalid)
	}
	request.ContractType = contract_model.ContractType(contract.GetContractType().TYP)
	participations, err := s.createParticipations(ctx, request)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(participations) == 0 {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Data_Invalid)
	}

	for _, participation := range participations {
		if request.IsApplyForGroup != nil && *request.IsApplyForGroup {
			createdFavParticipations, err := s.createFavGroupContractParticipation(ctx,
				participation.ContractId,
				participation.PatientId,
				participation.Status,
				participation.StartDate,
			)
			if err != nil {
				return participations, errors.WithStack(err)
			}
			participations = append(participations, createdFavParticipations...)
		}
	}

	// Notification is now handled by createParticipations via defer
	return participations, nil
}

func (s *Service) TreatAsDeputy(ctx *titan.Context, patientId uuid.UUID, contractId string, applyFavContractGroup bool) error {
	patientProfile, err := s.patientProfileRepos.GetPatientProfileByPatientId(ctx, patientId)
	if err != nil {
		return errors.WithStack(err)
	}

	insuranceData := patientProfile.PatientInfo.GetActiveInsurance()
	if insuranceData == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_NotActive_InsuranceInfo, "")
	}

	participationsGroup, err := s.getValidParticipationsByContractId(ctx, patientId, contractId, insuranceData.InsuranceNumber, insuranceData.IkNumber, applyFavContractGroup)
	if err != nil {
		return errors.WithStack(err)
	}
	now := util.NowUnixMillis(ctx)

	for i := 0; i < len(participationsGroup); i++ {
		participationsGroup[i].DoctorFunctionType = patient_participation_repo.DoctorFunctionType_Deputy
		participationsGroup[i].UpdateDate = &now
		participationsGroup[i].UpdatedBy = ctx.UserInfo().UserUUID()
	}
	updatedParticipations, err := s.repo.UpdateMany(ctx, participationsGroup)
	if err != nil {
		return errors.WithStack(err)
	}
	if len(updatedParticipations) > 0 {
		errNotification := s.NotifyPatientParticipationsChanged(ctx, pkg_util.GetStringValue(insuranceData.InsuranceNumber), updatedParticipations)
		if errNotification != nil {
			ctx.Logger().Error(errors.WithStack(errNotification).Error())
		}
	}
	return err
}

func (s *Service) ActiveCustodianTreatment(ctx *titan.Context, patientId uuid.UUID, contractId string, applyFavContractGroup bool) error {
	patientProfile, err := s.patientProfileRepos.GetPatientProfileByPatientId(ctx, patientId)
	if err != nil {
		return errors.WithStack(err)
	}

	insuranceData := patientProfile.PatientInfo.GetActiveInsurance()
	if insuranceData == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_NotActive_InsuranceInfo, "")
	}

	participationsGroup, err := s.getValidParticipationsByContractId(ctx, patientId, contractId, insuranceData.InsuranceNumber, insuranceData.IkNumber, applyFavContractGroup)
	if err != nil {
		return errors.WithStack(err)
	}
	now := util.NowUnixMillis(ctx)

	for i := 0; i < len(participationsGroup); i++ {
		participationsGroup[i].DoctorFunctionType = patient_participation_repo.DoctorFunctionType_Custodian
		participationsGroup[i].UpdateDate = &now
		participationsGroup[i].UpdatedBy = ctx.UserInfo().UserUUID()
	}
	updatedParticipations, err := s.repo.UpdateMany(ctx, participationsGroup)
	if err != nil {
		return errors.WithStack(err)
	}
	if len(updatedParticipations) > 0 {
		errNotification := s.NotifyPatientParticipationsChanged(ctx, pkg_util.GetStringValue(insuranceData.InsuranceNumber), updatedParticipations)
		if errNotification != nil {
			ctx.Logger().Error(errors.WithStack(errNotification).Error())
		}
	}
	return err
}
