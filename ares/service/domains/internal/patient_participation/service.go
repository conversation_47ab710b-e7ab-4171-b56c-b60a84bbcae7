package patient_participation

import (
	"fmt"
	"strconv"
	"time"

	model_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/doctor_participate"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"emperror.dev/errors"
	"git.tutum.dev/medi/tutum/ares/app/mvz/patient_profile"
	"git.tutum.dev/medi/tutum/ares/pkg/operator"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"

	hpm_rest_client "git.tutum.dev/medi/tutum/ares/pkg/hpm_rest/client"
	"git.tutum.dev/medi/tutum/ares/pkg/hpm_service"
	"git.tutum.dev/medi/tutum/ares/pkg/hpm_service/hpm_next"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	contract_resource "git.tutum.dev/medi/tutum/ares/service/contract/contract"
	profile_service "git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	"git.tutum.dev/medi/tutum/ares/service/domains/internal/doctor_participate"
	employee_profile_service "git.tutum.dev/medi/tutum/ares/service/domains/internal/profile/employee"
	patient_participation_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_participation"
	employeeRepo "git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee"
	"git.tutum.dev/medi/tutum/pkg/slice"
	pkg_util "git.tutum.dev/medi/tutum/pkg/util"
	"github.com/submodule-org/submodule.go/v2"

	bsnr_service "git.tutum.dev/medi/tutum/ares/service/bsnr"
	cal_service "git.tutum.dev/medi/tutum/ares/service/cal"
	contract_model "git.tutum.dev/medi/tutum/ares/service/contract/contract/model"
	enrollment_service_api "git.tutum.dev/medi/tutum/ares/service/domains/api/enrollment"
	patient_participation_service "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_participation"
	"github.com/google/uuid"
	"gitlab.com/silenteer-oss/hestia/infrastructure/util"
	"gitlab.com/silenteer-oss/titan"
)

type Service struct {
	repo                      *Repository
	notifier                  *patient_participation_service.PatientParticipationNotifier
	hpmNextService            hpm_service.HpmNextService
	hpmRestService            hpm_rest_client.ServiceRest
	doctorParticipate         *doctor_participate.Service
	employeeProfileService    *employee_profile_service.ProfileService
	contractService           *contract_resource.Service
	patientProfileRepos       *patient_profile.PatientProfileRepository
	notifierPatientEnrollment *enrollment_service_api.EnrollmentNotifier
	CALService                *cal_service.CALService
	bsnrService               *bsnr_service.BSNRService
}

func (s *Service) HandleEventInsuranceInformationChange(ctx *titan.Context, request profile_service.EventHealthInsuranceChange) error {
	supportedContracts, err := s.contractService.FindContractsByIkNumber(ctx, request.IkNumber)
	if err != nil {
		return errors.WithStack(err)
	}
	supportedContractIds := make(map[contract_model.ContractId]struct{})
	for _, contract := range supportedContracts {
		supportedContractIds[contract.Id] = struct{}{}
	}
	currentDate := util.UnixMillis(util.Now(ctx))
	participations, err := s.repo.FindByFilter(ctx, patient_participation_repo.Filter{
		Statuses: []patient_participation_repo.PatientParticipationStatus{
			patient_participation_repo.PatientParticipationStatus_Active,
			patient_participation_repo.PatientParticipationStatus_Requested,
		},
		Time:                  &currentDate,
		HashedInsuranceNumber: &request.InsuranceNumber,
	})
	if err != nil {
		return errors.WithStack(err)
	}

	terminateDate := util.UnixMillis(util.Now(ctx).Add(-24 * time.Hour))

	for _, participation := range participations {
		if _, ok := supportedContractIds[participation.ContractId]; !ok {
			if participation.Status == patient_participation_repo.PatientParticipationStatus_Requested {
				err := s.CancelPatientParticipation(ctx, &participation)
				if err != nil {
					return errors.WithStack(err)
				}
			} else {
				err := s.terminateParticipation(ctx, &participation, terminateDate)
				if err != nil {
					return errors.WithStack(err)
				}
			}
		}
	}
	return nil
}

const ServiceErrorMessage = "PatientParticipation"

/***
 * Support to get patient participations for Hzv/Fav
 */
func (s *Service) GetPatientParticipation(ctx *titan.Context, request patient_participation_service.GetPatientParticipationRequest) (*patient_participation_service.GetPatientParticipationResponse, error) {
	participationGroups, err := s.getListValidPatientParticipations(ctx, patient_participation_repo.Filter{
		PatientId: &request.PatientId,
		Time:      &request.CheckDate,
		Statuses: []patient_participation_repo.PatientParticipationStatus{
			patient_participation_repo.PatientParticipationStatus_Active,
			patient_participation_repo.PatientParticipationStatus_Requested,
			patient_participation_repo.PatientParticipationStatus_Terminated,
			patient_participation_repo.PatientParticipationStatus_Rejected,
			patient_participation_repo.PatientParticipationStatus_Cancelled,
		},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	response := patient_participation_service.GetPatientParticipationResponse{
		Participations: make([]patient_participation_service.PatientParticipation, 0, len(participationGroups)),
	}

	for _, group := range participationGroups {
		participations := group.GetParticipations()
		for _, participation := range participations {
			response.Participations = append(response.Participations, ToResponse(participation))
		}
	}
	return &response, nil
}

func (s *Service) GetPatientParticipationForPTVImport(ctx *titan.Context, request patient_participation_service.GetPatientParticipationForPTVImportRequest) (*patient_participation_service.GetPatientParticipationForPTVImportResponse, error) {
	participations, err := s.repo.FindByFilter(ctx, patient_participation_repo.Filter{
		Statuses: []patient_participation_repo.PatientParticipationStatus{
			patient_participation_repo.PatientParticipationStatus_Active,
			patient_participation_repo.PatientParticipationStatus_Requested,
			patient_participation_repo.PatientParticipationStatus_Terminated,
			patient_participation_repo.PatientParticipationStatus_Rejected,
			patient_participation_repo.PatientParticipationStatus_Cancelled,
		},
		ContractIds: []contract_model.ContractId{request.ContractId},
		DoctorId:    &request.DoctorId,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	ptvImportPatientParticipations := make([]patient_participation_service.PatientParticipationPTVImport, 0, len(participations))
	for _, participation := range participations {
		ptvImportPatientParticipations = append(ptvImportPatientParticipations, patient_participation_service.PatientParticipationPTVImport{
			StartDate:       participation.StartDate,
			EndDate:         participation.EndDate,
			Status:          patient_participation_service.PatientParticipationStatus(participation.Status),
			InsuranceNumber: participation.HashedInsuranceNumber,
			IkNumber:        int64(participation.IkNumber),
			PatientId:       pkg_util.NewPointer(participation.PatientId),
			PpId:            participation.Id,
		})
	}
	return &patient_participation_service.GetPatientParticipationForPTVImportResponse{ParticipationPTVImports: ptvImportPatientParticipations}, nil
}

// GetCurrentActivatingAndRequestedCustodianParticipations on Patient Participate DB
func (s *Service) GetCurrentActivatingAndRequestedCustodianParticipations(ctx *titan.Context, insuranceNumber string, patientId uuid.UUID, contractType contract_model.ContractType) (patientParticipations []patient_participation_repo.PatientParticipation, err error) {
	hashedInsuranceNumber := util.Hash(insuranceNumber, "")
	activeStatuses := []patient_participation_repo.PatientParticipationStatus{
		patient_participation_repo.PatientParticipationStatus_Active,
		patient_participation_repo.PatientParticipationStatus_Requested,
		patient_participation_repo.PatientParticipationStatus_Terminated,
	}
	now := util.NowUnixMillis(ctx)

	result, err := s.repo.FindByFilter(ctx, patient_participation_repo.Filter{
		Statuses:              activeStatuses,
		HashedInsuranceNumber: &hashedInsuranceNumber,
		Time:                  &now,
		ContractType:          &contractType,
		PatientId:             &patientId,
	})
	if err != nil {
		return patientParticipations, errors.WithStack(err)
	}
	for _, participation := range result {
		if participation.IsValidNow() && participation.DoctorFunctionType == patient_participation_repo.DoctorFunctionType_Custodian {
			patientParticipations = append(patientParticipations, participation)
		}
	}
	return patientParticipations, nil
}

// FindLatestValidPatientParticipation Get latest with ignore cancelled case.
// if the result is not found,nil will be returned.
func (s *Service) FindLatestValidPatientParticipation(ctx *titan.Context, patientId *uuid.UUID, insuranceNumber string, contractId contract_model.ContractId) (*patient_participation_repo.PatientParticipation, error) {
	hashedInsuranceNumber := util.Hash(insuranceNumber, "")
	participations, err := s.repo.GetLatestDistinctValidContracts(ctx, patient_participation_repo.Filter{
		HashedInsuranceNumber: &hashedInsuranceNumber,
		ContractIds:           []contract_model.ContractId{contractId},
		PatientId:             patientId,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(participations) > 0 {
		return participations[0].PatientParticipation, nil
	}
	return nil, nil
}

// GetListValidPatientParticipations
// contractType filter by contract type if it's not nil
// ContractIds filter by contractIds if it's not empty
// return list of latest participation with status are not cancelled, grouped by contractId
// order by desc createdDate
// patientParticipation: the first result of participation.
// previousParticipation: the second result of participation if have.
func (s *Service) GetListValidPatientParticipations(ctx *titan.Context,
	insuranceNumber *string,
	contractType *contract_model.ContractType,
	patientId uuid.UUID,
) ([]*patient_participation_repo.PatientParticipationDistinctGroup, error) {
	hashedInsuranceNumber := util.Hash(pkg_util.GetStringValue(insuranceNumber), "")
	return s.getListValidPatientParticipations(ctx, patient_participation_repo.Filter{
		HashedInsuranceNumber: &hashedInsuranceNumber,
		ContractType:          contractType,
		PatientId:             &patientId,
	})
}

func (s *Service) getListValidPatientParticipations(ctx *titan.Context,
	filter patient_participation_repo.Filter,
) ([]*patient_participation_repo.PatientParticipationDistinctGroup, error) {
	return s.repo.GetLatestDistinctValidContracts(ctx, filter)
}

func (s *Service) FindPatientParticipations(ctx *titan.Context, ids []uuid.UUID) ([]patient_participation_repo.PatientParticipation, error) {
	return s.repo.FindByIds(ctx, ids)
}

// CancelParticipationByContractId cancel participations
// applyFavContractGroup: true
//
//	Will return error if contract is not FAV.
//	Will cancel all participations which are in the same fav group regardless their current statuses.
//
// Will send notify to participation changed for changed participations.
func (s *Service) CancelParticipationByContractId(ctx *titan.Context, patientId uuid.UUID, contractId contract_model.ContractId, applyFavContractGroup bool) error {
	patientProfile, err := s.patientProfileRepos.GetPatientProfileByPatientId(ctx, patientId)
	if err != nil {
		return errors.WithStack(err)
	}

	insuranceData := patientProfile.PatientInfo.GetActiveInsurance()
	if insuranceData == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_NotActive_InsuranceInfo, "")
	}

	participationsGroup, err := s.getValidParticipationsByContractId(ctx, patientId, contractId, insuranceData.InsuranceNumber, insuranceData.IkNumber, applyFavContractGroup)
	if err != nil {
		return errors.WithStack(err)
	}
	now := util.NowUnixMillis(ctx)

	for i := 0; i < len(participationsGroup); i++ {
		participationsGroup[i].Status = patient_participation_repo.PatientParticipationStatus_Cancelled
		participationsGroup[i].UpdateDate = &now
		participationsGroup[i].UpdatedBy = ctx.UserInfo().UserUUID()
	}
	updatedParticipations, err := s.repo.UpdateMany(ctx, participationsGroup)
	if err != nil {
		return errors.WithStack(err)
	}
	if len(updatedParticipations) > 0 {
		errNotification := s.NotifyPatientParticipationsChanged(ctx, pkg_util.GetStringValue(insuranceData.InsuranceNumber), updatedParticipations)
		if errNotification != nil {
			ctx.Logger().Error(errors.WithStack(errNotification).Error())
		}
		err = s.notifierPatientEnrollment.NotifyPatientEnrollmentChange(ctx, &enrollment_service_api.EventPatientEnrollmentChange{
			PatientId:   &patientId,
			ContractIds: []string{contractId},
			Status:      enrollment_service_api.PatientEnrollmentStatus_Cancelled,
		})
		if err != nil {
			ctx.Logger().Error(errors.WithStack(errNotification).Error())
		}
	}
	return err
}

func (s *Service) CancelPatientParticipation(ctx *titan.Context, patientParticipation *patient_participation_repo.PatientParticipation) error {
	now := util.NowUnixMillis(ctx)
	patientParticipation.Status = patient_participation_repo.PatientParticipationStatus_Cancelled
	patientParticipation.UpdateDate = &now
	patientParticipation.UpdatedBy = ctx.UserInfo().UserUUID()
	_, err := s.updateAndNotify(ctx, patientParticipation)
	return err
}

// ChangeFavGroupContractStartDate change start date of fav group contracts.
// return err when contract is not fav contract.
// func will notify when there are changed participations.
func (s *Service) ChangeFavGroupContractStartDate(ctx *titan.Context,
	patientId uuid.UUID,
	contractId contract_model.ContractId,
	startDate int64,
) error {
	contract := s.contractService.GetContractDetailById(contractId)
	if contract == nil || !contract.IsFavContract() {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Data_Invalid)
	}
	patientProfile, err := s.patientProfileRepos.GetPatientProfileByPatientId(ctx, patientId)
	if err != nil {
		return errors.WithStack(err)
	}

	insuranceData := patientProfile.PatientInfo.GetActiveInsurance()
	if insuranceData == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_NotActive_InsuranceInfo, "")
	}
	now := util.NowUnixMillis(ctx)
	groupParticipations, err := s.getValidParticipationsByContractId(ctx, patientId, contractId, insuranceData.InsuranceNumber, insuranceData.IkNumber, true)
	if err != nil {
		return errors.WithStack(err)
	}
	for i := 0; i < len(groupParticipations); i++ {
		participation := &groupParticipations[i]
		if participation.EndDate != nil && *participation.EndDate <= startDate {
			participation.Status = patient_participation_repo.PatientParticipationStatus_Cancelled
			participation.EndDate = nil
		}
		participation.StartDate = &startDate
		participation.UpdateDate = &now
		participation.UpdatedBy = ctx.UserInfo().UserUUID()
	}
	results, err := s.repo.UpdateMany(ctx, groupParticipations)
	if err == nil {
		errNotification := s.NotifyPatientParticipationsChanged(ctx, pkg_util.GetStringValue(insuranceData.InsuranceNumber), results)
		if errNotification != nil {
			_ = ctx.Logger().WithMessage(errNotification, "Failed to notify patient participations changed")
		}
	}
	return err
}

func (s *Service) ChangeParticipationsStartDate(ctx *titan.Context,
	participationIds []uuid.UUID,
	startDate int64,
	applyContractGroup bool,
) error {
	now := util.NowUnixMillis(ctx)
	if startDate > now {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Data_Invalid)
	}
	participations, err := s.repo.GetByIds(ctx, participationIds)
	if err != nil {
		return errors.WithStack(err)
	}
	if len(participations) == 0 {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Record_Not_Found, "")
	}

	for _, participation := range participations {
		insuranceNumber := participation.HashedInsuranceNumber
		contract := s.contractService.GetContractDetailById(participation.ContractId)
		if contract == nil {
			return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Contract_Not_Found, participation.ContractId)
		}
		if applyContractGroup && !contract.IsFavContract() {
			return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Data_Invalid)
		}
		groupParticipations, err := s.getValidParticipationsByContractId(ctx, participation.PatientId, contract.Id, &insuranceNumber, participation.IkNumber, applyContractGroup)
		if err != nil {
			return errors.WithStack(err)
		}
		for i := 0; i < len(groupParticipations); i++ {
			participation := &groupParticipations[i]
			if participation.EndDate != nil && *participation.EndDate <= startDate {
				participation.Status = patient_participation_repo.PatientParticipationStatus_Cancelled
				participation.EndDate = nil
			}
			participation.StartDate = &startDate
			participation.UpdateDate = &now
			participation.UpdatedBy = ctx.UserInfo().UserUUID()
		}
		results, err := s.repo.UpdateMany(ctx, groupParticipations)
		if err == nil {
			errNotification := s.NotifyPatientParticipationsChanged(ctx, insuranceNumber, results)
			if errNotification != nil {
				ctx.Logger().Error(errors.WithStack(errNotification).Error())
			}
		}
		if err != nil {
			return err
		}
	}
	return nil
}

// TerminateParticipation terminate participation and notify to clients about changed participations.
// if applyGroupContract = true, the function will terminate all contracts regardless status condition.
func (s *Service) TerminateParticipationByContractId(ctx *titan.Context,
	patientId uuid.UUID,
	contractId contract_model.ContractId,
	endDate int64,
	applyGroupContract bool,
) error {
	patientProfile, err := s.patientProfileRepos.GetPatientProfileByPatientId(ctx, patientId)
	if err != nil {
		return errors.WithStack(err)
	}

	insuranceData := patientProfile.PatientInfo.GetActiveInsurance()
	if insuranceData == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_NotActive_InsuranceInfo, "")
	}
	updatedParticipations, err := s.terminateParticipationByContractId(ctx, patientId, insuranceData.InsuranceNumber, insuranceData.IkNumber, contractId, endDate, applyGroupContract)
	if err != nil {
		return errors.WithStack(err)
	}
	err = s.NotifyPatientParticipationsChanged(ctx, pkg_util.GetStringValue(insuranceData.InsuranceNumber), updatedParticipations)
	if err != nil {
		ctx.Logger().Error(err.Error())
	}
	return nil
}

func (s *Service) terminateParticipationByContractId(
	ctx *titan.Context,
	patientId uuid.UUID,
	insuranceNumber *string,
	ikNumber int32,
	contractId contract_model.ContractId,
	endDate int64,
	applyGroupContract bool,
) (terminatedParticipations []patient_participation_repo.PatientParticipation, err error) {
	updatingParticipations, err := s.getValidParticipationsByContractId(ctx, patientId, contractId, insuranceNumber, ikNumber, applyGroupContract)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	for i := 0; i < len(updatingParticipations); i++ {
		s.updateTerminateStatus(ctx, &updatingParticipations[i], endDate)
	}
	return s.repo.UpdateMany(ctx, updatingParticipations)
}

// getValidParticipationsByContractId get list of valid participations
// participations: list same group, if getGroup = true and contract is FAV, participations including contractId's participation if have, empty if not found
// err: if samgGroup = true and contract is not FAV contract.
func (s *Service) getValidParticipationsByContractId(
	ctx *titan.Context,
	patientId uuid.UUID,
	contractId contract_model.ContractId,
	insuranceNumber *string,
	ikNumber int32,
	getGroup bool,
) (
	participations []patient_participation_repo.PatientParticipation,
	err error,
) {
	contract := s.contractService.GetContractDetailById(contractId)
	if getGroup && !contract.IsFavContract() {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Data_Invalid)
	}
	sameGroupContractIds := []contract_model.ContractId{contractId}

	if getGroup {
		if ikNumber == 0 {
			return nil, errors.New("iknumber is required when get contracts by group")
		}
		// https://www.notion.so/silenteer/RFD-Online-enrollment-FaV-enrollment-ea436af7acb2469487ff27c04d10b1a9?d=e8d55095-ec76-4d72-9463-e4f199044fe7#1368e2e65d4e41b7ae7af36dc97533ee
		favContractType := contract_model.ContractType_SpecialistCare
		supportedContracts, err := s.GetSupportedContracts(ctx, ikNumber, &favContractType)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		scannedContractIds := make(map[contract_model.ContractId]struct{})
		scannedContractIds[contractId] = struct{}{}
		for _, contract := range supportedContracts {
			id := contract.Id
			if _, scanned := scannedContractIds[id]; !scanned {
				sameGroupContractIds = append(sameGroupContractIds, id)
				scannedContractIds[id] = struct{}{}
			}
		}
		// To avoid missed contract when it does not support current ik number but before it supported.
		contractGroupIds := contract.GetFavPatientParticipationContractGroup()
		for _, sameId := range contractGroupIds {
			id := sameId
			if _, scanned := scannedContractIds[id]; !scanned {
				sameGroupContractIds = append(sameGroupContractIds, id)
				scannedContractIds[id] = struct{}{}
			}
		}
	}
	hashedInsuranceNumber := util.Hash(pkg_util.GetStringValue(insuranceNumber), "")

	participationGroups, err := s.repo.GetLatestDistinctValidContracts(ctx, patient_participation_repo.Filter{
		HashedInsuranceNumber: &hashedInsuranceNumber,
		ContractIds:           sameGroupContractIds,
		PatientId:             &patientId,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	for _, group := range participationGroups {
		participations = append(participations, group.GetParticipations()...)
	}
	return participations, err
}

// updateTerminateStatus update terminate status of struct patientParticipation
// if patientParticipation is nil, do nothing
// if the start date of patient participation is after or equal to endDate, func will cancel this participation.
func (*Service) updateTerminateStatus(ctx *titan.Context, patientParticipation *patient_participation_repo.PatientParticipation, endDate int64) {
	if patientParticipation == nil {
		return
	}
	if patientParticipation.Status != patient_participation_repo.PatientParticipationStatus_Terminated {
		patientParticipation.TerminateHistory = &patient_participation_repo.History{
			Status:    patientParticipation.Status,
			StartDate: patientParticipation.StartDate,
			EndDate:   patientParticipation.EndDate,
		}
	}
	patientParticipation.Status = patient_participation_repo.PatientParticipationStatus_Terminated
	patientParticipation.EndDate = &endDate

	now := util.NowUnixMillis(ctx)
	patientParticipation.UpdateDate = &now
	patientParticipation.UpdatedBy = ctx.UserInfo().UserUUID()
}

func (s *Service) terminateParticipation(ctx *titan.Context,
	patientParticipation *patient_participation_repo.PatientParticipation,
	endDate int64,
) error {
	s.updateTerminateStatus(ctx, patientParticipation, endDate)
	_, err := s.updateAndNotify(ctx, patientParticipation)
	return err
}

// UndoTerminatedParticipation set participation status from history terminate.
// if applyContractGroup: true
//
//	return err if contract is not FAV
//	undo all terminated contracts which are in the same group with passed contract id.
//
// This func will send notification for changed participations.
func (s *Service) UndoTerminatedParticipation(ctx *titan.Context, patientId uuid.UUID, contractId contract_model.ContractId, applyContractGroup bool) error {
	patientProfile, err := s.patientProfileRepos.GetPatientProfileByPatientId(ctx, patientId)
	if err != nil {
		return errors.WithStack(err)
	}

	insuranceData := patientProfile.PatientInfo.GetActiveInsurance()
	if insuranceData == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_NotActive_InsuranceInfo, "")
	}

	participations, err := s.getValidParticipationsByContractId(ctx, patientId, contractId, insuranceData.InsuranceNumber, insuranceData.IkNumber, applyContractGroup)
	if err != nil {
		return errors.WithStack(err)
	}
	updatingParticipations := make([]patient_participation_repo.PatientParticipation, len(participations))
	now := util.NowUnixMillis(ctx)
	for i := 0; i < len(participations); i++ {
		if participations[i].Status != patient_participation_repo.PatientParticipationStatus_Terminated || participations[i].TerminateHistory == nil {
			continue
		}

		participation := participations[i]
		participation.Status = participation.TerminateHistory.Status
		participation.StartDate = participation.TerminateHistory.StartDate
		participation.EndDate = participation.TerminateHistory.EndDate
		participation.TerminateHistory = nil
		participation.UpdateDate = &now
		participation.UpdatedBy = ctx.UserInfo().UserUUID()
		updatingParticipations = append(updatingParticipations, participation)
	}
	if len(updatingParticipations) > 0 {
		result, err := s.repo.UpdateMany(ctx, updatingParticipations)
		if err != nil {
			return errors.WithStack(err)
		}
		if len(result) > 0 {
			errNotification := s.NotifyPatientParticipationsChanged(ctx, pkg_util.GetStringValue(insuranceData.InsuranceNumber), result)
			if errNotification != nil {
				ctx.Logger().Error(errors.WithStack(errNotification).Error())
			}
		}
	}
	return nil
}

// TODO: unuse
// func (s *Service) undoTerminateParticipation(ctx *titan.Context, patientParticipation *patient_participation_repo.PatientParticipation) error {
// 	if patientParticipation.TerminateHistory == nil {
// 		return titan.NewCommonException(ServiceErrorMessage, fmt.Errorf("participation's history is not found"))
// 	}
// 	if patientParticipation.Status != patient_participation_repo.PatientParticipationStatus_Terminated {
// 		return titan.NewCommonException(ServiceErrorMessage, fmt.Errorf("participation is not terminated"))
// 	}
// 	patientParticipation.StartDate = patientParticipation.TerminateHistory.StartDate
// 	patientParticipation.EndDate = patientParticipation.TerminateHistory.EndDate
// 	patientParticipation.Status = patientParticipation.TerminateHistory.Status
// 	patientParticipation.TerminateHistory = nil
// 	patientParticipation.UpdatedBy = ctx.UserInfo().UserUUID()
// 	now := util.NowUnixMillis(ctx)
// 	patientParticipation.UpdateDate = &now
// 	_, err := s.repo.Update(ctx, *patientParticipation)
// 	return err
// }

// GetLatestValidHZVPatientParticipation
// return err if there is not found.
func (s *Service) GetLatestValidPatientParticipation(ctx *titan.Context, patientId *uuid.UUID, insuranceNumber string, contractId contract_model.ContractId) (*patient_participation_repo.PatientParticipation, error) {
	result, err := s.FindLatestValidPatientParticipation(ctx, patientId, insuranceNumber, contractId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return result, nil
}

func (s *Service) GetDoctorsCanTreatAsDeputy(ctx *titan.Context, request patient_participation_service.GetDoctorsCanTreatAsDeputyRequest) (*patient_participation_service.GetDoctorsCanTreatAsDeputyResponse, error) {
	employee, err := s.employeeProfileService.GetEmployeeProfileById(ctx, &profile_service.EmployeeProfileGetRequest{
		OriginalId: &request.DoctorId,
	})
	if err != nil {
		return nil, err
	}
	doctorParticipateResponse, err := s.doctorParticipate.GetDoctorsByContract(ctx, request.ContractId)
	if err != nil {
		return nil, err
	}
	doctorProfiles, err := s.employeeProfileService.FindEmployeeProfileByIds(ctx, slice.Map(doctorParticipateResponse, func(p model_repo.DoctorParticipate) uuid.UUID {
		return *p.DoctorId
	}))
	if err != nil {
		return nil, err
	}
	if doctorProfiles == nil {
		return nil, nil
	}

	doctorProfilesInSameBsnr := []profile_service.EmployeeProfileResponse{}

	for _, profile := range doctorProfiles {
		if profile.Bsnr == employee.Bsnr {
			doctorProfilesInSameBsnr = append(doctorProfilesInSameBsnr, profile_service.EmployeeProfileResponse{
				Id:             profile.Id,
				FirstName:      profile.FirstName,
				LastName:       profile.LastName,
				Title:          profile.Title,
				IntendWord:     profile.IntendWord,
				Salutation:     profile.Salutation,
				AdditionalName: profile.AdditionalName,
			})
		}
	}

	return &patient_participation_service.GetDoctorsCanTreatAsDeputyResponse{
		Profiles: doctorProfilesInSameBsnr,
	}, nil
}

// CreateDeputyParticipation status will be active.
func (s *Service) CreateDeputyParticipation(ctx *titan.Context,
	doctorId *uuid.UUID,
	patientId uuid.UUID,
	ikNumber int32,
	insuranceNumber string,
	contractId string,
	contractType contract_model.ContractType,
	startDate *int64,
	endDate *int64,
	status patient_participation_repo.PatientParticipationStatus,
) ([]patient_participation_repo.PatientParticipation, error) {
	if status == patient_participation_repo.PatientParticipationStatus_Active && startDate != nil {
		endDate := util.UnixMillis(time.Unix(0, int64(time.Millisecond)*(*startDate)).Add(-time.Hour * 24))
		err := s.TerminateActiveHZVParticipationByTime(ctx, insuranceNumber, *startDate, endDate)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	participations, err := s.createParticipations(ctx, CreateParticipation{
		PatientId:          patientId,
		IkNumber:           ikNumber,
		InsuranceNumber:    insuranceNumber,
		ContractId:         contractId,
		ContractType:       contractType,
		DoctorId:           *doctorId,
		StartDate:          startDate,
		EndDate:            endDate,
		IsTransmittedHpm:   false,
		IsChangingDoctor:   false,
		IVPFormPrinted:     false,
		Status:             status,
		DoctorFunctionType: patient_participation_repo.DoctorFunctionType_Deputy,
		TeId:               "",
		EnrollmentId:       nil,
	})
	// Notification is now handled by createParticipations via defer
	return participations, err
}

func (s *Service) createParticipations(ctx *titan.Context, request CreateParticipation) ([]patient_participation_repo.PatientParticipation, error) {
	now := util.NowUnixMillis(ctx)
	chargeSystemIds := []string{}
	if request.DoctorId != uuid.Nil {
		doctorParticipations, err := s.doctorParticipate.GetDoctorParticipatesByDoctorIDContractIDWithTimeRange(ctx, request.DoctorId, request.ContractId, now)
		if err != nil {
			return nil, err
		}
		chargeSystemIds = slice.Map(doctorParticipations, func(dp model_repo.DoctorParticipate) string {
			return dp.ChargeSystemId
		})
		if len(chargeSystemIds) == 0 {
			chargeSystemIds = append(chargeSystemIds, request.ContractId)
		}
	}

	// if status is REQUESTED set startdate to now same as updateDate, same as PTV import, start date is the request date
	if request.Status == patient_participation_repo.PatientParticipationStatus_Requested {
		if request.StartDate == nil {
			request.StartDate = &now
		}
	}

	participation := patient_participation_repo.PatientParticipation{
		StartDate:             request.StartDate,
		EndDate:               request.EndDate,
		DoctorId:              &request.DoctorId,
		PatientId:             request.PatientId,
		ContractId:            request.ContractId,
		IkNumber:              request.IkNumber,
		HashedInsuranceNumber: request.InsuranceNumber,
		Status:                request.Status,
		TerminateHistory:      nil,
		CreatedDate:           now,
		CreatedBy:             pkg_util.GetPointerValue(ctx.UserInfo().UserUUID()),
		UpdateDate:            &now,
		UpdatedBy:             ctx.UserInfo().UserUUID(),
		ContractType:          request.ContractType,
		DoctorFunctionType:    request.DoctorFunctionType,
		IsChangingDoctor:      request.IsChangingDoctor,
		IsTransmittedHpm:      request.IsTransmittedHpm,
		TeId:                  request.TeId,
		EnrollmentId:          request.EnrollmentId,
		Reason:                request.Reason,
	}

	participations := []patient_participation_repo.PatientParticipation{}
	for _, chargeSystemId := range chargeSystemIds {
		if chargeSystemId == "AOK_BW_IV_P" && !request.IVPFormPrinted {
			continue
		}
		participation.Id = pkg_util.NewUUID()
		participation.ChargeSystemId = chargeSystemId
		participations = append(participations, participation)
	}

	createdParticipations, err := s.repo.CreateMany(ctx, participations)

	// Make sure to notify about participation changes, even if there's an error afterward
	if len(createdParticipations) > 0 {
		defer func() {
			errNotification := s.NotifyPatientParticipationsChanged(ctx, request.InsuranceNumber, createdParticipations)
			if errNotification != nil {
				ctx.Logger().Error(errors.WithStack(errNotification).Error())
			}
		}()
	}

	return createdParticipations, err
}

// ActiveParticipationById return error if startDate is future or participation is not found or isApplyForFavGroup is true but the contract is not FAV
// isApplyForFavGroup true, the function will active for all fav contracts regardless sub contracts' status.
// will send notification changed participations
func (s *Service) ActiveParticipationByIds(ctx *titan.Context,
	participationIds []uuid.UUID,
	startDate int64,
	insuranceNumber string,
	isApplyForFavGroup *bool) (
	[]patient_participation_repo.PatientParticipation,
	error,
) {
	now := util.NowUnixMillis(ctx)
	if startDate > now {
		return nil, titan.NewCommonException(ServiceErrorMessage, fmt.Errorf(patient_participation_repo.ErrorMessageStartDateIsFuture))
	}

	participations, err := s.repo.GetByIds(ctx, participationIds)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	updatedParticipations := make([]patient_participation_repo.PatientParticipation, 0)

	// Activate HzV participations
	hzvParticipations := slice.Filter(participations, func(p patient_participation_repo.PatientParticipation) bool {
		return p.ContractType == contract_model.ContractType_HouseDoctorCare
	})
	updatedHzvParticipations, err := s.ActivateHzvParticipations(ctx, hzvParticipations, startDate, insuranceNumber)
	updatedParticipations = append(updatedParticipations, updatedHzvParticipations...)

	favParticipations := slice.Filter(participations, func(p patient_participation_repo.PatientParticipation) bool {
		return p.ContractType == contract_model.ContractType_SpecialistCare
	})
	for _, participation := range favParticipations {
		if isApplyForFavGroup == nil {
			return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Data_Invalid)
		}
		results, err := s.activateFavContract(ctx, participation.PatientId, participation.ContractId, startDate, nil, *isApplyForFavGroup)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if len(results) > 0 {
			updatedParticipations = append(updatedParticipations, results...)
		}
	}

	// notify contracts changed
	if len(updatedParticipations) > 0 {
		errNotification := s.NotifyPatientParticipationsChanged(ctx, insuranceNumber, updatedParticipations)
		if errNotification != nil {
			ctx.Logger().Error(errors.WithStack(errNotification).Error())
		}
	}
	return updatedParticipations, err
}

func (s *Service) NotifyPatientParticipationsChanged(ctx *titan.Context, insuranceNumber string, updatedParticipations []patient_participation_repo.PatientParticipation) error {
	changedContractIds := make([]contract_model.ContractId, 0, len(updatedParticipations))
	var updatedPatientId uuid.UUID
	for _, participation := range updatedParticipations {
		contractId := participation.ContractId
		changedContractIds = append(changedContractIds, contractId)
		updatedPatientId = participation.PatientId
	}
	return s.notifier.NotifyPatientParticipationChange(ctx,
		&patient_participation_service.EventPatientParticipationChange{
			PatientId:       updatedPatientId,
			ContractIds:     changedContractIds,
			InsuranceNumber: insuranceNumber,
		})
}

func (s *Service) CheckParticipationListFromHpmService(ctx *titan.Context, ikNumber int32, insuranceNumber string, contractIds []string, contractType contract_model.ContractType, checkDate int64, doctorIds []uuid.UUID) (response *hpm_next.CheckParticipationResponse, err error) {
	var doctors []employeeRepo.EmployeeProfile
	if len(doctorIds) == 0 {
		// hack code get 1 doctor profile to call Hpm service with contract.
		supportedDoctors, err := s.doctorParticipate.FindActiveDoctorIdsByContractIds(ctx, contractIds)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if contractType == contract_model.ContractType_SpecialistCare || contractType == contract_model.ContractType_IntegratedCareSpecialist {
			doctors, err = s.employeeProfileService.FindDoctorsHasMediId(ctx, supportedDoctors)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if len(doctors) == 0 {
				doctors, err = s.employeeProfileService.FindDoctorsHasHavgId(ctx, supportedDoctors)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}
		} else {
			doctors, err = s.employeeProfileService.FindDoctorsHasHavgId(ctx, supportedDoctors)
			if err != nil {
				return nil, errors.WithStack(err)
			}
			if len(doctors) == 0 {
				doctors, err = s.employeeProfileService.FindDoctorsHasMediId(ctx, supportedDoctors)
				if err != nil {
					return nil, errors.WithStack(err)
				}
			}
		}
	} else {
		doctors, err = s.employeeProfileService.FindEmployeeByIds(ctx, doctorIds)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	if len(doctors) == 0 {
		return nil, errors.New("Cannot find any doctor to check participation")
	}
	var checkParticipationErr error
	var hpmResponses []*hpm_next.CheckParticipationResponse
	for _, v := range doctors {
		checkReq := &hpm_next.CheckParticipationRequest{
			PatientId:   titan.RandomString(6),
			InsuranceId: insuranceNumber,
			IkNumber:    strconv.FormatInt(int64(ikNumber), 10),
			Doctor:      (*hpm_next.ContractDoctor)(employee_profile_service.ToContractDoctor(&v)),
			ContractIds: contractIds,
			CheckTime:   checkDate,
		}
		hpmConfig, err := s.bsnrService.GetHpmConfig(ctx, v.BsnrId)
		if err != nil {
			checkParticipationErr = err
			continue
		}
		hpmRestService := hpm_rest_client.NewServiceRestWithEndpoint(hpmConfig.Endpoint)
		rs, err := hpmRestService.CheckParticipationList(ctx, checkReq)
		if err != nil {
			checkParticipationErr = err
			continue
		}
		if rs == nil {
			checkParticipationErr = errors.New("response is nil")
			continue
		}
		if rs.Status == hpm_next.ParticipateResultStatus_Ok {
			return rs, nil
		}
		hpmResponses = append(hpmResponses, rs)
	}

	if checkParticipationErr != nil {
		return nil, checkParticipationErr
	}

	if len(hpmResponses) == 0 {
		return nil, nil
	}
	return hpmResponses[0], checkParticipationErr
}

func (s *Service) GetSupportedHzVContracts(ctx *titan.Context, ikNumber int32) (hzvCanEnrollContracts, hzvByFavContract []contract_model.Contract, err error) {
	hzvContractType := pkg_util.NewPointer(contract_model.ContractType_HouseDoctorCare)
	activeHzVContractIds, err := s.doctorParticipate.GetActiveContractIds(ctx, hzvContractType)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	hzvCanEnrollContracts, err = s.contractService.GetSupportedContracts(ctx, activeHzVContractIds, ikNumber, hzvContractType)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	hzvByFavContract, err = s.contractService.GetSupportedContracts(ctx, []string{}, ikNumber, hzvContractType)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	return hzvCanEnrollContracts, hzvByFavContract, nil
}

func (s *Service) GetSupportedFaVContracts(ctx *titan.Context, ikNumber int32) ([]contract_model.Contract, error) {
	byFavDoctor, err := s.getSupportedFavContractsByFavDoctor(ctx, ikNumber)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	byHzvDoctor, err := s.getSupportedFavContractsByHzvDoctor(ctx, ikNumber)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	supportedContracts := append(byFavDoctor, byHzvDoctor...)
	return supportedContracts, nil
}

// GetSupportedContracts
// 1st step will get all active contractIds based on ContractType
// 2nd scan through contracts definition to make sure the iknumber valid on list active contracts
// ContractType can be null which will not check condition contract type on the list contract results.
func (s *Service) GetSupportedContracts(ctx *titan.Context, ikNumber int32, contractType *contract_model.ContractType) (contracts []contract_model.Contract, err error) {
	activeContractIds, err := s.doctorParticipate.GetActiveContractIds(ctx, contractType)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return s.contractService.GetSupportedContracts(ctx, activeContractIds, ikNumber, contractType)
}

// as PTVImport flow begin from Doctor and contract to get PatientParticipation
// so update PatientParticipation is only about StartDate, EndDate and status
// use PP ID to update
func (s *Service) UpdatePatientParticipationForPTVImport(ctx *titan.Context, request patient_participation_service.UpdatePatientParticipationForPTVImportRequest) (*patient_participation_service.CreatePatientParticipationForPTVImportResponse, error) {
	// If PpId is nil, create a new participation record
	if request.PpId == nil {
		contract := s.contractService.GetContractDetailById(request.ContractId)
		if contract == nil {
			err := pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Contract_Not_Found, request.ContractId)
			return nil, err
		}
		createRequest := &CreateParticipation{
			PatientId:          request.PatientId,
			IkNumber:           request.IkNumber,
			InsuranceNumber:    request.InsuranceNumber,
			ContractId:         request.ContractId,
			ContractType:       contract_model.ContractType(contract.GetContractType().TYP),
			DoctorId:           request.DoctorId,
			Status:             patient_participation_repo.PatientParticipationStatus(request.Status),
			StartDate:          request.StartDate,
			EndDate:            request.EndDate,
			IsChangingDoctor:   false,
			IsTransmittedHpm:   true,
			IsApplyForGroup:    nil,
			TeId:               "",
			Reason:             request.Reason,
			DoctorFunctionType: patient_participation_repo.DoctorFunctionType_Custodian,
			// Upda
		}

		var rs []patient_participation_repo.PatientParticipation
		var err error
		if contract.IsHzvContract() {
			rs, err = s.CreateHzvCustodianParticipation(ctx, *createRequest)
			if err != nil {
				return nil, err
			}
		} else if contract.IsFavContract() {
			rs, err = s.CreateFavParticipation(ctx, *createRequest)
			if err != nil {
				return nil, err
			}
		} else {
			return nil, fmt.Errorf("contract type %s is not supported", contract.GetContractType().TYP)
		}
		errNotification := s.NotifyPatientParticipationsChanged(ctx, createRequest.InsuranceNumber, rs)
		if errNotification != nil {
			return nil, errNotification
		}
		rsResponse := slice.Map(rs, func(p patient_participation_repo.PatientParticipation) patient_participation_service.PatientParticipation {
			return ToResponse(p)
		})
		return &patient_participation_service.CreatePatientParticipationForPTVImportResponse{
			Participations: rsResponse,
		}, nil
	}

	// If PpId exists, update existing participation
	patientParticipation, err := s.repo.GetById(ctx, *request.PpId)
	if err != nil {
		return nil, errors.WithMessage(err, "PatientParticipation not found")
	}
	now := util.NowUnixMillis(ctx)
	patientParticipation.StartDate = request.StartDate
	patientParticipation.EndDate = request.EndDate
	patientParticipation.Status = patient_participation_repo.PatientParticipationStatus(request.Status)
	patientParticipation.UpdateDate = &now
	patientParticipation.UpdatedBy = ctx.UserInfo().UserUUID()
	patientParticipation.Reason = request.Reason

	patientParticipationResult, err := s.updateAndNotify(ctx, patientParticipation)
	if err != nil {
		return nil, err
	}

	return &patient_participation_service.CreatePatientParticipationForPTVImportResponse{
		Participations: []patient_participation_service.PatientParticipation{
			ToResponse(*patientParticipationResult),
		},
	}, nil
}

// createRequestedGroupFavParticipation create requested participations in same group of contract.
// if the sub contract already had Active and Requested, the func will not create new requested contract.
func (s *Service) createRequestedGroupFavParticipation(ctx *titan.Context,
	_ contract_model.ContractId,
	patientId uuid.UUID,
) ([]patient_participation_repo.PatientParticipation, error) {
	patientProfile, err := s.patientProfileRepos.GetPatientProfileByPatientId(ctx, patientId)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	insuranceData := patientProfile.PatientInfo.GetActiveInsurance()
	if insuranceData == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_NotActive_InsuranceInfo, "")
	}
	requestedStatus := patient_participation_repo.PatientParticipationStatus_Requested

	favContractType := contract_model.ContractType_SpecialistCare
	supportedContracts, err := s.GetSupportedContracts(ctx, insuranceData.IkNumber, &favContractType)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	now := util.NowUnixMillis(ctx)

	contractFilterIds := make([]contract_model.ContractId, 0, len(supportedContracts))
	for _, contract := range supportedContracts {
		newId := contract.Id
		contractFilterIds = append(contractFilterIds, newId)
	}
	defaultDoctorFunctionType := patient_participation_repo.DoctorFunctionType_Custodian
	currentParticipatingContracts, err := s.getListValidPatientParticipations(ctx, patient_participation_repo.Filter{
		HashedInsuranceNumber: insuranceData.InsuranceNumber,
		Time:                  &now,
		ContractIds:           contractFilterIds,
		DoctorFunctionType:    &defaultDoctorFunctionType,
	})
	if err != nil {
		return nil, err
	}

	creatingContractIds := make([]contract_model.ContractId, 0, len(supportedContracts))

	for _, contract := range supportedContracts {
		contractId := contract.Id
		needToCreate := true

		for _, currentContract := range currentParticipatingContracts {
			if currentContract.ContractId == contractId &&
				currentContract.PatientParticipation != nil &&
				(currentContract.PatientParticipation.Status == patient_participation_repo.PatientParticipationStatus_Active ||
					currentContract.PatientParticipation.Status == requestedStatus) {
				needToCreate = false
				break
			}
		}
		if needToCreate {
			creatingContractIds = append(creatingContractIds, contractId)
		}
	}
	if len(creatingContractIds) == 0 {
		return nil, nil
	}

	mapContractDoctorId, err := s.GetMapSupportedContractDoctors(ctx, creatingContractIds)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	creatingParticipations := s.generateNewParticipations(ctx,
		creatingContractIds,
		mapContractDoctorId,
		patientId,
		pkg_util.GetStringValue(insuranceData.InsuranceNumber),
		nil,
		nil,
		insuranceData.IkNumber,
		requestedStatus,
		contract_model.ContractType_SpecialistCare,
		patient_participation_repo.DoctorFunctionType_Custodian,
		false,
		false,
	)
	if len(creatingParticipations) > 0 {
		return s.repo.CreateMany(ctx, creatingParticipations)
	}
	return creatingParticipations, nil
}

var PatientParticipationServiceMod = submodule.Make[*Service](func(
	doctorParticipate *doctor_participate.Service,
	contractService *contract_resource.Service,
	repo *Repository,
	employeeProfileService *employee_profile_service.ProfileService,
	hpmNextService hpm_service.HpmNextService,
	CALService *cal_service.CALService,
	hpmRestService hpm_rest_client.ServiceRest,
	bsnrService *bsnr_service.BSNRService,
) *Service {
	notifier := patient_participation_service.NewPatientParticipationNotifier()
	notifierPatientEnrollment := enrollment_service_api.NewEnrollmentNotifier()

	return &Service{
		repo:                      repo,
		notifier:                  notifier,
		doctorParticipate:         doctorParticipate,
		employeeProfileService:    employeeProfileService,
		contractService:           contractService,
		patientProfileRepos:       patient_profile.NewPatientProfileRepository(),
		notifierPatientEnrollment: notifierPatientEnrollment,
		hpmNextService:            hpmNextService,
		CALService:                CALService,
		hpmRestService:            hpmRestService,
		bsnrService:               bsnrService,
	}
},
	doctor_participate.DoctorParticipateServiceAPIMod,
	contract_resource.ContractServiceMod,
	PatientParticipationRepositoryMod,
	employee_profile_service.ProfileServiceMod,
	hpm_service.HPMNextServiceMod,
	cal_service.CALServiceMod,
	hpm_rest_client.HpmRestServiceMod,
	bsnr_service.BSNRServiceMod,
)

/***
 * Support to get active patient participations for Hzv/Fav
 */
func (s *Service) GetActivePatientParticipation(ctx *titan.Context, request patient_participation_service.GetPatientParticipationRequest) (*patient_participation_service.GetPatientParticipationResponse, error) {
	participations, err := s.getListValidPatientParticipations(ctx, patient_participation_repo.Filter{
		PatientId: &request.PatientId,
		Time:      &request.CheckDate,
		Statuses: []patient_participation_repo.PatientParticipationStatus{
			patient_participation_repo.PatientParticipationStatus_Active,
		},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	response := patient_participation_service.GetPatientParticipationResponse{
		Participations: make([]patient_participation_service.PatientParticipation, 0, len(participations)),
	}
	for i := 0; i < len(participations); i++ {
		if participation := participations[i].PatientParticipation; participation != nil {
			response.Participations = append(response.Participations, ToResponse(*participation))
		}
	}
	return &response, nil
}

func (s *Service) CheckPatientParticipation(ctx *titan.Context, request patient_participation_service.CheckPatientParticipationRequest) (*patient_participation_service.CheckPatientParticipationResponse, error) {
	patientProfile, err := s.patientProfileRepos.GetPatientProfileByPatientId(ctx, request.PatientId)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	insuranceData := patientProfile.PatientInfo.GetActiveInsurance()
	if insuranceData == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_NotActive_InsuranceInfo, "")
	}
	doctor, err := s.employeeProfileService.FindEmployeeById(ctx, request.DoctorId)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if insuranceData.InsuranceNumber == nil || *insuranceData.InsuranceNumber == "" {
		return &patient_participation_service.CheckPatientParticipationResponse{
			IsAvailable: false,
		}, nil
	}
	hpmConfig, err := s.bsnrService.GetHpmConfig(ctx, ctx.UserInfo().BsnrId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	hpmRestService := hpm_rest_client.NewServiceRestWithEndpoint(hpmConfig.Endpoint)
	resp, err := hpmRestService.CheckParticipationList(ctx, &hpm_next.CheckParticipationRequest{
		PatientId:   request.PatientId.String(),
		InsuranceId: *insuranceData.InsuranceNumber,
		IkNumber:    fmt.Sprintf("%d", insuranceData.IkNumber),
		CheckTime:   request.CheckDate.UnixMilli(),
		Doctor:      (*hpm_next.ContractDoctor)(employee_profile_service.ToContractDoctor(doctor)),
		ContractIds: []string{request.ContractId},
	})
	if err != nil {
		if err.Error() == string(error_code.ErrorCode_HpmFunction_Not_Available) {
			return &patient_participation_service.CheckPatientParticipationResponse{
				IsAvailable: false,
			}, nil
		}
		return nil, errors.WithStack(err)
	}
	return &patient_participation_service.CheckPatientParticipationResponse{
		IsAvailable: resp.Status == hpm_next.ParticipateResultStatus_Ok,
	}, nil
}

func (s *Service) GetActivePatientParticipationWithoutCheckDate(ctx *titan.Context, request patient_participation_service.GetPatientParticipationWithoutCheckDateRequest) (*patient_participation_service.GetPatientParticipationResponse, error) {
	participations, err := s.getListValidPatientParticipations(ctx, patient_participation_repo.Filter{
		PatientId: &request.PatientId,
		Statuses: []patient_participation_repo.PatientParticipationStatus{
			patient_participation_repo.PatientParticipationStatus_Active,
		},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	response := patient_participation_service.GetPatientParticipationResponse{
		Participations: make([]patient_participation_service.PatientParticipation, 0, len(participations)),
	}
	for i := 0; i < len(participations); i++ {
		if participation := participations[i].PatientParticipation; participation != nil {
			response.Participations = append(response.Participations, ToResponse(*participation))
		}
	}
	return &response, nil
}

func (s Service) GetCancelableParticipations(ctx *titan.Context, patientId *uuid.UUID) ([]patient_participation_repo.PatientParticipation, error) {
	return s.repo.FindByFilter(ctx, patient_participation_repo.Filter{
		PatientId: patientId,
		Statuses: []patient_participation_repo.PatientParticipationStatus{
			patient_participation_repo.PatientParticipationStatus_Active,
			patient_participation_repo.PatientParticipationStatus_Requested,
			patient_participation_repo.PatientParticipationStatus_Terminated,
		},
	})
}

func (s Service) GetLatestCanceledParticipation(ctx *titan.Context, patientId *uuid.UUID) (*patient_participation_repo.PatientParticipation, error) {
	filter := bson.M{
		patient_participation_repo.Field_PatientId: patientId,
		patient_participation_repo.Field_Status:    patient_participation_repo.PatientParticipationStatus_Cancelled,
	}

	opts := &options.FindOneOptions{Sort: bson.M{patient_participation_repo.Field_UpdateDate: -1}}

	return s.repo.FindOne(ctx, filter, opts)
}

func (s Service) GetActiveParticipationByContract(ctx *titan.Context, contractIds []string) ([]*patient_participation_service.PatientParticipation, error) {
	now := util.NowUnixMillis(ctx)
	participations, err := s.repo.Find(ctx, bson.M{
		model_repo.Field_ContractId: bson.M{operator.In: contractIds},
		operator.Or: []bson.M{
			{model_repo.Field_Status: patient_participation_repo.PatientParticipationStatus_Active},
			{
				model_repo.Field_Status:  patient_participation_repo.PatientParticipationStatus_Terminated,
				model_repo.Field_EndDate: bson.M{operator.Gt: now},
			},
		},
	})
	if err != nil {
		return nil, errors.WithMessage(err, "GetActiveParticipationByContract")
	}

	result := []*patient_participation_service.PatientParticipation{}
	for _, participation := range participations {
		result = append(result, pkg_util.NewPointer(ToResponse(participation)))
	}
	return result, nil
}

func (s *Service) getSupportedFavContractsByFavDoctor(ctx *titan.Context, ikNumber int32) ([]contract_model.Contract, error) {
	favContractType := pkg_util.NewPointer(contract_model.ContractType_SpecialistCare)
	activeFaVContractIds, err := s.doctorParticipate.GetActiveContractIds(ctx, favContractType)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	supportedFavContracts, err := s.contractService.GetSupportedContracts(ctx, activeFaVContractIds, ikNumber, favContractType)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return supportedFavContracts, nil
}

func (s *Service) getSupportedFavContractsByHzvDoctor(ctx *titan.Context, ikNumber int32) ([]contract_model.Contract, error) {
	favContractType := pkg_util.NewPointer(contract_model.ContractType_SpecialistCare)
	hzvContractType := pkg_util.NewPointer(contract_model.ContractType_HouseDoctorCare)
	activeIds, err := s.doctorParticipate.GetActiveContractIds(ctx, hzvContractType)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(activeIds) == 0 {
		return nil, nil
	}
	hzvContracts := s.contractService.GetContractByIds(activeIds)
	favContractIds := make([]contract_model.ContractId, 0)
	for _, hzvContract := range hzvContracts {
		if hzvContract.ContractDefinition.VertragsuebergreifendePatiententeilnahmeanfrage == nil {
			continue
		}

		relatedFAVContracts := hzvContract.ContractDefinition.VertragsuebergreifendePatiententeilnahmeanfrage.PatiententeilnahmeanfrageVertrag
		for _, favContract := range relatedFAVContracts {
			favContractIds = append(favContractIds, favContract.ID)
		}
	}
	supportedFavContracts, err := s.contractService.GetSupportedContracts(ctx, favContractIds, ikNumber, favContractType)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return supportedFavContracts, nil
}

// updateAndNotify updates a single patient participation record and notifies about the change.
// It returns the updated participation and any error that occurred.
func (s *Service) updateAndNotify(ctx *titan.Context, patientParticipation *patient_participation_repo.PatientParticipation) (*patient_participation_repo.PatientParticipation, error) {
	// Store the insurance number before the update
	insuranceNumber := patientParticipation.HashedInsuranceNumber

	// Update the participation
	updatedParticipation, err := s.repo.Update(ctx, *patientParticipation)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to update PatientParticipation")
	}

	// Ensure notification is sent, even if there's an error in the calling function
	defer func() {
		errNotification := s.NotifyPatientParticipationsChanged(ctx, insuranceNumber, []patient_participation_repo.PatientParticipation{*updatedParticipation})
		if errNotification != nil {
			ctx.Logger().Error(errors.WithStack(errNotification).Error())
		}
	}()

	return updatedParticipation, nil
}
