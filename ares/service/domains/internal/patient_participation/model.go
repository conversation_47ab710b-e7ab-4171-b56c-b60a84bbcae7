package patient_participation

import (
	contract_model "git.tutum.dev/medi/tutum/ares/service/contract/contract/model"
	patient_participation_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_participation"
	"github.com/google/uuid"
)

type CreateParticipation struct {
	PatientId        uuid.UUID
	IkNumber         int32
	InsuranceNumber  string
	ContractId       string
	ContractType     contract_model.ContractType
	DoctorId         uuid.UUID
	StartDate        *int64
	EndDate          *int64
	Status           patient_participation_repo.PatientParticipationStatus
	IsChangingDoctor bool
	IsTransmittedHpm bool
	IsApplyForGroup  *bool
	TeId             string
	EnrollmentId     *uuid.UUID
	// this field was used for IVP schein creation
	IVPFormPrinted     bool
	DoctorFunctionType patient_participation_repo.DoctorFunctionType
	Reason             *string
	// UpdatedDate        *int64 // keep requested date
}
