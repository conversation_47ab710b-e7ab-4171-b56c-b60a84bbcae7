package billing_patient_service

import (
	"fmt"
	"math"

	"emperror.dev/errors"

	api "git.tutum.dev/medi/tutum/ares/app/mvz/api/billing_patient"
	api_patient_profile "git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_profile"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/timeline"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/app/mvz/patient_profile"
	"git.tutum.dev/medi/tutum/ares/share"

	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/printer_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	"git.tutum.dev/medi/tutum/ares/service/domains/billing_patient/common/billing_patient_common"
	catalog_sdebm_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_sdebm"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/sdebm"
	billing_patient_history "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/billing_patient_history"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_bill"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	timeline_common "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/timeline_service"
	service_schein "git.tutum.dev/medi/tutum/ares/service/schein"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
)

type BillingPatientService struct {
	bsnrRepo                        *bsnr_repo.BSNRDefaultRepository
	sdebmRepo                       *sdebm.SdebmRepo
	catalogSdebmService             *catalog_sdebm_service.CatalogSdebmService
	billingPatientHistoryRepo       *billing_patient_history.BillingPatientHistoryRepo
	timelineServiceReferencePayload *timeline_service.TimelineService[timeline_common.ReferencePayload]
	scheinRepo                      schein.ScheinRepoDefaultRepository
	employeeProfileClient           profile.EmployeeProfileService
	patientProfileClient            *patient_profile.PatientProfileBffImpl
	billingPatientRepo              *patient_bill.PatientBillRepo
	timelineServiceEncounter        *timeline_service.TimelineService[patient_encounter.EncounterServiceTimeline]
}

var BillingPatientServiceMod = submodule.Make[*BillingPatientService](func(
	sdebmRepo *sdebm.SdebmRepo,
	patientBillRepo *patient_bill.PatientBillRepo,
	timelineServiceEncounter *timeline_service.TimelineService[patient_encounter.EncounterServiceTimeline],
	catalogSdebmService *catalog_sdebm_service.CatalogSdebmService,
	timelineServiceReferencePayload *timeline_service.TimelineService[timeline_common.ReferencePayload],
	patientProfileBff *patient_profile.PatientProfileBffImpl,
	timelineServiceAny *timeline_service.TimelineService[any],
	scheinService *service_schein.ScheinService,
	employeeProfileClient profile.EmployeeProfileService,
) *BillingPatientService {
	bsnrRepo := bsnr_repo.NewBSNRDefaultRepository()

	patientBillService := &BillingPatientService{
		bsnrRepo:                        &bsnrRepo,
		sdebmRepo:                       sdebmRepo,
		billingPatientHistoryRepo:       billing_patient_history.NewBillingPatientHistoryRepoDefaultRepository(),
		timelineServiceReferencePayload: timelineServiceReferencePayload,
		scheinRepo:                      schein.NewScheinRepoDefaultRepository(),
		employeeProfileClient:           employeeProfileClient,
		patientProfileClient:            patientProfileBff,
		billingPatientRepo:              patientBillRepo,
		timelineServiceEncounter:        timelineServiceEncounter,
		catalogSdebmService:             catalogSdebmService,
	}

	timelineServiceAny.HookAfterAction.RegisterOnCreateFunc(patientBillService.OnTimelineCreate)
	timelineServiceAny.HookAfterAction.RegisterOnUpdateFunc(patientBillService.OnTimelineUpdate)
	timelineServiceAny.HookAfterAction.RegisterOnDeleteFunc(patientBillService.OnTimelineRemove)

	timelineServiceEncounter.HookAfterAction.RegisterOnCreateFunc(patientBillService.OnTimelineCreate)
	timelineServiceEncounter.HookAfterAction.RegisterOnUpdateFunc(patientBillService.OnTimelineUpdate)
	timelineServiceEncounter.HookAfterAction.RegisterOnDeleteFunc(patientBillService.OnTimelineRemove)

	scheinService.OnUpdate.RegisterFunc(func(ctx *titan.Context, updateInfo schein.ScheinRepo) error {
		doctorProfile, err := patientBillService.employeeProfileClient.GetEmployeeProfileById(ctx, &profile.EmployeeProfileGetRequest{
			OriginalId: &updateInfo.DoctorId,
		})
		if err != nil {
			return fmt.Errorf("failed to get employee profile: %w", err)
		}
		if doctorProfile == nil || doctorProfile.Id == nil {
			return nil
		}
		if err := patientBillService.billingPatientRepo.UpdateScheinWithDoctor(ctx, updateInfo, *doctorProfile); err != nil {
			return fmt.Errorf("failed to update patient profile: %w", err)
		}
		return nil
	})

	patientProfileBff.CUDHook.RegisterOnUpdateFunc(func(ctx *titan.Context, updateInfo api_patient_profile.EventPatientProfileChange) error {
		if updateInfo.PatientId == nil || updateInfo.PatientInfo == nil {
			return nil
		}
		if updateInfo.EventName != api_patient_profile.EventName_UpdatePatientProfile {
			return nil
		}
		if err := patientBillService.billingPatientRepo.UpdatePatientProfile(ctx, *updateInfo.PatientId, *updateInfo.PatientInfo); err != nil {
			return fmt.Errorf("failed to update patient profile: %w", err)
		}
		return nil
	})

	return patientBillService
},
	sdebm.SDEBMRepoMod,
	config.GetDefaultTitanClientMod,
	patient_bill.PatientBillRepoMod,
	timeline_service.TimelineServiceEncounterMod,
	catalog_sdebm_service.CatalogSdebmServiceMod,
	timeline_service.TimelineServiceReferenceMod,
	patient_profile.PatientProfileServiceMod,
	timeline_service.TimelineServiceAnyMod,
	service_schein.ScheinServiceMod,
	share.EmployeeProfileServiceMod,
)

type CreateBillingPatientRequest struct {
	PatientId            uuid.UUID
	ScheinId             uuid.UUID
	TimelineServiceId    uuid.UUID
	OldScheinId          *uuid.UUID
	OldTimelineServiceId *uuid.UUID
}

func (d *BillingPatientService) OnTimelineCreate(ctx *titan.Context, eventCreateModel *timeline.EventTimelineCreate) error {
	if len(eventCreateModel.TimelineModel.ScheinIds) == 0 || eventCreateModel.TimelineModel.EncounterServiceTimeline == nil {
		return nil
	}
	for _, v := range eventCreateModel.TimelineModel.ScheinIds {
		if err := d.createOrUpdate(ctx, CreateBillingPatientRequest{
			PatientId:         eventCreateModel.PatientId,
			ScheinId:          v,
			TimelineServiceId: *eventCreateModel.TimelineModel.Id,
		}); err != nil {
			ctx.Logger().Error(fmt.Sprintf("cannot create patient bill when timeline create: %s", err))
			return fmt.Errorf("cannot create patient bill: %w", err)
		}
	}
	return nil
}

func (d *BillingPatientService) OnTimelineUpdate(ctx *titan.Context, eventUpdateModel *timeline.EventTimelineUpdate) error {
	if eventUpdateModel.TimelineModel.EncounterServiceTimeline == nil {
		return nil
	}

	var (
		oldScheinId, oldTimelineServiceId *uuid.UUID
	)

	if eventUpdateModel.OldTimelineModel != nil {
		if len(eventUpdateModel.OldTimelineModel.ScheinIds) > 0 {
			oldScheinId = &eventUpdateModel.OldTimelineModel.ScheinIds[0]
		}
		oldTimelineServiceId = eventUpdateModel.OldTimelineModel.Id
	}

	if len(eventUpdateModel.TimelineModel.ScheinIds) == 0 && oldScheinId != nil && oldTimelineServiceId != nil {
		if err := d.removeScheinWithTimeline(ctx, CreateBillingPatientRequest{
			PatientId:            eventUpdateModel.PatientId,
			OldScheinId:          oldScheinId,
			OldTimelineServiceId: oldTimelineServiceId,
		}); err != nil {
			ctx.Logger().Error(fmt.Sprintf("cannot create patient bill when timeline update: %s", err))
			return fmt.Errorf("cannot create patient bill: %w", err)
		}
		return nil
	}

	if len(eventUpdateModel.TimelineModel.ScheinIds) > 0 {
		if err := d.createOrUpdate(ctx, CreateBillingPatientRequest{
			PatientId:            eventUpdateModel.PatientId,
			ScheinId:             eventUpdateModel.TimelineModel.ScheinIds[0],
			TimelineServiceId:    *eventUpdateModel.TimelineModel.Id,
			OldScheinId:          oldScheinId,
			OldTimelineServiceId: oldTimelineServiceId,
		}); err != nil {
			ctx.Logger().Error(fmt.Sprintf("cannot create patient bill when timeline update: %s", err))
			return fmt.Errorf("cannot create patient bill: %w", err)
		}
	}

	return nil
}

func (d *BillingPatientService) OnTimelineRemove(ctx *titan.Context, eventRemoveModel *timeline.EventTimelineRemove) error {
	if err := d.billingPatientHistoryRepo.RemoveById(ctx, *eventRemoveModel.TimelineModel.Id); err != nil {
		return fmt.Errorf("failed to remove patient bill history: %w", err)
	}

	if len(eventRemoveModel.TimelineModel.ScheinIds) == 0 || eventRemoveModel.TimelineModel.EncounterServiceTimeline == nil {
		return nil
	}
	for _, v := range eventRemoveModel.TimelineModel.ScheinIds {
		if err := d.delete(ctx, DeleteRequest{
			TimelineId: *eventRemoveModel.TimelineModel.Id,
			ScheinId:   v,
			PatientId:  eventRemoveModel.PatientId,
		}); err != nil {
			ctx.Logger().Error(fmt.Sprintf("cannot create patient bill when timeline update: %s", err))
			return fmt.Errorf("cannot create patient bill: %w", err)
		}
	}

	return nil
}

type DeleteRequest struct {
	TimelineId uuid.UUID
	ScheinId   uuid.UUID
	PatientId  uuid.UUID
}

func (srv *BillingPatientService) delete(ctx *titan.Context, req DeleteRequest) error {
	patientBill, err := srv.billingPatientRepo.GetByScheinAndPatient(ctx, req.ScheinId, req.PatientId)
	if err != nil {
		return fmt.Errorf("failed to get patient bill: %w", err)
	}

	if patientBill == nil {
		return nil
	}

	patientBill.ServicesTimeline = slice.Filter(patientBill.ServicesTimeline, func(t billing_patient_common.ServiceTimeline) bool {
		return t.TimelineId != req.TimelineId
	})

	if len(patientBill.ServicesTimeline) == 0 {
		_, err = srv.billingPatientRepo.DeleteById(ctx, *patientBill.BaseEntity.Id)
		if err != nil {
			return fmt.Errorf("failed to delete patient bill: %w", err)
		}
		return nil
	}

	patientBill.UpdatedAt = util.NewPointer(util.Now(ctx))
	patientBill.UpdatedBy = ctx.UserInfo().UserUUID()
	if _, err = srv.billingPatientRepo.Update(ctx, patientBill); err != nil {
		return fmt.Errorf("failed to update patient bill: %w", err)
	}
	return nil
}

func (srv *BillingPatientService) removeScheinWithTimeline(ctx *titan.Context, req CreateBillingPatientRequest) error {
	patientBill, err := srv.billingPatientRepo.GetByScheinAndPatient(ctx, *req.OldScheinId, req.PatientId)
	if err != nil {
		return fmt.Errorf("failed to get patient bill: %w", err)
	}
	if patientBill == nil {
		return nil
	}

	patientBill.ServicesTimeline = slice.Filter(patientBill.ServicesTimeline, func(t billing_patient_common.ServiceTimeline) bool {
		return t.TimelineId != *req.OldTimelineServiceId
	})

	if len(patientBill.ServicesTimeline) == 0 {
		_, err = srv.billingPatientRepo.DeleteById(ctx, *patientBill.BaseEntity.Id)
		if err != nil {
			return fmt.Errorf("failed to delete patient bill: %w", err)
		}
		return nil
	}
	patientBill.UpdatedAt = util.NewPointer(util.Now(ctx))
	patientBill.UpdatedBy = ctx.UserInfo().UserUUID()
	if _, err = srv.billingPatientRepo.Update(ctx, patientBill); err != nil {
		return fmt.Errorf("failed to update patient bill: %w", err)
	}
	return nil
}

func (srv *BillingPatientService) createOrUpdate(ctx *titan.Context, req CreateBillingPatientRequest) error {
	if req.OldScheinId != nil {
		if err := srv.removeScheinWithTimeline(ctx, req); err != nil {
			return fmt.Errorf("failed to remove schein with timeline: %w", err)
		}
	}
	patientProfile, err := srv.patientProfileClient.GetProfileById(ctx, profile.GetByIdRequest{
		PatientId: &req.PatientId,
	})
	if err != nil {
		return fmt.Errorf("failed to get patient profile: %w", err)
	}

	if patientProfile == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Patient_Not_Found)
	}
	schein, err := srv.scheinRepo.FindById(ctx, req.ScheinId)
	if err != nil {
		return fmt.Errorf("failed to get schein: %w", err)
	}

	if schein == nil || schein.Id == nil {
		return nil
	}

	employeeProfile, err := srv.employeeProfileClient.GetEmployeeProfileById(ctx, &profile.EmployeeProfileGetRequest{
		OriginalId: &schein.DoctorId,
	})
	if err != nil {
		return fmt.Errorf("failed to get employee profile: %w", err)
	}

	if employeeProfile == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Employee_Not_Found)
	}

	if schein == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Schein_Not_Found)
	}

	patientBill, err := srv.billingPatientRepo.GetByScheinAndPatient(ctx, req.ScheinId, req.PatientId)
	if err != nil {
		return fmt.Errorf("failed to get patient bill: %w", err)
	}

	serviceTimeline, err := srv.timelineServiceEncounter.FindById(ctx, req.TimelineServiceId)
	if err != nil {
		return fmt.Errorf("failed to get service timeline: %w", err)
	}

	if serviceTimeline == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Service_Timeline_Not_Found)
	}

	if patientBill == nil {
		now := util.Now(ctx)
		id := uuid.New()
		_, err := srv.billingPatientRepo.Create(ctx, &patient_bill.PatientBillEntity{
			BaseEntity: repos.BaseEntity{
				Id:        &id,
				CreatedAt: now,
				CreatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
			},
			BillingPatientModel: billing_patient_common.BillingPatientModel{
				Id:             &id,
				PatientProfile: *patientProfile,
				DoctorProfile:  *employeeProfile,
				ServicesTimeline: []billing_patient_common.ServiceTimeline{
					{
						TimelineId:       *serviceTimeline.Id,
						ServicesTimeline: serviceTimeline.Payload,
						CreatedAt:        serviceTimeline.SelectedDate.UnixMilli(),
					},
				},
				Schein: billing_patient_common.ScheinInfo{
					ScheinId:            req.ScheinId,
					ScheinTreatmentCase: schein.Schein.KvTreatmentCase,
					ScheinSubGroup:      util.GetPointerValue(schein.Schein.KvScheinSubGroup),
					DoctorId:            schein.DoctorId,
				},
				CreatedAt: util.NewPointer(now.UnixMilli()),
			},
		})
		if err != nil {
			return fmt.Errorf("failed to create patient bill: %w", err)
		}
		return nil
	}

	patientBill.UpdatedAt = util.NewPointer(util.Now(ctx))
	patientBill.UpdatedBy = ctx.UserInfo().UserUUID()
	patientBill.Schein = billing_patient_common.ScheinInfo{
		ScheinId:            req.ScheinId,
		ScheinTreatmentCase: schein.Schein.KvTreatmentCase,
		ScheinSubGroup:      util.GetPointerValue(schein.Schein.KvScheinSubGroup),
		DoctorId:            schein.DoctorId,
	}
	patientBill.ServicesTimeline = slice.AppendDistinctBy(
		patientBill.ServicesTimeline,
		[]billing_patient_common.ServiceTimeline{
			{
				TimelineId:       req.TimelineServiceId,
				ServicesTimeline: serviceTimeline.Payload,
				CreatedAt:        serviceTimeline.SelectedDate.UnixMilli(),
			},
		}, func(t billing_patient_common.ServiceTimeline) uuid.UUID {
			return t.TimelineId
		})

	_, err = srv.billingPatientRepo.Update(ctx, patientBill)
	if err != nil {
		return fmt.Errorf("failed to update patient bill: %w", err)
	}
	return nil
}

// GetBillingPatient
// return list of billing patient, total and page
func (srv *BillingPatientService) GetBillingPatient(
	ctx *titan.Context,
	billingDoctorId uuid.UUID,
	selectedDate int64,
	pagination common.PaginationRequest) (billingPatientPaging []billing_patient_common.BillingPatientModel, total, page int64, err error) {
	patientBills, total, err := srv.billingPatientRepo.GetBillingPatient(ctx, billingDoctorId, selectedDate, pagination)
	if err != nil {
		return nil, 0, 0, fmt.Errorf("failed to get billing patient: %w", err)
	}

	if len(patientBills) == 0 {
		return []billing_patient_common.BillingPatientModel{}, 0, 0, nil
	}

	scheinIds := slice.Map(patientBills, func(p patient_bill.PatientBillEntity) uuid.UUID {
		return p.Schein.ScheinId
	})

	scheins, err := srv.scheinRepo.FindByIds(ctx, scheinIds)
	if err != nil {
		return []billing_patient_common.BillingPatientModel{}, 0, 0, errors.WithMessage(err, "failed to get schein")
	}

	if len(scheins) == 0 {
		return []billing_patient_common.BillingPatientModel{}, 0, 0, nil
	}

	var results []billing_patient_common.BillingPatientModel
	for _, v := range patientBills {
		patientBillScheinId := v.Schein.ScheinId
		schein := slice.FindOne(scheins, func(s schein.ScheinRepo) bool {
			return *s.Id == patientBillScheinId
		})

		if schein == nil {
			return []billing_patient_common.BillingPatientModel{}, 0, 0, nil
		}

		insuranceById := v.PatientProfile.PatientInfo.GetInsurance(schein.Schein.InsuranceId)
		insuranceNumber := ""
		if insuranceById != nil {
			insuranceNumber = util.GetStringValue(insuranceById.InsuranceNumber)
		}
		v.BillingPatientModel.InsuranceNumber = insuranceNumber
		results = append(results, v.BillingPatientModel)
	}

	return results, total, pagination.Page, nil
}

func (srv *BillingPatientService) GetBillingPatientPrintContent(ctx *titan.Context, request api.GetBillingPatientPrintContentRequest) ([]billing_patient_common.BillingPatientPrintModel, error) {
	patientBills, err := srv.billingPatientRepo.FindByIdsWithQuarter(ctx, request.PatientBillIds, request.SelectedDate)
	if err != nil {
		return nil, err
	}

	if len(patientBills) == 0 {
		return []billing_patient_common.BillingPatientPrintModel{}, nil
	}

	bsnrCodes := slice.Map(patientBills, func(p patient_bill.PatientBillEntity) string {
		return p.DoctorProfile.Bsnr
	})
	bsnrs, err := srv.bsnrRepo.FindByCodes(ctx, bsnrCodes)
	if err != nil {
		return nil, err
	}
	if len(bsnrs) == 0 {
		return nil, errors.New("BSNR Code invalid")
	}

	scheinIds := slice.Map(patientBills, func(p patient_bill.PatientBillEntity) uuid.UUID {
		return p.Schein.ScheinId
	})

	scheins, err := srv.scheinRepo.FindByIds(ctx, scheinIds)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get schein")
	}
	if len(scheins) == 0 {
		return nil, errors.New("schein not found")
	}

	kvCodes := slice.Uniq(
		slice.Reduce(patientBills, func(acc []string, cur patient_bill.PatientBillEntity) []string {
			for _, v := range cur.ServicesTimeline {
				acc = append(acc, v.ServicesTimeline.Code)
			}
			return acc
		}, []string{}),
	)

	yearQuarter := util.ToYearQuarter(request.SelectedDate)
	quarterRange, err := yearQuarter.GetQuarterDateRange()
	if err != nil {
		return nil, err
	}
	startTime := quarterRange.Start

	regionalKv := request.BsnrCode[0:2]
	selectedDate := startTime.In(ctx.RequestTimeZone()).UnixMilli()
	sdebms, err := srv.catalogSdebmService.GetSdebmCatalogByCodes(ctx, &catalog_sdebm_service.GetEbmCatalogOverviewByCodesRequest{
		Codes:        kvCodes,
		RegionalKv:   regionalKv,
		SelectedDate: selectedDate,
	})
	if err != nil {
		return nil, err
	}

	if sdebms == nil {
		return nil, nil
	}

	var result []billing_patient_common.BillingPatientPrintModel
	nowStr := util.Now(ctx).In(ctx.RequestTimeZone()).Format("02.01.2006")
	for _, p := range patientBills {
		patientBillScheinId := p.Schein.ScheinId
		schein := slice.FindOne(scheins, func(s schein.ScheinRepo) bool {
			return *s.Id == patientBillScheinId
		})

		if schein == nil {
			ctx.Logger().Error("schein not found", "scheinId", patientBillScheinId.String())
			return nil, errors.New("schein not found")
		}

		patientAddress := p.PatientProfile.PatientInfo.AddressInfo.Address
		insuranceById := p.PatientProfile.PatientInfo.GetInsurance(schein.Schein.InsuranceId)
		insuranceNumber := ""
		insuranceName := ""
		if insuranceById != nil {
			insuranceNumber = util.GetStringValue(insuranceById.InsuranceNumber)
			insuranceName = insuranceById.InsuranceCompanyName
		}

		bsnrByDoctor := slice.FindOne(bsnrs, func(b bsnr_repo.BSNR) bool {
			return b.Code == p.DoctorProfile.Bsnr
		})
		totalPrice, servicePrice := func() (float64, []billing_patient_common.ServiceInfo) {
			totalPrice := 0.0
			var serviceInfos []billing_patient_common.ServiceInfo
			for _, timeLine := range p.ServicesTimeline {
				sdebm, ok := sdebms.Get(timeLine.ServicesTimeline.Code, regionalKv, selectedDate)
				if !ok {
					continue
				}

				date := util.ConvertMillisecondsToTime(timeLine.CreatedAt)
				servicePoint := sdebm.Evaluation
				price := math.Round(servicePoint*request.PointValue) / 100
				totalPrice += price
				serviceInfos = append(serviceInfos, billing_patient_common.ServiceInfo{
					Date:        date.In(ctx.RequestTimeZone()).Format("02.01.2006"),
					ServiceCode: timeLine.ServicesTimeline.Code,
					Description: sdebm.ReceiptDescription,
					Points:      servicePoint,
					Price:       price,
				})
			}
			return totalPrice, serviceInfos
		}()
		printModel := billing_patient_common.BillingPatientPrintModel{
			PracticeName: bsnrByDoctor.Name,
			PracticeAddress: billing_patient_common.AddressModel{
				Street:      bsnrByDoctor.Street,
				HouseNumber: bsnrByDoctor.Number,
				PostalCode:  bsnrByDoctor.PostCode,
				City:        bsnrByDoctor.City,
			},
			PatientId:         *p.PatientProfile.Id,
			PatientFirstName:  p.PatientProfile.FirstName,
			PatientLastName:   p.PatientProfile.LastName,
			PatientIntendWord: p.PatientProfile.PatientInfo.PersonalInfo.IntendWord.ToStringValue(),
			PatientTitle:      util.GetPointerValue(p.PatientProfile.PatientInfo.PersonalInfo.Title),
			PatientAddress: billing_patient_common.AddressModel{
				Street:      util.GetPointerValue(patientAddress.Street),
				HouseNumber: util.GetPointerValue(patientAddress.Number),
				PostalCode:  patientAddress.PostCode,
				City:        util.GetPointerValue(patientAddress.City),
			},
			PatientGender:   p.PatientProfile.GetGenderPatientParameter(),
			InsuranceName:   insuranceName,
			InsuranceNumber: insuranceNumber,
			CreationDate:    nowStr,
			TotalPrice:      totalPrice,
			Reimbursement:   math.Round(totalPrice*(1-util.GetPointerValue(request.Qoute)/100)*100) / 100,
			PeriodStartDate: startTime.UnixMilli(),
			PeriodEndDate:   util.GetPointerValue(yearQuarter.EndTimeDisplay()),
			Qoute:           request.Qoute,
			PointValue:      request.PointValue,
			ServiceInfos:    servicePrice,
			PracticeStamp:   bsnrByDoctor.PracticeStamp,
			ScheinInfo:      p.Schein,
			BillingDoctorId: p.Schein.DoctorId,
		}
		result = append(result, printModel)
	}

	return result, nil
}

func (srv *BillingPatientService) PrintBillingPatient(ctx *titan.Context, printerProfile printer_common.PrinterProfile, data []billing_patient_common.BillingPatientPrintModel) (*bool, error) {
	timeNow := util.Now(ctx)
	createdBy := ctx.UserInfo().UserUUID()
	yearQuarter := util.ToYearQuarter(timeNow.UnixMilli())

	historyEntities := []*billing_patient_history.BillingPatientHistoryEntity{}
	timelineEntities := []timeline_repo.TimelineEntity[timeline_common.ReferencePayload]{}

	for _, printModel := range data {
		historyId := util.NewUUID()
		historyEntities = append(historyEntities, &billing_patient_history.BillingPatientHistoryEntity{
			Id:             historyId,
			PrinterProfile: &printerProfile,
			CreatedDate:    timeNow,
			CreatedBy:      createdBy,
			Content:        &printModel,
			IsDeleted:      false,
		})
		quarter := util.ToYearQuarter(printModel.PeriodStartDate)
		timelineEntities = append(timelineEntities, timeline_repo.TimelineEntity[timeline_common.ReferencePayload]{
			Id:   historyId,
			Type: timeline_common.TimelineEntityType_BillingPatient,
			Payload: timeline_common.ReferencePayload{
				Id:                  *historyId,
				Quarter:             quarter.Quarter,
				Year:                quarter.Year,
				ScheinId:            printModel.ScheinInfo.ScheinId,
				ScheinTreatmentCase: printModel.ScheinInfo.ScheinTreatmentCase,
				ScheinSubGroup:      printModel.ScheinInfo.ScheinSubGroup,
			},
			PatientId:         printModel.PatientId,
			CreatedAt:         timeNow,
			CreatedAtString:   timeNow.Format("02.01.2006"),
			CreatedBy:         *createdBy,
			Quarter:           int(yearQuarter.Quarter),
			Year:              int(yearQuarter.Year),
			IsDeleted:         false,
			BillingDoctorId:   util.NewPointer(printModel.BillingDoctorId),
			TreatmentDoctorId: printModel.BillingDoctorId,
			ScheinIds:         []uuid.UUID{printModel.ScheinInfo.ScheinId},
		})
	}

	_, err := srv.billingPatientHistoryRepo.CreateMany(ctx, historyEntities)
	if err != nil {
		return nil, err
	}

	_, err = srv.timelineServiceReferencePayload.CreateMany(ctx, timelineEntities)
	if err != nil {
		return nil, err
	}

	res := true
	return &res, nil
}

func (srv *BillingPatientService) GetBillingPatientPrintHistory(ctx *titan.Context, historyId uuid.UUID) (*billing_patient_common.BillingPatientPrintHistoryModel, error) {
	res, err := srv.billingPatientHistoryRepo.FindById(ctx, historyId)
	if err != nil {
		return nil, fmt.Errorf("failed to get billing patient history: %w", err)
	}
	if res == nil {
		return nil, nil
	}
	return &billing_patient_common.BillingPatientPrintHistoryModel{
		PrintModel:     *res.Content,
		PrinterProfile: *res.PrinterProfile,
	}, nil
}
