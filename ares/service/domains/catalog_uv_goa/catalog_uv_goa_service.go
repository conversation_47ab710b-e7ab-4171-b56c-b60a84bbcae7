package catalog_uv_goa_service

import (
	"errors"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_uv_goa"
	"git.tutum.dev/medi/tutum/ares/pkg/masterdata"
	masterdata_model "git.tutum.dev/medi/tutum/ares/pkg/masterdata/model"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_uv_goa_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"

	uv_goa_master_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/bg_service_code"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
)

const Field_UvGoa_Code = "uvgoa.code"

type CatalogUvGoaService struct {
	uvGoaMasterRepo *uv_goa_master_repo.BGServiceCodeRepo
}

var CatalogUvGoaServiceMod = submodule.Make[*CatalogUvGoaService](func(uvGoaMasterRepo *uv_goa_master_repo.BGServiceCodeRepo) *CatalogUvGoaService {
	return &CatalogUvGoaService{
		uvGoaMasterRepo: uvGoaMasterRepo,
	}
}, uv_goa_master_repo.BGServiceCodeRepoMod)

func (srv *CatalogUvGoaService) GetUvGoaCatalogByCode(ctx *titan.Context, request *catalog_uv_goa.GetUvGoaCatalogByCodeRequest) (*catalog_uv_goa.UvGoaCatalogItem, error) {
	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	res, err := srv.uvGoaMasterRepo.GetByCode(ctx, &uv_goa_master_repo.GetByCodeRequest{
		YearQuarter: masterdata_model.YearQuarter{
			Year:    yearQuarter.Year,
			Quarter: yearQuarter.Quarter,
		},
		Code: request.Number,
	})
	if err != nil {
		return nil, err
	}

	if res == nil {
		return nil, nil
	}

	uvGoaCatalog, err := res.ToUvGoaCatalog()
	if err != nil {
		return nil, err
	}

	return &catalog_uv_goa.UvGoaCatalogItem{
		UvGoa: uvGoaCatalog,
	}, nil
}

func (srv *CatalogUvGoaService) GetUvGoaCatalogByCodes(ctx *titan.Context, request *catalog_uv_goa.GetUvGoaCatalogByCodesRequest) (*catalog_uv_goa.GetUvGoaCatalogByCodesResponse, error) {
	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	res, err := srv.uvGoaMasterRepo.GetByCodes(ctx, &uv_goa_master_repo.GetByCodesRequest{
		YearQuarter: masterdata_model.YearQuarter{
			Year:    yearQuarter.Year,
			Quarter: yearQuarter.Quarter,
		},
		Codes: request.Numbers,
	})
	if err != nil {
		return nil, err
	}

	if res == nil {
		return nil, nil
	}

	uvGoaCatalogs := []*catalog_uv_goa_common.UvGoaCatalog{}

	for _, item := range res {
		uvGoaCatalog, err := item.ToUvGoaCatalog()
		if err != nil {
			return nil, err
		}

		uvGoaCatalogs = append(uvGoaCatalogs, uvGoaCatalog)
	}

	return &catalog_uv_goa.GetUvGoaCatalogByCodesResponse{
		Items: uvGoaCatalogs,
	}, nil
}

func (srv *CatalogUvGoaService) GetUvGoaCatalogs(ctx *titan.Context, request *catalog_uv_goa.GetUvGoaCatalogsRequest) (*catalog_uv_goa.GetUvGoaCatalogsResponse, error) {
	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	res, err := srv.uvGoaMasterRepo.GetPagination(ctx, &uv_goa_master_repo.GetPaginationRequest{
		YearQuarter: masterdata_model.YearQuarter{
			Year:    yearQuarter.Year,
			Quarter: yearQuarter.Quarter,
		},
		Pagination: masterdata_model.Pagination{
			Page:   request.Pagination.Page,
			Limit:  request.Pagination.PageSize,
			SortBy: request.Pagination.SortBy,
			Order:  request.Pagination.Order,
		},
		OnlySelfCreated: util.NewPointer(request.IsOnlySelfCreated),
		Query:           &request.Value,
	})
	if err != nil {
		return nil, err
	}

	if res == nil {
		return &catalog_uv_goa.GetUvGoaCatalogsResponse{
			Items: []*catalog_uv_goa_common.UvGoaCatalog{},
			Total: 0,
		}, nil
	}

	items := []*catalog_uv_goa_common.UvGoaCatalog{}
	for _, item := range res.Data {
		uvGoaCatalog, err := item.ToUvGoaCatalog()
		if err != nil {
			return nil, err
		}

		items = append(items, uvGoaCatalog)
	}

	return &catalog_uv_goa.GetUvGoaCatalogsResponse{
		Items: items,
		Total: res.Meta.Total,
	}, nil
}

func (srv *CatalogUvGoaService) SearchUvGoaCatalogs(ctx *titan.Context, request *catalog_uv_goa.SearchUvGoaRequest) (*catalog_uv_goa.SearchUvGoaResponse, error) {
	// For BG always search in current year quarter
	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	res, err := srv.uvGoaMasterRepo.Search(ctx, &uv_goa_master_repo.SearchRequest{
		YearQuarter: masterdata_model.YearQuarter{
			Year:    yearQuarter.Year,
			Quarter: yearQuarter.Quarter,
		},
		Query: request.Value,
	})
	if err != nil {
		return nil, err
	}

	if len(res) == 0 {
		return &catalog_uv_goa.SearchUvGoaResponse{
			Items: []*catalog_uv_goa_common.UvGoaItem{},
		}, nil
	}

	listUvGoaItem := []*catalog_uv_goa_common.UvGoaItem{}
	selectedDate := request.SelectedDate
	for _, item := range res {
		uvGoaItem, err := item.ToUvGoaItem(request.IsGeneral)
		if err != nil {
			return nil, err
		}

		if selectedDate != nil && item.Validity != nil && item.Validity.ToDate != nil {
			documentDate := util.ConvertMillisecondsToTime(*selectedDate)
			validUntil := util.ConvertMillisecondsToTime(*item.Validity.ToDate)
			if validUntil.Before(documentDate) {
				continue
			}
		}

		listUvGoaItem = append(listUvGoaItem, uvGoaItem)
	}

	return &catalog_uv_goa.SearchUvGoaResponse{
		Items: listUvGoaItem,
	}, nil
}

func (srv *CatalogUvGoaService) GetCatalogsByListUvGoaCode(ctx *titan.Context, code []string) ([]*catalog_uv_goa_common.UvGoaCatalog, error) {
	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	res, err := srv.uvGoaMasterRepo.GetByCodes(ctx, &uv_goa_master_repo.GetByCodesRequest{
		YearQuarter: masterdata_model.YearQuarter{
			Year:    yearQuarter.Year,
			Quarter: yearQuarter.Quarter,
		},
		Codes: code,
	})
	if err != nil {
		return nil, err
	}

	if len(res) == 0 {
		return nil, nil
	}

	items := []*catalog_uv_goa_common.UvGoaCatalog{}
	for _, item := range res {
		uvGoaCatalog, err := item.ToUvGoaCatalog()
		if err != nil {
			return nil, err
		}
		items = append(items, uvGoaCatalog)
	}

	return items, nil
}

func (srv *CatalogUvGoaService) IsValidUpdateUvGoa(ctx *titan.Context, request *catalog_uv_goa.UpdateUvGoaCatalogRequest) (*catalog_uv_goa.IsValidUpdateUvGoaResponse, error) {
	validationResponse := catalog_uv_goa.IsValidUpdateUvGoaResponse{
		Errors: map[string]*common.FieldError{},
	}

	uvGoa := request.UvGoa
	uvGoaCatalog, err := srv.GetUvGoaCatalogByCode(ctx, &catalog_uv_goa.GetUvGoaCatalogByCodeRequest{
		Number: uvGoa.Code,
	})
	if err != nil {
		return nil, err
	}

	if uvGoa.UvGoaId == "" {
		if uvGoaCatalog != nil {
			validationResponse.Errors[Field_UvGoa_Code] = &common.FieldError{
				Field:          Field_UvGoa_Code,
				ValidationType: common.ValidationType_Error,
				ErrorCode:      string(error_code.ErrorCode_UV_GOA_Existed),
			}
		}

		return &validationResponse, nil
	}

	updatingUvGoaCatalog, err := srv.uvGoaMasterRepo.GetById(ctx, &masterdata.GetByIdRequest{
		Id: uvGoa.UvGoaId,
	})
	if err != nil {
		return nil, err
	}

	if updatingUvGoaCatalog != nil && updatingUvGoaCatalog.Code != uvGoa.Code && uvGoaCatalog != nil {
		validationResponse.Errors[Field_UvGoa_Code] = &common.FieldError{
			Field:          Field_UvGoa_Code,
			ValidationType: common.ValidationType_Error,
			ErrorCode:      string(error_code.ErrorCode_UV_GOA_Existed),
		}
	}

	return &validationResponse, nil
}

func (srv *CatalogUvGoaService) DeleteUvGoaCatalog(ctx *titan.Context, request *catalog_uv_goa.DeleteUvGoaCatalogRequest) error {
	return srv.uvGoaMasterRepo.Delete(ctx, &uv_goa_master_repo.DeleteRequest{
		Id: request.Id,
	})
}

func (srv *CatalogUvGoaService) CreateUvGoaCatalog(ctx *titan.Context, request *catalog_uv_goa.CreateUvGoaCatalogRequest) (*catalog_uv_goa.UvGoaCatalogItem, error) {
	uvGoaCatalog, err := srv.upsertUvGoa(ctx, request.UvGoa)
	if err != nil {
		return nil, err
	}

	return &catalog_uv_goa.UvGoaCatalogItem{
		UvGoa: uvGoaCatalog,
	}, nil
}

func (srv *CatalogUvGoaService) UpdateUvGoaCatalog(ctx *titan.Context, request *catalog_uv_goa.UpdateUvGoaCatalogRequest) (*catalog_uv_goa.UvGoaCatalogItem, error) {
	uvGoaCatalog, err := srv.upsertUvGoa(ctx, request.UvGoa)
	if err != nil {
		return nil, err
	}

	return &catalog_uv_goa.UvGoaCatalogItem{
		UvGoa: uvGoaCatalog,
	}, nil
}

func (srv *CatalogUvGoaService) upsertUvGoa(ctx *titan.Context, req *catalog_uv_goa_common.UvGoaCatalog) (*catalog_uv_goa_common.UvGoaCatalog, error) {
	validationResponse, err := srv.IsValidUpdateUvGoa(ctx, &catalog_uv_goa.UpdateUvGoaCatalogRequest{
		UvGoa: req,
	})
	if err != nil {
		return nil, err
	}

	if len(validationResponse.Errors) > 0 {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_UV_GOA_Existed)
	}

	bgServiceCode, err := srv.uvGoaMasterRepo.TransformToBgServiceCode(ctx, req)
	if err != nil {
		return nil, err
	}

	upsertResponse, err := srv.uvGoaMasterRepo.Upsert(ctx, bgServiceCode)
	if err != nil {
		return nil, err
	}

	if upsertResponse == nil {
		return nil, errors.New("upsert uv goa failed")
	}

	uvGoaCatalog, err := upsertResponse.ToUvGoaCatalog()
	if err != nil {
		return nil, err
	}

	return uvGoaCatalog, nil
}
