// This code was autogenerated from service/domains/schein_common.proto, do not edit.

package schein_common

import (
	"encoding/json"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	common "git.tutum.dev/medi/tutum/ares/service/domains/api/common"

	patient_participation "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_participation"

	patient_profile_common "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type PatientSnapshot struct {
	PatientId   uuid.UUID                          `json:"patientId"`
	PatientInfo patient_profile_common.PatientInfo `json:"patientInfo"`
	UpdatedAt   int64                              `json:"updatedAt"`
}

type ReferralDoctor struct {
	Bsnr string `json:"bsnr"`
	Lanr string `json:"lanr"`
}

type MarkAsReferralRequest struct {
	ScheinId       uuid.UUID      `json:"scheinId"`
	ReferralDoctor ReferralDoctor `json:"referralDoctor"`
}

type RemoveReferralRequest struct {
	ScheinId uuid.UUID `json:"scheinId"`
}

type Schein struct {
	ScheinMainGroup       string           `json:"scheinMainGroup" bson:"scheinMainGroup"`
	KvScheinSubGroup      *string          `json:"kvScheinSubGroup" bson:"kvScheinSubGroup"`
	KvTreatmentCase       string           `json:"kvTreatmentCase" bson:"kvTreatmentCase"`
	G4101Quarter          *int32           `json:"g4101Quarter" bson:"g4101Quarter"`
	G4101Year             *int32           `json:"g4101Year" bson:"g4101Year"`
	TariffType            *string          `json:"tariffType" bson:"tariffType"`
	BgType                *string          `json:"bgType" bson:"bgType"`
	BgAccidentDate        *int64           `json:"bgAccidentDate" bson:"bgAccidentDate"`
	BgAccidentTime        *string          `json:"bgAccidentTime" bson:"bgAccidentTime"`
	BgWorkingTimeFrom     *string          `json:"bgWorkingTimeFrom" bson:"bgWorkingTimeFrom"`
	BgWorkingTimeTo       *string          `json:"bgWorkingTimeTo" bson:"bgWorkingTimeTo"`
	BgEmployerName        *string          `json:"bgEmployerName" bson:"bgEmployerName"`
	BgEmployerStreet      *string          `json:"bgEmployerStreet" bson:"bgEmployerStreet"`
	BgEmployerHousenumber *string          `json:"bgEmployerHousenumber" bson:"bgEmployerHousenumber"`
	BgEmployerPostcode    *string          `json:"bgEmployerPostcode" bson:"bgEmployerPostcode"`
	BgEmployerCity        *string          `json:"bgEmployerCity" bson:"bgEmployerCity"`
	BgEmployerCountry     *string          `json:"bgEmployerCountry" bson:"bgEmployerCountry"`
	InsuranceId           uuid.UUID        `json:"insuranceId" validate:"required"`
	IkNumber              *int32           `json:"ikNumber"`
	PatientSnapshot       *PatientSnapshot `json:"patientSnapshot" fake:"skip"`
}

type ScheinDetail struct {
	G4122                      *string          `json:"g4122"`
	G4106                      *string          `json:"g4106"`
	G4102                      *int64           `json:"g4102"`
	Ad4206                     *int64           `json:"ad4206"`
	Ad4125From                 *int64           `json:"ad4125From" bson:"ad4125From"`
	Ad4125To                   *int64           `json:"ad4125To" bson:"ad4125To"`
	Ad4126                     *string          `json:"ad4126"`
	Ad4124                     *string          `json:"ad4124"`
	Ad4204                     *bool            `json:"ad4204"`
	Ad4202                     *bool            `json:"ad4202"`
	Re4241                     *string          `json:"re4241" validate:"omitempty,ne=888888800"`
	Re4248                     *string          `json:"re4248"`
	Re4249                     *string          `json:"re4249"`
	Re4242                     *string          `json:"re4242" validate:"omitempty,ne=888888800"`
	Re4225                     *string          `json:"re4225"`
	Re4233                     []DateTimeFromTo `json:"re4233" bson:"re4233"`
	Re4226                     *string          `json:"re4226"`
	Re4221                     *string          `json:"re4221"`
	Re4219                     *string          `json:"re4219"`
	Re4218                     *string          `json:"re4218"`
	Re4217                     *string          `json:"re4217"`
	Re4209                     *string          `json:"re4209"`
	Re4208                     *string          `json:"re4208"`
	Re4207                     *string          `json:"re4207"`
	Re4205                     *string          `json:"re4205"`
	Re4220                     *string          `json:"re4220"`
	Re4229                     *string          `json:"re4229"`
	Ps4257                     *int32           `json:"ps4257"`
	Ps4299                     *int32           `json:"ps4299" validate:"omitempty,ne=888888800"`
	Ps4256                     []string         `json:"ps4256"`
	Ps4253                     []string         `json:"ps4253"`
	Ps4254                     *int32           `json:"ps4254"`
	Ps4255                     *int32           `json:"ps4255"`
	Ps4252                     *int32           `json:"ps4252"`
	Ps4251                     *int32           `json:"ps4251"`
	Ps4247                     *int64           `json:"ps4247"`
	Ps4244                     []string         `json:"ps4244"`
	Ps4245                     *string          `json:"ps4245"`
	Ps4246                     *string          `json:"ps4246"`
	Ps4235                     *int64           `json:"ps4235"`
	Ps4236                     *bool            `json:"ps4236"`
	Ps4234                     *bool            `json:"ps4234"`
	Ps4250                     *bool            `json:"ps4250"`
	Re4243                     *string          `json:"re4243"`
	G4104                      *int32           `json:"g4104"`
	TsvgContactType            *string          `json:"tsvgContactType" bson:"tsvgContactType"`
	TsvgInfor                  *string          `json:"tsvgInfor" bson:"tsvgInfor"`
	TsvgTranferCode            *string          `json:"tsvgTranferCode" bson:"tsvgTranferCode"`
	TsvgContactDate            *int64           `json:"tsvgContactDate" bson:"tsvgContactDate"`
	PausingStartDate           *int64           `json:"pausingStartDate" bson:"pausingStartDate"`
	PausingEndDate             *int64           `json:"pausingEndDate" bson:"pausingEndDate"`
	IsInsuranceInformedTherapy *bool            `json:"isInsuranceInformedTherapy" bson:"isInsuranceInformedTherapy"`
	Ad4123                     *string          `json:"ad4123"`
	Re4214                     *int64           `json:"re4214"`
	Psychotherapy              []Psychotherapy  `json:"psychotherapy"`
}

type DateTimeFromTo struct {
	From int64 `json:"from"`
	To   int64 `json:"to"`
}

type GroupServicesCode struct {
	ServiceCode  string `json:"serviceCode"`
	AmountBilled int32  `json:"amountBilled"`
}

type GroupServicesCodeBefore2017 struct {
	ServiceCode    string `json:"serviceCode"`
	AmountApproval int32  `json:"amountApproval"`
	AmountBilled   int32  `json:"amountBilled"`
}

type Psychotherapy struct {
	Ps4235                      *int64                        `json:"ps4235" validate:"required"`
	Ps4247                      *int64                        `json:"ps4247"`
	Ps4245                      *int64                        `json:"ps4245"`
	Ps4246                      *string                       `json:"ps4246"`
	PausingStartDate            *int64                        `json:"pausingStartDate" bson:"pausingStartDate"`
	PausingEndDate              *int64                        `json:"pausingEndDate" bson:"pausingEndDate"`
	IsInsuranceInformedTherapy  *bool                         `json:"isInsuranceInformedTherapy" bson:"isInsuranceInformedTherapy"`
	Ps4251                      *int32                        `json:"ps4251"`
	Ps4250                      *bool                         `json:"ps4250"`
	Ps4299                      *int32                        `json:"ps4299" validate:"omitempty,ne=888888800"`
	Ps4254                      *int32                        `json:"ps4254"`
	Ps4257                      *int32                        `json:"ps4257"`
	Ps4255                      *int32                        `json:"ps4255"`
	Ps4252                      *int32                        `json:"ps4252"`
	GroupServicesCode           []GroupServicesCode           `json:"groupServicesCode" bson:"groupServicesCode"`
	GroupCareGiver              []GroupServicesCode           `json:"groupCareGiver" bson:"groupCareGiver"`
	IsReason                    bool                          `json:"isReason"`
	Id                          *uuid.UUID                    `json:"id"`
	TakeOverId                  *uuid.UUID                    `json:"takeOverId"`
	GroupServicesCodeBefore2017 []GroupServicesCodeBefore2017 `json:"groupServicesCodeBefore2017" bson:"groupServicesCodeBefore2017"`
}

type UpdateScheinRequest struct {
	ScheinId              uuid.UUID          `json:"scheinId"`
	PatientId             uuid.UUID          `json:"patientId"`
	DoctorId              uuid.UUID          `json:"doctorId"`
	ScheinMainGroup       MainGroup          `json:"scheinMainGroup"`
	KvTreatmentCase       TreatmentCaseNames `json:"kvTreatmentCase"`
	KvScheinSubGroup      *string            `json:"kvScheinSubGroup"`
	G4101Year             *int32             `json:"g4101Year"`
	G4101Quarter          *int32             `json:"g4101Quarter"`
	TariffType            *string            `json:"tariffType"`
	BgType                *string            `json:"bgType"`
	BgAccidentDate        *int64             `json:"bgAccidentDate"`
	BgAccidentTime        *string            `json:"bgAccidentTime"`
	BgWorkingTimeFrom     *string            `json:"bgWorkingTimeFrom"`
	BgWorkingTimeTo       *string            `json:"bgWorkingTimeTo"`
	BgEmployerName        *string            `json:"bgEmployerName"`
	BgEmployerStreet      *string            `json:"bgEmployerStreet"`
	BgEmployerHousenumber *string            `json:"bgEmployerHousenumber"`
	BgEmployerPostcode    *string            `json:"bgEmployerPostcode"`
	BgEmployerCity        *string            `json:"bgEmployerCity"`
	BgEmployerCountry     *string            `json:"bgEmployerCountry"`
	HzvContractId         *string            `json:"hzvContractId"`
	ScheinDetails         *ScheinDetail      `json:"scheinDetails"`
	InsuranceId           uuid.UUID          `json:"insuranceId"`
	ExcludeFromBilling    bool               `json:"excludeFromBilling"`
	AssignedToBsnrId      *uuid.UUID         `json:"assignedToBsnrId"`
}

type CheckEmptyScheinRequest struct {
	ScheinId uuid.UUID `json:"scheinId"`
}

type CheckExistKVScheinCurrentQuarterRequest struct {
	PatientId uuid.UUID `json:"patientId"`
	Quarter   *int32    `json:"quarter"`
	Year      *int32    `json:"year"`
}

type CheckExistKVScheinCurrentQuarterResponse struct {
	IsExist bool `json:"isExist"`
}

type CheckEmptyScheinResponse struct {
	IsEmpty bool `json:"isEmpty"`
}

type DeleteScheinRequest struct {
	ScheinId  uuid.UUID `json:"scheinId"`
	PatientId uuid.UUID `json:"patientId"`
}

type GetScheinDetailRequest struct {
	PatientId uuid.UUID `json:"patientId"`
}

type GetScheinDetailByIdRequest struct {
	ScheinId uuid.UUID `json:"scheinId"`
}

type GetScheinDetailByIdsRequest struct {
	ScheinIds []uuid.UUID `json:"scheinIds"`
}

type GetScheinDetailByIdResponse struct {
	PatientId             uuid.UUID          `json:"patientId"`
	DoctorId              uuid.UUID          `json:"doctorId"`
	ScheinMainGroup       MainGroup          `json:"scheinMainGroup"`
	KvTreatmentCase       TreatmentCaseNames `json:"kvTreatmentCase"`
	KvScheinSubGroup      *string            `json:"kvScheinSubGroup"`
	G4101Year             *int32             `json:"g4101Year"`
	G4101Quarter          *int32             `json:"g4101Quarter"`
	TariffType            *string            `json:"tariffType"`
	BgType                *string            `json:"bgType"`
	BgAccidentDate        *int64             `json:"bgAccidentDate"`
	BgAccidentTime        *string            `json:"bgAccidentTime"`
	BgWorkingTimeFrom     *string            `json:"bgWorkingTimeFrom"`
	BgWorkingTimeTo       *string            `json:"bgWorkingTimeTo"`
	BgEmployerName        *string            `json:"bgEmployerName"`
	BgEmployerStreet      *string            `json:"bgEmployerStreet"`
	BgEmployerHousenumber *string            `json:"bgEmployerHousenumber"`
	BgEmployerPostcode    *string            `json:"bgEmployerPostcode"`
	BgEmployerCity        *string            `json:"bgEmployerCity"`
	BgEmployerCountry     *string            `json:"bgEmployerCountry"`
	HzvContractId         *string            `json:"hzvContractId"`
	ScheinDetails         *ScheinDetail      `json:"scheinDetails"`
	ScheinId              uuid.UUID          `json:"scheinId"`
	InsuranceId           uuid.UUID          `json:"insuranceId"`
	ExcludeFromBilling    bool               `json:"excludeFromBilling"`
	MarkedAsBilled        bool               `json:"markedAsBilled"`
	ChargeSystemId        *string            `json:"chargeSystemId"`
	SvScheinDetail        *SvScheinDetail    `json:"svScheinDetail"`
	AssignedToBsnrId      *uuid.UUID         `json:"assignedToBsnrId"`
}

type GetScheinDetailByIdsResponse struct {
	Data []GetScheinDetailByIdResponse `json:"data"`
}

type GetScheinDetailResponse struct {
	ScheinIds []uuid.UUID `json:"scheinIds"`
}

type ScheinItem struct {
	ScheinId           uuid.UUID                             `json:"scheinId"`
	ScheinMainGroup    MainGroup                             `json:"scheinMainGroup"`
	KvTreatmentCase    TreatmentCaseNames                    `json:"kvTreatmentCase"`
	KvScheinSubGroup   *string                               `json:"kvScheinSubGroup"`
	G4101Year          *int32                                `json:"g4101Year"`
	G4101Quarter       *int32                                `json:"g4101Quarter"`
	HzvContractId      *string                               `json:"hzvContractId"`
	CreatedTime        int64                                 `json:"createdTime"`
	MarkedAsBilled     bool                                  `json:"markedAsBilled"`
	G4110              *int64                                `json:"g4110"`
	InsuranceId        uuid.UUID                             `json:"insuranceId"`
	ExcludeFromBilling bool                                  `json:"excludeFromBilling"`
	UpdatedBy          *uuid.UUID                            `json:"updatedBy"`
	UpdatedAt          *int64                                `json:"updatedAt"`
	ChargeSystemId     *string                               `json:"chargeSystemId"`
	TsvgContactType    *string                               `json:"tsvgContactType"`
	TsvgInfor          *string                               `json:"tsvgInfor"`
	TsvgTranferCode    *string                               `json:"tsvgTranferCode"`
	TsvgContactDate    *int64                                `json:"tsvgContactDate"`
	ScheinDetail       ScheinDetail                          `json:"scheinDetail"`
	IsTechnicalSchein  *bool                                 `json:"isTechnicalSchein"`
	PatientSnapshot    *PatientSnapshot                      `json:"patientSnapshot"`
	DoctorId           uuid.UUID                             `json:"doctorId"`
	IssueDate          *int64                                `json:"issueDate"`
	ScheinStatus       *ScheinStatus                         `json:"scheinStatus"`
	InvoiceNumber      *string                               `json:"invoiceNumber"`
	ReferralDoctor     *ReferralDoctor                       `json:"referralDoctor"`
	IsGeneral          *bool                                 `json:"isGeneral"`
	AccidentDate       *int64                                `json:"accidentDate"`
	JobOccupation      *string                               `json:"jobOccupation"`
	ArrivalDate        *int64                                `json:"arrivalDate"`
	WorkingTimeStart   int64                                 `json:"workingTimeStart"`
	WorkingTimeEnd     int64                                 `json:"workingTimeEnd"`
	CompanyAddress     patient_profile_common.CompanyAddress `json:"companyAddress"`
	BgScheinDetail     *BgScheinDetail                       `json:"bgScheinDetail"`
}

type GetScheinsOverviewRequest struct {
	PatientId uuid.UUID           `json:"patientId" validate:"required"`
	Quarter   *common.YearQuarter `json:"quarter"`
	MainGroup *MainGroup          `json:"mainGroup"`
}

type GetScheinsOverviewResponse struct {
	ScheinItems   []ScheinItem                           `json:"scheinItems"`
	InsuranceInfo []patient_profile_common.InsuranceInfo `json:"insuranceInfo"`
}

type MarkNotBilledRequest struct {
	ScheinId uuid.UUID `json:"scheinId"`
}

type ScheinFields struct {
	ScheinId         uuid.UUID      `json:"scheinId"`
	HzvEncounterCase *EncounterCase `json:"hzvEncounterCase,omitempty"`
	HzvTreatmentCase *TreatmentCase `json:"hzvTreatmentCase,omitempty"`
}

type PatientDiagnose struct {
	Id                  uuid.UUID   `json:"id"`
	Code                string      `json:"code"`
	DiagnoseDescription string      `json:"diagnoseDescription"`
	ChronicDiagnose     bool        `json:"chronicDiagnose"`
	ValidUntil          *int64      `json:"validUntil,omitempty"`
	Certainty           *Certainty  `json:"certainty,omitempty"`
	Laterality          *Laterality `json:"laterality,omitempty"`
	PatientId           uuid.UUID   `json:"patientId"`
	FreeText            string      `json:"freeText"`
	Command             string      `json:"command"`
	Source              Sources     `json:"source"`
	TreatmentDoctorId   uuid.UUID   `json:"treatmentDoctorId"`
	CreatedTime         int64       `json:"createdTime"`
	CreatedUser         uuid.UUID   `json:"createdUser"`
	UpdatedTime         int64       `json:"updatedTime"`
	UpdatedUser         uuid.UUID   `json:"updatedUser"`
	EncounterDate       int64       `json:"encounterDate"`
}

type ScheinDiagnose struct {
	Id                  uuid.UUID     `json:"id"`
	Code                string        `json:"code"`
	DiagnoseDescription string        `json:"diagnoseDescription"`
	ChronicDiagnose     bool          `json:"chronicDiagnose"`
	ValidUntil          *int64        `json:"validUntil,omitempty"`
	Certainty           *Certainty    `json:"certainty,omitempty"`
	Laterality          *Laterality   `json:"laterality,omitempty"`
	PatientId           uuid.UUID     `json:"patientId"`
	FreeText            string        `json:"freeText"`
	Command             string        `json:"command"`
	Source              Sources       `json:"source"`
	TreatmentDoctorId   uuid.UUID     `json:"treatmentDoctorId"`
	CreatedTime         int64         `json:"createdTime"`
	CreatedUser         uuid.UUID     `json:"createdUser"`
	UpdatedTime         int64         `json:"updatedTime"`
	UpdatedUser         uuid.UUID     `json:"updatedUser"`
	EncounterDate       int64         `json:"encounterDate"`
	PatientDiagnoseId   uuid.UUID     `json:"patientDiagnoseId"`
	ScheinId            uuid.UUID     `json:"scheinId"`
	HzvEncounterCase    EncounterCase `json:"hzvEncounterCase"`
	HzvTreatmentCase    TreatmentCase `json:"hzvTreatmentCase"`
}

type CreateDiagnoseRequest struct {
	Code                 string         `json:"code"`
	DiagnoseDescription  string         `json:"diagnoseDescription"`
	ChronicDiagnose      bool           `json:"chronicDiagnose"`
	ValidUntil           *int64         `json:"validUntil,omitempty"`
	Certainty            *Certainty     `json:"certainty,omitempty"`
	Laterality           *Laterality    `json:"laterality,omitempty"`
	PatientId            uuid.UUID      `json:"patientId"`
	FreeText             string         `json:"freeText"`
	Command              string         `json:"command"`
	Source               Sources        `json:"source"`
	TreatmentDoctorId    uuid.UUID      `json:"treatmentDoctorId"`
	ScheinDiagnoseFields []ScheinFields `json:"scheinDiagnoseFields,omitempty"`
	Id                   uuid.UUID      `json:"id"`
}

type CreateDiagnoseResponse struct {
	DiagnoseId uuid.UUID `json:"diagnoseId"`
}

type UpdateDiagnoseRequest struct {
	Code                 string         `json:"code"`
	DiagnoseDescription  string         `json:"diagnoseDescription"`
	ChronicDiagnose      bool           `json:"chronicDiagnose"`
	ValidUntil           *int64         `json:"validUntil,omitempty"`
	Certainty            *Certainty     `json:"certainty,omitempty"`
	Laterality           *Laterality    `json:"laterality,omitempty"`
	PatientId            uuid.UUID      `json:"patientId"`
	FreeText             string         `json:"freeText"`
	Command              string         `json:"command"`
	Source               Sources        `json:"source"`
	DiagnoseId           uuid.UUID      `json:"diagnoseId"`
	TreatmentDoctorId    uuid.UUID      `json:"treatmentDoctorId"`
	ScheinDiagnoseFields []ScheinFields `json:"scheinDiagnoseFields,omitempty"`
}

type DeleteDiagnoseRequest struct {
	DiagnoseId uuid.UUID `json:"diagnoseId"`
}

type GetDiagnoseByIdRequest struct {
	DiagnoseId uuid.UUID `json:"diagnoseId"`
}

type GetDiagnoseByIdResponse struct {
	Diagnose PatientDiagnose `json:"diagnose"`
}

type GetScheinDiagnoseByDiagnoseIdRequest struct {
	DiagnoseId uuid.UUID `json:"diagnoseId"`
}

type GetScheinDiagnoseByDiagnoseIdResponse struct {
	ScheinDiagnoses []ScheinDiagnose `json:"scheinDiagnoses"`
}

type PatientService struct {
	Id                        uuid.UUID      `json:"id"`
	Code                      string         `json:"code"`
	ServiceDescription        string         `json:"serviceDescription"`
	IsPreParticipate          *bool          `json:"isPreParticipate,omitempty"`
	HzvReferralDoctorBsnr     *string        `json:"hzvReferralDoctorBsnr,omitempty"`
	HzvReferralDoctorLanr     *string        `json:"hzvReferralDoctorLanr,omitempty"`
	HzvCareDoctorFacilityName *string        `json:"hzvCareDoctorFacilityName,omitempty"`
	HzvCareDoctorFacilityOrt  *string        `json:"hzvCareDoctorFacilityOrt,omitempty"`
	PatientId                 uuid.UUID      `json:"patientId"`
	FreeText                  string         `json:"freeText"`
	Command                   string         `json:"command"`
	Source                    Sources        `json:"source"`
	TreatmentDoctorId         uuid.UUID      `json:"treatmentDoctorId"`
	CreatedUser               uuid.UUID      `json:"createdUser"`
	CreatedTime               int64          `json:"createdTime"`
	UpdatedUser               uuid.UUID      `json:"updatedUser"`
	UpdatedTime               int64          `json:"updatedTime"`
	ScheinServiceFields       []ScheinFields `json:"scheinServiceFields,omitempty"`
}

type CreateServiceRequest struct {
	Code                      string         `json:"code"`
	ServiceDescription        string         `json:"serviceDescription"`
	IsPreParticipate          *bool          `json:"isPreParticipate,omitempty"`
	HzvReferralDoctorBsnr     *string        `json:"hzvReferralDoctorBsnr,omitempty"`
	HzvReferralDoctorLanr     *string        `json:"hzvReferralDoctorLanr,omitempty"`
	HzvCareDoctorFacilityName *string        `json:"hzvCareDoctorFacilityName,omitempty"`
	HzvCareDoctorFacilityOrt  *string        `json:"hzvCareDoctorFacilityOrt,omitempty"`
	PatientId                 uuid.UUID      `json:"patientId"`
	FreeText                  string         `json:"freeText"`
	Command                   string         `json:"command"`
	Source                    Sources        `json:"source"`
	TreatmentDoctorId         uuid.UUID      `json:"treatmentDoctorId"`
	ScheinServiceFields       []ScheinFields `json:"scheinServiceFields,omitempty"`
	Id                        uuid.UUID      `json:"id"`
}

type CreateServiceResponse struct {
	ServiceId uuid.UUID `json:"serviceId"`
}

type UpdateServiceRequest struct {
	Id                        uuid.UUID      `json:"id"`
	Code                      string         `json:"code"`
	ServiceDescription        string         `json:"serviceDescription"`
	IsPreParticipate          *bool          `json:"isPreParticipate,omitempty"`
	HzvReferralDoctorBsnr     *string        `json:"hzvReferralDoctorBsnr,omitempty"`
	HzvReferralDoctorLanr     *string        `json:"hzvReferralDoctorLanr,omitempty"`
	HzvCareDoctorFacilityName *string        `json:"hzvCareDoctorFacilityName,omitempty"`
	HzvCareDoctorFacilityOrt  *string        `json:"hzvCareDoctorFacilityOrt,omitempty"`
	FreeText                  string         `json:"freeText"`
	Command                   string         `json:"command"`
	Source                    Sources        `json:"source"`
	TreatmentDoctorId         uuid.UUID      `json:"treatmentDoctorId"`
	ScheinServiceFields       []ScheinFields `json:"scheinServiceFields,omitempty"`
}

type DeleteServiceRequest struct {
	ServiceId uuid.UUID `json:"serviceId"`
}

type GetPatientServiceByIdRequest struct {
	PatientServiceId uuid.UUID `json:"patientServiceId"`
}

type GetPatientServiceByIdResponse struct {
	PatientService PatientService `json:"patientService"`
}

type RunValidationForScheinsRequest struct {
	ScheinIds  []uuid.UUID    `json:"scheinIds,omitempty"`
	ServiceId  *uuid.UUID     `json:"serviceId,omitempty"`
	DiagnoseId *uuid.UUID     `json:"diagnoseId,omitempty"`
	Type       ValidationType `json:"type"`
}

type SaveSettingRequest struct {
	AssignDiagnosisCreatedschein      Assigndiagnosis `json:"assignDiagnosisCreatedschein"`
	PreviouslySelected                bool            `json:"previouslySelected"`
	DefaultTreatmentcase              *string         `json:"defaultTreatmentcase"`
	DefaultSubgroup                   *string         `json:"defaultSubgroup"`
	AutoCreatekvscheinHzvfav          bool            `json:"autoCreatekvscheinHzvfav"`
	ComposerAutoSelectscheinDiagnosis bool            `json:"composerAutoSelectscheinDiagnosis"`
	ShowWhenPatientCardIsRead         string          `json:"showWhenPatientCardIsRead"`
	IncludeCurrentPatient             bool            `json:"includeCurrentPatient"`
	ShowHintSpecialGroup09            bool            `json:"showHintSpecialGroup09"`
	HideHintForTfSG                   bool            `json:"hideHintForTfSG"`
}

type GetSettingResponse struct {
	AssignDiagnosisCreatedschein      Assigndiagnosis `json:"assignDiagnosisCreatedschein"`
	PreviouslySelected                bool            `json:"previouslySelected"`
	DefaultTreatmentcase              *string         `json:"defaultTreatmentcase"`
	DefaultSubgroup                   *string         `json:"defaultSubgroup"`
	AutoCreatekvscheinHzvfav          bool            `json:"autoCreatekvscheinHzvfav"`
	ComposerAutoSelectscheinDiagnosis bool            `json:"composerAutoSelectscheinDiagnosis"`
	ShowWhenPatientCardIsRead         string          `json:"showWhenPatientCardIsRead"`
	IncludeCurrentPatient             bool            `json:"includeCurrentPatient"`
	ShowHintSpecialGroup09            bool            `json:"showHintSpecialGroup09"`
	HideHintForTfSG                   bool            `json:"hideHintForTfSG"`
}

type OrderValue struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type GetOrderListResponse struct {
	OrderValues []OrderValue `json:"orderValues"`
}

type SaveOrderListRequest struct {
	OrderValues []OrderValue `json:"orderValues"`
}

type GetSelectedTreatmentCaseSubgroupRequest struct {
	PatientId uuid.UUID `json:"patientId"`
}

type GetSelectedTreatmentCaseSubgroupResponse struct {
	KvTreatmentCase  TreatmentCaseNames `json:"kvTreatmentCase"`
	KvScheinSubGroup *string            `json:"kvScheinSubGroup"`
}

type GetFieldsRequest struct {
	TreatmentCase TreatmentCaseNames `json:"treatmentCase"`
	PatientId     *uuid.UUID         `json:"patientId"`
}

type FieldValidation struct {
	Name           string              `json:"name"`
	ValidationType FieldValidationType `json:"validationType"`
}

type SubGroupRules struct {
	Code  string   `json:"code"`
	Rules []string `json:"rules"`
}

type CaseFields struct {
	Code         string            `json:"code"`
	Fields       []FieldValidation `json:"fields"`
	SubGroups    []SubGroupRules   `json:"subGroups"`
	SpecialRules []string          `json:"specialRules"`
}

type GetFieldsResponse struct {
	CaseFields CaseFields `json:"caseFields"`
}

type CreateSvScheinRequest struct {
	PatientId             uuid.UUID                                    `json:"patientId"`
	DoctorId              uuid.UUID                                    `json:"doctorId"`
	PatientParticipations []patient_participation.PatientParticipation `json:"patientParticipations"`
	InsuranceInfo         patient_profile_common.InsuranceInfo         `json:"insuranceInfo"`
	StartDate             *int64                                       `json:"startDate"`
	EndDate               *int64                                       `json:"endDate"`
}

type OmimG struct {
	Id      string `json:"id"`
	Code    string `json:"code"`
	GenName string `json:"genName"`
}

type GetOmimGResponse struct {
	OmimGs []OmimG `json:"omimGs"`
}

type OmimP struct {
	Id            string `json:"id"`
	Code          string `json:"code"`
	TypeOfIllness string `json:"typeOfIllness"`
	Pmk           int32  `json:"pmk"`
}

type GetOmimPResponse struct {
	OmimPs []OmimP `json:"omimPs"`
}

type OmimGChain struct {
	Id       uuid.UUID `json:"id"`
	Name     string    `json:"name"`
	OmimGIds []string  `json:"omimGIds"`
}

type GetOmimGChainResponse struct {
	OmimGChains []OmimGChain `json:"omimGChains"`
}

type InsertOmimGChainRequest struct {
	Name     string   `json:"name"`
	OmimGIds []string `json:"omimGIds"`
}

type UpdateOmimGChainRequest struct {
	Id       uuid.UUID `json:"id"`
	Name     string    `json:"name"`
	OmimGIds []string  `json:"omimGIds"`
}

type DeleteOmimGChainRequest struct {
	Id uuid.UUID `json:"id"`
}

type InsertOmimPChainRequest struct {
	Name     string   `json:"name"`
	OmimPIds []string `json:"omimPIds"`
}

type UpdateOmimPChainRequest struct {
	Id       uuid.UUID `json:"id"`
	Name     string    `json:"name"`
	OmimPIds []string  `json:"omimPIds"`
}

type DeleteOmimPChainRequest struct {
	Id uuid.UUID `json:"id"`
}

type GetOmimPChainResponse struct {
	OmimPChains []OmimPChain `json:"omimPChains"`
}

type OmimPChain struct {
	Id       uuid.UUID `json:"id"`
	Name     string    `json:"name"`
	OmimPIds []string  `json:"omimPIds"`
}

type GroupTherapy struct {
	ServiceCode             []string `json:"serviceCode"`
	AmountApprovalTherapy   int32    `json:"amountApprovalTherapy"`
	AmountDocumentedTherapy int32    `json:"amountDocumentedTherapy"`
}

type PyschoTherapy struct {
	IsCheckingSomaticSymptom   bool          `json:"isCheckingSomaticSymptom"`
	IsApproval                 bool          `json:"isApproval"`
	DeadlineApproval           *int64        `json:"deadlineApproval,omitempty"`
	Lanr                       *string       `json:"lanr,omitempty"`
	DateApproval               *int64        `json:"dateApproval,omitempty"`
	ServiceCodeApproval        []string      `json:"serviceCodeApproval,omitempty"`
	AmountApprovedTherapy      *int64        `json:"amountApprovedTherapy,omitempty"`
	AmountBilledTherapy        *int64        `json:"amountBilledTherapy,omitempty"`
	PausingStartDate           *int64        `json:"pausingStartDate,omitempty"`
	PausingEndDate             *int64        `json:"pausingEndDate,omitempty"`
	IsInsuranceInformedTherapy *bool         `json:"isInsuranceInformedTherapy,omitempty"`
	IsCombinatedTreatment      *bool         `json:"isCombinatedTreatment,omitempty"`
	TypeOfCombinatedTreatment  *int32        `json:"typeOfCombinatedTreatment,omitempty"`
	Patient                    *GroupTherapy `json:"patient,omitempty"`
	OtherPerson                *GroupTherapy `json:"otherPerson,omitempty"`
}

type CreateSvScheinAutomaticlyRequest struct {
	ReferenceScheinIds []uuid.UUID `json:"referenceScheinIds"`
	SelectedDate       int64       `json:"selectedDate"`
}

type CreateSvScheinAutomaticlyResponse struct {
	ScheinIds []uuid.UUID `json:"scheinIds"`
}

type CreateSvScheinFromReferenceRequest struct {
	ReferenceScheinId uuid.UUID `json:"referenceScheinId"`
	DoctorId          uuid.UUID `json:"doctorId"`
	ChargeSystemId    string    `json:"chargeSystemId"`
	SelectedDate      int64     `json:"selectedDate"`
	StartDate         *int64    `json:"startDate"`
	EndDate           *int64    `json:"endDate"`
}

type CreateSvScheinFromReferenceResponse struct {
	ScheinId uuid.UUID `json:"scheinId"`
}

type CreateSvScheinManuallyRequest struct {
	PatientId        uuid.UUID  `json:"patientId"`
	DoctorId         uuid.UUID  `json:"doctorId"`
	InsuranceId      uuid.UUID  `json:"insuranceId"`
	SelectedDate     int64      `json:"selectedDate"`
	ContractId       string     `json:"contractId"`
	ChargeSystemId   string     `json:"chargeSystemId"`
	StartDate        *int64     `json:"startDate"`
	EndDate          *int64     `json:"endDate"`
	ScheinMainGroup  MainGroup  `json:"scheinMainGroup"`
	AssignedToBsnrId *uuid.UUID `json:"assignedToBsnrId"`
}

type CreateSvScheinManuallyResponse struct {
	ScheinId uuid.UUID `json:"scheinId"`
}

type UpdateSvScheinRequest struct {
	ScheinId         uuid.UUID  `json:"scheinId"`
	DoctorId         uuid.UUID  `json:"doctorId"`
	ChargeSystemId   string     `json:"chargeSystemId"`
	SelectedDate     int64      `json:"selectedDate"`
	StartDate        *int64     `json:"startDate"`
	EndDate          *int64     `json:"endDate"`
	AssignedToBsnrId *uuid.UUID `json:"assignedToBsnrId"`
}

type UpdateSvScheinResponse struct {
	ScheinId uuid.UUID `json:"scheinId"`
}

type BgScheinItem struct {
	ScheinId                uuid.UUID                             `json:"scheinId"`
	InsuranceId             *uuid.UUID                            `json:"insuranceId"`
	DoctorId                uuid.UUID                             `json:"doctorId" validate:"required"`
	PatientId               uuid.UUID                             `json:"patientId" validate:"required"`
	CreatedOn               int64                                 `json:"createdOn" validate:"required"`
	EndDate                 *int64                                `json:"endDate"`
	PersonalAccident        bool                                  `json:"personalAccident"`
	AccidentDate            int64                                 `json:"accidentDate" validate:"required"`
	ArrivalDate             *int64                                `json:"arrivalDate"`
	FileNumber              *int64                                `json:"fileNumber"`
	ExcludeFromBilling      bool                                  `json:"excludeFromBilling"`
	MarkedAsBilled          bool                                  `json:"markedAsBilled"`
	ScheinMainGroup         MainGroup                             `json:"scheinMainGroup"`
	WorkingTimeStart        int64                                 `json:"workingTimeStart"`
	WorkingTimeEnd          int64                                 `json:"workingTimeEnd"`
	ScheinStatus            *ScheinStatus                         `json:"scheinStatus"`
	BGType                  BGType                                `json:"bGType"`
	EmploymentInfo          patient_profile_common.EmploymentInfo `json:"employmentInfo"`
	AssignedToBsnrId        *uuid.UUID                            `json:"assignedToBsnrId"`
	EmploymentInfoUpdatedAt *int64                                `json:"employmentInfoUpdatedAt"`
	FileNumberStr           *string                               `json:"fileNumberStr"`
	IkNumber                *int32                                `json:"ikNumber"`
}

type BgScheinDetail struct {
	CreatedOn               int64                                 `json:"createdOn" validate:"required"`
	EndDate                 *int64                                `json:"endDate"`
	PersonalAccident        bool                                  `json:"personalAccident"`
	AccidentDate            int64                                 `json:"accidentDate" validate:"required"`
	ArrivalDate             *int64                                `json:"arrivalDate"`
	FileNumber              *int64                                `json:"fileNumber"`
	WorkingTimeStart        int64                                 `json:"workingTimeStart"`
	WorkingTimeEnd          int64                                 `json:"workingTimeEnd"`
	BGType                  BGType                                `json:"bGType"`
	InvoiceNumber           *string                               `json:"invoiceNumber"`
	EmploymentInfo          patient_profile_common.EmploymentInfo `json:"employmentInfo"`
	EmploymentInfoUpdatedAt *int64                                `json:"employmentInfoUpdatedAt"`
	FileNumberStr           *string                               `json:"fileNumberStr"`
}

type SvScheinDetail struct {
	StartDate                   *int64 `json:"startDate"`
	EndDate                     *int64 `json:"endDate"`
	OnlineParticipatedCheckDate *int64 `json:"onlineParticipatedCheckDate"`
}

// enum definitions
type MainGroup string

const (
	HZV     MainGroup = "HZV"
	FAV     MainGroup = "FAV"
	KV      MainGroup = "KV"
	BG      MainGroup = "BG"
	PRIVATE MainGroup = "PRIVATE"
	IGEL    MainGroup = "IGEL"
)

type EncounterCase string

const (
	AB             EncounterCase = "AB"
	PB             EncounterCase = "PB"
	NOT            EncounterCase = "NOT"
	PRE_ENROLLMENT EncounterCase = "PRE_ENROLLMENT"
)

type TreatmentCase string

const (
	TreatmentCaseCustodian      TreatmentCase = "TreatmentCaseCustodian"
	TreatmentCaseDelegate       TreatmentCase = "TreatmentCaseDelegate"
	TreatmentCaseDeputy         TreatmentCase = "TreatmentCaseDeputy"
	TreatmentCasePreParticipate TreatmentCase = "TreatmentCasePreParticipate"
)

type Laterality string

const (
	U Laterality = "U"
	L Laterality = "L"
	R Laterality = "R"
	B Laterality = "B"
)

type Certainty string

const (
	G Certainty = "G"
	V Certainty = "V"
	Z Certainty = "Z"
	A Certainty = "A"
)

type Sources string

const (
	Imported Sources = "Imported"
	Composer Sources = "Composer"
	Timeline Sources = "Timeline"
)

type TreatmentCaseNames string

const (
	TCKvOutpatient     TreatmentCaseNames = "0101"
	TCKvReferral       TreatmentCaseNames = "0102"
	TCKvHospital       TreatmentCaseNames = "0103"
	TCKvEmergency      TreatmentCaseNames = "0104"
	TCKvSpaMedical     TreatmentCaseNames = "0109"
	TCKvSadtOutpatient TreatmentCaseNames = "sadt1"
	TCKvSadtReferral   TreatmentCaseNames = "sadt2"
	TCKvSadtHospital   TreatmentCaseNames = "sadt3"
	TCBgCase           TreatmentCaseNames = "BG"
	TCPrivateCase      TreatmentCaseNames = "PRIVATE"
)

type ValidationType string

const (
	ScheinValidation   ValidationType = "ScheinValidation"
	ServiceValidation  ValidationType = "ServiceValidation"
	DiagnoseValidation ValidationType = "DiagnoseValidation"
)

type CreateScheinErrorCode string

const (
	CostUnitIsNotAvailableInKvRegion       CreateScheinErrorCode = "CostUnitIsNotAvailableInKvRegion"
	CostUnitHasExpired                     CreateScheinErrorCode = "CostUnitHasExpired"
	CostUnitIsTerminated                   CreateScheinErrorCode = "CostUnitIsTerminated"
	TheBillingAreaIsOutOfValidityDateRange CreateScheinErrorCode = "TheBillingAreaIsOutOfValidityDateRange"
	WarningKvx3SKTAddtional                CreateScheinErrorCode = "WarningKvx3SKTAddtional"
	TheBillingAreaIsInvalid                CreateScheinErrorCode = "TheBillingAreaIsInvalid"
)

type Assigndiagnosis string

const (
	AssigndiagnosisAll       Assigndiagnosis = "all"
	AssigndiagnosisPermanent Assigndiagnosis = "permanent"
	AssigndiagnosisManually  Assigndiagnosis = "manually"
)

type FieldValidationType string

const (
	FieldValidationType_Required FieldValidationType = "required"
	FieldValidationType_Optional FieldValidationType = "optional"
)

type ScheinStatus string

const (
	ScheinStatus_Normal   ScheinStatus = "ScheinStatus_Normal"
	ScheinStatus_Printed  ScheinStatus = "ScheinStatus_Printed"
	ScheinStatus_Billed   ScheinStatus = "ScheinStatus_Billed"
	ScheinStatus_Canceled ScheinStatus = "ScheinStatus_Canceled"
)

type BGType string

const (
	GereralAction BGType = "GereralAction"
	SpecialAction BGType = "SpecialAction"
)

// Define constants
const NATS_SUBJECT = "" // nats subject this service will listen to

// service event constants

// message event constants

// Define service interface -------------------------------------------------------------

// Define service proxy -------------------------------------------------------------------

// Define service router -----------------------------------------------------------------

// Define client ----------------------------------------------------------------------------------------------

// Define event Notifier -----------------------------------------------------------------------------------------
type ScheinCommonNotifier struct {
	client *titan.Client
}

func NewScheinCommonNotifier() *ScheinCommonNotifier {
	client := titan.GetDefaultClient()
	return &ScheinCommonNotifier{client}
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type ScheinCommonSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewScheinCommonSocketNotifier(socket *socket_api.SocketServiceClient) *ScheinCommonSocketNotifier {
	return &ScheinCommonSocketNotifier{socket}
}

// -----------------------------------------------
// Test event listener
type ScheinCommonEventListener struct {
	mux sync.Mutex
}

func (listener *ScheinCommonEventListener) Reset() {
	listener.mux.Lock()
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *ScheinCommonEventListener) Subscribe(s *titan.MessageSubscriber) {
}
