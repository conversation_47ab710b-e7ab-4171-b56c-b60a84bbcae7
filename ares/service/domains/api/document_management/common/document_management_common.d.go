// This code was autogenerated from service/domains/document_management_common.proto, do not edit.

package common

import (
	"encoding/json"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	catalog_sdav_common "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdav_common"

	common "git.tutum.dev/medi/tutum/ares/service/domains/api/document_type/common"

	patient_profile_common "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type Patient struct {
	Id            uuid.UUID                          `json:"id"`
	FirstName     string                             `json:"firstName"`
	LastName      string                             `json:"lastName"`
	DateOfBirth   patient_profile_common.DateOfBirth `json:"dateOfBirth"`
	PatientNumber int64                              `json:"patientNumber"`
	Gender        string                             `json:"gender"`
	FullName      string                             `json:"fullName"`
}

type DocumentManagementSender struct {
	Bsnr string `json:"bsnr"`
	Lanr string `json:"lanr"`
}

type DocumentManagementItem struct {
	Id                uuid.UUID                        `json:"id"`
	CompanionFileId   *int64                           `json:"companionFileId"`
	CompanionFilePath *string                          `json:"companionFilePath"`
	Patient           *Patient                         `json:"patient"`
	Sender            *catalog_sdav_common.SdavCatalog `json:"sender"`
	DocumentName      string                           `json:"documentName"`
	DocumentType      *common.DocumentType             `json:"documentType"`
	Description       string                           `json:"description"`
	Status            DocumentManagementStatus         `json:"status"`
	ImportedDate      int64                            `json:"importedDate"`
	DocumentDirPath   string                           `json:"documentDirPath"`
	GdtSenderName     string                           `json:"gdtSenderName"`
	MetaData          *map[string]string               `json:"metaData"`
}

type DocumentManagementModel struct {
	CompanionFileId   *int64                   `json:"companionFileId"`
	CompanionFilePath *string                  `json:"companionFilePath"`
	PatientId         *uuid.UUID               `json:"patientId"`
	SenderId          *string                  `json:"senderId"`
	DocumentType      *common.DocumentType     `json:"documentType"`
	DocumentName      string                   `json:"documentName"`
	Description       string                   `json:"description"`
	Status            DocumentManagementStatus `json:"status"`
	ImportedDate      int64                    `json:"importedDate"`
	DocumentSettingId *uuid.UUID               `json:"documentSettingId"`
	GdtImportModTime  int64                    `json:"gdtImportModTime"`
	MetaData          *map[string]string       `json:"metaData"`
}

type ReadBy struct {
	Id     uuid.UUID `json:"id"`
	Name   string    `json:"name"`
	ReadAt int64     `json:"readAt"`
}

type FolderState struct {
	CurrentState map[string]int64 `json:"currentState"`
	DirPath      string           `json:"dirPath"`
}

// enum definitions
type DocumentManagementStatus string

const (
	DocumentManagementStatus_New        DocumentManagementStatus = "NEW"
	DocumentManagementStatus_ReImport   DocumentManagementStatus = "RE_IMPORT"
	DocumentManagementStatus_InProgress DocumentManagementStatus = "IN_PROGRESS"
	DocumentManagementStatus_Completed  DocumentManagementStatus = "COMPLETED"
	DocumentManagementStatus_Failed     DocumentManagementStatus = "FAILED"
)

type DocumentNotificationType string

const (
	DocumentNotificationType_LDT DocumentNotificationType = "LDT"
	DocumentNotificationType_GDT DocumentNotificationType = "GDT"
)

type MetaDataKey string

const (
	MetaDataKey_PatientLabOrder        MetaDataKey = "patient_lab_order"
	MetaDataKey_PatientLabResult       MetaDataKey = "patient_lab_result"
	MetaDataKey_FileNameUploaded       MetaDataKey = "file_name_uploaded"
	MetaDataKey_LabResultChunkFileName MetaDataKey = "lab_result_chunk_file_name"
)

// Define constants
const NATS_SUBJECT = "" // nats subject this service will listen to

// service event constants

// message event constants

// Define service interface -------------------------------------------------------------

// Define service proxy -------------------------------------------------------------------

// Define service router -----------------------------------------------------------------

// Define client ----------------------------------------------------------------------------------------------

// Define event Notifier -----------------------------------------------------------------------------------------
type CommonNotifier struct {
	client *titan.Client
}

func NewCommonNotifier() *CommonNotifier {
	client := titan.GetDefaultClient()
	return &CommonNotifier{client}
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type CommonSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewCommonSocketNotifier(socket *socket_api.SocketServiceClient) *CommonSocketNotifier {
	return &CommonSocketNotifier{socket}
}

// -----------------------------------------------
// Test event listener
type CommonEventListener struct {
	mux sync.Mutex
}

func (listener *CommonEventListener) Reset() {
	listener.mux.Lock()
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *CommonEventListener) Subscribe(s *titan.MessageSubscriber) {
}
