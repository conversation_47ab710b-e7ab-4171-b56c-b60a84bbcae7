// This code was autogenerated from service/domains/text_module_common.proto, do not edit.

package text_module_common

import (
	"encoding/json"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	patient_encounter "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type TextModuleContent struct {
	Text string           `json:"text"`
	Data []TextModuleNode `json:"data"`
}

type TextModuleNode struct {
	Type          TextModuleNodeType                      `json:"type"`
	Children      []TextModuleNode                        `json:"children"`
	Text          *TextModuleTextNode                     `json:"text"`
	Placeholder   *TextModulePlaceholderNode              `json:"placeholder"`
	Questionnaire *TextModuleQuestionnaireNode            `json:"questionnaire"`
	Variable      *TextModuleVariableNode                 `json:"variable"`
	OmimG         *patient_encounter.AdditionalInfoParent `json:"omimG"`
}

type TextModuleTextNode struct {
	Value string `json:"value"`
}

type TextModulePlaceholderNode struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

type TextModuleQuestionnaireNode struct {
	Label        string                    `json:"label"`
	QuestionType QuestionnaireQuestionType `json:"questionType"`
	Answers      []Answer                  `json:"answers"`
	Value        *string                   `json:"value"`
}

type Answer struct {
	Label      string     `json:"label"`
	AnswerType AnswerType `json:"answerType"`
	Value      string     `json:"value"`
}

type TextModuleVariableNode struct {
}

type TextModule struct {
	Id           *uuid.UUID         `json:"id" bson:"omitempty"`
	UseFor       []TextModuleUseFor `json:"useFor" validate:"required"`
	TextShortcut string             `json:"textShortcut" validate:"required"`
	ModuleType   ModuleType         `json:"moduleType" validate:"required"`
	Content      TextModuleContent  `json:"content" validate:"required"`
	Status       TextModuleStatus   `json:"status" validate:"required"`
	BsnrId       *uuid.UUID         `json:"bsnrId"`
}

type TextModulePaginationRequest struct {
	Query    string             `json:"query"`
	Page     *int64             `json:"page"`
	PageSize *int64             `json:"pageSize"`
	UseFors  []TextModuleUseFor `json:"useFors"`
	BsnrId   *uuid.UUID         `json:"bsnrId"`
}

type TextModulePaginationResponse struct {
	TextModules []TextModule `json:"textModules"`
	Page        int64        `json:"page"`
	Total       int64        `json:"total"`
}

// enum definitions
type TextModuleUseFor string

const (
	TextModuleUseFor_Anamnesis    TextModuleUseFor = "TextModuleUseFor_Anamnesis"
	TextModuleUseFor_Cave         TextModuleUseFor = "TextModuleUseFor_Cave"
	TextModuleUseFor_Findings     TextModuleUseFor = "TextModuleUseFor_Findings"
	TextModuleUseFor_Note         TextModuleUseFor = "TextModuleUseFor_Note"
	TextModuleUseFor_Therapy      TextModuleUseFor = "TextModuleUseFor_Therapy"
	TextModuleUseFor_OmimGChain   TextModuleUseFor = "TextModuleUseFor_OmimGChain"
	TextModuleUseFor_Form         TextModuleUseFor = "TextModuleUseFor_Form"
	TextModuleUseFor_BMP          TextModuleUseFor = "TextModuleUseFor_BMP"
	TextModuleUseFor_Doctorletter TextModuleUseFor = "TextModuleUseFor_Doctorletter"
	TextModuleUseFor_HGNC         TextModuleUseFor = "TextModuleUseFor_HGNC"
)

type ModuleType string

const (
	ModuleType_FreeText      ModuleType = "ModuleType_FreeText"
	ModuleType_Questionnaire ModuleType = "ModuleType_Questionnaire"
)

type TextModuleStatus string

const (
	TextModuleStatus_Active     TextModuleStatus = "TextModuleStatus_Active"
	TextModuleNodeType_Deactive TextModuleStatus = "TextModuleNodeType_Deactive"
)

type TextModuleNodeType string

const (
	TextModuleNodeType_Text           TextModuleNodeType = "TextModuleNodeType_Text"
	TextModuleNodeType_Placeholder    TextModuleNodeType = "TextModuleNodeType_Placeholder"
	TextModuleNodeType_Questionnaire  TextModuleNodeType = "TextModuleNodeType_Questionnaire"
	TextModuleNodeType_Variable       TextModuleNodeType = "TextModuleNodeType_Variable"
	TextModuleNodeType_AdditionalInfo TextModuleNodeType = "TextModuleNodeType_AdditionalInfo"
	TextModuleNodeType_LineBreak      TextModuleNodeType = "TextModuleNodeType_LineBreak"
)

type QuestionnaireQuestionType string

const (
	QuestionnaireQuestionType_SingleSelection   QuestionnaireQuestionType = "QuestionnaireQuestionType_SingleSelection"
	QuestionnaireQuestionType_MultipleSelection QuestionnaireQuestionType = "QuestionnaireQuestionType_MultipleSelection"
	QuestionnaireQuestionType_Freetext          QuestionnaireQuestionType = "QuestionnaireQuestionType_Freetext"
)

type AnswerType string

const (
	AnswerType_Select AnswerType = "AnswerType_Select"
	AnswerType_Others AnswerType = "AnswerType_Others"
)

// Define constants
const NATS_SUBJECT = "" // nats subject this service will listen to

// service event constants

// message event constants

// Define service interface -------------------------------------------------------------

// Define service proxy -------------------------------------------------------------------

// Define service router -----------------------------------------------------------------

// Define client ----------------------------------------------------------------------------------------------

// Define event Notifier -----------------------------------------------------------------------------------------
type TextModuleCommonNotifier struct {
	client *titan.Client
}

func NewTextModuleCommonNotifier() *TextModuleCommonNotifier {
	client := titan.GetDefaultClient()
	return &TextModuleCommonNotifier{client}
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type TextModuleCommonSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewTextModuleCommonSocketNotifier(socket *socket_api.SocketServiceClient) *TextModuleCommonSocketNotifier {
	return &TextModuleCommonSocketNotifier{socket}
}

// -----------------------------------------------
// Test event listener
type TextModuleCommonEventListener struct {
	mux sync.Mutex
}

func (listener *TextModuleCommonEventListener) Reset() {
	listener.mux.Lock()
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *TextModuleCommonEventListener) Subscribe(s *titan.MessageSubscriber) {
}
