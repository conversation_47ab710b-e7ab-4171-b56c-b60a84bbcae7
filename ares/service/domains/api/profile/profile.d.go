// This code was autogenerated from service/domains/profile.proto, do not edit.

package profile

import (
	"encoding/json"
	"errors"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	admin_bff "git.tutum.dev/medi/tutum/ares/app/admin/api/admin_bff"

	common "git.tutum.dev/medi/tutum/ares/service/domains/api/common"

	patient_profile_common "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type GetByIdsRequest struct {
	OriginalIds []uuid.UUID `json:"originalIds" validate:"required"`
}

type GetByLanrIDRequest struct {
	Lanr string `json:"lanr"`
}

type GetByHzvIDRequest struct {
	HavgId string `json:"havgId"`
}

type GetByMediIDRequest struct {
	MediId string `json:"mediId"`
}

type GetByBsnrIdRequest struct {
	BsnrId *uuid.UUID `json:"bsnrId" validate:"required"`
}

type Contract struct {
	ContractId string `json:"contractId"`
	StartDate  int64  `json:"startDate"`
	EndDate    *int64 `json:"endDate"`
}

type EmployeeProfileResponse struct {
	Id                     *uuid.UUID                             `json:"id"`
	FullName               string                                 `json:"fullName"`
	FirstName              string                                 `json:"firstName"`
	LastName               string                                 `json:"lastName"`
	Title                  *string                                `json:"title"`
	Dob                    *int64                                 `json:"dob"`
	Salutation             *patient_profile_common.Salutation     `json:"salutation"`
	Email                  *string                                `json:"email"`
	Phone                  *string                                `json:"phone"`
	Address                *string                                `json:"address"`
	Lanr                   *string                                `json:"lanr"`
	Bsnr                   string                                 `json:"bsnr"`
	HavgId                 *string                                `json:"havgId"`
	HavgVpId               *string                                `json:"havgVpId"`
	MediverbundId          *string                                `json:"mediverbundId"`
	MediverbundVpId        *string                                `json:"mediverbundVpId"`
	AreaOfExpertise        *[]string                              `json:"areaOfExpertise"`
	MobilePhone            string                                 `json:"mobilePhone" bson:"omitempty"`
	Okv                    *string                                `json:"okv"`
	HasHzvContracts        bool                                   `json:"hasHzvContracts"`
	HasFavContracts        bool                                   `json:"hasFavContracts"`
	ZipCode                string                                 `json:"zipCode"`
	Street                 string                                 `json:"street"`
	Fax                    string                                 `json:"fax"`
	AdditionalName         *patient_profile_common.AdditionalName `json:"additionalName"`
	IntendWord             *patient_profile_common.IntendWord     `json:"intendWord"`
	Initial                string                                 `json:"initial"`
	DmpPrograms            *[]string                              `json:"dmpPrograms"`
	JobDescription         *string                                `json:"jobDescription"`
	MarkAsBillingDoctor    bool                                   `json:"markAsBillingDoctor"`
	BsnrId                 *uuid.UUID                             `json:"bsnrId"`
	EmployeeProfileId      *uuid.UUID                             `json:"employeeProfileId"`
	PseudoLanr             *string                                `json:"pseudoLanr"`
	TeamNumbers            *[]string                              `json:"teamNumbers"`
	DoctorStamp            string                                 `json:"doctorStamp"`
	BsnrCity               string                                 `json:"bsnrCity"`
	BsnrPracticeStamp      string                                 `json:"bsnrPracticeStamp"`
	BankInformations       []*common.BankInformation              `json:"bankInformations"`
	MarkAsEmployedDoctor   bool                                   `json:"markAsEmployedDoctor"`
	ResponsibleDoctorId    *uuid.UUID                             `json:"responsibleDoctorId"`
	RepresentativeDoctorId *uuid.UUID                             `json:"representativeDoctorId"`
	BsnrName               *string                                `json:"bsnrName"`
	BsnrStreet             string                                 `json:"bsnrStreet"`
	BsnrNumber             string                                 `json:"bsnrNumber"`
	BsnrPostCode           string                                 `json:"bsnrPostCode"`
	BsnrPhoneNumber        string                                 `json:"bsnrPhoneNumber"`
	BsnrFaxNumber          string                                 `json:"bsnrFaxNumber"`
	IsParticipationActive  bool                                   `json:"isParticipationActive"`
	Types                  []common.UserType                      `json:"types"`
	ExternalId             string                                 `json:"externalId"`
	BsnrFacilityType       string                                 `json:"bsnrFacilityType"`
	DeviceId               *uuid.UUID                             `json:"deviceId"`
	UserName               string                                 `json:"userName"`
	OrgId                  string                                 `json:"orgId"`
	HpmEndpoint            string                                 `json:"hpmEndpoint"`
	CreatedDate            int64                                  `json:"createdDate"`
	Status                 common.EmployeeStatus                  `json:"status"`
	HzvContracts           []*admin_bff.Contract                  `json:"hzvContracts"`
	FavContracts           []*admin_bff.Contract                  `json:"favContracts"`
	IsDoctor               bool                                   `json:"isDoctor"`
	Bsnrs                  []string                               `json:"bsnrs"`
	BsnrIds                []uuid.UUID                            `json:"bsnrIds"`
	EHKSType               *common.EHKSType                       `json:"eHKSType"`
}

type EmployeeProfilesResponse struct {
	Profiles []*EmployeeProfileResponse `json:"profiles"`
}

type EmployeeProfileDeleteResponse struct {
	Result int64 `json:"result"`
}

type EmployeeProfileDeleteRequest struct {
	OriginalId *uuid.UUID `json:"originalId" validate:"required"`
}

type EmployeeProfileGetRequest struct {
	OriginalId *uuid.UUID `json:"originalId" validate:"required"`
	BsnrId     *uuid.UUID `json:"bsnrId"`
}

type EmployeeProfileRequest struct {
	OriginalId             *uuid.UUID                             `json:"originalId" validate:"required"`
	FirstName              string                                 `json:"firstName" validate:"trim"`
	LastName               string                                 `json:"lastName" validate:"trim"`
	Title                  *string                                `json:"title"`
	Dob                    *int64                                 `json:"dob"`
	Salutation             *patient_profile_common.Salutation     `json:"salutation"`
	Email                  *string                                `json:"email"`
	Phone                  *string                                `json:"phone"`
	Address                *string                                `json:"address"`
	Lanr                   *string                                `json:"lanr"`
	Bsnr                   string                                 `json:"bsnr"`
	HavgId                 *string                                `json:"havgId"`
	HavgVpId               *string                                `json:"havgVpId"`
	MediverbundId          *string                                `json:"mediverbundId"`
	MediverbundVpId        *string                                `json:"mediverbundVpId"`
	AreaOfExpertise        *[]string                              `json:"areaOfExpertise"`
	MobilePhone            string                                 `json:"mobilePhone" bson:"omitempty"`
	Okv                    *string                                `json:"okv"`
	HasHzvContracts        bool                                   `json:"hasHzvContracts"`
	HasFavContracts        bool                                   `json:"hasFavContracts"`
	AdditionalName         *patient_profile_common.AdditionalName `json:"additionalName"`
	IntendWord             *patient_profile_common.IntendWord     `json:"intendWord"`
	Initial                string                                 `json:"initial"`
	DmpPrograms            *[]string                              `json:"dmpPrograms"`
	JobDescription         *string                                `json:"jobDescription"`
	MarkAsBillingDoctor    bool                                   `json:"markAsBillingDoctor"`
	BsnrId                 *uuid.UUID                             `json:"bsnrId"`
	PseudoLanr             *string                                `json:"pseudoLanr"`
	TeamNumbers            *[]string                              `json:"teamNumbers"`
	DoctorStamp            string                                 `json:"doctorStamp"`
	BsnrPracticeStamp      string                                 `json:"bsnrPracticeStamp"`
	BankInformations       []*common.BankInformation              `json:"bankInformations"`
	MarkAsEmployedDoctor   bool                                   `json:"markAsEmployedDoctor"`
	ResponsibleDoctorId    *uuid.UUID                             `json:"responsibleDoctorId"`
	RepresentativeDoctorId *uuid.UUID                             `json:"representativeDoctorId"`
	BsnrName               *string                                `json:"bsnrName"`
	BsnrStreet             string                                 `json:"bsnrStreet"`
	BsnrNumber             string                                 `json:"bsnrNumber"`
	BsnrPostCode           string                                 `json:"bsnrPostCode"`
	Password               string                                 `json:"password"`
	IsParticipationActive  bool                                   `json:"isParticipationActive"`
	Types                  []common.UserType                      `json:"types"`
	DeviceId               *uuid.UUID                             `json:"deviceId"`
	UserName               string                                 `json:"userName"`
	ExternalId             string                                 `json:"externalId"`
	HpmEndpoint            string                                 `json:"hpmEndpoint"`
	HzvContracts           []*admin_bff.Contract                  `json:"hzvContracts"`
	FavContracts           []*admin_bff.Contract                  `json:"favContracts"`
	Bsnrs                  []string                               `json:"bsnrs"`
	BsnrIds                []uuid.UUID                            `json:"bsnrIds"`
	EHKSType               *common.EHKSType                       `json:"eHKSType"`
	IsDoctor               bool                                   `json:"isDoctor"`
}

type GetAllInitialResponse struct {
	Data []string `json:"data"`
}

type GetEmployeeByExternalIdRequest struct {
	ExternalId string `json:"externalId" validate:"required"`
}

type UpdateDeviceRequest struct {
	DeviceId *uuid.UUID `json:"deviceId" validate:"required"`
}

type GetEmployeeByDeviceIdRequest struct {
	DeviceId *uuid.UUID `json:"deviceId" validate:"required"`
}

type GetByCareProviderIdRequest struct {
	CareProviderId *uuid.UUID `json:"careProviderId" validate:"required"`
}

type SearchEmployeeByNameRequest struct {
	Name              string                    `json:"name"`
	CareProviderId    *uuid.UUID                `json:"careProviderId" validate:"required"`
	PaginationRequest *common.PaginationRequest `json:"paginationRequest"`
}

type SearchEmployeeByNameResponse struct {
	Profiles           []*EmployeeProfileResponse `json:"profiles"`
	PaginationResponse *common.PaginationResponse `json:"paginationResponse"`
}

type UpdateEmployeeStatusRequest struct {
	ExternalId string                `json:"externalId" validate:"required"`
	Status     common.EmployeeStatus `json:"status"`
}

type ResetEmployeePasswordRequest struct {
	ExternalId string `json:"externalId" validate:"required"`
}

type ResetEmployeePasswordResponse struct {
	Password string `json:"password" validate:"required"`
}

type ResetEmployee2FARequest struct {
	ExternalId string `json:"externalId" validate:"required"`
}

type GetByIdRequest struct {
	PatientId *uuid.UUID `json:"patientId"`
}

type PatientProfileCreateRequest struct {
	PatientId        *uuid.UUID                    `json:"patientId"`
	Title            string                        `json:"title"`
	LastName         string                        `json:"lastName" validate:"notblank"`
	FirstName        string                        `json:"firstName" validate:"notblank"`
	DateOfBirth      *int64                        `json:"dateOfBirth" validate:"required"`
	Gender           patient_profile_common.Gender `json:"gender" validate:"required"`
	Street           string                        `json:"street" validate:"notblank"`
	HouseNumber      string                        `json:"houseNumber" validate:"notblank"`
	PostCode         string                        `json:"postCode" validate:"notblank"`
	CityState        string                        `json:"cityState" validate:"notblank"`
	Country          string                        `json:"country"`
	PrimaryContact   string                        `json:"primaryContact"`
	SecondaryContact string                        `json:"secondaryContact"`
	ContactPerson    string                        `json:"contactPerson"`
	Email            string                        `json:"email"`
	IntendWords      string                        `json:"intendWords"`
}

type PatientProfileUpdateRequest struct {
	PatientId                        *uuid.UUID                    `json:"patientId" validate:"required"`
	Title                            string                        `json:"title"`
	FirstName                        string                        `json:"firstName" validate:"notblank"`
	LastName                         string                        `json:"lastName" validate:"notblank"`
	DateOfBirth                      *int64                        `json:"dateOfBirth" validate:"required"`
	Gender                           patient_profile_common.Gender `json:"gender" validate:"required"`
	Street                           string                        `json:"street" validate:"notblank"`
	HouseNumber                      string                        `json:"houseNumber" validate:"notblank"`
	PostCode                         string                        `json:"postCode" validate:"notblank"`
	CityState                        string                        `json:"cityState" validate:"notblank"`
	Country                          string                        `json:"country"`
	PrimaryContact                   string                        `json:"primaryContact"`
	SecondaryContact                 string                        `json:"secondaryContact"`
	ContactPerson                    string                        `json:"contactPerson"`
	Email                            string                        `json:"email"`
	IntendWords                      string                        `json:"intendWords"`
	AdditionalName                   string                        `json:"additionalName"`
	Job                              string                        `json:"job"`
	Company                          string                        `json:"company"`
	PreviousDoctor                   string                        `json:"previousDoctor"`
	AdditionalAddress                string                        `json:"additionalAddress"`
	Postfach                         string                        `json:"postfach"`
	PlzPostfach                      string                        `json:"plzPostfach"`
	StadtPostfach                    string                        `json:"stadtPostfach"`
	IsEmployee                       IsEmploymentAnswer            `json:"isEmployee"`
	JobStatus                        string                        `json:"jobStatus"`
	WorkingHourInWeek                float32                       `json:"workingHourInWeek"`
	WorkActivity1                    WorkActivity1                 `json:"workActivity1"`
	WorkActivity2                    WorkActivity2                 `json:"workActivity2"`
	EmploymentInformationLastUpdated int64                         `json:"employmentInformationLastUpdated"`
	SpecialProblemAtWork             string                        `json:"specialProblemAtWork"`
}

type UpdatePatientMedicalDataRequest struct {
	PatientId          *uuid.UUID                                 `json:"patientId" validate:"required"`
	PatientMedicalData *patient_profile_common.PatientMedicalData `json:"patientMedicalData"`
}

type UpdatePatientMedicalDataResponse struct {
	MedicalDataHistoryId *uuid.UUID                                 `json:"medicalDataHistoryId"`
	PatientMedicalData   *patient_profile_common.PatientMedicalData `json:"patientMedicalData"`
}

type PatientProfile struct {
	Id                      *uuid.UUID                                 `json:"id"`
	FirstName               string                                     `json:"firstName"`
	LastName                string                                     `json:"lastName"`
	DateOfBirth             *int64                                     `json:"dateOfBirth" validate:"required"`
	HpmInformation          []*HpmInformation                          `json:"hpmInformation"`
	PatientMedicalData      *patient_profile_common.PatientMedicalData `json:"patientMedicalData"`
	PatientInfo             *patient_profile_common.PatientInfo        `json:"patientInfo"`
	EmploymentInfoUpdatedAt *int64                                     `json:"employmentInfoUpdatedAt"`
	MedicalDataUpdatedAt    *int64                                     `json:"medicalDataUpdatedAt"`
}

type PatientResponse struct {
	Id             *uuid.UUID `json:"id"`
	AccountId      *uuid.UUID `json:"accountId"`
	CareProviderId *uuid.UUID `json:"careProviderId"`
}

type HpmInformation struct {
	CheckedDate int64                `json:"checkedDate"`
	ContractId  string               `json:"contractId"`
	Status      HpmInformationStatus `json:"status"`
}

type UpdateHpmInformationRequest struct {
	PatientId      *uuid.UUID        `json:"patientId" validate:"required"`
	HpmInformation []*HpmInformation `json:"hpmInformation" validate:"required"`
}

type GetProfileByIdsRequest struct {
	Ids []uuid.UUID `json:"ids"`
}

type GetProfileByIdsResponse struct {
	PatientProfiles []*PatientProfile `json:"patientProfiles"`
}

type EventHealthInsuranceChange struct {
	InsuranceNumber string     `json:"insuranceNumber"`
	IkNumber        int32      `json:"ikNumber"`
	PatientId       *uuid.UUID `json:"patientId"`
}

// enum definitions
type WorkActivity1 string

const (
	Physical WorkActivity1 = "Physical"
	Mental   WorkActivity1 = "Mental"
)

type WorkActivity2 string

const (
	Standing WorkActivity2 = "Standing"
	Sitting  WorkActivity2 = "Sitting"
)

type IsEmploymentAnswer string

const (
	Yes IsEmploymentAnswer = "Yes"
	No  IsEmploymentAnswer = "No"
)

type HpmInformationStatus string

const (
	HpmInformationStatus_Active   HpmInformationStatus = "Active"
	HpmInformationStatus_InActive HpmInformationStatus = "InActive"
	HpmInformationStatus_Error    HpmInformationStatus = "Error"
)

// Define constants
const NATS_SUBJECT = "api.service.domains" // nats subject this service will listen to

// service event constants
const EVENT_GetAll = "api.service.domains.EmployeeProfileService.GetAll"
const LEGACY_TOPIC_GetAll = "/api/service/domains/employee/profile/getAll"
const EVENT_SearchEmployeeByName = "api.service.domains.EmployeeProfileService.SearchEmployeeByName"
const LEGACY_TOPIC_SearchEmployeeByName = "/api/service/domains/employee/profile/searchEmployeeByName"
const EVENT_GetEmployeeByExternalId = "api.service.domains.EmployeeProfileService.GetEmployeeByExternalId"
const LEGACY_TOPIC_GetEmployeeByExternalId = "/api/service/domains/employee/profile/getEmployeeByExternalId"
const EVENT_GetAllInitial = "api.service.domains.EmployeeProfileService.GetAllInitial"
const LEGACY_TOPIC_GetAllInitial = "/api/service/domains/employee/profile/getAllInitial"
const EVENT_GetMyEmployeeProfile = "api.service.domains.EmployeeProfileService.GetMyEmployeeProfile"
const LEGACY_TOPIC_GetMyEmployeeProfile = "/api/service/domains/employee/profile/getMyEmployeeProfile"
const EVENT_GetEmployeeProfileByLanrId = "api.service.domains.EmployeeProfileService.GetEmployeeProfileByLanrId"
const LEGACY_TOPIC_GetEmployeeProfileByLanrId = "/api/service/domains/employee/profile/getEmployeeProfileByLanrId"
const EVENT_GetEmployeeProfilesByBsnrId = "api.service.domains.EmployeeProfileService.GetEmployeeProfilesByBsnrId"
const LEGACY_TOPIC_GetEmployeeProfilesByBsnrId = "/api/service/domains/employee/profile/getEmployeeProfilesByBsnrId"
const EVENT_GetEmployeeProfileByHzvId = "api.service.domains.EmployeeProfileService.GetEmployeeProfileByHzvId"
const LEGACY_TOPIC_GetEmployeeProfileByHzvId = "/api/service/domains/employee/profile/getEmployeeProfileByHzvId"
const EVENT_GetEmployeeProfileByMediId = "api.service.domains.EmployeeProfileService.GetEmployeeProfileByMediId"
const LEGACY_TOPIC_GetEmployeeProfileByMediId = "/api/service/domains/employee/profile/getEmployeeProfileByMediId"
const EVENT_GetEmployeeProfileById = "api.service.domains.EmployeeProfileService.GetEmployeeProfileById"
const LEGACY_TOPIC_GetEmployeeProfileById = "/api/service/domains/employee/profile/getEmployeeProfileById"
const EVENT_GetEmployeeProfileByIds = "api.service.domains.EmployeeProfileService.GetEmployeeProfileByIds"
const LEGACY_TOPIC_GetEmployeeProfileByIds = "/api/service/domains/employee/profile/getEmployeeProfileByIds"
const EVENT_CreateEmployeeProfile = "api.service.domains.EmployeeProfileService.CreateEmployeeProfile"
const LEGACY_TOPIC_CreateEmployeeProfile = "/api/service/domains/employee/profile/createEmployeeProfile"
const EVENT_CreateEmployeeProfileWithoutAuth = "api.service.domains.EmployeeProfileService.CreateEmployeeProfileWithoutAuth"
const LEGACY_TOPIC_CreateEmployeeProfileWithoutAuth = "/api/service/domains/employee/profile/createEmployeeProfileWithoutAuth"
const EVENT_UpdateEmployeeProfile = "api.service.domains.EmployeeProfileService.UpdateEmployeeProfile"
const LEGACY_TOPIC_UpdateEmployeeProfile = "/api/service/domains/employee/profile/updateEmployeeProfile"
const EVENT_UpdateEmployeeStatus = "api.service.domains.EmployeeProfileService.UpdateEmployeeStatus"
const LEGACY_TOPIC_UpdateEmployeeStatus = "/api/service/domains/employee/profile/updateEmployeeStatus"
const EVENT_DeleteEmployeeProfileById = "api.service.domains.EmployeeProfileService.DeleteEmployeeProfileById"
const LEGACY_TOPIC_DeleteEmployeeProfileById = "/api/service/domains/employee/profile/deleteEmployeeProfileById"
const EVENT_GetEmployeeByIds = "api.service.domains.EmployeeProfileService.GetEmployeeByIds"
const LEGACY_TOPIC_GetEmployeeByIds = "/api/service/domains/employee/profile/getEmployeeByIds"
const EVENT_UpdateDevice = "api.service.domains.EmployeeProfileService.UpdateDevice"
const LEGACY_TOPIC_UpdateDevice = "/api/service/domains/employee/profile/updateDevice"
const EVENT_GetEmployeeByDeviceId = "api.service.domains.EmployeeProfileService.GetEmployeeByDeviceId"
const LEGACY_TOPIC_GetEmployeeByDeviceId = "/api/service/domains/employee/profile/getEmployeeByDeviceId"
const EVENT_ResetEmployeePassword = "api.service.domains.EmployeeProfileService.ResetEmployeePassword"
const LEGACY_TOPIC_ResetEmployeePassword = "/api/service/domains/employee/profile/resetEmployeePassword"
const EVENT_ResetEmployee2FA = "api.service.domains.EmployeeProfileService.ResetEmployee2FA"
const LEGACY_TOPIC_ResetEmployee2FA = "/api/service/domains/employee/profile/resetEmployee2FA"
const EVENT_UpdateDefaultBsnrOfEmployee = "api.service.domains.EmployeeProfileService.UpdateDefaultBsnrOfEmployee"
const LEGACY_TOPIC_UpdateDefaultBsnrOfEmployee = "/api/service/domains/employee/profile/updateDefaultBsnrOfEmployee"
const EVENT_HandleEventDeviceChange = "api.service.domains.EmployeeProfileService.HandleEventDeviceChange"
const LEGACY_TOPIC_HandleEventDeviceChange = "/api/service/domains/employee/profile/handleEventDeviceChange"
const EVENT_CreateProfile = "api.service.domains.PatientProfileService.CreateProfile"
const LEGACY_TOPIC_CreateProfile = "/api/service/domains/patient/profile/createProfile"
const EVENT_UpdateProfile = "api.service.domains.PatientProfileService.UpdateProfile"
const LEGACY_TOPIC_UpdateProfile = "/api/service/domains/patient/profile/updateProfile"
const EVENT_CreatePatientMedicalData = "api.service.domains.PatientProfileService.CreatePatientMedicalData"
const LEGACY_TOPIC_CreatePatientMedicalData = "/api/service/domains/patient/profile/createPatientMedicalData"
const EVENT_GetProfileById = "api.service.domains.PatientProfileService.GetProfileById"
const LEGACY_TOPIC_GetProfileById = "/api/service/domains/patient/profile/getProfileById"
const EVENT_GetProfileByIds = "api.service.domains.PatientProfileService.GetProfileByIds"
const LEGACY_TOPIC_GetProfileByIds = "/api/service/domains/patient/profile/getProfileByIds"
const EVENT_UpdateHpmInformation = "api.service.domains.PatientProfileService.UpdateHpmInformation"
const LEGACY_TOPIC_UpdateHpmInformation = "/api/service/domains/patient/profile/updateHpmInformation"

// message event constants
const EVENT_HealthInsuranceChange = "api.service.domains.ServiceDomainsProfile.HealthInsuranceChange"

// Define service interface -------------------------------------------------------------
type EmployeeProfileService interface {
	GetAll(ctx *titan.Context, request *GetByCareProviderIdRequest) (*EmployeeProfilesResponse, error)
	SearchEmployeeByName(ctx *titan.Context, request *SearchEmployeeByNameRequest) (*SearchEmployeeByNameResponse, error)
	GetEmployeeByExternalId(ctx *titan.Context, request *GetEmployeeByExternalIdRequest) (*EmployeeProfileResponse, error)
	GetAllInitial(ctx *titan.Context) (*GetAllInitialResponse, error)
	GetMyEmployeeProfile(ctx *titan.Context) (*EmployeeProfileResponse, error)
	GetEmployeeProfileByLanrId(ctx *titan.Context, request *GetByLanrIDRequest) (*EmployeeProfileResponse, error)
	GetEmployeeProfilesByBsnrId(ctx *titan.Context, request *GetByBsnrIdRequest) (*EmployeeProfilesResponse, error)
	GetEmployeeProfileByHzvId(ctx *titan.Context, request *GetByHzvIDRequest) (*EmployeeProfileResponse, error)
	GetEmployeeProfileByMediId(ctx *titan.Context, request *GetByMediIDRequest) (*EmployeeProfileResponse, error)
	GetEmployeeProfileById(ctx *titan.Context, request *EmployeeProfileGetRequest) (*EmployeeProfileResponse, error)
	GetEmployeeProfileByIds(ctx *titan.Context, request *GetByIdsRequest) (*EmployeeProfilesResponse, error)
	CreateEmployeeProfile(ctx *titan.Context, request *EmployeeProfileRequest) (*EmployeeProfileResponse, error)
	CreateEmployeeProfileWithoutAuth(ctx *titan.Context, request *EmployeeProfileRequest) (*EmployeeProfileResponse, error)
	UpdateEmployeeProfile(ctx *titan.Context, request *EmployeeProfileRequest) (*EmployeeProfileResponse, error)
	UpdateEmployeeStatus(ctx *titan.Context, request *UpdateEmployeeStatusRequest) error
	DeleteEmployeeProfileById(ctx *titan.Context, request *EmployeeProfileDeleteRequest) (*EmployeeProfileDeleteResponse, error)
	GetEmployeeByIds(ctx *titan.Context, request *GetByIdsRequest) (*EmployeeProfilesResponse, error)
	UpdateDevice(ctx *titan.Context, request *UpdateDeviceRequest) error
	GetEmployeeByDeviceId(ctx *titan.Context, request *GetEmployeeByDeviceIdRequest) (*EmployeeProfilesResponse, error)
	ResetEmployeePassword(ctx *titan.Context, request *ResetEmployeePasswordRequest) (*ResetEmployeePasswordResponse, error)
	ResetEmployee2FA(ctx *titan.Context, request *ResetEmployee2FARequest) error
	UpdateDefaultBsnrOfEmployee(ctx *titan.Context, request *admin_bff.UpdateDefaultBsnrOfEmployeeRequest) error
	HandleEventDeviceChange(ctx *titan.Context, request *admin_bff.EventDeviceChange) error
}
type PatientProfileService interface {
	CreateProfile(ctx *titan.Context, request *PatientProfileCreateRequest) (*PatientProfile, error)
	UpdateProfile(ctx *titan.Context, request *PatientProfileUpdateRequest) (*PatientProfile, error)
	CreatePatientMedicalData(ctx *titan.Context, request *UpdatePatientMedicalDataRequest) (*UpdatePatientMedicalDataResponse, error)
	GetProfileById(ctx *titan.Context, request *GetByIdRequest) (*PatientProfile, error)
	GetProfileByIds(ctx *titan.Context, request *GetProfileByIdsRequest) (*GetProfileByIdsResponse, error)
	UpdateHpmInformation(ctx *titan.Context, request *UpdateHpmInformationRequest) error
}

// Define service proxy -------------------------------------------------------------------
type EmployeeProfileServiceProxy struct {
	service EmployeeProfileService
}

func (srv *EmployeeProfileServiceProxy) GetAll(ctx *titan.Context, request *GetByCareProviderIdRequest) (*EmployeeProfilesResponse, error) {
	return srv.service.GetAll(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) SearchEmployeeByName(ctx *titan.Context, request *SearchEmployeeByNameRequest) (*SearchEmployeeByNameResponse, error) {
	return srv.service.SearchEmployeeByName(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) GetEmployeeByExternalId(ctx *titan.Context, request *GetEmployeeByExternalIdRequest) (*EmployeeProfileResponse, error) {
	return srv.service.GetEmployeeByExternalId(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) GetAllInitial(ctx *titan.Context) (*GetAllInitialResponse, error) {
	return srv.service.GetAllInitial(ctx)
}
func (srv *EmployeeProfileServiceProxy) GetMyEmployeeProfile(ctx *titan.Context) (*EmployeeProfileResponse, error) {
	return srv.service.GetMyEmployeeProfile(ctx)
}
func (srv *EmployeeProfileServiceProxy) GetEmployeeProfileByLanrId(ctx *titan.Context, request *GetByLanrIDRequest) (*EmployeeProfileResponse, error) {
	return srv.service.GetEmployeeProfileByLanrId(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) GetEmployeeProfilesByBsnrId(ctx *titan.Context, request *GetByBsnrIdRequest) (*EmployeeProfilesResponse, error) {
	return srv.service.GetEmployeeProfilesByBsnrId(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) GetEmployeeProfileByHzvId(ctx *titan.Context, request *GetByHzvIDRequest) (*EmployeeProfileResponse, error) {
	return srv.service.GetEmployeeProfileByHzvId(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) GetEmployeeProfileByMediId(ctx *titan.Context, request *GetByMediIDRequest) (*EmployeeProfileResponse, error) {
	return srv.service.GetEmployeeProfileByMediId(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) GetEmployeeProfileById(ctx *titan.Context, request *EmployeeProfileGetRequest) (*EmployeeProfileResponse, error) {
	return srv.service.GetEmployeeProfileById(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) GetEmployeeProfileByIds(ctx *titan.Context, request *GetByIdsRequest) (*EmployeeProfilesResponse, error) {
	return srv.service.GetEmployeeProfileByIds(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) CreateEmployeeProfile(ctx *titan.Context, request *EmployeeProfileRequest) (*EmployeeProfileResponse, error) {
	return srv.service.CreateEmployeeProfile(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) CreateEmployeeProfileWithoutAuth(ctx *titan.Context, request *EmployeeProfileRequest) (*EmployeeProfileResponse, error) {
	return srv.service.CreateEmployeeProfileWithoutAuth(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) UpdateEmployeeProfile(ctx *titan.Context, request *EmployeeProfileRequest) (*EmployeeProfileResponse, error) {
	return srv.service.UpdateEmployeeProfile(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) UpdateEmployeeStatus(ctx *titan.Context, request *UpdateEmployeeStatusRequest) error {
	return srv.service.UpdateEmployeeStatus(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) DeleteEmployeeProfileById(ctx *titan.Context, request *EmployeeProfileDeleteRequest) (*EmployeeProfileDeleteResponse, error) {
	return srv.service.DeleteEmployeeProfileById(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) GetEmployeeByIds(ctx *titan.Context, request *GetByIdsRequest) (*EmployeeProfilesResponse, error) {
	return srv.service.GetEmployeeByIds(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) UpdateDevice(ctx *titan.Context, request *UpdateDeviceRequest) error {
	return srv.service.UpdateDevice(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) GetEmployeeByDeviceId(ctx *titan.Context, request *GetEmployeeByDeviceIdRequest) (*EmployeeProfilesResponse, error) {
	return srv.service.GetEmployeeByDeviceId(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) ResetEmployeePassword(ctx *titan.Context, request *ResetEmployeePasswordRequest) (*ResetEmployeePasswordResponse, error) {
	return srv.service.ResetEmployeePassword(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) ResetEmployee2FA(ctx *titan.Context, request *ResetEmployee2FARequest) error {
	return srv.service.ResetEmployee2FA(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) UpdateDefaultBsnrOfEmployee(ctx *titan.Context, request *admin_bff.UpdateDefaultBsnrOfEmployeeRequest) error {
	return srv.service.UpdateDefaultBsnrOfEmployee(ctx, request)
}
func (srv *EmployeeProfileServiceProxy) HandleEventDeviceChange(ctx *titan.Context, request *admin_bff.EventDeviceChange) error {
	return srv.service.HandleEventDeviceChange(ctx, request)
}

type PatientProfileServiceProxy struct {
	service PatientProfileService
}

func (srv *PatientProfileServiceProxy) CreateProfile(ctx *titan.Context, request *PatientProfileCreateRequest) (*PatientProfile, error) {
	return srv.service.CreateProfile(ctx, request)
}
func (srv *PatientProfileServiceProxy) UpdateProfile(ctx *titan.Context, request *PatientProfileUpdateRequest) (*PatientProfile, error) {
	return srv.service.UpdateProfile(ctx, request)
}
func (srv *PatientProfileServiceProxy) CreatePatientMedicalData(ctx *titan.Context, request *UpdatePatientMedicalDataRequest) (*UpdatePatientMedicalDataResponse, error) {
	return srv.service.CreatePatientMedicalData(ctx, request)
}
func (srv *PatientProfileServiceProxy) GetProfileById(ctx *titan.Context, request *GetByIdRequest) (*PatientProfile, error) {
	return srv.service.GetProfileById(ctx, request)
}
func (srv *PatientProfileServiceProxy) GetProfileByIds(ctx *titan.Context, request *GetProfileByIdsRequest) (*GetProfileByIdsResponse, error) {
	return srv.service.GetProfileByIds(ctx, request)
}
func (srv *PatientProfileServiceProxy) UpdateHpmInformation(ctx *titan.Context, request *UpdateHpmInformationRequest) error {
	return srv.service.UpdateHpmInformation(ctx, request)
}

// Define service router -----------------------------------------------------------------
type EmployeeProfileServiceRouter struct {
	proxy *EmployeeProfileServiceProxy
}

func (router *EmployeeProfileServiceRouter) Register(r titan.Router) {
	r.RegisterTopic(EVENT_GetAll, router.proxy.GetAll, titan.Secured(infra.SYS_ADMIN))
	r.RegisterTopic(LEGACY_TOPIC_GetAll, router.proxy.GetAll, titan.Secured(infra.SYS_ADMIN))
	r.RegisterTopic(EVENT_SearchEmployeeByName, router.proxy.SearchEmployeeByName, titan.Secured(infra.CARE_PROVIDER_MEMBER_PRE_SWITCH, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(LEGACY_TOPIC_SearchEmployeeByName, router.proxy.SearchEmployeeByName, titan.Secured(infra.CARE_PROVIDER_MEMBER_PRE_SWITCH, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(EVENT_GetEmployeeByExternalId, router.proxy.GetEmployeeByExternalId, titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_GetEmployeeByExternalId, router.proxy.GetEmployeeByExternalId, titan.IsAuthenticated())
	r.RegisterTopic(EVENT_GetAllInitial, router.proxy.GetAllInitial, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(LEGACY_TOPIC_GetAllInitial, router.proxy.GetAllInitial, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(EVENT_GetMyEmployeeProfile, router.proxy.GetMyEmployeeProfile, titan.Secured(infra.CARE_PROVIDER_MEMBER_PRE_SWITCH, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(LEGACY_TOPIC_GetMyEmployeeProfile, router.proxy.GetMyEmployeeProfile, titan.Secured(infra.CARE_PROVIDER_MEMBER_PRE_SWITCH, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(EVENT_GetEmployeeProfileByLanrId, router.proxy.GetEmployeeProfileByLanrId, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(LEGACY_TOPIC_GetEmployeeProfileByLanrId, router.proxy.GetEmployeeProfileByLanrId, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(EVENT_GetEmployeeProfilesByBsnrId, router.proxy.GetEmployeeProfilesByBsnrId, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(LEGACY_TOPIC_GetEmployeeProfilesByBsnrId, router.proxy.GetEmployeeProfilesByBsnrId, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(EVENT_GetEmployeeProfileByHzvId, router.proxy.GetEmployeeProfileByHzvId, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(LEGACY_TOPIC_GetEmployeeProfileByHzvId, router.proxy.GetEmployeeProfileByHzvId, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(EVENT_GetEmployeeProfileByMediId, router.proxy.GetEmployeeProfileByMediId, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(LEGACY_TOPIC_GetEmployeeProfileByMediId, router.proxy.GetEmployeeProfileByMediId, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(EVENT_GetEmployeeProfileById, router.proxy.GetEmployeeProfileById, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(LEGACY_TOPIC_GetEmployeeProfileById, router.proxy.GetEmployeeProfileById, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(EVENT_GetEmployeeProfileByIds, router.proxy.GetEmployeeProfileByIds, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(LEGACY_TOPIC_GetEmployeeProfileByIds, router.proxy.GetEmployeeProfileByIds, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(EVENT_CreateEmployeeProfile, router.proxy.CreateEmployeeProfile, titan.Secured(infra.SYS_ADMIN, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(LEGACY_TOPIC_CreateEmployeeProfile, router.proxy.CreateEmployeeProfile, titan.Secured(infra.SYS_ADMIN, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(EVENT_CreateEmployeeProfileWithoutAuth, router.proxy.CreateEmployeeProfileWithoutAuth, titan.Secured(infra.SYS_ADMIN, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(LEGACY_TOPIC_CreateEmployeeProfileWithoutAuth, router.proxy.CreateEmployeeProfileWithoutAuth, titan.Secured(infra.SYS_ADMIN, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(EVENT_UpdateEmployeeProfile, router.proxy.UpdateEmployeeProfile, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(LEGACY_TOPIC_UpdateEmployeeProfile, router.proxy.UpdateEmployeeProfile, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(EVENT_UpdateEmployeeStatus, router.proxy.UpdateEmployeeStatus, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(LEGACY_TOPIC_UpdateEmployeeStatus, router.proxy.UpdateEmployeeStatus, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(EVENT_DeleteEmployeeProfileById, router.proxy.DeleteEmployeeProfileById, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(LEGACY_TOPIC_DeleteEmployeeProfileById, router.proxy.DeleteEmployeeProfileById, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(EVENT_GetEmployeeByIds, router.proxy.GetEmployeeByIds, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(LEGACY_TOPIC_GetEmployeeByIds, router.proxy.GetEmployeeByIds, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(EVENT_UpdateDevice, router.proxy.UpdateDevice, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(LEGACY_TOPIC_UpdateDevice, router.proxy.UpdateDevice, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(EVENT_GetEmployeeByDeviceId, router.proxy.GetEmployeeByDeviceId, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(LEGACY_TOPIC_GetEmployeeByDeviceId, router.proxy.GetEmployeeByDeviceId, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(EVENT_ResetEmployeePassword, router.proxy.ResetEmployeePassword, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(LEGACY_TOPIC_ResetEmployeePassword, router.proxy.ResetEmployeePassword, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(EVENT_ResetEmployee2FA, router.proxy.ResetEmployee2FA, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(LEGACY_TOPIC_ResetEmployee2FA, router.proxy.ResetEmployee2FA, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(EVENT_UpdateDefaultBsnrOfEmployee, router.proxy.UpdateDefaultBsnrOfEmployee, titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_UpdateDefaultBsnrOfEmployee, router.proxy.UpdateDefaultBsnrOfEmployee, titan.IsAuthenticated())
}

// Subscriber
func (router *EmployeeProfileServiceRouter) Subscribe(s *titan.MessageSubscriber) {
	s.Register("api.app.admin.AppAdmin.DeviceChange", "api.service.domains_EmployeeProfileService_HandleEventDeviceChange_Queue", func(p *titan.Message) error {
		var resp admin_bff.EventDeviceChange
		ctx, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		return router.proxy.HandleEventDeviceChange(ctx, &resp)
	})
}

func NewEmployeeProfileServiceRouter(s EmployeeProfileService) *EmployeeProfileServiceRouter {
	p := &EmployeeProfileServiceProxy{s}
	return &EmployeeProfileServiceRouter{p}
}

type PatientProfileServiceRouter struct {
	proxy *PatientProfileServiceProxy
}

func (router *PatientProfileServiceRouter) Register(r titan.Router) {
	r.RegisterTopic(EVENT_CreateProfile, router.proxy.CreateProfile, titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_CreateProfile, router.proxy.CreateProfile, titan.IsAuthenticated())
	r.RegisterTopic(EVENT_UpdateProfile, router.proxy.UpdateProfile, titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_UpdateProfile, router.proxy.UpdateProfile, titan.IsAuthenticated())
	r.RegisterTopic(EVENT_CreatePatientMedicalData, router.proxy.CreatePatientMedicalData, titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_CreatePatientMedicalData, router.proxy.CreatePatientMedicalData, titan.IsAuthenticated())
	r.RegisterTopic(EVENT_GetProfileById, router.proxy.GetProfileById, titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_GetProfileById, router.proxy.GetProfileById, titan.IsAuthenticated())
	r.RegisterTopic(EVENT_GetProfileByIds, router.proxy.GetProfileByIds, titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_GetProfileByIds, router.proxy.GetProfileByIds, titan.IsAuthenticated())
	r.RegisterTopic(EVENT_UpdateHpmInformation, router.proxy.UpdateHpmInformation, titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_UpdateHpmInformation, router.proxy.UpdateHpmInformation, titan.IsAuthenticated())
}

// Subscriber
func (router *PatientProfileServiceRouter) Subscribe(s *titan.MessageSubscriber) {
}

func NewPatientProfileServiceRouter(s PatientProfileService) *PatientProfileServiceRouter {
	p := &PatientProfileServiceProxy{s}
	return &PatientProfileServiceRouter{p}
}

// Define client ----------------------------------------------------------------------------------------------
type EmployeeProfileServiceClient struct {
	client *titan.Client
}

func (srv *EmployeeProfileServiceClient) GetAll(ctx *titan.Context, request *GetByCareProviderIdRequest) (*EmployeeProfilesResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetAll).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfilesResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *EmployeeProfileServiceClient) SearchEmployeeByName(ctx *titan.Context, request *SearchEmployeeByNameRequest) (*SearchEmployeeByNameResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_SearchEmployeeByName).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &SearchEmployeeByNameResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *EmployeeProfileServiceClient) GetEmployeeByExternalId(ctx *titan.Context, request *GetEmployeeByExternalIdRequest) (*EmployeeProfileResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetEmployeeByExternalId).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfileResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *EmployeeProfileServiceClient) GetAllInitial(ctx *titan.Context) (*GetAllInitialResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetAllInitial).
		Subject(NATS_SUBJECT).
		Build()
	var resp = &GetAllInitialResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *EmployeeProfileServiceClient) GetMyEmployeeProfile(ctx *titan.Context) (*EmployeeProfileResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetMyEmployeeProfile).
		Subject(NATS_SUBJECT).
		Build()
	var resp = &EmployeeProfileResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *EmployeeProfileServiceClient) GetEmployeeProfileByLanrId(ctx *titan.Context, request *GetByLanrIDRequest) (*EmployeeProfileResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetEmployeeProfileByLanrId).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfileResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *EmployeeProfileServiceClient) GetEmployeeProfilesByBsnrId(ctx *titan.Context, request *GetByBsnrIdRequest) (*EmployeeProfilesResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetEmployeeProfilesByBsnrId).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfilesResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *EmployeeProfileServiceClient) GetEmployeeProfileByHzvId(ctx *titan.Context, request *GetByHzvIDRequest) (*EmployeeProfileResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetEmployeeProfileByHzvId).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfileResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *EmployeeProfileServiceClient) GetEmployeeProfileByMediId(ctx *titan.Context, request *GetByMediIDRequest) (*EmployeeProfileResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetEmployeeProfileByMediId).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfileResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *EmployeeProfileServiceClient) GetEmployeeProfileById(ctx *titan.Context, request *EmployeeProfileGetRequest) (*EmployeeProfileResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetEmployeeProfileById).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfileResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *EmployeeProfileServiceClient) GetEmployeeProfileByIds(ctx *titan.Context, request *GetByIdsRequest) (*EmployeeProfilesResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetEmployeeProfileByIds).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfilesResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *EmployeeProfileServiceClient) CreateEmployeeProfile(ctx *titan.Context, request *EmployeeProfileRequest) (*EmployeeProfileResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CreateEmployeeProfile).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfileResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *EmployeeProfileServiceClient) CreateEmployeeProfileWithoutAuth(ctx *titan.Context, request *EmployeeProfileRequest) (*EmployeeProfileResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CreateEmployeeProfileWithoutAuth).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfileResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *EmployeeProfileServiceClient) UpdateEmployeeProfile(ctx *titan.Context, request *EmployeeProfileRequest) (*EmployeeProfileResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UpdateEmployeeProfile).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfileResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *EmployeeProfileServiceClient) UpdateEmployeeStatus(ctx *titan.Context, request *UpdateEmployeeStatusRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UpdateEmployeeStatus).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *EmployeeProfileServiceClient) DeleteEmployeeProfileById(ctx *titan.Context, request *EmployeeProfileDeleteRequest) (*EmployeeProfileDeleteResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_DeleteEmployeeProfileById).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfileDeleteResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *EmployeeProfileServiceClient) GetEmployeeByIds(ctx *titan.Context, request *GetByIdsRequest) (*EmployeeProfilesResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetEmployeeByIds).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfilesResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *EmployeeProfileServiceClient) UpdateDevice(ctx *titan.Context, request *UpdateDeviceRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UpdateDevice).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *EmployeeProfileServiceClient) GetEmployeeByDeviceId(ctx *titan.Context, request *GetEmployeeByDeviceIdRequest) (*EmployeeProfilesResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetEmployeeByDeviceId).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfilesResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *EmployeeProfileServiceClient) ResetEmployeePassword(ctx *titan.Context, request *ResetEmployeePasswordRequest) (*ResetEmployeePasswordResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_ResetEmployeePassword).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &ResetEmployeePasswordResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *EmployeeProfileServiceClient) ResetEmployee2FA(ctx *titan.Context, request *ResetEmployee2FARequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_ResetEmployee2FA).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *EmployeeProfileServiceClient) UpdateDefaultBsnrOfEmployee(ctx *titan.Context, request *admin_bff.UpdateDefaultBsnrOfEmployeeRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UpdateDefaultBsnrOfEmployee).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *EmployeeProfileServiceClient) HandleEventDeviceChange(ctx *titan.Context, request *admin_bff.EventDeviceChange) error {
	return srv.client.Publish(ctx, "api.app.admin.AppAdmin.DeviceChange", request)
}

func NewEmployeeProfileServiceClient(clients ...*titan.Client) *EmployeeProfileServiceClient {
	var client *titan.Client
	if len(clients) <= 0 {
		client = titan.GetDefaultClient()
	} else {
		client = clients[0]
	}
	return &EmployeeProfileServiceClient{client: client}
}

type PatientProfileServiceClient struct {
	client *titan.Client
}

func (srv *PatientProfileServiceClient) CreateProfile(ctx *titan.Context, request *PatientProfileCreateRequest) (*PatientProfile, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CreateProfile).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &PatientProfile{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *PatientProfileServiceClient) UpdateProfile(ctx *titan.Context, request *PatientProfileUpdateRequest) (*PatientProfile, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UpdateProfile).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &PatientProfile{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *PatientProfileServiceClient) CreatePatientMedicalData(ctx *titan.Context, request *UpdatePatientMedicalDataRequest) (*UpdatePatientMedicalDataResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CreatePatientMedicalData).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &UpdatePatientMedicalDataResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *PatientProfileServiceClient) GetProfileById(ctx *titan.Context, request *GetByIdRequest) (*PatientProfile, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetProfileById).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &PatientProfile{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *PatientProfileServiceClient) GetProfileByIds(ctx *titan.Context, request *GetProfileByIdsRequest) (*GetProfileByIdsResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetProfileByIds).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetProfileByIdsResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *PatientProfileServiceClient) UpdateHpmInformation(ctx *titan.Context, request *UpdateHpmInformationRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UpdateHpmInformation).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}

func NewPatientProfileServiceClient(clients ...*titan.Client) *PatientProfileServiceClient {
	var client *titan.Client
	if len(clients) <= 0 {
		client = titan.GetDefaultClient()
	} else {
		client = clients[0]
	}
	return &PatientProfileServiceClient{client: client}
}

// Define event Notifier -----------------------------------------------------------------------------------------
type ProfileNotifier struct {
	client *titan.Client
}

func NewProfileNotifier() *ProfileNotifier {
	client := titan.GetDefaultClient()
	return &ProfileNotifier{client}
}
func (p *ProfileNotifier) NotifyHealthInsuranceChange(ctx *titan.Context, event *EventHealthInsuranceChange) error {
	return p.client.Publish(ctx, EVENT_HealthInsuranceChange, event)
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type ProfileSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewProfileSocketNotifier(socket *socket_api.SocketServiceClient) *ProfileSocketNotifier {
	return &ProfileSocketNotifier{socket}
}
func (n *ProfileSocketNotifier) NotifyCareProviderHealthInsuranceChange(ctx *titan.Context, event *EventHealthInsuranceChange) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_HealthInsuranceChange,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *ProfileSocketNotifier) NotifyUserHealthInsuranceChange(ctx *titan.Context, event *EventHealthInsuranceChange) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_HealthInsuranceChange,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *ProfileSocketNotifier) NotifyDeviceHealthInsuranceChange(ctx *titan.Context, event *EventHealthInsuranceChange) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_HealthInsuranceChange,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *ProfileSocketNotifier) NotifyClientHealthInsuranceChange(ctx *titan.Context, event *EventHealthInsuranceChange) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_HealthInsuranceChange,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}

// -----------------------------------------------
// Test event listener
type ProfileEventListener struct {
	mux                                sync.Mutex
	LastEventHealthInsuranceChangeList []EventHealthInsuranceChange
}

func (listener *ProfileEventListener) Reset() {
	listener.mux.Lock()
	listener.LastEventHealthInsuranceChangeList = []EventHealthInsuranceChange{}
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *ProfileEventListener) Subscribe(s *titan.MessageSubscriber) {
	s.Register(EVENT_HealthInsuranceChange, "api.service.domains_ServiceDomainsProfile_HealthInsuranceChange_Queue_test", func(p *titan.Message) error {
		var resp EventHealthInsuranceChange
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventHealthInsuranceChangeList) >= 100 {
			listener.LastEventHealthInsuranceChangeList = listener.LastEventHealthInsuranceChangeList[1:]
		}
		listener.LastEventHealthInsuranceChangeList = append(listener.LastEventHealthInsuranceChangeList, resp)
		listener.mux.Unlock()
		return nil
	})
}
func (listener *ProfileEventListener) GetLastEventHealthInsuranceChangeList(timeOutInMilliSeconds time.Duration) []EventHealthInsuranceChange {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventHealthInsuranceChangeList
		default:
			// if any value
			if len(listener.LastEventHealthInsuranceChangeList) > 0 {
				return listener.LastEventHealthInsuranceChangeList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
