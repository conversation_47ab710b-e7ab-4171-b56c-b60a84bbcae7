// This code was autogenerated from service/domains/patient_profile_common.proto, do not edit.

package patient_profile_common

import (
	"encoding/json"
	"errors"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	catalog_sdkt_common "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdkt_common"

	catalog_utils_common "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_utils_common"

	common "git.tutum.dev/medi/tutum/ares/service/domains/api/common"

	error_code "git.tutum.dev/medi/tutum/ares/service/domains/error_code"

	insurance "git.tutum.dev/medi/tutum/ares/service/insurance"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type AddressInsurance struct {
	Street   *string `json:"street"`
	Number   *string `json:"number"`
	PostCode *string `json:"postCode"`
	City     *string `json:"city"`
	Country  *string `json:"country"`
}

type InsuranceInfo struct {
	Id                         uuid.UUID                        `json:"id"`
	InsuranceCompanyId         string                           `json:"insuranceCompanyId"`
	InsuranceCompanyName       string                           `json:"insuranceCompanyName"`
	LocationNames              []string                         `json:"locationNames"`
	Validity                   catalog_utils_common.Validity    `json:"validity"`
	IkNumber                   int32                            `json:"ikNumber"`
	InsuranceNumber            *string                          `json:"insuranceNumber"`
	InsuranceStatus            InsuranceStatus                  `json:"insuranceStatus"`
	StartDate                  *int64                           `json:"startDate"`
	EndDate                    *int64                           `json:"endDate"`
	SpecialGroup               SpecialGroupDescription          `json:"specialGroup"`
	DMPLabeling                string                           `json:"dMPLabeling"`
	HaveCoPaymentExemptionTill bool                             `json:"haveCoPaymentExemptionTill"`
	HavePatientReceipt         bool                             `json:"havePatientReceipt"`
	HaveHeimiLongTermApproval  bool                             `json:"haveHeimiLongTermApproval"`
	CopaymentExemptionTillDate *int64                           `json:"copaymentExemptionTillDate"`
	Wop                        string                           `json:"wop" bson:"wop,omitempty"`
	LHMs                       []LHM                            `json:"lHMs"`
	FeeSchedule                int32                            `json:"feeSchedule"`
	AcquiredInsuranceInfo      *InsuranceInfo                   `json:"acquiredInsuranceInfo"`
	FeeCatalogue               catalog_sdkt_common.FeeCatalogue `json:"feeCatalogue"`
	InsuranceType              TypeOfInsurance                  `json:"insuranceType"`
	ValidUntil                 *MMYYYY                          `json:"validUntil"`
	ReadCardDatas              []ReadCardModel                  `json:"readCardDatas"`
	IsActive                   bool                             `json:"isActive"`
	Address                    *AddressInsurance                `json:"address"`
	Tel                        *string                          `json:"tel"`
	Fax                        *string                          `json:"fax"`
	IsTerminated               *bool                            `json:"isTerminated"`
}

type VSDStatus struct {
	Status    string `json:"status"`
	Timestamp int64  `json:"timestamp"`
	Version   string `json:"version"`
}

type ReadCardModel struct {
	FromCardType       FromCardType      `json:"fromCardType"`
	ProofOfInsurance   *ProofOfInsurance `json:"proofOfInsurance"`
	RegistrationNumber *string           `json:"registrationNumber"`
	ReadCardDate       int64             `json:"readCardDate"`
	CdmVersion         string            `json:"cdmVersion"`
	VSDStatus          *VSDStatus        `json:"vSDStatus"`
}

type ProofOfInsurance struct {
	OnlineCheckDate int64   `json:"onlineCheckDate"`
	ResultCode      int32   `json:"resultCode"`
	CheckDigit      *string `json:"checkDigit"`
	ErrorCode       *int32  `json:"errorCode"`
}

type InsuranceRequestCreate struct {
	InsuranceCompanyId         string                           `json:"insuranceCompanyId"`
	InsuranceCompanyName       string                           `json:"insuranceCompanyName"`
	LocationNames              []string                         `json:"locationNames"`
	Validity                   catalog_utils_common.Validity    `json:"validity"`
	IkNumber                   int32                            `json:"ikNumber"`
	InsuranceNumber            *string                          `json:"insuranceNumber"`
	InsuranceStatus            InsuranceStatus                  `json:"insuranceStatus"`
	StartDate                  *int64                           `json:"startDate"`
	EndDate                    *int64                           `json:"endDate"`
	SpecialGroup               SpecialGroupDescription          `json:"specialGroup"`
	DMPLabeling                string                           `json:"dMPLabeling"`
	HaveCoPaymentExemptionTill bool                             `json:"haveCoPaymentExemptionTill"`
	HavePatientReceipt         bool                             `json:"havePatientReceipt"`
	HaveHeimiLongTermApproval  bool                             `json:"haveHeimiLongTermApproval"`
	CopaymentExemptionTillDate *int64                           `json:"copaymentExemptionTillDate"`
	Wop                        string                           `json:"wop" bson:"omitempty"`
	LHMs                       []LHM                            `json:"lHMs"`
	IsActive                   bool                             `json:"isActive"`
	FeeSchedule                int32                            `json:"feeSchedule"`
	FromCardType               *insurance.FromCardType          `json:"fromCardType"`
	AcquiredInsuranceInfo      *insurance.InsuranceInfo         `json:"acquiredInsuranceInfo"`
	FeeCatalogue               catalog_sdkt_common.FeeCatalogue `json:"feeCatalogue"`
	InsuranceType              TypeOfInsurance                  `json:"insuranceType"`
	ValidUntil                 *MMYYYY                          `json:"validUntil"`
}

type MMYYYY struct {
	Year  *int32 `json:"year" validate:"omitempty,gte=1900"`
	Month *int32 `json:"month" validate:"omitempty,gte=1,lte=12"`
}

type GenericInfo struct {
	PatientType         PatientType `json:"patientType"`
	LastManualEntryDate *int64      `json:"lastManualEntryDate"`
	LastCardReadinDate  *int64      `json:"lastCardReadinDate"`
}

type DateOfBirth struct {
	Year       *int32 `json:"year" validate:"omitempty,gte=1900,lte=2100"`
	Month      *int32 `json:"month" validate:"omitempty,gte=1,lte=12"`
	Date       *int32 `json:"date" validate:"omitempty,gte=1,lte=31"`
	IsValidDOB bool   `json:"isValidDOB"`
}

type PersonalInfo struct {
	FirstName       string           `json:"firstName" validate:"trim"`
	LastName        string           `json:"lastName" validate:"trim"`
	Title           *string          `json:"title"`
	AdditionalNames []AdditionalName `json:"additionalNames"`
	IntendWord      *IntendWord      `json:"intendWord"`
	Gender          Gender           `json:"gender"`
	DOB             int64            `json:"dOB"`
	DateOfBirth     DateOfBirth      `json:"dateOfBirth" validate:"required"`
	DateOfDeath     *int64           `json:"dateOfDeath"`
	BirthName       *string          `json:"birthName"`
	Status          *string          `json:"status"`
}

type Address struct {
	Street                *string  `json:"street"`
	Number                *string  `json:"number"`
	PostCode              string   `json:"postCode"`
	City                  *string  `json:"city"`
	CountryCode           *string  `json:"countryCode"`
	AdditionalAddressInfo *string  `json:"additionalAddressInfo"`
	Distance              *float32 `json:"distance"`
}

type PostalAddress struct {
	PostCode    string `json:"postCode"`
	OfficeBox   string `json:"officeBox"`
	CityBox     string `json:"cityBox"`
	CountryCode string `json:"countryCode"`
}

type BillingAddress struct {
	Street          *string          `json:"street"`
	Number          *string          `json:"number"`
	PostCode        string           `json:"postCode"`
	City            *string          `json:"city"`
	CountryCode     *string          `json:"countryCode"`
	FirstName       string           `json:"firstName"`
	LastName        string           `json:"lastName"`
	Title           *string          `json:"title"`
	AdditionalNames []AdditionalName `json:"additionalNames"`
	IntendWord      *IntendWord      `json:"intendWord"`
}

type CompanyAddress struct {
	Street      *string `json:"street"`
	Number      *string `json:"number"`
	PostCode    *string `json:"postCode"`
	City        *string `json:"city"`
	CountryCode *string `json:"countryCode"`
	Employer    *string `json:"employer"`
}

type AddressInfo struct {
	Address        Address        `json:"address"`
	BillingAddress BillingAddress `json:"billingAddress"`
	PostalAddress  *PostalAddress `json:"postalAddress"`
}

type ContactPerson struct {
	Name         *string `json:"name"`
	PhoneNumber  *string `json:"phoneNumber"`
	Email        *string `json:"email"`
	Relationship *string `json:"relationship"`
}

type ContactInfo struct {
	PrimaryContactNumber                *string         `json:"primaryContactNumber"`
	FurtherContactNumber                []string        `json:"furtherContactNumber"`
	EmailAddress                        *string         `json:"emailAddress"`
	HaveDeclarationOfAgreementToContact bool            `json:"haveDeclarationOfAgreementToContact"`
	ContactPersons                      []ContactPerson `json:"contactPersons"`
	ContactAgreementFile                *FileInfo       `json:"contactAgreementFile"`
	Address                             *string         `json:"address"`
}

type DoctorInfo struct {
	GeneralPractitionerDoctorId []uuid.UUID `json:"generalPractitionerDoctorId"`
	SpecialistDoctorId          []uuid.UUID `json:"specialistDoctorId"`
	TreatmentDoctorId           *uuid.UUID  `json:"treatmentDoctorId"`
}

type FileInfo struct {
	FileName string `json:"fileName"`
	FileUrl  string `json:"fileUrl"`
}

type OtherInfo struct {
	Cave                            string    `json:"cave"`
	PatientSinceDate                *int64    `json:"patientSinceDate"`
	LastTreatmentDate               *int64    `json:"lastTreatmentDate"`
	HaveMedicalHistoryFormCompleted bool      `json:"haveMedicalHistoryFormCompleted"`
	IsLivingWillAvailable           bool      `json:"isLivingWillAvailable"`
	IsAgreeWithBillingViaPVS        bool      `json:"isAgreeWithBillingViaPVS"`
	IsPrivacyPolicySigned           bool      `json:"isPrivacyPolicySigned"`
	MedicalHistoryFileUrl           *FileInfo `json:"medicalHistoryFileUrl"`
	LivingWillFileUrl               *FileInfo `json:"livingWillFileUrl"`
	BillingFileUrl                  *FileInfo `json:"billingFileUrl"`
	PrivacyPolicyFileUrl            *FileInfo `json:"privacyPolicyFileUrl"`
}

type VisitInfo struct {
	TreatmentDoctorId   *uuid.UUID `json:"treatmentDoctorId"`
	AdditionalVisitInfo *string    `json:"additionalVisitInfo"`
}

type PostOfficeBox struct {
	PostCode         string `json:"postCode"`
	PlaceOfResidence string `json:"placeOfResidence"`
	OfficeBox        string `json:"officeBox"`
	CountryCode      string `json:"countryCode"`
}

type EuropeanHealthInsurance struct {
	HasEuropeanHealthInsuranceCard bool                                  `json:"hasEuropeanHealthInsuranceCard"`
	FormSetting                    *string                               `json:"formSetting" validate:"omitempty,trimjson"`
	Language                       *string                               `json:"language"`
	Status                         *common.EuropeanHealthInsuranceStatus `json:"status"`
	TreatmentDoctorId              uuid.UUID                             `json:"treatmentDoctorId"`
}

type PatientInfo struct {
	PatientNumber           int32                   `json:"patientNumber"`
	GenericInfo             GenericInfo             `json:"genericInfo"`
	PersonalInfo            PersonalInfo            `json:"personalInfo"`
	AddressInfo             AddressInfo             `json:"addressInfo"`
	ContactInfo             ContactInfo             `json:"contactInfo"`
	InsuranceInfos          []InsuranceInfo         `json:"insuranceInfos"`
	DoctorInfo              DoctorInfo              `json:"doctorInfo"`
	OtherInfo               OtherInfo               `json:"otherInfo"`
	VisitInfo               VisitInfo               `json:"visitInfo"`
	EmploymentInfo          EmploymentInfo          `json:"employmentInfo"`
	PostOfficeBox           PostOfficeBox           `json:"postOfficeBox"`
	EuropeanHealthInsurance EuropeanHealthInsurance `json:"europeanHealthInsurance"`
	PatientId               *uuid.UUID              `json:"patientId" bson:"-"`
	CardRawId               *uuid.UUID              `json:"cardRawId"`
}

type EmploymentInfo struct {
	JobStatus            *JobStatus     `json:"jobStatus"`
	Occupation           string         `json:"occupation"`
	SpecialProblemAtWork string         `json:"specialProblemAtWork"`
	WorkingHourInWeek    float32        `json:"workingHourInWeek"`
	WorkActivity1        WorkActivity1  `json:"workActivity1"`
	WorkActivity2        WorkActivity2  `json:"workActivity2"`
	CompanyAddress       CompanyAddress `json:"companyAddress"`
	IsEmployed           bool           `json:"isEmployed"`
}

type Allergy struct {
	Allergy               string `json:"allergy"`
	IsPrescriptionRelated bool   `json:"isPrescriptionRelated"`
}

type PatientMedicalData struct {
	Weight              *float64  `json:"weight"`
	Height              *float64  `json:"height"`
	BloodPressure       *string   `json:"bloodPressure"`
	HeartFrequency      *int64    `json:"heartFrequency"`
	Allergies           []Allergy `json:"allergies"`
	Creatinine          *float64  `json:"creatinine"`
	AmountOfChildren    *int32    `json:"amountOfChildren"`
	IsPregnant          *bool     `json:"isPregnant"`
	DateOfPlannedBirth  *int64    `json:"dateOfPlannedBirth"`
	AmountOfBirth       *int64    `json:"amountOfBirth"`
	AmountOfPregnancies *int64    `json:"amountOfPregnancies"`
	IsBreastfeeding     *bool     `json:"isBreastfeeding"`
	CareLevel           *int32    `json:"careLevel"`
	AdditionalNote      *string   `json:"additionalNote"`
	IsSmoker            *bool     `json:"isSmoker"`
	CigarettesPerDay    *int64    `json:"cigarettesPerDay"`
	AllergiesFor4205    []Allergy `json:"allergiesFor4205"`
	BodyTemperature     *float64  `json:"bodyTemperature"`
	PulseOxiMetric      *float64  `json:"pulseOxiMetric"`
	Pulse               *Pulse    `json:"pulse"`
}

type LHM struct {
	Id                            *uuid.UUID `json:"id"`
	FirstICDCode                  string     `json:"firstICDCode" validate:"required"`
	SecondICDCode                 *string    `json:"secondICDCode"`
	DiagnosisGroupCode            string     `json:"diagnosisGroupCode"`
	IsStandardCombination         bool       `json:"isStandardCombination"`
	PrimaryRemediesPosition       []string   `json:"primaryRemediesPosition"`
	ComplementaryRemediesPosition []string   `json:"complementaryRemediesPosition"`
	ValidUntilDate                *int64     `json:"validUntilDate"`
	Note                          *string    `json:"note"`
}

type PatientPrepared struct {
	PatientInfo     PatientInfo           `json:"patientInfo"`
	CompareStatus   CompareStatus         `json:"compareStatus"`
	ValidationError *error_code.ErrorCode `json:"validationError"`
	IsCreateShown   bool                  `json:"isCreateShown"`
}

type PatientDeleteRequest struct {
	PatientId uuid.UUID `json:"patientId" validate:"required"`
}

type EventPatientProfileChange struct {
	EventName                   EventName           `json:"eventName"`
	PatientId                   uuid.UUID           `json:"patientId"`
	PatientMedicalData          *PatientMedicalData `json:"patientMedicalData"`
	PatientInfo                 *PatientInfo        `json:"patientInfo"`
	MedicalDataUpdatedAt        *int64              `json:"medicalDataUpdatedAt"`
	EmploymentInfoUpdatedAt     *int64              `json:"employmentInfoUpdatedAt"`
	ShouldCreateNewEHIC         bool                `json:"shouldCreateNewEHIC"`
	CardInsurance               *InsuranceInfo      `json:"cardInsurance"`
	ReadingDate                 int64               `json:"readingDate"`
	ShouldRunTimelineValidation bool                `json:"shouldRunTimelineValidation"`
}

type GetInsurancesByPatientIdRequest struct {
	PatientId uuid.UUID `json:"patientId"`
}

type GetInsurancesByPatientIdResponse struct {
	InsuranceInfos []InsuranceInfo `json:"insuranceInfos" validate:"required"`
}

// enum definitions
type TypeOfInsurance string

const (
	Public  TypeOfInsurance = "Public"
	Private TypeOfInsurance = "Private"
	BG      TypeOfInsurance = "BG"
)

type Gender string

const (
	M Gender = "M"
	W Gender = "W"
	X Gender = "X"
	D Gender = "D"
	U Gender = "U"
)

type Salutation string

const (
	Salutation_Herr  Salutation = "Herr"
	Salutation_Frau  Salutation = "Frau"
	Salutation_Keine Salutation = "Keine"
)

type CreatePatientProfileErrorCode string

const (
	DuplicatedInsuranceNumber        CreatePatientProfileErrorCode = "DuplicatedInsuranceNumber"
	MultipleActiveCostUnits          CreatePatientProfileErrorCode = "MultipleActiveCostUnits"
	EndDateOutOfDate                 CreatePatientProfileErrorCode = "EndDateOutOfDate"
	LHMUnique                        CreatePatientProfileErrorCode = "LHMUnique"
	CostUnitIsNotAvailableInKvRegion CreatePatientProfileErrorCode = "CostUnitIsNotAvailableInKvRegion"
	CostUnitHasExpired               CreatePatientProfileErrorCode = "CostUnitHasExpired"
	DuplicateLHMItem                 CreatePatientProfileErrorCode = "DuplicateLHMItem"
	IKNumberHasExpired               CreatePatientProfileErrorCode = "IKNumberHasExpired"
	CanNotChangeInsuranceType        CreatePatientProfileErrorCode = "CanNotChangeInsuranceType"
	NotFoundInsurance                CreatePatientProfileErrorCode = "NotFoundInsurance"
	InvalidInsuranceDate             CreatePatientProfileErrorCode = "InvalidInsuranceDate"
	NotFoundActiveInsurance          CreatePatientProfileErrorCode = "NotFoundActiveInsurance"
)

type SpecialGroupDescription string

const (
	SpecialGroup_00 SpecialGroupDescription = "00"
	SpecialGroup_04 SpecialGroupDescription = "04"
	SpecialGroup_06 SpecialGroupDescription = "06"
	SpecialGroup_07 SpecialGroupDescription = "07"
	SpecialGroup_08 SpecialGroupDescription = "08"
	SpecialGroup_09 SpecialGroupDescription = "09"
)

type AdditionalName string

const (
	AdditionalName_Bar           AdditionalName = "Bar"
	AdditionalName_Baron         AdditionalName = "Baron"
	AdditionalName_Baroness      AdditionalName = "Baroness"
	AdditionalName_Baronesse     AdditionalName = "Baronesse"
	AdditionalName_Baronin       AdditionalName = "Baronin"
	AdditionalName_Brand         AdditionalName = "Brand"
	AdditionalName_Burggraf      AdditionalName = "Burggraf"
	AdditionalName_Burggrafin    AdditionalName = "Burggräfin"
	AdditionalName_Condesa       AdditionalName = "Condesa"
	AdditionalName_Earl          AdditionalName = "Earl"
	AdditionalName_Edle          AdditionalName = "Edle"
	AdditionalName_Edler         AdditionalName = "Edler"
	AdditionalName_Erbgraf       AdditionalName = "Erbgraf"
	AdditionalName_Erbgrafin     AdditionalName = "Erbgräfin"
	AdditionalName_Erbprinz      AdditionalName = "Erbprinz"
	AdditionalName_Erbprinzessin AdditionalName = "Erbprinzessin"
	AdditionalName_Ffr           AdditionalName = "Ffr"
	AdditionalName_Freifr        AdditionalName = "Freifr"
	AdditionalName_Freifraulein  AdditionalName = "Freifräulein"
	AdditionalName_Freifrau      AdditionalName = "Freifrau"
	AdditionalName_Freih         AdditionalName = "Freih"
	AdditionalName_Freiherr      AdditionalName = "Freiherr"
	AdditionalName_Freiin        AdditionalName = "Freiin"
	AdditionalName_Frf           AdditionalName = "Frf"
	AdditionalName_FrfDot        AdditionalName = "Frf."
	AdditionalName_Frfr          AdditionalName = "Frfr"
	AdditionalName_FrfrDot       AdditionalName = "Frfr."
	AdditionalName_Frh           AdditionalName = "Frh"
	AdditionalName_FrhDot        AdditionalName = "Frh."
	AdditionalName_Frhr          AdditionalName = "Frhr"
	AdditionalName_FrhrDot       AdditionalName = "Frhr."
	AdditionalName_Fst           AdditionalName = "Fst"
	AdditionalName_FstDot        AdditionalName = "Fst."
	AdditionalName_Fstn          AdditionalName = "Fstn"
	AdditionalName_FstnDot       AdditionalName = "Fstn."
	AdditionalName_Furst         AdditionalName = "Fürst"
	AdditionalName_Furstin       AdditionalName = "Fürstin"
	AdditionalName_Gr            AdditionalName = "Gr"
	AdditionalName_Graf          AdditionalName = "Graf"
	AdditionalName_Grafin        AdditionalName = "Gräfin"
	AdditionalName_Grf           AdditionalName = "Grf"
	AdditionalName_Grfn          AdditionalName = "Grfn"
	AdditionalName_Grossherzog   AdditionalName = "Grossherzog"
	AdditionalName_GroBherzog    AdditionalName = "Großherzog"
	AdditionalName_Grossherzogin AdditionalName = "Grossherzogin"
	AdditionalName_GroBherzogin  AdditionalName = "Großherzogin"
	AdditionalName_Herzog        AdditionalName = "Herzog"
	AdditionalName_Herzogin      AdditionalName = "Herzogin"
	AdditionalName_Jhr           AdditionalName = "Jhr"
	AdditionalName_JhrDot        AdditionalName = "Jhr."
	AdditionalName_Jonkheer      AdditionalName = "Jonkheer"
	AdditionalName_Junker        AdditionalName = "Junker"
	AdditionalName_Landgraf      AdditionalName = "Landgraf"
	AdditionalName_Landgrafin    AdditionalName = "Landgräfin"
	AdditionalName_Markgraf      AdditionalName = "Markgraf"
	AdditionalName_Markgrafin    AdditionalName = "Markgräfin"
	AdditionalName_Marques       AdditionalName = "Marques"
	AdditionalName_Marquis       AdditionalName = "Marquis"
	AdditionalName_Marschall     AdditionalName = "Marschall"
	AdditionalName_Ostoja        AdditionalName = "Ostoja"
	AdditionalName_Prinz         AdditionalName = "Prinz"
	AdditionalName_Prinzessin    AdditionalName = "Prinzessin"
	AdditionalName_Przin         AdditionalName = "Przin"
	AdditionalName_Rabe          AdditionalName = "Rabe"
	AdditionalName_Reichsgraf    AdditionalName = "Reichsgraf"
	AdditionalName_Reichsgrafin  AdditionalName = "Reichsgräfin"
	AdditionalName_Ritter        AdditionalName = "Ritter"
	AdditionalName_Rr            AdditionalName = "Rr"
	AdditionalName_Truchsess     AdditionalName = "Truchsess"
	AdditionalName_TruchseB      AdditionalName = "Truchseß"
)

type IntendWord string

const (
	IntendWord_A           IntendWord = "a"
	IntendWord_Aande       IntendWord = "aan de"
	IntendWord_Aanden      IntendWord = "aan den"
	IntendWord_Al          IntendWord = "al"
	IntendWord_Am          IntendWord = "am"
	IntendWord_An          IntendWord = "an"
	IntendWord_Ander       IntendWord = "an der"
	IntendWord_Auf         IntendWord = "auf"
	IntendWord_Aufdem      IntendWord = "auf dem"
	IntendWord_Aufder      IntendWord = "auf der"
	IntendWord_Aufmspace   IntendWord = "auf m"
	IntendWord_Aufm        IntendWord = "aufm"
	IntendWord_Auffm       IntendWord = "auff m"
	IntendWord_Aus         IntendWord = "aus"
	IntendWord_Ausdem      IntendWord = "aus dem"
	IntendWord_Ausden      IntendWord = "aus den"
	IntendWord_Ausder      IntendWord = "aus der"
	IntendWord_B           IntendWord = "b"
	IntendWord_Be          IntendWord = "be"
	IntendWord_Bei         IntendWord = "bei"
	IntendWord_Beider      IntendWord = "bei der"
	IntendWord_Beim        IntendWord = "beim"
	IntendWord_Ben         IntendWord = "ben"
	IntendWord_Bey         IntendWord = "bey"
	IntendWord_Beyder      IntendWord = "bey der"
	IntendWord_Che         IntendWord = "che"
	IntendWord_Cid         IntendWord = "cid"
	IntendWord_D           IntendWord = "d"
	IntendWord_DDot        IntendWord = "d."
	IntendWord_DQuote      IntendWord = "d'"
	IntendWord_Da          IntendWord = "da"
	IntendWord_Dacosta     IntendWord = "da costa"
	IntendWord_Dalas       IntendWord = "da las"
	IntendWord_Dasilva     IntendWord = "da silva"
	IntendWord_Dal         IntendWord = "dal"
	IntendWord_Dall        IntendWord = "dall"
	IntendWord_Dallquote   IntendWord = "dall'"
	IntendWord_Dalla       IntendWord = "dalla"
	IntendWord_Dalle       IntendWord = "dalle"
	IntendWord_Dallo       IntendWord = "dallo"
	IntendWord_Das         IntendWord = "das"
	IntendWord_De          IntendWord = "de"
	IntendWord_Degli       IntendWord = "degli"
	IntendWord_Dei         IntendWord = "dei"
	IntendWord_Den         IntendWord = "den"
	IntendWord_Delquote    IntendWord = "de l '"
	IntendWord_Dela        IntendWord = "de la"
	IntendWord_Delas       IntendWord = "de las"
	IntendWord_Dele        IntendWord = "de le"
	IntendWord_Delos       IntendWord = "de los"
	IntendWord_Del         IntendWord = "del"
	IntendWord_Delcoz      IntendWord = "del coz"
	IntendWord_Deli        IntendWord = "deli"
	IntendWord_Dell        IntendWord = "dell"
	IntendWord_Dellquote   IntendWord = "dell'"
	IntendWord_Della       IntendWord = "della"
	IntendWord_Delle       IntendWord = "delle"
	IntendWord_Delli       IntendWord = "delli"
	IntendWord_Dello       IntendWord = "dello"
	IntendWord_Der         IntendWord = "der"
	IntendWord_Des         IntendWord = "des"
	IntendWord_Di          IntendWord = "di"
	IntendWord_Dit         IntendWord = "dit"
	IntendWord_Do          IntendWord = "do"
	IntendWord_Doceu       IntendWord = "do ceu"
	IntendWord_Don         IntendWord = "don"
	IntendWord_Donle       IntendWord = "don le"
	IntendWord_Dos         IntendWord = "dos"
	IntendWord_Dossantos   IntendWord = "dos santos"
	IntendWord_Du          IntendWord = "du"
	IntendWord_Dy          IntendWord = "dy"
	IntendWord_El          IntendWord = "el"
	IntendWord_G           IntendWord = "g"
	IntendWord_Gen         IntendWord = "gen"
	IntendWord_Gil         IntendWord = "gil"
	IntendWord_Gli         IntendWord = "gli"
	IntendWord_Grosse      IntendWord = "grosse"
	IntendWord_GroBe       IntendWord = "große"
	IntendWord_I           IntendWord = "i"
	IntendWord_Im          IntendWord = "im"
	IntendWord_In          IntendWord = "in"
	IntendWord_Inde        IntendWord = "in de"
	IntendWord_Inden       IntendWord = "in den"
	IntendWord_Inder       IntendWord = "in der"
	IntendWord_Inhet       IntendWord = "in het"
	IntendWord_Intquote    IntendWord = "in't"
	IntendWord_Kl          IntendWord = "kl"
	IntendWord_Kleine      IntendWord = "kleine"
	IntendWord_L           IntendWord = "l"
	IntendWord_Ldot        IntendWord = "l."
	IntendWord_Lquote      IntendWord = "l'"
	IntendWord_La          IntendWord = "la"
	IntendWord_Le          IntendWord = "le"
	IntendWord_Lee         IntendWord = "lee"
	IntendWord_Li          IntendWord = "li"
	IntendWord_Lo          IntendWord = "lo"
	IntendWord_M           IntendWord = "m"
	IntendWord_Mc          IntendWord = "mc"
	IntendWord_Mac         IntendWord = "mac"
	IntendWord_N           IntendWord = "n"
	IntendWord_O           IntendWord = "o"
	IntendWord_Oquote      IntendWord = "o'"
	IntendWord_Op          IntendWord = "op"
	IntendWord_Opde        IntendWord = "op de"
	IntendWord_Opden       IntendWord = "op den"
	IntendWord_Opgen       IntendWord = "op gen"
	IntendWord_Ophet       IntendWord = "op het"
	IntendWord_Opte        IntendWord = "op te"
	IntendWord_Opten       IntendWord = "op ten"
	IntendWord_Oude        IntendWord = "oude"
	IntendWord_Pla         IntendWord = "pla"
	IntendWord_Pro         IntendWord = "pro"
	IntendWord_S           IntendWord = "s"
	IntendWord_Stdot       IntendWord = "st."
	IntendWord_T           IntendWord = "t"
	IntendWord_Te          IntendWord = "te"
	IntendWord_Ten         IntendWord = "ten"
	IntendWord_Ter         IntendWord = "ter"
	IntendWord_Thi         IntendWord = "thi"
	IntendWord_Tho         IntendWord = "tho"
	IntendWord_Thom        IntendWord = "thom"
	IntendWord_Thor        IntendWord = "thor"
	IntendWord_Thum        IntendWord = "thum"
	IntendWord_To          IntendWord = "to"
	IntendWord_Tom         IntendWord = "tom"
	IntendWord_Tor         IntendWord = "tor"
	IntendWord_Tu          IntendWord = "tu"
	IntendWord_Tum         IntendWord = "tum"
	IntendWord_Unten       IntendWord = "unten"
	IntendWord_Unter       IntendWord = "unter"
	IntendWord_Unterm      IntendWord = "unterm"
	IntendWord_Vdot        IntendWord = "v."
	IntendWord_Vddot       IntendWord = "v. d."
	IntendWord_Vdem        IntendWord = "v. dem"
	IntendWord_Vden        IntendWord = "v. den"
	IntendWord_Vder        IntendWord = "v. der"
	IntendWord_Vd          IntendWord = "v.d."
	IntendWord_Vdemdot     IntendWord = "v.dem"
	IntendWord_Vdendot     IntendWord = "v.den"
	IntendWord_Vderdot     IntendWord = "v.der"
	IntendWord_Van         IntendWord = "van"
	IntendWord_Vandespace  IntendWord = "van de"
	IntendWord_Vandemspace IntendWord = "van dem"
	IntendWord_Vandenspace IntendWord = "van den"
	IntendWord_Vanderspace IntendWord = "van der"
	IntendWord_Vande       IntendWord = "vande"
	IntendWord_Vandem      IntendWord = "vandem"
	IntendWord_Vanden      IntendWord = "vanden"
	IntendWord_Vander      IntendWord = "vander"
	IntendWord_Vangen      IntendWord = "van gen"
	IntendWord_Vanhet      IntendWord = "van het"
	IntendWord_Vant        IntendWord = "van t"
	IntendWord_Ven         IntendWord = "ven"
	IntendWord_Vender      IntendWord = "ven der"
	IntendWord_Ver         IntendWord = "ver"
	IntendWord_Vo          IntendWord = "vo"
	IntendWord_Vom         IntendWord = "vom"
	IntendWord_Vomundzu    IntendWord = "vom und zu"
	IntendWord_Von         IntendWord = "von"
	IntendWord_Vonundzu    IntendWord = "von und zu"
	IntendWord_Vonundzuder IntendWord = "von und zu der"
	IntendWord_Vonundzur   IntendWord = "von und zur"
	IntendWord_Vondespace  IntendWord = "von de"
	IntendWord_Vondemspace IntendWord = "von dem"
	IntendWord_Vondenspace IntendWord = "von den"
	IntendWord_Vonderspace IntendWord = "von der"
	IntendWord_Vonla       IntendWord = "von la"
	IntendWord_Vonzu       IntendWord = "von zu"
	IntendWord_Vonzum      IntendWord = "von zum"
	IntendWord_Vonzur      IntendWord = "von zur"
	IntendWord_Vonde       IntendWord = "vonde"
	IntendWord_Vonden      IntendWord = "vonden"
	IntendWord_Vondem      IntendWord = "vondem"
	IntendWord_Vonder      IntendWord = "vonder"
	IntendWord_Voneinem    IntendWord = "von einem"
	IntendWord_Vonmast     IntendWord = "von mast"
	IntendWord_Vor         IntendWord = "vor"
	IntendWord_Vordem      IntendWord = "vor dem"
	IntendWord_Vorden      IntendWord = "vor den"
	IntendWord_Vorder      IntendWord = "vor der"
	IntendWord_Vorm        IntendWord = "vorm"
	IntendWord_Vorn        IntendWord = "vorn"
	IntendWord_Y           IntendWord = "y"
	IntendWord_Ydel        IntendWord = "y del"
	IntendWord_Zu          IntendWord = "zu"
	IntendWord_Zum         IntendWord = "zum"
	IntendWord_Zur         IntendWord = "zur"
)

type InsuranceStatus string

const (
	Mitglied         InsuranceStatus = "1"
	Familienmitglied InsuranceStatus = "3"
	Rentner          InsuranceStatus = "5"
)

type WorkActivity1 string

const (
	Physical WorkActivity1 = "Physical"
	Mental   WorkActivity1 = "Mental"
)

type WorkActivity2 string

const (
	Standing WorkActivity2 = "Standing"
	Sitting  WorkActivity2 = "Sitting"
)

type FromCardType string

const (
	FromCardType_EGK    FromCardType = "EGK"
	FromCardType_KVK    FromCardType = "KVK"
	FromCardType_Mobile FromCardType = "Mobile"
)

type CardReadInStatus string

const (
	CardReadInStatus_CardReadin      CardReadInStatus = "CardReadin"
	CardReadInStatus_ManualEntry     CardReadInStatus = "ManualEntry"
	CardReadInStatus_NotReadin       CardReadInStatus = "NotReadin"
	CardReadInStatus_OnlineCheckFail CardReadInStatus = "OnlineCheckFail"
)

type PatientType string

const (
	PatientType_Private PatientType = "Private"
	PatientType_Public  PatientType = "Public"
)

type JobStatus string

const (
	JobStatus_Employees        JobStatus = "Angestellte (erwerbstätig=Y)"
	JobStatus_Executives       JobStatus = "Leitende Angestellte (erwerbstätig=Y)"
	JobStatus_ManagingDirector JobStatus = "Geschäftsfühhrter (erwerbstätig=Y)"
	JobStatus_CivilServant     JobStatus = "Zivilbediensteter/Diener (erwerbstätig=Y)"
	JobStatus_JobSeeker        JobStatus = "Arbeitssuchend (erwerbstätig=N)"
)

type CompareStatus string

const (
	NotExist             CompareStatus = "NotExist"
	PartialMatch         CompareStatus = "PartialMatch"
	ExactMatch           CompareStatus = "ExactMatch"
	ExactInsuranceNumber CompareStatus = "ExactInsuranceNumber"
)

type Pulse string

const (
	Pulse_Rhythmic  Pulse = "Pulse_Rhythmic"
	Pulse_Irregular Pulse = "Pulse_Irregular"
)

type EventName string

const (
	EventName_UpdateMedicalData    EventName = "EventName_UpdateMedicalData"
	EventName_UpdatePatientProfile EventName = "EventName_UpdatePatientProfile"
	EventName_UpdateLHM            EventName = "EventName_UpdateLHM"
	EventName_CreatedPatient       EventName = "EventName_CreatedPatient"
	EventName_DeletedPatient       EventName = "EventName_DeletedPatient"
)

// Define constants
const NATS_SUBJECT = "" // nats subject this service will listen to

// service event constants

// message event constants
const EVENT_PatientProfileChange = ".PatientProfileCommon.PatientProfileChange"

// Define service interface -------------------------------------------------------------

// Define service proxy -------------------------------------------------------------------

// Define service router -----------------------------------------------------------------

// Define client ----------------------------------------------------------------------------------------------

// Define event Notifier -----------------------------------------------------------------------------------------
type PatientProfileCommonNotifier struct {
	client *titan.Client
}

func NewPatientProfileCommonNotifier() *PatientProfileCommonNotifier {
	client := titan.GetDefaultClient()
	return &PatientProfileCommonNotifier{client}
}
func (p *PatientProfileCommonNotifier) NotifyPatientProfileChange(ctx *titan.Context, event *EventPatientProfileChange) error {
	return p.client.Publish(ctx, EVENT_PatientProfileChange, event)
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type PatientProfileCommonSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewPatientProfileCommonSocketNotifier(socket *socket_api.SocketServiceClient) *PatientProfileCommonSocketNotifier {
	return &PatientProfileCommonSocketNotifier{socket}
}
func (n *PatientProfileCommonSocketNotifier) NotifyCareProviderPatientProfileChange(ctx *titan.Context, event *EventPatientProfileChange) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_PatientProfileChange,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *PatientProfileCommonSocketNotifier) NotifyUserPatientProfileChange(ctx *titan.Context, event *EventPatientProfileChange) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_PatientProfileChange,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *PatientProfileCommonSocketNotifier) NotifyDevicePatientProfileChange(ctx *titan.Context, event *EventPatientProfileChange) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_PatientProfileChange,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *PatientProfileCommonSocketNotifier) NotifyClientPatientProfileChange(ctx *titan.Context, event *EventPatientProfileChange) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_PatientProfileChange,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}

// -----------------------------------------------
// Test event listener
type PatientProfileCommonEventListener struct {
	mux                               sync.Mutex
	LastEventPatientProfileChangeList []EventPatientProfileChange
}

func (listener *PatientProfileCommonEventListener) Reset() {
	listener.mux.Lock()
	listener.LastEventPatientProfileChangeList = []EventPatientProfileChange{}
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *PatientProfileCommonEventListener) Subscribe(s *titan.MessageSubscriber) {
	s.Register(EVENT_PatientProfileChange, "_PatientProfileCommon_PatientProfileChange_Queue_test", func(p *titan.Message) error {
		var resp EventPatientProfileChange
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventPatientProfileChangeList) >= 100 {
			listener.LastEventPatientProfileChangeList = listener.LastEventPatientProfileChangeList[1:]
		}
		listener.LastEventPatientProfileChangeList = append(listener.LastEventPatientProfileChangeList, resp)
		listener.mux.Unlock()
		return nil
	})
}
func (listener *PatientProfileCommonEventListener) GetLastEventPatientProfileChangeList(timeOutInMilliSeconds time.Duration) []EventPatientProfileChange {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventPatientProfileChangeList
		default:
			// if any value
			if len(listener.LastEventPatientProfileChangeList) > 0 {
				return listener.LastEventPatientProfileChangeList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
