package patient_profile_common

import (
	"fmt"
	"strconv"

	"emperror.dev/errors"
	"github.com/google/uuid"
	"github.com/spf13/cast"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_sdkt"
	masterdata_model "git.tutum.dev/medi/tutum/ares/pkg/masterdata/model"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/card_common"
	catalog_sdkt_common "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdkt_common"
	catalog_utils_common "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_utils_common"
	insurance_common "git.tutum.dev/medi/tutum/ares/service/insurance"
	"git.tutum.dev/medi/tutum/ares/share/config"

	catalog_sdkt_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_sdkt"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	bg_insurance_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/bg_insurance"
	sdik_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/sdik"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
)

const (
	Private_PrintingName = "PRIVAT"
	InsuranceType_GKV    = "GKV"
	InsuranceType_KVK    = "KVK"
	InsuranceType_PKV    = "PKV"
)

var (
	pnValidCode = []int32{1, 2}
)

type IsDummyVknrFunc func(vknr string) bool

var IsDummyVknrFuncMod = submodule.Make[IsDummyVknrFunc](func(env string) IsDummyVknrFunc {
	return func(vknr string) bool {
		// Restriction apply on staging and production
		if env == config.DeploymentProfileStaging || env == config.DeploymentProfileProd {
			dummyVknrs := []string{string(insurance_common.InsuraceVKNR_74799)}
			return slice.Contains(dummyVknrs, vknr)
		}

		return false
	}
}, config.EnvironmentNameMod)

type PatientInsuranceInfos []InsuranceInfo

func (p PatientInsuranceInfos) GetReadCardDate(y util.YearQuarter, insuranceCompanyId string) *int64 {
	if len(p) == 0 {
		return nil
	}

	insurance := slice.FindOne(p, func(t InsuranceInfo) bool {
		return t.InsuranceCompanyId == insuranceCompanyId
	})

	if insurance == nil || len(insurance.ReadCardDatas) == 0 {
		return nil
	}

	readCardData := slice.FindOne(insurance.ReadCardDatas, func(t ReadCardModel) bool {
		return t.IsReadCardDateInQuarter(y)
	})

	if readCardData == nil {
		return nil
	}

	return &readCardData.ReadCardDate
}

func (p PatientInsuranceInfos) GetActiveInsurance() *InsuranceInfo {
	if len(p) == 0 {
		return nil
	}
	return slice.FindOne(p, func(t InsuranceInfo) bool {
		return t.IsActive
	})
}

func (p PatientInsuranceInfos) GetLastedCardData() *ReadCardModel {
	if len(p) == 0 {
		return nil
	}
	var readCardDatas []ReadCardModel
	for _, v := range p {
		if len(v.ReadCardDatas) > 0 {
			readCardDatas = append(readCardDatas, v.ReadCardDatas...)
		}
	}
	if len(readCardDatas) == 0 {
		return nil
	}
	slice.SortBy(readCardDatas, func(a, b ReadCardModel) bool {
		return a.ReadCardDate > b.ReadCardDate
	})
	return &readCardDatas[0]
}

func (p PatientInsuranceInfos) GetInsurance(insuranceId uuid.UUID) *InsuranceInfo {
	if len(p) == 0 {
		return nil
	}
	return slice.FindOne(p, func(t InsuranceInfo) bool {
		return t.Id == insuranceId
	})
}

func (p PatientInsuranceInfos) GetCardInsurance() *InsuranceInfo {
	if len(p) == 0 {
		return nil
	}

	cardInsurances := slice.Filter(p, func(t InsuranceInfo) bool {
		return len(t.ReadCardDatas) > 0
	})
	if len(cardInsurances) == 0 {
		return nil
	}

	res := cardInsurances[0]
	for _, cI := range cardInsurances {
		cRcm := cI.GetLatestReadCardModel()
		rRcm := res.GetLatestReadCardModel()

		if cRcm != nil && rRcm != nil && cRcm.ReadCardDate > rRcm.ReadCardDate {
			res = cI
		}
	}

	return &res
}

func (p *PatientInfo) GetActiveInsurance() *InsuranceInfo {
	if p == nil {
		return nil
	}
	return PatientInsuranceInfos(p.InsuranceInfos).GetActiveInsurance()
}

func (p *PatientInfo) GetInsurance(insuranceId uuid.UUID) *InsuranceInfo {
	if p == nil {
		return nil
	}
	return PatientInsuranceInfos(p.InsuranceInfos).GetInsurance(insuranceId)
}

func (p *PatientInfo) GetInsuranceIds() []uuid.UUID {
	if p == nil || len(p.InsuranceInfos) == 0 {
		return nil
	}
	return slice.Map(p.InsuranceInfos, func(i InsuranceInfo) uuid.UUID {
		return i.Id
	})
}

func (p *PatientInfo) GetVKNRs() []string {
	if p == nil || len(p.InsuranceInfos) == 0 {
		return nil
	}
	return slice.Map(p.InsuranceInfos, func(i InsuranceInfo) string {
		return i.InsuranceCompanyId
	})
}
func (p *PatientInfo) GetInsuranceByIkNumber(ikNumber int32) *InsuranceInfo {
	if p == nil {
		return nil
	}
	return slice.FindOne(p.InsuranceInfos, func(ii InsuranceInfo) bool {
		return ii.IkNumber == ikNumber
	})
}

func (p *PatientInfo) GetInsuranceByStatusIkNumberSpecialGroup(
	ikNumber int32,
	specialGroup SpecialGroupDescription,
	insuranceStatus InsuranceStatus,
) *InsuranceInfo {
	if p == nil {
		return nil
	}
	return slice.FindOne(p.InsuranceInfos, func(ii InsuranceInfo) bool {
		return ii.IkNumber == ikNumber && ii.SpecialGroup == specialGroup && ii.InsuranceStatus == insuranceStatus
	})
}

func (p *PatientInfo) GetInsuranceIndexByStatusIkNumberSpecialGroup(
	ikNumber int32,
	specialGroup SpecialGroupDescription,
	insuranceStatus InsuranceStatus,
) (*InsuranceInfo, int) {
	if p == nil {
		return nil, -1
	}
	return slice.FindOneIndex(p.InsuranceInfos, func(ii InsuranceInfo) bool {
		return ii.IkNumber == ikNumber && ii.SpecialGroup == specialGroup && ii.InsuranceStatus == insuranceStatus
	})
}

func (p *PatientInfo) GetInsuranceByName(insuranceName string) *InsuranceInfo {
	if p == nil {
		return nil
	}
	return slice.FindOne(p.InsuranceInfos, func(ii InsuranceInfo) bool {
		return ii.InsuranceCompanyName == insuranceName
	})
}

func (p *PatientInfo) GetCardInsurance() *InsuranceInfo {
	if p == nil {
		return nil
	}
	return PatientInsuranceInfos(p.InsuranceInfos).GetCardInsurance()
}

// warning that the SKT addtional information field
// The software displays a (warning) note for recording the additionally required billing information or restrictions according to record type "kvx3" if
// (1) field FK 4109 (read in date) is not available and
// (2) the serial number of the VKNR >= 800 (and the cost unit accounting area (KTAB) = 00 - 09) or the serial number of the VKNR < 800 and the KTAB ≠ 00 and
// (3) neither field FK 3105 (Insurance number) nor field FK 3119 (Insurance ID) is present. NOTE: this depend on card reading date so don't need to check
func (p *PatientInfo) IsKVKPatientCreatedManual(vknr, ktabVal string) bool {
	vknrSerial := cast.ToInt(vknr[len(vknr)-3:])
	ktabIntVal := cast.ToInt(ktabVal)
	cond2 := vknrSerial >= 800 && 0 <= ktabIntVal && ktabIntVal <= 9
	cond3 := vknrSerial < 800 && ktabVal != "00"
	return cond2 || cond3
}

// use for patient read from card first time
func (p *PatientInfo) MarkFirstInsuranceAsActive() {
	if p == nil || len(p.InsuranceInfos) == 0 {
		return
	}

	activeInsurance := p.GetActiveInsurance()
	if activeInsurance != nil {
		return
	}

	if p.InsuranceInfos[0].InsuranceType == Private {
		return
	}

	p.InsuranceInfos[0].IsActive = true
}

// HasManyActiveInsurances uses for getting data for validation
func (p PatientInsuranceInfos) HasManyActiveInsurances() bool {
	if len(p) == 0 {
		return false
	}
	countActiveInsurances := slice.CountBy(p, func(t InsuranceInfo) bool {
		return t.IsActive && t.InsuranceType != Private
	})
	return countActiveInsurances > 1
}

// HasOneActiveInsurances uses for getting data for validation
func (p PatientInsuranceInfos) HasOneActiveInsurances() bool {
	if len(p) == 0 {
		return false
	}

	countActiveInsurances := slice.CountBy(p, func(t InsuranceInfo) bool {
		return t.IsActive && t.InsuranceType != Private
	})

	return countActiveInsurances == 1
}

func (p PatientInsuranceInfos) HasActivePublicInsurance() bool {
	if len(p) == 0 {
		return true
	}

	countActiveInsurances := slice.CountBy(p, func(t InsuranceInfo) bool {
		return t.IsActive && (t.InsuranceType == Public || t.InsuranceType == BG)
	})

	return countActiveInsurances > 0
}

func (p PatientInsuranceInfos) HasPrivateInsurance() bool {
	if len(p) == 0 {
		return false
	}
	hasPrivateInsurances := slice.Any(p, func(t InsuranceInfo) bool {
		return t.IsActive && t.InsuranceType == Private
	})

	return hasPrivateInsurances
}

type FindCatalogByInsuranceIdCallback func(string, func(*catalog_sdkt.GetSdktCatalogByVknrResponse))

func (p *InsuranceInfo) GetPrintingName(scheinG4106 string, f FindCatalogByInsuranceIdCallback) string {
	if p == nil || scheinG4106 == "" {
		return ""
	}
	var printingName string
	f(p.InsuranceCompanyId, func(catalogSdkt *catalog_sdkt.GetSdktCatalogByVknrResponse) {
		if catalogSdkt == nil || catalogSdkt.Data == nil {
			return
		}

		for _, ktabObj := range catalogSdkt.Data.KTABs {
			if catalog_sdkt_common.KTABValue(scheinG4106) == ktabObj.Value {
				printingName = ktabObj.PrintingName
				return
			}
		}
	})
	if printingName == "" {
		return p.InsuranceCompanyName
	}
	return printingName
}

type ScheinType int

const (
	ScheinTypePrivate ScheinType = iota
	ScheinTypeInsurance
	ScheinTypeSv
	ScheinTypeBg
)

type InsurancePrintingParams struct {
	ScheinType    ScheinType
	G4106         *string
	IkNumber      *int32
	InsuranceInfo *InsuranceInfo
}

type InsurancePrintingGetter struct {
	catalogSdktService *catalog_sdkt_service.CatalogSdktService
	sdikRepo           *sdik_repo.SdikRepo
	bgInsuranceRepo    *bg_insurance_repo.BGInsuranceRepo
}

func NewInsurancePrinting(catalogSdktService *catalog_sdkt_service.CatalogSdktService, sdikRepo *sdik_repo.SdikRepo, bgInsuranceRepo *bg_insurance_repo.BGInsuranceRepo) *InsurancePrintingGetter {
	return &InsurancePrintingGetter{
		catalogSdktService: catalogSdktService,
		sdikRepo:           sdikRepo,
		bgInsuranceRepo:    bgInsuranceRepo,
	}
}

func (i *InsurancePrintingGetter) GetPrintingName(ctx *titan.Context, params InsurancePrintingParams) (printingName string, err error) {
	noValue := params.IkNumber == nil && params.G4106 == nil && params.InsuranceInfo == nil
	if noValue {
		return Private_PrintingName, nil
	}
	defer func() {
		if printingName == "" && err == nil {
			printingName = Private_PrintingName
		}
	}()
	switch params.ScheinType {
	case ScheinTypeInsurance:
		if params.InsuranceInfo == nil {
			return "", errors.New("insurance info should not be nil")
		}
		if params.G4106 == nil {
			return "", errors.New("schein g4106 should not be nil")
		}
		var (
			getSdktCatalogByIdErr error
			printingName          string
		)
		printingName = params.InsuranceInfo.GetPrintingName(*params.G4106, func(id string, f func(*catalog_sdkt.GetSdktCatalogByVknrResponse)) {
			now := util.NowUnixMillis(ctx)
			result, err := i.catalogSdktService.GetSdktCatalogByVknr(ctx, &catalog_sdkt.GetSdktCatalogByVknrRequest{
				Vknr:         id,
				SelectedDate: &now,
			})
			if err != nil {
				getSdktCatalogByIdErr = err
				return
			}
			f(result)
		})
		return printingName, getSdktCatalogByIdErr
	case ScheinTypeSv:
		if params.InsuranceInfo == nil {
			return "", errors.New("insurance info should not be nil")
		}
		return params.InsuranceInfo.InsuranceCompanyName, nil
	case ScheinTypePrivate:
		if params.IkNumber == nil {
			return "", errors.New("ik number should not be nil")
		}
		yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
		res, err := i.sdikRepo.GetSdikByIknumber(ctx, &sdik_repo.GetSdikByIknumberRequest{
			IkNumber: cast.ToString(params.IkNumber),
			YearQuarter: masterdata_model.YearQuarter{
				Year:    yearQuarter.Year,
				Quarter: yearQuarter.Quarter,
			},
		})
		if err != nil {
			return "", errors.WithMessage(err, "failed to get sdik catalog by ik number")
		}

		if res != nil {
			printingName = res.PrintName
		}
		return printingName, nil
	case ScheinTypeBg:
		if params.IkNumber == nil {
			return "", errors.New("ik number should not be nil")
		}

		yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
		bg, err := i.bgInsuranceRepo.GetByIknumber(ctx, &bg_insurance_repo.GetByIKNumberRequest{
			IkNumber: cast.ToString(params.IkNumber),
			YearQuarter: masterdata_model.YearQuarter{
				Year:    yearQuarter.Year,
				Quarter: yearQuarter.Quarter,
			},
		})
		if err != nil {
			return "", errors.WithMessage(err, "failed to get bg catalog by ik number")
		}

		if bg != nil {
			printingName = bg.PrintName
		}

		return printingName, nil
	default:
		return "", errors.New("unknown schein type")
	}
}

// IsValidWithQuarter checks if the insurance is valid for the given quarter
func (i *InsuranceInfo) IsValidWithQuarter(quarter util.YearQuarter) bool {
	if i == nil {
		return false
	}
	if i.StartDate == nil && i.EndDate == nil {
		return true
	}
	startDateOfInsurance := util.GetPointerValue(i.StartDate)
	if startDateOfInsurance == 0 {
		startDateOfInsurance = util.GetPointerValue(quarter.StartTime())
	}

	endOfInsurance := util.GetPointerValue(i.EndDate)
	if endOfInsurance == 0 {
		endOfInsurance = util.GetPointerValue(quarter.EndTime())
	}

	firstQuarterInsurance := util.ToYearQuarter(startDateOfInsurance)
	if firstQuarterInsurance.Year == quarter.Year {
		validQuartersOfInsurance := []string{}
		for _, firstDayOfMonth := range util.GetMonthsBetweenInt(startDateOfInsurance, endOfInsurance) {
			q, y := util.GetCurrentQuarter(firstDayOfMonth)
			qy := fmt.Sprintf("%d-%d", q, y)
			if !slice.Contains(validQuartersOfInsurance, qy) {
				validQuartersOfInsurance = append(validQuartersOfInsurance, qy)
			}
		}
		currentQuarterYear := fmt.Sprintf("%d-%d", quarter.Quarter, quarter.Year)
		return slice.Contains(validQuartersOfInsurance, currentQuarterYear)
	}

	if firstQuarterInsurance.Year > quarter.Year {
		return false
	}
	// valid year, need to check end date only, end date not before start date of quarter
	return endOfInsurance >= util.GetPointerValue(quarter.StartTime())
}

func (p *InsuranceInfo) GetActiveLHMs(ctx *titan.Context) []LHM {
	if p == nil {
		return nil
	}
	var activeLHMs []LHM
	for _, lhm := range p.LHMs {
		if lhm.ValidUntilDate == nil {
			activeLHMs = append(activeLHMs, lhm)
		} else {
			validUntilDate := util.ConvertMillisecondsToTime(*lhm.ValidUntilDate)
			if util.Now(ctx).Before(validUntilDate) {
				activeLHMs = append(activeLHMs, lhm)
			}
		}
	}
	return activeLHMs
}

func (i *InsuranceInfo) GetIkNumberString() string {
	if i == nil {
		return ""
	}
	ikNumber := cast.ToString(i.IkNumber)
	if len(ikNumber) < 9 {
		ikNumber = "10" + ikNumber
	}
	return ikNumber

}

func (i *ReadCardModel) GetIsMobileCardReadIn() bool {
	if i == nil {
		return false
	}
	return i.FromCardType == FromCardType_Mobile
}

// Return true. if patient has a card read in this quarter (EV = true)
func (i *ReadCardModel) IsTICardReadIn() bool {
	if i == nil {
		return false
	}
	return i.FromCardType == FromCardType_EGK || i.FromCardType == FromCardType_KVK
}

func (r *ReadCardModel) GetReadCardDate() *int64 {
	if r == nil {
		return nil
	}
	return &r.ReadCardDate
}

func (i *InsuranceInfo) GetLatestReadCardModel() *ReadCardModel {
	if i == nil || len(i.ReadCardDatas) == 0 {
		return nil
	}

	res := i.ReadCardDatas[0]
	for _, r := range i.ReadCardDatas {
		if r.ReadCardDate > res.ReadCardDate {
			res = r
		}
	}

	return &res
}

func (r *ReadCardModel) GetCreatedAt() *int64 {
	if r == nil {
		return nil
	}
	return &r.ReadCardDate
}

func (r *ReadCardModel) GetResultCode() *int32 {
	if r == nil {
		return nil
	}
	if r.FromCardType == FromCardType_EGK {
		if r.ProofOfInsurance == nil {
			return nil
		}
		return &r.ProofOfInsurance.ResultCode
	}
	return nil
}

func (r *ReadCardModel) IsReadCardDateInQuarter(quarter util.YearQuarter) bool {
	startDate := util.GetPointerValue(quarter.StartTime())
	endDate := util.GetPointerValue(quarter.EndTime())

	readCardDate := r.GetReadCardDate()
	if readCardDate == nil {
		return false
	}

	return *readCardDate >= startDate && *readCardDate < endDate
}

// GetReadCardByCreatedAt assume readcard data has one record per quarter
// TI card and mobile card update to the same record
func (i *InsuranceInfo) GetReadCardByCreatedAt(yearQuarter util.YearQuarter) *ReadCardModel {
	if i == nil {
		return nil
	}
	readCard := slice.FindOne(i.ReadCardDatas, func(r ReadCardModel) bool {
		return r.IsReadCardDateInQuarter(yearQuarter)
	})
	return readCard
}

func (i *InsuranceInfo) GetReadCardByReadCardDate(yearQuarter util.YearQuarter) *ReadCardModel {
	if i == nil {
		return nil
	}

	startDate := util.GetPointerValue(yearQuarter.StartTime())
	endDate := util.GetPointerValue(yearQuarter.EndTime())

	readCard := slice.FindOne(i.ReadCardDatas, func(r ReadCardModel) bool {
		readCardDate := r.GetReadCardDate()
		return readCardDate != nil && *readCardDate >= startDate && *readCardDate < endDate
	})

	return readCard
}

func (i *InsuranceInfo) GetCardByQuarter(yearQuarter util.YearQuarter) *ReadCardModel {
	if i == nil {
		return nil
	}

	startDate := util.GetPointerValue(yearQuarter.StartTime())
	endDate := util.GetPointerValue(yearQuarter.EndTime())

	readCard := slice.FindOne(i.ReadCardDatas, func(r ReadCardModel) bool {
		createdAt := r.GetCreatedAt()
		return createdAt != nil && *createdAt >= startDate && *createdAt < endDate
	})

	return readCard
}

func (i *InsuranceInfo) GetReadCardByCardType(yearQuarter util.YearQuarter) *ReadCardModel {
	if i == nil {
		return nil
	}

	return i.GetReadCardByReadCardDate(yearQuarter)
}

func (i *InsuranceInfo) GetReadCardData(yearQuarter util.YearQuarter) *int {
	if i == nil || i.ReadCardDatas == nil {
		return nil
	}
	startDate := util.GetPointerValue(yearQuarter.StartTime())
	endDate := util.GetPointerValue(yearQuarter.EndTime())

	readCard, indexReadCard := slice.FindOneIndex(i.ReadCardDatas, func(r ReadCardModel) bool {
		readCardDate := r.GetReadCardDate()
		return readCardDate != nil && *readCardDate >= startDate && *readCardDate < endDate
	})
	if readCard != nil {
		return &indexReadCard
	}
	return util.NewPointer(0)
}

func (i *InsuranceInfo) CompareWith(insuranceInfo InsuranceInfo) bool {
	if i == nil {
		return false
	}

	if i.InsuranceType == Private {
		isMatchCompanyName := i.InsuranceCompanyName == insuranceInfo.InsuranceCompanyName
		isMatchIkNumber := i.IkNumber == insuranceInfo.IkNumber
		isMatchEndDate := (i.EndDate == nil && insuranceInfo.EndDate == nil) || (i.EndDate != nil && insuranceInfo.EndDate != nil && util.Compare2Dates(*i.EndDate, *insuranceInfo.EndDate))
		isMatchInsuranceNumber := util.GetPointerValue(i.InsuranceNumber) == util.GetPointerValue(insuranceInfo.InsuranceNumber)

		return isMatchCompanyName &&
			isMatchIkNumber &&
			isMatchEndDate &&
			isMatchInsuranceNumber
	}

	return i.SpecialGroup == insuranceInfo.SpecialGroup &&
		i.InsuranceCompanyId == insuranceInfo.InsuranceCompanyId &&
		i.InsuranceStatus == insuranceInfo.InsuranceStatus
}

func (i *InsuranceInfo) GetCardOnlineCheckDateInQuarter(yearQuarter util.YearQuarter, cartType FromCardType) *int64 {
	if i == nil {
		return nil
	}

	startDate := util.GetPointerValue(yearQuarter.StartTime())
	endDate := util.GetPointerValue(yearQuarter.EndTime())
	cardInfo := slice.FindOne(i.ReadCardDatas, func(r ReadCardModel) bool {
		hasProofOfInsurance := r.ProofOfInsurance != nil
		isValidOnlineCheckDate := hasProofOfInsurance && r.ProofOfInsurance.OnlineCheckDate > 0
		isOnlineInQuarter := hasProofOfInsurance && r.ProofOfInsurance.OnlineCheckDate >= startDate && r.ProofOfInsurance.OnlineCheckDate < endDate
		isMatchCard := r.FromCardType == cartType
		return isValidOnlineCheckDate && isOnlineInQuarter && isMatchCard
	})

	if cardInfo == nil {
		return nil
	}

	return util.NewPointer(cardInfo.ProofOfInsurance.OnlineCheckDate)
}

// TODO: need to enhance
func (i *InsuranceInfo) SetInsuranceCompanyInfo(ctx *titan.Context, selectedDate int64) (*catalog_sdkt.SearchSdktByIKNumberResponse, error) {
	if i == nil || i.InsuranceType == Private {
		return nil, nil
	}

	catalogSdktService, err := catalog_sdkt_service.CatalogSdktServiceMod.SafeResolve()
	if err != nil {
		return nil, fmt.Errorf("failed to create catalogService, %s", err)
	}

	insuranceCompanyResponse, err := catalogSdktService.SearchSdktByIKNumber(ctx, &catalog_sdkt.SearchSdktByIKNumberRequest{
		Value:        i.GetIkNumberString(),
		SelectedDate: selectedDate,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "Search sdkt by iknumber failed")
	}
	if insuranceCompanyResponse == nil || insuranceCompanyResponse.Data == nil {
		return nil, errors.New(string(error_code.ErrorCode_IK_NotFound))
	}
	insuranceCompany := insuranceCompanyResponse.Data

	if insuranceCompany.AccquiredCostUnit != nil { //Insurance has been acquired
		acquiredCostUnit, err := catalogSdktService.UniqCompanyByAccquiredCostUnit(ctx, &catalog_sdkt.UniqCompanyByAccquiredCostUnitRequest{
			SdktCatalog: insuranceCompany,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "get UniqCompanyByAcquiredCostUnit failed")
		}

		acquiredInsuranceInfo := *i
		acquiredInsuranceInfo.InsuranceCompanyId = insuranceCompany.Vknr
		acquiredInsuranceInfo.InsuranceCompanyName = insuranceCompany.Name
		acquiredInsuranceInfo.LocationNames = insuranceCompany.LocationNames
		acquiredInsuranceInfo.Validity = *insuranceCompany.Validity
		acquiredInsuranceInfo.FeeCatalogue = insuranceCompany.FeeCatalogue

		i.AcquiredInsuranceInfo = util.NewPointer(acquiredInsuranceInfo)

		insuranceCompany = acquiredCostUnit.SdktCatalog
	}

	i.InsuranceCompanyId = insuranceCompany.Vknr
	i.InsuranceCompanyName = insuranceCompany.Name
	i.LocationNames = insuranceCompany.LocationNames
	i.Validity = *insuranceCompany.Validity
	i.FeeCatalogue = insuranceCompany.FeeCatalogue
	return insuranceCompanyResponse, nil
}

func (i *InsuranceInfo) isStartDateInFuture(currentDate int64) bool {
	return util.GetPointerValue(i.StartDate) >= currentDate
}

func (i *InsuranceInfo) isEndDateInPast(currentDate int64) bool {
	endDate := util.GetPointerValue(i.EndDate)
	return endDate > 0 && endDate <= currentDate
}

func (i *InsuranceInfo) isExpired(ikNumber catalog_sdkt_common.IkNumber, currentDate int64) bool {
	return ikNumber.Validity != nil && ikNumber.Validity.ToDate != nil && currentDate > *ikNumber.Validity.ToDate
}

func (i *InsuranceInfo) isHasBeenTerminated(currentDate int64, validity *catalog_utils_common.Validity) bool {
	return (validity.FromDate != nil && *validity.FromDate > currentDate) || (validity.ToDate != nil && *validity.ToDate < currentDate)
}

func (i *InsuranceInfo) IsMatchInsuranceNumber(insurances []InsuranceInfo) bool {
	if i == nil {
		return false
	}

	return slice.FindOne(insurances, func(ii InsuranceInfo) bool {
		return util.GetPointerValue(ii.InsuranceNumber) == util.GetPointerValue(i.InsuranceNumber)
	}) != nil
}

func (i *InsuranceInfo) IsValidForPrivateSchein(issueDate int64) bool {
	if i == nil {
		return false
	}
	if i.StartDate == nil && i.EndDate == nil {
		return true
	}

	startOfInsurance := util.GetPointerValue(i.StartDate)
	endOfInsurance := util.GetPointerValue(i.EndDate)

	if endOfInsurance == 0 {
		endOfInsurance = issueDate
	}
	return startOfInsurance <= issueDate && endOfInsurance >= issueDate
}

func (i *InsuranceInfo) isValidKvKSdkt() bool {
	vknr := i.InsuranceCompanyId
	if len(vknr) == 5 {
		validVknr := string(vknr[len(vknr)-3:])
		vknrNo, _ := strconv.Atoi(validVknr)
		return vknrNo < 800
	}
	return false
}

func (i *InsuranceInfo) GetCurrentReadCardModel(ctx *titan.Context) *ReadCardModel {
	if i == nil || len(i.ReadCardDatas) == 0 {
		return nil
	}

	startQuarter, endQuarter := util.GetCurrentQuarterRange(ctx)

	return slice.FindOne(i.ReadCardDatas, func(rcm ReadCardModel) bool {
		return startQuarter <= rcm.ReadCardDate && rcm.ReadCardDate <= endQuarter
	})
}

func (i *InsuranceInfo) Validation(ctx *titan.Context, foundSdkt *catalog_sdkt_common.SdktCatalog, cardType card_common.CardTypeType) *error_code.ErrorCode {
	if i == nil || foundSdkt == nil {
		return nil
	}

	currentDate := util.NowUnixMillis(ctx)
	ikNumberOfCard := slice.FindOne(foundSdkt.IKNumbers, func(ik *catalog_sdkt_common.IkNumber) bool {
		return *ik.Value == i.IkNumber
	})
	if i.isExpired(**ikNumberOfCard, currentDate) {
		return util.NewPointer(error_code.ErrorCode_CardReader_IK_Invalid_Expired)
	}
	if i.isStartDateInFuture(currentDate) {
		return util.NewPointer(error_code.ErrorCode_CardReader_Insurance_EndDate_Before_Current_Date)
	}
	if i.isEndDateInPast(currentDate) {
		return util.NewPointer(error_code.ErrorCode_CardReader_Insurance_StartDate_After_Current_Date)
	}
	if i.isHasBeenTerminated(currentDate, foundSdkt.Validity) {
		return util.NewPointer(error_code.ErrorCode_CardReader_InsHasBeenTerminated)
	}
	if cardType == card_common.CardTypeTypeKVK && i.isValidKvKSdkt() {
		return util.NewPointer(error_code.ErrorCode_CardReader_SDKT_Vknr_Invalid)
	}
	// Need to check mobile is need to check KvRegion or not
	// if i.isHasBeenRestrictArea(sdktData.RestrictKvRegions, "") {
	// 	return util.NewPointer(error_code.ErrorCode_CardReader_InsHasBeenRestrictArea)
	// }
	return nil
}

func (i *InsuranceInfo) IsReadByTiInQuarter(yearQuarter util.YearQuarter) bool {
	if i == nil {
		return false
	}
	readCardByQuarter := i.GetReadCardByCardType(yearQuarter)

	if readCardByQuarter == nil {
		return false
	}

	isMobile := readCardByQuarter.FromCardType == FromCardType_Mobile
	if isMobile {
		return false
	}

	isKVK := readCardByQuarter.FromCardType == FromCardType_KVK
	isEGKWithPNValid := readCardByQuarter.FromCardType == FromCardType_EGK &&
		readCardByQuarter.ProofOfInsurance != nil &&
		(readCardByQuarter.ProofOfInsurance.ResultCode == 1 ||
			readCardByQuarter.ProofOfInsurance.ResultCode == 2)

	return isKVK || isEGKWithPNValid
}

type CardInsurancesBuilder struct {
	PatientInsurances     []InsuranceInfo
	PatientCardInsurances []InsuranceInfo
}

func (b *CardInsurancesBuilder) UpdateId() *CardInsurancesBuilder {
	for _, pi := range b.PatientInsurances {
		for i, pci := range b.PatientCardInsurances {
			isMatchCurrentInsurance := pi.InsuranceCompanyId == pci.InsuranceCompanyId &&
				pi.InsuranceStatus == pci.InsuranceStatus &&
				pi.SpecialGroup == pci.SpecialGroup

			b.PatientCardInsurances[i].Id = *util.NewUUID()
			if isMatchCurrentInsurance {
				b.PatientCardInsurances[i].Id = pi.Id
			}
		}
	}

	return b
}

func (b *CardInsurancesBuilder) UpdateIsActive() *CardInsurancesBuilder {
	if len(b.PatientCardInsurances) == 0 {
		return b
	}

	b.PatientInsurances = slice.Map(b.PatientInsurances, func(insurance InsuranceInfo) InsuranceInfo {
		insurance.IsActive = false
		return insurance
	})
	b.PatientCardInsurances[0].IsActive = true

	return b
}

func (b *CardInsurancesBuilder) UpdateStartDate() *CardInsurancesBuilder {
	for _, pi := range b.PatientInsurances {
		for i, pci := range b.PatientCardInsurances {
			isMatchCurrentInsurance := pi.InsuranceCompanyId == pci.InsuranceCompanyId &&
				pi.InsuranceStatus == pci.InsuranceStatus &&
				pi.SpecialGroup == pci.SpecialGroup

			rcm := pci.GetCurrentReadCardModel(titan.NewBackgroundContext())
			if isMatchCurrentInsurance && rcm != nil && rcm.FromCardType == FromCardType_KVK {
				b.PatientCardInsurances[i].StartDate = pi.StartDate
			}
		}
	}

	return b
}

func isSameYearQuarter(time int64, comparedTime int64) bool {
	yearQuarter := util.ToYearQuarter(time)
	yearQuarterCompared := util.ToYearQuarter(comparedTime)

	return yearQuarter.Year == yearQuarterCompared.Year && yearQuarter.Quarter == yearQuarterCompared.Quarter
}

func (b *CardInsurancesBuilder) UpdateReadCardModels() *CardInsurancesBuilder {
	for _, pi := range b.PatientInsurances {
		for i, pci := range b.PatientCardInsurances {
			if pi.Id != pci.Id || len(pci.ReadCardDatas) == 0 {
				continue
			}

			readCardDate := pci.ReadCardDatas[0].ReadCardDate
			fromCardType := pci.ReadCardDatas[0].FromCardType
			proofOfInsurance := pci.ReadCardDatas[0].ProofOfInsurance
			vsdStatus := pci.ReadCardDatas[0].VSDStatus
			yearQuarter := util.ToYearQuarter(readCardDate)

			//pci -> check coi doc card ti before ? -> return
			isReadByTi := pci.IsReadByTiInQuarter(yearQuarter)
			if fromCardType == FromCardType_Mobile && isReadByTi {
				return b
			}

			// get read card model by card type and quarter
			readCardByQuarter := pi.GetReadCardByCardType(yearQuarter)

			if fromCardType == FromCardType_KVK {
				if readCardByQuarter == nil {
					b.PatientCardInsurances[i].ReadCardDatas = append(pi.ReadCardDatas, pci.ReadCardDatas...)
					continue
				}

				b.PatientCardInsurances[i].ReadCardDatas = slice.Map(pi.ReadCardDatas, func(rc ReadCardModel) ReadCardModel {
					rc.ReadCardDate = readCardDate
					return rc
				})
				continue
			}

			if fromCardType == FromCardType_EGK {
				if readCardByQuarter == nil {
					/*
						if online check date in the past, with PN is 1, 2 and now read card with ts in the past (same quarter) also with PN 1, 2 -> update read card date
						if online check date in the past with PN is 3, 5, 6 and now read card with ts in the past (same quarter) -> update read card model
					*/
					readCardModelInPast := slice.FindOne(pi.ReadCardDatas, func(r ReadCardModel) bool {
						return r.ProofOfInsurance != nil && proofOfInsurance != nil &&
							isSameYearQuarter(r.ProofOfInsurance.OnlineCheckDate, proofOfInsurance.OnlineCheckDate)
					})

					if readCardModelInPast == nil {
						b.PatientCardInsurances[i].ReadCardDatas = append(pi.ReadCardDatas, pci.ReadCardDatas...)
						continue
					}

					pn := readCardModelInPast.GetResultCode()
					if pn == nil {
						continue
					}

					if slice.Contains([]int32{1, 2}, *pn) {
						b.PatientCardInsurances[i].ReadCardDatas = slice.Map(pi.ReadCardDatas, func(r ReadCardModel) ReadCardModel {
							if r.ReadCardDate == readCardModelInPast.ReadCardDate {
								r.ReadCardDate = readCardDate
							}
							return r
						})
						continue
					}

					b.PatientCardInsurances[i].ReadCardDatas = slice.Map(pi.ReadCardDatas, func(rc ReadCardModel) ReadCardModel {
						rc.ReadCardDate = readCardDate
						rc.ProofOfInsurance = proofOfInsurance
						rc.VSDStatus = vsdStatus
						return rc
					})
					continue
				}

				pn := readCardByQuarter.GetResultCode()
				if pn == nil {
					continue
				}

				if slice.Contains([]int32{1, 2}, *pn) {
					b.PatientCardInsurances[i].ReadCardDatas = slice.Map(pi.ReadCardDatas, func(r ReadCardModel) ReadCardModel {
						if r.ReadCardDate == readCardByQuarter.ReadCardDate {
							r.ReadCardDate = readCardDate
						}
						return r
					})
					continue
				}

				b.PatientCardInsurances[i].ReadCardDatas = slice.Map(pi.ReadCardDatas, func(rc ReadCardModel) ReadCardModel {
					rc.ReadCardDate = readCardDate
					rc.ProofOfInsurance = proofOfInsurance
					rc.VSDStatus = vsdStatus
					return rc
				})
			}
		}
	}

	return b
}

func (b *CardInsurancesBuilder) Merge() *CardInsurancesBuilder {
	var result []InsuranceInfo
	for _, pi := range b.PatientInsurances {
		for _, pci := range b.PatientCardInsurances {
			isMatchCurrentInsurance := pi.InsuranceCompanyId == pci.InsuranceCompanyId &&
				pi.InsuranceStatus == pci.InsuranceStatus &&
				pi.SpecialGroup == pci.SpecialGroup

			if !isMatchCurrentInsurance {
				result = append(result, pi)
			}
		}
	}
	b.PatientCardInsurances = append(result, b.PatientCardInsurances...)

	return b
}

func (b *CardInsurancesBuilder) GetCardInsuranceInfos() []InsuranceInfo {
	return b.PatientCardInsurances
}

func (c *InsuranceInfo) GetInsuranceCompanyId() string {
	if c == nil {
		return ""
	}

	isPublic := c.InsuranceType == Public
	if isPublic {
		return c.InsuranceCompanyId
	}

	h := c.InsuranceCompanyName
	vknr := h[len(h)-4:]
	if vknr == "" {
		return ""
	}

	return vknr
}

func (i *InsuranceInfo) TypeOfInsurance(ctx *titan.Context) (string, error) {
	if i == nil {
		return "", nil
	}

	code := InsuranceType_GKV
	insuranceCompanyIdWithPadding := util.LeftPad(i.InsuranceCompanyId, 5, '0')
	digitsToCheck, err := strconv.Atoi(insuranceCompanyIdWithPadding[2:5])
	if err != nil {
		return "", errors.WithMessage(err, "failed to convert insurance company id to int")
	}

	if i.InsuranceType == Private {
		code = InsuranceType_PKV
	}

	if digitsToCheck > 800 {
		code = InsuranceType_KVK
	}

	return code, nil
}
