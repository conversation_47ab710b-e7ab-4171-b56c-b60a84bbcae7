package doctor_letter_service

import (
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"

	"emperror.dev/errors"
	api "git.tutum.dev/medi/tutum/ares/app/admin/api/doctor_letter"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_bg_insurance"
	catalog_sdav_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_sdav"
	mvzApi "git.tutum.dev/medi/tutum/ares/app/mvz/api/doctor_letter"
	"git.tutum.dev/medi/tutum/ares/app/mvz/patient_profile"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	"git.tutum.dev/medi/tutum/ares/service/bsnr"
	admin_service_employee "git.tutum.dev/medi/tutum/ares/service/domains/api/admin_service"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_bg_insurance_common"

	catalog_sdik_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_sdik"
	catalog_sdkt_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_sdkt"
	catalog_sdav_common "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdav_common"
	catalog_sdik_common "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdik_common"
	catalog_sdkt_common "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdkt_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/private_schein_common"
	profile_service_employee "git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	bg_billing_service "git.tutum.dev/medi/tutum/ares/service/domains/bg_billing/service"
	catalog_bg_insurance_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_bg_insurance"
	catalog_sdav_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_sdav"
	catalog_sdik_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_sdik"
	catalog_sdkt_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_sdkt"
	doctor_letter_common "git.tutum.dev/medi/tutum/ares/service/domains/doctor_letter/common"
	eab_service "git.tutum.dev/medi/tutum/ares/service/domains/eab/service"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	private_billing_service "git.tutum.dev/medi/tutum/ares/service/domains/private_billing/service"
	pbs_service "git.tutum.dev/medi/tutum/ares/service/domains/private_billing_setting/service"
	qes_common "git.tutum.dev/medi/tutum/ares/service/domains/qes/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_repo"
	header_footer_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/header_footer"
	doctor_letter_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/letter_template"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	scheinRepo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	timeline_common "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/timeline_service"
	document_type_repo "git.tutum.dev/medi/tutum/ares/service/timeline_document_type/repo"
	"git.tutum.dev/medi/tutum/ares/share"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/spf13/cast"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	SalutationContent_Herr string = "Sehr geehrter Herr"
	SalutationContent_Frau string = "Sehr geehrte Frau"
	SalutationContent_None string = "Sehr geehrte/r"
)

type (
	DoctorLetterService struct {
		patientRepo                  *patient_profile.PatientProfileRepository
		templateRepo                 doctor_letter_repo.LetterTemplatesEntityRepo
		headerFooterRepo             header_footer_repo.HeaderFooterEntityRepo
		bsnrService                  *bsnr.BSNRService
		adminService                 admin_service_employee.EmployeeAdminService
		timelineService              *timeline_service.TimelineService[doctor_letter_common.DoctorLetter]
		timelineRepo                 timeline_repo.TimelineEntityRepo[any]
		profileService               profile_service_employee.EmployeeProfileService
		catalogSdktService           *catalog_sdkt_service.CatalogSdktService
		catalogSdavService           *catalog_sdav_service.CatalogSdavService
		scheinRepo                   scheinRepo.ScheinRepoDefaultRepository
		privateBillingService        *private_billing_service.Service
		privateBillingSettingService *pbs_service.PrivateBillingSettingService
		eabService                   *eab_service.EABService
		bgBillingService             *bg_billing_service.Service
		timelineDocumentTypeRepo     *document_type_repo.TimelineDocumentTypeRepo
		catalogBgInsuranceService    *catalog_bg_insurance_service.CatalogBGInsuranceService
		catalogSdikService           *catalog_sdik_service.CatalogSdikService
	}

	UpdateDoctorLetterStatusByIdRequest struct {
		Id     uuid.UUID
		Status qes_common.DocumentStatus
	}

	UpdateDoctorLetterRequest struct {
		TimelineModel timeline_common.TimelineModel
	}
)

type GetReceiverForPrivateInvoiceRequest struct {
	PatientId    uuid.UUID
	ScheinEntity *scheinRepo.ScheinRepo
}

var DoctorLetterServiceMod = submodule.Make[*DoctorLetterService](
	newDoctorLetterService,
	patient_profile.PatientProfileRepositoryMod,
	share.EmployeeAdminServiceMod,
	share.EmployeeProfileServiceMod,
	share.CatalogOverviewServiceMod,
	catalog_sdav_service.CatalogSdavServiceMod,
	timeline_service.TimelineServiceDoctorLetterMod,
	private_billing_service.PrivateBillingServiceMod,
	pbs_service.PrivateBillingSettingServiceMod,
	bsnr.BSNRServiceMod,
	eab_service.EABServiceMod,
	bg_billing_service.BgBillingServiceMod,
	document_type_repo.TimelineDocumentTypeRepoMod,
	catalog_bg_insurance_service.CatalogBGInsuranceServiceMod,
	catalog_sdkt_service.CatalogSdktServiceMod,
	catalog_sdik_service.CatalogSdikServiceMod,
)

func newDoctorLetterService(
	patientRepo *patient_profile.PatientProfileRepository,
	adminService admin_service_employee.EmployeeAdminService,
	profileService profile_service_employee.EmployeeProfileService,
	catalogSdavService *catalog_sdav_service.CatalogSdavService,
	timelineService *timeline_service.TimelineService[doctor_letter_common.DoctorLetter],
	privateBillingService *private_billing_service.Service,
	privateBillingSettingService *pbs_service.PrivateBillingSettingService,
	catalogSdktService *catalog_sdkt_service.CatalogSdktService,
	bsnrService *bsnr.BSNRService,
	eabService *eab_service.EABService,
	bgBillingService *bg_billing_service.Service,
	timelineDocumentTypeRepo *document_type_repo.TimelineDocumentTypeRepo,
	catalogBgInsuranceService *catalog_bg_insurance_service.CatalogBGInsuranceService,
	catalogSdikService *catalog_sdik_service.CatalogSdikService,
) *DoctorLetterService {
	return &DoctorLetterService{
		templateRepo:                 doctor_letter_repo.NewLetterTemplatesEntityRepo(),
		headerFooterRepo:             header_footer_repo.NewHeaderFooterEntityRepo(),
		bsnrService:                  bsnrService,
		timelineService:              timelineService,
		patientRepo:                  patientRepo,
		adminService:                 adminService,
		profileService:               profileService,
		catalogSdavService:           catalogSdavService,
		scheinRepo:                   scheinRepo.NewScheinRepoDefaultRepository(),
		privateBillingService:        privateBillingService,
		privateBillingSettingService: privateBillingSettingService,
		catalogSdktService:           catalogSdktService,
		eabService:                   eabService,
		bgBillingService:             bgBillingService,
		timelineDocumentTypeRepo:     timelineDocumentTypeRepo,
		catalogBgInsuranceService:    catalogBgInsuranceService,
		catalogSdikService:           catalogSdikService,
		timelineRepo:                 timeline_repo.NewTimelineRepoDefaultRepository[any](),
	}
}

func (srv *DoctorLetterService) checkExistTemplate(ctx *titan.Context, templateName string, bsnrId *uuid.UUID) (bool, error) {
	templateCount, err := srv.templateRepo.Count(ctx, bson.M{
		doctor_letter_repo.Field_Name:      templateName,
		doctor_letter_repo.Field_IsDeleted: false,
		doctor_letter_repo.Field_BsnrId:    bsnrId,
	})

	return templateCount > 0, err
}

func (srv *DoctorLetterService) CreateTemplate(ctx *titan.Context, request api.CreateTemplateRequest) (*api.CreateTemplateResponse, error) {
	isExist, err := srv.checkExistTemplate(ctx, request.LetterTemplate.Name, request.BsnrId)
	if err != nil {
		return nil, err
	}
	if isExist {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Template_Is_Exist, "")
	}

	return srv.templateRepo.CreateTemplate(ctx, &request)
}

func (srv *DoctorLetterService) CreatePreDefineTemplate(ctx *titan.Context, bsnrId *uuid.UUID) error {
	if bsnrId == nil {
		return errors.New("CreatePreDefineTemplate error: bsnrId is required")
	}
	// create new predefine template
	return srv.templateRepo.CreatePreDefineTemplate(ctx, bsnrId)
}

func (srv *DoctorLetterService) EditTemplate(ctx *titan.Context, request api.EditTemplateRequest) (*doctor_letter_repo.LetterTemplatesEntity, error) {
	return srv.templateRepo.EditTemplate(ctx, &request)
}

func (srv *DoctorLetterService) DeleteTemplate(ctx *titan.Context, request api.DeleteTemplateRequest) error {
	privateBillingSetting, err := srv.privateBillingSettingService.GetPrivateBillingSetting(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to get private billing setting: %w", err)
	}
	if privateBillingSetting != nil &&
		(util.GetPointerValue(privateBillingSetting.Item.PrivateInvoiceTemplateId) == request.Id ||
			util.GetPointerValue(privateBillingSetting.Item.FirstReminder.PrivateReminderTemplateId) == request.Id ||
			util.GetPointerValue(privateBillingSetting.Item.SecondReminder.PrivateReminderTemplateId) == request.Id ||
			util.GetPointerValue(privateBillingSetting.Item.ThirdReminder.PrivateReminderTemplateId) == request.Id) {
		return errors.New((string(doctor_letter_common.ErrorTemplate_Has_Been_Used)))
	}
	return srv.templateRepo.DeleteTemplate(ctx, request)
}

func (srv *DoctorLetterService) GetListVariable(ctx *titan.Context) (*api.GetListVariableResponse, error) {
	documentTypes, err := srv.timelineDocumentTypeRepo.GetCustomTimelineDocumentTypes(ctx)
	if err != nil {
		return nil, err
	}

	resp, err := srv.templateRepo.GetListVariable(ctx, documentTypes)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (srv *DoctorLetterService) GetTemplates(ctx *titan.Context, request *api.GetTemplateRequest) (*api.GetTemplatesResponse, error) {
	res, total, err := srv.templateRepo.GetTemplates(ctx, request)
	if err != nil {
		return nil, err
	}

	results := slice.Map(res, func(resp doctor_letter_repo.LetterTemplatesEntity) doctor_letter_common.LetterTemplate {
		return doctor_letter_common.LetterTemplate{
			Id:             resp.Id,
			Name:           resp.Name,
			Body:           resp.Body,
			Type:           resp.Type,
			Variables:      resp.Variables,
			IsPredefine:    &resp.IsPredefine,
			UpdatedAt:      util.ConvertToUnixMillis(resp.BaseEntity.UpdatedAt),
			HeaderFooterID: resp.HeaderFooterID,
		}
	})
	totalPage := math.Ceil(float64(total) / float64(request.PaginationRequest.PageSize))

	return &api.GetTemplatesResponse{
		LetterTemplates: results,
		PaginationResponse: common.PaginationResponse{
			Total:     total,
			TotalPage: int64(totalPage),
		},
	}, nil
}

func (srv *DoctorLetterService) GetTemplateDetail(ctx *titan.Context, request *mvzApi.GetTemplateDetailRequest) (*mvzApi.GetTemplateDetailsResponse, error) {
	if request.Id != nil {
		resp, err := srv.templateRepo.FindById(ctx, *request.Id)
		if err != nil {
			return nil, err
		}
		if resp == nil {
			return nil, nil
		}
		return &mvzApi.GetTemplateDetailsResponse{
			LetterTemplates: doctor_letter_common.LetterTemplate{
				Id:             resp.Id,
				Name:           resp.Name,
				Body:           resp.Body,
				Variables:      resp.Variables,
				Type:           resp.Type,
				HeaderFooterID: resp.HeaderFooterID,
				UpdatedAt:      util.ConvertToUnixMillis(resp.BaseEntity.UpdatedAt),
			},
		}, nil
	}

	return nil, nil
}

func (srv *DoctorLetterService) getSender(ctx *titan.Context, schein *scheinRepo.ScheinRepo) ([]doctor_letter_common.Sender, error) {
	//	Sender - BSNR
	bsnrs, err := srv.bsnrService.GetListBSNR(ctx)
	if err != nil {
		return nil, err
	}
	emp, err := srv.profileService.GetEmployeeProfileById(ctx, &profile_service_employee.EmployeeProfileGetRequest{OriginalId: &schein.DoctorId})
	if err != nil {
		return nil, err
	}
	return srv.getSenderData(bsnrs, emp)
}

func (*DoctorLetterService) getSenderData(bsnrs []bsnr_repo.BSNR, emp *profile_service_employee.EmployeeProfileResponse) ([]doctor_letter_common.Sender, error) {
	senders := []doctor_letter_common.Sender{}
	for _, bsnr := range bsnrs {
		senders = append(senders, doctor_letter_common.Sender{
			SenderType: doctor_letter_common.SenderType_BSNR,
			BSNRPayload: &doctor_letter_common.BSNRPayload{
				Id:          bsnr.Id,
				Name:        bsnr.Name,
				Code:        bsnr.Code,
				Street:      bsnr.Street,
				Number:      bsnr.Number,
				PostCode:    bsnr.PostCode,
				City:        bsnr.City,
				PhoneNumber: bsnr.PhoneNumber,
				Fax:         bsnr.Fax,
				Email:       bsnr.Email,
			},
		})
	}
	if emp == nil {
		return senders, nil
	}
	//	Sender - Doctor
	sender := doctor_letter_common.Sender{
		SenderType: doctor_letter_common.SenderType_Doctor,
		DoctorPayload: &doctor_letter_common.DoctorPayload{
			FirstName:      emp.FirstName,
			LastName:       emp.LastName,
			IntendWord:     emp.IntendWord,
			AdditionalName: emp.AdditionalName,
			Bsnr:           emp.Bsnr,
			Id:             *emp.Id,
		},
	}
	if emp.Lanr != nil {
		sender.DoctorPayload.Lanr = *emp.Lanr
	} else if emp.PseudoLanr != nil {
		sender.DoctorPayload.Lanr = *emp.PseudoLanr
	}
	if emp.Title != nil {
		sender.DoctorPayload.Title = *emp.Title
	}
	if emp.Id != nil {
		sender.DoctorPayload.Id = *emp.Id
	}
	senders = append(senders, sender)
	return senders, nil
}

func (srv *DoctorLetterService) getScheinEntity(ctx *titan.Context, scheinId uuid.UUID) (*scheinRepo.ScheinRepo, error) {
	scheinEntity, err := srv.scheinRepo.FindById(ctx, scheinId)
	if err != nil {
		return nil, err
	}
	if scheinEntity == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Schein_Not_Found, "")
	}
	return scheinEntity, nil
}

func (srv *DoctorLetterService) getReceiverForPrivateInvoice(ctx *titan.Context, request GetReceiverForPrivateInvoiceRequest) ([]doctor_letter_common.Receiver, error) {
	// get patient
	patientID := request.PatientId
	patient, err := srv.patientRepo.FindById(ctx, patientID)
	if err != nil {
		return nil, err
	}

	// get insurance info
	scheinEntity := request.ScheinEntity
	privateSchein := scheinEntity.ToPrivateScheinItem()
	insuranceInfo := slice.FindOne(patient.PatientInfo.InsuranceInfos, func(ii patient_profile_common.InsuranceInfo) bool {
		return ii.Id == *privateSchein.InsuranceId && ii.InsuranceType == patient_profile_common.Private
	})

	// get sdik catalog
	var sdikCatalog *catalog_sdik_common.SdikCatalog
	if insuranceInfo != nil {
		catalog, err := srv.catalogSdikService.GetSdikCatalogByIkNumber(ctx, &catalog_sdik_api.GetSdikCatalogByIkNumberRequest{
			IkNumber: strconv.Itoa(int(insuranceInfo.IkNumber)),
		})
		if err != nil {
			return nil, err
		}
		hasSDIK := catalog != nil && catalog.Sdik != nil
		if hasSDIK {
			sdikCatalog = catalog.Sdik
		}
	}
	return srv.getPrivateReceiverData(patient, scheinEntity, sdikCatalog)
}

func (*DoctorLetterService) getPrivateReceiverData(patient *patient.PatientProfile, scheinEntity *scheinRepo.ScheinRepo, sdikCatalog *catalog_sdik_common.SdikCatalog) ([]doctor_letter_common.Receiver, error) {
	// initialize receivers
	receivers := []doctor_letter_common.Receiver{}
	if patient != nil {
		patientReceiver := doctor_letter_common.Receiver{
			ReceiverType: doctor_letter_common.ReceiverType_Patient,
			PatientPayload: &doctor_letter_common.PatientPayload{
				Id:           *patient.Id,
				PersonalInfo: patient.PatientInfo.PersonalInfo,
				ContactInfo:  patient.PatientInfo.ContactInfo,
				Address:      patient.PatientInfo.AddressInfo.Address,
			},
		}

		if scheinEntity != nil {
			privateSchein := scheinEntity.ToPrivateScheinItem()
			if privateSchein.InvoiceSendingType == private_schein_common.InvoiceSendingType_BillingAddress {
				patientReceiver.PatientPayload.BillingAddress = patient.PatientInfo.AddressInfo.BillingAddress
				patientReceiver.PatientPayload.UseBillingAddress = true
			}
		}
		// add patient receiver
		receivers = append(receivers, patientReceiver)
	}
	// add insurance receiver
	if sdikCatalog != nil {
		receivers = append(receivers, doctor_letter_common.Receiver{
			ReceiverType: doctor_letter_common.ReceiverType_Insurance,
			InsurancePayload: &doctor_letter_common.InsurancePayload{
				Id: &sdikCatalog.IkNumber, // use this field as resource id when get private insurance info
				IkNumber: []int32{
					cast.ToInt32(sdikCatalog.IkNumber),
				},
				Name:     sdikCatalog.InsuranceName,
				Street:   sdikCatalog.Address.Street,
				City:     sdikCatalog.Address.City,
				PostCode: sdikCatalog.Address.Street,
			},
		})
	}
	return receivers, nil
}

func (srv *DoctorLetterService) getReceiver(ctx *titan.Context, request mvzApi.GetSenderAndReceiverRequest, isBGSchein bool) ([]doctor_letter_common.Receiver, error) {
	receivers := []doctor_letter_common.Receiver{}
	//	Receiver - Patient
	patient, err := srv.patientRepo.FindById(ctx, request.PatientId)
	if err != nil {
		return nil, err
	}
	receivers = append(receivers, doctor_letter_common.Receiver{
		ReceiverType: doctor_letter_common.ReceiverType_Patient,
		PatientPayload: &doctor_letter_common.PatientPayload{
			Id:           *patient.Id,
			PersonalInfo: patient.PatientInfo.PersonalInfo,
			ContactInfo:  patient.PatientInfo.ContactInfo,
			Address:      patient.PatientInfo.AddressInfo.Address,
		},
	})
	//	Receiver - Employer
	employer := patient.PatientInfo.EmploymentInfo
	receivers = append(receivers, doctor_letter_common.Receiver{
		ReceiverType: doctor_letter_common.ReceiverType_Employee,
		EmployerPayload: &doctor_letter_common.EmployerPayload{
			PatientId:      request.PatientId,
			EmployeeName:   employer.CompanyAddress.Employer,
			CompanyAddress: &employer.CompanyAddress,
		},
	})
	// Receiver - Insurance
	if isBGSchein {
		scheinEntity, err := srv.getScheinEntity(ctx, *request.ScheinID)
		if err != nil {
			return nil, err
		}

		insuranceId := scheinEntity.Schein.InsuranceId
		findInsurance := slice.FindOne(patient.PatientInfo.InsuranceInfos, func(i patient_profile_common.InsuranceInfo) bool {
			return i.Id == insuranceId
		})

		res, err := srv.catalogBgInsuranceService.GetBGInsuranceByIkNumber(ctx, &catalog_bg_insurance.GetBGInsuranceByIkNumberRequest{
			IkNumber: findInsurance.GetIkNumberString(),
		})
		if err != nil || res == nil {
			return nil, err
		}

		datum := res.BgInsurance
		insurancePayload := doctor_letter_common.InsurancePayload{
			Id:       &datum.IkNumber,
			IkNumber: []int32{cast.ToInt32(datum.IkNumber)},
			Name:     datum.InsuranceName,
		}
		insurancePayload.Street = datum.Street
		insurancePayload.City = datum.Location
		insurancePayload.PostCode = datum.PostCode

		receivers = append(receivers, doctor_letter_common.Receiver{
			ReceiverType:     doctor_letter_common.ReceiverType_Insurance,
			InsurancePayload: &insurancePayload,
		})
	} else {
		var ikNumberStringArr []string
		for _, insurance := range patient.PatientInfo.InsuranceInfos {
			ikNumberStringArr = append(ikNumberStringArr, cast.ToString(insurance.IkNumber))
		}
		ikNUmbersString := strings.Join(ikNumberStringArr, ",")

		res, err := srv.catalogSdktService.SearchSdkt(ctx, &catalog_sdkt_api.SearchSdktRequest{
			SearchType:   catalog_sdkt_common.SearchType_IkNumbers,
			SelectedDate: util.NowUnixMillis(ctx),
			Value:        ikNUmbersString,
		})
		if err != nil {
			return nil, err
		}

		if res == nil || len(res.Items) == 0 {
			return receivers, nil
		}

		for _, sdkt := range res.Items {
			var iks []int32
			for _, ikNumb := range sdkt.IKNumbers {
				if ikNumb != nil {
					iks = append(iks, *ikNumb.Value)
				}
			}
			insurancePayload := doctor_letter_common.InsurancePayload{
				Id:       util.NewString(sdkt.Vknr),
				IkNumber: iks,
				Name:     sdkt.Name,
			}
			if sdkt.Address != nil {
				insurancePayload.Street = util.GetValueFromStringPointer(sdkt.Address.Street)
				insurancePayload.City = util.GetValueFromStringPointer(sdkt.Address.City)
				insurancePayload.PostCode = util.GetValueFromStringPointer(sdkt.Address.PostCode)
				insurancePayload.Number = util.GetValueFromStringPointer(sdkt.Address.Number)
			}
			receivers = append(receivers, doctor_letter_common.Receiver{
				ReceiverType:     doctor_letter_common.ReceiverType_Insurance,
				InsurancePayload: &insurancePayload,
			})
		}
	}
	return receivers, nil
}

func (srv *DoctorLetterService) GetSenderAndReceiver(ctx *titan.Context, request mvzApi.GetSenderAndReceiverRequest) (*mvzApi.GetSenderAndReceiverResponse, error) {
	scheinEntity, err := srv.getScheinEntity(ctx, *request.ScheinID)
	if err != nil {
		return nil, err
	}
	// get senders
	senders, err := srv.getSender(ctx, scheinEntity)
	if err != nil {
		return nil, err
	}

	if scheinEntity.IsPrivateSchein() {
		// get receivers for private invoice
		receivers, err := srv.getReceiverForPrivateInvoice(ctx, GetReceiverForPrivateInvoiceRequest{
			PatientId:    request.PatientId,
			ScheinEntity: scheinEntity,
		})
		if err != nil {
			return nil, err
		}

		var privateBillingId *uuid.UUID
		if request.ScheinID != nil {
			privateBilling, err := srv.privateBillingService.GetPrivateBillingByScheinId(ctx, *request.ScheinID)
			if err != nil {
				return nil, errors.WithMessage(err, "failed to get private billing by schein id")
			}
			privateBillingId = privateBilling.Id
		}

		return &mvzApi.GetSenderAndReceiverResponse{
			Senders:          senders,
			Receivers:        receivers,
			PrivateBillingId: privateBillingId,
		}, nil
	}

	// get receivers for normal doctor letter
	receivers, err := srv.getReceiver(ctx, request, scheinEntity.IsBgSchein())
	if err != nil {
		return nil, err
	}
	return &mvzApi.GetSenderAndReceiverResponse{
		Senders:   senders,
		Receivers: receivers,
	}, nil
}

func checkTemplateEAB(templateType doctor_letter_common.TemplateType) bool {
	return templateType == doctor_letter_common.TemplateType_Eab
}

func (srv *DoctorLetterService) CreateDoctorLetter(ctx *titan.Context, request mvzApi.CreateDoctorLetterRequest) (*timeline_repo.TimelineEntity[doctor_letter_common.DoctorLetter], error) {
	letterItem := request.DoctorLetter
	if letterItem.Id == nil {
		letterItem.Id = util.NewPointer(uuid.New())
	}

	scheinIds := []uuid.UUID{}
	if request.ScheinId != nil {
		scheinIds = append(scheinIds, util.GetPointerValue(request.ScheinId))
		letterItem.ScheinId = request.ScheinId
	} else {
		scheinIds = nil
	}

	doctorLetterEntity, err := srv.timelineService.Create(ctx, timeline_repo.TimelineEntity[doctor_letter_common.DoctorLetter]{
		PatientId:         request.PatientId,
		Payload:           letterItem,
		TreatmentDoctorId: request.TreatmentDoctorId,
		ScheinIds:         scheinIds,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to create doctor letter timeline.")
	}

	isEabTemplate := checkTemplateEAB((doctorLetterEntity.Payload.Type))
	scheinEntity, err := srv.getScheinEntity(ctx, util.GetPointerValue(request.ScheinId))
	if err != nil {
		return nil, err
	}

	scheinPrivate := scheinEntity.IsPrivateSchein()
	if isEabTemplate && !scheinPrivate {
		TimelineId := util.GetPointerValue(doctorLetterEntity.Id)
		_, err := srv.eabService.CreateEAB(ctx, eab_service.CreateEABRequest{
			DoctorLetterId: TimelineId,
		})
		if err != nil {
			return nil, err
		}
	}
	return doctorLetterEntity, nil
}

func (srv *DoctorLetterService) UpdateDoctorLetter(ctx *titan.Context, request UpdateDoctorLetterRequest) (*timeline_repo.TimelineEntity[doctor_letter_common.DoctorLetter], error) {
	templateType := request.TimelineModel.DoctorLetter.Type
	doctorLetterId := request.TimelineModel.DoctorLetter.Id
	isTemplateEAB := checkTemplateEAB(templateType)

	entity := timeline_service.ConvertModelToEntity[doctor_letter_common.DoctorLetter](request.TimelineModel)
	doctorLetterEntity, err := srv.timelineService.Edit(ctx, entity)
	if err != nil {
		return nil, err
	}
	if doctorLetterEntity == nil {
		return nil, nil
	}

	eabModel, err := srv.eabService.FindByDoctorLetterId(ctx, util.GetPointerValue(doctorLetterId))
	if err != nil {
		return nil, err
	}

	if isTemplateEAB {
		if eabModel == nil {
			_, err := srv.eabService.CreateEAB(ctx, eab_service.CreateEABRequest{
				DoctorLetterId: util.GetPointerValue(request.TimelineModel.Id),
			})
			if err != nil {
				return nil, err
			}
		} else {
			err := srv.eabService.RestoreById(ctx, *eabModel.GetId())
			if err != nil {
				return nil, err
			}
		}
	} else {
		if eabModel != nil {
			err := srv.eabService.DeleteById(ctx, *eabModel.GetId())
			if err != nil {
				return nil, err
			}
		}
	}

	return doctorLetterEntity, nil
}

func ConvertTimeEntityToModel(entity *timeline_repo.TimelineEntity[doctor_letter_common.DoctorLetter]) (*timeline_common.TimelineModel, error) {
	timelineAny := timeline_repo.TimelineEntity[any]{}
	err := copier.Copy(&timelineAny, entity)
	if err != nil {
		return nil, err
	}
	model := timeline_service.ConvertEntityAnyToModel(timelineAny)
	return &model, nil
}

// func (srv *DoctorLetterService) GetValueVariable(ctx *titan.Context, request mvzApi.GetValueVariableRequest) (*mvzApi.GetValueVariableResponse, error) {
// 	resourceUuid, _ := uuid.Parse(request.ResourceId)
// 	switch request.Category {
// 	case doctor_letter_common.General:
// 		bsnrs, err := srv.bsnrService.GetByCodes(ctx, []string{request.ResourceId})
// 		if err != nil {
// 			return nil, err
// 		}
// 		currentBsnr := slice.FindOne(bsnrs, func(bsnr bsnr_repo.BSNR) bool {
// 			return bsnr.Code == request.ResourceId
// 		})
// 		if currentBsnr == nil {
// 			return nil, nil
// 		}
// 		return &mvzApi.GetValueVariableResponse{
// 			Variables: ToGeneralVariable(currentBsnr),
// 		}, nil

// 	case doctor_letter_common.BSNR:
// 		bsnr, err := srv.bsnrService.GetById(ctx, resourceUuid)
// 		if err != nil {
// 			return nil, err
// 		}
// 		if bsnr == nil {
// 			return nil, nil
// 		}
// 		// NOTE: bsnr info
// 		variables := ToBsnrVariable(bsnr)

// 		// NOTE: general sender
// 		variables = append(variables, FromBsnrToGeneralSenderVariables(bsnr)...)

// 		return &mvzApi.GetValueVariableResponse{
// 			Variables: variables,
// 		}, nil

// 	case doctor_letter_common.DoctorInformation:
// 		doctor, err := srv.profileService.GetEmployeeProfileById(ctx, &profile_service_employee.EmployeeProfileGetRequest{
// 			OriginalId: &resourceUuid,
// 		})
// 		if err != nil {
// 			return nil, err
// 		}
// 		isRegularTemplate := request.TemplateType == nil ||
// 			slice.Contains([]doctor_letter_common.TemplateType{
// 				doctor_letter_common.TemplateType_DoctorLetter,
// 				doctor_letter_common.TemplateType_Eab,
// 				doctor_letter_common.TemplateType_Bg,
// 			}, util.GetPointerValue(request.TemplateType))
// 		variables := srv.getDoctorVariables(doctor, isRegularTemplate)

// 		return &mvzApi.GetValueVariableResponse{
// 			Variables: variables,
// 		}, nil

// 	case doctor_letter_common.VitalParamenter:
// 		patient, err := srv.patientRepo.FindById(ctx, resourceUuid)
// 		if err != nil {
// 			return nil, err
// 		}
// 		if patient == nil || patient.PatientMedicalData == nil {
// 			return nil, nil
// 		}
// 		return &mvzApi.GetValueVariableResponse{
// 			Variables: ToVitalParameterVariable(patient),
// 		}, nil

// 	case doctor_letter_common.PatientInformation:
// 		patient, err := srv.patientRepo.FindById(ctx, resourceUuid)
// 		if err != nil {
// 			return nil, err
// 		}
// 		if patient == nil || patient.PatientInfo == nil {
// 			return nil, nil
// 		}
// 		scheinEntity, err := srv.getScheinEntity(ctx, util.GetPointerValue(request.ScheinId))
// 		if err != nil {
// 			return nil, err
// 		}
// 		isRegularTemplate := request.TemplateType == nil ||
// 			slice.Contains([]doctor_letter_common.TemplateType{
// 				doctor_letter_common.TemplateType_DoctorLetter,
// 				doctor_letter_common.TemplateType_Eab,
// 			}, util.GetPointerValue(request.TemplateType))

// 		variables, err := srv.getPatientVariables(ctx, patient, scheinEntity, isRegularTemplate)
// 		if err != nil {
// 			return nil, err
// 		}

// 		return &mvzApi.GetValueVariableResponse{
// 			Variables: variables,
// 		}, nil

// 	case doctor_letter_common.EmployerInformation:
// 		patient, err := srv.patientRepo.FindById(ctx, resourceUuid)
// 		if err != nil {
// 			return nil, err
// 		}
// 		if patient == nil || patient.PatientInfo == nil {
// 			return nil, nil
// 		}
// 		return &mvzApi.GetValueVariableResponse{
// 			Variables: ToEmployerVariable(patient.PatientInfo.EmploymentInfo),
// 		}, nil

// 	case doctor_letter_common.InsuranceInformation:
// 		scheinEntity, err := srv.getScheinEntity(ctx, util.GetPointerValue(request.ScheinId))
// 		if err != nil {
// 			return nil, err
// 		}
// 		if scheinEntity.IsPrivateSchein() {
// 			return srv.getPrivateInsuranceInfomation(ctx, request)
// 		}
// 		if scheinEntity.IsBgSchein() {
// 			return srv.getBgInsuranceInformation(ctx, scheinEntity.PatientId, request)
// 		}
// 		return srv.getPublicInsuranceInformation(ctx, scheinEntity.PatientId, request)

// 	case doctor_letter_common.SDAV:
// 		sdav, err := srv.catalogSdavService.GetSdavById(ctx, &catalog_sdav.GetSdavByIdRequest{
// 			Id:           &resourceUuid,
// 			SelectedDate: util.NowUnixMillis(ctx),
// 		})
// 		if err != nil {
// 			return nil, err
// 		}
// 		if sdav == nil {
// 			return nil, nil
// 		}

// 		fullName := sdav.DoctorInfo.GetFullName()
// 		return &mvzApi.GetValueVariableResponse{
// 			Variables: FromSDAVToGeneralVariable(*sdav, fullName),
// 		}, nil
// 	case doctor_letter_common.Invoice:
// 		return srv.getInvoiceVariablesInfo(ctx, request)
// 	case doctor_letter_common.BG:
// 		return srv.getBgScheinVariablesInfo(ctx, request)
// 	case doctor_letter_common.MedicalDocumentation:
// 		return srv.getMedicalDocumentationVariablesInfo(ctx, request)
// 	}

// 	return nil, nil
// }

func (srv *DoctorLetterService) getMedicalDocumentationVariablesInfo(ctx *titan.Context, patientId uuid.UUID, category doctor_letter_common.Category, startTime, endTime *int64, scheinId uuid.UUID) (*[]doctor_letter_common.Variable, error) {
	timelines, err := srv.timelineRepo.GetTimelinesByCondition(ctx, timeline_repo.GetTimelinesByCondition{
		PatientId: patientId,
		StartTime: startTime,
		EndTime:   endTime,
		TimelineEntityTypes: []timeline_common.TimelineEntityType{
			timeline_common.TimelineEntityType_Note,
			timeline_common.TimelineEntityType_Diagnose,
			timeline_common.TimelineEntityType_Diagnose_AD,
			timeline_common.TimelineEntityType_Diagnose_DD,
			timeline_common.TimelineEntityType_MedicinePrescription,
			timeline_common.TimelineEntityType_GDT,
			timeline_common.TimelineEntityType_Customize,
		},
		ScheinId: scheinId,
	})
	if err != nil {
		return nil, err
	}
	slice.SortBy(timelines, func(a, b timeline_repo.TimelineEntity[any]) bool {
		return a.SelectedDate.Before(b.SelectedDate)
	})

	results := []doctor_letter_common.Variable{
		{
			Category:          category,
			CategoryItemName:  doctor_letter_common.MedicalDocumentation_Anamnesis,
			CustomizeItemName: nil,
			Value: function.Do(func() *string {
				anamnesis := slice.Filter(timelines, func(t timeline_repo.TimelineEntity[any]) bool {
					tm := timeline_service.ConvertEntityAnyToModel(t)
					return tm.EncounterNoteTimeline != nil && tm.EncounterNoteTimeline.Type != nil && *tm.EncounterNoteTimeline.Type == patient_encounter.ANAMNESE
				})
				if len(anamnesis) == 0 {
					return nil
				}
				return util.NewString(
					strings.Join(
						slice.Map(anamnesis, func(a timeline_repo.TimelineEntity[any]) string {
							anamnese := timeline_service.ConvertEntityAnyToModel(a)
							if anamnese.EncounterNoteTimeline == nil {
								return ""
							}
							return anamnese.EncounterNoteTimeline.Note
						}),
						" ",
					),
				)
			}),
		},
		{
			Category:          category,
			CategoryItemName:  doctor_letter_common.MedicalDocumentation_AnamnesticDiagnosis,
			CustomizeItemName: nil,
			Value: function.Do(func() *string {
				diagnoses := slice.Filter(timelines, func(t timeline_repo.TimelineEntity[any]) bool {
					tm := timeline_service.ConvertEntityAnyToModel(t)
					return tm.EncounterDiagnoseTimeline != nil && tm.EncounterDiagnoseTimeline.Type == patient_encounter.DIAGNOSETYPE_ANAMNESTIC
				})
				if len(diagnoses) == 0 {
					return nil
				}
				return util.NewString(
					strings.Join(
						slice.Map(diagnoses, func(a timeline_repo.TimelineEntity[any]) string {
							diagnose := timeline_service.ConvertEntityAnyToModel(a)
							if diagnose.EncounterDiagnoseTimeline == nil {
								return ""
							}
							tp := ""

							if diagnose.EncounterDiagnoseTimeline.Certainty != nil && string(*diagnose.EncounterDiagnoseTimeline.Certainty) != "" {
								tp += " " + string(*diagnose.EncounterDiagnoseTimeline.Certainty)
							}
							if diagnose.EncounterDiagnoseTimeline.Laterality != nil && string(*diagnose.EncounterDiagnoseTimeline.Laterality) != "" {
								tp += " " + string(*diagnose.EncounterDiagnoseTimeline.Laterality)
							}

							return diagnose.EncounterDiagnoseTimeline.Description + " (" + diagnose.EncounterDiagnoseTimeline.Code + tp + ")"
						}),
						" ",
					),
				)
			}),
		},
		{
			Category:          category,
			CategoryItemName:  doctor_letter_common.MedicalDocumentation_Findings,
			CustomizeItemName: nil,
			Value: function.Do(func() *string {
				filtereds := slice.Filter(timelines, func(t timeline_repo.TimelineEntity[any]) bool {
					tm := timeline_service.ConvertEntityAnyToModel(t)
					return tm.EncounterNoteTimeline != nil && tm.EncounterNoteTimeline.Type != nil && *tm.EncounterNoteTimeline.Type == patient_encounter.FINDING
				})
				if len(filtereds) == 0 {
					return nil
				}
				return util.NewString(
					strings.Join(
						slice.Map(filtereds, func(a timeline_repo.TimelineEntity[any]) string {
							filtered := timeline_service.ConvertEntityAnyToModel(a)
							if filtered.EncounterNoteTimeline == nil {
								return ""
							}
							return filtered.EncounterNoteTimeline.Note
						}),
						" ",
					),
				)
			}),
		},
		{
			Category:          category,
			CategoryItemName:  doctor_letter_common.MedicalDocumentation_AcuteDiagnosis,
			CustomizeItemName: nil,
			Value: function.Do(func() *string {
				diagnoses := slice.Filter(timelines, func(t timeline_repo.TimelineEntity[any]) bool {
					tm := timeline_service.ConvertEntityAnyToModel(t)
					return tm.EncounterDiagnoseTimeline != nil && tm.EncounterDiagnoseTimeline.Type == patient_encounter.DIAGNOSETYPE_ACUTE
				})
				if len(diagnoses) == 0 {
					return nil
				}
				return util.NewString(
					strings.Join(
						slice.Map(diagnoses, func(a timeline_repo.TimelineEntity[any]) string {
							diagnose := timeline_service.ConvertEntityAnyToModel(a)
							if diagnose.EncounterDiagnoseTimeline == nil {
								return ""
							}

							tp := ""

							if diagnose.EncounterDiagnoseTimeline.Certainty != nil && string(*diagnose.EncounterDiagnoseTimeline.Certainty) != "" {
								tp += " " + string(*diagnose.EncounterDiagnoseTimeline.Certainty)
							}
							if diagnose.EncounterDiagnoseTimeline.Laterality != nil && string(*diagnose.EncounterDiagnoseTimeline.Laterality) != "" {
								tp += " " + string(*diagnose.EncounterDiagnoseTimeline.Laterality)
							}

							return diagnose.EncounterDiagnoseTimeline.Description + " (" + diagnose.EncounterDiagnoseTimeline.Code + tp + ")"
						}),
						" ",
					),
				)
			}),
		},
		{
			Category:          category,
			CategoryItemName:  doctor_letter_common.MedicalDocumentation_PermanentDiagnosis,
			CustomizeItemName: nil,
			Value: function.Do(func() *string {
				diagnoses := slice.Filter(timelines, func(t timeline_repo.TimelineEntity[any]) bool {
					tm := timeline_service.ConvertEntityAnyToModel(t)
					return tm.EncounterDiagnoseTimeline != nil && tm.EncounterDiagnoseTimeline.Type == patient_encounter.DIAGNOSETYPE_PERMANENT
				})
				if len(diagnoses) == 0 {
					return nil
				}
				return util.NewString(
					strings.Join(
						slice.Map(diagnoses, func(a timeline_repo.TimelineEntity[any]) string {
							diagnose := timeline_service.ConvertEntityAnyToModel(a)
							if diagnose.EncounterDiagnoseTimeline == nil {
								return ""
							}

							tp := ""

							if diagnose.EncounterDiagnoseTimeline.Certainty != nil && string(*diagnose.EncounterDiagnoseTimeline.Certainty) != "" {
								tp += " " + string(*diagnose.EncounterDiagnoseTimeline.Certainty)
							}
							if diagnose.EncounterDiagnoseTimeline.Laterality != nil && string(*diagnose.EncounterDiagnoseTimeline.Laterality) != "" {
								tp += " " + string(*diagnose.EncounterDiagnoseTimeline.Laterality)
							}

							return diagnose.EncounterDiagnoseTimeline.Description + " (" + diagnose.EncounterDiagnoseTimeline.Code + tp + ")"
						}),
						" ",
					),
				)
			}),
		},
		{
			Category:          category,
			CategoryItemName:  doctor_letter_common.MedicalDocumentation_PrescribedMedication,
			CustomizeItemName: nil,
			Value: function.Do(func() *string {
				diagnoses := slice.Filter(timelines, func(t timeline_repo.TimelineEntity[any]) bool {
					tm := timeline_service.ConvertEntityAnyToModel(t)
					return tm.EncounterMedicinePrescription != nil
				})
				if len(diagnoses) == 0 {
					return nil
				}
				return util.NewString(
					strings.Join(
						slice.Map(diagnoses, func(a timeline_repo.TimelineEntity[any]) string {
							diagnose := timeline_service.ConvertEntityAnyToModel(a)
							if diagnose.EncounterMedicinePrescription == nil {
								return ""
							}
							results := []string{}
							for _, f := range diagnose.EncounterMedicinePrescription.FormInfos {
								if f == nil {
									continue
								}
								for _, m := range f.Medicines {
									results = append(results, m.Name)
								}
							}
							return strings.Join(results, " ")
						}),
						" ",
					),
				)
			}),
		},
		{
			Category:          category,
			CategoryItemName:  doctor_letter_common.MedicalDocumentation_Therapy,
			CustomizeItemName: nil,
			Value: function.Do(func() *string {
				filtereds := slice.Filter(timelines, func(t timeline_repo.TimelineEntity[any]) bool {
					tm := timeline_service.ConvertEntityAnyToModel(t)
					return tm.EncounterNoteTimeline != nil && tm.EncounterNoteTimeline.Type != nil && *tm.EncounterNoteTimeline.Type == patient_encounter.THERAPY
				})
				val, _ := json.Marshal(filtereds)
				fmt.Println(string(val))
				if len(filtereds) == 0 {
					return nil
				}
				return util.NewString(
					strings.Join(
						slice.Map(filtereds, func(a timeline_repo.TimelineEntity[any]) string {
							filtered := timeline_service.ConvertEntityAnyToModel(a)
							if filtered.EncounterNoteTimeline == nil {
								return ""
							}
							return filtered.EncounterNoteTimeline.Note
						}),
						" ",
					),
				)
			}),
		},
		{
			Category:          category,
			CategoryItemName:  doctor_letter_common.MedicalDocumentation_Note,
			CustomizeItemName: nil,
			Value: function.Do(func() *string {
				filtereds := slice.Filter(timelines, func(t timeline_repo.TimelineEntity[any]) bool {
					tm := timeline_service.ConvertEntityAnyToModel(t)
					return tm.EncounterNoteTimeline != nil && tm.EncounterNoteTimeline.Type != nil && *tm.EncounterNoteTimeline.Type == patient_encounter.NOTE
				})
				if len(filtereds) == 0 {
					return nil
				}
				return util.NewString(
					strings.Join(
						slice.Map(filtereds, func(a timeline_repo.TimelineEntity[any]) string {
							filtered := timeline_service.ConvertEntityAnyToModel(a)
							if filtered.EncounterNoteTimeline == nil {
								return ""
							}
							return filtered.EncounterNoteTimeline.Note
						}),
						" ",
					),
				)
			}),
		},
		{
			Category:          category,
			CategoryItemName:  doctor_letter_common.MedicalDocumentation_LabParameter,
			CustomizeItemName: nil,
			Value: function.Do(func() *string {
				filtereds := slice.Filter(timelines, func(t timeline_repo.TimelineEntity[any]) bool {
					tm := timeline_service.ConvertEntityAnyToModel(t)
					return tm.EncounterLab != nil
				})
				if len(filtereds) == 0 {
					return nil
				}
				return util.NewString(
					strings.Join(
						slice.Map(filtereds, func(a timeline_repo.TimelineEntity[any]) string {
							filtered := timeline_service.ConvertEntityAnyToModel(a)
							if filtered.EncounterLab == nil {
								return ""
							}
							return filtered.EncounterLab.LabForm.LabOrderNumber
						}),
						", ",
					),
				)
			}),
		},
		{
			Category:          category,
			CategoryItemName:  doctor_letter_common.MedicalDocumentation_GdtImported,
			CustomizeItemName: nil,
			Value: function.Do(func() *string {
				gdts := slice.Filter(timelines, func(t timeline_repo.TimelineEntity[any]) bool {
					tm := timeline_service.ConvertEntityAnyToModel(t)
					return tm.EncounterGDT != nil
				})
				if len(gdts) == 0 {
					return nil
				}
				return util.NewString(
					strings.Join(
						slice.Map(gdts, func(a timeline_repo.TimelineEntity[any]) string {
							gdt := timeline_service.ConvertEntityAnyToModel(a)
							if gdt.EncounterGDT == nil {
								return ""
							}
							return gdt.EncounterGDT.Note
						}),
						" ",
					),
				)
			}),
		},
	}

	documentTypes, err := srv.timelineDocumentTypeRepo.GetAllTimelineDocumentTypes(ctx)
	if err != nil {
		return nil, err
	}

	for _, d := range documentTypes {
		if d == nil {
			continue
		}
		results = append(results, doctor_letter_common.Variable{
			Category:          category,
			CustomizeItemName: util.NewString(d.Abbreviation),
			Value: function.Do(func() *string {
				filtereds := slice.Filter(timelines, func(t timeline_repo.TimelineEntity[any]) bool {
					tm := timeline_service.ConvertEntityAnyToModel(t)
					return tm.EncounterCustomize != nil && tm.EncounterCustomize.Command == d.Abbreviation
				})
				if len(filtereds) == 0 {
					return nil
				}
				return util.NewString(
					strings.Join(
						slice.Map(filtereds, func(a timeline_repo.TimelineEntity[any]) string {
							gdt := timeline_service.ConvertEntityAnyToModel(a)
							if gdt.EncounterCustomize == nil {
								return ""
							}
							return gdt.EncounterCustomize.Description
						}),
						" ",
					),
				)
			}),
		})
	}

	return util.NewPointer(results), nil
}

func (srv *DoctorLetterService) getBgScheinVariablesInfo(ctx *titan.Context, scheinId uuid.UUID) (*[]doctor_letter_common.Variable, error) {
	scheinItem, err := srv.getScheinEntity(ctx, scheinId)
	if err != nil {
		return nil, err
	}

	dateString := ""
	createdOn := ""

	if scheinItem.BgScheinDetail.AccidentDate != 0 {
		dateString = util.FormatMillisecondsToString(&scheinItem.BgScheinDetail.AccidentDate, "02.01.2006 15:04")
	}

	if scheinItem.BgScheinDetail.CreatedOn != 0 {
		createdOn = util.FormatMillisecondsToString(&scheinItem.BgScheinDetail.CreatedOn, "02.01.2006")
	}

	variables := []doctor_letter_common.Variable{
		{
			Category:         doctor_letter_common.BG,
			CategoryItemName: doctor_letter_common.BG_Accidentday,
			Value:            &dateString,
		},
		{
			Category:         doctor_letter_common.BG,
			CategoryItemName: doctor_letter_common.Invoice_Date,
			Value:            &createdOn,
		},
		{
			Category:         doctor_letter_common.BG,
			CategoryItemName: doctor_letter_common.Invoice_Number,
			Value:            scheinItem.BgScheinDetail.InvoiceNumber,
		},
	}
	return &variables, nil
}

func (srv *DoctorLetterService) getInvoiceVariablesInfo(ctx *titan.Context, scheinId uuid.UUID) (*[]doctor_letter_common.Variable, error) {
	// get private billing
	privateBilling, err := srv.privateBillingService.GetPrivateBillingByScheinId(ctx, scheinId)
	if err != nil {
		return nil, err
	}
	if privateBilling == nil {
		return nil, nil
	}
	privateBillingSetting, err := srv.privateBillingSettingService.GetPrivateBillingSetting(ctx, nil)
	if err != nil {
		return nil, err
	}
	return util.NewPointer(srv.getInvoiceVariables(ctx, *privateBilling, privateBillingSetting)), nil
}

func (srv *DoctorLetterService) getPublicInsuranceInformation(ctx *titan.Context, patientId uuid.UUID, insuranceCompanyId string) (*[]doctor_letter_common.Variable, error) {
	patient, err := srv.patientRepo.FindById(ctx, patientId)
	if err != nil {
		return nil, err
	}
	sdkts, err := srv.catalogSdktService.SearchSdkt(ctx, &catalog_sdkt_api.SearchSdktRequest{
		SearchType:   catalog_sdkt_common.SearchType_VKNR,
		SelectedDate: util.NowUnixMillis(ctx),
		Value:        insuranceCompanyId,
	})
	if err != nil {
		return nil, err
	}
	if sdkts == nil || len(sdkts.Items) == 0 {
		return nil, nil
	}
	currentInsurance := slice.FindOne(sdkts.Items, func(i *catalog_sdkt_common.SdktCatalog) bool {
		return i.Name != "" && i.Vknr == insuranceCompanyId
	})
	if currentInsurance == nil || util.GetPointerValue(currentInsurance) == nil {
		return nil, nil
	}
	var ikNumberStringArr []string
	for _, ik := range (*currentInsurance).IKNumbers {
		ikNumberStringArr = append(ikNumberStringArr, cast.ToString(*ik.Value))
	}
	// find insurance in patient insurances match with current insurnace sdkt to get one iknumber
	findInsurance := slice.FindOne(patient.PatientInfo.InsuranceInfos, func(i patient_profile_common.InsuranceInfo) bool {
		return i.InsuranceCompanyId == (*currentInsurance).Vknr
	})

	IkNumber := ""
	if findInsurance != nil {
		IkNumber = cast.ToString(findInsurance.IkNumber)
	} else if len(ikNumberStringArr) == 0 {
		IkNumber = strings.Join(ikNumberStringArr, ",")
	}

	return util.NewPointer(ToInsuranceVariable(*currentInsurance, IkNumber)), nil
}

func (srv *DoctorLetterService) getBgInsuranceInformation(ctx *titan.Context, patientId, insuranceId uuid.UUID) (*[]doctor_letter_common.Variable, error) {
	patient, err := srv.patientRepo.FindById(ctx, patientId)
	if err != nil {
		return nil, err
	}
	findInsurance := slice.FindOne(patient.PatientInfo.InsuranceInfos, func(i patient_profile_common.InsuranceInfo) bool {
		return i.Id == insuranceId
	})

	bgCatalog, err := srv.catalogBgInsuranceService.GetBGInsuranceByIkNumber(ctx, &catalog_bg_insurance.GetBGInsuranceByIkNumberRequest{
		IkNumber: findInsurance.GetIkNumberString(),
	})
	if err != nil {
		return nil, err
	}
	if bgCatalog == nil {
		return nil, nil
	}
	return util.NewPointer(ToInsuranceVariableFromBg(util.GetPointerValue((bgCatalog.BgInsurance)))), nil
}

func (srv *DoctorLetterService) getPrivateInsuranceInfomation(ctx *titan.Context, ikNumber string) (*[]doctor_letter_common.Variable, error) {
	sdikCatalog, err := srv.catalogSdikService.GetSdikCatalogByIkNumber(ctx, &catalog_sdik_api.GetSdikCatalogByIkNumberRequest{
		IkNumber: ikNumber,
	})
	if err != nil {
		return nil, err
	}
	if sdikCatalog == nil {
		return nil, nil
	}
	return util.NewPointer(ToInsuranceAndRecevierVariableFromSDIK(util.GetPointerValue(sdikCatalog.Sdik))), nil
}

func (srv *DoctorLetterService) fromPatientToInsuranceVariables(ctx *titan.Context, patient *patient.PatientProfile, scheinEntity *scheinRepo.ScheinRepo) ([]doctor_letter_common.Variable, error) {
	if patient.PatientInfo == nil || len(patient.PatientInfo.InsuranceInfos) == 0 {
		return []doctor_letter_common.Variable{}, nil
	}

	selectedInsurance := slice.FindOne(patient.PatientInfo.InsuranceInfos, func(i patient_profile_common.InsuranceInfo) bool {
		return i.Id == scheinEntity.Schein.InsuranceId
	})

	if selectedInsurance == nil {
		return []doctor_letter_common.Variable{}, nil
	}

	ikNumberStr := strconv.Itoa(int(selectedInsurance.IkNumber))

	res, err := srv.catalogSdktService.SearchSdkt(ctx, &catalog_sdkt_api.SearchSdktRequest{
		SearchType:   catalog_sdkt_common.SearchType_IkNumbers,
		SelectedDate: util.NowUnixMillis(ctx),
		Value:        ikNumberStr,
	})
	if err != nil {
		return []doctor_letter_common.Variable{}, err
	}

	if res == nil || len(res.Items) == 0 {
		return []doctor_letter_common.Variable{}, nil
	}

	variables := []doctor_letter_common.Variable{
		{
			Category:         doctor_letter_common.InsuranceInformation,
			CategoryItemName: doctor_letter_common.InsuranceInformation_InsuranceName,
			Value:            &selectedInsurance.InsuranceCompanyName,
		},
		{
			Category:         doctor_letter_common.InsuranceInformation,
			CategoryItemName: doctor_letter_common.InsuranceInformation_InsuranceStatus,
			Value:            util.NewString(string(selectedInsurance.InsuranceStatus)),
		},
	}

	sdktCatalog := slice.FindOne(res.Items, func(sdkt *catalog_sdkt_common.SdktCatalog) bool {
		isMatch := slice.FindOne(sdkt.IKNumbers, func(ikNum *catalog_sdkt_common.IkNumber) bool {
			return ikNum != nil && ikNum.Value != nil && *ikNum.Value == selectedInsurance.IkNumber
		})
		return isMatch != nil
	})

	if sdktCatalog != nil && *sdktCatalog != nil {
		sdkt := *sdktCatalog

		streetNumber := ""
		postCodeCity := ""
		if sdkt.Address != nil {
			streetNumber = util.GetPointerValue(getAddressVariableValue(sdkt.Address.Street, sdkt.Address.Number))
			postCodeCity = util.GetPointerValue(getAddressVariableValue(sdkt.Address.PostCode, sdkt.Address.City))
		}

		variables = append(variables, doctor_letter_common.Variable{
			Category:         doctor_letter_common.InsuranceInformation,
			CategoryItemName: doctor_letter_common.InsuranceInformation_Ik,
			Value:            util.NewPointer(ikNumberStr),
		}, doctor_letter_common.Variable{
			Category:         doctor_letter_common.InsuranceInformation,
			CategoryItemName: doctor_letter_common.InsuranceInformation_StreetNumber,
			Value:            util.NewPointer(streetNumber),
		}, doctor_letter_common.Variable{
			Category:         doctor_letter_common.InsuranceInformation,
			CategoryItemName: doctor_letter_common.InsuranceInformation_PostalCodeCity,
			Value:            util.NewPointer(postCodeCity),
		})
	}

	return variables, nil
}

func getAddressVariableValue(address, subAddress *string) *string {
	addressStr := []string{util.GetValueFromStringPointer(address), util.GetValueFromStringPointer(subAddress)}
	return util.NewString(strings.Join(addressStr, " "))
}

func ToGeneralVariable(bsnr *bsnr_repo.BSNR) []doctor_letter_common.Variable {
	bankInfoStr := slice.Reduce(bsnr.BankInformations, func(accStr string, bank common.BankInformation) string {
		return accStr + "\n" + fmt.Sprintf("%s %s %s %s", util.GetStringValue(bank.BankName),
			util.GetStringValue(bank.BankAccount),
			util.GetStringValue(bank.Iban),
			util.GetStringValue(bank.Bic))
	}, "")
	bsnrVars := []doctor_letter_common.Variable{
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.BSNR_PracticeName, // NOTE: for special cases
			Value:            &bsnr.Name,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_PracticeEmail,
			Value:            &bsnr.Email,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_PracticePhone,
			Value:            &bsnr.PhoneNumber,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_PracticeFax,
			Value:            &bsnr.Fax,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_PracticePostalCodeCity,
			Value:            getAddressVariableValue(&bsnr.PostCode, &bsnr.City),
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_PracticeStreetNo,
			Value:            getAddressVariableValue(&bsnr.Street, &bsnr.Number),
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.BSNR_BSNR,
			Value:            &bsnr.Code,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.BSNR_Stamp,
			Value:            &bsnr.PracticeStamp,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.BSNR_BankName,
			Value:            &bankInfoStr,
		},
	}

	bsnrVars = append(bsnrVars, ToBsnrVariable(bsnr)...)

	return bsnrVars
}

func FromSDAVToGeneralVariable(sdav catalog_sdav_common.SdavCatalog, fullName string) []doctor_letter_common.Variable {
	bsnrVars := []doctor_letter_common.Variable{
		{
			Category:         doctor_letter_common.SDAV,
			CategoryItemName: doctor_letter_common.General_ReceiverName,
			Value:            util.NewString(fullName),
		},
		{
			Category:         doctor_letter_common.SDAV,
			CategoryItemName: doctor_letter_common.General_ReceiverMail,
			Value:            sdav.ContactInfo.Email,
		},
		{
			Category:         doctor_letter_common.SDAV,
			CategoryItemName: doctor_letter_common.General_ReceiverPhone,
			Value:            sdav.ContactInfo.MobilePhoneNumber,
		},
		{
			Category:         doctor_letter_common.SDAV,
			CategoryItemName: doctor_letter_common.General_ReceiverStreetNo,
			Value:            getAddressVariableValue(sdav.GeneralInfo.Street, sdav.GeneralInfo.Number),
		},
		{
			Category:         doctor_letter_common.SDAV,
			CategoryItemName: doctor_letter_common.General_ReceiverPostalCodeCity,
			Value:            getAddressVariableValue(sdav.GeneralInfo.PostCode, sdav.GeneralInfo.City),
		},
	}

	return bsnrVars
}

func ToBsnrVariable(bsnr *bsnr_repo.BSNR) []doctor_letter_common.Variable {
	bankInfoStr := slice.Reduce(bsnr.BankInformations, func(accStr string, bank common.BankInformation) string {
		return accStr + "\n" + fmt.Sprintf("%s %s %s %s", util.GetStringValue(bank.BankName),
			util.GetStringValue(bank.BankAccount),
			util.GetStringValue(bank.Iban),
			util.GetStringValue(bank.Bic))
	}, "")

	bsnrVariables := []doctor_letter_common.Variable{
		{
			Category:         doctor_letter_common.BSNR,
			CategoryItemName: doctor_letter_common.BSNR_PracticeName,
			Value:            &bsnr.Name,
		},
		{
			Category:         doctor_letter_common.BSNR,
			CategoryItemName: doctor_letter_common.BSNR_BSNR,
			Value:            &bsnr.Code,
		},
		{
			Category:         doctor_letter_common.BSNR,
			CategoryItemName: doctor_letter_common.BSNR_PostalCodeCity,
			Value:            getAddressVariableValue(&bsnr.PostCode, &bsnr.City),
		},
		{
			Category:         doctor_letter_common.BSNR,
			CategoryItemName: doctor_letter_common.BSNR_StreetNumber,
			Value:            getAddressVariableValue(&bsnr.Street, &bsnr.Number),
		},
		{
			Category:         doctor_letter_common.BSNR,
			CategoryItemName: doctor_letter_common.BSNR_Phone,
			Value:            &bsnr.PhoneNumber,
		},
		{
			Category:         doctor_letter_common.BSNR,
			CategoryItemName: doctor_letter_common.BSNR_Fax,
			Value:            &bsnr.Fax,
		},
		{
			Category:         doctor_letter_common.BSNR,
			CategoryItemName: doctor_letter_common.BSNR_Email,
			Value:            &bsnr.Email,
		},
		{
			Category:         doctor_letter_common.BSNR,
			CategoryItemName: doctor_letter_common.BSNR_Stamp,
			Value:            &bsnr.PracticeStamp,
		},
		{
			Category:         doctor_letter_common.BSNR,
			CategoryItemName: doctor_letter_common.BSNR_BankName,
			Value:            &bankInfoStr,
		},
	}

	return bsnrVariables
}

func getLatestBankInfo(bankInfos []*common.BankInformation) *common.BankInformation {
	if len(bankInfos) == 0 {
		return nil
	}
	return bankInfos[len(bankInfos)-1]
}

func ToDoctorVariable(doctor *profile_service_employee.EmployeeProfileResponse) []doctor_letter_common.Variable {
	bankInformation := getLatestBankInfo(doctor.BankInformations)
	bankAccount := ""
	bankName := ""
	iban := ""
	ibic := ""
	if bankInformation != nil {
		bankAccount = util.GetPointerValue(bankInformation.BankAccount)
		bankName = util.GetPointerValue(bankInformation.BankName)
		iban = util.GetPointerValue(bankInformation.Iban)
		ibic = util.GetPointerValue(bankInformation.Bic)
	}
	return []doctor_letter_common.Variable{
		{
			Category:         doctor_letter_common.DoctorInformation,
			CategoryItemName: doctor_letter_common.DoctorInformation_Name,
			Value:            &doctor.FullName,
		},
		{
			Category:         doctor_letter_common.DoctorInformation,
			CategoryItemName: doctor_letter_common.DoctorInformation_LANR,
			Value:            doctor.Lanr,
		},
		{
			Category:         doctor_letter_common.DoctorInformation,
			CategoryItemName: doctor_letter_common.DoctorInformation_Stamp,
			Value:            &doctor.DoctorStamp,
		},
		{
			Category:         doctor_letter_common.DoctorInformation,
			CategoryItemName: doctor_letter_common.DoctorInformation_BankAccount,
			Value:            &bankAccount,
		},
		{
			Category:         doctor_letter_common.DoctorInformation,
			CategoryItemName: doctor_letter_common.DoctorInformation_Bank,
			Value:            &bankName,
		},
		{
			Category:         doctor_letter_common.DoctorInformation,
			CategoryItemName: doctor_letter_common.DoctorInformation_IBAN,
			Value:            &iban,
		},
		{
			Category:         doctor_letter_common.DoctorInformation,
			CategoryItemName: doctor_letter_common.DoctorInformation_BIC,
			Value:            &ibic,
		},
	}
}

func ToPatientVariable(patient *patient.PatientProfile, sendInvoiceToBillingAddress bool, insuranceId uuid.UUID) []doctor_letter_common.Variable {
	currentInsurance := slice.FindOne(patient.PatientInfo.InsuranceInfos, func(i patient_profile_common.InsuranceInfo) bool {
		return i.Id == insuranceId
	})
	patientStreetNo := getAddressVariableValue(patient.PatientInfo.AddressInfo.Address.Street, patient.PatientInfo.AddressInfo.Address.Number)
	patientPostalcodeAndCity := getAddressVariableValue(&patient.PatientInfo.AddressInfo.Address.PostCode, patient.PatientInfo.AddressInfo.Address.City)

	postBoxNumber := patient.PatientInfo.PostOfficeBox.OfficeBox
	postBoxLocation := fmt.Sprintf("%s %s", patient.PatientInfo.PostOfficeBox.PostCode, patient.PatientInfo.PostOfficeBox.PlaceOfResidence)
	if postBoxNumber != "" || strings.Trim(postBoxLocation, " ") != "" {
		patientStreetNo = util.NewString(postBoxNumber)
		patientPostalcodeAndCity = util.NewString(postBoxLocation)
	}

	// use billing address
	if sendInvoiceToBillingAddress {
		patientStreetNo = getAddressVariableValue(patient.PatientInfo.AddressInfo.BillingAddress.Street, patient.PatientInfo.AddressInfo.BillingAddress.Number)
		patientPostalcodeAndCity = getAddressVariableValue(&patient.PatientInfo.AddressInfo.BillingAddress.PostCode, patient.PatientInfo.AddressInfo.BillingAddress.City)
	}

	patientin := patient.GetPatientin()
	salutation := patient.GetSalutation()
	salutationAkk := patient.GetSalulationAkk()

	return []doctor_letter_common.Variable{
		{
			Category:         doctor_letter_common.PatientInformation,
			CategoryItemName: doctor_letter_common.PatientInformation_Title,
			Value:            patient.PatientInfo.PersonalInfo.Title,
		},
		{
			Category:         doctor_letter_common.PatientInformation,
			CategoryItemName: doctor_letter_common.PatientInformation_FirstName,
			Value:            &patient.PatientInfo.PersonalInfo.FirstName,
		},
		{
			Category:         doctor_letter_common.PatientInformation,
			CategoryItemName: doctor_letter_common.PatientInformation_LastName,
			Value:            &patient.PatientInfo.PersonalInfo.LastName,
		},
		{
			Category:         doctor_letter_common.PatientInformation,
			CategoryItemName: doctor_letter_common.PatientInformation_AdditionalName,
			Value:            util.NewPointer(patient.PatientInfo.PersonalInfo.GetAdditionalNames()),
		},
		{
			Category:         doctor_letter_common.PatientInformation,
			CategoryItemName: doctor_letter_common.PatientInformation_IntentWord,
			Value:            (*string)(patient.PatientInfo.PersonalInfo.IntendWord),
		},
		{
			Category:         doctor_letter_common.PatientInformation,
			CategoryItemName: doctor_letter_common.PatientInformation_ContactNumerLandline,
			Value:            util.NewPointer(strings.Join(patient.PatientInfo.ContactInfo.FurtherContactNumber, ",")),
		},
		{
			Category:         doctor_letter_common.PatientInformation,
			CategoryItemName: doctor_letter_common.PatientInformation_ContactNumerMobile,
			Value:            patient.PatientInfo.ContactInfo.PrimaryContactNumber,
		},
		{
			Category:         doctor_letter_common.PatientInformation,
			CategoryItemName: doctor_letter_common.PatientInformation_Email,
			Value:            patient.PatientInfo.ContactInfo.EmailAddress,
		},
		{
			Category:         doctor_letter_common.PatientInformation,
			CategoryItemName: doctor_letter_common.PatientInformation_StreetNumber,
			Value:            patientStreetNo,
		},
		{
			Category:         doctor_letter_common.PatientInformation,
			CategoryItemName: doctor_letter_common.PatientInformation_PostalCodeCity,
			Value:            patientPostalcodeAndCity,
		},
		{
			Category:         doctor_letter_common.PatientInformation,
			CategoryItemName: doctor_letter_common.PatientInformation_AdditionalAdress,
			Value:            patient.PatientInfo.AddressInfo.Address.AdditionalAddressInfo,
		},
		{
			Category:         doctor_letter_common.PatientInformation,
			CategoryItemName: doctor_letter_common.PatientInformation_Dob,
			Value:            util.NewPointer(patient.PatientInfo.PersonalInfo.DateOfBirthStringPatientSidebar()),
		},
		{
			Category:         doctor_letter_common.PatientInformation,
			CategoryItemName: doctor_letter_common.PatientInformation_Gender,
			Value:            (*string)(&patient.PatientInfo.PersonalInfo.Gender),
		},
		{
			Category:         doctor_letter_common.PatientInformation,
			CategoryItemName: doctor_letter_common.PatientInformation_InsuranceNumber,
			Value:            currentInsurance.InsuranceNumber,
		},
		{
			Category:         doctor_letter_common.PatientInformation,
			CategoryItemName: doctor_letter_common.PatientInformation_JobDescription,
			Value:            (*string)(patient.PatientInfo.EmploymentInfo.JobStatus),
		},
		{
			Category:         doctor_letter_common.PatientInformation,
			CategoryItemName: doctor_letter_common.PatientInformation_Patient,
			Value:            util.NewString(patientin),
		},
		{
			Category:         doctor_letter_common.PatientInformation,
			CategoryItemName: doctor_letter_common.PatientInformation_Salutation,
			Value:            util.NewString(salutation),
		},
		{
			Category:         doctor_letter_common.PatientInformation,
			CategoryItemName: doctor_letter_common.PatientInformation_SalutationAkkusativ,
			Value:            util.NewString(salutationAkk),
		},
	}
}

func ToInsuranceVariable(insurance *catalog_sdkt_common.SdktCatalog, ikNumber string) []doctor_letter_common.Variable {
	streetNumber := ""
	postalCodeCity := ""

	if insurance.Address != nil {
		streetNumber = *getAddressVariableValue(insurance.Address.Street, insurance.Address.Number)
		postalCodeCity = *getAddressVariableValue(insurance.Address.PostCode, insurance.Address.City)
	}
	return []doctor_letter_common.Variable{
		{
			Category:         doctor_letter_common.InsuranceInformation,
			CategoryItemName: doctor_letter_common.InsuranceInformation_InsuranceName,
			Value:            &insurance.Name,
		},
		{
			Category:         doctor_letter_common.InsuranceInformation,
			CategoryItemName: doctor_letter_common.InsuranceInformation_Ik,
			Value:            util.NewPointer(ikNumber),
		},
		{
			Category:         doctor_letter_common.InsuranceInformation,
			CategoryItemName: doctor_letter_common.InsuranceInformation_StreetNumber,
			Value:            &streetNumber,
		},
		{
			Category:         doctor_letter_common.InsuranceInformation,
			CategoryItemName: doctor_letter_common.InsuranceInformation_PostalCodeCity,
			Value:            &postalCodeCity,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverName,
			Value:            util.NewPointer(insurance.Name),
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverStreetNo,
			Value:            &streetNumber,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverPostalCodeCity,
			Value:            &postalCodeCity,
		},
	}
}

func ToInsuranceVariableFromBg(insurance catalog_bg_insurance_common.BGInsuranceCatalog) []doctor_letter_common.Variable {
	streetNumber := insurance.Street
	postalCodeCity := *getAddressVariableValue(&insurance.PostCode, &insurance.Location)

	return []doctor_letter_common.Variable{
		{
			Category:         doctor_letter_common.InsuranceInformation,
			CategoryItemName: doctor_letter_common.InsuranceInformation_InsuranceName,
			Value:            &insurance.InsuranceName,
		},
		{
			Category:         doctor_letter_common.InsuranceInformation,
			CategoryItemName: doctor_letter_common.InsuranceInformation_Ik,
			Value:            util.NewPointer(insurance.IkNumber),
		},
		{
			Category:         doctor_letter_common.BG,
			CategoryItemName: doctor_letter_common.General_ReceiverName,
			Value:            &insurance.InsuranceName,
		},
		{
			Category:         doctor_letter_common.BG,
			CategoryItemName: doctor_letter_common.General_ReceiverStreetNo,
			Value:            &streetNumber,
		},
		{
			Category:         doctor_letter_common.BG,
			CategoryItemName: doctor_letter_common.General_ReceiverPostalCodeCity,
			Value:            &postalCodeCity,
		},
	}
}

func ToInsuranceAndRecevierVariableFromSDIK(sdik catalog_sdik_common.SdikCatalog) []doctor_letter_common.Variable {
	postalCodeCity := getAddressVariableValue(util.NewPointer(sdik.Address.PostCode), util.NewPointer(sdik.Address.City))
	return []doctor_letter_common.Variable{
		{
			Category:         doctor_letter_common.InsuranceInformation,
			CategoryItemName: doctor_letter_common.InsuranceInformation_InsuranceName,
			Value:            util.NewPointer(sdik.InsuranceName),
		},
		{
			Category:         doctor_letter_common.InsuranceInformation,
			CategoryItemName: doctor_letter_common.InsuranceInformation_Ik,
			Value:            util.NewPointer(sdik.IkNumber),
		},
		{
			Category:         doctor_letter_common.InsuranceInformation,
			CategoryItemName: doctor_letter_common.InsuranceInformation_StreetNumber,
			Value:            util.NewPointer(sdik.Address.Street),
		},
		{
			Category:         doctor_letter_common.InsuranceInformation,
			CategoryItemName: doctor_letter_common.InsuranceInformation_PostalCodeCity,
			Value:            postalCodeCity,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverName,
			Value:            util.NewPointer(sdik.InsuranceName),
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverStreetNo,
			Value:            util.NewPointer(sdik.Address.Street),
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverPostalCodeCity,
			Value:            postalCodeCity,
		},
	}
}

func ToEmployerVariable(employer patient_profile_common.EmploymentInfo) []doctor_letter_common.Variable {
	postalCodeCity := ""
	if employer.CompanyAddress.PostCode != nil || employer.CompanyAddress.City != nil {
		if employer.CompanyAddress.PostCode != nil {
			postalCodeCity += *employer.CompanyAddress.PostCode
			if employer.CompanyAddress.City != nil {
				postalCodeCity += " " + *employer.CompanyAddress.City
			}
		} else if employer.CompanyAddress.City != nil {
			postalCodeCity += *employer.CompanyAddress.City
		}
	}
	streetNumber := *getAddressVariableValue(employer.CompanyAddress.Street, employer.CompanyAddress.Number)
	return []doctor_letter_common.Variable{
		{
			Category:         doctor_letter_common.EmployerInformation,
			CategoryItemName: doctor_letter_common.EmployerInformation_EmployerName,
			Value:            employer.CompanyAddress.Employer,
		},
		{
			Category:         doctor_letter_common.EmployerInformation,
			CategoryItemName: doctor_letter_common.EmployerInformation_StreetNumber,
			Value:            &streetNumber,
		},
		{
			Category:         doctor_letter_common.EmployerInformation,
			CategoryItemName: doctor_letter_common.EmployerInformation_PostalCodeCity,
			Value:            &postalCodeCity,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverName,
			Value:            employer.CompanyAddress.Employer,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverStreetNo,
			Value:            &streetNumber,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverPostalCodeCity,
			Value:            &postalCodeCity,
		},
	}
}

func ToVitalParameterVariable(patient *patient.PatientProfile) []doctor_letter_common.Variable {
	allergies := []string{}

	if patient.PatientMedicalData == nil {
		emptyValue := ""

		return []doctor_letter_common.Variable{
			{
				Category:         doctor_letter_common.VitalParamenter,
				CategoryItemName: doctor_letter_common.VitalParamenter_Cave,
				Value:            &emptyValue,
			},
			{
				Category:         doctor_letter_common.VitalParamenter,
				CategoryItemName: doctor_letter_common.VitalParamenter_Height,
				Value:            &emptyValue,
			},
			{
				Category:         doctor_letter_common.VitalParamenter,
				CategoryItemName: doctor_letter_common.VitalParamenter_Weight,
				Value:            &emptyValue,
			},
			{
				Category:         doctor_letter_common.VitalParamenter,
				CategoryItemName: doctor_letter_common.VitalParamenter_BMI,
				Value:            &emptyValue,
			},
			{
				Category:         doctor_letter_common.VitalParamenter,
				CategoryItemName: doctor_letter_common.VitalParamenter_BloodPressure,
				Value:            &emptyValue,
			},
			{
				Category:         doctor_letter_common.VitalParamenter,
				CategoryItemName: doctor_letter_common.VitalParamenter_Allergies,
				Value:            &emptyValue,
			},
		}
	}

	for _, allergy := range patient.PatientMedicalData.Allergies {
		allergies = append(allergies, allergy.Allergy)
	}

	weightFloat := util.GetFloat64Value(patient.PatientMedicalData.Weight)
	heightFloat := util.GetFloat64Value(patient.PatientMedicalData.Height)
	bloodPressure := util.GetPointerValue(patient.PatientMedicalData.BloodPressure)

	weightFloatStr := ""
	if weightFloat != 0 {
		weightFloatStr = fmt.Sprintf("%s kg", util.ToFixed2Decimal(weightFloat))
	}

	heightFloatStr := ""
	if heightFloat != 0 {
		heightFloatStr = fmt.Sprintf("%s cm", util.ToFixed2Decimal(heightFloat))
	}

	// prevent NaN value in the editor
	vitalBMI := ""
	bmi := (weightFloat / math.Pow((heightFloat/float64(100)), 2))
	if !math.IsNaN(bmi) {
		vitalBMI = util.ToFixed2Decimal(bmi)
	}

	bloodPresserStr := ""
	if bloodPressure != "" {
		bloodPresserStr = fmt.Sprintf("%s mmHg", bloodPressure)
	}

	return []doctor_letter_common.Variable{
		{
			Category:         doctor_letter_common.VitalParamenter,
			CategoryItemName: doctor_letter_common.VitalParamenter_Cave,
			Value:            &patient.PatientInfo.OtherInfo.Cave,
		},
		{
			Category:         doctor_letter_common.VitalParamenter,
			CategoryItemName: doctor_letter_common.VitalParamenter_Height,
			Value:            util.NewPointer(heightFloatStr),
		},
		{
			Category:         doctor_letter_common.VitalParamenter,
			CategoryItemName: doctor_letter_common.VitalParamenter_Weight,
			Value:            util.NewPointer(weightFloatStr),
		},
		{
			Category:         doctor_letter_common.VitalParamenter,
			CategoryItemName: doctor_letter_common.VitalParamenter_BMI,
			Value:            util.NewPointer(vitalBMI),
		},
		{
			Category:         doctor_letter_common.VitalParamenter,
			CategoryItemName: doctor_letter_common.VitalParamenter_BloodPressure,
			Value:            util.NewPointer(bloodPresserStr),
		},
		{
			Category:         doctor_letter_common.VitalParamenter,
			CategoryItemName: doctor_letter_common.VitalParamenter_Allergies,
			Value:            util.NewPointer(strings.Join(allergies, ", ")),
		},
	}
}

func FromPatientToGeneralReceiverVariables(patient *patient.PatientProfile, sendInvoiceToBillingAddress bool) []doctor_letter_common.Variable {
	patientFullName := patient.GetGeneralFullNameNoSalutation()
	patientStreetNo := getAddressVariableValue(patient.PatientInfo.AddressInfo.Address.Street, patient.PatientInfo.AddressInfo.Address.Number)
	patientPostalcodeAndCity := getAddressVariableValue(&patient.PatientInfo.AddressInfo.Address.PostCode, patient.PatientInfo.AddressInfo.Address.City)

	postBoxNumber := patient.PatientInfo.PostOfficeBox.OfficeBox
	postBoxLocation := fmt.Sprintf("%s %s", patient.PatientInfo.PostOfficeBox.PostCode, patient.PatientInfo.PostOfficeBox.PlaceOfResidence)

	if postBoxNumber != "" || strings.Trim(postBoxLocation, " ") != "" {
		patientStreetNo = util.NewString(postBoxNumber)
		patientPostalcodeAndCity = util.NewString(postBoxLocation)
	}
	// use billing address
	if sendInvoiceToBillingAddress {
		patientStreetNo = getAddressVariableValue(patient.PatientInfo.AddressInfo.BillingAddress.Street, patient.PatientInfo.AddressInfo.BillingAddress.Number)
		patientPostalcodeAndCity = getAddressVariableValue(&patient.PatientInfo.AddressInfo.BillingAddress.PostCode, patient.PatientInfo.AddressInfo.BillingAddress.City)
		patientFullName = patient.GetBillingAddressFullName()
	}

	patientSalutation := generateSalutationContentForPatient(patient.PatientInfo.PersonalInfo.Gender)

	return []doctor_letter_common.Variable{
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverMail,
			Value:            patient.PatientInfo.ContactInfo.EmailAddress,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverName,
			Value:            &patientFullName,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverPhone,
			Value:            patient.PatientInfo.ContactInfo.PrimaryContactNumber,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverStreetNo,
			Value:            patientStreetNo,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverPostalCodeCity,
			Value:            patientPostalcodeAndCity,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverSalutation,
			Value:            &patientSalutation,
		},
	}
}

func FromDoctorToGeneralSenderVariables(doctor *profile_service_employee.EmployeeProfileResponse) []doctor_letter_common.Variable {
	return []doctor_letter_common.Variable{
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_SenderName,
			Value:            &doctor.FullName,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_Sender_BSNR_LANR,
			Value:            doctor.Lanr,
		},
	}
}

func FromBsnrToGeneralSenderVariables(bsnr *bsnr_repo.BSNR) []doctor_letter_common.Variable {
	return []doctor_letter_common.Variable{
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_SenderName,
			Value:            &bsnr.Name,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_Sender_BSNR_LANR,
			Value:            &bsnr.Code,
		},
	}
}

func FromDoctorToGeneralReceiverVariables(doctor *profile_service_employee.EmployeeProfileResponse) []doctor_letter_common.Variable {
	doctorSalutation := generateSalutationContentForDoctor(doctor.Salutation)

	return []doctor_letter_common.Variable{
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverName,
			Value:            &doctor.FullName,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverMail,
			Value:            doctor.Email,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverPhone,
			Value:            &doctor.MobilePhone,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverStreetNo,
			Value:            &doctor.Street,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverPostalCodeCity,
			Value:            &doctor.ZipCode,
		},
		{
			Category:         doctor_letter_common.General,
			CategoryItemName: doctor_letter_common.General_ReceiverSalutation,
			Value:            &doctorSalutation,
		},
	}
}

func generateSalutationContentForDoctor(salutation *patient_profile_common.Salutation) string {
	if salutation == nil {
		return SalutationContent_None
	}

	switch *salutation {
	case patient_profile_common.Salutation_Herr:
		return SalutationContent_Herr
	case patient_profile_common.Salutation_Frau:
		return SalutationContent_Frau
	default:
		return SalutationContent_None
	}
}

func generateSalutationContentForPatient(gender patient_profile_common.Gender) string {
	switch gender {
	case patient_profile_common.M:
		return SalutationContent_Herr
	case patient_profile_common.W:
		return SalutationContent_Frau
	default:
		return SalutationContent_None
	}
}

func (srv *DoctorLetterService) CreateHeaderFooter(ctx *titan.Context, request api.CreateHeaderFooterRequest) (*api.CreateHeaderFooterResponse, error) {
	count, err := srv.headerFooterRepo.Count(ctx, bson.M{
		header_footer_repo.Field_Name:      request.Name,
		header_footer_repo.Field_IsDeleted: false,
		header_footer_repo.Field_BsnrId:    request.BsnrId,
	})
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_HeaderFooter_Is_Exist, "")
	}

	res, err := srv.headerFooterRepo.CreateHeaderFooter(ctx, request)
	if err != nil {
		return nil, err
	}

	return res, nil
}

func (srv *DoctorLetterService) GetHeaderFooters(ctx *titan.Context, request api.GetHeaderFootersRequest) (*api.GetHeaderFootersResponse, error) {
	res, total, err := srv.headerFooterRepo.GetHeaderFooters(ctx, request)
	if err != nil {
		return nil, err
	}
	if res == nil {
		return nil, nil
	}

	result := slice.Map(res, func(m header_footer_repo.HeaderFooterEntity) doctor_letter_common.HeaderFooter {
		return doctor_letter_common.HeaderFooter{
			Id:         m.Id,
			UpdatedAt:  util.NewPointer(util.UnixMillis(*m.UpdatedAt)),
			Name:       m.Name,
			BodyHeader: &m.BodyHeader,
			BodyFooter: &m.BodyFooter,
		}
	})

	resPagination := api.GetHeaderFootersResponse{
		Pagination:    nil,
		HeaderFooters: result,
	}
	if request.Pagination != nil {
		totalPage := math.Ceil(float64(total) / float64(request.Pagination.PageSize))
		resPagination.Pagination = &common.PaginationResponse{
			Total:     int64(total),
			TotalPage: int64(totalPage),
		}
	}

	return &resPagination, nil
}

func (srv *DoctorLetterService) UpdateHeaderFooter(ctx *titan.Context, request api.UpdateHeaderFooterRequest) (*header_footer_repo.HeaderFooterEntity, error) {
	res, err := srv.headerFooterRepo.FindById(ctx, request.Id)
	if err != nil {
		return nil, err
	}
	if res == nil {
		return nil, nil
	}
	if res.IsDeleted {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Data_Invalid, "")
	}

	res, err = srv.headerFooterRepo.FindOneAndUpdate(ctx, bson.M{
		repos.Field_Id: request.Id,
	}, bson.M{
		"$set": bson.M{
			header_footer_repo.Field_Name:       request.Name,
			header_footer_repo.Field_BodyHeader: request.BodyHeader,
			header_footer_repo.Field_BodyFooter: request.BodyFooter,
			header_footer_repo.Field_UpdatedAt:  util.Now(ctx),
		},
	}, options.FindOneAndUpdate().SetReturnDocument((options.After)))
	if err != nil {
		return nil, err
	}

	return res, nil
}

func (srv *DoctorLetterService) DeleteHeaderFooter(ctx *titan.Context, request api.DeleteHeaderFooterRequest) error {
	_, err := srv.headerFooterRepo.FindOneAndUpdate(ctx, bson.M{
		repos.Field_Id: request.Id,
	}, bson.M{
		"$set": bson.M{
			header_footer_repo.Field_IsDeleted: true,
		},
	})
	if err != nil {
		return err
	}
	filter := bson.M{
		doctor_letter_repo.Field_HeaderFooterID: request.Id,
		doctor_letter_repo.Field_IsDeleted:      false,
	}
	update := bson.M{
		"$set": bson.M{
			doctor_letter_repo.Field_HeaderFooterID: nil,
			header_footer_repo.Field_UpdatedAt:      util.Now(ctx),
			header_footer_repo.Field_UpdatedBy:      util.GetPointerValue(ctx.UserInfo().UserUUID()),
		},
	}
	_, err = srv.templateRepo.IDBClient.UpdateMany(ctx, filter, update, nil, nil)

	return err
}

func (srv *DoctorLetterService) UpdateDoctorLetterTemplateType(ctx *titan.Context, request api.UpdateDoctorLetterTemplateTypeRequest) error {
	for _, data := range request.UpdateDataList {
		_, err := srv.templateRepo.FindOneAndUpdate(ctx, bson.M{
			repos.Field_Id: data.Id,
		}, bson.M{
			"$set": bson.M{
				doctor_letter_repo.Field_TemplateType: data.TemplateType,
			},
		})
		if err != nil {
			return err
		}
	}
	return nil
}

func (srv *DoctorLetterService) GetValueVariables(ctx *titan.Context, request mvzApi.GetValueVariablesRequest) (*mvzApi.GetValueVariablesResponse, error) {
	mapValueVariables := make(map[string]mvzApi.ValueVariable)
	patient, err := srv.patientRepo.GetById(ctx, request.PatientId)
	if err != nil {
		return nil, err
	}
	if patient == nil || patient.PatientInfo == nil {
		return nil, errors.New("data doesn't exist")
	}
	scheinEntity, err := srv.getScheinEntity(ctx, request.ScheinId)
	if err != nil {
		return nil, err
	}

	currentInsurance := slice.FindOne(patient.PatientInfo.InsuranceInfos, func(i patient_profile_common.InsuranceInfo) bool {
		return i.Id == scheinEntity.Schein.InsuranceId
	})

	for _, category := range request.Categories {
		switch category {
		case doctor_letter_common.General:
			if request.Bsnr == nil {
				continue
			}
			bsnr := *request.Bsnr
			bsnrs, err := srv.bsnrService.GetByCodes(ctx, []string{bsnr})
			if err != nil {
				continue
			}
			currentBsnr := slice.FindOne(bsnrs, func(b bsnr_repo.BSNR) bool {
				return b.Code == bsnr
			})
			if currentBsnr == nil {
				continue
			}

			mapValueVariables[string(category)] = mvzApi.ValueVariable{
				Variables: ToGeneralVariable(currentBsnr),
			}
			continue

		case doctor_letter_common.BSNR:
			if request.BsnrId == nil {
				continue
			}
			bsnr, err := srv.bsnrService.GetById(ctx, *request.BsnrId)
			if err != nil {
				continue
			}
			if bsnr == nil {
				continue
			}
			// NOTE: bsnr info
			variables := ToBsnrVariable(bsnr)

			// NOTE: general sender
			variables = append(variables, FromBsnrToGeneralSenderVariables(bsnr)...)

			mapValueVariables[string(category)] = mvzApi.ValueVariable{
				Variables: variables,
			}
			continue

		case doctor_letter_common.DoctorInformation:
			if request.DoctorId == nil {
				continue
			}
			doctor, err := srv.profileService.GetEmployeeProfileById(ctx, &profile_service_employee.EmployeeProfileGetRequest{
				OriginalId: request.DoctorId,
			})
			if err != nil {
				continue
			}
			isRegularTemplate := request.TemplateType == nil ||
				slice.Contains([]doctor_letter_common.TemplateType{
					doctor_letter_common.TemplateType_DoctorLetter,
					doctor_letter_common.TemplateType_Eab,
					doctor_letter_common.TemplateType_Bg,
				}, util.GetPointerValue(request.TemplateType))
			variables := srv.getDoctorVariables(doctor, isRegularTemplate)

			mapValueVariables[string(category)] = mvzApi.ValueVariable{
				Variables: variables,
			}
			continue

		case doctor_letter_common.VitalParamenter:
			patient, err := srv.patientRepo.FindById(ctx, request.PatientId)
			if err != nil {
				continue
			}
			if patient == nil {
				continue
			}
			mapValueVariables[string(category)] = mvzApi.ValueVariable{
				Variables: ToVitalParameterVariable(patient),
			}
			continue

		case doctor_letter_common.PatientInformation:
			isRegularTemplate := request.TemplateType == nil ||
				slice.Contains([]doctor_letter_common.TemplateType{
					doctor_letter_common.TemplateType_DoctorLetter,
					doctor_letter_common.TemplateType_Eab,
				}, util.GetPointerValue(request.TemplateType))

			variables, err := srv.getPatientVariables(ctx, patient, scheinEntity, isRegularTemplate)
			if err != nil {
				continue
			}

			mapValueVariables[string(category)] = mvzApi.ValueVariable{
				Variables: variables,
			}
			continue

		case doctor_letter_common.EmployerInformation:
			patient, err := srv.patientRepo.FindById(ctx, request.PatientId)
			if err != nil {
				continue
			}
			if patient == nil || patient.PatientInfo == nil {
				continue
			}
			mapValueVariables[string(category)] = mvzApi.ValueVariable{
				Variables: ToEmployerVariable(patient.PatientInfo.EmploymentInfo),
			}
			continue

		// TODO: need to check resourceId
		case doctor_letter_common.InsuranceInformation:
			if scheinEntity.IsPrivateSchein() {
				result, err := srv.getPrivateInsuranceInfomation(ctx, *request.IkNumber)
				if err != nil {
					continue
				}
				if result == nil {
					continue
				}
				mapValueVariables[string(category)] = mvzApi.ValueVariable{
					Variables: *result,
				}
				continue
			}
			if scheinEntity.IsBgSchein() {
				result, err := srv.getBgInsuranceInformation(ctx, scheinEntity.PatientId, scheinEntity.Schein.InsuranceId)
				if err != nil {
					continue
				}
				if result == nil {
					continue
				}
				mapValueVariables[string(category)] = mvzApi.ValueVariable{
					Variables: *result,
				}
				continue
			}
			result, err := srv.getPublicInsuranceInformation(ctx, scheinEntity.PatientId, activeInsurance.InsuranceCompanyId)
			if err != nil {
				continue
			}
			if result == nil {
				continue
			}
			mapValueVariables[string(category)] = mvzApi.ValueVariable{
				Variables: *result,
			}
			continue

		case doctor_letter_common.SDAV:
			if request.SdavId == nil {
				continue
			}
			res, err := srv.catalogSdavService.GetSdavById(ctx, &catalog_sdav_api.GetSdavByIdRequest{
				Id: request.SdavId.String(),
			})
			if err != nil {
				continue
			}

			if res == nil || res.Data == nil {
				continue
			}

			sdav := res.Data
			fullName := sdav.DoctorInfo.GetFullName()
			mapValueVariables[string(category)] = mvzApi.ValueVariable{
				Variables: FromSDAVToGeneralVariable(*sdav, fullName),
			}
			continue

			// TODO: Need to check resourceId
		case doctor_letter_common.Invoice:
			result, err := srv.getInvoiceVariablesInfo(ctx, request.ScheinId)
			if err != nil {
				continue
			}
			if result == nil {
				continue
			}
			mapValueVariables[string(category)] = mvzApi.ValueVariable{
				Variables: *result,
			}
			continue
		case doctor_letter_common.BG:
			result, err := srv.getBgScheinVariablesInfo(ctx, request.ScheinId)
			if err != nil {
				continue
			}
			if result == nil {
				continue
			}
			mapValueVariables[string(category)] = mvzApi.ValueVariable{
				Variables: *result,
			}
			continue
		case doctor_letter_common.MedicalDocumentation:
			result, err := srv.getMedicalDocumentationVariablesInfo(ctx, request.PatientId, category, request.StartTime, request.EndTime, *scheinEntity.Id)
			if err != nil {
				continue
			}
			if result == nil {
				continue
			}
			mapValueVariables[string(category)] = mvzApi.ValueVariable{
				Variables: *result,
			}
			continue
		}
	}
	return &mvzApi.GetValueVariablesResponse{
		Data: mapValueVariables,
	}, nil
}
