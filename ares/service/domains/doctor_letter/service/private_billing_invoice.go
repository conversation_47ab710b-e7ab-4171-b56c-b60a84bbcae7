package doctor_letter_service

import (
	"fmt"

	"emperror.dev/errors"
	mvzApi "git.tutum.dev/medi/tutum/ares/app/mvz/api/doctor_letter"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/private_billing_setting"
	catalog_sdik_common "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdik_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/private_schein_common"
	profile_service_employee "git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"
	doctor_letter_common "git.tutum.dev/medi/tutum/ares/service/domains/doctor_letter/common"
	pb_common "git.tutum.dev/medi/tutum/ares/service/domains/private_billing/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/private_billing/repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	timeline_common "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"gitlab.com/silenteer-oss/titan"
)

type PrivateBillingData struct {
	PrivateBillingId uuid.UUID
	Schein           *schein.ScheinRepo
	Patient          *patient.PatientProfile
	Doctor           *profile_service_employee.EmployeeProfileResponse
	PrivateBilling   repo.PrivateBillingEntity
}

func (srv *DoctorLetterService) HandlePrivateBillingInvoice(ctx *titan.Context, request mvzApi.HandlePrivateInvoiceRequest) (*timeline_common.TimelineModel, error) {
	letterItem := request.DoctorLetter
	letterItemType := letterItem.Type
	privateBillingStatus := util.GetPointerValue(request.PrivateBillingStatus)

	if letterItem.PrivateInvoice == nil {
		return nil, fmt.Errorf("privateInvoice data is required field")
	}

	// if template type is invoice and private billing status is no invoice
	if letterItemType == doctor_letter_common.TemplateType_Invoice && privateBillingStatus == pb_common.PrivBillingStatus_NoInvoice {
		return srv.createOrUpdatePrivateInvoice(ctx, request)
	}

	// if template type is reminder and private billing status is unpaid or 1st reminder or 2nd reminder
	isOpenReminderTemplate := privateBillingStatus == pb_common.PrivBillingStatus_UnPaid ||
		privateBillingStatus == pb_common.PrivBillingStatus_1stReminder ||
		privateBillingStatus == pb_common.PrivBillingStatus_2ndReminder
	if letterItemType == doctor_letter_common.TemplateType_Invoice && isOpenReminderTemplate {
		return srv.createOrUpdateReminderInvoice(ctx, request)
	}

	return nil, fmt.Errorf("invalid template type or private billing status")
}

func (srv *DoctorLetterService) createOrUpdatePrivateInvoice(ctx *titan.Context, request mvzApi.HandlePrivateInvoiceRequest) (*timeline_common.TimelineModel, error) {
	// create/update invoice timeline
	doctorLetterEntity, err := srv.createOrUpdateInvoiceTimeline(ctx, request)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to create/update private invoice entity timeline.")
	}
	if doctorLetterEntity == nil {
		return nil, nil
	}

	// Update private billing
	letterItem := request.DoctorLetter
	err = srv.updatePrivateBillingFromPrivateInvoice(ctx, letterItem, *doctorLetterEntity.Id)
	if err != nil {
		return nil, fmt.Errorf("failed to update private billing: %w", err)
	}

	if letterItem.PrivateInvoice.Status == doctor_letter_common.InvoiceStatus_Billing_Printed {
		// update private billing status
		privateBillingId := letterItem.PrivateInvoice.PrivateBillingId
		privateBillingItem, err := srv.privateBillingService.GetPrivateBillingById(ctx, privateBillingId)
		if err != nil {
			return nil, fmt.Errorf("failed to get private billing item: %w", err)
		}
		_, err = srv.scheinRepo.UpdatePrivateScheinStatus(ctx, privateBillingItem.PrivScheinId, schein_common.ScheinStatus_Printed)
		if err != nil {
			return nil, fmt.Errorf("failed to mark private schein billed: %w", err)
		}
	}
	return ConvertTimeEntityToModel(doctorLetterEntity)
}

func (srv *DoctorLetterService) createOrUpdateReminderInvoice(ctx *titan.Context, request mvzApi.HandlePrivateInvoiceRequest) (*timeline_common.TimelineModel, error) {
	// create/update invoice timeline
	doctorLetterEntity, err := srv.createOrUpdateInvoiceTimeline(ctx, request)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to create/update private invoice entity timeline.")
	}
	if doctorLetterEntity == nil {
		return nil, nil
	}

	// Update private billing
	letterItem := request.DoctorLetter
	err = srv.updatePrivateBillingFromReminderInvoice(ctx, letterItem, *doctorLetterEntity.Id)
	if err != nil {
		return nil, fmt.Errorf("failed to update private billing: %w", err)
	}

	return ConvertTimeEntityToModel(doctorLetterEntity)
}

func (srv *DoctorLetterService) createOrUpdateInvoiceTimeline(ctx *titan.Context, request mvzApi.HandlePrivateInvoiceRequest) (*timeline_repo.TimelineEntity[doctor_letter_common.DoctorLetter], error) {
	letterItem := request.DoctorLetter
	var doctorLetterEntity *timeline_repo.TimelineEntity[doctor_letter_common.DoctorLetter]
	var err error

	if request.InvoiceTimelineId == nil {
		// If the InvoiceTimeline Id is nil, it's a new  invoice timeline
		letterItem.Id = util.NewUUID()
		// create invoice entity timeline
		doctorLetterEntity, err = srv.timelineService.Create(ctx, timeline_repo.TimelineEntity[doctor_letter_common.DoctorLetter]{
			PatientId: request.PatientId,
			Payload:   letterItem,
		})
	} else {
		// If the InvoiceTimeline id is provided, update the existing invoice timeline
		timelineId := request.InvoiceTimelineId
		if timelineId == nil {
			return nil, fmt.Errorf("invoiceTimelineId is required field")
		}
		doctorLetterEntity, err = srv.timelineService.UpdatePrivateInvoiceTimeline(ctx, *timelineId, &letterItem)
	}
	return doctorLetterEntity, err
}

func (srv *DoctorLetterService) updatePrivateBillingFromPrivateInvoice(ctx *titan.Context, letterItem doctor_letter_common.DoctorLetter, invoiceTimelineId uuid.UUID) error {
	// extract data from private invoice
	privateBillingId := letterItem.PrivateInvoice.PrivateBillingId
	feeAmount := letterItem.PrivateInvoice.Amount
	privateBillingStatus := pb_common.PrivBillingStatus_NoInvoice
	if letterItem.PrivateInvoice.Status == doctor_letter_common.InvoiceStatus_Billing_Printed {
		privateBillingStatus = pb_common.PrivBillingStatus_UnPaid
	}

	// update private billing history
	_, err := srv.privateBillingService.UpdatePrivateBillingFromInvoice(ctx, pb_common.UpdatePrivateBillingFromInvoiceRequest{
		PrivateBillingId:  privateBillingId,
		Status:            privateBillingStatus,
		FeeAmount:         feeAmount,
		InvoiceTimelineId: invoiceTimelineId,
	})

	return err
}

func (srv *DoctorLetterService) updatePrivateBillingFromReminderInvoice(ctx *titan.Context, letterItem doctor_letter_common.DoctorLetter, invoiceTimelineId uuid.UUID) error {
	// extract data from private invoice
	privateBillingId := letterItem.PrivateInvoice.PrivateBillingId

	// Define a map to map invoice statuses to billing statuses
	invoiceToBillingStatus := map[doctor_letter_common.InvoiceStatus]pb_common.PrivateBillingStatus{
		doctor_letter_common.InvoiceStatus_1stReminder_Saved:   pb_common.PrivBillingStatus_UnPaid,
		doctor_letter_common.InvoiceStatus_1stReminder_Printed: pb_common.PrivBillingStatus_1stReminder,
		doctor_letter_common.InvoiceStatus_2ndReminder_Saved:   pb_common.PrivBillingStatus_1stReminder,
		doctor_letter_common.InvoiceStatus_2ndReminder_Printed: pb_common.PrivBillingStatus_2ndReminder,
		doctor_letter_common.InvoiceStatus_3rdReminder_Saved:   pb_common.PrivBillingStatus_2ndReminder,
		doctor_letter_common.InvoiceStatus_3rdReminder_Printed: pb_common.PrivBillingStatus_3rdReminder,
	}
	isPrinted := util.GetPointerValue(letterItem.IsPrinted)
	// Retrieve the private billing status from the map
	privateBillingStatus := invoiceToBillingStatus[letterItem.PrivateInvoice.Status]
	// update private billing history
	_, err := srv.privateBillingService.UpdatePrivateBillingFromReminderInvoice(ctx, pb_common.UpdatePrivateBillingFromInvoiceRequest{
		PrivateBillingId:  privateBillingId,
		Status:            privateBillingStatus,
		InvoiceTimelineId: invoiceTimelineId,
		IsPrintReminder:   isPrinted,
	})

	return err
}

func (srv *DoctorLetterService) GetBulkInvoiceData(ctx *titan.Context, request mvzApi.GetBulkInvoiceDataRequest) (*mvzApi.GetBulkInvoiceDataResponse, error) {
	privateBillingsData, err := srv.prepairDataGetBulkVariables(ctx, request.PrivateBillingIds)
	if err != nil {
		return nil, err
	}
	if len(privateBillingsData) == 0 {
		return nil, nil
	}

	scheins := slice.Reduce(privateBillingsData, func(curr []schein.ScheinRepo, next PrivateBillingData) []schein.ScheinRepo {
		if next.Schein == nil {
			return curr
		}
		if next.Schein.Id == nil {
			return curr
		}
		return append(curr, *next.Schein)
	}, []schein.ScheinRepo{})
	resp := &mvzApi.GetValueVariablesResponse{
		Data: map[string]mvzApi.ValueVariable{},
	}
	for _, schein := range scheins {
		mapVariables, err := srv.GetValueVariables(ctx, mvzApi.GetValueVariablesRequest{
			Categories: []doctor_letter_common.Category{doctor_letter_common.General},
			Bsnr:       &request.BnsrCode,
			PatientId:  schein.PatientId,
			ScheinId:   *schein.Id,
		})

		if err != nil {
			return nil, err
		}
		for k, v := range mapVariables.Data {
			if _, ok := resp.Data[k]; !ok {
				resp.Data[k] = mvzApi.ValueVariable{
					Variables: []doctor_letter_common.Variable{},
				}
			}
			resp.Data[k] = mvzApi.ValueVariable{
				Variables: append(resp.Data[k].Variables, v.Variables...),
			}
		}
	}
	generalReponse, ok := resp.Data[string(doctor_letter_common.General)]
	if !ok {
		return nil, errors.New("data doesn't exist")
	}

	// get doctor variables by private billing ids
	// key: private billing id, value: doctor variables
	mapDoctorVariables := srv.getBulkDoctorVariables(privateBillingsData)

	// get patient variables by private billing ids
	// key: private billing id, value: patient variables
	mapPatientVariables, err := srv.getBulkPatientVariables(ctx, privateBillingsData)
	if err != nil {
		return nil, err
	}

	// get invoice variables by private billing ids
	// key: private billing id, value: invoice variables
	mapInvoiceVariables, err := srv.getBulkInvoiceVariables(ctx, privateBillingsData)
	if err != nil {
		return nil, err
	}

	// get senders by private billing ids
	// key: private billing id, value: senders
	mapSenders, err := srv.getBulkSenders(ctx, privateBillingsData)
	if err != nil {
		return nil, err
	}

	mapReceivers, err := srv.getBulkReceivers(ctx, privateBillingsData)
	if err != nil {
		return nil, err
	}

	// build list invoice data
	listInvoiceData := make([]doctor_letter_common.InvoiceData, 0)
	for _, id := range request.PrivateBillingIds {
		// get senders and receivers
		senders := mapSenders[id]
		receivers := mapReceivers[id]

		invoiceData := doctor_letter_common.InvoiceData{
			PrivateBillingId: id,
			Senders:          senders,
			Receivers:        receivers,
		}

		// add doctor variables
		if doctorVariables, ok := mapDoctorVariables[id]; ok {
			invoiceData.Variables = append(invoiceData.Variables, doctorVariables...)
		}
		// add patient variables
		if patientVariables, ok := mapPatientVariables[id]; ok {
			invoiceData.Variables = append(invoiceData.Variables, patientVariables...)
		}

		// add invoice variables
		if invoiceVariables, ok := mapInvoiceVariables[id]; ok {
			invoiceData.Variables = append(invoiceData.Variables, invoiceVariables...)
		}

		// add general variables
		invoiceData.Variables = append(invoiceData.Variables, generalReponse.Variables...)

		listInvoiceData = append(listInvoiceData, invoiceData)
	}

	return &mvzApi.GetBulkInvoiceDataResponse{
		InvoiceData: listInvoiceData,
	}, nil
}

func (srv *DoctorLetterService) getBulkSenders(ctx *titan.Context, privateBillingsData []PrivateBillingData) (map[uuid.UUID][]doctor_letter_common.Sender, error) {
	mapSenders := make(map[uuid.UUID][]doctor_letter_common.Sender)
	bsnrs, err := srv.bsnrService.GetListBSNR(ctx)
	if err != nil {
		return nil, err
	}
	for _, data := range privateBillingsData {
		senders, err := srv.getSenderData(bsnrs, data.Doctor)
		if err != nil {
			return nil, err
		}
		mapSenders[data.PrivateBillingId] = senders
	}
	return mapSenders, nil
}

func (srv *DoctorLetterService) getBulkReceivers(ctx *titan.Context, privateBillingsData []PrivateBillingData) (map[uuid.UUID][]doctor_letter_common.Receiver, error) {
	mapReceivers := make(map[uuid.UUID][]doctor_letter_common.Receiver)
	// step 1: get list sdik catalogs of private billing
	mapUniqueIKNumbers := make(map[string]bool)
	mapBillingIDtoIKNumbers := make(map[uuid.UUID]string) // key: private billing id, value: ik number
	for _, data := range privateBillingsData {
		patient := data.Patient
		scheinEntity := data.Schein
		if patient == nil || patient.PatientInfo == nil || scheinEntity == nil {
			continue
		}
		privateSchein := scheinEntity.ToPrivateScheinItem()
		// get insurance info from list patient's insurances and private schein insurance id
		insurance := slice.FindOne(patient.PatientInfo.InsuranceInfos, func(i patient_profile_common.InsuranceInfo) bool {
			return i.Id == *privateSchein.InsuranceId && i.InsuranceType == patient_profile_common.Private
		})
		if insurance != nil {
			mapUniqueIKNumbers[insurance.GetIkNumberString()] = true
			mapBillingIDtoIKNumbers[data.PrivateBillingId] = insurance.GetIkNumberString()
		}
	}
	listIKNumbers := make([]string, 0)
	for ikNumber := range mapUniqueIKNumbers {
		listIKNumbers = append(listIKNumbers, ikNumber)
	}

	if len(listIKNumbers) == 0 {
		return mapReceivers, nil
	}

	// get list sdik catalogs by list ik numbers
	// start := time.Now()
	listSdiks, err := srv.catalogSdikService.GetSdiksByListIKnumbers(ctx, listIKNumbers)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	// ctx.Logger().Info("get list sdik catalogs by list ik numbers", "ikNumbers", listIKNumbers, "duration", time.Since(start))
	// map sdik catalogs by ik number
	mapSdikCatalogs := make(map[string]*catalog_sdik_common.SdikCatalog) // key: ik number, value: sdik catalog
	for _, sdik := range listSdiks {
		mapSdikCatalogs[sdik.IkNumber] = sdik
	}
	// step 2: get list receivers
	for _, data := range privateBillingsData {
		ikNumber := mapBillingIDtoIKNumbers[data.PrivateBillingId]
		sdikCatalog := mapSdikCatalogs[ikNumber]
		receivers, err := srv.getPrivateReceiverData(data.Patient, data.Schein, sdikCatalog)
		if err != nil {
			return nil, err
		}
		mapReceivers[data.PrivateBillingId] = receivers
	}
	return mapReceivers, nil
}

func (srv *DoctorLetterService) prepairDataGetBulkVariables(ctx *titan.Context, privateBillingIds []uuid.UUID) ([]PrivateBillingData, error) {
	// get list private billing by ids
	privateBillings, err := srv.privateBillingService.GetPrivateBillingByIds(ctx, privateBillingIds)
	if err != nil {
		return nil, err
	}

	if len(privateBillings) == 0 {
		return nil, nil
	}

	// get list patient ids and schein ids
	patientIdMap := make(map[uuid.UUID]bool)
	scheinIdMap := make(map[uuid.UUID]bool)
	doctorIdMap := make(map[uuid.UUID]bool)

	for _, privateBilling := range privateBillings {
		patientIdMap[privateBilling.Patient.PatientId] = true
		scheinIdMap[privateBilling.PrivScheinId] = true
		doctorIdMap[privateBilling.Doctor.DoctorId] = true
	}

	// Convert maps to slices for repository queries
	patientIds := make([]uuid.UUID, 0, len(patientIdMap))
	for id := range patientIdMap {
		patientIds = append(patientIds, id)
	}

	scheinIds := make([]uuid.UUID, 0, len(scheinIdMap))
	for id := range scheinIdMap {
		scheinIds = append(scheinIds, id)
	}

	doctorIds := make([]uuid.UUID, 0, len(doctorIdMap))
	for id := range doctorIdMap {
		doctorIds = append(doctorIds, id)
	}

	// get patient profiles
	if len(patientIds) == 0 || len(scheinIds) == 0 || len(doctorIds) == 0 {
		return nil, nil
	}

	patients, err := srv.patientRepo.FindByIds(ctx, patientIds)
	if err != nil {
		return nil, err
	}

	scheins, err := srv.scheinRepo.FindByIds(ctx, scheinIds)
	if err != nil {
		return nil, err
	}

	doctorResponse, err := srv.profileService.GetEmployeeProfileByIds(ctx, &profile_service_employee.GetByIdsRequest{
		OriginalIds: doctorIds,
	})
	if err != nil {
		return nil, err
	}
	if doctorResponse == nil {
		return nil, nil
	}

	// Create maps for easy lookup
	patientMap := make(map[uuid.UUID]patient.PatientProfile)
	for _, p := range patients {
		patientMap[*p.Id] = p
	}

	scheinMap := make(map[uuid.UUID]schein.ScheinRepo)
	for _, s := range scheins {
		scheinMap[*s.Id] = s
	}

	doctorMap := make(map[uuid.UUID]*profile_service_employee.EmployeeProfileResponse)
	for _, d := range doctorResponse.Profiles {
		doctorMap[*d.Id] = d
	}

	privateBillingsData := make([]PrivateBillingData, 0, len(privateBillings))
	for _, privateBilling := range privateBillings {
		schein := scheinMap[privateBilling.PrivScheinId]
		patient := patientMap[privateBilling.Patient.PatientId]
		doctor := doctorMap[privateBilling.Doctor.DoctorId]
		data := PrivateBillingData{
			PrivateBillingId: *privateBilling.Id,
			Schein:           util.NewPointer(schein),
			Patient:          util.NewPointer(patient),
			Doctor:           doctor,
			PrivateBilling:   privateBilling,
		}
		privateBillingsData = append(privateBillingsData, data)
	}
	return privateBillingsData, nil
}

func (srv *DoctorLetterService) getBulkDoctorVariables(privateBillingsData []PrivateBillingData) map[uuid.UUID][]doctor_letter_common.Variable {
	// mapDoctorVariables key: private billing id, value: doctor variables
	mapDoctorVariables := make(map[uuid.UUID][]doctor_letter_common.Variable)
	for _, data := range privateBillingsData {
		doctor := data.Doctor
		if doctor == nil {
			continue
		}
		mapDoctorVariables[data.PrivateBillingId] = srv.getDoctorVariables(doctor, false)
	}
	return mapDoctorVariables
}

func (srv *DoctorLetterService) getBulkPatientVariables(ctx *titan.Context, privateBillingsData []PrivateBillingData) (map[uuid.UUID][]doctor_letter_common.Variable, error) {
	mapPatientVariables := make(map[uuid.UUID][]doctor_letter_common.Variable)
	for _, data := range privateBillingsData {
		patient := data.Patient
		schein := data.Schein

		if patient == nil || patient.PatientInfo == nil || schein == nil {
			continue
		}

		// get patient variables
		variables, err := srv.getPatientVariables(ctx, patient, schein, false)
		if err != nil {
			return nil, err
		}
		mapPatientVariables[data.PrivateBillingId] = variables
	}
	return mapPatientVariables, nil
}

func (srv *DoctorLetterService) getBulkInvoiceVariables(ctx *titan.Context, privateBillingsData []PrivateBillingData) (map[uuid.UUID][]doctor_letter_common.Variable, error) {
	privateBillingSetting, err := srv.privateBillingSettingService.GetPrivateBillingSetting(ctx, nil)
	if err != nil {
		return nil, err
	}
	mapInvoiceVariables := make(map[uuid.UUID][]doctor_letter_common.Variable)
	for _, data := range privateBillingsData {
		variables := srv.getInvoiceVariables(ctx, data.PrivateBilling, privateBillingSetting)
		mapInvoiceVariables[data.PrivateBillingId] = variables
	}
	return mapInvoiceVariables, nil
}

func (srv *DoctorLetterService) getInvoiceVariables(ctx *titan.Context, privateBilling repo.PrivateBillingEntity, privateBillingSetting *private_billing_setting.GetPrivateBillingSettingResponse) []doctor_letter_common.Variable {
	reminder := srv.privateBillingService.GetReminder(ctx, privateBillingSetting, privateBilling, false)
	invoiceDate := util.FormatMillisecondsToString(privateBilling.InvoiceDate, "02.01.2006")

	isNoInvoiceBilling := privateBilling.Status == pb_common.PrivBillingStatus_NoInvoice
	if isNoInvoiceBilling {
		invoiceDate = util.Now(ctx).Format("02.01.2006")
	}
	openInvoiceAmount := privateBilling.FeeAmount - privateBilling.PaidAmount
	invoiceAmount := reminder.Fee + openInvoiceAmount
	// build variable
	var variables []doctor_letter_common.Variable
	variables = append(variables, doctor_letter_common.Variable{
		Category:         doctor_letter_common.Invoice,
		CategoryItemName: doctor_letter_common.Invoice_Number,
		Value:            util.NewPointer(privateBilling.InvoiceNumber),
	}, doctor_letter_common.Variable{
		Category:         doctor_letter_common.Invoice,
		CategoryItemName: doctor_letter_common.Invoice_Date,
		Value:            util.NewString(invoiceDate),
	}, doctor_letter_common.Variable{
		Category:         doctor_letter_common.Invoice,
		CategoryItemName: doctor_letter_common.Invoice_ReminderFee,
		Value:            util.NewString(fmt.Sprintf("%v", util.ToFixed(reminder.Fee, 2))),
	}, doctor_letter_common.Variable{
		Category:         doctor_letter_common.Invoice,
		CategoryItemName: doctor_letter_common.Invoice_OpenAmount,
		Value:            util.NewString(fmt.Sprintf("%v", util.ToFixed(openInvoiceAmount, 2))),
	}, doctor_letter_common.Variable{
		Category:         doctor_letter_common.Invoice,
		CategoryItemName: doctor_letter_common.Invoice_Amount,
		Value:            util.NewString(fmt.Sprintf("%v", util.ToFixed(invoiceAmount, 2))),
	})
	return variables
}

func (*DoctorLetterService) getDoctorVariables(doctor *profile_service_employee.EmployeeProfileResponse, isRegularTemplate bool) []doctor_letter_common.Variable {
	if doctor == nil {
		return nil
	}
	doctor.FullName = doctor.GetDoctorFullName()
	// NOTE: doctor info
	variables := ToDoctorVariable(doctor)
	// NOTE: general sender
	variables = append(variables, FromDoctorToGeneralSenderVariables(doctor)...)
	if isRegularTemplate {
		// NOTE: general receiver
		variables = append(variables, FromDoctorToGeneralReceiverVariables(doctor)...)
	}
	return variables
}

func (srv *DoctorLetterService) getPatientVariables(ctx *titan.Context, patient *patient.PatientProfile, scheinEntity *schein.ScheinRepo, isRegularTemplate bool) ([]doctor_letter_common.Variable, error) {
	sendInvoiceToBillingAddress := false
	if !isRegularTemplate && scheinEntity.IsPrivateSchein() {
		privateSchein := scheinEntity.ToPrivateScheinItem()
		if privateSchein.InvoiceSendingType == private_schein_common.InvoiceSendingType_BillingAddress {
			sendInvoiceToBillingAddress = true
		}
	}
	// NOTE: patient info
	variables := ToPatientVariable(patient, sendInvoiceToBillingAddress, scheinEntity.Schein.InsuranceId)

	// NOTE: general receiver info
	variables = append(variables, FromPatientToGeneralReceiverVariables(patient, sendInvoiceToBillingAddress)...)

	// NOTE: insurance info
	if len(patient.PatientInfo.InsuranceInfos) != 0 && isRegularTemplate {
		insuranceVariables, err := srv.fromPatientToInsuranceVariables(ctx, patient, scheinEntity)
		if err != nil {
			return nil, err
		}
		if len(insuranceVariables) != 0 {
			variables = append(variables, insuranceVariables...)
		}
	}

	return variables, nil
}
