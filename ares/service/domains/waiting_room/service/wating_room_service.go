package service

import (
	"fmt"

	"emperror.dev/errors"
	waiting_room_api "git.tutum.dev/medi/tutum/ares/app/admin/api/waiting_room"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	cal_service "git.tutum.dev/medi/tutum/ares/service/cal"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/waiting_room_patient_audit_log_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/waiting_room_patient_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/task_type"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/waiting_room"
	employeeProfileRepo "git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee"
	patient_profile "git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/timeline_service"
	"git.tutum.dev/medi/tutum/ares/service/domains/waiting_room/common/waiting_room_common"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type WaitingRoomService struct {
	waitingRoomRepo                waiting_room.WaitingRoomRepo
	employeeRepo                   employeeProfileRepo.EmployeeProfileDefaultRepository
	bsnrRepo                       bsnr_repo.BSNRDefaultRepository
	patientProfileRepo             patient_profile.PatientProfileDefaultRepository
	waitingRoomPatientRepo         waiting_room_patient_repo.WaitingRoomPatientDefaultRepository
	waitingRoomPatientAuditLogRepo waiting_room_patient_audit_log_repo.WaitingRoomPatientAuditLogDefaultRepository
	taskTypeRepo                   task_type.TaskTypeRepo
	timelineService                *timeline_service.TimelineService[patient_encounter.EncounterCalendarTimeline]
	CALService                     *cal_service.CALService
}

var WaitingRoomServiceMod = submodule.Make[WaitingRoomService](func() WaitingRoomService {
	return NewWaitingRoomService()
})

func NewWaitingRoomService() WaitingRoomService {
	timelineService, err := timeline_service.TimelineServiceCalendarMod.SafeResolve()
	if err != nil {
		panic(fmt.Errorf("timeline_service.TimelineServiceCalendarMod.Get failed: %s", err.Error()))
	}

	CALService, err := cal_service.CALServiceMod.SafeResolve()
	if err != nil {
		panic(fmt.Errorf("cal_service.CALServiceMod.Get failed: %s", err.Error()))
	}

	return WaitingRoomService{
		waitingRoomRepo:                waiting_room.NewWaitingRoomRepoDefaultRepository(),
		employeeRepo:                   employeeProfileRepo.NewEmployeeProfileDefaultRepository(),
		bsnrRepo:                       bsnr_repo.NewBSNRDefaultRepository(),
		patientProfileRepo:             patient_profile.NewPatientProfileDefaultRepository(),
		waitingRoomPatientRepo:         waiting_room_patient_repo.NewWaitingRoomPatientDefaultRepository(),
		taskTypeRepo:                   task_type.NewTaskTypeRepoDefaultRepository(),
		waitingRoomPatientAuditLogRepo: waiting_room_patient_audit_log_repo.NewWaitingRoomPatientAuditLogDefaultRepository(),
		timelineService:                timelineService,
		CALService:                     CALService,
	}
}

func (app *WaitingRoomService) DeleteByPatientId(ctx *titan.Context, patientId uuid.UUID) error {
	_, err := app.waitingRoomRepo.DeleteByPatientId(ctx, patientId)
	if err != nil {
		return errors.WithMessage(err, "waitingRoomRepo.DeleteByPatientId failed")
	}
	_, err = app.waitingRoomPatientRepo.Db.DeleteByPatientId(ctx, waiting_room_patient_repo.Field_PatientId, patientId)
	if err != nil {
		return errors.WithMessage(err, "waitingRoomRepo.DeleteByPatientId failed")
	}
	_, err = app.waitingRoomPatientAuditLogRepo.Db.DeleteByPatientId(ctx, waiting_room_patient_audit_log_repo.Field_PatientId, patientId)
	if err != nil {
		return errors.WithMessage(err, "waitingRoomPatientAuditLogRepo.DeleteByPatientId failed")
	}
	return nil
}

func (app *WaitingRoomService) OnHardDeleteTimeline(ctx *titan.Context, timelineId uuid.UUID) error {
	err := app.waitingRoomPatientAuditLogRepo.DeleteByWaitingRoomId(ctx, timelineId)
	if err != nil {
		return errors.WithMessage(err, "waitingRoomPatientAuditLogRepo.DeleteByWaitingRoomId failed")
	}
	_, err = app.waitingRoomPatientRepo.DeleteById(ctx, timelineId)
	if err != nil {
		return errors.WithMessage(err, "waitingRoomPatientRepo.DeleteById failed")
	}

	return nil
}

func (app *WaitingRoomService) AssignPatient(ctx *titan.Context, previousRoomId *uuid.UUID, roomId, patientId uuid.UUID, position int64) (*waiting_room.WaitingRoomEntity, error) {
	if previousRoomId != nil && previousRoomId.String() == roomId.String() {
		return nil, app.waitingRoomRepo.ChangePosition(ctx, roomId, patientId, position)
	}
	var note string
	if previousRoomId != nil {
		waitingRoom, err := app.UnAssignPatient(ctx, *previousRoomId, patientId)
		if err != nil {
			return nil, err
		}

		if waitingRoom == nil {
			return nil, nil
		}

		currentTime := util.NowUnixMillis(ctx)
		previousWaitingRoomPatient, err := app.EditPatientAuditLog(ctx, *previousRoomId, nil, &currentTime, true, nil, &patientId)
		if err != nil {
			return nil, err
		}
		if previousWaitingRoomPatient != nil {
			note = previousWaitingRoomPatient.Note
		}
	}
	err := app.Assign(ctx, roomId, patientId, position)
	if err != nil {
		return nil, err
	}

	currentTime := util.NowUnixMillis(ctx)
	waitingRoom, err := app.waitingRoomRepo.FindById(ctx, roomId)
	if err != nil {
		return nil, err
	}
	waitingRoomPatient, err := app.waitingRoomPatientRepo.Create(ctx, waiting_room_patient_repo.WaitingRoomPatient{
		Id:               util.NewPointer(uuid.New()),
		WaitingRoom:      &roomId,
		PatientId:        &patientId,
		CreatedAt:        currentTime,
		StartWaitingTime: currentTime,
		Note:             note,
	})
	if err != nil {
		return waitingRoom, err
	}
	if waitingRoom != nil {
		_, err = app.AddPatientAuditLog(ctx, *waitingRoomPatient, waiting_room_patient_audit_log_repo.AuditLogStatusType_Add, waitingRoom.WaitingRoom.ActiveTimeMeasurement)

		if err != nil {
			return nil, err
		}

		return waitingRoom, app.CreateTimelineEntry(ctx, waiting_room_common.WaitingRoomPatient{
			Id:                 waitingRoomPatient.Id,
			WaitingRoom:        waitingRoomPatient.WaitingRoom,
			PatientId:          waitingRoomPatient.PatientId,
			TodoTypeId:         waitingRoomPatient.TodoTypeId,
			Note:               waitingRoomPatient.Note,
			IsDeleted:          waitingRoomPatient.IsDeleted,
			CreatedAt:          waitingRoomPatient.CreatedAt,
			StartTreatmentTime: waitingRoomPatient.StartTreatmentTime,
			EndTreatmentTime:   waitingRoomPatient.EndTreatmentTime,
			StartWaitingTime:   waitingRoomPatient.StartWaitingTime,
			EndWaitingTime:     waitingRoomPatient.EndWaitingTime,
			TreatingDoctor:     waitingRoom.WaitingRoom.TreatingDoctorId,
		}, waitingRoom.WaitingRoom.Name, waitingRoom.WaitingRoom.AcceptableWaitingTimeInMinutes, waitingRoom.WaitingRoom.ActiveTimeMeasurement)
	}
	return waitingRoom, nil
}

func (app *WaitingRoomService) UnAssignPatient(ctx *titan.Context, roomId, patientId uuid.UUID) (*waiting_room.WaitingRoomEntity, error) {
	waitingRoom, err := app.waitingRoomRepo.UnAssignPatient(ctx, roomId, patientId)
	if err != nil {
		return nil, err
	}

	waitingRoomPatient, err := app.waitingRoomPatientRepo.FindOne(ctx, bson.M{
		waiting_room_patient_repo.Field_WaitingRoom: roomId,
		waiting_room_patient_repo.Field_PatientId:   patientId,
		waiting_room_patient_repo.Field_IsDeleted:   false,
	})

	if err != nil {
		return nil, err
	}

	if waitingRoomPatient == nil {
		return nil, nil
	}

	currentTime := util.NowUnixMillis(ctx)
	waitingRoomPatient.IsDeleted = true

	if waitingRoomPatient.EndWaitingTime == 0 {
		waitingRoomPatient.EndWaitingTime = currentTime
	} else if waitingRoomPatient.StartTreatmentTime != 0 {
		waitingRoomPatient.EndTreatmentTime = currentTime
	}

	_, err = app.waitingRoomPatientRepo.Update(ctx, *waitingRoomPatient)

	if err != nil {
		return nil, err
	}

	filter := bson.M{
		waiting_room_patient_audit_log_repo.Field_PatientId: patientId,
	}

	patientAuditLog, err := app.waitingRoomPatientAuditLogRepo.Find(ctx, filter)

	if err != nil {
		return nil, err
	}

	for _, auditLog := range patientAuditLog {
		if auditLog.EndTreatmentTime == 0 {
			if _, err := app.EditPatientAuditLog(ctx, *auditLog.WaitingRoom, &auditLog.StartTreatmentTime, &currentTime, true, nil, auditLog.PatientId); err != nil {
				return nil, errors.WithMessage(err, "EditPatientAuditLog failed")
			}
		}
	}

	_, err = app.timelineService.UpdateByIdWithCallback(
		ctx,
		roomId,
		nil,
		func(entry *timeline_repo.TimelineEntity[patient_encounter.EncounterCalendarTimeline]) {
			entry.Payload.WaitingRoomPatient.IsDeleted = true
			entry.Payload.WaitingRoomPatient.EndWaitingTime = currentTime
		})

	if err != nil {
		return nil, err
	}

	return waitingRoom, nil
}

func (app *WaitingRoomService) Remove(ctx *titan.Context, roomId uuid.UUID) error {
	callback := func(ctx *titan.Context) (any, error) {
		foundPatientInRoom, err := app.waitingRoomPatientRepo.FindOne(ctx, bson.M{
			waiting_room_patient_repo.Field_WaitingRoom: roomId,
			waiting_room_patient_repo.Field_IsDeleted:   false,
		})
		if err != nil {
			return nil, err
		}
		if foundPatientInRoom != nil && foundPatientInRoom.Id != nil {
			return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Waiting_Room_Have_Patient, "")
		}
		err = app.waitingRoomRepo.Remove(ctx, roomId)
		if err != nil {
			return nil, err
		}
		waitingRoomPatients, err := app.waitingRoomPatientRepo.Find(ctx, bson.M{
			waiting_room_patient_repo.Field_WaitingRoom: roomId,
			waiting_room_patient_repo.Field_IsDeleted:   false,
		})
		if err != nil {
			return nil, err
		}
		entryIds := slice.Map(waitingRoomPatients, func(t waiting_room_patient_repo.WaitingRoomPatient) uuid.UUID {
			return *t.Id
		})

		err = app.timelineService.UpdateWaitingRoomInCalendarEntry(ctx, entryIds, nil, 0, false)
		if err != nil {
			return nil, err
		}
		err = app.CALService.DeleteRoomCAL(ctx, cal_service.DeleteRoomRequest{
			Id:             roomId,
			CareProviderId: ctx.UserInfo().CareProviderId.String(),
		})
		if err != nil {
			ctx.Logger().Error("Fail to remove room in CAL", "error", err)
		}
		return nil, nil
	}
	_, err := app.waitingRoomRepo.WithTransaction(ctx, callback)
	if err != nil {
		return err
	}
	return nil
}

func (app *WaitingRoomService) CreateWaitingRoom(ctx *titan.Context, request waiting_room_common.WaitingRoomModel) (*waiting_room_common.WaitingRoomModel, error) {
	callback := func(ctx *titan.Context) (any, error) {
		id := util.NewUUID()
		request.Id = id
		res, err := app.waitingRoomRepo.Create(ctx, &waiting_room.WaitingRoomEntity{
			Id:          id,
			WaitingRoom: request,
			IsDeleted:   false,
		})
		if err != nil {
			return nil, err
		}
		if res == nil {
			return nil, nil
		}
		err = app.CALService.CreateRoomCAL(ctx, cal_service.CreateOrUpdateRoomRequest{
			Id:             util.GetPointerValue(res.Id),
			CareProviderId: ctx.UserInfo().CareProviderId.String(),
			Name:           res.WaitingRoom.Name,
			StaffId:        util.GetPointerValue(res.WaitingRoom.TreatingDoctorId),
			PracticeId:     util.GetPointerValue(res.WaitingRoom.BsnrId),
		})
		if err != nil {
			ctx.Logger().Error("Fail to create room in CAL", "error", err)
			return nil, err
		}
		return res.WaitingRoom, nil
	}

	res, err := app.waitingRoomRepo.WithTransaction(ctx, callback)
	if err != nil {
		return nil, err
	}

	return util.NewPointer(res.(waiting_room_common.WaitingRoomModel)), nil
}

func (app *WaitingRoomService) EditWaitingRoom(ctx *titan.Context, request waiting_room_common.WaitingRoomModel) (*waiting_room_common.WaitingRoomModel, error) {
	callback := func(ctx *titan.Context) (any, error) {
		res, err := app.waitingRoomRepo.Update(ctx, &waiting_room.WaitingRoomEntity{
			Id:          request.Id,
			WaitingRoom: request,
		})
		if err != nil {
			return nil, err
		}
		if res == nil {
			return nil, nil
		}
		waitingPatients, err := app.waitingRoomPatientRepo.Find(ctx, bson.M{waiting_room_patient_repo.Field_WaitingRoom: *request.Id, waiting_room_patient_repo.Field_IsDeleted: false})
		if err != nil {
			return nil, err
		}
		entryIds := slice.Map(waitingPatients, func(t waiting_room_patient_repo.WaitingRoomPatient) uuid.UUID {
			return *t.Id
		})

		for _, waitingPatient := range waitingPatients {
			_, err = app.EditPatientAuditLog(ctx, *request.Id, nil, nil, false, &request.ActiveTimeMeasurement, waitingPatient.PatientId)

			if err != nil {
				return nil, err
			}
		}

		err = app.timelineService.UpdateWaitingRoomInCalendarEntry(ctx, entryIds, &request.Name, request.AcceptableWaitingTimeInMinutes, request.ActiveTimeMeasurement)
		if err != nil {
			return nil, err
		}
		err = app.CALService.UpdateRoomCAL(ctx, cal_service.CreateOrUpdateRoomRequest{
			Id:             util.GetPointerValue(request.Id),
			CareProviderId: ctx.UserInfo().CareProviderId.String(),
			Name:           request.Name,
			StaffId:        util.GetPointerValue(request.TreatingDoctorId),
			PracticeId:     util.GetPointerValue(request.BsnrId),
		})
		if err != nil {
			ctx.Logger().Error("Fail to update room in CAL", "error", err)
		}
		return res.WaitingRoom, nil
	}

	res, err := app.waitingRoomRepo.WithTransaction(ctx, callback)
	if err != nil {
		return nil, err
	}

	return util.NewPointer(res.(waiting_room_common.WaitingRoomModel)), nil
}

// GetWaitingRooms
func (app *WaitingRoomService) GetWaitingRooms(ctx *titan.Context, request waiting_room_api.GetWaitingRoomRequest) ([]waiting_room_common.WaitingRoomView, error) {
	waitingRooms, err := app.waitingRoomRepo.GetWaitingRooms(ctx, waiting_room.GetWaitingRooms{
		RoomId: request.RoomId,
		BsnrId: request.BsnrId,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get waiting rooms")
	}
	var response []waiting_room_common.WaitingRoomView
	if len(waitingRooms) == 0 {
		return response, nil
	}
	var doctorIds []uuid.UUID
	for _, t := range waitingRooms {
		if t.WaitingRoom.TreatingDoctorId != nil {
			doctorIds = append(doctorIds, *t.WaitingRoom.TreatingDoctorId)
		}
	}
	var employees []employeeProfileRepo.EmployeeProfile
	if len(doctorIds) > 0 {
		employees, err = app.employeeRepo.GetProfileByIds(ctx, doctorIds)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get employees")
		}
	}

	bsnrs, err := app.bsnrRepo.GetBsnrByIds(ctx, slice.Map(waitingRooms, func(t waiting_room.WaitingRoomEntity) uuid.UUID {
		return *t.WaitingRoom.BsnrId
	}))
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get bsnrs")
	}

	waitingRoomPatients, err := app.waitingRoomPatientRepo.Find(ctx, bson.M{})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to find waiting room patients")
	}
	patientIds := slice.Map(waitingRoomPatients, func(p waiting_room_patient_repo.WaitingRoomPatient) uuid.UUID {
		return *p.PatientId
	})
	var patientProfiles []*patient_profile.PatientProfile
	if len(patientIds) > 0 {
		temp, err := app.patientProfileRepo.GetByIds(ctx, patientIds)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		patientProfiles = temp
	}
	for _, v := range waitingRooms {
		employee := slice.FindOne(employees, func(t employeeProfileRepo.EmployeeProfile) bool {
			if v.WaitingRoom.TreatingDoctorId != nil {
				return *t.Id == *v.WaitingRoom.TreatingDoctorId
			}
			return false
		})
		treatingDoctor := &profile.EmployeeProfileResponse{}
		if employee != nil {
			if err = copier.Copy(treatingDoctor, employee); err != nil {
				return nil, errors.WithMessage(err, "copier.Copy")
			}
			treatingDoctor.EmployeeProfileId = employee.Id
		}

		bsnr := slice.FindOne(bsnrs, func(t bsnr_repo.BSNR) bool {
			return v.WaitingRoom.BsnrId.String() == t.Id.String()
		})
		if bsnr == nil {
			return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_BSNR_NOT_FOUND, "")
		}
		bsnrCommon := &bsnr_common.BSNR{}
		if err = copier.Copy(bsnrCommon, bsnr); err != nil {
			return nil, errors.WithMessage(err, "copier.Copy")
		}
		var wtPatients []*waiting_room_common.WaitingRoomPatientView
		for _, id := range v.WaitingRoom.PatientIds {
			waitingRoomPatient := slice.FindOne(waitingRoomPatients, func(t waiting_room_patient_repo.WaitingRoomPatient) bool {
				return t.PatientId != nil && t.PatientId.String() == id.String() && !t.IsDeleted
			})
			if waitingRoomPatient == nil {
				continue
			}
			patientProfile := slice.FindOne(patientProfiles, func(t *patient_profile.PatientProfile) bool {
				return *t.Id == id
			})
			wtPatients = append(wtPatients, &waiting_room_common.WaitingRoomPatientView{
				Id:                 waitingRoomPatient.Id,
				WaitingRoom:        waitingRoomPatient.WaitingRoom,
				PatientProfile:     ToPatientProfileResponse(util.GetPointerValue(patientProfile)),
				CreatedAt:          waitingRoomPatient.CreatedAt,
				Note:               waitingRoomPatient.Note,
				StartTreatmentTime: waitingRoomPatient.StartTreatmentTime,
				EndTreatmentTime:   waitingRoomPatient.EndTreatmentTime,
				StartWaitingTime:   waitingRoomPatient.StartWaitingTime,
				EndWaitingTime:     waitingRoomPatient.EndWaitingTime,
			})
		}
		response = append(response, waiting_room_common.WaitingRoomView{
			Id:                             v.Id,
			RoomColor:                      v.WaitingRoom.RoomColor,
			Name:                           v.WaitingRoom.Name,
			AcceptableWaitingTimeInMinutes: v.WaitingRoom.AcceptableWaitingTimeInMinutes,
			ActiveTimeMeasurement:          v.WaitingRoom.ActiveTimeMeasurement,
			PatientIds:                     v.WaitingRoom.PatientIds,
			BSNR:                           bsnrCommon,
			TreatingDoctor:                 treatingDoctor,
			WaitingRoomPatients:            wtPatients,
		})
	}

	return response, nil
}

func (app *WaitingRoomService) ToggleMeasurement(ctx *titan.Context, roomId uuid.UUID) error {
	waitingRooms, err := app.waitingRoomRepo.GetWaitingRooms(ctx, waiting_room.GetWaitingRooms{
		RoomId: &roomId,
	})

	if err != nil {
		return fmt.Errorf("waitingRoomRepo.GetWaitingRooms failed: %s", err.Error())
	}

	patientIds := waitingRooms[0].WaitingRoom.PatientIds
	activeTimeMeasurement := &waitingRooms[0].WaitingRoom.ActiveTimeMeasurement
	pointerValue := util.NewPointer(!util.GetPointerValue(activeTimeMeasurement))

	for _, patientId := range patientIds {
		_, err := app.EditPatientAuditLog(ctx, roomId, nil, nil, false, pointerValue, &patientId)

		if err != nil {
			return fmt.Errorf("EditPatientAuditLog failed: %s", err.Error())
		}

		_, err = app.timelineService.UpdateByIdWithCallback(
			ctx,
			roomId,
			&patientId,
			func(entry *timeline_repo.TimelineEntity[patient_encounter.EncounterCalendarTimeline]) {
				entry.Payload.ActiveTimeMeasurement = pointerValue
			})

		if err != nil {
			return fmt.Errorf("timelineService.UpdateByIdWithCallback failed: %s", err.Error())
		}
	}
	return app.waitingRoomRepo.ToggleMeasurementById(ctx, roomId)
}

func ToPatientProfileResponse(patient *patient_profile.PatientProfile) *profile.PatientProfile {
	if patient == nil {
		return nil
	}
	profile := &profile.PatientProfile{
		Id:                      patient.Id,
		FirstName:               patient.FirstName,
		LastName:                patient.LastName,
		DateOfBirth:             &patient.DateOfBirth,
		PatientMedicalData:      patient.PatientMedicalData,
		PatientInfo:             patient.PatientInfo,
		EmploymentInfoUpdatedAt: patient.EmploymentInfoUpdatedAt,
		MedicalDataUpdatedAt:    patient.MedicalDataUpdatedAt,
	}
	for _, hpmItem := range patient.ListHpmInformation {
		profile.HpmInformation = append(profile.HpmInformation, ToHpmInformationResponse(hpmItem))
	}
	return profile
}
func ToHpmInformationResponse(hpmInformation *patient_profile.HpmInformation) *profile.HpmInformation {
	if hpmInformation == nil {
		return nil
	}
	return &profile.HpmInformation{
		CheckedDate: hpmInformation.CheckedDate,
		ContractId:  hpmInformation.ContractId,
		Status:      profile.HpmInformationStatus(hpmInformation.Status),
	}
}

func (app *WaitingRoomService) Assign(ctx *titan.Context, roomId, patientId uuid.UUID, position int64) error {
	return app.waitingRoomRepo.AssignPatient(ctx, roomId, patientId, position)
}

func (app *WaitingRoomService) Unassign(ctx *titan.Context, roomId, patientId uuid.UUID) error {
	_, err := app.waitingRoomRepo.UnAssignPatient(ctx, roomId, patientId)
	return err
}

func (app *WaitingRoomService) AddPatientAuditLog(ctx *titan.Context, req waiting_room_patient_repo.WaitingRoomPatient, status waiting_room_patient_audit_log_repo.AuditLogStatusType, activeTimeMeasurement bool) (*waiting_room_patient_repo.WaitingRoomPatient, error) {
	patientProfile, err := app.patientProfileRepo.FindById(ctx, *req.PatientId)

	if err != nil {
		return nil, err
	}

	var patientInfo *patient_profile_common.PatientInfo

	if patientProfile != nil && patientProfile.PatientInfo != nil {
		patientInfo = patientProfile.PatientInfo
	}

	// Add audit log when patient is added to waiting room
	auditLog := waiting_room_patient_audit_log_repo.WaitingRoomPatientAuditLog{
		Id:                    util.NewPointer(uuid.New()),
		WaitingRoomId:         req.Id,
		WaitingRoom:           req.WaitingRoom,
		PatientId:             req.PatientId,
		Note:                  req.Note,
		IsDeleted:             req.IsDeleted,
		CreatedAt:             req.CreatedAt,
		StartTreatmentTime:    req.StartTreatmentTime,
		EndTreatmentTime:      req.EndTreatmentTime,
		StartWaitingTime:      req.StartWaitingTime,
		EndWaitingTime:        req.EndWaitingTime,
		ActiveTimeMeasurement: activeTimeMeasurement,
		PatientInfo:           patientInfo,
		Status:                status,
	}
	_, err = app.waitingRoomPatientAuditLogRepo.Create(ctx, auditLog)

	if err != nil {
		return nil, err
	}

	return &req, nil
}

func (app *WaitingRoomService) EditPatientAuditLog(ctx *titan.Context, id uuid.UUID, startTreatmentTime, endTreatmentTime *int64, isDeleted bool, activeTimeMeasurement *bool, patientId *uuid.UUID) (*waiting_room_patient_audit_log_repo.WaitingRoomPatientAuditLog, error) {
	update := bson.M{}

	if startTreatmentTime != nil && endTreatmentTime != nil {
		if util.GetPointerValue(endTreatmentTime) == 0 {
			update[waiting_room_patient_audit_log_repo.Field_StartTreatmentTime] = startTreatmentTime
			update[waiting_room_patient_audit_log_repo.Field_EndWaitingTime] = startTreatmentTime
		} else {
			update[waiting_room_patient_audit_log_repo.Field_EndTreatmentTime] = endTreatmentTime
			if util.GetPointerValue(startTreatmentTime) == 0 {
				update[waiting_room_patient_audit_log_repo.Field_StartTreatmentTime] = endTreatmentTime
				update[waiting_room_patient_audit_log_repo.Field_EndWaitingTime] = endTreatmentTime
			}
		}
	}

	if activeTimeMeasurement != nil {
		update[waiting_room_patient_audit_log_repo.Field_ActiveTimeMeasurement] = activeTimeMeasurement
	}

	update[waiting_room_patient_audit_log_repo.Field_IsDeleted] = isDeleted

	filter := bson.M{
		waiting_room_patient_audit_log_repo.Field_WaitingRoom: id,
		waiting_room_patient_audit_log_repo.Field_IsDeleted:   false,
	}

	if patientId != nil {
		filter[waiting_room_patient_audit_log_repo.Field_PatientId] = patientId
	}

	entity, err := app.waitingRoomPatientAuditLogRepo.FindOneAndUpdate(ctx, filter, bson.M{
		"$set": update,
	}, options.FindOneAndUpdate().SetReturnDocument(options.After))

	if err != nil {
		return nil, err
	}

	return entity, nil
}

func (app *WaitingRoomService) MoveToAuditLog(ctx *titan.Context, roomId, patientId uuid.UUID, status waiting_room_patient_audit_log_repo.AuditLogStatusType) (*waiting_room_patient_repo.WaitingRoomPatient, error) {
	previousWaitingRoomPatient, err := app.waitingRoomPatientRepo.FindOne(ctx, bson.M{
		waiting_room_patient_repo.Field_WaitingRoom: roomId,
		waiting_room_patient_repo.Field_PatientId:   patientId,
		waiting_room_patient_repo.Field_IsDeleted:   false,
	})
	if err != nil {
		return nil, err
	}

	if previousWaitingRoomPatient == nil {
		return nil, nil
	}

	previousWaitingRoomPatient.IsDeleted = true
	currentTime := util.NowUnixMillis(ctx)
	if previousWaitingRoomPatient.EndTreatmentTime == 0 {
		previousWaitingRoomPatient.EndTreatmentTime = currentTime
	}
	if previousWaitingRoomPatient.EndWaitingTime == 0 {
		previousWaitingRoomPatient.EndWaitingTime = currentTime
	}

	patientProfile, err := app.patientProfileRepo.FindById(ctx, *previousWaitingRoomPatient.PatientId)

	if err != nil {
		return nil, err
	}

	var patientInfo *patient_profile_common.PatientInfo

	if patientProfile != nil && patientProfile.PatientInfo != nil {
		patientInfo = patientProfile.PatientInfo
	}

	// Move record to waiting_room_patient_audit_log from waiting_room_patient
	auditLog := waiting_room_patient_audit_log_repo.WaitingRoomPatientAuditLog{
		Id:                 util.NewPointer(uuid.New()),
		WaitingRoomId:      previousWaitingRoomPatient.Id,
		WaitingRoom:        previousWaitingRoomPatient.WaitingRoom,
		PatientId:          previousWaitingRoomPatient.PatientId,
		Note:               previousWaitingRoomPatient.Note,
		IsDeleted:          true,
		CreatedAt:          previousWaitingRoomPatient.CreatedAt,
		StartTreatmentTime: previousWaitingRoomPatient.StartTreatmentTime,
		EndTreatmentTime:   previousWaitingRoomPatient.EndTreatmentTime,
		StartWaitingTime:   previousWaitingRoomPatient.StartWaitingTime,
		EndWaitingTime:     previousWaitingRoomPatient.EndWaitingTime,
		PatientInfo:        patientInfo,
		Status:             status,
	}
	_, err = app.waitingRoomPatientAuditLogRepo.Create(ctx, auditLog)
	if err != nil {
		return nil, err
	}
	_, err = app.waitingRoomPatientRepo.DeleteById(ctx, *previousWaitingRoomPatient.Id)
	if err != nil {
		return nil, err
	}
	if previousWaitingRoomPatient.Id != nil {
		_, err := app.timelineService.UpdateByIdWithCallback(
			ctx,
			*previousWaitingRoomPatient.WaitingRoom,
			nil,
			func(entry *timeline_repo.TimelineEntity[patient_encounter.EncounterCalendarTimeline]) {
				entry.Payload.WaitingRoomPatient.IsDeleted = true
				entry.Payload.WaitingRoomPatient.EndWaitingTime = currentTime
			})
		if err != nil {
			return nil, err
		}
	}
	return previousWaitingRoomPatient, nil
}

func (app *WaitingRoomService) CreateWaitingRoomForPatient(ctx *titan.Context, req waiting_room_patient_repo.WaitingRoomPatient) (*waiting_room.WaitingRoomEntity, error) {
	exist, err := app.CheckExistPatientInAWaitingRoom(ctx, *req.WaitingRoom, *req.PatientId)
	if err != nil {
		return nil, err
	}
	if exist {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Exist_Patient_In_A_Waiting_Room, "")
	}
	req.Id = util.NewPointer(uuid.New())
	currentTime := util.NowUnixMillis(ctx)
	req.CreatedAt = currentTime
	req.StartWaitingTime = currentTime

	err = app.Assign(ctx, *req.WaitingRoom, *req.PatientId, 0)
	if err != nil {
		return nil, err
	}
	_, err = app.waitingRoomPatientRepo.Create(ctx, req)
	if err != nil {
		return nil, err
	}

	waitingRoom, err := app.waitingRoomRepo.FindById(ctx, *req.WaitingRoom)
	if err != nil {
		return nil, err
	}

	_, err = app.AddPatientAuditLog(ctx, req, waiting_room_patient_audit_log_repo.AuditLogStatusType_Add, waitingRoom.WaitingRoom.ActiveTimeMeasurement)
	if err != nil {
		return nil, err
	}
	var treatingDoctorId uuid.UUID
	if waitingRoom != nil && waitingRoom.WaitingRoom.TreatingDoctorId != nil {
		treatingDoctorId = *waitingRoom.WaitingRoom.TreatingDoctorId
	}
	waitingRoomName := ""
	waitingRoomAcceptableWaitingTimeInMinutes := int64(0)
	waitingRoomActiveTimeMeasurement := true
	if waitingRoom != nil {
		waitingRoomName = waitingRoom.WaitingRoom.Name
		waitingRoomAcceptableWaitingTimeInMinutes = waitingRoom.WaitingRoom.AcceptableWaitingTimeInMinutes
		waitingRoomActiveTimeMeasurement = waitingRoom.WaitingRoom.ActiveTimeMeasurement
	}
	err = app.CreateTimelineEntry(ctx, waiting_room_common.WaitingRoomPatient{
		Id:                 req.Id,
		WaitingRoom:        req.WaitingRoom,
		PatientId:          req.PatientId,
		Note:               req.Note,
		IsDeleted:          req.IsDeleted,
		CreatedAt:          req.CreatedAt,
		StartTreatmentTime: req.StartTreatmentTime,
		EndTreatmentTime:   req.EndTreatmentTime,
		StartWaitingTime:   req.StartWaitingTime,
		EndWaitingTime:     req.EndWaitingTime,
		TreatingDoctor:     &treatingDoctorId,
	}, waitingRoomName, waitingRoomAcceptableWaitingTimeInMinutes, waitingRoomActiveTimeMeasurement)
	if err != nil {
		return nil, errors.WithMessage(err, "CreateTimelineEntry failed")
	}

	return waitingRoom, nil
}

func (app *WaitingRoomService) CreateTimelineEntry(ctx *titan.Context, data waiting_room_common.WaitingRoomPatient, waitingRoomName string, acceptableWaitingTimeInMinutes int64, activeTimeMeasurement bool) error {
	_, err := app.timelineService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterCalendarTimeline]{
		Id: data.Id,
		Payload: patient_encounter.EncounterCalendarTimeline{
			WaitingRoomPatient:             &data,
			WaitingRoomName:                &waitingRoomName,
			AcceptableWaitingTimeInMinutes: &acceptableWaitingTimeInMinutes,
			ActiveTimeMeasurement:          &activeTimeMeasurement,
		},
		TreatmentDoctorId: util.GetPointerValue(data.TreatingDoctor),
		PatientId:         *data.PatientId,
	})
	return err
}

func (app *WaitingRoomService) CheckExistPatient(ctx *titan.Context, patientId uuid.UUID) (*uuid.UUID, error) {
	waitingRoom, err := app.waitingRoomRepo.FindOne(ctx, bson.M{
		"waitingroom.patientids": patientId,
	})
	if err != nil {
		return nil, err
	}
	if waitingRoom == nil {
		return nil, nil
	}
	if waitingRoom.Id != nil {
		return waitingRoom.Id, nil
	}
	return nil, nil
}

func (app *WaitingRoomService) CheckExistPatientInAWaitingRoom(ctx *titan.Context, roomId, patientId uuid.UUID) (bool, error) {
	waitingRoom, err := app.waitingRoomRepo.FindOne(ctx, bson.M{
		waiting_room_patient_repo.Field_WaitingRoom: roomId,
		waiting_room_patient_repo.Field_PatientId:   patientId,
		waiting_room_patient_repo.Field_IsDeleted:   false,
	})
	if err != nil {
		return false, err
	}
	return waitingRoom != nil, nil
}

func (app *WaitingRoomService) StartTreatmentTime(ctx *titan.Context, id, patientId uuid.UUID) (*waiting_room.WaitingRoomEntity, *uuid.UUID, error) {
	currentTime := util.NowUnixMillis(ctx)
	err := app.waitingRoomPatientRepo.Db.FindOneAndUpdate(ctx, bson.M{
		waiting_room_patient_repo.Field_PatientId:      patientId,
		waiting_room_patient_repo.Field_WaitingRoom:    id,
		waiting_room_patient_repo.Field_EndWaitingTime: 0,
		waiting_room_patient_repo.Field_IsDeleted:      false,
	}, bson.M{
		"$set": bson.M{
			waiting_room_patient_repo.Field_StartTreatmentTime: currentTime,
			waiting_room_patient_repo.Field_EndWaitingTime:     currentTime,
		},
	}, options.FindOneAndUpdate().SetReturnDocument(options.After))
	if err != nil {
		return nil, nil, errors.WithMessage(err, "waitingRoomPatientRepo.FindOneAndUpdate failed")
	}

	var endTreatmentTime int64

	entity, err := app.EditPatientAuditLog(ctx, id, &currentTime, &endTreatmentTime, false, nil, &patientId)
	if err != nil {
		return nil, nil, errors.WithMessage(err, "EditPatientAuditLog failed")
	}

	if entity == nil {
		return nil, nil, nil
	}

	entry, err := app.timelineService.UpdateByIdWithCallback(
		ctx,
		id,
		nil,
		func(entry *timeline_repo.TimelineEntity[patient_encounter.EncounterCalendarTimeline]) {
			entry.Payload.WaitingRoomPatient.StartTreatmentTime = currentTime
			entry.Payload.WaitingRoomPatient.EndWaitingTime = currentTime
		})
	if err != nil {
		return nil, nil, err
	}
	var waitingRoom *waiting_room.WaitingRoomEntity
	if entry.Payload.WaitingRoomPatient.WaitingRoom != nil {
		waitingRoom, err = app.waitingRoomRepo.FindById(ctx, *entry.Payload.WaitingRoomPatient.WaitingRoom)
		if err != nil {
			return nil, nil, err
		}
	}
	return waitingRoom, &entry.PatientId, err
}

func (app *WaitingRoomService) StopTreatmentTime(ctx *titan.Context, id, patientId uuid.UUID) (*waiting_room.WaitingRoomEntity, *uuid.UUID, error) {
	currentTime := util.NowUnixMillis(ctx)
	err := app.waitingRoomPatientRepo.Db.FindOneAndUpdate(ctx, bson.M{
		waiting_room_patient_repo.Field_PatientId:        patientId,
		waiting_room_patient_repo.Field_WaitingRoom:      id,
		waiting_room_patient_repo.Field_EndTreatmentTime: 0,
		waiting_room_patient_repo.Field_IsDeleted:        false,
	}, bson.M{
		"$set": bson.M{
			waiting_room_patient_repo.Field_EndTreatmentTime: currentTime,
		},
	}, options.FindOneAndUpdate().SetReturnDocument(options.After))
	if err != nil {
		return nil, nil, err
	}

	entity, err := app.EditPatientAuditLog(ctx, id, nil, &currentTime, false, nil, &patientId)
	if err != nil {
		return nil, nil, err
	}

	if entity == nil {
		return nil, nil, nil
	}

	entry, err := app.timelineService.UpdateByIdWithCallback(
		ctx,
		id,
		nil,
		func(entry *timeline_repo.TimelineEntity[patient_encounter.EncounterCalendarTimeline]) {
			entry.Payload.WaitingRoomPatient.EndTreatmentTime = currentTime
		})
	if err != nil {
		return nil, nil, err
	}
	var roomId uuid.UUID
	var waitingRoom *waiting_room.WaitingRoomEntity
	if entry.Payload.WaitingRoomPatient.WaitingRoom != nil {
		waitingRoom, err = app.waitingRoomRepo.FindById(ctx, *entry.Payload.WaitingRoomPatient.WaitingRoom)
		if err != nil {
			return nil, nil, err
		}
		roomId = *waitingRoom.Id
	}

	_, err = app.UnAssignPatient(ctx, roomId, entry.PatientId)
	if err != nil {
		return nil, nil, fmt.Errorf("UnAssignPatient failed: %s", err.Error())
	}
	return nil, nil, err
}

func (app *WaitingRoomService) CheckExistWaitingRoomName(ctx *titan.Context, name string, roomId *uuid.UUID) (*waiting_room_api.CheckExistWaitingRoomNameResponse, error) {
	waitingRoom, err := app.waitingRoomRepo.FindOne(ctx, bson.M{
		"waitingroom.name": name,
		"isdeleted":        false,
	})
	if err != nil {
		return nil, err
	}
	isExist := waitingRoom != nil && (roomId == nil || waitingRoom.Id.String() != roomId.String())
	return &waiting_room_api.CheckExistWaitingRoomNameResponse{
		IsExist: isExist,
	}, nil
}

func (app *WaitingRoomService) GetWaitingRoomById(ctx *titan.Context, roomId uuid.UUID) (*waiting_room.WaitingRoomEntity, error) {
	return app.waitingRoomRepo.FindById(ctx, roomId)
}

func (app *WaitingRoomService) UpdateNote(ctx *titan.Context, roomId, patientId uuid.UUID, note string) error {
	_, err := app.waitingRoomPatientRepo.FindOneAndUpdate(ctx, bson.M{
		waiting_room_patient_repo.Field_PatientId:   patientId,
		waiting_room_patient_repo.Field_WaitingRoom: roomId,
		waiting_room_patient_repo.Field_IsDeleted:   false,
	}, bson.M{
		"$set": bson.M{
			waiting_room_patient_repo.Field_Note: note,
		},
	}, options.FindOneAndUpdate().SetReturnDocument(options.After))
	if err != nil {
		return err
	}
	_, err = app.timelineService.UpdateByIdWithCallback(
		ctx,
		roomId,
		nil,
		func(entry *timeline_repo.TimelineEntity[patient_encounter.EncounterCalendarTimeline]) {
			entry.Payload.WaitingRoomPatient.Note = note
		})
	if err != nil {
		return err
	}
	return nil
}
