package patient_combination_service

import (
	patient_combination_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_combination"
	"git.tutum.dev/medi/tutum/ares/service/patient_combination/combinator"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
)

type PatientCombinationService struct {
	patientCombinator *combinator.Combinator
}

var PatientCombinationServiceMod = submodule.Make[*PatientCombinationService](func(patientCombinator *combinator.Combinator) *PatientCombinationService {
	return &PatientCombinationService{
		patientCombinator: patientCombinator,
	}
}, combinator.CombinatorMod)

func (s *PatientCombinationService) CombinePatients(ctx *titan.Context, req *patient_combination_api.CombinePatientsRequest) error {
	return s.patientCombinator.CombinePatients(ctx, &combinator.CombineRequest{
		TargetPatientId:      req.TargetPatientId,
		DuplicatedPatientIds: req.DuplicatedPatientIds,
	})
}
