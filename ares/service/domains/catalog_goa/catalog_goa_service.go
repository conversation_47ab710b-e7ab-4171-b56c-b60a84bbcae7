package catalog_goa

import (
	catalog_goa_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_goa"
	"git.tutum.dev/medi/tutum/ares/pkg/hook"
	"git.tutum.dev/medi/tutum/ares/pkg/masterdata"
	masterdata_model "git.tutum.dev/medi/tutum/ares/pkg/masterdata/model"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_goa_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_utils_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	goa_master_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/goa"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/pkg/errors"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
)

const (
	ActionCreate ActionType = "create"
	ActionUpdate ActionType = "update"
)

type (
	ActionType        string
	CatalogGoaService struct {
		goaMasterRepo  *goa_master_repo.GoaRepo
		goaChapterRepo *goa_master_repo.GoaChapterRepo
		*hook.CUDHook[*catalog_goa_common.GoaCatalog, *catalog_goa_common.GoaCatalog, *catalog_goa_common.GoaCatalog]
	}
	ValidateBeforeUpdateGoaCatalogRequest struct {
		GoaNumber string
		GoaId     string
	}
)

var CatalogGoaServiceMod = submodule.Make[*CatalogGoaService](func(goaMasterRepo *goa_master_repo.GoaRepo, goaChapterRepo *goa_master_repo.GoaChapterRepo) *CatalogGoaService {
	return &CatalogGoaService{
		goaMasterRepo:  goaMasterRepo,
		goaChapterRepo: goaChapterRepo,
		CUDHook:        hook.NewCUDInMemoryHook[*catalog_goa_common.GoaCatalog, *catalog_goa_common.GoaCatalog, *catalog_goa_common.GoaCatalog](),
	}
}, goa_master_repo.GoaMasterRepoMod, goa_master_repo.GoaChapterRepoMod)

const RATE = 5.82873

func (srv *CatalogGoaService) GetGoaCatalogByGoaNumber(ctx *titan.Context, request *catalog_goa_api.GetGoaCatalogByNumberRequest) (*catalog_goa_api.GoaCatalogItem, error) {
	if request == nil || request.GoaNumber == "" {
		return nil, nil
	}

	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	payload := goa_master_repo.GetByGoaNumberRequest{
		YearQuarter: masterdata_model.YearQuarter{
			Year:    yearQuarter.Year,
			Quarter: yearQuarter.Quarter,
		},
		GoaNumber: request.GoaNumber,
	}
	res, err := srv.goaMasterRepo.GetByGoaNumber(ctx, &payload)
	if err != nil {
		return nil, err
	}

	if res == nil {
		return nil, nil
	}
	goaCatalog := goa_master_repo.ToGoaCatalog(res)

	return &catalog_goa_api.GoaCatalogItem{
		Goa: goaCatalog,
	}, nil
}

func (srv *CatalogGoaService) GetGoaCatalogs(ctx *titan.Context, request *catalog_goa_api.GetGoaCatalogsRequest) (*catalog_goa_api.GetGoaCatalogsResponse, error) {
	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	res, err := srv.goaMasterRepo.GetPagination(ctx, &goa_master_repo.GetPaginationRequest{
		YearQuarter: masterdata_model.YearQuarter{
			Year:    yearQuarter.Year,
			Quarter: yearQuarter.Quarter,
		},
		Pagination: masterdata_model.Pagination{
			Limit:  request.Pagination.PageSize,
			Page:   request.Pagination.Page,
			SortBy: request.Pagination.SortBy,
			Order:  request.Pagination.Order,
		},
		OnlySelfCreated: util.NewPointer(request.IsOnlySelfCreated),
		Query:           &request.Value,
	})
	if err != nil {
		return nil, err
	}

	goaCatalogs := slice.Map(res.Data, func(item masterdata_model.Goa) *catalog_goa_common.GoaCatalog {
		return goa_master_repo.ToGoaCatalog(&item)
	})

	if request.ExcludeCode != nil && len(*request.ExcludeCode) > 0 {
		goaCatalogs = slice.Filter(goaCatalogs, func(item *catalog_goa_common.GoaCatalog) bool {
			return !slice.Contains(*request.ExcludeCode, item.GoaNumber)
		})
	}

	return &catalog_goa_api.GetGoaCatalogsResponse{
		Items: goaCatalogs,
		Total: res.Meta.Total,
	}, nil
}

func (srv *CatalogGoaService) SearchGoaCatalogs(ctx *titan.Context, request *catalog_goa_api.SearchGoaRequest) (*catalog_goa_api.SearchGoaResponse, error) {
	// For GOA always search in current year quarter
	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	res, err := srv.goaMasterRepo.Search(ctx, &goa_master_repo.SearchRequest{
		YearQuarter: masterdata_model.YearQuarter{
			Year:    yearQuarter.Year,
			Quarter: yearQuarter.Quarter,
		},
		Query: request.Value,
	})
	if err != nil {
		return nil, err
	}

	listGoaItem := []*catalog_goa_common.GoaItem{}
	for _, item := range res {
		goaItem := goa_master_repo.ToGoaItem(&item)
		if request.SelectedDate != nil && item.Validity.ToDate != nil {
			documentDate := util.ConvertMillisecondsToTime(*request.SelectedDate)
			validUntil := util.ConvertMillisecondsToTime(*item.Validity.ToDate)
			if validUntil.Before(documentDate) {
				continue
			}
		}
		listGoaItem = append(listGoaItem, goaItem)
	}

	return &catalog_goa_api.SearchGoaResponse{
		Items: listGoaItem,
	}, nil
}

func (srv *CatalogGoaService) GetGoaChapters(ctx *titan.Context) (*catalog_goa_api.GetChaptersResponse, error) {
	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	res, err := srv.goaChapterRepo.GetGoaChapters(ctx, &goa_master_repo.GetGoaChaptersRequest{
		YearQuarter: masterdata_model.YearQuarter{
			Year:    yearQuarter.Year,
			Quarter: yearQuarter.Quarter,
		},
	})
	if err != nil {
		return nil, err
	}

	if res == nil || len(res.Data) == 0 {
		return nil, nil
	}

	res.Data = append(res.Data, catalog_goa_common.Chapter{
		ChapterNumber: "",
		ChapterName:   "Analogziffern",
	})
	items := slice.Map(res.Data, func(item catalog_goa_common.Chapter) *catalog_goa_common.Chapter {
		return &item
	})
	return &catalog_goa_api.GetChaptersResponse{
		Items: items,
	}, nil
}

func (*CatalogGoaService) GetMapDeletedByGoaNumbers(goaNumbers []string, goaCatalogs []*catalog_goa_common.GoaCatalog) (map[string]bool, error) {
	mapDeletedByGoaNumbers := make(map[string]bool, len(goaNumbers))
	for _, goaNumber := range goaNumbers {
		mapDeletedByGoaNumbers[goaNumber] = true
	}

	for _, g := range goaCatalogs {
		mapDeletedByGoaNumbers[g.GoaNumber] = false
	}
	return mapDeletedByGoaNumbers, nil
}

func (*CatalogGoaService) GetMapExcludedCodeByGoaCatalogs(goaCatalogs []*catalog_goa_common.GoaCatalog) (map[string][]string, error) {
	if len(goaCatalogs) == 0 {
		return nil, nil
	}
	// filter by goa numbers
	mapExcludedCodeByGoaNumber := make(map[string][]string)
	for _, catalog := range goaCatalogs {
		mapExcludedCodeByGoaNumber[catalog.GoaNumber] = catalog.ExcludedCode
	}
	return mapExcludedCodeByGoaNumber, nil
}

// this function return a map of goa number and its source type based on the given goa numbers
func (*CatalogGoaService) GetCatalogSourceByGoaNumbers(goaCatalogs []*catalog_goa_common.GoaCatalog) map[string]catalog_utils_common.SourceType {
	// get all goa catalogs
	if len(goaCatalogs) == 0 {
		return nil
	}
	// filter by goa numbers
	mapCatalogSources := make(map[string]catalog_utils_common.SourceType)
	for _, catalog := range goaCatalogs {
		mapCatalogSources[catalog.GoaNumber] = catalog.Source
	}
	return mapCatalogSources
}

func (srv *CatalogGoaService) GetCatalogsByListGoaNumbers(ctx *titan.Context, goaNumbers []string) ([]*catalog_goa_common.GoaCatalog, error) {
	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	payload := goa_master_repo.GetListByGoaNumbersRequest{
		YearQuarter: masterdata_model.YearQuarter{
			Year:    yearQuarter.Year,
			Quarter: yearQuarter.Quarter,
		},
		GoaNumbers: goaNumbers,
	}

	res, err := srv.goaMasterRepo.GetListByGoaNumbers(ctx, &payload)
	if err != nil {
		return nil, err
	}

	if res == nil {
		return nil, nil
	}

	items := slice.Map(res, func(item masterdata_model.Goa) *catalog_goa_common.GoaCatalog {
		return goa_master_repo.ToGoaCatalog(&item)
	})

	return items, nil
}

func (srv *CatalogGoaService) DeleteGoaCatalog(ctx *titan.Context, request *catalog_goa_api.DeleteGoaCatalogRequest) error {
	res, err := srv.goaMasterRepo.GetById(ctx, &masterdata.GetByIdRequest{
		Id: request.Id,
	})
	if err != nil {
		return err
	}

	if res == nil {
		return errors.New("goa catalog not found")
	}

	if err := srv.CUDHook.ExecuteOnDelete(ctx, goa_master_repo.ToGoaCatalog(res)); err != nil {
		return err
	}

	return srv.goaMasterRepo.Delete(ctx, &goa_master_repo.DeleteRequest{
		Id: request.Id,
	})
}

func (srv *CatalogGoaService) upsertGoaCatalog(ctx *titan.Context, request *catalog_goa_common.GoaCatalog) (*catalog_goa_common.GoaCatalog, error) {
	if request == nil {
		return nil, errors.New("goa catalog is empty")
	}
	goa := request
	validationRes, err := srv.IsValidUpdateGoa(ctx, &catalog_goa_api.UpdateGoaCatalogRequest{
		Goa: goa,
	})
	if err != nil {
		return nil, err
	}

	if validationRes != nil && len(validationRes.Errors) > 0 {
		return nil, errors.Errorf("goa number %s already exists", goa.GoaNumber)
	}

	goaModel, err := srv.goaMasterRepo.TransformToGoaModel(ctx, goa)
	if err != nil {
		return nil, err
	}

	res, err := srv.goaMasterRepo.Upsert(ctx, goaModel)
	if err != nil {
		return nil, err
	}

	if res == nil {
		return nil, errors.New("failed to upsert goa catalog")
	}

	return goa_master_repo.ToGoaCatalog(res), nil
}

func (srv *CatalogGoaService) CreateGoaCatalog(ctx *titan.Context, request *catalog_goa_api.CreateGoaCatalogRequest) (*catalog_goa_api.GoaCatalogItem, error) {
	res, err := srv.upsertGoaCatalog(ctx, request.Goa)
	if err != nil {
		return nil, err
	}

	if err := srv.CUDHook.ExecuteOnCreate(ctx, request.Goa); err != nil {
		return nil, err
	}

	return &catalog_goa_api.GoaCatalogItem{
		Goa: res,
	}, nil
}

func (srv *CatalogGoaService) UpdateGoaCatalog(ctx *titan.Context, request *catalog_goa_api.UpdateGoaCatalogRequest) (*catalog_goa_api.GoaCatalogItem, error) {
	res, err := srv.upsertGoaCatalog(ctx, request.Goa)
	if err != nil {
		return nil, err
	}

	if err := srv.CUDHook.ExecuteOnUpdate(ctx, res); err != nil {
		return nil, err
	}

	return &catalog_goa_api.GoaCatalogItem{
		Goa: res,
	}, nil
}

func (srv *CatalogGoaService) IsValidUpdateGoa(ctx *titan.Context, request *catalog_goa_api.UpdateGoaCatalogRequest) (*catalog_goa_api.IsValidUpdateGoaResponse, error) {
	validationResponse := catalog_goa_api.IsValidUpdateGoaResponse{
		Errors: map[string]*common.FieldError{},
	}
	fieldError := common.FieldError{
		Field:          "goa.goanumber",
		ValidationType: common.ValidationType_Error,
		ErrorCode:      string(error_code.ErrorCode_GOA_Existed),
	}

	goa := request.Goa
	id := goa.GoaId
	// case update sdik catalog
	if id != "" {
		updatingGoa, err := srv.goaMasterRepo.GetById(ctx, &masterdata.GetByIdRequest{
			Id: id,
		})
		if err != nil {
			return nil, err
		}

		if updatingGoa != nil && updatingGoa.GoaNumber == request.Goa.GoaNumber {
			return &validationResponse, nil
		}
	}

	goaData, err := srv.GetGoaCatalogByGoaNumber(ctx, &catalog_goa_api.GetGoaCatalogByNumberRequest{
		GoaNumber: request.Goa.GoaNumber,
	})
	if err != nil {
		return nil, err
	}

	if goaData == nil {
		return &validationResponse, nil
	}

	validationResponse.Errors["goa.goanumber"] = &fieldError
	return &validationResponse, nil
}
