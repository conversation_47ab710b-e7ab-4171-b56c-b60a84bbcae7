// This code was autogenerated from service/domains/error_code.proto, do not edit.

package error_code

import (
	"encoding/json"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type CommonException struct {
	Status           int32    `json:"status"`
	Message          string   `json:"message"`
	ServerError      string   `json:"serverError"`
	ServerErrorParam []string `json:"serverErrorParam"`
}

// enum definitions
type ErrorCode string

const (
	ErrorCode_ValidationError_NotActive_InsuranceInfo            ErrorCode = "ErrorCode_ValidationError_NotActive_InsuranceInfo"
	ErrorCode_CardReader_NotAvailable                            ErrorCode = "ErrorCode_CardReader_NotAvailable"
	ErrorCode_CardReader_Error                                   ErrorCode = "ErrorCode_CardReader_Error"
	ErrorCode_Setting_TI_Not_Found                               ErrorCode = "ErrorCode_Setting_TI_Not_Found"
	ErrorCode_Patient_Not_Found                                  ErrorCode = "ErrorCode_Patient_Not_Found"
	ErrorCode_Cost_Unit_Not_Found                                ErrorCode = "ErrorCode_Cost_Unit_Not_Found"
	ErrorCode_Doctor_Not_Found                                   ErrorCode = "ErrorCode_Doctor_Not_Found"
	ErrorCode_Schein_Not_Found                                   ErrorCode = "ErrorCode_Schein_Not_Found"
	ErrorCode_SDEBM_Rule_Not_Found                               ErrorCode = "ErrorCode_SDEBM_Rule_Not_Found"
	ErrorCode_Employee_Not_Found                                 ErrorCode = "ErrorCode_Employee_Not_Found"
	ErrorCode_Prescribe_Not_Found                                ErrorCode = "ErrorCode_Prescribe_Not_Found"
	ErrorCode_Form_Not_Found                                     ErrorCode = "ErrorCode_Form_Not_Found"
	ErrorCode_PrinterProfile_Not_Found                           ErrorCode = "ErrorCode_PrinterProfile_Not_Found"
	ErrorCode_Login_Invalid_Verify_Password                      ErrorCode = "ErrorCode_Login_Invalid_Verify_Password"
	ErrorCode_Login_Invalid_New_Password                         ErrorCode = "ErrorCode_Login_Invalid_New_Password"
	ErrorCode_Login_Invalid_Current_Password                     ErrorCode = "ErrorCode_Login_Invalid_Current_Password"
	ErrorCode_SDEBM_Service_Code_Existed                         ErrorCode = "ErrorCode_SDEBM_Service_Code_Existed"
	ErrorCode_SDKT_Duplicated_VKNR                               ErrorCode = "ErrorCode_SDKT_Duplicated_VKNR"
	ErrorCode_SDKT_Required_CatalogId                            ErrorCode = "ErrorCode_SDKT_Required_CatalogId"
	ErrorCode_SDKT_Create_Failed                                 ErrorCode = "ErrorCode_SDKT_Create_Failed"
	ErrorCode_SDKT_Not_Found                                     ErrorCode = "ErrorCode_SDKT_Not_Found"
	ErrorCode_SDEBM_Cannot_Deleted                               ErrorCode = "ErrorCode_SDEBM_Cannot_Deleted"
	ErrorCode_BSNR_NOT_FOUND                                     ErrorCode = "ErrorCode_BSNR_NOT_FOUND"
	ErrorCode_NO_SCHEIN_BILLING_FOUND                            ErrorCode = "ErrorCode_NO_SCHEIN_BILLING_FOUND"
	ErrorCode_NO_SCHEIN_BILLING_IN_QUARTER_FOUND                 ErrorCode = "ErrorCode_NO_SCHEIN_BILLING_IN_QUARTER_FOUND"
	ErrorCode_NO_DIAGNOSIS_BILLING_FOUND                         ErrorCode = "ErrorCode_NO_DIAGNOSIS_BILLING_FOUND"
	ErrorCode_NO_SERVICES_BILLING_FOUND                          ErrorCode = "ErrorCode_NO_SERVICES_BILLING_FOUND"
	ErrorCode_CAN_NOT_CREATE_ERROR_HISTORY                       ErrorCode = "ErrorCode_CAN_NOT_CREATE_ERROR_HISTORY"
	ErrorCode_WAITING_ROOM_NOT_FOUND                             ErrorCode = "ErrorCode_WAITING_ROOM_NOT_FOUND"
	ErrorCode_SERVICE_CODE_USED_IN_TIMELINE                      ErrorCode = "ErrorCode_SERVICE_CODE_USED_IN_TIMELINE"
	ErrorCode_Value_Is_Required                                  ErrorCode = "ErrorCode_Value_Is_Required"
	ErrorCode_Waiting_Room_Have_Patient                          ErrorCode = "ErrorCode_Waiting_Room_Have_Patient"
	ErrorCode_Exist_Patient_In_A_Waiting_Room                    ErrorCode = "ErrorCode_Exist_Patient_In_A_Waiting_Room"
	ErrorCode_Contract_Not_Found                                 ErrorCode = "ErrorCode_Contract_Not_Found"
	ErrorCode_Record_Not_Found                                   ErrorCode = "ErrorCode_Record_Not_Found"
	ErrorCode_Data_Invalid                                       ErrorCode = "ErrorCode_Data_Invalid"
	ErrorCode_Validation_MessageABRD613                          ErrorCode = "ErrorCode_Validation_MessageABRD613"
	ErrorCode_Validation_MessageABRD969                          ErrorCode = "ErrorCode_Validation_MessageABRD969"
	ErrorCode_Validation_MessageABRD514                          ErrorCode = "ErrorCode_Validation_MessageABRD514"
	ErrorCode_Validation_MessageABRD612                          ErrorCode = "ErrorCode_Validation_MessageABRD612"
	ErrorCode_Validation_MessageABRD786                          ErrorCode = "ErrorCode_Validation_MessageABRD786"
	ErrorCode_Validation_MessageP10470MM                         ErrorCode = "ErrorCode_Validation_MessageP10470MM"
	ErrorCode_Validation_MessageP10470MF                         ErrorCode = "ErrorCode_Validation_MessageP10470MF"
	ErrorCode_Validation_MessageP10470KM                         ErrorCode = "ErrorCode_Validation_MessageP10470KM"
	ErrorCode_Validation_MessageP10470KF                         ErrorCode = "ErrorCode_Validation_MessageP10470KF"
	ErrorCode_Validation_MessageP10480MM                         ErrorCode = "ErrorCode_Validation_MessageP10480MM"
	ErrorCode_Validation_MessageP10480MR                         ErrorCode = "ErrorCode_Validation_MessageP10480MR"
	ErrorCode_Validation_MessageP10480MZ                         ErrorCode = "ErrorCode_Validation_MessageP10480MZ"
	ErrorCode_Validation_MessageP10480KM                         ErrorCode = "ErrorCode_Validation_MessageP10480KM"
	ErrorCode_Validation_MessageP10480KR                         ErrorCode = "ErrorCode_Validation_MessageP10480KR"
	ErrorCode_Validation_MessageABRD456                          ErrorCode = "ErrorCode_Validation_MessageABRD456"
	ErrorCode_Validation_MessageABRD887                          ErrorCode = "ErrorCode_Validation_MessageABRD887"
	ErrorCode_Validation_MessageABRD970                          ErrorCode = "ErrorCode_Validation_MessageABRD970"
	ErrorCode_Validation_MessageFieldIsRequiredFormat            ErrorCode = "ErrorCode_Validation_MessageFieldIsRequiredFormat"
	ErrorCode_Validation_MessageMedicineTypeRequireFormat        ErrorCode = "ErrorCode_Validation_MessageMedicineTypeRequireFormat"
	ErrorCode_Validation_MessageMappingError                     ErrorCode = "ErrorCode_Validation_MessageMappingError"
	ErrorCode_Validation_MessageNotBillingAble                   ErrorCode = "ErrorCode_Validation_MessageNotBillingAble"
	ErrorCode_Validation_MessageNotFillingForBilling             ErrorCode = "ErrorCode_Validation_MessageNotFillingForBilling"
	ErrorCode_Validation_MessageRareDiseaseEu                    ErrorCode = "ErrorCode_Validation_MessageRareDiseaseEu"
	ErrorCode_Validation_MessageIfSG                             ErrorCode = "ErrorCode_Validation_MessageIfSG"
	ErrorCode_Validation_MessageNotSuitablePermanent             ErrorCode = "ErrorCode_Validation_MessageNotSuitablePermanent"
	ErrorCode_Validation_MessageNeedPrimaryCode                  ErrorCode = "ErrorCode_Validation_MessageNeedPrimaryCode"
	ErrorCode_CardReader_Validity_Card                           ErrorCode = "ErrorCode_CardReader_Validity_Card"
	ErrorCode_CardReader_UnknownType_Card                        ErrorCode = "ErrorCode_CardReader_UnknownType_Card"
	ErrorCode_CardReader_Error_VSDService                        ErrorCode = "ErrorCode_CardReader_Error_VSDService"
	ErrorCode_TIConnector_Error_Version_Not_Support              ErrorCode = "ErrorCode_TIConnector_Error_Version_Not_Support"
	ErrorCode_TIConnector_Error_Service_Not_Found                ErrorCode = "ErrorCode_TIConnector_Error_Service_Not_Found"
	ErrorCode_CardReader_Confirm_PN_Code_No_Successful           ErrorCode = "ErrorCode_CardReader_Confirm_PN_Code_No_Successful"
	ErrorCode_CardReader_Confirm_PN_Code_Successful              ErrorCode = "ErrorCode_CardReader_Confirm_PN_Code_Successful"
	ErrorCode_CardReader_Insurance_EndDate_Before_Current_Date   ErrorCode = "ErrorCode_CardReader_Insurance_EndDate_Before_Current_Date"
	ErrorCode_CardReader_Insurance_StartDate_After_Current_Date  ErrorCode = "ErrorCode_CardReader_Insurance_StartDate_After_Current_Date"
	ErrorCode_CardReader_SDKT_Vknr_Invalid                       ErrorCode = "ErrorCode_CardReader_SDKT_Vknr_Invalid"
	ErrorCode_CardReader_Proof_Of_Insurance_Invalid              ErrorCode = "ErrorCode_CardReader_Proof_Of_Insurance_Invalid"
	Warning_CardReader_PnResultCode_1                            ErrorCode = "Warning_CardReader_PnResultCode_1"
	Warning_CardReader_PnResultCode_2                            ErrorCode = "Warning_CardReader_PnResultCode_2"
	Warning_CardReader_PnResultCode_3                            ErrorCode = "Warning_CardReader_PnResultCode_3"
	Warning_CardReader_PnResultCode_4                            ErrorCode = "Warning_CardReader_PnResultCode_4"
	Warning_CardReader_PnResultCode_5                            ErrorCode = "Warning_CardReader_PnResultCode_5"
	Warning_CardReader_PnResultCode_6                            ErrorCode = "Warning_CardReader_PnResultCode_6"
	Warning_CardReader_PnErrorCode_114                           ErrorCode = "Warning_CardReader_PnErrorCode_114"
	Warning_CardReader_PnErrorCode_106                           ErrorCode = "Warning_CardReader_PnErrorCode_106"
	Warning_CardReader_PnErrorCode_107                           ErrorCode = "Warning_CardReader_PnErrorCode_107"
	Warning_CardReader_PnErrorCode_113                           ErrorCode = "Warning_CardReader_PnErrorCode_113"
	Warning_CardReader_PnErrorCode_4192                          ErrorCode = "Warning_CardReader_PnErrorCode_4192"
	Warning_CardReader_PnErrorCode_102                           ErrorCode = "Warning_CardReader_PnErrorCode_102"
	Warning_CardReader_PnErrorCode_103                           ErrorCode = "Warning_CardReader_PnErrorCode_103"
	Warning_CardReader_PnErrorCode_104                           ErrorCode = "Warning_CardReader_PnErrorCode_104"
	Warning_CardReader_PnErrorCode_109                           ErrorCode = "Warning_CardReader_PnErrorCode_109"
	Warning_CardReader_PnErrorCode_110                           ErrorCode = "Warning_CardReader_PnErrorCode_110"
	Warning_CardReader_PnErrorCode_112                           ErrorCode = "Warning_CardReader_PnErrorCode_112"
	Warning_CardReader_PnErrorCode_4147                          ErrorCode = "Warning_CardReader_PnErrorCode_4147"
	Warning_CardReader_PnErrorCode_12999                         ErrorCode = "Warning_CardReader_PnErrorCode_12999"
	Warning_CardReader_PnErrorCode_101                           ErrorCode = "Warning_CardReader_PnErrorCode_101"
	Warning_CardReader_PnErrorCode_111                           ErrorCode = "Warning_CardReader_PnErrorCode_111"
	Warning_CardReader_PnErrorCode_4093                          ErrorCode = "Warning_CardReader_PnErrorCode_4093"
	Warning_CardReader_PnErrorCode_3001                          ErrorCode = "Warning_CardReader_PnErrorCode_3001"
	Warning_CardReader_PnErrorCode_12105                         ErrorCode = "Warning_CardReader_PnErrorCode_12105"
	ErrorCode_CardReader_ErrorPn35WithErrorCode12103             ErrorCode = "ErrorCode_CardReader_ErrorPn35WithErrorCode12103"
	ErrorCode_CardReader_ErrorPn6                                ErrorCode = "ErrorCode_CardReader_ErrorPn6"
	ErrorCode_CardReader_ErrorPnNotEqual4AndListErrorCode        ErrorCode = "ErrorCode_CardReader_ErrorPnNotEqual4AndListErrorCode"
	ErrorCode_CardReader_PN4WithErrorCode                        ErrorCode = "ErrorCode_CardReader_PN4WithErrorCode"
	ErrorCode_CardReader_ErrorCode300112105                      ErrorCode = "ErrorCode_CardReader_ErrorCode300112105"
	ErrorCode_CardReader_ErrorCode30403039                       ErrorCode = "ErrorCode_CardReader_ErrorCode30403039"
	ErrorCode_Patient_Not_Found_In_Con_File_Result               ErrorCode = "ErrorCode_Patient_Not_Found_In_Con_File_Result"
	ErrorCode_CardReader_Confirm_PN_Code_Invalid                 ErrorCode = "ErrorCode_CardReader_Confirm_PN_Code_Invalid"
	ErrorCode_SDAV_Cannot_Modified                               ErrorCode = "ErrorCode_SDAV_Cannot_Modified"
	ErrorCode_Template_Is_Exist                                  ErrorCode = "ErrorCode_Template_Is_Exist"
	ErrorCode_Validation_Document_88130                          ErrorCode = "ErrorCode_Validation_Document_88130"
	ErrorCode_KV_Connect_Account_Not_Found                       ErrorCode = "ErrorCode_KV_Connect_Account_Not_Found"
	ErrorCode_Validation_EDMPSuggestion                          ErrorCode = "EDMPSuggestion"
	ErrorCode_TIConnector_Not_Found                              ErrorCode = "ErrorCode_TIConnector_Not_Found"
	ErrorCode_TI_Status_Not_Found                                ErrorCode = "ErrorCode_TI_Status_Not_Found"
	ErrorCode_HeaderFooter_Is_Exist                              ErrorCode = "ErrorCode_HeaderFooter_Is_Exist"
	ErrorCode_TI_Can_Not_Check_Certificate_Expire                ErrorCode = "ErrorCode_TI_Can_Not_Check_Certificate_Expire"
	ErrorCode_TI_Certificate_Expire_Not_Found                    ErrorCode = "ErrorCode_TI_Certificate_Expire_Not_Found"
	ErrorCode_Erezept_Invalid_Request                            ErrorCode = "ErrorCode_Erezept_Invalid_Request"
	ErrorCode_Device_Not_Found                                   ErrorCode = "ErrorCode_Device_Not_Found"
	ErrorCode_EDMP_Case_Number_Invalid                           ErrorCode = "ErrorCode_EDMP_Case_Number_Invalid"
	ErrorCode_EDMP_Enrollment_Not_Found                          ErrorCode = "ErrorCode_EDMP_Enrollment_Not_Found"
	ErrorCode_EDMP_Not_Allowed                                   ErrorCode = "ErrorCode_EDMP_Not_Allowed"
	ErrorCode_EREZEPT_TRAINING_DOCTOR_AUTHORIZATION              ErrorCode = "ErrorCode_EREZEPT_TRAINING_DOCTOR_AUTHORIZATION"
	ErrorCode_Validation_ICD_Code_Not_In_Master_Data             ErrorCode = "ErrorCode_Validation_ICD_Code_Not_In_Master_Data"
	ErrorCode_CANNOT_SEARCH_SERVICE_CODE                         ErrorCode = "ErrorCode_CANNOT_SEARCH_SERVICE_CODE"
	ErrorCode_CERTAINTY_IS_REQUIRED                              ErrorCode = "ErrorCode_CERTAINTY_IS_REQUIRED"
	ErrorCode_Email_Not_Found                                    ErrorCode = "ErrorCode_Email_Not_Found"
	ErrorCode_EDMP_ED_Exist                                      ErrorCode = "ErrorCode_EDMP_ED_Exist"
	ErrorCode_Validation_Missing_Treatment_Time                  ErrorCode = "ErrorCode_Validation_Missing_Treatment_Time"
	ErrorCode_Missing_Referral_Doctor                            ErrorCode = "ErrorCode_Missing_Referral_Doctor"
	ErrorCode_EDMP_ED_Not_Exist                                  ErrorCode = "ErrorCode_EDMP_ED_Not_Exist"
	ErrorCode_Validation_Missing_Pseudo_GNR                      ErrorCode = "ErrorCode_Validation_Missing_Pseudo_GNR"
	ErrorCode_kvConnect_CertificateOlderThanOnKvServer           ErrorCode = "ErrorCode_kvConnect_CertificateOlderThanOnKvServer"
	ErrorCode_empty                                              ErrorCode = "ErrorCode_empty"
	ErrorCode_CardReader_InsHasBeenTerminated                    ErrorCode = "ErrorCode_CardReader_InsHasBeenTerminated"
	ErrorCode_CardReader_InsHasBeenDeactive                      ErrorCode = "ErrorCode_CardReader_InsHasBeenDeactive"
	ErrorCode_CardReader_InsHasBeenRestrictArea                  ErrorCode = "ErrorCode_CardReader_InsHasBeenRestrictArea"
	ErrorCode_CardReader_IK_Invalid_Expired                      ErrorCode = "ErrorCode_CardReader_IK_Invalid_Expired"
	ErrorCode_Common_DataExisting                                ErrorCode = "ErrorCode_Common_DataExisting"
	ErrorCode_EDMP_Get_Document_Not_Found                        ErrorCode = "ErrorCode_EDMP_Get_Document_Not_Found"
	ErrorCode_ValidationError_ScheinHasTimeline                  ErrorCode = "ErrorCode_ValidationError_ScheinHasTimeline"
	ErrorCode_Real_Billing_Already_Sent                          ErrorCode = "ErrorCode_Real_Billing_Already_Sent"
	ErrorCode_Cannot_Correct_Billing_Before_Real_Billing         ErrorCode = "ErrorCode_Cannot_Correct_Billing_Before_Real_Billing"
	ErrorCode_IK_NotFound                                        ErrorCode = "ErrorCode_IK_NotFound"
	ErrorCode_MissICDCodeToBilling                               ErrorCode = "ErrorCode_MissICDCodeToBilling"
	ErrorCode_InvalidServiceCode                                 ErrorCode = "ErrorCode_InvalidServiceCode"
	ErrorCode_ServiceValidationError                             ErrorCode = "ErrorCode_ServiceValidationError"
	ErrorCode_Service_Timeline_Not_Found                         ErrorCode = "ErrorCode_Service_Timeline_Not_Found"
	ErrorCode_SearchSdkt                                         ErrorCode = "ErrorCode_SearchSdkt"
	ErrorCode_Validation_Missing_ScheinId                        ErrorCode = "ErrorCode_Validation_Missing_ScheinId"
	ErrorCode_kvconnect_invalidSignature                         ErrorCode = "ErrorCode_kvconnect_invalidSignature"
	ErrorCode_kvconnect_tss_fail                                 ErrorCode = "ErrorCode_kvconnect_tss_fail"
	ErrorCode_kvconnect_serverCert_invalid                       ErrorCode = "ErrorCode_kvconnect_serverCert_invalid"
	ErrorCode_kvconnect_login                                    ErrorCode = "ErrorCode_kvconnect_login"
	ErrorCode_kvconnect_SendCSR                                  ErrorCode = "ErrorCode_kvconnect_SendCSR"
	ErrorCode_Validation_Invalid_Pseudo_GNR                      ErrorCode = "ErrorCode_Validation_Invalid_Pseudo_GNR"
	ErrorCode_Validation_RangeAge                                ErrorCode = "ErrorCode_Validation_RangeAge"
	ErrorCode_kvconnect_serverCert_expired                       ErrorCode = "ErrorCode_kvconnect_serverCert_expired"
	ErrorCode_kvconnect_tss_timeout                              ErrorCode = "ErrorCode_kvconnect_tss_timeout"
	ErrorCode_Empty_InsuranceInfo                                ErrorCode = "ErrorCode_Empty_InsuranceInfo"
	ErrorCode_Validation_ReplacedWithServiceCodeWhenBilling      ErrorCode = "ReplacedWithServiceCodeWhenBilling"
	ErrorCode_Cannot_Delete_Schein                               ErrorCode = "ErrorCode_Cannot_Delete_Schein"
	ErrorCode_ServerError_KvBilling                              ErrorCode = "ErrorCode_ServerError_KvBilling"
	ErrorCode_ValidationError_InsuranceInfo_Not_Valid_In_Quarter ErrorCode = "ErrorCode_ValidationError_InsuranceInfo_Not_Valid_In_Quarter"
	ErrorCode_ValidationError_ScheinHasActiveDocuments           ErrorCode = "ErrorCode_ValidationError_ScheinHasActiveDocuments"
	ErrorCode_ValidationError_OpsMustInList                      ErrorCode = "ErrorCode_ValidationError_OpsMustInList"
	ErrorCode_ValidationError_GnrMustInList                      ErrorCode = "ErrorCode_ValidationError_GnrMustInList"
	ErrorCode_Validation_Must_Not_Present_Treatment_Time         ErrorCode = "ErrorCode_Validation_Must_Not_Present_Treatment_Time"
	ErrorCode_ValidationError_TSS_Surcharge_Acute                ErrorCode = "tss_surcharge_acute_error"
	ErrorCode_ValidationError_TSS_Surcharge_Routine              ErrorCode = "tss_surcharge_routine_error"
	ErrorCode_ValidationError_TSS_Surcharge_Common               ErrorCode = "tss_surcharge_error_common"
	ErrorCode_ValidationError_TSS_Suggestion                     ErrorCode = "tss_surcharge_suggestion"
	ErrorCode_ValidationError_VknrIsRequired                     ErrorCode = "ErrorCode_ValidationError_VknrIsRequired"
	ErrorCode_ValidationError_BSNR_NOT_VALID_IN_QUARTER          ErrorCode = "ErrorCode_ValidationError_BSNR_NOT_VALID_IN_QUARTER"
	ErrorCode_ValidationError_MultipleActive_InsuranceInfo       ErrorCode = "ErrorCode_ValidationError_MultipleActive_InsuranceInfo"
	ErrorCode_ValidationError_Form_IcdIsRequired                 ErrorCode = "ErrorCode_ValidationError_Form_IcdIsRequired"
	ErrorCode_CardReader_Not_Found_Patient_Card                  ErrorCode = "ErrorCode_CardReader_Not_Found_Patient_Card"
	ErrorCode_GOA_Existed                                        ErrorCode = "ErrorCode_GOA_Existed"
	ErrorCode_Create_Goa_Catalog_Failed                          ErrorCode = "ErrorCode_Create_Goa_Catalog_Failed"
	ErrorCode_ValidationError_EDMP_Not_Supported_Version         ErrorCode = "ErrorCode_ValidationError_EDMP_Not_Supported_Version"
	ErrorCode_CardReader_UnknownType_Card_Status                 ErrorCode = "ErrorCode_CardReader_UnknownType_Card_Status"
	ErrorCode_Sdik_Existed                                       ErrorCode = "ErrorCode_Sdik_Existed"
	ErrorCode_Patient_Missing_Gender                             ErrorCode = "ErrorCode_Patient_Missing_Gender"
	ErrorCode_Insurance_InValid_For_PrivateSchein                ErrorCode = "ErrorCode_Insurance_InValid_For_PrivateSchein"
	ErrorCode_PrivateContractGroup_NotFound                      ErrorCode = "ErrorCode_PrivateContractGroup_NotFound"
	ErrorCode_CardReader_ErrorCode105                            ErrorCode = "ErrorCode_CardReader_ErrorCode105"
	ErrorCode_LDAP_AddressBookNotFound                           ErrorCode = "ErrorCode_LDAP_AddressBookNotFound"
	ErrorCode_LDAP_MailNotFound                                  ErrorCode = "ErrorCode_LDAP_MailNotFound"
	ErrorCode_CardReader_Not_Found_Doctor_Card                   ErrorCode = "ErrorCode_CardReader_Not_Found_Doctor_Card"
	ErrorCode_Validation_Ad4125_InValid                          ErrorCode = "ErrorCode_Validation_Ad4125_InValid"
	ErrorCode_CardReader_Invalid_Vsd_TimeStamp                   ErrorCode = "ErrorCode_CardReader_Invalid_Vsd_TimeStamp"
	ErrorCode_CardReader_Invalid_Insurance                       ErrorCode = "ErrorCode_CardReader_Invalid_Insurance"
	ErrorCode_CardReader_Cannot_Delete_Insurance                 ErrorCode = "ErrorCode_CardReader_Cannot_Delete_Insurance"
	ErrorCode_GoaService_ExcludedCode                            ErrorCode = "ErrorCode_GoaService_ExcludedCode"
	ErrorCode_Companion_NotReady                                 ErrorCode = "ErrorCode_Companion_NotReady"
	ErrorCode_EDMP_Gender_Not_Allowed                            ErrorCode = "ErrorCode_EDMP_Gender_Not_Allowed"
	ErrorCode_Sdkt_Invalid_Validity                              ErrorCode = "ErrorCode_Sdkt_Invalid_Validity"
	ErrorCode_Sdkt_Invalid_Restrict_Region                       ErrorCode = "ErrorCode_Sdkt_Invalid_Restrict_Region"
	ErrorCode_ValidationError_MustHaveRVSA                       ErrorCode = "ErrorCode_ValidationError_MustHaveRVSA"
	ErrorCode_ValidationError_RequireLanr                        ErrorCode = "InVertretungFuer_LANR"
	ErrorCode_ValidationError_RequireBsnr                        ErrorCode = "InVertretungFuer_BSNR"
	ErrorCode_Schein_Validation_Patient_Insurance_Not_Found      ErrorCode = "ErrorCode_Schein_Validation_Patient_Insurance_Not_Found"
	ErrorCode_Sdik_Assigned_To_Schein                            ErrorCode = "ErrorCode_Sdik_Assigned_To_Schein"
	ErrorCode_Validation_MessageVERT647                          ErrorCode = "ErrorCode_Validation_MessageVERT647"
	ErrorCode_TI_Card_Not_Found                                  ErrorCode = "ErrorCode_TI_Card_Not_Found"
	ErrorCode_SMCB_Card_Not_Found                                ErrorCode = "ErrorCode_SMCB_Card_Not_Found"
	ErrorCode_CardReader_ReadCardDate_Not_Found                  ErrorCode = "ErrorCode_CardReader_ReadCardDate_Not_Found"
	ErrorCode_EAU_NotTransmitted                                 ErrorCode = "ErrorCode_EAU_NotTransmitted"
	ErrorCode_Record_Exist                                       ErrorCode = "ErrorCode_Record_Exist"
	ErrorCode_HpmFunction_Not_Available                          ErrorCode = "ErrorCode_HpmFunction_Not_Available"
	ErrorCode_Zitadel_Resource_Already_Exits                     ErrorCode = "Resource_Already_Exits"
	ErrorCode_Zitadel_Resource_Not_Found                         ErrorCode = "Resource_Not_Found"
	ErrorCode_Zitadel_Resource_Not_Changed                       ErrorCode = "Resource_Not_Changed"
	ErrorCode_CardReader_Invalid_CardType                        ErrorCode = "ErrorCode_CardReader_Invalid_CardType"
	ErrorCode_GoaService_GoaNumber_Not_Found                     ErrorCode = "ErrorCode_GoaService_GoaNumber_Not_Found"
	ErrorCode_GoaService_Invalid_GOA                             ErrorCode = "ErrorCode_GoaService_Invalid_GOA"
	ErrorCode_DoctorLetter_Timeline_Not_Found                    ErrorCode = "ErrorCode_DoctorLetter_Timeline_Not_Found"
	ErrorCode_ValidationError_Attachment                         ErrorCode = "ErrorCode_ValidationError_Attachment"
	ErrorCode_CardReader_Invalid_Vknr                            ErrorCode = "ErrorCode_CardReader_Invalid_Vknr"
	ErrorCode_CardReader_Invalid_Sdik                            ErrorCode = "ErrorCode_CardReader_Invalid_Sdik"
	ErrorCode_EAU_SendPrintOutToInsurance                        ErrorCode = "ErrorCode_EAU_SendPrintOutToInsurance"
	ErrorCode_EAB_RecordNotFound                                 ErrorCode = "ErrorCode_EAB_RecordNotFound"
	ErrorCode_EAB_SettingNotFound                                ErrorCode = "ErrorCode_EAB_SettingNotFound"
	ErrorCode_ServiceCodeInMasterDataNotFound                    ErrorCode = "ErrorCode_ServiceCodeInMasterDataNotFound"
	ErrorCode_Validation_MessageABRG669                          ErrorCode = "ErrorCode_Validation_MessageABRG669"
	ErrorCode_Diga_PznAlreadyExists                              ErrorCode = "ErrorCode_Diga_PznAlreadyExists"
	ErrorCode_Diga_PznExpired                                    ErrorCode = "ErrorCode_Diga_PznExpired"
	ErrorCode_EAB_Mail_Invalid                                   ErrorCode = "ErrorCode_EAB_Mail_Invalid"
	ErrorCode_EAB_History_RecordNotFound                         ErrorCode = "ErrorCode_EAB_History_RecordNotFound"
	ErrorCode_Cannot_Delete_Timeline_Entry                       ErrorCode = "ErrorCode_Cannot_Delete_Timeline_Entry"
	ErrorCode_Add_Organization_Failed                            ErrorCode = "ErrorCode_Add_Organization_Failed"
	ErrorCode_Organization_Exists                                ErrorCode = "ErrorCode_Organization_Exists"
	ErrorCode_GrantPRO_Failed                                    ErrorCode = "ErrorCode_GrantPRO_Failed"
	ErrorCode_GrantCAL_Failed                                    ErrorCode = "ErrorCode_GrantCAL_Failed"
	ErrorCode_Organization_Deactivated                           ErrorCode = "ErrorCode_Organization_Deactivated"
	ErrorCode_Invalid_IK_Number                                  ErrorCode = "ErrorCode_Invalid_IK_Number"
	ErrorCode_MobileCard_SettingNotFound                         ErrorCode = "ErrorCode_MobileCard_SettingNotFound"
	ErrorCode_Validation_Abrd1564                                ErrorCode = "ErrorCode_Validation_Abrd1564"
	ErrorCode_WarningForGroupDoctorValidate                      ErrorCode = "ErrorCode_WarningForGroupDoctorValidate"
	Hint_ABRD1062                                                ErrorCode = "Hint_ABRD1062"
	ErrorCode_Arriba_Not_Eligible                                ErrorCode = "ErrorCode_Arriba_Not_Eligible"
	ErrorCode_PrinterProfileGroup_Not_Found                      ErrorCode = "ErrorCode_PrinterProfileGroup_Not_Found"
	ErrorCode_LDAPConnection_Failed                              ErrorCode = "ErrorCode_LDAPConnection_Failed"
	ErrorCode_Arriba_Session_Not_Finished                        ErrorCode = "ErrorCode_Arriba_Session_Not_Finished"
	ErrorCode_Device_Wrong_Format                                ErrorCode = "ErrorCode_Device_Wrong_Format"
	ErrorCode_KIM_Account_Invalid                                ErrorCode = "ErrorCode_KIM_Account_Invalid"
	ErrorCode_TI_Setting_Not_Found                               ErrorCode = "ErrorCode_TI_Setting_Not_Found"
	ErrorCode_UniqueFileAndFolder_GdtImport                      ErrorCode = "ErrorCode_UniqueFileAndFolder_GdtImport"
	ErrorCode_Forbidden                                          ErrorCode = "ErrorCode_Forbidden"
	ErrorCode_Unauthorized                                       ErrorCode = "ErrorCode_Unauthorized"
	ErrorCode_Insurance_Has_Assign_Schein                        ErrorCode = "ErrorCode_Insurance_Has_Assign_Schein"
	ErrorCode_Duplicate_LHMItem                                  ErrorCode = "ErrorCode_Duplicate_LHMItem"
	ErrorCode_Cost_Unit_Has_Expired                              ErrorCode = "ErrorCode_Cost_Unit_Has_Expired"
	ErrorCode_Cost_Unit_IsNot_Available_In_KvRegion              ErrorCode = "ErrorCode_Cost_Unit_IsNot_Available_In_KvRegion"
	ErrorCode_CanNot_Change_InsuranceType                        ErrorCode = "ErrorCode_CanNot_Change_InsuranceType"
	ErrorCode_Missmatch5005And5050                               ErrorCode = "ErrorCode_Missmatch5005And5050"
	ErrorCode_UV_GOA_Existed                                     ErrorCode = "ErrorCode_UV_GOA_Existed"
	ErrorCode_DocumentType_NameExisted                           ErrorCode = "ErrorCode_DocumentType_NameExisted"
	ErrorCode_DocumentType_AbbrExisted                           ErrorCode = "ErrorCode_DocumentType_AbbrExisted"
	ErrorCode_CardReader_Invalid_KVK                             ErrorCode = "ErrorCode_CardReader_Invalid_KVK"
	ErrorCode_ExportReportFailed                                 ErrorCode = "ErrorCode_ExportReportFailed"
	ErrorCode_Insurance_InValid_For_BgSchein                     ErrorCode = "ErrorCode_Insurance_InValid_For_BgSchein"
	ErrorCode_PrinterHost_Not_Found                              ErrorCode = "ErrorCode_PrinterHost_Not_Found"
	ErrorCode_PrinterSetting_IsInvalid                           ErrorCode = "ErrorCode_PrinterSetting_IsInvalid"
	ErrorCode_Printer_Connection_Failed                          ErrorCode = "ErrorCode_Printer_Connection_Failed"
	ErrorCode_Validation_Abrg1565                                ErrorCode = "ErrorCode_Validation_Abrg1565"
	ErrorCode_Goa_Not_Found                                      ErrorCode = "ErrorCode_Goa_Not_Found"
	ErrorCode_Sdebm_Catalog_Existed                              ErrorCode = "ErrorCode_Sdebm_Catalog_Existed"
	ErrorCode_Sdebm_Catalog_Not_Found                            ErrorCode = "ErrorCode_Sdebm_Catalog_Not_Found"
	ErrorCode_Sdik_NotFound                                      ErrorCode = "ErrorCode_Sdik_NotFound"
	ErrorCode_UV_GOA_Not_Found                                   ErrorCode = "ErrorCode_UV_GOA_Not_Found"
	ErrorCode_Masterdata_GenerateApiKey_Failed                   ErrorCode = "ErrorCode_Masterdata_GenerateApiKey_Failed"
	ErrorCode_PTV_Import_Older_Than_Latest                       ErrorCode = "ErrorCode_PTV_Import_Older_Than_Latest"
)

// Define constants
const NATS_SUBJECT = "" // nats subject this service will listen to

// service event constants

// message event constants

// Define service interface -------------------------------------------------------------

// Define service proxy -------------------------------------------------------------------

// Define service router -----------------------------------------------------------------

// Define client ----------------------------------------------------------------------------------------------

// Define event Notifier -----------------------------------------------------------------------------------------
type ErrorCodeNotifier struct {
	client *titan.Client
}

func NewErrorCodeNotifier() *ErrorCodeNotifier {
	client := titan.GetDefaultClient()
	return &ErrorCodeNotifier{client}
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type ErrorCodeSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewErrorCodeSocketNotifier(socket *socket_api.SocketServiceClient) *ErrorCodeSocketNotifier {
	return &ErrorCodeSocketNotifier{socket}
}

// -----------------------------------------------
// Test event listener
type ErrorCodeEventListener struct {
	mux sync.Mutex
}

func (listener *ErrorCodeEventListener) Reset() {
	listener.mux.Lock()
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *ErrorCodeEventListener) Subscribe(s *titan.MessageSubscriber) {
}
