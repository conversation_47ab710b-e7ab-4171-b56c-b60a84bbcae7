package companion_service_provider

import (
	"gitlab.com/silenteer-oss/titan"

	"git.tutum.dev/medi/tutum/ares/app/companion/idp_app"
	"git.tutum.dev/medi/tutum/ares/app/companion/ti_app"
	"git.tutum.dev/medi/tutum/ares/pkg/api_telematik_util"
	"git.tutum.dev/medi/tutum/ares/pkg/redis"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/CCTX"
)

type idpService struct {
	redisClient        *redis.Redis
	comApp             idp_app.IdpApp
	productTypeVersion string
	tiInfo             *ti_app.TiInfo
}

func NewIdpService(
	redisPool *redis.Redis,
	comApp idp_app.IdpApp,
	productTypeVersion string,
	tiInfo *ti_app.TiInfo,
) *idpService {
	return &idpService{
		redisClient:        redisPool,
		comApp:             comApp,
		productTypeVersion: productTypeVersion,
		tiInfo:             tiInfo,
	}
}

func (srv *idpService) GetBearerToken(ctx *titan.Context, request CCTX.ContextType) (*string, error) {
	res, err := srv.comApp.GetBearerToken(ctx, idp_app.GetBearerTokenRequest{
		ConnectorContext: idp_app.ConnectorContext{
			MandantId:      string(request.MandantId),
			ClientSystemId: string(request.ClientSystemId),
			WorkplaceId:    string(request.WorkplaceId),
			UserId:         nil,
		},
		Ptv:    srv.productTypeVersion,
		TiInfo: *srv.tiInfo,
	})
	if err != nil {
		return nil, api_telematik_util.ParseTiError(ctx, err)
	}
	return &res.BearerToken, nil
}
