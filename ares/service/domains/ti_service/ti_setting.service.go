package ti_service

import (
	"bytes"
	"encoding/base64"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"

	"git.tutum.dev/medi/tutum/ares/app/admin/api/ti_connector"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	mongodb "git.tutum.dev/medi/tutum/ares/pkg/repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/ti_connector_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/companion_modules"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/admin/device"
	settingsRepos "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/settings"
	settings_common "git.tutum.dev/medi/tutum/ares/service/domains/settings/common"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/CCTX"
	"git.tutum.dev/medi/tutum/pkg/cer_util"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"github.com/submodule-org/submodule.go/v2"
)

type TiConnectorSetting struct {
	TIConnector ti_connector_common.TIConnector
	TiContext   CCTX.ContextType
}

type TiSettingService struct {
	settingsRepo       mongodb.Repo[*settingsRepos.Settings[ti_connector_common.TIConnector]]
	employeeDeviceRepo device.EmployeeDeviceDefaultRepository
	companionService   companion_modules.CompanionService
}

var TiSettingServiceMod = submodule.Make[*TiSettingService](
	func(companionService companion_modules.CompanionService) (*TiSettingService, error) {
		tmp := submodule.CreateScope()
		tmp.InitValue(companion_modules.CompanionServiceMod, companionService)
		return &TiSettingService{
			settingsRepo:       settingsRepos.NewSettingsDefaultRepository[ti_connector_common.TIConnector](),
			employeeDeviceRepo: device.NewEmployeeDeviceDefaultRepository(),
			companionService:   companionService,
		}, nil
	},
	companion_modules.CompanionServiceMod,
)

func (srv *TiSettingService) RemoveById(ctx *titan.Context, id uuid.UUID) error {
	filter := bson.M{
		settingsRepos.Field_Feature: string(settings_common.SettingsFeatures_TIConnector),
		"_id":                       id,
	}
	count, err := srv.settingsRepo.Delete(ctx, filter)
	if err != nil {
		return fmt.Errorf("TI_DELETE_ERROR: %w", err)
	}
	if count == 1 {
		return nil
	}

	return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Setting_TI_Not_Found)
}

func (srv *TiSettingService) GetBySettingId(ctx *titan.Context, id uuid.UUID) (*ti_connector_common.TIConnector, error) {
	tiSetting, err := srv.settingsRepo.FindById(ctx, id)
	if err != nil {
		return nil, err
	}
	if tiSetting == nil {
		return nil, nil
	}
	return &tiSetting.Settings, nil
}

func (srv *TiSettingService) GetBySettingIdAndDeviceId(ctx *titan.Context, id uuid.UUID, deviceId string) (*ti_connector_common.TIConnector, error) {
	tiSetting, err := srv.settingsRepo.FindOne(ctx, bson.D{
		{
			Key:   settingsRepos.Field_Id,
			Value: id,
		},
		{
			Key:   settingsRepos.Field_Settings_DeviceIds,
			Value: deviceId,
		},
	})
	if err != nil {
		return nil, err
	}

	if tiSetting == nil {
		return nil, nil
	}

	return &tiSetting.Settings, nil
}

func (srv *TiSettingService) GetTIConnectors(ctx *titan.Context) ([]ti_connector_common.TIConnector, error) {
	filter := bson.M{
		settingsRepos.Field_Feature: string(settings_common.SettingsFeatures_TIConnector),
	}
	tiSettings, err := srv.settingsRepo.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	return slice.Map(tiSettings, func(t *settingsRepos.Settings[ti_connector_common.TIConnector]) ti_connector_common.TIConnector {
		return t.Settings
	}), nil
}

// GetTiConnectorByDeviceId: deprecated
func (srv *TiSettingService) GetTiConnectorByDeviceId(ctx *titan.Context, deviceId string) (*settingsRepos.Settings[ti_connector_common.TIConnector], error) {
	filter := bson.M{
		settingsRepos.Field_Feature: string(settings_common.SettingsFeatures_TIConnector),
	}
	tiSettings, err := srv.settingsRepo.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	foundTiSettings := slice.Filter(tiSettings, func(tiSetting *settingsRepos.Settings[ti_connector_common.TIConnector]) bool {
		return slice.Contains(tiSetting.Settings.DeviceIds, deviceId)
	})
	if len(foundTiSettings) == 0 {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Setting_TI_Not_Found)
	}
	if len(foundTiSettings) > 1 {
		return nil, fmt.Errorf("invalid data: not allow to have many TI settings assign to a device %s", deviceId)
	}
	return foundTiSettings[0], nil
}

// GetByDeviceId get if the ti setting link to a device
func (srv *TiSettingService) GetByDeviceId(ctx *titan.Context, deviceId string) (*TiConnectorSetting, error) {
	deviceIdUuid, err := uuid.Parse(deviceId)
	if err != nil {
		return nil, err
	}

	device, err := srv.employeeDeviceRepo.FindById(ctx, deviceIdUuid)
	if err != nil {
		return nil, err
	}
	if device == nil {
		return nil, errors.New(string(error_code.ErrorCode_Device_Not_Found))
	}
	tiSetting, err := srv.GetTiConnectorByDeviceId(ctx, deviceId)
	if err != nil {
		return nil, err
	}

	return &TiConnectorSetting{
		TIConnector: tiSetting.Settings,
		TiContext:   tiSetting.Settings.ToCCTXContextType(device.DeviceName),
	}, nil
}

func (srv *TiSettingService) SaveTIConnector(
	ctx *titan.Context,
	request ti_connector.SaveTIConnectorRequest,
) (*ti_connector_common.TIConnector, error) {
	tiConnector := ti_connector_common.TIConnector{
		Host:                request.TIConnector.Host,
		Port:                request.TIConnector.Port,
		TLS:                 request.TIConnector.TLS,
		TLSUsername:         request.TIConnector.TLSUsername,
		TLSPassword:         request.TIConnector.TLSPassword,
		DeviceIds:           request.TIConnector.DeviceIds,
		MandantId:           request.TIConnector.MandantId,
		ClientSystemId:      request.TIConnector.ClientSystemId,
		OnlineCheck:         request.TIConnector.OnlineCheck,
		Certificate:         request.TIConnector.Certificate,
		CertificatePassword: request.TIConnector.CertificatePassword,
		IsGetCertificate:    request.TIConnector.IsGetCertificate,
		AdminDevice:         request.TIConnector.AdminDevice,
	}

	// Perform P12 to PEM conversion if certificate data is provided and not already in PEM format
	// TODO:
	// Enhanced certificate validation (expiration, CA, key usage).
	// More specific error handling for certificate issues.
	if tiConnector.Certificate != "" {
		// Check if the certificate is already in PEM format
		// A simple check for PEM header, adjust as necessary for more robust validation
		certData := tiConnector.Certificate
		if commaIndex := strings.IndexByte(certData, ','); commaIndex != -1 {
			certData = certData[commaIndex+1:]
		}
		decodedCert, err := base64.StdEncoding.DecodeString(certData)
		if err != nil {
			return nil, fmt.Errorf("failed to decode base64 certificate: %w", err)
		}
		if len(decodedCert) == 0 {
			return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_empty)
		}
		isPEM := bytes.Contains(decodedCert, []byte("-----BEGIN"))

		if !isPEM {
			keyPair, err := cer_util.LoadFromP12Data(decodedCert, tiConnector.CertificatePassword)
			if err != nil {
				return nil, fmt.Errorf("P12 to PEM conversion failed: %w", err)
			}

			var pemBlocksForStorage [][]byte
			if keyPair.PrivateKeyPEM != nil {
				pemBlocksForStorage = append(pemBlocksForStorage, keyPair.PrivateKeyPEM)
			}
			// Add certificates. The order from CertificatePEMs should be leaf first, then intermediates.
			pemBlocksForStorage = append(pemBlocksForStorage, keyPair.CertificatePEMs...)
			concatenatedPEMBytes := bytes.Join(pemBlocksForStorage, []byte("\n")) // Join with a newline.

			// Store the concatenated PEM bytes back as a base64 encoded string
			tiConnector.Certificate = base64.StdEncoding.EncodeToString(concatenatedPEMBytes)
			tiConnector.CertificatePassword = "" // Clear the password as it's no longer needed for PEM
		} else {
			// If it's already PEM, ensure the password is cleared if it was for P12
			// The certificate remains base64 encoded as it was received
			// Validate the PEM certificate
			valid, errMsg := cer_util.IsValidPEMWithKeyAndCert(decodedCert)
			if !valid {
				return nil, fmt.Errorf("invalid PEM certificate: %s", errMsg)
			}
			tiConnector.Certificate = base64.StdEncoding.EncodeToString(decodedCert)
			tiConnector.CertificatePassword = ""
		}
	}

	if request.ID == nil {
		// create
		newId := uuid.New()
		tiConnector.ID = &newId
		tiConnectorSetting := settingsRepos.Settings[ti_connector_common.TIConnector]{
			Id:       &newId,
			Feature:  string(settings_common.SettingsFeatures_TIConnector),
			Settings: tiConnector,
		}
		_, err := srv.settingsRepo.Create(ctx, &tiConnectorSetting)
		if err != nil {
			return nil, err
		}
	} else {
		// update
		tiConnector.ID = request.ID
		filter := bson.M{
			settingsRepos.Field_Id: *request.ID,
		}
		update := bson.M{
			"$set": bson.M{
				settingsRepos.Field_Settings: tiConnector,
			},
		}
		_, err := srv.settingsRepo.FindOneAndUpdate(ctx, filter, update)
		if err != nil {
			return nil, err
		}
	}

	return &tiConnector, nil
}
