package timeline_service

import (
	"fmt"
	"strconv"

	"emperror.dev/errors"
	coding_rule_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/coding_rule"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdkrw_common"
	api_common "git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/master_data_common"
	coding_rule_common "git.tutum.dev/medi/tutum/ares/service/domains/coding_rule/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/samber/lo"
	"github.com/thoas/go-funk"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
)

type CodingRuleService interface {
	ValidateCodingRuleByCheckTime(ctx *titan.Context, patientId uuid.UUID, yearQuarter util.YearQuarter, checkTime master_data_common.CheckTime) ([]*coding_rule_common.CodingRuleSuggestion, error)
	PaginateCodingRuleSuggestionByPatient(ctx *titan.Context, yearQuarter util.YearQuarter, page, size int64) (*coding_rule_api.GetCodingRuleOverviewByQuarterResponse, error)
}

func (srv *TimelineService[any]) PaginateCodingRuleSuggestionByPatient(
	ctx *titan.Context, yearQuarter util.YearQuarter, page, size int64,
) (*coding_rule_api.GetCodingRuleOverviewByQuarterResponse, error) {
	diagnosePagination, err := srv.timelineDiagnosisRepo.PaginateDiagnoseTimelineByPatient(ctx, page, size, yearQuarter)
	if err != nil {
		return nil, err
	}
	if len(diagnosePagination.Data) == 0 {
		return nil, nil
	}

	patientIds := []uuid.UUID{}
	for _, diagnose := range diagnosePagination.Data {
		if slice.Contains(patientIds, diagnose.PatientId) {
			continue
		}
		patientIds = append(patientIds, diagnose.PatientId)
	}

	suggestionResponses := []coding_rule_api.GetCodingRuleOverviewData{}
	for _, patientId := range patientIds {
		codingRuleSuggestions, validatingErr := srv.ValidateCodingRuleByCheckTime(
			ctx, patientId, yearQuarter, master_data_common.CheckTime_Selectable,
		)
		if validatingErr != nil {
			return nil, validatingErr
		}
		if len(codingRuleSuggestions) == 0 {
			continue
		}

		// NOTE: flat suggestion by code and certainty
		suggestions := []*coding_rule_api.CodingRuleOverviewSuggestion{}
		for _, codingRuleSuggestion := range codingRuleSuggestions {
			codingRuleSuggestionVal := util.GetPointerValue(codingRuleSuggestion)
			if len(codingRuleSuggestionVal.DiagnoseSuggestions) == 0 {
				continue
			}

			for _, suggestion := range codingRuleSuggestionVal.DiagnoseSuggestions {
				suggestions = append(suggestions, &coding_rule_api.CodingRuleOverviewSuggestion{
					RuleId:                 suggestion.RuleId,
					IcdCode:                codingRuleSuggestionVal.IcdCode,
					IcdCertainty:           codingRuleSuggestionVal.IcdCertainty,
					IcdDescription:         codingRuleSuggestionVal.IcdDescription,
					Hint:                   suggestion.Hint,
					LastDocumentedDoctorId: codingRuleSuggestionVal.LastDocumentedDoctorId,
				})
			}
		}

		suggestionResponses = append(suggestionResponses, coding_rule_api.GetCodingRuleOverviewData{
			PatientInfo: &coding_rule_api.PatientInfoResponse{
				Id: &patientId,
			},
			Suggestions: suggestions,
		})
	}

	if len(suggestionResponses) == 0 {
		return &coding_rule_api.GetCodingRuleOverviewByQuarterResponse{
			TotalPatient: diagnosePagination.TotalPatient,
			Data:         nil,
		}, nil
	}

	patientIds = slice.Map(suggestionResponses, func(suggestion coding_rule_api.GetCodingRuleOverviewData) uuid.UUID {
		return *suggestion.PatientInfo.Id
	})
	patients, err := srv.patientProfileRepo.FindByIds(ctx, patientIds)
	if err != nil {
		return nil, err
	}
	mapPatientById := map[string]patient.PatientProfile{}
	for _, patient := range patients {
		mapPatientById[patient.Id.String()] = patient
	}

	for _, suggestion := range suggestionResponses {
		patient := mapPatientById[suggestion.PatientInfo.Id.String()]
		suggestion.PatientInfo.Id = patient.Id
		suggestion.PatientInfo.FirstName = patient.FirstName
		suggestion.PatientInfo.LastName = patient.LastName
		suggestion.PatientInfo.PatientNumber = strconv.Itoa(int(patient.PatientInfo.PatientNumber))
		suggestion.PatientInfo.DateOfBirth = patient.DateOfBirth
	}

	return &coding_rule_api.GetCodingRuleOverviewByQuarterResponse{
		TotalPatient: diagnosePagination.TotalPatient,
		Data:         lo.ToSlicePtr(suggestionResponses),
	}, nil
}

func (srv *TimelineService[any]) ValidateCodingRuleByCheckTime(
	ctx *titan.Context, patientId uuid.UUID, yearQuarter util.YearQuarter, checkTime master_data_common.CheckTime,
) (suggestions []*coding_rule_common.CodingRuleSuggestion, err error) {
	currentUser := ctx.UserInfo().UserUUID()
	rules, err := srv.sdkrwService.GetValidRulesByCheckTime(
		ctx, checkTime, patientId, *currentUser, yearQuarter,
	)
	if err != nil {
		return nil, err
	}

	switch checkTime {
	case master_data_common.CheckTime_Selectable:
		suggestions, err = srv.validateForSelectableByPatientID(ctx, rules, patientId, yearQuarter)
	case master_data_common.CheckTime_Billing:
		suggestions, err = srv.validateForBillingByPatientID(ctx, rules, patientId, yearQuarter)
	case master_data_common.CheckTime_Coding:
		suggestions, err = srv.validateForCodingByPatientID(ctx, rules, patientId, yearQuarter)
	}
	if err != nil {
		return nil, err
	}

	return suggestions, nil
}

// TODO: update to treatment case
func (srv *TimelineService[any]) validateForCodingByPatientID(
	ctx *titan.Context, rules []*catalog_sdkrw_common.SdkrwCatalog, patientId uuid.UUID, yearQuarter util.YearQuarter,
) ([]*coding_rule_common.CodingRuleSuggestion, error) {
	timelineModels, err := srv.getListUniqTimelineModelByQuarter(ctx, patientId, 1, yearQuarter)
	if err != nil {
		return nil, err
	}

	suggestionResponses := []coding_rule_common.CodingRuleSuggestion{}
	for _, model := range timelineModels {
		treatmentCaseSuggestion, treatmentCaseDotorId, err := srv.getTreatmentCaseSuggestionsFromRuleAndTimelineModel(
			ctx,
			&model,
			rules,
			timelineModels,
			master_data_common.CheckTime_Coding,
		)
		if err != nil {
			return nil, err
		}
		if treatmentCaseDotorId == nil || treatmentCaseSuggestion == nil {
			continue
		}

		suggestionResponses = append(suggestionResponses, *treatmentCaseSuggestion)
	}

	return lo.ToSlicePtr(suggestionResponses), nil
}

func (srv *TimelineService[any]) validateForBillingByPatientID(
	ctx *titan.Context, rules []*catalog_sdkrw_common.SdkrwCatalog, patientId uuid.UUID, yearQuarter util.YearQuarter,
) ([]*coding_rule_common.CodingRuleSuggestion, error) {
	suggestionResponses := []coding_rule_common.CodingRuleSuggestion{}

	ruleQuarterNumberNeedFetch := slice.Map(rules, func(rule *catalog_sdkrw_common.SdkrwCatalog) int32 {
		return rule.Quarter + 1
	})
	ruleQuarterNumberNeedFetch = append(ruleQuarterNumberNeedFetch, 1) // at least has one quarter for validating
	maxNumOfQuarter := lo.Max(ruleQuarterNumberNeedFetch)

	// NOTE: get rules and models for quarterly
	timelineModels, err := srv.getListUniqTimelineModelByQuarter(ctx, patientId, maxNumOfQuarter, yearQuarter)
	if err != nil {
		return nil, err
	}
	timelineModelsInSeletectedQuarter := slice.Filter(timelineModels, func(model common.TimelineModel) bool {
		return model.Year == yearQuarter.Year && model.Quarter == yearQuarter.Quarter
	})

	quarterlyRules := slice.Filter(rules, func(rule *catalog_sdkrw_common.SdkrwCatalog) bool {
		return rule.RuleReference == master_data_common.RuleReference_Quarterly
	})
	treatmentCaseRules := slice.Filter(rules, func(rule *catalog_sdkrw_common.SdkrwCatalog) bool {
		return rule.RuleReference == master_data_common.RuleReference_TreatmentCase
	})

	for _, model := range timelineModelsInSeletectedQuarter {
		// NOTE: Validate for treatment case
		treatmentCaseSuggestion, treatmentCaseDoctorID, err := srv.getTreatmentCaseSuggestionsFromRuleAndTimelineModel(
			ctx,
			&model,
			treatmentCaseRules,
			timelineModelsInSeletectedQuarter,
			master_data_common.CheckTime_Billing,
		)
		if err != nil {
			return nil, err
		}
		if treatmentCaseSuggestion != nil && treatmentCaseDoctorID != nil {
			suggestionResponses = append(suggestionResponses, *treatmentCaseSuggestion)
		}

		// NOTE: Validate for quarterly
		quarterlySuggestion, quarterlyDoctorId, err := srv.getQuarterlySuggestionsFromRuleAndTimelineModel(
			ctx,
			&model,
			quarterlyRules,
			timelineModels,
			master_data_common.CheckTime_Billing,
		)
		if err != nil {
			return nil, err
		}
		if quarterlySuggestion != nil && quarterlyDoctorId != nil {
			suggestionResponses = append(suggestionResponses, *quarterlySuggestion)
		}
	}

	return lo.ToSlicePtr(suggestionResponses), nil
}

func (srv *TimelineService[any]) validateForSelectableByPatientID(
	ctx *titan.Context,
	rules []*catalog_sdkrw_common.SdkrwCatalog,
	patientId uuid.UUID,
	yearQuarter util.YearQuarter,
) ([]*coding_rule_common.CodingRuleSuggestion, error) {
	ruleQuarterNumberNeedFetch := slice.Map(rules, func(rule *catalog_sdkrw_common.SdkrwCatalog) int32 {
		return rule.Quarter + 1
	})
	ruleQuarterNumberNeedFetch = append(ruleQuarterNumberNeedFetch, 1) // at least has one quarter for validating
	maxNumOfQuarter := lo.Max(ruleQuarterNumberNeedFetch)

	// TODO: move this func to timeline service
	timelineModels, err := srv.getListUniqTimelineModelByQuarter(ctx, patientId, maxNumOfQuarter, yearQuarter)
	if err != nil {
		return nil, err
	}

	// NOTE: validate coding rule for selectable base on quarterly rules
	quarterlyRules := slice.Filter(rules, func(rule *catalog_sdkrw_common.SdkrwCatalog) bool {
		return rule.RuleReference == master_data_common.RuleReference_Quarterly
	})

	suggestionResponses := []coding_rule_common.CodingRuleSuggestion{}
	for _, model := range timelineModels {
		if model.Year != yearQuarter.Year || model.Quarter != yearQuarter.Quarter {
			continue
		}

		quarterlySuggestion, quarterlyDoctorId, err := srv.getQuarterlySuggestionsFromRuleAndTimelineModel(
			ctx,
			&model,
			quarterlyRules,
			timelineModels,
			master_data_common.CheckTime_Selectable,
		)
		if err != nil {
			return nil, err
		}
		if quarterlySuggestion == nil || quarterlyDoctorId == nil {
			continue
		}

		suggestionResponses = append(suggestionResponses, *quarterlySuggestion)
	}

	return lo.ToSlicePtr(suggestionResponses), nil
}

func (srv *TimelineService[any]) getTreatmentCaseSuggestionsFromRuleAndTimelineModel(
	ctx *titan.Context,
	model *common.TimelineModel,
	rules []*catalog_sdkrw_common.SdkrwCatalog,
	timelineModels []common.TimelineModel,
	checkTime master_data_common.CheckTime,
) (codingSuggestion *coding_rule_common.CodingRuleSuggestion, lastDocumentedDoctorId *uuid.UUID, err error) {
	encounter := model.EncounterDiagnoseTimeline
	if encounter == nil {
		return nil, nil, nil
	}
	sdkrwSuggestions, err := srv.sdkrwService.GetDiagnoseSuggestionsByTreatmentCaseRules(
		ctx,
		checkTime,
		rules,
		model,
		timelineModels,
	)
	if err != nil {
		return nil, nil, fmt.Errorf("get treatment case suggestions from rule and timeline model: %w", err)
	}
	if len(sdkrwSuggestions) == 0 {
		return nil, nil, nil
	}

	lastDocumentedDoctorId = nil
	if len(model.AuditLogs) != 0 {
		lastDocumentedDoctorId = &model.AuditLogs[len(model.AuditLogs)-1].UserId
	}
	var scheinId *uuid.UUID = nil
	if len(model.ScheinIds) > 0 {
		scheinId = &model.ScheinIds[0]
	}
	codingSuggestion = &coding_rule_common.CodingRuleSuggestion{
		DiagnoseId:             model.Id,
		IcdCode:                model.EncounterDiagnoseTimeline.Code,
		IcdCertainty:           *model.EncounterDiagnoseTimeline.Certainty,
		IcdDescription:         model.EncounterDiagnoseTimeline.Description,
		DiagnoseSuggestions:    lo.ToSlicePtr(sdkrwSuggestions),
		LastDocumentedDoctorId: lastDocumentedDoctorId,
		ScheinId:               scheinId,
	}

	return codingSuggestion, lastDocumentedDoctorId, nil
}

func (srv *TimelineService[any]) getQuarterlySuggestionsFromRuleAndTimelineModel(
	ctx *titan.Context,
	model *common.TimelineModel,
	rules []*catalog_sdkrw_common.SdkrwCatalog,
	timelineModels []common.TimelineModel,
	checkTime master_data_common.CheckTime,
) (codingRuleSuggestion *coding_rule_common.CodingRuleSuggestion, lastDocumentedDoctorId *uuid.UUID, err error) {
	encounter := model.EncounterDiagnoseTimeline
	if encounter == nil {
		return nil, nil, nil
	}
	sdkrwSuggestions, err := srv.sdkrwService.GetDiagnoseSuggestionsByQuarterlyRules(
		ctx,
		checkTime,
		rules,
		model,
		timelineModels,
	)
	if err != nil {
		return nil, nil, err
	}
	if len(sdkrwSuggestions) == 0 {
		return nil, nil, nil
	}

	lastDocumentedDoctorId = nil
	if len(model.AuditLogs) != 0 {
		lastDocumentedDoctorId = &model.AuditLogs[len(model.AuditLogs)-1].UserId
	}
	var scheinId *uuid.UUID = nil
	if len(model.ScheinIds) > 0 {
		scheinId = &model.ScheinIds[0]
	}
	codingRuleSuggestion = &coding_rule_common.CodingRuleSuggestion{
		DiagnoseId:             model.Id,
		IcdCode:                model.EncounterDiagnoseTimeline.Code,
		IcdCertainty:           *model.EncounterDiagnoseTimeline.Certainty,
		IcdDescription:         model.EncounterDiagnoseTimeline.Description,
		DiagnoseSuggestions:    lo.ToSlicePtr(sdkrwSuggestions),
		LastDocumentedDoctorId: lastDocumentedDoctorId,
		ScheinId:               scheinId,
	}

	return codingRuleSuggestion, lastDocumentedDoctorId, nil
}

func (srv *TimelineService[any]) getListUniqTimelineModelByQuarter(
	ctx *titan.Context, patientId uuid.UUID, maxNumOfQuarter int32, yearQuarter util.YearQuarter,
) ([]common.TimelineModel, error) {
	firstQuarter := util.GetPreviousQuarterFrom(maxNumOfQuarter-1, yearQuarter)
	lastQuarter := yearQuarter
	firstQuarterRange, err := firstQuarter.GetQuarterDateRange()
	if err != nil {
		return nil, errors.WithMessage(err, "get list uniq timeline model by quarter")
	}
	lastQuarterRange, err := lastQuarter.GetQuarterDateRange()
	if err != nil {
		return nil, errors.WithMessage(err, "get list uniq timeline model by quarter")
	}

	filter := bson.M{
		timeline_repo.Field_PatientId:          patientId,
		timeline_repo.Field_timelineEntityType: common.TimelineEntityType_Diagnose,
		timeline_repo.Field_IsDeleted:          false,
	}
	fromDateStart, _ := util.GetDayRange(util.GetPointerValue(firstQuarterRange.Start))
	toDateEnd, _ := util.GetDayRange(util.GetPointerValue(lastQuarterRange.End))
	filter[timeline_repo.Field_Selected_Date] = bson.M{
		"$gte": fromDateStart,
		"$lte": toDateEnd,
	}

	diagnosis, err := srv.timelineDiagnosisRepo.Find(ctx, filter)
	if err != nil {
		return nil, errors.WithMessage(err, "get list uniq timeline model by quarter")
	}

	if len(diagnosis) == 0 {
		return []common.TimelineModel{}, nil
	}

	timelineDiagnosisUniques := slice.UniqBy(
		diagnosis,
		func(model *timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) string {
			yearQuarterStr := strconv.Itoa(model.Quarter) + strconv.Itoa(model.Year)
			codeAndCertaintyStr := model.Payload.Code + string(util.GetPointerValue(model.Payload.Certainty))
			return yearQuarterStr + codeAndCertaintyStr
		})

	timelineDiagnosisModels := make([]common.TimelineModel, 0, len(timelineDiagnosisUniques))
	for _, v := range timelineDiagnosisUniques {
		timelineDiagnosisModels = append(timelineDiagnosisModels, ConvertEntityToModel(*v))
	}

	return timelineDiagnosisModels, nil
}

func (srv *TimelineService[T]) getDistinctKVDiagnosesByPatientId(
	ctx *titan.Context, patientId uuid.UUID, yearQuarter util.YearQuarter,
) ([]common.TimelineModel, error) {
	diagnoses, err := srv.GetDiagnosesByQuarter(ctx, patientId, yearQuarter.Quarter, yearQuarter.Year)
	if err != nil {
		return nil, err
	}

	kvDiagnoses := slice.Filter(diagnoses, func(diagnose common.TimelineModel) bool {
		encounter := diagnose.EncounterDiagnoseTimeline
		if encounter == nil {
			return false
		}
		if encounter.Scheins == nil {
			return false
		}
		hasKVSchein := slice.Any(*encounter.Scheins, func(schein *api_common.ScheinWithMainGroup) bool {
			return schein.Group == api_common.KV
		})
		return hasKVSchein
	})

	diagnoseMap := map[string]common.TimelineModel{}
	for _, diagnose := range kvDiagnoses {
		encounter := diagnose.EncounterDiagnoseTimeline
		key := encounter.Code
		if encounter.Certainty != nil {
			key = encounter.Code + string(util.GetPointerValue(encounter.Certainty))
		}
		if _, ok := diagnoseMap[key]; !ok {
			diagnoseMap[key] = diagnose
		}
	}
	var distinctKVDiagnoses []common.TimelineModel
	for _, diagnose := range diagnoseMap {
		distinctKVDiagnoses = append(distinctKVDiagnoses, diagnose)
	}

	return distinctKVDiagnoses, nil
}

func (_ *TimelineService[T]) canUpdateDiagnoseInTimeline(ctx *titan.Context, timeline *common.TimelineModel) bool {
	if util.GetPointerValue(timeline.Type) != common.TimelineEntityType_Diagnose || util.GetPointerValue(timeline.IsImported) {
		return false
	}

	logger := ctx.Logger()
	checkingDiagnose := timeline.EncounterDiagnoseTimeline
	if checkingDiagnose == nil ||
		checkingDiagnose.RunSdkrw == patient_encounter.RUNSDKRWENUM_CANCELLED ||
		checkingDiagnose.RunSdkrw == patient_encounter.RUNSDKRWENUM_RUNNING ||
		checkingDiagnose.Scheins == nil {
		logger.Debug("SDKRW:", "not run validate", "")
		return false
	}

	foundKvSchein := funk.Find(*checkingDiagnose.Scheins, func(i *api_common.ScheinWithMainGroup) bool {
		return i.Group == api_common.KV
	})
	if foundKvSchein == nil {
		logger.Debug("SDKRW:", "not foundKvSchein", "")
		return false
	}
	return true
}

type codingRuleRunner[T any] struct {
	treatmentCaseRules  []*catalog_sdkrw_common.SdkrwCatalog
	distinctKVDiagnoses []common.TimelineModel
	timelineService     *TimelineService[T]
}

func (s *TimelineService[any]) NewCodingRuleRunner(ctx *titan.Context, patientId uuid.UUID, yearQuarter util.YearQuarter) (*codingRuleRunner[any], error) {
	distinctKVDiagnoses, err := s.getDistinctKVDiagnosesByPatientId(ctx, patientId, yearQuarter)
	if err != nil {
		return nil, fmt.Errorf("getDistinctKVDiagnosesByPatientId error: %w", err)
	}
	doctorId := util.GetPointerValue(ctx.UserInfo().UserUUID())
	rulesForCoding, err := s.sdkrwService.GetValidRulesByCheckTime(
		ctx, master_data_common.CheckTime_Coding, patientId, doctorId, yearQuarter,
	)
	if err != nil {
		return nil, fmt.Errorf("GetDiagnosesByQuarter: %w", err)
	}
	treatmentCaseRules := slice.Filter(rulesForCoding, func(rule *catalog_sdkrw_common.SdkrwCatalog) bool {
		return rule.RuleReference == master_data_common.RuleReference_TreatmentCase
	})
	return &codingRuleRunner[any]{
		treatmentCaseRules:  treatmentCaseRules,
		distinctKVDiagnoses: distinctKVDiagnoses,
		timelineService:     s,
	}, nil
}

func (s *codingRuleRunner[any]) runCodingRuleOnDocumenting(ctx *titan.Context, timelineModel *common.TimelineModel) error {
	if !s.timelineService.canUpdateDiagnoseInTimeline(ctx, timelineModel) {
		return nil
	}

	yearQuarter := util.ToYearQuarter(timelineModel.SelectedDate)
	distinctKVDiagnoses := s.distinctKVDiagnoses

	treatmentCaseRules := s.treatmentCaseRules
	if len(distinctKVDiagnoses) == 0 {
		return nil
	}
	var codingRulesUpdateDadas []timeline_repo.UpdateDiagnoseItemWithSuggestionsData
	for _, model := range distinctKVDiagnoses {
		logger := ctx.Logger()

		if util.GetPointerValue(model.Type) != common.TimelineEntityType_Diagnose {
			logger.Debug("SDKRW:", "not run validate", "")
			return nil
		}
		encounter := model.EncounterDiagnoseTimeline
		if encounter == nil ||
			encounter.RunSdkrw == patient_encounter.RUNSDKRWENUM_CANCELLED ||
			encounter.RunSdkrw == patient_encounter.RUNSDKRWENUM_RUNNING ||
			encounter.Scheins == nil {
			logger.Debug("SDKRW:", "not run validate", "")
			continue
		}

		suggestion, _, err := s.timelineService.getTreatmentCaseSuggestionsFromRuleAndTimelineModel(
			ctx,
			&model,
			treatmentCaseRules,
			distinctKVDiagnoses,
			master_data_common.CheckTime_Coding,
		)
		if err != nil {
			return fmt.Errorf("getTreatmentCaseSuggestionsFromRuleAndTimelineModel: %w", err)
		}
		codingRulesUpdateDadas = append(codingRulesUpdateDadas, timeline_repo.UpdateDiagnoseItemWithSuggestionsData{
			Model: &model,
			DiagnoseSuggestion: function.Do(func() []*patient_encounter.DiagnoseSuggestion {
				if suggestion == nil {
					return nil
				}
				return suggestion.DiagnoseSuggestions
			}),
			YearQuarter: yearQuarter,
		})
	}
	if err := s.timelineService.timelineRepo.UpdateDiagnoseItemWithSuggestions(ctx, codingRulesUpdateDadas); err != nil {
		return fmt.Errorf("UpdateDiagnoseItemWithSuggestion: %w", err)
	}
	return nil
}
