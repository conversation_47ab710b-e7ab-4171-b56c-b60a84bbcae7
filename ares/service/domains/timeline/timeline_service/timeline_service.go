package timeline_service

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strings"
	"time"

	"emperror.dev/errors"
	catalog_goa_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_goa"
	patient_profile_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_profile"
	settings_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/settings"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/timeline"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/pkg/hook"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_copy"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	contract_resource "git.tutum.dev/medi/tutum/ares/service/contract/contract"
	"git.tutum.dev/medi/tutum/ares/service/contract/contract/model"
	domains_api_common "git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/lab_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/master_data_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/medicine_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"
	sdkvApi "git.tutum.dev/medi/tutum/ares/service/domains/api/sdkv"
	audit_log_service "git.tutum.dev/medi/tutum/ares/service/domains/audit_log/service"
	catalog_goa_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_goa"
	form_common "git.tutum.dev/medi/tutum/ares/service/domains/form/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/ares/share"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/typesense"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/thoas/go-funk"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	// catalog_overview_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_overview"
	diga "git.tutum.dev/medi/tutum/ares/app/mvz/api/diga"
	hpm_rest_client "git.tutum.dev/medi/tutum/ares/pkg/hpm_rest/client"
	bsnr_service "git.tutum.dev/medi/tutum/ares/service/bsnr"
	doctor_letter_common "git.tutum.dev/medi/tutum/ares/service/domains/doctor_letter/common"
	edmp_common "git.tutum.dev/medi/tutum/ares/service/domains/edmp/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	"git.tutum.dev/medi/tutum/ares/service/domains/internal/patient_overview"
	qes_common "git.tutum.dev/medi/tutum/ares/service/domains/qes/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/sdebm"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/sdomim"
	eau_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/eau"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/edmp"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_participation"
	schein_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	sdicd_common "git.tutum.dev/medi/tutum/ares/service/domains/sdicd/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/sdicd/sdicd_service"
	sdkv_service "git.tutum.dev/medi/tutum/ares/service/domains/sdkv"
	settings_common "git.tutum.dev/medi/tutum/ares/service/domains/settings/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	timeline_utils "git.tutum.dev/medi/tutum/ares/service/domains/timeline/utils"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/validation_timeline"
	mail_common "git.tutum.dev/medi/tutum/ares/service/mail/common"
	"git.tutum.dev/medi/tutum/ares/service/rezidiv"
	settings_service "git.tutum.dev/medi/tutum/ares/service/settings"
	"github.com/submodule-org/submodule.go/v2"
)

var (
	TerminalServiceCodes []string = []string{"88130", "88131"}

	TimelineServiceDiagnosisMod = submodule.Make[*TimelineService[patient_encounter.EncounterDiagnoseTimeline]](func(deps *timelineServiceDeps) *TimelineService[patient_encounter.EncounterDiagnoseTimeline] {
		timelineDiagnosesService := newTimelineService[patient_encounter.EncounterDiagnoseTimeline](deps)
		timelineDiagnosesService.HookBeforeAction.RegisterOnCreateFunc(deps.auditLogService.OnTimelineCreate)
		timelineDiagnosesService.HookBeforeAction.RegisterOnUpdateFunc(deps.auditLogService.OnTimelineUpdate)
		timelineDiagnosesService.HookBeforeAction.RegisterOnDeleteFunc(deps.auditLogService.OnTimelineRemove)
		return timelineDiagnosesService
	}, timelineServiceDepsMod)

	TimelineServiceEnrollmentMod = submodule.Make[*TimelineService[edmp_common.EnrollmentInfo]](func(deps *timelineServiceDeps) *TimelineService[edmp_common.EnrollmentInfo] {
		timelineServiceEnrollment := newTimelineService[edmp_common.EnrollmentInfo](deps)
		timelineServiceEnrollment.HookBeforeAction.RegisterOnCreateFunc(deps.auditLogService.OnTimelineCreate)
		timelineServiceEnrollment.HookBeforeAction.RegisterOnUpdateFunc(deps.auditLogService.OnTimelineUpdate)
		timelineServiceEnrollment.HookBeforeAction.RegisterOnDeleteFunc(deps.auditLogService.OnTimelineRemove)
		return timelineServiceEnrollment
	}, timelineServiceDepsMod)

	TimelineServiceEnrollmentDocumentMod = submodule.Make[*TimelineService[edmp_common.DocumentationOverview]](func(deps *timelineServiceDeps) *TimelineService[edmp_common.DocumentationOverview] {
		timelineServiceEnrollmentDocument := newTimelineService[edmp_common.DocumentationOverview](deps)
		timelineServiceEnrollmentDocument.HookBeforeAction.RegisterOnCreateFunc(deps.auditLogService.OnTimelineCreate)
		timelineServiceEnrollmentDocument.HookBeforeAction.RegisterOnUpdateFunc(deps.auditLogService.OnTimelineUpdate)
		timelineServiceEnrollmentDocument.HookBeforeAction.RegisterOnDeleteFunc(deps.auditLogService.OnTimelineRemove)
		return timelineServiceEnrollmentDocument
	}, timelineServiceDepsMod)

	TimelineServiceEdokuDocumentMod = submodule.Make[*TimelineService[edmp_common.EdokuDocumentationOverview]](func(deps *timelineServiceDeps) *TimelineService[edmp_common.EdokuDocumentationOverview] {
		timelineServiceEdokuDocument := newTimelineService[edmp_common.EdokuDocumentationOverview](deps)
		timelineServiceEdokuDocument.HookBeforeAction.RegisterOnCreateFunc(deps.auditLogService.OnTimelineCreate)
		timelineServiceEdokuDocument.HookBeforeAction.RegisterOnUpdateFunc(deps.auditLogService.OnTimelineUpdate)
		timelineServiceEdokuDocument.HookBeforeAction.RegisterOnDeleteFunc(deps.auditLogService.OnTimelineRemove)
		return timelineServiceEdokuDocument
	}, timelineServiceDepsMod)

	TimelineServiceAnyMod = submodule.Make[*TimelineService[any]](func(deps *timelineServiceDeps) *TimelineService[any] {
		timelineService := newTimelineService[any](deps)
		timelineService.HookBeforeAction.RegisterOnCreateFunc(deps.auditLogService.OnTimelineCreate)
		timelineService.HookBeforeAction.RegisterOnUpdateFunc(deps.auditLogService.OnTimelineUpdate)
		timelineService.HookBeforeAction.RegisterOnDeleteFunc(deps.auditLogService.OnTimelineRemove)

		return timelineService
	}, timelineServiceDepsMod)

	TimelineServiceEncounterMod = submodule.Make[*TimelineService[patient_encounter.EncounterServiceTimeline]](func(deps *timelineServiceDeps) *TimelineService[patient_encounter.EncounterServiceTimeline] {
		return newTimelineService[patient_encounter.EncounterServiceTimeline](deps)
	}, timelineServiceDepsMod)

	TimelineServicePsychotherapyMod = submodule.Make[*TimelineService[patient_encounter.EncounterPsychotherapy]](func(deps *timelineServiceDeps) *TimelineService[patient_encounter.EncounterPsychotherapy] {
		return newTimelineService[patient_encounter.EncounterPsychotherapy](deps)
	}, timelineServiceDepsMod)

	TimelineEmailItemMod = submodule.Make[*TimelineService[mail_common.EmailItem]](func(deps *timelineServiceDeps) *TimelineService[mail_common.EmailItem] {
		return newTimelineService[mail_common.EmailItem](deps)
	}, timelineServiceDepsMod)

	TimelineServiceDoctorLetterMod = submodule.Make[*TimelineService[doctor_letter_common.DoctorLetter]](func(deps *timelineServiceDeps) *TimelineService[doctor_letter_common.DoctorLetter] {
		return newTimelineService[doctor_letter_common.DoctorLetter](deps)
	}, timelineServiceDepsMod)

	TimelineServiceCalendarMod = submodule.Make[*TimelineService[patient_encounter.EncounterCalendarTimeline]](func(deps *timelineServiceDeps) *TimelineService[patient_encounter.EncounterCalendarTimeline] {
		return newTimelineService[patient_encounter.EncounterCalendarTimeline](deps)
	}, timelineServiceDepsMod)

	TimelineEncounterMedicinePlanHistoryMod = submodule.Make[*TimelineService[patient_encounter.EncounterMedicinePlanHistory]](func(deps *timelineServiceDeps) *TimelineService[patient_encounter.EncounterMedicinePlanHistory] {
		return newTimelineService[patient_encounter.EncounterMedicinePlanHistory](deps)
	}, timelineServiceDepsMod)

	TimelineServiceHimiPrescriptionMod = submodule.Make[*TimelineService[patient_encounter.EncounterHimiPrescription]](func(deps *timelineServiceDeps) *TimelineService[patient_encounter.EncounterHimiPrescription] {
		return newTimelineService[patient_encounter.EncounterHimiPrescription](deps)
	}, timelineServiceDepsMod)

	TimelineServiceHeimiPrescriptionMod = submodule.Make[*TimelineService[patient_encounter.EncounterHeimiPrescription]](func(deps *timelineServiceDeps) *TimelineService[patient_encounter.EncounterHeimiPrescription] {
		return newTimelineService[patient_encounter.EncounterHeimiPrescription](deps)
	}, timelineServiceDepsMod)

	TimelineServiceMedicinePrescriptionMod = submodule.Make[*TimelineService[patient_encounter.EncounterMedicinePrescription]](func(deps *timelineServiceDeps) *TimelineService[patient_encounter.EncounterMedicinePrescription] {
		timelineService := newTimelineService[patient_encounter.EncounterMedicinePrescription](deps)
		timelineDiagnosesService := newTimelineService[patient_encounter.EncounterDiagnoseTimeline](deps)
		timelineDiagnosesService.HookBeforeAction.RegisterOnCreateFunc(deps.auditLogService.OnTimelineCreate)
		timelineDiagnosesService.HookBeforeAction.RegisterOnUpdateFunc(deps.auditLogService.OnTimelineUpdate)
		timelineDiagnosesService.HookBeforeAction.RegisterOnDeleteFunc(deps.auditLogService.OnTimelineRemove)
		return timelineService
	}, timelineServiceDepsMod)

	TimelineServiceDigaPrescriptionMod = submodule.Make[*TimelineService[diga.Prescribe]](func(deps *timelineServiceDeps) *TimelineService[diga.Prescribe] {
		return newTimelineService[diga.Prescribe](deps)
	}, timelineServiceDepsMod)

	TimelineServiceFormMod = submodule.Make[*TimelineService[patient_encounter.EncounterForm]](func(deps *timelineServiceDeps) *TimelineService[patient_encounter.EncounterForm] {
		return newTimelineService[patient_encounter.EncounterForm](deps)
	}, timelineServiceDepsMod)
	TimelineServiceLabMod = submodule.Make[*TimelineService[patient_encounter.EncounterLab]](func(deps *timelineServiceDeps) *TimelineService[patient_encounter.EncounterLab] {
		return newTimelineService[patient_encounter.EncounterLab](deps)
	}, timelineServiceDepsMod)
	TimelineServiceReferenceMod = submodule.Make[*TimelineService[common.ReferencePayload]](func(deps *timelineServiceDeps) *TimelineService[common.ReferencePayload] {
		return newTimelineService[common.ReferencePayload](deps)
	}, timelineServiceDepsMod)
	TimelineServiceEHICMod = submodule.Make[*TimelineService[common.EHICPayload]](func(deps *timelineServiceDeps, _ *titan.Client) *TimelineService[common.EHICPayload] {
		t := newTimelineService[common.EHICPayload](deps)
		return t
	}, timelineServiceDepsMod, config.GetDefaultTitanClientMod)

	TimelineNoteMod = submodule.Make[*TimelineService[patient_encounter.EncounterNoteTimeline]](func(deps *timelineServiceDeps) *TimelineService[patient_encounter.EncounterNoteTimeline] {
		timelineNoteService := newTimelineService[patient_encounter.EncounterNoteTimeline](deps)
		timelineNoteService.HookBeforeAction.RegisterOnCreateFunc(deps.auditLogService.OnTimelineCreate)
		timelineNoteService.HookBeforeAction.RegisterOnUpdateFunc(deps.auditLogService.OnTimelineUpdate)
		timelineNoteService.HookBeforeAction.RegisterOnDeleteFunc(deps.auditLogService.OnTimelineRemove)
		return timelineNoteService
	}, timelineServiceDepsMod)

	TimelineServiceDocumentManagementMod = submodule.Make[*TimelineService[patient_encounter.EncounterDocumentManagement]](func(deps *timelineServiceDeps) *TimelineService[patient_encounter.EncounterDocumentManagement] {
		return newTimelineService[patient_encounter.EncounterDocumentManagement](deps)
	}, timelineServiceDepsMod)

	TimelineServiceArribaMod = submodule.Make[*TimelineService[patient_encounter.EncounterArriba]](func(deps *timelineServiceDeps) *TimelineService[patient_encounter.EncounterArriba] {
		return newTimelineService[patient_encounter.EncounterArriba](deps)
	}, timelineServiceDepsMod)

	TimelineServiceGdtMod = submodule.Make[*TimelineService[patient_encounter.EncounterGDT]](func(deps *timelineServiceDeps) *TimelineService[patient_encounter.EncounterGDT] {
		return newTimelineService[patient_encounter.EncounterGDT](deps)
	}, timelineServiceDepsMod)

	PsychotherapyStatusCanUpdate = []patient_encounter.EncounterPsychotherapyStatus{
		patient_encounter.INPROGRESS,
		patient_encounter.READY_TO_BILL,
		patient_encounter.HAS_BEEN_TAKE_OVER,
	}
)

type (
	TimelineService[T timeline_repo.TimelineEntityConstant] struct {
		timelineRepo                     timeline_repo.TimelineEntityRepo[T]
		notifier                         *timeline.TimelineNotifier
		patientProfileRepo               patient.PatientProfileDefaultRepository
		employeeProfileRepo              employee.EmployeeProfileDefaultRepository
		patientParticipationRepo         patient_participation.PatientParticipationDefaultRepository
		contractService                  *contract_resource.Service
		sdebmRepo                        *sdebm.SdebmRepo
		sdomimRepo                       *sdomim.SdomimRepo
		catalogGoaService                *catalog_goa_service.CatalogGoaService
		PatientOverviewDefaultRepository *patient_overview.Repository
		scheinRepo                       schein_repo.ScheinRepoDefaultRepository
		sdicdService                     *sdicd_service.SdicdService
		sdkrwService                     *TimelineSdkrwService
		timelineEHICRepo                 timeline_repo.TimelineEntityRepo[common.EHICPayload]
		timelineDiagnosisRepo            timeline_repo.TimelineEntityRepo[patient_encounter.EncounterDiagnoseTimeline]
		sdkvService                      sdkvApi.SDKVService
		timelineServiceRepo              timeline_repo.TimelineEntityRepo[patient_encounter.EncounterServiceTimeline]
		FindActiveEMDPByPatientIdFunc    *FuncFindActiveEdmp
		timelinePsychotherapyRepo        timeline_repo.TimelineEntityRepo[patient_encounter.EncounterPsychotherapy]
		rezidivService                   *rezidiv.RezidivList
		validationService                *validation_timeline.TimelineValidationService
		auditLogService                  audit_log_service.AuditLogger
		HookBeforeAction                 *hook.CUDHook[*timeline.EventTimelineCreate, *timeline.EventTimelineUpdate, *timeline.EventTimelineRemove]
		HookAfterAction                  *hook.CUDHook[*timeline.EventTimelineCreate, *timeline.EventTimelineUpdate, *timeline.EventTimelineRemove]
		eauRepo                          *eau_repo.EAURepo
		hpmRestService                   hpm_rest_client.ServiceRest
		typesenseClient                  *typesense.TypesenseGeneric[TimelineSearch]
		getSettingsFn                    settings_service.GetSettingsFn
		bsnrService                      *bsnr_service.BSNRService
		edmpRepo                         *edmp.EDMPRepo
	}
	GetTakeOverDiagnosisRequest struct {
		Type      common.TakeoverDiagnosisType
		PatientId uuid.UUID
		Query     string
		StartDate *time.Time
		EndDate   *time.Time
		ScheinId  *uuid.UUID
	}
	GetTakeOverDiagnosisResponse struct {
		Data []common.TakeOverDiagnosisGroup
	}
	GetLastDocumentedQuarterRequest struct {
		PatientId          uuid.UUID
		Year               int32
		Quarter            int32
		timelineEntityType *common.TimelineEntityType
	}
	FuncFindActiveEdmp                               func(ctx *titan.Context, patientId uuid.UUID) ([]string, error)
	GetTimelinesDiagnoseAndServiceByScheinIdsRequest struct {
		PatientWithScheinIds  []timeline_repo.PatientWithScheinIds
		ExcludeEABServiceCode *bool
	}
	GroupByQuarterRequest struct {
		PatientId           uuid.UUID
		FromDate            *int64
		ToDate              *int64
		TimelineEntityTypes []timeline.ITimelineEntityType
		IsSortByCategory    bool
		IsHistoryMode       bool
		ScheinId            *uuid.UUID
		Keyword             *string
		Year                *int32
		Quarter             *int32
	}
	GroupByQuarterResponse[T timeline_repo.TimelineEntityConstant] struct {
		Timelines     []timeline_repo.GroupByQuarter[T]
		MatchedTokens []string
	}
	GetEncounterByP4ValidationReportRequest struct {
		PatientId  uuid.UUID
		ContractId *string
		Quarter    int32
		Year       int32
	}
	ValidateDiagnoseRequest struct {
		IcdCode   []string
		TypeCheck []common.IcdErrorTypeCheck
	}
	FullTextSearchRequest struct {
		PatientId uuid.UUID
		Keyword   *string
		FromDate  *int64
		ToDate    *int64
	}
	FullTextSearchResponse struct {
		MatchedIds    []uuid.UUID
		MatchedTokens []string
	}
)

func registerNatsHook[T timeline_repo.TimelineEntityConstant](client *titan.Client, timelineService *TimelineService[T]) {
	hook.NewNatsEventWithQueueName[*patient_profile_api.EventPatientProfileChange](
		patient_profile_api.EVENT_PatientProfileChange,
		patient_profile_api.EVENT_PatientProfileChange+"timeline_queue",
		client,
	).RegisterFunc(timelineService.HandlePatientUpdated)
}

var timelineServiceDepsMod = submodule.Make[*timelineServiceDeps](
	newTimelineServiceDeps,
	validation_timeline.TimelineValidationServiceMod,
	sdebm.SDEBMRepoMod,
	sdomim.SdomimRepoMod,
	share.CatalogGoaServiceMod,
	sdicd_service.SdicdServiceMod,
	sdkv_service.ServiceMod,
	contract_resource.ContractServiceMod,
	rezidiv.RezidivRepoMod,
	audit_log_service.AuditLogServiceMod,
	eau_repo.EAURepoMod,
	TimelineSdkrwServiceMod,
	config.GetDefaultTitanClientMod,
	hpm_rest_client.HpmRestServiceMod,
	typesense.TypesenseMod,
	settings_service.GetSettingsFlow,
	bsnr_service.BSNRServiceMod,
	edmp.EDMPRepoMod,
)

type timelineServiceDeps struct {
	validationService    *validation_timeline.TimelineValidationService
	sdebmRepo            *sdebm.SdebmRepo
	sdomimRepo           *sdomim.SdomimRepo
	catalogGoaService    *catalog_goa_service.CatalogGoaService
	sdicdService         *sdicd_service.SdicdService
	sdkvService          *sdkv_service.SDKVService
	contractService      *contract_resource.Service
	rezidivRepo          *rezidiv.RezidivList
	auditLogService      audit_log_service.AuditLogger
	eauRepo              *eau_repo.EAURepo
	timelineSdkrwService *TimelineSdkrwService
	client               *titan.Client
	hpmRestService       hpm_rest_client.ServiceRest
	typesenseClient      *typesense.TypesenseClient
	getSettingsFn        settings_service.GetSettingsFn
	bsnrService          *bsnr_service.BSNRService
	edmpRepo             *edmp.EDMPRepo
}

func newTimelineServiceDeps(
	validationService *validation_timeline.TimelineValidationService,
	sdebmRepo *sdebm.SdebmRepo,
	sdomimRepo *sdomim.SdomimRepo,
	catalogGoaService *catalog_goa_service.CatalogGoaService,
	sdicdService *sdicd_service.SdicdService,
	sdkvService *sdkv_service.SDKVService,
	contractService *contract_resource.Service,
	rezidivRepo *rezidiv.RezidivList,
	auditLogService audit_log_service.AuditLogger,
	eauRepo *eau_repo.EAURepo,
	timelineSdkrwService *TimelineSdkrwService,
	client *titan.Client,
	hpmRestService hpm_rest_client.ServiceRest,
	typesenseClient *typesense.TypesenseClient,
	getSettings settings_service.GetSettingsFn,
	bsnrService *bsnr_service.BSNRService,
	edmpRepo *edmp.EDMPRepo,
) *timelineServiceDeps {
	return &timelineServiceDeps{
		validationService:    validationService,
		sdebmRepo:            sdebmRepo,
		sdomimRepo:           sdomimRepo,
		catalogGoaService:    catalogGoaService,
		sdicdService:         sdicdService,
		sdkvService:          sdkvService,
		contractService:      contractService,
		rezidivRepo:          rezidivRepo,
		auditLogService:      auditLogService,
		eauRepo:              eauRepo,
		timelineSdkrwService: timelineSdkrwService,
		client:               client,
		hpmRestService:       hpmRestService,
		typesenseClient:      typesenseClient,
		getSettingsFn:        getSettings,
		bsnrService:          bsnrService,
		edmpRepo:             edmpRepo,
	}
}

func newTimelineService[T timeline_repo.TimelineEntityConstant](
	timelineServiceDeps *timelineServiceDeps,
) *TimelineService[T] {
	timelineService := &TimelineService[T]{
		timelineRepo:                     timeline_repo.NewTimelineRepoDefaultRepository[T](),
		notifier:                         timeline.NewTimelineNotifier(),
		patientProfileRepo:               patient.NewPatientProfileDefaultRepository(),
		employeeProfileRepo:              employee.NewEmployeeProfileDefaultRepository(),
		patientParticipationRepo:         patient_participation.NewPatientParticipationDefaultRepository(),
		contractService:                  timelineServiceDeps.contractService,
		catalogGoaService:                timelineServiceDeps.catalogGoaService,
		sdebmRepo:                        timelineServiceDeps.sdebmRepo,
		sdomimRepo:                       timelineServiceDeps.sdomimRepo,
		PatientOverviewDefaultRepository: patient_overview.InitPatientOverviewRepository(),
		scheinRepo:                       schein_repo.NewScheinRepoDefaultRepository(),
		sdicdService:                     timelineServiceDeps.sdicdService,
		sdkrwService:                     timelineServiceDeps.timelineSdkrwService,
		timelineEHICRepo:                 timeline_repo.NewTimelineRepoDefaultRepository[common.EHICPayload](),
		timelineDiagnosisRepo:            timeline_repo.NewTimelineRepoDefaultRepository[patient_encounter.EncounterDiagnoseTimeline](),
		timelineServiceRepo:              timeline_repo.NewTimelineRepoDefaultRepository[patient_encounter.EncounterServiceTimeline](),
		sdkvService:                      timelineServiceDeps.sdkvService,
		timelinePsychotherapyRepo:        timeline_repo.NewTimelineRepoDefaultRepository[patient_encounter.EncounterPsychotherapy](),
		rezidivService:                   timelineServiceDeps.rezidivRepo,
		validationService:                timelineServiceDeps.validationService,
		auditLogService:                  timelineServiceDeps.auditLogService,
		HookBeforeAction:                 hook.NewCUDInMemoryHook[*timeline.EventTimelineCreate, *timeline.EventTimelineUpdate, *timeline.EventTimelineRemove](),
		HookAfterAction:                  hook.NewCUDInMemoryHook[*timeline.EventTimelineCreate, *timeline.EventTimelineUpdate, *timeline.EventTimelineRemove](),
		eauRepo:                          timelineServiceDeps.eauRepo,
		hpmRestService:                   timelineServiceDeps.hpmRestService,
		typesenseClient:                  typesense.NewTypesenseGeneric[TimelineSearch](timelineServiceDeps.typesenseClient, "timeline"),
		getSettingsFn:                    timelineServiceDeps.getSettingsFn,
		bsnrService:                      timelineServiceDeps.bsnrService,
		edmpRepo:                         timelineServiceDeps.edmpRepo,
	}

	// NOTE: trigger validation
	timelineService.HookBeforeAction.RegisterOnCreateFunc(timelineServiceDeps.validationService.RunValidateOnCreatingTimeline)
	timelineService.HookBeforeAction.RegisterOnUpdateFunc(timelineServiceDeps.validationService.RunValidateOnUpdatingTimeline)
	timelineService.HookBeforeAction.RegisterOnDeleteFunc(timelineServiceDeps.validationService.RunValidateOnDeletingTimeline)
	registerNatsHook(timelineServiceDeps.client, timelineService)
	return timelineService
}

func (srv *TimelineService[T]) GetCountValidationsByPatientIds(ctx *titan.Context, patientIds []uuid.UUID, startTime, endTime time.Time) (map[uuid.UUID]int64, error) {
	return srv.timelineRepo.GetCountValidationsByPatientIds(ctx, patientIds, startTime, endTime)
}

type DocumentSuggestion struct {
	TimelineId     uuid.UUID
	SuggestionCode string
	SuggestionData *map[string]string
}

func (srv *TimelineService[T]) DocumentSuggestion(ctx *titan.Context, req DocumentSuggestion) error {
	return srv.handleTssSuggestion(ctx, req)
}

func (srv *TimelineService[T]) handleTssSuggestion(ctx *titan.Context, req DocumentSuggestion) error {
	timelineById, err := srv.timelineServiceRepo.FindById(ctx, req.TimelineId)
	if err != nil {
		return fmt.Errorf("cannot find timeline: %w", err)
	}

	if timelineById == nil {
		return nil
	}

	description := ""
	if req.SuggestionData != nil {
		description = (*req.SuggestionData)["description"]
	}
	timelineSuggestion := pkg_copy.CloneTo[timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]](*timelineById)
	timelineSuggestion.Id = nil
	timelineSuggestion.Payload.Code = req.SuggestionCode
	timelineSuggestion.RecentAuditLogs = []timeline_repo.AuditLog{}
	timelineSuggestion.Payload.AuditLog = nil
	timelineSuggestion.Payload.Errors = &[]*patient_encounter.EncounterItemError{}
	timelineSuggestion.Payload.FreeText = fmt.Sprintf("(%s) ", req.SuggestionCode) + description

	createdTimelineServiceSuggestion, err := srv.timelineServiceRepo.Create(ctx, timelineSuggestion)
	if err != nil {
		return fmt.Errorf("cannot create timeline service suggestion: %w", err)
	}

	timelineById.Payload.TimelineServiceSuggestionId = createdTimelineServiceSuggestion.Id
	_, err = srv.timelineServiceRepo.Update(ctx, *timelineById)
	if err != nil {
		return fmt.Errorf("cannot update timeline service suggestion")
	}

	timelineModel := ConvertEntityToModel(*createdTimelineServiceSuggestion)
	if err := srv.NotifyTimelineCreate(ctx, &timeline.EventTimelineCreate{
		PatientId:     timelineById.PatientId,
		TimelineModel: timelineModel,
	}); err != nil {
		return fmt.Errorf("cannot notify timeline create when document suggestion: %w", err)
	}

	return nil
}

func (srv *TimelineService[T]) MarkTreatmentRelevant(ctx *titan.Context, timelineId uuid.UUID) error {
	diagnosisTimeline, err := srv.timelineDiagnosisRepo.FindById(ctx, timelineId)
	if err != nil {
		return fmt.Errorf("cannot find diagnosis timeline: %w", err)
	}
	if diagnosisTimeline == nil || diagnosisTimeline.Id == nil {
		return nil
	}

	if diagnosisTimeline.Payload.Command != "AD" {
		return nil
	}

	return srv.timelineDiagnosisRepo.MarkTreatmentRelevantOnDiagnosis(ctx, diagnosisTimeline)
}

func (srv *TimelineService[T]) UpdateEncounterCaseForServiceEntries(ctx *titan.Context, timelineId uuid.UUID) error {
	serviceTimeline, err := srv.timelineServiceRepo.FindById(ctx, timelineId)
	if err != nil {
		return fmt.Errorf("cannot find service timeline: %w", err)
	}
	if serviceTimeline == nil || serviceTimeline.Id == nil {
		return nil
	}

	return srv.timelineServiceRepo.UpdateEncounterCaseForServiceEntries(ctx, serviceTimeline)
}

func (srv *TimelineService[T]) handleGoaServicePrice(ctx *titan.Context, entity *timeline_repo.TimelineEntity[T]) error {
	payloadAsAny := any(entity.Payload)
	goaService, ok := payloadAsAny.(patient_encounter.EncounterGoaService)
	if !ok {
		return fmt.Errorf("invalid payload type: %T", entity.Payload)
	}
	goaCatalogs, err := srv.catalogGoaService.GetGoaCatalogByGoaNumber(ctx, &catalog_goa_api.GetGoaCatalogByNumberRequest{
		GoaNumber: goaService.Code,
	})
	if err != nil {
		return errors.WithMessage(err, "cannot get goa catalog")
	}
	hasData := goaCatalogs != nil && goaCatalogs.Goa != nil
	if hasData {
		goaService.OriginalPrice = &goaCatalogs.Goa.Price
		entity.Payload = any(goaService).(T)
	}
	return nil
}

func (srv *TimelineService[T]) Create(ctx *titan.Context, entity timeline_repo.TimelineEntity[T]) (*timeline_repo.TimelineEntity[T], error) {
	ctx, span := titan.SpanContext(ctx, ctx.GetHTTPHeader(), "TimelineService.Create")
	defer span.End()
	if entity.Type == common.TimelineEntityType_Service_GOA {
		err := srv.handleGoaServicePrice(ctx, &entity)
		if err != nil {
			return nil, err
		}
	}
	timelineItem, err := srv.timelineRepo.Create(ctx, entity)
	if err != nil {
		return nil, err
	}
	timelineModel := ConvertEntityToModel(*timelineItem)

	// NOTE: auto create, update, replace... timeline entries
	err = srv.doAutoActionWhenDocumenting(ctx, &timelineModel)
	if err != nil {
		return nil, err
	}

	if err := srv.NotifyTimelineCreate(ctx, &timeline.EventTimelineCreate{
		PatientId:     timelineItem.PatientId,
		TimelineModel: timelineModel,
	}); err != nil {
		return nil, err
	}

	return timelineItem, nil
}

func (srv *TimelineService[T]) Document88130(ctx *titan.Context, request *timeline.Document88130Request) (*timeline.Document88130Response, error) {
	timelineItem, err := srv.FindById(ctx, request.ServiceEntryId)
	if err != nil {
		return nil, err
	}
	if timelineItem == nil {
		return nil, nil
	}

	if len(timelineItem.ScheinIds) == 0 {
		return nil, nil
	}
	timelineModel := ConvertEntityToModel(*timelineItem)

	if timelineModel.EncounterServiceTimeline == nil {
		return nil, nil
	}
	var scheinId *uuid.UUID = nil
	takeoverDiagnosis := []common.TimelineModel{}
	if len(request.TimelineModel.ScheinIds) == 0 {
		takeoverDiagnosisEntities, err := srv.getDiagnosisUnique(ctx, timelineModel.PatientId)
		if err != nil {
			return nil, err
		}
		for _, v := range takeoverDiagnosisEntities {
			takeoverDiagnosis = append(takeoverDiagnosis, ConvertEntityToModel(v))
		}
		scheinCreated, err := srv.AutoCreateScheinAndTakeOverDiagnoses(ctx, *timelineModel.Id, timelineModel.ScheinIds[0], true)
		if err != nil {
			return nil, err
		}
		if scheinCreated == nil {
			return nil, nil
		}
		scheinId = scheinCreated.Id
		request.TimelineModel.ScheinIds = append(request.TimelineModel.ScheinIds, *scheinCreated.Id)
		scheinWithMainGroup := []*domains_api_common.ScheinWithMainGroup{}
		scheinWithMainGroup = append(scheinWithMainGroup, &domains_api_common.ScheinWithMainGroup{
			Group:    domains_api_common.MainGroup(scheinCreated.Schein.ScheinMainGroup),
			ScheinId: scheinCreated.Id,
		})
		request.TimelineModel.EncounterServiceTimeline.Scheins = &scheinWithMainGroup
	}

	timelineServiceEncounter, err := TimelineServiceEncounterMod.SafeResolve()
	if err != nil {
		return nil, fmt.Errorf("cannot get timeline service encounter: %w", err)
	}
	timelineService := pkg_copy.CloneTo[timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]](request.TimelineModel)
	timelineService.Payload = *request.TimelineModel.EncounterServiceTimeline
	timelineService.ChainId = timelineItem.ChainId
	timlineService88130Created, err := timelineServiceEncounter.Create(ctx, timelineService)
	if err != nil {
		return nil, fmt.Errorf("cannot create timeline service: %w", err)
	}
	timelineService88130CreatedTimelineModel := ConvertEntityToModel(*timlineService88130Created)
	err = srv.MarkPyschotherapyCompleted(ctx, &timelineModel, &timelineService88130CreatedTimelineModel, timelineItem.ScheinIds[0])
	if err != nil {
		return nil, err
	}
	return &timeline.Document88130Response{
		TakeoverDiagnosis: takeoverDiagnosis,
		ScheinId:          scheinId,
		ServiceId:         *timlineService88130Created.Id,
	}, nil
}

// Get take over diagnosis for schein and forms
func (srv *TimelineService[T]) GetTakeOverDiagnosis(ctx *titan.Context, request *GetTakeOverDiagnosisRequest) (*GetTakeOverDiagnosisResponse, error) {
	res, err := srv.timelineRepo.GetTakeOverDiagnosis(ctx, &timeline_repo.GetTakeOverDiagnosisRequest{
		Type:      request.Type,
		PatientId: request.PatientId,
		Query:     request.Query,
		StartDate: request.StartDate,
		EndDate:   request.EndDate,
		ScheinId:  request.ScheinId,
	})
	if err != nil {
		return nil, err
	}

	if res == nil {
		return nil, nil
	}

	currentDiagnosis := []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{}
	if request.ScheinId != nil {
		currentDiagnosis, err = srv.timelineDiagnosisRepo.FindDiagnosisByPatientIdAndScheinIds(ctx, request.PatientId, []uuid.UUID{*request.ScheinId})
		if err != nil {
			return nil, err
		}
	}

	data := []common.TimelineModel{}
	if len(currentDiagnosis) > 0 {
		for _, item := range res {
			dModel := ConvertEntityToModel(item)
			match := slice.FindOne(currentDiagnosis, func(c timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) bool {
				return c.Payload.IsDuplicated(dModel.EncounterDiagnoseTimeline)
			})
			if match != nil {
				continue
			}
			data = append(data, dModel)
		}
	} else {
		data = slice.Map(res, ConvertEntityToModel)
	}

	takeOverDiagnosisGroup := GroupTakeOverDiagnosis(data)
	return &GetTakeOverDiagnosisResponse{
		Data: takeOverDiagnosisGroup,
	}, nil
}

func (srv *TimelineService[T]) UpdateTerminalServiceCodeForPsychotherapy(ctx *titan.Context, model *common.TimelineModel) error {
	if len(model.ScheinIds) == 0 {
		return nil
	}
	schein, err := srv.scheinRepo.FindById(ctx, model.ScheinIds[0])
	if err != nil {
		return err
	}
	if schein != nil {
		return nil
	}
	return nil
}

func (srv *TimelineService[T]) UpdateDiagnosesScheinId(ctx *titan.Context, ids []uuid.UUID, newScheinId uuid.UUID) error {
	return srv.timelineDiagnosisRepo.UpdateDiagnosesScheinId(ctx, ids, newScheinId)
}

func (srv *TimelineService[T]) FindDiagnosisDMPSuggestions(ctx *titan.Context, patientIds []uuid.UUID) (map[uuid.UUID][]timeline_repo.TimelineEntity[T], error) {
	timelineItem, err := srv.timelineRepo.FindDiagnosisDMPs(ctx, patientIds)
	if err != nil {
		return nil, fmt.Errorf("cannot find FindDiagnosisDMPs items: %w", err)
	}

	result := make(map[uuid.UUID][]timeline_repo.TimelineEntity[T])
	for _, item := range timelineItem {
		patientId := item.PatientId
		result[patientId] = append(result[patientId], item)
	}

	return result, nil
}

func (srv *TimelineService[T]) CreateEHIC(ctx *titan.Context, request timeline_repo.TimelineEntity[common.EHICPayload]) (*timeline_repo.TimelineEntity[common.EHICPayload], error) {
	res, err := srv.timelineEHICRepo.Create(ctx, request)
	if err != nil {
		ctx.Logger().Error("create timeline EHIC error",
			"error", err,
		)
		return nil, err
	}
	if res == nil || res.Id == nil {
		return nil, nil
	}
	return res, nil
}

func (srv *TimelineService[T]) CountByScheinId(ctx *titan.Context, patientId, scheinId uuid.UUID) (int64, error) {
	res, err := srv.timelineRepo.CountByScheinId(ctx, patientId, scheinId)
	if err != nil {
		return -1, err
	}
	return res, nil
}

func (srv *TimelineService[T]) HandlePatientUpdated(ctx *titan.Context, request *patient_profile_api.EventPatientProfileChange) error {
	switch request.EventName {
	case patient_profile_api.EventName_UpdatePatientProfile, patient_profile_api.EventName_CreatedPatient:
		return srv.handlePatientUpdate(ctx, request)
	case patient_profile_api.EventName_DeletedPatient:
		return srv.deleteByPatientId(ctx, *request.PatientId)
	default:
		return nil
	}
}

func (srv *TimelineService[T]) deleteByPatientId(ctx *titan.Context, patientId uuid.UUID) error {
	return srv.timelineRepo.DeleteByPatientId(ctx, patientId)
}

func (srv *TimelineService[T]) handlePatientUpdate(ctx *titan.Context, request *patient_profile_api.EventPatientProfileChange) error {
	if (request.EventName == patient_profile_api.EventName_UpdatePatientProfile ||
		request.EventName == patient_profile_api.EventName_CreatedPatient) &&
		request.ShouldCreateNewEHIC {
		if request.PatientInfo.EuropeanHealthInsurance.HasEuropeanHealthInsuranceCard && util.GetPointerValue(request.PatientInfo.EuropeanHealthInsurance.FormSetting) != "" {
			entity, err := srv.CreateEHIC(ctx, timeline_repo.TimelineEntity[common.EHICPayload]{
				PatientId:         *request.PatientId,
				TreatmentDoctorId: request.PatientInfo.EuropeanHealthInsurance.TreatmentDoctorId,
				Payload: common.EHICPayload{
					FormSetting: request.PatientInfo.EuropeanHealthInsurance.FormSetting,
					Language:    request.PatientInfo.EuropeanHealthInsurance.Language,
					Status:      request.PatientInfo.EuropeanHealthInsurance.Status,
				},
			})
			if err != nil {
				return err
			}
			if entity != nil {
				err = srv.NotifyTimelineCreate(ctx, &timeline.EventTimelineCreate{
					PatientId:     entity.PatientId,
					TimelineModel: ConvertEntityToModel(*entity),
				})
				return err
			}
		}
	}
	return nil
}

func (srv *TimelineService[T]) CreateMany(ctx *titan.Context, models []timeline_repo.TimelineEntity[T]) ([]timeline_repo.TimelineEntity[T], error) {
	for i, model := range models {
		if model.Type == common.TimelineEntityType_Service_GOA {
			err := srv.handleGoaServicePrice(ctx, &models[i])
			if err != nil {
				return nil, err
			}
		}
	}
	timelineItems, err := srv.timelineRepo.CreateMany(ctx, models)
	if err != nil {
		return nil, err
	}

	for _, timelineItem := range timelineItems {
		timelineModel := ConvertEntityToModel(timelineItem)
		err = srv.doAutoActionWhenDocumenting(ctx, &timelineModel)
		if err != nil {
			return nil, err
		}
		err = srv.NotifyTimelineCreate(ctx, &timeline.EventTimelineCreate{
			PatientId:     timelineItem.PatientId,
			TimelineModel: timelineModel,
		})
		if err != nil {
			return nil, err
		}
	}

	return timelineItems, nil
}

func (srv *TimelineService[T]) GetDiagnoseUnique(ctx *titan.Context, request timeline_repo.GetDiagnoseUniqueRequest) ([]patient_encounter.EncounterDiagnoseTimeline, error) {
	return srv.timelineRepo.GetDiagnoseUnique(ctx, request)
}

func (srv *TimelineService[T]) GetDiagnoseByPatient(ctx *titan.Context, patientId uuid.UUID) ([]timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline], error) {
	return srv.timelineRepo.GetDiagnoseByPatient(ctx, patientId)
}

func (srv *TimelineService[T]) GetByPatientId(ctx *titan.Context, patientId uuid.UUID) ([]timeline_repo.TimelineEntity[T], error) {
	return srv.timelineRepo.GetByPatientId(ctx, patientId)
}

func (srv *TimelineService[T]) GetServiceSuggestionTSSByQuarter(ctx *titan.Context, quarter, year int) ([]timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline], error) {
	return srv.timelineRepo.GetServiceSuggestionTSSByQuarter(ctx, quarter, year)
}

func (srv *TimelineService[T]) RemoveDiagnoseErrorForEDMP(ctx *titan.Context, patientId uuid.UUID, dmpLabelingValue string) error {
	diagnoses, err := srv.timelineDiagnosisRepo.GetDiagnoseByPatient(ctx, patientId)
	if err != nil {
		return fmt.Errorf("cannot get diagnose by patient: %w", err)
	}
	if len(diagnoses) == 0 {
		return nil
	}
	var diagnosisUpdate []*timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]
	for i := range diagnoses {
		dmp := edmp_common.GetDMPByICDCode(diagnoses[i].Payload.Code)
		if dmp == nil || dmp.Value != dmpLabelingValue {
			continue
		}
		if diagnoses[i].Payload.Errors != nil && len(*diagnoses[i].Payload.Errors) > 0 {
			diagnoseErrorWithoutEDMPSuggestion := slice.Filter(*diagnoses[i].Payload.Errors, func(err *patient_encounter.EncounterItemError) bool {
				return err.ErrorCode != string(error_code.ErrorCode_Validation_EDMPSuggestion)
			})
			diagnoses[i].Payload.Errors = &diagnoseErrorWithoutEDMPSuggestion
			diagnosisUpdate = append(diagnosisUpdate, &diagnoses[i])
		}
	}

	if len(diagnosisUpdate) > 0 {
		updated, err := srv.timelineDiagnosisRepo.UpdateMany(ctx, diagnosisUpdate)
		if err != nil {
			return fmt.Errorf("cannot update diagnose: %w", err)
		}
		if len(updated) == 0 {
			return nil
		}
		eventTimelineUpdates := slice.Map(updated, func(v timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) *timeline.EventTimelineUpdate {
			return &timeline.EventTimelineUpdate{
				PatientId:                           patientId,
				SkipUpdateEndDatePermanentDiagnoses: true,
				TimelineModel:                       ConvertEntityToModel(v),
			}
		})
		if err := srv.NotifyTimelineUpdate(ctx, eventTimelineUpdates...); err != nil {
			return fmt.Errorf("cannot notify timeline update: %w", err)
		}
	}
	return nil
}

func (srv *TimelineService[T]) UpdateDiagnoseErrorForEDMP(ctx *titan.Context, patientId uuid.UUID, dmpLabelingValue string) error {
	diagnoses, err := srv.timelineDiagnosisRepo.GetDiagnoseByPatient(ctx, patientId)
	if err != nil {
		return fmt.Errorf("cannot get diagnose by patient: %w", err)
	}
	if len(diagnoses) == 0 {
		return nil
	}
	edmpSuggestion := patient_encounter.EncounterItemError{
		Type:            patient_encounter.EncounterItemErrorType_info,
		ErrorCode:       string(error_code.ErrorCode_Validation_EDMPSuggestion),
		ValidationLevel: patient_encounter.ValidationLevelTimeline,
	}

	var diagnosisUpdate []*timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]
	for i := range diagnoses {
		dmp := edmp_common.GetDMPByICDCode(diagnoses[i].Payload.Code)
		if dmp == nil || dmp.Value != dmpLabelingValue {
			continue
		}

		if diagnoses[i].Payload.Errors == nil {
			diagnoses[i].Payload.Errors = &[]*patient_encounter.EncounterItemError{
				&edmpSuggestion,
			}
			diagnosisUpdate = append(diagnosisUpdate, &diagnoses[i])
		} else {
			hasErrorSuggestion := slice.Any(*diagnoses[i].Payload.Errors, func(eie *patient_encounter.EncounterItemError) bool {
				return eie.ErrorCode == string(error_code.ErrorCode_Validation_EDMPSuggestion)
			})
			if hasErrorSuggestion {
				continue
			}
			withEDMPSuggestion := append(*diagnoses[i].Payload.Errors, &edmpSuggestion)
			diagnoses[i].Payload.Errors = &withEDMPSuggestion
			diagnosisUpdate = append(diagnosisUpdate, &diagnoses[i])
		}
	}

	if len(diagnosisUpdate) > 0 {
		updated, err := srv.timelineDiagnosisRepo.UpdateMany(ctx, diagnosisUpdate)
		if err != nil {
			return fmt.Errorf("cannot update diagnose: %w", err)
		}
		if len(updated) == 0 {
			return nil
		}
		eventTimelineUpdates := slice.Map(updated, func(v timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) *timeline.EventTimelineUpdate {
			return &timeline.EventTimelineUpdate{
				PatientId:                           patientId,
				SkipUpdateEndDatePermanentDiagnoses: true,
				TimelineModel:                       ConvertEntityToModel(v),
			}
		})
		if err := srv.NotifyTimelineUpdate(ctx, eventTimelineUpdates...); err != nil {
			return fmt.Errorf("cannot notify timeline update: %w", err)
		}
	}
	return nil
}

func (srv *TimelineService[T]) Get(ctx *titan.Context, request timeline_repo.GetRequest) ([]timeline_repo.TimelineEntity[T], error) {
	return srv.timelineRepo.Get(ctx, request)
}

func (srv *TimelineService[T]) GroupByQuarter(ctx *titan.Context, request GroupByQuarterRequest) (GroupByQuarterResponse[T], error) {
	var matchedTokens []string
	shouldAddCustomizeType := false
	for _, timelineEntityType := range request.TimelineEntityTypes {
		if timelineEntityType.TimelineEntityType == common.TimelineEntityType_Customize && timelineEntityType.Command == nil {
			shouldAddCustomizeType = true
		}
	}
	groupByQuarterRequest := timeline_repo.GroupByQuarterRequest{
		PatientId: request.PatientId,
		TimelineEntityType: slice.Map(slice.Filter(request.TimelineEntityTypes, func(t timeline.ITimelineEntityType) bool {
			return t.TimelineEntityType != common.TimelineEntityType_Customize
		}), func(t timeline.ITimelineEntityType) common.TimelineEntityType { return t.TimelineEntityType }),
		IsSortByCategory: request.IsSortByCategory,
		IsHistoryMode:    request.IsHistoryMode,
		ScheinId:         request.ScheinId,
		Quarter:          request.Quarter,
		Year:             request.Year,
		CustomizeCommands: function.Do(func() *[]string {
			customizeTypes := slice.Filter(request.TimelineEntityTypes, func(t timeline.ITimelineEntityType) bool {
				return t.TimelineEntityType == common.TimelineEntityType_Customize && t.Command != nil
			})
			if len(customizeTypes) == 0 {
				return nil
			}
			return util.NewPointer(slice.Map(customizeTypes, func(t timeline.ITimelineEntityType) string { return *t.Command }))
		}),
	}
	if shouldAddCustomizeType && groupByQuarterRequest.CustomizeCommands == nil {
		groupByQuarterRequest.TimelineEntityType = append(groupByQuarterRequest.TimelineEntityType, common.TimelineEntityType_Customize)
	}
	if request.FromDate != nil {
		groupByQuarterRequest.FromDate = util.NewPointer(util.ConvertMillisecondsToTime(*request.FromDate, ctx.RequestTimeZone()))
	}

	if request.ToDate != nil {
		groupByQuarterRequest.ToDate = util.NewPointer(util.ConvertMillisecondsToTime(*request.ToDate, ctx.RequestTimeZone()))
	}

	if strings.TrimSpace(util.GetPointerValue(request.Keyword)) != "" {
		res, err := srv.fullTextSearch(ctx, FullTextSearchRequest{
			Keyword:   request.Keyword,
			PatientId: request.PatientId,
			FromDate:  request.FromDate,
			ToDate:    request.ToDate,
		})
		if err != nil || len(res.MatchedTokens) == 0 {
			return GroupByQuarterResponse[T]{}, err
		}
		groupByQuarterRequest.IncludedIds = res.MatchedIds
		matchedTokens = res.MatchedTokens
	}

	resp, err := srv.getSettingsFn(ctx, settings_service.SettingsRequest{
		Feature: settings_common.SettingsFeatures_TimelineEntryColor,
	})
	if err != nil {
		return GroupByQuarterResponse[T]{}, err
	}

	settings := map[string]int32{}
	for k, v := range resp.Settings {
		setting := settings_api.TimelineEntrySetting{
			Bg:        settings_service.DEFAULT_BACKGROUND,
			Text:      settings_service.DEFAULT_TEXT,
			SortOrder: 0,
		}
		err := json.Unmarshal([]byte(v), &setting)
		if err != nil {
			continue
		}
		settings[k] = setting.SortOrder
	}

	groupByQuarterRequest.TimelineEntrySettings = settings
	result, err := srv.timelineRepo.GroupByQuarter(ctx, groupByQuarterRequest)
	if err != nil || len(result) == 0 {
		return GroupByQuarterResponse[T]{}, err
	}

	return GroupByQuarterResponse[T]{
		Timelines:     result,
		MatchedTokens: matchedTokens,
	}, err
}

func (srv *TimelineService[T]) CheckIsVSST785(ctx *titan.Context, request timeline.CheckIsVSST785Request) (*timeline.CheckIsVSST785Response, error) {
	now := util.NowUnixMillis(ctx)
	yearQuarter := util.ToYearQuarter(now)
	resp := timeline.CheckIsVSST785Response{
		IsVSST785: false,
	}
	contract := srv.contractService.GetContractDetailById(request.ContractId)
	if contract != nil && contract.CheckExistAnforderung(model.ICheckExistAnforderung{
		ComplianceIds: []string{"VSST785"},
		CheckTime:     now,
		IkNumber:      request.IkNumber,
	}) {
		hpmConfig, err := srv.bsnrService.GetHpmConfig(ctx, ctx.UserInfo().BsnrId)
		if err != nil {
			return nil, err
		}
		hpmRestService := hpm_rest_client.NewServiceRestWithEndpoint(hpmConfig.Endpoint)
		indicators, err := hpmRestService.GetIndicatorActiveIngredients(ctx, contract.Id)
		if err != nil {
			return nil, err
		}
		if len(indicators) == 0 {
			return &resp, nil
		}
		codes := []string{}
		for _, i := range indicators {
			if i.AtcCode == nil {
				continue
			}
			if i.DiagnoseCode == nil {
				continue
			}
			if slice.Any(request.AtcCodes, func(c string) bool {
				return strings.HasPrefix(c, *i.AtcCode)
			}) {
				codes = append(codes, *i.DiagnoseCode)
			}
		}
		if len(codes) > 0 {
			isHasDiagnoses, err := srv.timelineRepo.CheckExistDiagnoseInListCodes(ctx, request.PatientId, yearQuarter, codes)
			if err != nil {
				return nil, err
			}
			resp.IsVSST785 = !isHasDiagnoses
		}
	}
	return &resp, nil
}

func (srv *TimelineService[T]) FindById(ctx *titan.Context, id uuid.UUID) (*timeline_repo.TimelineEntity[T], error) {
	return srv.timelineRepo.FindById(ctx, id)
}

func (srv *TimelineService[T]) FindByIds(ctx *titan.Context, ids []uuid.UUID) ([]timeline_repo.TimelineEntity[T], error) {
	return srv.timelineRepo.FindByIds(ctx, ids)
}

func (srv *TimelineService[T]) FindByIdsNotFilterDelete(ctx *titan.Context, ids []uuid.UUID) ([]timeline_repo.TimelineEntity[T], error) {
	return srv.timelineRepo.FindByIdsNotFilterDelete(ctx, ids)
}

func (srv *TimelineService[T]) FindByIdNotFilterDelete(ctx *titan.Context, id uuid.UUID) (*timeline_repo.TimelineEntity[T], error) {
	return srv.timelineRepo.FindByIdNotFilterDelete(ctx, id)
}

func (srv *TimelineService[T]) UpdateByUser(ctx *titan.Context, id uuid.UUID) (*timeline_repo.TimelineEntity[T], error) {
	return srv.timelineRepo.FindByIdNotFilterDelete(ctx, id)
}

func (srv *TimelineService[T]) UpdateDoctorLetterStatusByPayloadId(ctx *titan.Context, id uuid.UUID, status qes_common.DocumentStatus) (*common.TimelineModel, error) {
	res, err := srv.timelineRepo.UpdateDoctorLetterStatusByPayloadId(ctx, id, status)
	if err != nil {
		return nil, err
	}

	if res == nil {
		return nil, errors.New("cannot update doctor letter status")
	}

	model := ConvertEntityToModel(*res)
	return &model, nil
}

func (srv *TimelineService[T]) UpdatePsychotherapyStatusByIds(ctx *titan.Context, ids []uuid.UUID, statusesFrom []patient_encounter.EncounterPsychotherapyStatus, statusTo patient_encounter.EncounterPsychotherapyStatus) ([]timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy], error) {
	return srv.timelinePsychotherapyRepo.UpdatePsychotherapyStatusByIds(ctx, &timeline_repo.UpdatePsychotherapyStatusByIdsRequest{
		Ids:          ids,
		StatusesFrom: statusesFrom,
		StatusTo:     statusTo,
	})
}

// UpdatePsychotherapy

type UpdatePsychotherapyRequest struct {
	PatientId           uuid.UUID
	ScheinId            uuid.UUID
	IsDelete            bool
	Psychotherapy       []schein_common.Psychotherapy
	Ps4234              *bool
	CachedPsychotherapy []schein_common.Psychotherapy
	ScheinUpdated       schein_repo.ScheinRepo
}

func (srv *TimelineService[T]) UpdateTakeoverPsychotherapy(
	ctx *titan.Context,
	takeOverId uuid.UUID,
	payload *patient_encounter.EncounterPsychotherapy,
	p *schein_common.Psychotherapy,
) error {
	takeOverPsychotherapyEntry, err := srv.timelinePsychotherapyRepo.FindById(ctx, takeOverId)
	if err != nil {
		return err
	}
	if takeOverPsychotherapyEntry != nil && takeOverPsychotherapyEntry.Payload.Status == patient_encounter.HAS_BEEN_TAKE_OVER {
		payload.Entries = takeOverPsychotherapyEntry.Payload.Entries
		takeOverPsychotherapyEntry.Payload.Entries = takeOverPsychotherapyEntry.Payload.MappingEntriesApprovalForTakeover(p)
		takeOverPsychotherapyEntry.Payload.Status = patient_encounter.INPROGRESS
		_, err := srv.timelinePsychotherapyRepo.Update(ctx, *takeOverPsychotherapyEntry)
		if err != nil {
			return err
		}
	}
	return nil
}

func (srv *TimelineService[T]) UpdatePsychotherapy(ctx *titan.Context, req *UpdatePsychotherapyRequest) error {
	entries, err := srv.timelinePsychotherapyRepo.GetPsychotherapy(ctx, &timeline_repo.GetPsychotherapyRequest{
		ScheinIds: []uuid.UUID{req.ScheinId},
		PatientId: req.PatientId,
	})
	if err != nil {
		return fmt.Errorf("cannot find timeline psychotherapy by schein ids: %w", err)
	}

	if len(entries) == 0 {
		err := srv.HandlePsychotherapies(ctx, &req.ScheinUpdated)
		return err
	}

	if len(req.Psychotherapy) == 0 || (req.Ps4234 == nil || (req.Ps4234 != nil && !*req.Ps4234)) || req.IsDelete {
		return srv.DeletePsychotherapyByIds(ctx, slice.Map(entries, func(t timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy]) uuid.UUID { return *t.Id }))
	}

	quarterYear := req.ScheinUpdated.GetYearQuarter()
	timelines, err := srv.timelineServiceRepo.GetPsychotherapyByQuarter(ctx, quarterYear)
	if err != nil {
		return fmt.Errorf("cannot get timelines by ids: %w", err)
	}

	if len(timelines) == 0 {
		return nil
	}

	isExistEntryID := []uuid.UUID{}
	deleteEntriesIds := []uuid.UUID{}
	takeOverPsychotherapyIds := []uuid.UUID{}
	ignoreApprovalIds := []uuid.UUID{}
	newApprovalIds := []uuid.UUID{}
	psychotherapyNeedToUpdate := []schein_common.Psychotherapy{}
	for _, entry := range entries {
		isExistEntryID = append(isExistEntryID, *entry.Id)
		updatedPsychotherapy := slice.FindOne(req.Psychotherapy, func(p schein_common.Psychotherapy) bool {
			return p.Id.String() == entry.Id.String()
		})
		cachedPsychotherapy := slice.FindOne(req.CachedPsychotherapy, func(p schein_common.Psychotherapy) bool {
			return p.Id.String() == entry.Id.String()
		})
		if updatedPsychotherapy != nil && cachedPsychotherapy != nil && reflect.DeepEqual(*updatedPsychotherapy, *cachedPsychotherapy) {
			continue
		}
		if updatedPsychotherapy == nil {
			continue
		}
		if updatedPsychotherapy.TakeOverId != nil {
			takeOverPsychotherapyIds = append(takeOverPsychotherapyIds, *updatedPsychotherapy.TakeOverId)
		}
		psychotherapy := slice.FindOne(req.Psychotherapy, func(p schein_common.Psychotherapy) bool {
			return p.Id.String() == entry.Id.String()
		})
		if psychotherapy == nil {
			deleteEntriesIds = append(deleteEntriesIds, *entry.Id)
			continue
		}
		cached := slice.FindOne(req.CachedPsychotherapy, func(p schein_common.Psychotherapy) bool {
			return p.Id.String() == entry.Id.String()
		})
		cachedEntries := entry.Payload.Entries
		entry.Payload.ConvertPsychotherapy(*psychotherapy, &req.ScheinId)
		newEntries, unassignApproval := srv.updateEntriesInPsychotherapyEntry(cachedEntries, entry.Payload.Entries)
		entry.Payload.Entries = newEntries
		yearQuarter := req.ScheinUpdated.GetYearQuarter()
		entry.SelectedDate = util.ConvertLastDateQuarterUTC(ctx, yearQuarter.Quarter, yearQuarter.Year)
		if cached != nil {
			if reflect.DeepEqual(cached.GroupServicesCode, psychotherapy.GroupServicesCode) || reflect.DeepEqual(cached.GroupCareGiver, psychotherapy.GroupCareGiver) || reflect.DeepEqual(cached.GroupServicesCodeBefore2017, psychotherapy.GroupServicesCodeBefore2017) {
				for k := range entry.Payload.Entries {
					e, ok := cachedEntries[k]
					v := entry.Payload.Entries[k]
					if ok {
						if len(e.EntryIds) > int(v.AmountBilled) {
							entry.Payload.Entries[k].EntryIds = e.EntryIds[:v.AmountBilled]
							deleteEntriesIds = append(deleteEntriesIds, e.EntryIds[v.AmountBilled:]...)
						} else {
							entry.Payload.Entries[k].EntryIds = e.EntryIds
						}
					}
					if cached.Ps4235 != nil && psychotherapy.Ps4235 != nil && *cached.Ps4235 != *psychotherapy.Ps4235 {
						filtered := slice.Filter(timelines, func(te timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) bool {
							return te.Id != nil && slice.Contains(entry.Payload.Entries[k].EntryIds, *te.Id)
						})
						filteredCorrect := []timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{}
						ignoreApprovals := []timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{}
						for _, t := range filtered {
							if t.SelectedDate.After(util.ConvertMillisecondsToTime(*psychotherapy.Ps4235)) {
								filteredCorrect = append(filteredCorrect, t)
							} else {
								ignoreApprovals = append(ignoreApprovals, t)
							}
						}
						ignoreApprovalIds = append(ignoreApprovalIds, slice.Map(ignoreApprovals, func(t timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) uuid.UUID {
							return *t.Id
						})...)
						entry.Payload.Entries[k].AmountBilled -= int32(len(ignoreApprovals))
						entry.Payload.Entries[k].EntryIds = slice.Map(filteredCorrect, func(t timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) uuid.UUID {
							return *t.Id
						})

						filteredTimelineCanApproval := slice.Filter(timelines, func(te timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) bool {
							converted := ConvertEntityToModel(te)
							return te.Id != nil &&
								!slice.Contains(entry.Payload.Entries[k].EntryIds, *te.Id) &&
								!slice.Contains(ignoreApprovalIds, *te.Id) &&
								!slice.Contains(newApprovalIds, *te.Id) &&
								te.SelectedDate.After(util.ConvertMillisecondsToTime(*psychotherapy.Ps4235)) &&
								converted.EncounterServiceTimeline != nil &&
								converted.EncounterServiceTimeline.Code == k
						})
						if v.AmountApproval == nil {
							// Normal case
							totalApproval := entry.Payload.GetEntryApprovalIdsWithoutTerminalId()
							if int32(len(totalApproval)) < entry.Payload.AmountApproval {
								amountTimelineCanAssign := entry.Payload.AmountApproval - int32(len(totalApproval))
								if len(filteredTimelineCanApproval) < int(amountTimelineCanAssign) {
									entry.Payload.Entries[k].EntryIds = append(
										entry.Payload.Entries[k].EntryIds, slice.Map(filteredTimelineCanApproval,
											func(t timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) uuid.UUID {
												return *t.Id
											})...,
									)
									entry.Payload.Entries[k].AmountBilled += int32(len(filteredTimelineCanApproval))
									newApprovalIds = append(newApprovalIds, slice.Map(filteredTimelineCanApproval, func(t timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) uuid.UUID {
										return *t.Id
									})...)
								} else {
									newAssignTimelines := filteredTimelineCanApproval[:amountTimelineCanAssign]
									entry.Payload.Entries[k].EntryIds = append(
										entry.Payload.Entries[k].EntryIds, slice.Map(newAssignTimelines,
											func(t timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) uuid.UUID {
												return *t.Id
											})...,
									)
									entry.Payload.Entries[k].AmountBilled += int32(len(newAssignTimelines))
									newApprovalIds = append(newApprovalIds, slice.Map(newAssignTimelines, func(t timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) uuid.UUID {
										return *t.Id
									})...)
								}
							}
						} else if *v.AmountApproval > v.AmountBilled {
							amountTimelineCanAssign := *v.AmountApproval - v.AmountBilled
							if len(filteredTimelineCanApproval) < int(amountTimelineCanAssign) {
								entry.Payload.Entries[k].EntryIds = append(
									entry.Payload.Entries[k].EntryIds, slice.Map(filteredTimelineCanApproval,
										func(t timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) uuid.UUID {
											return *t.Id
										})...,
								)
								entry.Payload.Entries[k].AmountBilled += int32(len(filteredTimelineCanApproval))
								newApprovalIds = append(newApprovalIds, slice.Map(filteredTimelineCanApproval, func(t timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) uuid.UUID {
									return *t.Id
								})...)
							} else {
								newAssignTimelines := filteredTimelineCanApproval[:amountTimelineCanAssign]
								entry.Payload.Entries[k].EntryIds = append(
									entry.Payload.Entries[k].EntryIds, slice.Map(newAssignTimelines,
										func(t timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) uuid.UUID {
											return *t.Id
										})...,
								)
								entry.Payload.Entries[k].AmountBilled += int32(len(newAssignTimelines))
								newApprovalIds = append(newApprovalIds, slice.Map(newAssignTimelines, func(t timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) uuid.UUID {
									return *t.Id
								})...)
							}
						}

						updatePsychotherapy := *psychotherapy
						isUpdated := false
						updatePsychotherapy.GroupServicesCode = slice.Map(updatePsychotherapy.GroupServicesCode, func(t schein_common.GroupServicesCode) schein_common.GroupServicesCode {
							if t.ServiceCode == k {
								t.AmountBilled = t.AmountBilled - int32(len(ignoreApprovals)) + int32(len(filteredTimelineCanApproval))
								if t.AmountBilled < 0 {
									t.AmountBilled = 0
								}
								isUpdated = true
							}
							return t
						})
						if !isUpdated {
							updatePsychotherapy.GroupCareGiver = slice.Map(updatePsychotherapy.GroupCareGiver, func(t schein_common.GroupServicesCode) schein_common.GroupServicesCode {
								if t.ServiceCode == k {
									t.AmountBilled = t.AmountBilled - int32(len(ignoreApprovals)) + int32(len(filteredTimelineCanApproval))
									if t.AmountBilled < 0 {
										t.AmountBilled = 0
									}
									isUpdated = true
								}
								return t
							})
						}
						if !isUpdated {
							updatePsychotherapy.GroupServicesCodeBefore2017 = slice.Map(updatePsychotherapy.GroupServicesCodeBefore2017, func(t schein_common.GroupServicesCodeBefore2017) schein_common.GroupServicesCodeBefore2017 {
								if t.ServiceCode == k {
									t.AmountBilled = t.AmountBilled - int32(len(ignoreApprovals)) + int32(len(filteredTimelineCanApproval))
									if t.AmountBilled < 0 {
										t.AmountBilled = 0
									}
									isUpdated = true
								}
								return t
							})
						}
						psychotherapyNeedToUpdate = append(psychotherapyNeedToUpdate, updatePsychotherapy)
					}
				}
			}
		}
		if _, err := srv.timelinePsychotherapyRepo.Update(ctx, entry); err != nil {
			return err
		}
		if len(unassignApproval) > 0 {
			if err := srv.timelinePsychotherapyRepo.UpdateStatusServiceEntries(ctx, unassignApproval, nil); err != nil {
				return err
			}
		}
	}
	if len(takeOverPsychotherapyIds) > 0 {
		if err := srv.UpdateTakeOverPsychotherapyStatus(ctx, takeOverPsychotherapyIds, patient_encounter.HAS_BEEN_TAKE_OVER); err != nil {
			return fmt.Errorf("update takeover psychotherapy error: %w", err)
		}
	}
	psychotherapyNeedCreate := slice.Filter(req.Psychotherapy, func(p schein_common.Psychotherapy) bool {
		return slice.FindOne(isExistEntryID, func(u uuid.UUID) bool {
			return p.Id.String() == u.String()
		}) == nil
	})
	if len(psychotherapyNeedCreate) > 0 {
		if _, err := srv.createPsychotherapyEntry(ctx, psychotherapyNeedCreate, &req.ScheinUpdated); err != nil {
			return err
		}
	}
	if len(deleteEntriesIds) > 0 {
		return srv.DeletePsychotherapyByIds(ctx, deleteEntriesIds)
	}
	if len(ignoreApprovalIds) > 0 {
		if err := srv.UpdateApprovalByIds(ctx, ignoreApprovalIds, nil); err != nil {
			return err
		}
	}
	if len(newApprovalIds) > 0 {
		if err := srv.UpdateApprovalByIds(ctx, newApprovalIds, util.NewPointer(patient_encounter.IsApproval)); err != nil {
			return err
		}
	}
	if len(psychotherapyNeedToUpdate) > 0 {
		if err := srv.scheinRepo.UpdateMultiplePsychotherapy(ctx, psychotherapyNeedToUpdate); err != nil {
			return err
		}
	}
	return nil
}

func (_ *TimelineService[T]) updateEntriesInPsychotherapyEntry(oldEntries, newEntries map[string]*patient_encounter.ServiceCodeApproval) (map[string]*patient_encounter.ServiceCodeApproval, []uuid.UUID) {
	result := newEntries
	unassignApproval := []uuid.UUID{}
	for k, v := range oldEntries {
		_, ok := result[k]
		if ok {
			result[k].EntryIds = v.EntryIds
			if len(v.EntryIds) > int(result[k].AmountBilled) {
				result[k].EntryIds = v.EntryIds[:result[k].AmountBilled]
				unassignApproval = append(unassignApproval, v.EntryIds[result[k].AmountBilled:]...)
			}
		}
	}
	return result, unassignApproval
}

func (srv *TimelineService[T]) GetServiceCodeTimelineByBsnr(ctx *titan.Context, bsnrCode string, yearQuarter util.YearQuarter) ([]string, error) {
	employees, err := srv.employeeProfileRepo.GetByBsnrCode(ctx, bsnrCode)
	if err != nil {
		return nil, fmt.Errorf("cannot get employee by bsnr code: %w", err)
	}
	if len(employees) == 0 {
		return nil, nil
	}

	employeeIds := slice.Map(employees, func(e employee.EmployeeProfile) uuid.UUID {
		return *e.Id
	})

	timelineServices, err := srv.timelineServiceRepo.GetServiceCodeTimelineByEmployeeIds(ctx, employeeIds, yearQuarter)
	if err != nil {
		return nil, fmt.Errorf("cannot get service timeline by employee ids: %w", err)
	}
	if len(timelineServices) == 0 {
		return nil, nil
	}
	res := slice.Map(timelineServices, func(t timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) string {
		return t.Payload.Code
	})

	res = slice.Uniq(res)

	return res, nil
}

func (srv *TimelineService[T]) DeleteDocumentRelatedTimeline(ctx *titan.Context, entity *timeline_repo.TimelineEntity[T], hasHardDelete bool) error {
	var err error
	switch entity.Type {
	case common.TimelineEntityType_EDOKU_Document:
		err = srv.edmpRepo.EdokuEnrollmentDocumentRepo.DeleteByIdWithMode(ctx, *entity.Id, hasHardDelete)
		return err
	default:
		return nil
	}
}

func (srv *TimelineService[T]) Remove(ctx *titan.Context, id uuid.UUID, hasHardDelete bool) error {
	isBilled, err := srv.checkBilledTimeline(ctx, id)
	if err != nil {
		return err
	}
	if util.GetPointerValue(isBilled) {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Cannot_Delete_Timeline_Entry, "")
	}

	entity, err := srv.timelineRepo.DeleteById(ctx, id)
	if err != nil {
		return err
	}
	if entity == nil || entity.Id == nil {
		return nil
	}
	timelineModel := ConvertEntityToModel(*entity)

	if err := srv.NotifyTimelineRemove(ctx, &timeline.EventTimelineRemove{
		PatientId:     timelineModel.PatientId,
		TimelineModel: timelineModel,
	}); err != nil {
		return fmt.Errorf("cannot notify timeline remove: %w", err)
	}

	if hasHardDelete {
		if err := srv.timelineRepo.HardDeleteById(ctx, id); err != nil {
			return fmt.Errorf("cannot delete timeline: %w", err)
		}

		return srv.notifier.NotifyTimelineHardRemove(ctx, &timeline.EventTimelineHardRemove{
			PatientId:     timelineModel.PatientId,
			TimelineModel: timelineModel,
			TimelineId:    util.GetPointerValue(timelineModel.Id),
		})
	}
	if err := srv.DeleteDocumentRelatedTimeline(ctx, entity, hasHardDelete); err != nil {
		return err
	}
	return nil
}

func (srv *TimelineService[T]) RemoveChain(ctx *titan.Context, chainID uuid.UUID, hasHardDelete bool) error {
	entries, err := srv.timelineRepo.GetTimelineEntryIdsByChainId(ctx, chainID)
	if err != nil {
		return err
	}
	for _, entry := range entries {
		err = srv.Remove(ctx, *entry.Id, hasHardDelete)
		if err != nil {
			return fmt.Errorf("failed to remove timeline entry %v: %w", *entry.Id, err)
		}
	}
	return nil
}

func (srv *TimelineService[T]) checkServiceTerminalPsychotherapyApproval(ctx *titan.Context, timelineModel *common.TimelineModel) error {
	if timelineModel.Id == nil {
		return nil
	}
	psychotherapyEntry, err := srv.timelinePsychotherapyRepo.GetPsychotherapyByTerminalId(ctx, timelineModel.PatientId, *timelineModel.Id)
	if err != nil {
		return err
	}
	if psychotherapyEntry == nil {
		return nil
	}
	psychotherapyEntry.Payload.TerminateServiceId = nil
	psychotherapyEntry.Payload.Status = patient_encounter.INPROGRESS
	if err := srv.timelinePsychotherapyRepo.UpdateStatusServiceEntries(ctx, psychotherapyEntry.Payload.GetEntryApprovalIds(), util.NewPointer(patient_encounter.IsApproval)); err != nil {
		return err
	}
	_, err = srv.timelinePsychotherapyRepo.Update(ctx, *psychotherapyEntry)
	return err
}

func (srv *TimelineService[T]) RemovePsychotherapy(ctx *titan.Context, timelineModel *common.TimelineModel) error {
	if timelineModel.EncounterPsychotherapy == nil {
		return nil
	}
	var tempServiceEntry *timeline_repo.TimelineEntity[T]
	if timelineModel.EncounterPsychotherapy.TakeOverId != nil {
		err := srv.RecoverPsychotherapyHasBeenTakeover(ctx, timelineModel.EncounterPsychotherapy)
		if err != nil {
			return err
		}
	} else {
		entryIds := timelineModel.EncounterPsychotherapy.GetEntryApprovalIds()
		err := srv.timelinePsychotherapyRepo.UpdateStatusServiceEntries(ctx, entryIds, nil)
		if err != nil {
			return err
		}
		if len(entryIds) > 0 {
			tempServiceEntry, err = srv.timelineRepo.FindById(ctx, entryIds[0])
			if err != nil {
				return err
			}
		}
	}
	err := srv.scheinRepo.RemovePsychotherapyById(ctx, *timelineModel.Id)
	if err != nil {
		return err
	}
	if tempServiceEntry != nil {
		model := ConvertEntityToModel(*tempServiceEntry)
		err := srv.validationService.RunValidateOnUpdatingTimeline(ctx, &timeline.EventTimelineUpdate{
			PatientId:     model.PatientId,
			TimelineModel: model,
		})
		if err != nil {
			return err
		}
	}
	return nil
}

func (srv *TimelineService[T]) RecoverPsychotherapyHasBeenTakeover(
	ctx *titan.Context,
	encounterPsychotherapy *patient_encounter.EncounterPsychotherapy,
) error {
	_, err := srv.timelinePsychotherapyRepo.RecoverPsychotherapyHasBeenTakeover(ctx, encounterPsychotherapy)
	if err != nil {
		return err
	}
	return nil
}

// func (srv *TimelineService[T]) UndoRemove(ctx *titan.Context, request timeline.RestoreEntryHistoryRequest) error {
// 	entity, err := srv.timelineRepo.UndoDeleteById(ctx, request.TimelineId)
// 	if err != nil {
// 		return err
// 	}
// 	if entity == nil {
// 		return nil
// 	}

// 	if err := srv.NotifyTimelineUpdate(ctx, &timeline.EventTimelineUpdate{
// 		PatientId:     entity.PatientId,
// 		TimelineModel: ConvertEntityToModel(*entity),
// 		AuditLogId:    &request.AuditLogId,
// 	}); err != nil {
// 		return err
// 	}

// 	return nil
// }

// UpdateMany
func (srv *TimelineService[T]) UpdateMany(ctx *titan.Context, requests []timeline_repo.TimelineEntity[T]) ([]timeline_repo.TimelineEntity[T], error) {
	result, err := srv.timelineRepo.UpdateMany(ctx, slice.ToPointerType(requests))
	if err != nil {
		return nil, fmt.Errorf("cannot update many timeline: %w", err)
	}
	var eventTimelineUpdates []*timeline.EventTimelineUpdate
	for _, timelineItem := range result {
		old := slice.FindOne(requests, func(te timeline_repo.TimelineEntity[T]) bool {
			return te.Id.String() == timelineItem.Id.String()
		})
		if old == nil {
			continue
		}
		oldTimeline := ConvertEntityToModel(*old)
		eventTimelineUpdates = append(eventTimelineUpdates, &timeline.EventTimelineUpdate{
			PatientId:        timelineItem.PatientId,
			TimelineModel:    ConvertEntityToModel(timelineItem),
			OldTimelineModel: &oldTimeline,
		})
	}

	if err := srv.NotifyTimelineUpdate(ctx, eventTimelineUpdates...); err != nil {
		return nil, fmt.Errorf("cannot notify timeline update: %w", err)
	}
	return result, nil
}

func (srv *TimelineService[T]) Edit(ctx *titan.Context, request timeline_repo.TimelineEntity[T]) (*timeline_repo.TimelineEntity[T], error) {
	isBilled, err := srv.checkBilledTimeline(ctx, *request.Id)
	if err != nil {
		return nil, err
	}
	if util.GetPointerValue(isBilled) {
		return nil, nil
	}

	old, err := srv.timelineRepo.EditTimelineById(ctx, request, options.FindOneAndUpdate().SetReturnDocument(options.Before))
	if err != nil {
		return nil, err
	}
	if old == nil || old.Id == nil {
		return nil, nil
	}
	entity, err := srv.timelineRepo.FindById(ctx, *request.Id)
	if err != nil {
		return nil, err
	}
	if entity == nil || entity.Id == nil {
		return nil, nil
	}
	timelineModel := ConvertEntityToModel(*entity)

	// NOTE: auto create, update, replace... timeline entries
	err = srv.doAutoActionWhenDocumenting(ctx, &timelineModel)
	if err != nil {
		return nil, err
	}

	oldTimelineModel := ConvertEntityToModel(*old)
	if err := srv.NotifyTimelineUpdate(ctx, &timeline.EventTimelineUpdate{
		PatientId:        timelineModel.PatientId,
		TimelineModel:    timelineModel,
		OldTimelineModel: &oldTimelineModel,
	}); err != nil {
		return nil, fmt.Errorf("cannot notify timeline update: %w", err)
	}

	return entity, nil
}

func (srv *TimelineService[T]) UpdateBgInvoiceTimeline(
	ctx *titan.Context,
	timelineId uuid.UUID,
	letterItem *doctor_letter_common.DoctorLetter,
) (*timeline_repo.TimelineEntity[T], error) {
	// find entity by id
	entity, err := srv.timelineRepo.FindById(ctx, timelineId)
	if err != nil {
		return nil, err
	}
	if entity == nil || entity.Id == nil {
		return nil, nil
	}
	// set payload doctor letter to update
	model := ConvertEntityToModel(*entity)
	model.DoctorLetter = util.NewPointer(pkg_copy.CloneTo[doctor_letter_common.DoctorLetter](letterItem))

	// convert model to entity for update
	updateEntity := ConvertModelToEntity[T](model)
	// update entity
	old, err := srv.timelineRepo.EditTimelineById(ctx, updateEntity, options.FindOneAndUpdate().SetReturnDocument(options.Before))
	if err != nil {
		return nil, err
	}
	if old == nil || old.Id == nil {
		return nil, nil
	}

	// find entity by id
	entity, err = srv.timelineRepo.FindById(ctx, timelineId)
	if err != nil {
		return nil, err
	}
	if entity == nil || entity.Id == nil {
		return nil, nil
	}
	timelineModel := ConvertEntityToModel(*entity)
	oldTimelineModel := ConvertEntityToModel(*old)

	// notify timeline update
	if err := srv.NotifyTimelineUpdate(ctx, &timeline.EventTimelineUpdate{
		PatientId:        timelineModel.PatientId,
		TimelineModel:    timelineModel,
		OldTimelineModel: &oldTimelineModel,
	}); err != nil {
		return nil, fmt.Errorf("cannot notify timeline update: %w", err)
	}
	return entity, nil
}

func (srv *TimelineService[T]) UpdatePrivateInvoiceTimeline(
	ctx *titan.Context,
	timelineId uuid.UUID,
	letterItem *doctor_letter_common.DoctorLetter,
) (*timeline_repo.TimelineEntity[T], error) {
	// find entity by id
	entity, err := srv.timelineRepo.FindById(ctx, timelineId)
	if err != nil {
		return nil, err
	}
	if entity == nil || entity.Id == nil {
		return nil, nil
	}
	// set payload doctor letter to update
	model := ConvertEntityToModel(*entity)
	model.DoctorLetter = util.NewPointer(pkg_copy.CloneTo[doctor_letter_common.DoctorLetter](letterItem))

	// convert model to entity for update
	updateEntity := ConvertModelToEntity[T](model)
	// update entity
	old, err := srv.timelineRepo.EditTimelineById(ctx, updateEntity, options.FindOneAndUpdate().SetReturnDocument(options.Before))
	if err != nil {
		return nil, err
	}
	if old == nil || old.Id == nil {
		return nil, nil
	}

	// find entity by id
	entity, err = srv.timelineRepo.FindById(ctx, timelineId)
	if err != nil {
		return nil, err
	}
	if entity == nil || entity.Id == nil {
		return nil, nil
	}
	timelineModel := ConvertEntityToModel(*entity)
	oldTimelineModel := ConvertEntityToModel(*old)

	// notify timeline update
	if err := srv.NotifyTimelineUpdate(ctx, &timeline.EventTimelineUpdate{
		PatientId:        timelineModel.PatientId,
		TimelineModel:    timelineModel,
		OldTimelineModel: &oldTimelineModel,
	}); err != nil {
		return nil, fmt.Errorf("cannot notify timeline update: %w", err)
	}
	return entity, nil
}

func (srv *TimelineService[T]) RestoreFromLogId(ctx *titan.Context, auditLogId uuid.UUID) (*timeline_repo.TimelineEntity[T], error) {
	logData, err := srv.auditLogService.GetById(ctx, auditLogId)
	if err != nil {
		return nil, err
	}
	if logData == nil {
		ctx.Logger().Error("RestoreEntryHistory error: no log data", "AuditLogId", auditLogId)
		return nil, err
	}

	restoringTimelineModel := &common.TimelineModel{}
	err = audit_log_service.DecodeMapToStruct(logData.Data, restoringTimelineModel)
	if err != nil {
		return nil, err
	}

	restoringEntity := ConvertModelToEntity[T](*restoringTimelineModel)
	old, err := srv.timelineRepo.RestoreTimeline(ctx, restoringEntity, options.FindOneAndUpdate().SetReturnDocument(options.Before))
	if err != nil {
		return nil, err
	}
	if old == nil || old.Id == nil {
		return nil, nil
	}
	entity, err := srv.timelineRepo.FindById(ctx, *restoringEntity.Id)
	if err != nil {
		return nil, err
	}
	if entity == nil || entity.Id == nil {
		return nil, nil
	}
	timelineModel := ConvertEntityToModel(*entity)
	oldTimelineModel := ConvertEntityToModel[T](*old)
	if err := srv.NotifyTimelineUpdate(ctx, &timeline.EventTimelineUpdate{
		PatientId:        timelineModel.PatientId,
		TimelineModel:    timelineModel,
		OldTimelineModel: &oldTimelineModel,
		AuditLogId:       &auditLogId,
	}); err != nil {
		return nil, err
	}

	return entity, nil
}

// func (srv *TimelineService[T]) checkUpdateAmount(ctx *titan.Context, )

func (srv *TimelineService[T]) UpdateCalculatorPsychotherapy(
	ctx *titan.Context,
	oldScheinId, newScheinId uuid.UUID,
	timelineModel *common.TimelineModel,
) error {
	if timelineModel.EncounterServiceTimeline == nil {
		return nil
	}
	if oldScheinId.String() == newScheinId.String() {
		return nil
	}

	psychotherapyEntries, err := srv.timelinePsychotherapyRepo.GetPsychotherapy(ctx, &timeline_repo.GetPsychotherapyRequest{
		PatientId: timelineModel.PatientId,
		ScheinIds: []uuid.UUID{oldScheinId, newScheinId},
		Statuses:  []patient_encounter.EncounterPsychotherapyStatus{patient_encounter.INPROGRESS},
	})
	if err != nil {
		return err
	}
	var oldPsychotherapy *timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy]
	for _, p := range psychotherapyEntries {
		if p.Payload.ScheinId.String() == oldScheinId.String() {
			oldPsychotherapy = &p
			break
		}
	}
	serviceCode := timelineModel.EncounterServiceTimeline.Code
	if oldPsychotherapy != nil {
		if _, ok := oldPsychotherapy.Payload.Entries[serviceCode]; ok {
			if slice.Contains(oldPsychotherapy.Payload.Entries[serviceCode].EntryIds, *timelineModel.Id) {
				oldPsychotherapy.Payload.Entries[serviceCode].EntryIds = slice.Filter(oldPsychotherapy.Payload.Entries[serviceCode].EntryIds, func(u uuid.UUID) bool {
					return u.String() != timelineModel.Id.String()
				})
				if oldPsychotherapy.Payload.Entries[serviceCode].EntryIds == nil {
					oldPsychotherapy.Payload.Entries[serviceCode].EntryIds = []uuid.UUID{}
				}
				if oldPsychotherapy.Payload.Entries[serviceCode].AmountBilled != 0 {
					oldPsychotherapy.Payload.Entries[serviceCode].AmountBilled -= 1
				}
				_, err := srv.timelinePsychotherapyRepo.Update(ctx, *oldPsychotherapy)
				if err != nil {
					return err
				}
			}
		}
	}
	return nil
}

func (srv *TimelineService[T]) UpdateMedicinePrescriptionPrintDateById(ctx *titan.Context, patientId, medicineId uuid.UUID, printDate int64) (*timeline_repo.TimelineEntity[T], error) {
	return srv.timelineRepo.UpdateMedicinePrescriptionPrintDateById(ctx, &timeline_repo.UpdateMedicinePrescriptionPrintDateByIdRequest{
		MedicineId: medicineId,
		PrintDate:  printDate,
		PatientId:  patientId,
	})
}

func (srv *TimelineService[T]) UpdateLabFormStatus(ctx *titan.Context, labId uuid.UUID, labResultStatus lab_common.LabResultStatus, labFormStatus lab_common.LabFormStatus) error {
	entity, err := srv.timelineRepo.UpdateLabResultStatus(ctx, labId, labResultStatus, labFormStatus)
	if err != nil {
		return err
	}
	if entity == nil {
		return nil
	}
	if err := srv.NotifyTimelineUpdate(ctx, &timeline.EventTimelineUpdate{
		PatientId:     entity.PatientId,
		TimelineModel: ConvertEntityToModel(*entity),
	}); err != nil {
		return err
	}
	return err
}

func (srv *TimelineService[T]) handleTimelineCreate(ctx *titan.Context, eventCreateModels ...*timeline.EventTimelineCreate) error {
	if len(eventCreateModels) == 0 {
		return nil
	}
	codingRuleMemo := function.Memoize[*codingRuleRunner[T], string]()
	for _, eventCreateModel := range eventCreateModels {
		if eventCreateModel.TimelineModel.Type != nil && *eventCreateModel.TimelineModel.Type == common.TimelineEntityType_Service {
			if err := srv.HandleUpdateAmountPyschotherapy(ctx, &eventCreateModel.TimelineModel, true); err != nil {
				return errors.WithMessage(err, "cannot handle update amount psychotherapy")
			}
			if slice.Contains(TerminalServiceCodes, eventCreateModel.TimelineModel.EncounterServiceTimeline.Code) {
				if err := srv.checkAndApplyTerminalService(ctx, &eventCreateModel.TimelineModel, nil); err != nil {
					return errors.WithMessage(err, "cannot check and apply terminal service")
				}
			}
		}

		if eventCreateModel.TimelineModel.EncounterDiagnoseTimeline != nil && eventCreateModel.TimelineModel.EncounterDiagnoseTimeline.Type == patient_encounter.DIAGNOSETYPE_PERMANENT {
			entity := ConvertModelToEntity[patient_encounter.EncounterDiagnoseTimeline](eventCreateModel.TimelineModel)
			if err := srv.syncPermanentDiagnoseEndDate(ctx, &entity); err != nil {
				return errors.WithMessage(err, "cannot sync permanent diagnose end date")
			}
		}
		yearQuarter := util.ToYearQuarter(eventCreateModel.TimelineModel.SelectedDate)
		runCodingRuleOnCreatingTimeline, err := codingRuleMemo(
			func() string {
				return fmt.Sprintf("%s_%s", yearQuarter, eventCreateModel.TimelineModel.PatientId.String())
			},
			func() (*codingRuleRunner[T], error) {
				return srv.NewCodingRuleRunner(ctx, eventCreateModel.TimelineModel.PatientId, yearQuarter)
			},
		)
		if err != nil {
			return errors.WithMessage(err, "cannot get coding rule runner")
		}
		if err := runCodingRuleOnCreatingTimeline.runCodingRuleOnDocumenting(ctx, &eventCreateModel.TimelineModel); err != nil {
			return errors.WithMessage(err, "cannot run coding rule on creating timeline")
		}
	}

	return nil
}

func (srv *TimelineService[T]) triggerHookCreate(ctx *titan.Context, eventCreateModel *timeline.EventTimelineCreate) error {
	if err := srv.HookBeforeAction.ExecuteOnCreate(ctx, eventCreateModel); err != nil {
		return errors.WithMessage(err, "cannot execute hook before action")
	}

	timeline, err := srv.FindById(ctx, *eventCreateModel.TimelineModel.Id)
	if err != nil {
		return fmt.Errorf("cannot find timeline by id has error : %w", err)
	}
	if timeline == nil {
		return fmt.Errorf("cannot find timeline by id: %s", eventCreateModel.TimelineModel.Id.String())
	}
	eventCreateModel.TimelineModel = ConvertEntityToModel(*timeline)
	err = srv.HookAfterAction.ExecuteOnCreate(ctx, eventCreateModel)
	if err != nil {
		return errors.WithMessage(err, "cannot execute hook after action")
	}
	return srv.notifier.NotifyTimelineCreate(ctx, eventCreateModel)
}

func (srv *TimelineService[T]) NotifyTimelineCreate(ctx *titan.Context, eventCreateModel ...*timeline.EventTimelineCreate) error {
	ctx, span := titan.SpanContext(ctx, ctx.GetHTTPHeader(), "NotifyTimelineCreate")
	defer span.End()
	if err := srv.handleTimelineCreate(ctx, eventCreateModel...); err != nil {
		return errors.WithMessage(err, "cannot handle timeline create")
	}
	lastItem := eventCreateModel[len(eventCreateModel)-1]
	return srv.triggerHookCreate(ctx, lastItem)
}

func (srv *TimelineService[T]) doAutoActionWhenDocumenting(
	ctx *titan.Context,
	timelineModel *common.TimelineModel,
) error {
	ctx, span := titan.SpanContext(ctx, ctx.GetHTTPHeader(), "doAutoActionWhenDocumenting")
	defer span.End()
	timelineType := util.GetPointerValue(timelineModel.Type)
	if timelineType != common.TimelineEntityType_Service || util.GetPointerValue(timelineModel.EncounterServiceTimeline.ServiceMainGroup) != domains_api_common.HZV {
		return nil
	}

	if util.GetPointerValue(timelineModel.ContractId) == "" {
		return nil
	}

	hasSchein := timelineModel.EncounterServiceTimeline.Scheins != nil && len(*timelineModel.EncounterServiceTimeline.Scheins) > 0
	if !hasSchein {
		return nil
	}

	schein := util.GetPointerValue(timelineModel.EncounterServiceTimeline.Scheins)[0]
	if schein.Group != domains_api_common.FAV && schein.Group != domains_api_common.HZV {
		return nil
	}
	contractId := util.GetPointerValue(timelineModel.ContractId)
	chargeSystemId := util.GetPointerValue(timelineModel.EncounterServiceTimeline.ChargeSystemId)
	contract := srv.contractService.GetContractDetailById(contractId)
	if contract == nil {
		return fmt.Errorf("cannot find contract by id: %s when auto document", contractId)
	}

	// NOTE: implement ABRD920 rule, that is mentioned in pdf file only
	requireServiceCode := "80092.2"
	now := util.NowUnixMillis(ctx)
	hasABRD920 := contract.CheckExistAnforderung(model.ICheckExistAnforderung{
		ComplianceIds: []string{"ABRG921"},
		CheckTime:     now,
	})
	if !hasABRD920 {
		return nil
	}
	chargeSystem := slice.FindOne(contract.GetChargeSystems(), func(c model.HonoraranlageTyp) bool {
		return c.GetId() == chargeSystemId
	})
	requiredServiceInContract := slice.FindOne(chargeSystem.GetServices(), func(s model.LeistungTyp) bool {
		return s.GetCode() == requireServiceCode
	})

	// NOTE: look up service.code == "80092.2" in the same quarter
	// TODO: optimize to get service timeline only
	_, services, _, err := srv.GetTimelinesDiagnoseAndServiceByScheinIds(ctx, GetTimelinesDiagnoseAndServiceByScheinIdsRequest{
		PatientWithScheinIds: []timeline_repo.PatientWithScheinIds{
			{
				PatientId: timelineModel.PatientId,
				ScheinIds: []uuid.UUID{*schein.ScheinId},
			},
		},
	})
	if err != nil {
		return errors.WithMessage(err, "get timelines diagnose and service by schein ids error")
	}

	// NOTE: get the first service code that matching condition ABRG921 in a schein
	var firstMatchedServiceTimeline *timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]
	for _, s := range services {
		if util.GetPointerValue(s.EncounterCase) == patient_encounter.PB {
			if firstMatchedServiceTimeline == nil {
				firstMatchedServiceTimeline = &s
				continue
			}

			if s.SelectedDate.UnixMilli() < firstMatchedServiceTimeline.SelectedDate.UnixMilli() {
				firstMatchedServiceTimeline = &s
			}
		}
	}
	if firstMatchedServiceTimeline == nil {
		return nil
	}
	hasRequireServiceCode := slice.Any(services, func(s timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) bool {
		return s.Payload.Code == requireServiceCode
	})

	if hasRequireServiceCode {
		return nil
	}

	newEntry := timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
		ChainId:           firstMatchedServiceTimeline.ChainId,
		ContractId:        firstMatchedServiceTimeline.ContractId,
		TreatmentDoctorId: firstMatchedServiceTimeline.TreatmentDoctorId,
		BillingDoctorId:   firstMatchedServiceTimeline.BillingDoctorId,
		PatientId:         firstMatchedServiceTimeline.PatientId,
		BillingInfo:       &domains_api_common.BillingInfo{},
		ScheinIds:         firstMatchedServiceTimeline.ScheinIds,
		EncounterCase:     firstMatchedServiceTimeline.EncounterCase,
		TreatmentCase:     firstMatchedServiceTimeline.TreatmentCase,
		Payload: patient_encounter.EncounterServiceTimeline{
			Code:               requireServiceCode,
			Description:        requiredServiceInContract.GetDescription(),
			ReferralDoctorInfo: &patient_encounter.ReferralDoctorInfo{},
			MaterialCosts:      &patient_encounter.MaterialCosts{},
			CareFacility:       firstMatchedServiceTimeline.Payload.CareFacility,
			FreeText:           fmt.Sprintf("(%s) %s", requireServiceCode, requiredServiceInContract.GetDescription()),
			Errors:             &[]*patient_encounter.EncounterItemError{},
			Command:            "L",
			AuditLog:           &domains_api_common.AuditLog{},
			Scheins:            firstMatchedServiceTimeline.Payload.Scheins,
			ServiceMainGroup:   firstMatchedServiceTimeline.Payload.ServiceMainGroup,
			ChargeSystemId:     firstMatchedServiceTimeline.Payload.ChargeSystemId,
		},
		Type:            common.TimelineEntityType_Service,
		RecentAuditLogs: []timeline_repo.AuditLog{},
		CreatedAt:       firstMatchedServiceTimeline.CreatedAt,
		Quarter:         firstMatchedServiceTimeline.Quarter,
		Year:            firstMatchedServiceTimeline.Year,
		CreatedAtString: firstMatchedServiceTimeline.CreatedAt.String(),
		CreatedBy:       util.GetPointerValue(ctx.UserInfo().UserUUID()),
		Errors:          []patient_encounter.EncounterItemError{},
		SelectedDate:    firstMatchedServiceTimeline.SelectedDate,
	}
	if _, err := srv.timelineServiceRepo.Create(ctx, newEntry); err != nil {
		return errors.WithMessage(err, "create timeline error")
	}
	if err := srv.notifier.NotifyAutoAction(ctx, &timeline.EventAutoAction{
		PatientId:        timelineModel.PatientId,
		NotificationCode: "AUTO_CREATE_BY_ABRG921",
	}); err != nil {
		return errors.WithMessage(err, "notify auto action error")
	}
	return nil
}

func (srv *TimelineService[T]) checkAndApplyTerminalService(
	ctx *titan.Context,
	timeline *common.TimelineModel,
	oldTimeline *common.TimelineModel,
) error {
	ctx, span := titan.SpanContext(ctx, ctx.GetHTTPHeader(), "checkAndApplyTerminalService")
	defer span.End()
	if len(timeline.ScheinIds) == 0 {
		return nil
	}
	if timeline.EncounterServiceTimeline == nil {
		return nil
	}
	schein, err := srv.scheinRepo.FindById(ctx, timeline.ScheinIds[0])
	if err != nil {
		return err
	}
	if schein == nil {
		return err
	}
	psychotherapyIds := []uuid.UUID{}
	if schein.ScheinDetail.Psychotherapy != nil {
		psychotherapyIds = funk.Map(schein.ScheinDetail.Psychotherapy, func(p schein_common.Psychotherapy) uuid.UUID {
			return *p.Id
		}).([]uuid.UUID)
	}
	if len(psychotherapyIds) == 0 {
		return nil
	}
	psychotherapyEntries, err := srv.timelinePsychotherapyRepo.FindByIds(ctx, psychotherapyIds)
	if err != nil {
		return err
	}
	slice.SortBy(psychotherapyEntries, func(a, b timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy]) bool {
		return a.Payload.ApprovalDate < b.Payload.ApprovalDate
	})
	for _, p := range psychotherapyEntries {
		if p.Payload.TerminateServiceId != nil {
			continue
		}
		entryIds := p.Payload.GetEntryApprovalIdsWithoutTerminalId()
		if len(entryIds) > 0 {
			service, err := srv.timelineServiceRepo.FindById(ctx, entryIds[0])
			if err != nil {
				return err
			}
			if service != nil {
				model := ConvertEntityToModel(*service)
				err := srv.MarkPyschotherapyCompleted(ctx, &model, timeline, p.ScheinIds[0])
				if err != nil {
					return err
				}
				break
			}
		} else {
			if !p.Payload.CheckIsBefore2017(ctx) {
				if err := srv.markPyschotherapyCompletedAfter2017WithoutService(ctx, p, timeline.Id); err != nil {
					return nil
				}
				break
			} else {
				if err := srv.markPyschotherapyCompletedBefore2017WithService(ctx, p, timeline.Id); err != nil {
					return nil
				}
				break
			}
		}
	}
	if oldTimeline != nil && len(oldTimeline.ScheinIds) > 0 {
		if timeline.ScheinIds[0].String() != oldTimeline.ScheinIds[0].String() {
			if err := srv.checkServiceTerminalPsychotherapyApproval(ctx, oldTimeline); err != nil {
				return err
			}
		}
	}
	return nil
}

func (srv *TimelineService[T]) markPyschotherapyCompletedAfter2017WithoutService(ctx *titan.Context, p timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy], terminateServiceId *uuid.UUID) error {
	p.Payload.Status = patient_encounter.READY_TO_BILL
	p.Payload.TerminateServiceId = terminateServiceId
	_, err := srv.timelinePsychotherapyRepo.Update(ctx, p)
	return err
}

func (srv *TimelineService[T]) markPyschotherapyCompletedBefore2017WithService(ctx *titan.Context, p timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy], terminateServiceId *uuid.UUID) error {
	for _, v := range p.Payload.Entries {
		if v.TerminalId == nil {
			v.TerminalId = terminateServiceId
			break
		}
	}
	countingTerminateServiceID := 0
	for _, v := range p.Payload.Entries {
		if v.TerminalId != nil {
			countingTerminateServiceID++
		}
	}
	if countingTerminateServiceID == len(p.Payload.Entries) {
		p.Payload.Status = patient_encounter.READY_TO_BILL
	}
	_, err := srv.timelinePsychotherapyRepo.Update(ctx, p)
	return err
}

func (srv *TimelineService[T]) HandleUpdateAmountPyschotherapy(
	ctx *titan.Context,
	timelineModel *common.TimelineModel,
	isUpdate bool,
) error {
	ctx, span := titan.SpanContext(ctx, ctx.GetHTTPHeader(), "HandleUpdateAmountPyschotherapy")
	defer span.End()
	if timelineModel.Type == nil && *timelineModel.Type != common.TimelineEntityType_Service {
		return nil
	}
	if len(timelineModel.ScheinIds) == 0 || timelineModel.Id == nil {
		return nil
	}

	psychotherapyTimelineEntry, err := srv.CheckExistPyschotherapy(ctx, timelineModel, isUpdate)
	if err != nil {
		return fmt.Errorf("cannot check exist psychotherapy: %w", err)
	}
	if psychotherapyTimelineEntry == nil || psychotherapyTimelineEntry.Id == nil {
		return nil
	}

	if timelineModel.SelectedDate < psychotherapyTimelineEntry.Payload.ApprovalDate {
		return nil
	}

	if isUpdate {
		existServiceInPsychotherapy := false
		for _, v := range psychotherapyTimelineEntry.Payload.Entries {
			if slice.FindOne(v.EntryIds, func(u uuid.UUID) bool {
				return u.String() == timelineModel.Id.String()
			}) != nil {
				existServiceInPsychotherapy = true
				break
			}
		}
		if existServiceInPsychotherapy {
			return nil
		}
		if psychotherapyTimelineEntry.Payload.AmountApproval != 0 {
			if psychotherapyTimelineEntry.Payload.CheckIsNoLeftOver(timelineModel.EncounterServiceTimeline.Code) {
				return nil
			}
		} else {
			v, ok := psychotherapyTimelineEntry.Payload.Entries[timelineModel.EncounterServiceTimeline.Code]
			if ok && v.AmountApproval != nil && v.AmountBilled == *v.AmountApproval {
				return nil
			}
		}
	}
	if !slice.Contains(PsychotherapyStatusCanUpdate, psychotherapyTimelineEntry.Payload.Status) {
		return nil
	}
	if len(psychotherapyTimelineEntry.ScheinIds) == 0 {
		return nil
	}
	schein, err := srv.scheinRepo.FindById(ctx, psychotherapyTimelineEntry.ScheinIds[0])
	if err != nil {
		return err
	}
	if schein == nil {
		return nil
	}
	if schein.ScheinDetail.Psychotherapy == nil {
		return nil
	}
	psychotherapy, index := slice.FindOneIndex(schein.ScheinDetail.Psychotherapy, func(t schein_common.Psychotherapy) bool {
		return t.Id.String() == psychotherapyTimelineEntry.Id.String()
	})
	if psychotherapy == nil {
		return nil
	}
	updaterServiceCode := bson.M{
		fmt.Sprintf("%s.%s.%s", timeline_repo.Field_Psychotherapy_Entries, timelineModel.EncounterServiceTimeline.Code, "entryids"): timelineModel.Id,
	}
	amountApprovalKey := fmt.Sprintf("%s.%s.%s", timeline_repo.Field_Psychotherapy_Entries, timelineModel.EncounterServiceTimeline.Code, "amountbilled")
	updater := bson.M{
		"$set": bson.M{
			repos.Field_UpdatedAt: util.Now(ctx),
		},
	}
	if isUpdate {
		updater["$push"] = updaterServiceCode
		updater["$inc"] = bson.M{
			amountApprovalKey: 1,
		}
		updateAmountBilledSchein(schein, timelineModel.EncounterServiceTimeline.Code, index, 1)
	} else {
		updater["$pull"] = updaterServiceCode
		updater["$inc"] = bson.M{
			amountApprovalKey: -1,
		}
		updateAmountBilledSchein(schein, timelineModel.EncounterServiceTimeline.Code, index, -1)
	}
	_, err = srv.timelineRepo.FindOneAndUpdate(ctx, bson.M{
		timeline_repo.Field_Id:                 *psychotherapy.Id,
		timeline_repo.Field_timelineEntityType: common.TimelineEntityType_Psychotherapy,
		timeline_repo.Field_Psychotherapy_Status: bson.M{
			"$in": PsychotherapyStatusCanUpdate,
		},
	}, updater)
	if err != nil {
		return err
	}
	if timelineModel.EncounterServiceTimeline != nil && timelineModel.EncounterServiceTimeline.ApprovalStatus != nil && *timelineModel.EncounterServiceTimeline.ApprovalStatus == patient_encounter.NotAcceptedByKV {
		return nil
	}
	psychotherapyUpdater := bson.M{
		timeline_repo.Field_Psychotherapy_ApprovalStatus: patient_encounter.IsApproval,
		repos.Field_UpdatedAt:                            util.Now(ctx),
	}
	if !isUpdate {
		psychotherapyUpdater[timeline_repo.Field_Psychotherapy_ApprovalStatus] = patient_encounter.HasBeenRemoveApproval
	}
	_, err = srv.timelineRepo.FindOneAndUpdate(ctx, bson.M{
		timeline_repo.Field_Id:                 timelineModel.Id,
		timeline_repo.Field_timelineEntityType: common.TimelineEntityType_Service,
	}, bson.M{
		"$set": psychotherapyUpdater,
	})
	if err != nil {
		return err
	}
	_, err = srv.scheinRepo.Update(ctx, *schein)
	return err
}

func updateAmountBilledSchein(schein *schein_repo.ScheinRepo, serviceCode string, index int, amountInc int32) {
	for idx := range schein.ScheinDetail.Psychotherapy[index].GroupServicesCode {
		if schein.ScheinDetail.Psychotherapy[index].GroupServicesCode[idx].ServiceCode == serviceCode {
			schein.ScheinDetail.Psychotherapy[index].GroupServicesCode[idx].AmountBilled += amountInc
			if schein.ScheinDetail.Psychotherapy[index].GroupServicesCode[idx].AmountBilled < 0 {
				schein.ScheinDetail.Psychotherapy[index].GroupServicesCode[idx].AmountBilled = 0
			}
			break
		}
	}
	for idx := range schein.ScheinDetail.Psychotherapy[index].GroupCareGiver {
		if schein.ScheinDetail.Psychotherapy[index].GroupCareGiver[idx].ServiceCode == serviceCode {
			schein.ScheinDetail.Psychotherapy[index].GroupCareGiver[idx].AmountBilled += amountInc
			if schein.ScheinDetail.Psychotherapy[index].GroupCareGiver[idx].AmountBilled < 0 {
				schein.ScheinDetail.Psychotherapy[index].GroupCareGiver[idx].AmountBilled = 0
			}
			break
		}
	}
	for idx := range schein.ScheinDetail.Psychotherapy[index].GroupServicesCodeBefore2017 {
		if schein.ScheinDetail.Psychotherapy[index].GroupServicesCodeBefore2017[idx].ServiceCode == serviceCode {
			schein.ScheinDetail.Psychotherapy[index].GroupServicesCodeBefore2017[idx].AmountBilled += amountInc
			if schein.ScheinDetail.Psychotherapy[index].GroupServicesCodeBefore2017[idx].AmountBilled < 0 {
				schein.ScheinDetail.Psychotherapy[index].GroupServicesCodeBefore2017[idx].AmountBilled = 0
			}
			break
		}
	}
}

func (srv *TimelineService[T]) CheckExistPyschotherapy(
	ctx *titan.Context,
	timelineServiceModel *common.TimelineModel,
	isUpdate bool,
) (*timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy], error) {
	scheinId := timelineServiceModel.ScheinIds[0]
	filter := bson.D{
		{
			Key:   timeline_repo.Field_PatientId,
			Value: timelineServiceModel.PatientId,
		},
		{
			Key:   timeline_repo.Field_ServiceCodeApproval,
			Value: timelineServiceModel.EncounterServiceTimeline.Code,
		},
		{
			Key:   timeline_repo.Field_IsDeleted,
			Value: false,
		},
		{
			Key:   timeline_repo.Field_timelineEntityType,
			Value: common.TimelineEntityType_Psychotherapy,
		},
		{
			Key:   timeline_repo.Field_Psychotherapy_ScheinId,
			Value: scheinId,
		},
		{
			Key: timeline_repo.Field_Psychotherapy_Status,
			Value: bson.M{
				"$in": PsychotherapyStatusCanUpdate,
			},
		},
	}
	if timelineServiceModel.EncounterServiceTimeline.ApprovalStatus != nil && *timelineServiceModel.EncounterServiceTimeline.ApprovalStatus == patient_encounter.IsApproval {
		filter = append(filter, bson.E{
			Key:   fmt.Sprintf("%s.%s.%s", timeline_repo.Field_Psychotherapy_Entries, timelineServiceModel.EncounterServiceTimeline.Code, "entryids"),
			Value: timelineServiceModel.Id,
		})
	}

	timelineRecords, err := srv.timelinePsychotherapyRepo.Find(ctx, filter, options.Find().SetSort(bson.M{
		timeline_repo.Field_Psychotherapy_ApprovalDate: -1,
	}))
	if err != nil {
		return nil, fmt.Errorf("cannot find timeline by filter has error for psychotherapy : %w", err)
	}

	if len(timelineRecords) == 0 {
		return nil, nil
	}

	for _, t := range timelineRecords {
		foundId := slice.FindOne(t.Payload.GetEntryApprovalIds(), func(u uuid.UUID) bool {
			return u == *timelineServiceModel.Id
		})
		if isUpdate && foundId == nil {
			return t, nil
		} else if !isUpdate && foundId != nil {
			return t, nil
		}
	}
	return nil, nil
}

func (srv *TimelineService[T]) handleTimelineRemove(ctx *titan.Context, eventRemoveModels ...*timeline.EventTimelineRemove) error {
	validStatusCheck := []patient_encounter.EncounterServiceTimelinePsychotherapyStatus{
		patient_encounter.HasBeenRemoveApproval,
		patient_encounter.IsApproval,
		patient_encounter.IsCompleted,
	}
	if len(eventRemoveModels) == 0 {
		return nil
	}
	codingRuleMemo := function.Memoize[*codingRuleRunner[T], string]()
	for _, eventRemoveModel := range eventRemoveModels {
		if eventRemoveModel.TimelineModel.EncounterServiceTimeline != nil {
			if (eventRemoveModel.TimelineModel.EncounterServiceTimeline.ApprovalStatus != nil && slice.Contains(validStatusCheck, *eventRemoveModel.TimelineModel.EncounterServiceTimeline.ApprovalStatus)) ||
				eventRemoveModel.TimelineModel.EncounterServiceTimeline.ApprovalStatus == nil {
				if err := srv.HandleUpdateAmountPyschotherapy(ctx, &eventRemoveModel.TimelineModel, false); err != nil {
					return errors.WithMessage(err, "handle update amount psychotherapy error")
				}
			}
			if slice.Contains(TerminalServiceCodes, eventRemoveModel.TimelineModel.EncounterServiceTimeline.Code) {
				if err := srv.revertApproval(ctx, &eventRemoveModel.TimelineModel); err != nil {
					return errors.WithMessage(err, "revert approval error")
				}
			}
		}

		if *eventRemoveModel.TimelineModel.Type == common.TimelineEntityType_Psychotherapy {
			if err := srv.RemovePsychotherapy(ctx, &eventRemoveModel.TimelineModel); err != nil {
				return errors.WithMessage(err, "remove psychotherapy error")
			}
		}

		if len(eventRemoveModel.TimelineModel.ScheinIds) > 0 {
			if err := srv.deleteTechnicalSchein(ctx, eventRemoveModel.TimelineModel.ScheinIds[0]); err != nil {
				return errors.WithMessage(err, "delete technicalschein error")
			}
		}

		yearQuarter := util.ToYearQuarter(eventRemoveModel.TimelineModel.SelectedDate)
		runCodingRuleOnCreatingTimeline, err := codingRuleMemo(
			func() string {
				return fmt.Sprintf("%s_%s", yearQuarter, eventRemoveModel.TimelineModel.PatientId.String())
			},
			func() (*codingRuleRunner[T], error) {
				return srv.NewCodingRuleRunner(ctx, eventRemoveModel.TimelineModel.PatientId, yearQuarter)
			},
		)
		if err != nil {
			return errors.WithMessage(err, "cannot get coding rule runner")
		}
		if err := runCodingRuleOnCreatingTimeline.runCodingRuleOnDocumenting(ctx, &eventRemoveModel.TimelineModel); err != nil {
			return errors.WithMessage(err, "cannot run coding rule on creating timeline")
		}
	}

	return nil
}

func (srv *TimelineService[T]) NotifyTimelineRemove(ctx *titan.Context, eventRemoveModels ...*timeline.EventTimelineRemove) error {
	if len(eventRemoveModels) == 0 {
		return nil
	}
	if err := srv.handleTimelineRemove(ctx, eventRemoveModels...); err != nil {
		return errors.WithMessage(err, "handle timeline remove error")
	}
	// NOTE: validation
	lastItem := eventRemoveModels[len(eventRemoveModels)-1]
	if err := srv.HookBeforeAction.ExecuteOnDelete(ctx, lastItem); err != nil {
		return errors.WithMessage(err, "execute on delete error")
	}

	timeline, err := srv.FindByIdNotFilterDelete(ctx, *lastItem.TimelineModel.Id)
	if err != nil {
		return fmt.Errorf("cannot find timeline by id has error : %w", err)
	}
	if timeline == nil || timeline.Id == nil {
		return fmt.Errorf("cannot find timeline by id: %s", util.GetPointerValue(lastItem).TimelineModel.Id)
	}
	lastItem.TimelineModel = ConvertEntityToModel(*timeline)
	err = srv.HookAfterAction.ExecuteOnDelete(ctx, lastItem)
	if err != nil {
		return err
	}

	return srv.notifier.NotifyTimelineRemove(ctx, lastItem)
}

func (srv *TimelineService[T]) FindTerminalPsychotherapyApprovalById(ctx *titan.Context, patientId, terminalId uuid.UUID) (*timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy], error) {
	result, err := srv.timelinePsychotherapyRepo.GetPsychotherapyByTerminalId(ctx, patientId, terminalId)
	if err != nil {
		return nil, err
	}
	if result != nil {
		return result, nil
	}
	matchPatientState := bson.M{
		"$match": bson.M{
			timeline_repo.Field_PatientId:          patientId,
			timeline_repo.Field_timelineEntityType: common.TimelineEntityType_Psychotherapy,
			timeline_repo.Field_IsDeleted:          false,
		},
	}
	projectState := bson.M{
		"$project": bson.M{
			"entry": bson.M{
				"$objectToArray": "$payload.entries",
			},
		},
	}
	matchTerminalId := bson.M{
		"$match": bson.M{
			"entry.v.terminalid": terminalId,
		},
	}

	results := []struct {
		Id uuid.UUID `bson:"_id"`
	}{}

	err = srv.timelinePsychotherapyRepo.IDBClient.Aggregate(ctx, []bson.M{matchPatientState, projectState, matchTerminalId}, &results)
	if err != nil {
		return nil, err
	}
	if len(results) == 0 {
		return nil, nil
	}
	return srv.timelinePsychotherapyRepo.FindById(ctx, results[0].Id)
}

func (srv *TimelineService[T]) revertApproval(ctx *titan.Context, timelineModel *common.TimelineModel) error {
	result, err := srv.FindTerminalPsychotherapyApprovalById(ctx, timelineModel.PatientId, *timelineModel.Id)
	if err != nil {
		return fmt.Errorf("cannot find terminal psychotherapy approval by id has error : %w", err)
	}
	if result == nil || result.Id == nil {
		return nil
	}
	result.Payload.Status = patient_encounter.INPROGRESS
	if result.Payload.CheckIsBefore2017(ctx) {
		for k, v := range result.Payload.Entries {
			if v.TerminalId != nil && *v.TerminalId == *timelineModel.Id {
				result.Payload.Entries[k].TerminalId = nil
			}
		}
	} else {
		result.Payload.TerminateServiceId = nil
	}
	_, err = srv.timelinePsychotherapyRepo.Update(ctx, *result)
	if err != nil {
		return err
	}
	if len(result.Payload.GetEntryApprovalIds()) == 0 {
		return nil
	}
	return srv.timelinePsychotherapyRepo.UpdateStatusServiceEntries(ctx, result.Payload.GetEntryApprovalIds(), util.NewPointer(patient_encounter.IsApproval))
}

func (srv *TimelineService[T]) deleteTechnicalSchein(ctx *titan.Context, scheinID uuid.UUID) error {
	schein, err := srv.scheinRepo.FindById(ctx, scheinID)
	if err != nil {
		return fmt.Errorf("cannot find schein by id has error : %w", err)
	}
	if schein == nil || schein.Id == nil {
		return nil
	}

	if !util.GetPointerValue(schein.IsTechnicalSchein) {
		return nil
	}
	diagnoses, services, _, err := srv.GetTimelinesDiagnoseAndServiceByScheinIds(ctx, GetTimelinesDiagnoseAndServiceByScheinIdsRequest{
		PatientWithScheinIds: []timeline_repo.PatientWithScheinIds{
			{
				ScheinIds: []uuid.UUID{scheinID},
				PatientId: schein.PatientId,
			},
		},
	})
	if err != nil {
		return err
	}
	if len(diagnoses)+len(services) > 0 {
		return nil
	}

	return srv.scheinRepo.DeleteScheinById(ctx, scheinID)
}

func (srv *TimelineService[T]) handleTimelineUpdate(ctx *titan.Context, eventUpdateModels ...*timeline.EventTimelineUpdate) error {
	codingRuleMemo := function.Memoize[*codingRuleRunner[T], string]()
	for _, eventUpdateModel := range eventUpdateModels {
		if *eventUpdateModel.TimelineModel.Type == common.TimelineEntityType_Service {
			if err := srv.HandleUpdateAmountPyschotherapy(ctx, &eventUpdateModel.TimelineModel, true); err != nil {
				return fmt.Errorf("HandleUpdateAmountPyschotherapy: %w", err)
			}
			if eventUpdateModel.OldTimelineModel != nil && len(eventUpdateModel.OldTimelineModel.ScheinIds) > 0 && len(eventUpdateModel.TimelineModel.ScheinIds) > 0 {
				if err := srv.UpdateCalculatorPsychotherapy(ctx, eventUpdateModel.OldTimelineModel.ScheinIds[0], eventUpdateModel.TimelineModel.ScheinIds[0], &eventUpdateModel.TimelineModel); err != nil {
					return fmt.Errorf("UpdateCalculatorPsychotherapy: %w", err)
				}
				if slice.Contains(TerminalServiceCodes, eventUpdateModel.TimelineModel.EncounterServiceTimeline.Code) {
					if err := srv.checkAndApplyTerminalService(ctx, &eventUpdateModel.TimelineModel, eventUpdateModel.OldTimelineModel); err != nil {
						return fmt.Errorf("checkAndApplyTerminalService: %w", err)
					}
				}
			}
		}
		if eventUpdateModel.TimelineModel.EncounterDiagnoseTimeline != nil &&
			eventUpdateModel.TimelineModel.EncounterDiagnoseTimeline.Type == patient_encounter.DIAGNOSETYPE_PERMANENT {
			entity := ConvertModelToEntity[patient_encounter.EncounterDiagnoseTimeline](eventUpdateModel.TimelineModel)
			err := srv.syncPermanentDiagnoseEndDate(ctx, &entity)
			if err != nil {
				return errors.WithMessage(err, "sync permanent diagnose end date error")
			}
		}
		yearQuarter := util.ToYearQuarter(eventUpdateModel.TimelineModel.SelectedDate)
		runCodingRuleOnCreatingTimeline, err := codingRuleMemo(
			func() string {
				return fmt.Sprintf("%s_%s", yearQuarter, eventUpdateModel.TimelineModel.PatientId.String())
			},
			func() (*codingRuleRunner[T], error) {
				return srv.NewCodingRuleRunner(ctx, eventUpdateModel.TimelineModel.PatientId, yearQuarter)
			},
		)
		if err != nil {
			return errors.WithMessage(err, "cannot get coding rule runner")
		}
		if err := runCodingRuleOnCreatingTimeline.runCodingRuleOnDocumenting(ctx, &eventUpdateModel.TimelineModel); err != nil {
			return errors.WithMessage(err, "cannot run coding rule on creating timeline")
		}
	}
	return nil
}

func (srv *TimelineService[T]) NotifyTimelineUpdate(ctx *titan.Context, eventUpdateModel ...*timeline.EventTimelineUpdate) error {
	if len(eventUpdateModel) == 0 {
		return nil
	}
	if err := srv.handleTimelineUpdate(ctx, eventUpdateModel...); err != nil {
		return errors.WithMessage(err, "handle timeline update error")
	}
	// NOTE: validation
	lastItem := eventUpdateModel[len(eventUpdateModel)-1]
	if err := srv.HookBeforeAction.ExecuteOnUpdate(ctx, lastItem); err != nil {
		return errors.WithMessage(err, "hook before action error")
	}

	timeline, err := srv.FindById(ctx, *lastItem.TimelineModel.Id)
	if err != nil {
		return fmt.Errorf("cannot find timeline by id has error : %w", err)
	}
	if timeline == nil || timeline.Id == nil {
		return fmt.Errorf("cannot find timeline by id: %s", timeline.Id)
	}
	lastItem.TimelineModel = ConvertEntityToModel(*timeline)
	err = srv.HookAfterAction.ExecuteOnUpdate(ctx, lastItem)
	if err != nil {
		return errors.WithMessage(err, "hook after action error")
	}

	return srv.notifier.NotifyTimelineUpdate(ctx, lastItem)
}

type ValidationRequest struct {
	EntityType common.TimelineEntityType
	PatientId  uuid.UUID
	ContractId *string
}

func (srv *TimelineService[T]) UpdateTreatmentDoctorMedicationForm(ctx *titan.Context, patientId, medicationFormId, treatmentDoctorId uuid.UUID) error {
	_, err := srv.timelineRepo.UpdateTreatmentDoctorMedicationForm(ctx, &timeline_repo.UpdateTreatmentDoctorMedicationFormRequest{
		PatientId:         patientId,
		MedicationFormId:  medicationFormId,
		TreatmentDoctorId: treatmentDoctorId,
	})
	return err
}

func (srv *TimelineService[T]) GetDiagnosesByQuarter(ctx *titan.Context, patientId uuid.UUID, quarter, year int32) ([]common.TimelineModel, error) {
	d, err := srv.timelineRepo.GetDiagnosesByQuarter(ctx, patientId, util.YearQuarter{
		Quarter: quarter,
		Year:    year,
	})
	if err != nil {
		return nil, err
	}

	return slice.Map(d, ConvertEntityToModel), nil
}

func (srv *TimelineService[T]) GetActiveDiagnosesCodesUniqueByQuarter(ctx *titan.Context, patientId uuid.UUID, quarter, yearQuarter []int) ([]string, error) {
	d, err := srv.timelineDiagnosisRepo.GetByPatientIdAndQuarter(ctx, patientId, quarter, yearQuarter)
	if err != nil {
		return nil, fmt.Errorf("GetDiagnosesCodesUniqueByQuarter: %w", err)
	}

	var codes []string
	for _, v := range d {
		if !slice.Contains([]string{"DD", "DA", "D", "AD"}, v.Payload.Command) {
			continue
		}

		if v.Payload.ValidUntil != nil {
			continue
		}

		codes = append(codes, v.Payload.Code)
	}

	return slice.Uniq(codes), nil
}

func (srv *TimelineService[T]) GetMedicationFormById(ctx *titan.Context, patientId, medicationFormId uuid.UUID) (*timeline_repo.FormInfoTimeline, error) {
	return srv.timelineRepo.GetMedicationFormById(ctx, patientId, medicationFormId)
}

func (srv *TimelineService[T]) UpdateFormPrintDateTimeline(ctx *titan.Context, patientId, formId uuid.UUID, printDate int64) (*timeline_repo.TimelineEntity[T], error) {
	result, err := srv.timelineRepo.UpdateFormPrintDateTimeline(ctx, &timeline_repo.UpdateFormPrintDateTimelineRequest{
		PatientId: patientId,
		FormId:    formId,
		PrintDate: printDate,
	})
	if err != nil {
		return nil, err
	}
	if result == nil || result.Id == nil {
		return nil, nil
	}

	err = srv.notifier.NotifyTimelineUpdate(ctx, &timeline.EventTimelineUpdate{
		PatientId:     result.PatientId,
		TimelineModel: ConvertEntityToModel(*result),
	})
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (srv *TimelineService[T]) UpdateHimiPrintDate(ctx *titan.Context, himiprescriptionid uuid.UUID, printDate *int64) (*timeline_repo.TimelineEntity[T], error) {
	now := util.NowUnixMillis(ctx)
	if printDate != nil {
		now = *printDate
	}

	entity, err := srv.timelineRepo.UpdateHimiPrintDate(ctx, himiprescriptionid, now)
	if err != nil {
		return nil, err
	}

	if entity == nil {
		return nil, nil
	}

	err = srv.NotifyTimelineUpdate(ctx, &timeline.EventTimelineUpdate{
		PatientId:     entity.PatientId,
		TimelineModel: ConvertEntityToModel(*entity),
	})

	return entity, err
}

func (srv *TimelineService[T]) UpdateDigaPrescribe(ctx *titan.Context, id uuid.UUID, formInfo *diga.FormInfo) (*timeline_repo.TimelineEntity[T], error) {
	entity, err := srv.timelineRepo.UpdateDigaPrescribe(ctx, id, formInfo)
	if err != nil {
		return nil, err
	}

	if entity == nil {
		return nil, fmt.Errorf("not exists diga entry %s in timeline", id)
	}

	err = srv.NotifyTimelineUpdate(ctx, &timeline.EventTimelineUpdate{
		PatientId:     entity.PatientId,
		TimelineModel: ConvertEntityToModel(*entity),
	})

	return entity, err
}

func (srv *TimelineService[T]) UpdateHeimiPrintDate(ctx *titan.Context, heimiPrescriptionId uuid.UUID, printDate *int64) (*timeline_repo.TimelineEntity[T], error) {
	now := util.NowUnixMillis(ctx)
	if printDate != nil {
		now = *printDate
	}

	entity, err := srv.timelineRepo.UpdateHeimiPrintDate(ctx, heimiPrescriptionId, now)
	if err != nil {
		return nil, err
	}

	if entity == nil {
		return nil, nil
	}

	err = srv.NotifyTimelineUpdate(ctx, &timeline.EventTimelineUpdate{
		PatientId:     entity.PatientId,
		TimelineModel: ConvertEntityToModel(*entity),
	})

	return entity, err
}

func (r *TimelineService[T]) GetPrescribeHimi(ctx *titan.Context, himiprescriptionid uuid.UUID) (*timeline_repo.TimelineEntity[T], error) {
	rs, err := r.timelineRepo.GetPrescribeHimiById(ctx, himiprescriptionid)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return rs, nil
}

func (r *TimelineService[T]) GetDoctorLetterById(ctx *titan.Context, doctorLetterId uuid.UUID) (*timeline_repo.TimelineEntity[T], error) {
	rs, err := r.timelineRepo.GetDoctorLetterById(ctx, doctorLetterId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return rs, nil
}

func (r *TimelineService[T]) GetMailItem(ctx *titan.Context, patientId uuid.UUID, messageId string) (*timeline_repo.TimelineEntity[T], error) {
	return r.timelineRepo.GetMailItemByMessageId(ctx, timeline_repo.GetMailItemByMessageId{
		PatientId: patientId,
		MessageId: messageId,
	})
}

func (r *TimelineService[T]) GetPrescribeHeimiById(ctx *titan.Context, prescribeId uuid.UUID) (*timeline_repo.TimelineEntity[T], error) {
	rs, err := r.timelineRepo.GetPrescribeHeimiById(ctx, prescribeId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return rs, nil
}

func (r *TimelineService[T]) GetPermanentDiagnoses(ctx *titan.Context, patientIds []uuid.UUID) (
	[]timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline],
	error,
) {
	rs, err := r.timelineRepo.GetPermanentDiagnoses(ctx, patientIds)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var timelineDiagnoses []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]
	for _, timelineEntity := range rs {
		if timelineEntity.Type == common.TimelineEntityType_Diagnose {
			timelineDiagnoses = append(timelineDiagnoses, timeline_repo.ToEntityTypeFromT[T, patient_encounter.EncounterDiagnoseTimeline](timelineEntity))
		}
	}
	return timelineDiagnoses, nil
}

func (r *TimelineService[T]) GetMedicinePrescriptions(ctx *titan.Context, patientIds []uuid.UUID, contractId *string, quarter, year int32) (
	[]*common.TimelineModel,
	error,
) {
	rs, err := r.timelineRepo.GetMedicinePrescriptions(ctx, &timeline_repo.GetMedicinePrescriptions{
		PatientIds: patientIds,
		ContractId: contractId,
		Quarter:    quarter,
		Year:       year,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return slice.Map(rs, func(e timeline_repo.TimelineEntity[T]) *common.TimelineModel {
		temp := ConvertEntityToModel(e)
		return &temp
	}), nil
}

// NOTE: keep the function name for keep tracking
// GetEncounterByP4ValidationReport gets all diagnoses of a quarter having Certainty value is mvz_patient_encounter.G
func (srv *TimelineService[T]) GetEncounterByP4ValidationReport(ctx *titan.Context, request GetEncounterByP4ValidationReportRequest) ([]timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline], error) {
	return srv.timelineDiagnosisRepo.GetEncounterByP4ValidationReport(ctx, timeline_repo.GetEncounterByP4ValidationReportRequest{
		PatientId:  request.PatientId,
		ContractId: request.ContractId,
		Quarter:    request.Quarter,
		Year:       request.Year,
	})
}

func (r *TimelineService[T]) GetAllBillableEncounters(ctx *titan.Context) ([]timeline_repo.TimelineEntity[T], error) {
	scheinIDsWithBillCheck, err := r.scheinRepo.GetAllScheinIDsByMainGroup(ctx, []schein_common.MainGroup{
		schein_common.HZV,
		schein_common.FAV,
	})
	if err != nil {
		return nil, err
	}

	if len(scheinIDsWithBillCheck) == 0 {
		return []timeline_repo.TimelineEntity[T]{}, nil
	}
	return r.timelineRepo.GetAllBillableTimelineItems(ctx, scheinIDsWithBillCheck)
}

// GetTimelinesDiagnoseAndServiceByScheinIds
// return all diagnose and service timelines by schein ids
//
//nolint:gocritic // find the better name for this function
func (r *TimelineService[T]) GetTimelinesDiagnoseAndServiceByScheinIds(ctx *titan.Context, req GetTimelinesDiagnoseAndServiceByScheinIdsRequest) (
	[]timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline],
	[]timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline],
	[]timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy],
	error,
) {
	timelineEntities, err := r.timelineRepo.GetTimelinesForBillingByScheinIds(ctx, timeline_repo.GetTimelinesForBillingByScheinIdsRequest{
		PatientWithScheinIds:  req.PatientWithScheinIds,
		ExcludeEABServiceCode: req.ExcludeEABServiceCode,
	})
	if err != nil {
		return nil, nil, nil, err
	}
	var (
		timelineDiagnoses     []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]
		timelineServices      []timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]
		timelinePsychotherapy []timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy]
	)
	for _, timelineEntity := range timelineEntities {
		if timelineEntity.Type == common.TimelineEntityType_Diagnose {
			timelineDiagnoseNonBilling := timeline_repo.ToEntityTypeFromT[T, patient_encounter.EncounterDiagnoseTimeline](timelineEntity)
			if timelineDiagnoseNonBilling.Payload.Errors == nil || len(*timelineDiagnoseNonBilling.Payload.Errors) == 0 {
				timelineDiagnoses = append(timelineDiagnoses, timelineDiagnoseNonBilling)
				continue
			}

			hasBillableError := slice.Any(util.GetPointerValue(timelineDiagnoseNonBilling.Payload.Errors), func(t *patient_encounter.EncounterItemError) bool {
				return t.ErrorCode == string(error_code.ErrorCode_Validation_MessageNotBillingAble) || t.ErrorCode == string(error_code.ErrorCode_Validation_MessageNotFillingForBilling) || t.ErrorCode == string(error_code.ErrorCode_Validation_MessageNeedPrimaryCode)
			})

			if !hasBillableError {
				timelineDiagnoses = append(timelineDiagnoses, timelineDiagnoseNonBilling)
			}
		}
		if timelineEntity.Type == common.TimelineEntityType_Service {
			timelineServices = append(timelineServices, timeline_repo.ToEntityTypeFromT[T, patient_encounter.EncounterServiceTimeline](timelineEntity))
		}
		if timelineEntity.Type == common.TimelineEntityType_Psychotherapy {
			timelinePsychotherapy = append(timelinePsychotherapy, timeline_repo.ToEntityTypeFromT[T, patient_encounter.EncounterPsychotherapy](timelineEntity))
		}
	}
	return timelineDiagnoses, timelineServices, timelinePsychotherapy, nil
}

type UpdateCallback[T timeline_repo.TimelineEntityConstant] func(timelineEntity *timeline_repo.TimelineEntity[T])

func (r *TimelineService[T]) UpdateByIdWithCallback(ctx *titan.Context, roomId uuid.UUID, patientId *uuid.UUID, updateFunc UpdateCallback[T]) (*timeline_repo.TimelineEntity[T], error) {
	filter := bson.M{
		"payload.waitingRoomPatient.waitingroom":    roomId,
		"payload.waitingRoomPatient.endwaitingtime": 0,
	}

	if patientId != nil {
		filter["payload.waitingRoomPatient.patientid"] = patientId
	}

	entity, err := r.timelineRepo.FindOne(ctx, filter)
	if err != nil {
		return nil, err
	}
	if entity == nil || entity.Id == nil {
		return nil, nil
	}
	entity.RecentAuditLogs = append(entity.RecentAuditLogs, timeline_repo.AuditLog{
		AuditLogId: util.NewUUID(),
		UserId:     ctx.UserInfo().UserUUID(),
		Date:       util.Now(ctx),
		ActionType: common.Edit,
	})
	updateFunc(entity)
	result, err := r.timelineRepo.EditTimelineById(ctx, *entity)
	if err != nil {
		return nil, err
	}

	if result == nil {
		return nil, nil
	}

	err = r.NotifyTimelineUpdate(ctx, &timeline.EventTimelineUpdate{
		PatientId:     result.PatientId,
		TimelineModel: ConvertEntityToModel(*result),
	})

	return result, err
}

type UpdatesCallback[T timeline_repo.TimelineEntityConstant] func(timelineEntities []timeline_repo.TimelineEntity[T]) []timeline_repo.TimelineEntity[T]

func (r *TimelineService[T]) UpdateByIdsWithCallback(ctx *titan.Context, timelineIds []uuid.UUID, updatesFunc UpdatesCallback[T]) ([]timeline_repo.TimelineEntity[T], error) {
	entities, err := r.timelineRepo.FindByIds(ctx, timelineIds)
	if err != nil {
		return nil, err
	}
	if len(entities) == 0 {
		return nil, nil
	}
	for i := range entities {
		entities[i].RecentAuditLogs = append(entities[i].RecentAuditLogs, timeline_repo.AuditLog{
			AuditLogId: util.NewUUID(),
			UserId:     ctx.UserInfo().UserUUID(),
			Date:       util.Now(ctx),
			ActionType: common.Edit,
		})
	}
	newEntities := updatesFunc(entities)
	resultsUpdated, err := r.timelineRepo.EditTimelineByIds(ctx, newEntities)
	if err != nil {
		return nil, err
	}

	if len(resultsUpdated) == 0 {
		return nil, nil
	}

	err = r.NotifyTimelineUpdate(ctx, &timeline.EventTimelineUpdate{
		PatientId:     resultsUpdated[0].PatientId,
		TimelineModel: ConvertEntityToModel(resultsUpdated[0]),
	})

	return resultsUpdated, err
}

func (r *TimelineService[T]) UpdateWaitingRoomInCalendarEntry(ctx *titan.Context, entryIds []uuid.UUID, waitingRoomName *string, acceptableWaitingTimeInMinutes int64, activeTimeMeasurement bool) error {
	if len(entryIds) == 0 {
		return nil
	}

	err := r.timelineRepo.UpdateWaitingRoomInCalendarEntry(ctx, entryIds, waitingRoomName, acceptableWaitingTimeInMinutes, activeTimeMeasurement)
	if err != nil {
		return err
	}

	result, err := r.timelineRepo.FindById(ctx, entryIds[0])
	if err != nil {
		return err
	}

	if result == nil {
		return nil
	}

	return r.NotifyTimelineUpdate(ctx, &timeline.EventTimelineUpdate{
		PatientId:     result.PatientId,
		TimelineModel: ConvertEntityToModel(*result),
	})
}

func (srv *TimelineService[T]) UpdateSuggestionRuleApplied(ctx *titan.Context, timelineId uuid.UUID, ruleId string) error {
	err := srv.timelineRepo.UpdateSuggestionRuleApplied(ctx, timelineId, ruleId)
	return err
}

func (srv *TimelineService[T]) IgnoreSuggestionRule(ctx *titan.Context, patientId uuid.UUID, ruleId string, encounterDate int64) error {
	yearQuarter := util.ToYearQuarter(encounterDate)
	err := srv.sdkrwService.IgnoreSdkrwRule(ctx, patientId, encounterDate, ruleId)
	if err != nil {
		return errors.WithStack(err)
	}

	timelineDiagnosis, err := srv.timelineDiagnosisRepo.GetDiagnosesByQuarter(ctx, patientId, yearQuarter)
	if err != nil {
		return errors.WithStack(err)
	}

	if len(timelineDiagnosis) == 0 {
		return nil
	}

	for _, et := range timelineDiagnosis {
		if et.Payload.DiagnoseSuggestions != nil && len(*et.Payload.DiagnoseSuggestions) > 0 {
			for _, s := range *et.Payload.DiagnoseSuggestions {
				if s.RuleId == ruleId {
					s.Applied = true
				}
			}
		}
	}
	_, err = srv.timelineDiagnosisRepo.UpdateMany(ctx, slice.ToPointerType(timelineDiagnosis))
	return err
}

func (srv *TimelineService[T]) AssignScheinToDiagnosesByQuarter(ctx *titan.Context, patientId, newSheinId uuid.UUID, quarter, year int32) error {
	diagnoses, err := srv.timelineRepo.GetDiagnosesByQuarter(ctx, patientId, util.YearQuarter{
		Year:    year,
		Quarter: quarter,
	})
	if err != nil {
		return errors.WithStack(err)
	}

	if len(diagnoses) == 0 {
		return nil
	}

	diagnoseIds := slice.Map(diagnoses, func(diag timeline_repo.TimelineEntity[T]) uuid.UUID {
		return *diag.Id
	})

	err = srv.timelineRepo.UpdateDiagnosesScheinId(ctx, diagnoseIds, newSheinId)

	return err
}

func (srv *TimelineService[T]) AssignScheinToDiagnoses(ctx *titan.Context, diagnoseIds []uuid.UUID, newSheinId uuid.UUID) error {
	err := srv.timelineRepo.UpdateDiagnosesScheinId(ctx, diagnoseIds, newSheinId)
	return err
}

func (srv *TimelineService[T]) GetLastDocumentedQuarter(ctx *titan.Context, request GetLastDocumentedQuarterRequest) (*util.YearQuarter, error) {
	result, err := srv.timelineRepo.GetLastDocumentedEntity(ctx, timeline_repo.GetLastDocumentedEntityRequest{
		PatientId:          request.PatientId,
		Year:               &request.Year,
		Quarter:            &request.Quarter,
		TimelineEntityType: request.timelineEntityType,
	})
	if err != nil {
		return nil, err
	}

	if result == nil {
		return &util.YearQuarter{
			Year:    request.Year,
			Quarter: request.Quarter,
		}, nil
	}

	return &util.YearQuarter{
		Year:    int32(result.Year),
		Quarter: int32(result.Quarter),
	}, nil
}

// TODO:PSYCHOTHERAPY
func (srv *TimelineService[T]) GetTherapies(ctx *titan.Context, request timeline.GetTherapiesRequest) (*timeline.GetTherapiesResponse, error) {
	result := timeline.GetTherapiesResponse{}
	schein, err := srv.scheinRepo.FindById(ctx, request.ScheinId)
	if err != nil {
		return nil, err
	}
	if schein == nil {
		return nil, err
	}
	scheinsWithCostUnit, err := srv.scheinRepo.GetScheinsByInsuranceId(ctx, schein.Schein.InsuranceId)
	if err != nil {
		return nil, err
	}
	scheinWithCostUnitIds := slice.Map(scheinsWithCostUnit, func(s schein_repo.ScheinRepo) uuid.UUID {
		return *s.Id
	})

	entries, err := srv.timelinePsychotherapyRepo.GetPsychotherapy(ctx, &timeline_repo.GetPsychotherapyRequest{
		PatientId: request.PatientId,
		ScheinIds: scheinWithCostUnitIds,
		Statuses: []patient_encounter.EncounterPsychotherapyStatus{
			patient_encounter.INPROGRESS,
			patient_encounter.READY_TO_BILL,
		},
	})
	if err != nil {
		return nil, err
	}
	if len(entries) == 0 {
		return &result, nil
	}
	timelineIds := []uuid.UUID{}
	scheinIds := []uuid.UUID{}
	models := slice.Map(entries, func(t timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy]) common.TimelineModel {
		temp := ConvertEntityToModel(t)
		if temp.EncounterPsychotherapy.ScheinId != nil {
			scheinIds = append(scheinIds, *temp.EncounterPsychotherapy.ScheinId)
		}
		timelineIds = slice.Uniq(append(timelineIds, temp.EncounterPsychotherapy.GetEntryApprovalIds()...))
		return temp
	})
	timelines, err := srv.timelineServiceRepo.FindByIds(ctx, timelineIds, options.Find().SetSort(
		bson.D{
			{
				Key:   timeline_repo.Field_Selected_Date,
				Value: 1,
			},
			{
				Key:   timeline_repo.Field_CreatedAt,
				Value: 1,
			},
		},
	))
	if err != nil {
		return nil, err
	}
	slice.SortBy(timelines, func(a, b timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) bool {
		if a.SelectedDate.Format(util.DateFormat) == b.SelectedDate.Format(util.DateFormat) {
			return a.CreatedAt.UnixMilli() < b.CreatedAt.UnixMilli()
		}
		return a.SelectedDate.UnixMilli() < b.SelectedDate.UnixMilli()
	})
	scheins, err := srv.scheinRepo.GetByIds(ctx, scheinIds)
	if err != nil {
		return nil, err
	}
	for _, m := range models {
		if len(m.EncounterPsychotherapy.ServiceCodes) == 0 {
			continue
		}
		temp := slice.FindOne(scheins, func(t schein_repo.ScheinRepo) bool {
			return t.Id.String() == m.EncounterPsychotherapy.ScheinId.String()
		})
		if m.EncounterPsychotherapy != nil {
			remapEntries := map[string]*patient_encounter.ServiceCodeApproval{}
			for k, v := range m.EncounterPsychotherapy.Entries {
				if k != "" {
					remapEntries[k] = v
				}
			}
			m.EncounterPsychotherapy.Entries = remapEntries
			if len(remapEntries) == 0 {
				continue
			}
		}
		if temp != nil {
			psychotherapy := slice.FindOne(temp.ScheinDetail.Psychotherapy, func(t schein_common.Psychotherapy) bool {
				return t.Id.String() == m.Id.String()
			})
			if psychotherapy != nil {
				if !(psychotherapy.IsInsuranceInformedTherapy == nil || psychotherapy.IsInsuranceInformedTherapy != nil && !*psychotherapy.IsInsuranceInformedTherapy) {
					continue
				}
				result.Pyschotherapies = append(result.Pyschotherapies, timeline.TherapiesResponse{
					TimelineModel: m,
					Pyschotherapy: *psychotherapy,
				})
			}
		}
	}
	return &result, nil
}

func MapDiagnoseSuggestionByCheckTime(timelineModel *common.TimelineModel, checkTime master_data_common.CheckTime) common.TimelineModel {
	encounter := timelineModel.EncounterDiagnoseTimeline
	if encounter == nil || encounter.DiagnoseSuggestions == nil {
		return *timelineModel
	}
	suggestions := slice.Filter(*encounter.DiagnoseSuggestions, func(suggestion *patient_encounter.DiagnoseSuggestion) bool {
		return suggestion.CheckTime == checkTime
	})
	timelineModel.EncounterDiagnoseTimeline.DiagnoseSuggestions = &suggestions

	return *timelineModel
}

// Convert list of non-empty entity that belong to the same service chain to 1 timeline model
func ConvertEntityChainToModel(entries []timeline_repo.TimelineEntity[any]) common.TimelineModel {
	timelineEntity := entries[0]
	model := common.TimelineModel{
		Id:                timelineEntity.ChainId,
		ContractId:        timelineEntity.ContractId,
		TreatmentDoctorId: timelineEntity.TreatmentDoctorId,
		BillingDoctorId:   timelineEntity.BillingDoctorId,
		PatientId:         timelineEntity.PatientId,
		ScheinIds:         timelineEntity.ScheinIds,
		EncounterCase:     timelineEntity.EncounterCase,
		TreatmentCase:     timelineEntity.TreatmentCase,
		Type:              util.NewPointer(common.TimelineEntityType_Service_Chain),
		CreatedAt:         util.NewPointer(util.ConvertTimeToMiliSecond(timelineEntity.CreatedAt)),
		CreatedAtString:   &timelineEntity.CreatedAtString,
		CreatedBy:         &timelineEntity.CreatedBy,
		Quarter:           int32(timelineEntity.Quarter),
		Year:              int32(timelineEntity.Year),
		AuditLogs: slice.Map(timelineEntity.RecentAuditLogs, func(t timeline_repo.AuditLog) common.AuditLog {
			return common.AuditLog{
				Id:         util.GetPointerValue(t.AuditLogId),
				UserId:     util.GetPointerValue(t.UserId),
				Date:       util.ConvertTimeToMiliSecond(t.Date),
				ActionType: t.ActionType,
			}
		}),
		SelectedDate:     util.ConvertTimeToMiliSecond(timelineEntity.SelectedDate),
		IsImported:       &timelineEntity.IsImported,
		AssignedToBsnrId: timelineEntity.GetBsnr(),
	}
	services := []patient_encounter.EncounterServiceTimeline{}
	goaServices := []patient_encounter.EncounterGoaService{}
	uvGoaServices := []patient_encounter.EncounterUvGoaService{}

	for _, entry := range entries {
		switch entry.Type {
		case common.TimelineEntityType_Service:
			service := *getPayloadByType[patient_encounter.EncounterServiceTimeline](entry.Payload)
			service.ServiceId = entry.Id
			services = append(services, service)
			model.EncounterServiceChain = &common.EncounterServiceChain{
				Services:                 services,
				EncounterServiceChainRaw: util.GetPointerValue(timelineEntity.EncounterServiceChainRaw),
			}
			model.Type = util.NewPointer(common.TimelineEntityType_Service_Chain)
		case common.TimelineEntityType_Service_GOA:
			service := *getPayloadByType[patient_encounter.EncounterGoaService](entry.Payload)
			service.ServiceId = entry.Id
			goaServices = append(goaServices, service)
			model.EncounterGoaServiceChain = &common.EncounterGoaServiceChain{
				GoaServices:                 goaServices,
				EncounterGoaServiceChainRaw: util.GetPointerValue(timelineEntity.EncounterGoaServiceChainRaw),
			}
			model.Type = util.NewPointer(common.TimelineEntityType_Service_GOA_Chain)
		case common.TimelineEntityType_Service_UV_GOA:
			service := *getPayloadByType[patient_encounter.EncounterUvGoaService](entry.Payload)
			service.ServiceId = entry.Id
			uvGoaServices = append(uvGoaServices, service)
			model.EncounterUvGoaServiceChain = &common.EncounterUvGoaServiceChain{
				UvGoaServices:                 uvGoaServices,
				EncounterUvGoaServiceChainRaw: util.GetPointerValue(timelineEntity.EncounterUvGoaServiceChainRaw),
			}
			model.Type = util.NewPointer(common.TimelineEntityType_Service_UV_GOA_Chain)
		}
	}

	return model
}

func ConvertEntityAnyToModel(timelineEntity timeline_repo.TimelineEntity[any]) common.TimelineModel {
	model := common.TimelineModel{
		Id:                timelineEntity.Id,
		ContractId:        timelineEntity.ContractId,
		TreatmentDoctorId: timelineEntity.TreatmentDoctorId,
		BillingDoctorId:   timelineEntity.BillingDoctorId,
		PatientId:         timelineEntity.PatientId,
		ScheinIds:         timelineEntity.ScheinIds,
		EncounterCase:     timelineEntity.EncounterCase,
		TreatmentCase:     timelineEntity.TreatmentCase,
		Type:              &timelineEntity.Type,
		CreatedAt:         util.NewPointer(util.ConvertTimeToMiliSecond(timelineEntity.CreatedAt)),
		CreatedAtString:   &timelineEntity.CreatedAtString,
		CreatedBy:         &timelineEntity.CreatedBy,
		Quarter:           int32(timelineEntity.Quarter),
		Year:              int32(timelineEntity.Year),
		AuditLogs: slice.Map(timelineEntity.RecentAuditLogs, func(t timeline_repo.AuditLog) common.AuditLog {
			return common.AuditLog{
				Id:         util.GetPointerValue(t.AuditLogId),
				UserId:     util.GetPointerValue(t.UserId),
				Date:       util.ConvertTimeToMiliSecond(t.Date),
				ActionType: t.ActionType,
			}
		}),
		SelectedDate:     util.ConvertTimeToMiliSecond(timelineEntity.SelectedDate),
		IsImported:       &timelineEntity.IsImported,
		AssignedToBsnrId: timelineEntity.AssignedToBsnrId,
	}
	switch timelineEntity.Type {
	case common.TimelineEntityType_Diagnose:
		model.EncounterDiagnoseTimeline = getPayloadByType[patient_encounter.EncounterDiagnoseTimeline](timelineEntity.Payload)
	case common.TimelineEntityType_Service:
		model.EncounterServiceTimeline = getPayloadByType[patient_encounter.EncounterServiceTimeline](timelineEntity.Payload)
	case common.TimelineEntityType_Note:
		model.EncounterNoteTimeline = getPayloadByType[patient_encounter.EncounterNoteTimeline](timelineEntity.Payload)
	case common.TimelineEntityType_MedicinePlan:
		model.EncounterMedicinePlanHistory = getPayloadByType[patient_encounter.EncounterMedicinePlanHistory](timelineEntity.Payload)
	case common.TimelineEntityType_MedicinePrescription:
		model.EncounterMedicinePrescription = getPayloadByType[patient_encounter.EncounterMedicinePrescription](timelineEntity.Payload)
	case common.TimelineEntityType_HimiPrescription:
		model.EncounterHimiPrescription = getPayloadByType[patient_encounter.EncounterHimiPrescription](timelineEntity.Payload)
	case common.TimelineEntityType_HeimiPrescription:
		model.EncounterHeimiPrescription = getPayloadByType[patient_encounter.EncounterHeimiPrescription](timelineEntity.Payload)
	case common.TimelineEntityType_Lab:
		model.EncounterLab = getPayloadByType[patient_encounter.EncounterLab](timelineEntity.Payload)
	case common.TimelineEntityType_Form:
		model.EncounterForm = getPayloadByType[patient_encounter.EncounterForm](timelineEntity.Payload)
	case common.TimelineEntityType_PatientMedicalData:
		model.EncounterPatientMedicalData = getPayloadByType[patient_encounter.EncounterPatientMedicalData](timelineEntity.Payload)
	case common.TimelineEntityType_Room:
		model.EncounterCalendarTimeline = getPayloadByType[patient_encounter.EncounterCalendarTimeline](timelineEntity.Payload)
	case common.TimelineEntityType_MailItem:
		model.EmailItem = getPayloadByType[mail_common.EmailItem](timelineEntity.Payload)
	case common.TimelineEntityType_DoctorLetter:
		model.DoctorLetter = getPayloadByType[doctor_letter_common.DoctorLetter](timelineEntity.Payload)
	case common.TimelineEntityType_BillingPatient:
		model.ReferencePayload = getPayloadByType[common.ReferencePayload](timelineEntity.Payload)
	case common.TimelineEntityType_EHIC:
		model.EncounterEHIC = getPayloadByType[patient_encounter.EncounterEHIC](timelineEntity.Payload)
	case common.TimelineEntityType_Psychotherapy:
		model.EncounterPsychotherapy = getPayloadByType[patient_encounter.EncounterPsychotherapy](timelineEntity.Payload)
	case common.TimelineEntityType_Medicine:
		model.EncounterMedicine = getPayloadByType[medicine_common.Medicine](timelineEntity.Payload)
	case common.TimelineEntityType_EDMPEnrollment:
		model.EDMPEnrollmentInfo = getPayloadByType[edmp_common.EnrollmentInfo](timelineEntity.Payload)
	case common.TimelineEntityType_EDMPEnrollment_Document:
		model.DocumentationOverview = getPayloadByType[edmp_common.DocumentationOverview](timelineEntity.Payload)
	case common.TimelineEntityType_EDOKU_Document:
		model.DocumentationOverview = getPayloadByType[edmp_common.DocumentationOverview](timelineEntity.Payload)
	case common.TimelineEntityType_Service_GOA:
		model.EncounterGoaService = getPayloadByType[patient_encounter.EncounterGoaService](timelineEntity.Payload)
	case common.TimelineEntityType_Calendar:
		model.EncounterAppointmentTimeline = getPayloadByType[patient_encounter.EncounterAppointmentTimeline](timelineEntity.Payload)
	case common.TimelineEntityType_DocumentManagement:
		model.EncounterDocumentManagementTimeline = getPayloadByType[patient_encounter.EncounterDocumentManagement](timelineEntity.Payload)
	case common.TimelineEntityType_Diga:
		model.DigaPrescriptionTimeline = getPayloadByType[diga.Prescribe](timelineEntity.Payload)
	case common.TimelineEntityType_Arriba:
		model.EncounterArriba = getPayloadByType[patient_encounter.EncounterArriba](timelineEntity.Payload)
	case common.TimelineEntityType_GDT:
		model.EncounterGDT = getPayloadByType[patient_encounter.EncounterGDT](timelineEntity.Payload)
	case common.TimelineEntityType_LDT:
		model.EncounterLDT = getPayloadByType[patient_encounter.EncounterLDT](timelineEntity.Payload)
	case common.TimelineEntityType_Service_UV_GOA:
		model.EncounterUvGoaService = getPayloadByType[patient_encounter.EncounterUvGoaService](timelineEntity.Payload)
	case common.TimelineEntityType_Customize:
		model.EncounterCustomize = getPayloadByType[patient_encounter.EncounterCustomize](timelineEntity.Payload)
	default:
		panic("unknown type") // should be panic to find this fast
	}
	return model
}

// TODO:PSYCHOTHERAPY
func (srv *TimelineService[T]) GetAmountServiceCode(ctx *titan.Context, request timeline.GetAmountServiceCodeRequest) (*timeline.GetAmountServiceCodeResponse, error) {
	entries, err := srv.timelineRepo.Find(ctx, bson.M{
		timeline_repo.Field_PatientId: request.PatientId,
		timeline_repo.Field_Code: bson.M{
			"$in": request.ServiceCodes,
		},
		timeline_repo.Field_Quarter: request.Quarter,
		timeline_repo.Field_Year:    request.Year,
	})
	if err != nil {
		return nil, err
	}
	result := map[string]int64{}
	for _, e := range entries {
		entry := ConvertEntityToModel(*e)
		if entry.EncounterServiceTimeline != nil {
			_, ok := result[entry.EncounterServiceTimeline.Code]
			if !ok {
				result[entry.EncounterServiceTimeline.Code] = 0
			}
			result[entry.EncounterServiceTimeline.Code] += 1
		}
	}
	return &timeline.GetAmountServiceCodeResponse{
		AmountServiceCode: result,
	}, nil
}

func (srv *TimelineService[T]) Find(ctx *titan.Context, filter any, opts ...*options.FindOptions) ([]timeline_repo.TimelineEntity[T], error) {
	return slice.ToValueTypeWithError(srv.timelineRepo.Find(ctx, filter, opts...))
}

func (srv *TimelineService[T]) UpdateManyBillingInforForMedicinePrescription(
	ctx *titan.Context,
	billingInfo *domains_api_common.BillingInfo,
	ids []uuid.UUID,
) ([]timeline_repo.TimelineEntity[T], error) {
	return srv.timelineRepo.UpdateManyBillingInforForMedicinePrescription(ctx, billingInfo, ids)
}

func (srv *TimelineService[T]) GetPreParticipationServiceCodes(ctx *titan.Context, patientId uuid.UUID) ([]timeline_repo.TimelineEntity[T], error) {
	return srv.timelineRepo.GetPreParticipationServiceCodes(ctx, patientId)
}

func (srv *TimelineService[T]) MarkNotApprovedPyschotherapyForTimelineEntry(ctx *titan.Context, request timeline.MarkNotApprovedPyschotherapyRequest) (*timeline_repo.TimelineEntity[T], error) {
	return srv.timelineRepo.MarkNotApprovedPyschotherapyForTimelineEntry(ctx, request.TimelineId)
}

func (srv *TimelineService[T]) MarkNotApprovedPyschotherapy(ctx *titan.Context, timelineModel *common.TimelineModel) error {
	if timelineModel.EncounterServiceTimeline == nil {
		return nil
	}
	entry, err := srv.timelinePsychotherapyRepo.FindOne(ctx, bson.M{
		fmt.Sprintf("%s.%s.%s", timeline_repo.Field_Psychotherapy_Entries, timelineModel.EncounterServiceTimeline.Code, "entryids"): *timelineModel.Id,
		timeline_repo.Field_IsDeleted:          false,
		timeline_repo.Field_PatientId:          timelineModel.PatientId,
		timeline_repo.Field_timelineEntityType: common.TimelineEntityType_Psychotherapy,
		timeline_repo.Field_Psychotherapy_TerminalServiceId: bson.M{
			"$exists": true,
		},
	})
	if err != nil {
		return err
	}
	if entry == nil {
		return nil
	}
	if entry.Payload.TerminateServiceId == nil {
		return nil
	}
	terminateService, err := srv.FindById(ctx, *entry.Payload.TerminateServiceId)
	if err != nil {
		return err
	}
	if terminateService == nil {
		return nil
	}
	if len(terminateService.ScheinIds) == 0 {
		return nil
	}
	schein, err := srv.scheinRepo.FindById(ctx, terminateService.ScheinIds[0])
	if err != nil {
		return err
	}
	if schein == nil {
		return nil
	}
	if schein.IsBilled {
		return nil
	}
	if _, err := srv.timelineRepo.DeleteById(ctx, *entry.Payload.TerminateServiceId); err != nil {
		return err
	}
	entry.Payload.Status = patient_encounter.INPROGRESS
	entry.Payload.TerminateServiceId = nil
	_, err = srv.timelinePsychotherapyRepo.Update(ctx, *entry)
	return err
}

func (srv *TimelineService[T]) ValidateDiagnose(ctx *titan.Context, request *ValidateDiagnoseRequest) ([]common.ValidationDiagnoseResult, error) {
	results := []common.ValidationDiagnoseResult{}
	if len(request.IcdCode) == 0 {
		return results, nil
	}

	diagnosis, err := srv.sdicdService.GetDiagnoseByCodes(ctx, sdicd_service.GetDiagnoseByCodesRequest{
		Codes: request.IcdCode,
		Year:  util.NowYear(ctx),
	})
	if err != nil {
		return nil, err
	}

	for _, typeCheck := range request.TypeCheck {
		switch typeCheck {
		case common.Secondary:
			codes := []string{}
			isPrimary := false
			isSecondary := false
			for _, v := range diagnosis {
				if v.IsPrimary() {
					isPrimary = true
					break
				}

				isSecondary = true
				codes = append(codes, v.Code)
			}
			if isSecondary && !isPrimary && len(codes) > 0 {
				results = append(results, common.ValidationDiagnoseResult{
					Codes:     codes,
					ErrorType: common.Secondary,
				})
			}
		case common.NonAble:
			codes := []string{}
			for _, v := range diagnosis {
				if !v.Billable {
					codes = append(codes, v.Code)
				}
			}

			if len(codes) > 0 {
				results = append(results, common.ValidationDiagnoseResult{
					Codes:     codes,
					ErrorType: common.NonAble,
				})
			}
		case common.Reserved:
			codes := []string{}
			for _, v := range diagnosis {
				if v.Schlusselnummer {
					continue
				}
				codes = append(codes, v.Code)
			}

			if len(codes) > 0 {
				results = append(results, common.ValidationDiagnoseResult{
					Codes:     codes,
					ErrorType: common.Reserved,
				})
			}
		case common.NonBilling:
			codes := []string{}
			for _, v := range diagnosis {
				if !v.Schlusselnummer || !v.Billable {
					codes = append(codes, v.Code)
				}
			}

			if len(codes) > 0 {
				results = append(results, common.ValidationDiagnoseResult{
					Codes:     codes,
					ErrorType: common.NonBilling,
				})
			}
		case common.All:
			isPrimary := false
			isSecondary := false
			codes := []string{}
			for _, v := range diagnosis {
				if !v.Schlusselnummer || !v.Billable {
					codes = append(codes, v.Code)
					results = append(results, common.ValidationDiagnoseResult{
						Codes:     codes,
						ErrorType: common.NonBilling,
					})
					break
				}

				if v.IsPrimary() {
					isPrimary = true
					continue
				}

				isSecondary = true
				codes = append(codes, v.Code)
			}
			if isSecondary && !isPrimary {
				results = append(results, common.ValidationDiagnoseResult{
					Codes:     codes,
					ErrorType: common.Secondary,
				})
			}
		case common.NonExist:
			if len(diagnosis) == 0 {
				results = append(results, common.ValidationDiagnoseResult{
					Codes:     request.IcdCode,
					ErrorType: common.NonExist,
				})
				break
			}
			codes := []string{}
			for _, v := range request.IcdCode {
				existedDiagnose := slice.FindOne(diagnosis, func(t sdicd_common.IcdItem) bool {
					return t.Code == v
				})
				if existedDiagnose != nil {
					continue
				}

				codes = append(codes, v)
			}

			if len(codes) > 0 {
				results = append(results, common.ValidationDiagnoseResult{
					Codes:     codes,
					ErrorType: common.NonExist,
				})
			}
		default:
			codes := slice.Map(diagnosis, func(t sdicd_common.IcdItem) string {
				return t.Code
			})
			results = append(results, common.ValidationDiagnoseResult{
				Codes:     codes,
				ErrorType: common.None,
			})
		}
	}

	return results, nil
}

func (srv *TimelineService[T]) MarkPyschotherapyCompleted(
	ctx *titan.Context,
	serviceEntry *common.TimelineModel,
	entry88130 *common.TimelineModel,
	scheinId uuid.UUID,
) error {
	entry, err := srv.timelinePsychotherapyRepo.GetPsychotherapyByEntryId(ctx, timeline_repo.GetPsychotherapyByEntryIdRequest{
		ScheinId:     scheinId,
		ServiceEntry: serviceEntry,
	})
	if err != nil {
		return err
	}
	if entry == nil {
		return nil
	}
	isBefore2017 := entry.Payload.CheckIsBefore2017(ctx)
	entryIds := []uuid.UUID{}
	if isBefore2017 {
		if _, ok := entry.Payload.Entries[serviceEntry.EncounterServiceTimeline.Code]; ok {
			entry.Payload.Entries[serviceEntry.EncounterServiceTimeline.Code].TerminalId = entry88130.Id
			entryIds = entry.Payload.Entries[serviceEntry.EncounterServiceTimeline.Code].EntryIds
		}
		if entry.Payload.CheckAllServiceTerminated() {
			entry.Payload.Status = patient_encounter.READY_TO_BILL
		}
	} else {
		entry.Payload.Status = patient_encounter.READY_TO_BILL
		entry.Payload.TerminateServiceId = entry88130.Id
		entryIds = entry.Payload.GetEntryApprovalIds()
	}
	_, err = srv.timelinePsychotherapyRepo.Update(ctx, *entry)
	if err != nil {
		return err
	}
	var result []timeline_repo.TimelineEntity[T]
	_, err = srv.timelineRepo.IDBClient.UpdateMany(ctx, bson.M{
		timeline_repo.Field_Id: bson.M{
			"$in": entryIds,
		},
	}, bson.M{
		"$set": bson.M{
			timeline_repo.Field_Psychotherapy_ApprovalStatus: patient_encounter.IsCompleted,
		},
	}, &result)
	if err != nil {
		return err
	}

	return srv.validationService.RunValidateOnUpdatingTimeline(ctx, &timeline.EventTimelineUpdate{
		PatientId:     entry88130.PatientId,
		TimelineModel: *entry88130,
	})
}

func (srv *TimelineService[T]) GetPsychotherapyTakeOver(ctx *titan.Context, request timeline.GetPsychotherapyTakeOverRequest) (*timeline.GetPsychotherapyTakeOverResponse, error) {
	entries, err := srv.timelinePsychotherapyRepo.GetPsychotherapy(ctx, &timeline_repo.GetPsychotherapyRequest{
		PatientId: request.PatientId,
		Statuses: []patient_encounter.EncounterPsychotherapyStatus{
			patient_encounter.INPROGRESS,
		},
	})
	if err != nil {
		return nil, err
	}
	result := timeline.GetPsychotherapyTakeOverResponse{}
	result.PsychotherapyEntry = slice.Map(entries, func(t timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy]) common.TimelineModel {
		entry := ConvertEntityToModel(t)
		return entry
	})
	return &result, nil
}

func (srv *TimelineService[T]) UpdateApprovalByIds(ctx *titan.Context, ids []uuid.UUID, status *patient_encounter.EncounterServiceTimelinePsychotherapyStatus) error {
	return srv.timelinePsychotherapyRepo.UpdateApprovalByIds(ctx, ids, status)
}

func (srv *TimelineService[T]) DeletePsychotherapyByIds(ctx *titan.Context, ids []uuid.UUID) error {
	entries, err := srv.timelinePsychotherapyRepo.GetByIds(ctx, ids)
	if err != nil {
		return err
	}
	takeOverIds := []uuid.UUID{}
	deletedIds := []uuid.UUID{}
	for _, e := range entries {
		if e.Id != nil {
			deletedIds = append(deletedIds, *e.Id)
		}
		if e.Payload.TakeOverId != nil {
			takeOverIds = append(takeOverIds, *e.Payload.TakeOverId)
		}
		var result []timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]
		_, err := srv.timelineServiceRepo.IDBClient.UpdateMany(
			ctx,
			bson.M{
				timeline_repo.Field_Id: bson.M{
					"$in": e.Payload.GetEntryApprovalIds(),
				},
			}, bson.M{
				"$set": bson.M{
					timeline_repo.Field_Psychotherapy_ApprovalStatus: nil,
				},
			},
			&result,
		)
		if err != nil {
			return err
		}
	}
	if len(takeOverIds) > 0 {
		var result []timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]
		_, err := srv.timelinePsychotherapyRepo.IDBClient.UpdateMany(
			ctx,
			bson.M{
				timeline_repo.Field_Id: bson.M{
					"$in": takeOverIds,
				},
			}, bson.M{
				"$set": bson.M{
					timeline_repo.Field_Psychotherapy_Status: patient_encounter.INPROGRESS,
				},
			},
			&result,
		)
		if err != nil {
			return err
		}
	}
	_, err = srv.timelineRepo.DeleteByIds(ctx, deletedIds)
	return err
}

func (srv *TimelineService[T]) UpdateTakeOverPsychotherapyStatus(ctx *titan.Context, ids []uuid.UUID, status patient_encounter.EncounterPsychotherapyStatus) error {
	return srv.timelinePsychotherapyRepo.UpdateTakeOverPsychotherapyStatus(ctx, ids, status)
}

func (srv *TimelineService[T]) HandlePsychotherapies(ctx *titan.Context, scheinRepo *schein_repo.ScheinRepo) error {
	if len(scheinRepo.ScheinDetail.Psychotherapy) == 0 {
		return nil
	}
	entries, err := srv.timelinePsychotherapyRepo.GetPsychotherapy(ctx, &timeline_repo.GetPsychotherapyRequest{
		PatientId: scheinRepo.PatientId,
		Statuses: []patient_encounter.EncounterPsychotherapyStatus{
			patient_encounter.INPROGRESS,
		},
	})
	if err != nil {
		return err
	}
	takeOverPsychotherapyIds := []uuid.UUID{}
	for _, p := range scheinRepo.ScheinDetail.Psychotherapy {
		treatmentCase := patient_encounter.TreatmentCaseCustodian
		encounterCase := patient_encounter.AB
		payload := patient_encounter.EncounterPsychotherapy{}
		payload.ConvertPsychotherapy(p, scheinRepo.Id)
		if p.TakeOverId != nil {
			takeOverPsychotherapyIds = append(takeOverPsychotherapyIds, *p.TakeOverId)
			entry := slice.FindOne(entries, func(te timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy]) bool {
				return te.Id.String() == p.TakeOverId.String()
			})
			if entry != nil {
				for k := range payload.Entries {
					e, ok := entry.Payload.Entries[k]
					v := payload.Entries[k]
					if ok {
						if len(e.EntryIds) > int(v.AmountBilled) {
							payload.Entries[k].EntryIds = e.EntryIds[:v.AmountBilled]
						} else {
							payload.Entries[k].EntryIds = e.EntryIds
						}
					}
				}
			}
		}
		yearQuarter := scheinRepo.GetYearQuarter()
		timelineEntry := timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy]{
			Payload:           payload,
			TreatmentDoctorId: scheinRepo.DoctorId,
			BillingDoctorId:   &scheinRepo.DoctorId,
			TreatmentCase:     &treatmentCase,
			PatientId:         scheinRepo.PatientId,
			EncounterCase:     &encounterCase,
			Id:                p.Id,
			ScheinIds:         []uuid.UUID{*scheinRepo.Id},
			RecentAuditLogs:   []timeline_repo.AuditLog{},
			SelectedDate:      util.ConvertLastDateQuarterUTC(ctx, yearQuarter.Quarter, yearQuarter.Year),
		}
		_, err := srv.timelinePsychotherapyRepo.Upsert(ctx, timelineEntry)
		if err != nil {
			return err
		}
	}

	if len(takeOverPsychotherapyIds) > 0 {
		if err := srv.UpdateTakeOverPsychotherapyStatus(ctx, takeOverPsychotherapyIds, patient_encounter.HAS_BEEN_TAKE_OVER); err != nil {
			return fmt.Errorf("update takeover psychotherapy error: %w", err)
		}
	}
	return nil
}

func (srv *TimelineService[T]) createPsychotherapyEntry(
	ctx *titan.Context,
	psychotherapies []schein_common.Psychotherapy,
	scheinRepo *schein_repo.ScheinRepo,
) ([]timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy], error) {
	timelineEntries := []timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy]{}
	for _, psychotherapy := range psychotherapies {
		treatmentCase := patient_encounter.TreatmentCaseCustodian
		encounterCase := patient_encounter.AB
		payload := patient_encounter.EncounterPsychotherapy{}
		payload.ConvertPsychotherapy(psychotherapy, scheinRepo.Id)
		timelineEntries = append(timelineEntries, timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy]{
			Payload:           payload,
			TreatmentDoctorId: scheinRepo.DoctorId,
			BillingDoctorId:   &scheinRepo.DoctorId,
			TreatmentCase:     &treatmentCase,
			PatientId:         scheinRepo.PatientId,
			EncounterCase:     &encounterCase,
			Id:                psychotherapy.Id,
			ScheinIds:         []uuid.UUID{*scheinRepo.Id},
		})
	}
	return srv.timelinePsychotherapyRepo.CreateMany(ctx, timelineEntries)
}

func (srv *TimelineService[T]) TakeoverServiceTerminalApproval(ctx *titan.Context, request timeline.TakeoverServiceTerminalApprovalRequest) error {
	entity, err := srv.timelineServiceRepo.FindById(ctx, request.TimelineId)
	if err != nil {
		return err
	}
	if entity == nil {
		return nil
	}
	schein, err := srv.scheinRepo.FindById(ctx, request.ScheinId)
	if err != nil {
		return err
	}
	if schein == nil {
		return nil
	}
	entity.ScheinIds = append(entity.ScheinIds, request.ScheinId)
	var scheinWithMainGroup domains_api_common.MainGroup
	switch schein.Schein.ScheinMainGroup {
	case string(domains_api_common.HZV):
		scheinWithMainGroup = domains_api_common.HZV
	case string(domains_api_common.FAV):
		scheinWithMainGroup = domains_api_common.FAV
	case string(domains_api_common.BG):
		scheinWithMainGroup = domains_api_common.BG
	case string(domains_api_common.PRIVATE):
		scheinWithMainGroup = domains_api_common.PRIVATE
	default:
		scheinWithMainGroup = domains_api_common.KV
	}
	newScheins := *entity.Payload.Scheins
	newScheins = append(newScheins, &domains_api_common.ScheinWithMainGroup{
		ScheinId: &request.ScheinId,
		Group:    scheinWithMainGroup,
	})
	entity.Payload.Scheins = &newScheins
	_, err = srv.timelineServiceRepo.Update(ctx, *entity)
	if err != nil {
		return err
	}

	return srv.validationService.RunValidateOnUpdatingTimeline(ctx, &timeline.EventTimelineUpdate{
		PatientId:     entity.PatientId,
		TimelineModel: ConvertEntityToModel(*entity),
	})
}

func (srv *TimelineService[T]) GetPsychotherapyBefore2020(ctx *titan.Context, request timeline.GetPsychotherapyBefore2020Request) (*timeline.GetPsychotherapyBefore2020Response, error) {
	timelines, err := srv.timelinePsychotherapyRepo.Find(ctx, bson.M{
		timeline_repo.Field_timelineEntityType:   common.TimelineEntityType_Psychotherapy,
		timeline_repo.Field_Psychotherapy_Status: patient_encounter.INPROGRESS,
		timeline_repo.Field_PatientId:            request.PatientId,
	})
	if err != nil {
		return nil, err
	}
	serviceEntryIds := []uuid.UUID{}
	for _, t := range timelines {
		serviceEntryIds = append(serviceEntryIds, t.Payload.GetEntryApprovalIds()...)
	}
	if len(serviceEntryIds) == 0 {
		return nil, nil
	}
	serviceEntries, err := srv.timelineServiceRepo.FindByIds(ctx, serviceEntryIds)
	if err != nil {
		return nil, err
	}
	if len(serviceEntries) == 0 {
		return nil, err
	}
	currQuarter, currYear := util.GetCurrentQuarter(util.Now(ctx))
	scheins, err := srv.scheinRepo.FilterSchein(ctx, schein_repo.FilterScheinParams{
		PatientId:    util.NewPointer(request.PatientId),
		G4101Quarter: util.NewPointer(int32(currQuarter)),
		G4101Year:    util.NewPointer(int32(currYear)),
	})
	if err != nil {
		return nil, err
	}
	if len(scheins) == 0 {
		return nil, nil
	}
	result := timeline.GetPsychotherapyBefore2020Response{}
	for _, t := range timelines {
		if util.ConvertMillisecondsToTime(t.Payload.ApprovalDate).Year() >= 2020 {
			continue
		}
		schein := slice.FindOne(scheins, func(sr schein_repo.ScheinRepo) bool {
			return slice.FindOne(sr.ScheinDetail.Psychotherapy, func(p schein_common.Psychotherapy) bool {
				return p.Id.String() == t.Id.String()
			}) != nil && sr.DoctorId.String() == t.BillingDoctorId.String()
		})
		if schein == nil {
			continue
		}
		entryIds := t.Payload.GetEntryApprovalIds()
		services := slice.Filter(serviceEntries, func(te timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) bool {
			return slice.FindOne(entryIds, func(u uuid.UUID) bool {
				return te.Id != nil && te.Id.String() == u.String()
			}) != nil
		})
		if len(services) == 0 {
			continue
		}
		slice.SortBy(services, func(a, b timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) bool {
			if a.SelectedDate.Format(util.DateFormat) == b.SelectedDate.Format(util.DateFormat) {
				return a.CreatedAt.UnixMilli() > b.CreatedAt.UnixMilli()
			}
			return a.SelectedDate.UnixMilli() > b.SelectedDate.UnixMilli()
		})
		service := services[0]
		curr := currQuarter + currYear*4
		timeService := service.Year*4 + service.Quarter
		if curr-timeService <= 2 {
			continue
		}
		result.Data = append(result.Data, timeline.GetPsychotherapyBefore2020{
			TimelineModel: ConvertEntityToModel(*t),
			ErrorCode:     timeline_utils.CheckServiceCodeSuggestHints(t.Payload.ServiceCodes, srv.rezidivService.GetAllRezidivList()),
			ServiceEntry:  ConvertEntityToModel(service),
		})
	}
	return &result, nil
}

func (srv *TimelineService[T]) GetPsychotherapyByPatientIds(ctx *titan.Context, patientIds []uuid.UUID) ([]timeline_repo.TimelineEntity[T], error) {
	return srv.timelineRepo.GetPsychotherapyByPatientIds(ctx, patientIds)
}

func (srv *TimelineService[T]) AutoCreateScheinAndTakeOverDiagnoses(ctx *titan.Context, serviceId, scheinId uuid.UUID, isTechnicalSchein bool) (*schein_repo.ScheinRepo, error) {
	schein, err := srv.scheinRepo.FindById(ctx, scheinId)
	if err != nil {
		return nil, err
	}
	if schein == nil {
		return nil, nil
	}
	cloneSchein := *schein
	cloneSchein.Id = util.NewUUID()
	now := util.ToYearQuarter(util.Now(ctx).UnixMilli())
	cloneSchein.Schein.G4101Year = &now.Year
	cloneSchein.Schein.G4101Quarter = &now.Quarter
	if isTechnicalSchein {
		cloneSchein.IsTechnicalSchein = util.NewBool(true)
	}
	psychotherapies, err := srv.timelinePsychotherapyRepo.FindByIds(ctx, slice.Map(schein.ScheinDetail.Psychotherapy, func(p schein_common.Psychotherapy) uuid.UUID {
		return *p.Id
	}))
	if err != nil {
		return nil, err
	}
	psychotherapy := slice.FindOne(psychotherapies, func(t timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy]) bool {
		return slice.FindOne(t.Payload.GetEntryApprovalIds(), func(u uuid.UUID) bool { return u.String() == serviceId.String() }) != nil
	})
	if psychotherapy == nil {
		return nil, nil
	}
	cloneSchein.ScheinDetail.Psychotherapy = slice.Map(slice.Filter(schein.ScheinDetail.Psychotherapy, func(p schein_common.Psychotherapy) bool {
		return p.Id.String() == psychotherapy.Id.String()
	}), func(p schein_common.Psychotherapy) schein_common.Psychotherapy {
		p.Id = util.NewUUID()
		return p
	})
	scheinCreated, err := srv.scheinRepo.Create(ctx, cloneSchein)
	return scheinCreated, err
}

func (srv *TimelineService[T]) getDiagnosisUnique(ctx *titan.Context, patientId uuid.UUID) ([]timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline], error) {
	res, err := srv.timelineDiagnosisRepo.GetDiagnoseByPatient(ctx, patientId)
	if err != nil {
		return nil, errors.WithMessage(err, "can not get diagnosis")
	}

	if len(res) == 0 {
		return nil, nil
	}

	timelineDiagnosesUnique := slice.UniqBy(res, func(t timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) string {
		encounter := t.Payload
		return fmt.Sprintf("%s-%s-%s-%s", t.Payload.Type, encounter.Code, util.GetPointerValue(encounter.Certainty), util.GetPointerValue(encounter.Laterality))
	})

	return timelineDiagnosesUnique, nil
}

func (srv *TimelineService[T]) TakeOverScheinDiagnoses(
	ctx *titan.Context,
	createdSchein *schein_repo.ScheinRepo,
	diagnoses []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline],
	mappingTreatmentRelevent map[string]bool,
) ([]timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline], error) {
	permanentDiagnoseIds := []uuid.UUID{}
	newDiagnoses := []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{}
	scheinYearQuarter := createdSchein.GetYearQuarter()

	for _, diagnose := range diagnoses {
		payload := diagnose.Payload
		payload.Scheins = &[]*domains_api_common.ScheinWithMainGroup{
			{
				ScheinId: createdSchein.Id,
				Group:    domains_api_common.MainGroup(createdSchein.Schein.ScheinMainGroup),
			},
		}

		timelineEntity := srv.EnforceSelectedDateDiagnose(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
			Id:                util.NewUUID(),
			ContractId:        createdSchein.ContractId,
			TreatmentDoctorId: diagnose.TreatmentDoctorId,
			BillingDoctorId:   diagnose.BillingDoctorId,
			PatientId:         diagnose.PatientId,
			ScheinIds:         []uuid.UUID{*createdSchein.Id},
			EncounterCase:     diagnose.EncounterCase,
			TreatmentCase:     diagnose.TreatmentCase,
			Payload:           payload,
			RecentAuditLogs: append(diagnose.RecentAuditLogs, timeline_repo.AuditLog{
				AuditLogId: util.NewUUID(),
				UserId:     ctx.UserInfo().UserUUID(),
				Date:       util.Now(ctx),
				ActionType: common.TakeOver,
			}),
		}, scheinYearQuarter)

		for k, v := range mappingTreatmentRelevent {
			if diagnose.Id.String() == k {
				timelineEntity.Payload.MarkedTreatmentRelevant = v
			}
		}
		newDiagnoses = append(newDiagnoses, timelineEntity)
	}

	if len(permanentDiagnoseIds) > 0 {
		if err := srv.UpdateDiagnosesScheinId(ctx, permanentDiagnoseIds, *createdSchein.Id); err != nil {
			return nil, errors.WithMessage(err, "can not update permanent diagnoses")
		}
	}

	if len(newDiagnoses) > 0 {
		timelineItems, err := srv.timelineDiagnosisRepo.CreateMany(ctx, newDiagnoses)
		if err != nil {
			return nil, errors.WithMessage(err, "can not create diagnosis")
		}
		eventTimelineCreates := slice.Map(timelineItems, func(v timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) *timeline.EventTimelineCreate {
			return &timeline.EventTimelineCreate{
				PatientId:     v.PatientId,
				TimelineModel: ConvertEntityToModel(v),
			}
		})
		if err := srv.NotifyTimelineCreate(ctx, eventTimelineCreates...); err != nil {
			return nil, errors.WithMessage(err, "can not notify timeline create")
		}
		return timelineItems, nil
	}
	return []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{}, nil
}

// Case for psychotherapy
func (srv *TimelineService[T]) TakeOverDiagnosisWithScheinId(ctx *titan.Context, request timeline.TakeOverDiagnosisWithScheinIdRequest) ([]timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline], error) {
	entities := []timeline_repo.TimelineEntity[T]{}
	for _, t := range request.NewDiagnosis {
		if t.Id == nil {
			t.Id = util.NewUUID()
		}
		if !slice.Contains(t.ScheinIds, request.ScheinId) {
			t.ScheinIds = []uuid.UUID{request.ScheinId}
		}
		entities = append(entities, ConvertModelToEntity[T](t))
	}
	if _, err := srv.CreateMany(ctx, entities); err != nil {
		return nil, errors.WithMessage(err, "can not create diagnosis")
	}
	schein, err := srv.scheinRepo.FindById(ctx, request.ScheinId)
	if err != nil {
		return nil, errors.WithMessage(err, "can not get schein")
	}
	if schein == nil {
		return []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{}, nil
	}
	timelines, err := srv.timelineDiagnosisRepo.FindByIds(ctx, request.TimelineModelIds)
	if err != nil {
		return nil, errors.WithMessage(err, "can not get timelines")
	}
	diagnosis, service, _, err := srv.GetTimelinesDiagnoseAndServiceByScheinIds(ctx, GetTimelinesDiagnoseAndServiceByScheinIdsRequest{
		PatientWithScheinIds: []timeline_repo.PatientWithScheinIds{
			{
				PatientId: schein.PatientId,
				ScheinIds: []uuid.UUID{*schein.Id},
			},
		},
	})
	if err != nil {
		return nil, errors.WithMessage(err, "can not get timelines")
	}
	if len(diagnosis)+len(service) == 0 {
		return []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{}, nil
	}
	if schein.IsTechnicalSchein == nil || (schein.IsTechnicalSchein != nil && !*schein.IsTechnicalSchein) {
		schein.IsTechnicalSchein = util.NewBool(true)
		if _, err := srv.scheinRepo.Update(ctx, *schein); err != nil {
			return nil, errors.WithMessage(err, "can not update schein")
		}
	}
	takenOver, err := srv.TakeOverScheinDiagnoses(ctx, schein, timelines, request.MappingTreatmentRelevent)
	if err != nil {
		return nil, errors.WithMessage(err, "can not take over timelines")
	}
	return append(timelines, takenOver...), nil
}

func (srv *TimelineService[T]) MarkAcceptedByKV(ctx *titan.Context, request timeline.MarkAcceptedByKVRequest) error {
	timelineModel, err := srv.timelineServiceRepo.FindById(ctx, request.TimelineId)
	if err != nil {
		return errors.WithMessage(err, "find timeline service error")
	}
	if timelineModel == nil {
		return nil
	}
	entry, err := srv.timelinePsychotherapyRepo.FindOne(ctx, bson.M{
		timeline_repo.Field_timelineEntityType:  common.TimelineEntityType_Psychotherapy,
		timeline_repo.Field_IsDeleted:           false,
		timeline_repo.Field_ServiceCodeApproval: timelineModel.Payload.Code,
		timeline_repo.Field_ScheinIds: bson.M{
			"$in": timelineModel.ScheinIds,
		},
	})
	if err != nil {
		return errors.WithMessage(err, "find timeline psychotherapy error")
	}
	if entry == nil {
		return nil
	}
	_, ok := entry.Payload.Entries[timelineModel.Payload.Code]
	if !ok {
		entry.Payload.Entries[timelineModel.Payload.Code] = &patient_encounter.ServiceCodeApproval{
			AmountBilled: 0,
			EntryIds:     []uuid.UUID{*timelineModel.Id},
		}
	} else {
		entry.Payload.Entries[timelineModel.Payload.Code].EntryIds = append(entry.Payload.Entries[timelineModel.Payload.Code].EntryIds, *timelineModel.Id)
		entry.Payload.Entries[timelineModel.Payload.Code].AmountBilled++
	}
	_, err = srv.timelinePsychotherapyRepo.Update(ctx, *entry)
	if err != nil {
		return errors.WithMessage(err, "update timeline psychotherapy error")
	}
	timelineModel.Payload.ApprovalStatus = util.NewPointer(patient_encounter.IsApproval)
	updateded, err := srv.timelineServiceRepo.Update(ctx, *timelineModel)
	if err != nil {
		return errors.WithMessage(err, "update timeline service error")
	}
	if updateded == nil {
		return nil
	}
	eventUpdateModel := timeline.EventTimelineUpdate{
		TimelineModel: ConvertEntityToModel(*updateded),
		PatientId:     updateded.PatientId,
	}
	if err := srv.HookBeforeAction.ExecuteOnUpdate(ctx, &eventUpdateModel); err != nil {
		return errors.WithMessage(err, "execute on update error")
	}
	return srv.notifier.NotifyTimelineUpdate(ctx, &eventUpdateModel)
}

func (srv *TimelineService[T]) FindOne(ctx *titan.Context, filter any, opts ...*options.FindOneOptions) (*timeline_repo.TimelineEntity[T], error) {
	return srv.timelineRepo.FindOne(ctx, filter, opts...)
}

func (srv *TimelineService[T]) syncPermanentDiagnoseEndDate(ctx *titan.Context, diagnose *timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) error {
	ctx, span := titan.SpanContext(ctx, ctx.GetHTTPHeader(), "syncPermanentDiagnoseEndDate")
	defer span.End()
	if diagnose.Payload.Type != patient_encounter.DIAGNOSETYPE_PERMANENT {
		return nil
	}

	permanentDiagnoses, err := srv.timelineDiagnosisRepo.GetPermanentDiagnosesForUpdatingEndDate(
		ctx,
		diagnose.PatientId,
		diagnose.Payload.Code,
		string(util.GetPointerValue(diagnose.Payload.Certainty)),
	)
	if err != nil {
		return errors.WithStack(err)
	}

	if len(permanentDiagnoses) == 0 {
		return nil
	}

	endDate := diagnose.Payload.ValidUntil
	// NOTE: Find the end_date when DD no include valid_until
	if endDate == nil {
		lastedHasValidUntil := slice.FindOne(permanentDiagnoses, func(te timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) bool {
			return te.Payload.ValidUntil != nil
		})
		if lastedHasValidUntil != nil {
			endDate = lastedHasValidUntil.Payload.ValidUntil
		} else {
			return nil
		}
	}

	billingSchein := map[uuid.UUID]bool{}
	scheins, err := srv.scheinRepo.FindByPatient(ctx, diagnose.PatientId)
	if err != nil {
		return err
	}
	for i := range scheins {
		id := *scheins[i].Id
		if scheins[i].IsBilled {
			billingSchein[id] = true
		}
		billingSchein[id] = false
	}
	permanentDiagnoses = slice.Filter(permanentDiagnoses, func(diagnose timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) bool {
		for _, scheinId := range diagnose.ScheinIds {
			if billingSchein[scheinId] {
				return false
			}
		}
		return true
	})
	diagnoseIds := slice.Map(permanentDiagnoses, func(diagnose timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) *uuid.UUID {
		return diagnose.Id
	})

	err = srv.timelineRepo.UpdateDiagnoseEndDateByIds(ctx, diagnoseIds, endDate)
	if err != nil {
		return errors.WithStack(err)
	}

	return nil
}

func (_ *TimelineService[T]) EnforceSelectedDateDiagnose(ctx *titan.Context, timelineEntity timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline], yearQuarter util.YearQuarter) timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline] {
	currentYearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	if yearQuarter.Year < currentYearQuarter.Year || (yearQuarter.Year == currentYearQuarter.Year && yearQuarter.Quarter < currentYearQuarter.Quarter) {
		yearQuarter := util.YearQuarter{
			Year:     yearQuarter.Year,
			Quarter:  yearQuarter.Quarter,
			Location: ctx.RequestTimeZone(),
		}
		firstDateOfQuarter := time.Date(int(yearQuarter.Year), time.Month(yearQuarter.Quarter)*3-2, 1, 0, 0, 0, 0, yearQuarter.Location)
		endTime := util.EndOfQuarter(firstDateOfQuarter).AddDate(0, 0, -1).Add(time.Minute)
		timelineEntity.SelectedDate = endTime
	}

	return timelineEntity
}

func (srv *TimelineService[T]) RollbackDocumentTerminateService(ctx *titan.Context, request timeline.RollbackDocumentTerminateServiceRequest) error {
	if _, err := srv.scheinRepo.DeletedById(ctx, request.TehcnicalScheinId); err != nil {
		return err
	}
	queriedTimelines, err := srv.timelineRepo.GetTimelinesForBillingByScheinIds(ctx, timeline_repo.GetTimelinesForBillingByScheinIdsRequest{
		PatientWithScheinIds: []timeline_repo.PatientWithScheinIds{
			{
				PatientId: request.PatientId,
				ScheinIds: []uuid.UUID{request.TehcnicalScheinId},
			},
		},
	})
	if err != nil {
		return err
	}
	var foundTerminate *timeline_repo.TimelineEntity[T]
	timelines := slice.Filter(queriedTimelines, func(t timeline_repo.TimelineEntity[T]) bool {
		if t.Id.String() == request.TerminateServiceId.String() {
			foundTerminate = &t
			return false
		}
		return t.Type != common.TimelineEntityType_Psychotherapy
	})
	if foundTerminate != nil {
		if err := srv.Remove(ctx, *foundTerminate.Id, false); err != nil {
			return err
		}
	}
	if len(timelines) == 0 {
		return nil
	}
	timelineIds := slice.Map(timelines, func(t timeline_repo.TimelineEntity[T]) uuid.UUID {
		return *t.Id
	})
	if _, err = srv.timelineRepo.DeleteByIds(ctx, timelineIds); err != nil {
		return err
	}
	return srv.notifier.NotifyTimelineRemove(ctx, &timeline.EventTimelineRemove{
		PatientId:     timelines[0].PatientId,
		TimelineModel: ConvertEntityToModel(timelines[0]),
	})
}

func (srv *TimelineService[T]) GetTimelineByEnrollmentId(ctx *titan.Context, request timeline.GetTimelineByEnrollmentIdRequest) (*timeline.GetTimelineByEnrollmentIdResponse, error) {
	entry, err := srv.timelineRepo.GetTimelineByEnrollmentId(ctx, request.PatientId, request.EnrollmentId)
	if err != nil {
		return nil, err
	}

	if entry == nil {
		return nil, nil
	}

	return &timeline.GetTimelineByEnrollmentIdResponse{
		TimelineModel: ConvertEntityToModel(*entry),
	}, nil
}

func checkValidRemoveMedicinePrescription(schein *schein_repo.ScheinRepo, timelineModel common.TimelineModel) bool {
	isHzvOrFavSchein := schein.Schein.ScheinMainGroup == string(domains_api_common.HZV) || schein.Schein.ScheinMainGroup == string(domains_api_common.FAV)
	if isHzvOrFavSchein && schein.IsBilled {
		return true
	}
	if timelineModel.EncounterMedicinePrescription.FormInfos[0].PrintDate == nil {
		return true
	}
	return false
}

func (srv *TimelineService[T]) checkBilledTimeline(ctx *titan.Context, id uuid.UUID) (*bool, error) {
	deletingEntry, err := srv.timelineRepo.FindById(ctx, id)
	if err != nil {
		return nil, err
	}

	if len(deletingEntry.ScheinIds) == 0 {
		return util.NewBool(false), nil
	}

	timelineModel := ConvertEntityToModel(*deletingEntry)

	schein, err := srv.scheinRepo.FindById(ctx, deletingEntry.ScheinIds[0])
	if err != nil {
		return nil, err
	}

	if timelineModel.EncounterMedicinePrescription != nil {
		isValidRemove := checkValidRemoveMedicinePrescription(schein, timelineModel)
		if isValidRemove {
			return util.NewBool(false), nil
		}
	}

	if timelineModel.DoctorLetter != nil || timelineModel.EncounterForm != nil {
		return util.NewBool(false), nil
	}

	return util.NewPointer(schein.IsBilled), nil
}

func (r *TimelineService[T]) UpdateWithCallBack(ctx *titan.Context, filter any, updateFunc UpdateCallback[T]) (*timeline_repo.TimelineEntity[T], error) {
	entity, err := r.timelineRepo.FindOne(ctx, filter)
	if err != nil {
		return nil, err
	}
	if entity == nil || entity.Id == nil {
		return nil, nil
	}
	entity.RecentAuditLogs = append(entity.RecentAuditLogs, timeline_repo.AuditLog{
		AuditLogId: util.NewUUID(),
		UserId:     ctx.UserInfo().UserUUID(),
		Date:       util.Now(ctx),
		ActionType: common.Edit,
	})
	updateFunc(entity)
	result, err := r.timelineRepo.EditTimelineById(ctx, *entity)
	if err != nil {
		return nil, err
	}

	if result == nil {
		return nil, nil
	}

	err = r.NotifyTimelineUpdate(ctx, &timeline.EventTimelineUpdate{
		PatientId:     result.PatientId,
		TimelineModel: ConvertEntityToModel(*result),
	})

	return result, err
}

func (r *TimelineService[T]) UpdateByEnrollmentIdWithCallback(ctx *titan.Context, enrollmentId uuid.UUID, updateFunc UpdateCallback[T]) (*timeline_repo.TimelineEntity[T], error) {
	filter := bson.M{
		"payload.enrollmentid": enrollmentId,
	}
	return r.UpdateWithCallBack(ctx, filter, updateFunc)
}

func (r *TimelineService[T]) UpdateByDocumentationOverviewIdWithCallback(ctx *titan.Context, documentOverviewId uuid.UUID, updateFunc UpdateCallback[T]) (*timeline_repo.TimelineEntity[T], error) {
	filter := bson.M{
		"payload.documentationoverviewid": documentOverviewId,
		repos.Field_IsDeleted:             false,
	}
	return r.UpdateWithCallBack(ctx, filter, updateFunc)
}

func (srv *TimelineService[T]) BulkUpdateStatusEdokuDocumentInTimelineByEdokuDocumentIds(ctx *titan.Context, documentOveriewIds []uuid.UUID, status edmp_common.DocumentStatus) error {
	updatedTimelines, err := srv.timelineRepo.UpdateEDokuStatusInTimelineByEdokuDocumentIds(ctx, &timeline_repo.UpdateEDokuStatusInTimelineByDocumentIdsRequest{
		EdokuDocumentIds: documentOveriewIds,
		Status:           status,
	})
	if err != nil {
		return err
	}
	if len(updatedTimelines) == 0 {
		return errors.Errorf("no timeline have edoku document found")
	}
	return nil
}

func (srv *TimelineService[T]) GetByDocumentManagement(ctx *titan.Context, documentManagementId uuid.UUID, patientId *uuid.UUID) (*timeline_repo.TimelineEntity[T], error) {
	return srv.timelineRepo.GetByDocumentManagement(ctx, documentManagementId, patientId)
}

func (srv *TimelineService[T]) GetOnlyOneExistedEntryByOrderId(ctx *titan.Context, orderId string) (*timeline_repo.TimelineEntity[T], error) {
	return srv.timelineRepo.GetOnlyOneExistedEntryByOrderId(ctx, orderId)
}

func (srv *TimelineService[T]) GetGdtTimelineByDocumentManagementId(ctx *titan.Context, documentManagementId uuid.UUID) (*timeline_repo.TimelineEntity[T], error) {
	return srv.timelineRepo.GetGdtTimelineByDocumentManagementId(ctx, documentManagementId)
}

func (srv *TimelineService[T]) GetByDocumentManagementReaderId(ctx *titan.Context, readerId uuid.UUID) ([]timeline_repo.TimelineEntity[T], error) {
	return srv.timelineRepo.GetByDocumentManagementReaderId(ctx, readerId)
}

func (srv *TimelineService[T]) UpsertEABServiceCode(ctx *titan.Context, req *timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) (*timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline], error) {
	return srv.timelineRepo.UpsertEAB(ctx, req)
}

func (srv *TimelineService[T]) DeleteEABServiceCode(ctx *titan.Context, patientId, eabId uuid.UUID) ([]timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline], error) {
	timelineModels, err := srv.timelineRepo.FindEABServiceCode(ctx, patientId, eabId)
	if err != nil {
		return nil, fmt.Errorf("cannot find eab service code: %w", err)
	}

	if timelineModels == nil {
		return nil, nil
	}

	timelineId := slice.Map(timelineModels, func(item timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) uuid.UUID {
		return *item.Id
	})

	res, err := srv.timelineRepo.DeleteByIds(ctx, timelineId)
	if err != nil {
		return nil, fmt.Errorf("cannot delete service code: %w", err)
	}

	if res == nil {
		return nil, nil
	}

	return timelineModels, nil
}

func (srv *TimelineService[T]) UpdateEAUPrescribe(ctx *titan.Context, req *form_common.Prescribe) (*timeline_repo.TimelineEntity[T], error) {
	res, err := srv.timelineRepo.UpdateEAUPrescribe(ctx, req)
	if err != nil {
		return nil, err
	}

	if res == nil {
		return nil, errors.Errorf("cannot update prescribe eau")
	}

	var formName form_common.FormName

	if req.PrintedDate != nil {
		formName = req.FormName
	}

	err = srv.NotifyTimelineUpdate(ctx, &timeline.EventTimelineUpdate{
		PatientId:         res.PatientId,
		TimelineModel:     ConvertEntityToModel(*res),
		PrescribeFormName: &formName,
	})

	return res, err
}

func (srv *TimelineService[T]) FindLatestTimelineEntry(ctx *titan.Context, request timeline.FindLatesTimelineEntryRequest) (*timeline.FindLatesTimelineEntryResponse, error) {
	entry, err := srv.timelineRepo.FindLatestTimelineEntry(ctx, timeline_repo.FindLatestTimelineEntryRequest{
		PatientId:         request.PatientId,
		ContractId:        request.ContractId,
		TimelineEntryType: request.Type,
	})
	if err != nil {
		return nil, err
	}

	if entry == nil {
		return nil, errors.New("no latest timeline entry found")
	}

	return &timeline.FindLatesTimelineEntryResponse{
		TimelineModel: ConvertEntityToModel(*entry),
	}, nil
}

func (srv *TimelineService[T]) UpdateMailDateById(ctx *titan.Context, req timeline_repo.UpdateMailDateByIdRequest) error {
	if req.SelectedDate == nil {
		beginOfDate, _ := util.GetDayRange(req.UpdatedAt.UTC())
		req.SelectedDate = &beginOfDate
	}

	res, err := srv.timelineRepo.UpdateMailDateById(ctx, req)
	if err != nil {
		return err
	}

	if res == nil || res.Id == nil {
		return errors.New("cannot update mail date by message id")
	}

	if err := srv.NotifyTimelineUpdate(ctx, &timeline.EventTimelineUpdate{
		TimelineModel: ConvertEntityToModel(*res),
		PatientId:     req.PatientId,
	}); err != nil {
		return errors.Errorf("cannot notify timeline update: %s", err)
	}

	return nil
}

func (srv *TimelineService[T]) GetDiagnosisInCurrentDate(ctx *titan.Context, patientId uuid.UUID) ([]common.TimelineModel, error) {
	res, err := srv.timelineDiagnosisRepo.GetDiagnosisInCurrentDate(ctx, patientId)
	if err != nil {
		return nil, err
	}

	if len(res) == 0 {
		return nil, nil
	}

	diagnosisTimelime := slice.Map(res, func(t timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) common.TimelineModel {
		return ConvertEntityToModel(t)
	})

	return diagnosisTimelime, nil
}

func (srv *TimelineService[T]) GetActionChainDiagnoseByCodes(ctx *titan.Context, request timeline.GetActionChainDiagnoseByCodesRequest) (*timeline.GetActionChainDiagnoseByCodesResponse, error) {
	res, err := srv.sdicdService.GetDiagnoseByCodes(ctx, sdicd_service.GetDiagnoseByCodesRequest{
		Codes: request.Codes,
		Year:  request.Year,
	})
	if err != nil {
		return nil, err
	}

	diagnoseCodes := slice.Map(res, func(item sdicd_common.IcdItem) string {
		return item.Code
	})
	validCodes := []string{}
	invalidCodes := []string{}

	for _, code := range request.Codes {
		if slice.Contains(diagnoseCodes, code) {
			validCodes = append(validCodes, code)
			continue
		}
		invalidCodes = append(invalidCodes, code)
	}

	return &timeline.GetActionChainDiagnoseByCodesResponse{
		ValidItems:   validCodes,
		InValidItems: invalidCodes,
	}, nil
}

func (srv *TimelineService[T]) fullTextSearch(ctx *titan.Context, request FullTextSearchRequest) (FullTextSearchResponse, error) {
	filterBy := fmt.Sprintf("%s:=%s", "patient_id", request.PatientId.String())
	result := FullTextSearchResponse{}
	if request.FromDate != nil {
		if request.ToDate != nil {
			filterBy = fmt.Sprintf("%s && %s: [%d..%d]", filterBy, "createdAt", *request.FromDate, *request.ToDate)
		} else {
			filterBy = fmt.Sprintf("%s && %s: [>%d]", filterBy, "createdAt", *request.FromDate)
		}
	}
	res, err := srv.typesenseClient.Search(ctx, &typesense.SearchCollectionParams{
		Q:        request.Keyword,
		QueryBy:  util.NewString("search"),
		Infix:    util.NewString("fallback"),
		FilterBy: &filterBy,
	})
	if err != nil {
		return FullTextSearchResponse{}, errors.WithMessage(err, "can not search typesense")
	}
	if res == nil || len(res.Models) == 0 {
		return result, err
	}
	for _, v := range res.Models {
		m, tokenMatchs := v.GetTokensMatched()
		result.MatchedIds = append(result.MatchedIds, m.ID)
		if len(tokenMatchs) > 0 {
			result.MatchedTokens = append(result.MatchedTokens, tokenMatchs...)
		}
	}
	result.MatchedTokens = slice.Uniq(result.MatchedTokens)
	return result, nil
}

func (srv *TimelineService[T]) DeleteDoctorLetterByPayloadId(ctx *titan.Context, payloadId uuid.UUID) error {
	res, err := srv.timelineRepo.DeleteDoctorLetterByPayloadId(ctx, payloadId)
	if err != nil {
		return err
	}

	if res == nil {
		return nil
	}

	timelineModel := ConvertEntityToModel(*res)
	if err := srv.NotifyTimelineRemove(ctx, &timeline.EventTimelineRemove{
		PatientId:     timelineModel.PatientId,
		TimelineModel: timelineModel,
	}); err != nil {
		return fmt.Errorf("cannot notify timeline remove: %w", err)
	}

	return nil
}

func (srv *TimelineService[T]) DeleteServiceChain(ctx *titan.Context, chainId uuid.UUID) error {
	deletedTimelines, err := srv.timelineRepo.DeleteServiceChain(ctx, chainId)
	if err != nil {
		return errors.WithMessage(err, "cannot delete service chain")
	}

	auditLogIds := slice.Reduce(deletedTimelines, func(acc []uuid.UUID, item *timeline_repo.TimelineEntity[T]) []uuid.UUID {
		return append(acc, slice.Map(item.RecentAuditLogs, func(log timeline_repo.AuditLog) uuid.UUID {
			return *log.AuditLogId
		})...)
	}, []uuid.UUID{})

	if err := srv.auditLogService.DeleteByIds(ctx, auditLogIds); err != nil {
		return errors.WithMessage(err, "cannot delete audit log")
	}

	eventTimelineRemove := slice.Map(deletedTimelines, func(item *timeline_repo.TimelineEntity[T]) *timeline.EventTimelineRemove {
		return &timeline.EventTimelineRemove{
			PatientId:     item.PatientId,
			TimelineModel: ConvertEntityToModel(*item),
		}
	})

	if err := srv.NotifyTimelineRemove(ctx, eventTimelineRemove...); err != nil {
		return errors.WithMessage(err, "cannot notify timeline remove")
	}
	return nil
}

func (srv *TimelineService[T]) GetAnyBlankCodeFromTimeLine(ctx *titan.Context, code string) (bool, error) {
	return srv.timelineRepo.GetAnyBlankCodeFromTimeLine(ctx, code)
}

func (srv *TimelineService[T]) DeleteEdokuDocument(ctx *titan.Context, documentOverviewId uuid.UUID) error {
	entity, err := srv.timelineRepo.FindOne(ctx, bson.M{
		"payload.documentationoverviewid": documentOverviewId,
		"payload.documentstatus":          edmp_common.DocumentStatus_Saved,
	})
	if err != nil {
		return err
	}
	if entity == nil {
		return fmt.Errorf("documentation overview not found")
	}
	_, err = srv.timelineRepo.DeleteById(ctx, *entity.Id)
	if err != nil {
		return err
	}

	return nil
}
