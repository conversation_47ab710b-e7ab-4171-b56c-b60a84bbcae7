package precondition

import (
	"fmt"

	"emperror.dev/errors"
	catalog_sdkt_common "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdkt_common"
	domain_common "git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common/patientfile"
	validation_timeline_service "git.tutum.dev/medi/tutum/ares/service/domains/api/validation_timeline"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/sdebm/additional_field"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	sdopsService "git.tutum.dev/medi/tutum/ares/service/domains/sdops/sdops_service"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/validation_timeline/validations/common"
	sdebmModel "git.tutum.dev/medi/tutum/ares/tools/sdebm/model"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"gitlab.com/silenteer-oss/titan"
)

type KvServiceIncludedOpsValidator struct {
	ctx          *titan.Context
	bedingung    sdebmModel.BedingungTyp
	sdktCatalog  *catalog_sdkt_common.SdktCatalog
	sdopsService *sdopsService.SdopsService
}

const (
	OPSDate_5034                      = "5034"
	OPS_5035                          = "5035"
	OPSEBM_5036                       = "5036"
	LateralityKey                     = "5041"
	ChargeNumberKey_5010              = "5010"
	reportIDImplantRegistry           = "5050"
	reportIDHashStringImplantRegistry = "5051"
	reportIDHashWertImplantRegistry   = "5052"
)

func (s *KvServiceIncludedOpsValidator) Validate(dataRow timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) []*validation_timeline_service.ValidationError {
	validationErrors := make([]*validation_timeline_service.ValidationError, 0)
	refType := validation_timeline_service.ValidationEntryReferenceTypeService
	serviceId := dataRow.Id
	ref := common.CreateValidationEntryReference(serviceId, refType)

	kvOpsDates := []patientfile.AdditionalInfoParent{}
	kvOpsCodes := []patientfile.AdditionalInfoParent{}
	kvOpsEbmCodes := []patientfile.AdditionalInfoParent{}
	chargeNumbers := []patientfile.AdditionalInfoParent{}
	reportIDImplantRegistries := []patientfile.AdditionalInfoParent{}
	reportIDHashStringImplantRegistries := []patientfile.AdditionalInfoParent{}
	reportIDHashWertImplantRegistries := []patientfile.AdditionalInfoParent{}

	if dataRow.Payload.AdditionalInfos != nil {
		for _, addInfo := range *dataRow.Payload.AdditionalInfos {
			if addInfo == nil {
				continue
			}

			parent := *addInfo
			switch addInfo.FK {
			case OPSDate_5034:
				kvOpsDates = append(kvOpsDates, common.ConvertToAdditionalInfoParent(parent))
			case OPS_5035:
				kvOpsCodes = append(kvOpsCodes, common.ConvertToAdditionalInfoParent(parent))
			case OPSEBM_5036:
				kvOpsEbmCodes = append(kvOpsEbmCodes, common.ConvertToAdditionalInfoParent(parent))
			case ChargeNumberKey_5010:
				chargeNumbers = append(chargeNumbers, common.ConvertToAdditionalInfoParent(parent))
			case reportIDImplantRegistry:
				reportIDImplantRegistries = append(reportIDImplantRegistries, common.ConvertToAdditionalInfoParent(parent))
			case reportIDHashStringImplantRegistry:
				reportIDHashStringImplantRegistries = append(reportIDHashStringImplantRegistries, common.ConvertToAdditionalInfoParent(parent))
			case reportIDHashWertImplantRegistry:
				reportIDHashWertImplantRegistries = append(reportIDHashWertImplantRegistries, common.ConvertToAdditionalInfoParent(parent))
			}
		}
	}

	rules := []func() []*domain_common.FieldError{
		s.validateLaterality(kvOpsCodes),
		s.validate_701W(kvOpsDates),
		s.validate_702W(kvOpsCodes, kvOpsEbmCodes),
		s.validate_703W(kvOpsCodes),
		s.validate_704W(kvOpsEbmCodes),
		s.validate_705W(kvOpsCodes),
		s.validate_868W(chargeNumbers),
		s.validate_888W(reportIDImplantRegistries),
		s.validate_889W(reportIDHashStringImplantRegistries),
		s.validate_890W(reportIDHashWertImplantRegistries),
	}
	var serviceErrMessages []*domain_common.FieldError
	for _, validate := range rules {
		errs := validate()
		serviceErrMessages = append(serviceErrMessages, errs...)
	}

	if len(serviceErrMessages) > 0 {
		for _, errr := range serviceErrMessages {
			var validationError *validation_timeline_service.ValidationError
			var ruleError *domain_common.FieldError
			switch {
			case errors.As(errr, &ruleError):
				validationType := validation_timeline_service.ValidationType_error
				if ruleError.ValidationType != domain_common.ValidationType_Error {
					validationType = validation_timeline_service.ValidationType_warning
				}
				validationError = common.CreateValidationEntryErrorCode(
					ruleError.ErrorCode,
					ruleError.MetaData,
					validationType,
					[]*validation_timeline_service.ValidationEntryReference{ref},
				)
			default:
				validationError = common.CreateValidationEntryError(
					errr.Error(),
					"",
					[]*validation_timeline_service.ValidationEntryReference{ref},
				)
			}

			validationErrors = append(validationErrors, validationError)
		}
	}
	return validationErrors
}

// KP2-910
func (s *KvServiceIncludedOpsValidator) validateLaterality(kvOpsCodes []patientfile.AdditionalInfoParent) func() []*domain_common.FieldError {
	return func() []*domain_common.FieldError {
		if s.bedingung.BegruendungenListe == nil || s.bedingung.BegruendungenListe.OpsListe == nil {
			return nil
		}

		mapOpsWithLaterality := map[string]string{}
		for _, kategorie := range s.bedingung.BegruendungenListe.OpsListe.Kategorie {
			for _, op := range kategorie.Ops {
				if op.Seite == nil || op.Seite.V == "" {
					continue
				}

				mapOpsWithLaterality[op.V] = op.Seite.V
			}
		}

		if len(mapOpsWithLaterality) == 0 {
			return nil
		}

		metaData := map[string]string{}
		for _, kvOpsCode := range kvOpsCodes {
			if _, ok := mapOpsWithLaterality[kvOpsCode.Value]; !ok {
				continue
			}

			expectedLaterality := mapOpsWithLaterality[kvOpsCode.Value]
			matchedLaterality := slice.FindOne(kvOpsCode.Children, func(childField *patientfile.AdditionalInfoChild) bool {
				return childField.FK == LateralityKey && childField.Value == expectedLaterality
			})

			if matchedLaterality == nil {
				metaData[kvOpsCode.Value] = expectedLaterality
			}
		}
		if len(metaData) == 0 {
			return nil
		}

		laterlityError := domain_common.RequiredFieldError{
			Field: LateralityKey,
		}
		return []*domain_common.FieldError{
			laterlityError.ToFieldError(
				domain_common.ValidationType_Error, string(validation_timeline_service.ServiceErrorCode_OPS_5041), metaData,
			),
		}
	}
}

// 701W -> new validation
func (s *KvServiceIncludedOpsValidator) validate_701W(
	kvOpsDates []patientfile.AdditionalInfoParent,
) func() []*domain_common.FieldError {
	return func() []*domain_common.FieldError {
		if s.bedingung.GnrZusatzangaben == nil {
			return nil
		}
		gnrAddInfos := s.bedingung.GnrZusatzangaben.GnrZusatzangabenListe
		if gnrAddInfos == nil {
			return nil
		}

		isRequired5034 := false
		for _, zusatzangaben := range gnrAddInfos {
			for _, zusatzangabe := range zusatzangaben.GnrZusatzangabe {
				if zusatzangabe.V == OPSDate_5034 {
					isRequired5034 = true
					break
				}
			}
		}

		if isRequired5034 && len(kvOpsDates) == 0 {
			requiredError := domain_common.RequiredFieldError{
				Field: OPSDate_5034,
			}
			return []*domain_common.FieldError{requiredError.ToFieldError(domain_common.ValidationType_Error, "", nil)}
		}

		return nil
	}
}

func (s *KvServiceIncludedOpsValidator) validate_702W(
	kvOpsCodes []patientfile.AdditionalInfoParent,
	kvOpsEbmCodes []patientfile.AdditionalInfoParent,
) func() []*domain_common.FieldError {
	return func() []*domain_common.FieldError {
		hasValue := s.bedingung.GnrZusatzangaben != nil
		if !hasValue {
			return nil
		}
		gnrAddInfos := s.bedingung.GnrZusatzangaben.GnrZusatzangabenListe
		if gnrAddInfos == nil {
			return nil
		}

		isRequired5035 := false
		isRequired5036 := false
		for _, zusatzangaben := range gnrAddInfos {
			// GnrZusatzangabe can have 3 values 5034 5035 5036, 5010?
			for _, zusatzangabe := range zusatzangaben.GnrZusatzangabe {
				if zusatzangabe.V == OPS_5035 {
					isRequired5035 = true
					continue
				}

				if zusatzangabe.V == OPSEBM_5036 {
					isRequired5036 = true
				}
			}
		}

		hasNoRequired5035 := isRequired5035 && len(kvOpsCodes) == 0
		hasNoRequired5036 := isRequired5036 && len(kvOpsEbmCodes) == 0
		// NOTE: at least one be present => valid
		if isRequired5035 && isRequired5036 && (len(kvOpsCodes) > 0 || len(kvOpsEbmCodes) > 0) {
			return []*domain_common.FieldError{}
		}

		if hasNoRequired5035 && hasNoRequired5036 {
			bothRequiredError := additional_field.NewRuleWarningCode(OPS_5035+"_"+OPSEBM_5036,
				additional_field.RuleErrorCode_EitherRequire,
				map[string]string{
					"field1":     OPS_5035,
					"fieldName1": "",
					"field2":     OPSEBM_5036,
					"fieldName2": "",
				},
			)
			return []*domain_common.FieldError{bothRequiredError}
		}

		if hasNoRequired5035 {
			requiredOpsError := domain_common.RequiredFieldError{
				Field: OPS_5035,
			}
			return []*domain_common.FieldError{requiredOpsError.ToFieldError(domain_common.ValidationType_Warning, "", nil)}
		}

		if hasNoRequired5036 {
			requiredOpsEbmError := domain_common.RequiredFieldError{
				Field: OPSEBM_5036,
			}
			return []*domain_common.FieldError{requiredOpsEbmError.ToFieldError(domain_common.ValidationType_Warning, "", nil)}
		}

		return nil
	}
}

func (s *KvServiceIncludedOpsValidator) validate_703W(kvOpsCodes []patientfile.AdditionalInfoParent) func() []*domain_common.FieldError {
	return func() []*domain_common.FieldError {
		if s.sdktCatalog == nil {
			return nil
		}
		if s.sdktCatalog.FeeCatalogue == catalog_sdkt_common.FeeCatalogue_3 || len(kvOpsCodes) == 0 {
			return nil
		}
		if s.bedingung.BegruendungenListe == nil || s.bedingung.BegruendungenListe.OpsListe == nil {
			return nil
		}

		opsListByCategory := s.bedingung.BegruendungenListe.OpsListe.Kategorie
		if len(opsListByCategory) == 0 {
			return nil
		}

		mapOpsValid := make(map[string]bool)
		for _, kategorie := range opsListByCategory {
			for _, op := range kategorie.Ops {
				mapOpsValid[op.V] = true
			}
		}

		hasAnyValidOps := slice.Any(kvOpsCodes, func(field patientfile.AdditionalInfoParent) bool {
			return mapOpsValid[field.Value]
		})

		// NOTE: at least one valid
		if hasAnyValidOps {
			return nil
		}

		fieldError := &domain_common.FieldError{
			Field:          OPS_5035,
			ErrorCode:      string(error_code.ErrorCode_ValidationError_OpsMustInList),
			MetaData:       map[string]string{},
			ValidationType: domain_common.ValidationType_Warning,
		}
		return []*domain_common.FieldError{fieldError}
	}
}

func (s *KvServiceIncludedOpsValidator) validate_705W(kvOpsCodes []patientfile.AdditionalInfoParent) func() []*domain_common.FieldError {
	return func() []*domain_common.FieldError {
		keyMissingLaterality := map[string]string{}
		for k, ops := range kvOpsCodes {
			hasLaterality := slice.Any(ops.Children, func(childField *patientfile.AdditionalInfoChild) bool {
				return childField.FK == LateralityKey && childField.Value != ""
			})

			opsItem, err := s.sdopsService.GetOpsByCode(s.ctx, ops.Value, util.NowUnixMillis(s.ctx))
			if err != nil {
				s.ctx.Logger().Error("Failed to get ops", ops.Value, "error", err.Error())
				continue
			}
			if opsItem != nil && opsItem.IsLaterality && !hasLaterality {
				keyMissingLaterality[fmt.Sprintf("OPKey%v", k)] = opsItem.Code
			}
		}

		if len(keyMissingLaterality) > 0 {
			requiredLaterlityError := domain_common.RequiredFieldError{
				Field: LateralityKey,
			}
			return []*domain_common.FieldError{
				requiredLaterlityError.ToFieldError(
					domain_common.ValidationType_Error, string(validation_timeline_service.ServiceErrorCode_OPS_5041), keyMissingLaterality),
			}
		}

		return nil
	}
}

func (s *KvServiceIncludedOpsValidator) validate_704W(kvOpsEbmCodes []patientfile.AdditionalInfoParent) func() []*domain_common.FieldError {
	return func() []*domain_common.FieldError {
		if s.sdktCatalog == nil {
			return nil
		}
		if s.sdktCatalog.FeeCatalogue == catalog_sdkt_common.FeeCatalogue_3 || len(kvOpsEbmCodes) == 0 {
			return nil
		}

		if s.bedingung.BegruendungenListe == nil || s.bedingung.BegruendungenListe.GnrListe == nil {
			return nil
		}

		gnrList := s.bedingung.BegruendungenListe.GnrListe.Gnr
		if len(gnrList) == 0 {
			return nil
		}

		mapGnrValid := make(map[string]bool)
		for _, gnr := range gnrList {
			mapGnrValid[gnr.V] = true
		}

		hasAnyValidEbmCode := slice.Any(kvOpsEbmCodes, func(field patientfile.AdditionalInfoParent) bool {
			return mapGnrValid[field.Value]
		})
		if hasAnyValidEbmCode {
			return nil
		}

		fieldError := &domain_common.FieldError{
			Field:          OPS_5035,
			ErrorCode:      string(error_code.ErrorCode_ValidationError_GnrMustInList),
			MetaData:       map[string]string{},
			ValidationType: domain_common.ValidationType_Warning,
		}
		return []*domain_common.FieldError{fieldError}
	}
}

func (s *KvServiceIncludedOpsValidator) validate_868W(chargeNumbers []patientfile.AdditionalInfoParent) func() []*domain_common.FieldError {
	return s.validateGnrAdditionalInfoPresence(chargeNumbers, ChargeNumberKey_5010, additional_field.RuleErrorCode_1_1Relation)
}
func (s *KvServiceIncludedOpsValidator) validate_888W(chargeNumbers []patientfile.AdditionalInfoParent) func() []*domain_common.FieldError {
	return s.validateGnrAdditionalInfoPresence(chargeNumbers, reportIDImplantRegistry, additional_field.RuleErrorCode_1_1Relation)
}
func (s *KvServiceIncludedOpsValidator) validate_889W(chargeNumbers []patientfile.AdditionalInfoParent) func() []*domain_common.FieldError {
	return s.validateGnrAdditionalInfoPresence(chargeNumbers, reportIDHashStringImplantRegistry, additional_field.RuleErrorCode_1_1Relation)
}
func (s *KvServiceIncludedOpsValidator) validate_890W(chargeNumbers []patientfile.AdditionalInfoParent) func() []*domain_common.FieldError {
	return s.validateGnrAdditionalInfoPresence(chargeNumbers, reportIDHashWertImplantRegistry, additional_field.RuleErrorCode_1_1Relation)
}

//nolint:unparam
func (s *KvServiceIncludedOpsValidator) validateGnrAdditionalInfoPresence(
	fields []patientfile.AdditionalInfoParent,
	fieldName string,
	errCode additional_field.RuleErrorCode,
) func() []*domain_common.FieldError {
	return func() []*domain_common.FieldError {
		if s.bedingung.GnrZusatzangaben == nil {
			return nil
		}

		gnrAddInfos := s.bedingung.GnrZusatzangaben.GnrZusatzangabenListe
		if len(gnrAddInfos) == 0 {
			return nil
		}

		isCheck := false
		for _, zusatzangaben := range gnrAddInfos {
			for _, zusatzangabe := range zusatzangaben.GnrZusatzangabe {
				if zusatzangabe.V == fieldName {
					isCheck = true
					break
				}
			}
		}
		if !isCheck {
			return nil
		}

		if len(fields) < 1 {
			err := additional_field.NewRuleWarningCode(
				fieldName,
				errCode,
				map[string]string{
					"field": fieldName,
				},
			)
			return []*domain_common.FieldError{err}
		}

		return nil
	}
}

func NewKvServiceIncludedOpsValidator(
	ctx *titan.Context,
	bedingungDefinition sdebmModel.BedingungTyp,
	sdktCatalog *catalog_sdkt_common.SdktCatalog,
	sdopsService *sdopsService.SdopsService,
) common.ValidationRule[patient_encounter.EncounterServiceTimeline] {
	return &KvServiceIncludedOpsValidator{
		ctx:          ctx,
		bedingung:    bedingungDefinition,
		sdktCatalog:  sdktCatalog,
		sdopsService: sdopsService,
	}
}
