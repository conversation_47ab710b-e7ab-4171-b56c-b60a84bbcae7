package ops_validator_test

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gitlab.com/silenteer-oss/titan"

	// mvz_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/app"

	catalog_sdkt_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_sdkt"
	masterdata_model "git.tutum.dev/medi/tutum/ares/pkg/masterdata/model"
	ares_test "git.tutum.dev/medi/tutum/ares/pkg/test"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/validation_timeline"
	catalog_sdkt_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_sdkt"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	okv_util "git.tutum.dev/medi/tutum/ares/service/domains/pkg/okv"
	sdebm_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/sdebm"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/sdebm/additional_field"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/sdops/sdops_service"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/validation_timeline/validations/service_code/kv/precondition"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
)

type OpsValidationTestSuite struct {
	ares_test.WithDefaultInfraTestSuite
	catalogSdktService *catalog_sdkt_service.CatalogSdktService
	sdebmRepo          *sdebm_repo.SdebmRepo
}

func TestCodingRuleTestSuite(t *testing.T) {
	ares_test.NewInfraForTesting()

	catalogSdktService, err := catalog_sdkt_service.CatalogSdktServiceMod.SafeResolve()
	require.Nil(t, err)

	sdebmRepo, err := sdebm_repo.SDEBMRepoMod.SafeResolve()
	require.Nil(t, err)

	suite.Run(t, &OpsValidationTestSuite{
		catalogSdktService: catalogSdktService,
		sdebmRepo:          sdebmRepo,
	})
}

func (ts *OpsValidationTestSuite) Test_Rule_701W() {
	t := ts.T()
	careProviderId := uuid.MustParse("00000000-0000-0000-0000-000000000001")
	ctx := titan.NewBackgroundContext().LoginToCareProviderAsRoleMustBeUsedAfterAuthentication(careProviderId.String(), titan.Role("test_role"))

	// Get ebm
	ebmCode := "06335B"
	kv := "74"
	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	ebm, err := ts.sdebmRepo.GetEbmByCode(ctx, &sdebm_repo.GetSdebmByCodeRequest{
		Code: ebmCode,
		YearQuarter: masterdata_model.YearQuarter{
			Year:    yearQuarter.Year,
			Quarter: yearQuarter.Quarter,
		},
		OrganizationId: kv,
	})
	require.Nil(t, err)

	// Get sdkt
	now := util.NowUnixMillis(ctx)
	sdkt, err := ts.catalogSdktService.GetSdktCatalogByVknr(ctx, &catalog_sdkt_api.GetSdktCatalogByVknrRequest{
		Vknr:         "61125",
		SelectedDate: &now,
	})
	require.Nil(t, err)

	entity := timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
		Payload: patient_encounter.EncounterServiceTimeline{
			Command:         "Z",
			Code:            ebmCode,
			AdditionalInfos: &[]*patient_encounter.AdditionalInfoParent{},
		},
		CreatedAt:    time.Now(),
		SelectedDate: time.Now(),
	}
	validator := precondition.NewKvServiceIncludedOpsValidator(ctx, *ebm.Bedingung, sdkt.Data, sdops_service.SdopsServiceMod.Resolve())
	errs := validator.Validate(entity)
	require.Equal(t, "5034", errs[0].MetaData["field"], "Should have field error 5034")
}

func (ts *OpsValidationTestSuite) Test_Rule_702W() {
	t := ts.T()
	careProviderId := uuid.MustParse("00000000-0000-0000-0000-000000000001")
	ctx := titan.NewBackgroundContext().LoginToCareProviderAsRoleMustBeUsedAfterAuthentication(careProviderId.String(), titan.Role("test_role"))

	// Get sdkt
	now := util.NowUnixMillis(ctx)
	sdkt, err := ts.catalogSdktService.GetSdktCatalogByVknr(ctx, &catalog_sdkt_api.GetSdktCatalogByVknrRequest{
		Vknr:         "61125",
		SelectedDate: &now,
	})
	require.Nil(t, err)

	t.Run("require 5035 & 5036", func(t *testing.T) {
		// Get ebm
		ebmCode := "31101"
		kv := "74"
		yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
		ebm, err := ts.sdebmRepo.GetEbmByCode(ctx, &sdebm_repo.GetSdebmByCodeRequest{
			Code: ebmCode,
			YearQuarter: masterdata_model.YearQuarter{
				Year:    yearQuarter.Year,
				Quarter: yearQuarter.Quarter,
			},
			OrganizationId: kv,
		})
		require.Nil(t, err)

		entityHasNo5035And5036 := timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
			Payload: patient_encounter.EncounterServiceTimeline{
				Command:         "Z",
				Code:            ebmCode,
				AdditionalInfos: &[]*patient_encounter.AdditionalInfoParent{},
			},
			CreatedAt:    time.Now(),
			SelectedDate: time.Now(),
		}

		validator := precondition.NewKvServiceIncludedOpsValidator(ctx, *ebm.Bedingung, sdkt.Data, sdops_service.SdopsServiceMod.Resolve())
		errs := validator.Validate(entityHasNo5035And5036)
		actualErr := slice.FindOne(errs, func(e *validation_timeline.ValidationError) bool {
			return e.ErrorCode == string(additional_field.RuleErrorCode_EitherRequire)
		})
		require.NotNil(t, actualErr, "Should have an error")
		require.Equal(t, "5035", util.GetPointerValue(actualErr).MetaData["field1"], "Should have field error 5035")
		require.Equal(t, "5036", util.GetPointerValue(actualErr).MetaData["field2"], "Should have field error 5036")
	})

	t.Run("require 5035", func(t *testing.T) {
		// Get ebm
		ebmCode := "31101"
		kv := "74"
		yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
		ebm, err := ts.sdebmRepo.GetEbmByCode(ctx, &sdebm_repo.GetSdebmByCodeRequest{
			Code: ebmCode,
			YearQuarter: masterdata_model.YearQuarter{
				Year:    yearQuarter.Year,
				Quarter: yearQuarter.Quarter,
			},
			OrganizationId: kv,
		})
		require.Nil(t, err)

		entityHasNo5035 := timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
			Payload: patient_encounter.EncounterServiceTimeline{
				Command: "Z",
				Code:    ebmCode,
				AdditionalInfos: &[]*patient_encounter.AdditionalInfoParent{
					{FK: "5036", Value: "02300"},
				},
			},
			CreatedAt:    time.Now(),
			SelectedDate: time.Now(),
		}

		validator := precondition.NewKvServiceIncludedOpsValidator(ctx, *ebm.Bedingung, sdkt.Data, sdops_service.SdopsServiceMod.Resolve())
		errs := validator.Validate(entityHasNo5035)
		actualErr := slice.FindOne(errs, func(e *validation_timeline.ValidationError) bool {
			return e.ErrorCode == string(additional_field.RuleErrorCode_EitherRequire)
		})
		require.Nil(t, actualErr, "Should have an error")
	})

	t.Run("require 5036", func(t *testing.T) {
		// Get ebm
		ebmCode := "31101"
		kv := "74"
		yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
		ebm, err := ts.sdebmRepo.GetEbmByCode(ctx, &sdebm_repo.GetSdebmByCodeRequest{
			Code: ebmCode,
			YearQuarter: masterdata_model.YearQuarter{
				Year:    yearQuarter.Year,
				Quarter: yearQuarter.Quarter,
			},
			OrganizationId: kv,
		})
		require.Nil(t, err)

		entityHasNo5036 := timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
			Payload: patient_encounter.EncounterServiceTimeline{
				Command: "Z",
				Code:    ebmCode,
				AdditionalInfos: &[]*patient_encounter.AdditionalInfoParent{
					{FK: "5035", Value: "5-911.10"},
				},
			},
			CreatedAt:    time.Now(),
			SelectedDate: time.Now(),
		}

		validator := precondition.NewKvServiceIncludedOpsValidator(ctx, *ebm.Bedingung, sdkt.Data, sdops_service.SdopsServiceMod.Resolve())
		errs := validator.Validate(entityHasNo5036)
		actualErr := slice.FindOne(errs, func(e *validation_timeline.ValidationError) bool {
			return e.ErrorCode == string(additional_field.RuleErrorCode_EitherRequire)
		})
		require.Nil(t, actualErr, "Should have an error")
	})
}

func (ts *OpsValidationTestSuite) Test_Rule_703W() {
	t := ts.T()
	careProviderId := uuid.MustParse("00000000-0000-0000-0000-000000000001")
	ctx := titan.NewBackgroundContext().LoginToCareProviderAsRoleMustBeUsedAfterAuthentication(careProviderId.String(), titan.Role("test_role"))

	// Get ebm
	ebmCode := "01787"
	kv := "74"
	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	ebm, err := ts.sdebmRepo.GetEbmByCode(ctx, &sdebm_repo.GetSdebmByCodeRequest{
		Code: ebmCode,
		YearQuarter: masterdata_model.YearQuarter{
			Year:    yearQuarter.Year,
			Quarter: yearQuarter.Quarter,
		},
		OrganizationId: kv,
	})
	require.Nil(t, err)

	// Get sdkt has fee_catalog != 3
	now := util.NowUnixMillis(ctx)
	sdkt, err := ts.catalogSdktService.GetSdktCatalogByVknr(ctx, &catalog_sdkt_api.GetSdktCatalogByVknrRequest{
		Vknr:         "61125",
		SelectedDate: &now,
	})
	require.Nil(t, err)

	entity := timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
		Payload: patient_encounter.EncounterServiceTimeline{
			Command: "Z",
			Code:    ebmCode,
			AdditionalInfos: &[]*patient_encounter.AdditionalInfoParent{
				{FK: "5035", Value: "12345"},
			},
		},
		CreatedAt:    time.Now(),
		SelectedDate: time.Now(),
	}
	validator := precondition.NewKvServiceIncludedOpsValidator(ctx, *ebm.Bedingung, sdkt.Data, sdops_service.SdopsServiceMod.Resolve())
	errs := validator.Validate(entity)
	actualErr := slice.FindOne(errs, func(e *validation_timeline.ValidationError) bool {
		return e.ErrorCode == string(error_code.ErrorCode_ValidationError_OpsMustInList)
	})
	require.NotNil(t, actualErr, "Should have an OpsMustInList error")
}

func (ts *OpsValidationTestSuite) Test_Rule_704W() {
	t := ts.T()
	careProviderId := uuid.MustParse("00000000-0000-0000-0000-000000000001")
	ctx := titan.NewBackgroundContext().LoginToCareProviderAsRoleMustBeUsedAfterAuthentication(careProviderId.String(), titan.Role("test_role"))

	// Get ebm
	ebmCode := "31101"
	kv := "74"
	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	ebm, err := ts.sdebmRepo.GetEbmByCode(ctx, &sdebm_repo.GetSdebmByCodeRequest{
		Code: ebmCode,
		YearQuarter: masterdata_model.YearQuarter{
			Year:    yearQuarter.Year,
			Quarter: yearQuarter.Quarter,
		},
		OrganizationId: kv,
	})
	require.Nil(t, err)

	// Get sdkt has fee_catalog != 3
	now := util.NowUnixMillis(ctx)
	sdkt, err := ts.catalogSdktService.GetSdktCatalogByVknr(ctx, &catalog_sdkt_api.GetSdktCatalogByVknrRequest{
		Vknr:         "61125",
		SelectedDate: &now,
	})
	require.Nil(t, err)

	entity := timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
		Payload: patient_encounter.EncounterServiceTimeline{
			Command: "Z",
			Code:    ebmCode,
			AdditionalInfos: &[]*patient_encounter.AdditionalInfoParent{
				{FK: "5036", Value: "12345"},
			},
		},
		CreatedAt:    time.Now(),
		SelectedDate: time.Now(),
	}
	validator := precondition.NewKvServiceIncludedOpsValidator(ctx, *ebm.Bedingung, sdkt.Data, sdops_service.SdopsServiceMod.Resolve())
	errs := validator.Validate(entity)
	actualErr := slice.FindOne(errs, func(e *validation_timeline.ValidationError) bool {
		return e.ErrorCode == string(error_code.ErrorCode_ValidationError_GnrMustInList)
	})
	require.NotNil(t, actualErr, "Should have an error")
}

func (ts *OpsValidationTestSuite) Test_Rule_705W() {
	t := ts.T()
	careProviderId := uuid.MustParse("00000000-0000-0000-0000-000000000001")
	ctx := titan.NewBackgroundContext().LoginToCareProviderAsRoleMustBeUsedAfterAuthentication(careProviderId.String(), titan.Role("test_role"))

	// Get ebm
	ebmCode := "03040"
	kv, err := okv_util.GetOkvByBsnr("931111100")
	require.Nil(t, err)
	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	ebm, err := ts.sdebmRepo.GetEbmByCode(ctx, &sdebm_repo.GetSdebmByCodeRequest{
		Code: ebmCode,
		YearQuarter: masterdata_model.YearQuarter{
			Year:    yearQuarter.Year,
			Quarter: yearQuarter.Quarter,
		},
		OrganizationId: kv,
	})
	require.Nil(t, err)

	// Get sdkt
	now := util.NowUnixMillis(ctx)
	sdkt, err := ts.catalogSdktService.GetSdktCatalogByVknr(ctx, &catalog_sdkt_api.GetSdktCatalogByVknrRequest{
		Vknr:         "61125", // TODO: add valid vknr
		SelectedDate: &now,
	})
	require.Nil(t, err)

	t.Run("should have error 5041", func(t *testing.T) {
		entity := timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
			Payload: patient_encounter.EncounterServiceTimeline{
				Command: "Z",
				Code:    ebmCode,
				AdditionalInfos: &[]*patient_encounter.AdditionalInfoParent{
					{
						FK:    "5035",
						Value: "5-911.1x",
					},
				},
			},
			CreatedAt:    time.Now(),
			SelectedDate: time.Now(),
		}
		validator := precondition.NewKvServiceIncludedOpsValidator(ctx, *ebm.Bedingung, sdkt.Data, sdops_service.SdopsServiceMod.Resolve())
		errs := validator.Validate(entity)
		actualErr := slice.FindOne(errs, func(e *validation_timeline.ValidationError) bool {
			return e.ErrorCode == string(validation_timeline.ServiceErrorCode_OPS_5041)
		})
		require.NotNil(t, actualErr, "Should have an error")
		require.Equal(t, "5041", util.GetPointerValue(actualErr).MetaData["field"], "Should have field error 5041")
	})

	t.Run("should not have error 5041", func(t *testing.T) {
		entity := timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
			Payload: patient_encounter.EncounterServiceTimeline{
				Command: "Z",
				Code:    ebmCode,
				AdditionalInfos: &[]*patient_encounter.AdditionalInfoParent{
					{
						FK:    "5035",
						Value: "5-911.1x",
						Children: []*patient_encounter.AdditionalInfoChild{
							{
								FK:    "5041",
								Value: "B",
							},
						},
					},
				},
			},
			CreatedAt:    time.Now(),
			SelectedDate: time.Now(),
		}
		validator := precondition.NewKvServiceIncludedOpsValidator(ctx, *ebm.Bedingung, sdkt.Data, sdops_service.SdopsServiceMod.Resolve())
		errs := validator.Validate(entity)
		require.Empty(t, errs)
	})
}

func (ts *OpsValidationTestSuite) Test_Rule_868W() {
	t := ts.T()
	t.Skip("TODO: fail on q4 fix later")
	careProviderId := uuid.MustParse("00000000-0000-0000-0000-000000000001")
	ctx := titan.NewBackgroundContext().LoginToCareProviderAsRoleMustBeUsedAfterAuthentication(careProviderId.String(), titan.Role("test_role"))

	// Get valid ebm
	ebmCode := "88331V"
	kv := "74"
	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	ebm, err := ts.sdebmRepo.GetEbmByCode(ctx, &sdebm_repo.GetSdebmByCodeRequest{
		Code: ebmCode,
		YearQuarter: masterdata_model.YearQuarter{
			Year:    yearQuarter.Year,
			Quarter: yearQuarter.Quarter,
		},
		OrganizationId: kv,
	})
	require.Nil(t, err)

	// Get sdkt
	now := util.NowUnixMillis(ctx)
	sdkt, err := ts.catalogSdktService.GetSdktCatalogByVknr(ctx, &catalog_sdkt_api.GetSdktCatalogByVknrRequest{
		Vknr:         "61125",
		SelectedDate: &now,
	})
	require.Nil(t, err)

	entity := timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
		Payload: patient_encounter.EncounterServiceTimeline{
			Command:         "Z",
			Code:            ebmCode,
			AdditionalInfos: &[]*patient_encounter.AdditionalInfoParent{},
		},
		CreatedAt:    time.Now(),
		SelectedDate: time.Now(),
	}
	validator := precondition.NewKvServiceIncludedOpsValidator(ctx, *ebm.Bedingung, sdkt.Data, sdops_service.SdopsServiceMod.Resolve())
	errs := validator.Validate(entity)
	actualErr := slice.FindOne(errs, func(e *validation_timeline.ValidationError) bool {
		return e.ErrorCode == string(additional_field.RuleErrorCode_1_1Relation)
	})
	require.NotNil(t, actualErr, "Should have an error")
	require.Equal(t, "5010", util.GetPointerValue(actualErr).MetaData["field"], "Should have field error 5010")
}

func (ts *OpsValidationTestSuite) Test_Validate_Laterality() {
	t := ts.T()
	careProviderId := uuid.MustParse("00000000-0000-0000-0000-000000000001")
	ctx := titan.NewBackgroundContext().LoginToCareProviderAsRoleMustBeUsedAfterAuthentication(careProviderId.String(), titan.Role("test_role"))

	// Get ebm
	ebmCode := "31236"
	kv, err := okv_util.GetOkvByBsnr("931111100")
	require.Nil(t, err)
	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	ebm, err := ts.sdebmRepo.GetEbmByCode(ctx, &sdebm_repo.GetSdebmByCodeRequest{
		Code: ebmCode,
		YearQuarter: masterdata_model.YearQuarter{
			Year:    yearQuarter.Year,
			Quarter: yearQuarter.Quarter,
		},
		OrganizationId: kv,
	})
	require.Nil(t, err)
	now := time.Now()

	t.Run("should have error", func(t *testing.T) {
		entity := timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
			Payload: patient_encounter.EncounterServiceTimeline{
				Command: "Z",
				Code:    ebmCode,
				AdditionalInfos: &[]*patient_encounter.AdditionalInfoParent{
					{
						FK:    "5035",
						Value: "5-224.63",
						Children: []*patient_encounter.AdditionalInfoChild{
							{
								FK:    "5041",
								Value: "L",
							},
						},
					},
				},
			},
			CreatedAt:    now,
			SelectedDate: now,
		}
		validator := precondition.NewKvServiceIncludedOpsValidator(ctx, *ebm.Bedingung, nil, sdops_service.SdopsServiceMod.Resolve())
		errs := validator.Validate(entity)
		actualErr := slice.FindOne(errs, func(e *validation_timeline.ValidationError) bool {
			return e.ErrorCode == string(validation_timeline.ServiceErrorCode_OPS_5041)
		})
		require.NotNil(t, actualErr, "Should have an error")
		require.Equal(t, "5041", util.GetPointerValue(actualErr).MetaData["field"])
	})

	t.Run("should not have error", func(t *testing.T) {
		entity := timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
			Payload: patient_encounter.EncounterServiceTimeline{
				Command: "Z",
				Code:    ebmCode,
				AdditionalInfos: &[]*patient_encounter.AdditionalInfoParent{
					{
						FK:    "5035",
						Value: "5-224.63",
						Children: []*patient_encounter.AdditionalInfoChild{
							{
								FK:    "5041",
								Value: "B",
							},
						},
					},
				},
			},
			CreatedAt:    now,
			SelectedDate: now,
		}
		validator := precondition.NewKvServiceIncludedOpsValidator(ctx, *ebm.Bedingung, nil, sdops_service.SdopsServiceMod.Resolve())
		errs := validator.Validate(entity)
		require.Empty(t, errs)
	})
}
