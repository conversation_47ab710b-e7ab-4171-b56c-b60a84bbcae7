package validations

import (
	"fmt"

	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdebm_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdkt_common"
	validation_timeline_service "git.tutum.dev/medi/tutum/ares/service/domains/api/validation_timeline"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/sdebm/additional_field"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	schein_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/validation_timeline/validations/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/validation_timeline/validations/validation_context"
	"gitlab.com/silenteer-oss/titan"
)

type AdditionalInfoValidator struct {
	ctx               *titan.Context
	validationContext *validation_context.CommonContext
	// entryValidationContext *common.EntryValidationContext
	schein              *schein_repo.ScheinRepo
	costUnit            *catalog_sdkt_common.SdktCatalog
	AdditionalFieldRepo *additional_field.AdditionalFieldRepo
}

// use for both SV and KV
func NewAdditionalInfoValidator(
	ctx *titan.Context,
	validationContext *validation_context.CommonContext,
	schein *schein_repo.ScheinRepo,
	costUnit *catalog_sdkt_common.SdktCatalog,
) common.ValidationRule[patient_encounter.EncounterServiceTimeline] {
	additionalFieldRepo, err := additional_field.AdditionalFieldRepoMod.SafeResolve()
	if err != nil {
		panic(err)
	}

	return &AdditionalInfoValidator{
		ctx:                 ctx,
		validationContext:   validationContext,
		schein:              schein,
		costUnit:            costUnit,
		AdditionalFieldRepo: additionalFieldRepo,
	}
}

func (g *AdditionalInfoValidator) Validate(
	timeline timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline],
) []*validation_timeline_service.ValidationError {
	validationErrors := make([]*validation_timeline_service.ValidationError, 0)
	refType := validation_timeline_service.ValidationEntryReferenceTypeService
	serviceId := timeline.Id
	serviceData := timeline.Payload
	ref := common.CreateValidationEntryReference(serviceId, refType)

	// now call ebm/additional info validator
	fieldValues := []catalog_sdebm_common.FieldValue{}
	if serviceData.AdditionalInfos != nil {
		for _, additionalInfo := range *serviceData.AdditionalInfos {
			fieldValue := catalog_sdebm_common.FieldValue{
				FK:                     additionalInfo.FK,
				Value:                  additionalInfo.Value,
				AdditionalInformations: []catalog_sdebm_common.FieldValue{},
			}
			if additionalInfo.Children != nil {
				children := []catalog_sdebm_common.FieldValue{}
				for _, child := range additionalInfo.Children {
					children = append(children, catalog_sdebm_common.FieldValue{
						FK:    child.FK,
						Value: child.Value,
					})
				}
				fieldValue.AdditionalInformations = children
			}

			fieldValues = append(fieldValues, fieldValue)
		}
	}

	// prepare more data
	extendedData := catalog_sdebm_common.ExtendedData{
		ServiceCode: serviceData.Code,
	}
	if g.schein != nil {
		extendedData.KvScheinSubGroup = g.schein.Schein.KvScheinSubGroup
		extendedData.TreatmentCase = g.schein.Schein.KvTreatmentCase
		if g.costUnit != nil {
			extendedData.FeeCatalogue = g.costUnit.FeeCatalogue
		} else {
			validationErrors = append(validationErrors, common.CreateValidationEntryError(
				"CostUnit nil",
				"0",
				[]*validation_timeline_service.ValidationEntryReference{ref},
			))
		}
	}

	// Check field Fk 5003 for service codes 03008 and 04008
	if timeline.Payload.Code == "03008" || timeline.Payload.Code == "04008" {
		isHasValue := false
		for _, additionalInfo := range *timeline.Payload.AdditionalInfos {
			if additionalInfo.FK == "5003" && additionalInfo.Value != "" {
				isHasValue = true
			}
		}
		if !isHasValue {
			validationErrors = append(validationErrors, common.CreateValidationEntryErrorCode(
				"MissingFk5003",
				map[string]string{
					"code": timeline.Payload.Code,
				},
				validation_timeline_service.ValidationType_error,
				[]*validation_timeline_service.ValidationEntryReference{ref},
			))
		}
	}

	// Check field Fk 5005 match Fk 5050 refs PRO-14390
	valueFk5005 := 0
	countFk5050 := 0
	if timeline.Payload.AdditionalInfos != nil {
		for _, additionalInfo := range *timeline.Payload.AdditionalInfos {
			if additionalInfo.FK == "5005" {
				_, _ = fmt.Sscan(additionalInfo.Value, &valueFk5005)
			}
			if additionalInfo.FK == "5050" {
				countFk5050++
			}
		}
		if countFk5050 > 0 && valueFk5005 > 0 && countFk5050 != valueFk5005 {
			validationErrors = append(validationErrors, common.CreateValidationEntryWarning(
				string(error_code.ErrorCode_Missmatch5005And5050),
				"",
				[]*validation_timeline_service.ValidationEntryReference{ref},
			))
		}
	}

	fieldErrors, _ := g.AdditionalFieldRepo.IsValid(g.ctx, fieldValues, extendedData)

	if len(fieldErrors) == 0 {
		return validationErrors
	}

	for _, fieldError := range fieldErrors {
		validationErrors = append(validationErrors, common.ToValidationError(&fieldError, ref))
	}

	g.ctx.Logger().Debug("===============FINISHED additional info VALIDATOR================")
	return validationErrors
}
