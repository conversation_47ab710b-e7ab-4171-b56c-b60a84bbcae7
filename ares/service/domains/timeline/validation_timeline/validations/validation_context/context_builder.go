package validation_context

import (
	"fmt"
	"strconv"
	"strings"
	"sync"

	"emperror.dev/errors"
	"github.com/google/uuid"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	catalog_sdkt_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_sdkt"
	masterdata_model "git.tutum.dev/medi/tutum/ares/pkg/masterdata/model"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	"git.tutum.dev/medi/tutum/ares/service/bsnr"
	contract_resource "git.tutum.dev/medi/tutum/ares/service/contract/contract"
	contract_model "git.tutum.dev/medi/tutum/ares/service/contract/contract/model"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdkt_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	sdkv_api "git.tutum.dev/medi/tutum/ares/service/domains/api/sdkv"

	catalog_sdebm_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_sdebm"
	catalog_sdkt_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_sdkt"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_repo"
	sdomim "git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/sdomim"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_participation"
	schein_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	sdicd_common "git.tutum.dev/medi/tutum/ares/service/domains/sdicd/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/sdicd/sdicd_service"
	sdkv_service "git.tutum.dev/medi/tutum/ares/service/domains/sdkv"
	"git.tutum.dev/medi/tutum/ares/service/domains/settings/settings_service"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/ares/service/rezidiv"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/submodule-org/submodule.go/v2"
)

var cache sync.Map

type PatientGetByIdFunc func(ctx *titan.Context, uuid uuid.UUID) (*patient.PatientProfile, error)
type EmployeeProfileFinderFunc func(ctx *titan.Context, filter any, opts ...*options.FindOptions) ([]employee.EmployeeProfile, error)
type BsnrGetByIdsFunc func(ctx *titan.Context, ids []uuid.UUID) ([]bsnr_repo.BSNR, error)
type ScheinFindByIdsFunc func(ctx *titan.Context, ids []uuid.UUID) ([]schein_repo.ScheinRepo, error)
type ScheinFindFunc func(ctx *titan.Context, filter any, opts ...*options.FindOptions) ([]schein_repo.ScheinRepo, error)
type ContextBuilder struct {
	sdkvService               sdkv_api.SDKVService
	catalogSdktService        *catalog_sdkt_service.CatalogSdktService
	catalogSdebmService       *catalog_sdebm_service.CatalogSdebmService
	contractService           *contract_resource.Service
	sdicdService              *sdicd_service.SdicdService
	timelineRepo              timeline_repo.TimelineEntityRepo[any]
	employeeProfileRepo       employee.EmployeeProfileDefaultRepository
	patientParticipationRepo  patient_participation.PatientParticipationDefaultRepository
	scheinRepo                schein_repo.ScheinRepoDefaultRepository
	sdomimRepo                *sdomim.SdomimRepo
	rezidivService            *rezidiv.RezidivList
	patientGetByIdFunc        PatientGetByIdFunc
	employeeProfileFinderFunc EmployeeProfileFinderFunc
	scheinFindByIdsFunc       ScheinFindByIdsFunc
	scheinFindFunc            ScheinFindFunc
	getGnrConditionWarningFn  settings_service.GetGnrConditionWarningFn
	getHideHintTfSGFn         settings_service.GetHideHintTfSGFn
	bsnrGetByIdsFunc          BsnrGetByIdsFunc
}

var PatientGetByHashIdMod = submodule.Make[PatientGetByIdFunc](func() PatientGetByIdFunc {
	repo := patient.NewPatientProfileDefaultRepository()
	return repo.GetById
})

var EmployeeProfileFinderMod = submodule.Make[EmployeeProfileFinderFunc](func() EmployeeProfileFinderFunc {
	repo := employee.NewEmployeeProfileDefaultRepository()
	return repo.Find
})

var BsnrGetByIdsMod = submodule.Make[BsnrGetByIdsFunc](func(bsnrService *bsnr.BSNRService) BsnrGetByIdsFunc {
	return bsnrService.GetByIds
}, bsnr.BSNRServiceMod)

var ScheinFindByIdsMod = submodule.Make[ScheinFindByIdsFunc](func() ScheinFindByIdsFunc {
	repo := schein_repo.NewScheinRepoDefaultRepository()
	return repo.FindByIds
})

var ScheinFindMod = submodule.Make[ScheinFindFunc](func() ScheinFindFunc {
	repo := schein_repo.NewScheinRepoDefaultRepository()
	return repo.Find
})

var ContextBuilderMod = submodule.Make[*ContextBuilder](func(
	patientGetByIdMod PatientGetByIdFunc,
	employeeProfileFinderMod EmployeeProfileFinderFunc,
	scheinFindByIdsMod ScheinFindByIdsFunc,
	scheinFindMod ScheinFindFunc,
	sdkvService *sdkv_service.SDKVService,
	sdomimRepo *sdomim.SdomimRepo,
	catalogSdktService *catalog_sdkt_service.CatalogSdktService,
	sdicdService *sdicd_service.SdicdService,
	contractService *contract_resource.Service,
	rezidivRepo *rezidiv.RezidivList,
	getGnrConditionWarningFn settings_service.GetGnrConditionWarningFn,
	getHideHintTfSGFn settings_service.GetHideHintTfSGFn,
	bsnrGetByIdsMod BsnrGetByIdsFunc,
	catalogSdebmService *catalog_sdebm_service.CatalogSdebmService,
) *ContextBuilder {
	return &ContextBuilder{
		sdkvService:               sdkvService,
		catalogSdktService:        catalogSdktService,
		sdicdService:              sdicdService,
		timelineRepo:              timeline_repo.NewTimelineRepoDefaultRepository[any](),
		employeeProfileRepo:       employee.NewEmployeeProfileDefaultRepository(),
		patientParticipationRepo:  patient_participation.NewPatientParticipationDefaultRepository(),
		scheinRepo:                schein_repo.NewScheinRepoDefaultRepository(),
		sdomimRepo:                sdomimRepo,
		rezidivService:            rezidivRepo,
		contractService:           contractService,
		patientGetByIdFunc:        patientGetByIdMod,
		employeeProfileFinderFunc: employeeProfileFinderMod,
		scheinFindByIdsFunc:       scheinFindByIdsMod,
		scheinFindFunc:            scheinFindMod,
		getGnrConditionWarningFn:  getGnrConditionWarningFn,
		getHideHintTfSGFn:         getHideHintTfSGFn,
		bsnrGetByIdsFunc:          bsnrGetByIdsMod,
		catalogSdebmService:       catalogSdebmService,
	}
},
	PatientGetByHashIdMod,
	EmployeeProfileFinderMod,
	ScheinFindByIdsMod,
	ScheinFindMod,
	sdkv_service.ServiceMod,
	sdomim.SdomimRepoMod,
	catalog_sdkt_service.CatalogSdktServiceMod,
	sdicd_service.SdicdServiceMod,
	contract_resource.ContractServiceMod,
	rezidiv.RezidivRepoMod,
	settings_service.GetGnrConditionWarningSettingFlow,
	settings_service.GetHideHintTfSGSettingFlow,
	BsnrGetByIdsMod,
	catalog_sdebm_service.CatalogSdebmServiceMod,
)

func (builder *ContextBuilder) InitSvContext(ctx *titan.Context, patientId uuid.UUID, contractId *string) (*SvContext, error) {
	commonContext, err := builder.InitCommonContext(ctx, patientId, contractId)
	if err != nil {
		return nil, errors.WithMessage(err, "init common context")
	}

	// NOTE: Get contract & charge system
	contractIdString := util.GetPointerValue(contractId)
	var contract *contract_model.Contract
	var chargeSystem *contract_model.HonoraranlageTyp
	mapFuncAKA := map[string]struct{}{}
	if contractIdString != "" {
		contract = builder.contractService.GetContractDetailById(contractIdString)
		if contract == nil {
			return nil, pkg_errors.NewTitanCommonException(
				ctx,
				error_code.ErrorCode_Contract_Not_Found,
				fmt.Sprintf("Contract with id %s not found", contractIdString),
			)
		}
		for _, funcAka := range contract.ContractDefinition.Vertrag.Anforderungen.Anforderung {
			mapFuncAKA[funcAka.ID] = struct{}{}
		}

		chargeSystem = contract.GetEffectiveChargeSystem(contractIdString)
		if chargeSystem == nil {
			return nil, errors.Errorf("%s is not a valid charge system of contract %s", contractIdString, *contractId)
		}
	}

	patientHealthInsurance := commonContext.PatientProfile.PatientInfo.GetActiveInsurance()
	// NOTE: Get patient particapates
	patientParticipates, err := builder.patientParticipationRepo.Find(ctx, bson.M{
		patient_participation.Field_PatientId: patientId,
		patient_participation.Field_Status: bson.M{
			"$in": bson.A{
				patient_participation.PatientParticipationStatus_Active,
				patient_participation.PatientParticipationStatus_Terminated,
			},
		},
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patient participates when in init sv context")
	}

	return &SvContext{
		CommonContext:          *commonContext,
		Contract:               contract,
		ChargeSystem:           chargeSystem,
		FuncAKAs:               mapFuncAKA,
		PatientHealthInsurance: patientHealthInsurance,
		PatientParticipates:    patientParticipates,
		IsSkipRules:            map[string]bool{},
	}, nil
}

func (builder *ContextBuilder) InitKvContext(ctx *titan.Context, patientId uuid.UUID) (*KvContext, error) {
	commonContext, err := builder.InitCommonContext(ctx, patientId, nil)
	if err != nil {
		return nil, errors.WithMessage(err, "init common context")
	}
	if commonContext == nil {
		ctx.Logger().Warn("common context for validation is nil")
		return nil, nil
	}

	scheinByPatientIds, err := builder.getScheinByPatientId(ctx, patientId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get schein by patient id when init kv context")
	}

	// NOTE: get bsnr by doctor profiles
	bsnrMapByDoctorId, err := builder.getBsnrMapByDoctorId(ctx, commonContext.DoctorProfilesMap)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get bsnr map by doctor id when init kv context")
	}

	// NOTE: Get ebm
	ebmMapByKey, err := builder.getEbmMapByKey(ctx, commonContext.Services, commonContext.DoctorProfilesMap, commonContext.ScheinMap)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get ebm map by key when init kv context")
	}
	// NOTE: Get sdkv
	sdkvMapByBsnr, err := builder.getSdkvMapByBsnr(ctx, commonContext.DoctorProfilesMap)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get sdkv map by bsnr when init kv context")
	}

	// NOTE: Get tss service code
	// TODO: update logic to get correct ebms
	tssServiceCode, err := builder.getTssServiceCodes(ctx, commonContext.timelines, commonContext.DoctorProfilesMap)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get tss service code when init kv context")
	}

	// TODO: move to builder function
	patientParticipates, err := builder.patientParticipationRepo.Find(ctx, bson.M{
		patient_participation.Field_PatientId: patientId,
		patient_participation.Field_Status: bson.M{
			"$in": bson.A{
				patient_participation.PatientParticipationStatus_Active,
				patient_participation.PatientParticipationStatus_Terminated,
			},
		},
	})
	if err != nil {
		return nil, err
	}

	return &KvContext{
		CommonContext:       *commonContext,
		Scheins:             scheinByPatientIds,
		EbmMapByKey:         ebmMapByKey,
		RezidivList:         builder.rezidivService.GetAllRezidivList(),
		Sdkvs:               sdkvMapByBsnr,
		TssServiceCodes:     tssServiceCode,
		Psychotherapies:     builder.getPsychotherapys(commonContext.timelines),
		BsnrMapByDoctorId:   bsnrMapByDoctorId,
		PatientParticipates: patientParticipates,
		SdomimRepo:          builder.sdomimRepo,
	}, nil
}

func (builder *ContextBuilder) InitCommonContext(ctx *titan.Context, patientId uuid.UUID, contractId *string) (*CommonContext, error) {
	patientProfile, err := builder.patientGetByIdFunc(ctx, patientId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patient profile")
	}
	if patientProfile == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Patient_Not_Found)
	}

	// NOTE: Get timelines
	timelines, err := builder.timelineRepo.GetTimelinesForValidation(ctx, patientId, contractId)
	if err != nil {
		return nil, fmt.Errorf("get timelines for validation: %w", err)
	}
	diagnoses := builder.getDiagnoses(timelines)
	services := builder.getServices(timelines)

	// NOTE: get schein
	scheinMap, err := builder.getScheinMap(ctx, timelines)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get schein map")
	}

	// NOTE: Get Doctor profile
	doctorProfilesMap, err := builder.getDoctorProfilesMap(ctx, timelines, scheinMap)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get doctor profile map")
	}

	// NOTE: get sdkt
	sdktMapByScheinId := map[uuid.UUID]*catalog_sdkt_common.SdktCatalog{}
	memoize := function.Memoize[*catalog_sdkt_api.GetSdktCatalogByVknrResponse, string]()
	for _, schein := range scheinMap {
		insurance := slice.FindOne(patientProfile.PatientInfo.InsuranceInfos, func(ins patient_profile_common.InsuranceInfo) bool {
			return schein.Schein.InsuranceId == ins.Id
		})
		if insurance == nil || insurance.InsuranceCompanyId == "" {
			continue
		}

		sdkt, err := memoize(
			func() string {
				return insurance.InsuranceCompanyId
			},
			func() (*catalog_sdkt_api.GetSdktCatalogByVknrResponse, error) {
				now := util.NowUnixMillis(ctx)
				return builder.catalogSdktService.GetSdktCatalogByVknr(ctx, &catalog_sdkt_api.GetSdktCatalogByVknrRequest{
					Vknr:         insurance.InsuranceCompanyId,
					SelectedDate: &now,
				})
			},
		)
		if err != nil {
			return nil, errors.WithDetails(err, "get sdkt by id", insurance.InsuranceCompanyId)
		}
		if sdkt != nil {
			sdktMapByScheinId[*schein.Id] = sdkt.Data
		}
	}

	// NOTE: get icd map
	icdMap, err := builder.getIcdMap(ctx, diagnoses)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get icd map")
	}

	enableGnrWarning, err := builder.getGnrConditionWarningFn(ctx)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get gnr setting")
	}
	isHideTfSGError, err := builder.getHideHintTfSGFn(ctx)
	if err != nil {
		return nil, errors.WithDetails(err, "failed to get gnr setting")
	}

	bsnrMapByBsnrId, err := builder.getBsnrMapByBsnrId(ctx, doctorProfilesMap)
	if err != nil {
		return nil, errors.WithDetails(err, "failed to get bsnr map by bsnr id")
	}

	return &CommonContext{
		PatientProfile:    patientProfile,
		Diagnoses:         diagnoses,
		Services:          services,
		DoctorProfilesMap: doctorProfilesMap,
		ScheinMap:         scheinMap,
		SdktMapByScheinId: sdktMapByScheinId,
		IcdMap:            icdMap,
		EnableGnrWarning:  util.GetPointerValue(enableGnrWarning),
		IsHideTfSGError:   util.GetPointerValue(isHideTfSGError),
		timelines:         timelines,
		bsnrMapByBsnrId:   bsnrMapByBsnrId,
	}, nil
}

func (*ContextBuilder) getDiagnoses(
	timelines []timeline_repo.TimelineEntity[any],
) []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline] {
	diagnoses := slice.Filter(timelines, func(t timeline_repo.TimelineEntity[any]) bool {
		return t.Type == common.TimelineEntityType_Diagnose
	})
	return slice.Map(diagnoses, func(t timeline_repo.TimelineEntity[any]) timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline] {
		return timeline_repo.ToEntityTypeFromT[any, patient_encounter.EncounterDiagnoseTimeline](t)
	})
}

func (builder *ContextBuilder) getIcdMap(ctx *titan.Context, diagnoses []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) (map[string]*sdicd_common.IcdItem, error) {
	uniqDiagnoses := slice.UniqBy(diagnoses, func(d timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) string {
		return d.Payload.Code
	})
	uniqCodes := slice.Map(uniqDiagnoses, func(d timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) string {
		return d.Payload.Code
	})
	filterCodes := slice.Filter(uniqCodes, func(code string) bool {
		return code != ""
	})

	// TODO: review logic search icd by code and year
	sdicdRequest := sdicd_service.GetDiagnoseByCodesRequest{
		Codes: filterCodes,
		Year:  util.NowYear(ctx),
	}
	icds, err := builder.sdicdService.GetDiagnoseByCodes(ctx, sdicdRequest)
	if err != nil {
		return nil, err
	}

	if icds == nil {
		return nil, nil
	}

	icdMap := map[string]*sdicd_common.IcdItem{}
	for _, icd := range icds {
		icdMap[icd.Code] = &icd
	}

	return icdMap, nil
}

func (*ContextBuilder) getServices(
	timelines []timeline_repo.TimelineEntity[any],
) []timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline] {
	services := slice.Filter(timelines, func(t timeline_repo.TimelineEntity[any]) bool {
		return t.Type == common.TimelineEntityType_Service
	})
	return slice.Map(services, func(t timeline_repo.TimelineEntity[any]) timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline] {
		return timeline_repo.ToEntityTypeFromT[any, patient_encounter.EncounterServiceTimeline](t)
	})
}

func (*ContextBuilder) getPsychotherapys(
	timelines []timeline_repo.TimelineEntity[any],
) []timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy] {
	pyschotherapies := slice.Filter(timelines, func(t timeline_repo.TimelineEntity[any]) bool {
		return t.Type == common.TimelineEntityType_Psychotherapy
	})
	return slice.Map(pyschotherapies, func(t timeline_repo.TimelineEntity[any]) timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy] {
		return timeline_repo.ToEntityTypeFromT[any, patient_encounter.EncounterPsychotherapy](t)
	})
}

func (builder *ContextBuilder) getScheinMap(ctx *titan.Context, timelines []timeline_repo.TimelineEntity[any]) (map[uuid.UUID]schein_repo.ScheinRepo, error) {
	timelineWithScheins := slice.Filter(timelines, func(t timeline_repo.TimelineEntity[any]) bool {
		return len(t.ScheinIds) > 0
	})
	scheinIds := slice.Map(timelineWithScheins, func(t timeline_repo.TimelineEntity[any]) uuid.UUID {
		return t.ScheinIds[0]
	})

	scheins, err := builder.scheinFindByIdsFunc(ctx, scheinIds)
	if err != nil {
		return nil, err
	}
	if len(scheins) == 0 {
		return nil, nil
	}

	scheinMap := map[uuid.UUID]schein_repo.ScheinRepo{}
	for _, schein := range scheins {
		scheinMap[*schein.Id] = schein
	}

	return scheinMap, nil
}

func (builder *ContextBuilder) getScheinByPatientId(
	ctx *titan.Context,
	patientId uuid.UUID,
) ([]schein_repo.ScheinRepo, error) {
	scheins, err := builder.scheinFindFunc(ctx, bson.M{
		schein_repo.Field_PatientId: patientId,
		schein_repo.Field_IsDeleted: false,
	})
	if err != nil {
		return nil, err
	}
	return scheins, nil
}

func (builder *ContextBuilder) getDoctorProfilesMap(
	ctx *titan.Context,
	timelines []timeline_repo.TimelineEntity[any],
	scheinMap map[uuid.UUID]schein_repo.ScheinRepo,
) (map[uuid.UUID]*employee.EmployeeProfile, error) {
	doctorIds := getDoctorIds(timelines, scheinMap)
	doctorProfiles, err := builder.employeeProfileFinderFunc(ctx, bson.M{
		employee.Field_Id: bson.M{
			"$in": doctorIds,
		},
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get doctor profiles")
	}

	doctorProfilesMap := make(map[uuid.UUID]*employee.EmployeeProfile)
	for _, dp := range doctorProfiles {
		tmp := dp
		doctorProfilesMap[*dp.Id] = &tmp
	}

	return doctorProfilesMap, nil
}

func (builder *ContextBuilder) getBsnrMapByBsnrId(ctx *titan.Context,
	doctorProfilesMap map[uuid.UUID]*employee.EmployeeProfile) (map[uuid.UUID]bsnr_repo.BSNR, error) {
	bsnrIds := []uuid.UUID{}
	for _, v := range doctorProfilesMap {
		bsnrIds = append(bsnrIds, v.BsnrIds...)
	}
	bsnrIds = slice.Uniq(bsnrIds)
	bsnrEntities, err := builder.bsnrGetByIdsFunc(ctx, bsnrIds)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get bsnr entities")
	}

	if len(bsnrEntities) == 0 {
		return map[uuid.UUID]bsnr_repo.BSNR{}, nil
	}

	mapper := map[uuid.UUID]bsnr_repo.BSNR{}
	for _, bsnr := range bsnrEntities {
		mapper[*bsnr.Id] = bsnr
	}

	return mapper, nil
}

func (builder *ContextBuilder) getBsnrMapByDoctorId(ctx *titan.Context, doctorProfilesMap map[uuid.UUID]*employee.EmployeeProfile) (map[uuid.UUID]bsnr_repo.BSNR, error) {
	bsnrIds := []uuid.UUID{}
	for _, v := range doctorProfilesMap {
		bsnrIds = append(bsnrIds, util.GetPointerValue(v.BsnrId))
	}
	bsnrEntities, err := builder.bsnrGetByIdsFunc(ctx, bsnrIds)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get bsnr entities")
	}

	if len(bsnrEntities) == 0 {
		return map[uuid.UUID]bsnr_repo.BSNR{}, nil
	}

	mapper := map[uuid.UUID]bsnr_repo.BSNR{}
	for id, doctor := range doctorProfilesMap {
		if doctor == nil || doctor.BsnrId == nil {
			continue
		}
		bsnr := slice.FindOne(bsnrEntities, func(b bsnr_repo.BSNR) bool { return *doctor.BsnrId == *b.Id })
		if bsnr != nil {
			mapper[id] = *bsnr
		}
	}

	return mapper, nil
}

func (builder *ContextBuilder) getSdkvMapByBsnr(ctx *titan.Context, doctorProfilesMap map[uuid.UUID]*employee.EmployeeProfile) (map[string]sdkv_api.SDKV, error) {
	bsnrs := []string{}
	for _, v := range doctorProfilesMap {
		if v.Bsnr != "" {
			bsnrs = append(bsnrs, v.Bsnr)
		}
		bsnrs = append(bsnrs, v.Bsnrs...)
	}
	bsnrs = slice.Uniq(bsnrs)
	resp, err := builder.sdkvService.GetSDKVByBsnrs(ctx, sdkv_api.GetSDKVByBsnrsRequest{
		Bsnrs: bsnrs,
	})
	if err != nil {
		return nil, fmt.Errorf("get sdkv by bsnrs: %w", err)
	}

	if resp == nil || resp.Sdkvs == nil {
		return map[string]sdkv_api.SDKV{}, nil
	}

	return resp.Sdkvs, nil
}

// TODO: Need to check tss validation flow (wrong data)
func (builder *ContextBuilder) getTssServiceCodes(
	ctx *titan.Context,
	timelines []timeline_repo.TimelineEntity[any],
	doctorProfilesMap map[uuid.UUID]*employee.EmployeeProfile,
) (map[string][]string, error) {
	if len(timelines) == 0 {
		return map[string][]string{}, nil
	}

	firstTreatmentDoctorId := timelines[0].TreatmentDoctorId
	selectedDate := util.NowUnixMillis(ctx)
	if len(timelines) > 0 {
		selectedDate = util.ConvertTimeToMiliSecond(timelines[0].SelectedDate)
	}
	d, ok := doctorProfilesMap[firstTreatmentDoctorId]
	if !ok {
		return map[string][]string{}, nil
	}
	okv := d.GetOKV()
	yq := util.ToYearQuarter(selectedDate)
	key := fmt.Sprintf("TssServiceCodes_%s_%d_%d", okv, yq.Year, yq.Quarter)
	if value, ok := cache.Load(key); ok {
		return value.(map[string][]string), nil
	}

	result, err := builder.catalogSdebmService.GetTssServiceCodes(ctx, &catalog_sdebm_service.GetTssServiceCodesRequest{
		KvRegion:     okv,
		SelectedDate: selectedDate,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get tss service codes")
	}
	cache.Store(key, result)

	return result, nil
}

type EbmCatalogOverviewByCodes []*catalog_sdebm_service.GetEbmCatalogOverviewByCodesResponse

func (o EbmCatalogOverviewByCodes) Get(code, kvRegion string, year, quarter int32) *masterdata_model.Sdebm {
	for _, v := range o {
		if c, ok := v.GetWithQuarter(code, kvRegion, year, quarter); ok {
			return &c
		}
	}
	return nil
}

// TODO: output need scheinMap, doctorMap
func (builder *ContextBuilder) getEbmMapByKey(
	ctx *titan.Context,
	services []timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline],
	doctorProfileMapById map[uuid.UUID]*employee.EmployeeProfile,
	scheinMap map[uuid.UUID]schein_repo.ScheinRepo,
) (EbmCatalogOverviewByCodes, error) {
	servicesWithSchein := slice.Filter(services, func(s timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) bool {
		return len(s.ScheinIds) > 0
	})

	serviceCodeGroupByBsnrAndTime := slice.GroupBy(servicesWithSchein, func(s timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) string {
		// NOTE: timeline -> schein -> doctor -> bsnr
		schein := scheinMap[s.ScheinIds[0]]
		doctor := doctorProfileMapById[schein.DoctorId]
		okv := doctor.GetOKV()
		key := strconv.Itoa(s.Year) + "_" + strconv.Itoa(s.Quarter) + "_" + okv

		return key
	})

	var getEbmCatalogOverviewByCodesResponses []*catalog_sdebm_service.GetEbmCatalogOverviewByCodesResponse
	memoize := function.Memoize[*catalog_sdebm_service.GetEbmCatalogOverviewByCodesResponse, string]()
	for _, serviceGroup := range serviceCodeGroupByBsnrAndTime {
		codes := slice.Map(serviceGroup, func(s timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) string {
			return s.Payload.Code
		})

		if len(codes) == 0 {
			continue
		}

		// NOTE: a group has the same year & quarter & kvRegion
		yearQuarter := util.YearQuarter{
			Year:    int32(serviceGroup[0].Year),    //nolint:gosec // Intentional conversion
			Quarter: int32(serviceGroup[0].Quarter), //nolint:gosec // Intentional conversion
		}
		if yearQuarter.Year <= 2022 {
			yearQuarter.Year = 2022
			yearQuarter.Quarter = 4
		}
		schein := scheinMap[serviceGroup[0].ScheinIds[0]]
		kvRegion := doctorProfileMapById[schein.DoctorId].GetOKV()
		ebms, err := memoize(
			func() string {
				return fmt.Sprintf("GetEbmByCodes_%s_%s_%d_%d", strings.Join(codes, ","), kvRegion, yearQuarter.Year, yearQuarter.Quarter)
			},
			func() (*catalog_sdebm_service.GetEbmCatalogOverviewByCodesResponse, error) {
				ebms, err := builder.catalogSdebmService.GetSdebmCatalogByCodes(ctx, &catalog_sdebm_service.GetEbmCatalogOverviewByCodesRequest{
					Codes:        codes,
					RegionalKv:   kvRegion,
					SelectedDate: *yearQuarter.StartTime(),
				})
				if err != nil {
					return nil, errors.WithDetails(err, "codes", codes)
				}
				return ebms, nil
			},
		)
		if err != nil {
			return nil, errors.WithDetails(err, "codes", codes)
		}

		if ebms == nil || len(ebms.Result) == 0 {
			continue
		}
		getEbmCatalogOverviewByCodesResponses = append(getEbmCatalogOverviewByCodesResponses, ebms)
	}
	return getEbmCatalogOverviewByCodesResponses, nil
}

// NOTE: helper func ----------------------------------------------------------------------------------------------------------------------
func GetEbmKey(code, kvRegion string, year, quarter int32) string {
	return code + "_" + kvRegion + "_" + strconv.Itoa(int(year)) + "_" + strconv.Itoa(int(quarter))
}

func getDoctorIds(timelines []timeline_repo.TimelineEntity[any], scheinMap map[uuid.UUID]schein_repo.ScheinRepo) []uuid.UUID {
	// NOTE: Get treatment doctors
	doctorIds := slice.Map(timelines, func(t timeline_repo.TimelineEntity[any]) uuid.UUID {
		return t.TreatmentDoctorId
	})

	// NOTE: Get billing doctors
	var billingDoctorIds []uuid.UUID
	for _, t := range timelines {
		if t.BillingDoctorId != nil {
			billingDoctorIds = append(billingDoctorIds, *t.BillingDoctorId)
		}
	}
	doctorIds = append(doctorIds, billingDoctorIds...)

	// TODO: Get selected doctor from schein
	for _, schein := range scheinMap {
		doctorIds = append(doctorIds, schein.DoctorId)
	}

	return doctorIds
}
