package validation_context

import (
	"emperror.dev/errors"
	"git.tutum.dev/medi/tutum/ares/service/contract/contract/model"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdkt_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/sdkv"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_repo"
	sdomim "git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/sdomim"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_participation"
	schein_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	sdicd_common "git.tutum.dev/medi/tutum/ares/service/domains/sdicd/common"
	"git.tutum.dev/medi/tutum/ares/service/rezidiv"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
)

type CommonContext struct {
	PatientProfile    *patient.PatientProfile
	ScheinMap         map[uuid.UUID]schein_repo.ScheinRepo
	DoctorProfilesMap map[uuid.UUID]*employee.EmployeeProfile
	Diagnoses         []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline] // NOTE: Validation: Store all diagnoses and services of 3 last quarters of patient
	Services          []timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]  // NOTE: Validation: Store all diagnoses and services of 3 last quarters of patient
	SdktMapByScheinId map[uuid.UUID]*catalog_sdkt_common.SdktCatalog
	IcdMap            map[string]*sdicd_common.IcdItem
	EnableGnrWarning  bool
	IsHideTfSGError   bool
	bsnrMapByBsnrId   map[uuid.UUID]bsnr_repo.BSNR

	timelines []timeline_repo.TimelineEntity[any]
}

func (v *CommonContext) FindDiagnoses(code string) []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline] {
	return slice.Filter(v.Diagnoses, func(t timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) bool {
		return t.Payload.Code == code
	})
}

func (v *CommonContext) FindServices(code string) []timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline] {
	return slice.Filter(v.Services, func(t timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) bool {
		return t.Payload.Code == code
	})
}

func (v *CommonContext) GetKvRegionByScheinId(scheinId uuid.UUID) string {
	schein := v.ScheinMap[scheinId]
	if schein.AssignedToBsnrId == nil {
		return ""
	}
	bsnr := v.bsnrMapByBsnrId[*schein.AssignedToBsnrId].Code
	if len(bsnr) < 3 {
		return ""
	}
	return util.GetOKVByUKV(bsnr[:2])
}

func (v *CommonContext) GetOkvs(
	billingDoctorId *uuid.UUID,
) []string {
	if billingDoctorId == nil {
		return nil
	}

	if doctorProfile, ok := v.DoctorProfilesMap[*billingDoctorId]; ok {
		return []string{*doctorProfile.Okv}
	}
	return nil
}

type KvContext struct {
	CommonContext
	TssServiceCodes     map[string][]string                                                      // kv only
	Psychotherapies     []timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy] // kv only
	Sdkvs               map[string]sdkv.SDKV                                                     // kv only
	RezidivList         []rezidiv.Rezidiv                                                        // kv only
	EbmMapByKey         EbmCatalogOverviewByCodes                                                // kv only
	Scheins             []schein_repo.ScheinRepo                                                 // kv only
	BsnrMapByDoctorId   map[uuid.UUID]bsnr_repo.BSNR                                             // kv only
	PatientParticipates []patient_participation.PatientParticipation                             // For ComplianceID ABRD1564
	SdomimRepo          *sdomim.SdomimRepo
}

type SvContext struct {
	CommonContext
	Contract               *model.Contract                              // sv only
	ChargeSystem           *model.HonoraranlageTyp                      // sv only
	FuncAKAs               map[string]struct{}                          // sv only
	PatientHealthInsurance *patient_profile_common.InsuranceInfo        // sv only
	PatientParticipates    []patient_participation.PatientParticipation // sv only
	IsSkipRules            map[string]bool                              // sv only
}

func (v *SvContext) FindServiceDefinition(chargeSystemId, code string, selectedTime int64) *model.LeistungTyp {
	// NOTE: get charge system
	chargeSystem := v.Contract.GetEffectiveChargeSystem(chargeSystemId)
	if chargeSystem == nil {
		chargeSystem = v.ChargeSystem
		moduleChargeSystems := v.Contract.GetModuleChargeSystems()
		if len(moduleChargeSystems) > 0 {
			for _, moduleChargeSystem := range moduleChargeSystems {
				if moduleChargeSystem.GetId() == chargeSystemId {
					chargeSystem = &moduleChargeSystem
				}
			}
		}
	}

	foundServiceDefinition, _ := chargeSystem.GetServiceDefinition(code, selectedTime)
	return foundServiceDefinition
}

func (v *SvContext) GetMainInsuranceOffice(
	encounterDate int64,
	billingDoctorId *uuid.UUID,
) *model.Hauptkasse {
	if v.PatientHealthInsurance == nil {
		return nil
	}
	mainInsurances := v.Contract.MainOfficeInsurances()
	okvs := v.GetOkvs(billingDoctorId)
	ikNumber := v.PatientHealthInsurance.IkNumber

	return mainInsurances.GetInsuranceOffice(ikNumber, encounterDate, okvs)
}

func (v *SvContext) AreConditionsValid(
	encounterDate int64,
	billingDoctorId *uuid.UUID,
	conditions *model.Bedingungen,
) bool {
	if conditions == nil {
		return true
	}

	foundMainInsurance := v.GetMainInsuranceOffice(encounterDate, billingDoctorId)
	okvs := v.GetOkvs(billingDoctorId)

	return conditions.IsKvRegionValid(okvs, encounterDate) &&
		conditions.IsIkGroupValid(encounterDate, foundMainInsurance.IkgruppeRef) &&
		conditions.IsPrimaryIkValid(encounterDate, foundMainInsurance.IK)
}

func (v *SvContext) ValidateConditions(
	encounterDate int64,
	billingDoctorId *uuid.UUID,
	conditions *model.Bedingungen,
) error {
	if conditions == nil {
		return nil
	}

	foundMainInsurance := v.GetMainInsuranceOffice(encounterDate, billingDoctorId)
	okvs := v.GetOkvs(billingDoctorId)

	if !conditions.IsIkGroupValid(encounterDate, foundMainInsurance.IkgruppeRef) {
		return errors.Errorf("INVALID_IK_GROUP_TO_ADD_SERVICE")
	}

	if !conditions.IsKvRegionValid(okvs, encounterDate) {
		return errors.Errorf("INVALID_REGION_TO_ADD_SERVICE")
	}

	if !conditions.IsPrimaryIkValid(encounterDate, foundMainInsurance.IK) {
		return errors.Errorf("NO_AVAILABLE_IK_FUND")
	}

	return nil
}
