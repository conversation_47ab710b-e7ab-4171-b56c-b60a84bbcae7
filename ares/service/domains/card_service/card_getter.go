package card_service

import (
	"fmt"

	"emperror.dev/errors"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/cardservice"
	catalog_sdkt_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_sdkt"
	patient_profile_repo "git.tutum.dev/medi/tutum/ares/app/mvz/patient_profile"
	"git.tutum.dev/medi/tutum/ares/pkg/api_telematik_util"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	"git.tutum.dev/medi/tutum/ares/pkg/validator"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/card_common"
	catalog_sdkt_common "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdkt_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	card_service_common "git.tutum.dev/medi/tutum/ares/service/domains/card_service/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	"git.tutum.dev/medi/tutum/ares/service/domains/insurance_service/insurance_validator"
	"git.tutum.dev/medi/tutum/ares/service/domains/insurance_service/sdkt_validator"
	"git.tutum.dev/medi/tutum/ares/service/domains/patient_finder"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/KVK"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/VSD"
	"git.tutum.dev/medi/tutum/pkg/util"

	"gitlab.com/silenteer-oss/titan"
)

type EgkGetter struct {
	cardResponse *GetCardResponse
	cardService  *CardService
	mandantId    string
}

type KvkGetter struct {
	cardResponse *GetCardResponse
	cardService  *CardService
	mandantId    string
}

func (s *KvkGetter) Get(ctx *titan.Context) (*cardservice.GetCardsResponse, error) {
	now := util.NowUnixMillis(ctx)

	regionCode, err := s.cardService.getRegionCode(s.mandantId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get region code")
	}

	readKvkResponse, ok := s.cardResponse.CardInfo.(*KVK.ReadKvkresponse)
	if !ok {
		return nil, errors.New("failed to cast cardResponse to kvk.ReadKVKResponse")
	}

	patientKvkData, err := card_service_common.ReadPatientFromKVK(readKvkResponse)
	if err != nil {
		return nil, api_telematik_util.ParseTiError(ctx, err)
	}
	if patientKvkData == nil {
		return nil, errors.New("patient kvk data is nil")
	}
	if patientKvkData.CountryCode == "" {
		patientKvkData.CountryCode = "D"
	}

	kvkMapper := api_telematik_util.NewKvkMapper(patientKvkData, api_telematik_util.FromTi)

	kvkCardInsurance := kvkMapper.ToPatientInfo(ctx).GetCardInsurance()
	if kvkCardInsurance == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_CardReader_Invalid_Insurance)
	}

	sdkt, sdik, err := s.cardService.patientProfileService.GetSdktAndSdikByInsuranceInfo(ctx, *kvkCardInsurance)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get sdkt catalog")
	}

	patientInfo := kvkMapper.WithSdkt(sdkt).WithSdik(sdik).ToPatientInfo(ctx)
	if patientInfo == nil {
		return nil, errors.New("patient info is nil")
	}

	cardInsurance := patientInfo.GetCardInsurance()
	if cardInsurance == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_CardReader_Invalid_Insurance)
	}

	err = validator.Run(ctx,
		api_telematik_util.KvkValidator{
			KvkPatient:     patientKvkData,
			Sdkt:           sdkt,
			Sdik:           sdik,
			RestrictRegion: regionCode,
		},
		&api_telematik_util.KvkValidator{})
	if err != nil {
		switch {
		case errors.Is(err, api_telematik_util.ErrorCardInvalidSdik):
			return nil, pkg_errors.NewTitanCommonExceptionWithParams(error_code.ErrorCode(err.Error()), map[string]string{"InsuranceCompanyName": cardInsurance.InsuranceCompanyName})
		default:
			return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode(err.Error()))
		}
	}

	readingDate := cardInsurance.GetReadCardByCreatedAt(util.ToYearQuarter(now)).GetReadCardDate()
	if readingDate == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_CardReader_ReadCardDate_Not_Found)
	}

	cardRaw, err := s.cardService.saveCardRawData(ctx, SaveCardRawDataRequest{
		Iccsn:       s.cardResponse.Iccsn,
		KvkData:     &api_telematik_util.KvkData{Kvk: *readKvkResponse},
		ReadingDate: *readingDate,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to save card raw data: %w", err)
	}

	err = s.cardService.checkValidKVKSdkt(patientKvkData, cardInsurance, *cardRaw.Id)
	if err != nil {
		return nil, err
	}

	err = validator.Run(ctx, *cardInsurance, &insurance_validator.InsuranceValidator{})
	if err != nil {
		return nil, pkg_errors.NewTitanCommonExceptionWithParams(error_code.ErrorCode(err.Error()), map[string]string{"CardRawId": cardRaw.Id.String()})
	}

	result, err := patient_finder.
		NewPatientFinder(s.cardService.cardRawService).
		FindPatient(ctx, card_common.CardTypeTypeKVK, *patientInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to find patient: %w", err)
	}

	for _, v := range result.PatientCompareData.PatientsPrepared {
		if v.ValidationError != nil && *v.ValidationError == error_code.ErrorCode_CardReader_Invalid_KVK {
			return nil, pkg_errors.NewTitanCommonException(ctx, *v.ValidationError)
		}
	}

	err = s.cardService.onReadCard(ctx, &result.PatientCompareData, *cardRaw.Id)
	if err != nil {
		return nil, fmt.Errorf("failed to on read card exact match: %w", err)
	}

	patientsPrepared := patient_profile_common.PatientsPrepared(result.PatientCompareData.PatientsPrepared)
	result.PatientCompareData.PatientsPrepared = patientsPrepared.UpdateCardRawId(cardRaw.Id)
	result.PatientCompareData.PatientFromCard.CardRawId = cardRaw.Id

	return result, nil
}

func (s *EgkGetter) Get(ctx *titan.Context) (*cardservice.GetCardsResponse, error) {
	now := util.NowUnixMillis(ctx)

	regionCode, err := s.cardService.getRegionCode(s.mandantId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get region code")
	}

	kvName := s.cardService.getKvAreaByKvRegion(*regionCode)
	readVsdResponse, ok := s.cardResponse.CardInfo.(*VSD.ReadVsdresponse)
	if !ok {
		return nil, errors.New("failed to cast cardResponse to vsds.ReadVSDResponse")
	}

	vsdData, err := api_telematik_util.GetVsdData(ctx, readVsdResponse)
	if err != nil {
		return nil, err
	}

	egkProfile := vsdData.GetPatientInfo()
	vsdWarning := util.GetPointerValue(vsdData.GetWarningCode())
	warningErrorCodes := []error_code.ErrorCode{vsdWarning}

	cardInsurance := egkProfile.GetCardInsurance()
	if cardInsurance == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_NotActive_InsuranceInfo)
	}

	sdktCatalog, err := s.cardService.patientProfileService.GetSdktCatalogByIkNumber(ctx, patient_profile_repo.GetSdktCatalogByIkNumberRequest{
		IkNumber:     cardInsurance.GetIkNumberString(),
		SelectedDate: now,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get sdkt catalog")
	}
	if sdktCatalog == nil {
		res, err := s.cardService.catalogSdktService.SearchSdkt(ctx, &catalog_sdkt_api.SearchSdktRequest{
			SearchType:   catalog_sdkt_common.SearchType_CostUnitName,
			Value:        cardInsurance.InsuranceCompanyName,
			SelectedDate: now,
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}

		foundName := res != nil && len(res.Items) > 0
		return nil, pkg_errors.NewTitanCommonExceptionWithParams(error_code.ErrorCode_IK_NotFound, map[string]string{
			"IkNumber":             cardInsurance.GetIkNumberString(),
			"InsuranceCompanyName": cardInsurance.InsuranceCompanyName,
			"FoundName":            fmt.Sprintf("%t", foundName),
		})
	}

	isAcquiredCostUnit := sdktCatalog.AccquiredCostUnit != nil
	if isAcquiredCostUnit {
		acquiredCostUnit, err := s.cardService.catalogSdktService.UniqCompanyByAccquiredCostUnit(ctx, &catalog_sdkt_api.UniqCompanyByAccquiredCostUnitRequest{
			SdktCatalog: sdktCatalog,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "get UniqCompanyByAcquiredCostUnit failed")
		}

		sdktCatalog = acquiredCostUnit.SdktCatalog
	}

	egkData, err := api_telematik_util.GetEgkData(readVsdResponse, false)
	if err != nil {
		return nil, errors.WithMessage(err, "get egk data failed")
	}

	readingDate := cardInsurance.GetReadCardByCreatedAt(util.ToYearQuarter(now)).GetReadCardDate()
	if readingDate == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_CardReader_ReadCardDate_Not_Found)
	}

	cardRaw, err := s.cardService.saveCardRawData(ctx, SaveCardRawDataRequest{
		EgkData:     egkData,
		ReadingDate: *readingDate,
		Iccsn:       s.cardResponse.Iccsn,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to save card raw data: %w", err)
	}

	err = validator.Run(ctx, *cardInsurance, &insurance_validator.InsuranceValidator{})
	if err != nil {
		return nil, pkg_errors.NewTitanCommonExceptionWithParams(error_code.ErrorCode(err.Error()), map[string]string{"CardRawId": cardRaw.Id.String()})
	}

	err = validator.Run(
		ctx,
		sdkt_validator.SdktValidator{
			Sdkt:           *sdktCatalog,
			RestrictRegion: regionCode,
			IkNumber:       sdktCatalog.GetIkNumberByValue(cardInsurance.IkNumber),
		},
		&sdkt_validator.SdktValidator{},
	)
	switch {
	case errors.Is(err, sdkt_validator.ErrorSdktInvalidValidity):
		return nil, pkg_errors.NewTitanCommonExceptionWithParams(error_code.ErrorCode_CardReader_InsHasBeenTerminated, nil)
	case errors.Is(err, sdkt_validator.ErrorSdktInvalidRestrictRegion):
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_CardReader_InsHasBeenRestrictArea, kvName)
	case errors.Is(err, sdkt_validator.ErrorSdktIkNumberInvalid):
		warningErrorCodes = append(warningErrorCodes, error_code.ErrorCode_CardReader_IK_Invalid_Expired)
	}

	b := patient_profile_common.PatientInfoBuilder{
		PatientInfo:  egkProfile,
		SdktCatalogs: []*catalog_sdkt_common.SdktCatalog{sdktCatalog},
	}
	egkProfile = b.UpdateSdktToInsurance(ctx).GetPatientInfo()
	if egkProfile == nil {
		return nil, errors.New("failed to update egkProfile")
	}

	result, err := patient_finder.
		NewPatientFinder(s.cardService.cardRawService).
		FindPatient(ctx, card_common.CardTypeTypeEGK, *egkProfile)
	if err != nil {
		return nil, err
	}

	err = s.cardService.onReadCard(ctx, &result.PatientCompareData, *cardRaw.Id)
	if err != nil {
		return nil, fmt.Errorf("failed to on read card exact match: %w", err)
	}

	patientsPrepared := patient_profile_common.PatientsPrepared(result.PatientCompareData.PatientsPrepared)
	result.PatientCompareData.PatientsPrepared = patientsPrepared.UpdateCardRawId(cardRaw.Id)
	result.PatientCompareData.PatientFromCard.CardRawId = cardRaw.Id
	result.WarningErrorCodes = warningErrorCodes
	result.IsInsuranceHasBeenAcquired = &isAcquiredCostUnit

	return result, nil
}
