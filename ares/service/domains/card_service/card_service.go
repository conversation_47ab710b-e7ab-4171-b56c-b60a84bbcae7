package card_service

import (
	"encoding/xml"
	"fmt"
	"strconv"

	"emperror.dev/errors"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	"github.com/thoas/go-funk"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/cardservice"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_profile"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"

	patient_profile_repo "git.tutum.dev/medi/tutum/ares/app/mvz/patient_profile"
	"git.tutum.dev/medi/tutum/ares/pkg/api_telematik_util"
	"git.tutum.dev/medi/tutum/ares/pkg/minio"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	"git.tutum.dev/medi/tutum/ares/pkg/validator"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/card_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/ti_connector_common"
	card_raw_common "git.tutum.dev/medi/tutum/ares/service/domains/card_raw/common"
	card_raw_service "git.tutum.dev/medi/tutum/ares/service/domains/card_raw/service"
	"git.tutum.dev/medi/tutum/ares/service/domains/card_raw/ti"
	catalog_sdkt_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_sdkt"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/card_raw"
	kv_region_master_data "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/edmp/edmp_kv_connect"
	"git.tutum.dev/medi/tutum/ares/service/domains/ti_service"
	"git.tutum.dev/medi/tutum/ares/service/domains/ti_service/companion_service_provider"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/CARD"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/CARDCMN"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/EVT"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/KVK"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/VSD"
	"git.tutum.dev/medi/tutum/pkg/slice"
	pkg_utils "git.tutum.dev/medi/tutum/pkg/util"
)

const FORMAT_DATE_COMPARE string = "02.01.2006"

var (
	supportCards []CARDCMN.CardTypeType = []CARDCMN.CardTypeType{
		CARDCMN.CardTypeTypeEgk,
		CARDCMN.CardTypeTypeKvk,
	}
	ErrorNotFoundPatientCard        error = errors.New(string(error_code.ErrorCode_CardReader_Not_Found_Patient_Card))
	ErrorNotFoundPracticeDoctorCard error = errors.New(string(error_code.ErrorCode_CardReader_Not_Found_Doctor_Card))
	validPNCodes                          = []int32{1, 2}
	inValidPNCodes                        = []int32{3, 4, 5, 6}
	StatusOK                              = "OK"
)

type GetCardFunc func(ctx *titan.Context, request cardservice.GetCardsRequest) (*GetCardResponse, error)

type CardService struct {
	minioClient           *minio.Minio
	socketNotifier        *cardservice.CardserviceSocketNotifier
	patientProfileService *patient_profile_repo.PatientProfileBffImpl
	cardRawService        *card_raw_service.CardRawService
	KvRegionMasterData    *kv_region_master_data.EdmpKvRepo
	catalogSdktService    *catalog_sdkt_service.CatalogSdktService
	cardReader            CardReader
}

var CardReaderMod = submodule.Make[CardReader](func(
	minioClient *minio.Minio,
	socketClient *socket_api.SocketServiceClient,
	cardRawService *card_raw_service.CardRawService,
	tiSettingService *ti_service.TiSettingService,
) CardReader {
	return &defaultCardReader{
		minioClient:      minioClient,
		socketNotifier:   cardservice.NewCardserviceSocketNotifier(socketClient),
		cardRawService:   cardRawService,
		tiSettingService: tiSettingService,
	}
}, minio.MinioMod, config.SocketServiceClientMod, card_raw_service.CardRawServiceMod, ti_service.TiSettingServiceMod)

var CardServiceMod = submodule.Make[*CardService](func(
	minioClient *minio.Minio,
	socketClient *socket_api.SocketServiceClient,
	patientProfileService *patient_profile_repo.PatientProfileBffImpl,
	cardRawService *card_raw_service.CardRawService,
	catalogSdktService *catalog_sdkt_service.CatalogSdktService,
	cardReader CardReader,
	kvRegionMasterData *kv_region_master_data.EdmpKvRepo,
) *CardService {
	return &CardService{
		minioClient:           minioClient,
		socketNotifier:        cardservice.NewCardserviceSocketNotifier(socketClient),
		KvRegionMasterData:    kvRegionMasterData,
		patientProfileService: patientProfileService,
		cardRawService:        cardRawService,
		catalogSdktService:    catalogSdktService,
		cardReader:            cardReader,
	}
},
	minio.MinioMod,
	config.SocketServiceClientMod,
	patient_profile_repo.PatientProfileServiceMod,
	card_raw_service.CardRawServiceMod,
	catalog_sdkt_service.CatalogSdktServiceMod,
	CardReaderMod,
	kv_region_master_data.EdmpKvRepoMod,
)

type validatePOIRequest struct {
	pOI             *getPreviousProofOfInsuranceResponse
	isOnlineCheck   bool
	onlineCheckMode ti_connector_common.ITConnectorOnlineCheck
}

type defaultCardReader struct {
	minioClient      *minio.Minio
	cardRawService   *card_raw_service.CardRawService
	socketNotifier   *cardservice.CardserviceSocketNotifier
	tiSettingService *ti_service.TiSettingService
}

func (*defaultCardReader) validatePOI(ctx *titan.Context, req validatePOIRequest) error {
	if req.isOnlineCheck || req.onlineCheckMode != ti_connector_common.OnlineCheck_ManualCheck {
		return nil
	}

	if req.pOI == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_CardReader_Confirm_PN_Code_No_Successful)
	}

	if funk.Contains(validPNCodes, req.pOI.proofOfInsurance.ResultCode) {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_CardReader_Confirm_PN_Code_Successful, fmt.Sprintf("%d", req.pOI.proofOfInsurance.ResultCode))
	}

	if funk.Contains(inValidPNCodes, req.pOI.proofOfInsurance.ResultCode) {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_CardReader_Confirm_PN_Code_Invalid, fmt.Sprintf("%d", req.pOI.proofOfInsurance.ResultCode))
	}

	return nil
}

type getPreviousProofOfInsuranceResponse struct {
	proofOfInsurance       patient_profile_common.ProofOfInsurance
	proofOfInsuranceXmlStr string
}

func (s *defaultCardReader) getPreviousProofOfInsurance(ctx *titan.Context, iccsn string) (*getPreviousProofOfInsuranceResponse, error) {
	cardRaw, err := s.cardRawService.GetValidCardRawTIByIccsn(ctx, iccsn)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get previous proof of insurance")
	}
	if cardRaw == nil || len(cardRaw.CardReaderPayload.Card.CardInfo.PatientId) == 0 {
		return nil, nil
	}

	isEGK := cardRaw.CardReaderPayload.Card.CardTypeTI == card_raw_common.CardTypeTI_EGK
	if !isEGK {
		return nil, nil
	}

	egk, _, err := card_raw_common.ParseRawStringToCompanionTI(cardRaw.CardReaderPayload.Payload, card_raw_common.CardTypeTI_EGK)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get previous proof of insurance")
	}
	if egk == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_CardReader_Proof_Of_Insurance_Invalid)
	}

	proofOfInsurance := egk.GetProofOfInsurance()

	proofOfInsuranceXmlStr, err := egk.GetProofOfInsuranceXmlStringEncoded()
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get proof of insurance")
	}

	if proofOfInsurance == nil || proofOfInsuranceXmlStr == nil {
		return nil, errors.New("proof of insurance invalid when get previous proof of insurance")
	}

	return &getPreviousProofOfInsuranceResponse{
		proofOfInsurance:       *egk.GetProofOfInsurance(),
		proofOfInsuranceXmlStr: *proofOfInsuranceXmlStr,
	}, nil
}

type readVsdRequest struct {
	vsdService                   *companion_service_provider.VsdService
	patientCardHandle            string
	doctorCardHandle             string
	pOI                          *getPreviousProofOfInsuranceResponse
	onlineCheck                  ti_connector_common.ITConnectorOnlineCheck
	isCardNotBeenReadThisQuarter *bool
	isCarryOutOnlineCheck        *bool
}

func (s *defaultCardReader) readVsd(ctx *titan.Context, req *readVsdRequest) (*VSD.ReadVsdresponse, error) {
	if req == nil {
		return nil, nil
	}

	var poICode *int32
	if req.pOI != nil {
		poICode = &req.pOI.proofOfInsurance.ResultCode
	}

	readOnlineReceipt := getReadOnlineReceipt(poICode, req.pOI)
	performOnlineCheck := getPerformOnlineCheck(poICode, req.onlineCheck)
	if req.isCarryOutOnlineCheck != nil {
		performOnlineCheck, readOnlineReceipt = *req.isCarryOutOnlineCheck, false
	}

	if performOnlineCheck {
		err := s.handleEventGetCards(ctx, cardservice.EventReadCard{
			IsPerformed:   true,
			CurrentUserId: pkg_utils.GetPointerValue(ctx.UserInfo().UserUUID()),
		})
		if err != nil {
			return nil, errors.WithMessage(err, "handle event get cards failed")
		}
	}

	deviceId := ctx.UserInfo().DeviceId
	tiSettings, err := s.tiSettingService.GetByDeviceId(ctx, deviceId)
	if err != nil {
		return nil, err
	}
	readVSDRequest := &VSD.ReadVsd{
		EhcHandle:          req.patientCardHandle,
		HpcHandle:          req.doctorCardHandle,
		PerformOnlineCheck: performOnlineCheck,
		ReadOnlineReceipt:  readOnlineReceipt,
		Context:            tiSettings.TiContext,
	}
	res, err := req.vsdService.ReadVSD(ctx, readVSDRequest)
	if err != nil {
		return nil, err
	}

	if res == nil {
		return nil, errors.New("no response from ti service")
	}

	if res.Pruefungsnachweis == "" {
		if req.pOI == nil {
			return nil, errors.New("no proof of insurance found")
		}

		res.Pruefungsnachweis = req.pOI.proofOfInsuranceXmlStr
	}

	return res, nil
}

type GetCardResponse struct {
	CardType card_common.CardTypeType
	CardInfo any
	Iccsn    string
}

type CardReader interface {
	GetCard(ctx *titan.Context, request cardservice.GetCardsRequest) (*GetCardResponse, error)
}

func (srv *defaultCardReader) GetCard(ctx *titan.Context, request cardservice.GetCardsRequest) (*GetCardResponse, error) {
	providers, err := srv.getCardsProviders(ctx)
	if err != nil {
		return nil, err
	}

	patientCardHolder := convertCardHolders(&providers.patientCard)
	doctorsCardHolder := convertCardHolders(&providers.doctorCard)
	if patientCardHolder == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_CardReader_Not_Found_Patient_Card)
	}
	if doctorsCardHolder == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_CardReader_Not_Found_Doctor_Card)
	}

	deviceId := ctx.UserInfo().DeviceId
	tiSettings, err := srv.tiSettingService.GetByDeviceId(ctx, deviceId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get config by deviceId")
	}
	tiServiceProvider, err := companion_service_provider.GetConnectorServiceProvider(ctx, tiSettings.TIConnector.ToTiInfo())
	if err != nil {
		return nil, err
	}

	switch patientCardHolder.CardType {
	case card_common.CardTypeTypeEGK:
		pOI, err := srv.getPreviousProofOfInsurance(ctx, string(*providers.patientCard.Iccsn))
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get previous proof of insurance")
		}

		onlineCheckMode := tiSettings.TIConnector.OnlineCheck
		isOnlineCheck := request.IsCarryOutOnlineCheck != nil || request.IsCardNotBeenReadThisQuarter != nil

		err = srv.validatePOI(ctx, validatePOIRequest{
			pOI:             pOI,
			isOnlineCheck:   isOnlineCheck,
			onlineCheckMode: onlineCheckMode,
		})
		if err != nil {
			return nil, err
		}

		vsdService := tiServiceProvider.GetVSDService()

		res, err := srv.readVsd(ctx, &readVsdRequest{
			vsdService:                   vsdService,
			patientCardHandle:            patientCardHolder.CardHandle,
			doctorCardHandle:             doctorsCardHolder.CardHandle,
			pOI:                          pOI,
			onlineCheck:                  onlineCheckMode,
			isCardNotBeenReadThisQuarter: request.IsCardNotBeenReadThisQuarter,
			isCarryOutOnlineCheck:        request.IsCarryOutOnlineCheck,
		})
		if err != nil {
			return nil, err
		}

		return &GetCardResponse{
			CardType: patientCardHolder.CardType,
			CardInfo: res,
			Iccsn:    string(*providers.patientCard.Iccsn),
		}, nil

	case card_common.CardTypeTypeKVK:
		kvkService := tiServiceProvider.GetKVKService()

		readKvkRequest := &KVK.ReadKvk{
			Kvkhandle: KVK.CardHandleType(patientCardHolder.CardHandle),
			Context:   tiSettings.TiContext,
		}
		res, err := kvkService.ReadKVK(ctx, readKvkRequest)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get kvk response")
		}

		return &GetCardResponse{
			CardType: patientCardHolder.CardType,
			CardInfo: res,
			Iccsn: func() string {
				if providers.patientCard.Iccsn == nil {
					return ""
				}

				return string(*providers.patientCard.Iccsn)
			}(),
		}, nil
	default:
		return nil, errors.New("not support card type")
	}
}

type getCardsProvidersResponse struct {
	patientCard CARD.CardInfoType
	doctorCard  CARD.CardInfoType
}

func (srv *defaultCardReader) getCardsProviders(ctx *titan.Context) (*getCardsProvidersResponse, error) {
	deviceId := ctx.UserInfo().DeviceId
	tiSettings, err := srv.tiSettingService.GetByDeviceId(ctx, deviceId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get config by deviceId")
	}
	tiServiceProvider, err := companion_service_provider.GetConnectorServiceProvider(ctx, tiSettings.TIConnector.ToTiInfo())
	if err != nil {
		return nil, err
	}

	eventService := tiServiceProvider.GetEventService()

	getCardsRequest := &EVT.GetCards{
		MandantWide: false,
		Context:     tiSettings.TiContext,
	}
	getCardsResponseRs, err := eventService.GetCards(ctx, getCardsRequest)
	if err != nil { // retry
		eventService = tiServiceProvider.GetEventService()

		getCardsResponseRs, err = eventService.GetCards(ctx, getCardsRequest)
		if err != nil {
			return nil, api_telematik_util.ParseTiError(ctx, err)
		}
	}

	if getCardsResponseRs.Status.Result != StatusOK {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_CardReader_Error,
			api_telematik_util.Error(getCardsResponseRs.Status.Error))
	}

	allCards := getCardsResponseRs.Cards.Card

	unknowCard := slice.FindOne(allCards, func(card CARD.CardInfoType) bool {
		return card_common.CardTypeType(card.CardType) == card_common.CardTypeTypeUNKNOWN
	})
	if unknowCard != nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_CardReader_UnknownType_Card, "Has an unknow card type")
	}

	patientCard := slice.FindOne(allCards, func(card CARD.CardInfoType) bool {
		return slice.Contains(supportCards, card.CardType)
	})
	if patientCard == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_CardReader_Not_Found_Patient_Card, ErrorNotFoundPatientCard.Error())
	}

	if patientCard.CardType != CARDCMN.CardTypeTypeKvk {
		doctorCard := slice.FindOne(allCards, func(card CARD.CardInfoType) bool {
			return card.CardType == CARDCMN.CardTypeTypeSmcB
		})
		// if smcb card is not found, try to find HBA or HBAX card
		if doctorCard == nil {
			doctorCard = slice.FindOne(allCards, func(card CARD.CardInfoType) bool {
				return card.CardType == CARDCMN.CardTypeTypeHba ||
					card.CardType == CARDCMN.CardTypeTypeHbax
			})
		}

		if doctorCard == nil {
			return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_CardReader_Not_Found_Doctor_Card, ErrorNotFoundPracticeDoctorCard.Error())
		}
		return &getCardsProvidersResponse{
			patientCard: *patientCard,
			doctorCard:  *doctorCard,
		}, nil
	}

	return &getCardsProvidersResponse{
		patientCard: *patientCard,
	}, nil
}

func (s *defaultCardReader) handleEventGetCards(ctx *titan.Context, request cardservice.EventReadCard) error {
	return s.socketNotifier.NotifyCareProviderReadCard(ctx, &request)
}

func convertCardHolders(card *CARD.CardInfoType) *cardservice.CardHolder {
	if card == nil {
		return nil
	}

	return &cardservice.CardHolder{
		CardHandle:     string(card.CardHandle),
		CardType:       card_common.CardTypeType(card.CardType),
		CardHolderName: card.CardHolderName,
		Kvnr:           card.Kvnr,
	}
}

func getReadOnlineReceipt(poICode *int32, pOI *getPreviousProofOfInsuranceResponse) bool {
	isValidProofOfInsurance := pOI != nil
	isValidPnCode := poICode != nil && funk.Contains(validPNCodes, *poICode)
	if isValidProofOfInsurance && isValidPnCode {
		return false
	}

	return true
}

func getPerformOnlineCheck(poICode *int32, connectorMode ti_connector_common.ITConnectorOnlineCheck) bool {
	if connectorMode == ti_connector_common.OnlineCheck_Never || (connectorMode == ti_connector_common.OnlineCheck_AtFirstInQuarter && poICode != nil && funk.Contains(validPNCodes, *poICode)) {
		return false
	}
	return true
}

type SaveCardRawDataRequest struct {
	EgkData     *api_telematik_util.EgkData
	KvkData     *api_telematik_util.KvkData
	ReadingDate int64
	Iccsn       string
}

func (s *CardService) saveCardRawData(ctx *titan.Context, request SaveCardRawDataRequest) (*card_raw.CardRawEntity[*card_raw_common.TiCardInfo], error) {
	patientFromCard, err := ti.ParseCompanionTIToPatientInfo(ctx, request.EgkData, request.KvkData)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to parse companion ti to patient info")
	}

	cardInsurance := patientFromCard.GetCardInsurance()
	if cardInsurance == nil {
		return nil, fmt.Errorf("no insurance from card %v", request)
	}

	cardTypeTI := card_raw_common.CardTypeTI_EGK
	payloadContent := ""
	if request.EgkData != nil {
		b, err := xml.Marshal(request.EgkData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		payloadContent = string(b)
	}
	if request.KvkData != nil {
		b, err := xml.Marshal(request.KvkData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		payloadContent = string(b)
		cardTypeTI = card_raw_common.CardTypeTI_KVK
	}

	entity := card_raw.CardRawEntity[*card_raw_common.TiCardInfo]{
		CardReaderPayload: card_raw.CardReaderPayload[*card_raw_common.TiCardInfo]{
			Card: &card_raw_common.TiCardInfo{
				CardInfo: card_raw_common.CardInfo{
					Patient: &card_raw_common.Patient{
						FirstName:   patientFromCard.PersonalInfo.FirstName,
						LastName:    patientFromCard.PersonalInfo.LastName,
						DateOfBirth: &patientFromCard.PersonalInfo.DateOfBirth,
						DOB:         patientFromCard.PersonalInfo.DOB,
					},
					InsuranceNumber: *cardInsurance.InsuranceNumber,
					ImportStatus:    card_raw_common.ImportStatus_RawData,
					ReadingDate:     request.ReadingDate,
					InsuranceName:   cardInsurance.InsuranceCompanyName,
					IKNumber:        cardInsurance.IkNumber,
				},
				CardTypeTI: cardTypeTI,
				Iccsn:      request.Iccsn,
			},
			Payload:     payloadContent,
			PayloadType: card_raw.PayloadType_XML,
		},
	}

	cardRaw, err := s.cardRawService.SaveTIRawData(ctx, entity)
	if err != nil {
		return nil, fmt.Errorf("save mobile card draw data failed %w", err)
	}

	return cardRaw, nil
}

func (s *CardService) getKvAreaByKvRegion(regionCode string) string {
	kvData := s.KvRegionMasterData.GetKVAreaByRegion(regionCode)
	var kvName string
	if kvData != nil {
		kvName = kvData.Label
	}

	return kvName
}

func (*CardService) getRegionCode(mandantId string) (*string, error) {
	regionCode := pkg_utils.GetOKVByUKV(mandantId[0:2])

	_, err := strconv.Atoi(regionCode)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get region code")
	}

	return &regionCode, nil
}

// use for update patient when read card successfully with status is exact match
func (s *CardService) onReadCard(ctx *titan.Context, patientCompareData *cardservice.PatientCompareData, cardRawId uuid.UUID) error {
	if patientCompareData == nil || patientCompareData.CompareResult != patient_profile_common.ExactMatch {
		return nil
	}

	if len(patientCompareData.PatientsPrepared) == 0 {
		return errors.WithMessage(errors.New("no patients prepared"), "failed to update read card model")
	}

	// cuz with exact match there is only one patient prepared
	patientInfo := patientCompareData.PatientsPrepared[0].PatientInfo
	if patientInfo.PatientId == nil {
		return errors.WithMessage(errors.New("no patient id"), "failed to update read card model")
	}

	/*
		With case exact match there is only one patient matched with
		With case exact match we need to check vsd time stamp is valid good to go
		if not, the system should be do nothing and return the nil, the error already mapped by patient finder
		if yes, update the read card model
	*/
	patientMatched := patientCompareData.PatientsMatched[0]
	err := validator.Run(
		ctx,
		patient_profile_common.NewPatientCardValidator(
			patientMatched,
			patientCompareData.PatientFromCard,
			s.cardRawService,
		),
		&patient_profile_common.PatientCardValidator{},
	)
	if err != nil {
		return nil
	}

	cardInsurance := patientInfo.GetCardInsurance()
	if cardInsurance == nil {
		return errors.WithMessage(errors.New("no card insurance"), "failed to update read card model")
	}

	/*
		in patient prepared we already prepared fully patient information
		the bottle neck is user cannot make any actions if card reading status exact match
		-> missing the cases hook to update patient profile

		ex: patient snap shot
	*/
	patientInfo.CardRawId = &cardRawId
	_, err = s.patientProfileService.UpdatePatientProfileV2(ctx, &patient_profile.UpdatePatientProfileV2Request{
		Id:            patientInfo.PatientId,
		PatientInfo:   &patientInfo,
		CardInsurance: cardInsurance,
	})
	if err != nil {
		return errors.WithMessage(err, "failed to update read card model")
	}

	return nil
}

type checkValidKVKSdktResponse struct {
	Message   string
	CardRawId uuid.UUID
}

func (*CardService) checkValidKVKSdkt(data *api_telematik_util.KvkPatient, activeIns *patient_profile_common.InsuranceInfo, cardRawId uuid.UUID) error {
	if data == nil || activeIns == nil || activeIns.InsuranceType == patient_profile_common.Private {
		return nil
	}

	vknr := activeIns.InsuranceCompanyId
	if len(vknr) != 5 {
		return nil
	}

	validVknr := vknr[len(vknr)-3:]
	vknrNo, err := strconv.Atoi(validVknr)
	if err != nil {
		return errors.WithMessage(err, "failed to convert vknr to int")
	}
	if vknrNo >= 800 {
		return nil
	}

	return pkg_errors.NewTitanCommonExceptionWithParams(
		error_code.ErrorCode_CardReader_SDKT_Vknr_Invalid,
		checkValidKVKSdktResponse{
			CardRawId: cardRawId,
			Message: fmt.Sprintf("Krankenkassen Name: %s; Krankenkassen Nummer: %s; VKNR: %s; WOP: %s; Versichertennummer: %s; Versicherungsstatus: %s; Besondere Personengruppe: %s; DMP Kennzeichnung: %s; Titel: %s; Vorname: %s; Vorsatzwort: %s; Nachname: %s; Geburtsdatum: %s; Wohnort: %s; Wohnsitzländer Code: %s; Gültigkeitsdatum: %s",
				data.HealthInsuranceName,
				data.HealthInsuranceNumber,
				*data.VKNR,
				*data.WOP,
				data.InsuranceNumber,
				data.InsuranceStatus,
				data.SpecialGroup,
				data.DMPLabeling,
				*data.Title,
				data.FirstName,
				*data.NameSuffix,
				data.LastName,
				data.DOB.Format(FORMAT_DATE_COMPARE),
				fmt.Sprintf("%s, %s, %s", data.Street, data.PostalCode, data.PlaceName),
				data.CountryCode,
				data.ValidityDate.Format(FORMAT_DATE_COMPARE),
			),
		},
	)
}

type cardGetter interface {
	Get(ctx *titan.Context) (*cardservice.GetCardsResponse, error)
}

func (s *CardService) GetCards(ctx *titan.Context, req cardservice.GetCardsRequest) (*cardservice.GetCardsResponse, error) {
	cardResponse, err := s.cardReader.GetCard(ctx, req)
	if err != nil {
		return nil, err
	}
	if cardResponse == nil {
		return nil, errors.New("get card response is empty")
	}

	var getter cardGetter
	switch cardResponse.CardType {
	case card_common.CardTypeTypeEGK:
		getter = &EgkGetter{
			cardService:  s,
			cardResponse: cardResponse,
			mandantId:    req.MandantId,
		}
	case card_common.CardTypeTypeKVK:
		getter = &KvkGetter{
			cardService:  s,
			cardResponse: cardResponse,
			mandantId:    req.MandantId,
		}
	default:
		return nil, errors.New("not support card type")
	}

	return getter.Get(ctx)
}
