package service

import (
	"bytes"
	"encoding/base64"
	"encoding/xml"
	"errors"
	"fmt"
	"os"
	"strings"

	billing_edoku_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/billing_edoku"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/edoku"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/mail"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/pkg/edmp_testmodule"
	"git.tutum.dev/medi/tutum/ares/pkg/minio"
	"git.tutum.dev/medi/tutum/ares/pkg/xkm"
	"git.tutum.dev/medi/tutum/ares/pkg/zip_file"
	"git.tutum.dev/medi/tutum/ares/service/billing_history/billing_history_common"
	"git.tutum.dev/medi/tutum/ares/service/bsnr"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	billing_edoku_common "git.tutum.dev/medi/tutum/ares/service/domains/billing_edoku/common"
	edmp_common "git.tutum.dev/medi/tutum/ares/service/domains/edmp/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/schema_model"
	edmp_service "git.tutum.dev/medi/tutum/ares/service/domains/edmp/service"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/transfer_letter"
	okv_util "git.tutum.dev/medi/tutum/ares/service/domains/pkg/okv"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mail/mail_setting_repo"
	sdda_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/sdda"
	billing_history_edoku_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/billing_history_edoku"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/edmp/edmp_document"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/edmp/edmp_kv_connect"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/edmp/edoku_document"
	scheinRepo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	employee_profile_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/sdkvca"
	mail_service "git.tutum.dev/medi/tutum/ares/service/mail"
	mail_common "git.tutum.dev/medi/tutum/ares/service/mail/common"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"
)

const (
	HKS_DocumentType              = "eHKS"
	HKS_Subject_Billing           = "eHKS-Einsendung"
	HKS_SenderSystem              = "test;eHKS1"
	HKS_ServiceIdentifier         = "eHKS;Einsendung;V1.0"
	HKS_ReceiptIdentifier         = "eHKS;Quittung;V1.0"
	HKS_ReceiptSubject            = "eHKS-Quittung"
	HKS_XmlFile_Description       = "eHKS-Begleitdatei"
	HKS_CompanionFile_Description = "eHKS-Archiv"
)

type (
	BillingEDokuService struct {
		billingHistoryEdokuRepo    *billing_history_edoku_repo.BillingHistoryEDokuRepo
		bsnrService                *bsnr.BSNRService
		minioClient                *minio.Minio
		mailService                *mail_service.MailService
		mailSettingRepo            *mail_setting_repo.MailSettingEntityRepo
		getSdkvcaFunc              sdkvca.GetSdkvcaDataFunc
		edokuDocumentRepo          *edoku_document.EdokuEnrollmentDocumentRepo
		patientProfileRepo         patient.PatientProfileDefaultRepository
		scheinRepo                 scheinRepo.ScheinRepoDefaultRepository
		edokuService               *edmp_service.EDokuService
		bucketDmpBilling           string
		html2PdfPath               string
		sddaRepo                   *sdda_repo.SddaRepo
		billingEDokuSocketNotifier *billing_edoku_api.BillingEdokuSocketNotifier
		edmpKvRepo                 *edmp_kv_connect.EdmpKvRepo
		employeeProfileRepo        employee_profile_repo.EmployeeProfileDefaultRepository
		requestWithFileContentFunc xkm.RequestWithFileContentFunc
	}
	GetDispatchListRequest struct {
		Query *string
	}
	SendMailRequest struct {
		BillingHistoryId    *uuid.UUID
		SenderMailSettingId *uuid.UUID
		TestToMail          *string
	}
	GetEdokuBillingSelectionResponse struct {
		Quarters      []*common.YearQuarter
		Bsnrs         []*billing_history_common.BSNR
		DocumentTypes []string
	}
	GetValidationListRequest struct {
		Quarter             *common.YearQuarter
		BsnrId              *uuid.UUID
		DocumentType        string
		OpenPreviousQuarter bool
	}
	GetValidationListResponse struct {
		BillingValidationList []*billing_edoku_common.BillingValidationListModel
		TotalPatient          int
	}
	UndoSubmissionRequest struct {
		BillingHistoryId *uuid.UUID
	}

	PrepareForShippingRequest struct {
		BillingHistoryId *uuid.UUID
	}
	CreateBillingRequest struct {
		DocumentIds           []uuid.UUID
		Quarter               *common.YearQuarter
		Bsnr                  string
		DMPValue              edmp_common.DMPValueEnum
		IsOpenPreviousQuarter bool
	}
	CreateBillingResponse struct {
		Status                            bool
		DMPBillingHistoryId               *uuid.UUID
		DMPBillingFiles                   []*edmp_common.DMPBillingFile
		DMPBillingFieldsValidationResults []*edmp_common.DMPBillingFieldsValidationResult
		TransferLetters                   []*edmp_common.DMPBillingFile
	}

	CheckForValidationRequest struct {
		DocumentIds []uuid.UUID
		Quarter     *common.YearQuarter
		Bsnr        string
	}
	CheckForValidationMultipleDocumentsResponse struct {
		Status                            bool
		DMPBillingFieldsValidationResults []*edmp_common.DMPBillingFieldsValidationResult
		XPMStatisticsFiles                []edmp_common.DMPBillingFile
		ZipFile                           edmp_common.DMPBillingFile
	}

	CreateAndUploadCompanionAndZipForDocumentsRequest struct {
		documents []*edmp_document.EdmpEnrollmentDocumentEntity
		quarter   common.YearQuarter
		bsnr      string
		version   int
		dmpValue  edmp_common.DMPValueEnum
	}
	CreateAndUploadTransferLettersRequest struct {
		documents    []*edmp_document.EdmpEnrollmentDocumentEntity
		quarter      common.YearQuarter
		bsnr         string
		dmpValue     edmp_common.DMPValueEnum
		billingFiles []edmp_common.DMPBillingFile
	}
	GetBillingHistoryRequest struct {
		BillingHistoryId *uuid.UUID
	}
	GetBillingHistoryResponse struct {
		BillingHistory *billing_edoku_common.BillingHistoryModel
	}
	EncryptBillingZipFileRequest struct {
		BillingHistoryId *uuid.UUID
		ZipFileUrl       string
	}
)

func newBillingEDokuService(billingHistoryEdokuRepo *billing_history_edoku_repo.BillingHistoryEDokuRepo,
	bsnrService *bsnr.BSNRService,
	minioClient *minio.Minio,
	mailService *mail_service.MailService,
	mailSettingRepo *mail_setting_repo.MailSettingEntityRepo,
	getSdkvcaFunc sdkvca.GetSdkvcaDataFunc,
	patientProfileRepo patient.PatientProfileDefaultRepository,
	edokuService *edmp_service.EDokuService,
	sddaRepo *sdda_repo.SddaRepo,
	socketClient *socket_api.SocketServiceClient,
	edmpKvRepo *edmp_kv_connect.EdmpKvRepo,
	employeeProfileRepo employee_profile_repo.EmployeeProfileDefaultRepository,
	requestWithFileContentFunc xkm.RequestWithFileContentFunc,
	appConfig *config.MvzAppConfigs,
) *BillingEDokuService {
	billingEDokuService := &BillingEDokuService{
		billingHistoryEdokuRepo:    billingHistoryEdokuRepo,
		bsnrService:                bsnrService,
		minioClient:                minioClient,
		mailService:                mailService,
		mailSettingRepo:            mailSettingRepo,
		getSdkvcaFunc:              getSdkvcaFunc,
		patientProfileRepo:         patientProfileRepo,
		edokuDocumentRepo:          edoku_document.NewEdokuEnrollmentDocumentRepo(),
		scheinRepo:                 scheinRepo.NewScheinRepoDefaultRepository(),
		edokuService:               edokuService,
		bucketDmpBilling:           appConfig.MinioClientConfig.BucketDmpBilling,
		html2PdfPath:               appConfig.Html2PdfPath,
		sddaRepo:                   sddaRepo,
		billingEDokuSocketNotifier: billing_edoku_api.NewBillingEdokuSocketNotifier(socketClient),
		edmpKvRepo:                 edmpKvRepo,
		employeeProfileRepo:        employeeProfileRepo,
		requestWithFileContentFunc: requestWithFileContentFunc,
	}
	mailService.RegisterOnCreateFunc(billingEDokuService.handleReceiptMail)
	return billingEDokuService
}

var BillingEDokuServiceMod = submodule.Make[*BillingEDokuService](newBillingEDokuService,
	billing_history_edoku_repo.BillingHistoryEDokuRepoMod,
	bsnr.BSNRServiceMod,
	minio.MinioMod,
	mail_service.MailKimServiceMod,
	mail_setting_repo.MailSettingRepoMod,
	sdkvca.GetSdkvcaDataFuncMod,
	patient.PatientProfileDefaultRepositoryMod,
	edmp_service.EDokuServiceMod,
	sdda_repo.SDDARepoMod,
	config.SocketServiceClientMod,
	edmp_kv_connect.EdmpKvRepoMod,
	employee_profile_repo.EmployeeProfileRepoMod,
	xkm.XKMMod,
	config.MvzAppConfigMod,
)

func (s *BillingEDokuService) GetEdokuBillingSelection(ctx *titan.Context) (*GetEdokuBillingSelectionResponse, error) {
	bsnrs, err := s.bsnrService.GetListBSNR(ctx)
	if err != nil {
		return nil, err
	}

	bsnrList := make([]*billing_history_common.BSNR, len(bsnrs))
	for i, bsnr := range bsnrs {
		bsnrList[i] = &billing_history_common.BSNR{
			Id:   bsnr.Id,
			Name: bsnr.Name,
			Code: bsnr.Code,
		}
	}

	quarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	yearQuarters := quarter.GetFromLastYearQuarter(5)
	quarters := make([]*common.YearQuarter, len(yearQuarters))
	for i, yearQuarter := range yearQuarters {
		quarters[i] = &common.YearQuarter{
			Quarter: yearQuarter.Quarter,
			Year:    yearQuarter.Year,
		}
	}

	return &GetEdokuBillingSelectionResponse{
		Quarters:      quarters,
		Bsnrs:         bsnrList,
		DocumentTypes: []string{HKS_DocumentType},
	}, nil
}

func (srv *BillingEDokuService) GetDispatchList(ctx *titan.Context, req *GetDispatchListRequest) ([]*billing_edoku_common.BillingHistoryModel, error) {
	res, err := srv.billingHistoryEdokuRepo.GetDispatchList(ctx, &billing_history_edoku_repo.GetDispatchListRequest{
		Query: req.Query,
	})
	if err != nil {
		return nil, err
	}

	if len(res) == 0 {
		return nil, nil
	}

	results := make([]*billing_edoku_common.BillingHistoryModel, len(res))
	for i, item := range res {
		if item == nil || item.BillingHistoryModel == nil {
			continue
		}

		item.BillingHistoryModel.Id = item.GetId()
		results[i] = item.BillingHistoryModel
	}

	return results, nil
}

func (srv *BillingEDokuService) UpdateEdokuDocumentStatusByBillingHistoryId(ctx *titan.Context, billingHistoryId uuid.UUID, status edmp_common.DocumentStatus) error {
	billingHistoryEntity, err := srv.billingHistoryEdokuRepo.GetById(ctx, billingHistoryId)
	if err != nil {
		return err
	}
	if billingHistoryEntity == nil || billingHistoryEntity.BillingHistoryModel == nil {
		return errors.New("billing history not found")
	}
	if err := srv.edokuService.UpdateStatusEdokuDocumentByIds(ctx, edoku.UpdateStatusEdokuDocumentByIdsRequest{
		DocumentIds: billingHistoryEntity.EdokuDocumentIds,
		Status:      status,
	}); err != nil {
		return err
	}
	return nil
}

func (srv *BillingEDokuService) HandlePrepareForShipping(ctx *titan.Context, req *PrepareForShippingRequest) error {
	if req.BillingHistoryId == nil {
		return errors.New("billing history id is required")
	}
	if err := srv.UpdateEdokuDocumentStatusByBillingHistoryId(ctx, *req.BillingHistoryId, edmp_common.DocumentStatus_Billed); err != nil {
		return err
	}
	return nil
}

func (srv *BillingEDokuService) HandleSendMail(ctx *titan.Context, req *SendMailRequest) error {
	if req.BillingHistoryId == nil {
		return errors.New("billing history id is required")
	}

	billingHistoryId := req.BillingHistoryId

	if err := srv.updateStatusById(ctx, billingHistoryId, billing_edoku_common.BillingStatus_Pending); err != nil {
		return err
	}

	userId := ctx.UserInfo().UserUUID()
	if userId == nil {
		return errors.New("user not found")
	}

	employee, err := srv.employeeProfileRepo.GetById(ctx, *userId)
	if err != nil {
		return err
	}

	if employee == nil {
		return errors.New("employee not found")
	}

	if _, err := srv.billingHistoryEdokuRepo.UpdateSubmittedUser(ctx, &billing_history_edoku_repo.UpdateSubmittedUserRequest{
		BillingHistoryId: billingHistoryId,
		SubmittedUser: &billing_history_common.SubmittedUser{
			EmployeeId: *employee.Id,
			FirstName:  employee.FirstName,
			LastName:   employee.LastName,
		},
	}); err != nil {
		return err
	}

	go func() {
		ctx := ctx.Clone()
		if err := srv.sendMail(ctx, req); err != nil {
			if err := srv.updateStatusById(ctx, billingHistoryId, billing_edoku_common.BillingStatus_Failed); err != nil {
				ctx.Logger().Error("failed to update status to failed", "error", err)
			}
			return
		}

		if err := srv.updateStatusById(ctx, billingHistoryId, billing_edoku_common.BillingStatus_Submitted); err != nil {
			ctx.Logger().Error("failed to update status to submitted", "error", err)
		}
		if err := srv.UpdateEdokuDocumentStatusByBillingHistoryId(ctx, *billingHistoryId, edmp_common.DocumentStatus_Billed); err != nil {
			ctx.Logger().Error("failed to update status of documents", "error", err)
		}
	}()
	return nil
}

func (srv *BillingEDokuService) sendMail(ctx *titan.Context, req *SendMailRequest) error {
	if req.BillingHistoryId == nil {
		return errors.New("billing history id is required")
	}

	billingHistoryId := req.BillingHistoryId
	res, err := srv.billingHistoryEdokuRepo.GetById(ctx, *billingHistoryId)
	if err != nil {
		return err
	}

	if res == nil || res.BillingHistoryModel == nil {
		return errors.New("billing history not found")
	}

	if res.CompanionFileUrl == nil || res.ZipFileUrl == nil {
		return errors.New("companion or zip file not found")
	}

	mailSetting, err := srv.mailSettingRepo.FindByIdAndBsnr(ctx, *req.SenderMailSettingId, res.Bsnr.Code)
	if err != nil {
		return err
	}

	if mailSetting == nil {
		return errors.New("mail setting not found")
	}

	sdkvcaData, err := srv.getSdkvcaFunc(ctx)
	if err != nil {
		return err
	}

	hksMail, err := sdkvcaData.GetHksMail(res.Bsnr.Code)
	if err != nil {
		return err
	}

	toAddress := mail_common.Address{
		Name:    "",
		Address: hksMail.KimEmail,
	}
	isTesting := util.GetPointerValue(res.TypeOfBilling) == billing_history_common.TypeOfBilling_Send_As_Real_Test_Billing
	if isTesting {
		toAddress.Address = hksMail.KimTestEmail
	}

	companionFileUrl := util.GetPointerValue(res.CompanionFileUrl)
	zipFileUrl := util.GetPointerValue(res.ZipFileUrl)
	companionObj, err := minio.ExtractBucketAndKey(companionFileUrl)
	if err != nil {
		return err
	}

	if companionObj == nil {
		return errors.New("companion file not found")
	}

	zipObj, err := minio.ExtractBucketAndKey(zipFileUrl)
	if err != nil {
		return err
	}

	if zipObj == nil {
		return errors.New("zip file not found")
	}

	companionFileName := companionObj.Key
	mailAccount := mailSetting.AccountInfor
	mailRes, err := srv.mailService.SendMail(ctx, mail.SendMailRequest{
		From: mail_common.Address{
			Name:    mailAccount.Email,
			Address: mailAccount.Email,
		},
		To: []mail_common.Address{
			toAddress,
		},
		Subject: HKS_Subject_Billing,
		ExtraHeaders: map[string]string{
			string(mail_common.MailHeaderKey_Sendersystem): HKS_SenderSystem,
		},
		InReplyTo: []string{
			mailAccount.Email,
		},
		Category: HKS_ServiceIdentifier,
		Attachments: []mail_common.Attachment{
			{
				Name: companionFileName,
				Url:  companionFileUrl,
				Headers: map[string]string{
					string(mail_service.MailHeader_ContentType):             fmt.Sprintf("%s; name=%s", string(mail_common.AttachmentContentType_Xml), companionFileName),
					string(mail_service.MailHeader_ContentTransferEncoding): string(mail_common.ContentTransferEncoding_Base64),
					string(mail_service.MailHeader_ContentDisposition):      fmt.Sprintf("attachment; filename=%s", companionFileName),
					string(mail_service.MailHeader_ContentDescription):      HKS_XmlFile_Description,
				},
			},
			{
				Name: zipObj.Key,
				Url:  zipFileUrl,
				Headers: map[string]string{
					string(mail_service.MailHeader_ContentType):             fmt.Sprintf("%s; name=%s", string(mail_common.AttachmentContentType_Zip), zipObj.Key),
					string(mail_service.MailHeader_ContentTransferEncoding): string(mail_common.ContentTransferEncoding_Base64),
					string(mail_service.MailHeader_ContentDisposition):      fmt.Sprintf("attachment; filename=%s", zipObj.Key),
					string(mail_service.MailHeader_ContentDescription):      HKS_CompanionFile_Description,
				},
			},
		},
	})
	if err != nil {
		return err
	}

	if mailRes == nil {
		return errors.New("failed to send mail")
	}

	if _, err := srv.billingHistoryEdokuRepo.UpdateMailMessageId(ctx, &billing_history_edoku_repo.UpdateMailMessageIdRequest{
		Id:            billingHistoryId,
		MailMessageId: &mailRes.EmailItem.MessageID,
	}); err != nil {
		return err
	}

	if _, err := srv.billingHistoryEdokuRepo.UpdateMailSendId(ctx, &billing_history_edoku_repo.UpdateMailSendIdRequest{
		BillingHistoryId: billingHistoryId,
		MailSendId:       &mailRes.MailId,
	}); err != nil {
		return err
	}

	return nil
}

func (srv *BillingEDokuService) GetValidationList(ctx *titan.Context, request *GetValidationListRequest) (*GetValidationListResponse, error) {
	if request.BsnrId == nil || request.Quarter == nil {
		return nil, errors.New("missing required parameters: BsnrId or Quarter")
	}

	entities, err := srv.edokuDocumentRepo.GetEdokuDocumentForValidationList(ctx, util.GetPointerValue(request.BsnrId), util.GetPointerValue(request.Quarter), request.OpenPreviousQuarter)
	if err != nil {
		return nil, err
	}

	if len(entities) == 0 {
		return nil, nil
	}

	// Get and map patient data
	mapPatientById, err := srv.getPatientMap(ctx, entities)
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve patient data: %w", err)
	}
	mapInsuranceIdByDocumentId, err := srv.getMapInsuranceId(ctx, entities)
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve insurance id: %w", err)
	}

	validationList := make([]*billing_edoku_common.BillingValidationListModel, 0)
	for _, entity := range entities {
		if entity.Id == nil || entity.PatientId == nil {
			continue
		}
		validation := billing_edoku_common.BillingValidationListModel{
			DocumentId: util.GetPointerValue(entity.Id),
			CaseNumber: entity.DMPCaseNumber,
			Status:     util.GetPointerValue(entity.DocumentStatus),
			Type:       entity.DocumentType,
		}

		patient, exists := mapPatientById[util.GetPointerValue(entity.PatientId)]
		if exists {
			if patient.PatientInfo != nil {
				activeInsurance := patient.PatientInfo.GetActiveInsurance()
				validation.Patient = edmp_common.Patient{
					PatientId:       util.GetPointerValue(patient.Id),
					FirstName:       patient.FirstName,
					LastName:        patient.LastName,
					DateOfBirth:     patient.PatientInfo.PersonalInfo.DateOfBirth,
					FullName:        util.GetPatientName(patient),
					ActiveInsurance: activeInsurance,
				}
				insuranceId, isExist := mapInsuranceIdByDocumentId[*entity.Id]
				if isExist {
					insurance := patient.PatientInfo.GetInsurance(insuranceId)
					if insurance != nil {
						validation.InsuranceName = insurance.InsuranceCompanyName
					}
				}
			}
		}
		validationList = append(validationList, &validation)
	}

	return &GetValidationListResponse{
		BillingValidationList: validationList,
		TotalPatient:          len(mapPatientById),
	}, nil
}

func (srv *BillingEDokuService) getPatientMap(ctx *titan.Context, entities []*edmp_document.EdmpEnrollmentDocumentEntity) (map[uuid.UUID]patient.PatientProfile, error) {
	patientIds := make([]uuid.UUID, 0)
	for _, entity := range entities {
		if entity.PatientId != nil {
			patientIds = append(patientIds, *entity.PatientId)
		}
	}

	patients, err := srv.patientProfileRepo.GetByIds(ctx, patientIds)
	if err != nil {
		return nil, err
	}

	patientMap := make(map[uuid.UUID]patient.PatientProfile, len(patients))
	for _, patient := range patients {
		if patient != nil && patient.Id != nil {
			patientMap[*patient.Id] = *patient
		}
	}

	return patientMap, nil
}

func (srv *BillingEDokuService) getMapInsuranceId(ctx *titan.Context, entities []*edmp_document.EdmpEnrollmentDocumentEntity) (map[uuid.UUID]uuid.UUID, error) {
	scheinIds := make([]uuid.UUID, 0)
	for _, entity := range entities {
		if entity != nil {
			scheinIds = append(scheinIds, entity.ScheinId)
		}
	}
	scheins, err := srv.scheinRepo.GetByIds(ctx, scheinIds)
	if err != nil {
		return nil, err
	}
	if len(scheins) == 0 {
		return nil, nil
	}
	mapInsuranceIdBySchein := make(map[uuid.UUID]uuid.UUID, len(scheins))
	for _, schein := range scheins {
		mapInsuranceIdBySchein[*schein.Id] = schein.Schein.InsuranceId
	}

	mapInsuranceIdByEdoku := make(map[uuid.UUID]uuid.UUID, len(entities))
	for _, entity := range entities {
		if entity != nil {
			mapInsuranceIdByEdoku[*entity.Id] = mapInsuranceIdBySchein[entity.ScheinId]
		}
	}

	return mapInsuranceIdByEdoku, nil
}

func (srv *BillingEDokuService) GetEdokuDocumentationByIds(ctx *titan.Context, documentIds []uuid.UUID) ([]*edmp_common.DocumentationOverview, error) {
	if len(documentIds) == 0 {
		return nil, errors.New("document ids are required")
	}

	documents, err := srv.edokuDocumentRepo.GetByIds(ctx, documentIds)
	if err != nil {
		return nil, err
	}
	if len(documents) == 0 {
		return nil, errors.New("documents not found")
	}
	var result []*edmp_common.DocumentationOverview
	for _, document := range documents {
		if document == nil || document.Id == nil {
			continue
		}
		result = append(result, &document.DocumentationOverview)
	}
	return result, nil
}

func (srv *BillingEDokuService) UndoSubmission(ctx *titan.Context, request *UndoSubmissionRequest) error {
	if request.BillingHistoryId == nil {
		return errors.New("billing history id is required")
	}

	res, err := srv.billingHistoryEdokuRepo.GetBillingForUndoSubmission(ctx, &billing_history_edoku_repo.GetBillingForUndoSubmissionRequest{
		BillingHistoryId: request.BillingHistoryId,
	})
	if err != nil {
		return err
	}

	if res == nil {
		return errors.New("billing history not found for undo submission")
	}

	_, err = srv.billingHistoryEdokuRepo.UpdateStatus(ctx, &billing_history_edoku_repo.UpdateStatusRequest{
		BillingHistoryId: request.BillingHistoryId,
		Status:           billing_edoku_common.BillingStatus_Cancelled,
	})
	if err != nil {
		return err
	}
	if err := srv.billingEDokuSocketNotifier.NotifyCareProviderBillingEDokuStatusChanged(ctx, &billing_edoku_api.EventBillingEDokuStatusChanged{
		BillingHistoryId: request.BillingHistoryId,
		Status:           billing_edoku_common.BillingStatus_Cancelled,
	}); err != nil {
		return err
	}
	return nil
}

func (srv *BillingEDokuService) CheckForValidation(ctx *titan.Context, request *CheckForValidationRequest) (*CheckForValidationMultipleDocumentsResponse, error) {
	if request.Quarter == nil {
		return nil, errors.New("quarter is required")
	}
	if len(request.DocumentIds) == 0 {
		return nil, errors.New("document ids are required")
	}

	documents, err := srv.edokuDocumentRepo.GetByIds(ctx, request.DocumentIds)
	if err != nil {
		return nil, err
	}
	if len(documents) == 0 {
		return nil, errors.New("documents not found")
	}

	var (
		billingFieldsValidationResults = make([]*edmp_common.DMPBillingFieldsValidationResult, 0)
		// BillingFiles                   []*edmp_common.DMPBillingFile
		// TransferLetters                []*edmp_common.DMPBillingFile
	)

	// create temp dir
	tempDir, err := os.MkdirTemp("./", "dmpBilling-")
	if err != nil {
		return nil, err
	}

	dmpVersion := fmt.Sprintf("%v.%v.0", request.Quarter.Year, request.Quarter.Quarter)
	if request.Quarter.Year < 2025 {
		dmpVersion = "2025.1.0"
	}

	dirPath := tempDir + "/" + edmp_common.GetDMPPathByDMPValue(string(edmp_common.DMPValueEnum_EDO_SkinCancer))
	err = os.MkdirAll(dirPath, 0777)
	if err != nil {
		return nil, err
	}
	defer os.RemoveAll(tempDir)
	documentsByDmpBillingFileName := make(map[string]*edmp_document.EdmpEnrollmentDocumentEntity)
	documentTypeMap := make(map[edmp_common.DocumentType]edmp_common.DocumentType)

	for _, document := range documents {
		if document == nil || document.Id == nil {
			continue
		}
		documentType := document.DocumentType
		documentTypeMap[documentType] = documentType
		if documentType == edmp_common.DocumentType_EHKS_D && util.GetPointerValue(document.AdditionalContracts) == edmp_common.AdditionalContractsEnum_Yes {
			documentType = edmp_common.DocumentType_EHKS_D_EV
		}
		if documentType == edmp_common.DocumentType_EHKS_ND && util.GetPointerValue(document.AdditionalContracts) == edmp_common.AdditionalContractsEnum_Yes {
			documentType = edmp_common.DocumentType_EHKS_ND_EV
		}
		fileByte, err := srv.minioClient.DownloadFile(ctx, srv.bucketDmpBilling, document.DMPBillingFile.FileName, minio.GetObjectOptions{})
		if err != nil {
			return nil, fmt.Errorf("download file failed: %w", err)
		}
		fileName, err := edmp_service.GenBillingFileName(
			request.Bsnr,
			document.DMPCaseNumber,
			document.DMPLabelingValue,
			documentType,
			util.ConvertMillisecondsToTime(document.CreatedAt.UnixMilli()),
			false,
		)
		if err != nil {
			return nil, err
		}
		documentsByDmpBillingFileName[fileName] = document
		err = os.WriteFile(fmt.Sprintf("%s/%s", dirPath, fileName), fileByte, 0644)
		if err != nil {
			return nil, err
		}
	}

	xkmAbbreviation := edmp_common.GetXKMAbbreviation(edmp_common.DMPValueEnum_EDO_SkinCancer, util.YearQuarter{
		Year:    request.Quarter.Year,
		Quarter: request.Quarter.Quarter,
	})
	zipFileName := fmt.Sprintf("%v.zip", edmp_service.GenFileName(ctx, request.Bsnr, 1, xkmAbbreviation))
	err = zip_file.ZipFolder(tempDir, zipFileName)
	defer os.RemoveAll(zipFileName)

	if err != nil {
		return nil, err
	}

	zipDecodeBytes, err := os.ReadFile(zipFileName)
	if err != nil {
		return nil, err
	}

	zipFileContentStringReader := bytes.NewReader(zipDecodeBytes)
	_, err = srv.minioClient.PutObject(ctx, srv.bucketDmpBilling, zipFileName, zipFileContentStringReader, zipFileContentStringReader.Size(), minio.PutObjectOptions{})
	if err != nil {
		return nil, err
	}

	xpmFileType := edmp_testmodule.FieldType_PDF

	checkPlausibilityMultipleDocumentsRes, err := srv.edokuService.CheckPlausibilityMultipleDocuments(ctx, &edmp_service.CheckPlausibilityMultipleDocumentsRequest{
		ZipFileData:      zipDecodeBytes,
		ZipFileName:      zipFileName,
		DMPVersion:       dmpVersion,
		DMPLabelingValue: string(edmp_common.DMPValueEnum_EDO_SkinCancer),
		XPMFileType:      xpmFileType,
		DocumentType:     documents[0].DocumentType,
		DocumentTypeMap:  documentTypeMap,
	})
	if err != nil {
		return nil, err
	}

	fieldsValidationGroupedByBillingFileName := slice.GroupBy(checkPlausibilityMultipleDocumentsRes.FieldValidationResults, func(field edmp_common.FieldValidationResult) string {
		return field.BillingFileName
	})

	billingFileNames := slice.Keys(fieldsValidationGroupedByBillingFileName)

	for _, billingFileName := range billingFileNames {
		document := documentsByDmpBillingFileName[billingFileName]
		if document == nil {
			continue
		}
		documentId := document.DocumentationOverviewId
		billingFieldsValidationResults = append(billingFieldsValidationResults, &edmp_common.DMPBillingFieldsValidationResult{
			DocumentId:             *documentId,
			FieldValidationResults: fieldsValidationGroupedByBillingFileName[billingFileName],
		})
	}

	return &CheckForValidationMultipleDocumentsResponse{
		Status:                            checkPlausibilityMultipleDocumentsRes.IsPlausible,
		DMPBillingFieldsValidationResults: billingFieldsValidationResults,
		XPMStatisticsFiles:                checkPlausibilityMultipleDocumentsRes.XPMStatisticFiles,
		ZipFile: edmp_common.DMPBillingFile{
			FileName: zipFileName,
			FilePath: fmt.Sprintf("%s/%s", srv.bucketDmpBilling, zipFileName),
			FileType: edmp_common.DMPBillingHistoryFileType_Billing,
		},
	}, nil
}

func (srv *BillingEDokuService) CreateBilling(ctx *titan.Context, request *CreateBillingRequest) (*CreateBillingResponse, error) {
	res, err := srv.CheckForValidation(ctx, &CheckForValidationRequest{
		DocumentIds: request.DocumentIds,
		Quarter:     request.Quarter,
		Bsnr:        request.Bsnr,
	})
	if err != nil {
		return nil, err
	}

	if !res.Status {
		return &CreateBillingResponse{
			Status:                            false,
			DMPBillingFieldsValidationResults: res.DMPBillingFieldsValidationResults,
		}, nil
	}

	xpmResultFiles := res.XPMStatisticsFiles
	zipFile := res.ZipFile
	documents, err := srv.edokuDocumentRepo.GetByIds(ctx, request.DocumentIds)
	if err != nil {
		return nil, err
	}
	if len(documents) == 0 {
		return nil, errors.New("documents not found")
	}

	// get billing files
	billingFiles, err := srv.createAndUploadCompanionAndZipForDocuments(ctx, CreateAndUploadCompanionAndZipForDocumentsRequest{
		documents: documents,
		quarter:   *request.Quarter,
		bsnr:      request.Bsnr,
		version:   1,
		dmpValue:  request.DMPValue,
	})
	if err != nil {
		return nil, err
	}

	if len(billingFiles) == 0 {
		return nil, errors.New("billing files not found")
	}
	mergedBillingFiles := append(billingFiles, zipFile)

	// transfer letters
	transferLetters, err := srv.createAndUploadTransferLetters(ctx, CreateAndUploadTransferLettersRequest{
		documents:    documents,
		quarter:      *request.Quarter,
		bsnr:         request.Bsnr,
		dmpValue:     request.DMPValue,
		billingFiles: mergedBillingFiles,
	})
	if err != nil {
		return nil, err
	}

	if transferLetters == nil {
		return nil, errors.New("transfer letters not found")
	}

	// bsnr
	bsnr, err := srv.bsnrService.FindByCode(ctx, request.Bsnr)
	if err != nil {
		return nil, err
	}
	if bsnr == nil {
		return nil, errors.New("bsnr not found")
	}

	companionFilePath := ""
	zipFilePath := ""
	for _, billingFile := range mergedBillingFiles {
		if billingFile.FileType == edmp_common.DMPBillingHistoryFileType_Companion {
			companionFilePath = billingFile.FilePath
		}
		if billingFile.FileType == edmp_common.DMPBillingHistoryFileType_Billing {
			zipFilePath = billingFile.FilePath
		}
	}

	sdkvcaData, err := srv.getSdkvcaFunc(ctx)
	if err != nil {
		return nil, err
	}

	hksMail, err := sdkvcaData.GetHksMail(request.Bsnr)
	if err != nil {
		return nil, err
	}

	var recipient *billing_edoku_common.Recipient
	if hksMail != nil {
		recipient = &billing_edoku_common.Recipient{
			KimEmail: &hksMail.KimEmail,
		}
	}

	var recipientKv string
	regionCode := util.GetOKVByUKV(request.Bsnr[0:2])
	kvArea := srv.edmpKvRepo.GetKVAreaByRegion(regionCode)
	if kvArea != nil {
		recipientKv = kvArea.Label
	}

	// create billing history
	billingHistory, err := srv.billingHistoryEdokuRepo.Create(ctx, &billing_edoku_common.BillingHistoryModel{
		Year:    request.Quarter.Year,
		Quarter: request.Quarter.Quarter,
		Bsnr: billing_history_common.BSNR{
			Id:   bsnr.Id,
			Name: bsnr.Name,
			Code: bsnr.Code,
		},
		CheckModuleStatus:     util.NewPointer(billing_history_common.CheckModuleStatus_Ok),
		TransferLetterFileUrl: util.NewPointer(transferLetters.FilePath),
		CompanionFileUrl:      util.NewPointer(companionFilePath),
		ZipFileUrl:            util.NewPointer(zipFilePath),
		Recipient:             recipient,
		RecipientKv:           &recipientKv,
		DMPValue:              request.DMPValue,
		EdokuDocumentIds:      request.DocumentIds,
		IsOpenPreviousQuarter: request.IsOpenPreviousQuarter,
		XPMResultFiles:        xpmResultFiles,
	})
	if err != nil {
		return nil, err
	}

	if billingHistory == nil || billingHistory.GetId() == nil {
		return nil, errors.New("billing history not found")
	}
	billingHistoryId := billingHistory.GetId()
	if zipFilePath != "" {
		if err := srv.encryptBillingZipFile(ctx, &EncryptBillingZipFileRequest{
			BillingHistoryId: billingHistoryId,
			ZipFileUrl:       zipFilePath,
		}); err != nil {
			if _, err := srv.billingHistoryEdokuRepo.UpdateEncryptionStatus(ctx, &billing_history_edoku_repo.UpdateEncryptionStatusRequest{
				BillingHistoryId: billingHistoryId,
				EncryptionStatus: billing_history_common.EncryptionStatus_Failed,
				XkmFileUrl:       nil,
			}); err != nil {
				ctx.Logger().Error("failed to update encryption status", "error", err)
				return nil, err
			}
			return nil, err
		}
	}

	return &CreateBillingResponse{
		Status:                            true,
		DMPBillingHistoryId:               billingHistory.GetId(),
		DMPBillingFieldsValidationResults: res.DMPBillingFieldsValidationResults,
	}, nil
}

func (srv *BillingEDokuService) GetBillingHistory(ctx *titan.Context, request *GetBillingHistoryRequest) (*billing_edoku_common.BillingHistoryModel, error) {
	if request.BillingHistoryId == nil {
		return nil, errors.New("billing history id is required")
	}

	billingHistory, err := srv.billingHistoryEdokuRepo.GetById(ctx, *request.BillingHistoryId)
	if err != nil {
		return nil, err
	}

	if billingHistory == nil {
		return nil, errors.New("billing history not found")
	}

	return billingHistory.BillingHistoryModel, nil
}

func (srv *BillingEDokuService) encryptBillingZipFile(ctx *titan.Context, req *EncryptBillingZipFileRequest) error {
	bucketAndKey, err := minio.ExtractBucketAndKey(req.ZipFileUrl)
	if err != nil {
		return fmt.Errorf("failed to extract bucket and key: %w", err)
	}

	if bucketAndKey == nil {
		return fmt.Errorf("bucket and key is nil")
	}

	zipFileContent, err := srv.minioClient.DownloadFile(ctx, bucketAndKey.Bucket, bucketAndKey.Key, minio.GetObjectOptions{})
	if err != nil {
		return fmt.Errorf("failed to download billing zip file: %w", err)
	}

	encryptionStatus := billing_history_common.EncryptionStatus_Success
	xkmResult, err := srv.requestWithFileContentFunc(ctx, zipFileContent, bucketAndKey.Key, xkm.Encrypt, true, true)
	if err != nil {
		return fmt.Errorf("failed to encrypt billing zip file: %w", err)
	}
	if xkmResult == nil {
		return fmt.Errorf("failed to encrypt billing zip file: xkm result is nil")
	}

	if xkmResult.ErrorMsg != "" {
		return fmt.Errorf("failed to encrypt billing zip file: %v", xkmResult.ErrorMsg)
	}

	xkmBillingZipFileContentEncrypted, err := base64.StdEncoding.DecodeString(xkmResult.EncryptedFileBase64)
	if err != nil {
		return fmt.Errorf("failed to decrypt billing zip file: %w", err)
	}

	if len(xkmBillingZipFileContentEncrypted) == 0 {
		return fmt.Errorf("encrypted billing zip file content is empty")
	}

	xkmFileName := fmt.Sprintf("%s.XKM", bucketAndKey.Key)
	if _, err := srv.minioClient.PutObject(ctx, srv.bucketDmpBilling, xkmFileName, bytes.NewReader(xkmBillingZipFileContentEncrypted), int64(len(xkmBillingZipFileContentEncrypted)), minio.PutObjectOptions{}); err != nil {
		return fmt.Errorf("failed to upload encrypted billing file: %w", err)
	}

	fileUrl := fmt.Sprintf("%s/%s", srv.bucketDmpBilling, xkmFileName)
	if _, err := srv.billingHistoryEdokuRepo.UpdateEncryptionStatus(ctx, &billing_history_edoku_repo.UpdateEncryptionStatusRequest{
		BillingHistoryId: req.BillingHistoryId,
		EncryptionStatus: encryptionStatus,
		XkmFileUrl:       &fileUrl,
	}); err != nil {
		ctx.Logger().Error("failed to update encryption status", "error", err)
		return err
	}
	return nil
}

func (srv *BillingEDokuService) createAndUploadCompanionAndZipForDocuments(ctx *titan.Context, request CreateAndUploadCompanionAndZipForDocumentsRequest) ([]edmp_common.DMPBillingFile, error) {
	// build companion file content
	dmbValue := request.dmpValue
	xkmAbbreviation := edmp_common.GetXKMAbbreviation(dmbValue, util.YearQuarter{
		Year:    request.quarter.Year,
		Quarter: request.quarter.Quarter,
	})
	fileName := edmp_service.GenFileName(ctx, request.bsnr, request.version, xkmAbbreviation)
	archiveFileName := fmt.Sprintf("%v.zip.XKM", fileName)
	companionFileName := fmt.Sprintf("%v.idx", fileName)
	iknumber, err := srv.getIKNumberOfDataCenter(ctx, request.quarter, request.bsnr)
	if err != nil {
		return nil, err
	}
	if iknumber == nil {
		iknumber = util.NewString("")
	}
	companionFileContent, err := srv.edokuService.BuildCompanionFileContent(
		ctx,
		request.bsnr,
		*iknumber,
		archiveFileName,
		string(dmbValue),
	)
	if err != nil {
		return nil, err
	}

	// upload companion file to minio
	companionFileContentStringReader := strings.NewReader(companionFileContent)
	_, err = srv.minioClient.PutObject(ctx, srv.bucketDmpBilling, companionFileName, companionFileContentStringReader, int64(companionFileContentStringReader.Len()), minio.PutObjectOptions{})
	if err != nil {
		return nil, err
	}

	return []edmp_common.DMPBillingFile{
		{
			FileName: companionFileName,
			FilePath: fmt.Sprintf("%s/%s", srv.bucketDmpBilling, companionFileName),
			FileType: edmp_common.DMPBillingHistoryFileType_Companion,
		},
	}, nil
}

func (srv *BillingEDokuService) createAndUploadTransferLetters(ctx *titan.Context, request CreateAndUploadTransferLettersRequest) (*edmp_common.DMPBillingFile, error) {
	iknumber, err := srv.getIKNumberOfDataCenter(ctx, request.quarter, request.bsnr)
	if err != nil {
		return nil, err
	}
	if iknumber == nil {
		iknumber = util.NewString("")
	}
	builder := transfer_letter.EdokuTransferLetter{
		Html2PdfHost: srv.html2PdfPath,
		Sender:       request.bsnr,
		Receiver:     *iknumber,
		AmountOfData: int64(1),
		CreatedAt:    util.Now(ctx).Format("02.01.2006"),
		Content: slice.Map(request.billingFiles, func(file edmp_common.DMPBillingFile) string {
			return file.FileName
		}),
		SpecialHint: "",
	}
	transferLetter, err := builder.Build()
	if err != nil {
		return nil, fmt.Errorf("failed to build transfer letter: %w", err)
	}

	fileName := fmt.Sprintf("%v_Transportbegleitzettel.pdf", util.Now(ctx).Format("02012006150405"))
	displayName := fmt.Sprintf("%v_Transportbegleitzettel.pdf", util.Now(ctx).Format("02012006"))

	transferLetterReader := bytes.NewReader(transferLetter)
	_, err = srv.minioClient.PutObject(ctx, srv.bucketDmpBilling, fileName, transferLetterReader, transferLetterReader.Size(), minio.PutObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to upload transfer letter file to minio: %w", err)
	}

	transferLetterFile := &edmp_common.DMPBillingFile{
		FileName:    fileName,
		FilePath:    fmt.Sprintf("%s/%s", srv.bucketDmpBilling, fileName),
		FileType:    edmp_common.DMPBillingHistoryFileType_TransferLetter,
		DisplayName: util.NewString(displayName),
	}

	return transferLetterFile, nil
}

func (srv *BillingEDokuService) getIKNumberOfDataCenter(ctx *titan.Context, yq common.YearQuarter, bsnrCode string) (*string, error) {
	res, err := srv.sddaRepo.GetXMLDataCenters(ctx, &sdda_repo.GetSddaRequest{
		Year:    yq.Year,
		Quarter: yq.Quarter,
	})
	if err != nil {
		return nil, err
	}

	if len(res) == 0 {
		return nil, nil
	}

	dataCenters := res

	okv, err := okv_util.GetOkvByBsnr(bsnrCode)
	if err != nil {
		return nil, err
	}

	for _, d := range dataCenters {
		for _, kv := range d.DataCenter.KV {
			if kv.KVArea != nil && *kv.KVArea == okv {
				return &d.DataCenter.IKNumber, nil
			}
		}
	}
	return nil, nil
}

func (srv *BillingEDokuService) updateStatusById(ctx *titan.Context, billingHistoryId *uuid.UUID, status billing_edoku_common.BillingStatus) error {
	res, err := srv.billingHistoryEdokuRepo.UpdateStatus(ctx, &billing_history_edoku_repo.UpdateStatusRequest{
		BillingHistoryId: billingHistoryId,
		Status:           status,
	})
	if err != nil {
		return err
	}

	if res == nil || res.BillingHistoryModel == nil {
		return errors.New("billing history not found")
	}

	if err := srv.billingEDokuSocketNotifier.NotifyCareProviderBillingEDokuStatusChanged(ctx, &billing_edoku_api.EventBillingEDokuStatusChanged{
		BillingHistoryId: billingHistoryId,
		Status:           status,
	}); err != nil {
		return err
	}

	return nil
}

func (srv *BillingEDokuService) handleReceiptMail(ctx *titan.Context, mailIds []uuid.UUID) error {
	if len(mailIds) == 0 {
		return nil
	}

	mailItems, err := srv.mailService.GetInboxByIds(ctx, mailIds)
	if err != nil {
		return err
	}

	for _, mailItem := range mailItems {
		identifier := mailItem.GetHeader(string(mail_common.MailHeaderKey_ServiceIdentifer))
		if identifier != HKS_ReceiptIdentifier || mailItem.Subject != HKS_ReceiptSubject || len(mailItem.Attachments) == 0 {
			continue
		}

		for _, attachment := range mailItem.Attachments {
			if attachment.Url == "" {
				continue
			}

			bucketAndKey, err := minio.ExtractBucketAndKey(attachment.Url)
			if err != nil {
				return err
			}

			if bucketAndKey == nil || bucketAndKey.Bucket == "" || bucketAndKey.Key == "" {
				continue
			}

			fileContent, err := srv.minioClient.DownloadFile(ctx, bucketAndKey.Bucket, bucketAndKey.Key, minio.GetObjectOptions{})
			if err != nil {
				return err
			}

			if len(fileContent) == 0 {
				continue
			}

			receipt := &schema_model.Empfangsquittung{}
			if err := xml.Unmarshal(fileContent, receipt); err != nil {
				return err
			}

			error := billing_edoku_common.Error{
				Code:    int32(receipt.Paket.Fehler),
				Message: receipt.Paket.Fehlertext,
			}

			messageId := receipt.Paket.MessageID
			res, err := srv.billingHistoryEdokuRepo.UpdateErrorByMailMessageId(ctx, &billing_history_edoku_repo.UpdateErrorByMailMessageIdRequest{
				MailMessageId: &messageId,
				Error:         &error,
			})
			if err != nil {
				return err
			}

			if res == nil || res.BillingHistoryModel == nil {
				return errors.New("billing history not found")
			}

			if err := srv.updateStatusById(ctx, res.GetId(), billing_edoku_common.BillingStatus_Failed); err != nil {
				return err
			}

			if _, err := srv.billingHistoryEdokuRepo.UpdateMailErrorId(ctx, &billing_history_edoku_repo.UpdateMailErrorIdRequest{
				BillingHistoryId: res.GetId(),
				MailErrorId:      mailItem.Id,
			}); err != nil {
				return err
			}
		}
	}
	return nil
}
