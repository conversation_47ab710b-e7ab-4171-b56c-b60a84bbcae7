package service

import (
	"bytes"
	_ "embed"
	"encoding/base64"
	"encoding/xml"
	"fmt"
	"strconv"
	"strings"

	"emperror.dev/errors"
	"git.tutum.dev/medi/tutum/ares/app/companion/mail_app"
	catalog_sdkt_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_sdkt"
	eab_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/eab"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/mail"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/timeline"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/app/mvz/patient_profile"
	pkg_eab "git.tutum.dev/medi/tutum/ares/pkg/bundle_builder/eAB"
	eab_model_gen "git.tutum.dev/medi/tutum/ares/pkg/bundle_builder/eAB/schema"
	"git.tutum.dev/medi/tutum/ares/pkg/minio"
	"git.tutum.dev/medi/tutum/ares/pkg/operator"
	"git.tutum.dev/medi/tutum/ares/pkg/pdf_util"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	mongodb "git.tutum.dev/medi/tutum/ares/pkg/repo"
	"git.tutum.dev/medi/tutum/ares/service/bsnr"
	common_api "git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/share"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"golang.org/x/net/html/charset"

	"git.tutum.dev/medi/tutum/ares/service/domains/api/card_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	profile_service "git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	catalog_sdav_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_sdav"
	catalog_sdkt_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_sdkt"
	"git.tutum.dev/medi/tutum/ares/service/domains/companion_modules"
	doctor_letter_common "git.tutum.dev/medi/tutum/ares/service/domains/doctor_letter/common"
	eab_common "git.tutum.dev/medi/tutum/ares/service/domains/eab/common"
	eab_service_history_common "git.tutum.dev/medi/tutum/ares/service/domains/eab_service_history/common"
	eab_service_history_srv "git.tutum.dev/medi/tutum/ares/service/domains/eab_service_history/service"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	"git.tutum.dev/medi/tutum/ares/service/domains/patient_finder"
	qes_service "git.tutum.dev/medi/tutum/ares/service/domains/qes"
	qes_common "git.tutum.dev/medi/tutum/ares/service/domains/qes/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/billing_kv_history"
	eab_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/eab"
	schein_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	settingsRepos "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/settings"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	settings_common "git.tutum.dev/medi/tutum/ares/service/domains/settings/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/ti_service"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/timeline_service"
	mail_service "git.tutum.dev/medi/tutum/ares/service/mail"
	mail_common "git.tutum.dev/medi/tutum/ares/service/mail/common"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var Acceptable_Extensions = []string{"pdf", "xml", "ldt"}

const XML_HEADER = `<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../stylesheets/cda.xsl"?>
<?xml-model href="../schematron/abde-ab2015.sch" type="application/xml" schematypens="http://purl.oclc.org/dsdl/schematron"?>`

const (
	Default_Subject = "Arztbrief"
	EAB             = "Arztbrief"
	EAB_MDN_SUBJECT = "Arztbrief-Eingangsbestaetigung"
)

type (
	SettingRepo = settingsRepos.Settings[eab_common.EABSetting]

	EABService struct {
		timelineDocterLetterService *timeline_service.TimelineService[doctor_letter_common.DoctorLetter]
		mailService                 *mail_service.MailService
		qesService                  *qes_service.QESService
		patientProfileService       profile_service.PatientProfileService
		catalogSdavService          *catalog_sdav_service.CatalogSdavService
		eabRepo                     *eab_repo.EABRepo
		minioClient                 *minio.Minio
		bucketEab                   string
		bucketDoctorLetter          string
		companionService            companion_modules.CompanionService
		catalogSdktService          *catalog_sdkt_service.CatalogSdktService
		pdfConverter                *pdf_util.PDFConverter
		settingRepo                 *mongodb.Repo[*SettingRepo]
		scheinRepo                  schein_repo.ScheinRepoDefaultRepository
		eabServiceHistorySrv        *eab_service_history_srv.EABServiceHistoryService
		bsnrService                 *bsnr.BSNRService
		kvBillingHistoryRepo        billing_kv_history.BillingKVHistoryDefaultRepository
		employeeProfileService      profile_service.EmployeeProfileService
		patientProfileBff           *patient_profile.PatientProfileBffImpl
		eabSocketNotifier           *eab_api.EabSocketNotifier
		timelineService             *timeline_service.TimelineService[any]
		tiService                   *ti_service.TiSettingService
	}
	CreateEABRequest struct {
		DoctorLetterId uuid.UUID
	}
	UpdateEABRequest struct {
		TimelineModel  timeline_repo.TimelineEntity[doctor_letter_common.DoctorLetter]
		DoctorLetterId uuid.UUID
		EABId          uuid.UUID
	}
	UpdateEABResponse struct {
		TimelineModel timeline_repo.TimelineEntity[doctor_letter_common.DoctorLetter]
		EABData       eab_common.EABModel
	}
	DeleteByIdRequest struct {
		Id uuid.UUID
	}
	SendMailRequest struct {
		EABId     uuid.UUID
		BsnrCode  string
		EmailItem mail_common.EmailItem
		MailId    *uuid.UUID
	}
	UploadPDFRequest struct {
		DoctorLetterId uuid.UUID
		Base64         string
	}
	UploadToMinioRequest struct {
		BucketName string
		FileName   string
		Data       string
	}
	GetMailAttachmentsRequest struct {
		EABModel              eab_common.EABModel
		AdditionalAttachments []mail_common.Attachment
		MailId                *uuid.UUID
	}
	AssignPatientRequest struct {
		MailId    uuid.UUID
		PatientId uuid.UUID
	}
	UnAssignPatientRequest struct {
		MailId    uuid.UUID
		PatientId uuid.UUID
	}
	CheckAutomaticallyAssignPatientResponse struct {
		PatientInfo   *patient_profile_common.PatientInfo
		IsExistSchein bool
	}
	CreateEABServiceHistoryRequest struct {
		PatientId   uuid.UUID
		ServiceCode eab_service_history_common.EABServiceCode
		MailId      *uuid.UUID
	}
	SaveSettingRequest struct {
		IsAutoSending   bool
		IsAutoReceiving bool
	}
	AssignPatientResponse struct {
		PatientInfo *patient_profile_common.PatientInfo
	}
	UnAssignPatientResponse struct {
		PatientInfo *patient_profile_common.PatientInfo
	}
	UpdateEabStateForBsnrRequest struct {
		BsnrCode string
	}
	GetProposalPatientsResponse struct {
		ProposalPatients   []eab_api.ProposalPatient
		ProposalTextSearch string
	}
	GetEABRequest struct {
		Query             *string
		DateRange         *common_api.DateRange
		PaginationRequest common_api.PaginationRequest
		Statuses          []qes_common.DocumentStatus
	}
	UpdateStatusByIdRequest struct {
		Id     uuid.UUID
		Status qes_common.DocumentStatus
	}
)

var EABServiceMod = submodule.Make[*EABService](
	func(
		minioClient *minio.Minio,
		mvzConfig *config.MvzAppConfigs,
		companionService companion_modules.CompanionService,
		catalogSdavService *catalog_sdav_service.CatalogSdavService,
		eabRepo *eab_repo.EABRepo,
		pdfConverter *pdf_util.PDFConverter,
		eabServiceCodeHistoryService *eab_service_history_srv.EABServiceHistoryService,
		bsnrService *bsnr.BSNRService,
		employeeProfileService profile_service.EmployeeProfileService,
		patientProfileBffImpl *patient_profile.PatientProfileBffImpl,
		patientProfileService profile_service.PatientProfileService,
		timelineDocterLetterService *timeline_service.TimelineService[doctor_letter_common.DoctorLetter],
		mailService *mail_service.MailService,
		qesService *qes_service.QESService,
		socketClient *socket_api.SocketServiceClient,
		timelineService *timeline_service.TimelineService[any],
		tiService *ti_service.TiSettingService,
		catalogSdktService *catalog_sdkt_service.CatalogSdktService,
	) *EABService {
		settingRepo := settingsRepos.NewSettingsDefaultRepository[eab_common.EABSetting]()
		kvBillingHistoryRepo := billing_kv_history.NewBillingKVHistoryDefaultRepository()
		eabSocketNotifier := eab_api.NewEabSocketNotifier(socketClient)
		eabService := &EABService{
			eabServiceHistorySrv:        eabServiceCodeHistoryService,
			timelineDocterLetterService: timelineDocterLetterService,
			mailService:                 mailService,
			qesService:                  qesService,
			patientProfileService:       patientProfileService,
			eabRepo:                     eabRepo,
			minioClient:                 minioClient,
			bucketEab:                   mvzConfig.MinioClientConfig.BucketEAB,
			bucketDoctorLetter:          mvzConfig.MinioClientConfig.BucketDoctorLetter,
			catalogSdavService:          catalogSdavService,
			settingRepo:                 &settingRepo,
			companionService:            companionService,
			catalogSdktService:          catalogSdktService,
			pdfConverter:                pdfConverter,
			scheinRepo:                  schein_repo.NewScheinRepoDefaultRepository(),
			bsnrService:                 bsnrService,
			kvBillingHistoryRepo:        kvBillingHistoryRepo,
			employeeProfileService:      employeeProfileService,
			patientProfileBff:           patientProfileBffImpl,
			eabSocketNotifier:           eabSocketNotifier,
			timelineService:             timelineService,
			tiService:                   tiService,
		}
		mailService.RegisterOnCreateFunc(eabService.onReceiveMail)
		timelineService.HookBeforeAction.RegisterOnDeleteFunc(eabService.onDeleteTimeline)
		return eabService
	},
	minio.MinioMod,
	config.MvzAppConfigMod,
	companion_modules.CompanionServiceMod,
	catalog_sdav_service.CatalogSdavServiceMod,
	eab_repo.EABRepoMod,
	catalog_sdkt_service.CatalogSdktServiceMod,
	pdf_util.PDFConverterMod,
	eab_service_history_srv.EABServiceHistoryServiceMod,
	bsnr.BSNRServiceMod,
	profile_service.EmployeeProfileServiceMod,
	patient_profile.PatientProfileServiceMod,
	share.PatientProfileServiceMod,
	timeline_service.TimelineServiceDoctorLetterMod,
	timeline_service.TimelineServiceAnyMod,
	mail_service.MailKimServiceMod,
	qes_service.QesServiceMod,
	config.SocketServiceClientMod,
	ti_service.TiSettingServiceMod,
)

func (srv *EABService) GetTimelineAndPatientData(ctx *titan.Context, doctorLetterId uuid.UUID) (*timeline_repo.TimelineEntity[doctor_letter_common.DoctorLetter], *profile_service.PatientProfile, *patient_profile_common.InsuranceInfo, error) {
	timelineModel, err := srv.timelineDocterLetterService.FindById(ctx, doctorLetterId)
	if err != nil {
		return nil, nil, nil, err
	}
	if timelineModel == nil || timelineModel.Payload.Receiver == nil {
		return nil, nil, nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Record_Not_Found)
	}

	patientProfile, err := srv.patientProfileService.GetProfileById(ctx, &profile_service.GetByIdRequest{
		PatientId: util.NewPointer(timelineModel.PatientId),
	})
	if err != nil {
		return nil, nil, nil, errors.WithStack(err)
	}
	if patientProfile == nil {
		return nil, nil, nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Patient_Not_Found)
	}
	if len(patientProfile.PatientInfo.InsuranceInfos) == 0 {
		return nil, nil, nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Empty_InsuranceInfo)
	}

	insuranceInfo := patientProfile.PatientInfo.GetActiveInsurance()
	if insuranceInfo == nil && len(patientProfile.PatientInfo.InsuranceInfos) > 0 {
		insuranceInfo = &patientProfile.PatientInfo.InsuranceInfos[0]
	}

	return timelineModel, patientProfile, insuranceInfo, nil
}

func (srv *EABService) GetDoctorAndBSNR(ctx *titan.Context) (*profile_service.EmployeeProfileResponse, *bsnr_repo.BSNR, error) {
	doctorId := ctx.UserInfo().UserUUID()
	doctor, err := srv.employeeProfileService.GetEmployeeProfileById(ctx, &profile_service.EmployeeProfileGetRequest{
		OriginalId: doctorId,
	})
	if err != nil {
		return nil, nil, errors.WithMessage(err, "can not get doctor")
	}
	if doctor == nil {
		return nil, nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Doctor_Not_Found)
	}

	bsnr, err := srv.bsnrService.FindByCode(ctx, doctor.Bsnr)
	if err != nil {
		return nil, nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_BSNR_NOT_FOUND)
	}

	return doctor, bsnr, nil
}

func (srv *EABService) BuildAndUploadEABBundle(
	ctx *titan.Context,
	request *pkg_eab.NewBundleBuilderRequest,
	doctorLetterId uuid.UUID,
) (string, error) {
	eabBuilder := pkg_eab.NewEABBuilder(ctx, request)
	bundle := eabBuilder.SetMetaData().
		CreatePatientRole().
		CreateParticipant().
		CreateAuthor().
		CreateCustodian().
		CreateBody().
		Build()

	if bundle == nil {
		return "", nil
	}

	res, err := xml.Marshal(bundle)
	if err != nil {
		return "", err
	}

	// Prepare the XML file content
	dataString := []string{XML_HEADER}
	dataString = append(dataString, string(res))
	fileContent := strings.Join(dataString, "")

	// Generate the file name
	fileName := fmt.Sprintf("%s_%s.xml", EAB, doctorLetterId)

	// Upload to Minio
	err = srv.UploadToMinio(ctx, &UploadToMinioRequest{
		Data:       fileContent,
		FileName:   fileName,
		BucketName: srv.bucketEab,
	})
	if err != nil {
		return "", err
	}

	return fileName, nil
}

func (srv *EABService) CreateEAB(ctx *titan.Context, request CreateEABRequest) (*eab_common.EABModel, error) {
	timelineModel, patientProfile, insuranceInfo, err := srv.GetTimelineAndPatientData(ctx, request.DoctorLetterId)
	if err != nil {
		return nil, err
	}

	doctor, bsnr, err := srv.GetDoctorAndBSNR(ctx)
	if err != nil {
		return nil, err
	}

	fileName, err := srv.BuildAndUploadEABBundle(ctx, &pkg_eab.NewBundleBuilderRequest{
		PatientInfo:   patientProfile.PatientInfo,
		InsuranceInfo: insuranceInfo,
		DoctorInfo:    doctor,
		DataMap:       timelineModel.Payload.DataMap,
		Bsnr:          bsnr,
		Receiver:      timelineModel.Payload.Receiver,
	}, request.DoctorLetterId)
	if err != nil {
		return nil, err
	}

	eabEntity, err := srv.eabRepo.Create(ctx, eab_common.EABModel{
		DoctorLetterId: util.GetPointerValue(timelineModel.Payload.Id),
		Status:         qes_common.Status_Created,
		XmlFile: &eab_common.File{
			Name: fmt.Sprintf("%s.xml", EAB),
			Url:  fmt.Sprintf("%s/%s", srv.bucketEab, fileName),
		},
		PatientProfile: util.GetPointerValue(patientProfile),
	})
	if err != nil {
		return nil, err
	}

	if eabEntity == nil {
		return nil, errors.Errorf("failed to create eab")
	}

	if err := srv.eabSocketNotifier.NotifyCareProviderEABChanged(ctx, &eab_api.EventEABChanged{
		EventType: eab_common.EABEventType_Create,
		Model:     []eab_common.EABModel{eabEntity.EABModel},
	}); err != nil {
		return nil, err
	}

	return &eabEntity.EABModel, nil
}

func (srv *EABService) UpdateEAB(ctx *titan.Context, request UpdateEABRequest) (*UpdateEABResponse, error) {
	timelineModel, patientProfile, insuranceInfo, err := srv.GetTimelineAndPatientData(ctx, request.DoctorLetterId)
	if err != nil || timelineModel == nil {
		return nil, err
	}

	timelineModel, err = srv.timelineDocterLetterService.Edit(ctx, request.TimelineModel)
	if err != nil {
		return nil, err
	}

	doctor, bsnr, err := srv.GetDoctorAndBSNR(ctx)
	if err != nil {
		return nil, err
	}

	fileName, err := srv.BuildAndUploadEABBundle(ctx, &pkg_eab.NewBundleBuilderRequest{
		PatientInfo:   patientProfile.PatientInfo,
		InsuranceInfo: insuranceInfo,
		DoctorInfo:    doctor,
		DataMap:       timelineModel.Payload.DataMap,
		Bsnr:          bsnr,
		Receiver:      timelineModel.Payload.Receiver,
	}, request.DoctorLetterId)
	if err != nil {
		return nil, err
	}

	eabEntity, err := srv.eabRepo.Update(ctx, eab_common.EABModel{
		DoctorLetterId: util.GetPointerValue(timelineModel.Payload.Id),
		XmlFile: &eab_common.File{
			Name: fmt.Sprintf("%s.xml", EAB),
			Url:  fmt.Sprintf("%s/%s", srv.bucketEab, fileName),
		},
		Id:             &request.EABId,
		PatientProfile: util.GetPointerValue(patientProfile),
	})
	if err != nil {
		return nil, err
	}

	if eabEntity == nil {
		return nil, errors.Errorf("failed to update eab")
	}

	if err := srv.eabSocketNotifier.NotifyCareProviderEABChanged(ctx, &eab_api.EventEABChanged{
		EventType: eab_common.EABEventType_Update,
		Model:     []eab_common.EABModel{eabEntity.EABModel},
	}); err != nil {
		return nil, err
	}

	return &UpdateEABResponse{
		TimelineModel: *timelineModel,
		EABData:       eabEntity.EABModel,
	}, nil
}

func (srv *EABService) Sign(ctx *titan.Context, ids []uuid.UUID) ([]uuid.UUID, error) {
	res, err := srv.eabRepo.FindByIds(ctx, ids)
	if err != nil {
		return nil, err
	}

	if res == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Record_Not_Found)
	}

	mapRequestIdPdf := map[string]string{}
	for _, doc := range res {
		if doc.XmlFile == nil {
			continue
		}

		pdfData, err := srv.readFileMinio(ctx, util.GetPointerValue(doc.PDFUrl))
		if err != nil {
			return nil, err
		}

		if pdfData == nil {
			continue
		}

		mapRequestIdPdf[doc.GetId().String()] = string(pdfData)
	}

	if len(mapRequestIdPdf) == 0 {
		return nil, errors.Errorf("empty data to sign")
	}

	cardForSigning, err := srv.qesService.GetSigningCard(ctx, &qes_service.GetSigningCardRequest{
		CardType: card_common.CardTypeTypeHBA,
	})
	if err != nil {
		return nil, err
	}

	if cardForSigning == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_TI_Card_Not_Found)
	}

	signedRes, err := srv.qesService.SignDocument(ctx, qes_service.SignDocumentRequest{
		CardForSigning:         cardForSigning,
		MapRequestIdBundleData: mapRequestIdPdf,
		DocumentType:           qes_service.DocumentType_PDFA,
		FeatureType:            qes_service.FeatureType_EAB,
	})
	if err != nil {
		return nil, err
	}

	if signedRes == nil {
		return nil, errors.New("empty signed response")
	}

	result, err := srv.qesService.UploadResources(ctx, qes_service.UploadResourcesRequest{
		BucketName:     srv.bucketEab,
		SignedResponse: signedRes,
	})
	if err != nil {
		return nil, err
	}

	signedIds := []uuid.UUID{}
	if len(result) > 0 {
		for id, data := range result {
			idUuid, err := uuid.Parse(id)
			if err != nil {
				return nil, err
			}
			err = srv.updateById(ctx, idUuid, bson.D{
				{Key: operator.Set, Value: bson.D{
					{Key: eab_repo.Field_SignedFile, Value: eab_common.File{
						Name: fmt.Sprintf("%s.pdf", EAB),
						Url:  data.SignatureUrl,
					}},
				}},
			})
			if err != nil {
				return nil, err
			}

			signedIds = append(signedIds, idUuid)
		}
	}

	return signedIds, nil
}

func toAddresses(addresses []mail_common.Address) []mail_common.Address {
	return slice.Map(addresses, func(add mail_common.Address) mail_common.Address {
		return mail_common.Address{
			Address: add.Address,
			Name:    "",
		}
	})
}

func extractAddresses(addresses []mail_common.Address) []string {
	result := make([]string, len(addresses))
	for i, addr := range addresses {
		result[i] = addr.Address
	}
	return result
}

func (srv *EABService) sendMail(ctx *titan.Context, request SendMailRequest) error {
	eabRes, err := srv.eabRepo.FindById(ctx, request.EABId)
	if err != nil {
		return err
	}

	if eabRes == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Record_Not_Found)
	}

	mailItem := request.EmailItem

	subject := Default_Subject
	if mailItem.Subject != "" {
		subject = mailItem.Subject
	}

	attachments, err := srv.GetMailAttachments(ctx, GetMailAttachmentsRequest{
		EABModel:              eabRes.EABModel,
		AdditionalAttachments: mailItem.Attachments,
		MailId:                request.MailId,
	})
	if err != nil {
		return err
	}

	isInvalidSignature := eabRes.SignedFile == nil
	res := &mail.SendMailResponse{}
	if request.MailId != nil {
		mailId := util.GetPointerValue(request.MailId)
		res, err = srv.mailService.ReSendMail(ctx, mailId)
	} else {
		res, err = srv.mailService.SendMail(ctx, mail.SendMailRequest{
			From: mail_common.Address{
				Name:    "",
				Address: mailItem.From.Address,
			},
			To:                   toAddresses(mailItem.To),
			Cc:                   toAddresses(mailItem.Cc),
			Bcc:                  toAddresses(mailItem.Bcc),
			Subject:              subject,
			Body:                 mailItem.Body,
			Attachments:          attachments,
			Category:             string(mail_common.MailCategory_EAB),
			DeliveryConfirmation: mailItem.RequestDeliveryConfirmation,
			IsInvalidSignature:   &isInvalidSignature,
			Patient:              request.EmailItem.Patient,
		})
	}

	if err != nil {
		return err
	}

	toAddresses := mailItem.To
	if len(mailItem.Cc) > 0 {
		toAddresses = append(toAddresses, mailItem.Cc...)
	}

	if len(mailItem.Bcc) > 0 {
		toAddresses = append(toAddresses, mailItem.Bcc...)
	}

	mailTo := extractAddresses(toAddresses)
	errUpdate := srv.updateById(ctx, *eabRes.GetId(), bson.D{
		{Key: operator.Set, Value: bson.D{
			{Key: eab_repo.Field_Receiver, Value: mailTo},
		}},
	})

	if errUpdate != nil {
		return errUpdate
	}

	if res == nil {
		return nil
	}

	err = srv.updateById(ctx, *eabRes.GetId(), bson.D{
		{Key: operator.Set, Value: bson.D{
			{Key: eab_repo.Field_Status, Value: qes_common.Status_Sent},
			{Key: eab_repo.Field_MailSentId, Value: res.MailId},
		}},
	})
	if err != nil {
		return err
	}

	if request.EmailItem.Patient != nil {
		if err := srv.CreateEABServiceHistory(ctx, CreateEABServiceHistoryRequest{
			PatientId:   request.EmailItem.Patient.Id,
			ServiceCode: eab_service_history_common.EABServiceCode_Sending,
		}); err != nil {
			return err
		}
	}

	if err := srv.UpdateEabStateForBsnr(ctx, UpdateEabStateForBsnrRequest{
		BsnrCode: request.BsnrCode,
	}); err != nil {
		return err
	}

	return nil
}

func (srv *EABService) ValidateEmailAddresses(ctx *titan.Context, emailAddresses []string) ([]string, error) {
	ldapSetting, err := srv.mailService.GetAddressBook(ctx)
	if err != nil {
		return nil, err
	}
	if ldapSetting == nil || ldapSetting.LDAPSetting.Ip == "" {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_LDAP_AddressBookNotFound)
	}

	builder := strings.Builder{}
	builder.WriteString("(|")
	for _, address := range emailAddresses {
		builder.WriteString(fmt.Sprintf("(mail=%s)", address))
	}
	builder.WriteString(")")
	filterQuery := builder.String()

	companionMailService, err := srv.companionService.GetMailApp(ctx)
	if err != nil {
		return nil, err
	}
	res, err := companionMailService.QueryLDAP(ctx, mail_app.QueryLdapRequest{
		FilterValue: filterQuery,
		LDAPConfig: mail_app.LDAPConfig{
			Host:   ldapSetting.LDAPSetting.Ip,
			Port:   ldapSetting.LDAPSetting.Port,
			BaseDn: ldapSetting.LDAPSetting.BasisDN,
		},
	})
	if err != nil {
		return nil, err
	}

	if res == nil || len(res.Entries) == 0 {
		return nil, nil
	}

	result := slice.Map(res.Entries, func(entry mail_app.LdapEntry) string {
		return entry.GetAttributeValue("mail")
	})

	return result, nil
}

func (srv *EABService) GetById(ctx *titan.Context, id uuid.UUID) (*eab_repo.EABEntity, error) {
	return srv.eabRepo.FindById(ctx, id)
}

func (srv *EABService) GetKIMAccounts(ctx *titan.Context, bsnrCodes []string) ([]mail_common.MailAccountDto, error) {
	return srv.mailService.GetAllAccount(ctx, bsnrCodes)
}

func (srv *EABService) UploadPDF(ctx *titan.Context, request *UploadPDFRequest) (string, error) {
	convertRes, err := srv.pdfConverter.ConvertPDFToPDFA(request.Base64)
	if err != nil {
		return "", err
	}

	if convertRes == nil {
		return "", nil
	}

	pdfConverted, err := base64.RawStdEncoding.DecodeString(convertRes.Data)
	if err != nil {
		return "", err
	}

	fileName := fmt.Sprintf("%s_%s.pdf", EAB, request.DoctorLetterId)
	err = srv.UploadToMinio(ctx, &UploadToMinioRequest{
		FileName:   fileName,
		Data:       string(pdfConverted),
		BucketName: srv.bucketDoctorLetter,
	})
	if err != nil {
		return "", err
	}

	res, err := srv.eabRepo.FindByDoctorLetterId(ctx, request.DoctorLetterId)
	if err != nil {
		return "", err
	}

	if res == nil {
		return "", pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_DoctorLetter_Timeline_Not_Found)
	}

	pdfUrl := fmt.Sprintf("%s/%s", srv.bucketDoctorLetter, fileName)
	err = srv.updateById(ctx, *res.GetId(), bson.D{
		{Key: operator.Set, Value: bson.D{
			{Key: eab_repo.Field_PDFUrl, Value: pdfUrl},
		}},
	})
	if err != nil {
		return "", err
	}

	return pdfUrl, nil
}

func (srv *EABService) UploadToMinio(ctx *titan.Context, request *UploadToMinioRequest) error {
	strReader := strings.NewReader(request.Data)
	_, err := srv.minioClient.PutObject(ctx, request.BucketName, request.FileName, strReader, strReader.Size(), minio.PutObjectOptions{})
	return err
}

func (*EABService) GetMailAttachments(ctx *titan.Context, request GetMailAttachmentsRequest) ([]mail_common.Attachment, error) {
	attachments := []mail_common.Attachment{}
	if len(request.AdditionalAttachments) > 0 {
		for index, item := range request.AdditionalAttachments {
			ext, err := item.ValidateExtension(ctx, Acceptable_Extensions)
			if err != nil {
				return nil, err
			}

			key := fmt.Sprint(index + 1)
			contentDescription := fmt.Sprintf("%s-%s", eab_common.EAB_ADDITIONAL_FILE, util.LeftPad(key, 2, '0'))
			result := mail_common.Attachment{
				Name: item.Name,
				Headers: map[string]string{
					mail_service.MailHeader_ContentDisposition:      fmt.Sprintf(`attachment; filename=%q`, item.Name),
					mail_service.MailHeader_ContentType:             fmt.Sprintf(`application/%s; name=%q`, ext, item.Name),
					mail_service.MailHeader_ContentTransferEncoding: "base64",
					mail_service.MailHeader_ContentDescription:      contentDescription,
				},
				Url: item.Url,
			}
			attachments = append(attachments, result)
		}
	}

	if request.MailId != nil {
		return attachments, nil
	}

	eabModel := request.EABModel
	if eabModel.XmlFile == nil {
		return nil, fmt.Errorf("missing xml file")
	}

	if eabModel.SignedFile != nil {
		signedFile := util.GetPointerValue(eabModel.SignedFile)
		attachments = append(attachments, mail_common.Attachment{
			Name: signedFile.Name,
			Url:  signedFile.Url,
			Headers: map[string]string{
				mail_service.MailHeader_ContentType:             fmt.Sprintf(`%s; name=%q`, mail_common.AttachmentContentType_Pdf, signedFile.Name),
				mail_service.MailHeader_ContentDisposition:      fmt.Sprintf(`attachment; filename=%q`, signedFile.Name),
				mail_service.MailHeader_ContentDescription:      string(eab_common.EAB_PDF_SIGNED),
				mail_service.MailHeader_ContentTransferEncoding: string(mail_common.ContentTransferEncoding_Base64),
			},
		})
	}

	xmlFile := util.GetPointerValue(eabModel.XmlFile)
	attachments = append(attachments, []mail_common.Attachment{
		{
			Name: xmlFile.Name,
			Url:  xmlFile.Url,
			Headers: map[string]string{
				mail_service.MailHeader_ContentType:             fmt.Sprintf(`%s; name=%q`, mail_common.AttachmentContentType_Xml, xmlFile.Name),
				mail_service.MailHeader_ContentDisposition:      fmt.Sprintf(`attachment; filename=%q`, xmlFile.Name),
				mail_service.MailHeader_ContentDescription:      string(eab_common.EAB_XML),
				mail_service.MailHeader_ContentTransferEncoding: string(mail_common.ContentTransferEncoding_Base64),
			},
		},
	}...)

	return attachments, nil
}

func (srv *EABService) readFileMinio(ctx *titan.Context, fileUrl string) ([]byte, error) {
	if fileUrl == "" {
		return nil, nil
	}
	data := strings.Split(fileUrl, "/")
	return srv.minioClient.DownloadFile(ctx, data[0], data[1], minio.GetObjectOptions{})
}

func (srv *EABService) updateById(ctx *titan.Context, id uuid.UUID, updated any) error {
	res, err := srv.eabRepo.FindOneAndUpdate(ctx, bson.D{
		{
			Key:   repos.Field_Id,
			Value: id,
		},
		{
			Key:   repos.Field_IsDeleted,
			Value: false,
		},
	},
		updated,
		options.FindOneAndUpdate().SetReturnDocument(options.After),
	)
	if err != nil {
		return err
	}

	if res == nil {
		return errors.Errorf("update failed")
	}

	return nil
}

func (srv *EABService) CheckAutomaticallyAssignPatient(ctx *titan.Context, mailId uuid.UUID) (*CheckAutomaticallyAssignPatientResponse, error) {
	mailItem, err := srv.mailService.GetInboxById(ctx, mailId)
	if err != nil {
		return nil, err
	}

	if mailItem == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Email_Not_Found)
	}

	if !mailItem.IsEAB() {
		return nil, nil
	}

	patientInfo, err := srv.GetPatientInfoFromAttachments(ctx, mailItem.Attachments)
	if err != nil {
		return nil, err
	}

	if patientInfo == nil {
		return nil, nil
	}

	eabPatientFinder := patient_finder.NewEABPatientFinderWithPatientProfileService(
		patient_finder.NewEABPatientFinderRequest{
			PatientInfoFormXml: patientInfo,
		},
		srv.patientProfileBff,
	)

	matchedPatient, err := eabPatientFinder.FindMatchPatient(ctx)
	if err != nil {
		return nil, err
	}

	if matchedPatient == nil {
		return nil, nil
	}

	personalInfo := matchedPatient.PersonalInfo
	matchedPatientId := util.GetPointerValue(matchedPatient.PatientId)
	if err := srv.mailService.AssignPatient(ctx, mail.AssignPatientRequest{
		Id:    *mailItem.GetId(),
		Inbox: true,
		Patient: &mail_common.Patient{
			Id:       matchedPatientId,
			FistName: personalInfo.FirstName,
			LastName: personalInfo.LastName,
		},
	}); err != nil {
		return nil, err
	}

	if err := srv.CreateEABServiceHistory(ctx, CreateEABServiceHistoryRequest{
		PatientId:   matchedPatientId,
		ServiceCode: eab_service_history_common.EABServiceCode_Receiving,
		MailId:      mailItem.Id,
	}); err != nil {
		return nil, err
	}

	isExistSchein, err := srv.CheckExistScheinByPatientId(ctx, matchedPatientId)
	if err != nil {
		return nil, err
	}

	return &CheckAutomaticallyAssignPatientResponse{
		PatientInfo:   matchedPatient,
		IsExistSchein: isExistSchein,
	}, nil
}

func (srv *EABService) GetProposalPatients(ctx *titan.Context, mailId uuid.UUID) (*GetProposalPatientsResponse, error) {
	mailItem, err := srv.mailService.GetInboxById(ctx, mailId)
	if err != nil {
		return nil, err
	}

	if mailItem == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Email_Not_Found)
	}

	if !mailItem.IsEAB() {
		return nil, nil
	}

	patientInfo, err := srv.GetPatientInfoFromAttachments(ctx, mailItem.Attachments)
	if err != nil {
		return nil, err
	}

	if patientInfo == nil {
		return nil, nil
	}

	eabPatientFinder := patient_finder.NewEABPatientFinderWithPatientProfileService(
		patient_finder.NewEABPatientFinderRequest{
			PatientInfoFormXml: patientInfo,
		},
		srv.patientProfileBff,
	)

	patients, err := eabPatientFinder.FindPartialMatchingPatient(ctx)
	if err != nil {
		return nil, err
	}

	proposalTextSearch := fmt.Sprintf("%s,%s,%s", patientInfo.PersonalInfo.LastName, patientInfo.PersonalInfo.FirstName, patientInfo.PersonalInfo.DateOfBirthStringPatientSidebar())
	if len(patients) == 0 {
		return &GetProposalPatientsResponse{
			ProposalTextSearch: proposalTextSearch,
		}, nil
	}

	proposalPatients := make([]eab_api.ProposalPatient, 0)
	for _, p := range patients {
		if proposalPatient := toProposalPatient(p); proposalPatient != nil {
			proposalPatients = append(proposalPatients, *proposalPatient)
		}
	}

	if len(proposalPatients) == 0 {
		return nil, nil
	}

	return &GetProposalPatientsResponse{
		ProposalPatients:   proposalPatients,
		ProposalTextSearch: proposalTextSearch,
	}, nil
}

func toProposalPatient(p patient.PatientProfile) *eab_api.ProposalPatient {
	patientInfo := p.PatientInfo
	if patientInfo == nil {
		return nil
	}
	var insuranceNumber *string
	activeInsurance := patientInfo.GetActiveInsurance()
	if activeInsurance != nil {
		insuranceNumber = activeInsurance.InsuranceNumber
	}
	proposalPatient := eab_api.ProposalPatient{
		Id:              *p.Id,
		PatientNumber:   int64(patientInfo.PatientNumber),
		FirstName:       patientInfo.PersonalInfo.FirstName,
		LastName:        patientInfo.PersonalInfo.LastName,
		DOB:             patientInfo.PersonalInfo.DOB,
		InsuranceNumber: insuranceNumber,
		Gender:          patientInfo.PersonalInfo.Gender,
		TypeOfInsurance: patient_profile_common.TypeOfInsurance(patientInfo.GenericInfo.PatientType),
		DateOfDeath:     patientInfo.PersonalInfo.DateOfDeath,
		DateOfBirth:     patientInfo.PersonalInfo.DateOfBirth,
	}
	return &proposalPatient
}

func (srv *EABService) setInsuranceCompanyInfo(ctx *titan.Context, i patient_profile_common.InsuranceInfo, sdkt *catalog_sdkt_api.SearchSdktByIKNumberResponse) (*patient_profile_common.InsuranceInfo, error) {
	newInsuranceInfo := i
	insuranceCompany := sdkt.Data
	if insuranceCompany.AccquiredCostUnit != nil {
		acquiredCostUnit, err := srv.catalogSdktService.UniqCompanyByAccquiredCostUnit(ctx, &catalog_sdkt_api.UniqCompanyByAccquiredCostUnitRequest{
			SdktCatalog: insuranceCompany,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "get UniqCompanyByAcquiredCostUnit failed")
		}

		acquiredInsuranceInfo := newInsuranceInfo
		acquiredInsuranceInfo.InsuranceCompanyId = insuranceCompany.Vknr
		acquiredInsuranceInfo.InsuranceCompanyName = insuranceCompany.Name
		acquiredInsuranceInfo.LocationNames = insuranceCompany.LocationNames
		if insuranceCompany.Validity != nil {
			acquiredInsuranceInfo.Validity = util.GetPointerValue(insuranceCompany.Validity)
		}
		acquiredInsuranceInfo.FeeCatalogue = insuranceCompany.FeeCatalogue

		newInsuranceInfo.AcquiredInsuranceInfo = util.NewPointer(acquiredInsuranceInfo)

		insuranceCompany = acquiredCostUnit.SdktCatalog
	}

	newInsuranceInfo.InsuranceCompanyId = insuranceCompany.Vknr
	newInsuranceInfo.InsuranceCompanyName = insuranceCompany.Name
	newInsuranceInfo.LocationNames = insuranceCompany.LocationNames
	if insuranceCompany.Validity != nil {
		newInsuranceInfo.Validity = util.GetPointerValue(insuranceCompany.Validity)
	}
	newInsuranceInfo.FeeCatalogue = insuranceCompany.FeeCatalogue
	return &newInsuranceInfo, nil
}

func (srv *EABService) GetPatientInfoFromAttachments(ctx *titan.Context, attachments []mail_common.Attachment) (*patient_profile_common.PatientInfo, error) {
	xmlAttachment := slice.FindOne(attachments, func(attachment mail_common.Attachment) bool {
		if attachment.Headers == nil {
			return false
		}
		header := attachment.Headers
		return strings.Contains(strings.ToUpper(attachment.Name), "XML") && strings.Contains(header[mail_service.MailHeader_ContentDescription], string(eab_common.EAB_XML))
	})

	if xmlAttachment == nil || xmlAttachment.Url == "" {
		return nil, nil
	}

	data, err := srv.readFileMinio(ctx, xmlAttachment.Url)
	if err != nil {
		return nil, err
	}

	if data == nil {
		return nil, nil
	}

	reader := bytes.NewReader(data)
	decoder := xml.NewDecoder(reader)
	decoder.CharsetReader = charset.NewReaderLabel

	var clinicDocument eab_model_gen.ClinicalDocumentExtend
	err = decoder.Decode(&clinicDocument)
	if err != nil {
		return nil, err
	}
	return clinicDocument.ToPatientProfile()
}

func (srv *EABService) CheckSignatureStatus(ctx *titan.Context, fileUrl string) (bool, error) {
	if fileUrl == "" {
		return false, nil
	}

	signedData, err := srv.readFileMinio(ctx, fileUrl)
	if err != nil {
		return false, err
	}

	if signedData == nil {
		return false, nil
	}

	res, err := srv.qesService.VerifySignature(ctx, qes_service.VerifyDocumentRequest{
		DocumentType: qes_service.DocumentType_PDFA,
		SignedData:   string(signedData),
	})
	if err != nil {
		ctx.Logger().Error("verify signature error. %w", err)
		return false, nil
	}

	if res == nil {
		return false, nil
	}

	return res.HighLevelResult == string(qes_common.SignatureStatus_Valid), nil
}

func (srv *EABService) onReceiveMail(ctx *titan.Context, mailIds []uuid.UUID) error {
	mailItems, err := srv.mailService.GetInboxByIds(ctx, mailIds)
	if err != nil {
		return err
	}

	isAutoSendMDN, err := srv.mailService.IsAutoSendMDN(ctx)
	if err != nil {
		return err
	}

	for _, mailItem := range mailItems {
		if mailItem.IsEABMDN() {
			if len(mailItem.InReplyTo) == 0 {
				continue
			}

			messageId := mailItem.InReplyTo[0]
			mailSent, err := srv.mailService.GeOutboxByMessageId(ctx, messageId)
			if err != nil {
				return err
			}

			if mailSent == nil {
				return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Email_Not_Found)
			}

			if err := srv.mailService.UpdateOutboxMDNStatusById(ctx, *mailSent.GetId(), mail_common.MDNStatus_Received); err != nil {
				return err
			}
			continue
		}

		if !mailItem.IsEAB() {
			continue
		}

		mailId := *mailItem.GetId()
		// Update signature status
		signedFile := slice.FindOne(mailItem.Attachments, func(attachment mail_common.Attachment) bool {
			if attachment.Headers == nil {
				return false
			}
			headers := attachment.Headers
			return headers[mail_service.MailHeader_ContentDescription] == string(eab_common.EAB_PDF_SIGNED)
		})

		isInvalid := true
		if signedFile != nil {
			isValid, err := srv.CheckSignatureStatus(ctx, signedFile.Url)
			if err != nil {
				return err
			}
			isInvalid = !isValid
		}

		if err := srv.mailService.UpdateInboxSignatureStatusById(ctx, mailId, isInvalid); err != nil {
			return err
		}

		dispositionNotificationTo := mailItem.GetHeader(mail_service.MailHeader_DispositionNotificationTo)
		if dispositionNotificationTo == "" {
			continue
		}

		// Update MDN status
		if err := srv.mailService.UpdateInboxMDNStatusById(ctx, mailId, mail_common.MDNStatus_Pending); err != nil {
			return err
		}

		if !isAutoSendMDN {
			continue
		}

		toAddresses := mailItem.To
		if len(mailItem.Cc) > 0 {
			toAddresses = append(toAddresses, mailItem.Cc...)
		}

		if len(mailItem.Bcc) > 0 {
			toAddresses = append(toAddresses, mailItem.Bcc...)
		}

		go func() {
			ctxCloned := ctx.Clone()
			for _, address := range toAddresses {
				err := srv.mailService.SendMailMDN(ctxCloned, mail.SendMailRequest{
					From: address,
					To: []mail_common.Address{
						{
							Address: dispositionNotificationTo,
						},
					},
					InReplyTo: []string{
						mailItem.MessageID,
					},
					Subject:  EAB_MDN_SUBJECT,
					Body:     fmt.Sprintf("Dies ist eine Eingangsbestaetigung fuer eine eArztbrief-Nachricht, die Sie an folgenden Empfaenger gesendet haben: %s \nBeachten Sie:\nDiese Eingangsbestaetigung sagt nur aus, dass die eArztbrief-Nachricht vom System des Empfaengers abgeholt wurde.\nEs gibt keine Garantie, dass der Empfaenger die Nachrichteninhalte gelesen hat.\n\n", address),
					Category: string(mail_common.MailCategory_EABMDN),
					ExtraHeaders: map[string]string{
						string(mail_common.MailHeaderKey_Sendersystem): "Beispielsystem",
						"Original-Message-ID":                          mailItem.MessageID,
					},
				})
				if err != nil {
					ctxCloned.Logger().Error("reply confirmation error. %w", err)
					return
				}
			}

			if err := srv.mailService.UpdateInboxMDNStatusById(ctxCloned, mailId, mail_common.MDNStatus_Sent); err != nil {
				ctxCloned.Logger().Error("update mail error. %w", err)
				return
			}

			if err := srv.eabSocketNotifier.NotifyUserMDNStatusChanged(ctxCloned, &eab_api.EventMDNStatusChanged{
				Id:        mailId,
				MdnStatus: mail_common.MDNStatus_Sent,
			}); err != nil {
				ctxCloned.Logger().Error("notify error. %w", err)
				return
			}
		}()
	}

	return nil
}

func (srv *EABService) GetSetting(ctx *titan.Context) (*SettingRepo, error) {
	time := util.NowUnixMillis(ctx)
	quarter, year := util.GetCurrentQuarter(util.ConvertMillisecondsToTime(time))
	setting, err := srv.settingRepo.FindOneAndUpdate(ctx, bson.M{
		settingsRepos.Field_Feature: settings_common.SettingsFeatures_EABAutoBilling,
		settingsRepos.Field_Signal:  "",
	}, bson.M{
		"$setOnInsert": bson.M{
			settingsRepos.Field_Id:                                        uuid.New(),
			settingsRepos.Field_Settings_EAB_IsAutoSending:                false,
			settingsRepos.Field_Settings_EAB_IsAutoReceiving:              false,
			settingsRepos.Field_Settings_EAB_IsConsentTriggerForReceiving: false,
			settingsRepos.Field_Settings_EAB_IsConsentTriggerForSending:   false,
			settingsRepos.Field_Settings_EAB_IsQuarterReset:               true,
			settingsRepos.Field_Settings_Quarter:                          util.NewPointer(int32(quarter)),
			settingsRepos.Field_Settings_Year:                             util.NewPointer(int32(year)),
			settingsRepos.Field_Signal:                                    "",
			settingsRepos.Field_Feature:                                   settings_common.SettingsFeatures_EABAutoBilling,
		},
	}, &options.FindOneAndUpdateOptions{
		Upsert: util.NewPointer(true),
	})
	if err != nil || setting.Id == nil {
		return nil, err
	}

	// update eab setting when reached a new quarter
	currentQuarter := util.GetPointerValue(setting.Settings.Quarter)
	if currentQuarter != int32(quarter) {
		newSettings, err := srv.settingRepo.Update(ctx, &settingsRepos.Settings[eab_common.EABSetting]{
			Id:      setting.Id,
			Feature: string(settings_common.SettingsFeatures_EABAutoBilling),
			Settings: eab_common.EABSetting{
				IsAutoSending:                false,
				IsAutoReceiving:              false,
				IsQuarterReset:               true,
				Quarter:                      util.NewPointer(int32(quarter)),
				Year:                         util.NewPointer(int32(year)),
				IsConsentTriggerForReceiving: false,
				IsConsentTriggerForSending:   false,
			},
			Signal: "",
		})
		if err != nil {
			return nil, err
		}

		return newSettings, nil
	}

	return setting, nil
}

func (srv *EABService) SaveSetting(ctx *titan.Context, request *SaveSettingRequest) (*SettingRepo, error) {
	setting, err := srv.GetSetting(ctx)
	if err != nil {
		return nil, err
	}
	filter := bson.M{
		settingsRepos.Field_Id: setting.Id,
	}
	update := bson.M{
		"$set": bson.M{
			settingsRepos.Field_Settings_EAB_IsAutoSending:   request.IsAutoSending,
			settingsRepos.Field_Settings_EAB_IsAutoReceiving: request.IsAutoReceiving,
			settingsRepos.Field_Settings_EAB_IsQuarterReset:  false,
		},
	}
	res, err := srv.settingRepo.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))

	return res, err
}

func (srv *EABService) SendMailWithTimelineStatusNotification(ctx *titan.Context, request eab_api.SendMailRequest) error {
	eab, err := srv.GetById(ctx, request.EABId)
	if err != nil {
		return err
	}

	if eab == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_EAB_RecordNotFound)
	}

	accountSetting, err := srv.mailService.GetMailSettingCheckCard(ctx, request.EmailItem.From.Address)
	if err != nil {
		return err
	}

	if accountSetting == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Email_Not_Found)
	}

	tiSetting, err := srv.tiService.GetByDeviceId(ctx, ctx.UserInfo().DeviceId)
	if err != nil {
		return err
	}

	account := mail_app.Account{
		User:           accountSetting.AccountInfor.Email,
		Password:       accountSetting.AccountInfor.PasswordHash,
		MandantId:      string(tiSetting.TiContext.MandantId),
		ClientSystemId: string(tiSetting.TiContext.ClientSystemId),
		WorkplaceId:    string(tiSetting.TiContext.WorkplaceId),
	}
	smtpConfig := mail_app.SmtpConfig{
		Host:     accountSetting.Provider.HostName,
		Port:     accountSetting.Provider.SmtpPort,
		FdServer: accountSetting.ClientModule.HostName,
	}

	companionMailService, err := srv.companionService.GetMailApp(ctx)
	if err != nil {
		return err
	}

	err = companionMailService.ConnectSMTPClient(ctx, mail_app.ConnectSmtp{
		Account:    account,
		SmtpConfig: smtpConfig,
	})
	if err != nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_KIM_Account_Invalid)
	}

	go func() {
		ctxClone := ctx.Clone()
		eabId := *eab.GetId()

		if err := srv.UpdateStatusById(ctxClone, UpdateStatusByIdRequest{
			Id:     eabId,
			Status: qes_common.Status_Sending,
		}); err != nil {
			ctxClone.Logger().Error("notify error. %w", err)
			return
		}

		err := srv.sendMail(ctxClone, SendMailRequest{
			EABId:     request.EABId,
			EmailItem: request.EmailItem,
			BsnrCode:  request.BsnrCode,
			MailId:    request.MailId,
		})
		if err != nil {
			ctxClone.Logger().Error("sending error. %w", err)
			if err := srv.UpdateStatusById(ctxClone, UpdateStatusByIdRequest{
				Id:     eabId,
				Status: qes_common.Status_SendingError,
			}); err != nil {
				ctxClone.Logger().Error("notify error. %w", err)
			}
			return
		}

		if err := srv.UpdateStatusById(ctxClone, UpdateStatusByIdRequest{
			Id:     eabId,
			Status: qes_common.Status_Sent,
		}); err != nil {
			ctxClone.Logger().Error("notify error. %w", err)
		}
	}()
	return nil
}

func (srv *EABService) AssignPatient(ctx *titan.Context, request AssignPatientRequest) (*AssignPatientResponse, error) {
	mailItem, err := srv.mailService.GetInboxById(ctx, request.MailId)
	if err != nil {
		return nil, err
	}

	if mailItem == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Email_Not_Found)
	}

	if !mailItem.IsEAB() {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_EAB_Mail_Invalid)
	}

	patientProfile, err := srv.patientProfileService.GetProfileById(ctx, &profile_service.GetByIdRequest{
		PatientId: &request.PatientId,
	})
	if err != nil {
		return nil, err
	}

	if patientProfile == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Patient_Not_Found)
	}

	selectedPatientInfo := patientProfile.PatientInfo
	if err := srv.mailService.AssignPatient(ctx, mail.AssignPatientRequest{
		Id:    *mailItem.GetId(),
		Inbox: true,
		Patient: &mail_common.Patient{
			Id:       request.PatientId,
			FistName: selectedPatientInfo.PersonalInfo.FirstName,
			LastName: selectedPatientInfo.PersonalInfo.LastName,
		},
	}); err != nil {
		return nil, err
	}

	if err := srv.CreateEABServiceHistory(ctx, CreateEABServiceHistoryRequest{
		PatientId:   request.PatientId,
		ServiceCode: eab_service_history_common.EABServiceCode_Receiving,
		MailId:      mailItem.Id,
	}); err != nil {
		return nil, err
	}

	return &AssignPatientResponse{
		PatientInfo: selectedPatientInfo,
	}, nil
}

func (srv *EABService) UnAssignPatientPatient(ctx *titan.Context, request UnAssignPatientRequest) (*UnAssignPatientResponse, error) {
	mailItem, err := srv.mailService.GetInboxById(ctx, request.MailId)
	if err != nil {
		return nil, err
	}

	if mailItem == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Email_Not_Found)
	}

	if !mailItem.IsEAB() {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_EAB_Mail_Invalid)
	}

	patientProfile, err := srv.patientProfileService.GetProfileById(ctx, &profile_service.GetByIdRequest{
		PatientId: &request.PatientId,
	})
	if err != nil {
		return nil, err
	}

	if patientProfile == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Patient_Not_Found)
	}

	selectedPatientInfo := patientProfile.PatientInfo
	if err := srv.mailService.AssignPatient(ctx, mail.AssignPatientRequest{
		Id:           *mailItem.GetId(),
		Inbox:        true,
		Patient:      nil,
		OldPatientId: &request.PatientId,
	}); err != nil {
		return nil, err
	}

	_, err = srv.eabServiceHistorySrv.RemoveHistoryAndServiceCodeEAB(ctx, eab_service_history_srv.DeleteEABServiceHistory{
		PatientId: request.PatientId,
		MailId:    mailItem.Id,
	})
	if err != nil {
		return nil, errors.Errorf("failed to delete eab service history. %s", err)
	}

	return &UnAssignPatientResponse{
		PatientInfo: selectedPatientInfo,
	}, nil
}

func (srv *EABService) PreparePatientCompare(ctx *titan.Context, request AssignPatientRequest) (*eab_common.PatientCompareData, error) {
	mailItem, err := srv.mailService.GetInboxById(ctx, request.MailId)
	if err != nil {
		return nil, err
	}

	if mailItem == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Email_Not_Found)
	}

	if !mailItem.IsEAB() {
		return nil, nil
	}

	patientProfile, err := srv.patientProfileService.GetProfileById(ctx, &profile_service.GetByIdRequest{
		PatientId: &request.PatientId,
	})
	if err != nil {
		return nil, err
	}

	if patientProfile == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Patient_Not_Found)
	}

	xmlPatientInfo, err := srv.GetPatientInfoFromAttachments(ctx, mailItem.Attachments)
	if err != nil {
		return nil, err
	}

	isExistSchein, err := srv.CheckExistScheinByPatientId(ctx, request.PatientId)
	if err != nil {
		return nil, err
	}

	selectedPatientInfo := patientProfile.PatientInfo

	return &eab_common.PatientCompareData{
		SelectedPatient:   selectedPatientInfo,
		XmlPatient:        xmlPatientInfo,
		IsExistSchein:     isExistSchein,
		SelectedPatientId: *patientProfile.Id,
	}, nil
}

func (srv *EABService) ReSendMail(ctx *titan.Context, doctorLetterId uuid.UUID) error {
	eabEntity, err := srv.eabRepo.FindByDoctorLetterId(ctx, doctorLetterId)
	if err != nil {
		return err
	}

	if eabEntity == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_EAB_RecordNotFound)
	}

	mailRes, err := srv.mailService.GetOutboxById(ctx, util.GetPointerValue(eabEntity.MailSentId))
	if err != nil {
		return err
	}

	if mailRes == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Email_Not_Found)
	}

	return srv.SendMailWithTimelineStatusNotification(ctx, eab_api.SendMailRequest{
		EABId:     *eabEntity.GetId(),
		EmailItem: mailRes.EmailItem,
		MailId:    mailRes.Id,
	})
}

func (srv *EABService) GetByDoctorLetterId(ctx *titan.Context, id uuid.UUID) (*eab_repo.EABEntity, error) {
	return srv.eabRepo.FindByDoctorLetterId(ctx, id)
}

func (srv *EABService) FindByDoctorLetterId(ctx *titan.Context, id uuid.UUID) (*eab_repo.EABEntity, error) {
	return srv.eabRepo.FindAllByDoctorLetterId(ctx, id)
}

func (srv *EABService) CheckExistInsuranceByIKNumber(ctx *titan.Context, ikNumber string) (*eab_api.CheckExistInsuranceByIKNumberResponse, error) {
	res, err := srv.catalogSdktService.SearchSdktByIKNumber(ctx, &catalog_sdkt_api.SearchSdktByIKNumberRequest{
		Value:        ikNumber,
		SelectedDate: util.NowUnixMillis(ctx),
	})
	if err != nil {
		return nil, err
	}

	if res == nil {
		return &eab_api.CheckExistInsuranceByIKNumberResponse{
			IsExist: false,
		}, nil
	}

	return &eab_api.CheckExistInsuranceByIKNumberResponse{
		IsExist:          true,
		InsuranceCompany: res.Data,
	}, nil
}

func (srv *EABService) CheckExistScheinByPatientId(ctx *titan.Context, patientId uuid.UUID) (bool, error) {
	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	res, err := srv.scheinRepo.FilterSchein(ctx, schein_repo.FilterScheinParams{
		PatientId:    util.NewPointer(patientId),
		G4101Quarter: util.NewPointer(yearQuarter.Quarter),
		G4101Year:    util.NewPointer(yearQuarter.Year),
	})
	if err != nil {
		return false, err
	}

	if len(res) == 0 {
		return false, nil
	}

	return true, nil
}

func (srv *EABService) GetConsentDocumenting(ctx *titan.Context, automaticDocumentingCase eab_common.AutomaticDocumentingCase) (*eab_common.EABSetting, error) {
	setting, err := srv.GetSetting(ctx)
	if err != nil {
		return nil, errors.Errorf("failed to get eab setting. %s", err)
	}

	if setting == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_EAB_SettingNotFound)
	}

	payload := bson.D{}
	if automaticDocumentingCase == eab_common.Sending {
		if setting.Settings.IsConsentTriggerForSending {
			return &setting.Settings, nil
		}
		payload = bson.D{{
			Key:   settingsRepos.Field_Settings_EAB_IsConsentTriggerForSending,
			Value: true,
		}}
	}

	if automaticDocumentingCase == eab_common.Receiving {
		if setting.Settings.IsConsentTriggerForReceiving {
			return &setting.Settings, nil
		}
		payload = bson.D{{
			Key:   settingsRepos.Field_Settings_EAB_IsConsentTriggerForReceiving,
			Value: true,
		}}
	}
	update := bson.M{
		"$set": payload,
	}
	filter := bson.D{{
		Key:   settingsRepos.Field_Id,
		Value: setting.Id,
	}}
	res, err := srv.settingRepo.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate())
	if err != nil {
		return nil, errors.Errorf("failed to update eab setting. %s", err)
	}

	if res == nil {
		return nil, nil
	}

	return &res.Settings, nil
}

func (srv *EABService) CreateEABServiceHistory(ctx *titan.Context, request CreateEABServiceHistoryRequest) error {
	setting, err := srv.GetSetting(ctx)
	if err != nil {
		return errors.Errorf("failed to get setting. %s", err)
	}

	if setting == nil || (request.ServiceCode == eab_service_history_common.EABServiceCode_Receiving && !setting.Settings.IsAutoReceiving) || (request.ServiceCode == eab_service_history_common.EABServiceCode_Sending && !setting.Settings.IsAutoSending) {
		return nil
	}

	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))

	payload := eab_service_history_common.EABServiceHistoryModel{
		PatientId:   request.PatientId,
		Year:        yearQuarter.Year,
		Quarter:     yearQuarter.Quarter,
		ServiceCode: request.ServiceCode,
	}

	if request.MailId != nil {
		payload = eab_service_history_common.EABServiceHistoryModel{
			PatientId:   request.PatientId,
			Year:        yearQuarter.Year,
			Quarter:     yearQuarter.Quarter,
			ServiceCode: request.ServiceCode,
			MailId:      request.MailId,
		}
	}

	_, err = srv.eabServiceHistorySrv.Create(ctx, payload)
	if err != nil {
		return errors.Errorf("failed to create eab service history. %s", err)
	}

	return nil
}

func (srv *EABService) UpdateEabStateForBsnr(ctx *titan.Context, request UpdateEabStateForBsnrRequest) error {
	bsnr, err := srv.bsnrService.FindByCode(ctx, request.BsnrCode)
	if err != nil {
		return errors.WithStack(err)
	}
	if bsnr == nil {
		return nil
	}
	if bsnr.EArztbrief.IsDetectedOnCurrentQuarter(ctx) {
		return nil
	}

	now := util.NowUnixMillis(ctx)
	yearQuarter := util.ToYearQuarter(now)
	quarterRange, err := yearQuarter.GetQuarterDateRange()
	if err != nil {
		return errors.WithStack(err)
	}

	startQuarter := util.GetPointerValue(quarterRange.Start)
	endQuarter := util.GetPointerValue(quarterRange.End)
	eABMailCount, err := srv.mailService.CountMailOutboxInRange(ctx, startQuarter, endQuarter, util.NewString(string(mail_common.MailCategory_EAB)))
	if err != nil {
		return err
	}

	prevQuarter := util.GetPreviousQuarterFrom(1, util.ToYearQuarter(now))
	billingHistory, err := srv.kvBillingHistoryRepo.GetLatestKvBillingOnQuarter(ctx, bsnr.Id, prevQuarter.Year, prevQuarter.Quarter)
	if err != nil {
		return err
	}

	bsnr.EArztbrief = function.Do(func() bsnr_common.TiComponent {
		prev := false
		if billingHistory != nil {
			prev = billingHistory.TiInfo.EArztbrief.Value
		}
		hasEABMailSent := eABMailCount > 0

		return srv.bsnrService.SelectCurrentTiComponentOnDetect(ctx, prev, hasEABMailSent)
	})
	err = srv.bsnrService.UpdateTiComponents(ctx, bsnr)

	return err
}

func (srv *EABService) GetPatientXMLData(ctx *titan.Context, mailId uuid.UUID) (*patient_profile_common.PatientInfo, error) {
	mailItem, err := srv.mailService.GetInboxById(ctx, mailId)
	if err != nil {
		return nil, err
	}

	if mailItem == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Email_Not_Found)
	}

	if !mailItem.IsEAB() {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_EAB_Mail_Invalid)
	}

	xmlPatientInfo, err := srv.GetPatientInfoFromAttachments(ctx, mailItem.Attachments)
	if err != nil {
		return nil, err
	}

	if len(xmlPatientInfo.InsuranceInfos) == 0 {
		return xmlPatientInfo, nil
	}

	ikNumber := xmlPatientInfo.InsuranceInfos[0].IkNumber

	ikNumberString := strconv.FormatInt(int64(ikNumber), 10)

	res, err := srv.catalogSdktService.SearchSdktByIKNumber(ctx, &catalog_sdkt_api.SearchSdktByIKNumberRequest{
		Value:        ikNumberString,
		SelectedDate: util.NowUnixMillis(ctx),
	})
	if err != nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_SearchSdkt)
	}

	// Insurance not found in SDKT allow to user continue go to Create Patient screen
	// PRO-13328 [BUG] Do not pre-fill patient's data with an insurance with an unknown IK number
	if res == nil || res.Data == nil {
		xmlPatientInfo.InsuranceInfos = nil
		return xmlPatientInfo, nil
	}

	insuranceInfo := xmlPatientInfo.InsuranceInfos[0]
	newInsurance, err := srv.setInsuranceCompanyInfo(ctx, insuranceInfo, res)
	if err != nil {
		return xmlPatientInfo, err
	}

	xmlPatientInfo.InsuranceInfos[0] = *newInsurance

	return xmlPatientInfo, nil
}

func (srv *EABService) GetEAB(ctx *titan.Context, request GetEABRequest) ([]eab_common.EABModel, *int64, error) {
	res, total, err := srv.eabRepo.FindByFilter(ctx, eab_repo.FindByFilterRequest{
		DateRange:         request.DateRange,
		PaginationRequest: request.PaginationRequest,
		Statuses:          request.Statuses,
		Query:             request.Query,
	})
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}

	if res == nil {
		return nil, nil, nil
	}

	return slice.Map(res, func(item eab_repo.EABEntity) eab_common.EABModel {
		return eab_common.EABModel{
			Id:             item.GetId(),
			PatientProfile: item.PatientProfile,
			Receiver:       item.Receiver,
			Status:         item.Status,
			CreatedAt:      util.NewPointer(util.ConvertTimeToMiliSecond(item.BaseEntity.CreatedAt)),
			MailSentId:     item.MailSentId,
			PDFUrl:         item.PDFUrl,
			XmlFile:        item.XmlFile,
			SignedFile:     item.SignedFile,
			DoctorLetterId: item.DoctorLetterId,
		}
	}), &total, nil
}

func (srv *EABService) DeleteByIdSyncTimeline(ctx *titan.Context, request DeleteByIdRequest) error {
	res, err := srv.eabRepo.DeleteById(ctx, request.Id)
	if err != nil {
		return err
	}

	if res == nil {
		return nil
	}

	return srv.timelineDocterLetterService.DeleteDoctorLetterByPayloadId(ctx, res.DoctorLetterId)
}

func (srv *EABService) DeleteById(ctx *titan.Context, id uuid.UUID) error {
	_, err := srv.eabRepo.DeleteById(ctx, id)
	if err != nil {
		return err
	}
	return nil
}

func (srv *EABService) DeleteByPatientId(ctx *titan.Context, id uuid.UUID) error {
	_, err := srv.eabRepo.DeleteByPatientId(ctx, id)
	if err != nil {
		return err
	}
	return srv.eabServiceHistorySrv.DeleteByPatientId(ctx, id)
}

func (srv *EABService) RestoreById(ctx *titan.Context, id uuid.UUID) error {
	_, err := srv.eabRepo.RestoreById(ctx, id)
	if err != nil {
		return err
	}
	return nil
}

func (srv *EABService) UpdateStatusById(ctx *titan.Context, request UpdateStatusByIdRequest) error {
	res, err := srv.eabRepo.UpdateStatusById(ctx, eab_repo.UpdateEABStatusRequest{
		Id:     request.Id,
		Status: request.Status,
	})
	if err != nil {
		return err
	}

	if res == nil {
		return errors.New("update status failed")
	}

	if err := srv.eabSocketNotifier.NotifyCareProviderEABChanged(ctx, &eab_api.EventEABChanged{
		Model:     []eab_common.EABModel{res.EABModel},
		EventType: eab_common.EABEventType_Update,
	}); err != nil {
		return err
	}

	timelineModel, err := srv.timelineDocterLetterService.UpdateDoctorLetterStatusByPayloadId(ctx, res.DoctorLetterId, request.Status)
	if err != nil {
		return err
	}

	if timelineModel == nil {
		return errors.New("update timeline failed")
	}

	return srv.timelineDocterLetterService.NotifyTimelineUpdate(ctx, &timeline.EventTimelineUpdate{
		PatientId:     timelineModel.PatientId,
		TimelineModel: *timelineModel,
	})
}

func (srv *EABService) onDeleteTimeline(ctx *titan.Context, event *timeline.EventTimelineRemove) error {
	if event == nil || event.TimelineModel.DoctorLetter == nil {
		return nil
	}

	doctorLetterId := util.GetPointerValue(event.TimelineModel.DoctorLetter.Id)
	res, err := srv.eabRepo.DeleteByDocterLetterId(ctx, doctorLetterId)
	if err != nil {
		return err
	}

	// not e-doctor-letter
	if res == nil {
		return nil
	}

	if err := srv.eabSocketNotifier.NotifyCareProviderEABChanged(ctx, &eab_api.EventEABChanged{
		Model:     []eab_common.EABModel{res.EABModel},
		EventType: eab_common.EABEventType_Delete,
	}); err != nil {
		return err
	}

	return nil
}
