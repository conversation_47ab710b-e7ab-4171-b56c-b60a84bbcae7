// This code was autogenerated from service/domains/form_common.proto, do not edit.

package common

import (
	"encoding/json"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	common "git.tutum.dev/medi/tutum/ares/service/domains/api/common"

	common1 "git.tutum.dev/medi/tutum/ares/service/domains/eau/common"

	common2 "git.tutum.dev/medi/tutum/ares/service/domains/qes/common"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type Form struct {
	Id                  string          `json:"id"`
	FormTab             bool            `json:"formTab"`
	Size                FormSize        `json:"size"`
	Orientation         FormOrientation `json:"orientation"`
	AdditionalDocs      *[]string       `json:"additionalDocs"`
	Actions             []FormAction    `json:"actions"`
	FormType            *FormType       `json:"formType"`
	Title               string          `json:"title"`
	FileNameWithVersion string          `json:"fileNameWithVersion"`
	IsDuplex            bool            `json:"isDuplex"`
	PrintTypes          []PrintType     `json:"printTypes"`
	HasRefill           bool            `json:"hasRefill"`
	IsHzvFav            bool            `json:"isHzvFav"`
}

type Prescribe struct {
	Id                       *uuid.UUID              `json:"id"`
	DoctorId                 *uuid.UUID              `json:"doctorId" validate:"required"`
	TreatmentDoctorId        *uuid.UUID              `json:"treatmentDoctorId" validate:"required"`
	PatientId                *uuid.UUID              `json:"patientId" validate:"required"`
	CreatedDate              int64                   `json:"createdDate"`
	UpdatedDate              *int64                  `json:"updatedDate"`
	PrintedDate              *int64                  `json:"printedDate"`
	Payload                  string                  `json:"payload" validate:"required,trimjson"`
	FormName                 FormName                `json:"formName" validate:"required"`
	EncounterCase            string                  `json:"encounterCase"`
	ContractType             common.ContractType     `json:"contractType"`
	ContractId               *string                 `json:"contractId"`
	ScheinId                 *uuid.UUID              `json:"scheinId"`
	PrescribeDate            int64                   `json:"prescribeDate"`
	UpdatedBy                *uuid.UUID              `json:"updatedBy"`
	PdfUrl                   *[]string               `json:"pdfUrl"`
	EAUStatus                *common2.DocumentStatus `json:"eAUStatus"`
	EAUSetting               *common1.EAUSetting     `json:"eAUSetting"`
	FormTitle                *string                 `json:"formTitle"`
	EAUBundleUrl             *string                 `json:"eAUBundleUrl"`
	IsImported               *bool                   `json:"isImported"`
	IsTerminated             *bool                   `json:"isTerminated"`
	EAUCancellationBundleUrl *string                 `json:"eAUCancellationBundleUrl"`
	AssignedToBsnrId         *uuid.UUID              `json:"assignedToBsnrId"`
}

type PrintOption struct {
	DateOfPrint          int64      `json:"dateOfPrint"`
	PdfWithBackground    bool       `json:"pdfWithBackground"`
	FormAction           FormAction `json:"formAction"`
	PreventAddToTimeline *bool      `json:"preventAddToTimeline"`
}

type PrintResult struct {
	FormName FormName `json:"formName"`
	FormUrl  string   `json:"formUrl"`
}

// enum definitions
type FormCategory string

const (
	FormCategory_A4_portrait  FormCategory = "A4_portrait"
	FormCategory_A4_landscape FormCategory = "A4_landscape"
	FormCategory_A5_portrait  FormCategory = "A5_portrait"
	FormCategory_A5_landscape FormCategory = "A5_landscape"
	FormCategory_A6_portrait  FormCategory = "A6_portrait"
	FormCategory_A6_landscape FormCategory = "A6_landscape"
)

type FormType string

const (
	FormType_medication           FormType = "medication"
	FormType_heimi                FormType = "heimi"
	FormType_himi                 FormType = "himi"
	FormType_lab                  FormType = "lab"
	FormType_generic_form         FormType = "generic_form"
	FormType_public_document      FormType = "public_document"
	FormType_public_contract_text FormType = "public_contract_text"
	FormType_contract_hint        FormType = "contract_hint"
	FormType_diga                 FormType = "diga"
)

type FormSize string

const (
	FormSize_A4 FormSize = "A4"
	FormSize_A5 FormSize = "A5"
	FormSize_A6 FormSize = "A6"
)

type FormOrientation string

const (
	PrintOrientation_portrait  FormOrientation = "portrait"
	PrintOrientation_landscape FormOrientation = "landscape"
)

type PrintType string

const (
	PrintType_formPrint PrintType = "formPrint"
	PrintType_fullPrint PrintType = "fullPrint"
)

type FormAction string

const (
	FormAction_PrintFull            FormAction = "FormAction_PrintFull"
	FormAction_PrintHeader          FormAction = "FormAction_PrintHeader"
	FormAction_PrintWithoutContent  FormAction = "FormAction_PrintWithoutContent"
	FormAction_OpenNewTab           FormAction = "FormAction_OpenNewTab"
	FormAction_PrintOnly            FormAction = "FormAction_PrintOnly"
	FormAction_PrintWithBSNRAndLANR FormAction = "FormAction_PrintWithBSNRAndLANR"
)

type EAUFormType string

const (
	FormType_V  EAUFormType = "v"
	FormType_KK EAUFormType = "kk"
	FormType_AG EAUFormType = "ag"
)

type FormName string

const (
	Muster_8                                                                  FormName = "Muster_8"
	Muster_8A                                                                 FormName = "Muster_8A"
	Muster_16                                                                 FormName = "Muster_16"
	Muster_15                                                                 FormName = "Muster_15"
	Muster_1                                                                  FormName = "Muster_1"
	Muster_6                                                                  FormName = "Muster_6"
	Muster_10                                                                 FormName = "Muster_10"
	Muster_10A                                                                FormName = "Muster_10A"
	Muster_13                                                                 FormName = "Muster_13"
	Muster_39A                                                                FormName = "Muster_39A"
	Muster_2B                                                                 FormName = "Muster_2B"
	Muster_4                                                                  FormName = "Muster_4"
	Muster_2A                                                                 FormName = "Muster_2A"
	KREZ                                                                      FormName = "Muster_16"
	GREZ                                                                      FormName = "Gruenes_Rezept"
	BTM                                                                       FormName = "Btm_Rezept_Print"
	TPrescription                                                             FormName = "T-Rezept-Muster"
	Private                                                                   FormName = "Blaues_Rezept"
	Muster_6_cover_letter                                                     FormName = "Muster_6_cover_letter"
	Muster_2C                                                                 FormName = "Muster_2C"
	Muster_3A                                                                 FormName = "Muster_3A"
	Muster_3B                                                                 FormName = "Muster_3B"
	Muster_5                                                                  FormName = "Muster_5"
	Muster_9                                                                  FormName = "Muster_9"
	Muster_21                                                                 FormName = "Muster_21"
	Muster_19A                                                                FormName = "Muster_19A"
	Muster_36_E_2017_07                                                       FormName = "Muster_36_E_2017_07"
	Muster_12A                                                                FormName = "Muster_12A"
	Muster_28A                                                                FormName = "Muster_28A"
	Muster_28B                                                                FormName = "Muster_28B"
	Muster_28C                                                                FormName = "Muster_28C"
	Muster_52_0_V2                                                            FormName = "Muster_52_0_V2"
	Muster_12B                                                                FormName = "Muster_12B"
	Muster_12C                                                                FormName = "Muster_12C"
	Muster_55                                                                 FormName = "Muster_55"
	Muster_10C                                                                FormName = "Muster_10C"
	Muster_20A                                                                FormName = "Muster_20A"
	Muster_20B                                                                FormName = "Muster_20B"
	Muster_20C                                                                FormName = "Muster_20C"
	Muster_20D                                                                FormName = "Muster_20D"
	Muster_65A                                                                FormName = "Muster_65A"
	Muster_65B                                                                FormName = "Muster_65B"
	Muster_70                                                                 FormName = "Muster_70"
	Muster_70_B                                                               FormName = "Muster_70_B"
	Muster_70A                                                                FormName = "Muster_70A"
	Muster_70A_B                                                              FormName = "Muster_70A_B"
	Muster_N63A                                                               FormName = "Muster_N63A"
	Muster_N63B                                                               FormName = "Muster_N63B"
	Muster_N63C                                                               FormName = "Muster_N63C"
	Muster_N63D                                                               FormName = "Muster_N63D"
	Muster_61A                                                                FormName = "Muster_61A"
	Muster_61B                                                                FormName = "Muster_61B"
	Muster_64                                                                 FormName = "Muster_64"
	Muster_64B                                                                FormName = "Muster_64B"
	Muster_19B                                                                FormName = "Muster_19B"
	Muster_19C                                                                FormName = "Muster_19C"
	Muster_56                                                                 FormName = "Muster_56"
	Muster_61                                                                 FormName = "Muster_61"
	Muster_PTV_11A                                                            FormName = "Muster_PTV_11A"
	Muster_PTV_11B                                                            FormName = "Muster_PTV_11B"
	Muster_PTV_3                                                              FormName = "Muster_PTV_3"
	Muster_PTV_10                                                             FormName = "Muster_PTV_10"
	G81_EHIC_Bulgarisch                                                       FormName = "G81_EHIC_Bulgarisch"
	G81_EHIC_Danisch                                                          FormName = "G81_EHIC_Danisch"
	G81_EHIC_Englisch                                                         FormName = "G81_EHIC_Englisch"
	G81_EHIC_Franzosisch                                                      FormName = "G81_EHIC_Franzosisch"
	G81_EHIC_Griechisch                                                       FormName = "G81_EHIC_Griechisch"
	G81_EHIC_Italienisch                                                      FormName = "G81_EHIC_Italienisch"
	G81_EHIC_Kroatisch                                                        FormName = "G81_EHIC_Kroatisch"
	G81_EHIC_Niederlandisch                                                   FormName = "G81_EHIC_Niederlandisch"
	G81_EHIC_Polnisch                                                         FormName = "G81_EHIC_Polnisch"
	G81_EHIC_Rumanisch                                                        FormName = "G81_EHIC_Rumanisch"
	G81_EHIC_Spanisch                                                         FormName = "G81_EHIC_Spanisch"
	G81_EHIC_Tschechisch                                                      FormName = "G81_EHIC_Tschechisch"
	G81_EHIC_Ungarisch                                                        FormName = "G81_EHIC_Ungarisch"
	Muster_16A                                                                FormName = "Muster_16A"
	Muster_16A_Bay                                                            FormName = "Muster_16a_bay"
	G81_EHIC_All                                                              FormName = "G81_EHIC_All"
	DMP_Enrollment_Form                                                       FormName = "DMP_Enrollment_Form"
	PHQ_9_Q3_2023                                                             FormName = "PHQ_9_Q3_2023"
	Muster_7                                                                  FormName = "Muster_7"
	Muster_11                                                                 FormName = "Muster_11"
	Muster_50                                                                 FormName = "Muster_50"
	Muster_51                                                                 FormName = "Muster_51"
	G81_EHIC_Finnisch                                                         FormName = "G81_EHIC_Finnisch"
	G81_EHIC_Estnisch                                                         FormName = "G81_EHIC_Estnisch"
	G81_EHIC_Slowenisch                                                       FormName = "G81_EHIC_Slowenisch"
	G81_EHIC_Slowakisch                                                       FormName = "G81_EHIC_Slowakisch"
	G81_EHIC_Schwedisch                                                       FormName = "G81_EHIC_Schwedisch"
	G81_EHIC_Portugiesisch                                                    FormName = "G81_EHIC_Portugiesisch"
	G81_EHIC_Litauisch                                                        FormName = "G81_EHIC_Litauisch"
	G81_EHIC_Lettisch                                                         FormName = "G81_EHIC_Lettisch"
	Muster_22A                                                                FormName = "Muster_22A"
	Muster_22B                                                                FormName = "Muster_22B"
	Muster_22C                                                                FormName = "Muster_22C"
	Muster_22D                                                                FormName = "Muster_22D"
	Muster_26A                                                                FormName = "Muster_26A"
	Muster_26B                                                                FormName = "Muster_26B"
	Muster_26C                                                                FormName = "Muster_26C"
	Muster_27A                                                                FormName = "Muster_27A"
	Muster_27B                                                                FormName = "Muster_27B"
	Muster_27C                                                                FormName = "Muster_27C"
	Muster_PTV_1A                                                             FormName = "Muster_PTV_1A"
	Muster_PTV_1B                                                             FormName = "Muster_PTV_1B"
	Muster_PTV_1C                                                             FormName = "Muster_PTV_1C"
	Muster_PTV_12A                                                            FormName = "Muster_PTV_12A"
	Muster_PTV_12B                                                            FormName = "Muster_PTV_12B"
	Muster_PTV_2A                                                             FormName = "Muster_PTV_2A"
	Muster_PTV_2B                                                             FormName = "Muster_PTV_2B"
	Muster_PTV_2C                                                             FormName = "Muster_PTV_2C"
	Muster_39B                                                                FormName = "Muster_39B"
	Muster_61C                                                                FormName = "Muster_61C"
	Muster_61D                                                                FormName = "Muster_61D"
	AOK_FA_NPPP_BW                                                            FormName = "AOK_FA_NPPP_BW"
	BKK_BOSCH_FA_BW                                                           FormName = "BKK_BOSCH_FA_BW"
	MEDI_FA_PT_BW                                                             FormName = "MEDI_FA_PT_BW"
	HIMI_QUESTION_NAME                                                        FormName = "HIMI_QUESTION_NAME"
	BKK_VAG_BW_Schnellinformation_Patientenbegleitung_V4                      FormName = "BKK_VAG_BW_Schnellinformation_Patientenbegleitung_V4"
	BKK_VAG_FA_BW_Versichertenteilnahmeerklaerung_V5                          FormName = "BKK_VAG_FA_BW_Versichertenteilnahmeerklaerung_V5"
	Muster_52_2_V3                                                            FormName = "Muster_52_2_V3"
	HIMIFB0399051_V3                                                          FormName = "HIMIFB0399051_V3"
	HIMIFB0399054_V3                                                          FormName = "HIMIFB0399054_V3"
	HIMIFB039906_V3                                                           FormName = "HIMIFB039906_V3"
	HIMIFB0440_V3                                                             FormName = "HIMIFB0440_V3"
	HIMIFB1129_V3                                                             FormName = "HIMIFB1129_V3"
	HIMIFB1424_V3                                                             FormName = "HIMIFB1424_V3"
	HIMIFB1525192_V3                                                          FormName = "HIMIFB1525192_V3"
	HIMIFB1846ER_V3                                                           FormName = "HIMIFB1846ER_V3"
	HIMIFB1865_V3                                                             FormName = "HIMIFB1865_V3"
	HIMIFB1940_V3                                                             FormName = "HIMIFB1940_V3"
	HIMIFB213401_V4                                                           FormName = "HIMIFB213401_V4"
	HIMIFB31_V3                                                               FormName = "HIMIFB31_V3"
	BKK_VAG_FA_BW_TE_HepCModul_V2                                             FormName = "BKK_VAG_FA_BW_TE_HepCModul_V2"
	TK_HZV_Versichertenteilnahmeerklaerung_V9                                 FormName = "TK_HZV_Versichertenteilnahmeerklaerung_V9"
	HZV_Beleg_Muster_V3                                                       FormName = "HZV_Beleg_Muster_V3"
	SI_IKK_HZV_Versichertenteilnahmeerklaerung_V7                             FormName = "SI_IKK_HZV_Versichertenteilnahmeerklaerung_V7"
	RV_KBS_HZV_Versichertenteilnahmeerklaerung_V4                             FormName = "RV_KBS_HZV_Versichertenteilnahmeerklaerung_V4"
	Ueberleitungsbogen_AOK_KBS_NO_WL_V2                                       FormName = "Ueberleitungsbogen_AOK_KBS_NO_WL_V2"
	RV_KBS_SN_HZV_Ueberleitungsmanagement_Ueberleitungsbogen_V3               FormName = "RV_KBS_SN_HZV_Ueberleitungsmanagement_Ueberleitungsbogen_V3"
	RV_KBS_BW_HZV_Versichertenteilnahmeerklaerung_V5                          FormName = "RV_KBS_BW_HZV_Versichertenteilnahmeerklaerung_V5"
	BKK_VAG_FA_PT_BW_Versichertenteilnahmeerklaerung_V17                      FormName = "BKK_VAG_FA_PT_BW_Versichertenteilnahmeerklaerung_V17"
	BKK_VAG_FA_PT_BW_GDK_Antragsformular_V3                                   FormName = "BKK_VAG_FA_PT_BW_GDK_Antragsformular_V3"
	BKK_VAG_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2                           FormName = "BKK_VAG_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2"
	BKK_VAG_FA_PT_BW_Ausschreibeformular_V5                                   FormName = "BKK_VAG_FA_PT_BW_Ausschreibeformular_V5"
	BKK_GWQ_FA_PT_BW_Ausschreibeformular_V2                                   FormName = "BKK_GWQ_FA_PT_BW_Ausschreibeformular_V2"
	BKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2                           FormName = "BKK_GWQ_FA_PT_BW_Bericht_Hausarzt_Psychiater_V2"
	BKK_GWQ_FA_PT_BW_GDK_Antragsformular_V2                                   FormName = "BKK_GWQ_FA_PT_BW_GDK_Antragsformular_V2"
	BKK_GWQ_FA_PT_BW_Versichertenteilnahmeerklaerung_V2                       FormName = "BKK_GWQ_FA_PT_BW_Versichertenteilnahmeerklaerung_V2"
	LKK_Teilnahme_und_Einwilligungserklaerung_V7                              FormName = "LKK_Teilnahme-und Einwilligungserklaerung_V7"
	LKK_BY_HZV_Muster_Versicherteneinschreibebeleg_V3                         FormName = "LKK_BY_HZV_Muster_Versicherteneinschreibebeleg_V3"
	Praxisuebergabe_V1                                                        FormName = "Praxisuebergabe_V1"
	LKK_BY_Teilnahme_und_Einwilligungserklaerung_V4                           FormName = "LKK_BY_Teilnahme-und Einwilligungserklaerung_V4"
	LKK_BW_HZV_VersichertenTeilnahmeerklaerung_V15                            FormName = "LKK_BW_HZV_VersichertenTeilnahmeerklaerung_V15"
	IKK_CL_BW_HZV_Versichertenteilnahmeerklaerung_V12                         FormName = "IKK_CL_BW_HZV_Versichertenteilnahmeerklaerung_V12"
	HKK_HZV_NORD_Versichertenteilnahmeerklaerung_V4                           FormName = "HKK_HZV_NORD_Versichertenteilnahmeerklaerung_V4"
	EK_WL_HZV_Versichertenteilnahmeerklaerung_V2                              FormName = "EK_WL_HZV_Versichertenteilnahmeerklaerung_V2"
	Ueberleitungsbogen_EK_BKK_NO_WL_V1                                        FormName = "Ueberleitungsbogen_EK_BKK_NO_WL_V1"
	EK_SN_HZV_Versichertenteilnahmeerklaerung_V3                              FormName = "EK_SN_HZV_Versichertenteilnahmeerklaerung_V3"
	EK_HZV_BARMER_DAK_Versichertenteilnahmeerklaerung_V3                      FormName = "EK_HZV_BARMER_DAK_Versichertenteilnahmeerklaerung_V3"
	EK_RLP_HZV_Versichertenteilnahmeerklaerung_V5                             FormName = "EK_RLP_HZV_Versichertenteilnahmeerklaerung_V5"
	EK_NO_HZV_Versichertenteilnahmeerklaerung_V2                              FormName = "EK_NO_HZV_Versichertenteilnahmeerklaerung_V2"
	EK_FA_DIA_BW_Versichertenteilnahmeerklaerung_V1                           FormName = "EK_FA_DIA_BW_Versichertenteilnahmeerklaerung_V1"
	EK_BY_HZV_S12_Versichertenteilnahmeerklaerung_V7                          FormName = "EK_BY_HZV_S12_Versichertenteilnahmeerklaerung_V7"
	Versichertenteilnahmeerklaerung_Online_EK_BW_HZV_V5                       FormName = "Versichertenteilnahmeerklaerung_Online_EK_BW_HZV_V5"
	EK_BLN_HZV_Versichertenteilnahmeerklaerung_V4                             FormName = "EK_BLN_HZV_Versichertenteilnahmeerklaerung_V4"
	DAK_HZV_VersichertenTeilnahmeerklaerung_V4                                FormName = "DAK_HZV_VersichertenTeilnahmeerklaerung_V4"
	Versichertenteilnahmeerklaerung_Online_BKK_GWQ_HZV_V8                     FormName = "Versichertenteilnahmeerklaerung_Online_BKK_GWQ_HZV_V8"
	BKK_VAG_HE_Versichertenteilnahmeerklaerung_V4                             FormName = "BKK_VAG_HE_Versichertenteilnahmeerklaerung_V4"
	BKK_VAG_HE_Schnellinformation_Patientenbegleitung_V1                      FormName = "BKK_VAG_HE_Schnellinformation_Patientenbegleitung_V1"
	BKK_BOSCH_VAG_BW_Praeventionsverordnung_V1                                FormName = "BKK_BOSCH_VAG_BW_Praeventionsverordnung_V1"
	Begleitschreiben_FaV_V4                                                   FormName = "Begleitschreiben_FaV_V4"
	Versichertenteilnahmeerklaerung_Online_Variante_A_V11                     FormName = "Versichertenteilnahmeerklaerung_Online_Variante_A_V11"
	Versichertenteilnahmeerklaerung_Online_BKK_SPECTRUM_HZV_V5                FormName = "Versichertenteilnahmeerklaerung_Online_BKK_SPECTRUM_HZV_V5"
	Ambulantes_Operieren_V1                                                   FormName = "Ambulantes_Operieren_V1"
	AOK_FA_URO_BW_BKK_FA_URO_BW_Uebertragung_Honorar_Anaesthesist_V4          FormName = "AOK_FA_URO_BW_BKK_FA_URO_BW_Uebertragung_Honorar_Anaesthesist_V4"
	BKK_BOSCH_FA_BW_Versichertenteilnahmeerklaerung_V11                       FormName = "BKK_BOSCH_FA_BW_Versichertenteilnahmeerklaerung_V11"
	BKK_BOSCH_BW_Schnellinfo_Patientenbegleitung_V6                           FormName = "BKK_BOSCH_BW_Schnellinfo_Patientenbegleitung_V6"
	AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Arthrose_V3                         FormName = "AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Arthrose_V3"
	AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_entzuendliche_Gelenkerkrankungen_V3 FormName = "AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_entzuendliche_Gelenkerkrankungen_V3"
	AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Grundversorgung_V3                  FormName = "AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Grundversorgung_V3"
	AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_V3                      FormName = "AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_V3"
	AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Rueckenschmerz_V3                   FormName = "AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Rueckenschmerz_V3"
	AOK_FA_OC_BW_BKK_FA_OC_BW_Uebertragung_Honorar_Anaesthesist_V5            FormName = "AOK_FA_OC_BW_BKK_FA_OC_BW_Uebertragung_Honorar_Anaesthesist_V5"
	BKK_BOSCH_Beratungsbogen_Einbindung_PBG_V10                               FormName = "BKK_BOSCH_Beratungsbogen_Einbindung_PBG_V10"
	BKK_BOSCH_FA_TE_HepCModul_V4                                              FormName = "BKK_BOSCH_FA_TE_HepCModul_V4"
	BKK_BOSCH_FA_GASTRO_BW_Maviret_Teilnahmeerklaerung_V3                     FormName = "BKK_BOSCH_FA_GASTRO_BW_Maviret_Teilnahmeerklaerung_V3"
	BKK_BOSCH_FA_GASTRO_BW_Epclusa_Teilnahmeerklaerung_V2                     FormName = "BKK_BOSCH_FA_GASTRO_BW_Epclusa_Teilnahmeerklaerung_V2"
	Versichertenteilnahmeerklaerung_Online_BKK_BY_HZV_V6                      FormName = "Versichertenteilnahmeerklaerung_Online_BKK_BY_HZV_V6"
	BKK_BY_HZV_Notfallplan_geriatrischer_Patient_V1                           FormName = "BKK_BY_HZV_Notfallplan_geriatrischer_Patient_V1"
	BKK_BY_HZV_Schnellinfo_Patientenbegleitung_V6                             FormName = "BKK_BY_HZV_Schnellinfo_Patientenbegleitung_V6"
	BKK_BW_HZV_Versichertenteilnahmeerklaerung_V9                             FormName = "BKK_BW_HZV_Versichertenteilnahmeerklaerung_V9"
	BKK_BOSCH_FA_BW_GDK_Antragsformular_V4                                    FormName = "BKK_BOSCH_FA_BW_GDK_Antragsformular_V4"
	AWH_01_Checkliste_Somatik_V1                                              FormName = "AWH_01_Checkliste_Somatik_V1"
	AWH_01_Checkliste_Psychosomatik_V1                                        FormName = "AWH_01_Checkliste_Psychosomatik_V1"
	AWH_01_Kurzantrag_HZV_KinderReha_V1                                       FormName = "AWH_01_Kurzantrag_HZV-KinderReha_V1"
	AWH_01_BVKJ_Anlage_7b_Osteopathie_V2                                      FormName = "AWH_01_BVKJ_Anlage_7b_Osteopathie_V2"
	AWH_01_Patientenfragebogen_AOK_Check_18_V2                                FormName = "AWH_01_Patientenfragebogen_AOK-Check 18+_V2"
	AWH_01_Versichertenteilnahmeerklaerung_Volldruck_V12                      FormName = "AWH_01_Versichertenteilnahmeerklaerung_Volldruck_V12"
	AOK_BW_Beratungsbogen_Einbindung_SD_V7                                    FormName = "AOK_BW_Beratungsbogen_Einbindung_SD_V7"
	AOK_FaV_Versichertenteilnahmeerklaerung_Volldruck_V9                      FormName = "AOK_FaV_Versichertenteilnahmeerklaerung_Volldruck_V9"
	AOK_BW_IV_P_Versichertenteilnahmeerklaerung_Formulardruck_V3              FormName = "AOK_BW_IV_P_Versichertenteilnahmeerklaerung_Formulardruck_V3"
	AOK_WL_HZV_Versichertenteilnahmeerklaerung_V8                             FormName = "AOK_WL_HZV_Versichertenteilnahmeerklaerung_V8"
	AOK_SH_HZV_Ueberleitungsmanagement_V3                                     FormName = "AOK_SH_HZV_Ueberleitungsmanagement_V3"
	AOK_SH_HZV_Versichertenteilnahmeerklaerung_V7                             FormName = "AOK_SH_HZV_Versichertenteilnahmeerklaerung_V7"
	AOK_RP_HZV_Versichertenteilnahmeerklaerung_V4                             FormName = "AOK_RP_HZV_Versichertenteilnahmeerklaerung_V4"
	AOK_PLUS_Versichertenteilnahmeerklaerung_V6                               FormName = "AOK_PLUS_Versichertenteilnahmeerklaerung_V6"
	AOK_NO_HH_Versichertenteilnahmeerklaerung_V5                              FormName = "AOK_NO_HH_Versichertenteilnahmeerklaerung_V5"
	AOK_IKK_BLN_HZV_Versichertenteilnahmeerklaerung_V11                       FormName = "AOK_IKK_BLN_HZV_Versichertenteilnahmeerklaerung_V11"
	AOK_IKK_BLN_HZV_Muster_Versicherteneinschreibebeleg_V2                    FormName = "AOK_IKK_BLN_HZV_Muster_Versicherteneinschreibebeleg_V2"
	AOK_HH_HZV_Ueberleitungsbogen_V2                                          FormName = "AOK_HH_HZV_Ueberleitungsbogen_V2"
	AOK_HE_HZV_Versichertenteilnahmeerklaerung_V12                            FormName = "AOK_HE_HZV_Versichertenteilnahmeerklaerung_V12"
	AOK_FA_NPPP_BW_GDK_KJPY_Antragsformular_V3                                FormName = "AOK_FA_NPPP_BW_GDK_KJPY_Antragsformular_V3"
	AOK_FA_NPPP_BW_GDK_Antragsformular_V6                                     FormName = "AOK_FA_NPPP_BW_GDK_Antragsformular_V6"
	AOK_FA_BW_GDK_Antragsformular_DF_V4                                       FormName = "AOK_FA_BW_GDK_Antragsformular_DF_V4"
	AOK_FA_BW_TE_HepCModul_V3                                                 FormName = "AOK_FA_BW_TE_HepCModul_V3"
	AOK_FA_GASTRO_BW_Maviret_Teilnahmeerklaerung_V2                           FormName = "AOK_FA_GASTRO_BW_Maviret_Teilnahmeerklaerung_V2"
	AOK_FA_GASTRO_BW_Epclusa_Teilnahmeerklaerung_V2                           FormName = "AOK_FA_GASTRO_BW_Epclusa_Teilnahmeerklaerung_V2"
	AOK_SL_HZV_Versichertenteilnahmeerklaerung_V2                             FormName = "AOK_SL_HZV_Versichertenteilnahmeerklaerung_V2"
	BKK_BAHN_HZV_Versichertenteilnahmeerklaerung_V4                           FormName = "BKK_BAHN_HZV_Versichertenteilnahmeerklaerung_V4"
	AOKNordwet                                                                FormName = "AOK_Nordwet"
	AOKBremen                                                                 FormName = "AOK_Bremen_impfstoff"
	Muster16aBay                                                              FormName = "Muster_16a_bay"
	Versichertenteilnahmeerklaerung_Online_BKK_BY_HZV_V8                      FormName = "Versichertenteilnahmeerklaerung_Online_BKK_BY_HZV_V8"
	F1050                                                                     FormName = "F1050"
	F9990                                                                     FormName = "F9990"
	F2100                                                                     FormName = "F2100"
	F1000                                                                     FormName = "F1000"
	BKK_BY_HZV_Einwilligungserklaerung_Telemedizinisches_Facharztkonsil       FormName = "BKK_BY_HZV_Einwilligungserklaerung_Telemedizinisches_Facharztkonsil"
	Muster_eRezept                                                            FormName = "Muster_eRezept"
	AOK_FA_OC_BW_Antrag_AOK_Sports_V3                                         FormName = "AOK_FA_OC_BW_Antrag_AOK_Sports_V3"
	AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_Anleitung_Bedruckung_V2 FormName = "AOK_FA_OC_BW_BKK_FA_OC_BW_Befundbogen_Osteoporose_Anleitung_Bedruckung_V2"
	Muster_4_A5                                                               FormName = "Muster_4_print_preview_n"
	EK_NO_WL_HZV_Versichertenteilnahmeerklaerung_V3                           FormName = "EK_NO_WL_HZV_Versichertenteilnahmeerklaerung_V3"
	AOK_SL_HZV_Versichertenteilnahmeerklaerung_V3                             FormName = "AOK_SL_HZV_Versichertenteilnahmeerklaerung_V3"
)

// Define constants
const NATS_SUBJECT = "" // nats subject this service will listen to

// service event constants

// message event constants

// Define service interface -------------------------------------------------------------

// Define service proxy -------------------------------------------------------------------

// Define service router -----------------------------------------------------------------

// Define client ----------------------------------------------------------------------------------------------

// Define event Notifier -----------------------------------------------------------------------------------------
type CommonNotifier struct {
	client *titan.Client
}

func NewCommonNotifier() *CommonNotifier {
	client := titan.GetDefaultClient()
	return &CommonNotifier{client}
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type CommonSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewCommonSocketNotifier(socket *socket_api.SocketServiceClient) *CommonSocketNotifier {
	return &CommonSocketNotifier{socket}
}

// -----------------------------------------------
// Test event listener
type CommonEventListener struct {
	mux sync.Mutex
}

func (listener *CommonEventListener) Reset() {
	listener.mux.Lock()
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *CommonEventListener) Subscribe(s *titan.MessageSubscriber) {
}
