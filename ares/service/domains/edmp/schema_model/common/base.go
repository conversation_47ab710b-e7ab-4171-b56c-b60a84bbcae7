package common

import (
	"fmt"

	"git.tutum.dev/medi/tutum/ares/pkg/config/software"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/document_type"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/resources"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/schema_model"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/schema_model/model"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/pkg/errors"
)

var MapAdministrativeGenderCd = map[patient_profile_common.Gender]string{
	"M": "M",
	"W": "F",
	"U": "UN", //TODO: will be provided later
	"X": "X",
	"D": "UN",
}

type SchemaExtractor interface {
	GetSchema(documentType common.DocumentType, dmpVersion string) (*model.Schema, error)
}

type ParameterExtractor interface {
	GetParametersByCaption(caption string, paragraphIndex int, documentType common.DocumentType, dmpVersion string) ([]common.Field, error)
}

type BaseField struct {
	paragraphRepo SchemaExtractor
	parameterRepo ParameterExtractor
}

func NewBaseField(s SchemaExtractor, p ParameterExtractor) *BaseField {
	return &BaseField{
		paragraphRepo: s,
		parameterRepo: p,
	}
}

func buildContent(fields []common.Field) schema_model.LocalMarkup {
	var beobachtungs []schema_model.Beobachtung
	for _, f := range fields {
		beobachtung := ToBeobachtung(f)
		if beobachtung == nil {
			continue
		}
		beobachtungs = append(beobachtungs, *beobachtung)
	}

	return schema_model.LocalMarkup{
		Descriptor: "sciphox",
		Ignore:     "all",
		SciphoxSSU: schema_model.SciphoxSSU{
			Country: "de",
			Version: "v1",
			Type:    "observation",
			Beobachtungen: &schema_model.Beobachtungen{
				Beobachtung: beobachtungs,
			},
		},
	}
}

func (*BaseField) BuildHeader(
	employeeProfile employee.EmployeeProfile,
	bsnrInfo bsnr_repo.BSNR,
	dmpDocumentType common.DocumentType,
	versionNumber int,
	dmpLabelingValue,
	dmpCaseNumber string,
	documentId uuid.UUID,
	softwareConfig software.SoftwareConfig,
	patientProfile patient_profile_common.PatientInfo,
	insuranceId uuid.UUID,
	_ []common.Field,
	documentDate int64,
	documentedAt int64,
	printingName string,
	doctorRelationType common.DoctorRelationType,
) (*schema_model.ClinicalDocumentHeader, error) {
	insuranceInfo := patientProfile.GetInsurance(insuranceId)
	if insuranceInfo == nil {
		return nil, fmt.Errorf("insurance not found")
	}

	documentType, ok := document_type.GetDocumentTypeByDMPLabelingValue(dmpLabelingValue, dmpDocumentType)
	if !ok {
		return nil, fmt.Errorf("document type not found for dmp labeling value %s", dmpLabelingValue)
	}

	if len(doctorRelationType) == 0 {
		return nil, fmt.Errorf("doctor relation type not found %s", doctorRelationType)
	}

	documentedAtString := util.FormatMillisecondsToString(util.NewPointer(documentedAt), "2006-01-02")

	documentDateString := util.FormatMillisecondsToString(util.NewPointer(documentDate), "2006-01-02")

	personInfo := []schema_model.ID{
		{
			EX: employeeProfile.Bsnr,
			RT: "BSNR",
		},
	}

	if util.GetPointerValue(employeeProfile.Lanr) != "" {
		personInfo = append(personInfo, schema_model.ID{
			EX: util.GetPointerValue(employeeProfile.Lanr),
			RT: "LANR",
		})
	}

	subSoftware, ok := schema_model.MapDMPLabelingValueWithSubSoftware[dmpLabelingValue]
	if !ok {
		return nil, fmt.Errorf("software not found for dmp labeling value %s", dmpLabelingValue)
	}

	yearQuarter := util.ToYearQuarter(documentDate)
	readCardModel := insuranceInfo.GetReadCardByCreatedAt(yearQuarter)
	cardOnlineCheckDate := insuranceInfo.GetCardOnlineCheckDateInQuarter(yearQuarter, patient_profile_common.FromCardType_EGK)

	header := &schema_model.ClinicalDocumentHeader{
		ID: schema_model.ID{
			EX: uuid.NewString(),
			RT: employeeProfile.Bsnr,
		},
		SetID: &schema_model.ID{
			EX: documentId.String(),
			RT: employeeProfile.Bsnr,
		},
		VersionNumber: &schema_model.VersionNumber{
			V: util.NumberToString(versionNumber),
		},
		DocumentTypeCode: schema_model.DocumentTypeCode{
			V:  documentType.V,
			S:  documentType.S,
			SN: "KBV",
			DN: documentType.DN,
		},
		ServiceTmr: schema_model.ServiceTmr{
			V: documentDateString,
		},
		OriginationDttm: schema_model.OriginationDttm{
			V: documentedAtString,
		},
		Provider: schema_model.Provider{
			TypeCode: schema_model.ProviderTypeCode{
				V: "PRF",
			},
			FunctionCode: function.Do(func() *schema_model.FunctionCode {
				if doctorRelationType == common.DoctorRelationType_Treatment {
					return &schema_model.FunctionCode{
						V:  "BEHA",
						S:  "1.2.276.0.76.5.105",
						DN: util.NewString("Behandelnde Arzt"),
					}
				}

				if doctorRelationType == common.DoctorRelationType_Deputy {
					return &schema_model.FunctionCode{
						V:  "VERTRETER",
						S:  "1.2.276.0.76.5.105",
						DN: util.NewString("Vertreter Arzt"),
					}
				}

				return &schema_model.FunctionCode{
					V:  "ARZTW",
					S:  "1.2.276.0.76.5.105",
					DN: util.NewString("Arztwechsel"),
				}
			}),
			Person: schema_model.Person{
				IDs: personInfo,
				PersonName: &schema_model.PersonName{
					NM: schema_model.NM{
						GIV: schema_model.SingleValueField{
							V: employeeProfile.FirstName,
						},
						FAM: schema_model.SingleValueField{
							V: employeeProfile.LastName,
						},
						PFX: function.Do(func() []schema_model.PFX {
							if employeeProfile.Title == nil && employeeProfile.IntendWord == nil && employeeProfile.AdditionalName == nil {
								return nil
							}

							pfxs := []schema_model.PFX{}
							if employeeProfile.Title != nil {
								pfxs = append(pfxs, schema_model.PFX{
									V:    util.GetPointerValue(employeeProfile.Title),
									QUAL: "AC",
								})
							}
							if employeeProfile.IntendWord != nil {
								pfxs = append(pfxs, schema_model.PFX{
									V:    string(util.GetPointerValue(employeeProfile.IntendWord)),
									QUAL: "VV",
								})
							}
							if employeeProfile.AdditionalName != nil {
								pfxs = append(pfxs, schema_model.PFX{
									V:    string(util.GetPointerValue(employeeProfile.AdditionalName)),
									QUAL: "NB",
								})
							}

							return pfxs
						}),
					},
				},
				Addr: &schema_model.Address{
					Street: util.NewPointer(schema_model.SingleValueField{
						V: bsnrInfo.Street,
					}),
					HouseNo: util.NewPointer(schema_model.SingleValueField{
						V: function.Do(func() string {
							if len(bsnrInfo.Number) > 9 {
								return bsnrInfo.Number[:9]
							}

							return bsnrInfo.Number
						}),
					}),
					ZipCode: schema_model.SingleValueField{
						V: bsnrInfo.PostCode,
					},
					City: schema_model.SingleValueField{
						V: bsnrInfo.City,
					},
				},
				Telecom: &schema_model.Telecom{
					Value: "tel:" + bsnrInfo.PhoneNumber,
					Use:   "WP", // TODO : hardcode now
				},
			},
		},
		Patient: schema_model.Patient{
			TypeCode: schema_model.PatientTypeCode{
				V: "PATSBJ",
			},
			Person: schema_model.PatientPerson{
				IDs: []schema_model.ID{
					{
						EX: dmpCaseNumber,
						RT: employeeProfile.Bsnr,
					},
				},
				PersonName: &schema_model.PersonName{
					NM: schema_model.NM{
						GIV: schema_model.SingleValueField{
							V: patientProfile.PersonalInfo.FirstName,
						},
						FAM: schema_model.SingleValueField{
							V: patientProfile.PersonalInfo.LastName,
						},
						PFX: function.Do(func() []schema_model.PFX {
							if patientProfile.PersonalInfo.Title == nil && patientProfile.PersonalInfo.IntendWord == nil && patientProfile.PersonalInfo.AdditionalNames == nil {
								return nil
							}

							pfxs := []schema_model.PFX{}
							if patientProfile.PersonalInfo.Title != nil {
								pfxs = append(pfxs, schema_model.PFX{
									V:    util.GetPointerValue(patientProfile.PersonalInfo.Title),
									QUAL: "AC",
								})
							}
							if patientProfile.PersonalInfo.IntendWord != nil {
								pfxs = append(pfxs, schema_model.PFX{
									V:    string(util.GetPointerValue(patientProfile.PersonalInfo.IntendWord)),
									QUAL: "VV",
								})
							}
							if len(patientProfile.PersonalInfo.AdditionalNames) > 0 {
								additionalNames := patientProfile.PersonalInfo.AdditionalNames[0]
								for i := 1; i < len(patientProfile.PersonalInfo.AdditionalNames); i++ {
									additionalNames += " " + patientProfile.PersonalInfo.AdditionalNames[i]
								}
								pfxs = append(pfxs, schema_model.PFX{
									V:    string(additionalNames),
									QUAL: "NB",
								})
							}

							return pfxs
						}),
					},
				},
				Addr: function.Do(func() []schema_model.Address {
					baseAddress := []schema_model.Address{
						{
							Use: util.NewString("PHYS"),
							Street: function.Do(func() *schema_model.SingleValueField {
								street := patientProfile.AddressInfo.Address.Street
								if street == nil {
									return nil
								}

								return util.NewPointer(
									schema_model.SingleValueField{
										V: util.GetPointerValue(street),
									},
								)
							}),
							HouseNo: function.Do(func() *schema_model.SingleValueField {
								number := patientProfile.AddressInfo.Address.Number
								if number == nil {
									return nil
								}

								return util.NewPointer(
									schema_model.SingleValueField{
										V: util.GetPointerValue(number),
									},
								)
							}),
							ZipCode: function.Do(func() schema_model.SingleValueField {
								if patientProfile.AddressInfo.Address.PostCode == "" {
									return schema_model.SingleValueField{
										V: "0",
									}
								}
								return schema_model.SingleValueField{
									V: patientProfile.AddressInfo.Address.PostCode,
								}
							}),
							City: schema_model.SingleValueField{
								V: util.GetPointerValue(patientProfile.AddressInfo.Address.City),
							},
							CNT: &schema_model.SingleValueField{
								V: util.GetPointerValue(patientProfile.AddressInfo.Address.CountryCode),
							},
						},
					}

					isPostOfficeBoxValid := patientProfile.PostOfficeBox.CountryCode != "" &&
						patientProfile.PostOfficeBox.OfficeBox != "" &&
						patientProfile.PostOfficeBox.PlaceOfResidence != "" &&
						patientProfile.PostOfficeBox.PostCode != ""
					if !isPostOfficeBoxValid {
						return baseAddress
					}

					baseAddress = append(baseAddress, schema_model.Address{
						Use: util.NewString("PST"),
						ZipCode: schema_model.SingleValueField{
							V: patientProfile.PostOfficeBox.PostCode,
						},
						City: schema_model.SingleValueField{
							V: patientProfile.PostOfficeBox.PlaceOfResidence,
						},
						CNT: &schema_model.SingleValueField{
							V: patientProfile.PostOfficeBox.CountryCode,
						},
						POB: &schema_model.SingleValueField{
							V: patientProfile.PostOfficeBox.OfficeBox,
						},
					})

					return baseAddress
				}),
			},
			BirthDttm: &schema_model.BirthDttm{
				V: patientProfile.PersonalInfo.DateOfBirthForEDMP(),
			},
			AdministrativeGenderCd: function.Do(func() *schema_model.AdministrativeGenderCd {
				gender := MapAdministrativeGenderCd[patientProfile.PersonalInfo.Gender]
				return &schema_model.AdministrativeGenderCd{
					V: patient_profile_common.Gender(gender),
				}
			}),
			LocalHeader: schema_model.LocalHeader{
				Ignore:     "all",
				Descriptor: "sciphox",
				SciphoxSSU: schema_model.SciphoxSSU{
					Country: "de",
					Version: "v3",
					Type:    "insurance",
					GKV: &schema_model.GesetzlicheKrankenvers{
						Kostentraegerbezeichnung: schema_model.Kostentraegerbezeichnung{
							V: printingName,
						},
						Kostentraegerkennung: schema_model.Kostentraegerkennung{
							V: util.NumberToString(insuranceInfo.IkNumber),
						},
						KostentraegerAbrechnungsbereich: &schema_model.FunctionCode{
							V: "00", // TODO: hardcode now => get from schein,
							S: "2.16.840.1.113883.********",
						},
						WOP: function.Do(func() *schema_model.FunctionCode {
							if insuranceInfo == nil {
								return nil
							}
							if insuranceInfo.Wop != "" {
								return &schema_model.FunctionCode{
									V: insuranceInfo.Wop,
									S: "2.16.840.1.113883.********",
								}
							}

							// meaning patient create manually and have no wop
							if readCardModel == nil {
								return nil
							}

							// meaning patient read by card and have no owp, return 00
							return &schema_model.FunctionCode{
								V: "00",
								S: "2.16.840.1.113883.********",
							}
						}),
						Versichertennummer: function.Do(func() *schema_model.Versichertennummer {
							if insuranceInfo == nil || util.GetPointerValue(insuranceInfo.InsuranceNumber) == "" {
								return nil
							}

							return &schema_model.Versichertennummer{
								V: util.GetPointerValue(insuranceInfo.InsuranceNumber),
							}
						}),
						Versichertenart: function.Do(func() *schema_model.FunctionCode {
							if insuranceInfo == nil {
								return nil
							}

							return &schema_model.FunctionCode{
								V: string(insuranceInfo.InsuranceStatus),
								S: "2.16.840.1.113883.3.7.1.1",
							}
						}),
						BesonderePersonengruppe: function.Do(func() *schema_model.FunctionCode {
							if insuranceInfo == nil || len(insuranceInfo.SpecialGroup) == 0 {
								return nil
							}

							return &schema_model.FunctionCode{
								V: string(insuranceInfo.SpecialGroup),
								S: "1.2.276.0.76.5.222",
							}
						}),
						DMP_Kennzeichnung: function.Do(func() *schema_model.FunctionCode {
							if insuranceInfo == nil || insuranceInfo.DMPLabeling == "" {
								return nil
							}

							return &schema_model.FunctionCode{
								V: insuranceInfo.DMPLabeling,
								S: "1.2.276.0.76.5.223",
							}
						}),
						VersicherungsschutzBeginn: function.Do(func() *schema_model.SingleValueField {
							if insuranceInfo == nil || insuranceInfo.StartDate == nil {
								return nil
							}

							return &schema_model.SingleValueField{
								V: util.FormatMillisecondsToString(insuranceInfo.StartDate, "2006-01-02"),
							}
						}),
						VersicherungsschutzEnde: function.Do(func() *schema_model.SingleValueField {
							if insuranceInfo == nil || insuranceInfo.EndDate == nil {
								return nil
							}

							return &schema_model.SingleValueField{
								V: util.FormatMillisecondsToString(insuranceInfo.EndDate, "2006-01-02"),
							}
						}),
						Einlesedatum: function.Do(func() *schema_model.SingleValueField {
							if cardOnlineCheckDate == nil {
								return nil
							}

							return &schema_model.SingleValueField{
								V: util.FormatMillisecondsToString(cardOnlineCheckDate, "2006-01-02"),
							}
						}),
						AbrechnungsVKNR: function.Do(func() *schema_model.FunctionCode {
							if insuranceInfo.InsuranceCompanyId == "" {
								return nil
							}

							return &schema_model.FunctionCode{
								V: insuranceInfo.InsuranceCompanyId,
								S: "AbrechnungsVKNR",
							}
						}),
					},
				},
			},
		},
		LocalHeader: schema_model.LocalHeader{
			Ignore:     "all",
			Descriptor: "sciphox",
			SciphoxSSU: schema_model.SciphoxSSU{
				Country: "de",
				Version: "v1",
				Type:    "software",
				Software: &schema_model.Software{
					ID: &schema_model.ID{
						EX: softwareConfig.KbvPruefnummer,
						RT: "KBV-Prüfnummer",
					},
					SoftwareName: schema_model.SingleValueField{
						V: softwareConfig.Name,
					},
					SoftwareVersion: schema_model.SingleValueField{
						V: softwareConfig.Version,
					},
					SoftwareTyp: schema_model.SingleValueField{
						V: "PVS",
					},
					Kontakts: []schema_model.Kontakt{
						{
							Kontakttyp: schema_model.Kontakttyp{
								V:  "SOFTV",
								S:  "1.2.276.********.*******",
								DN: "Softwareverantwortlicher",
							},
							Organization: schema_model.Organization{
								Name: softwareConfig.Organisation,
							},
							Address: schema_model.Address{
								Street: util.NewPointer(
									schema_model.SingleValueField{
										V: softwareConfig.Strasse,
									},
								),
								HouseNo: util.NewPointer(
									schema_model.SingleValueField{
										V: softwareConfig.Hausnummer,
									},
								),
								ZipCode: schema_model.SingleValueField{
									V: "50859", //TODO: hard code
								},
								City: schema_model.SingleValueField{
									V: "Köln", //TODO: hard code
								},
							},
							Telecoms: function.Do(func() []schema_model.Telecom {
								if softwareConfig.TelefonMobil == "" {
									return []schema_model.Telecom{
										{
											Value: "tel:(0000)0000-0",
											Use:   "WP",
										},
									}
								}

								return []schema_model.Telecom{
									{
										Value: softwareConfig.TelefonMobil,
										Use:   "WP",
									},
								}
							}),
						},
					},
					SubSoftware: &subSoftware,
				},
			},
		},
	}
	return header, nil
}

func (f *BaseField) BuildBody(documentType common.DocumentType, dmpLabelingValue common.DMPValueEnum, fields []common.Field, dmpVersion string) (*schema_model.Body, error) {
	headers, err := f.GetHeaders(documentType, dmpLabelingValue, dmpVersion)
	if err != nil {
		return nil, fmt.Errorf("failed to get headers: %w", err)
	}

	if len(headers) == 0 {
		return nil, nil
	}

	paragraph := make([]schema_model.Paragraph, 0)
	for _, t := range headers {
		fieldsByHeaders := slice.Filter(fields, func(f common.Field) bool {
			return f.Header == t.HeaderName
		})

		localMarkup := buildContent(fieldsByHeaders)
		beobachtungen := localMarkup.SciphoxSSU.Beobachtungen
		if len(fieldsByHeaders) > 0 && beobachtungen != nil && beobachtungen.Beobachtung != nil {
			paragraph = append(paragraph, schema_model.Paragraph{
				Caption: schema_model.Caption{
					CaptionCD: schema_model.CaptionCD{
						DN: t.HeaderName,
					},
				},
				Content: schema_model.Content{
					LocalMarkup: localMarkup,
				},
			})
		}
	}

	body := schema_model.Body{
		Section: schema_model.Section{
			Paragraph: paragraph,
		},
	}
	return &body, nil
}

func (*BaseField) GetHeaders(documentType common.DocumentType, dmpLabelingValue common.DMPValueEnum, dmpVersion string) ([]common.DMP, error) {
	loader := resources.ResourceLoader{
		DmpValue:     dmpLabelingValue,
		DmpVersion:   dmpVersion,
		DocumentType: documentType,
	}

	resource, err := loader.Load()
	if err != nil {
		return nil, errors.WithMessage(err, "failed to load resources")
	}

	res := resource.
		ToEdmpModel().
		WithOverrideHeaders().
		WithOverrideFields().
		WithOverrideOptions().
		GetDMPs()

	return res, nil
}

func (*BaseField) ReorderHeaders(headers []common.Header, dmpValue string) ([]common.Header, error) {
	var allgemeine_Anamnese_Fields = []string{
		"Körpergröße",
		"Körpergewicht",
		"Blutdruck systolisch",
		"Blutdruck diastolisch",
		"Raucher",
		"Begleiterkrankungen",
	}

	var behandlungsplanung_Fields = []string{
		"Vom Patienten gewünschte Informationsangebote der Krankenkasse",
		"Dokumentationsintervall",
	}

	// copy DisplayHeader from HeaderName for all DMPs
	for i := range headers {
		for k := range headers[i].DMPs {
			headers[i].DMPs[k].DisplayHeader = util.NewString(headers[i].DMPs[k].HeaderName)
		}
	}

	// get 'Anamnese- und Befunddaten' header and build 'Allgemeine Anamnese- und Befunddaten' header
	header := slice.FindOne(headers, func(h common.Header) bool {
		return h.Name == "Anamnese- und Befunddaten"
	})
	if header == nil {
		return headers, nil
	}
	allgemeineHeader, err := schema_model.RemapFieldsToHeader(header, util.Contains, allgemeine_Anamnese_Fields, true, dmpValue)
	if err != nil {
		return nil, err
	}
	allgemeineHeader.Name = "Allgemeine Anamnese- und Befunddaten"
	// set DisplayHeader for all allgemeineHeader.DMPs
	for i := range allgemeineHeader.DMPs {
		allgemeineHeader.DMPs[i].DisplayHeader = util.NewString("Allgemeine Anamnese- und Befunddaten")
	}
	// insert 'Allgemeine Anamnese- und Befunddaten' header to the second position of headers
	newHeaders := slice.Insert(headers, 1, allgemeineHeader)

	// re-map 'Anamnese- und Befunddaten' header
	anamneseHeader, err := schema_model.RemapFieldsToHeader(header, util.Contains, allgemeine_Anamnese_Fields, false, dmpValue)
	if err != nil {
		return nil, err
	}
	// re-assign 'Anamnese- und Befunddaten' header with new Fields
	for i := 0; i < len(newHeaders); i++ {
		if newHeaders[i].Name == "Anamnese- und Befunddaten" {
			newHeaders[i] = anamneseHeader
		}
	}

	// get Behandlungsplanung header
	header = slice.FindOne(headers, func(h common.Header) bool {
		return h.Name == "Behandlungsplanung"
	})
	if header == nil {
		return newHeaders, nil
	}

	// re-map then re-assign Behandlungsplanung header with new Fields
	behandlungsplanungHeader, err := schema_model.RemapFieldsToHeader(header, util.Contains, behandlungsplanung_Fields, true, dmpValue)
	if err != nil {
		return nil, err
	}
	// set new DisplayHeader for all behandlungsplanungHeader.DMPs
	for i := range behandlungsplanungHeader.DMPs {
		behandlungsplanungHeader.DMPs[i].DisplayHeader = util.NewString("NewBehandlungsplanung")
	}
	for i := 0; i < len(newHeaders); i++ {
		if newHeaders[i].Name == "Behandlungsplanung" {
			newHeaders[i] = behandlungsplanungHeader
		}
	}

	// build new Behandlungsplanung header
	newBehandlungsplanungHeader, err := schema_model.RemapFieldsToHeader(header, util.Contains, behandlungsplanung_Fields, false, dmpValue)
	if err != nil {
		return nil, err
	}
	// append newBehandlungsplanungHeader to the end of headers
	newHeaders = append(newHeaders, newBehandlungsplanungHeader)

	// set DisplayHeader for all newHeaders.DMPs.Fields
	for i := range newHeaders {
		for j := range newHeaders[i].DMPs {
			for k := range newHeaders[i].DMPs[j].Fields {
				newHeaders[i].DMPs[j].Fields[k].DisplayHeader = newHeaders[i].DMPs[j].DisplayHeader
			}
		}
	}
	return newHeaders, nil
}
