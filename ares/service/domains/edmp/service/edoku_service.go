package service

import (
	"bytes"
	"fmt"
	"sort"

	"emperror.dev/errors"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/edoku"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/pkg/edmp_testmodule"
	"git.tutum.dev/medi/tutum/ares/pkg/minio"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/billing_builder"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/field_adapter"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/edmp/edmp_document"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	timeline_common "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"
)

type EDokuService struct {
	*EDMPService
	edokuDocumentSocketNotifier *edoku.EdokuSocketNotifier
}
type UploadBillingFileForEdokuDocumentRequest struct {
	document          common.EdokuDocumentationOverview
	documentCreatedAt int64
}

type CheckPlausibilityMultipleDocumentsRequest struct {
	DMPLabelingValue string
	ZipFileData      []byte
	ZipFileName      string
	DMPVersion       string
	XPMFileType      edmp_testmodule.FileType
	DocumentType     common.DocumentType
	DocumentTypeMap  map[common.DocumentType]common.DocumentType
}

type CheckPlausibilityMultipleDocumentsResponse struct {
	IsPlausible            bool
	FieldValidationResults []common.FieldValidationResult
	XPMStatisticFiles      []common.DMPBillingFile
}

var _ edoku.EdokuApp = &EDokuService{}

type DocumentKey = uuid.UUID
type EdokuDocument = edmp_document.EdmpEnrollmentDocumentEntity

func newEdokuService(edmp *EDMPService, socketClient *socket_api.SocketServiceClient) *EDokuService {
	return &EDokuService{EDMPService: edmp, edokuDocumentSocketNotifier: edoku.NewEdokuSocketNotifier(socketClient)}
}

var EDokuServiceMod = submodule.Make[*EDokuService](newEdokuService, EDMPServiceMod, config.SocketServiceClientMod)

func (e *EDokuService) AddOhneNameFieldToDocument(ctx *titan.Context, documentOverview common.DocumentationOverview) (common.DocumentationOverview, error) {
	patientInfo, err := e.patientProfileService.GetProfileById(ctx, &profile.GetByIdRequest{
		PatientId: documentOverview.PatientId,
	})
	if err != nil {
		return documentOverview, err
	}
	if patientInfo == nil {
		return documentOverview, errors.New("patient not found")
	}
	patientAge := util.GetPointerValue(patientInfo.GetAge(ctx))
	ohneNameFieldValues := []common.FieldValue{
		{
			Name:      "Alter",
			Value:     fmt.Sprintf("%d", patientAge),
			FieldType: common.FieldType_Number,
			ValueUnit: util.NewPointer("Jahre"),
		},
	}
	yearQuarter := util.ToYearQuarter(documentOverview.DocumentDate)
	dmpVersion := fmt.Sprintf("%v.%v.0", yearQuarter.Year, yearQuarter.Quarter)
	edokuDocumentForm, err := e.fieldService.GetEDOKUDocument(documentOverview.DMPLabelingValue, documentOverview.DocumentType, dmpVersion)
	if err != nil {
		return documentOverview, err
	}
	ohneNameHeader := slice.FindOne(edokuDocumentForm.Headers, func(h common.Header) bool {
		return h.Name == "ohne Name"
	})
	if ohneNameHeader == nil {
		return documentOverview, errors.New("ohne Name field not found")
	}

	ohneNameField := ohneNameHeader.DMPs[0].Fields[0]
	ohneNameField.Values = ohneNameFieldValues
	documentOverview.Fields = append(documentOverview.Fields, ohneNameField)
	return documentOverview, nil
}

func (e *EDokuService) CreateDocument(ctx *titan.Context, request edoku.CreateDocumentRequest) (*edoku.CreateDocumentResponse, error) {
	documentOverviewWithOhneNameField, err := e.AddOhneNameFieldToDocument(ctx, request.DocumentationOverview)
	if err != nil {
		return nil, err
	}
	edokuDocumentEntity, err := e.EDMPService.edmpRepo.EdokuEnrollmentDocumentRepo.Create(ctx, documentOverviewWithOhneNameField)
	if err != nil {
		return nil, err
	}
	edokuDocumentationOverview := common.EdokuDocumentationOverview(edokuDocumentEntity.DocumentationOverview)

	// gen billing file
	billingFileName, err := GenBillingFileName(
		util.GetPointerValue(edokuDocumentationOverview.BsnrCode),
		edokuDocumentationOverview.DMPCaseNumber,
		edokuDocumentationOverview.DMPLabelingValue,
		edokuDocumentationOverview.DocumentType,
		util.ConvertMillisecondsToTime(edokuDocumentEntity.CreatedAt.UnixMilli()),
		true,
	)
	if err != nil {
		return nil, err
	}
	edokuDocumentationOverview.DMPBillingFile.FileName = billingFileName
	edokuDocumentationOverview.DocumentationOverviewId = edokuDocumentEntity.Id

	// upload billing file
	err = e.uploadBillingFileForEdokuDocument(ctx, UploadBillingFileForEdokuDocumentRequest{
		document:          edokuDocumentationOverview,
		documentCreatedAt: edokuDocumentEntity.CreatedAt.UnixMilli(),
	})
	if err != nil {
		return nil, err
	}

	_, err = e.edmpRepo.EdokuEnrollmentDocumentRepo.UpdateDMPBillingFileById(ctx, util.GetPointerValue(edokuDocumentEntity.Id), common.DMPBillingFile{
		FileName: billingFileName,
		FilePath: fmt.Sprintf("%s/%s", e.bucketDmpBilling, billingFileName),
		FileType: common.DMPBillingHistoryFileType_Billing,
	})
	if err != nil {
		return nil, err
	}

	timeline := timeline_repo.TimelineEntity[common.EdokuDocumentationOverview]{
		Id:                edokuDocumentEntity.Id,
		PatientId:         *edokuDocumentEntity.PatientId,
		TreatmentDoctorId: edokuDocumentEntity.TreatmentDoctorId,
		Payload:           edokuDocumentationOverview,
		Type:              timeline_common.TimelineEntityType_EDOKU_Document,
	}

	_, err = e.EDMPService.timelineServiceEdokuDocument.Create(ctx, timeline)
	if err != nil {
		return nil, err
	}

	return &edoku.CreateDocumentResponse{
		DocumentId: edokuDocumentEntity.Id,
	}, nil
}

func (s *EDokuService) FinishDocumentationOverview(ctx *titan.Context, request edoku.UpdateDocumentationOverviewRequest) error {
	_, err := s.CheckPlausibility(ctx, edoku.CheckPlausibilityRequest{
		PatientId:           util.GetPointerValue(request.DocumentationOverview.PatientId),
		DocumentId:          &request.Id,
		DocumentType:        request.DocumentationOverview.DocumentType,
		DocumentDate:        request.DocumentationOverview.DocumentDate,
		RelatedFields:       request.DocumentationOverview.Fields,
		DoctorRelationType:  request.DocumentationOverview.DoctorRelationType,
		AdditionalContracts: request.DocumentationOverview.AdditionalContracts,
		DMPLabelingValue:    request.DocumentationOverview.DMPLabelingValue,
		DoctorId:            request.DocumentationOverview.DoctorId,
		DMPCaseNumber:       request.DocumentationOverview.DMPCaseNumber,
		ScheinId:            request.DocumentationOverview.ScheinId,
		IsBillingCheck:      true,
	})
	if err != nil {
		return err
	}
	request.DocumentationOverview.DocumentStatus = util.NewPointer(common.DocumentStatus_Finished)
	return s.SaveDocumentationOverview(ctx, request)
}

func (s *EDokuService) SaveDocumentationOverview(ctx *titan.Context, request edoku.UpdateDocumentationOverviewRequest) error {
	documentOverviewWithOhneNameField, err := s.AddOhneNameFieldToDocument(ctx, request.DocumentationOverview)
	if err != nil {
		return err
	}
	entity, err := s.EDMPService.edmpRepo.EdokuEnrollmentDocumentRepo.UpdateById(ctx, request.Id, documentOverviewWithOhneNameField)
	if err != nil {
		return err
	}
	edokuDocumentationOverview := common.EdokuDocumentationOverview(entity.DocumentationOverview)

	err = s.uploadBillingFileForEdokuDocument(ctx, UploadBillingFileForEdokuDocumentRequest{
		document:          edokuDocumentationOverview,
		documentCreatedAt: entity.CreatedAt.UnixMilli(),
	})
	if err != nil {
		return err
	}

	_, err = s.EDMPService.timelineServiceEdokuDocument.UpdateByDocumentationOverviewIdWithCallback(ctx, *entity.Id, func(entity *timeline_repo.TimelineEntity[common.EdokuDocumentationOverview]) {
		entity.Payload = edokuDocumentationOverview
	})
	if err != nil {
		return err
	}

	err = s.edokuDocumentSocketNotifier.NotifyCareProviderEDokuDocumentStatusChanged(ctx, &edoku.EventEDokuDocumentStatusChanged{
		EdokuDocumentId: *entity.DocumentationOverviewId,
		Status:          *entity.DocumentationOverview.DocumentStatus,
	})
	if err != nil {
		return err
	}
	return nil
}

func (s *EDokuService) GetEDOKUDocument(ctx *titan.Context, req edoku.GetEDOKUDocumentRequest) (*edoku.GetEDOKUDocumentResponse, error) {
	yearQuarter := util.ToYearQuarter(util.ConvertTimeToMiliSecond(req.DocumentDate))
	dmpVersion := fmt.Sprintf("%v.%v.0", yearQuarter.Year, yearQuarter.Quarter)

	res, err := s.fieldService.GetEDOKUDocument(req.DMPLabelingValue, req.DocumentType, dmpVersion)
	if err != nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_EDMP_Get_Document_Not_Found, fmt.Sprintf("failed to get dmp document: %s", err.Error()))
	}

	headers := slice.Filter(res.Headers, func(h common.Header) bool {
		return h.Name != "ohne Name"
	})
	return &edoku.GetEDOKUDocumentResponse{
		EDOKUDocument: common.EDOKUDocument{
			Headers:  headers,
			DMPValue: res.DMPValue,
		},
	}, nil
}

func (s *EDokuService) CheckPlausibility(ctx *titan.Context, req edoku.CheckPlausibilityRequest) (*edoku.CheckPlausibilityResponse, error) {
	var (
		xpmResult         *CheckXPMRulesResponse
		validationResults []common.FieldValidationResult
		err               error
		fdDocuments       []common.DocumentationOverview
	)
	patient, err := s.patientProfileService.GetProfileById(ctx, &profile.GetByIdRequest{
		PatientId: util.NewPointer(req.PatientId),
	})
	if err != nil || patient == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Patient_Not_Found)
	}
	patientAge := util.GetPointerValue(patient.GetAge(ctx))

	// billing check
	if req.IsBillingCheck {
		fields := normalizeFields(req.RelatedFields, "")
		xpmResult, err = s.EDMPService.CheckXPMRules(ctx, CheckXPMRulesRequest{
			DMPLabelingValue:    req.DMPLabelingValue,
			DoctorId:            req.DoctorId,
			PatientId:           req.PatientId,
			DMPCaseNumber:       req.DMPCaseNumber,
			Fields:              fields,
			DocumentType:        req.DocumentType,
			DocumentDate:        req.DocumentDate,
			DocumentId:          req.DocumentId,
			ScheinId:            req.ScheinId,
			DoctorRelationType:  req.DoctorRelationType,
			AdditionalContracts: req.AdditionalContracts,
			DMPVersion:          getDMPVersion(req.DocumentDate),
		})

		if err != nil {
			return nil, fmt.Errorf("failed to check xpm rules: %w", err)
		}
		if xpmResult == nil {
			return nil, errors.New("failed to check xpm rules")
		}

		if len(xpmResult.xpmValidationResults) > 0 {
			validationResults = append(validationResults, xpmResult.xpmValidationResults...)
		}

		return &edoku.CheckPlausibilityResponse{
			IsPlausible:            isPlausible(validationResults),
			FieldValidationResults: validationResults,
			BillingFile: function.Do(func() *common.DMPBillingFile {
				if xpmResult == nil {
					return nil
				}
				return util.NewPointer(xpmResult.billingFile)
			}),
			XPMPFile: function.Do(func() *common.DMPBillingFile {
				if xpmResult == nil {
					return nil
				}
				return util.NewPointer(xpmResult.xpmFile)
			}),
		}, nil
	}

	// self check
	if req.Field != nil {
		fields := normalizeFields(req.RelatedFields, req.Field.Name)
		validationResults, err = s.fieldService.CheckRule4SelectedFields(ctx, CheckRulesRequest{
			fields:       fields,
			dmpValue:     req.DMPLabelingValue,
			documentType: req.DocumentType,
			patientAge:   patientAge,
			documentDate: req.DocumentDate,
			fdDocuments:  fdDocuments,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to check rule for one field: %w", err)
		}
	} else {
		fields := normalizeFields(req.RelatedFields, "")
		validationResults, err = s.fieldService.CheckAllRules(ctx, CheckRulesRequest{
			fields:       fields,
			dmpValue:     req.DMPLabelingValue,
			documentType: req.DocumentType,
			patientAge:   patientAge,
			documentDate: req.DocumentDate,
			fdDocuments:  fdDocuments,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to check all rules: %w", err)
		}
	}
	return &edoku.CheckPlausibilityResponse{
		IsPlausible:            isPlausible(validationResults),
		FieldValidationResults: validationResults,
		BillingFile: function.Do(func() *common.DMPBillingFile {
			if xpmResult == nil {
				return nil
			}
			return util.NewPointer(xpmResult.billingFile)
		}),
		XPMPFile: function.Do(func() *common.DMPBillingFile {
			if xpmResult == nil {
				return nil
			}
			return util.NewPointer(xpmResult.xpmFile)
		}),
	}, nil
}

func (s *EDokuService) CheckPlausibilityMultipleDocuments(ctx *titan.Context, req *CheckPlausibilityMultipleDocumentsRequest) (*CheckPlausibilityMultipleDocumentsResponse, error) {
	var validationResults []common.FieldValidationResult
	xpmResult, err := s.EDMPService.CheckXPMRulesWithZip(
		ctx,
		CheckXPMRulesRequest{
			DMPLabelingValue: req.DMPLabelingValue,
			DocumentType:     req.DocumentType,
			Fields:           []common.Field{},
		},
		req.ZipFileData,
		req.ZipFileName,
		req.DMPVersion,
		req.XPMFileType,
		req.DocumentTypeMap,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to check xpm rules: %w", err)
	}

	if xpmResult == nil {
		return nil, errors.New("failed to check xpm rules")
	}

	if len(xpmResult.xpmValidationResults) > 0 {
		validationResults = append(validationResults, xpmResult.xpmValidationResults...)
	}

	return &CheckPlausibilityMultipleDocumentsResponse{
		IsPlausible:            isPlausible(validationResults),
		FieldValidationResults: validationResults,
		XPMStatisticFiles:      xpmResult.xpmFiles,
	}, nil
}

func (s *EDokuService) uploadBillingFileForEdokuDocument(ctx *titan.Context, request UploadBillingFileForEdokuDocumentRequest) error {
	document := request.document
	patient, err := s.patientProfileRepo.GetById(ctx, *document.PatientId)
	if err != nil {
		return fmt.Errorf("failed to get patient: %w", err)
	}
	if patient == nil || patient.Id == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Patient_Not_Found)
	}

	employeeProfile, err := s.employeeProfileRepo.GetProfileById(ctx, document.DoctorId)
	if err != nil {
		return fmt.Errorf("failed to get employee profile: %w", err)
	}
	if employeeProfile == nil || employeeProfile.Id == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Employee_Not_Found)
	}

	dmpValue := document.DMPLabelingValue
	f, err := field_adapter.GetDMPFielder(common.DMPValueEnum(dmpValue))
	if err != nil {
		return fmt.Errorf("failed to get fielder: %w", err)
	}

	sequenceType := util.NewPointer(common.SequenceType_Billing)
	sequenceNumber, err := s.edmpRepo.EdmpSequence.GetSequenceNumber(ctx, employeeProfile.Bsnr, dmpValue, sequenceType)
	if err != nil {
		return fmt.Errorf("failed to get sequenceNumber %w", err)
	}

	bsnrInfo, err := s.bsnrService.GetById(ctx, util.GetPointerValue(employeeProfile.BsnrId))
	if err != nil {
		return fmt.Errorf("failed to get bsnr: %w", err)
	}
	if bsnrInfo == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_BSNR_NOT_FOUND)
	}

	printingName, err := s.GetInsurancePrintingName(ctx, util.GetPointerValue(document.PatientId), document.ScheinId)
	if err != nil {
		return fmt.Errorf("failed to get insurance printing name: %w", err)
	}

	schein, err := s.scheinRepo.GetById(ctx, document.ScheinId)
	if err != nil {
		return fmt.Errorf("failed to get schein by insurance id %w", err)
	}
	if schein == nil || schein.Id == nil {
		return fmt.Errorf("failed to get schein by id %v", schein)
	}

	b := billing_builder.BillingFieldBuilder{
		PatientProfile:     util.GetPointerValue(patient.PatientInfo),
		BsnrInfo:           util.GetPointerValue(bsnrInfo),
		EmployeeProfile:    util.GetPointerValue(employeeProfile),
		VersionNumber:      sequenceNumber,
		DocumentType:       document.DocumentType,
		DmpLabelingValue:   dmpValue,
		SoftwareConfig:     s.softwareConfig,
		Fielder:            f,
		Fields:             document.Fields,
		DocumentId:         *document.DocumentationOverviewId,
		DmpCaseNumber:      document.DMPCaseNumber,
		DocumentDate:       document.DocumentDate,
		DocumentAt:         request.documentCreatedAt,
		PrintingName:       util.GetPointerValue(printingName),
		InsuranceId:        schein.Schein.InsuranceId,
		DoctorRelationType: document.DoctorRelationType,
	}

	documentType := document.DocumentType
	if documentType == common.DocumentType_EHKS_D && util.GetPointerValue(document.AdditionalContracts) == common.AdditionalContractsEnum_Yes {
		documentType = common.DocumentType_EHKS_D_EV
	}
	if documentType == common.DocumentType_EHKS_ND && util.GetPointerValue(document.AdditionalContracts) == common.AdditionalContractsEnum_Yes {
		documentType = common.DocumentType_EHKS_ND_EV
	}

	billingXMLdata, err := b.BuildHeader(documentType).BuildBody().ToXMl()
	if err != nil {
		return fmt.Errorf("failed to build billing xml %w", err)
	}

	if document.DMPBillingFile.FileName == "" {
		billingFileName, err := GenBillingFileName(
			employeeProfile.Bsnr,
			document.DMPCaseNumber,
			document.DMPLabelingValue,
			document.DocumentType,
			util.ConvertMillisecondsToTime(document.DocumentDate),
			true,
		)
		if err != nil {
			return fmt.Errorf("failed to generate billing file name %w", err)
		}
		document.DMPBillingFile.FileName = billingFileName
	}

	billingReader := bytes.NewReader(billingXMLdata)
	_, err = s.minioClient.PutObject(ctx, s.bucketDmpBilling, document.DMPBillingFile.FileName, billingReader, billingReader.Size(), minio.PutObjectOptions{})
	if err != nil {
		return fmt.Errorf("failed to put object %w", err)
	}

	return nil
}

func (s *EDokuService) IsCaseNumberExist(ctx *titan.Context, request edoku.IsCaseNumberExistRequest) (*edoku.IsCaseNumberExistResponse, error) {
	exist, err := s.edmpRepo.EdokuEnrollmentDocumentRepo.IsCaseNumberExist(ctx, request.CaseNumber, request.DocumentId)
	if err != nil {
		return nil, fmt.Errorf("failed to check if case number exists: %w", err)
	}
	return &edoku.IsCaseNumberExistResponse{
		IsExist: exist,
	}, nil
}

func (s *EDokuService) GetEdokuPatientInfo(ctx *titan.Context, request edoku.GetEdokuPatientInfoRequest) (*edoku.GetEdokuPatientInfoResponse, error) {
	documents, err := s.edmpRepo.EdokuEnrollmentDocumentRepo.GetEdokuDocuments(ctx, request.Filter)
	if err != nil {
		return nil, fmt.Errorf("failed to get edoku documents: %w", err)
	}

	patientMap, err := s.getPatientMap(ctx, documents)
	if err != nil {
		return nil, fmt.Errorf("failed to get patient map: %w", err)
	}

	doctorMap, err := s.getDoctorMap(ctx, documents)
	if err != nil {
		return nil, fmt.Errorf("failed to get doctor map: %w", err)
	}

	edokuPatientOverviewList := []common.EdokuPatientOverview{}
	for _, document := range documents {
		edokuPatientOverviewList = append(edokuPatientOverviewList, common.EdokuPatientOverview{
			Id:        *document.Id,
			Document:  document.DocumentationOverview,
			Patient:   patientMap[util.GetPointerValue(document.PatientId)],
			Doctor:    doctorMap[document.DoctorId],
			CreatedAt: util.NewPointer(util.ConvertTimeToMiliSecond(document.CreatedAt)),
		})
	}
	sort.SliceStable(edokuPatientOverviewList, func(i, j int) bool {
		return edokuPatientOverviewList[i].Patient.FullName < edokuPatientOverviewList[j].Patient.FullName
	})
	return &edoku.GetEdokuPatientInfoResponse{
		EdokuPatientOverviewList: edokuPatientOverviewList,
	}, nil
}

func (s *EDokuService) getPatientMap(ctx *titan.Context, documents []*EdokuDocument) (map[DocumentKey]common.Patient, error) {
	patientIds := slice.Map(documents, func(d *EdokuDocument) uuid.UUID {
		return util.GetPointerValue(d.PatientId)
	})
	patientIds = slice.Uniq(patientIds)
	patientProfiles, err := s.patientProfileRepo.GetByIds(ctx, patientIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get patient profiles: %w", err)
	}

	patientMap := make(map[uuid.UUID]common.Patient)
	for _, p := range patientProfiles {
		var activeInsurance *patient_profile_common.InsuranceInfo

		if p.PatientInfo != nil {
			activeInsurance = p.PatientInfo.GetActiveInsurance()
		}

		patientMap[util.GetPointerValue(p.Id)] = common.Patient{
			PatientId:       util.GetPointerValue(p.Id),
			FirstName:       p.FirstName,
			LastName:        p.LastName,
			DateOfBirth:     p.PatientInfo.PersonalInfo.DateOfBirth,
			PatientNumber:   int64(p.PatientInfo.PatientNumber),
			FullName:        util.GetPatientName(p),
			ActiveInsurance: activeInsurance,
		}
	}
	return patientMap, nil
}

func (s *EDokuService) getDoctorMap(ctx *titan.Context, documents []*EdokuDocument) (map[DocumentKey]common.Doctor, error) {
	doctorIds := slice.Map(documents, func(d *EdokuDocument) uuid.UUID {
		return d.DoctorId
	})
	doctorIds = slice.Uniq(doctorIds)
	doctorProfiles, err := s.employeeProfileRepo.GetByIds(ctx, doctorIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get doctor profiles: %w", err)
	}
	doctorMap := make(map[uuid.UUID]common.Doctor)
	for _, d := range doctorProfiles {
		doctorMap[util.GetPointerValue(d.Id)] = common.Doctor{
			DoctorId:  util.GetPointerValue(d.Id),
			FirstName: d.FirstName,
			LastName:  d.LastName,
			Initial:   d.Initial,
			Title:     util.GetPointerValue(d.Title),
			FullName:  util.GetDoctorName(d),
		}
	}
	return doctorMap, nil
}

func (s *EDokuService) DeleteDocumentationOverview(ctx *titan.Context, request edoku.DeleteDocumentationOverviewRequest) error {
	entity, err := s.edmpRepo.EdokuEnrollmentDocumentRepo.GetById(ctx, request.Id)
	if err != nil {
		return fmt.Errorf("failed to get documentation overview: %w", err)
	}
	if entity == nil {
		return fmt.Errorf("documentation overview not found")
	}
	if util.GetPointerValue(entity.DocumentationOverview.DocumentStatus) != common.DocumentStatus_Saved {
		return fmt.Errorf("documentation overview is not saved")
	}
	err = s.edmpRepo.EdokuEnrollmentDocumentRepo.DeleteById(ctx, request.Id)
	if err != nil {
		return fmt.Errorf("failed to delete documentation overview: %w", err)
	}
	err = s.timelineServiceEdokuDocument.DeleteEdokuDocument(ctx, request.Id)
	if err != nil {
		return fmt.Errorf("failed to delete timeline: %w", err)
	}
	return nil
}

func (s *EDokuService) UpdateStatusEdokuDocumentByIds(ctx *titan.Context, request edoku.UpdateStatusEdokuDocumentByIdsRequest) error {
	if len(request.DocumentIds) == 0 {
		return errors.New("document ids are required")
	}
	err := s.edmpRepo.EdokuEnrollmentDocumentRepo.UpdateStatusEdokuDocumentByIds(ctx, request.DocumentIds, request.Status)
	if err != nil {
		return err
	}
	if err := s.EDMPService.timelineServiceEdokuDocument.BulkUpdateStatusEdokuDocumentInTimelineByEdokuDocumentIds(ctx, request.DocumentIds, request.Status); err != nil {
		return err
	}
	return nil
}
