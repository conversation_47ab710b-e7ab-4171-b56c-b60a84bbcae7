package service

import (
	"bytes"
	_ "embed"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math"
	"net/url"
	"os"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"text/template"
	"time"

	"emperror.dev/errors"
	"github.com/google/uuid"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	catalog_sdkt_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_sdkt"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/edmp"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/timeline"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/pkg/config/software"
	"git.tutum.dev/medi/tutum/ares/pkg/edmp_testmodule"
	"git.tutum.dev/medi/tutum/ares/pkg/minio"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	"git.tutum.dev/medi/tutum/ares/pkg/xkm"
	"git.tutum.dev/medi/tutum/ares/pkg/zip_file"
	bsnr_service "git.tutum.dev/medi/tutum/ares/service/bsnr"
	api_common "git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	catalog_sdkt_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_sdkt"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/billing_builder"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/companion_builder"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/field_adapter"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/schema_model"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/sdda_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/transfer_letter"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	kv_connect_services "git.tutum.dev/medi/tutum/ares/service/domains/kv_connect"
	"git.tutum.dev/medi/tutum/ares/service/domains/pkg/okv"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/sdda"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/billing_edmp_history"
	edmp_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/edmp"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/edmp/edmp_document"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/edmp/edmp_enrollment"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	timeline_common "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/timeline_service"
	mail_common "git.tutum.dev/medi/tutum/ares/service/mail/common"
	"git.tutum.dev/medi/tutum/ares/share"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/submodule-org/submodule.go/v2"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
)

const (
	oneDay = 24 * time.Hour
)

var billingExtensionMapping = map[common.DMPValueEnum]map[common.DocumentType]string{
	common.DMPValueEnum_MellitusType1: {
		common.DocumentType_ED: "EED1",
		common.DocumentType_FD: "EVD1",
	},
	common.DMPValueEnum_MellitusType2: {
		common.DocumentType_ED: "EED2",
		common.DocumentType_FD: "EVD2",
	},
	common.DMPValueEnum_Brustkrebs: {
		common.DocumentType_ED: "EBK",
		common.DocumentType_PD: "EBK",
		common.DocumentType_FD: "FBK",
	},
	common.DMPValueEnum_AsthmaBronchiale: {
		common.DocumentType_ED: "EEAB",
		common.DocumentType_FD: "EVAB",
	},
	common.DMPValueEnum_COPD: {
		common.DocumentType_ED: "EECO",
		common.DocumentType_FD: "EVCO",
	},
	common.DMPValueEnum_ChronicHeartFailure: {
		common.DocumentType_ED: "EEHI",
		common.DocumentType_FD: "EVHI",
	},
	common.DMPValueEnum_CoronaryArteryDisease: {
		common.DocumentType_ED: "EEKHK",
		common.DocumentType_FD: "EVKHK",
	},
	common.DMPValueEnum_Depression: {
		common.DocumentType_ED: "EEDE",
		common.DocumentType_FD: "EVDE",
	},
	common.DMPValueEnum_EDO_SkinCancer: {
		common.DocumentType_EHKS_D:     "HKSD",
		common.DocumentType_EHKS_ND:    "HKSND",
		common.DocumentType_EHKS_D_EV:  "HKSDEV",
		common.DocumentType_EHKS_ND_EV: "HKSNDEV",
	},
}

var ehksDocumentTypes = []common.DocumentType{
	common.DocumentType_EHKS_D,
	common.DocumentType_EHKS_ND,
	common.DocumentType_EHKS_D_EV,
	common.DocumentType_EHKS_ND_EV,
}

var AllowedPreoperativeList = []string{
	string(common.DMPValueEnum_Brustkrebs),
}

type EDMPService struct {
	edmpNotifier                      *edmp.EdmpNotifier
	edmpSocketNotifier                *edmp.EdmpSocketNotifier
	timelineServiceEnrollment         *timeline_service.TimelineService[common.EnrollmentInfo]
	timelineServiceEnrollmentDocument *timeline_service.TimelineService[common.DocumentationOverview]
	timelineServiceEdokuDocument      *timeline_service.TimelineService[common.EdokuDocumentationOverview]
	timelineServiceDiagnosis          *timeline_service.TimelineService[patient_encounter.EncounterDiagnoseTimeline]
	edmpRepo                          *edmp_repo.EDMPRepo
	patientProfileRepo                patient.PatientProfileDefaultRepository
	employeeProfileRepo               employee.EmployeeProfileDefaultRepository
	fieldService                      FieldService
	sddaRepo                          *sdda.SddaRepo
	patientProfileService             profile.PatientProfileService
	catalogSdktService                *catalog_sdkt_service.CatalogSdktService
	edmpTestmodulePath                string
	html2PdfPath                      string
	softwareConfig                    software.SoftwareConfig
	bucketDmpBilling                  string
	minioClient                       *minio.Minio
	scheinRepo                        schein.ScheinRepoDefaultRepository
	kvConnectService                  *kv_connect_services.KvConnectService
	bsnrService                       *bsnr_service.BSNRService
	insurancePrintingGetter           *patient_profile_common.InsurancePrintingGetter
}

type CompanionItem struct {
	CreatedDate     int64
	BsnrCode        string
	IKNumber        string
	FileArchiveName string
	DmpValue        string
}

var EDMPServiceMod = submodule.Make[*EDMPService](
	newEDMPService,
	sdda.SDDARepoMod,
	timeline_service.TimelineServiceEnrollmentMod,
	timeline_service.TimelineServiceEnrollmentDocumentMod,
	timeline_service.TimelineServiceEdokuDocumentMod,
	timeline_service.TimelineServiceEdokuDocumentMod,
	timeline_service.TimelineServiceDiagnosisMod,
	config.SocketServiceClientMod,
	share.PatientProfileServiceMod,
	minio.MinioMod,
	kv_connect_services.KvConnectServiceMod,
	edmp_repo.EDMPRepoMod,
	bsnr_service.BSNRServiceMod,
	timeline_service.TimelineServiceAnyMod,
	share.InsurancePrinterNameGetterMod,
	catalog_sdkt_service.CatalogSdktServiceMod,
	config.MvzAppConfigMod,
)

func newEDMPService(
	sddaRepo *sdda.SddaRepo,
	timelineServiceEnrollment *timeline_service.TimelineService[common.EnrollmentInfo],
	timelineServiceEnrollmentDocument *timeline_service.TimelineService[common.DocumentationOverview],
	timelineServiceEdokuDocument *timeline_service.TimelineService[common.EdokuDocumentationOverview],
	timelineServiceDiagnosis *timeline_service.TimelineService[patient_encounter.EncounterDiagnoseTimeline],
	socket *socket_api.SocketServiceClient,
	patientProfileService profile.PatientProfileService,
	minioClient *minio.Minio,
	kvConnectService *kv_connect_services.KvConnectService,
	edmpRepo *edmp_repo.EDMPRepo,
	bsnrService *bsnr_service.BSNRService,
	catalogSdktService *catalog_sdkt_service.CatalogSdktService,
	timelineServiceAny *timeline_service.TimelineService[any],
	insurancePrintingGetter *patient_profile_common.InsurancePrintingGetter,
	appConfig *config.MvzAppConfigs,
) *EDMPService {
	edmpService := &EDMPService{
		edmpNotifier:                      edmp.NewEdmpNotifier(),
		edmpSocketNotifier:                edmp.NewEdmpSocketNotifier(socket),
		timelineServiceEnrollment:         timelineServiceEnrollment,
		timelineServiceEnrollmentDocument: timelineServiceEnrollmentDocument,
		timelineServiceEdokuDocument:      timelineServiceEdokuDocument,
		timelineServiceDiagnosis:          timelineServiceDiagnosis,
		edmpRepo:                          edmpRepo,
		patientProfileRepo:                patient.NewPatientProfileDefaultRepository(),
		employeeProfileRepo:               employee.NewEmployeeProfileDefaultRepository(),
		sddaRepo:                          sddaRepo,
		patientProfileService:             patientProfileService,
		catalogSdktService:                catalogSdktService,
		edmpTestmodulePath:                appConfig.EdmpTestmoduleUrl,
		html2PdfPath:                      appConfig.Html2PdfPath,
		softwareConfig:                    appConfig.SoftwareConfig,
		minioClient:                       minioClient,
		bucketDmpBilling:                  appConfig.MinioClientConfig.BucketDmpBilling,
		scheinRepo:                        schein.NewScheinRepoDefaultRepository(),
		kvConnectService:                  kvConnectService,
		bsnrService:                       bsnrService,
		insurancePrintingGetter:           insurancePrintingGetter,
	}
	timelineServiceAny.HookBeforeAction.RegisterOnDeleteFunc(edmpService.handleTimelineDelete)
	timelineServiceAny.HookBeforeAction.RegisterOnUpdateFunc(edmpService.handleTimelineUpdate)

	kvConnectService.RegisterMailItemReceivedFunc(edmpService.handleMailReceiver)
	return edmpService
}

// GetBillingHistoryId returns the billing history id from the email header In-Reply-To
// <<EMAIL>> -> 66e29a69-a246-4647-8133-484f39276675
var regexExtractUUID = regexp.MustCompile(`(?i)([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})`)

var ehksDocumentTypeValue = map[common.DocumentType]string{
	common.DocumentType_EHKS_D:     "HKS_D",
	common.DocumentType_EHKS_ND:    "HKS_ND",
	common.DocumentType_EHKS_D_EV:  "HKS_D_EV",
	common.DocumentType_EHKS_ND_EV: "HKS_ND_EV",
}

func extractUUID(k string) *uuid.UUID {
	if k == "" {
		return nil
	}
	// k = strings.ReplaceAll(k, ".", "-")
	matches := regexExtractUUID.FindStringSubmatch(k)
	if len(matches) != 2 {
		return nil
	}
	u, err := uuid.Parse(matches[1])
	if err != nil {
		return nil
	}
	return &u
}

func (s *EDMPService) handleTimelineDelete(ctx *titan.Context, event *timeline.EventTimelineRemove) error {
	if *event.TimelineModel.Type == timeline_common.TimelineEntityType_EDMPEnrollment_Document {
		err := s.edmpRepo.EdmpEnrollmentDocumentRepo.DeleteById(ctx, *event.TimelineModel.DocumentationOverview.DocumentationOverviewId)
		if err != nil {
			return err
		}
		s.notifier(ctx, common.EventEnrollType_FetchEnroll, nil, nil)
		return nil
	}
	return nil
}

func (s *EDMPService) DeleteByPatientId(ctx *titan.Context, patientId uuid.UUID) error {
	return s.edmpRepo.DeleteByPatientId(ctx, patientId)
}
func (s *EDMPService) handleTimelineUpdate(ctx *titan.Context, event *timeline.EventTimelineUpdate) error {
	if *event.TimelineModel.Type == timeline_common.TimelineEntityType_EDMPEnrollment_Document && event.AuditLogId != nil && *event.AuditLogId != uuid.Nil {
		documentId := event.TimelineModel.DocumentationOverview.DocumentationOverviewId
		err := s.edmpRepo.EdmpEnrollmentDocumentRepo.RestoreById(ctx, *documentId)
		if err != nil {
			return errors.WithMessage(err, "failed to restore documentation overview")
		}
		s.notifier(ctx, common.EventEnrollType_FetchEnroll, nil, nil)
		return nil
	}
	return nil
}

func (s *EDMPService) FindActiveEMDPByPatientId(ctx *titan.Context, patientId uuid.UUID) ([]string, error) {
	enrollments, err := s.edmpRepo.EdmpEnrollmentRepo.FindActiveByPatientId(ctx, patientId)
	if err != nil {
		return nil, fmt.Errorf("failed to find active enrollment by patient id: %w", err)
	}
	patientEnrollmentDmpValues := slice.Map(enrollments, func(t edmp_enrollment.EdmpEnrollmentEntity) string {
		return t.EnrollmentInfo.ParticipationForm.DMPLabelingValue
	})
	return patientEnrollmentDmpValues, err
}

func (s *EDMPService) GetActiveEnrollmentByPatientId(ctx *titan.Context, patientId uuid.UUID) ([]edmp_enrollment.EdmpEnrollmentEntity, error) {
	res, err := s.edmpRepo.EdmpEnrollmentRepo.FindActiveByPatientId(ctx, patientId)
	if err != nil {
		return nil, fmt.Errorf("failed to get active enrollment by patient id: %w", err)
	}
	if len(res) == 0 {
		return nil, nil
	}

	return res, nil
}

func (s *EDMPService) handleMailReceiver(ctx *titan.Context, mailItem mail_common.EmailItem) {
	if mailItem.Subject != "eDMP;Quittung;V1.0" {
		return
	}

	ctx.Logger().Info("received mail item", "subject", mailItem.Subject, "from", mailItem.From, "to", mailItem.To)

	messageId := extractUUID(mailItem.InReplyTo[0])
	if messageId == nil {
		return
	}

	billingHistory, err := s.edmpRepo.EdmpBillingHistoryRepo.UpdateBillingStatusByMessageId(ctx, *messageId, common.DmpBillingStatus_Feedback_Available)
	if err != nil {
		errorMsg := fmt.Sprintf("failed to update billing status by message id: %s", err.Error())
		ctx.Logger().Error(errorMsg,
			"messageId", messageId,
			"err", err,
		)
		return
	}

	if billingHistory == nil {
		warningMsg := fmt.Sprintf("billing history not found due to enrollment id %s", messageId.String())
		ctx.Logger().Warn(warningMsg)
		return
	}
	ctx.Logger().Info("update billing status by message id successfully",
		"messageId", messageId,
	)

	if billingHistory.DmpBillingStatus != common.DmpBillingStatus_Sent {
		_ = s.edmpSocketNotifier.NotifyCareProviderChangeEDMPHistoryStatus(ctx, &edmp.EventChangeEDMPHistoryStatus{
			Id:            *billingHistory.Id,
			CurrentUserId: billingHistory.CreatedBy,
		})
	}
}

func (s *EDMPService) UpdateTerminateOnTimeline(ctx *titan.Context) error {
	enrollments, err := s.edmpRepo.EdmpEnrollmentRepo.FindTerminate(ctx)
	if err != nil {
		return fmt.Errorf("failed to get enrollments: %w", err)
	}
	if len(enrollments) == 0 {
		return nil
	}
	var updateEnrollments []*edmp_enrollment.EdmpEnrollmentEntity
	for i := range enrollments {
		// update timeline
		if err := s.timelineServiceDiagnosis.UpdateDiagnoseErrorForEDMP(ctx, enrollments[i].PatientId, enrollments[i].ParticipationForm.DMPLabelingValue); err != nil {
			return fmt.Errorf("failed to update diagnose error for edmp: %w", err)
		}
		enrollments[i].ParticipationForm.EnrollStatus = util.NewPointer(common.StatusTerminated)
		updateEnrollments = append(updateEnrollments, &enrollments[i])
	}
	if len(updateEnrollments) > 0 {
		_, err = s.edmpRepo.EdmpEnrollmentRepo.UpdateMany(ctx, updateEnrollments)
		if err != nil {
			return fmt.Errorf("failed to update terminate status for enrollment: %w", err)
		}
	}
	return nil
}

// GetEnrollmentDocument
func (s *EDMPService) GetEnrollmentDocument(ctx *titan.Context, enrollmentDocumentId uuid.UUID) (*common.EnrollmentDocumentInfoModel, error) {
	enrollmentDocument, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.GetById(ctx, enrollmentDocumentId)
	if err != nil {
		return nil, err
	}

	var patient common.Patient
	if enrollmentDocument.PatientId != nil {
		patientProfile, err := s.patientProfileRepo.GetById(ctx, *enrollmentDocument.PatientId)
		if err != nil {
			return nil, err
		}
		if patientProfile == nil || patientProfile.Id == nil {
			return nil, fmt.Errorf("failed to find patient with id %s", enrollmentDocument.PatientId)
		}

		patient = common.Patient{
			PatientId:     *patientProfile.Id,
			FirstName:     patientProfile.FirstName,
			LastName:      patientProfile.LastName,
			DateOfBirth:   patientProfile.PatientInfo.PersonalInfo.DateOfBirth,
			PatientNumber: int64(patientProfile.PatientInfo.PatientNumber),
			FullName:      util.GetPatientName(patientProfile),
		}
	}

	doctor, err := s.employeeProfileRepo.GetProfileById(ctx, enrollmentDocument.DoctorId)
	if err != nil {
		return nil, err
	}
	if doctor == nil || doctor.Id == nil {
		return nil, fmt.Errorf("failed to find doctor with id %s", enrollmentDocument.DoctorId)
	}

	schein, err := s.scheinRepo.FindById(ctx, enrollmentDocument.ScheinId)
	if err != nil {
		return nil, err
	}
	if schein == nil || schein.Id == nil {
		return nil, fmt.Errorf("failed to find schein with id %s", enrollmentDocument.ScheinId)
	}

	if enrollmentDocument == nil || enrollmentDocument.Id == nil {
		return nil, nil
	}
	return &common.EnrollmentDocumentInfoModel{
		Id:                    *enrollmentDocument.Id,
		DocumentationOverview: enrollmentDocument.DocumentationOverview,
		Patient:               patient,
		Doctor: common.Doctor{
			DoctorId:  enrollmentDocument.DocumentationOverview.DoctorId,
			FirstName: doctor.FirstName,
			LastName:  doctor.LastName,
			Initial:   doctor.Initial,
			Title:     *doctor.Title,
			FullName:  util.GetDoctorName(doctor),
		},
		Schein:    schein.Schein,
		CreatedAt: util.NewPointer(util.ConvertTimeToMiliSecond(enrollmentDocument.CreatedAt)),
	}, nil
}

type CheckXPMRulesRequest struct {
	DMPLabelingValue    string
	DoctorId            uuid.UUID
	PatientId           uuid.UUID
	Fields              []common.Field
	DMPCaseNumber       string
	DocumentType        common.DocumentType
	DocumentDate        int64
	DocumentId          *uuid.UUID
	ScheinId            uuid.UUID
	DoctorRelationType  common.DoctorRelationType
	AdditionalContracts *common.AdditionalContractsEnum
	DMPVersion          string
}

type CheckXPMRulesResponse struct {
	xpmValidationResults []common.FieldValidationResult
	billingFile          common.DMPBillingFile
	xpmFile              common.DMPBillingFile
}

type CheckXPMRulesMultipleDocumentsResponse struct {
	xpmValidationResults []common.FieldValidationResult
	xpmFiles             []common.DMPBillingFile
}

type BuildXMLDataBillingFileForCheckValidationRequest struct {
	ScheinId            uuid.UUID
	PatientId           uuid.UUID
	DoctorId            uuid.UUID
	DocumentType        common.DocumentType
	DmpLabelingValue    string
	Fields              []common.Field
	DmpCaseNumber       string
	DocumentDate        int64
	DocumentAt          int64
	DoctorRelationType  common.DoctorRelationType
	AdditionalContracts *common.AdditionalContractsEnum
}

func genXPMBillingFileName(billingFileName string) string {
	return fmt.Sprintf("%s_%s", "XPM_RESULT", billingFileName)
}

func genStatisticXPMEdokuBillingFileName(edokuDocumentType common.DocumentType) string {
	return fmt.Sprintf("Statistik_%s.%s", ehksDocumentTypeValue[edokuDocumentType], "pdf")
}

func getDMPVersion(documentDate int64) string {
	yearQuarter := util.ToYearQuarter(documentDate)
	return fmt.Sprintf("%v.%v.0", yearQuarter.Year, yearQuarter.Quarter)
}

func (s *EDMPService) GetInsurancePrintingName(ctx *titan.Context, patientId, scheinId uuid.UUID) (*string, error) {
	patient, err := s.patientProfileService.GetProfileById(ctx, &profile.GetByIdRequest{
		PatientId: util.NewPointer(patientId),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get patient profile: %w", err)
	}
	if patient == nil || patient.Id == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Patient_Not_Found)
	}

	schein, err := s.scheinRepo.GetById(ctx, scheinId)
	if err != nil {
		return nil, fmt.Errorf("failed to get schein by id %v", scheinId)
	}
	if schein == nil || schein.Id == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Schein_Not_Found)
	}

	insuranceInfo := patient.PatientInfo.GetInsurance(schein.Schein.InsuranceId)
	if insuranceInfo == nil {
		return nil, fmt.Errorf("insurance info is empty: %w", err)
	}

	printingNameParams := patient_profile_common.InsurancePrintingParams{
		G4106:         schein.ScheinDetail.G4106,
		IkNumber:      schein.Schein.IkNumber,
		InsuranceInfo: insuranceInfo,
	}
	if schein.IsSvSchein() {
		printingNameParams.ScheinType = patient_profile_common.ScheinTypeSv
	} else if !schein.IsPrivateSchein() {
		printingNameParams.ScheinType = patient_profile_common.ScheinTypeInsurance
	} else {
		printingNameParams.ScheinType = patient_profile_common.ScheinTypePrivate
	}

	insurancePrintingName, err := s.insurancePrintingGetter.GetPrintingName(ctx, printingNameParams)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get insurance printing name")
	}
	return util.NewPointer(insurancePrintingName), nil
}

func (s *EDMPService) BuildXMLDataBillingFileForCheckValidation(ctx *titan.Context, req *BuildXMLDataBillingFileForCheckValidationRequest) ([]byte, error) {
	// validate
	if len(req.DoctorRelationType) == 0 {
		return nil, fmt.Errorf("doctor relation type is empty")
	}

	// fielder
	f, err := field_adapter.GetDMPFielder(common.DMPValueEnum(req.DmpLabelingValue))
	if err != nil {
		return nil, fmt.Errorf("failed to get fielder %w", err)
	}

	// patient profile
	patient, err := s.patientProfileService.GetProfileById(ctx, &profile.GetByIdRequest{
		PatientId: util.NewPointer(req.PatientId),
	})
	if err != nil || patient == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Patient_Not_Found)
	}

	// doctor profile
	employeeProfile, err := s.employeeProfileRepo.GetProfileById(ctx, req.DoctorId)
	if err != nil {
		return nil, fmt.Errorf("failed to get employee profile %w", err)
	}
	if employeeProfile == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Employee_Not_Found)
	}

	// version number
	// check this later because this only should generate when create dmp billing history/sendmail
	sequenceNumber, err := s.edmpRepo.EdmpSequence.GetSequenceNumber(ctx, employeeProfile.Bsnr, req.DmpLabelingValue, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get sequenceNumber %w", err)
	}

	bsnrInfo, err := s.bsnrService.GetById(ctx, util.GetPointerValue(employeeProfile.BsnrId))
	if err != nil {
		return nil, fmt.Errorf("failed to get bsnr info %w", err)
	}

	if bsnrInfo == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_BSNR_NOT_FOUND)
	}

	printingName, err := s.GetInsurancePrintingName(ctx, req.PatientId, req.ScheinId)
	if err != nil {
		return nil, fmt.Errorf("failed to get insurance printing name: %w", err)
	}

	schein, err := s.scheinRepo.GetById(ctx, req.ScheinId)
	if err != nil {
		return nil, fmt.Errorf("failed to get schein by insurance id %w", err)
	}
	if schein == nil || schein.Id == nil {
		return nil, fmt.Errorf("failed to get schein by id %v", schein)
	}

	b := billing_builder.BillingFieldBuilder{
		PatientProfile:     util.GetPointerValue(patient.PatientInfo),
		BsnrInfo:           util.GetPointerValue(bsnrInfo),
		EmployeeProfile:    util.GetPointerValue(employeeProfile),
		VersionNumber:      sequenceNumber,
		DocumentType:       req.DocumentType,
		DmpLabelingValue:   req.DmpLabelingValue,
		SoftwareConfig:     s.softwareConfig,
		Fielder:            f,
		Fields:             req.Fields,
		DocumentId:         uuid.New(),
		DmpCaseNumber:      req.DmpCaseNumber,
		DocumentDate:       req.DocumentDate,
		DocumentAt:         req.DocumentAt,
		PrintingName:       util.GetPointerValue(printingName),
		InsuranceId:        schein.Schein.InsuranceId,
		DoctorRelationType: req.DoctorRelationType,
	}
	documentType := req.DocumentType
	if documentType == common.DocumentType_EHKS_D && util.GetPointerValue(req.AdditionalContracts) == common.AdditionalContractsEnum_Yes {
		documentType = common.DocumentType_EHKS_D_EV
	}
	if documentType == common.DocumentType_EHKS_ND && util.GetPointerValue(req.AdditionalContracts) == common.AdditionalContractsEnum_Yes {
		documentType = common.DocumentType_EHKS_ND_EV
	}

	billingXMLdata, err := b.BuildHeader(documentType).BuildBody().ToXMl()
	if err != nil {
		return nil, fmt.Errorf("failed to build billing xml %w", err)
	}
	return billingXMLdata, nil
}

// TODO: optimize code this service later
func (s *EDMPService) CheckXPMRules(ctx *titan.Context, req CheckXPMRulesRequest) (*CheckXPMRulesResponse, error) {
	// doctor profile
	employeeProfile, err := s.employeeProfileRepo.GetProfileById(ctx, req.DoctorId)
	if err != nil {
		return nil, fmt.Errorf("failed to get employee profile %w", err)
	}
	if employeeProfile == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Employee_Not_Found)
	}
	documentCreatedAt := util.Now(ctx).UnixMilli()
	if req.DocumentId != nil {
		var document *edmp_document.EdmpEnrollmentDocumentEntity
		if req.DMPLabelingValue != string(common.DMPValueEnum_EDO_SkinCancer) {
			document, err = s.edmpRepo.EdmpEnrollmentDocumentRepo.FindById(ctx, util.GetPointerValue(req.DocumentId))
		} else {
			document, err = s.edmpRepo.EdokuEnrollmentDocumentRepo.FindById(ctx, util.GetPointerValue(req.DocumentId))
		}
		if err != nil {
			return nil, fmt.Errorf("failed to get document with documentId %v %w", req.DocumentId, err)
		}
		if document == nil || document.Id == nil {
			return nil, fmt.Errorf("failed to get document with documentId %v", req.DocumentId)
		}

		documentCreatedAt = document.CreatedAt.UnixMilli()
	}
	documentType := req.DocumentType
	if documentType == common.DocumentType_EHKS_D && util.GetPointerValue(req.AdditionalContracts) == common.AdditionalContractsEnum_Yes {
		documentType = common.DocumentType_EHKS_D_EV
	}
	if documentType == common.DocumentType_EHKS_ND && util.GetPointerValue(req.AdditionalContracts) == common.AdditionalContractsEnum_Yes {
		documentType = common.DocumentType_EHKS_ND_EV
	}

	billingXMLdata, err := s.BuildXMLDataBillingFileForCheckValidation(ctx, &BuildXMLDataBillingFileForCheckValidationRequest{
		ScheinId:            req.ScheinId,
		PatientId:           req.PatientId,
		DoctorId:            req.DoctorId,
		DocumentType:        req.DocumentType,
		DmpLabelingValue:    req.DMPLabelingValue,
		Fields:              req.Fields,
		DmpCaseNumber:       req.DMPCaseNumber,
		DocumentDate:        req.DocumentDate,
		DocumentAt:          documentCreatedAt,
		DoctorRelationType:  req.DoctorRelationType,
		AdditionalContracts: req.AdditionalContracts,
	})
	// fmt.Println("billingXMLdata", string(billingXMLdata))
	if err != nil {
		return nil, fmt.Errorf("failed to build billing xml %w", err)
	}

	billingFileName, err := GenBillingFileName(
		employeeProfile.Bsnr,
		req.DMPCaseNumber,
		req.DMPLabelingValue,
		documentType,
		util.ConvertMillisecondsToTime(documentCreatedAt),
		false,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate billing file name %w", err)
	}

	// get dmp version
	dmpVersion := req.DMPVersion

	isEhksDocument := slice.Contains(ehksDocumentTypes, req.DocumentType)
	yearQuarter := util.ToYearQuarter(req.DocumentDate)
	if isEhksDocument && yearQuarter.Year < 2025 {
		dmpVersion = "2025.1.0"
	}

	// call edmp test module
	var xpmFileType edmp_testmodule.FileType
	if isEhksDocument {
		xpmFileType = edmp_testmodule.FieldType_PDF
	} else {
		xpmFileType = edmp_testmodule.FieldType_XML
	}
	xpmResult, err := edmp_testmodule.Verify(
		s.edmpTestmodulePath,
		req.DMPLabelingValue,
		dmpVersion,
		req.DocumentType,
		billingXMLdata,
		xpmFileType,
		billingFileName,
		false,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to call edmp test module %w", err)
	}

	if xpmResult.ErrorMessage != "" {
		ctx.Logger().Error(xpmResult.ErrorMessage,
			"status", xpmResult.Status,
			"msg", xpmResult.ErrorMessage,
		)
	}

	// convert xpmResult to fieldValidationResult
	var xpmErrorResponse []common.FieldValidationResult
	if xpmResult.Status != edmp_testmodule.StatusFile_OK {
		xpmProtocolError, err := xpmResult.GetError(common.XPMFileType_Protocol, req.Fields, req.DocumentDate, false)
		if err != nil {
			return nil, fmt.Errorf("get xpm error failed: %w", err)
		}

		protocolFieldsErr := slice.Map(xpmProtocolError.XPMErrorContents, func(c common.XPMErrorContent) common.FieldValidationResult {
			return common.FieldValidationResult{
				FieldName:                 c.FieldName,
				ErrorCode:                 c.ErrorNo,
				ErrorMessage:              c.Message,
				HeaderName:                &c.HeaderName,
				FieldValidationResultType: common.FieldValidationResultType_XPMCheck,
				ErrorType:                 c.ErrorType,
			}
		})

		xpmErrorResponse = append(xpmErrorResponse, protocolFieldsErr...)
	}

	billingFileNameWithUUID := uuid.NewString() + billingFileName
	billingFileReader := bytes.NewReader(billingXMLdata)
	_, err = s.minioClient.PutObject(ctx, s.bucketDmpBilling, billingFileNameWithUUID, billingFileReader, billingFileReader.Size(), minio.PutObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to upload billing file to minio: %w", err)
	}

	xpmFileName := ""
	if isEhksDocument {
		xpmFileName = genStatisticXPMEdokuBillingFileName(documentType)
	} else {
		xpmFileName = genXPMBillingFileName(billingFileNameWithUUID)
	}

	var xmlTypeForDecode string
	if isEhksDocument {
		xmlTypeForDecode = xpmResult.StatisticBase64
	} else {
		xmlTypeForDecode = xpmResult.ProtocolsBase64
	}
	xpmXMLdata, err := base64.StdEncoding.DecodeString(xmlTypeForDecode)
	if err != nil {
		return nil, fmt.Errorf("failed to decode xpm xml %w", err)
	}
	xpmFileReader := bytes.NewReader(xpmXMLdata)
	_, err = s.minioClient.PutObject(ctx, s.bucketDmpBilling, xpmFileName, xpmFileReader, xpmFileReader.Size(), minio.PutObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to upload xpm file to minio: %w", err)
	}

	return &CheckXPMRulesResponse{
		xpmValidationResults: xpmErrorResponse,
		billingFile: common.DMPBillingFile{
			FileName: billingFileNameWithUUID,
			FilePath: fmt.Sprintf("%s/%s", s.bucketDmpBilling, billingFileNameWithUUID),
			FileType: common.DMPBillingHistoryFileType_Billing,
		},
		xpmFile: common.DMPBillingFile{
			FileName: xpmFileName,
			FilePath: fmt.Sprintf("%s/%s", s.bucketDmpBilling, xpmFileName),
			FileType: common.DMPBillingHistoryFileType_Protocol,
		},
	}, nil
}

func (s *EDMPService) CheckXPMRulesWithZip(ctx *titan.Context, req CheckXPMRulesRequest, zipFileData []byte, zipFileName, dmpVersion string, xpmFileType edmp_testmodule.FileType, documentTypeMap map[common.DocumentType]common.DocumentType) (*CheckXPMRulesMultipleDocumentsResponse, error) {
	xpmXmlResult, err := edmp_testmodule.Verify(
		s.edmpTestmodulePath,
		req.DMPLabelingValue,
		dmpVersion,
		req.DocumentType,
		zipFileData,
		edmp_testmodule.FieldType_XML,
		zipFileName,
		true,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to call edmp test module %w", err)
	}
	var xpmErrorResponse []common.FieldValidationResult

	if xpmXmlResult.Status != edmp_testmodule.StatusFile_OK {
		xpmProtocolError, err := xpmXmlResult.GetError(common.XPMFileType_Statistic, req.Fields, req.DocumentDate, true)
		if err != nil {
			return nil, fmt.Errorf("get xpm error failed: %w", err)
		}

		protocolFieldsErr := slice.Map(xpmProtocolError.XPMErrorContents, func(c common.XPMErrorContent) common.FieldValidationResult {
			return common.FieldValidationResult{
				FieldName:                 c.FieldName,
				ErrorCode:                 c.ErrorNo,
				ErrorMessage:              c.Message,
				HeaderName:                &c.HeaderName,
				FieldValidationResultType: common.FieldValidationResultType_XPMCheck,
				ErrorType:                 c.ErrorType,
				BillingFileName:           c.BillingFileName,
			}
		})

		xpmErrorResponse = append(xpmErrorResponse, protocolFieldsErr...)
	}

	xpmResult, err := edmp_testmodule.Verify(
		s.edmpTestmodulePath,
		req.DMPLabelingValue,
		dmpVersion,
		req.DocumentType,
		zipFileData,
		xpmFileType,
		zipFileName,
		true,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to call edmp test module %w", err)
	}

	if xpmResult.ErrorMessage != "" {
		ctx.Logger().Error(xpmResult.ErrorMessage,
			"status", xpmResult.Status,
			"msg", xpmResult.ErrorMessage,
		)
	}

	documentTypes := slice.Keys(documentTypeMap)
	if len(documentTypes) == 0 {
		return nil, fmt.Errorf("documenttype is empty")
	}

	var xpmFiles []common.DMPBillingFile
	for _, documentType := range documentTypes {
		xpmStatisticFileName := genStatisticXPMEdokuBillingFileName(documentType)
		var statisticData []byte
		var err error
		if documentType == common.DocumentType_EHKS_D {
			statisticData, err = base64.StdEncoding.DecodeString(xpmResult.StatisticBase64)
		} else {
			statisticData, err = base64.StdEncoding.DecodeString(xpmResult.StatisticBase64ND)
		}
		if err != nil {
			return nil, fmt.Errorf("failed to decode xpm statistic %w", err)
		}
		xpmStatisticFileReader := bytes.NewReader(statisticData)
		_, err = s.minioClient.PutObject(ctx, s.bucketDmpBilling, xpmStatisticFileName, xpmStatisticFileReader, xpmStatisticFileReader.Size(), minio.PutObjectOptions{})
		if err != nil {
			return nil, fmt.Errorf("failed to upload xpm file to minio: %w", err)
		}
		xpmFiles = append(xpmFiles, common.DMPBillingFile{
			FileName: xpmStatisticFileName,
			FilePath: fmt.Sprintf("%s/%s", s.bucketDmpBilling, xpmStatisticFileName),
			FileType: common.DMPBillingHistoryFileType_Statistic,
		})
	}
	return &CheckXPMRulesMultipleDocumentsResponse{
		xpmValidationResults: xpmErrorResponse,
		xpmFiles:             xpmFiles,
	}, nil
}

func isPlausible(validationResults []common.FieldValidationResult) bool {
	if len(validationResults) == 0 {
		return true
	}

	for _, v := range validationResults {
		isSelfCheckError := v.FieldValidationResultType == common.FieldValidationResultType_SelfCheck && v.ErrorType == common.ErrorType_Error
		if isSelfCheckError {
			return false
		}

		isXPMCheck := v.FieldValidationResultType == common.FieldValidationResultType_XPMCheck
		isFieldError := v.ErrorType == common.ErrorType_Error
		isSchemaError := v.ErrorType == common.ErrorType_Schema
		if isXPMCheck && (isFieldError || isSchemaError) {
			return false
		}
	}

	return true
}

// remove empty values and nested fields of a field
func normalizeField(field common.Field) common.Field {
	// remove empty values of field
	field.Values = slice.Filter(field.Values, func(fv common.FieldValue) bool {
		return fv.Value != ""
	})

	for i := range field.Fields {
		// remove empty values of nested field
		field.Fields[i].Values = slice.Filter(field.Fields[i].Values, func(fv common.FieldValue) bool {
			return fv.Value != ""
		})
	}

	// remove empty nested field
	field.Fields = slice.Filter(field.Fields, func(f common.Field) bool {
		return len(f.Values) > 0
	})
	return field
}

// remove field with empty values and nested fields in a slice of fields
func normalizeFields(fields []common.Field, fieldName string) []common.Field {
	for i := range fields {
		fields[i] = normalizeField(fields[i])
	}
	return slice.Filter(fields, func(f common.Field) bool {
		return (fieldName != "" && f.Name == fieldName) || (len(f.Values) > 0 || len(f.Fields) > 0)
	})
}

func (s *EDMPService) getFdDocuments(ctx *titan.Context, enrollmentId uuid.UUID) ([]common.DocumentationOverview, error) {
	enrollment, err := s.edmpRepo.EdmpEnrollmentRepo.GetActiveById(ctx, enrollmentId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get enrollment")
	}
	if enrollment == nil || enrollment.Id == nil {
		return nil, errors.New("enrollment not found")
	}

	documents, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.GetByEnrollmentIdAndDocumentType(ctx, enrollmentId, common.DocumentType_FD)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get documents")
	}
	if len(documents) == 0 {
		return nil, nil
	}

	return slice.Map(documents, func(d edmp_document.EdmpEnrollmentDocumentEntity) common.DocumentationOverview {
		return d.DocumentationOverview
	}), nil
}

type CheckPlausibility func(ctx *titan.Context, req edmp.CheckPlausibilityRequest) (*edmp.CheckPlausibilityResponse, error)

var CheckPlausibilityFlow = submodule.Make[CheckPlausibility](func(edmpService *EDMPService) CheckPlausibility {
	return edmpService.CheckPlausibility
}, EDMPServiceMod)

// CheckPlausibility
func (s *EDMPService) CheckPlausibility(ctx *titan.Context, req edmp.CheckPlausibilityRequest) (*edmp.CheckPlausibilityResponse, error) {
	if req.DMPVersion == "" {
		req.DMPVersion = getDMPVersion(req.DocumentDate)
	}
	if req.DocumentType == common.DocumentType_PD && !util.Contains(AllowedPreoperativeList, req.DMPLabelingValue) {
		return nil, fmt.Errorf("preoperative is not allowed for this dmp value %v", req.DMPLabelingValue)
	}
	if req.DocumentType == common.DocumentType_PHQ9_ED || req.DocumentType == common.DocumentType_PHQ9_FD {
		return nil, fmt.Errorf("cannot check plausibility for PHQ9 form %v", req.DMPLabelingValue)
	}

	var (
		xpmResult         *CheckXPMRulesResponse
		validationResults []common.FieldValidationResult
		err               error
		fdDocuments       []common.DocumentationOverview
	)

	// check enrollment
	enrollment, err := s.getEnrollmentById(ctx, req.EnrollmentId)
	if err != nil {
		return nil, err
	}

	// add common fields
	fields := s.addCommonFields(req.RelatedFields, req.DocumentType, req.DMPLabelingValue, enrollment)
	req.RelatedFields = fields

	// patient profile
	patient, err := s.patientProfileService.GetProfileById(ctx, &profile.GetByIdRequest{
		PatientId: util.NewPointer(req.PatientId),
	})
	if err != nil || patient == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Patient_Not_Found)
	}
	patientAge := util.GetPointerValue(patient.GetAge(ctx))

	if req.DMPLabelingValue == string(common.DMPValueEnum_MellitusType1) || req.DMPLabelingValue == string(common.DMPValueEnum_MellitusType2) {
		fdDocuments, err = s.getFdDocuments(ctx, req.EnrollmentId)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get fd fields in past")
		}
	}
	if req.Field != nil {
		fields := normalizeFields(req.RelatedFields, req.Field.Name)
		validationResults, err = s.fieldService.CheckRule4SelectedFields(ctx, CheckRulesRequest{
			fields:       fields,
			dmpValue:     req.DMPLabelingValue,
			documentType: req.DocumentType,
			patientAge:   patientAge,
			documentDate: req.DocumentDate,
			fdDocuments:  fdDocuments,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to check rule for one field: %w", err)
		}
	} else {
		fields := normalizeFields(req.RelatedFields, "")
		validationResults, err = s.fieldService.CheckAllRules(ctx, CheckRulesRequest{
			fields:       fields,
			dmpValue:     req.DMPLabelingValue,
			documentType: req.DocumentType,
			patientAge:   patientAge,
			documentDate: req.DocumentDate,
			fdDocuments:  fdDocuments,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to check all rules: %w", err)
		}

		xpmResult, err = s.CheckXPMRules(ctx, CheckXPMRulesRequest{
			DMPLabelingValue:   req.DMPLabelingValue,
			DoctorId:           req.DoctorId,
			PatientId:          req.PatientId,
			DMPCaseNumber:      req.DMPCaseNumber,
			Fields:             fields,
			DocumentType:       req.DocumentType,
			DocumentDate:       req.DocumentDate,
			DocumentId:         req.DocumentId,
			ScheinId:           req.ScheinId,
			DoctorRelationType: req.DoctorRelationType,
			DMPVersion:         req.DMPVersion,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to check xpm rules: %w", err)
		}
		if xpmResult == nil {
			return nil, errors.New("failed to check xpm rules")
		}

		if len(xpmResult.xpmValidationResults) > 0 {
			validationResults = append(validationResults, xpmResult.xpmValidationResults...)
		}
	}
	return &edmp.CheckPlausibilityResponse{
		IsPlausible:            isPlausible(validationResults),
		FieldValidationResults: validationResults,
		BillingFile: function.Do(func() *common.DMPBillingFile {
			if xpmResult == nil {
				return nil
			}

			return util.NewPointer(xpmResult.billingFile)
		}),
		XPMPFile: function.Do(func() *common.DMPBillingFile {
			if xpmResult == nil {
				return nil
			}

			return util.NewPointer(xpmResult.xpmFile)
		}),
	}, nil
}

// GetDMPDocument
func (s *EDMPService) GetDMPDocument(ctx *titan.Context, req edmp.GetDMPDocumentRequest) ([]common.DMPDocument, error) {
	yearQuarter := util.ToYearQuarter(util.ConvertTimeToMiliSecond(req.DocumentDate))
	dmpVersion := fmt.Sprintf("%v.%v.0", yearQuarter.Year, yearQuarter.Quarter)

	res, err := s.fieldService.GetDMPDocument(req.DMPLabelingValues, dmpVersion)
	if err != nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_EDMP_Get_Document_Not_Found, fmt.Sprintf("failed to get dmp document: %s", err.Error()))
	}

	return res, nil
}

func (s *EDMPService) notifier(ctx *titan.Context,
	event common.EventEnrollType,
	enrollment *edmp_enrollment.EdmpEnrollmentEntity,
	enrollmentDocuments []edmp_document.EdmpEnrollmentDocumentEntity,
) {
	eventPayload := &edmp.EventEnrollEDMP{
		EventEnrollType: event,
	}
	defer func() {
		if err := s.edmpNotifier.NotifyEnrollEDMP(ctx, eventPayload); err != nil {
			ctx.Logger().Error(fmt.Sprintf("Failed to notify NotifyEnrollEDMP: %s", err.Error()))
		}
		if err := s.edmpSocketNotifier.NotifyCareProviderEnrollEDMP(ctx, eventPayload); err != nil {
			ctx.Logger().Error(fmt.Sprintf("Failed to notify NotifyCareProviderEnrollEDMP: %s", err.Error()))
		}
	}()

	switch event {
	case common.EventEnrollType_Enroll:
		if enrollment.Id == nil {
			return
		}
		eventPayload = &edmp.EventEnrollEDMP{
			EnrollmentInfoModel: &common.EnrollmentInfoModel{
				Id:             *enrollment.Id,
				EnrollmentInfo: enrollment.EnrollmentInfo,
			},
			EventEnrollType: event,
		}
		if _, err := s.timelineServiceEnrollment.Create(ctx, timeline_repo.TimelineEntity[common.EnrollmentInfo]{
			Id:                enrollment.Id,
			PatientId:         enrollment.PatientId,
			TreatmentDoctorId: enrollment.TreatmentDoctorId,
			Payload:           enrollment.EnrollmentInfo,
		}); err != nil {
			ctx.Logger().Error(fmt.Sprintf("Failed to create EnrollmentInfo: %s", err.Error()))
		}
		err := s.timelineServiceEnrollment.RemoveDiagnoseErrorForEDMP(ctx, enrollment.PatientId, enrollment.ParticipationForm.DMPLabelingValue)
		if err != nil {
			ctx.Logger().Error(fmt.Sprintf("Failed to remove diagnose error for edmp: %s", err.Error()))
		}
	case common.EventEnrollType_CreateDocument:
		if len(enrollmentDocuments) == 0 {
			return
		}

		eventPayload = &edmp.EventEnrollEDMP{
			EnrollmentDocumentInfoModel: slice.Map(enrollmentDocuments, func(t edmp_document.EdmpEnrollmentDocumentEntity) common.EnrollmentDocumentInfoModel {
				return common.EnrollmentDocumentInfoModel{
					Id:                    *t.Id,
					DocumentationOverview: t.DocumentationOverview,
					CreatedAt:             util.NewPointer(util.ConvertTimeToMiliSecond(t.CreatedAt)),
				}
			}),
			EventEnrollType: event,
		}

		var timelineEnrollmentDocuments []timeline_repo.TimelineEntity[common.DocumentationOverview]
		for _, v := range enrollmentDocuments {
			v.DocumentationOverviewId = v.Id
			timelineEnrollmentDocuments = append(timelineEnrollmentDocuments, timeline_repo.TimelineEntity[common.DocumentationOverview]{
				Id:                v.Id,
				PatientId:         *v.PatientId,
				TreatmentDoctorId: v.TreatmentDoctorId,
				Payload:           v.DocumentationOverview,
				Type:              timeline_common.TimelineEntityType_EDMPEnrollment_Document,
			})
		}

		_, err := s.timelineServiceEnrollmentDocument.CreateMany(ctx, timelineEnrollmentDocuments)
		if err != nil {
			ctx.Logger().Error(fmt.Sprintf("Failed to create DocumentationOverview: %s", err))
		}
	case common.EventEnrollType_Terminated:
		if len(enrollmentDocuments) == 0 {
			return
		}

		eventPayload = &edmp.EventEnrollEDMP{
			EnrollmentDocumentInfoModel: slice.Map(enrollmentDocuments, func(t edmp_document.EdmpEnrollmentDocumentEntity) common.EnrollmentDocumentInfoModel {
				return common.EnrollmentDocumentInfoModel{
					Id:                    *t.Id,
					DocumentationOverview: t.DocumentationOverview,
				}
			}),
		}

		timelineIds := slice.Map(enrollmentDocuments, func(t edmp_document.EdmpEnrollmentDocumentEntity) uuid.UUID {
			return *t.Id
		})

		_, err := s.timelineServiceEnrollmentDocument.UpdateByIdsWithCallback(
			ctx,
			timelineIds,
			func(timelineEntities []timeline_repo.TimelineEntity[common.DocumentationOverview]) []timeline_repo.TimelineEntity[common.DocumentationOverview] {
				return slice.Map(timelineEntities, func(t timeline_repo.TimelineEntity[common.DocumentationOverview]) timeline_repo.TimelineEntity[common.DocumentationOverview] {
					for _, enrollmentDocument := range enrollmentDocuments {
						if *t.Id == *enrollmentDocument.Id {
							enrollmentDocument.DocumentationOverview.DocumentationOverviewId = enrollmentDocument.Id
							t.Payload = enrollmentDocument.DocumentationOverview
						}
					}
					return t
				})
			},
		)
		if err != nil {
			ctx.Logger().Error(fmt.Sprintf("Failed to update DocumentationOverview: %s", err.Error()))
		}
	case common.EventEnrollType_SaveDocument, common.EventEnrollType_FinishDocument, common.EventEnrollType_BilledDocument:
		if len(enrollmentDocuments) == 0 {
			return
		}
		eventPayload = &edmp.EventEnrollEDMP{
			EnrollmentDocumentInfoModel: slice.Map(enrollmentDocuments, func(t edmp_document.EdmpEnrollmentDocumentEntity) common.EnrollmentDocumentInfoModel {
				return common.EnrollmentDocumentInfoModel{
					Id:                    *t.Id,
					DocumentationOverview: t.DocumentationOverview,
					CreatedAt:             util.NewPointer(util.ConvertTimeToMiliSecond(t.CreatedAt)),
				}
			}),
			EventEnrollType: event,
		}
		timelineIds := slice.Map(enrollmentDocuments, func(t edmp_document.EdmpEnrollmentDocumentEntity) uuid.UUID {
			return *t.Id
		})
		_, err := s.timelineServiceEnrollmentDocument.UpdateByIdsWithCallback(
			ctx,
			timelineIds,
			func(timelineEntities []timeline_repo.TimelineEntity[common.DocumentationOverview]) []timeline_repo.TimelineEntity[common.DocumentationOverview] {
				return slice.Map(timelineEntities, func(t timeline_repo.TimelineEntity[common.DocumentationOverview]) timeline_repo.TimelineEntity[common.DocumentationOverview] {
					for _, enrollmentDocument := range enrollmentDocuments {
						if *t.Id == *enrollmentDocument.Id {
							enrollmentDocument.DocumentationOverview.DocumentationOverviewId = enrollmentDocument.Id
							t.Payload = enrollmentDocument.DocumentationOverview
						}
					}
					return t
				})
			},
		)
		if err != nil {
			ctx.Logger().Error(fmt.Sprintf("Failed to update DocumentationOverview: %s", err.Error()))
		}
	}
}

func (s *EDMPService) Enroll(ctx *titan.Context, enrollmentInfoRequest common.EnrollmentInfoRequest) ([]uuid.UUID, error) {
	if isDMPCaseNumberExist, err := s.IsDMPCaseNumberExist(ctx, enrollmentInfoRequest.DMPCaseNumber); err != nil {
		return nil, err
	} else if isDMPCaseNumberExist {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_EDMP_Case_Number_Invalid)
	}

	patient, err := s.patientProfileRepo.GetById(ctx, enrollmentInfoRequest.PatientId)
	if err != nil {
		return nil, fmt.Errorf("failed to get patient profile: %w", err)
	}
	if patient == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Patient_Not_Found)
	}

	isBreastCancer := slice.FindOne(enrollmentInfoRequest.ParticipationForms, func(pf common.ParticipationForm) bool {
		return pf.DMPLabelingValue == string(common.DMPValueEnum_Brustkrebs)
	}) != nil
	isWoman := patient.PatientInfo.PersonalInfo.Gender == patient_profile_common.W
	if isBreastCancer && !isWoman {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_EDMP_Gender_Not_Allowed)
	}

	currentActiveInsurance := patient.PatientInfo.GetActiveInsurance()
	if currentActiveInsurance == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_NotActive_InsuranceInfo)
	}

	ids := make([]uuid.UUID, 0)
	for _, p := range enrollmentInfoRequest.ParticipationForms {
		enrollmentInfo := common.EnrollmentInfo{
			DoctorId:            enrollmentInfoRequest.DoctorId,
			PatientId:           enrollmentInfoRequest.PatientId,
			DMPCaseNumber:       enrollmentInfoRequest.DMPCaseNumber,
			ParticipationForm:   p,
			PatientFrequency:    enrollmentInfoRequest.PatientFrequency,
			InsuranceProgrammes: enrollmentInfoRequest.InsuranceProgrammes,
			PatientMedicalData:  enrollmentInfoRequest.PatientMedicalData,
			TreatmentDoctorId:   enrollmentInfoRequest.TreatmentDoctorId,
			ActivatedTime: &common.ActivatedTime{
				StartDate: util.NewPointer(util.Now(ctx).UnixMilli()),
				EndDate:   nil,
			},
			InsuranceInfo: currentActiveInsurance,
		}
		entity, err := s.edmpRepo.EdmpEnrollmentRepo.Create(ctx, enrollmentInfo)
		if err != nil {
			return nil, fmt.Errorf("failed to enroll: %w", err)
		}
		if entity == nil || entity.Id == nil {
			return nil, nil
		}
		ids = append(ids, *entity.Id)
		documentEds, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.GetByFilterFunc(ctx, edmp_document.GetByPatientWithDocumentType(
			enrollmentInfoRequest.PatientId,
			common.DocumentType_ED,
			p.DMPLabelingValue,
		))
		if err != nil {
			return nil, fmt.Errorf("failed to get enrollment document: %w", err)
		}
		if len(documentEds) > 0 {
			documentEd := documentEds[len(documentEds)-1]
			// if the last ED document is not deleted, we clone this ED document for new enrollmnent
			if !documentEd.IsDeleted {
				documentEd.EnrollmentId = *entity.Id
				documentEd.DocumentationOverview.EnrollmentId = *entity.Id
				documentEd.DocumentDate = util.Now(ctx).UnixMilli()
				createDocumentResponse, err := s.CreateDocument(ctx, documentEd.DocumentationOverview, false)
				if err != nil {
					return nil, fmt.Errorf("failed to create enrollment document for ED type: %w", err)
				}
				if createDocumentResponse != nil {
					// update timeline of the last ED document with new documentaionOverviewID and EnrollmentId
					_, err = s.timelineServiceEnrollmentDocument.UpdateByDocumentationOverviewIdWithCallback(ctx, *documentEd.DocumentationOverviewId, func(timelineEntity *timeline_repo.TimelineEntity[common.DocumentationOverview]) {
						timelineEntity.Payload.DocumentationOverviewId = createDocumentResponse.DocumentId
						timelineEntity.Payload.EnrollmentId = *entity.Id
					})
					if err != nil {
						return nil, err
					}
				}
			}
		}
		s.notifier(ctx, common.EventEnrollType_Enroll, entity, nil)
	}
	return ids, nil
}

func (s *EDMPService) IsDMPCaseNumberExist(ctx *titan.Context, caseNumber string) (bool, error) {
	return s.edmpRepo.EdmpEnrollmentRepo.IsDMPCaseNumberExist(ctx, caseNumber)
}

// GetEnrollment
func (s *EDMPService) GetEnrollment(ctx *titan.Context, patientId uuid.UUID) ([]common.EnrollmentWithDocumentModel, error) {
	entities, err := s.edmpRepo.GetEnrollment(ctx, edmp_repo.GetEnrollmentRequest{
		PatientId: patientId,
	})
	if err != nil {
		return nil, err
	}

	if len(entities) == 0 {
		return nil, nil
	}

	return entities, nil
}

func (s *EDMPService) CreateDocument(ctx *titan.Context, model common.DocumentationOverview, isCreateOnTimeline bool) (*edmp.CreateDocumentResponse, error) {
	enrollment, err := s.edmpRepo.EdmpEnrollmentRepo.GetById(ctx, model.EnrollmentId)
	if err != nil {
		return nil, err
	}
	if enrollment == nil || enrollment.Id == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_EDMP_Enrollment_Not_Found)
	}

	if model.DocumentType == common.DocumentType_PD && !util.Contains(AllowedPreoperativeList, enrollment.ParticipationForm.DMPLabelingValue) {
		return nil, fmt.Errorf("preoperative is not allowed for this dmp value %v", enrollment.InsuranceInfo.DMPLabeling)
	}

	edmps := enrollment.ParticipationForm.DMPLabelingValue
	isAllowEDMPPrograms := edmps == model.DMPLabelingValue
	if !isAllowEDMPPrograms {
		return nil, pkg_errors.NewTitanCommonExceptionWithParams(
			error_code.ErrorCode_EDMP_Not_Allowed,
			map[string]string{
				"edmps": edmps,
			},
		)
	}

	if model.DocumentType == common.DocumentType_ED || model.DocumentType == common.DocumentType_PD {
		enrollmentDocuments, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.GetByEnrollmentId(ctx, model.EnrollmentId)
		if err != nil {
			return nil, fmt.Errorf("failed to get enrollment: %w", err)
		}
		isHasDocumentEd := slice.Any(enrollmentDocuments, func(t edmp_document.EdmpEnrollmentDocumentEntity) bool {
			return t.DocumentType == common.DocumentType_ED
		})
		if isHasDocumentEd && (model.DocumentType == common.DocumentType_ED) {
			return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_EDMP_ED_Exist)
		}
		if !isHasDocumentEd && (model.DocumentType == common.DocumentType_PD) {
			return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_EDMP_ED_Not_Exist)
		}
	}

	// check plausibility just uses for ED, PD, FD
	var checkPlausibilityResponse *edmp.CheckPlausibilityResponse
	isDocumentFinished := util.GetPointerValue(model.DocumentStatus) == common.DocumentStatus_Finished
	isDocumentPrinted := util.GetPointerValue(model.DocumentStatus) == common.DocumentStatus_Printed
	isPHQ9Document := model.DocumentType == common.DocumentType_PHQ9_ED || model.DocumentType == common.DocumentType_PHQ9_FD
	if !isPHQ9Document && (isDocumentFinished || isDocumentPrinted) {
		checkPlausibilityResponse, err = s.CheckPlausibility(ctx, edmp.CheckPlausibilityRequest{
			RelatedFields:      model.Fields,
			DocumentType:       model.DocumentType,
			DMPLabelingValue:   model.DMPLabelingValue,
			DoctorId:           model.DoctorId,
			PatientId:          util.GetPointerValue(model.PatientId),
			DMPCaseNumber:      model.DMPCaseNumber,
			DocumentDate:       model.DocumentDate,
			EnrollmentId:       model.EnrollmentId,
			DoctorRelationType: model.DoctorRelationType,
			ScheinId:           model.ScheinId,
		})
		if err != nil {
			return nil, err
		}
		if !checkPlausibilityResponse.IsPlausible {
			return &edmp.CreateDocumentResponse{
				DocumentId:                nil,
				CheckPlausibilityResponse: checkPlausibilityResponse,
			}, nil
		}
	}

	model.Fields = slice.Filter(model.Fields, func(t common.Field) bool {
		return len(t.Values) > 0 || t.FieldType == common.FieldType_Nested
	})
	model.EnrollStatus = util.NewPointer(common.StatusActivated)
	model.EnrollmentId = util.GetPointerValue(enrollment.Id)
	model.PatientId = util.NewPointer(enrollment.PatientId)

	// update previous score if document is PHQ9
	if isPHQ9Document {
		previousScore, err := s.GetPreviousScorePHQ9(ctx, util.GetPointerValue(model.PatientId), nil)
		if err != nil {
			return nil, fmt.Errorf("failed to get previous score: %w", err)
		}

		model.PHQ9.PreviousScore = previousScore
	}

	enrollmentDocument, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.Create(ctx, model)
	if err != nil {
		return nil, fmt.Errorf("failed to create enrollment document: %w", err)
	}
	if enrollmentDocument == nil || enrollmentDocument.Id == nil {
		return nil, nil
	}

	// document type phq9, notifier and return
	if isPHQ9Document {
		if enrollment.DMPCaseNumber != model.DMPCaseNumber {
			entity, err := s.updateDMPCaseNumber(ctx, model.DMPCaseNumber, model.EnrollmentId)
			if err != nil {
				return nil, err
			}

			if entity == nil {
				return nil, nil
			}
		}

		if isCreateOnTimeline {
			s.notifier(ctx, common.EventEnrollType_CreateDocument, nil, []edmp_document.EdmpEnrollmentDocumentEntity{util.GetPointerValue(enrollmentDocument)})
		}

		return &edmp.CreateDocumentResponse{
			DocumentId:                enrollmentDocument.Id,
			CheckPlausibilityResponse: nil,
		}, nil
	}

	// Handling for billing file -> just use for ED, PD, FD
	employeeProfile, err := s.employeeProfileRepo.GetProfileById(ctx, model.DoctorId)
	if err != nil {
		return nil, fmt.Errorf("failed to get employee profile: %w", err)
	}
	if employeeProfile == nil || employeeProfile.Id == nil {
		return nil, nil
	}

	billingFileName, err := GenBillingFileName(
		employeeProfile.Bsnr,
		enrollment.DMPCaseNumber,
		enrollmentDocument.DMPLabelingValue,
		enrollmentDocument.DocumentType,
		util.ConvertMillisecondsToTime(enrollmentDocument.CreatedAt.UnixMilli()),
		true,
	)
	if err != nil {
		return nil, err
	}
	model.DMPBillingFile.FileName = billingFileName
	model.DocumentationOverviewId = enrollmentDocument.Id
	err = s.uploadBillingFileForDocumentOverview(ctx, uploadBillingFileForDocumentOverviewRequest{
		enrollment: util.GetPointerValue(enrollment),
		documentId: util.GetPointerValue(enrollmentDocument.Id),
		model:      model,
	})
	if err != nil {
		return nil, err
	}

	billingDocument, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.UpdateDMPBillingFileById(ctx, util.GetPointerValue(enrollmentDocument.Id), common.DMPBillingFile{
		FileName: model.DMPBillingFile.FileName,
		FilePath: fmt.Sprintf("%s/%s", s.bucketDmpBilling, model.DMPBillingFile.FileName),
		FileType: common.DMPBillingHistoryFileType_Billing,
	})
	if err != nil {
		return nil, err
	}
	if billingDocument == nil {
		return nil, nil
	}

	if enrollment.DMPCaseNumber != model.DMPCaseNumber {
		entity, err := s.updateDMPCaseNumber(ctx, model.DMPCaseNumber, model.EnrollmentId)
		if err != nil {
			return nil, err
		}

		if entity == nil {
			return nil, nil
		}
	}

	if isCreateOnTimeline {
		s.notifier(ctx, common.EventEnrollType_CreateDocument, nil, []edmp_document.EdmpEnrollmentDocumentEntity{util.GetPointerValue(billingDocument)})
	}
	return &edmp.CreateDocumentResponse{
		DocumentId:                enrollmentDocument.Id,
		CheckPlausibilityResponse: checkPlausibilityResponse,
	}, nil
}

func (s *EDMPService) Terminate(ctx *titan.Context, req edmp.TerminateRequest) error {
	enrollment, err := s.edmpRepo.Terminate(ctx, req)
	if err != nil {
		return fmt.Errorf("failed to terminate EDMP enrollment: %w", err)
	}
	if enrollment == nil {
		return nil
	}

	if len(enrollment.EdmpEnrollmentDocuments) > 0 {
		s.notifier(ctx, common.EventEnrollType_Terminated, nil, enrollment.EdmpEnrollmentDocuments)
	}

	return nil
}

func (s *EDMPService) GetPreviousScorePHQ9(ctx *titan.Context, patientId uuid.UUID, createdAt *time.Time) (*int32, error) {
	document, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.GetLatestPHQ9DocumentOverview(ctx, patientId, createdAt)
	if err != nil {
		return nil, fmt.Errorf("failed to get document: %w", err)
	}
	if document == nil || document.Id == nil || document.PHQ9 == nil {
		return util.NewPointer[int32](-1), nil
	}

	previousScore := document.PHQ9.Totals[0]

	return util.NewPointer(previousScore), nil
}

func buildDmpProgram(
	patientDMPValue string,
	timelineDMPValue []string,
	enrollmentDMP []common.DmpProgram,
	filter edmp.DmpFilter,
	advanceFilters *edmp.DMPAdvanceFilters,
) []common.DmpProgram {
	mapDMPProgram := make(map[string]common.DmpProgram)

	// map dmp program of patient that supported by doctor
	dmpLabeling := common.GetDMPByValue(patientDMPValue)
	if dmpLabeling != nil {
		mapDMPProgram[dmpLabeling.Value] = common.DmpProgram{
			DMPLabelingValue: dmpLabeling.GermanName,
			EnrollStatus:     common.StatusPotential,
		}
	}

	// map dmp program on timeline that supported by doctor
	for _, v := range timelineDMPValue {
		dmpLabeling := common.GetDMPByValue(v)
		if dmpLabeling == nil {
			continue
		}
		mapDMPProgram[v] = common.DmpProgram{
			DMPLabelingValue: dmpLabeling.GermanName,
			EnrollStatus:     common.StatusPotential,
		}
	}

	// map dmp program of enrollments which have not documented yet
	for _, v := range enrollmentDMP {
		dmpLabeling := common.GetDMPByValue(v.DMPLabelingValue)
		if dmpLabeling == nil {
			continue
		}

		mapDMPProgram[v.DMPLabelingValue] = common.DmpProgram{
			DMPLabelingValue: dmpLabeling.GermanName,
			EnrollStatus:     v.EnrollStatus,
			StartDate:        v.StartDate,
			Doctor:           v.Doctor,
			TotalED:          v.TotalED,
			TotalFD:          v.TotalFD,
			TotalPED:         v.TotalPED,
			TotalPHQ9_ED:     v.TotalPHQ9_ED,
			TotalPHQ9_FD:     v.TotalPHQ9_FD,
		}
	}

	// map to list
	dmpProgramList := make([]common.DmpProgram, 0, len(mapDMPProgram))
	for _, v := range mapDMPProgram {
		dmpProgramList = append(dmpProgramList, v)
	}

	// advance filter
	if advanceFilters != nil && dmpProgramList != nil {
		dmpProgramList = slice.Filter(dmpProgramList, func(dmpProgram common.DmpProgram) bool {
			isMatchedDMP := true
			isMatchedDoctor := true
			isMatchedStatus := true
			isMatchedTime := true

			if len(advanceFilters.DMPLabelingValues) > 0 {
				isMatchedDMP = slice.Contains(advanceFilters.DMPLabelingValues, dmpProgram.DMPLabelingValue)
			}

			if len(advanceFilters.DoctorIds) > 0 {
				isMatchedDoctor = dmpProgram.Doctor != nil && slice.Contains(advanceFilters.DoctorIds, dmpProgram.Doctor.DoctorId)
			}

			if len(advanceFilters.QuarterRanges) > 0 {
				for _, item := range advanceFilters.QuarterRanges {
					yearQuarter := util.YearQuarter{
						Year:    int32(item.Year),
						Quarter: int32(item.Quarter),
					}
					quarterRange, err := yearQuarter.GetQuarterDateRange()
					if err != nil {
						continue
					}

					startDate := util.GetPointerValue(quarterRange.Start)
					dmpStartDate := util.GetPointerValue(dmpProgram.StartDate)
					endDate := util.GetPointerValue(quarterRange.End)

					isMatchedTime = startDate.UnixMilli() <= dmpStartDate && dmpStartDate <= endDate.UnixMilli()
				}
			}

			return isMatchedDMP && isMatchedDoctor && isMatchedStatus && isMatchedTime
		})
	}

	// filter potential dmp
	if filter == edmp.PotentialDmp {
		return slice.Filter(dmpProgramList, func(dmpProgram common.DmpProgram) bool {
			return dmpProgram.EnrollStatus == common.StatusPotential
		})
	}

	// filter missing document dmp
	if filter == edmp.MissingDmpDoc {
		return slice.Filter(dmpProgramList, func(dmpProgram common.DmpProgram) bool {
			return dmpProgram.EnrollStatus == common.StatusActivated
		})
	}

	// filter all
	return dmpProgramList
}

// getTotalEDFDByEnrollmentIdAndDMPValue
// return total ED and FD , PED
func getTotalEDFDByEnrollmentIdAndDMPValue(
	enrollmentDocuments []edmp_document.EdmpEnrollmentDocumentEntity,
	enrollmentId uuid.UUID, dmpValue string,
) (totalED, totalFD, totalPED, totalPHQ9_ED, totalPHQ9_FD common.Total) {
	enrollmentDocumentsMatch := slice.Filter(enrollmentDocuments, func(t edmp_document.EdmpEnrollmentDocumentEntity) bool {
		return t.EnrollmentId == enrollmentId
	})

	if len(enrollmentDocumentsMatch) == 0 {
		return common.Total{}, common.Total{}, common.Total{}, common.Total{}, common.Total{}
	}

	enrollmentDocumentsMatchDMPValue := slice.Filter(enrollmentDocumentsMatch, func(t edmp_document.EdmpEnrollmentDocumentEntity) bool {
		return t.DMPLabelingValue == dmpValue
	})

	if len(enrollmentDocumentsMatchDMPValue) == 0 {
		return common.Total{}, common.Total{}, common.Total{}, common.Total{}, common.Total{}
	}

	for _, v := range enrollmentDocumentsMatchDMPValue {
		isCompleted := (util.GetPointerValue(v.DocumentStatus) == common.DocumentStatus_Finished) || (util.GetPointerValue(v.DocumentStatus) == common.DocumentStatus_Printed)
		isIncompleted := util.GetPointerValue(v.DocumentStatus) == common.DocumentStatus_Saved
		isSubmitted := util.GetPointerValue(v.DocumentStatus) == common.DocumentStatus_Billed

		// ED
		if (v.DocumentType == common.DocumentType_ED) && isCompleted {
			totalED.Complete++
		}
		if (v.DocumentType == common.DocumentType_ED) && isIncompleted {
			totalED.InComplete++
		}
		if (v.DocumentType == common.DocumentType_ED) && isSubmitted {
			totalED.Submitted++
		}

		// FD
		if v.DocumentType == common.DocumentType_FD && isCompleted {
			totalFD.Complete++
		}
		if v.DocumentType == common.DocumentType_FD && isIncompleted {
			totalFD.InComplete++
		}
		if (v.DocumentType == common.DocumentType_FD) && isSubmitted {
			totalFD.Submitted++
		}

		// PED
		if v.DocumentType == common.DocumentType_PD && isCompleted {
			totalPED.Complete++
		}
		if v.DocumentType == common.DocumentType_PD && isIncompleted {
			totalPED.InComplete++
		}
		if (v.DocumentType == common.DocumentType_PD) && isSubmitted {
			totalPED.Submitted++
		}

		// PHQ9 ED
		if v.DocumentType == common.DocumentType_PHQ9_ED && isCompleted {
			totalPHQ9_ED.Complete++
		}
		if v.DocumentType == common.DocumentType_PHQ9_ED && isIncompleted {
			totalPHQ9_ED.InComplete++
		}
		if (v.DocumentType == common.DocumentType_PHQ9_ED) && isSubmitted {
			totalPHQ9_ED.Submitted++
		}

		// PHQ9 FD
		if v.DocumentType == common.DocumentType_PHQ9_FD && isCompleted {
			totalPHQ9_FD.Complete++
		}
		if v.DocumentType == common.DocumentType_PHQ9_FD && isIncompleted {
			totalPHQ9_FD.InComplete++
		}
		if (v.DocumentType == common.DocumentType_PHQ9_FD) && isSubmitted {
			totalPHQ9_FD.Submitted++
		}
	}
	return totalED, totalFD, totalPED, totalPHQ9_ED, totalPHQ9_FD
}

func getDMPProgramFormEnrollmentEntities(
	ctx *titan.Context,
	doctor employee.EmployeeProfile,
	enrollmentByPatient []edmp_enrollment.EdmpEnrollmentEntity,
	mapDocumentsWithEnrollmentId map[uuid.UUID][]edmp_document.EdmpEnrollmentDocumentEntity,
) []common.DmpProgram {
	return slice.Map(enrollmentByPatient, func(e edmp_enrollment.EdmpEnrollmentEntity) common.DmpProgram {
		doctorEnrolled := function.Do(func() *common.Doctor {
			if *doctor.Id == e.DoctorId {
				return &common.Doctor{
					DoctorId:  util.GetPointerValue(doctor.Id),
					FirstName: doctor.FirstName,
					LastName:  doctor.LastName,
					Initial:   doctor.Initial,
					Title:     util.GetPointerValue(doctor.Title),
					FullName:  util.GetDoctorName(doctor),
				}
			}

			return nil
		})

		totalED, totalFD, totalPED, totalPHQ9_ED, totalPHQ9_FD := getTotalEDFDByEnrollmentIdAndDMPValue(
			mapDocumentsWithEnrollmentId[*e.Id],
			*e.Id,
			e.ParticipationForm.DMPLabelingValue,
		)

		return common.DmpProgram{
			DMPLabelingValue: e.ParticipationForm.DMPLabelingValue,
			EnrollStatus:     e.GetDmpStatus(ctx, util.Now(ctx)),
			StartDate:        util.NewPointer(util.ConvertTimeToMiliSecond(e.CreatedAt)),
			Doctor:           doctorEnrolled,
			TotalED:          totalED,
			TotalFD:          totalFD,
			TotalPED:         &totalPED,
			TotalPHQ9_ED:     &totalPHQ9_ED,
			TotalPHQ9_FD:     &totalPHQ9_FD,
		}
	})
}

func getDMPLabelingFromDiagnosisTimeline(diagnoses []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) []string {
	var result []string
	for _, v := range diagnoses {
		p := common.GetDMPByICDCode(v.Payload.Code)
		if p == nil {
			continue
		}
		result = append(result, p.Value)
	}
	return result
}

/*
*Note: All the dmp value should be supported by the doctor singing in

1. Filter all -> show dmp patient infos with enrollments actived
2. Filter potential -> show dmp patient infos with diagnosed on time line but without enrollment
3. Filter missing document -> shbow dmp patient infos with missing documents but already enrollment
*/
func (s *EDMPService) GetDmpPatientInfo(ctx *titan.Context, req edmp.GetDmpPatientInfoRequest) ([]common.DmpPatientInfo, error) {
	// find all patients and just get patient who have active insurance
	patients, err := s.patientProfileRepo.Find(ctx, bson.M{patient.Field_CollectionSequenceNumber: bson.M{
		"$exists": false,
	}})
	if err != nil {
		return nil, fmt.Errorf("failed to get patients: %w", err)
	}

	if len(patients) > 0 && req.DMPAdvancedFilters != nil && util.GetPointerValue(req.DMPAdvancedFilters.PatientName) != "" {
		patients = slice.Filter(patients, func(p *patient.PatientProfile) bool {
			if p.PatientInfo.GenericInfo.PatientType == patient_profile_common.PatientType_Private {
				return false
			}

			patientName := util.GetPatientName(p)

			return strings.Contains(strings.ToLower(patientName), strings.ToLower(util.GetPointerValue(req.DMPAdvancedFilters.PatientName)))
		})
	}

	if len(patients) == 0 {
		return nil, nil
	}

	patientsHaveActiveInsurance := slice.Filter(patients, func(p *patient.PatientProfile) bool {
		return p.PatientInfo != nil && p.PatientInfo.GetActiveInsurance() != nil
	})
	if len(patientsHaveActiveInsurance) == 0 {
		return nil, fmt.Errorf("there is no patients have active insurance")
	}

	patientIds := slice.Map(patientsHaveActiveInsurance, func(p *patient.PatientProfile) uuid.UUID {
		return util.GetPointerValue(p.Id)
	})

	// get doctor and get dmp values supported by the doctor who signing in
	doctor, err := s.employeeProfileRepo.GetProfileById(ctx, util.GetPointerValue(ctx.UserInfo().UserUUID()))
	if err != nil {
		return nil, fmt.Errorf("failed to get employees: %w", err)
	}
	if doctor == nil || doctor.Id == nil {
		return nil, fmt.Errorf("doctor is empty when get dmp patients info")
	}
	dmpValuesSupportedByDoctor := slice.Map(util.GetPointerValue(doctor.DmpPrograms), func(dmpGermanName string) string {
		dmpLabel := util.GetPointerValue(common.GetDMPByGermanName(dmpGermanName))
		return dmpLabel.Value
	})

	// get all doctors
	doctors, err := s.employeeProfileRepo.Find(ctx, bson.M{})
	if err != nil {
		return nil, fmt.Errorf("failed to get doctors: %w", err)
	}
	if len(doctors) == 0 {
		return nil, fmt.Errorf("doctors are empty when get dmp patients info")
	}

	// find all activated enrollments by patient id
	enrollments, err := s.edmpRepo.EdmpEnrollmentRepo.FindByPatientIds(ctx, patientIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get enrollment by patient ids: %w", err)
	}
	// if filter type is missing doccument but enrollments are empty -> return empty list
	if req.Filter == edmp.MissingDmpDoc && len(enrollments) == 0 {
		return []common.DmpPatientInfo{}, nil
	}

	mapEnrollmentWithPatientId := make(map[uuid.UUID][]edmp_enrollment.EdmpEnrollmentEntity)
	for _, enroll := range enrollments {
		mapEnrollmentWithPatientId[enroll.PatientId] = append(mapEnrollmentWithPatientId[enroll.PatientId], enroll)
	}

	// get all documents by patients ids
	documents, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.FindByPatientIds(ctx, patientIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get enrollment documents: %w", err)
	}

	mapDocumentsWithEnrollmentId := make(map[uuid.UUID][]edmp_document.EdmpEnrollmentDocumentEntity)
	for _, document := range documents {
		mapDocumentsWithEnrollmentId[document.EnrollmentId] = append(mapDocumentsWithEnrollmentId[document.EnrollmentId], document)
	}

	// filter enrollments have not documented yet
	var enrollsHaveNotDocumented []edmp_enrollment.EdmpEnrollmentEntity
	for _, e := range enrollments {
		doc := slice.FindOne(documents, func(d edmp_document.EdmpEnrollmentDocumentEntity) bool {
			return util.GetPointerValue(e.Id) == d.EnrollmentId
		})

		if doc == nil {
			enrollsHaveNotDocumented = append(enrollsHaveNotDocumented, e)
		}
	}
	mapPatientIdWithEnrollsHaveNotDocument := make(map[uuid.UUID][]edmp_enrollment.EdmpEnrollmentEntity)
	for _, enroll := range enrollsHaveNotDocumented {
		mapPatientIdWithEnrollsHaveNotDocument[enroll.PatientId] = append(mapPatientIdWithEnrollsHaveNotDocument[enroll.PatientId], enroll)
	}

	// get diagnosis DMP suggestions by patient ids
	diagnosisDMPSuggestion, err := s.timelineServiceDiagnosis.FindDiagnosisDMPSuggestions(ctx, patientIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get diagnosis DMP suggestions: %w", err)
	}

	dmpPatientInfo := []common.DmpPatientInfo{}
	for _, p := range patients {
		patientId := util.GetPointerValue(p.Id)
		insuranceInfo := p.PatientInfo.GetActiveInsurance()

		patient := common.Patient{
			PatientId:     patientId,
			FirstName:     p.FirstName,
			LastName:      p.LastName,
			DateOfBirth:   p.PatientInfo.PersonalInfo.DateOfBirth,
			PatientNumber: int64(p.PatientInfo.PatientNumber),
			FullName:      util.GetPatientName(p),
		}

		enrollmentsByPatient := function.Do(func() []edmp_enrollment.EdmpEnrollmentEntity {
			if len(req.Filter) == 0 {
				return mapEnrollmentWithPatientId[patientId]
			}

			return mapPatientIdWithEnrollsHaveNotDocument[patientId]
		})
		dmpEnrolledByPatient := slice.Map(enrollmentsByPatient, func(enroll edmp_enrollment.EdmpEnrollmentEntity) string {
			return enroll.EnrollmentInfo.ParticipationForm.DMPLabelingValue
		})

		dmpLabelingFromTimeline := function.Do(func() []string {
			// case filter all
			if len(req.Filter) == 0 {
				return []string{}
			}

			dmpValuesTimeline := getDMPLabelingFromDiagnosisTimeline(diagnosisDMPSuggestion[patientId])
			var dmpValues []string

			for i := 0; i < len(dmpValuesTimeline); i++ {
				isEnrolled := util.Contains(dmpEnrolledByPatient, dmpValuesTimeline[i])

				if isEnrolled {
					continue
				}

				dmpValue := slice.FindOne(dmpValuesSupportedByDoctor, func(s string) bool {
					return s == dmpValuesTimeline[i]
				})
				if dmpValue != nil {
					dmpValues = append(dmpValues, util.GetPointerValue(dmpValue))
				}
			}

			return dmpValues
		})

		dmpProgramEnrolled := getDMPProgramFormEnrollmentEntities(
			ctx,
			util.GetPointerValue(doctor),
			enrollmentsByPatient,
			mapDocumentsWithEnrollmentId,
		)

		supportedDMP := slice.FindOne(dmpValuesSupportedByDoctor, func(dp string) bool {
			return insuranceInfo != nil && dp == insuranceInfo.DMPLabeling
		})

		dmpProgram := buildDmpProgram(
			util.GetPointerValue(supportedDMP),
			dmpLabelingFromTimeline,
			dmpProgramEnrolled,
			req.Filter,
			req.DMPAdvancedFilters,
		)
		if len(dmpProgram) > 0 {
			dmpPatientInfo = append(dmpPatientInfo, common.DmpPatientInfo{
				Patient: patient,
				InsuranceNumber: function.Do(func() *string {
					if insuranceInfo == nil {
						return nil
					}
					return insuranceInfo.InsuranceNumber
				}),
				DmpProgram: dmpProgram,
			})
		}
	}

	return dmpPatientInfo, nil
}

type uploadBillingFileForDocumentOverviewRequest struct {
	enrollment edmp_enrollment.EdmpEnrollmentEntity
	documentId uuid.UUID
	model      common.DocumentationOverview
}

func (s *EDMPService) uploadBillingFileForDocumentOverview(ctx *titan.Context, request uploadBillingFileForDocumentOverviewRequest) error {
	patient, err := s.patientProfileRepo.GetById(ctx, request.enrollment.PatientId)
	if err != nil {
		return fmt.Errorf("failed to get patient: %w", err)
	}
	if patient == nil || patient.Id == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Patient_Not_Found)
	}

	employeeProfile, err := s.employeeProfileRepo.GetProfileById(ctx, request.model.DoctorId)
	if err != nil {
		return fmt.Errorf("failed to get employee profile: %w", err)
	}
	if employeeProfile == nil || employeeProfile.Id == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Employee_Not_Found)
	}

	dmpValue := request.enrollment.ParticipationForm.DMPLabelingValue
	f, err := field_adapter.GetDMPFielder(common.DMPValueEnum(dmpValue))
	if err != nil {
		return fmt.Errorf("failed to get fielder: %w", err)
	}

	sequenceType := util.NewPointer(common.SequenceType_Billing)
	sequenceNumber, err := s.edmpRepo.EdmpSequence.GetSequenceNumber(ctx, employeeProfile.Bsnr, dmpValue, sequenceType)
	if err != nil {
		return fmt.Errorf("failed to get sequenceNumber %w", err)
	}

	document, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.FindById(ctx, request.documentId)
	if err != nil {
		return fmt.Errorf("failed to find document with documentId: %v %w", request.documentId, err)
	}
	if document == nil || document.Id == nil {
		return fmt.Errorf("failed to find document with documentId: %v", request.documentId)
	}

	bsnrInfo, err := s.bsnrService.GetById(ctx, util.GetPointerValue(employeeProfile.BsnrId))
	if err != nil {
		return fmt.Errorf("failed to get bsnr: %w", err)
	}

	if bsnrInfo == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_BSNR_NOT_FOUND)
	}

	printingName, err := s.GetInsurancePrintingName(ctx, util.GetPointerValue(document.PatientId), document.ScheinId)
	if err != nil {
		return fmt.Errorf("failed to get insurance printing name: %w", err)
	}

	schein, err := s.scheinRepo.GetById(ctx, document.ScheinId)
	if err != nil {
		return fmt.Errorf("failed to get schein by insurance id %w", err)
	}
	if schein == nil || schein.Id == nil {
		return fmt.Errorf("failed to get schein by id %v", schein)
	}

	b := billing_builder.BillingFieldBuilder{
		PatientProfile:     util.GetPointerValue(patient.PatientInfo),
		BsnrInfo:           util.GetPointerValue(bsnrInfo),
		EmployeeProfile:    util.GetPointerValue(employeeProfile),
		VersionNumber:      sequenceNumber,
		DocumentType:       request.model.DocumentType,
		DmpLabelingValue:   request.enrollment.ParticipationForm.DMPLabelingValue,
		SoftwareConfig:     s.softwareConfig,
		Fielder:            f,
		Fields:             request.model.Fields,
		DocumentId:         request.documentId,
		DmpCaseNumber:      request.enrollment.DMPCaseNumber,
		DocumentDate:       request.model.DocumentDate,
		DocumentAt:         document.CreatedAt.UnixMilli(),
		PrintingName:       util.GetPointerValue(printingName),
		InsuranceId:        schein.Schein.InsuranceId,
		DoctorRelationType: request.model.DoctorRelationType,
	}

	billingXMLdata, err := b.BuildHeader(request.model.DocumentType).BuildBody().ToXMl()
	if err != nil {
		return fmt.Errorf("failed to build billing xml %w", err)
	}

	if request.model.DMPBillingFile.FileName == "" {
		billingFileName, err := GenBillingFileName(
			employeeProfile.Bsnr,
			request.enrollment.DMPCaseNumber,
			request.model.DMPLabelingValue,
			request.model.DocumentType,
			util.ConvertMillisecondsToTime(request.model.DocumentDate),
			true,
		)
		if err != nil {
			return fmt.Errorf("failed to generate billing file name %w", err)
		}

		request.model.DMPBillingFile.FileName = billingFileName
	}

	billingReader := bytes.NewReader(billingXMLdata)
	_, err = s.minioClient.PutObject(ctx, s.bucketDmpBilling, request.model.DMPBillingFile.FileName, billingReader, billingReader.Size(), minio.PutObjectOptions{})
	if err != nil {
		return fmt.Errorf("failed to put object %w", err)
	}

	return nil
}

func (s *EDMPService) getEnrollmentById(ctx *titan.Context, enrollmentId uuid.UUID) (*edmp_enrollment.EdmpEnrollmentEntity, error) {
	enrollment, err := s.edmpRepo.EdmpEnrollmentRepo.GetById(ctx, enrollmentId)
	if err != nil {
		return nil, err
	}
	if enrollment == nil || enrollment.Id == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_EDMP_Enrollment_Not_Found)
	}
	return enrollment, nil
}

func (s *EDMPService) updateDMPCaseNumber(ctx *titan.Context, dmpCaseNumber string, enrollmentId uuid.UUID) (*edmp_enrollment.EdmpEnrollmentEntity, error) {
	entity, err := s.edmpRepo.EdmpEnrollmentRepo.UpdateDMPCaseNumber(ctx, dmpCaseNumber, enrollmentId)
	if err != nil {
		return nil, err
	}
	return entity, nil
}

type updateDocumentationOverviewRequest struct {
	id            uuid.UUID
	model         common.DocumentationOverview
	event         common.EventEnrollType
	enrollment    edmp_enrollment.EdmpEnrollmentEntity
	documentType  common.DocumentType
	dMPCaseNumber string
}

func (s *EDMPService) updateDocumentationOverview(ctx *titan.Context, request updateDocumentationOverviewRequest) error {
	isAllowEDMPProgrammes := request.enrollment.ParticipationForm.DMPLabelingValue == request.model.DMPLabelingValue
	edmps := request.enrollment.ParticipationForm.DMPLabelingValue

	if !isAllowEDMPProgrammes {
		return pkg_errors.NewTitanCommonExceptionWithParams(
			error_code.ErrorCode_EDMP_Not_Allowed,
			map[string]string{
				"edmps": edmps,
			},
		)
	}
	request.model.Fields = slice.Filter(request.model.Fields, func(t common.Field) bool {
		return len(t.Values) > 0 || t.FieldType == common.FieldType_Nested
	})

	document, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.GetById(ctx, request.id)
	if err != nil {
		return fmt.Errorf("failed to find document with documentId: %v %w", request.id, err)
	}

	isPHQ9Document := request.documentType == common.DocumentType_PHQ9_ED || request.documentType == common.DocumentType_PHQ9_FD
	if isPHQ9Document {
		previousScore, err := s.GetPreviousScorePHQ9(ctx, util.GetPointerValue(request.model.PatientId), util.NewPointer(document.CreatedAt))
		if err != nil {
			return fmt.Errorf("failed to get previous score: %w", err)
		}

		request.model.PHQ9.PreviousScore = previousScore
	}

	entity, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.UpdateById(ctx, request.id, request.model)
	if err != nil {
		return fmt.Errorf("failed to update enrollment document: %w", err)
	}

	// Just upload billing file for document type ED, FD cuz document type PHQ9 has no billing file
	if !isPHQ9Document {
		err = s.uploadBillingFileForDocumentOverview(ctx, uploadBillingFileForDocumentOverviewRequest{
			enrollment: request.enrollment,
			documentId: request.id,
			model:      request.model,
		})
		if err != nil {
			return fmt.Errorf("failed to upload billing file to minio: %w", err)
		}
	}

	if request.enrollment.DMPCaseNumber != request.dMPCaseNumber {
		entity, err := s.updateDMPCaseNumber(ctx, request.dMPCaseNumber, *request.enrollment.Id)
		if err != nil {
			return err
		}

		if entity == nil {
			return nil
		}
	}

	// notifier event
	s.notifier(ctx, request.event, nil, []edmp_document.EdmpEnrollmentDocumentEntity{*entity})

	return nil
}

// add common fields for all(execpt Brustkrebs)
func (*EDMPService) addCommonFields(inputFields []common.Field, documentType common.DocumentType, dmpValue string, enrollment *edmp_enrollment.EdmpEnrollmentEntity) []common.Field {
	commonFields := []common.Field{
		{
			Name:  "Körpergröße",
			Label: "Körpergröße",
			Values: []common.FieldValue{
				{
					// parse to number with 2 digits after decimal point
					Value:     fmt.Sprintf("%.2f", float64(enrollment.PatientMedicalData.Height)/100),
					FieldType: common.FieldType_Number,
					ValueUnit: util.NewString("m"),
					Name:      "Körpergröße",
				},
			},
			FieldType:    common.FieldType_Number,
			DocumentType: documentType,
			Header:       "Anamnese- und Befunddaten",
			Unit:         util.NewString("cm"),
		},
		{
			Name:  "Körpergewicht",
			Label: "Körpergewicht",
			Values: []common.FieldValue{
				{
					// parse to number with 3 digits
					Value:     fmt.Sprintf("%03d", enrollment.PatientMedicalData.Weight),
					FieldType: common.FieldType_Number,
					ValueUnit: util.NewString("kg"),
					Name:      "Körpergewicht",
				},
			},
			FieldType:    common.FieldType_Number,
			DocumentType: documentType,
			Header:       "Anamnese- und Befunddaten",
			Unit:         util.NewString("kg"),
		},
		{
			Name:  "Blutdruck systolisch",
			Label: "Blutdruck systolisch",
			Values: []common.FieldValue{
				{
					Value:     enrollment.PatientMedicalData.BloodPressureSystolisch,
					FieldType: common.FieldType_Number,
					ValueUnit: util.NewString("mmHg"),
					Name:      "Blutdruck systolisch",
				},
			},
			FieldType:    common.FieldType_Number,
			DocumentType: documentType,
			Header:       "Anamnese- und Befunddaten",
			Unit:         util.NewString("mmHg"),
		},
		{
			Name:  "Blutdruck diastolisch",
			Label: "Blutdruck diastolisch",
			Values: []common.FieldValue{
				{
					Value:     enrollment.PatientMedicalData.BloodPressureDiastolisch,
					FieldType: common.FieldType_Number,
					ValueUnit: util.NewString("mmHg"),
					Name:      "Blutdruck diastolisch",
				},
			},
			FieldType:    common.FieldType_Number,
			DocumentType: documentType,
			Header:       "Anamnese- und Befunddaten",
			Unit:         util.NewString("mmHg"),
		},
		{
			Name:  "Raucher",
			Label: "Raucher",
			Values: []common.FieldValue{
				{
					Value: function.Do(func() string {
						raucherValue := "Nein"
						if enrollment.PatientMedicalData.Smoker {
							raucherValue = "Ja"
						}

						return raucherValue
					}),
					FieldType: common.FieldType_Checkbox,
					ValueUnit: nil,
					Name:      "Raucher",
				},
			},
			FieldType:    common.FieldType_Checkbox,
			DocumentType: documentType,
			Header:       "Anamnese- und Befunddaten",
		},
		{
			Name:  "Einschreibung wegen",
			Label: "Einschreibung wegen",
			Values: []common.FieldValue{
				{
					Value:     schema_model.GetEinschreibungwegenValue(common.DMPValueEnum(dmpValue)),
					FieldType: common.FieldType_Checkbox,
					ValueUnit: nil,
					Name:      "Einschreibung wegen",
				},
			},
			FieldType:    common.FieldType_Text,
			DocumentType: documentType,
			Header:       "Administrative Daten",
		},
	}

	// document type phq9 has no input fields
	if len(inputFields) == 0 {
		return nil
	}

	// ignore for Brustkrebs and Depression
	isBrustkrebs := dmpValue == string(common.DMPValueEnum_Brustkrebs)
	isDepression := dmpValue == string(common.DMPValueEnum_Depression)
	if isBrustkrebs || isDepression {
		return inputFields
	}

	// copy fields
	for _, cf := range commonFields {
		field := slice.FindOne(inputFields, func(f common.Field) bool {
			return f.Name == cf.Name
		})
		if field == nil {
			inputFields = append(inputFields, cf)
		}
	}

	return inputFields
}

func (s *EDMPService) SaveDocumentationOverview(ctx *titan.Context, request edmp.UpdateDocumentationOverviewRequest) error {
	document := request.DocumentationOverview

	// check enrollment
	enrollment, err := s.getEnrollmentById(ctx, document.EnrollmentId)
	if err != nil {
		return err
	}

	// Just add common fields for document type ED, FD cuz PHQ9 has no common fields
	fields := s.addCommonFields(document.Fields, document.DocumentType, document.DMPLabelingValue, enrollment)
	document.Fields = fields

	// update
	document.DocumentStatus = util.NewPointer(common.DocumentStatus_Saved)

	return s.updateDocumentationOverview(ctx, updateDocumentationOverviewRequest{
		id:            request.Id,
		model:         document,
		event:         common.EventEnrollType_SaveDocument,
		enrollment:    util.GetPointerValue(enrollment),
		documentType:  document.DocumentType,
		dMPCaseNumber: request.DocumentationOverview.DMPCaseNumber,
	})
}

func (s *EDMPService) FinishDocumentationOverview(ctx *titan.Context, request edmp.UpdateDocumentationOverviewRequest) (*edmp.FinishDocumentationOverviewResponse, error) {
	// check enrollment
	enrollment, err := s.getEnrollmentById(ctx, request.DocumentationOverview.EnrollmentId)
	if err != nil {
		return nil, err
	}

	// add common fields, if document is phq9, fields is empty
	fields := s.addCommonFields(request.DocumentationOverview.Fields, request.DocumentationOverview.DocumentType, request.DocumentationOverview.DMPLabelingValue, enrollment)
	request.DocumentationOverview.Fields = fields

	// check plausibility just use for ed, pd ,fd
	isPHQ9Document := request.DocumentationOverview.DocumentType == common.DocumentType_PHQ9_ED || request.DocumentationOverview.DocumentType == common.DocumentType_PHQ9_FD
	if !isPHQ9Document {
		response, err := s.CheckPlausibility(ctx, edmp.CheckPlausibilityRequest{
			RelatedFields:      request.DocumentationOverview.Fields,
			DocumentType:       request.DocumentationOverview.DocumentType,
			DMPLabelingValue:   request.DocumentationOverview.DMPLabelingValue,
			DoctorId:           request.DocumentationOverview.DoctorId,
			PatientId:          util.GetPointerValue(request.DocumentationOverview.PatientId),
			DMPCaseNumber:      request.DocumentationOverview.DMPCaseNumber,
			DocumentDate:       request.DocumentationOverview.DocumentDate,
			EnrollmentId:       util.GetPointerValue(enrollment.Id),
			DocumentId:         util.NewPointer(request.Id),
			ScheinId:           request.DocumentationOverview.ScheinId,
			DoctorRelationType: request.DocumentationOverview.DoctorRelationType,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to check plausibility: %w", err)
		}
		if !response.IsPlausible {
			return &edmp.FinishDocumentationOverviewResponse{
				CheckPlausibilityResponse: util.GetPointerValue(response),
			}, nil
		}
	}

	document := request.DocumentationOverview
	err = s.updateDocumentationOverview(ctx, updateDocumentationOverviewRequest{
		id:            request.Id,
		model:         document,
		event:         common.EventEnrollType_FinishDocument,
		enrollment:    util.GetPointerValue(enrollment),
		documentType:  document.DocumentType,
		dMPCaseNumber: request.DocumentationOverview.DMPCaseNumber,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to update documentation overview: %w", err)
	}
	return &edmp.FinishDocumentationOverviewResponse{
		CheckPlausibilityResponse: edmp.CheckPlausibilityResponse{
			IsPlausible: true,
		},
	}, nil
}

func (s *EDMPService) GetIncompleteDocumentationOverviews(ctx *titan.Context, request edmp.GetIncompleteDocumentationOverviewsRequest) ([]edmp.DocumentationOverviewInfo, error) {
	documentationOverviews, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.GetIncompleteDocumentationOverviews(ctx, request)
	if err != nil {
		return nil, err
	}
	return documentationOverviews, nil
}

func (s *EDMPService) GetCompleteDocumentationOverviews(ctx *titan.Context, request edmp.GetCompleteDocumentationOverviewsRequest) (*edmp.GetCompleteDocumentationOverviewsResponse, error) {
	documents, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.FindCompletedDocumentForPatient(ctx, request.PatientId, request.DocumentType, request.DMPLabelingValue)
	if err != nil {
		return nil, err
	}
	if len(documents) == 0 {
		return nil, nil
	}

	return &edmp.GetCompleteDocumentationOverviewsResponse{
		DocumentationOverviews: slice.Map(documents, func(d edmp_document.EdmpEnrollmentDocumentEntity) edmp.DocumentationOverviewInfo {
			return edmp.DocumentationOverviewInfo{
				Id:                    *d.Id,
				DocumentationOverview: d.DocumentationOverview,
			}
		}),
	}, nil
}

// data center
func (s *EDMPService) CreateDataCenter(ctx *titan.Context, request edmp.CreateDataCenterRequest) (*uuid.UUID, error) {
	entity, err := s.edmpRepo.EdmpDataCenterRepo.Create(ctx, request)
	if err != nil {
		return nil, err
	}
	return entity.Id, nil
}

func (s *EDMPService) RemoveDataCenter(ctx *titan.Context, id uuid.UUID) error {
	return s.edmpRepo.EdmpDataCenterRepo.Remove(ctx, id)
}

func (s *EDMPService) UpdateDataCenter(ctx *titan.Context, request edmp.UpdateDataCenterRequest) error {
	return s.edmpRepo.EdmpDataCenterRepo.Update(ctx, request)
}

func uniqueXmlDataCenter(editedXmlDataCenters, importedXmlDataCenters []edmp.DataCenterInfo) []edmp.DataCenterInfo {
	var xmlDataCenters []edmp.DataCenterInfo
	// make map for xmlDataCenters
	mapXmlDataCenters := make(map[int32]edmp.DataCenterInfo)
	for _, v := range importedXmlDataCenters {
		mapXmlDataCenters[*v.DataCenter.DocId] = v
	}
	// override xmlDataCenters with editedXmlDataCenters
	for _, v := range editedXmlDataCenters {
		mapXmlDataCenters[*v.DataCenter.DocId] = v
	}
	for _, v := range mapXmlDataCenters {
		xmlDataCenters = append(xmlDataCenters, v)
	}
	return xmlDataCenters
}

func (s *EDMPService) GetDataCenters(ctx *titan.Context, request edmp.GetDataCenterRequest) ([]edmp.DataCenterInfo, int64, error) {
	selfCreatedDataCenter, err := s.edmpRepo.EdmpDataCenterRepo.GetDataCenters(ctx, true)
	if err != nil {
		return nil, -1, err
	}

	editedXmlDataCenters, err := s.edmpRepo.EdmpDataCenterRepo.GetDataCenters(ctx, false)
	if err != nil {
		return nil, -1, err
	}

	yearQuarter := util.ToYearQuarter(request.SelectedDate)
	importedXmlDataCenters, err := s.sddaRepo.GetXMLDataCenters(ctx, &sdda.GetSddaRequest{
		Year:    yearQuarter.Year,
		Quarter: yearQuarter.Quarter,
	})
	if err != nil {
		return nil, -1, err
	}

	xmlDataCenter := uniqueXmlDataCenter(editedXmlDataCenters, importedXmlDataCenters)
	xmlDataCenter = append(xmlDataCenter, selfCreatedDataCenter...)

	sort.Slice(xmlDataCenter, func(i, j int) bool {
		if request.PaginationRequest.Order == "ASC" {
			return xmlDataCenter[i].DataCenter.Name < xmlDataCenter[j].DataCenter.Name
		}
		return xmlDataCenter[i].DataCenter.Name > xmlDataCenter[j].DataCenter.Name
	})

	if request.PaginationRequest.Page <= 1 {
		request.PaginationRequest.Page = 1
	}
	if request.PaginationRequest.PageSize <= 0 {
		request.PaginationRequest.PageSize = 10
	}

	total := len(xmlDataCenter)
	offset := (request.PaginationRequest.Page - 1) * request.PaginationRequest.PageSize
	offsetTo := int(math.Min(float64(offset+request.PaginationRequest.PageSize), float64(total)))
	xmlDataCenter = xmlDataCenter[offset:offsetTo]

	return xmlDataCenter, int64(total), nil
}

func (s *EDMPService) GetKVAreaInfos() []edmp.KVArea {
	return s.edmpRepo.EdmpKvRepo.GetKVAreaInfos()
}

func updateDmpDocumentType(dmpDocumentType []common.DMPDocumentationType, d edmp_document.EdmpEnrollmentDocumentEntity) {
	if util.GetPointerValue(d.DocumentStatus) == common.DocumentStatus_Saved {
		dmpDocumentType[1].DocumentationIds = append(dmpDocumentType[1].DocumentationIds, *d.Id)
		dmpDocumentType[1].Total += 1
	}
	if util.GetPointerValue(d.DocumentStatus) == common.DocumentStatus_Printed ||
		util.GetPointerValue(d.DocumentStatus) == common.DocumentStatus_Finished ||
		util.GetPointerValue(d.DocumentStatus) == common.DocumentStatus_Billed {
		dmpDocumentType[0].DocumentationIds = append(dmpDocumentType[0].DocumentationIds, *d.Id)
		dmpDocumentType[0].Total += 1
	}
}

func initDmpDocumentType(documentType common.DocumentType) []common.DMPDocumentationType {
	return []common.DMPDocumentationType{
		{
			DocumentationIds:    make([]uuid.UUID, 0),
			DocumentationStatus: common.DMPDocumentationStatus_Complete,
			Total:               0,
			DocumentType:        documentType,
		},
		{
			DocumentationIds:    make([]uuid.UUID, 0),
			DocumentationStatus: common.DMPDocumentationStatus_Incomplete,
			Total:               0,
			DocumentType:        documentType,
		},
	}
}

func getDocumentsGroupByType(documents []edmp_document.EdmpEnrollmentDocumentEntity) (dmpDocumentationTypeED, dmpDocumentationTypeFD, dmpDocumentationTypePED []common.DMPDocumentationType) {
	dmpDocumentationTypeED = initDmpDocumentType(common.DocumentType_ED)
	dmpDocumentationTypeFD = initDmpDocumentType(common.DocumentType_FD)
	dmpDocumentationTypePED = initDmpDocumentType(common.DocumentType_PD)

	for _, d := range documents {
		if d.DocumentType == common.DocumentType_ED {
			updateDmpDocumentType(dmpDocumentationTypeED, d)
		}
		if d.DocumentType == common.DocumentType_PD {
			updateDmpDocumentType(dmpDocumentationTypePED, d)
		}
		if d.DocumentType == common.DocumentType_FD {
			updateDmpDocumentType(dmpDocumentationTypeFD, d)
		}
	}
	return dmpDocumentationTypeED, dmpDocumentationTypeFD, dmpDocumentationTypePED
}

func (s *EDMPService) GetDMPBillingValidationList(ctx *titan.Context, request edmp.GetDMPBillingValidationListRequest) (*edmp.GetDMPBillingValidationListResponse, error) {
	documents, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.FindByQuarter(ctx, request.QuarterRange, request.DoctorId, request.DMPLabelingValues)
	if err != nil {
		return nil, fmt.Errorf("failed to find document enrollments: %w", err)
	}
	if len(documents) == 0 {
		return nil, fmt.Errorf("document enrollments are empty")
	}

	enrollments, err := s.edmpRepo.EdmpEnrollmentRepo.GetByIds(ctx, slice.Map(documents, func(d edmp_document.EdmpEnrollmentDocumentEntity) uuid.UUID {
		return d.EnrollmentId
	}))
	if err != nil {
		return nil, fmt.Errorf("failed to find enrollments: %w", err)
	}
	if len(enrollments) == 0 {
		return nil, fmt.Errorf("enrollments are empty")
	}

	patients, err := s.patientProfileRepo.GetByIds(ctx, slice.Map(documents, func(d edmp_document.EdmpEnrollmentDocumentEntity) uuid.UUID {
		return util.GetPointerValue(d.PatientId)
	}))
	if err != nil {
		return nil, fmt.Errorf("failed to find patients: %w", err)
	}
	if len(patients) == 0 {
		return nil, fmt.Errorf("patients are empty")
	}

	mapIdWithPatientProfile := slice.Reduce(patients, func(acc map[uuid.UUID]patient.PatientProfile, cur *patient.PatientProfile) map[uuid.UUID]patient.PatientProfile {
		acc[util.GetPointerValue(cur.Id)] = *cur
		return acc
	}, map[uuid.UUID]patient.PatientProfile{})

	dataCenters, err := s.getDataCenter(ctx, int32(request.QuarterRange.Year), int32(request.QuarterRange.Quarter))
	if err != nil {
		return nil, fmt.Errorf("failed to get data centers: %w", err)
	}

	if len(dataCenters) == 0 {
		return nil, fmt.Errorf("data centers are empty")
	}

	var validationLists []edmp.DMPBillingValidationModel
	for _, e := range enrollments {
		patient := mapIdWithPatientProfile[e.PatientId]
		defaultDataCenter, err := s.getDefaultDataCenter(ctx, dataCenters, util.GetPointerValue(patient.Id), e.ParticipationForm.DMPLabelingValue, request.BsnrCode)
		if err != nil {
			return nil, fmt.Errorf("get default data center error: %w", err)
		}
		var activeInsurance *patient_profile_common.InsuranceInfo = nil
		if patient.PatientInfo != nil {
			activeInsurance = patient.PatientInfo.GetActiveInsurance()
		}

		model := edmp.DMPBillingValidationModel{
			Id:           util.GetPointerValue(util.NewUUID()),
			EnrollmentId: util.GetPointerValue(e.Id),
			Patient: common.Patient{
				PatientId:       util.GetPointerValue(patient.Id),
				FirstName:       patient.FirstName,
				LastName:        patient.LastName,
				DateOfBirth:     patient.PatientInfo.PersonalInfo.DateOfBirth,
				PatientNumber:   int64(patient.PatientInfo.PatientNumber),
				FullName:        util.GetPatientName(patient),
				ActiveInsurance: activeInsurance,
			},
			DMPCaseNumber:     e.DMPCaseNumber,
			InsuranceName:     e.InsuranceInfo.InsuranceCompanyName,
			DMPLabeling:       util.GetPointerValue(common.GetDMPByValue(e.ParticipationForm.DMPLabelingValue)),
			DataCenters:       dataCenters,
			DefaultDataCenter: defaultDataCenter,
		}
		validationLists = append(validationLists, model)
	}

	mapEnrolmentIdWithDocuments := make(map[uuid.UUID][]edmp_document.EdmpEnrollmentDocumentEntity)
	for _, d := range documents {
		if mapEnrolmentIdWithDocuments[d.EnrollmentId] == nil {
			mapEnrolmentIdWithDocuments[d.EnrollmentId] = []edmp_document.EdmpEnrollmentDocumentEntity{d}
		} else {
			mapEnrolmentIdWithDocuments[d.EnrollmentId] = append(mapEnrolmentIdWithDocuments[d.EnrollmentId], d)
		}
	}

	var data []edmp.DMPBillingValidationModel
	for _, validation := range validationLists {
		dmpDocumentationTypeED, dmpDocumentationTypeFD, dmpDocumentationTypePED := getDocumentsGroupByType(mapEnrolmentIdWithDocuments[validation.EnrollmentId])
		validation.DMPDocumentationTypeED = dmpDocumentationTypeED
		validation.DMPDocumentationTypeFD = dmpDocumentationTypeFD
		validation.DMPDocumentationTypePED = dmpDocumentationTypePED

		data = append(data, validation)
	}

	return &edmp.GetDMPBillingValidationListResponse{
		Data: data,
	}, nil
}

func (s *EDMPService) UpdateStatus(ctx *titan.Context, id uuid.UUID, status common.DmpBillingStatus, dmpBillingError *common.DmpBillingError) (*billing_edmp_history.BillingDMPHistoryEntity, error) {
	entity, err := s.edmpRepo.EdmpBillingHistoryRepo.UpdateStatusById(ctx, id, status, dmpBillingError)
	if err != nil {
		ctx.Logger().Error(err.Error())
		return nil, fmt.Errorf("failed to update status: %w", err)
	}
	if entity == nil || entity.Id == nil {
		errMsg := "billing history not found"
		ctx.Logger().Error(errMsg)
		return nil, errors.New(errMsg)
	}
	return entity, nil
}

func (s *EDMPService) SendMailRetry(ctx *titan.Context, billingHistoryId uuid.UUID) error {
	_, err := s.SendBillingEmail(ctx, billingHistoryId)
	if err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}
	return nil
}

func (s *EDMPService) RemoveDocumentationOverview(ctx *titan.Context, request edmp.RemoveDocumentationOverviewRequest) (*edmp.RemoveDocumentationOverviewResponse, error) {
	res, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.RemoveById(ctx, request.Id)
	if err != nil {
		return nil, fmt.Errorf("failed to remove documentation overview: %w", err)
	}

	return &edmp.RemoveDocumentationOverviewResponse{
		Status: res,
	}, nil
}

func (s *EDMPService) UploadMailItemsToMinio(ctx *titan.Context, emailItems []MailItem) error {
	fileName := fmt.Sprintf("%v.eml", util.NewUUID())
	var fileBytes []byte
	for _, item := range emailItems {
		itemBytes, err := json.Marshal(item)
		if err != nil {
			return fmt.Errorf("failed to marshal MailItem: %w", err)
		}
		fileBytes = append(fileBytes, itemBytes...)
	}

	fileReader := bytes.NewBuffer(fileBytes)
	_, err := s.minioClient.PutObject(ctx, s.bucketDmpBilling, fileName, fileReader, int64(fileReader.Len()), minio.PutObjectOptions{})
	if err != nil {
		return fmt.Errorf("failed to upload mail items to minio: %w", err)
	}

	return nil
}

func (s *EDMPService) SendBillingEmail(ctx *titan.Context, billingHistoryId uuid.UUID) ([]MailItem, error) {
	emailItems, err := s.createEmailItem(ctx, billingHistoryId)
	if err != nil {
		return nil, fmt.Errorf("failed to create email item : %w", err)
	}
	if len(emailItems) == 0 {
		ctx.Logger().Warn("no email item to send edmp")
		return nil, nil
	}

	// TODO: upload mail item to minio for testing
	err = s.UploadMailItemsToMinio(ctx, emailItems)
	if err != nil {
		return nil, fmt.Errorf("failed to upload mail items to minio: %w", err)
	}

	kvMailItems := slice.Map(emailItems, func(e MailItem) kv_connect_services.SendRequest {
		return e.SendRequest
	})
	if err := s.SendKVMail(ctx, kvMailItems); err != nil {
		return nil, fmt.Errorf("failed to send email: %w", err)
	}
	return emailItems, nil
}

func (s *EDMPService) SendKVMail(ctx *titan.Context, mailItems []kv_connect_services.SendRequest) error {
	if len(mailItems) == 0 {
		return nil
	}
	for _, mail := range mailItems {
		if _, err := s.kvConnectService.Send(ctx, mail); err != nil {
			return fmt.Errorf("failed to send mail: %w", err)
		}
	}
	return nil
}

func (*EDMPService) getEmailAddress(ctx *titan.Context, bsnrCode string, dataCenter edmp.DataCenterInfo) string {
	// find email to base on bsnr code and KvArea
	code, err := okv.GetOkvByBsnr(bsnrCode)
	if err != nil {
		ctx.Logger().Error(fmt.Sprintf("failed to get okv by bsnr code: %s", err.Error()))
		return ""
	}
	for _, kv := range dataCenter.DataCenter.KV {
		if code == *kv.KVArea && kv.KVAddress != nil {
			return *kv.KVAddress
		}
	}
	// if not found, return first posible KVAddress
	for _, kv := range dataCenter.DataCenter.KV {
		if kv.KVAddress != nil {
			return *kv.KVAddress
		}
	}
	// not found, return empty
	return ""
}

func (*EDMPService) BuildCompanionFileContent(ctx *titan.Context, bsnrCode, ikNumber, fileArchiveName, dmpValue string) (string, error) {
	fileContent := companion_builder.GetCompanionFileContent(common.DMPValueEnum(dmpValue))

	companionFileTemplate, _ := template.
		New("companionFileTemplate").
		Funcs(template.FuncMap{
			"toDateString": func(createdAt int64) string {
				return util.ConvertMillisecondsToTime(createdAt).Format("2006-01-02")
			},
			"boolToString": strconv.FormatBool,
			"newId": func() string {
				return uuid.New().String()
			},
			"getBsnrCode": func(bsnrCode string) string {
				return bsnrCode[:2]
			},
			"getPath": common.GetDMPPathByDMPValue,
		}).Parse(fileContent)
	c := CompanionItem{
		CreatedDate:     util.Now(ctx).UnixMilli(),
		BsnrCode:        bsnrCode,
		IKNumber:        ikNumber,
		FileArchiveName: fileArchiveName,
		DmpValue:        dmpValue,
	}
	buf := new(bytes.Buffer)
	err := companionFileTemplate.Execute(buf, c)
	if err != nil {
		return "", fmt.Errorf("failed to execute companion file template: %w", err)
	}
	return buf.String(), nil
}

type UploadXKMZipFileToMinio struct {
	employeeProfile                           employee.EmployeeProfile
	enrollmentInfo                            edmp_enrollment.EdmpEnrollmentEntity
	xkmBillingZipFileContentEncrypted         []byte
	xkmBillingZipFileContentEncryptedFileName string
}

func (s *EDMPService) UploadXKMZipFileToMinio(ctx *titan.Context, request UploadXKMZipFileToMinio) error {
	xkmFileReader := bytes.NewReader(request.xkmBillingZipFileContentEncrypted)

	_, err := s.minioClient.PutObject(ctx, s.bucketDmpBilling, request.xkmBillingZipFileContentEncryptedFileName, xkmFileReader, int64(xkmFileReader.Len()), minio.PutObjectOptions{})
	if err != nil {
		return fmt.Errorf("failed to upload xkm billing zip file to minio: %w", err)
	}

	return nil
}

type MailItem struct {
	kv_connect_services.SendRequest
	EnrollmentId uuid.UUID
	MessageUUID  uuid.UUID
}

// TODO update as new kv connect service
func (s *EDMPService) createEmailItem(ctx *titan.Context, billingHistoryId uuid.UUID) ([]MailItem, error) {
	billingHistory, err := s.edmpRepo.EdmpBillingHistoryRepo.FindById(ctx, billingHistoryId)
	if err != nil {
		return nil, fmt.Errorf("failed to find edmp billing history by id: %w", err)
	}
	if billingHistory == nil || billingHistory.Id == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Record_Not_Found, "edmp billing history not found")
	}

	kvConnection, err := s.kvConnectService.NewKvConection(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create kv connection: %w", err)
	}

	kvConnect, err := kvConnection.AccountService.GetAccountById(ctx, billingHistory.KvConnectId)
	if err != nil {
		return nil, fmt.Errorf("failed to get kv connect account: %w", err)
	}
	if kvConnect == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Record_Not_Found, "kv connect account not found")
	}

	var mailItems []MailItem
	for _, b := range billingHistory.BillingsData {
		companionFile := slice.FindOne(b.BillingFiles, func(t common.DMPBillingFile) bool {
			return t.FileType == common.DMPBillingHistoryFileType_Companion
		})
		if companionFile == nil {
			return nil, fmt.Errorf("failed to find companion file in billing data")
		}
		companionFileContent, err := s.minioClient.DownloadFile(ctx, s.bucketDmpBilling, companionFile.FileName, minio.GetObjectOptions{})
		if err != nil {
			return nil, fmt.Errorf("failed to download companion file: %w", err)
		}
		enrollmentInfo, err := s.edmpRepo.EdmpEnrollmentRepo.FindById(ctx, b.EnrollmentId)
		if err != nil {
			return nil, fmt.Errorf("failed to find edmp enrollment by id: %w", err)
		}
		if enrollmentInfo == nil || enrollmentInfo.Id == nil {
			return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Record_Not_Found, "edmp enrollment not found")
		}
		employeeProfile, err := s.employeeProfileRepo.GetProfileById(ctx, enrollmentInfo.DoctorId)
		if err != nil {
			return nil, fmt.Errorf("failed to get employee profile by hash id: %w", err)
		}
		if employeeProfile == nil {
			return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Record_Not_Found, "employee profile not found")
		}
		emailTo := s.getEmailAddress(ctx, employeeProfile.Bsnr, edmp.DataCenterInfo{
			Id:         b.EnrollmentId,
			DataCenter: b.DataCenter,
		})
		if billingHistory.TypeOfBilling == common.TypeOfBilling_Send_As_Real_Test_Billing {
			// Test= <EMAIL>
			// Certi= <EMAIL>
			emailTo = "<EMAIL>"
		}
		if emailTo == "" {
			return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Email_Not_Found, "email address not found")
		}
		billingZipFile := slice.FindOne(b.BillingFiles, func(t common.DMPBillingFile) bool {
			return t.FileType == common.DMPBillingHistoryFileType_Billing
		})
		if billingZipFile == nil {
			return nil, fmt.Errorf("failed to find billing zip file in billing data")
		}

		zipContent, err := s.minioClient.DownloadFile(ctx, s.bucketDmpBilling, billingZipFile.FileName, minio.GetObjectOptions{})
		if err != nil {
			return nil, err
		}
		xkmRequestFunc, err := xkm.XKMMod.SafeResolve()
		if err != nil {
			return nil, fmt.Errorf("failed to get xkm request func: %w", err)
		}

		// remove .XKM from zip file
		billingZipFileNameConverted := strings.ReplaceAll(billingZipFile.FileName, ".XKM", "")
		xkmResult, err := xkmRequestFunc(ctx, zipContent, billingZipFileNameConverted, xkm.Encrypt, true, true) // TODO: hardcode true should change when xkm is ready
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt billing zip file: %w", err)
		}
		if xkmResult == nil {
			return nil, fmt.Errorf("failed to encrypt billing zip file: xkm result is nil")
		}

		if xkmResult.ErrorMsg != "" {
			return nil, fmt.Errorf("failed to encrypt billing zip file: %v", xkmResult.ErrorMsg)
		}
		xkmBillingZipFileContentEncrypted, err := base64.StdEncoding.DecodeString(xkmResult.EncryptedFileBase64)
		if err != nil {
			return nil, fmt.Errorf("failed to decrypt billing zip file: %w", err)
		}

		xkmBillingZipFileContentEncryptedFileName := fmt.Sprintf("%s.XKM", billingZipFile.FileName)
		err = s.UploadXKMZipFileToMinio(ctx, UploadXKMZipFileToMinio{
			employeeProfile:                           util.GetPointerValue(employeeProfile),
			enrollmentInfo:                            util.GetPointerValue(enrollmentInfo),
			xkmBillingZipFileContentEncrypted:         xkmBillingZipFileContentEncrypted,
			xkmBillingZipFileContentEncryptedFileName: xkmBillingZipFileContentEncryptedFileName,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to upload xkm billing zip file to minio: %w", err)
		}

		// get transfer letter
		transferLetter := slice.FindOne(billingHistory.TransferLetters, func(f common.DMPBillingFile) bool {
			return util.GetPointerValue(f.SddaIkNumber) == b.DataCenter.IKNumber
		})
		if transferLetter == nil {
			return nil, fmt.Errorf("failed to find transfer letter in billing data")
		}

		transferLetterContent, err := s.minioClient.DownloadFile(ctx, s.bucketDmpBilling, transferLetter.FileName, minio.GetObjectOptions{})
		if err != nil {
			return nil, fmt.Errorf("failed to download transfer letter: %w", err)
		}

		// msgId := b.EnrollmentId.String()
		msgId := uuid.New()
		mailItems = append(mailItems, MailItem{
			SendRequest: kv_connect_services.SendRequest{
				KvcServiceId: "eDMP;Einsendung;V1.0",
				UserName:     kvConnect.Username,
				Addressee:    emailTo,
				Subject:      "eDMP;Einsendung;V1.0",
				Text:         "edmp body",
				Attachments: []kv_connect_services.Attachment{
					{
						Content:     companionFileContent,
						FileName:    companionFile.FileName,
						ContentType: fmt.Sprintf("%s; name=%q", mail_common.AttachmentContentType_Xml, companionFile.FileName),
						Header: map[string]string{
							"Content-Description": "eDMP-Begleitdatei",
						},
					},
					{
						Content:     xkmBillingZipFileContentEncrypted,
						FileName:    xkmBillingZipFileContentEncryptedFileName,
						ContentType: fmt.Sprintf("%s; name=%q", mail_common.AttachmentContentType_OctetStream, xkmBillingZipFileContentEncryptedFileName),
						Header: map[string]string{
							"Content-Description": "eDMP-Archiv",
						},
					},
					{
						Content:     transferLetterContent,
						FileName:    util.GetPointerValue(transferLetter.DisplayName),
						ContentType: fmt.Sprintf("%s; name=%q", mail_common.AttachmentContentType_OctetStream, util.GetPointerValue(transferLetter.DisplayName)),
					},
				},
				MessageId:   msgId.String(),
				MessageType: "INVOICE",
			},
			EnrollmentId: b.EnrollmentId,
			MessageUUID:  msgId,
		})
	}
	return mailItems, nil
}

func (s *EDMPService) GetDMPBillingSelection(ctx *titan.Context, request edmp.GetDMPBillingSelectionRequest) (*edmp.GetDMPBillingSelectionResponse, error) {
	yearQuarter := util.YearQuarter{
		Year:     request.Year,
		Quarter:  request.Quarter,
		Location: ctx.RequestTimeZone(),
	}
	quarterRange, err := yearQuarter.GetQuarterDateRange()
	if err != nil {
		return nil, err
	}

	startDate := util.GetPointerValue(quarterRange.Start)
	endDate := util.GetPointerValue(quarterRange.End)
	documents, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.FindDocumentNotBilledByDateRange(ctx, startDate, endDate)
	if err != nil {
		return nil, err
	}
	if len(documents) == 0 {
		return nil, nil
	}

	doctors, err := s.employeeProfileRepo.GetProfileByIds(ctx, slice.Map(documents, func(d edmp_document.EdmpEnrollmentDocumentEntity) uuid.UUID {
		return d.DoctorId
	}))
	if err != nil {
		return nil, err
	}
	if len(doctors) == 0 {
		return nil, nil
	}
	mapIdWithDoctors := slice.Reduce(doctors, func(acc map[string]employee.EmployeeProfile, cur employee.EmployeeProfile) map[string]employee.EmployeeProfile {
		acc[cur.Id.String()] = cur
		return acc
	}, map[string]employee.EmployeeProfile{})

	mapEmployeeProfileIdWithEmployeeId := make(map[uuid.UUID]uuid.UUID)
	for _, doctor := range doctors {
		for _, document := range documents {
			if *doctor.Id == document.DoctorId {
				mapEmployeeProfileIdWithEmployeeId[*doctor.Id] = document.DoctorId
			}
		}
	}

	doctorsDistinct := make([]common.Doctor, 0, len(mapIdWithDoctors))
	for _, doc := range mapIdWithDoctors {
		doctorsDistinct = append(doctorsDistinct, common.Doctor{
			DoctorId:  mapEmployeeProfileIdWithEmployeeId[*doc.Id],
			FirstName: doc.FirstName,
			LastName:  doc.LastName,
			Initial:   doc.Initial,
			Title:     util.GetPointerValue(doc.Title),
			FullName:  util.GetDoctorName(doc),
		})
	}

	dmpLabelings := slice.Map(documents, func(d edmp_document.EdmpEnrollmentDocumentEntity) common.DMPLabeling {
		dmpLablingValue := util.GetPointerValue(common.GetDMPByValue(d.DMPLabelingValue))
		return common.DMPLabeling{
			Name:                dmpLablingValue.Name,
			GermanName:          dmpLablingValue.GermanName,
			ParticipationLetter: dmpLablingValue.ParticipationLetter,
			Value:               dmpLablingValue.Value,
			ICDCode:             dmpLablingValue.ICDCode,
		}
	})
	mapValueWithDMPLabeling := slice.Reduce(dmpLabelings, func(acc map[string]common.DMPLabeling, cur common.DMPLabeling) map[string]common.DMPLabeling {
		acc[cur.Value] = cur
		return acc
	}, map[string]common.DMPLabeling{})
	dmpLabelingsDistinct := make([]common.DMPLabeling, 0, len(mapValueWithDMPLabeling))
	for _, doc := range mapValueWithDMPLabeling {
		dmpLabelingsDistinct = append(dmpLabelingsDistinct, common.DMPLabeling{
			Name:                doc.Name,
			GermanName:          doc.GermanName,
			ParticipationLetter: doc.ParticipationLetter,
			Value:               doc.Value,
			ICDCode:             doc.ICDCode,
		})
	}

	return &edmp.GetDMPBillingSelectionResponse{
		Doctors:   doctorsDistinct,
		Labelings: dmpLabelingsDistinct,
	}, nil
}

type createAndUploadCompanionAndZipForEnrollmentRequest struct {
	enrollment edmp_enrollment.EdmpEnrollmentEntity
	dataCenter sdda_common.DataCenter
	version    int
	bsnr       string
	quarter    int32
	year       int32
}

func (s *EDMPService) createAndUploadCompanionAndZipForEnrollment(ctx *titan.Context, request createAndUploadCompanionAndZipForEnrollmentRequest) ([]common.DMPBillingFile, error) {
	dmpValue := common.DMPValueEnum(request.enrollment.ParticipationForm.DMPLabelingValue)
	xkmAbbreviation := common.GetXKMAbbreviation(dmpValue, util.YearQuarter{Year: request.year, Quarter: request.quarter})
	fileName := GenFileName(ctx, request.bsnr, request.version, xkmAbbreviation)
	archiveFileName := fmt.Sprintf("%v.zip.XKM", fileName)
	companionFileName := fmt.Sprintf("%v.idx", fileName)
	companionFileContent, err := s.BuildCompanionFileContent(
		ctx,
		request.bsnr,
		request.dataCenter.IKNumber,
		archiveFileName,
		request.enrollment.ParticipationForm.DMPLabelingValue,
	)
	if err != nil {
		return nil, err
	}
	companionFileContentStringReader := strings.NewReader(companionFileContent)
	_, err = s.minioClient.PutObject(ctx, s.bucketDmpBilling, companionFileName, companionFileContentStringReader, int64(companionFileContentStringReader.Len()), minio.PutObjectOptions{})
	if err != nil {
		return nil, err
	}

	// Zip
	documents, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.GetInQuarterByEnrollmentId(ctx, *request.enrollment.Id, request.quarter, request.year)
	if err != nil {
		return nil, err
	}
	if len(documents) == 0 {
		return nil, fmt.Errorf("no documents found")
	}

	tempDir, err := ioutil.TempDir("./", "dmpBilling-")
	if err != nil {
		return nil, err
	}

	dirPath := tempDir + "/" + common.GetDMPPathByDMPValue(request.enrollment.ParticipationForm.DMPLabelingValue)
	err = os.MkdirAll(dirPath, 0777)
	if err != nil {
		return nil, err
	}
	defer os.RemoveAll(tempDir)
	for _, d := range documents {
		fileByte, err := s.minioClient.DownloadFile(ctx, s.bucketDmpBilling, d.DMPBillingFile.FileName, minio.GetObjectOptions{})
		if err != nil {
			return nil, fmt.Errorf("download file failed: %w", err)
		}

		fileName, err := GenBillingFileName(
			request.bsnr,
			request.enrollment.DMPCaseNumber,
			d.DMPLabelingValue,
			d.DocumentType,
			util.ConvertMillisecondsToTime(d.CreatedAt.UnixMilli()),
			false,
		)
		if err != nil {
			return nil, fmt.Errorf("gen billing file name failed: %w", err)
		}

		err = ioutil.WriteFile(fmt.Sprintf("%s/%s", dirPath, fileName), fileByte, 0644)
		if err != nil {
			return nil, err
		}
	}

	zipFileName := fmt.Sprintf("%v.zip", fileName)
	err = zip_file.ZipFolder(tempDir, zipFileName)
	defer os.RemoveAll(zipFileName)

	if err != nil {
		return nil, err
	}

	zipDecodeBytes, err := ioutil.ReadFile(zipFileName)
	if err != nil {
		return nil, err
	}
	fileReader := bytes.NewReader(zipDecodeBytes)
	_, err = s.minioClient.PutObject(ctx, s.bucketDmpBilling, zipFileName, fileReader, fileReader.Size(), minio.PutObjectOptions{})
	if err != nil {
		return nil, err
	}

	billingFiles := []common.DMPBillingFile{
		{
			FileName: companionFileName,
			FilePath: fmt.Sprintf("%s/%s", s.bucketDmpBilling, companionFileName),
			FileType: common.DMPBillingHistoryFileType_Companion,
		},
		{
			FileName: zipFileName,
			FilePath: fmt.Sprintf("%s/%s", s.bucketDmpBilling, zipFileName),
			FileType: common.DMPBillingHistoryFileType_Billing,
		},
	}

	return billingFiles, nil
}

func (s *EDMPService) CheckAllRulesForDocuments(ctx *titan.Context, documents []edmp_document.EdmpEnrollmentDocumentEntity) ([]common.DMPBillingFieldsValidationResult, error) {
	result := make([]common.DMPBillingFieldsValidationResult, 0)

	for _, d := range documents {
		enrollemnt, err := s.edmpRepo.EdmpEnrollmentRepo.GetById(ctx, d.EnrollmentId)
		if err != nil {
			return nil, err
		}
		if enrollemnt == nil {
			return nil, fmt.Errorf("enrollment not found")
		}
		validationResults, err := s.CheckPlausibility(ctx, edmp.CheckPlausibilityRequest{
			RelatedFields:      d.Fields,
			DocumentType:       d.DocumentType,
			DMPLabelingValue:   d.DMPLabelingValue,
			DoctorId:           d.DoctorId,
			PatientId:          util.GetPointerValue(d.PatientId),
			DMPCaseNumber:      enrollemnt.DMPCaseNumber,
			DocumentDate:       d.DocumentDate,
			EnrollmentId:       util.GetPointerValue(enrollemnt.Id),
			DocumentId:         d.Id,
			ScheinId:           d.ScheinId,
			DoctorRelationType: d.DoctorRelationType,
		})
		if err != nil {
			return nil, err
		}

		if validationResults.IsPlausible {
			continue
		}

		result = append(result, common.DMPBillingFieldsValidationResult{
			DocumentId:             *d.Id,
			FieldValidationResults: validationResults.FieldValidationResults,
		})
		enrollment, err := s.getEnrollmentById(ctx, d.EnrollmentId)
		if err != nil {
			return nil, err
		}
		err = s.updateDocumentationOverview(
			ctx,
			updateDocumentationOverviewRequest{
				id:            util.GetPointerValue(d.Id),
				model:         d.DocumentationOverview,
				event:         common.EventEnrollType_SaveDocument,
				enrollment:    util.GetPointerValue(enrollment),
				documentType:  d.DocumentType,
				dMPCaseNumber: enrollemnt.DMPCaseNumber,
			},
		)
		if err != nil {
			return nil, fmt.Errorf("update documentation overview failed: %w", err)
		}
	}

	return result, nil
}

func (s *EDMPService) CheckValidationForDMPBilling(ctx *titan.Context, request edmp.CheckValidationForDMPBillingRequest) (*edmp.CheckValidationForDMPBillingResponse, error) {
	enrollmentIds := slice.Map(request.EnrollmentWithDataCenters, func(r common.EnrollmentWithDataCenter) uuid.UUID {
		return r.EnrollmentId
	})
	enrollments, err := s.edmpRepo.EdmpEnrollmentRepo.GetByIds(ctx, enrollmentIds)
	if err != nil {
		return nil, err
	}

	documents, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.GetInQuarterByEnrollmentIds(ctx, enrollmentIds, request.Quarter, request.Year)
	if err != nil {
		return nil, err
	}
	if len(documents) == 0 {
		return nil, fmt.Errorf("no documents found")
	}

	validationResults, err := s.CheckAllRulesForDocuments(ctx, documents)
	if err != nil {
		return nil, err
	}
	if len(validationResults) > 0 {
		return &edmp.CheckValidationForDMPBillingResponse{
			Status:                            false,
			DMPBillingFieldsValidationResults: validationResults,
		}, nil
	}

	mapIdWithEnrollment := slice.Reduce(enrollments, func(acc map[uuid.UUID]edmp_enrollment.EdmpEnrollmentEntity, cur *edmp_enrollment.EdmpEnrollmentEntity) map[uuid.UUID]edmp_enrollment.EdmpEnrollmentEntity {
		acc[util.GetPointerValue(cur.Id)] = *cur
		return acc
	}, map[uuid.UUID]edmp_enrollment.EdmpEnrollmentEntity{})

	dmpValues := common.GetAllDMPValues()
	mapDMPValueWithVersion := make(map[string]int, len(dmpValues))
	for _, v := range dmpValues {
		mapDMPValueWithVersion[v] = 0
	}

	var dmpBillingFiles []common.DMPBillingFile
	var dmpBillingsData []common.BillingData
	for _, r := range request.EnrollmentWithDataCenters {
		enrollment := mapIdWithEnrollment[r.EnrollmentId]
		_, ok := mapDMPValueWithVersion[enrollment.ParticipationForm.DMPLabelingValue]
		if ok {
			mapDMPValueWithVersion[enrollment.ParticipationForm.DMPLabelingValue] += 1
		}

		employeeProfile, err := s.employeeProfileRepo.GetProfileById(ctx, enrollment.DoctorId)
		if err != nil {
			return nil, err
		}
		if employeeProfile == nil {
			return nil, fmt.Errorf("employee profile not found")
		}
		billingFiles, err := s.createAndUploadCompanionAndZipForEnrollment(
			ctx,
			createAndUploadCompanionAndZipForEnrollmentRequest{
				enrollment: enrollment,
				dataCenter: r.DataCenter,
				version:    mapDMPValueWithVersion[enrollment.ParticipationForm.DMPLabelingValue],
				bsnr:       employeeProfile.Bsnr,
				quarter:    request.Quarter,
				year:       request.Year,
			},
		)
		if err != nil {
			return nil, err
		}

		dmpBillingsData = append(dmpBillingsData, common.BillingData{
			EnrollmentId: *enrollment.Id,
			DataCenter:   r.DataCenter,
			BillingFiles: billingFiles,
		})

		dmpBillingFiles = append(dmpBillingFiles, billingFiles...)
	}

	// generate transfer letter
	employeeInfo, err := s.employeeProfileRepo.GetProfileById(ctx, request.DoctorId)
	if err != nil {
		return nil, fmt.Errorf("failed to get employee info: %w", err)
	}
	if employeeInfo == nil || employeeInfo.Id == nil {
		return nil, fmt.Errorf("employee profile not found")
	}
	// group billing data by data center
	mapBillingDataGroupByDataCenter := slice.GroupBy(dmpBillingsData, func(t common.BillingData) string {
		return t.DataCenter.IKNumber
	})
	var transferLetters []common.DMPBillingFile
	for ikNumber, billingDatas := range mapBillingDataGroupByDataCenter {
		var billingFiles []common.DMPBillingFile
		for _, billingData := range billingDatas {
			billingFiles = append(billingFiles, billingData.BillingFiles...)
		}
		transferLetter, err := s.getTransferLetterForDMPBilling(ctx, transfer_letter.TransferLetter{
			Html2PdfHost: s.html2PdfPath,
			Sender:       employeeInfo.Bsnr,
			Receiver:     ikNumber,
			AmountOfData: int64(1),
			Content: slice.Map(billingFiles, func(billingFile common.DMPBillingFile) string {
				return billingFile.FileName
			}),
			CreatedAt:    util.Now(ctx).Format("02.01.2006"),
			SddaIkNumber: ikNumber,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to get transfer letter: %w", err)
		}
		transferLetters = append(transferLetters, *transferLetter)
	}

	// create billing history
	billingHistory, err := s.edmpRepo.EdmpBillingHistoryRepo.Create(ctx, common.DMPBillingHistoryInfo{
		BillingsData:    dmpBillingsData,
		TransferLetters: transferLetters,
	})
	if err != nil {
		return nil, err
	}

	return &edmp.CheckValidationForDMPBillingResponse{
		Status:              true,
		DMPBillingHistoryId: billingHistory.Id,
		DMPBillingFiles:     dmpBillingFiles,
		TransferLetters:     transferLetters,
	}, nil
}

func (s *EDMPService) getTransferLetterForDMPBilling(ctx *titan.Context, request transfer_letter.TransferLetter) (*common.DMPBillingFile, error) {
	displayName := fmt.Sprintf("%v_Transportbegleitzettel.pdf", util.Now(ctx).Format("02012006"))
	fileName := fmt.Sprintf("%v_Transportbegleitzettel.pdf", util.Now(ctx).Format("02012006150405"))
	t := request
	transferLetter, err := t.Build()
	if err != nil {
		return nil, fmt.Errorf("failed to build transfer letter: %w", err)
	}
	transferLetterReader := bytes.NewReader(transferLetter)
	_, err = s.minioClient.PutObject(ctx, s.bucketDmpBilling, fileName, transferLetterReader, transferLetterReader.Size(), minio.PutObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to upload transfer letter file to minio: %w", err)
	}

	return &common.DMPBillingFile{
		FileName:     fileName,
		FilePath:     fmt.Sprintf("%s/%s", s.bucketDmpBilling, fileName),
		FileType:     common.DMPBillingHistoryFileType_TransferLetter,
		DisplayName:  util.NewPointer(displayName),
		SddaIkNumber: util.NewString(request.SddaIkNumber),
	}, nil
}

func (s *EDMPService) SendKVForDMPBilling(ctx *titan.Context, request edmp.SendKVForDMPBillingRequest) (*edmp.SendKVForDMPBillingResponse, error) {
	employeeProfile, err := s.employeeProfileRepo.GetProfileById(ctx, request.DoctorId)
	if err != nil {
		return nil, err
	}
	if employeeProfile == nil || employeeProfile.Id == nil {
		return nil, nil
	}

	err = s.updateStatusForDocuments(ctx, request.DMPBillingHistoryId, request.Quarter, request.Year, common.DocumentStatus_Billed)
	if err != nil {
		return nil, err
	}
	billingHistory, err := s.edmpRepo.EdmpBillingHistoryRepo.GetById(ctx, request.DMPBillingHistoryId)
	if err != nil {
		return nil, fmt.Errorf("billing history not found")
	}
	if billingHistory == nil || billingHistory.Id == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Record_Not_Found, "billing history not found")
	}
	billingHistory.Quarter = request.Quarter
	billingHistory.Year = request.Year
	billingHistory.DoctorId = request.DoctorId
	billingHistory.TypeOfBilling = request.TypeOfBilling
	billingHistory.MarkAsCompletedBilling = request.MarkAsCompletedBilling
	billingHistory.KvConnectId = request.KvConnectId
	billingHistory.BsnrCode = employeeProfile.Bsnr
	billingHistory.SubmittedTime = util.NewPointer(util.NowUnixMillis(ctx))
	billingHistory.DmpBillingStatus = common.DmpBillingStatus_Sent
	billingHistory.SubmittedBy = util.GetPointerValue(ctx.UserInfo().UserUUID())
	if _, err = s.edmpRepo.EdmpBillingHistoryRepo.Update(ctx, billingHistory); err != nil {
		return nil, fmt.Errorf("error while updating billing history before sending email: %w", err)
	}
	mailItems, err := s.SendBillingEmail(ctx, request.DMPBillingHistoryId)
	if err != nil {
		return nil, fmt.Errorf("error while sending billing email: %w", err)
	}

	billingHistory.BillingsData = slice.Map(billingHistory.BillingsData, func(t common.BillingData) common.BillingData {
		messageIdByEnrollment := slice.FindOne(mailItems, func(m MailItem) bool {
			return m.EnrollmentId == t.EnrollmentId
		})
		if messageIdByEnrollment != nil {
			t.DmpBillingStatus = util.NewPointer(common.DmpBillingStatus_Sent)
			t.MessageId = util.NewPointer(messageIdByEnrollment.MessageUUID)
		}
		return t
	})

	if _, err = s.edmpRepo.EdmpBillingHistoryRepo.Update(ctx, billingHistory); err != nil {
		return nil, fmt.Errorf("error while updating billing history after sent email: %w", err)
	}

	return &edmp.SendKVForDMPBillingResponse{
		Status:              true,
		DMPBillingHistoryId: request.DMPBillingHistoryId,
	}, nil
}

func (s *EDMPService) updateStatusForDocuments(ctx *titan.Context, billingHistoryId uuid.UUID, quarter, year int32, stt common.DocumentStatus) error {
	billingHistory, err := s.edmpRepo.EdmpBillingHistoryRepo.FindById(ctx, billingHistoryId)
	if err != nil {
		return fmt.Errorf("failed to find edmp billing history by id: %w", err)
	}
	if billingHistory == nil || billingHistory.Id == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Record_Not_Found, "edmp billing history not found")
	}
	enrollmentIds := slice.Map(billingHistory.BillingsData, func(item common.BillingData) uuid.UUID {
		return item.EnrollmentId
	})

	documents, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.GetInQuarterByEnrollmentIds(ctx, enrollmentIds, quarter, year)
	if err != nil {
		return err
	}
	documentIds := slice.Map(documents, func(item edmp_document.EdmpEnrollmentDocumentEntity) uuid.UUID {
		return *item.Id
	})
	documentEntites, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.UpdateStatusByDocumentIds(ctx, documentIds, stt)
	if err != nil {
		return err
	}
	if len(documentEntites) > 0 {
		s.notifier(ctx, common.EventEnrollType_BilledDocument, nil, documentEntites)
	}
	return nil
}

func (s *EDMPService) GetDMPBillingHistories(ctx *titan.Context, request edmp.GetDMPBillingHistoriesRequest) (*edmp.GetDMPBillingHistoriesResponse, error) {
	var billingHistories []billing_edmp_history.BillingDMPHistoryEntity
	sortOrder := 1
	if request.PaginationRequest.Order == "DESC" {
		sortOrder = -1
	}
	total, err := s.edmpRepo.EdmpBillingHistoryRepo.Paging(
		ctx,
		bson.M{billing_edmp_history.Field_SubmittedTime: bson.M{"$ne": nil}},
		&billingHistories,
		request.PaginationRequest.Page,
		request.PaginationRequest.PageSize,
		options.Find().SetSort(bson.M{repos.Field_CreatedAt: sortOrder}),
	)
	if err != nil {
		return nil, err
	}

	if len(billingHistories) == 0 {
		return nil, nil
	}

	doctors, err := s.employeeProfileRepo.GetProfileByIds(ctx, slice.Map(billingHistories, func(b billing_edmp_history.BillingDMPHistoryEntity) uuid.UUID {
		return b.DoctorId
	}))
	if err != nil {
		return nil, fmt.Errorf("failed to get doctors %w", err)
	}
	if len(doctors) == 0 {
		return nil, nil
	}

	submittedByUsers, err := s.employeeProfileRepo.GetProfileByIds(ctx, slice.Map(billingHistories, func(b billing_edmp_history.BillingDMPHistoryEntity) uuid.UUID {
		return b.SubmittedBy
	}))
	if err != nil {
		return nil, fmt.Errorf("failed to get submitted by users %w", err)
	}
	if len(submittedByUsers) == 0 {
		return nil, nil
	}

	enrollmentIds := slice.Reduce(billingHistories, func(result []uuid.UUID, b billing_edmp_history.BillingDMPHistoryEntity) []uuid.UUID {
		return append(result, slice.Map(b.DMPBillingHistoryInfo.BillingsData, func(d common.BillingData) uuid.UUID {
			return d.EnrollmentId
		})...)
	}, []uuid.UUID{})

	enrollments, err := s.edmpRepo.EdmpEnrollmentRepo.GetByIds(ctx, enrollmentIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get enrollments %w", err)
	}

	if len(enrollments) == 0 {
		return nil, nil
	}

	totalPage := math.Ceil(float64(total) / float64(request.PaginationRequest.PageSize))
	var edmpBillingHistories []edmp.DMPBillingHistoryViewModel

	for _, billingHistory := range billingHistories {
		doctor := slice.FindOne(doctors, func(d employee.EmployeeProfile) bool {
			return *d.Id == billingHistory.DoctorId
		})

		if doctor == nil || doctor.Id == nil {
			continue
		}

		userSubmmitedBy := slice.FindOne(submittedByUsers, func(d employee.EmployeeProfile) bool {
			return *d.Id == billingHistory.SubmittedBy
		})
		if userSubmmitedBy == nil || userSubmmitedBy.Id == nil {
			continue
		}

		edmpBillingHistories = append(edmpBillingHistories, edmp.DMPBillingHistoryViewModel{
			DMPBillingHistoryId: *billingHistory.Id,
			Quarter:             billingHistory.Quarter,
			Year:                billingHistory.Year,
			Doctor: common.Doctor{
				DoctorId:  *doctor.Id,
				FirstName: doctor.FirstName,
				LastName:  doctor.LastName,
				Initial:   doctor.Initial,
				Title:     util.GetPointerValue(doctor.Title),
				FullName:  util.GetDoctorName(doctor),
			},
			SubmittedBy: edmp.SubmittedBy{
				Id:        util.GetPointerValue(userSubmmitedBy.Id),
				FirstName: userSubmmitedBy.FirstName,
				LastName:  userSubmmitedBy.LastName,
			},
			SubmittedTime: *billingHistory.SubmittedTime,
			BillingFiles: function.Do(func() []common.DMPBillingFile {
				var response []common.DMPBillingFile
				for _, d := range billingHistory.BillingsData {
					if len(d.BillingFiles) == 0 {
						continue
					}

					response = append(response, d.BillingFiles...)
				}

				return response
			}),
			TypeOfBilling:          billingHistory.TypeOfBilling,
			MarkAsCompletedBilling: billingHistory.MarkAsCompletedBilling,
			EnrollmentViewModels: slice.Map(enrollments, func(e *edmp_enrollment.EdmpEnrollmentEntity) edmp.EnrollmentViewModel {
				return edmp.EnrollmentViewModel{
					EnrollmentId:     *e.Id,
					DMPLabelingValue: e.ParticipationForm.DMPLabelingValue,
				}
			}),
			DMPLabelings: function.Do(func() []common.DMPLabeling {
				enrollmentIds := slice.Map(billingHistory.DMPBillingHistoryInfo.BillingsData, func(d common.BillingData) uuid.UUID {
					return d.EnrollmentId
				})

				var dmpLabelingValues []string
				enrollmentMap := make(map[uuid.UUID]bool)

				for _, eId := range enrollmentIds {
					enrollmentMap[eId] = true
				}

				for _, e := range enrollments {
					if enrollmentMap[util.GetPointerValue(e.Id)] {
						dmpLabelingValues = append(dmpLabelingValues, e.ParticipationForm.DMPLabelingValue)
					}
				}

				return common.GetDMPByValues(dmpLabelingValues)
			}),
			DmpBillingStatus: billingHistory.DmpBillingStatus,
			BillingsData:     billingHistory.BillingsData,
			TransferLetters:  billingHistory.TransferLetters,
		})
	}

	return &edmp.GetDMPBillingHistoriesResponse{
		DMPBillingHistories: edmpBillingHistories,
		PaginationResponse: api_common.PaginationResponse{
			Total:     total,
			TotalPage: int64(totalPage),
		},
	}, nil
}

func (s *EDMPService) GetDataCenters4DmpBilling(ctx *titan.Context, request edmp.GetDataCenters4DmpBillingsRequest) (*edmp.GetDataCenters4DmpBillingsResponse, error) {
	res, err := s.getDataCenter(ctx, request.Year, request.Quater)
	if err != nil {
		return nil, err
	}

	if res == nil {
		return nil, nil
	}

	dataCenters := res
	defaultDataCenter, err := s.getDefaultDataCenter(ctx, dataCenters, request.PatientId, request.DmpValue, request.BsnrCode)
	if err != nil {
		return nil, err
	}
	return &edmp.GetDataCenters4DmpBillingsResponse{
		DataCenters:       dataCenters,
		DefaultDataCenter: defaultDataCenter,
	}, nil
}

func (s *EDMPService) getDataCenter(ctx *titan.Context, year, quarter int32) ([]edmp.DataCenterInfo, error) {
	selfCreatedDataCenter, err := s.edmpRepo.EdmpDataCenterRepo.GetDataCenters(ctx, true)
	if err != nil {
		return nil, err
	}
	editedXmlDataCenters, err := s.edmpRepo.EdmpDataCenterRepo.GetDataCenters(ctx, false)
	if err != nil {
		return nil, err
	}

	importedXmlDataCenters, err := s.sddaRepo.GetXMLDataCenters(ctx, &sdda.GetSddaRequest{
		Year:    year,
		Quarter: quarter,
	})
	if err != nil {
		return nil, err
	}
	xmlDataCenter := uniqueXmlDataCenter(editedXmlDataCenters, importedXmlDataCenters)
	xmlDataCenter = append(selfCreatedDataCenter, xmlDataCenter...)
	sort.Slice(xmlDataCenter, func(i, j int) bool {
		return xmlDataCenter[i].DataCenter.Name > xmlDataCenter[j].DataCenter.Name
	})
	return xmlDataCenter, nil
}

func (s *EDMPService) getDefaultDataCenter(ctx *titan.Context, dataCenters []edmp.DataCenterInfo, patientId uuid.UUID, dmpValue, bsnrCode string) (edmp.DataCenterInfo, error) {
	code := bsnrCode[:2]
	vknrCode, err := s.getVknrGroup(ctx, patientId)
	if err != nil {
		return edmp.DataCenterInfo{}, err
	}
	matchedBaseOnKVArea := slice.Filter(dataCenters, func(d edmp.DataCenterInfo) bool {
		for _, kv := range d.DataCenter.KV {
			if *kv.KVArea == code {
				return true
			}
		}
		return false
	})
	if len(dataCenters) == 0 {
		return edmp.DataCenterInfo{}, fmt.Errorf("data center not found")
	}
	if len(matchedBaseOnKVArea) < 1 {
		return dataCenters[0], nil
	}
	if len(matchedBaseOnKVArea) == 1 {
		return matchedBaseOnKVArea[0], nil
	}

	matchedBaseOnDmpValue := slice.Filter(matchedBaseOnKVArea, func(d edmp.DataCenterInfo) bool {
		for _, kv := range d.DataCenter.KV {
			if *kv.KVArea == code {
				for _, dmp := range kv.DmpList {
					if dmp.Value == dmpValue {
						return true
					}
				}
			}
		}
		return false
	})

	if len(matchedBaseOnDmpValue) == 0 {
		return matchedBaseOnKVArea[0], nil
	}
	if len(matchedBaseOnDmpValue) > 0 {
		return matchedBaseOnDmpValue[0], nil
	}

	matchedBaseOnVknrCode := slice.Filter(matchedBaseOnDmpValue, func(d edmp.DataCenterInfo) bool {
		for _, kv := range d.DataCenter.KV {
			if *kv.KVArea == code {
				for _, dmp := range kv.DmpList {
					if dmp.Value == dmpValue {
						for _, vknr := range dmp.VknrGroup {
							if vknr == vknrCode {
								return true
							}
						}
					}
				}
			}
		}
		return false
	})
	if len(matchedBaseOnVknrCode) == 0 {
		return matchedBaseOnDmpValue[0], nil
	}
	return matchedBaseOnVknrCode[0], nil
}

func (s *EDMPService) getVknrGroup(ctx *titan.Context, patientId uuid.UUID) (string, error) {
	patientProfile, err := s.patientProfileService.GetProfileById(ctx, &profile.GetByIdRequest{
		PatientId: util.NewPointer(patientId),
	})
	if err != nil {
		return "", err
	}
	if patientProfile == nil {
		return "", pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Patient_Not_Found)
	}
	activeinsurance := patientProfile.PatientInfo.GetActiveInsurance()
	if activeinsurance == nil {
		return "", pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_NotActive_InsuranceInfo, "")
	}
	now := util.NowUnixMillis(ctx)
	res, err := s.catalogSdktService.GetSdktCatalogByVknr(ctx, &catalog_sdkt_api.GetSdktCatalogByVknrRequest{
		Vknr:         activeinsurance.InsuranceCompanyId,
		SelectedDate: &now,
	})
	if err != nil {
		return "", err
	}
	if res == nil || res.Data == nil {
		return "", nil
	}
	return res.Data.VknrGroup, nil
}

type GenUrlFileForDmpBillingSummaryResponse struct {
	DownloadLink string
	File         common.DMPBillingFile
}

func (s *EDMPService) GenUrlFileForDmpBillingSummary(
	ctx *titan.Context,
	name string,
	document *edmp_document.EdmpEnrollmentDocumentEntity,
	documentType common.DocumentType,
	fileReader *bytes.Reader,
	billingFileType common.DMPBillingHistoryFileType,
) (*GenUrlFileForDmpBillingSummaryResponse, error) {
	fileName := fmt.Sprintf("%s_%s_%s_%s_%s.pdf",
		name,
		common.GetDMPByValue(document.DMPLabelingValue).ICDCode,
		documentType,
		util.Now(ctx).Format("01_02_2006T15_04_05"),
		strings.ReplaceAll(uuid.NewString(), "-", "_"),
	)
	_, err := s.minioClient.PutObject(ctx, s.bucketDmpBilling, s.bucketDmpBilling, fileReader, fileReader.Size(), minio.PutObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to upload file to minio: %w", err)
	}

	url, err := s.minioClient.PresignedGetObject(ctx, s.bucketDmpBilling, fileName, oneDay, url.Values{
		"response-content-type":        []string{"application/pdf"},
		"response-content-disposition": []string{fmt.Sprintf("inline; filename=%q", fileName)},
	})
	if err != nil {
		return nil, err
	}

	res := GenUrlFileForDmpBillingSummaryResponse{
		DownloadLink: url.String(),
		File: common.DMPBillingFile{
			FileName: fileName,
			FilePath: fmt.Sprintf("%s/%s", s.bucketDmpBilling, fileName),
			FileType: billingFileType,
		},
	}
	return &res, nil
}

type ZipDMPFilesRequest struct {
	folderName string
	zipName    string
	files      []common.DMPBillingFile
}

func (s *EDMPService) zipDMPFiles(ctx *titan.Context, request ZipDMPFilesRequest) (*common.DMPBillingFile, error) {
	for _, f := range request.files {
		fileByte, err := s.minioClient.DownloadFile(ctx, s.bucketDmpBilling, f.FileName, minio.GetObjectOptions{})
		if err != nil {
			return nil, fmt.Errorf("failed to download file: %w", err)
		}

		err = ioutil.WriteFile(fmt.Sprintf("%s/%s", request.folderName, f.FileName), fileByte, 0644)
		if err != nil {
			return nil, fmt.Errorf("failed to write file: %w", err)
		}
	}

	err := zip_file.ZipFolder(request.folderName, request.zipName)
	if err != nil {
		return nil, fmt.Errorf("failed to zip folder: %w", err)
	}

	zipDecodeBytes, err := ioutil.ReadFile(request.zipName)
	if err != nil {
		return nil, fmt.Errorf("failed to read zip file: %w", err)
	}

	fileReader := bytes.NewReader(zipDecodeBytes)
	_, err = s.minioClient.PutObject(ctx, s.bucketDmpBilling, request.zipName, fileReader, fileReader.Size(), minio.PutObjectOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to upload zip file to minio: %w", err)
	}

	defer os.RemoveAll(request.zipName)    // TODO: remove zip file
	defer os.RemoveAll(request.folderName) // TODO: remove folder

	return &common.DMPBillingFile{
		FileName: request.zipName,
		FilePath: fmt.Sprintf("%s/%s", s.bucketDmpBilling, request.zipName),
		FileType: common.DMPBillingHistoryFileType_Billing,
	}, nil
}

func (*EDMPService) createDMPTempDir(name string) (*string, error) {
	tempDir, err := os.MkdirTemp("./", name)
	if err != nil {
		return nil, fmt.Errorf("failed to create temp dir: %w", err)
	}

	err = os.MkdirAll(tempDir, 0777)
	if err != nil {
		return nil, fmt.Errorf("failed to create temp dir: %w", err)
	}

	return &tempDir, nil
}

func (*EDMPService) genZipDMPBillingHistoryFileName(ctx *titan.Context) string {
	// TODO: Just a temp name for the zip file. Will be changed later
	return fmt.Sprint(util.NowUnixMillis(ctx)) + ".zip"
}

func (s *EDMPService) ZipDMPBillingHistories(ctx *titan.Context, request edmp.ZipDMPBillingHistoriesRequest) (*edmp.ZipDMPBillingHistoriesResponse, error) {
	billingHistories, err := s.edmpRepo.EdmpBillingHistoryRepo.FindByIds(ctx, request.DMPBillingHistoryIds)
	if err != nil {
		return nil, fmt.Errorf("failed to find billing histories: %w", err)
	}

	// zip all files in billing data
	var zipFiles []common.DMPBillingFile
	for _, bh := range billingHistories {
		if len(bh.BillingsData) == 0 {
			continue
		}

		for _, bd := range bh.BillingsData {
			if len(bd.BillingFiles) == 0 {
				continue
			}

			tempDir, err := s.createDMPTempDir("dmpBilling-")
			if err != nil {
				return nil, fmt.Errorf("failed to create temp dir: %w", err)
			}
			if tempDir == nil {
				return nil, fmt.Errorf("failed to create temp dir")
			}

			zipFileName := s.genZipDMPBillingHistoryFileName(ctx)

			dmpBillingZipFile, err := s.zipDMPFiles(
				ctx,
				ZipDMPFilesRequest{
					folderName: *tempDir,
					files:      bd.BillingFiles,
					zipName:    zipFileName,
				})
			if err != nil {
				return nil, fmt.Errorf("failed to zip files: %w", err)
			}
			if dmpBillingZipFile == nil {
				return nil, fmt.Errorf("failed to zip files")
			}

			zipFiles = append(zipFiles, *dmpBillingZipFile)
		}
	}

	// Zip all the zip files
	tempDir, err := s.createDMPTempDir("BillingHistory-")
	if err != nil {
		return nil, fmt.Errorf("failed to create temp dir: %w", err)
	}
	if tempDir == nil {
		return nil, fmt.Errorf("failed to create temp dir")
	}

	// TODO: Just a temp name for the zip file. Will be changed later
	fileName := fmt.Sprintf("%s%s%s", util.NewUUID().String(), fmt.Sprint(util.NowUnixMillis(ctx)), ".zip")

	_, err = s.zipDMPFiles(
		ctx,
		ZipDMPFilesRequest{
			folderName: util.GetPointerValue(tempDir),
			zipName:    fileName,
			files:      zipFiles,
		})
	if err != nil {
		return nil, fmt.Errorf("failed to zip files: %w", err)
	}

	defer os.RemoveAll(util.GetPointerValue(tempDir))

	return &edmp.ZipDMPBillingHistoriesResponse{
		DMPBillingFile: common.DMPBillingFile{
			FileName: fileName,
			FilePath: fmt.Sprintf("%s/%s", s.bucketDmpBilling, fileName),
			FileType: common.DMPBillingHistoryFileType_Billing,
		},
	}, nil
}

func (s *EDMPService) GetActiveDocumentsByScheinId(ctx *titan.Context, schienId, patientId uuid.UUID) ([]edmp_document.EdmpEnrollmentDocumentEntity, error) {
	documents, err := s.edmpRepo.EdmpEnrollmentDocumentRepo.GetByScheinId(ctx, schienId)
	if err != nil {
		return nil, fmt.Errorf("failed to get documents: %w", err)
	}

	mapEnrolmentIdWithDocuments := make(map[uuid.UUID][]edmp_document.EdmpEnrollmentDocumentEntity)
	for _, d := range documents {
		if mapEnrolmentIdWithDocuments[d.EnrollmentId] == nil {
			mapEnrolmentIdWithDocuments[d.EnrollmentId] = []edmp_document.EdmpEnrollmentDocumentEntity{d}
		} else {
			mapEnrolmentIdWithDocuments[d.EnrollmentId] = append(mapEnrolmentIdWithDocuments[d.EnrollmentId], d)
		}
	}

	enrollments, err := s.GetActiveEnrollmentByPatientId(ctx, patientId)
	if err != nil {
		return nil, fmt.Errorf("failed to get active enrollments: %w", err)
	}
	if len(enrollments) == 0 {
		return nil, nil
	}

	var activeDocuments []edmp_document.EdmpEnrollmentDocumentEntity
	for _, e := range enrollments {
		activeDocuments = append(activeDocuments, mapEnrolmentIdWithDocuments[*e.Id]...)
	}

	return activeDocuments, nil
}
