// This code was autogenerated from service/domains/edmp_common.proto, do not edit.

package common

import (
	"encoding/json"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	patient_profile_common "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"

	schein_common "git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"

	sdda_common "git.tutum.dev/medi/tutum/ares/service/domains/edmp/sdda_common"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type DMPLabeling struct {
	Name                string `json:"name"`
	GermanName          string `json:"germanName"`
	ParticipationLetter string `json:"participationLetter"`
	Value               string `json:"value"`
	ICDCode             string `json:"iCDCode"`
}

type ParticipationForm struct {
	DMPLabelingValue         string                   `json:"dMPLabelingValue"`
	ParticipationFormsStatus ParticipationFormsStatus `json:"participationFormsStatus"`
	TreatmentDoctorId        uuid.UUID                `json:"treatmentDoctorId"`
	EnrollStatus             *EnrollStatus            `json:"enrollStatus"`
	FormSetting              *string                  `json:"formSetting" validate:"omitempty,trimjson"`
}

type XPMErrorContent struct {
	ErrorNo         string    `json:"errorNo"`
	Message         string    `json:"message"`
	LineNumberError int32     `json:"lineNumberError"`
	HeaderName      string    `json:"headerName"`
	FieldName       string    `json:"fieldName"`
	ErrorType       ErrorType `json:"errorType"`
	BillingFileName string    `json:"billingFileName"`
}

type XPMErrorResponse struct {
	DPMCaseNumber    string            `json:"dPMCaseNumber"`
	XPMErrorContents []XPMErrorContent `json:"xPMErrorContents"`
	XPMFileType      XPMFileType       `json:"xPMFileType"`
}

type XPMResult struct {
	Status           string             `json:"status"`
	StatisticFile    DMPBillingFile     `json:"statisticFile"`
	ProtocolFile     DMPBillingFile     `json:"protocolFile"`
	XPMErrorResponse []XPMErrorResponse `json:"xPMErrorResponse"`
}

type PHQ9 struct {
	FileName      string  `json:"fileName"`
	Scores        []int32 `json:"scores"`
	Totals        []int32 `json:"totals"`
	PreviousScore *int32  `json:"previousScore"`
}

type DocumentationOverview struct {
	EnrollmentId            uuid.UUID                `json:"enrollmentId"`
	DocumentType            DocumentType             `json:"documentType"`
	ScheinId                uuid.UUID                `json:"scheinId" validate:"required"`
	DMPLabelingValue        string                   `json:"dMPLabelingValue" validate:"required"`
	TreatmentDoctorId       uuid.UUID                `json:"treatmentDoctorId" validate:"required"`
	DoctorId                uuid.UUID                `json:"doctorId" validate:"required"`
	DocumentStatus          *DocumentStatus          `json:"documentStatus"`
	EnrollStatus            *EnrollStatus            `json:"enrollStatus"`
	PatientId               *uuid.UUID               `json:"patientId"`
	Fields                  []Field                  `json:"fields"`
	DoctorRelationType      DoctorRelationType       `json:"doctorRelationType"`
	DocumentationOverviewId *uuid.UUID               `json:"documentationOverviewId"`
	DocumentDate            int64                    `json:"documentDate" validate:"required"`
	DMPBillingFile          DMPBillingFile           `json:"dMPBillingFile"`
	PHQ9                    *PHQ9                    `json:"pHQ9"`
	DMPCaseNumber           string                   `json:"dMPCaseNumber" validate:"required"`
	BsnrCode                *string                  `json:"bsnrCode"`
	BsnrId                  *uuid.UUID               `json:"bsnrId"`
	AdditionalContracts     *AdditionalContractsEnum `json:"additionalContracts"`
}

type PatientFrequency struct {
	IsEveryQuarter    bool `json:"isEveryQuarter"`
	IsEveryTwoQuarter bool `json:"isEveryTwoQuarter"`
}

type InsuranceProgrammes struct {
	SmokingCessation      bool `json:"smokingCessation"`
	NutritionalCounseling bool `json:"nutritionalCounseling"`
	PhysicalTraining      bool `json:"physicalTraining"`
}

type PatientMedicalData struct {
	Height                   float32 `json:"height"`
	Weight                   int32   `json:"weight"`
	BloodPressureDiastolisch string  `json:"bloodPressureDiastolisch"`
	BloodPressureSystolisch  string  `json:"bloodPressureSystolisch"`
	Smoker                   bool    `json:"smoker"`
}

type ActivatedTime struct {
	StartDate *int64 `json:"startDate"`
	EndDate   *int64 `json:"endDate"`
}

type EnrollmentInfo struct {
	DoctorId            uuid.UUID                             `json:"doctorId" validate:"required"`
	PatientId           uuid.UUID                             `json:"patientId" validate:"required"`
	DMPCaseNumber       string                                `json:"dMPCaseNumber" validate:"required"`
	ParticipationForm   ParticipationForm                     `json:"participationForm" validate:"required"`
	PatientFrequency    *PatientFrequency                     `json:"patientFrequency"`
	InsuranceProgrammes *InsuranceProgrammes                  `json:"insuranceProgrammes"`
	PatientMedicalData  PatientMedicalData                    `json:"patientMedicalData" validate:"required"`
	TreatmentDoctorId   uuid.UUID                             `json:"treatmentDoctorId" validate:"required"`
	ActivatedTime       *ActivatedTime                        `json:"activatedTime"`
	InsuranceInfo       *patient_profile_common.InsuranceInfo `json:"insuranceInfo"`
}

type EnrollmentInfoRequest struct {
	DoctorId            uuid.UUID            `json:"doctorId" validate:"required"`
	PatientId           uuid.UUID            `json:"patientId" validate:"required"`
	DMPCaseNumber       string               `json:"dMPCaseNumber" validate:"required"`
	ParticipationForms  []ParticipationForm  `json:"participationForms" validate:"required"`
	PatientFrequency    *PatientFrequency    `json:"patientFrequency"`
	InsuranceProgrammes *InsuranceProgrammes `json:"insuranceProgrammes"`
	PatientMedicalData  PatientMedicalData   `json:"patientMedicalData" validate:"required"`
	TreatmentDoctorId   uuid.UUID            `json:"treatmentDoctorId" validate:"required"`
}

type EnrollmentWithDocumentModel struct {
	EnrollmentInfoModel         EnrollmentInfoModel           `json:"enrollmentInfoModel"`
	EnrollmentDocumentInfoModel []EnrollmentDocumentInfoModel `json:"enrollmentDocumentInfoModel"`
}

type EnrollmentInfoModel struct {
	Id             uuid.UUID      `json:"id"`
	EnrollmentInfo EnrollmentInfo `json:"enrollmentInfo"`
}

type EnrollmentDocumentInfoModel struct {
	Id                    uuid.UUID             `json:"id"`
	DocumentationOverview DocumentationOverview `json:"documentationOverview"`
	Patient               Patient               `json:"patient"`
	Doctor                Doctor                `json:"doctor"`
	Schein                schein_common.Schein  `json:"schein"`
	CreatedAt             *int64                `json:"createdAt"`
}

type Patient struct {
	PatientId       uuid.UUID                             `json:"patientId"`
	FirstName       string                                `json:"firstName"`
	LastName        string                                `json:"lastName"`
	DateOfBirth     patient_profile_common.DateOfBirth    `json:"dateOfBirth"`
	PatientNumber   int64                                 `json:"patientNumber"`
	FullName        string                                `json:"fullName"`
	ActiveInsurance *patient_profile_common.InsuranceInfo `json:"activeInsurance"`
}

type Doctor struct {
	DoctorId  uuid.UUID `json:"doctorId"`
	FirstName string    `json:"firstName"`
	LastName  string    `json:"lastName"`
	Initial   string    `json:"initial"`
	Title     string    `json:"title"`
	FullName  string    `json:"fullName"`
}

type DmpProgram struct {
	DMPLabelingValue string       `json:"dMPLabelingValue"`
	EnrollStatus     EnrollStatus `json:"enrollStatus"`
	StartDate        *int64       `json:"startDate"`
	Doctor           *Doctor      `json:"doctor"`
	TotalED          Total        `json:"totalED"`
	TotalFD          Total        `json:"totalFD"`
	TotalPED         *Total       `json:"totalPED"`
	TotalPHQ9_ED     *Total       `json:"totalPHQ9_ED"`
	TotalPHQ9_FD     *Total       `json:"totalPHQ9_FD"`
}

type Total struct {
	Complete   int32 `json:"complete"`
	InComplete int32 `json:"inComplete"`
	Submitted  int32 `json:"submitted"`
}

type DmpPatientInfo struct {
	Patient         Patient      `json:"patient"`
	InsuranceNumber *string      `json:"insuranceNumber"`
	DmpProgram      []DmpProgram `json:"dmpProgram"`
}

type EdokuPatientOverview struct {
	Id        uuid.UUID             `json:"id"`
	Patient   Patient               `json:"patient"`
	Doctor    Doctor                `json:"doctor"`
	Document  DocumentationOverview `json:"document"`
	CreatedAt *int64                `json:"createdAt"`
}

type Option struct {
	Name              string     `json:"name"`
	Label             string     `json:"label"`
	Placeholder       string     `json:"placeholder"`
	FieldType         *FieldType `json:"fieldType"`
	Unit              *string    `json:"unit"`
	IsFloat           *bool      `json:"isFloat"`
	DecimalDigits     *int32     `json:"decimalDigits"`
	MinLength         *int32     `json:"minLength"`
	MaxLength         *int32     `json:"maxLength"`
	HiddenFieldNames  []string   `json:"hiddenFieldNames"`
	HiddenHeaderNames []string   `json:"hiddenHeaderNames"`
}

type FieldValue struct {
	Value     string    `json:"value"`
	FieldType FieldType `json:"fieldType"`
	ValueUnit *string   `json:"valueUnit"`
	Name      string    `json:"name"`
}

type Position struct {
	P int32 `json:"p"`
	E int32 `json:"e"`
}

type Field struct {
	Name             string        `json:"name"`
	Label            string        `json:"label"`
	IsRequire        bool          `json:"isRequire"`
	Values           []FieldValue  `json:"values"`
	FieldType        FieldType     `json:"fieldType"`
	Options          []Option      `json:"options"`
	IsVertical       bool          `json:"isVertical"`
	DocumentType     DocumentType  `json:"documentType"`
	Fields           []Field       `json:"fields"`
	PositionNoNumber int32         `json:"positionNoNumber"`
	PositionValue    string        `json:"positionValue"`
	Header           string        `json:"header"`
	Placeholder      string        `json:"placeholder"`
	Unit             *string       `json:"unit"`
	HeaderStatus     *HeaderStatus `json:"headerStatus"`
	NestedFieldNames []string      `json:"nestedFieldNames"`
	Position         Position      `json:"position"`
	MinLength        *int32        `json:"minLength"`
	MaxLength        *int32        `json:"maxLength"`
	IsFloat          *bool         `json:"isFloat"`
	DecimalDigits    *int32        `json:"decimalDigits"`
	ReadOnly         *bool         `json:"readOnly"`
	EmptyDateFormat  *bool         `json:"emptyDateFormat"`
	DisplayHeader    *string       `json:"displayHeader"`
	LiveCheck        *bool         `json:"liveCheck"`
}

type DMP struct {
	DMPLabelingValue string        `json:"dMPLabelingValue"`
	Fields           []Field       `json:"fields"`
	HeaderStatus     *HeaderStatus `json:"headerStatus"`
	HeaderName       string        `json:"headerName"`
	Position         Position      `json:"position"`
	DisplayHeader    *string       `json:"displayHeader"`
}

type Header struct {
	Name string `json:"name"`
	DMPs []DMP  `json:"dMPs"`
}

type DMPDocument struct {
	HeadersED []Header     `json:"headersED"`
	HeadersFD []Header     `json:"headersFD"`
	HeadersPD []Header     `json:"headersPD"`
	DMPValue  DMPValueEnum `json:"dMPValue"`
}

type EDOKUDocument struct {
	Headers  []Header     `json:"headers"`
	DMPValue DMPValueEnum `json:"dMPValue"`
}

type QuarterToDateRange struct {
	Quarter int64 `json:"quarter" validate:"required"`
	Year    int64 `json:"year" validate:"required"`
}

type DMPBillingFile struct {
	FileName     string                    `json:"fileName"`
	FilePath     string                    `json:"filePath"`
	DisplayName  *string                   `json:"displayName"`
	FileType     DMPBillingHistoryFileType `json:"fileType"`
	SddaIkNumber *string                   `json:"sddaIkNumber"`
}

type DmpBillingError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

type BillingData struct {
	EnrollmentId     uuid.UUID              `json:"enrollmentId"`
	DataCenter       sdda_common.DataCenter `json:"dataCenter"`
	BillingFiles     []DMPBillingFile       `json:"billingFiles"`
	MessageId        *uuid.UUID             `json:"messageId"`
	DmpBillingStatus *DmpBillingStatus      `json:"dmpBillingStatus"`
}

type DMPBillingHistoryInfo struct {
	Quarter                int32            `json:"quarter"`
	Year                   int32            `json:"year"`
	DoctorId               uuid.UUID        `json:"doctorId"`
	SubmittedBy            uuid.UUID        `json:"submittedBy"`
	SubmittedTime          *int64           `json:"submittedTime"`
	TypeOfBilling          TypeOfBilling    `json:"typeOfBilling"`
	MarkAsCompletedBilling bool             `json:"markAsCompletedBilling"`
	BillingsData           []BillingData    `json:"billingsData"`
	DmpBillingStatus       DmpBillingStatus `json:"dmpBillingStatus"`
	DmpBillingError        *DmpBillingError `json:"dmpBillingError"`
	KvConnectId            uuid.UUID        `json:"kvConnectId"`
	BsnrCode               string           `json:"bsnrCode"`
	TransferLetters        []DMPBillingFile `json:"transferLetters"`
}

type DMPDocumentationType struct {
	DocumentationIds    []uuid.UUID            `json:"documentationIds"`
	DocumentationStatus DMPDocumentationStatus `json:"documentationStatus"`
	Total               int32                  `json:"total"`
	DocumentType        DocumentType           `json:"documentType"`
}

type EnrollmentWithDataCenter struct {
	EnrollmentId uuid.UUID              `json:"enrollmentId" validate:"required"`
	DataCenter   sdda_common.DataCenter `json:"dataCenter" validate:"required"`
}

type Script struct {
	Version string `json:"version"`
	Name    string `json:"name"`
	Content string `json:"content"`
}

type FieldValidationResult struct {
	FieldName                 string                    `json:"fieldName"`
	ErrorCode                 string                    `json:"errorCode"`
	ErrorMessage              string                    `json:"errorMessage"`
	HeaderName                *string                   `json:"headerName"`
	FieldValidationResultType FieldValidationResultType `json:"fieldValidationResultType"`
	Script                    *Script                   `json:"script"`
	ErrorType                 ErrorType                 `json:"errorType"`
	BillingFileName           string                    `json:"billingFileName"`
}

type DMPBillingFieldsValidationResult struct {
	DocumentId             uuid.UUID               `json:"documentId"`
	FieldValidationResults []FieldValidationResult `json:"fieldValidationResults"`
	IsPlausibility         bool                    `json:"isPlausibility"`
	XPMResultFile          DMPBillingFile          `json:"xPMResultFile"`
}

// enum definitions
type ParticipationFormsStatus string

const (
	ParticipationFormsStatus_Print   ParticipationFormsStatus = "ParticipationFormsStatus_Print"
	ParticipationFormsStatus_Save    ParticipationFormsStatus = "ParticipationFormsStatus_Save"
	ParticipationFormsStatus_Created ParticipationFormsStatus = "ParticipationFormsStatus_Created"
)

type DocumentType string

const (
	DocumentType_ED         DocumentType = "DocumentType_ED"
	DocumentType_FD         DocumentType = "DocumentType_FD"
	DocumentType_PD         DocumentType = "DocumentType_PD"
	DocumentType_PHQ9_ED    DocumentType = "DocumentType_PHQ9_ED"
	DocumentType_PHQ9_FD    DocumentType = "DocumentType_PHQ9_FD"
	DocumentType_EHKS_D     DocumentType = "DocumentType_EHKS_D"
	DocumentType_EHKS_ND    DocumentType = "DocumentType_EHKS_ND"
	DocumentType_EHKS_D_EV  DocumentType = "DocumentType_EHKS_D_EV"
	DocumentType_EHKS_ND_EV DocumentType = "DocumentType_EHKS_ND_EV"
)

type DocumentStatus string

const (
	DocumentStatus_Saved    DocumentStatus = "DocumentStatus_Saved"
	DocumentStatus_Finished DocumentStatus = "DocumentStatus_Finished"
	DocumentStatus_Billed   DocumentStatus = "DocumentStatus_Billed"
	DocumentStatus_Printed  DocumentStatus = "DocumentStatus_Printed"
)

type EnrollStatus string

const (
	StatusActivated  EnrollStatus = "Activated"
	StatusPotential  EnrollStatus = "Potential"
	StatusTerminated EnrollStatus = "Terminated"
)

type DoctorRelationType string

const (
	DoctorRelationType_Treatment    DoctorRelationType = "DoctorRelationType_Treatment"
	DoctorRelationType_Deputy       DoctorRelationType = "DoctorRelationType_Deputy"
	DoctorRelationType_DoctorCharge DoctorRelationType = "DoctorRelationType_DoctorCharge"
)

type XPMFileType string

const (
	XPMFileType_Protocol  XPMFileType = "XPMFileType_Protocol"
	XPMFileType_Statistic XPMFileType = "XPMFileType_Statistic"
)

type AdditionalContractsEnum string

const (
	AdditionalContractsEnum_Yes AdditionalContractsEnum = "yes"
	AdditionalContractsEnum_No  AdditionalContractsEnum = "no"
)

type EventEnrollType string

const (
	EventEnrollType_Print_Save_Form EventEnrollType = "EventEnrollType_Print_Save_Form"
	EventEnrollType_Terminated      EventEnrollType = "EventEnrollType_Terminated"
	EventEnrollType_CreateDocument  EventEnrollType = "EventEnrollType_CreateDocument"
	EventEnrollType_Enroll          EventEnrollType = "EventEnrollType_Enroll"
	EventEnrollType_SaveDocument    EventEnrollType = "EventEnrollType_SaveDocument"
	EventEnrollType_FinishDocument  EventEnrollType = "EventEnrollType_FinishDocument"
	EventEnrollType_PrintDocument   EventEnrollType = "EventEnrollType_PrintDocument"
	EventEnrollType_BilledDocument  EventEnrollType = "EventEnrollType_BilledDocument"
	EventEnrollType_FetchEnroll     EventEnrollType = "EventEnrollType_FetchEnroll"
)

type FieldType string

const (
	FieldType_Text     FieldType = "FieldType_Text"
	FieldType_Number   FieldType = "FieldType_Number"
	FieldType_Date     FieldType = "FieldType_Date"
	FieldType_Checkbox FieldType = "FieldType_Checkbox"
	FieldType_Radio    FieldType = "FieldType_Radio"
	FieldType_Nested   FieldType = "FieldType_Nested"
	FieldType_Unknown  FieldType = "FieldType_Unknown"
)

type HeaderStatus string

const (
	HeaderStatus_NotFilled  HeaderStatus = "HeaderStatus_NotFilled"
	HeaderStatus_Completed  HeaderStatus = "HeaderStatus_Completed"
	HeaderStatus_Incomplete HeaderStatus = "HeaderStatus_Incomplete"
)

type DMPValueEnum string

const (
	DMPValueEnum_MellitusType1         DMPValueEnum = "04"
	DMPValueEnum_MellitusType2         DMPValueEnum = "01"
	DMPValueEnum_Brustkrebs            DMPValueEnum = "02"
	DMPValueEnum_CoronaryArteryDisease DMPValueEnum = "03"
	DMPValueEnum_AsthmaBronchiale      DMPValueEnum = "05"
	DMPValueEnum_COPD                  DMPValueEnum = "06"
	DMPValueEnum_ChronicHeartFailure   DMPValueEnum = "07"
	DMPValueEnum_Depression            DMPValueEnum = "08"
	DMPValueEnum_BackPain              DMPValueEnum = "09"
	DMPValueEnum_EDO_SkinCancer        DMPValueEnum = "10"
)

type FileTypeEnum string

const (
	FileTypeEnum_XML FileTypeEnum = "FileTypeEnum_XML"
	FileTypeEnum_PDF FileTypeEnum = "FileTypeEnum_PDF"
)

type EdmpTestModuleResultEnum string

const (
	EdmpTestModuleResultEnum_Correct   EdmpTestModuleResultEnum = "EdmpTestModuleResultEnum_Correct"
	EdmpTestModuleResultEnum_Incorrect EdmpTestModuleResultEnum = "EdmpTestModuleResultEnum_Incorrect"
)

type DMPBillingHistoryFileType string

const (
	DMPBillingHistoryFileType_Protocol       DMPBillingHistoryFileType = "DMPBillingHistoryFileType_Protocol"
	DMPBillingHistoryFileType_Statistic      DMPBillingHistoryFileType = "DMPBillingHistoryFileType_Statistic"
	DMPBillingHistoryFileType_Companion      DMPBillingHistoryFileType = "DMPBillingHistoryFileType_Companion"
	DMPBillingHistoryFileType_Billing        DMPBillingHistoryFileType = "DMPBillingHistoryFileType_Billing"
	DMPBillingHistoryFileType_TransferLetter DMPBillingHistoryFileType = "DMPBillingHistoryFileType_TransferLetter"
)

type DmpBillingStatus string

const (
	DmpBillingStatus_Sent                 DmpBillingStatus = "Sent"
	DmpBillingStatus_Confirmed            DmpBillingStatus = "Confirmed"
	DmpBillingStatus_Feedback_Available   DmpBillingStatus = "Feedback Available"
	DmpBillingStatus_Empty                DmpBillingStatus = "Empty"
	DmpBillingStatus_Sending              DmpBillingStatus = "Sending"
	DmpBillingStatus_Sending_Failed       DmpBillingStatus = "Sending Failed"
	DmpBillingStatus_Timeout_ReceiveEmail DmpBillingStatus = "Timeout Receive Email"
	DmpBillingStatus_ReceiveEmail_Failed  DmpBillingStatus = "Timeout Receive Email"
)

type TypeOfBilling string

const (
	TypeOfBilling_Send_As_Real_Billing      TypeOfBilling = "TypeOfBilling_Send_As_Real_Billing"
	TypeOfBilling_Send_As_Real_Test_Billing TypeOfBilling = "TypeOfBilling_Send_As_Real_Test_Billing"
)

type DMPDocumentationStatus string

const (
	DMPDocumentationStatus_Complete   DMPDocumentationStatus = "DMPDocumentationStatus_Complete"
	DMPDocumentationStatus_Incomplete DMPDocumentationStatus = "DMPDocumentationStatus_Incomplete"
)

type FieldValidationResultType string

const (
	FieldValidationResultType_XPMCheck  FieldValidationResultType = "FieldValidationResultType_XPMCheck"
	FieldValidationResultType_SelfCheck FieldValidationResultType = "FieldValidationResultType_SelfCheck"
)

type ErrorType string

const (
	ErrorType_Error   ErrorType = "ErrorType_Error"
	ErrorType_Warning ErrorType = "ErrorType_Warning"
	ErrorType_Schema  ErrorType = "ErrorType_Schema"
)

type SequenceType string

const (
	SequenceType_Billing   SequenceType = "SequenceType_Billing"
	SequenceType_Conpanion SequenceType = "SequenceType_Conpanion"
	SequenceType_XKM       SequenceType = "SequenceType_XKM"
)

// Define constants
const NATS_SUBJECT = "" // nats subject this service will listen to

// service event constants

// message event constants

// Define service interface -------------------------------------------------------------

// Define service proxy -------------------------------------------------------------------

// Define service router -----------------------------------------------------------------

// Define client ----------------------------------------------------------------------------------------------

// Define event Notifier -----------------------------------------------------------------------------------------
type CommonNotifier struct {
	client *titan.Client
}

func NewCommonNotifier() *CommonNotifier {
	client := titan.GetDefaultClient()
	return &CommonNotifier{client}
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type CommonSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewCommonSocketNotifier(socket *socket_api.SocketServiceClient) *CommonSocketNotifier {
	return &CommonSocketNotifier{socket}
}

// -----------------------------------------------
// Test event listener
type CommonEventListener struct {
	mux sync.Mutex
}

func (listener *CommonEventListener) Reset() {
	listener.mux.Lock()
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *CommonEventListener) Subscribe(s *titan.MessageSubscriber) {
}
