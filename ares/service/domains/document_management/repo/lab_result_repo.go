package repo

import (
	"emperror.dev/errors"
	"git.tutum.dev/medi/tutum/ares/pkg/repo"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	Field_DocumentManagementId = "documentManagementId"
	Field_PatientId            = "patientId"
	Field_LabParameterResults  = "labParameterResults"
)

type LabResultRepo struct {
	repo.Repo[*LabResultEntity]
}

type LabResultEntity struct {
	repos.BaseEntity `bson:",inline"`
	LabResult        `bson:",inline"`
}

type LabParameterResult struct {
	Name                   string `bson:"name"`
	Unit                   string `bson:"unit"`
	Min                    string `bson:"min"`
	Max                    string `bson:"max"`
	Value                  string `bson:"value"`
	TestResultText         string `bson:"testResultText"`
	TestNote               string `bson:"testNote"`
	ThresholdIndicatorIcon string `bson:"thresholdIndicatorIcon"`
}

type LabResult struct {
	PatientId            uuid.UUID            `bson:"patientId"`
	OrderId              string               `bson:"orderId"`
	LabParameterResults  []LabParameterResult `bson:"labParameterResults"`
	CreatedDate          int64                `bson:"createdDate"`
	DocumentManagementId *uuid.UUID           `bson:"documentManagementId"`
}

// LabResultFilter represents optional criteria when querying lab results.
// Using a small struct here avoids importing higher-level API packages and keeps the repository decoupled.
type LabResultFilter struct {
	PatientId  uuid.UUID
	FromDate   *int64   // milliseconds – inclusive lower bound
	ToDate     *int64   // milliseconds – inclusive upper bound
	FieldNames []string // names (case sensitive) to match in labParameterResults.name
	Limit      *int64   // max number of documents to return
}

var LabResultRepoMod = submodule.Make[*LabResultRepo](func() *LabResultRepo {
	return &LabResultRepo{
		repo.NewRepo[*LabResultEntity](mongodb_repo.NewMongoDbIDBClient("tutum_mvz_db", "lab_result", true)),
	}
})

func (r *LabResultRepo) Create(ctx *titan.Context, model *LabResult) (*LabResultEntity, error) {
	entity := &LabResultEntity{
		BaseEntity: repos.NewBaseEntity(ctx),
		LabResult:  *model,
	}
	return r.Repo.Create(ctx, entity)
}

func (r *LabResultRepo) GetLabResultByPatientId(ctx *titan.Context, patientId uuid.UUID) ([]*LabResultEntity, error) {
	filter := bson.M{Field_PatientId: patientId}
	return r.Repo.Find(ctx, filter, options.Find().SetSort(bson.M{"date": -1}))
}

func (r *LabResultRepo) GetLabResultByDocumentManagementIds(ctx *titan.Context, documentManagementIds []uuid.UUID) ([]*LabResultEntity, error) {
	filter := bson.M{Field_DocumentManagementId: bson.M{"$in": documentManagementIds}}
	return r.Repo.Find(ctx, filter, options.Find().SetSort(bson.M{"createdDate": -1}))
}

func (r *LabResultRepo) UpdateByDocumentManagementId(ctx *titan.Context, documentManagementId uuid.UUID, labParameterResults []LabParameterResult) error {
	filter := bson.M{Field_DocumentManagementId: documentManagementId}
	update := bson.M{"$set": bson.M{Field_LabParameterResults: labParameterResults}}
	_, err := r.Repo.IDBClient.UpdateMany(ctx, filter, update, nil)
	if err != nil {
		return errors.WithMessage(err, "failed to update lab result by document management id")
	}
	return nil
}

// GetLabResultsByFilter returns lab-results for a patient that satisfy the optional filter criteria.
// – If FromDate/ToDate are both provided, results are constrained to that date range.
// – If FieldNames is supplied, only results that contain at least one of the given field names are returned.
// – Limit allows caller to cap number of documents (sorted by most recent CreatedDate first).
func (r *LabResultRepo) GetLabResultsByFilter(ctx *titan.Context, f LabResultFilter) ([]*LabResultEntity, error) {
	mongoFilter := bson.M{
		"patientId": f.PatientId,
	}

	// Date range filtering: support providing only FromDate or only ToDate.
	if f.FromDate != nil || f.ToDate != nil {
		dateCond := bson.M{}
		if f.FromDate != nil {
			dateCond["$gte"] = *f.FromDate
		}
		if f.ToDate != nil {
			dateCond["$lte"] = *f.ToDate
		}
		mongoFilter["createdDate"] = dateCond
	}

	findOpts := options.Find().SetSort(bson.M{"createdDate": -1})
	if f.Limit != nil {
		findOpts.SetLimit(*f.Limit)
	}

	return r.Repo.Find(ctx, mongoFilter, findOpts)
}

func (r *LabResultRepo) UpdateDocumentManagementIdById(ctx *titan.Context, id, documentManagementId uuid.UUID) error {
	filter := bson.M{"_id": id}
	update := bson.M{"$set": bson.M{Field_DocumentManagementId: documentManagementId}}
	res, err := r.Repo.IDBClient.UpdateMany(ctx, filter, update, nil)
	if err != nil {
		return errors.WithMessage(err, "failed to update document management id by id")
	}
	if res.ModifiedCount == 0 {
		return errors.New("no lab results updated, id not found")
	}
	return nil
}

// UpdatePatientIdByDocumentManagementId sets (or unsets) the patientId for all lab-results that
// reference the specified DocumentManagement entry.
// If patientId is nil the field will be removed (unset) otherwise it will be updated.
func (r *LabResultRepo) UpdatePatientIdByDocumentManagementId(ctx *titan.Context, documentManagementId uuid.UUID, patientId *uuid.UUID) error {
	filter := bson.M{Field_DocumentManagementId: documentManagementId}

	var update bson.M
	if patientId == nil {
		update = bson.M{"$unset": bson.M{Field_PatientId: ""}}
	} else {
		update = bson.M{"$set": bson.M{Field_PatientId: *patientId}}
	}

	res, err := r.Repo.IDBClient.UpdateMany(ctx, filter, update, nil)
	if err != nil {
		return errors.WithMessage(err, "failed to update patient id by document management id")
	}
	if res.ModifiedCount == 0 {
		return errors.New("no lab results updated, document management id not found")
	}
	return nil
}

func (r *LabResultRepo) DeleteLabResult(ctx *titan.Context, documentManagementId, patientId uuid.UUID) error {
	filter := bson.M{
		Field_DocumentManagementId: documentManagementId,
		Field_PatientId:            patientId,
	}
	_, err := r.Repo.IDBClient.Delete(ctx, filter)
	return err
}

// DeleteByDocumentManagementIds
func (r *LabResultRepo) DeleteByDocumentManagementIds(ctx *titan.Context, documentManagementIds []uuid.UUID) error {
	filter := bson.M{Field_DocumentManagementId: bson.M{"$in": documentManagementIds}}
	_, err := r.Repo.IDBClient.Delete(ctx, filter)
	return err
}

// get by document management id
func (r *LabResultRepo) GetByDocumentManagementId(ctx *titan.Context, documentManagementId uuid.UUID) ([]*LabResultEntity, error) {
	filter := bson.M{
		Field_DocumentManagementId: documentManagementId,
	}
	return r.Repo.Find(ctx, filter, options.Find().SetSort(bson.M{repos.Field_CreatedAt: -1}))
}
