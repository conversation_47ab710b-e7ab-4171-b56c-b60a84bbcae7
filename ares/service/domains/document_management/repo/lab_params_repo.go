package repo

import (
	"emperror.dev/errors"
	"git.tutum.dev/medi/tutum/ares/pkg/repo"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type LabParamsRepo struct {
	repo.Repo[*LabParamEntity]
}

type LabParamEntity struct {
	repos.BaseEntity `bson:",inline"`
	LabParam         `bson:",inline"`
}

type LabParam struct {
	PatientId            uuid.UUID  `bson:"patientId"`
	Name                 string     `bson:"name"`
	Unit                 string     `bson:"unit"`
	Min                  string     `bson:"min"`
	Max                  string     `bson:"max"`
	DocumentManagementId *uuid.UUID `bson:"documentManagementId"`
}

var LabParamsRepoMod = submodule.Make[*LabParamsRepo](func() *LabParamsRepo {
	return &LabParamsRepo{
		repo.NewRepo[*LabParamEntity](mongodb_repo.NewMongoDbIDBClient("tutum_mvz_db", "lab_params", true)),
	}
})

func (r *LabParamsRepo) GetLabParams(ctx *titan.Context) ([]*LabParamEntity, error) {
	return r.Find(ctx, bson.M{})
}

func (r *LabParamsRepo) Create(ctx *titan.Context, model *LabParam) (*LabParamEntity, error) {
	return r.Repo.Create(ctx, &LabParamEntity{
		BaseEntity: repos.NewBaseEntity(ctx),
		LabParam:   *model,
	})
}

func (r *LabParamsRepo) CreateMany(ctx *titan.Context, models []LabParam) ([]*LabParamEntity, error) {
	entities := make([]*LabParamEntity, len(models))
	for i, model := range models {
		entities[i] = &LabParamEntity{
			BaseEntity: repos.NewBaseEntity(ctx),
			LabParam:   model,
		}
	}
	return r.Repo.CreateMany(ctx, entities)
}

func (r *LabParamsRepo) UpsertByLabParametersName(ctx *titan.Context, labParameter *LabParam) error {
	filter := bson.M{
		"name":                 labParameter.Name,
		"documentManagementId": labParameter.DocumentManagementId,
	}

	_, err := r.FindOneAndUpdate(
		ctx,
		filter,
		bson.M{
			"$set": bson.M{
				"unit":                labParameter.Unit,
				"min":                 labParameter.Min,
				"max":                 labParameter.Max,
				"patientId":           labParameter.PatientId,
				repos.Field_UpdatedAt: util.Now(ctx),
			},
			"$setOnInsert": bson.M{
				repos.Field_Id:        uuid.New(),
				repos.Field_CreatedAt: util.Now(ctx),
				// repos.Field_CreatedBy:        ctx.UserInfo().UserUUID(),
				// repos.Field_IsDeleted: false,
				// repos.Field_AssignedToBsnrId: ctx.UserInfo().GetBsnrId(),
				"name":                 labParameter.Name,
				"documentManagementId": labParameter.DocumentManagementId,
			},
		},
		options.FindOneAndUpdate().SetUpsert(true),
	)
	return err
}

func (r *LabParamsRepo) GetAllPartialLDTParams(ctx *titan.Context, includeDocumentManagementIds []uuid.UUID) ([]*LabParamEntity, error) {
	matchStage := bson.M{"$match": bson.M{Field_DocumentManagementId: bson.M{"$in": includeDocumentManagementIds}}}

	sortStage := bson.M{"$sort": bson.M{repos.Field_CreatedAt: -1}}

	groupStage := bson.M{"$group": bson.M{
		"_id": "$name",
		"doc": bson.M{"$first": "$$ROOT"},
	}}

	replaceRootStage := bson.M{"$replaceRoot": bson.M{"newRoot": "$doc"}}

	pipeline := []bson.M{
		matchStage,
		sortStage,
		groupStage,
		replaceRootStage,
	}

	return r.Aggregate(ctx, pipeline)
}

func (r *LabParamsRepo) AssignLabParamsByDocumentManagementId(
	ctx *titan.Context,
	documentManagementId uuid.UUID,
	patientId *uuid.UUID,
) error {
	filter := bson.M{
		"documentManagementId": documentManagementId,
	}

	res, err := r.IDBClient.UpdateMany(
		ctx,
		filter,
		bson.M{
			"$set": bson.M{
				"patientId": patientId,
			},
		},
		nil,
	)
	if err != nil {
		return errors.WithMessage(err, "failed to assign lab params by document management id")
	}
	if res.ModifiedCount == 0 {
		return errors.New("no lab params updated, document management id not found")
	}
	return err
}

func (r *LabParamsRepo) GetLabParamsByPatientId(ctx *titan.Context, patientId uuid.UUID) ([]*LabParamEntity, error) {
	// Build an aggregation pipeline that:
	// 1) matches the specified patientId
	// 2) sorts by createdAt descending so newest documents come first
	// 3) groups by parameter name and keeps the first (newest) document of each group
	// 4) flattens the result back into the original document shape

	matchStage := bson.M{"$match": bson.M{"patientId": patientId}}

	sortStage := bson.M{"$sort": bson.M{repos.Field_CreatedAt: -1}}

	groupStage := bson.M{"$group": bson.M{
		"_id": "$name",
		"doc": bson.M{"$first": "$$ROOT"},
	}}

	replaceRootStage := bson.M{"$replaceRoot": bson.M{"newRoot": "$doc"}}

	pipeline := []bson.M{
		matchStage,
		sortStage,
		groupStage,
		replaceRootStage,
	}

	return r.Aggregate(ctx, pipeline)
}

func (r *LabParamsRepo) DeleteByDocumentManagementIds(ctx *titan.Context, documentManagementIds []uuid.UUID) error {
	filter := bson.M{Field_DocumentManagementId: bson.M{"$in": documentManagementIds}}
	_, err := r.IDBClient.Delete(ctx, filter)
	return err
}

func (r *LabParamsRepo) DeleteLabParameter(ctx *titan.Context, documentManagementId, patientId uuid.UUID) error {
	filter := bson.M{
		"documentManagementId": documentManagementId,
		"patientId":            patientId,
	}
	_, err := r.IDBClient.Delete(ctx, filter)
	return err
}
