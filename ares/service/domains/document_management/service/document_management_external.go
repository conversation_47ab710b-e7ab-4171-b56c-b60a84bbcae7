package service

import (
	"fmt"
	"math"
	"path"
	"strings"
	"time"

	"git.tutum.dev/medi/tutum/ares/app/companion/dm_app"
	api "git.tutum.dev/medi/tutum/ares/app/mvz/api/document_management"

	document_management_common "git.tutum.dev/medi/tutum/ares/service/domains/api/document_management/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/document_setting_common"
	document_setting_repo "git.tutum.dev/medi/tutum/ares/service/domains/document_setting/repo"
	document_type_repo "git.tutum.dev/medi/tutum/ares/service/domains/document_type/repo"
	document_management_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/document_management"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.com/silenteer-oss/titan"
)

type SettingInfo struct {
	Host       *string
	Folder     string
	Username   *string
	Password   *string
	DeviceId   string
	SourceType dm_app.SourceTypeEnum
}

func (s *DocumentManagementService) ProcessDocumentUpload(ctx *titan.Context) error {
	if err := s.syncPreviousDocumentStatus(ctx); err != nil {
		return errors.WithMessage(err, "failed to sync previous document status")
	}

	// count document management settings
	total, err := s.documentSettingRepo.CountExternalDocumentSetting(ctx)
	if err != nil {
		return errors.WithMessage(err, "failed to count external document setting")
	}
	if total == 0 {
		// ctx.Logger().Info("no external document setting found")
		return nil
	}

	// create batch task
	totalBatch := int(math.Ceil(float64(total) / float64(batchSize)))
	p := function.NewPool[syncDocTask, *struct{}](function.WithContext(ctx), function.WithSize(maxConcurrency)).WithFunc(
		func(sdt syncDocTask) (*struct{}, error) {
			// get gdt import setting
			externalDocumentSetting, _, err := s.documentSettingRepo.GetExternalDocumentSettings(ctx, int64(sdt.batch), int64(sdt.batchSize))
			if err != nil {
				return nil, errors.WithMessage(err, "failed to get external document setting")
			}
			if len(externalDocumentSetting) == 0 {
				return nil, nil
			}

			for _, setting := range externalDocumentSetting {
				err := s.processImportExternalDocument(ctx.Clone(), setting)
				if err != nil {
					ctx.Clone().Logger().Error("failed to import external document", "dm_error", err)
				}
			}
			return nil, nil
		},
	)

	tasks := make([]syncDocTask, 0)
	for batch := 1; batch <= totalBatch; batch += 1 {
		tasks = append(tasks, syncDocTask{
			batch:     batch,
			batchSize: batchSize,
		})
	}

	// execute batch task
	if err := p.Process(tasks, nil); err != nil {
		return err
	}
	return nil
}

func (s *DocumentManagementService) processImportExternalDocument(ctx *titan.Context, setting document_setting_repo.DocumentSettingEntity) error {
	dmContext := cloneDmContext(ctx, setting.ExternalDocument.DeviceId.String())
	dmApp, err := s.companionService.GetDmApp(dmContext)
	if err != nil {
		return err
	}
	changedFileResult, err := s.getChangedFiles(ctx, dmApp, setting)
	changedFiles := changedFileResult.ChangedFiles

	if err != nil {
		return err
	}
	if len(changedFiles) == 0 {
		return nil
	}

	// Define batch size and max concurrency for processing files
	p := function.NewPool[[]string, *struct{}](function.WithContext(ctx), function.WithSize(maxConcurrency)).WithFunc(
		func(files []string) (*struct{}, error) {
			listFileUploadInfo := s.prepairInfoToUpload(ctx, files, setting)
			err := dmApp.UploadFiles(ctx, dm_app.UploadFilesRequest{
				ListFileUploadInfo: listFileUploadInfo,
				Folder:             setting.ExternalDocument.Browse,
				Host:               setting.ExternalDocument.Host,
				Username:           setting.ExternalDocument.Username,
				Password:           setting.ExternalDocument.Password,
				SourceType:         dm_app.SourceTypeEnum(setting.ExternalDocument.SourceType),
			})
			return nil, errors.WithMessage(err, "failed to upload files")
		},
	)

	tasks := make([][]string, 0)
	for i := 0; i < len(changedFiles); i += batchSize {
		end := i + batchSize
		if end > len(changedFiles) {
			end = len(changedFiles)
		}
		batchFiles := changedFiles[i:end]
		tasks = append(tasks, batchFiles)
	}

	if err := p.Process(tasks, nil); err != nil {
		return errors.WithMessage(err, "failed to process files")
	}

	return nil
}

type ChangedFilesResult struct {
	ChangedFiles   []string
	NetworkHomeDir string
}

func (s *DocumentManagementService) getChangedFiles(ctx *titan.Context, dmApp dm_app.DmApp, setting document_setting_repo.DocumentSettingEntity) (*ChangedFilesResult, error) {
	// get last state of document setting
	lastState, err := s.getLastState(ctx, *setting.Id)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get last state of document setting")
	}

	// get current state of document setting
	res, err := dmApp.GetFolderState(ctx, dm_app.GetFolderStateRequest{
		SettingId:  *setting.Id,
		Folder:     setting.ExternalDocument.Browse,
		Host:       setting.ExternalDocument.Host,
		Username:   setting.ExternalDocument.Username,
		Password:   setting.ExternalDocument.Password,
		SourceType: dm_app.SourceTypeEnum(setting.ExternalDocument.SourceType),
		Recursive:  setting.ExternalDocument.Recursive,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get current state of document setting")
	}
	currentState := res.FolderState
	if len(setting.ExternalDocument.FileFormats) != 0 {
		currentState = filterFilesByExtensions(currentState, setting.ExternalDocument.FileFormats)
	}

	// compare last state and current state to get new and modified files
	newFiles, modifiedFiles := compareFileStates(lastState, currentState)

	// update last state
	err = s.updateLastState(ctx, *setting.Id, currentState)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to update last state for document setting")
	}

	changedFiles := append(newFiles, modifiedFiles...)

	return &ChangedFilesResult{
		ChangedFiles: changedFiles,
	}, nil
}

func filterFilesByExtensions(currentState map[string]int64, s []string) map[string]int64 {
	filtered := make(map[string]int64)
	for file, modifyTime := range currentState {
		for _, ext := range s {
			if strings.HasSuffix(file, fmt.Sprintf(".%s", ext)) {
				filtered[file] = modifyTime
				break
			}
		}
	}
	return filtered
}

func (s *DocumentManagementService) getLastState(ctx *titan.Context, documentSettingId uuid.UUID) (map[string]int64, error) {
	entity, err := s.documentSettingStateRepo.GetDocumentSettingState(ctx, documentSettingId)
	if err != nil {
		return nil, err
	}
	if entity == nil {
		return nil, nil
	}
	return entity.ModtimeMap, nil
}

func (s *DocumentManagementService) updateLastState(ctx *titan.Context, documentSettingId uuid.UUID, currentState map[string]int64) error {
	_, err := s.documentSettingStateRepo.UpsertDocumentSettingState(ctx, document_setting_common.ExternalDocumentState{
		ExternalDocumentId: documentSettingId,
		ModtimeMap:         currentState,
	})
	return err
}

func (s *DocumentManagementService) prepairInfoToUpload(ctx *titan.Context, files []string, setting document_setting_repo.DocumentSettingEntity) []dm_app.FileUploadInfo {
	listFileUploadInfo := make([]dm_app.FileUploadInfo, 0)
	for _, file := range files {
		res, err := s.saveDocumentAndGetPresignUrlToUpload(ctx, file, setting)
		if err != nil {
			ctx.Logger().Error("failed to create document management", "error", err)
			continue
		}
		listFileUploadInfo = append(listFileUploadInfo, dm_app.FileUploadInfo{
			FilePath:     file,
			PresignedUrl: res.PresignedUrl,
		})
	}
	return listFileUploadInfo
}

func (s *DocumentManagementService) saveDocumentAndGetPresignUrlToUpload(ctx *titan.Context, file string, setting document_setting_repo.DocumentSettingEntity) (*api.CreateDocumentManagementResponse, error) {
	// create document management
	documentType, err := s.documentTypeService.GetOrCreateDocumentTypeByName(ctx, document_type_repo.ExternalDocument)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get or create document type")
	}

	documentName := file
	if setting.ExternalDocument.SourceType == document_setting_common.SourceTypeEnum_SMB {
		documentName = path.Join(setting.ExternalDocument.Browse, file)
	}
	entity, err := s.documentManagementRepo.Create(ctx, document_management_common.DocumentManagementModel{
		DocumentName:      documentName,
		DocumentType:      documentType,
		DocumentSettingId: setting.Id,
		// SenderId:     setting.Id,
		Status:       document_management_common.DocumentManagementStatus_New,
		ImportedDate: util.NowUnixMillis(ctx),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create document management")
	}

	// get presigned put object url
	fileName := entity.GetFileName(*ctx.UserInfo().CareProviderUUID())
	presignPutUrl, err := s.minioClient.PresignedPutObject(ctx, s.mvzAppConfig.MinioClientConfig.BucketDmCompanion, fileName, time.Minute*15)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get presigned put object url")
	}

	return &api.CreateDocumentManagementResponse{
		Id:                      *entity.GetId(),
		PresignedUrl:            presignPutUrl.String(),
		DocumentManagementModel: entity.DocumentManagementModel,
	}, nil
}

func (s *DocumentManagementService) syncPreviousDocumentStatus(ctx *titan.Context) error {
	// count all document not sysnc status
	count, err := s.documentManagementRepo.CountDocumentsNotSyncStatus(ctx)
	if err != nil {
		return errors.WithMessage(err, "failed to count documents not sync status")
	}

	totalBatch := int(math.Ceil(float64(count) / float64(batchSize)))
	if totalBatch == 0 {
		return nil
	}
	p := function.NewPool[syncDocTask, *struct{}](function.WithContext(ctx), function.WithSize(maxConcurrency)).WithFunc(
		func(sdt syncDocTask) (*struct{}, error) {
			documents, err := s.documentManagementRepo.GetDocumentsNotSyncStatus(ctx, sdt.batch, sdt.batchSize)
			if err != nil {
				return nil, errors.WithMessage(err, "failed to get documents not sync status")
			}
			if len(documents) == 0 {
				return nil, nil
			}
			// process each document and update status
			successIds := make([]uuid.UUID, 0)
			failedIds := make([]uuid.UUID, 0)

			for _, document := range documents {
				fileName := document.GetFileName(*ctx.UserInfo().CareProviderUUID())
				isExist, err := s.minioClient.ObjectExists(ctx, s.mvzAppConfig.MinioClientConfig.BucketDmCompanion, fileName)
				if err != nil {
					return nil, errors.WithMessage(err, "failed to check file exist")
				}
				if isExist {
					successIds = append(successIds, *document.Id)
				} else {
					failedIds = append(failedIds, *document.Id)
				}
			}

			if len(successIds) > 0 {
				err := s.documentManagementRepo.UpdateStatusByIds(ctx, successIds, document_management_common.DocumentManagementStatus_Completed)
				if err != nil {
					return nil, errors.WithMessage(err, "failed to update document status for success uploaded files")
				}
			}
			if len(failedIds) > 0 {
				err := s.documentManagementRepo.UpdateStatusByIds(ctx, failedIds, document_management_common.DocumentManagementStatus_Failed)
				if err != nil {
					return nil, errors.WithMessage(err, "failed to update document status for failed uploaded files")
				}
			}
			return nil, nil
		},
	)
	tasks := make([]syncDocTask, 0)
	for batch := 1; batch <= totalBatch; batch += 1 {
		tasks = append(tasks, syncDocTask{
			batch:     batch,
			batchSize: batchSize,
		})
	}
	if err := p.Process(tasks, nil); err != nil {
		return err
	}
	return nil
}

func compareFileStates(prevFolderState, currentFolderState map[string]int64) (newFiles, modifiedFiles []string) {
	// Detect added or modified files
	for file, modifyTime := range currentFolderState {
		if preModifyTime, exists := prevFolderState[file]; !exists {
			// File is new
			newFiles = append(newFiles, file)
		} else if preModifyTime != modifyTime {
			// File is modified
			modifiedFiles = append(modifiedFiles, file)
		}
	}
	return newFiles, modifiedFiles
}

func (s *DocumentManagementService) ReImportFailDocument(ctx *titan.Context, req api.ReImportFailDocumentRequest) error {
	// get document management
	entities, err := s.documentManagementRepo.GetByIds(ctx, req.Ids)
	if err != nil {
		return errors.WithMessage(err, "failed to get document management")
	}
	if len(entities) == 0 {
		return nil
	}
	// group by document setting id
	groupedEntities := make(map[uuid.UUID][]document_management_repo.DocumentManagementEntity)
	for _, entity := range entities {
		if entity.DocumentSettingId == nil {
			continue
		}
		groupedEntities[*entity.DocumentSettingId] = append(groupedEntities[*entity.DocumentSettingId], *entity)
	}

	// loop through each document setting
	for _, entities := range groupedEntities {
		setting, err := s.documentSettingService.GetDocumentSettingById(ctx, *entities[0].DocumentSettingId)
		if err != nil {
			ctx.Logger().Error("failed to get document setting", "error", err)
			continue
		}
		if setting == nil {
			continue
		}
		settingInfo := getSettingInfo(setting)
		if settingInfo == nil {
			continue
		}
		// get presigned put object url for each batch
		listFileUploadInfo := make([]dm_app.FileUploadInfo, 0)
		for _, entity := range entities {
			fileName := entity.GetFileName(*ctx.UserInfo().CareProviderUUID())
			presignPutUrl, err := s.minioClient.PresignedPutObject(ctx, s.mvzAppConfig.MinioClientConfig.BucketDmCompanion, fileName, time.Minute*15)
			if err != nil {
				ctx.Logger().Error("failed to get presigned put object url", "error", err)
				continue
			}
			// get filePath
			filePath := entity.DocumentName
			if settingInfo.SourceType == dm_app.SourceTypeEnum_SMB {
				filePath, _ = strings.CutPrefix(entity.DocumentName, fmt.Sprintf("%s/", settingInfo.Folder))
			}
			listFileUploadInfo = append(listFileUploadInfo, dm_app.FileUploadInfo{
				FilePath:     filePath,
				PresignedUrl: presignPutUrl.String(),
			})
		}
		// upload files
		dmContext := cloneDmContext(ctx, settingInfo.DeviceId)
		dmApp, err := s.companionService.GetDmApp(dmContext)
		if err != nil {
			ctx.Logger().Error("failed to get dm app", "error", err)
			continue
		}
		err = dmApp.UploadFiles(dmContext, dm_app.UploadFilesRequest{
			ListFileUploadInfo: listFileUploadInfo,
			Folder:             settingInfo.Folder,
			Host:               settingInfo.Host,
			Username:           settingInfo.Username,
			Password:           settingInfo.Password,
			SourceType:         settingInfo.SourceType,
		})
		if err != nil {
			ctx.Logger().Error("failed to upload files", "error", err)
		}
	}
	return nil
}

func getSettingInfo(setting *document_setting_repo.DocumentSettingEntity) *SettingInfo {
	if setting.DocumentSettingType == document_setting_common.DocumentSettingType_ExternalDocument {
		return &SettingInfo{
			Host:       setting.ExternalDocument.Host,
			Folder:     setting.ExternalDocument.Browse,
			Username:   setting.ExternalDocument.Username,
			Password:   setting.ExternalDocument.Password,
			DeviceId:   setting.ExternalDocument.DeviceId.String(),
			SourceType: dm_app.SourceTypeEnum(setting.ExternalDocument.SourceType),
		}
	} else if setting.DocumentSettingType == document_setting_common.DocumentSettingType_GdtImport {
		return &SettingInfo{
			Host:       setting.GdtImport.Host,
			Folder:     setting.GdtImport.Folder,
			Username:   setting.GdtImport.Username,
			Password:   setting.GdtImport.Password,
			DeviceId:   setting.GdtImport.DeviceId.String(),
			SourceType: dm_app.SourceTypeEnum(setting.GdtImport.SourceType),
		}
	}
	return nil
}
