<!DOCTYPE html
  PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns:th="urn:hl7-org:v3" xmlns:gc="urn:hl7-org:v3">

<head>
  <title>Title</title>
  <meta http-equiv=Content-Type content="text/html; charset=utf-8">
  <style type="text/css">
    body {
      counter-reset: initial;
      page-break-after: initial;
      font-family: "arial";
      margin: 4mm;
    }

    .table {
      text-align: left;
      border-collapse: collapse;
      padding: 0;
      margin: 0;
    }

    .col-lab-param {
      width: 30mm;
    }

    .col-norm {
      width: 32mm;
    }

    .col-day {
      width: 26mm;
    }


    .table-header {
      color: #4F6679;
      background-color: #F2F4F5;
      font-style: normal;
      font-weight: 600;
      font-size: 11px;
      /* font-size: 9px; */
      line-height: 16px;
      border-bottom-width: 0;
    }

    .table-header td {
      padding: 6px;
      border-top: 0.5px solid #DCE0E4;
      border-left: 0.5px solid #DCE0E4;
      border-right: 0.5px solid #DCE0E4;
    }

    .table-header .col-lab-param {
      padding-left: 16px;
      border-right-width: 0;
    }
    .table-header .col-norm {
      border-left-width: 0;
    }

    .table-header .col-day {
      color: #356BF5;
    }
    .table-header .cell-value-array {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .table-header .cell-total-recalls {
      color: white;
      background-color: #D84B4B;
      padding: 0 4px;
      border-radius: 8px;
    }


    .table-body {
      color: #13324B;
      font-style: normal;
      font-weight: normal;
      font-size: 13px;
      /* font-size: 10px; */
      line-height: 20px;
      /* line-height: 16px; */
    }
    .table-body .row-odd {
      background: #FAFBFD;
    }

    .table-body td {
      padding: 10px 8px;
      /* padding: 8px 6px; */
      word-break: break-all;
      vertical-align: top;
      border-bottom: 0.5px solid #DCE0E4;
      border-left: 0.5px solid #DCE0E4;
      border-right: 0.5px solid #DCE0E4;
    }

    .table-body .col-lab-param {
      padding-left: 16px;
      border-right-width: 0;
    }
    .table-body .col-norm {
      border-left-width: 0;
    }

    .table-body .cell-danger {
      font-weight: 600;
      color: #D84B4B;
    }

    .table-body .cell-value-icon {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    @page {
      /* size: A4 landscape; */

      @bottom-center {
        content: element(pageFooter);
      }

      margin: 0cm 0cm 1.7cm 0cm;
      padding: 0;
    }
    .page-header {
      padding-top: 32px;
      height: 30mm;
      color: lightgray;
      font-size: 64px;
    }

    /* #pageFooter {
        height: 1cm;
        width: 12cm;
        font-size: 8pt;
        position: running(pageFooter);
      }
      .footerNote {
        margin-left: 5mm;
        font-size: 8pt;
      }
      .line {
        line-height: 0px;
        margin: 8px 0px 8px 0px;
        width: 100%;
        border-top: 0.5px solid #000000;
        clear: both;
      }
      .pageBreak {
        page-break-after: always;
      } */
  </style>
</head>

<body>
  <svg style="display: none" version="2.0">
    <defs>
      <symbol id="circle-arrow-left" viewbox="0 -10 100 110">
        <path d="m 20 80 A 40 40 0 1 0 20 20" fill="none" stroke="#000" stroke-width="10" />
        <path d="M 10 0 v 40 h 40" fill="#000" />
      </symbol>
  
      <symbol id="alert" viewBox="0 0 16 16" fill="none">
        <path
          d="M1.33332 14.4C1.66666 14.6 1.99999 14.6667 2.33332 14.6667H13.6667C14.2 14.6667 14.6667 14.4667 15 14.0667C15.4 13.7333 15.6 13.2 15.6 12.6667C15.6 12.3333 15.5333 12 15.3333 11.6667L9.73333 2.26666C9.39999 1.8 8.99999 1.46666 8.46666 1.33333C7.93332 1.2 7.39999 1.26666 6.93332 1.53333C6.66666 1.66666 6.46666 1.93333 6.26666 2.2L0.599991 11.6667C0.066658 12.6 0.399991 13.8667 1.33332 14.4Z"
          fill="#D84B4B" />
        <path
          d="M7.99998 9.33334C7.59998 9.33334 7.33331 9.06668 7.33331 8.66668V6.00001C7.33331 5.60001 7.59998 5.33334 7.99998 5.33334C8.39998 5.33334 8.66665 5.60001 8.66665 6.00001V8.66668C8.66665 9.06668 8.39998 9.33334 7.99998 9.33334Z"
          fill="white" />
        <circle cx="8.03337" cy="11.3" r="0.699997" fill="white" />
      </symbol>
    </defs>
  </svg>
  <div class="page-header">Laborwerte</div>
  <table class="table">
    <thead>
      <tr class="table-header">
        {{range .Data.Header}}
        <td class="{{.ClassName}}">
          {{if eq .Type "value-only"}}
          {{.Value}}
          {{end}}
          {{if eq .Type "value-array"}}
          <div class="cell-value-array">
            {{ index .Value 0 }}
            <div class="cell-total-recalls">
              {{ index .Value 1 }}
            </div>
        
          </div>
        
          {{end}}
        </td>
        {{end}}
      </tr>
    </thead>
    <tbody class="table-body">
      {{$isEvenFunc := .IsEven}}
      {{range $iRow, $row := .Data.Rows}}
      {{$rowClass := "row-odd"}}
      {{if call $isEvenFunc $iRow }}
      {{$rowClass = "row-even"}}
      {{end}}
      <tr class="{{$rowClass}}">
        {{range $row.Cells}}
        <td class="{{.ClassName}}">
          {{if eq .Type "value-only"}}
          {{.Value}}
          {{end}}
          {{if eq .Type "value-icon"}}
          <div class="cell-value-icon">
            {{.Value}}
            <svg width="12" height="12" version="2.0">
              <use href="#alert" />
            </svg>
          </div>
          {{end}}
        </td>
        {{end}}
      </tr>
      {{end}}
    </tbody>
  </table>
  <div class="pageBreak"></div>
</body>

</html>