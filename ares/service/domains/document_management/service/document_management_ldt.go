package service

import (
	"fmt"
	"io"
	"runtime/debug"
	"strconv"
	"strings"
	"time"

	"git.tutum.dev/medi/tutum/ares/app/companion/dm_app"
	api "git.tutum.dev/medi/tutum/ares/app/mvz/api/document_management"
	"git.tutum.dev/medi/tutum/ares/pkg/html_parser"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/document_management/common"
	patient_profile_common "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	document_setting_state_repo "git.tutum.dev/medi/tutum/ares/service/domains/document_management/repo"
	document_setting_repo "git.tutum.dev/medi/tutum/ares/service/domains/document_setting/repo"
	document_type_repo "git.tutum.dev/medi/tutum/ares/service/domains/document_type/repo"
	document_management_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/document_management"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	timeline_common "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/ares/service/gdt"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
)

const (
	LabOrderTitle  = "Laboratory Order"
	LabResultTitle = "Laboratory Result"
)

type LDTStatus string
type LDTStatusCode string

const (
	LDTStatusCodeFinal         LDTStatusCode = "E"
	LDTStatusCodePartial       LDTStatusCode = "T"
	LDTStatusCodePreliminary   LDTStatusCode = "V"
	LDTStatusCodeArchived      LDTStatusCode = "A"
	LDTStatusCodeSupplementary LDTStatusCode = "N"
	LDTStatusCodeUnknown       LDTStatusCode = ""
)

const (
	LDTStatusFinal         LDTStatus = "Endbefund"
	LDTStatusPartial       LDTStatus = "Teilbefund"
	LDTStatusPreliminary   LDTStatus = "Vorläufiger Befund"
	LDTStatusArchived      LDTStatus = "Archiv-Befund"
	LDTStatusSupplementary LDTStatus = "Nachforderung"
	LDTStatusUnknown       LDTStatus = "Unknown"
)

var LDTStatusMap = map[LDTStatusCode]LDTStatus{
	LDTStatusCodeFinal:         LDTStatusFinal,
	LDTStatusCodePartial:       LDTStatusPartial,
	LDTStatusCodePreliminary:   LDTStatusPreliminary,
	LDTStatusCodeArchived:      LDTStatusArchived,
	LDTStatusCodeSupplementary: LDTStatusSupplementary,
	LDTStatusCodeUnknown:       LDTStatusUnknown,
}

// DecodeLDTOrder attempts to decode content as an LDT Order
func DecodeLDTOrder(reader io.Reader) (*gdt.LDTOrder, error) {
	var order gdt.LDTOrder
	err := gdt.DecodeLDTFile(reader, &order)
	if err != nil {
		return nil, err
	}

	// Validate that it's actually an order (8230 → 8218 → 8231)
	if len(order.Sentence8218s) == 0 {
		return nil, errors.New("no order data found in LDT file")
	}

	return &order, nil
}

// DecodeLDTResult attempts to decode content as an LDT Result
func DecodeLDTResult(reader io.Reader) (*gdt.LDTResult, error) {
	var result gdt.LDTResult
	err := gdt.DecodeLDTFile(reader, &result)
	if err != nil {
		return nil, err
	}

	// Validate that it's actually a result (8220 → 8202 → 8221)
	if len(result.Sentence8202s) == 0 {
		return nil, errors.New("no result data found in LDT file")
	}

	return &result, nil
}

func (s *DocumentManagementService) GetLabResultsPDF(ctx *titan.Context, request api.GetLabResultsRequest) (*api.GetLabResultsPDFResponse, error) {
	labResultsResp, err := s.GetLabResults(ctx, request)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	scale := 0.75
	isLandscape := false
	if len(labResultsResp.LabResults) > 4 {
		isLandscape = true
	}

	printMode := "portrait"
	if isLandscape {
		printMode = "landscape"
	}

	// convert html
	templateInputData := TransformPrintData(*labResultsResp)

	htmlParserRequest := html_parser.NewHtmlParser("") // init with empty body
	err = htmlParserRequest.ParseTemplateFS(htmlFileFS, htmlTemplateFile, TemplateData{
		Data:   templateInputData,
		IsEven: IsEven,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to parse template: %w", err)
	}

	// convert pdf
	responseData, err := GeneratePDFFile(
		s.html2PdfClient,
		htmlParserRequest.Body,
		scale, isLandscape)
	if err != nil {
		return nil, fmt.Errorf("failed to generate pdf: %w", err)
	}

	return &api.GetLabResultsPDFResponse{
		Pdf:  responseData,
		Mode: printMode,
	}, nil
}

func (s *DocumentManagementService) GetLabResults(ctx *titan.Context, request api.GetLabResultsRequest) (*api.GetLabResultsResponse, error) {
	var availableLabParametersResults []string

	fieldNameFilter := map[string]struct{}{}
	if len(request.FieldNames) > 0 {
		for _, n := range request.FieldNames {
			fieldNameFilter[n] = struct{}{}
		}
	}

	resultsFromRepo, err := s.labResultRepo.GetLabResultsByFilter(ctx, document_setting_state_repo.LabResultFilter{
		PatientId:  request.PatientId,
		FromDate:   request.FromDate,
		ToDate:     request.ToDate,
		FieldNames: request.FieldNames,
		Limit:      request.Results,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get lab results with filter")
	}

	labResultsResponse := make([]api.LabResultOverview, 0, len(resultsFromRepo))
	for _, labResult := range resultsFromRepo {
		filteredItems := make([]api.LabResultItem, 0)
		for _, lpr := range labResult.LabParameterResults {
			if len(fieldNameFilter) > 0 {
				if _, ok := fieldNameFilter[lpr.Name]; !ok {
					continue
				}
			}
			filteredItems = append(filteredItems, api.LabResultItem{
				Name:  lpr.Name,
				Value: lpr.Value,
				Icon:  lpr.ThresholdIndicatorIcon,
			})
		}
		if len(filteredItems) == 0 {
			continue
		}
		labResultsResponse = append(labResultsResponse, api.LabResultOverview{
			Date:       labResult.CreatedDate,
			LabOrderId: labResult.OrderId,
			Items:      filteredItems,
		})
		if request.IsOnlyPathologicalResults != nil && *request.IsOnlyPathologicalResults {
			for _, lri := range filteredItems {
				if lri.Icon != "" {
					availableLabParametersResults = append(availableLabParametersResults, lri.Name)
				}
			}
		} else {
			for _, lrItemFiltered := range filteredItems {
				if !slice.Contains(availableLabParametersResults, lrItemFiltered.Name) {
					availableLabParametersResults = append(availableLabParametersResults, lrItemFiltered.Name)
				}
			}
		}
	}

	if request.IsOnlyPathologicalResults != nil && *request.IsOnlyPathologicalResults {
		for _, lr := range labResultsResponse {
			filteredItems := slice.Filter(lr.Items, func(lri api.LabResultItem) bool {
				return lri.Icon != "" && slice.Contains(availableLabParametersResults, lri.Name)
			})
			lr.Items = filteredItems
		}
	}

	labParameters, err := s.labParamsRepo.GetLabParamsByPatientId(ctx, request.PatientId)
	if err != nil {
		return nil, fmt.Errorf("failed to get lab parameters: %w", err)
	}

	availableLabParameters := slice.Map(labParameters, func(lp *document_setting_state_repo.LabParamEntity) api.LabParameter {
		return api.LabParameter{
			Name: lp.Name,
			Min:  lp.Min,
			Max:  lp.Max,
			Unit: lp.Unit,
		}
	})

	labParametersResponse := slice.Filter(availableLabParameters, func(lp api.LabParameter) bool {
		return slice.Contains(availableLabParametersResults, lp.Name)
	})

	return &api.GetLabResultsResponse{
		LabParameters:          labParametersResponse,
		LabResults:             labResultsResponse,
		AvailableLabParameters: availableLabParameters,
	}, nil
}

// ProcessImportLdtDocuments processes all LDT import settings and handles document import
func (s *DocumentManagementService) ProcessImportLdtDocuments(ctx *titan.Context) error {
	// Get total count of LDT import settings
	total, err := s.documentSettingRepo.CountLdtImportSetting(ctx)
	if err != nil {
		return errors.WithMessage(err, "failed to count LDT import settings")
	}

	if total == 0 {
		return nil
	}

	ctx.Logger().Info("Processing LDT import documents", "total_settings", total)

	// Process LDT import settings in batches using similar logic to GDT
	err = s.processLdtBatchedImports(ctx, int(total))
	if err != nil {
		ctx.Logger().Error("Failed to process LDT batched imports", "error", err)
		return err
	}

	return s.socketNotifier.NotifyCareProviderDocumentManagementChange(ctx, &api.EventDocumentManagementChange{})
}

// processLdtBatchedImports handles the concurrent processing of LDT import settings in batches
func (s *DocumentManagementService) processLdtBatchedImports(ctx *titan.Context, total int) error {
	totalBatches := (total + batchSize - 1) / batchSize

	ctx.Logger().Debug("Starting LDT batch processing", "total_batches", totalBatches, "batch_size", batchSize, "max_concurrency", maxConcurrency)

	// Similar batching logic to GDT processing
	for batchNum := 1; batchNum <= totalBatches; batchNum++ {
		settings, err := s.getLdtImportSettings(ctx, batchNum, batchSize)
		if err != nil {
			return errors.WithMessage(err, "failed to get LDT import settings for batch")
		}

		for _, setting := range settings {
			s.processIndividualLdtImportSetting(ctx, setting)
		}
	}

	return nil
}

// getLdtImportSettings retrieves LDT import settings for a specific batch
func (s *DocumentManagementService) getLdtImportSettings(ctx *titan.Context, batch, batchSize int) ([]document_setting_repo.DocumentSettingEntity, error) {
	settings, _, err := s.documentSettingRepo.GetLdtImportSettings(
		ctx,
		int64(batch),
		int64(batchSize),
	)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get LDT import settings")
	}
	return settings, nil
}

// processIndividualLdtImportSetting processes a single LDT import setting
func (s *DocumentManagementService) processIndividualLdtImportSetting(ctx *titan.Context, setting document_setting_repo.DocumentSettingEntity) {
	// LDT import settings don't support specific filenames, so we always process multiple files
	err := s.importLdtDocumentWithMultipleFiles(ctx.Clone(), setting)
	if err != nil {
		ctx.Logger().Error("Failed to import LDT document with multiple files", "ldt_error", err, "setting_id", setting.Id)
	}
}

// importLdtDocumentWithMultipleFiles handles importing multiple LDT files from a directory
func (s *DocumentManagementService) importLdtDocumentWithMultipleFiles(ctx *titan.Context, setting document_setting_repo.DocumentSettingEntity) error {
	dmContext := cloneDmContext(ctx, setting.LdtImport.DeviceId.String())

	dmApp, err := s.companionService.GetDmApp(dmContext)
	if err != nil {
		return errors.WithMessage(err, "failed to get dm app")
	}

	err = dmApp.HandleGdtImportDocument(dmContext, dm_app.HandleDocumentRequest{
		Folder:            setting.LdtImport.Folder,
		Host:              setting.LdtImport.Host,
		Username:          setting.LdtImport.Username,
		Password:          setting.LdtImport.Password,
		SourceType:        dm_app.SourceTypeEnum(setting.LdtImport.SourceType),
		SettingId:         *setting.Id,
		CharacterEncoding: dm_app.CharacterEncoding(setting.LdtImport.CharacterEncoding),
	})
	return err
}

// parseLdtContent decodes LDT content and determines if it's an order or result
func parseLdtContent(ctx *titan.Context, gdtImportData *dm_app.GdtImportData) (order *gdt.LDTOrder, result *gdt.LDTResult, err error) {
	content := strings.TrimSpace(gdtImportData.Content)

	// First, try to decode as LDT Order (8230 → 8218 → 8231)
	if strings.HasPrefix(content, "01380008230") {
		order, err = gdt.DecodeLDTOrder(strings.NewReader(content))
		ctx.Logger().Info("debug", "order", order)
		if err == nil && order != nil {
			ctx.Logger().Info("Successfully decoded LDT as Order", "file", gdtImportData.FilePath)
			return order, nil, nil
		}
	}
	// If that fails, try to decode as LDT Result (8220 → 8202 → 8221)
	result, err = gdt.DecodeLDTResult(strings.NewReader(content))
	ctx.Logger().Info("debug", "result", result)
	if err == nil && result != nil {
		ctx.Logger().Info("Successfully decoded LDT as Result", "file", gdtImportData.FilePath)
		return nil, result, nil
	}

	return nil, nil, errors.WithMessage(err, "failed to decode LDT content as either order or result")
}

func (s *DocumentManagementService) processLdtImport(ctx *titan.Context, setting *document_setting_repo.DocumentSettingEntity, order *gdt.LDTOrder, result *gdt.LDTResult, gdtImportData *dm_app.GdtImportData) error {
	// Check if this LDT document has already been imported
	isExisted, err := s.repo.IsDocumentExisted(ctx, gdtImportData.FilePath, *setting.Id, 0)
	if err != nil {
		ctx.Logger().Error("Failed to check if LDT document exists", "error", err, "file", gdtImportData.FilePath)
		// Continue processing even if check fails to avoid blocking imports
	} else if isExisted {
		ctx.Logger().Info("LDT document already imported, skipping", "file", gdtImportData.FilePath, "setting_id", setting.Id)
		// Remove the file since it's already been processed
		if err := s.handleDocumentFileRemoval(ctx, setting, gdtImportData); err != nil {
			ctx.Logger().Error("Failed to remove already imported LDT document", "error", err)
		}
		return nil
	}

	var matchedPatient *patient.PatientProfile

	matchedPatients := make([]*patient.PatientProfile, 0)
	orderIds := make([]string, 0)
	ldtType := patient_encounter.LDT_ORDER
	timelineContents := make([]string, 0)
	labParams := make([][]document_setting_state_repo.LabParam, 0)

	// Scenario 1: LDT Order processing
	if order != nil {
		ctx.Logger().Info("Processing LDT Order", "file", gdtImportData.FilePath)
		for _, patientRecord := range order.Sentence8218s {
			// Match patient by insurance number (3105 or 3119)
			matchedPatient, err = s.matchPatientForLdtOrder(ctx, &patientRecord)
			if err != nil {
				ctx.Logger().Error("Failed to match patient for LDT order", "error", err)
			}

			// Generate timeline content for LDT order
			timelineContents = append(timelineContents, generateLdtOrderTimelineContent(&patientRecord))
			matchedPatients = append(matchedPatients, matchedPatient)
			orderIds = append(orderIds, patientRecord.RequestIdentifier)
		}
	}

	// Scenario 2: LDT Result processing
	if result != nil {
		ctx.Logger().Info("Processing LDT Result", "file", gdtImportData.FilePath)

		for _, patientRecord := range result.Sentence8202s {
			// Try to match to existing lab order first, then fall back to patient matching
			matchedPatient, err = s.matchPatientForLdtResult(ctx, &patientRecord)
			if err != nil {
				ctx.Logger().Error("Failed to match patient for LDT result", "error", err)
				continue
			}
			// Generate timeline content for LDT result
			timelineContents = append(timelineContents, generateLdtResultTimelineContent(&patientRecord))
			matchedPatients = append(matchedPatients, matchedPatient)
			orderIds = append(orderIds, patientRecord.RequestID)
			ldtType = patient_encounter.LDT_RESULT

			labParameterResults := make([]document_setting_state_repo.LabParameterResult, 0)
			params := make([]document_setting_state_repo.LabParam, 0)
			for _, test := range patientRecord.Tests {
				params = append(params, document_setting_state_repo.LabParam{
					Name: test.TestDescription,
					Unit: test.Unit,
					Min:  test.ReferenceLowerLimit,
					Max:  test.ReferenceUpperLimit,
				})
				labParameterResults = append(labParameterResults, document_setting_state_repo.LabParameterResult{
					Name:                   test.TestDescription,
					Value:                  test.ResultValue,
					Unit:                   test.Unit,
					Min:                    test.ReferenceLowerLimit,
					Max:                    test.ReferenceUpperLimit,
					ThresholdIndicatorIcon: test.ThresholdIndicator,
				})
			}
			labParams = append(labParams, params)
			patientId := uuid.Nil
			if matchedPatient != nil {
				patientId = *matchedPatient.Id
			}
			labResult := &document_setting_state_repo.LabResult{
				PatientId:            patientId,
				OrderId:              patientRecord.RequestID,
				LabParameterResults:  labParameterResults,
				CreatedDate:          parseLdtDate(patientRecord.ReportDate),
				DocumentManagementId: nil,
			}
			_, err = s.labResultRepo.Create(ctx, labResult)
			if err != nil {
				return fmt.Errorf("failed to create lab result: %w", err)
			}
		}
	}

	ctx.Logger().Info("debug", "matchedPatients", matchedPatients, "orderIds", orderIds, "timelineContents", timelineContents)
	for i := range matchedPatients {
		// Create document entity
		entity, err := s.createLdtDocumentEntity(ctx, setting, matchedPatients[i], gdtImportData.FilePath)
		if err != nil {
			return err
		}
		if i < len(labParams) {
			for _, param := range labParams[i] {
				if matchedPatients[i] != nil {
					param.PatientId = *matchedPatients[i].Id
				}
				param.DocumentManagementId = entity.Id
				err := s.labParamsRepo.UpsertByLabParametersName(ctx, &param)
				if err != nil {
					return fmt.Errorf("failed to upsert lab params: %w", err)
				}
			}
		}

		// Upload content to Minio
		if err := s.uploadDocumentToMinio(ctx, entity, gdtImportData.Content); err != nil {
			return s.handleUploadFailure(ctx, entity, err)
		}

		if err := s.handleDocumentFileRemoval(ctx, setting, gdtImportData); err != nil {
			ctx.Logger().Error("failed to remove ldt document", "error", err)
		}

		s.handleLdtImportSuccessAsync(ctx, entity, matchedPatients[i], setting, orderIds[i], ldtType, timelineContents[i])
	}

	return nil
}

// matchPatientForLdtOrder implements Scenario 1 patient matching logic
func (s *DocumentManagementService) matchPatientForLdtOrder(ctx *titan.Context, patientRecord *gdt.Sentence8218) (*patient.PatientProfile, error) {
	// Strategy 1: Try insurance number matching first (most reliable)
	if patient, err := s.matchByInsuranceNumber(ctx, patientRecord); err == nil && patient != nil {
		return patient, nil
	}

	// Strategy 2: Fallback to name + DOB matching
	if patient, err := s.matchByLdtNameAndDOB(ctx, patientRecord); err == nil && patient != nil {
		return patient, nil
	}

	return nil, fmt.Errorf("no patient found matching LDT order data for patient %s %s",
		patientRecord.FirstName, patientRecord.LastName)
}

// matchByInsuranceNumber attempts to match patient by insurance number (3105 or 3119)
func (s *DocumentManagementService) matchByInsuranceNumber(ctx *titan.Context, patientRecord *gdt.Sentence8218) (*patient.PatientProfile, error) {
	insuranceNumber := extractInsuranceNumber(patientRecord)
	ctx.Logger().Info("Attempting to match patient by insurance number", "insurance_number", insuranceNumber)

	if insuranceNumber == "" {
		return nil, nil
	}

	patients, err := s.patientProfileRepo.GetPatientsProfileByInsuranceNumber(ctx, insuranceNumber)
	if err != nil {
		ctx.Logger().Error("Error searching patients by insurance number", "error", err, "insurance_number", insuranceNumber)
		return nil, nil
	}

	return handleInsuranceNumberSearchResults(ctx, patients, patientRecord, insuranceNumber)
}

// matchByLdtNameAndDOB attempts to match patient by first name, last name, and date of birth
func (s *DocumentManagementService) matchByLdtNameAndDOB(ctx *titan.Context, patientRecord *gdt.Sentence8218) (*patient.PatientProfile, error) {
	dob := parseLdtDateOfBirthString(patientRecord.DateOfBirth)
	if dob == nil {
		return nil, fmt.Errorf("invalid date of birth format in LDT order: %s", patientRecord.DateOfBirth)
	}

	ctx.Logger().Info("Attempting patient match by name and DOB",
		"first_name", patientRecord.FirstName,
		"last_name", patientRecord.LastName,
		"dob", patientRecord.DateOfBirth)

	patients, err := s.patientProfileRepo.GetPatientByNameAndDOB(ctx, patientRecord.FirstName, patientRecord.LastName, *dob)
	if err != nil {
		ctx.Logger().Error("Error searching patients by name and DOB", "error", err)
		return nil, fmt.Errorf("failed to search patients by name and DOB: %w", err)
	}

	switch len(patients) {
	case 0:
		ctx.Logger().Info("No patients found matching name and DOB")
		return nil, nil
	case 1:
		ctx.Logger().Info("Found unique patient by name and DOB", "patient_id", patients[0].Id)
		return &patients[0], nil
	default:
		ctx.Logger().Info("Multiple patients found with same name and DOB", "patient_id", patients[0].Id)
		return nil, nil
	}
}

// matchPatientForLdtResult implements Scenario 2 patient matching logic
func (s *DocumentManagementService) matchPatientForLdtResult(ctx *titan.Context, result *gdt.Sentence8202) (*patient.PatientProfile, error) {
	// Step 1: Try to find existing lab order by order number (8310)
	orderId := result.RequestID
	if orderId != "" {
		timeline, err := s.timelineService.GetLDTEntryByOrderId(ctx, orderId)
		if err != nil {
			ctx.Logger().Error("Failed to get LDT entry by order id", "error", err, "order_id", orderId)
		}
		if timeline != nil {
			return s.patientProfileRepo.GetPatientProfileById(ctx, timeline.PatientId)
		}
	}
	ctx.Logger().Warn("No LDT entry found by order id", "order_id", orderId)

	// NOTE: how to get patient name in result
	// Step 2: Try to match by DOB if available - this is less reliable but may work
	// in cases where there's only one patient with that exact DOB
	// if resultRecord.DateOfBirth != "" {
	// 	dob := s.parseLdtDateOfBirthString(resultRecord.DateOfBirth)
	// 	if dob != nil {
	// 		patients, err := s.patientProfileRepo.GetPatientByNameAndDOB(ctx, "", "", *dob)
	// 		if err != nil {
	// 			return nil, fmt.Errorf("failed to get patient profile by name and DOB: %w", err)
	// 		}
	// 		if len(patients) == 1 {
	// 			ctx.Logger().Info("Found unique patient by date of birth only", "patient_id", patients[0].Id)
	// 			return &patients[0], nil
	// 		}
	// 	}
	// }

	// For now, return nil as LDT results have insufficient patient identification info
	ctx.Logger().Error("Cannot reliably match LDT result to patient with available information")
	return nil, nil
}

// Enhanced handleLdtImportSuccessAsync with timeline content
func (s *DocumentManagementService) handleLdtImportSuccessAsync(
	ctx *titan.Context,
	entity *document_management_repo.DocumentManagementEntity,
	patient *patient.PatientProfile,
	setting *document_setting_repo.DocumentSettingEntity,
	orderId string,
	ldtType patient_encounter.EncounterLDTType,
	timelineContent string,
) {
	ctxClone := ctx.Clone()
	go func() {
		defer func() {
			if err := recover(); err != nil {
				errMsg := fmt.Sprintf("panic: %v\n\n%s", err, string(debug.Stack()))
				ctxClone.Logger().Error(errMsg)
			}
		}()

		s.completeLdtImportProcess(ctxClone, entity, patient, setting, orderId, ldtType, timelineContent)
	}()
}

// Enhanced completeLdtImportProcess
func (s *DocumentManagementService) completeLdtImportProcess(
	ctx *titan.Context,
	entity *document_management_repo.DocumentManagementEntity,
	patient *patient.PatientProfile,
	setting *document_setting_repo.DocumentSettingEntity,
	orderId string,
	ldtType patient_encounter.EncounterLDTType,
	timelineContent string,
) {
	// Update document management status to completed
	_, err := s.repo.UpdateStatusById(ctx, *entity.Id, common.DocumentManagementStatus_Completed)
	if err != nil {
		ctx.Logger().Error("failed to update LDT document management status", "error", err)
		return
	}

	// Create LDT timeline entry if patient was found

	if err := s.createLdtTimelineEntry(ctx, entity, patient, setting, timelineContent, orderId, ldtType); err != nil {
		ctx.Logger().Error("failed to create LDT import timeline", "error", err)
		return
	}

	// update lab result document management id
	if err := s.labResultRepo.UpdateDocumentManagementIdByOrderId(ctx, orderId, *entity.Id); err != nil {
		ctx.Logger().Error("failed to update lab result document management id", "error", err)
		return
	}

	// Notify success for LDT import
	var patientName string
	if patient != nil && patient.PatientInfo != nil {
		patientName = patient.PatientInfo.PersonalInfo.GetFullName()
	}

	_ = s.socketNotifier.NotifyCareProviderGdtImportResult(ctx, &api.EventGdtImportResult{
		Result:      true,
		PatientName: patientName,
		Type:        common.DocumentNotificationType_LDT,
	})
}

// Enhanced createLdtTimelineEntry with custom content
func (s *DocumentManagementService) createLdtTimelineEntry(
	ctx *titan.Context,
	entity *document_management_repo.DocumentManagementEntity,
	patient *patient.PatientProfile,
	setting *document_setting_repo.DocumentSettingEntity,
	timelineContent string,
	orderId string,
	ldtType patient_encounter.EncounterLDTType,
) error {
	bucketName := s.mvzAppConfig.MinioClientConfig.BucketDmCompanion
	minioPath := entity.GetFileName(*ctx.UserInfo().CareProviderUUID())

	patientId := &uuid.Nil
	if patient != nil {
		patientId = patient.Id
	}

	timelineEntity := timeline_repo.TimelineEntity[patient_encounter.EncounterLDT]{
		Id:        util.NewUUID(),
		PatientId: *patientId,
		Payload: patient_encounter.EncounterLDT{
			FilePath:             minioPath,
			FileName:             entity.DocumentName,
			BucketName:           bucketName,
			Note:                 timelineContent, // Use the custom generated content
			LdtImportSettingId:   setting.Id,
			LdtImportSettingName: setting.LdtImport.Name,
			DocumentManagementId: entity.Id,
			Command:              setting.LdtImport.TimelineType,
			Type:                 ldtType,
			OrderId:              orderId,
		},
	}

	// Create timeline entry using any timeline service since there's no specific LDT service
	anyTimelineEntity := timeline_repo.TimelineEntity[any]{
		Id:        timelineEntity.Id,
		PatientId: timelineEntity.PatientId,
		Payload:   timelineEntity.Payload,
	}

	_, err := s.timelineServiceAny.Create(ctx, anyTimelineEntity)
	return err
}

// createLdtDocumentEntity creates a document management entity for LDT files
func (s *DocumentManagementService) createLdtDocumentEntity(ctx *titan.Context, setting *document_setting_repo.DocumentSettingEntity, patient *patient.PatientProfile, filePath string) (*document_management_repo.DocumentManagementEntity, error) {
	documentType, err := s.documentTypeService.GetOrCreateDocumentTypeByName(ctx, document_type_repo.LdtImport)
	if err != nil {
		return nil, fmt.Errorf("failed to get or create LDT document type: %w", err)
	}

	var patientId *uuid.UUID
	if patient != nil {
		patientId = patient.Id
	}

	entity, err := s.repo.Create(ctx, common.DocumentManagementModel{
		DocumentName:      filePath,
		DocumentType:      documentType,
		Status:            common.DocumentManagementStatus_New,
		PatientId:         patientId,
		DocumentSettingId: setting.Id,
		ImportedDate:      util.NowUnixMillis(ctx),
		GdtImportModTime:  0, // Set to 0 for LDT imports to enable duplicate detection
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create LDT document management: %w", err)
	}

	return entity, nil
}

// generateLdtOrderTimelineContent creates timeline content for LDT orders (Scenario 1)
func generateLdtOrderTimelineContent(patientRecord *gdt.Sentence8218) string {
	contentBuilder := strings.Builder{}

	var labParams []string

	contentBuilder.WriteString(LabOrderTitle + "\n")

	// Add lab parameters (FK 8411 or 8434)
	for _, test := range patientRecord.Tests {
		if test.TestName != "" { // FK 8411
			labParams = append(labParams, test.TestName)
		}
	}
	for _, req := range patientRecord.Requirements {
		if req.Requirement != "" { // FK 8434
			labParams = append(labParams, req.Requirement)
		}
	}
	if len(labParams) > 0 {
		contentBuilder.WriteString(strings.Join(labParams, ", ") + "\n")
	}
	return contentBuilder.String()
}

// generateLdtResultTimelineContent creates timeline content for LDT results (Scenario 2)
func generateLdtResultTimelineContent(result *gdt.Sentence8202) string {
	contentBuilder := strings.Builder{}

	contentBuilder.WriteString(LabResultTitle + "\n")

	// Line 1: Results with Status (FK 8401) are available for Lab tests from (FK 8302)
	status := LDTStatusMap[LDTStatusCode(result.TypeOfFindings)] // FK 8401

	reportDate := result.ReportDate // FK 8302
	if reportDate == "" {
		reportDate = "unknown date"
	} else if len(reportDate) == 8 {
		// Convert DDMMYYYY to DD.MM.YYYY
		year := reportDate[:4]
		month := reportDate[4:6]
		day := reportDate[6:8]
		reportDate = fmt.Sprintf("%s.%s.%s", day, month, year)
	}

	contentBuilder.WriteString(fmt.Sprintf("Results with Status %s are available for Lab tests from %s", status, reportDate))

	return contentBuilder.String()
}

func parseLdtDate(dateStr string) int64 {
	date, err := time.Parse("02012006", dateStr)
	if err != nil {
		return 0
	}
	return date.UnixMilli()
}

// parseLdtDateOfBirthString converts LDT date string (YYYYMMDD) to DateOfBirth struct
func parseLdtDateOfBirthString(dateStr string) *patient_profile_common.DateOfBirth {
	if len(dateStr) != 8 {
		return nil
	}

	year, err := strconv.ParseInt(dateStr[:4], 10, 32)
	if err != nil {
		return nil
	}

	month, err := strconv.ParseInt(dateStr[4:6], 10, 32)
	if err != nil {
		return nil
	}

	day, err := strconv.ParseInt(dateStr[6:8], 10, 32)
	if err != nil {
		return nil
	}

	return &patient_profile_common.DateOfBirth{
		Year:  util.NewPointer(int32(year)),
		Month: util.NewPointer(int32(month)),
		Date:  util.NewPointer(int32(day)),
	}
}

// compareDateOfBirth compares two DateOfBirth objects
func compareDateOfBirth(dob1, dob2 *patient_profile_common.DateOfBirth) bool {
	if dob1 == nil || dob2 == nil {
		return false
	}
	return util.GetPointerValue(dob1.Date) == util.GetPointerValue(dob2.Date) &&
		util.GetPointerValue(dob1.Month) == util.GetPointerValue(dob2.Month) &&
		util.GetPointerValue(dob1.Year) == util.GetPointerValue(dob2.Year)
}

// extractInsuranceNumber gets insurance number from patient record (3105 or 3119)
func extractInsuranceNumber(patientRecord *gdt.Sentence8218) string {
	if patientRecord.InsuranceNumber != "" {
		return patientRecord.InsuranceNumber // Field 3105
	}
	if patientRecord.InsuredPersonID != "" {
		return patientRecord.InsuredPersonID // Field 3119
	}
	return ""
}

// isPatientMatchByNameAndDOB checks if a patient matches the given name and DOB criteria
func isPatientMatchByNameAndDOB(patient patient.PatientProfile, firstName, lastName string, dob *patient_profile_common.DateOfBirth) bool {
	if patient.PatientInfo == nil {
		return false
	}

	personalInfo := &patient.PatientInfo.PersonalInfo
	return strings.EqualFold(personalInfo.FirstName, firstName) &&
		strings.EqualFold(personalInfo.LastName, lastName) &&
		compareDateOfBirth(&personalInfo.DateOfBirth, dob)
}

// refineInsuranceMatchWithNameAndDOB narrows down multiple insurance matches using name and DOB
func refineInsuranceMatchWithNameAndDOB(ctx *titan.Context, candidates []patient.PatientProfile, patientRecord *gdt.Sentence8218) (*patient.PatientProfile, error) {
	dob := parseLdtDateOfBirthString(patientRecord.DateOfBirth)
	if dob == nil {
		return nil, fmt.Errorf("invalid date of birth format in LDT order: %s", patientRecord.DateOfBirth)
	}

	// Filter candidates by exact name and DOB match
	var matches []patient.PatientProfile
	for _, candidate := range candidates {
		if isPatientMatchByNameAndDOB(candidate, patientRecord.FirstName, patientRecord.LastName, dob) {
			matches = append(matches, candidate)
		}
	}

	switch len(matches) {
	case 0:
		return nil, fmt.Errorf("no patients among insurance matches have matching name and DOB")
	case 1:
		ctx.Logger().Info("Refined to unique patient using name+DOB", "patient_id", matches[0].Id)
		return &matches[0], nil
	default:
		return nil, fmt.Errorf("multiple patients found even after name+DOB refinement")
	}
}

// handleInsuranceNumberSearchResults processes the results of insurance number search
func handleInsuranceNumberSearchResults(ctx *titan.Context, patients []patient.PatientProfile, patientRecord *gdt.Sentence8218, insuranceNumber string) (*patient.PatientProfile, error) {
	switch len(patients) {
	case 0:
		ctx.Logger().Info("No patients found by insurance number", "insurance_number", insuranceNumber)
		return nil, fmt.Errorf("no patients found with insurance number %s", insuranceNumber)

	case 1:
		ctx.Logger().Info("Found unique patient by insurance number", "insurance_number", insuranceNumber, "patient_id", patients[0].Id)
		return &patients[0], nil

	default:
		ctx.Logger().Info("Multiple patients found by insurance number, refining with name+DOB",
			"insurance_number", insuranceNumber, "count", len(patients))
		return refineInsuranceMatchWithNameAndDOB(ctx, patients, patientRecord)
	}
}

func (s *DocumentManagementService) handleAssignPatientToLdtTimeline(ctx *titan.Context,
	documentManagementId uuid.UUID,
	currentPatientId,
	newPatientId *uuid.UUID,
) error {
	// If the requested mapping is identical to the current one, nothing to do.
	if util.GetPointerValue(currentPatientId) == util.GetPointerValue(newPatientId) {
		return nil
	}

	// Locate the corresponding LDT timeline entry.
	filter := bson.M{
		timeline_repo.Field_IsDeleted:          false,
		timeline_repo.Field_timelineEntityType: timeline_common.TimelineEntityType_LDT,
		"payload.documentmanagementid":         documentManagementId,
	}

	timelines, err := s.timelineServiceAny.Find(ctx, filter)
	if err != nil {
		return errors.WithMessage(err, "failed to get ldt timeline")
	}
	if len(timelines) == 0 {
		return nil // no timeline found – nothing to update
	}

	timeline := timelines[0]

	// Scenario A: timeline currently has no patient – simply update it.
	if currentPatientId == nil {
		timeline.PatientId = util.GetPointerValue(newPatientId)
		if _, err := s.timelineServiceAny.Edit(ctx, timeline); err != nil {
			return fmt.Errorf("failed to edit ldt timeline: %w", err)
		}

		err = s.labParamsRepo.AssignLabParamsByDocumentManagementId(ctx, documentManagementId, newPatientId)
		if err != nil {
			return fmt.Errorf("failed to assign lab params: %w", err)
		}
		if ldt, ok := convertToEncounterLDT(timeline.Payload); ok && ldt.Type == patient_encounter.LDT_RESULT {
			return s.labResultRepo.UpdatePatientIdByDocumentManagementId(ctx, documentManagementId, newPatientId)
		}
		return nil
	}

	// Scenario B: re-assign timeline to another patient – remove old entry and create a new one.
	if err := s.timelineServiceAny.Remove(ctx, *timeline.Id, true); err != nil {
		return fmt.Errorf("failed to remove ldt timeline: %w", err)
	}

	// Ensure payload has the correct concrete type to avoid BSON marshaling issues.
	newPayload := timeline.Payload
	if ldtPayload, ok := convertToEncounterLDT(timeline.Payload); ok {
		newPayload = ldtPayload
	}

	newEntity := timeline_repo.TimelineEntity[any]{
		Id:        util.NewUUID(),
		Payload:   newPayload,
		PatientId: util.GetPointerValue(newPatientId),
		Type:      timeline_common.TimelineEntityType_LDT,
	}

	if _, err := s.timelineServiceAny.Create(ctx, newEntity); err != nil {
		return errors.WithMessage(err, "failed to create ldt timeline")
	}

	err = s.labParamsRepo.AssignLabParamsByDocumentManagementId(ctx, documentManagementId, newPatientId)
	if err != nil {
		return fmt.Errorf("failed to assign lab params: %w", err)
	}

	return s.labResultRepo.UpdatePatientIdByDocumentManagementId(ctx, documentManagementId, newPatientId)
}

// convertToEncounterLDT tries to transform any payload representation (struct, primitive.D, etc.)
// into a concrete patient_encounter.EncounterLDT value.
func convertToEncounterLDT(payload any) (patient_encounter.EncounterLDT, bool) {
	data, err := bson.MarshalWithRegistry(mongodb_repo.MongoRegistry, payload)
	if err != nil {
		return patient_encounter.EncounterLDT{}, false
	}
	var result patient_encounter.EncounterLDT
	err = bson.UnmarshalWithRegistry(mongodb_repo.MongoRegistry, data, &result)
	if err != nil {
		return patient_encounter.EncounterLDT{}, false
	}
	return result, true
}
