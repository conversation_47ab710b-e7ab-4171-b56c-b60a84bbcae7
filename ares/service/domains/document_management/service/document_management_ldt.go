package service

import (
	"fmt"
	"io"
	"path"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"git.tutum.dev/medi/tutum/ares/app/companion/dm_app"
	api "git.tutum.dev/medi/tutum/ares/app/mvz/api/document_management"
	"git.tutum.dev/medi/tutum/ares/pkg/html_parser"
	"git.tutum.dev/medi/tutum/ares/pkg/minio"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/document_management/common"
	patient_profile_common "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	document_setting_state_repo "git.tutum.dev/medi/tutum/ares/service/domains/document_management/repo"
	document_setting_repo "git.tutum.dev/medi/tutum/ares/service/domains/document_setting/repo"
	document_type_repo "git.tutum.dev/medi/tutum/ares/service/domains/document_type/repo"
	document_management_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/document_management"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	timeline_common "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/timeline_service"
	"git.tutum.dev/medi/tutum/ares/service/gdt"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
)

// DecodeLDTOrder attempts to decode content as an LDT Order
func DecodeLDTOrder(reader io.Reader) (*gdt.LDTOrder, error) {
	var order gdt.LDTOrder
	err := gdt.DecodeLDTFile(reader, &order)
	if err != nil {
		return nil, err
	}

	// Validate that it's actually an order (8230 → 8218 → 8231)
	if len(order.Sentence8218s) == 0 {
		return nil, errors.New("no order data found in LDT file")
	}

	return &order, nil
}

// DecodeLDTResult attempts to decode content as an LDT Result
func DecodeLDTResult(reader io.Reader) (*gdt.LDTResult, error) {
	var result gdt.LDTResult
	err := gdt.DecodeLDTFile(reader, &result)
	if err != nil {
		return nil, err
	}

	// Validate that it's actually a result (8220 → 8202 → 8221)
	if len(result.Sentence8202s) == 0 {
		return nil, errors.New("no result data found in LDT file")
	}

	return &result, nil
}

func (s *DocumentManagementService) GetLabResultsPDF(ctx *titan.Context, request api.GetLabResultsRequest) (*api.GetLabResultsPDFResponse, error) {
	labResultsResp, err := s.GetLabResults(ctx, request)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	sort.Slice(labResultsResp.LabParameters, func(i, j int) bool {
		return labResultsResp.LabParameters[i].Name < labResultsResp.LabParameters[j].Name
	})

	scale := 0.75
	isLandscape := false
	if len(labResultsResp.LabResults) > 4 {
		isLandscape = true
	}

	printMode := api.PORTRAIT
	if isLandscape {
		printMode = api.LANDSCAPE
	}

	// convert html
	templateInputData := TransformPrintData(*labResultsResp)

	htmlParserRequest := html_parser.NewHtmlParser("") // init with empty body
	err = htmlParserRequest.ParseTemplateFS(htmlFileFS, htmlTemplateFile, TemplateData{
		Data:   templateInputData,
		IsEven: IsEven,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to parse template: %w", err)
	}

	// convert pdf
	responseData, err := GeneratePDFFile(
		s.html2PdfClient,
		htmlParserRequest.Body,
		scale, isLandscape)
	if err != nil {
		return nil, fmt.Errorf("failed to generate pdf: %w", err)
	}

	return &api.GetLabResultsPDFResponse{
		Pdf:  responseData,
		Mode: printMode,
	}, nil
}

func (s *DocumentManagementService) GetLabResults(ctx *titan.Context, request api.GetLabResultsRequest) (*api.GetLabResultsResponse, error) {
	var availableLabParametersResults []string

	fieldNameFilter := map[string]struct{}{}
	if len(request.FieldNames) > 0 {
		for _, n := range request.FieldNames {
			fieldNameFilter[n] = struct{}{}
		}
	}

	resultsFromRepo, err := s.labResultRepo.GetLabResultsByFilter(ctx, document_setting_state_repo.LabResultFilter{
		PatientId:  request.PatientId,
		FromDate:   request.FromDate,
		ToDate:     request.ToDate,
		FieldNames: request.FieldNames,
		Limit:      request.Results,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get lab results with filter")
	}

	labResultsResponse := make([]api.LabResultOverview, 0, len(resultsFromRepo))
	for _, labResult := range resultsFromRepo {
		filteredItems := make([]api.LabResultItem, 0)
		for _, lpr := range labResult.LabParameterResults {
			if len(fieldNameFilter) > 0 {
				if _, ok := fieldNameFilter[lpr.Name]; !ok {
					continue
				}
			}
			filteredItems = append(filteredItems, api.LabResultItem{
				Name:           lpr.Name,
				Value:          lpr.Value,
				Icon:           lpr.ThresholdIndicatorIcon,
				TestNote:       lpr.TestNote,
				TestResultText: lpr.TestResultText,
			})
		}
		if len(filteredItems) == 0 {
			continue
		}
		labResultsResponse = append(labResultsResponse, api.LabResultOverview{
			Date:       labResult.CreatedDate,
			LabOrderId: labResult.OrderId,
			Items:      filteredItems,
		})
		if request.IsOnlyPathologicalResults != nil && *request.IsOnlyPathologicalResults {
			for _, lri := range filteredItems {
				if lri.Icon != "" {
					availableLabParametersResults = append(availableLabParametersResults, lri.Name)
				}
			}
		} else {
			for _, lrItemFiltered := range filteredItems {
				if !slice.Contains(availableLabParametersResults, lrItemFiltered.Name) {
					availableLabParametersResults = append(availableLabParametersResults, lrItemFiltered.Name)
				}
			}
		}
	}

	if request.IsOnlyPathologicalResults != nil && *request.IsOnlyPathologicalResults {
		for _, lr := range labResultsResponse {
			filteredItems := slice.Filter(lr.Items, func(lri api.LabResultItem) bool {
				return lri.Icon != "" && slice.Contains(availableLabParametersResults, lri.Name)
			})
			lr.Items = filteredItems
		}
	}

	labParameters, err := s.labParamsRepo.GetLabParamsByPatientId(ctx, request.PatientId)
	if err != nil {
		return nil, fmt.Errorf("failed to get lab parameters: %w", err)
	}

	availableLabParameters := slice.Map(labParameters, func(lp *document_setting_state_repo.LabParamEntity) api.LabParameter {
		return api.LabParameter{
			Name: lp.Name,
			Min:  lp.Min,
			Max:  lp.Max,
			Unit: lp.Unit,
		}
	})

	labParametersResponse := slice.Filter(availableLabParameters, func(lp api.LabParameter) bool {
		return slice.Contains(availableLabParametersResults, lp.Name)
	})

	return &api.GetLabResultsResponse{
		LabParameters:          labParametersResponse,
		LabResults:             labResultsResponse,
		AvailableLabParameters: availableLabParameters,
	}, nil
}

// ProcessImportLdtDocuments processes all LDT import settings and handles document import
func (s *DocumentManagementService) ProcessImportLdtDocuments(ctx *titan.Context) error {
	// Get total count of LDT import settings
	total, err := s.documentSettingRepo.CountLdtImportSetting(ctx)
	if err != nil {
		return errors.WithMessage(err, "failed to count LDT import settings")
	}

	if total == 0 {
		return nil
	}

	ctx.Logger().Info("Processing LDT import documents", "total_settings", total)

	// Process LDT import settings in batches using similar logic to GDT
	err = s.processLdtBatchedImports(ctx, int(total))
	if err != nil {
		ctx.Logger().Error("Failed to process LDT batched imports", "error", err)
		return err
	}

	return s.socketNotifier.NotifyCareProviderDocumentManagementChange(ctx, &api.EventDocumentManagementChange{})
}

// processLdtBatchedImports handles the concurrent processing of LDT import settings in batches
func (s *DocumentManagementService) processLdtBatchedImports(ctx *titan.Context, total int) error {
	totalBatches := (total + batchSize - 1) / batchSize

	ctx.Logger().Debug("Starting LDT batch processing", "total_batches", totalBatches, "batch_size", batchSize, "max_concurrency", maxConcurrency)

	// Similar batching logic to GDT processing
	for batchNum := 1; batchNum <= totalBatches; batchNum++ {
		ctx.Logger().Info("Processing LDT batch", "batch_num", batchNum, "batch_size", batchSize)
		settings, err := s.getLdtImportSettings(ctx, batchNum, batchSize)
		if err != nil {
			return errors.WithMessage(err, "failed to get LDT import settings for batch")
		}

		for _, setting := range settings {
			ctx.Logger().Info("Processing LDT setting", "setting_id", setting.Id)
			if err := s.processIndividualLdtImportSetting(ctx, setting); err != nil {
				return errors.WithMessagef(err, "failed to process LDT import setting: %s", setting.Id)
			}
		}
	}

	return nil
}

// getLdtImportSettings retrieves LDT import settings for a specific batch
func (s *DocumentManagementService) getLdtImportSettings(ctx *titan.Context, batch, batchSize int) ([]document_setting_repo.DocumentSettingEntity, error) {
	settings, _, err := s.documentSettingRepo.GetLdtImportSettings(
		ctx,
		int64(batch),
		int64(batchSize),
	)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get LDT import settings")
	}
	return settings, nil
}

// processIndividualLdtImportSetting processes a single LDT import setting
func (s *DocumentManagementService) processIndividualLdtImportSetting(ctx *titan.Context, setting document_setting_repo.DocumentSettingEntity) error {
	// LDT import settings don't support specific filenames, so we always process multiple files
	err := s.importLdtDocumentWithMultipleFiles(ctx, setting)
	return errors.WithMessage(err, "failed to import LDT document with multiple files")
}

type removePartialLDTDataRequest struct {
	orderId              string
	labParams            []document_setting_state_repo.LabParam
	labParameterResults  []document_setting_state_repo.LabParameterResult
	documentManagementId uuid.UUID
	patient              *patient.PatientProfile
}

// this will remove all the partial LDT data
// prcess ldt partial data
// process ldt partial result
// remove timeline
// remove lab result
// remove document management

func (s *DocumentManagementService) handlePartialLDTData(ctx *titan.Context, request removePartialLDTDataRequest) error {
	_, err := s.documentManagementRepo.WithTransaction(ctx, func(ctx *titan.Context) (any, error) {
		// Get all doc management with status partial or preliminary
		entitiesPartials, err := s.documentManagementRepo.GetByGDTStatusCode(ctx, document_management_repo.GetByGDTStatusCodeRequest{
			Status:  []gdt.LDTStatusCode{gdt.LDTStatusCodePartial, gdt.LDTStatusCodePreliminary},
			OrderId: request.orderId,
		})
		if err != nil {
			return nil, errors.WithMessagef(err, "failed to get partial LDT data: %s", request.orderId)
		}
		documentIds := slice.Map(entitiesPartials, func(entity *document_management_repo.DocumentManagementEntity) uuid.UUID {
			return *entity.Id
		})

		// handler and merge all lab params
		if err := s.handlePartialLDTLabParams(ctx, request, documentIds); err != nil {
			return nil, errors.WithMessagef(err, "failed to merge partial LDT data: %s", request.orderId)
		}

		// handler and merge all lab results
		if err := s.handlePartialLDTLabResults(ctx, request, documentIds); err != nil {
			return nil, errors.WithMessagef(err, "failed to merge partial LDT lab results: %s", request.orderId)
		}

		// Clean up all partial LDT data
		if err := s.cleanupPartialLDTData(ctx, request.orderId, documentIds); err != nil {
			return nil, errors.WithMessagef(err, "failed to cleanup partial LDT data: %s", request.orderId)
		}

		return nil, nil
	})
	return errors.WithMessagef(err, "failed to remove partial LDT data: %s", request.orderId)
}

// Handle partial LDT lab params
func (s *DocumentManagementService) handlePartialLDTLabParams(ctx *titan.Context, request removePartialLDTDataRequest, documentManagementIds []uuid.UUID) error {
	oldLabParams, err := s.labParamsRepo.GetAllPartialLDTParams(ctx, documentManagementIds)
	if err != nil {
		return errors.WithMessagef(err, "failed to get lab params by document management ids: %s", request.orderId)
	}

	mergedLabParams := mergeLabParams(oldLabParams, request.labParams, request.documentManagementId, request.patient)

	_, err = s.labParamsRepo.CreateMany(ctx, mergedLabParams)
	if err != nil {
		return errors.WithMessage(err, "failed to update lab params")
	}

	return nil
}

// Handle partial LDT lab results
func (s *DocumentManagementService) handlePartialLDTLabResults(ctx *titan.Context, request removePartialLDTDataRequest, documentManagementIds []uuid.UUID) error {
	oldLabResult, err := s.labResultRepo.GetLabResultByDocumentManagementIds(ctx, documentManagementIds)
	if err != nil {
		return errors.WithMessagef(err, "failed to get lab result by document management ids: %s", request.orderId)
	}

	// In many lab result document have same lab parameter name, so we need to merge them by time first this can be move to repo query pipeline
	labParameterResults := make([]document_setting_state_repo.LabParameterResult, 0)
	seenNames := make(map[string]bool)
	for _, labResult := range oldLabResult {
		for _, paramResult := range labResult.LabParameterResults {
			if !seenNames[paramResult.Name] {
				labParameterResults = append(labParameterResults, paramResult)
				seenNames[paramResult.Name] = true
			}
		}
	}

	mergedLabParameterResults := mergeLabResults(labParameterResults, request.labParameterResults)

	if err = s.labResultRepo.UpdateByDocumentManagementId(ctx, request.documentManagementId, mergedLabParameterResults); err != nil {
		return errors.WithMessagef(err, "failed to update lab result by document management id: %s", request.orderId)
	}

	return nil
}

// cleanupPartialLDTData removes all partial LDT data including document management, lab results, and timeline entries
func (s *DocumentManagementService) cleanupPartialLDTData(ctx *titan.Context, orderId string, documentManagementIds []uuid.UUID) error {
	if err := s.documentManagementRepo.SoftDelete(ctx, documentManagementIds); err != nil {
		return errors.WithMessagef(err, "failed to delete partial LDT data: %s", orderId)
	}

	if err := s.labResultRepo.DeleteByDocumentManagementIds(ctx, documentManagementIds); err != nil {
		return errors.WithMessagef(err, "failed to delete lab result by document management ids: %s", orderId)
	}

	if err := s.timelineServiceAny.DeleteByDocumentManagementIds(ctx, documentManagementIds); err != nil {
		return errors.WithMessagef(err, "failed to delete timeline by document management ids: %s", orderId)
	}

	if err := s.timelineDocumentManagementService.DeleteByDocumentManagementIds(ctx, documentManagementIds); err != nil {
		return errors.WithMessagef(err, "failed to delete timeline by document management ids: %s", orderId)
	}

	return nil
}

// importLdtDocumentWithMultipleFiles handles importing multiple LDT files from a directory
func (s *DocumentManagementService) importLdtDocumentWithMultipleFiles(ctx *titan.Context, setting document_setting_repo.DocumentSettingEntity) error {
	dmContext := cloneDmContext(ctx, setting.LdtImport.DeviceId.String())

	dmApp, err := s.companionService.GetDmApp(dmContext)
	if err != nil {
		return errors.WithMessage(err, "failed to get dm app")
	}

	err = dmApp.HandleGdtImportDocument(dmContext, dm_app.HandleDocumentRequest{
		Folder:            setting.LdtImport.Folder,
		Host:              setting.LdtImport.Host,
		FileExt:           ".ldt",
		Username:          setting.LdtImport.Username,
		Password:          setting.LdtImport.Password,
		SourceType:        dm_app.SourceTypeEnum(setting.LdtImport.SourceType),
		SettingId:         *setting.Id,
		CharacterEncoding: dm_app.CharacterEncoding(setting.LdtImport.CharacterEncoding),
	})
	return errors.WithMessage(err, "failed to import LDT document with multiple files")
}

// parseLdtContent decodes LDT content and determines if it's an order or result
func parseLdtContent(ctx *titan.Context, gdtImportData *dm_app.GdtImportData) (order *gdt.LDTOrder, result *gdt.LDTResult, err error) {
	content := strings.TrimSpace(gdtImportData.Content)

	// First, try to decode as LDT Order (8230 → 8218 → 8231)
	if strings.HasPrefix(content, "01380008230") {
		order, err = gdt.DecodeLDTOrder(strings.NewReader(content))
		ctx.Logger().Info("debug", "order", order)
		if err == nil && order != nil {
			ctx.Logger().Info("Successfully decoded LDT as Order", "file", gdtImportData.FilePath)
			return order, nil, nil
		}
	}
	// If that fails, try to decode as LDT Result (8220 → 8202 → 8221)
	result, err = gdt.DecodeLDTResult(strings.NewReader(content))
	ctx.Logger().Info("debug", "result", result)
	if err == nil && result != nil {
		ctx.Logger().Info("Successfully decoded LDT as Result", "file", gdtImportData.FilePath)
		return nil, result, nil
	}

	return nil, nil, errors.WithMessage(err, "failed to decode LDT content as either order or result")
}

type patientProfileWithLabOrder struct {
	patientProfile         *patient.PatientProfile
	requestOrderId         string
	requestId              string
	headerType             string
	labResultCreatedId     uuid.UUID
	gdtStatusCode          gdt.LDTStatusCode
	newLabParams           []document_setting_state_repo.LabParam
	newLabParameterResults []document_setting_state_repo.LabParameterResult
}

// func upload elt file to minio
// return the file name without the directory
func (s *DocumentManagementService) uploadLDTFileToMinio(ctx *titan.Context, fileName, content string) (string, error) {
	bucketName := s.mvzAppConfig.MinioClientConfig.BucketDmCompanion
	fileWithoutDir := filepath.Base(fileName)
	minioPath := fmt.Sprintf("%s/%s", *ctx.UserInfo().CareProviderUUID(), fileWithoutDir)
	object := strings.NewReader(content)
	_, err := s.minioClient.PutObject(ctx, bucketName, minioPath, object, object.Size(), minio.PutObjectOptions{})
	if err != nil {
		return "", errors.WithMessage(err, "failed to upload document data to minio")
	}
	return fileWithoutDir, nil
}

func splitLDTFileByHeader(ctx *titan.Context, content string) map[string][]string {
	chunks := make(map[string][]string)
	lines := strings.Split(content, "\n")
	headerFieldsValue := []string{"8201", "8202", "8204", "8203", "8218", "8219"}
	localStack := make([]string, 0)
	var orderIdFieldValue string
	for _, line := range lines {
		if line == "" || len(line) < 7 {
			ctx.Logger().Info("Skipping empty or too short line", "line", line)
			continue
		}
		orderIdField := line[3:7]
		fieldValue := strings.ReplaceAll(line[7:], "\r", "")
		cleanLine := strings.ReplaceAll(line, "\r", "")
		if slice.Contains(headerFieldsValue, fieldValue) {
			localStack = append(localStack, cleanLine) // keep track the previous header fields
			orderIdFieldValue = ""                     // reset order id field value
			continue
		}
		// found the header field, but not found the order id field, so we need to append to the local stack
		// to keep track the previous header fields
		// don't append to the local stack if the order id field is found
		if len(localStack) > 0 && orderIdFieldValue == "" && orderIdField != "8310" {
			localStack = append(localStack, cleanLine)
		}

		if orderIdField == "8310" {
			orderIdFieldValue = fieldValue
			chunks[orderIdFieldValue] = append(chunks[orderIdFieldValue], localStack...)
			localStack = make([]string, 0)
			chunks[orderIdFieldValue] = append(chunks[orderIdFieldValue], cleanLine)
			continue
		}

		if orderIdFieldValue != "" {
			chunks[orderIdFieldValue] = append(chunks[orderIdFieldValue], cleanLine)
		}
	}
	return chunks
}

func (s *DocumentManagementService) uploadLDTChunkFileToMinio(ctx *titan.Context, fileName, content string) error {
	chunks := splitLDTFileByHeader(ctx, content)
	for orderId, chunk := range chunks {
		content := strings.Join(chunk, "\r\n")
		if _, err := s.uploadLDTFileToMinio(ctx, fmt.Sprintf("%s_%s.chunk", fileName, orderId), content); err != nil {
			return errors.WithMessage(err, "failed to upload LDT chunk file to minio")
		}
	}
	return nil
}

func shouldProcessLdtFile(result *gdt.LDTResult, order *gdt.LDTOrder) bool {
	if result == nil && order == nil {
		return false
	}
	if result != nil {
		return len(result.Sentence8202s) > 0 ||
			len(result.Sentence8204s) > 0 ||
			len(result.Sentence8201s) > 0 ||
			len(result.Sentence8203s) > 0
	}
	if order != nil {
		return len(order.Sentence8218s) > 0 ||
			len(order.Sentence8219s) > 0
	}
	return false
}

func (s *DocumentManagementService) ProcessLdtImport(
	ctx *titan.Context,
	setting *document_setting_repo.DocumentSettingEntity,
	order *gdt.LDTOrder,
	result *gdt.LDTResult,
	gdtImportData *dm_app.GdtImportData) error {
	// Check if this LDT document has already been imported
	isExisted, err := s.documentManagementRepo.IsDocumentExisted(ctx, gdtImportData.FilePath, *setting.Id, 0)
	if err != nil {
		ctx.Logger().Error("Failed to check if LDT document exists", "error", err, "file", gdtImportData.FilePath)
		// Continue processing even if check fails to avoid blocking imports
	} else if isExisted {
		ctx.Logger().Info("LDT document already imported, skipping", "file", gdtImportData.FilePath, "setting_id", setting.Id)
		// Remove the file since it's already been processed
		if err := s.handleDocumentFileRemoval(ctx, setting, gdtImportData); err != nil {
			ctx.Logger().Error("Failed to remove already imported LDT document", "error", err)
		}
		return nil
	}
	if !shouldProcessLdtFile(result, order) {
		return fmt.Errorf("skipping LDT file because it doesn't contain any order or result, file path: %s", gdtImportData.FilePath)
	}
	fileName := fmt.Sprintf("%s_%s", uuid.NewString(), path.Base(gdtImportData.FilePath))
	fileNameUploaded, err := s.uploadLDTFileToMinio(ctx, fileName, gdtImportData.Content)
	if err != nil {
		return errors.WithMessage(err, "failed to upload LDT file to minio")
	}
	if err := s.uploadLDTChunkFileToMinio(ctx, fileNameUploaded, gdtImportData.Content); err != nil {
		return errors.WithMessage(err, "failed to upload LDT chunk file to minio")
	}

	matchedPatients := make([]patientProfileWithLabOrder, 0)
	orderIds := make([]string, 0)
	ldtType := patient_encounter.LDT_ORDER
	timelineContents := make([]string, 0)

	// Scenario 1: LDT Order processing
	if order != nil {
		ctx.Logger().Info("Processing LDT Order", "file", gdtImportData.FilePath)
		// Process both 8218 and 8219 records in a unified way
		type orderRecord struct {
			requestIdentifier string
			requirements      []gdt.RequirementInfo8218
			tests             []gdt.TestInfo8218
			canMatchPatient   bool
			sentence8218      *gdt.Sentence8218 // Only set for 8218 records
		}

		var allOrderRecords []orderRecord

		for _, patientRecord := range order.Sentence8218s {
			allOrderRecords = append(allOrderRecords, orderRecord{
				requestIdentifier: patientRecord.RequestIdentifier,
				requirements:      patientRecord.Requirements,
				tests:             patientRecord.Tests,
				canMatchPatient:   true,
				sentence8218:      &patientRecord,
			})
		}

		for _, patientRecord := range order.Sentence8219s {
			allOrderRecords = append(allOrderRecords, orderRecord{
				requestIdentifier: patientRecord.RequestIdentifier,
				requirements:      nil,
				tests:             patientRecord.Tests,
				canMatchPatient:   false,
				sentence8218:      nil,
			})
		}

		for _, record := range allOrderRecords {
			var matchedPatient *patient.PatientProfile

			if record.canMatchPatient && record.sentence8218 != nil {
				matchedPatient, err = s.matchPatientForLdtOrder(ctx, record.sentence8218)
				if err != nil {
					return errors.WithMessagef(err, "failed to match patient for LDT order: %s", record.requestIdentifier)
				}
			}

			timelineContents = append(timelineContents, generateLdtOrderTimelineContent(record.requirements, record.tests))

			matchedPatients = append(matchedPatients, patientProfileWithLabOrder{
				patientProfile: matchedPatient,
				requestOrderId: record.requestIdentifier,
			})
			orderIds = append(orderIds, record.requestIdentifier)
		}
	}

	extractor := func(headerInfo gdt.HeaderInfoGetter) error {
		matchedPatient, err := s.matchPatientForLdtResult(ctx, headerInfo)
		if err != nil {
			return errors.WithMessagef(err, "failed to match patient for LDT result: %s", headerInfo.GetRequestId())
		}
		timelineContent := generateLdtResultTimelineContent(headerInfo)
		timelineContents = append(timelineContents, timelineContent)

		orderIds = append(orderIds, headerInfo.GetRequestId())
		ldtType = patient_encounter.LDT_RESULT

		labParameterResults := make([]document_setting_state_repo.LabParameterResult, 0)
		params := make([]document_setting_state_repo.LabParam, 0)
		testInfos := headerInfo.GetTestInfo()

		requirements := headerInfo.GetTestRequirements()
		for _, requirement := range requirements {
			params = append(params, document_setting_state_repo.LabParam{
				Name: requirement.RequestID,
				Unit: requirement.Unit,
				Min:  requirement.ReferenceLowerLimit,
				Max:  requirement.ReferenceUpperLimit,
			})
			testTextValue := ""
			testNote := ""
			if len(requirement.ResultText) > 0 {
				testTextValue = strings.Join(requirement.ResultText, " ")
			}
			if len(requirement.TestRelatedNotes) > 0 {
				testNote = strings.Join(requirement.TestRelatedNotes, " ")
			}
			labParameterResults = append(labParameterResults, document_setting_state_repo.LabParameterResult{
				Name:                   requirement.RequestID,
				Value:                  requirement.ResultValue,
				Unit:                   requirement.Unit,
				Min:                    requirement.ReferenceLowerLimit,
				Max:                    requirement.ReferenceUpperLimit,
				ThresholdIndicatorIcon: requirement.ThresholdIndicator,
				TestResultText:         testTextValue,
				TestNote:               testNote,
			})
		}

		for _, test := range testInfos {
			params = append(params, document_setting_state_repo.LabParam{
				Name: test.TestDescription,
				Unit: test.Unit,
				Min:  test.ReferenceLowerLimit,
				Max:  test.ReferenceUpperLimit,
			})
			testTextValue := ""
			testNote := ""
			if len(test.ResultText) > 0 {
				testTextValue = strings.Join(test.ResultText, " ")
			}
			if len(test.TestRelatedNotes) > 0 {
				testNote = strings.Join(test.TestRelatedNotes, " ")
			}
			labParameterResults = append(labParameterResults, document_setting_state_repo.LabParameterResult{
				Name:                   test.TestDescription,
				Value:                  test.ResultValue,
				Unit:                   test.Unit,
				Min:                    test.ReferenceLowerLimit,
				Max:                    test.ReferenceUpperLimit,
				ThresholdIndicatorIcon: test.ThresholdIndicator,
				TestResultText:         testTextValue,
				TestNote:               testNote,
			})
		}
		patientId := uuid.Nil
		if matchedPatient != nil {
			patientId = *matchedPatient.Id
		}

		labResult := &document_setting_state_repo.LabResult{
			PatientId:            patientId,
			OrderId:              headerInfo.GetRequestId(),
			LabParameterResults:  labParameterResults,
			CreatedDate:          parseLdtDate(headerInfo.GetReportDate()),
			DocumentManagementId: nil,
		}
		labResultCreated, err := s.labResultRepo.Create(ctx, labResult)
		if err != nil {
			return errors.WithMessagef(err, "failed to create lab result, request_id: %s", headerInfo.GetRequestId())
		}
		if labResultCreated == nil {
			return fmt.Errorf("lab result created is nil, request_id: %s", headerInfo.GetRequestId())
		}
		matchedPatients = append(matchedPatients, patientProfileWithLabOrder{
			patientProfile:         matchedPatient,
			requestId:              headerInfo.GetRequestId(),
			headerType:             headerInfo.GetRecordType(),
			labResultCreatedId:     *labResultCreated.Id,
			gdtStatusCode:          headerInfo.GetFindings(),
			newLabParams:           params,
			newLabParameterResults: labParameterResults,
		})
		return nil
	}

	// Scenario 2: LDT Result processing
	if result != nil {
		ctx.Logger().Info("Processing LDT Result", "file", gdtImportData.FilePath)
		for _, patientRecord := range result.Sentence8201s {
			if err := extractor(&patientRecord); err != nil {
				return errors.WithMessagef(err, "failed to extract LDT sentence 8201 result: %s", patientRecord.RequestID)
			}
		}
		for _, patientRecord := range result.Sentence8202s {
			if err := extractor(&patientRecord); err != nil {
				return errors.WithMessagef(err, "failed to extract LDT sentence 8202 result: %s", patientRecord.RequestID)
			}
		}
		for _, patientRecord := range result.Sentence8203s {
			if err := extractor(&patientRecord); err != nil {
				return errors.WithMessagef(err, "failed to extract LDT sentence 8203 result: %s", patientRecord.RequestID)
			}
		}
		for _, patientRecord := range result.Sentence8204s {
			if err := extractor(&patientRecord); err != nil {
				return errors.WithMessagef(err, "failed to extract LDT sentence 8204 result: %s", patientRecord.RequestID)
			}
		}
	}

	for i := range matchedPatients {
		// Create document entity
		entity, err := s.createLdtDocumentEntity(ctx, createLdtDocumentEntityRequest{
			patientProfile:   matchedPatients[i].patientProfile,
			requestOrderId:   matchedPatients[i].requestOrderId,
			requestId:        matchedPatients[i].requestId,
			filePath:         gdtImportData.FilePath,
			setting:          setting,
			fileNameUploaded: fileNameUploaded,
			gdtStatusCode:    matchedPatients[i].gdtStatusCode,
		})
		if err != nil {
			return errors.WithMessage(err, "failed to create LDT document entity")
		}

		labParams := make([]document_setting_state_repo.LabParam, 0)

		if len(matchedPatients[i].newLabParams) > 0 {
			for _, param := range matchedPatients[i].newLabParams {
				param.DocumentManagementId = entity.Id
				if matchedPatients[i].patientProfile != nil {
					param.PatientId = *matchedPatients[i].patientProfile.Id
				}
				labParams = append(labParams, param)
			}
		}

		_, err = s.labParamsRepo.CreateMany(ctx, labParams)

		if err != nil {
			return errors.WithMessage(err, "failed to insert lab params")
		}

		if err := s.handleDocumentFileRemoval(ctx, setting, gdtImportData); err != nil {
			ctx.Logger().Error("failed to remove ldt document", "error", err)
		}

		if err := s.completeLdtImportProcess(ctx, handleLdtImportSuccessAsync{
			document:               entity,
			patient:                matchedPatients[i].patientProfile,
			setting:                setting,
			orderId:                orderIds[i],
			ldtType:                ldtType,
			timelineContent:        timelineContents[i],
			newLabParams:           matchedPatients[i].newLabParams,
			newLabParameterResults: matchedPatients[i].newLabParameterResults,
			labResultId:            matchedPatients[i].labResultCreatedId,
		}); err != nil {
			return errors.WithMessagef(err, "failed to handle LDT import success orderid: %s", orderIds[i])
		}
	}

	return nil
}

// matchPatientForLdtOrder implements Scenario 1 patient matching logic
func (s *DocumentManagementService) matchPatientForLdtOrder(ctx *titan.Context, patientRecord *gdt.Sentence8218) (*patient.PatientProfile, error) {
	// Strategy 1: Try insurance number matching first (most reliable)
	if patientRecord == nil {
		ctx.Logger().Warn("patient record is nil, skipping patient matching")
		return nil, nil
	}

	patient, err := s.matchByInsuranceNumber(ctx, patientRecord)
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("failed to match patient for LDT order by insurance number: %s,%s", patientRecord.InsuranceNumber, patientRecord.InsuredPersonID))
	}

	if patient != nil {
		return patient, nil
	}

	// Strategy 2: Fallback to name + DOB matching
	patientInfo := gdt.PatientInfo{
		FirstName: patientRecord.FirstName,
		LastName:  patientRecord.LastName,
		BirthDate: patientRecord.DateOfBirth,
	}

	patient, err = s.matchByLdtNameAndDOB(ctx, patientInfo)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to match patient for LDT order by name and DOB: %s,%s,%s", patientRecord.FirstName, patientRecord.LastName, patientRecord.DateOfBirth)
	}
	if patient != nil {
		return patient, nil
	}

	return nil, nil
}

// matchByInsuranceNumber attempts to match patient by insurance number (3105 or 3119)
func (s *DocumentManagementService) matchByInsuranceNumber(ctx *titan.Context, patientRecord *gdt.Sentence8218) (*patient.PatientProfile, error) {
	insuranceNumber := extractInsuranceNumber(patientRecord)
	if insuranceNumber == "" {
		ctx.Logger().Warn("No insurance number found", "request_id", patientRecord.RequestIdentifier)
		return nil, nil
	}

	patients, err := s.patientProfileRepo.GetPatientsProfileByInsuranceNumber(ctx, insuranceNumber)
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("failed to search patients by insurance number: %s", insuranceNumber))
	}

	return handleInsuranceNumberSearchResults(ctx, patients, patientRecord, insuranceNumber)
}

// matchByLdtNameAndDOB attempts to match patient by first name, last name, and date of birth
func (s *DocumentManagementService) matchByLdtNameAndDOB(ctx *titan.Context, patientInfo gdt.PatientInfo) (*patient.PatientProfile, error) {
	dob := parseLdtDateOfBirthString(patientInfo.BirthDate)
	if dob == nil {
		return nil, errors.WithMessage(errors.New("invalid date of birth format in LDT order"), patientInfo.BirthDate)
	}

	ctx.Logger().Info("Attempting patient match by name and DOB",
		"first_name", patientInfo.FirstName,
		"last_name", patientInfo.LastName,
		"dob", patientInfo.BirthDate)

	patients, err := s.patientProfileRepo.GetPatientByNameAndDOB(ctx, patientInfo.FirstName, patientInfo.LastName, *dob)
	if err != nil {
		ctx.Logger().Error("Error searching patients by name and DOB", "error", err)
		return nil, errors.WithMessage(err, "failed to search patients by name and DOB")
	}

	switch len(patients) {
	case 0:
		ctx.Logger().Info("No patients found matching name and DOB")
		return nil, nil
	case 1:
		ctx.Logger().Info("Found unique patient by name and DOB", "patient_id", patients[0].Id)
		return &patients[0], nil
	default:
		ctx.Logger().Info("Multiple patients found with same name and DOB", "patient_id", patients[0].Id)
		return nil, nil
	}
}

// matchPatientForLdtResult implements Scenario 2 patient matching logic
func (s *DocumentManagementService) matchPatientForLdtResult(
	ctx *titan.Context,
	headerInfo gdt.HeaderInfoGetter,
) (*patient.PatientProfile, error) {
	// Step 1: Try to find existing lab order by order number (8310)
	orderId := headerInfo.GetRequestId()
	if orderId != "" {
		patientId, err := s.timelineDocumentManagementService.GetPatientByLabOrder(ctx, timeline_service.GetPatientByLabOrderRequest{
			OrderId: orderId,
		})
		if err != nil {
			ctx.Logger().Error("Failed to get LDT entry by order id", "error", err, "order_id", orderId)
			return nil, errors.WithMessagef(err, "failed to get LDT entry by order id: %s", orderId)
		}
		if patientId != nil {
			return s.patientProfileRepo.GetPatientProfileById(ctx, *patientId)
		}
	}
	ctx.Logger().Warn("No LDT entry found by order id", "lab_order_id", orderId)

	// Step 2: Try to match by name and DOB if no patient found by order id
	patientInfo := headerInfo.GetPatientInfo()
	patient, err := s.matchByLdtNameAndDOB(ctx, patientInfo)
	if err != nil {
		ctx.Logger().Error("Failed to match patient by name and DOB", "error", err)
	}

	if patient != nil {
		return patient, nil
	}

	// NOTE: how to get patient name in result
	// Step 2: Try to match by DOB if available - this is less reliable but may work
	// in cases where there's only one patient with that exact DOB
	// if resultRecord.DateOfBirth != "" {
	// 	dob := s.parseLdtDateOfBirthString(resultRecord.DateOfBirth)
	// 	if dob != nil {
	// 		patients, err := s.patientProfileRepo.GetPatientByNameAndDOB(ctx, "", "", *dob)
	// 		if err != nil {
	// 			return nil, fmt.Errorf("failed to get patient profile by name and DOB: %w", err)
	// 		}
	// 		if len(patients) == 1 {
	// 			ctx.Logger().Info("Found unique patient by date of birth only", "patient_id", patients[0].Id)
	// 			return &patients[0], nil
	// 		}
	// 	}
	// }

	// For now, return nil as LDT results have insufficient patient identification info
	ctx.Logger().Error("Cannot reliably match LDT result to patient with available information")
	return nil, nil
}

type handleLdtImportSuccessAsync struct {
	document               *document_management_repo.DocumentManagementEntity
	patient                *patient.PatientProfile
	setting                *document_setting_repo.DocumentSettingEntity
	orderId                string
	ldtType                patient_encounter.EncounterLDTType
	newLabParams           []document_setting_state_repo.LabParam
	newLabParameterResults []document_setting_state_repo.LabParameterResult
	timelineContent        string
	labResultId            uuid.UUID
}

func mergeLabParams(
	allParams []*document_setting_state_repo.LabParamEntity,
	newParams []document_setting_state_repo.LabParam,
	documentManagementId uuid.UUID,
	patient *patient.PatientProfile,
) []document_setting_state_repo.LabParam {
	result := make([]document_setting_state_repo.LabParam, 0)
	newParamNames := make(map[string]bool)

	for _, newParam := range newParams {
		newParamNames[newParam.Name] = true
	}

	for _, oldParamEntity := range allParams {
		if oldParamEntity != nil && !newParamNames[oldParamEntity.LabParam.Name] {
			oldParam := oldParamEntity.LabParam
			oldParam.DocumentManagementId = &documentManagementId
			if patient != nil && patient.Id != nil {
				oldParam.PatientId = *patient.Id
			}
			result = append(result, oldParam)
		}
	}

	for _, newParam := range newParams {
		newParam.DocumentManagementId = &documentManagementId
		if patient != nil && patient.Id != nil {
			newParam.PatientId = *patient.Id
		}
		result = append(result, newParam)
	}

	return result
}

func mergeLabResults(
	allResults []document_setting_state_repo.LabParameterResult,
	newResults []document_setting_state_repo.LabParameterResult,
) []document_setting_state_repo.LabParameterResult {
	modifiedResults := make([]document_setting_state_repo.LabParameterResult, 0)
	updatedNames := make(map[string]bool)

	for _, newResult := range newResults {
		modifiedResults = append(modifiedResults, newResult)
		updatedNames[newResult.Name] = true
	}

	for _, oldResult := range allResults {
		if !updatedNames[oldResult.Name] {
			modifiedResults = append(modifiedResults, oldResult)
		}
	}

	return modifiedResults
}

// Enhanced completeLdtImportProcess
func (s *DocumentManagementService) completeLdtImportProcess(
	ctx *titan.Context,
	request handleLdtImportSuccessAsync,
) error {
	var patientName string
	_, err := s.documentManagementRepo.WithTransaction(ctx, func(ctx *titan.Context) (any, error) {
		if request.document.GetMetaData(common.MetaDataKey_GDTFileStatus) == string(gdt.LDTStatusCodeFinal) {
			if err := s.handlePartialLDTData(ctx, removePartialLDTDataRequest{
				orderId:              request.orderId,
				labParams:            request.newLabParams,
				labParameterResults:  request.newLabParameterResults,
				documentManagementId: *request.document.Id,
				patient:              request.patient,
			}); err != nil {
				return nil, errors.WithMessagef(err, "failed to remove partial LDT data: %s", request.orderId)
			}
		}
		// Update document management status to completed
		_, err := s.documentManagementRepo.UpdateStatusById(ctx, *request.document.Id, common.DocumentManagementStatus_Completed)
		if err != nil {
			ctx.Logger().Error("failed to update LDT document management status", "error", err)
			return nil, errors.WithMessagef(err, "failed to update LDT document management status: %s", request.orderId)
		}

		// Create LDT timeline entry if patient was found

		if err := s.createLdtTimelineEntry(ctx, createLdtTimelineEntryRequest{
			entity:          request.document,
			patient:         request.patient,
			setting:         request.setting,
			timelineContent: request.timelineContent,
			orderId:         request.orderId,
			ldtType:         request.ldtType,
		}); err != nil {
			ctx.Logger().Error("failed to create LDT import timeline", "error", err)
			return nil, errors.WithMessagef(err, "failed to create LDT import timeline orderid: %s", request.orderId)
		}

		if request.ldtType == patient_encounter.LDT_RESULT {
			if err := s.labResultRepo.UpdateDocumentManagementIdById(ctx, request.labResultId, *request.document.Id); err != nil {
				ctx.Logger().Error("failed to update lab result document management id", "error", err)
				return nil, errors.WithMessagef(err, "failed to update lab result document management id: %s", request.orderId)
			}
		}

		// Notify success for LDT import
		if request.patient != nil && request.patient.PatientInfo != nil {
			patientName = request.patient.PatientInfo.PersonalInfo.GetFullName()
		}

		return nil, nil
	})

	_ = s.socketNotifier.NotifyCareProviderGdtImportResult(ctx, &api.EventGdtImportResult{
		Result:      true,
		PatientName: patientName,
		Type:        common.DocumentNotificationType_LDT,
		Error: function.Do(func() string {
			if err != nil {
				return err.Error()
			}
			return ""
		}),
	})
	return errors.WithMessagef(err, "failed to complete LDT import process orderid: %s", request.orderId)
}

type createLdtTimelineEntryRequest struct {
	entity          *document_management_repo.DocumentManagementEntity
	patient         *patient.PatientProfile
	setting         *document_setting_repo.DocumentSettingEntity
	timelineContent string
	orderId         string
	ldtType         patient_encounter.EncounterLDTType
}

// Enhanced createLdtTimelineEntry with custom content
func (s *DocumentManagementService) createLdtTimelineEntry(
	ctx *titan.Context,
	request createLdtTimelineEntryRequest,
) error {
	bucketName := s.mvzAppConfig.MinioClientConfig.BucketDmCompanion
	minioPath := fmt.Sprintf("%s/%s", ctx.UserInfo().CareProviderUUID(), request.entity.GetMetaData(common.MetaDataKey_LabResultChunkFileName))

	patientId := &uuid.Nil
	if request.patient != nil {
		patientId = request.patient.Id
	}

	timelineEntity := timeline_repo.TimelineEntity[patient_encounter.EncounterLDT]{
		Id:        util.NewUUID(),
		PatientId: *patientId,
		Payload: patient_encounter.EncounterLDT{
			FilePath:             minioPath,
			FileName:             request.entity.DocumentName,
			BucketName:           bucketName,
			Note:                 request.timelineContent, // Use the custom generated content
			LdtImportSettingId:   request.setting.Id,
			LdtImportSettingName: request.setting.LdtImport.Name,
			DocumentManagementId: request.entity.Id,
			Command:              request.setting.LdtImport.TimelineType,
			Type:                 request.ldtType,
			LabOrderId:           request.orderId,
		},
	}

	// Create timeline entry using any timeline service since there's no specific LDT service
	anyTimelineEntity := timeline_repo.TimelineEntity[any]{
		Id:        timelineEntity.Id,
		PatientId: timelineEntity.PatientId,
		Payload:   timelineEntity.Payload,
	}

	_, err := s.timelineServiceAny.Create(ctx, anyTimelineEntity)
	return err
}

type createLdtDocumentEntityRequest struct {
	patientProfile   *patient.PatientProfile
	requestOrderId   string
	requestId        string
	filePath         string
	setting          *document_setting_repo.DocumentSettingEntity
	fileNameUploaded string
	gdtStatusCode    gdt.LDTStatusCode
}

// createLdtDocumentEntity creates a document management entity for LDT files
func (s *DocumentManagementService) createLdtDocumentEntity(ctx *titan.Context, request createLdtDocumentEntityRequest) (*document_management_repo.DocumentManagementEntity, error) {
	documentType, err := s.documentTypeService.GetOrCreateDocumentTypeByName(ctx, document_type_repo.LdtImport)
	if err != nil {
		return nil, fmt.Errorf("failed to get or create LDT document type: %w", err)
	}

	if documentType == nil {
		return nil, fmt.Errorf("failed to get or create LDT document type for name %s", document_type_repo.LdtImport)
	}

	var patientId *uuid.UUID
	if request.patientProfile != nil {
		patientId = request.patientProfile.Id
	}

	metaData := make(map[string]string)
	var orderId string
	if request.requestOrderId != "" {
		metaData[string(common.MetaDataKey_PatientLabOrder)] = request.requestOrderId
		orderId = request.requestOrderId
	}
	if request.requestId != "" {
		metaData[string(common.MetaDataKey_PatientLabResult)] = request.requestId
		orderId = request.requestId
	}
	if request.fileNameUploaded != "" {
		metaData[string(common.MetaDataKey_FileNameUploaded)] = request.fileNameUploaded
		metaData[string(common.MetaDataKey_LabResultChunkFileName)] = fmt.Sprintf("%s_%s.chunk", request.fileNameUploaded, orderId)
	}
	if request.gdtStatusCode != "" {
		metaData[string(common.MetaDataKey_GDTFileStatus)] = string(request.gdtStatusCode)
	}

	entity, err := s.documentManagementRepo.Create(ctx, common.DocumentManagementModel{
		DocumentName:      request.filePath,
		DocumentType:      documentType,
		Status:            common.DocumentManagementStatus_New,
		PatientId:         patientId,
		DocumentSettingId: request.setting.Id,
		ImportedDate:      util.NowUnixMillis(ctx),
		GdtImportModTime:  0, // Set to 0 for LDT imports to enable duplicate detection
		MetaData:          util.NewPointer(metaData),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create LDT document management: %w", err)
	}

	return entity, nil
}

// generateLdtOrderTimelineContent creates timeline content for LDT orders (Scenario 1)
func generateLdtOrderTimelineContent(requirements []gdt.RequirementInfo8218, tests []gdt.TestInfo8218) string {
	contentBuilder := strings.Builder{}

	contentBuilder.WriteString(gdt.LabOrderTitle + "\n")

	var requirementParams []string
	var testParams []string
	for _, req := range requirements {
		if req.Requirement != "" { // FK 8434
			requirementParams = append(requirementParams, req.Requirement)
		}
	}
	if len(requirementParams) > 0 {
		contentBuilder.WriteString(strings.Join(requirementParams, ", "))
	}

	for _, test := range tests {
		if test.TestName != "" { // FK 8411
			testParams = append(testParams, test.TestName)
		}
	}
	if len(testParams) > 0 {
		if len(requirementParams) > 0 {
			contentBuilder.WriteString("\n")
		}
		contentBuilder.WriteString(strings.Join(testParams, ", "))
	}
	return contentBuilder.String()
}

// generateLdtResultTimelineContent creates timeline content for LDT results (Scenario 2)
func generateLdtResultTimelineContent(result gdt.HeaderInfoGetter) string {
	status := result.GetFindings().GetStatus()
	reportDate := result.GetReportDate()

	contentBuilder := strings.Builder{}

	contentBuilder.WriteString(gdt.LabResultTitle + "\n")

	if reportDate == "" {
		reportDate = "unknown date"
	} else if len(reportDate) == 8 {
		// Convert DDMMYYYY to DD.MM.YYYY
		year := reportDate[:4]
		month := reportDate[4:6]
		day := reportDate[6:8]
		reportDate = fmt.Sprintf("%s.%s.%s", day, month, year)
	}

	contentBuilder.WriteString(fmt.Sprintf("Results with Status %s are available for Lab tests from %s", status, reportDate))

	return contentBuilder.String()
}

func parseLdtDate(dateStr string) int64 {
	date, err := time.Parse("20060102", dateStr)
	if err != nil {
		return 0
	}
	return date.UnixMilli()
}

// parseLdtDateOfBirthString converts LDT date string (YYYYMMDD) to DateOfBirth struct
func parseLdtDateOfBirthString(dateStr string) *patient_profile_common.DateOfBirth {
	if len(dateStr) != 8 {
		return nil
	}

	year, err := strconv.ParseInt(dateStr[:4], 10, 32)
	if err != nil {
		return nil
	}

	month, err := strconv.ParseInt(dateStr[4:6], 10, 32)
	if err != nil {
		return nil
	}

	day, err := strconv.ParseInt(dateStr[6:8], 10, 32)
	if err != nil {
		return nil
	}

	dob := &patient_profile_common.DateOfBirth{
		Year:       util.NewPointer(int32(year)),
		Month:      util.NewPointer(int32(month)),
		Date:       util.NewPointer(int32(day)),
		IsValidDOB: false,
	}

	if dob.Year != nil && dob.Month != nil && dob.Date != nil {
		dob.IsValidDOB = true
	}

	return dob
}

// compareDateOfBirth compares two DateOfBirth objects
func compareDateOfBirth(dob1, dob2 *patient_profile_common.DateOfBirth) bool {
	if dob1 == nil || dob2 == nil {
		return false
	}
	return util.GetPointerValue(dob1.Date) == util.GetPointerValue(dob2.Date) &&
		util.GetPointerValue(dob1.Month) == util.GetPointerValue(dob2.Month) &&
		util.GetPointerValue(dob1.Year) == util.GetPointerValue(dob2.Year)
}

// extractInsuranceNumber gets insurance number from patient record (3105 or 3119)
func extractInsuranceNumber(patientRecord *gdt.Sentence8218) string {
	if patientRecord.InsuranceNumber != "" {
		return patientRecord.InsuranceNumber // Field 3105
	}
	if patientRecord.InsuredPersonID != "" {
		return patientRecord.InsuredPersonID // Field 3119
	}
	return ""
}

// isPatientMatchByNameAndDOB checks if a patient matches the given name and DOB criteria
func isPatientMatchByNameAndDOB(patient patient.PatientProfile, firstName, lastName string, dob *patient_profile_common.DateOfBirth) bool {
	if patient.PatientInfo == nil {
		return false
	}

	personalInfo := &patient.PatientInfo.PersonalInfo
	return strings.EqualFold(personalInfo.FirstName, firstName) &&
		strings.EqualFold(personalInfo.LastName, lastName) &&
		compareDateOfBirth(&personalInfo.DateOfBirth, dob)
}

// refineInsuranceMatchWithNameAndDOB narrows down multiple insurance matches using name and DOB
func refineInsuranceMatchWithNameAndDOB(ctx *titan.Context, candidates []patient.PatientProfile, patientRecord *gdt.Sentence8218) (*patient.PatientProfile, error) {
	dob := parseLdtDateOfBirthString(patientRecord.DateOfBirth)
	if dob == nil {
		return nil, errors.WithMessage(errors.New("invalid date of birth format in LDT order"), patientRecord.DateOfBirth)
	}

	// Filter candidates by exact name and DOB match
	var matches []patient.PatientProfile
	for _, candidate := range candidates {
		if isPatientMatchByNameAndDOB(candidate, patientRecord.FirstName, patientRecord.LastName, dob) {
			matches = append(matches, candidate)
		}
	}

	switch len(matches) {
	case 0:
		return nil, errors.WithMessage(errors.New("no patients among insurance matches have matching name and DOB"),
			fmt.Sprintf("%s %s %s", patientRecord.FirstName, patientRecord.LastName, patientRecord.DateOfBirth))
	case 1:
		ctx.Logger().Info("Refined to unique patient using name+DOB", "patient_id", matches[0].Id)
		return &matches[0], nil
	default:
		return nil, errors.WithMessage(errors.New("multiple patients found even after name+DOB refinement"),
			fmt.Sprintf("%s %s %s", patientRecord.FirstName, patientRecord.LastName, patientRecord.DateOfBirth))
	}
}

// handleInsuranceNumberSearchResults processes the results of insurance number search
func handleInsuranceNumberSearchResults(ctx *titan.Context, patients []patient.PatientProfile, patientRecord *gdt.Sentence8218, insuranceNumber string) (*patient.PatientProfile, error) {
	switch len(patients) {
	case 0:
		ctx.Logger().Info("No patients found by insurance number", "insurance_number", insuranceNumber)
		return nil, nil

	case 1:
		ctx.Logger().Info("Found unique patient by insurance number", "insurance_number", insuranceNumber, "patient_id", patients[0].Id)
		return &patients[0], nil

	default:
		ctx.Logger().Info("Multiple patients found by insurance number, refining with name+DOB",
			"insurance_number", insuranceNumber, "count", len(patients))
		return refineInsuranceMatchWithNameAndDOB(ctx, patients, patientRecord)
	}
}

func (s *DocumentManagementService) handleAssignPatientToLdtTimeline(ctx *titan.Context,
	documentManagementId uuid.UUID,
	currentPatientId,
	newPatientId *uuid.UUID,
) error {
	// If the requested mapping is identical to the current one, nothing to do.
	if util.GetPointerValue(currentPatientId) == util.GetPointerValue(newPatientId) {
		return nil
	}

	// Locate the corresponding LDT timeline entry.
	filter := bson.M{
		timeline_repo.Field_IsDeleted:          false,
		timeline_repo.Field_timelineEntityType: timeline_common.TimelineEntityType_LDT,
		"payload.documentmanagementid":         documentManagementId,
	}

	timelines, err := s.timelineServiceAny.Find(ctx, filter)
	if err != nil {
		return errors.WithMessage(err, "failed to get ldt timeline")
	}
	if len(timelines) == 0 {
		return nil // no timeline found – nothing to update
	}

	timeline := timelines[0]

	// Scenario A: timeline currently has no patient – simply update it.
	if currentPatientId == nil {
		timeline.PatientId = util.GetPointerValue(newPatientId)
		timelineUpdated, err := s.timelineServiceAny.Edit(ctx, timeline)
		if err != nil {
			return fmt.Errorf("failed to edit ldt timeline: %w", err)
		}
		if timelineUpdated == nil || timelineUpdated.Id == nil {
			return errors.New("no timeline edited")
		}
		if ldt, ok := convertToEncounterLDT(timeline.Payload); ok && ldt.Type == patient_encounter.LDT_RESULT {
			if err = s.labParamsRepo.AssignLabParamsByDocumentManagementId(ctx, documentManagementId, newPatientId); err != nil {
				return errors.WithMessage(err, "failed to assign lab params")
			}
			return s.labResultRepo.UpdatePatientIdByDocumentManagementId(ctx, documentManagementId, newPatientId)
		}
		return nil
	}

	// Scenario B: re-assign timeline to another patient – remove old entry and create a new one.

	if _, err := s.timelineRepo.DeleteById(ctx, *timeline.Id); err != nil {
		return errors.WithMessage(err, "failed to remove ldt timeline")
	}

	// Ensure payload has the correct concrete type to avoid BSON marshaling issues.
	newPayload := timeline.Payload
	if ldtPayload, ok := convertToEncounterLDT(timeline.Payload); ok {
		newPayload = ldtPayload
	}

	newEntity := timeline_repo.TimelineEntity[any]{
		Id:        util.NewUUID(),
		Payload:   newPayload,
		PatientId: util.GetPointerValue(newPatientId),
		Type:      timeline_common.TimelineEntityType_LDT,
	}

	if _, err := s.timelineServiceAny.Create(ctx, newEntity); err != nil {
		return errors.WithMessage(err, "failed to create ldt timeline")
	}

	if ldtPayload, ok := convertToEncounterLDT(newPayload); ok && ldtPayload.Type == patient_encounter.LDT_RESULT {
		err = s.labParamsRepo.AssignLabParamsByDocumentManagementId(ctx, documentManagementId, newPatientId)
		if err != nil {
			return fmt.Errorf("failed to assign lab params: %w", err)
		}
		return s.labResultRepo.UpdatePatientIdByDocumentManagementId(ctx, documentManagementId, newPatientId)
	}

	return nil
}

// convertToEncounterLDT tries to transform any payload representation (struct, primitive.D, etc.)
// into a concrete patient_encounter.EncounterLDT value.
func convertToEncounterLDT(payload any) (patient_encounter.EncounterLDT, bool) {
	data, err := bson.MarshalWithRegistry(mongodb_repo.MongoRegistry, payload)
	if err != nil {
		return patient_encounter.EncounterLDT{}, false
	}
	var result patient_encounter.EncounterLDT
	err = bson.UnmarshalWithRegistry(mongodb_repo.MongoRegistry, data, &result)
	if err != nil {
		return patient_encounter.EncounterLDT{}, false
	}
	return result, true
}
