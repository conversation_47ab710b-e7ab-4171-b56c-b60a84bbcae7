package service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/url"
	"path"
	"path/filepath"
	"runtime/debug"
	"strconv"
	"strings"
	"time"

	"git.tutum.dev/medi/tutum/ares/app/companion/dm_app"
	api "git.tutum.dev/medi/tutum/ares/app/mvz/api/document_management"
	"git.tutum.dev/medi/tutum/ares/pkg/file_util"
	"git.tutum.dev/medi/tutum/ares/pkg/minio"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_copy"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/document_management/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/document_setting_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"
	document_setting_repo "git.tutum.dev/medi/tutum/ares/service/domains/document_setting/repo"
	document_type_repo "git.tutum.dev/medi/tutum/ares/service/domains/document_type/repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	document_management_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/document_management"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	employeeRepo "git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	"git.tutum.dev/medi/tutum/ares/service/gdt"
	"git.tutum.dev/medi/tutum/pkg/field_transfer"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.com/silenteer-oss/titan"
)

type LdtBuilderRequest struct {
	Setting  *document_setting_repo.DocumentSettingEntity
	Patient  *profile.PatientProfile
	ScheinId uuid.UUID
}

// Z01{{patientID}}{{DDMMYYYhhmmss}}.ldt
// ExportGdtPayload encapsulates the parameters for GDT document export operations
type ExportGdtPayload struct {
	Ctx               *titan.Context
	DmContext         *titan.Context
	Entity            *document_management_repo.DocumentManagementEntity
	Setting           *document_setting_repo.DocumentSettingEntity
	PresignUrl        *url.URL
	LdtExportFileName string
}

func generateLdtExportFileName(patientNumber int32) string {
	return fmt.Sprintf("Z01%d%s.ldt", patientNumber, time.Now().Format("02012006150405"))
}

// handle export gdt and ldt document
func (s *DocumentManagementService) ExportGdtDocument(ctx *titan.Context, req api.ExportGdtDocumentRequest) (*api.ExportGdtDocumentResponse, error) {
	// Step 1: Get and validate patient
	patient, err := s.getAndValidatePatient(ctx, req.PatientId)
	if err != nil {
		return nil, err
	}

	// Step 2: Validate and get export setting
	setting, dmContext, err := s.validateAndPrepareExport(ctx, req.GdtExportSettingId)
	if err != nil {
		return nil, err
	}

	// Step 3: Check if document already exists
	if err := s.checkDocumentExists(dmContext, setting); err != nil {
		return nil, err
	}

	// Step 4: Build GDT or LDT data
	var data []byte
	if setting.DocumentSettingType == document_setting_common.DocumentSettingType_GdtExport {
		data, err = s.buildGdtData(ctx, req, setting, patient)
	} else {
		data, err = s.buildLdtData(ctx,
			LdtBuilderRequest{
				Setting:  setting,
				Patient:  patient,
				ScheinId: *req.ScheinId,
			},
		)
	}
	if err != nil {
		return nil, err
	}

	// Step 5: Create document management entity
	entity, err := s.createDocumentManagementEntity(ctx, req, setting, patient)
	if err != nil {
		return nil, err
	}

	ldtExportFileName := generateLdtExportFileName(patient.PatientInfo.PatientNumber)

	// Step 6: Upload to Minio and get presigned URL
	presignUrl, err := s.uploadGdtDataAndGetPresignUrl(ctx, entity, data, ldtExportFileName)
	if err != nil {
		return nil, err
	}

	// Step 7: Export document asynchronously
	s.exportGdtDocumentAsync(ExportGdtPayload{
		Ctx:               ctx,
		DmContext:         dmContext,
		Entity:            entity,
		Setting:           setting,
		PresignUrl:        presignUrl,
		LdtExportFileName: ldtExportFileName,
	})

	return &api.ExportGdtDocumentResponse{}, nil
}

func (s *DocumentManagementService) validateAndPrepareExport(ctx *titan.Context, settingId uuid.UUID) (*document_setting_repo.DocumentSettingEntity, *titan.Context, error) {
	setting, err := s.documentSettingService.GetDocumentSettingById(ctx, settingId)
	if err != nil {
		return nil, nil, errors.WithMessage(err, "failed to get setting")
	}
	if setting == nil {
		return nil, nil, errors.New("setting not found")
	}

	var deviceId string
	switch setting.DocumentSettingType {
	case document_setting_common.DocumentSettingType_GdtExport:
		if setting.GdtExport == nil {
			return nil, nil, errors.New("gdt export setting not found")
		}
		deviceId = setting.GdtExport.DeviceId.String()
	case document_setting_common.DocumentSettingType_LdtExport:
		if setting.LdtExport == nil {
			return nil, nil, errors.New("ldt export setting not found")
		}
		deviceId = setting.LdtExport.DeviceId.String()
	default:
		return nil, nil, errors.New("unsupported document setting type")
	}

	dmContext := createDmContext(ctx, deviceId)

	if !s.companionService.IsCompanionReady(dmContext) {
		return nil, nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode("ErrorCode_GDT_Companion_NotReady"))
	}

	return setting, dmContext, nil
}

func (s *DocumentManagementService) checkDocumentExists(dmContext *titan.Context, setting *document_setting_repo.DocumentSettingEntity) error {
	// because we have timestamp in ldt export file name, so we don't need to check if document exists
	if setting.DocumentSettingType == document_setting_common.DocumentSettingType_LdtExport {
		return nil
	}

	dmApp, err := s.companionService.GetDmApp(dmContext)
	if err != nil {
		return errors.WithMessage(err, "failed to get dm app")
	}

	res, err := dmApp.CheckExistedDocument(dmContext, dm_app.HandleDocumentRequest{
		Filename:          setting.GdtExport.FileName,
		Folder:            setting.GdtExport.Folder,
		Host:              setting.GdtExport.Host,
		Username:          setting.GdtExport.Username,
		Password:          setting.GdtExport.Password,
		SourceType:        dm_app.SourceTypeEnum(setting.GdtExport.SourceType),
		CharacterEncoding: dm_app.CharacterEncoding(setting.GdtExport.CharacterEncoding),
	})
	if err != nil {
		return errors.WithMessage(err, "failed to check existed document")
	}
	if res.IsExisted {
		return pkg_errors.NewTitanCommonException(dmContext, error_code.ErrorCode("ErrorCode_Gdt_Export_Existed"))
	}

	return nil
}

func (s *DocumentManagementService) getAndValidatePatient(ctx *titan.Context, patientId uuid.UUID) (*profile.PatientProfile, error) {
	patient, err := s.patientProfileService.GetProfileById(ctx, profile.GetByIdRequest{
		PatientId: &patientId,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patient profile")
	}
	if patient == nil {
		return nil, errors.New("patient profile not found")
	}
	if patient.PatientInfo == nil {
		return nil, errors.New("patient info not found")
	}

	return patient, nil
}

func (s *DocumentManagementService) getAndValidateSchein(ctx *titan.Context, scheinId schein_common.GetScheinDetailByIdRequest) (*schein_common.GetScheinDetailByIdResponse, error) {
	schein, err := s.scheinService.GetScheinDetailById(ctx, scheinId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get schein detail")
	}
	if schein == nil {
		return nil, errors.New("schein not found")
	}

	return schein, nil
}

func (s *DocumentManagementService) getAndValidateEmployeeProfile(ctx *titan.Context, employeeId uuid.UUID) (*employeeRepo.EmployeeProfile, error) {
	if employeeId == uuid.Nil {
		return nil, errors.New("employee ID is required")
	}
	empl, err := s.employeeProfileService.FindEmployeeById(ctx, employeeId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get employee profile")
	}
	if empl == nil {
		return nil, errors.New("employee not found")
	}

	return empl, nil
}

func (s *DocumentManagementService) buildGdtData(ctx *titan.Context, req api.ExportGdtDocumentRequest, setting *document_setting_repo.DocumentSettingEntity, patient *profile.PatientProfile) ([]byte, error) {
	garrioProVersion := util.GetPointerValue(s.garrioProVersionConfig).Version
	builder := gdt.NewGDTBuilder(setting.GdtExport.CharacterEncoding, *patient, setting.GdtExport, garrioProVersion)

	if req.ScheinId != nil {
		schein, err := s.scheinService.GetScheinById(ctx, *req.ScheinId)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get schein")
		}
		builder.SetSchein(schein)
	}

	switch setting.GdtExport.SentenceTable {
	case document_setting_common.SentenceTable_6301:
		return builder.Build6301(), nil
	case document_setting_common.SentenceTable_6302:
		return builder.Build6302(), nil
	case document_setting_common.SentenceTable_6311:
		treatmentTime, readingTime := parseTimes(ctx, req)
		treatmentDate, readingDate := parseDates(ctx, req)
		return builder.Build6311(treatmentDate, readingDate, treatmentTime, readingTime), nil
	default:
		return nil, errors.New("unsupported sentence table")
	}
}

func (s *DocumentManagementService) buildLdtData(ctx *titan.Context, params LdtBuilderRequest) ([]byte, error) {
	schein, err := s.getAndValidateSchein(ctx, schein_common.GetScheinDetailByIdRequest{
		ScheinId: params.ScheinId,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get schein")
	}

	employee, err := s.getAndValidateEmployeeProfile(ctx, schein.DoctorId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get employee profile")
	}

	builder := gdt.NewLDTBuilder(gdt.LDTBuilder{
		PatientProfile: *params.Patient,
		LdtExport:      params.Setting.LdtExport,
		Schein:         schein,
		Employee:       employee,
	})
	data, err := builder.Build(ctx)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to build LDT data")
	}
	return data, nil
}

func parseDates(ctx *titan.Context, req api.ExportGdtDocumentRequest) (treatmentDate, readingDate time.Time) {
	if util.GetPointerValue(req.TreatmentDate) != 0 {
		treatmentDate = util.ConvertMillisecondsToTime(*req.TreatmentDate, ctx.RequestTimeZone())
	}
	if util.GetPointerValue(req.ReadingDate) != 0 {
		readingDate = util.ConvertMillisecondsToTime(*req.ReadingDate, ctx.RequestTimeZone())
	}

	return treatmentDate, readingDate
}

func parseTimes(ctx *titan.Context, req api.ExportGdtDocumentRequest) (treatmentTime, readingTime *time.Time) {
	if req.TreatmentTime != nil {
		treatmentTime = util.NewPointer(util.ConvertMillisecondsToTime(*req.TreatmentTime, ctx.RequestTimeZone()))
	}
	if req.ReadingTime != nil {
		readingTime = util.NewPointer(util.ConvertMillisecondsToTime(*req.ReadingTime, ctx.RequestTimeZone()))
	}

	return treatmentTime, readingTime
}

func (s *DocumentManagementService) createDocumentManagementEntity(ctx *titan.Context, req api.ExportGdtDocumentRequest, setting *document_setting_repo.DocumentSettingEntity, patient *profile.PatientProfile) (*document_management_repo.DocumentManagementEntity, error) {
	var filePath string
	switch setting.DocumentSettingType {
	case document_setting_common.DocumentSettingType_GdtExport:
		filePath = path.Join(setting.GdtExport.Folder, setting.GdtExport.FileName)
	case document_setting_common.DocumentSettingType_LdtExport:
		filePath = path.Join(setting.LdtExport.Folder, generateLdtExportFileName(patient.PatientInfo.PatientNumber))
	}

	documentType, err := s.documentTypeService.GetOrCreateDocumentTypeByName(ctx, document_type_repo.GdtExport)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get or create document type")
	}

	entity, err := s.documentManagementRepo.Create(ctx, common.DocumentManagementModel{
		DocumentName:      filePath,
		DocumentType:      documentType,
		Status:            common.DocumentManagementStatus_New,
		PatientId:         &req.PatientId,
		ImportedDate:      util.NowUnixMillis(ctx),
		DocumentSettingId: setting.Id,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create document management")
	}

	return entity, nil
}

func (s *DocumentManagementService) uploadGdtDataAndGetPresignUrl(ctx *titan.Context, entity *document_management_repo.DocumentManagementEntity, data []byte, ldtFileName string) (*url.URL, error) {
	fileName := entity.GetFileName(*ctx.UserInfo().CareProviderUUID())
	bucketName := s.mvzAppConfig.MinioClientConfig.BucketDmCompanion
	object := bytes.NewReader(data)

	_, err := s.minioClient.PutObject(ctx, bucketName, fileName, object, object.Size(), minio.PutObjectOptions{})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to upload gdt data to minio")
	}

	presignUrl, err := s.minioClient.PresignedGetObject(ctx, bucketName, fileName, time.Hour, url.Values{
		"response-content-disposition": []string{fmt.Sprintf("attachment; filename=%s", ldtFileName)},
	})

	if err != nil {
		return nil, errors.WithMessage(err, "failed to get presign url")
	}

	return presignUrl, nil
}

func createDmContext(ctx *titan.Context, deviceId string) *titan.Context {
	userInfo := ctx.UserInfo()
	dmUserInfo := pkg_copy.CloneTo[titan.UserInfo](userInfo)
	dmUserInfo.DeviceId = deviceId
	dmContext := ctx.Clone()
	return dmContext.WithValue(titan.XUserInfo, &dmUserInfo)
}

func (s *DocumentManagementService) exportGdtDocumentAsync(payload ExportGdtPayload) {
	ctxClone := payload.Ctx.Clone()

	go func() {
		defer func() {
			if err := recover(); err != nil {
				ctxClone.Logger().Error("panic occurred", "error", err)
			}
		}()

		dmApp, err := s.companionService.GetDmApp(payload.DmContext)
		if err != nil {
			s.handleExportError(ctxClone, payload.Entity, err, "failed to get dm app")
			return
		}

		var exportRequest dm_app.ExportGdtDocumentRequest
		switch payload.Setting.DocumentSettingType {
		case document_setting_common.DocumentSettingType_GdtExport:
			exportRequest = dm_app.ExportGdtDocumentRequest{
				PresignUrl:          payload.PresignUrl.String(),
				FileName:            payload.Setting.GdtExport.FileName,
				Folder:              payload.Setting.GdtExport.Folder,
				Host:                payload.Setting.GdtExport.Host,
				Username:            payload.Setting.GdtExport.Username,
				Password:            payload.Setting.GdtExport.Password,
				SourceType:          dm_app.SourceTypeEnum(payload.Setting.GdtExport.SourceType),
				ExternalAppFilePath: payload.Setting.GdtExport.ExternalAppFilePath,
			}
		case document_setting_common.DocumentSettingType_LdtExport:
			exportRequest = dm_app.ExportGdtDocumentRequest{
				PresignUrl:          payload.PresignUrl.String(),
				FileName:            payload.LdtExportFileName,
				Folder:              payload.Setting.LdtExport.Folder,
				Host:                payload.Setting.LdtExport.Host,
				Username:            payload.Setting.LdtExport.Username,
				Password:            payload.Setting.LdtExport.Password,
				SourceType:          dm_app.SourceTypeEnum(payload.Setting.LdtExport.SourceType),
				ExternalAppFilePath: nil,
			}
		}

		err = dmApp.ExportGdtDocument(payload.DmContext, exportRequest)

		if err != nil {
			s.handleExportError(ctxClone, payload.Entity, err, "failed to export gdt document")
		} else {
			s.handleExportSuccess(ctxClone, payload.Entity)
		}
	}()
}

func (s *DocumentManagementService) handleExportError(ctx *titan.Context, entity *document_management_repo.DocumentManagementEntity, err error, logMessage string) {
	ctx.Logger().Error(logMessage, "error", err)
	_ = s.socketNotifier.NotifyCareProviderGdtExportResult(ctx, &api.EventGdtExportResult{
		Result: false,
		Error:  err.Error(),
	})
	_, updateErr := s.documentManagementRepo.UpdateStatusById(ctx, *entity.Id, common.DocumentManagementStatus_Failed)
	if updateErr != nil {
		ctx.Logger().Error("failed to update document management status", "error", updateErr)
	}
	_ = s.socketNotifier.NotifyCareProviderDocumentManagementChange(ctx, &api.EventDocumentManagementChange{})
}

func (s *DocumentManagementService) handleExportSuccess(ctx *titan.Context, entity *document_management_repo.DocumentManagementEntity) {
	_, err := s.documentManagementRepo.UpdateStatusById(ctx, *entity.Id, common.DocumentManagementStatus_Completed)
	if err != nil {
		ctx.Logger().Error("failed to update document management status", "error", err)
		return
	}

	_ = s.socketNotifier.NotifyCareProviderGdtExportResult(ctx, &api.EventGdtExportResult{
		Result: true,
	})
	_ = s.socketNotifier.NotifyCareProviderDocumentManagementChange(ctx, &api.EventDocumentManagementChange{})
}

func (s *DocumentManagementService) ProcessImportGdtDocuments(ctx *titan.Context) error {
	total, err := s.documentSettingRepo.CountGdtImportSetting(ctx)
	if err != nil {
		return errors.WithMessage(err, "failed to count gdt import setting")
	}

	if total == 0 {
		ctx.Logger().Warn("no gdt import settings found, skip processing")
		return nil
	}

	ctx.Logger().Info("processing gdt import documents", "total_settings", total)

	err = s.processBatchedImports(ctx, int(total))
	if err != nil {
		ctx.Logger().Error("failed to process batched imports", "error", err)
		return err
	}

	return s.socketNotifier.NotifyCareProviderDocumentManagementChange(ctx, &api.EventDocumentManagementChange{})
}

// processBatchedImports handles the concurrent processing of GDT import settings in batches
func (s *DocumentManagementService) processBatchedImports(ctx *titan.Context, total int) error {
	totalBatches := (total + batchSize - 1) / batchSize

	pool := function.NewPool[syncDocTask, *struct{}](
		function.WithContext(ctx),
		function.WithSize(maxConcurrency),
	).WithFunc(func(task syncDocTask) (*struct{}, error) {
		return s.processImportBatch(ctx, task)
	})

	tasks := createBatchTasks(totalBatches)
	ctx.Logger().Info(
		"starting batch processing",
		"total_batches", totalBatches,
		"batch_size", batchSize,
		"max_concurrency", maxConcurrency,
		"tasks_size", len(tasks),
	)
	err := pool.Process(tasks, nil)
	if err != nil {
		ctx.Logger().Error("batch processing failed", "error", err)
		return errors.WithMessage(err, "failed to process import batches")
	}

	return nil
}

// processImportBatch processes a single batch of GDT import settings
func (s *DocumentManagementService) processImportBatch(ctx *titan.Context, task syncDocTask) (*struct{}, error) {
	settings, err := s.getGdtImportSettings(ctx, task)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get gdt import settings for batch")
	}

	if len(settings) == 0 {
		return nil, nil
	}

	for _, setting := range settings {
		s.processIndividualImportSetting(ctx, setting)
	}
	return nil, nil
}

// createBatchTasks generates the batch tasks for processing
func createBatchTasks(totalBatches int) []syncDocTask {
	tasks := make([]syncDocTask, 0, totalBatches)
	for batchNum := 1; batchNum <= totalBatches; batchNum++ {
		tasks = append(tasks, syncDocTask{
			batch:     batchNum,
			batchSize: batchSize,
		})
	}
	return tasks
}

// getGdtImportSettings retrieves GDT import settings for a specific batch
func (s *DocumentManagementService) getGdtImportSettings(ctx *titan.Context, task syncDocTask) ([]document_setting_repo.DocumentSettingEntity, error) {
	settings, _, err := s.documentSettingRepo.GetGdtImportSettings(
		ctx,
		int64(task.batch),
		int64(task.batchSize),
	)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get gdt import settings")
	}
	return settings, nil
}

// processIndividualImportSetting processes a single GDT import setting
func (s *DocumentManagementService) processIndividualImportSetting(ctx *titan.Context, setting document_setting_repo.DocumentSettingEntity) {
	clonedCtx := ctx.Clone()

	settingId := "unknown"
	if setting.Id != nil {
		settingId = setting.Id.String()
	}

	var err error
	if hasSpecificFileName(setting) {
		err = s.importGdtDocument(clonedCtx, setting)
		if err != nil {
			clonedCtx.Logger().Error("failed to import gdt document", "gdt_error", err, "setting_id", settingId)
		}
	} else {
		err = s.importGdtDocumentWithMultipleFiles(clonedCtx, setting)
		if err != nil {
			clonedCtx.Logger().Error("failed to import gdt document with multiple files", "gdt_error", err, "setting_id", settingId)
		}
	}
}

// hasSpecificFileName checks if the GDT import setting has a specific filename configured
func hasSpecificFileName(setting document_setting_repo.DocumentSettingEntity) bool {
	return util.GetPointerValue(setting.GdtImport.FileName) != ""
}

func (s *DocumentManagementService) importGdtDocument(ctx *titan.Context, setting document_setting_repo.DocumentSettingEntity) error {
	// clone context
	dmContext := cloneDmContext(ctx, setting.GdtImport.DeviceId.String())

	dmApp, err := s.companionService.GetDmApp(dmContext)
	if err != nil {
		return errors.WithMessage(err, "failed to get dm app")
	}

	// get filepath from gdt import setting to check
	filePath := path.Join(setting.GdtImport.Folder, util.GetPointerValue(setting.GdtImport.FileName))

	// call dm app to get modify time and gdt data
	// start := time.Now()
	res, err := dmApp.CheckGdtImportDocument(dmContext, dm_app.CheckGdtImportDocumentRequest{
		Filename:          util.GetPointerValue(setting.GdtImport.FileName),
		Folder:            setting.GdtImport.Folder,
		Host:              setting.GdtImport.Host,
		Username:          setting.GdtImport.Username,
		Password:          setting.GdtImport.Password,
		SourceType:        dm_app.SourceTypeEnum(setting.GdtImport.SourceType),
		LastModifyTime:    util.GetPointerValue(setting.GdtImport.ModTime),
		CharacterEncoding: dm_app.CharacterEncoding(setting.GdtImport.CharacterEncoding),
	})
	// end := time.Since(start)
	// ctx.Logger().Info("check gdt import document", "time", end)
	if err != nil {
		return errors.WithMessage(err, "failed to check gdt import document file")
	}

	// file not changed => do nothing
	if !res.IsChanged {
		ctx.Logger().Info("gdt import document not changed", "file", filePath)
		return nil
	}

	// file changed, get gdt file content => import to db
	var v gdt.TestDataTransfer
	data, err := processGDTDocumentContent(res.GdtDocumentContent, &v)
	if err != nil {
		return err
	}
	var p *patient.PatientProfile
	patientNumber, err := strconv.Atoi(v.PatientNumber)
	if err != nil {
		ctx.Logger().Error("failed to convert patient number to int", "error", err)
	} else {
		// get patient information
		p, err = s.patientProfileRepo.FindByPatientNumber(ctx, patientNumber)
		if err != nil || p == nil {
			ctx.Logger().Error("failed to find patient profile by patient number", "error", err)
		}
	}

	// update last modify time
	setting.GdtImport.ModTime = util.NewPointer(res.ModifyTime)
	_, err = s.documentSettingRepo.UpdateGdtImportSetting(ctx, *setting.Id, *setting.GdtImport)
	if err != nil {
		return errors.WithMessage(err, "failed to update modify time for gdt import setting")
	}

	// create gdt import document management
	documentType, err := s.documentTypeService.GetOrCreateDocumentTypeByName(ctx, document_type_repo.GdtImport)
	if err != nil {
		return errors.WithMessage(err, "failed to get or create document type")
	}
	var patientId *uuid.UUID
	if p != nil {
		patientId = p.Id
	}
	entity, err := s.documentManagementRepo.Create(ctx, common.DocumentManagementModel{
		DocumentName:      filePath,
		DocumentType:      documentType,
		Status:            common.DocumentManagementStatus_New,
		PatientId:         patientId,
		DocumentSettingId: setting.Id,
		ImportedDate:      util.NowUnixMillis(ctx),
	})
	if err != nil {
		return errors.WithMessage(err, "failed to create document management")
	}

	// mock archives
	// mockArchiveFile, _ := os.Open("/Users/<USER>/Desktop/dump/mock.txt")
	// defer mockArchiveFile.Close()
	// scanner := bufio.NewScanner(mockArchiveFile)
	// for scanner.Scan() {
	// 	line := scanner.Text()
	// 	v.ArchiveFiles = append(v.ArchiveFiles, gdt.ArchiveFile{
	// 		FileReference: line,
	// 	})
	// }

	// get archive files info (pdf) to minio
	bucketName := s.mvzAppConfig.MinioClientConfig.BucketDmCompanion
	archivedFiles := make([]*patient_encounter.ArchiveFile, 0)
	archivedUploadInfos := make([]dm_app.FileUploadInfo, 0)

	for _, file := range v.ArchiveFiles {
		filePath := file.FileReference
		if setting.GdtImport.SourceType == document_setting_common.SourceTypeEnum_SMB {
			// filerReference use absolute path, need to extract to relative path
			subPath, ok := extractSmbSubPath(setting.GdtImport.Folder, file.FileReference)
			if !ok {
				continue
			}
			filePath = subPath
		}
		objName := getObjectPath(ctx.UserInfo().CareProviderUUID().String(), filePath)
		putURL, err := s.minioClient.PresignedPutObject(ctx, bucketName, objName, 15*time.Minute)
		if err != nil {
			return errors.WithMessage(err, "failed to get presigned for archive file")
		}
		archivedFiles = append(archivedFiles, &patient_encounter.ArchiveFile{
			FileName:   file_util.GetFileName(filePath),
			ObjectName: objName,
		})
		archivedUploadInfos = append(archivedUploadInfos, dm_app.FileUploadInfo{
			PresignedUrl: putURL.String(),
			FilePath:     filePath,
		})
	}

	// upload gdt data to minio
	minioPath := entity.GetFileName(*ctx.UserInfo().CareProviderUUID())
	object := strings.NewReader(res.GdtDocumentContent)
	_, err = s.minioClient.PutObject(ctx, bucketName, minioPath, object, object.Size(), minio.PutObjectOptions{})
	// failed to upload gdt data to minio
	if err != nil {
		_ = s.socketNotifier.NotifyCareProviderGdtImportResult(ctx, &api.EventGdtImportResult{
			Result: false,
			Error:  err.Error(),
			Type:   common.DocumentNotificationType_GDT,
		})
		_, errStatus := s.documentManagementRepo.UpdateStatusById(ctx, *entity.Id, common.DocumentManagementStatus_Failed)
		if errStatus != nil {
			ctx.Logger().Error("failed to update document management status", "error", errStatus)
			return errStatus
		}
		return err
	}

	// success to upload gdt data to minio => remove gdt document from folder
	err = dmApp.RemoveGdtDocument(dmContext, dm_app.RemoveGdtDocumentRequest{
		Folder:     setting.GdtImport.Folder,
		Filename:   util.GetPointerValue(setting.GdtImport.FileName),
		Host:       setting.GdtImport.Host,
		Username:   setting.GdtImport.Username,
		Password:   setting.GdtImport.Password,
		SourceType: dm_app.SourceTypeEnum(setting.GdtImport.SourceType),
	})
	if err != nil {
		ctx.Logger().Error("failed to remove gdt document", "error", err)
	}

	// upload archived files to minio
	if len(archivedUploadInfos) > 0 {
		err = dmApp.UploadFiles(dmContext, dm_app.UploadFilesRequest{
			ListFileUploadInfo: archivedUploadInfos,
			Folder:             setting.GdtImport.Folder,
			Host:               setting.GdtImport.Host,
			Username:           setting.GdtImport.Username,
			Password:           setting.GdtImport.Password,
			SourceType:         dm_app.SourceTypeEnum(setting.GdtImport.SourceType),
			DeleteAfterUpload:  true,
		})
		if err != nil {
			return err
		}
	}

	// handle import success
	ctxClone := ctx.Clone()
	go func() {
		defer func() {
			if r := recover(); r != nil {
				errMsg := fmt.Sprintf("panic: %v\n\n%s", r, string(debug.Stack()))
				ctxClone.Logger().Error(errMsg)
			}
		}()
		// update document management status to completed
		_, err := s.documentManagementRepo.UpdateStatusById(ctxClone, *entity.Id, common.DocumentManagementStatus_Completed)
		if err != nil {
			ctxClone.Logger().Error("failed to update document management status", "error", err)
			return
		}
		// create gdt import timeline
		entity := timeline_repo.TimelineEntity[patient_encounter.EncounterGDT]{
			Id:        util.NewUUID(),
			PatientId: util.GetPointerValue(patientId),
			Payload: patient_encounter.EncounterGDT{
				FileName:             file_util.GetFileName(util.GetPointerValue(setting.GdtImport.FileName)),
				BucketName:           bucketName,
				FilePath:             minioPath,
				Note:                 *data,
				GdtImportSettingId:   setting.Id,
				GdtImportSettingName: setting.GdtImport.Name,
				DocumentManagementId: entity.Id,
				Command:              setting.GdtImport.TimelineType,
				ArchiveFiles:         archivedFiles,
				LabOrderId:           v.LabOrderId,
			},
		}
		if p != nil && p.PatientInfo != nil && p.PatientInfo.PatientId != nil {
			entity.PatientId = *p.PatientInfo.PatientId
		}
		_, err = s.gdtTimelineService.Create(ctxClone, entity)
		if err != nil {
			ctxClone.Logger().Error("failed to create gdt import timeline", "error", err)
			return
		}
		if p != nil && p.PatientInfo != nil {
			_ = s.socketNotifier.NotifyCareProviderGdtImportResult(ctxClone, &api.EventGdtImportResult{
				Result:      true,
				PatientName: p.PatientInfo.PersonalInfo.GetFullName(),
				Type:        common.DocumentNotificationType_GDT,
			})
		}
	}()
	return err
}

func (s *DocumentManagementService) importGdtDocumentWithMultipleFiles(ctx *titan.Context, setting document_setting_repo.DocumentSettingEntity) error {
	dmContext := cloneDmContext(ctx, setting.GdtImport.DeviceId.String())

	dmApp, err := s.companionService.GetDmApp(dmContext)
	if err != nil {
		return errors.WithMessage(err, "failed to get dm app")
	}

	err = dmApp.HandleGdtImportDocument(dmContext, dm_app.HandleDocumentRequest{
		Folder:            setting.GdtImport.Folder,
		Host:              setting.GdtImport.Host,
		Username:          setting.GdtImport.Username,
		Password:          setting.GdtImport.Password,
		SourceType:        dm_app.SourceTypeEnum(setting.GdtImport.SourceType),
		SettingId:         *setting.Id,
		FileExt:           ".gdt",
		CharacterEncoding: dm_app.CharacterEncoding(setting.GdtImport.CharacterEncoding),
	})
	return err
}

func (s *DocumentManagementService) HandleDmEvent(ctx *titan.Context, request dm_app.EventMessage) error {
	if !isValidGdtImportEvent(request) {
		return errors.New("invalid gdt import event")
	}

	gdtImportData, err := parseGdtImportData(request.Data)
	if err != nil {
		return errors.WithMessage(err, "failed to parse gdt import data")
	}

	ctx.Logger().Info("received gdt import data", "file_path", gdtImportData.FilePath)

	setting, err := s.getAndValidateDocumentSetting(ctx, gdtImportData.SettingId)
	if err != nil {
		return err
	}

	return s.ProcessDocumentImport(ctx, setting, gdtImportData)
}

func isValidGdtImportEvent(request dm_app.EventMessage) bool {
	return request.Data != "" && request.Topic == "import_gdt"
}

func parseGdtImportData(data string) (*dm_app.GdtImportData, error) {
	var gdtImportData dm_app.GdtImportData
	err := json.Unmarshal([]byte(data), &gdtImportData)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to unmarshal gdt import data")
	}
	return &gdtImportData, nil
}

func (s *DocumentManagementService) getAndValidateDocumentSetting(ctx *titan.Context, settingId uuid.UUID) (*document_setting_repo.DocumentSettingEntity, error) {
	setting, err := s.documentSettingRepo.GetById(ctx, settingId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get gdt import setting")
	}
	if setting == nil {
		return nil, errors.New("gdt import setting not found")
	}
	if setting.DocumentSettingType != document_setting_common.DocumentSettingType_GdtImport && setting.DocumentSettingType != document_setting_common.DocumentSettingType_LdtImport {
		return nil, errors.New("gdt import setting type is not gdt import or ldt import")
	}
	if setting.DocumentSettingType == document_setting_common.DocumentSettingType_LdtImport {
		if setting.LdtImport == nil {
			return nil, errors.New("ldt import setting is nil")
		}
	}
	if setting.DocumentSettingType == document_setting_common.DocumentSettingType_GdtImport {
		if setting.GdtImport == nil {
			return nil, errors.New("gdt import setting is nil")
		}
	}
	return setting, nil
}

func (s *DocumentManagementService) ProcessDocumentImport(ctx *titan.Context, setting *document_setting_repo.DocumentSettingEntity, gdtImportData *dm_app.GdtImportData) error {
	// Handle LDT content differently - LDT doesn't process GDT content
	if setting.DocumentSettingType == document_setting_common.DocumentSettingType_LdtImport {
		order, result, err := parseLdtContent(ctx, gdtImportData)
		if err != nil {
			return errors.WithMessage(err, "failed to parse LDT content")
		}
		return s.ProcessLdtImport(ctx, setting, order, result, gdtImportData)
	}

	filePath := resolveGDTFilePath(setting, gdtImportData)

	gdtContent, err := s.processGdtContent(ctx, setting, gdtImportData, filePath)
	if err != nil {
		return errors.WithMessage(err, "failed to process GDT content")
	}
	var patient *patient.PatientProfile
	patientNumber, err := strconv.Atoi(gdtContent.PatientNumber)
	if err != nil {
		ctx.Logger().Error("failed to convert patient number to int", "error", err)
	} else {
		patient, err = s.findPatientByNumber(ctx, patientNumber)
		if err != nil {
			ctx.Logger().Error("failed to find patient profile by patient number", "error", err)
		}
	}

	entity, err := s.createGdtDocumentEntity(ctx, setting, patient, filePath)
	if err != nil {
		return err
	}

	if err := s.uploadDocumentToMinio(ctx, entity, gdtImportData.Content); err != nil {
		return s.handleUploadFailure(ctx, entity, err)
	}

	if err := s.handleDocumentFileRemoval(ctx, setting, gdtImportData); err != nil {
		ctx.Logger().Error("failed to remove gdt document", "error", err)
	}

	_, err = s.processArchiveFiles(ctx, setting, gdtContent.ArchiveFiles)
	if err != nil {
		return errors.WithMessage(err, "failed to process archive files")
	}

	return s.completeImportGDTProcess(ctx, entity, patient, setting, gdtContent)
}

func resolveGDTFilePath(setting *document_setting_repo.DocumentSettingEntity, gdtImportData *dm_app.GdtImportData) string {
	filePath := gdtImportData.FilePath
	if setting.GdtImport.SourceType == document_setting_common.SourceTypeEnum_SMB {
		filePath = path.Join(setting.GdtImport.Folder, gdtImportData.FilePath)
	}
	return filePath
}

func (s *DocumentManagementService) processGdtContent(ctx *titan.Context, setting *document_setting_repo.DocumentSettingEntity, gdtImportData *dm_app.GdtImportData, filePath string) (*gdt.TestDataTransfer, error) {
	var gdtContent gdt.TestDataTransfer
	_, err := processGDTDocumentContent(gdtImportData.Content, &gdtContent)
	if err != nil {
		s.handleGdtProcessingError(ctx, setting, gdtImportData, filePath)
		return nil, errors.WithMessage(err, "failed to process gdt document content of file "+gdtImportData.FilePath)
	}
	return &gdtContent, nil
}

func (s *DocumentManagementService) handleGdtProcessingError(ctx *titan.Context, setting *document_setting_repo.DocumentSettingEntity, gdtImportData *dm_app.GdtImportData, filePath string) {
	isExisted, _ := s.documentManagementRepo.IsDocumentExisted(ctx, filePath, *setting.Id, gdtImportData.ModifyTime)
	if !isExisted {
		documentType, _ := s.documentTypeService.GetOrCreateDocumentTypeByName(ctx, document_type_repo.GdtImport)
		if documentType != nil {
			_, _ = s.documentManagementRepo.Create(ctx, common.DocumentManagementModel{
				DocumentName:      filePath,
				DocumentType:      documentType,
				Status:            common.DocumentManagementStatus_Failed,
				DocumentSettingId: setting.Id,
				ImportedDate:      util.NowUnixMillis(ctx),
				GdtImportModTime:  gdtImportData.ModifyTime,
			})
		}
	}
}

func (s *DocumentManagementService) findPatientByNumber(ctx *titan.Context, patientNumber int) (*patient.PatientProfile, error) {
	return s.patientProfileRepo.FindByPatientNumber(ctx, patientNumber)
}

func (s *DocumentManagementService) createGdtDocumentEntity(ctx *titan.Context, setting *document_setting_repo.DocumentSettingEntity, patient *patient.PatientProfile, filePath string) (*document_management_repo.DocumentManagementEntity, error) {
	settingType := document_type_repo.GdtImport
	if setting.DocumentSettingType == document_setting_common.DocumentSettingType_LdtImport {
		settingType = document_type_repo.LdtImport
	}
	documentType, err := s.documentTypeService.GetOrCreateDocumentTypeByName(ctx, settingType)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get or create document type")
	}

	var patientId *uuid.UUID
	if patient != nil {
		patientId = patient.Id
	}

	entity, err := s.documentManagementRepo.Create(ctx, common.DocumentManagementModel{
		DocumentName:      filePath,
		DocumentType:      documentType,
		Status:            common.DocumentManagementStatus_New,
		PatientId:         patientId,
		DocumentSettingId: setting.Id,
		ImportedDate:      util.NowUnixMillis(ctx),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to create document management")
	}

	return entity, nil
}

func (s *DocumentManagementService) uploadDocumentToMinio(ctx *titan.Context, entity *document_management_repo.DocumentManagementEntity, content string) error {
	bucketName := s.mvzAppConfig.MinioClientConfig.BucketDmCompanion
	minioPath := entity.GetFileName(*ctx.UserInfo().CareProviderUUID())
	object := strings.NewReader(content)

	_, err := s.minioClient.PutObject(ctx, bucketName, minioPath, object, object.Size(), minio.PutObjectOptions{})
	if err != nil {
		return errors.WithMessage(err, "failed to upload document data to minio")
	}

	return nil
}

func (s *DocumentManagementService) handleUploadFailure(ctx *titan.Context, entity *document_management_repo.DocumentManagementEntity, uploadErr error) error {
	_, errStatus := s.documentManagementRepo.UpdateStatusById(ctx, *entity.Id, common.DocumentManagementStatus_Failed)
	if errStatus != nil {
		return errors.WithMessage(errStatus, "failed to update document management status")
	}
	return uploadErr
}

func (s *DocumentManagementService) handleDocumentFileRemoval(ctx *titan.Context, setting *document_setting_repo.DocumentSettingEntity, gdtImportData *dm_app.GdtImportData) error {
	deviceId, err := getDeviceIdFromSetting(setting)
	if err != nil {
		return err
	}

	dmContext := cloneDmContext(ctx, deviceId)
	dmApp, err := s.companionService.GetDmApp(dmContext)
	if err != nil {
		return errors.WithMessage(err, "failed to get dm app")
	}

	folder := resolveRemovalFolder(setting)
	host, username, password, sourceType := getConnectionSettings(setting)

	return dmApp.RemoveGdtDocument(dmContext, dm_app.RemoveGdtDocumentRequest{
		Folder:     folder,
		Filename:   gdtImportData.FilePath,
		Host:       host,
		Username:   username,
		Password:   password,
		SourceType: sourceType,
	})
}

func getDeviceIdFromSetting(setting *document_setting_repo.DocumentSettingEntity) (string, error) {
	switch setting.DocumentSettingType {
	case document_setting_common.DocumentSettingType_GdtImport:
		return setting.GdtImport.DeviceId.String(), nil
	case document_setting_common.DocumentSettingType_LdtImport:
		return setting.LdtImport.DeviceId.String(), nil
	default:
		return "", errors.New("unsupported document setting type")
	}
}

func getConnectionSettings(setting *document_setting_repo.DocumentSettingEntity) (host, username, password *string, sourceType dm_app.SourceTypeEnum) {
	switch setting.DocumentSettingType {
	case document_setting_common.DocumentSettingType_GdtImport:
		return setting.GdtImport.Host, setting.GdtImport.Username, setting.GdtImport.Password, dm_app.SourceTypeEnum(setting.GdtImport.SourceType)
	case document_setting_common.DocumentSettingType_LdtImport:
		return setting.LdtImport.Host, setting.LdtImport.Username, setting.LdtImport.Password, dm_app.SourceTypeEnum(setting.LdtImport.SourceType)
	default:
		return nil, nil, nil, dm_app.SourceTypeEnum_LOCAL
	}
}

func resolveRemovalFolder(setting *document_setting_repo.DocumentSettingEntity) string {
	var folder string
	var sourceType document_setting_common.SourceTypeEnum

	switch setting.DocumentSettingType {
	case document_setting_common.DocumentSettingType_GdtImport:
		folder = setting.GdtImport.Folder
		sourceType = setting.GdtImport.SourceType
	case document_setting_common.DocumentSettingType_LdtImport:
		folder = setting.LdtImport.Folder
		sourceType = setting.LdtImport.SourceType
	}

	if sourceType == document_setting_common.SourceTypeEnum_LOCAL ||
		sourceType == document_setting_common.SourceTypeEnum_FTP {
		folder = ""
	}
	return folder
}

func (s *DocumentManagementService) processArchiveFiles(ctx *titan.Context, setting *document_setting_repo.DocumentSettingEntity, archiveFiles []gdt.ArchiveFile) ([]*patient_encounter.ArchiveFile, error) {
	if len(archiveFiles) == 0 {
		return nil, nil
	}

	bucketName := s.mvzAppConfig.MinioClientConfig.BucketDmCompanion
	archivedFiles := make([]*patient_encounter.ArchiveFile, 0)
	archivedUploadInfos := make([]dm_app.FileUploadInfo, 0)

	for _, file := range archiveFiles {
		filePath := resolveArchiveFilePath(setting, file.FileReference)
		if filePath == "" {
			continue
		}

		objName := getObjectPath(ctx.UserInfo().CareProviderUUID().String(), filePath)
		putURL, err := s.minioClient.PresignedPutObject(ctx, bucketName, objName, 15*time.Minute)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get presigned for archive file")
		}

		archivedFiles = append(archivedFiles, &patient_encounter.ArchiveFile{
			FileName:   file_util.GetFileName(filePath),
			ObjectName: objName,
		})
		archivedUploadInfos = append(archivedUploadInfos, dm_app.FileUploadInfo{
			PresignedUrl: putURL.String(),
			FilePath:     filePath,
		})
	}

	if len(archivedUploadInfos) > 0 {
		if err := s.uploadArchivedFiles(ctx, setting, archivedUploadInfos); err != nil {
			return nil, err
		}
	}

	return archivedFiles, nil
}

func resolveArchiveFilePath(setting *document_setting_repo.DocumentSettingEntity, fileReference string) string {
	filePath := fileReference

	var sourceType document_setting_common.SourceTypeEnum
	var folder string

	switch setting.DocumentSettingType {
	case document_setting_common.DocumentSettingType_GdtImport:
		sourceType = setting.GdtImport.SourceType
		folder = setting.GdtImport.Folder
	case document_setting_common.DocumentSettingType_LdtImport:
		sourceType = setting.LdtImport.SourceType
		folder = setting.LdtImport.Folder
	}

	if sourceType == document_setting_common.SourceTypeEnum_SMB {
		subPath, ok := extractSmbSubPath(folder, fileReference)
		if !ok {
			return ""
		}
		filePath = subPath
	}
	return filePath
}

func (s *DocumentManagementService) uploadArchivedFiles(ctx *titan.Context, setting *document_setting_repo.DocumentSettingEntity, uploadInfos []dm_app.FileUploadInfo) error {
	deviceId, err := getDeviceIdFromSetting(setting)
	if err != nil {
		return err
	}

	dmContext := cloneDmContext(ctx, deviceId)
	dmApp, err := s.companionService.GetDmApp(dmContext)
	if err != nil {
		return errors.WithMessage(err, "failed to get dm app")
	}

	var folder string
	host, username, password, sourceType := getConnectionSettings(setting)

	switch setting.DocumentSettingType {
	case document_setting_common.DocumentSettingType_GdtImport:
		folder = setting.GdtImport.Folder
	case document_setting_common.DocumentSettingType_LdtImport:
		folder = setting.LdtImport.Folder
	}

	return dmApp.UploadFiles(dmContext, dm_app.UploadFilesRequest{
		ListFileUploadInfo: uploadInfos,
		Folder:             folder,
		Host:               host,
		Username:           username,
		Password:           password,
		SourceType:         sourceType,
		DeleteAfterUpload:  true,
	})
}

func (s *DocumentManagementService) completeImportGDTProcess(
	ctx *titan.Context,
	entity *document_management_repo.DocumentManagementEntity,
	patient *patient.PatientProfile,
	setting *document_setting_repo.DocumentSettingEntity,
	gdtContent *gdt.TestDataTransfer,
) error {
	_, err := s.documentManagementRepo.UpdateStatusById(ctx, *entity.Id, common.DocumentManagementStatus_Completed)
	if err != nil {
		ctx.Logger().Error("failed to update document management status", "error", err)
		return errors.WithMessage(err, "failed to update document management status")
	}

	if err := s.createGdtTimelineEntry(ctx, entity, patient, setting, gdtContent); err != nil {
		ctx.Logger().Error("failed to create gdt import timeline", "error", err)
		return errors.WithMessage(err, "failed to create gdt import timeline")
	}

	if patient != nil && patient.PatientInfo != nil {
		_ = s.socketNotifier.NotifyCareProviderGdtImportResult(ctx, &api.EventGdtImportResult{
			Result:      true,
			PatientName: patient.PatientInfo.PersonalInfo.GetFullName(),
			Type:        common.DocumentNotificationType_GDT,
		})
	}
	return nil
}

func (s *DocumentManagementService) createGdtTimelineEntry(
	ctx *titan.Context,
	entity *document_management_repo.DocumentManagementEntity,
	patient *patient.PatientProfile,
	setting *document_setting_repo.DocumentSettingEntity,
	gdtContent *gdt.TestDataTransfer,
) error {
	if gdtContent == nil {
		return errors.New("gdt content is empty")
	}
	bucketName := s.mvzAppConfig.MinioClientConfig.BucketDmCompanion
	minioPath := entity.GetFileName(*ctx.UserInfo().CareProviderUUID())
	data := gdt.TestDataTransferToTimelineContent(*gdtContent)
	patientId := uuid.Nil
	if patient != nil && patient.Id != nil {
		patientId = *patient.Id // create default patient id nil value for initial timeline
	}

	timelineEntity := timeline_repo.TimelineEntity[patient_encounter.EncounterGDT]{
		Id:        util.NewUUID(),
		PatientId: patientId,
		Payload: patient_encounter.EncounterGDT{
			FileName:             entity.DocumentName,
			BucketName:           bucketName,
			FilePath:             minioPath,
			Note:                 data,
			GdtImportSettingId:   setting.Id,
			GdtImportSettingName: setting.GdtImport.Name,
			DocumentManagementId: entity.Id,
			Command:              setting.GdtImport.TimelineType,
			ArchiveFiles:         []*patient_encounter.ArchiveFile{}, // Initialize empty if not provided
			LabOrderId:           gdtContent.LabOrderId,
		},
	}

	_, err := s.gdtTimelineService.Create(ctx, timelineEntity)
	return errors.WithMessage(err, "failed to create gdt import timeline")
}

func cloneDmContext(ctx *titan.Context, deviceId string) *titan.Context {
	userInfo := ctx.UserInfo()
	cloneUserInfo := pkg_copy.CloneTo[titan.UserInfo](userInfo)
	cloneUserInfo.DeviceId = deviceId
	return ctx.Clone().WithValue(titan.XUserInfo, &cloneUserInfo)
}

func getObjectPath(provider, filePath string) string {
	dir := provider
	extension := filepath.Ext(filePath)
	objectPath := fmt.Sprintf("%s/%s%s", dir, util.NewUUID().String(), extension)
	return objectPath
}

// extract the sub path of the file from the folder
func extractSmbSubPath(folder, filePath string) (string, bool) {
	normalizedPath := strings.ReplaceAll(filePath, "\\", "/")
	normalizedFolder := strings.ReplaceAll(folder, "\\", "/")
	parts := strings.Split(normalizedPath, "/")
	for i, part := range parts {
		if part == normalizedFolder {
			return strings.Join(parts[i+1:], "/"), true
		}
	}
	return "", false
}

func processGDTDocumentContent(content string, v *gdt.TestDataTransfer) (*string, error) {
	fr := field_transfer.NewFieldReader(strings.NewReader(content))
	var fields []field_transfer.Field
	err := fr.ReadField(func(f *field_transfer.Field) {
		fields = append(fields, *f)
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to read field from file")
	}
	if err := fr.Decode(v); err != nil {
		return nil, errors.WithMessage(err, "failed to decode file")
	}
	if v.SentenceID != "6310" {
		return nil, errors.New(fmt.Sprintf("not support this gdt file yet: %s", v.SentenceID))
	}
	data := gdt.TestDataTransferToTimelineContent(*v)
	return &data, nil
}
