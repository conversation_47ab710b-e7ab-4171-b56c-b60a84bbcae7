package service

import (
	"embed"
	"fmt"
	"io"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/document_management"
	util_pkg "git.tutum.dev/medi/tutum/pkg/util"
	"github.com/golang-module/carbon"
	"github.com/thecodingmachine/gotenberg-go-client/v7"
)

const htmlTemplateFile string = "lab_results__portrait.html"

//go:embed lab_results__portrait.html
var htmlFileFS embed.FS

type TemplateData struct {
	Data   any
	IsEven any
}

func IsEven(i int) bool {
	return i%2 == 0
}

type TablePDF struct {
	Header []TableCell
	Rows   []TableRow
}

type TableRow struct {
	Cells []TableCell
}

type TableCell struct {
	ClassName string
	Type      string
	Value     any
}

const (
	CellTypeValue     = "value-only"
	CellTypeValueIcon = "value-icon"
	CellTypeValues    = "value-array"
)

type CellValueIcon struct {
	Value string
	Icon  string
}

func TransformPrintData(input document_management.GetLabResultsResponse) TablePDF {
	// header row
	headerRow := []TableCell{}
	headerRow = append(headerRow,
		TableCell{
			ClassName: "col-lab-param",
			Type:      CellTypeValue,
			Value:     "LABORWERTE",
		}, TableCell{
			ClassName: "col-norm",
			Type:      CellTypeValue,
			Value:     "NORM",
		},
	)

	for _, labResult := range input.LabResults {
		date := util_pkg.ConvertMillisecondsToTime(labResult.Date)
		headerRow = append(headerRow, TableCell{
			ClassName: "col-day",
			Type:      CellTypeValue,
			Value:     carbon.Time2Carbon(date).Format("d.m.Y"),
		})
	}

	rows := []TableRow{}
	for _, labParameter := range input.LabParameters {
		// row
		row := TableRow{
			Cells: []TableCell{},
		}
		row.Cells = append(row.Cells,
			TableCell{
				ClassName: "col-lab-param",
				Type:      CellTypeValue,
				Value:     labParameter.Name,
			},
			TableCell{
				ClassName: "col-norm",
				Type:      CellTypeValue,
				Value: fmt.Sprintf(
					"%s - %s %s",
					labParameter.Min,
					labParameter.Max,
					labParameter.Unit,
				),
			},
		)

		for _, labResult := range input.LabResults {
			cellClassName := "col-day"
			cellValue := ""
			cellType := CellTypeValue
			var icon string
			for _, labResultItem := range labResult.Items {
				if labParameter.Name == labResultItem.Name {
					cellValue = labResultItem.Value
					icon = labResultItem.Icon
				}
			}

			if icon != "" {
				cellClassName += " cell-danger"
				cellValue = fmt.Sprintf("%s (%s)", cellValue, icon)
			}

			row.Cells = append(row.Cells, TableCell{
				ClassName: cellClassName,
				Type:      cellType,
				Value:     cellValue,
			})
		}
		rows = append(rows, row)
	}
	return TablePDF{
		Header: headerRow,
		Rows:   rows,
	}
}

func GeneratePDFFile(
	html2PdfClient *gotenberg.Client,
	htmlPageData string,
	scale float64, isLandscape bool) ([]byte, error) {
	// convert html to pdf
	htmlPage, err := gotenberg.NewDocumentFromString("index.html", htmlPageData)
	if err != nil {
		return nil, fmt.Errorf("ParseTemplate cannot convert html to pdf: %w", err)
	}
	req := gotenberg.NewHTMLRequest(htmlPage)
	req.PaperSize(gotenberg.A4)
	// newMargin := [4]float64{0.34, 0.34, 0.34, 0.34}
	req.Margins(gotenberg.NoMargins)
	req.Scale(scale)
	req.Landscape(isLandscape)
	resp, err := html2PdfClient.Post(req)
	if err != nil {
		return nil, err
	}
	return io.ReadAll(resp.Body)
}
