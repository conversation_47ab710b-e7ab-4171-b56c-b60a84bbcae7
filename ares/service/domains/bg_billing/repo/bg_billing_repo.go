package repo

import (
	"fmt"
	"math"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/bg_billing"
	mongodb "git.tutum.dev/medi/tutum/ares/pkg/repo"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/bg_billing/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const BG_STRING = "BG"

const (
	Field_Patient                  = "patient"
	Field_Doctor                   = "doctor"
	Field_ScheinId                 = "scheinId"
	Field_InvoiceNumber            = "invoiceNumber"
	Field_InvoiceNumber_Count      = "invoiceNumberCount"
	Field_Status                   = "status"
	Field_InvoiceDate              = "invoiceDate"
	Field_InsuranceInfo            = "insuranceinfo"
	Field_Patient_FirstName        = "patient.firstname"
	Field_Patient_LastName         = "patient.lastname"
	Field_Patient_PatientId        = "patient.patientid"
	Field_Doctor_Id                = "doctor.doctorid"
	Field_InsuranceInfo_IkNumber   = "insuranceinfo.iknumber"
	Field_CurrentInvoiceTimelineId = "currentInvoiceTimelineId"
	Field_PaidAmount               = "paidAmount"
	Field_LastDocumentedAt         = "lastDocumentedAt"
	Field_ExcludeFromBilling       = "excludeFromBilling"
)

type BgBillingEntity struct {
	repos.BaseEntity     `bson:",inline"`
	common.BillingRecord `bson:",inline"`
}

func (*BgBillingEntity) GetPatientFieldName() string {
	return Field_Patient_PatientId
}

type BgBillingRepo struct {
	mongodb.Repo[*BgBillingEntity]
}

func NewBgBillingRepo() *BgBillingRepo {
	return &BgBillingRepo{
		mongodb.NewRepo[*BgBillingEntity](mongodb_repo.NewMongoDbIDBClient("tutum_mvz_db", "bg_billing_history", true)),
	}
}

var BgBillingRepoMod = submodule.Make[*BgBillingRepo](func() *BgBillingRepo {
	return NewBgBillingRepo()
})

func (p *BgBillingEntity) ToBgBillingItem() *common.BgBillingItem {
	item := &common.BgBillingItem{
		Id:                 util.GetPointerValue(p.GetId()),
		ScheinId:           p.ScheinId,
		Patient:            p.Patient,
		Doctor:             p.Doctor,
		InvoiceNumber:      p.InvoiceNumber,
		Status:             p.Status,
		InsuranceInfo:      util.GetPointerValue(p.InsuranceInfo),
		PaidAmount:         p.PaidAmount,
		LastDocumentedAt:   p.LastDocumentedAt,
		ExcludeFromBilling: p.ExcludeFromBilling,
		IsImported:         p.IsImported,
	}

	if p.UpdatedAt != nil {
		item.UpdatedAt = p.UpdatedAt.UnixMilli()
	}
	if p.InvoiceDate != nil {
		item.InvoiceDate = *p.InvoiceDate
	}
	if p.CurrentInvoiceTimelineId != nil {
		item.CurrentInvoiceTimelineId = p.CurrentInvoiceTimelineId
	}
	item.CreatedAt = p.CreatedAt.UnixMilli()
	return item
}

func (r *BgBillingRepo) CreateBgBilling(ctx *titan.Context, request *bg_billing.CreateBgBillingRequest) (*BgBillingEntity, error) {
	lastBillingRecord, err := r.FindOne(ctx, bson.M{}, options.FindOne().SetSort(bson.M{Field_InvoiceNumber_Count: -1}))
	if err != nil {
		return nil, err
	}
	if lastBillingRecord != nil {
		request.Item.InvoiceNumberCount = lastBillingRecord.InvoiceNumberCount + 1
	} else {
		request.Item.InvoiceNumberCount = 1
	}
	request.Item.InvoiceNumber = fmt.Sprintf("%s%d%s", BG_STRING, request.Item.InvoiceNumberCount, request.Item.Patient.PatientNumber)
	return r.Create(ctx, &BgBillingEntity{
		BaseEntity: repos.BaseEntity{
			Id:        util.NewUUID(),
			CreatedAt: util.Now(ctx),
			CreatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
		},
		BillingRecord: *request.Item,
	})
}

func (r *BgBillingRepo) GetBgBilling(ctx *titan.Context, request *bg_billing.GetBgBillingsRequest) ([]BgBillingEntity, int64, error) {
	// build search query
	filter := bson.M{
		repos.Field_IsDeleted:    false,
		Field_ExcludeFromBilling: false,
	}
	if request.Search != nil {
		searchQueries := []bson.M{
			{
				Field_InvoiceNumber: primitive.Regex{
					Pattern: *request.Search,
					Options: "i",
				},
			},
			{
				Field_Patient_LastName: primitive.Regex{
					Pattern: *request.Search,
					Options: "i",
				},
			},
			{
				Field_Patient_FirstName: primitive.Regex{
					Pattern: *request.Search,
					Options: "i",
				},
			},
		}
		filter["$or"] = searchQueries
	}

	if request.Filter != nil {
		if len(request.Filter.Status) > 0 {
			filter[Field_Status] = bson.M{
				"$in": request.Filter.Status,
			}
		}
		if request.Filter.MinPrice != 0 || request.Filter.MaxPrice != 0 {
			priceFilter := bson.M{}
			if request.Filter.MinPrice != 0 {
				priceFilter["$gte"] = request.Filter.MinPrice
			}
			if request.Filter.MaxPrice != 0 {
				priceFilter["$lte"] = request.Filter.MaxPrice
			} else {
				priceFilter["$lte"] = math.MaxFloat64
			}

			filter[Field_PaidAmount] = priceFilter
		}
		if len(request.Filter.DoctorIds) > 0 {
			filter[Field_Doctor_Id] = bson.M{
				"$in": request.Filter.DoctorIds,
			}
		}
		if len(request.Filter.IkNumber) > 0 {
			filter[Field_InsuranceInfo_IkNumber] = bson.M{
				"$in": request.Filter.IkNumber,
			}
		}
	}
	// build filter and find options
	findOptions := &options.FindOptions{
		Sort: bson.M{
			repos.Field_CreatedAt: -1,
		},
	}

	// query and return
	entities, err := r.Find(ctx, filter, findOptions)
	if err != nil {
		return nil, 0, err
	}
	total, err := r.Count(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	return slice.ToValueType(entities), total, nil
}

func (r *BgBillingRepo) GetByScheinId(ctx *titan.Context, scheinId uuid.UUID) (*BgBillingEntity, error) {
	filter := bson.M{
		Field_ScheinId:        scheinId,
		repos.Field_IsDeleted: false,
	}
	return r.FindOne(ctx, filter, options.FindOne().SetSort(bson.M{repos.Field_CreatedAt: -1}))
}

func (r *BgBillingRepo) UpdateBgBillingAsUnpaid(ctx *titan.Context, bgBillingId, currentInvoiceTimelineId uuid.UUID) (*BgBillingEntity, error) {
	filter := bson.M{
		repos.Field_Id: bgBillingId,
	}
	updated := bson.M{
		"$set": bson.M{
			Field_Status:                   common.BillingStatus_UnPaid,
			Field_CurrentInvoiceTimelineId: currentInvoiceTimelineId,
			repos.Field_UpdatedAt:          util.Now(ctx),
			repos.Field_UpdatedBy:          util.GetPointerValue(ctx.UserInfo().UserUUID()),
		},
	}
	return r.FindOneAndUpdate(ctx, filter, updated, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (r *BgBillingRepo) UpdateFieldLastDocumentedAt(ctx *titan.Context, bgBillingId uuid.UUID) (*BgBillingEntity, error) {
	filter := bson.M{
		repos.Field_Id: bgBillingId,
	}
	updated := bson.M{
		"$set": bson.M{
			repos.Field_UpdatedAt:  util.Now(ctx),
			repos.Field_UpdatedBy:  util.GetPointerValue(ctx.UserInfo().UserUUID()),
			Field_LastDocumentedAt: util.NowUnixMillis(ctx),
		},
	}
	return r.FindOneAndUpdate(ctx, filter, updated, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (r *BgBillingRepo) UpdateFieldExcludeFromBilling(ctx *titan.Context, scheinId uuid.UUID, excludeFromBilling bool) (*BgBillingEntity, error) {
	filter := bson.M{
		Field_ScheinId: scheinId,
	}
	updated := bson.M{
		"$set": bson.M{
			repos.Field_UpdatedAt:    util.Now(ctx),
			repos.Field_UpdatedBy:    util.GetPointerValue(ctx.UserInfo().UserUUID()),
			Field_ExcludeFromBilling: excludeFromBilling,
		},
	}
	return r.FindOneAndUpdate(ctx, filter, updated, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (r *BgBillingRepo) DeleteBgBillingByScheinId(ctx *titan.Context, scheinId uuid.UUID) ([]BgBillingEntity, error) {
	filter := bson.M{
		Field_ScheinId: scheinId,
		Field_Status: bson.M{
			"$ne": common.BillingStatus_Cancel,
		},
	}
	bgBillingHistories, err := r.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	if len(bgBillingHistories) == 0 {
		return nil, nil
	}
	for i := range bgBillingHistories {
		bgBillingHistories[i].IsDeleted = true
		bgBillingHistories[i].UpdatedAt = util.NewPointer(util.Now(ctx))
		bgBillingHistories[i].UpdatedBy = ctx.UserInfo().UserUUID()
	}
	return slice.ToValueTypeWithError(r.UpdateMany(ctx, bgBillingHistories))
}

func (r *BgBillingRepo) CreateManyBgBillingWithRawData(ctx *titan.Context, request []bg_billing.CreateBgBillingRequest) ([]BgBillingEntity, error) {
	createdAt := util.Now(ctx)
	createdBy := util.GetPointerValue(ctx.UserInfo().UserUUID())
	tmp := slice.Map(request, func(item bg_billing.CreateBgBillingRequest) *BgBillingEntity {
		return &BgBillingEntity{
			BaseEntity: repos.BaseEntity{
				Id:        util.NewUUID(),
				CreatedAt: createdAt,
				CreatedBy: createdBy,
			},
			BillingRecord: *item.Item,
		}
	})

	return slice.ToValueTypeWithError(r.CreateMany(ctx, tmp))
}

func (r *BgBillingRepo) UpdateBillingAsPaid(ctx *titan.Context, request common.UpdateBgBillingFromInvoiceRequest) (*BgBillingEntity, error) {
	entity, err := r.FindById(ctx, request.BillingId)
	if err != nil {
		return nil, fmt.Errorf("failed to find bg billing: %w", err)
	}
	if entity == nil {
		return nil, nil
	}
	filter := bson.M{
		repos.Field_Id: request.BillingId,
	}
	updated := bson.M{
		"$set": bson.M{
			Field_Status:                   common.BillingStatus_Paid,
			Field_CurrentInvoiceTimelineId: request.InvoiceTimelineId,
			repos.Field_UpdatedAt:          util.Now(ctx),
			repos.Field_UpdatedBy:          util.GetPointerValue(ctx.UserInfo().UserUUID()),
		},
	}
	return r.FindOneAndUpdate(ctx, filter, updated, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (r *BgBillingRepo) UpdateBgBillingAsCancelled(ctx *titan.Context, billingId, currentInvoiceTimelineId uuid.UUID) (*BgBillingEntity, error) {
	filter := bson.M{
		repos.Field_Id: billingId,
	}
	updated := bson.M{
		"$set": bson.M{
			Field_Status:                   common.BillingStatus_Cancel,
			Field_CurrentInvoiceTimelineId: currentInvoiceTimelineId,
			repos.Field_UpdatedAt:          util.Now(ctx),
			repos.Field_UpdatedBy:          util.GetPointerValue(ctx.UserInfo().UserUUID()),
		},
	}
	return r.FindOneAndUpdate(ctx, filter, updated, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (r *BgBillingRepo) UpdateBgBillingFromInvoice(ctx *titan.Context, request common.UpdateBgBillingFromInvoiceRequest) (*BgBillingEntity, error) {
	filter := bson.M{
		repos.Field_Id: request.BillingId,
	}
	data := bson.M{
		Field_Status:                   request.Status,
		Field_PaidAmount:               request.FeeAmount,
		Field_CurrentInvoiceTimelineId: request.InvoiceTimelineId,
		repos.Field_UpdatedAt:          util.Now(ctx),
		repos.Field_UpdatedBy:          util.GetPointerValue(ctx.UserInfo().UserUUID()),
	}
	if request.Status == common.BillingStatus_UnPaid {
		data[Field_InvoiceDate] = util.NewPointer(util.NowUnixMillis(ctx))
	}
	updated := bson.M{
		"$set": data,
	}
	return r.FindOneAndUpdate(ctx, filter, updated, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (r *BgBillingRepo) UpdateBgBillingFee(ctx *titan.Context, billingId uuid.UUID, feeAmount float64) (*BgBillingEntity, error) {
	filter := bson.M{
		repos.Field_Id: billingId,
	}
	updated := bson.M{
		"$set": bson.M{
			Field_PaidAmount:      feeAmount,
			repos.Field_UpdatedAt: util.Now(ctx),
			repos.Field_UpdatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
		},
	}
	option := options.FindOneAndUpdate().SetReturnDocument(options.After)
	return r.FindOneAndUpdate(ctx, filter, updated, option)
}

func (r *BgBillingRepo) UpdateBgBillingPatientInfo(ctx *titan.Context, patientId uuid.UUID, patient common.Patient) ([]BgBillingEntity, error) {
	filter := bson.M{
		Field_Patient_PatientId: patientId,
	}
	updated := bson.M{
		"$set": bson.M{
			Field_Patient:         patient,
			repos.Field_UpdatedAt: util.Now(ctx),
			repos.Field_UpdatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
		},
	}
	var entities []BgBillingEntity
	_, err := r.IDBClient.UpdateMany(ctx, filter, updated, &entities)
	if err != nil {
		return nil, err
	}
	return entities, nil
}

func (r *BgBillingRepo) GetListDoctor(ctx *titan.Context) ([]*common.Doctor, error) {
	bgBillings, _, err := r.GetBgBilling(ctx, &bg_billing.GetBgBillingsRequest{})
	if err != nil {
		return nil, fmt.Errorf("failed to get bg billings: %w", err)
	}
	var doctors []*common.Doctor
	doctorMap := make(map[string]struct{})
	for _, record := range bgBillings {
		doctorIdStr := record.Doctor.DoctorId.String()
		if _, exists := doctorMap[doctorIdStr]; !exists {
			doctors = append(doctors, &record.Doctor)
			doctorMap[doctorIdStr] = struct{}{}
		}
	}
	return doctors, nil
}

func (r *BgBillingRepo) GetListInsurance(ctx *titan.Context) ([]*patient_profile_common.InsuranceInfo, error) {
	bgBillings, _, err := r.GetBgBilling(ctx, &bg_billing.GetBgBillingsRequest{})
	if err != nil {
		return nil, fmt.Errorf("failed to get bg billings: %w", err)
	}
	var insurances []*patient_profile_common.InsuranceInfo
	insuranceMap := make(map[int32]struct{})
	for _, record := range bgBillings {
		if record.InsuranceInfo == nil {
			continue
		}

		insuranceIdStr := record.InsuranceInfo.IkNumber
		if _, exists := insuranceMap[insuranceIdStr]; !exists {
			insurances = append(insurances, record.InsuranceInfo)
			insuranceMap[insuranceIdStr] = struct{}{}
		}
	}
	return insurances, nil
}
