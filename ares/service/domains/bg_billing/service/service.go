package service

import (
	"fmt"

	"emperror.dev/errors"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/bg_billing"
	"git.tutum.dev/medi/tutum/ares/app/mvz/patient_profile"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_copy"
	catalog_uv_goa_common "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_uv_goa_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"
	bg_common "git.tutum.dev/medi/tutum/ares/service/domains/bg_billing/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/bg_billing/repo"
	catalog_uv_goa_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_uv_goa"
	doctor_letter_common "git.tutum.dev/medi/tutum/ares/service/domains/doctor_letter/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	scheinRepo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	timeline_common "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/timeline_service"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/spf13/cast"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
)

type Service struct {
	bgBillingRepo               *repo.BgBillingRepo
	scheinRepo                  scheinRepo.ScheinRepoDefaultRepository
	timelineServiceAny          *timeline_service.TimelineService[any]
	catalogGoaUvService         *catalog_uv_goa_service.CatalogUvGoaService
	timelineServiceDoctorLetter *timeline_service.TimelineService[doctor_letter_common.DoctorLetter]
}

type BillingData struct {
	BillingId      uuid.UUID
	uvGoaTimelines []timeline_repo.TimelineEntity[patient_encounter.EncounterUvGoaService]
	schein         scheinRepo.ScheinRepo
	uvGoaCatalogs  []*catalog_uv_goa_common.UvGoaCatalog
	uvGoaNumbers   []string
}

var BgBillingServiceMod = submodule.Make[*Service](func(timelineServiceAny *timeline_service.TimelineService[any],
	catalogGoaUvService *catalog_uv_goa_service.CatalogUvGoaService,
	timelineServiceDoctorLetter *timeline_service.TimelineService[doctor_letter_common.DoctorLetter],
	patientProfileService *patient_profile.PatientProfileBffImpl,
) *Service {
	service := Service{
		bgBillingRepo:               repo.NewBgBillingRepo(),
		scheinRepo:                  scheinRepo.NewScheinRepoDefaultRepository(),
		timelineServiceAny:          timelineServiceAny,
		catalogGoaUvService:         catalogGoaUvService,
		timelineServiceDoctorLetter: timelineServiceDoctorLetter,
	}
	timelineServiceAny.HookAfterAction.RegisterOnCreateFunc(service.onTimelineCreate)
	timelineServiceAny.HookAfterAction.RegisterOnUpdateFunc(service.onTimelineUpdate)
	timelineServiceAny.HookAfterAction.RegisterOnDeleteFunc(service.onTimelineDelete)
	patientProfileService.CUDHook.RegisterOnUpdateFunc(service.onPatientProfileUpdate)
	return &service
}, timeline_service.TimelineServiceAnyMod,
	catalog_uv_goa_service.CatalogUvGoaServiceMod,
	timeline_service.TimelineServiceDoctorLetterMod,
	patient_profile.PatientProfileServiceMod)

func (s *Service) CreateBgBilling(ctx *titan.Context, request *bg_billing.CreateBgBillingRequest) (*repo.BgBillingEntity, error) {
	entity, err := s.bgBillingRepo.CreateBgBilling(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("failed to create bg billing: %w", err)
	}
	return entity, nil
}

func (s *Service) GetBgBilling(ctx *titan.Context, request *bg_billing.GetBgBillingsRequest) ([]*bg_common.BgBillingItem, int64, error) {
	entities, total, err := s.bgBillingRepo.GetBgBilling(ctx, request)

	if err != nil {
		return nil, 0, err
	}

	if entities == nil {
		return nil, 0, nil
	}

	data := []*bg_common.BgBillingItem{}

	for _, entity := range entities {
		data = append(data, entity.ToBgBillingItem())
	}

	return data, total, err
}

func (s *Service) GetBgBillingByScheinId(ctx *titan.Context, scheinId uuid.UUID) (*bg_common.BgBillingItem, error) {
	entity, err := s.bgBillingRepo.GetByScheinId(ctx, scheinId)
	if err != nil {
		return nil, fmt.Errorf("failed to get bg billing: %w", err)
	}
	return entity.ToBgBillingItem(), nil
}

func (s *Service) GetBgBillingById(ctx *titan.Context, billingId uuid.UUID) (*repo.BgBillingEntity, error) {
	entity, err := s.bgBillingRepo.GetById(ctx, billingId)
	if err != nil {
		return nil, fmt.Errorf("failed to get bg billing by Id: %w", err)
	}
	return entity, nil
}

func (s *Service) GetUvGoaServiceCode(ctx *titan.Context, request *bg_billing.GetUvGoaServiceCodeRequest) (*bg_billing.GetUvGoaServiceCodeResponse, error) {
	billingData, err := s.prepairGetGoaServiceTimelines(ctx, util.GetPointerValue(request.Id))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if billingData == nil {
		return nil, nil
	}

	listUvGoaServiceTimelineData := bg_billing.UvGoaServiceTimelineData{}
	response := getUvGoaTablesInfo(util.GetPointerValue(billingData))
	if response == nil {
		goaServiceTimelineData := &bg_billing.UvGoaServiceTimelineData{
			BillingId:      util.NewPointer(billingData.BillingId),
			TimelineModels: nil,
			InvoiceInfo:    nil,
		}
		listUvGoaServiceTimelineData = util.GetPointerValue(goaServiceTimelineData)
	} else {
		goaServiceTimelineData := &bg_billing.UvGoaServiceTimelineData{
			BillingId:      util.NewPointer(billingData.BillingId),
			TimelineModels: response.TimelineModels,
			InvoiceInfo:    response.InvoiceInfo,
		}
		listUvGoaServiceTimelineData = util.GetPointerValue(goaServiceTimelineData)
	}

	return &bg_billing.GetUvGoaServiceCodeResponse{
		Data: &listUvGoaServiceTimelineData,
	}, nil
}

func (s *Service) GetUvGoaServiceCodeByIds(ctx *titan.Context, request *bg_billing.GetUvGoaServiceCodeByIdsRequest) (*bg_billing.GetUvGoaServiceCodeByIdsResponse, error) {
	listUvGoaServiceTimelineData := []*bg_billing.UvGoaServiceTimelineData{}

	for _, billingId := range request.Ids {
		data, err := s.GetUvGoaServiceCode(ctx, &bg_billing.GetUvGoaServiceCodeRequest{
			Id: &billingId,
		})

		if err != nil {
			return nil, errors.WithStack(err)
		}

		if data == nil {
			continue
		}

		listUvGoaServiceTimelineData = append(listUvGoaServiceTimelineData, data.Data)
	}

	return &bg_billing.GetUvGoaServiceCodeByIdsResponse{
		Data: listUvGoaServiceTimelineData,
	}, nil
}

func (s *Service) MarkBgBillingPaid(ctx *titan.Context, request *bg_billing.MarkBgBillingPaidRequest) (*timeline_common.TimelineModel, error) {
	billingItem, err := s.bgBillingRepo.GetById(ctx, *request.BillingId)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to get bg billing by id.")
	}
	if billingItem == nil {
		return nil, nil
	}
	letterItem := doctor_letter_common.DoctorLetter{
		Id:       util.NewUUID(),
		Type:     doctor_letter_common.TemplateType_BgBilling,
		ScheinId: util.NewPointer(billingItem.ScheinId),
		BgInvoice: &doctor_letter_common.BgInvoice{
			BillingId:     util.GetPointerValue(request.BillingId),
			InvoiceNumber: request.InvoiceNumber,
			Status:        doctor_letter_common.InvoiceStatus_Billing_Paid,
		},
	}
	timelineEntity, err := s.createBgBillingTimeline(ctx, letterItem, util.GetPointerValue(request.PatientId))
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to create invoice timeline entity.")
	}
	if timelineEntity == nil {
		return nil, nil
	}
	// update bg billing history
	updatedItem, err := s.bgBillingRepo.UpdateBillingAsPaid(ctx, bg_common.UpdateBgBillingFromInvoiceRequest{
		BillingId:         *request.BillingId,
		Status:            bg_common.BillingStatus_Paid,
		InvoiceTimelineId: *timelineEntity.Id,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to update bg billing as paid.")
	}
	// mark bg schein as billed
	_, err = s.scheinRepo.UpdateBgScheinStatus(ctx, updatedItem.ScheinId, schein_common.ScheinStatus_Billed)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to mark bg schein as unbilled.")
	}

	return s.ConvertTimeEntityToModel(timelineEntity)
}

func (s *Service) UpdateFieldLastDocumentedAt(ctx *titan.Context, billingId uuid.UUID) (*repo.BgBillingEntity, error) {
	entity, err := s.bgBillingRepo.UpdateFieldLastDocumentedAt(ctx, billingId)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to update field LastDocumentedAt of bg billing.")
	}
	return entity, nil
}

func (s *Service) MarkBgBillingCancelled(ctx *titan.Context, request *bg_billing.MarkBgBillingCancelledRequest) (*bg_common.BgBillingItem, error) {
	billingItem, err := s.bgBillingRepo.GetById(ctx, *request.BillingId)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to get bg billing by id.")
	}
	if billingItem == nil {
		return nil, nil
	}
	// create timeline entry for cancelled
	letterItem := doctor_letter_common.DoctorLetter{
		Id:       util.NewUUID(),
		Type:     doctor_letter_common.TemplateType_BgBilling,
		ScheinId: util.NewPointer(billingItem.ScheinId),
		BgInvoice: &doctor_letter_common.BgInvoice{
			BillingId:     util.GetPointerValue(request.BillingId),
			InvoiceNumber: billingItem.InvoiceNumber,
			Status:        doctor_letter_common.InvoiceStatus_Billing_Cancelled,
		},
	}
	timelineEntity, err := s.createBgBillingTimeline(ctx, letterItem, util.GetPointerValue(request.PatientId))
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to create invoice timeline entity.")
	}
	if timelineEntity == nil {
		return nil, nil
	}
	// update bg billing history item
	updatedItem, err := s.bgBillingRepo.UpdateBgBillingAsCancelled(ctx, *billingItem.Id, *timelineEntity.Id)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to update bg billing as cancelled.")
	}
	// mark bg schein as cancelled
	_, err = s.scheinRepo.UpdateBgScheinStatus(ctx, updatedItem.ScheinId, schein_common.ScheinStatus_Canceled)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to mark bg schein as unbilled.")
	}

	return updatedItem.ToBgBillingItem(), nil
}

func (s *Service) MarkBgBillingUnpaid(ctx *titan.Context, request *bg_billing.MarkBgBillingUnpaidRequest) (*bg_common.BgBillingItem, error) {
	// get bg billing item
	billingItem, err := s.bgBillingRepo.GetById(ctx, *request.BillingId)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to get billing by id.")
	}
	if billingItem == nil {
		return nil, nil
	}
	if billingItem.Status != bg_common.BillingStatus_Paid {
		return nil, errors.New("Only paid bg billing can be marked as unpaid.")
	}

	// create timeline entry for unpaid
	letterItem := doctor_letter_common.DoctorLetter{
		ScheinId: util.NewPointer(billingItem.ScheinId),
		Id:       util.NewUUID(),
		Type:     doctor_letter_common.TemplateType_BgBilling,
		BgInvoice: &doctor_letter_common.BgInvoice{
			BillingId:     util.GetPointerValue(request.BillingId),
			InvoiceNumber: billingItem.InvoiceNumber,
			Status:        doctor_letter_common.InvoiceStatus_Billing_Unpaid,
		},
	}
	timelineEntity, err := s.createBgBillingTimeline(ctx, letterItem, util.GetPointerValue(request.PatientId))
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to create invoice timeline entity.")
	}
	if timelineEntity == nil {
		return nil, nil
	}
	// update bg billing history item
	updatedItem, err := s.bgBillingRepo.UpdateBgBillingAsUnpaid(ctx, *billingItem.Id, *timelineEntity.Id)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to update bg billing as unpaid.")
	}
	// mark bg schein as printed
	_, err = s.scheinRepo.UpdateBgScheinStatus(ctx, *updatedItem.Id, schein_common.ScheinStatus_Printed)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to mark bg schein as unbilled.")
	}
	return updatedItem.ToBgBillingItem(), nil
}

func (s *Service) GetPrintedInvoices(ctx *titan.Context, request *bg_billing.GetPrintedInvoicesRequest) ([]*timeline_common.TimelineModel, error) {
	timelineDoctorLetterEntities, err := s.timelineServiceAny.GetBgInvoicesDoctorLetterPrinted(ctx, *request.PatientId, *request.BillingId)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to get printed invoices.")
	}

	var timelineModels []*timeline_common.TimelineModel

	if len(timelineDoctorLetterEntities) > 0 {
		for _, entity := range timelineDoctorLetterEntities {
			model, err := s.ConvertTimeEntityToModel(&entity)
			if err != nil {
				return nil, err
			}

			timelineModels = append(timelineModels, model)
		}
	}

	timelineFormEntities, err := s.timelineServiceAny.GetBgInvoicesFormPrinted(ctx, *request.PatientId, *request.BillingId)

	if err != nil {
		return nil, errors.WithMessage(err, "Failed to get printed invoices.")
	}

	if len(timelineFormEntities) > 0 {
		for _, entity := range timelineFormEntities {
			model, err := s.ConvertFormEntityToModel(&entity)
			if err != nil {
				return nil, err
			}

			timelineModels = append(timelineModels, model)
		}
	}

	return timelineModels, nil
}

func (srv *Service) createBgBillingTimeline(ctx *titan.Context, letterItem doctor_letter_common.DoctorLetter, patientID uuid.UUID) (*timeline_repo.TimelineEntity[doctor_letter_common.DoctorLetter], error) {
	doctorLetterEntity, err := srv.timelineServiceDoctorLetter.Create(ctx, timeline_repo.TimelineEntity[doctor_letter_common.DoctorLetter]{
		PatientId: patientID,
		Payload:   letterItem,
	})
	return doctorLetterEntity, err
}

func (srv *Service) UpdateBgBillingFromMarkScheinUnbilled(ctx *titan.Context, scheinId uuid.UUID) (*repo.BgBillingEntity, error) {
	bgBillingItem, err := srv.bgBillingRepo.GetByScheinId(ctx, scheinId)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to get bg billing by schein id.")
	}
	if bgBillingItem == nil {
		return nil, nil
	}

	// create timeline entry for unbilled
	letterItem := doctor_letter_common.DoctorLetter{
		Id:   util.NewUUID(),
		Type: doctor_letter_common.TemplateType_BgBilling,
		BgInvoice: &doctor_letter_common.BgInvoice{
			BillingId:     util.GetPointerValue(bgBillingItem.Id),
			InvoiceNumber: bgBillingItem.InvoiceNumber,
			Status:        doctor_letter_common.InvoiceStatus_Billing_NoInvoice,
			OldStatus:     util.NewPointer(bgBillingItem.Status),
		},
	}
	_, err = srv.createBgBillingTimeline(ctx, letterItem, bgBillingItem.Patient.PatientId)
	if err != nil {
		return nil, errors.WithMessage(err, "Failed to create bg invoice timeline entity.")
	}

	newBgBillingItem, err := srv.CreateBgBilling(ctx, &bg_billing.CreateBgBillingRequest{
		Item: &bg_common.BillingRecord{
			Patient:            bgBillingItem.Patient,
			Doctor:             bgBillingItem.Doctor,
			InsuranceInfo:      bgBillingItem.InsuranceInfo,
			ScheinId:           bgBillingItem.ScheinId,
			Status:             bg_common.BillingStatus_NoInvoice,
			InvoiceDate:        nil,
			ExcludeFromBilling: bgBillingItem.ExcludeFromBilling,
			LastDocumentedAt:   bgBillingItem.LastDocumentedAt,
		},
	})
	if err != nil {
		return nil, err
	}
	return newBgBillingItem, nil
}

func (srv *Service) prepairGetGoaServiceTimelines(ctx *titan.Context, bgBillingId uuid.UUID) (*BillingData, error) {
	bgBilling, err := srv.GetBgBillingById(ctx, bgBillingId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if bgBilling == nil {
		return nil, nil
	}

	// Extract schein id from bg billings
	patientWithScheinId := extractPatientAndScheinId(util.GetPointerValue(bgBilling))

	// Retrieve goa timelines using schein ids
	timelines, err := srv.timelineServiceAny.GetUvGoaTimelinesByScheinId(ctx, patientWithScheinId)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// Retrieve uv-goa goaCatalogs
	uvGoaCatalogs, err := srv.getListUvGoaCatalogs(ctx, timelines)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// Retrieve scheins using schein ids
	scheinIds := []uuid.UUID{}
	scheinIds = append(scheinIds, patientWithScheinId.ScheinIds...)
	scheins, err := srv.scheinRepo.GetByIds(ctx, scheinIds)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(scheins) == 0 {
		return nil, nil
	}

	// Map timelines by schein ids
	scheinIdToTimelines := mapTimelinesByScheinIds(timelines)
	scheinIdToSchein := mapScheins(scheins)

	// Build private billing data list
	billingDataList := buildBillingDataList(bgBilling, scheinIdToTimelines, scheinIdToSchein, uvGoaCatalogs)

	return &billingDataList, nil
}

func (srv *Service) UpdateBgBillingFromInvoice(ctx *titan.Context, request bg_common.UpdateBgBillingFromInvoiceRequest) (*repo.BgBillingEntity, error) {
	entity, err := srv.bgBillingRepo.UpdateBgBillingFromInvoice(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("failed to update bg billing from invoice: %w", err)
	}
	return entity, nil
}

func (srv *Service) GetListDoctor(ctx *titan.Context) ([]*bg_common.Doctor, error) {
	doctorModels, err := srv.bgBillingRepo.GetListDoctor(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return doctorModels, nil
}

func (srv *Service) GetListInsurance(ctx *titan.Context) ([]*patient_profile_common.InsuranceInfo, error) {
	insuranceModels, err := srv.bgBillingRepo.GetListInsurance(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return insuranceModels, nil
}

func (srv *Service) DeleteBgBillingByScheinId(ctx *titan.Context, scheinId uuid.UUID) error {
	_, err := srv.bgBillingRepo.DeleteBgBillingByScheinId(ctx, scheinId)
	if err != nil {
		return err
	}
	return nil
}

func getUvGoaTablesInfo(
	billingData BillingData) *bg_billing.UvGoaServiceTimelineData {
	uvGoaTimelines := billingData.uvGoaTimelines
	if len(uvGoaTimelines) == 0 {
		invoiceInfo := bg_common.InvoiceInfo{
			Amount: util.ToFixed(0, 2),
		}
		return &bg_billing.UvGoaServiceTimelineData{
			TimelineModels: nil,
			InvoiceInfo:    &invoiceInfo,
		}
	}

	// Define a map to store timeline lists based on their suffixes
	timelineLists := map[string][]timeline_repo.TimelineEntity[patient_encounter.EncounterUvGoaService]{}
	timelineLists["normal"] = append(timelineLists["normal"], uvGoaTimelines...)

	amount := 0.0
	// Calculate price for each group
	for group, timelines := range timelineLists {
		timelines, groupSubTotal := calculatePriceUvGoaServices(timelines)
		// Update timelines
		timelineLists[group] = timelines
		// Update subtotal
		amount += *groupSubTotal
	}

	// Create invoice info
	invoiceInfo := bg_common.InvoiceInfo{
		Amount: util.ToFixed(amount, 2),
	}

	// Convert to response
	timelineModels := convertToTimelineModels(uvGoaTimelines)

	return &bg_billing.UvGoaServiceTimelineData{
		TimelineModels: timelineModels,
		InvoiceInfo:    util.NewPointer(invoiceInfo),
	}
}

func calculatePriceUvGoaServices(
	uvGoaTimelines []timeline_repo.TimelineEntity[patient_encounter.EncounterUvGoaService],
) (updatedUvGoaTimelines []timeline_repo.TimelineEntity[patient_encounter.EncounterUvGoaService], groupSubTotal *float64) {
	totalPrice := 0.0
	totalMaterialCost := 0.0
	updatedUvGoaTimelines = uvGoaTimelines
	for i, timeline := range updatedUvGoaTimelines {
		timelinePrice := util.GetFloat64Value(updatedUvGoaTimelines[i].Payload.Price)
		// Handle total amount from additional info
		additionalInfos := util.GetPointerValue(timeline.Payload.AdditionalInfos)
		totalAmount := calculateTotalAmount(additionalInfos)
		if totalAmount > 0 {
			timelinePrice = totalAmount
			updatedUvGoaTimelines[i].Payload.Price = util.NewPointer(util.ToFixed(0, 2))
		}

		// Calculate subtotal
		totalPrice += timelinePrice
		totalMaterialCost += calculateMaterialCost(additionalInfos)
	}

	groupSubTotal = util.NewPointer(totalPrice + totalMaterialCost)
	return updatedUvGoaTimelines, groupSubTotal
}

func buildBillingDataList(bgBilling *repo.BgBillingEntity,
	scheinIdToTimelines map[uuid.UUID][]timeline_repo.TimelineEntity[patient_encounter.EncounterUvGoaService],
	scheinIdToSchein map[uuid.UUID]scheinRepo.ScheinRepo,
	goaCatalogs []*catalog_uv_goa_common.UvGoaCatalog) BillingData {
	scheinId := bgBilling.ScheinId
	timelines := scheinIdToTimelines[scheinId]
	schein := scheinIdToSchein[scheinId]

	billingData := BillingData{
		BillingId:      *bgBilling.Id,
		uvGoaTimelines: timelines,
		schein:         schein,
		uvGoaCatalogs:  goaCatalogs,
		uvGoaNumbers:   extractUvGoaNumbers(timelines),
	}
	return billingData
}

func mapTimelinesByScheinIds(timelines []timeline_repo.TimelineEntity[patient_encounter.EncounterUvGoaService]) map[uuid.UUID][]timeline_repo.TimelineEntity[patient_encounter.EncounterUvGoaService] {
	scheinIdToTimelines := make(map[uuid.UUID][]timeline_repo.TimelineEntity[patient_encounter.EncounterUvGoaService])
	for _, timeline := range timelines {
		scheinId := timeline.ScheinIds[0]
		scheinIdToTimelines[scheinId] = append(scheinIdToTimelines[scheinId], timeline)
	}

	return scheinIdToTimelines
}

func mapScheins(scheins []scheinRepo.ScheinRepo) map[uuid.UUID]scheinRepo.ScheinRepo {
	scheinIdToSchein := make(map[uuid.UUID]scheinRepo.ScheinRepo)
	for _, schein := range scheins {
		scheinIdToSchein[*schein.Id] = schein
	}
	return scheinIdToSchein
}

func (srv *Service) getListUvGoaCatalogs(ctx *titan.Context, timelines []timeline_repo.TimelineEntity[patient_encounter.EncounterUvGoaService]) ([]*catalog_uv_goa_common.UvGoaCatalog, error) {
	uvGoaNumbers := extractUvGoaNumbers(timelines)
	// Get GOA service catalogs
	if len(uvGoaNumbers) == 0 {
		return nil, nil
	}

	catalogs, err := srv.catalogGoaUvService.GetCatalogsByListUvGoaCode(ctx, uvGoaNumbers)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return catalogs, nil
}

func extractUvGoaNumbers(timelines []timeline_repo.TimelineEntity[patient_encounter.EncounterUvGoaService]) []string {
	goaNumbers := make([]string, 0)
	for _, timeline := range timelines {
		goaNumbers = append(goaNumbers, timeline.Payload.Code)
	}
	return goaNumbers
}

func calculateMaterialCost(additionalInfos []*patient_encounter.AdditionalInfoParent) float64 {
	materialCost := 0.0
	for _, info := range additionalInfos {
		if info.FK == "5012" {
			materialCost += cast.ToFloat64(info.Value)
		}
	}
	return (materialCost / 100)
}

func calculateTotalAmount(additionalInfos []*patient_encounter.AdditionalInfoParent) float64 {
	totalAmount := 0.0
	for _, info := range additionalInfos {
		if info.FK == "5300" {
			totalAmount = cast.ToFloat64(info.Value)
		}
	}
	return totalAmount
}

func extractPatientAndScheinId(bgBilling repo.BgBillingEntity) timeline_repo.PatientWithScheinIds {
	patientWithScheinIdsMap := make(map[uuid.UUID][]uuid.UUID)
	patientId := bgBilling.Patient.PatientId
	scheinId := bgBilling.ScheinId
	patientWithScheinIdsMap[patientId] = append(patientWithScheinIdsMap[patientId], scheinId)
	scheinIds := make([]uuid.UUID, 0)
	scheinIds = append(scheinIds, scheinId)
	patientWithScheinIds := timeline_repo.PatientWithScheinIds{
		ScheinIds: scheinIds,
		PatientId: patientId,
	}
	return patientWithScheinIds
}

func convertToTimelineModels(goaTimelines []timeline_repo.TimelineEntity[patient_encounter.EncounterUvGoaService]) []*timeline_common.TimelineModel {
	var timelineModels []*timeline_common.TimelineModel
	for _, timelineEntity := range goaTimelines {
		timelineAny := pkg_copy.CloneTo[timeline_repo.TimelineEntity[any]](timelineEntity)
		timelineModels = append(timelineModels, util.NewPointer(timeline_service.ConvertEntityAnyToModel(timelineAny)))
	}
	return timelineModels
}

func (*Service) ConvertTimeEntityToModel(entity *timeline_repo.TimelineEntity[doctor_letter_common.DoctorLetter]) (*timeline_common.TimelineModel, error) {
	timelineAny := timeline_repo.TimelineEntity[any]{}
	err := copier.Copy(&timelineAny, entity)
	if err != nil {
		return nil, err
	}
	model := timeline_service.ConvertEntityAnyToModel(timelineAny)
	return &model, nil
}

func (*Service) ConvertFormEntityToModel(entity *timeline_repo.TimelineEntity[patient_encounter.EncounterForm]) (*timeline_common.TimelineModel, error) {
	timelineAny := timeline_repo.TimelineEntity[any]{}
	err := copier.Copy(&timelineAny, entity)
	if err != nil {
		return nil, err
	}
	model := timeline_service.ConvertEntityAnyToModel(timelineAny)
	return &model, nil
}
