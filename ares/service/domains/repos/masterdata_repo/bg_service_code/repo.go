package bg_service_code

import (
	"errors"
	"fmt"

	"git.tutum.dev/medi/tutum/ares/pkg/masterdata"
	masterdata_model "git.tutum.dev/medi/tutum/ares/pkg/masterdata/model"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_utils_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_uv_goa_common"
	mapper "git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
)

var BGServiceCodeRepoMod = submodule.Make[*BGServiceCodeRepo](func(deps *masterdata.ClientDependencie) (*BGServiceCodeRepo, error) {
	return &BGServiceCodeRepo{
		masterDataClient: masterdata.NewClient[masterdata_model.BGServiceCode](deps),
	}, nil
}, masterdata.ClientDependenciesCombined)

type BGServiceCodeRepo struct {
	masterDataClient masterdata.MasterdataClientInterface[masterdata_model.BGServiceCode]
}

type GetPaginationRequest struct {
	masterdata_model.YearQuarter
	masterdata_model.Pagination
	Query           *string `json:"query,omitempty"`
	OnlySelfCreated *bool   `json:"onlySelfCreated,omitempty"`
}

type SearchRequest struct {
	masterdata_model.YearQuarter
	Query string `json:"query"`
}

type GetByCodeRequest struct {
	masterdata_model.YearQuarter
	Code string `json:"code"`
}

type GetByCodesRequest struct {
	masterdata_model.YearQuarter
	Codes []string `json:"code"`
}

type DeleteRequest struct {
	Id string `json:"id"`
}

func (repo *BGServiceCodeRepo) GetPagination(ctx *titan.Context, request *GetPaginationRequest) (*masterdata_model.PaginationResponse[masterdata_model.BGServiceCode], error) {
	return repo.masterDataClient.GetPagination(ctx, request)
}

func (repo *BGServiceCodeRepo) Search(ctx *titan.Context, request *SearchRequest) ([]masterdata_model.BGServiceCode, error) {
	return repo.masterDataClient.Search(ctx, request)
}

func (repo *BGServiceCodeRepo) GetByCode(ctx *titan.Context, request *GetByCodeRequest) (*masterdata_model.BGServiceCode, error) {
	return repo.masterDataClient.FindOne(ctx, request)
}

func (repo *BGServiceCodeRepo) GetByCodes(ctx *titan.Context, request *GetByCodesRequest) ([]masterdata_model.BGServiceCode, error) {
	return repo.masterDataClient.Find(ctx, request)
}

func (repo *BGServiceCodeRepo) Upsert(ctx *titan.Context, request *masterdata_model.BGServiceCode) (*masterdata_model.BGServiceCode, error) {
	return repo.masterDataClient.Upsert(ctx, request)
}

func (repo *BGServiceCodeRepo) Delete(ctx *titan.Context, request *DeleteRequest) error {
	return repo.masterDataClient.Delete(ctx, request)
}

func (repo *BGServiceCodeRepo) GetById(ctx *titan.Context, request *masterdata.GetByIdRequest) (*masterdata_model.BGServiceCode, error) {
	return repo.masterDataClient.GetById(ctx, request)
}

func (repo *BGServiceCodeRepo) TransformToBgServiceCode(ctx *titan.Context, req *catalog_uv_goa_common.UvGoaCatalog) (*masterdata_model.BGServiceCode, error) {
	baseEntity, err := mapper.GetBaseEntity(ctx, &mapper.GetBaseEntityRequest{
		Id:         req.UvGoaId,
		SourceType: req.Source,
	}, func() (*mapper.GetBaseEntityRequest, error) {
		existingBgServiceCode, err := repo.GetById(ctx, &masterdata.GetByIdRequest{
			Id: req.UvGoaId,
		})
		if err != nil {
			return nil, err
		}

		if existingBgServiceCode == nil {
			return nil, nil
		}

		return &mapper.GetBaseEntityRequest{
			Id:         existingBgServiceCode.Id,
			SourceType: existingBgServiceCode.SourceType,
		}, nil
	})
	if err != nil {
		return nil, err
	}

	if baseEntity == nil {
		return nil, errors.New("mapping bg service code model failed")
	}

	toDate := req.Validity.ToDate
	fromDate := util.RoundTimeToDateOnly(util.GetPointerValue(req.Validity.FromDate), ctx.RequestTimeZone())
	validity := &catalog_utils_common.Validity{
		FromDate: &fromDate,
		ToDate:   toDate,
	}

	now := util.NowUnixMillis(ctx)

	yearQuarter := util.ToYearQuarter(now)
	return &masterdata_model.BGServiceCode{
		BaseEntity: masterdata_model.BaseEntity{
			Year:    yearQuarter.Year,
			Quarter: yearQuarter.Quarter,
		},
		CareProviderId:                 baseEntity.CareProviderId,
		BsnrId:                         baseEntity.BsnrId,
		Id:                             baseEntity.Id,
		Code:                           req.Code,
		Description:                    req.Description,
		LongDescription:                req.LongDescription,
		AdditionalCode:                 req.AdditionalCode,
		GeneralCost:                    float64ToString(req.GeneralCost),
		GeneralTreatmentEvaluation:     float64ToString(req.GeneralTreatmentEvaluation),
		HospitalTreatmentEvaluation:    req.HospitalTreatmentEvaluation,
		MaterialCost:                   float64ToString(req.MaterialCost),
		ResidentialTreatmentEvaluation: req.ResidentialTreatmentEvaluation,
		SpecificTreatmentEvaluation:    float64ToString(req.SpecificTreatmentEvaluation),
		Validity:                       validity,
		SourceType:                     baseEntity.SourceType,
		Billable:                       !util.GetPointerValue(req.IsNotBillable),
	}, nil
}

func float64ToString(value *float64) string {
	if value == nil {
		return ""
	}

	return fmt.Sprintf("%f", *value)
}
