package goa

import (
	"errors"

	"git.tutum.dev/medi/tutum/ares/pkg/masterdata"
	masterdata_model "git.tutum.dev/medi/tutum/ares/pkg/masterdata/model"
	catalog_goa_common "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_goa_common"
	mapper "git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo"

	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
)

var GoaMasterRepoMod = submodule.Make[*GoaRepo](func(deps *masterdata.ClientDependencie) (*GoaRepo, error) {
	initCsvData()

	return &GoaRepo{
		goaClient: masterdata.NewClient[masterdata_model.Goa](deps),
	}, nil
}, masterdata.ClientDependenciesCombined)

var GoaChapterRepoMod = submodule.Make[*GoaChapterRepo](func(deps *masterdata.ClientDependencie) (*GoaChapterRepo, error) {
	return &GoaChapterRepo{
		chapterClient: masterdata.NewClient[masterdata_model.Chapter](deps),
	}, nil
}, masterdata.ClientDependenciesCombined)

type GoaRepo struct {
	goaClient masterdata.MasterdataClientInterface[masterdata_model.Goa]
}

type GoaChapterRepo struct {
	chapterClient masterdata.MasterdataClientInterface[catalog_goa_common.Chapter]
}

type GetPaginationRequest struct {
	masterdata_model.YearQuarter
	masterdata_model.Pagination
	Query           *string `json:"query,omitempty"`
	OnlySelfCreated *bool   `json:"onlySelfCreated,omitempty"`
}

type SearchRequest struct {
	masterdata_model.YearQuarter
	Query string `json:"query"`
}

type GetByGoaNumberRequest struct {
	masterdata_model.YearQuarter
	GoaNumber string `json:"goaNumber"`
}

type GetListByGoaNumbersRequest struct {
	masterdata_model.YearQuarter
	GoaNumbers []string `json:"goaNumber"`
}
type GetGoaChaptersRequest struct {
	masterdata_model.YearQuarter
}

type DeleteRequest struct {
	Id string `json:"id"`
}

func (r *GoaRepo) GetPagination(ctx *titan.Context, request *GetPaginationRequest) (*masterdata_model.PaginationResponse[masterdata_model.Goa], error) {
	return r.goaClient.GetPagination(ctx, request)
}

func (r *GoaRepo) Search(ctx *titan.Context, request *SearchRequest) ([]masterdata_model.Goa, error) {
	return r.goaClient.Search(ctx, request)
}

func (r *GoaRepo) GetByGoaNumber(ctx *titan.Context, request *GetByGoaNumberRequest) (*masterdata_model.Goa, error) {
	return r.goaClient.FindOne(ctx, request)
}

func (r *GoaRepo) GetListByGoaNumbers(ctx *titan.Context, request *GetListByGoaNumbersRequest) ([]masterdata_model.Goa, error) {
	return r.goaClient.Find(ctx, request)
}

func (r *GoaRepo) Upsert(ctx *titan.Context, request *masterdata_model.Goa) (*masterdata_model.Goa, error) {
	return r.goaClient.Upsert(ctx, request)
}

func (r *GoaRepo) Delete(ctx *titan.Context, request *DeleteRequest) error {
	return r.goaClient.Delete(ctx, request)
}

func (r *GoaRepo) GetById(ctx *titan.Context, request *masterdata.GetByIdRequest) (*masterdata_model.Goa, error) {
	return r.goaClient.GetById(ctx, request)
}

func (r *GoaRepo) TransformToGoaModel(ctx *titan.Context, req *catalog_goa_common.GoaCatalog) (*masterdata_model.Goa, error) {
	baseEntity, err := mapper.GetBaseEntity(ctx, &mapper.GetBaseEntityRequest{
		Id:         req.GoaId,
		SourceType: req.Source,
	}, func() (*mapper.GetBaseEntityRequest, error) {
		existingGoa, err := r.GetById(ctx, &masterdata.GetByIdRequest{
			Id: req.GoaId,
		})
		if err != nil {
			return nil, err
		}

		if existingGoa == nil {
			return nil, nil
		}

		return &mapper.GetBaseEntityRequest{
			Id:         existingGoa.Id,
			SourceType: existingGoa.SourceType,
		}, nil
	})
	if err != nil {
		return nil, err
	}

	if baseEntity == nil {
		return nil, errors.New("mapping goa model failed")
	}

	now := util.NowUnixMillis(ctx)
	yearQuarter := util.ToYearQuarter(now)

	return &masterdata_model.Goa{
		BaseEntity: masterdata_model.BaseEntity{
			Year:    yearQuarter.Year,
			Quarter: yearQuarter.Quarter,
		},
		CareProviderId: baseEntity.CareProviderId,
		BsnrId:         baseEntity.BsnrId,
		Id:             baseEntity.Id,
		SourceType:     baseEntity.SourceType,
		GoaNumber:      req.GoaNumber,
		Description:    req.Description,
		ExcludedCodes:  req.ExcludedCode,
		Validity:       req.Validity,
		Factor:         req.Factor,
		Evaluation:     req.Evaluation,
		Unit:           &req.Unit,
		Chapter:        req.Chapter,
		Remark:         req.Remark,
	}, nil
}

// Chapter Client
func (r *GoaChapterRepo) GetGoaChapters(ctx *titan.Context, request *GetGoaChaptersRequest) (*masterdata_model.PaginationResponse[catalog_goa_common.Chapter], error) {
	return r.chapterClient.GetPagination(ctx, request)
}
