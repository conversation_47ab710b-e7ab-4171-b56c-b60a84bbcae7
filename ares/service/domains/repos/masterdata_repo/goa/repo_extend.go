package goa

import (
	_ "embed"
	"slices"
	"strings"

	masterdata_model "git.tutum.dev/medi/tutum/ares/pkg/masterdata/model"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_goa_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_utils_common"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/gocarina/gocsv"
)

var (
	//go:embed data/goa.csv
	b                      []byte
	Factor_Medical_List    = []string{"B", "C", "D", "F", "G", "H", "I", "J", "K", "L", "N", "P"}
	Factor_Technical_List  = []string{"A", "E", "O"}
	Factor_Laboratory_List = []string{"M"}
	MapGoaData             = make(map[string]masterdata_model.GoaData)
)

const RATE = 5.82873

func initCsvData() {
	goaData := []masterdata_model.GoaData{}
	err := gocsv.UnmarshalBytes(b, &goaData)
	if err != nil {
		panic(err)
	}
	for _, goa := range goaData {
		MapGoaData[goa.GoaNumber] = goa
	}
}

func GetChapter(g *masterdata_model.Goa) catalog_goa_common.Chapter {
	catalogChapter := g.Chapter
	goaData, ok := MapGoaData[g.GoaNumber]
	if !ok {
		return catalogChapter
	}
	if goaData.Chapter == "" {
		return catalogChapter
	}

	return catalog_goa_common.Chapter{
		ChapterName: goaData.Chapter,
	}
}

func GetFactor(g *masterdata_model.Goa) catalog_goa_common.Factor {
	// handle exception
	goaData, ok := MapGoaData[g.GoaNumber]
	if ok {
		return catalog_goa_common.Factor(goaData.Factor)
	}

	chapterNumber := strings.Split(g.Chapter.ChapterNumber, ".")[0]

	if g.GoaNumber == "1401" {
		return catalog_goa_common.Factor_Technical
	}

	if slices.Contains(Factor_Medical_List, chapterNumber) {
		return catalog_goa_common.Factor_Medical
	}

	if slices.Contains(Factor_Technical_List, chapterNumber) {
		return catalog_goa_common.Factor_Technical
	}

	if slices.Contains(Factor_Laboratory_List, chapterNumber) || g.GoaNumber == "437" {
		return catalog_goa_common.Factor_Laboratory
	}

	return ""
}

func GetMaxValuation(source catalog_utils_common.SourceType, goaNumber string) string {
	selfCreated := source == catalog_utils_common.SelfCreated
	switch {
	case selfCreated:
		return ""
	case isEndWith(goaNumber, ".H1"):
		return "3541.H-Höchstwert für die mit H1 gekennzeichneten Untersuchungen des Abschnitts M II"
	case isEndWith(goaNumber, ".H2"):
		return "3630.H-Höchstwert für die mit H2 gekennzeichneten Untersuchungen aus Abschnitt M III 8"
	case isEndWith(goaNumber, ".H3"):
		return "3631.H-Höchstwert für die mit H3 gekennzeichneten Untersuchungen aus Abschnitt M III 10"
	case isEndWith(goaNumber, ".H4"):
		return "3633.H-Höchstwert für die mit H4 gekennzeichneten Untersuchungen aus Abschnitt M III 14"
	}
	return ""
}

func isEndWith(s, suffix string) bool {
	return strings.HasSuffix(s, suffix)
}

func CalculateGoaPrice(unit catalog_goa_common.Unit, evaluation float64) float64 {
	if evaluation == 0 {
		return 0
	}

	if unit == catalog_goa_common.Unit_Points {
		priceInCents := evaluation * RATE
		priceInEur := priceInCents / 100
		return util.ToFixed(priceInEur, 2)
	}
	if unit == catalog_goa_common.Unit_Euros {
		priceInEur := evaluation
		return util.ToFixed(priceInEur, 2)
	}
	return 0
}

func ToGoaCatalog(g *masterdata_model.Goa) *catalog_goa_common.GoaCatalog {
	description := g.Description
	factor := g.Factor
	chapter := g.Chapter
	if g.Unit == nil {
		g.Unit = util.NewPointer(catalog_goa_common.Unit_Points)
	}
	price := CalculateGoaPrice(*g.Unit, g.Evaluation)

	if g.SourceType == catalog_utils_common.XmlFile {
		description = masterdata_model.ModifyDescription(g.Description)
		chapter = GetChapter(g)
		factor = GetFactor(g)
		if g.Evaluation == 0 {
			price = g.Price
		}
	}

	return &catalog_goa_common.GoaCatalog{
		GoaId:         g.Id,
		GoaNumber:     g.GoaNumber,
		Description:   description,
		ExcludedCode:  g.ExcludedCodes,
		Validity:      g.Validity,
		Factor:        factor,
		Evaluation:    g.Evaluation,
		Unit:          *g.Unit,
		MaxEvaluation: GetMaxValuation(g.SourceType, g.GoaNumber),
		Source:        g.SourceType,
		Chapter:       chapter,
		Price:         price,
		Remark:        g.Remark,
	}
}

func ToGoaItem(g *masterdata_model.Goa) *catalog_goa_common.GoaItem {
	isSelfCreated := false
	if g.SourceType == catalog_utils_common.SelfCreated {
		isSelfCreated = true
	}

	price := g.Price
	if price == 0 {
		unit := catalog_goa_common.Unit_Points
		if g.Unit != nil {
			unit = *g.Unit
		}

		price = CalculateGoaPrice(unit, g.Evaluation)
	}

	return &catalog_goa_common.GoaItem{
		GoaNumber:     g.GoaNumber,
		Description:   g.Description,
		Evaluation:    g.Evaluation,
		Price:         price,
		Factor:        GetFactor(g),
		IsSelfCreated: isSelfCreated,
	}
}
