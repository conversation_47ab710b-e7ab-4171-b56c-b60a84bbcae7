package timeline_repo

import (
	"fmt"
	"time"

	"git.tutum.dev/medi/tutum/ares/pkg/operator"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	timeline_common "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type (
	UpdateDiagnoseItemWithSuggestionsData struct {
		Model              *timeline_common.TimelineModel
		DiagnoseSuggestion []*patient_encounter.DiagnoseSuggestion
		YearQuarter        util.YearQuarter
	}
	GetDiagnoseUniqueRequest struct {
		PatientId uuid.UUID
		Codes     []string
	}
	GetTakeOverDiagnosisRequest struct {
		Type      timeline_common.TakeoverDiagnosisType
		PatientId uuid.UUID
		Query     string
		StartDate *time.Time
		EndDate   *time.Time
		ScheinId  *uuid.UUID
	}
	GetEncounterByP4ValidationReportRequest struct {
		PatientId  uuid.UUID
		ContractId *string
		Quarter    int32
		Year       int32
	}
)

func (t *TimelineEntityRepo[T]) UpdateDiagnoseItemWithSuggestions(
	ctx *titan.Context,
	req []UpdateDiagnoseItemWithSuggestionsData,
) error {
	var bulkUpdateSDKRWStatus []mongo.WriteModel
	var bulkUpdateSuggestion []mongo.WriteModel
	now := util.Now(ctx)
	for _, data := range req {
		encounter := data.Model.EncounterDiagnoseTimeline
		filterBuilder := &FilterBuilder{}
		filter := filterBuilder.
			Init().
			AndPatient(data.Model.PatientId).
			AndQuarterYear(data.YearQuarter.Quarter, data.YearQuarter.Year).
			AndDiagnoseCode(encounter.Code).
			AndDiagnoseCertainty(string(util.GetPointerValue(encounter.Certainty))).
			AndType(string(timeline_common.TimelineEntityType_Diagnose)).
			Build()
		update := bson.M{
			"$set": bson.M{
				Field_DiagnoseRunSdkrw:    patient_encounter.RUNSDKRWENUM_DONE,
				Field_DiagnoseSuggestions: nil,
				Field_UpdatedAt:           now,
			},
		}
		// NOTE: Updating run sdkrw status for all diagnoses have the same code and certainty by quarter
		bulkUpdateSDKRWStatus = append(
			bulkUpdateSDKRWStatus,
			mongo.NewUpdateManyModel().SetFilter(filter).SetUpdate(update),
		)
		// NOTE: Updating diagnoseSuggestions for the first diagnose (have the same code and certainty)
		bulkUpdateSuggestion = append(
			bulkUpdateSuggestion,
			mongo.NewUpdateOneModel().
				SetFilter(bson.M{
					Field_Id:                 data.Model.Id,
					Field_timelineEntityType: timeline_common.TimelineEntityType_Diagnose,
					Field_IsDeleted:          false,
				}).
				SetUpdate(bson.M{
					"$set": bson.M{
						Field_DiagnoseSuggestions: data.DiagnoseSuggestion,
						Field_UpdatedAt:           now,
					},
				}),
		)
	}

	if err := t.IDBClient.BulkWrite(ctx, bulkUpdateSDKRWStatus, options.BulkWrite().SetOrdered(false)); err != nil {
		return errors.WithMessage(err, "failed to update diagnose run sdkrw status")
	}

	if err := t.IDBClient.BulkWrite(ctx, bulkUpdateSuggestion, options.BulkWrite().SetOrdered(false)); err != nil {
		return errors.WithMessage(err, "failed to update diagnose suggestions")
	}
	return nil
}

func (t *TimelineEntityRepo[T]) GetPermanentDiagnoses(ctx *titan.Context, patientIds []uuid.UUID) ([]TimelineEntity[T], error) {
	filterBuilder := &FilterBuilder{}
	filterVal := filterBuilder.
		Init().
		AndPatients(patientIds).
		AndDiagnoseType(patient_encounter.DIAGNOSETYPE_PERMANENT).
		Build()

	return slice.ToValueTypeWithError(t.Repo.Find(ctx, *filterVal))
}

func (s *TimelineEntityRepo[T]) UpdateSuggestionRuleApplied(ctx *titan.Context, timelineId uuid.UUID, ruleId string) error {
	_, err := s.Repo.FindOneAndUpdate(
		ctx,
		bson.M{
			Field_Id: timelineId,
		},
		bson.M{
			"$set": bson.M{
				"payload.diagnoseSuggestions.$[s].applied": true,
				Field_UpdatedAt: util.Now(ctx),
			},
		},
		options.FindOneAndUpdate().SetArrayFilters(options.ArrayFilters{
			Filters: []any{
				bson.M{
					"s.ruleId": bson.M{
						"$eq": ruleId,
					},
				},
			},
		}),
	)
	if err != nil {
		return err
	}

	return nil
}

func (s *TimelineEntityRepo[T]) GetPermanentDiagnosesForUpdatingEndDate(
	ctx *titan.Context,
	patientId uuid.UUID,
	code string,
	certainty string,
) ([]TimelineEntity[patient_encounter.EncounterDiagnoseTimeline], error) {
	// note: Valid_Until will be updated when it's different to endDate & Valid_Until > current time (it isn't terminated).
	// current := util.Now(ctx).UnixMilli()
	// filterWithFieldValidUntilShouldUpdate := bson.A{
	// 	bson.M{
	// 		Field_ValidUntil: nil,
	// 	},
	// 	bson.M{
	// 		Field_ValidUntil: bson.M{
	// 			"$ne": endDate,
	// 			"$gt": current,
	// 		},
	// 	},
	// }
	filterWithSameCodeAndCertainty := bson.M{
		Field_IsDeleted:    false,
		Field_PatientId:    patientId,
		Field_ICD_Code:     code,
		Field_Certainty:    certainty,
		Field_DiagnoseType: patient_encounter.DIAGNOSETYPE_PERMANENT,
		// "$or":              filterWithFieldValidUntilShouldUpdate, //verify later
	}

	diagnoses, err := s.Repo.Find(ctx, filterWithSameCodeAndCertainty)
	if err != nil {
		return nil, err
	}

	if len(diagnoses) == 0 {
		return nil, nil
	}
	var result []TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]
	for _, v := range diagnoses {
		result = append(result, ToEntityTypeFromT[T, patient_encounter.EncounterDiagnoseTimeline](*v))
	}

	return result, nil
}

func (s *TimelineEntityRepo[T]) UpdateDiagnoseEndDateByIds(ctx *titan.Context, ids []*uuid.UUID, endDate *int64) error {
	filter := bson.M{
		Field_Id: bson.M{
			"$in": ids,
		},
	}
	update := bson.M{
		"$set": bson.M{
			Field_ValidUntil: endDate,
			Field_UpdatedAt:  util.Now(ctx),
		},
		"$push": bson.M{
			Field_RecentAuditLogs: newAuditLog(ctx, ctx.UserInfo().UserUUID(), timeline_common.Edit),
		},
	}
	_, err := s.IDBClient.UpdateMany(ctx, filter, update, nil, nil)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (t *TimelineEntityRepo[T]) FindDiagnosisByPatientIdAndScheinIds(ctx *titan.Context, patientId uuid.UUID, scheinIds []uuid.UUID) ([]TimelineEntity[patient_encounter.EncounterDiagnoseTimeline], error) {
	filter := bson.D{
		{
			Key:   Field_PatientId,
			Value: patientId,
		},
		{
			Key:   Field_IsDeleted,
			Value: false,
		},
		{
			Key:   Field_timelineEntityType,
			Value: timeline_common.TimelineEntityType_Diagnose,
		},
		{
			Key: Field_ScheinIds,
			Value: bson.M{
				operator.In: scheinIds,
			},
		},
	}

	res, err := t.Repo.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	if len(res) == 0 {
		return nil, nil
	}

	var diagnosis []TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]
	for _, v := range res {
		diagnosis = append(diagnosis, ToEntityTypeFromT[T, patient_encounter.EncounterDiagnoseTimeline](*v))
	}

	return diagnosis, nil
}

func (t *TimelineEntityRepo[T]) GetDiagnosesByQuarter(ctx *titan.Context, patientId uuid.UUID, yearQuarter util.YearQuarter) ([]TimelineEntity[T], error) {
	filter := bson.D{
		{
			Key:   Field_PatientId,
			Value: patientId,
		},
		{
			Key:   Field_Year,
			Value: yearQuarter.Year,
		},
		{
			Key:   Field_Quarter,
			Value: yearQuarter.Quarter,
		},
		{
			Key:   Field_timelineEntityType,
			Value: timeline_common.TimelineEntityType_Diagnose,
		},
		{
			Key:   Field_IsDeleted,
			Value: false,
		},
	}
	return slice.ToValueTypeWithError(t.Repo.Find(ctx, filter))
}

func (t *TimelineEntityRepo[T]) GetDiagnoseUnique(ctx *titan.Context, req GetDiagnoseUniqueRequest) ([]patient_encounter.EncounterDiagnoseTimeline, error) {
	matchState := bson.M{
		Field_IsDeleted:          false,
		Field_PatientId:          req.PatientId,
		Field_timelineEntityType: timeline_common.TimelineEntityType_Diagnose,
		Field_ICD_Code:           bson.M{"$ne": ""},
	}

	var timelineDiagnoses []TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]
	data, err := t.Repo.Find(ctx, matchState, options.Find().SetSort(bson.M{Field_CreatedAt: -1}))
	if err != nil {
		return nil, err
	}

	for _, v := range data {
		timelineDiagnoses = append(timelineDiagnoses, ToEntityTypeFromT[T, patient_encounter.EncounterDiagnoseTimeline](*v))
	}

	var a []patient_encounter.EncounterDiagnoseTimeline

	for _, v := range timelineDiagnoses {
		a = append(a, v.Payload)
	}

	timelineDiagnosesUnique := slice.UniqBy(a, func(t patient_encounter.EncounterDiagnoseTimeline) string {
		return fmt.Sprintf("%s-%s-%s-%s-%s", t.Code, t.Command, t.Description, util.GetPointerValue(t.Certainty), util.GetPointerValue(t.Laterality))
	})

	return timelineDiagnosesUnique, nil
}

func (t *TimelineEntityRepo[T]) GetDiagnoseByPatient(ctx *titan.Context, patientId uuid.UUID) ([]TimelineEntity[patient_encounter.EncounterDiagnoseTimeline], error) {
	matchState := bson.M{
		Field_IsDeleted:          false,
		Field_PatientId:          patientId,
		Field_timelineEntityType: timeline_common.TimelineEntityType_Diagnose,
	}

	var timelineDiagnoses []TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]
	data, err := t.Repo.Find(ctx, matchState, options.Find().SetSort(bson.M{Field_CreatedAt: -1}))
	if err != nil {
		return nil, err
	}

	for _, v := range data {
		timelineDiagnoses = append(timelineDiagnoses, ToEntityTypeFromT[T, patient_encounter.EncounterDiagnoseTimeline](*v))
	}

	return timelineDiagnoses, nil
}

func (t *TimelineEntityRepo[T]) FindDiagnosisDMPs(ctx *titan.Context, patientIds []uuid.UUID) ([]TimelineEntity[T], error) {
	filter := bson.M{
		Field_IsDeleted:          false,
		Field_PatientId:          bson.M{"$in": patientIds},
		Field_timelineEntityType: timeline_common.TimelineEntityType_Diagnose,
		Field_Payload_Error_Code: error_code.ErrorCode_Validation_EDMPSuggestion,
	}
	return slice.ToValueTypeWithError(t.Repo.Find(ctx, filter))
}

func (t *TimelineEntityRepo[T]) UpdateDiagnosesScheinId(ctx *titan.Context, ids []uuid.UUID, newScheinId uuid.UUID) error {
	_, err := t.Repo.IDBClient.UpdateMany(ctx, bson.M{
		Field_Id: bson.M{
			"$in": ids,
		},
		Field_timelineEntityType: timeline_common.TimelineEntityType_Diagnose,
		Field_IsDeleted:          false,
	}, bson.M{
		"$push": bson.M{
			Field_ScheinIds: newScheinId,
		},
		"$set": bson.M{
			Field_UpdatedAt: util.Now(ctx),
		},
	},
		nil,
		nil,
	)
	return err
}

func (r *TimelineEntityRepo[T]) MarkTreatmentRelevantOnDiagnosis(ctx *titan.Context, diagnosisTimeline *TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) error {
	filter := bson.D{
		{Key: Field_PatientId, Value: diagnosisTimeline.PatientId},
		{Key: Field_timelineEntityType, Value: timeline_common.TimelineEntityType_Diagnose},
		{Key: Field_Code, Value: diagnosisTimeline.Payload.Code},
		{Key: Field_Quarter, Value: diagnosisTimeline.Quarter},
		{Key: Field_Year, Value: diagnosisTimeline.Year},
		{Key: Field_Certainty, Value: diagnosisTimeline.Payload.Certainty},
		{Key: Field_Laterality, Value: diagnosisTimeline.Payload.Laterality},
		{Key: Field_Command, Value: diagnosisTimeline.Payload.Command},
	}
	update := bson.M{
		"$set": bson.M{
			Field_MarkedTreatmentRelevant: true,
			Field_UpdatedAt:               util.Now(ctx),
		},
	}
	_, err := r.IDBClient.UpdateMany(ctx, filter, update, nil, nil)
	if err != nil {
		return fmt.Errorf("cannot update diagnosis marked treatment relevant : %w", err)
	}

	return nil
}

func (r *TimelineEntityRepo[T]) GetTakeOverDiagnosis(ctx *titan.Context, request *GetTakeOverDiagnosisRequest) ([]TimelineEntity[T], error) {
	matchSteps := bson.D{
		{Key: Field_PatientId, Value: request.PatientId},
		{Key: Field_timelineEntityType, Value: timeline_common.TimelineEntityType_Diagnose},
		{Key: Field_IsDeleted, Value: false},
	}

	if request.StartDate != nil {
		beginOfDate := util.GetStartOfDay(*request.StartDate)
		matchSteps = append(matchSteps, bson.E{
			Key: Field_Selected_Date,
			Value: bson.M{
				operator.Gte: beginOfDate,
			},
		})
	}

	if request.EndDate != nil {
		endOfDate := util.GetEndOfDay(*request.EndDate)
		matchSteps = append(matchSteps, bson.E{
			Key: Field_Selected_Date,
			Value: bson.M{
				operator.Lte: endOfDate,
			},
		})
	}

	if request.ScheinId != nil {
		matchSteps = append(matchSteps, bson.E{
			Key:   Field_ScheinIds,
			Value: bson.M{operator.Ne: request.ScheinId},
		})
	}

	if request.Query != "" {
		regexSearch := primitive.Regex{Pattern: request.Query, Options: "i"}
		orConditions := bson.A{
			bson.D{{Key: Field_ICD_Code, Value: regexSearch}},
			bson.D{{Key: Field_Command, Value: regexSearch}},
			bson.D{{Key: Field_Certainty, Value: regexSearch}},
			bson.D{{Key: Field_Laterality, Value: regexSearch}},
			bson.D{{Key: Field_Description, Value: regexSearch}},
		}
		matchSteps = append(matchSteps, bson.E{Key: operator.Or, Value: orConditions})
	}

	steps := bson.A{
		bson.M{
			operator.Match: matchSteps,
		},
		bson.M{
			operator.Group: bson.M{
				"_id": bson.M{
					"code":                    fmt.Sprintf("$%s", Field_Code),
					"certainty":               fmt.Sprintf("$%s", Field_Certainty),
					"laterality":              fmt.Sprintf("$%s", Field_Laterality),
					"command":                 fmt.Sprintf("$%s", Field_Command),
					"markedTreatmentRelevant": fmt.Sprintf("$%s", Field_MarkedTreatmentRelevant),
				},
				"doc": bson.M{"$first": "$$ROOT"},
			},
		},
		bson.M{operator.ReplaceRoot: bson.M{"newRoot": "$doc"}},
		bson.M{
			operator.Sort: bson.M{
				Field_Selected_Date: -1,
			},
		},
	}

	return slice.ToValueTypeWithError(r.Aggregate(ctx, steps))
}

// NOTE: keep the function name for keep tracking
// GetEncounterByP4ValidationReport gets all diagnoses of a quarter having Certainty value is mvz_patient_encounter.G
func (r *TimelineEntityRepo[T]) GetEncounterByP4ValidationReport(ctx *titan.Context, request GetEncounterByP4ValidationReportRequest) ([]TimelineEntity[T], error) {
	filterBuilder := &FilterBuilder{}
	filterVal := filterBuilder.
		Init().
		AndPatient(request.PatientId).
		AndContractId(request.ContractId).
		AndQuarterYear(request.Quarter, request.Year).
		AndType(string(timeline_common.TimelineEntityType_Diagnose)).
		AndDiagnoseCertainty("G"). // ABRD967
		Build()

	return slice.ToValueTypeWithError(r.Find(ctx, *filterVal))
}

func (r *TimelineEntityRepo[T]) GetDiagnosisInCurrentDate(ctx *titan.Context, patientId uuid.UUID) ([]TimelineEntity[T], error) {
	now := util.ConvertTimeToUTC(util.Now(ctx))
	beginOfDate, _ := util.GetDayRange(now.UTC())
	filter := bson.D{
		{
			Key:   Field_PatientId,
			Value: patientId,
		},
		{
			Key:   Field_timelineEntityType,
			Value: timeline_common.TimelineEntityType_Diagnose,
		},
		{
			Key:   Field_IsDeleted,
			Value: false,
		},
		{
			Key:   Field_Selected_Date,
			Value: beginOfDate,
		},
	}
	return slice.ToValueTypeWithError(r.Find(ctx, filter))
}
