package timeline_repo

import (
	"fmt"
	"sort"
	"time"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/diga"
	mongodb "git.tutum.dev/medi/tutum/ares/pkg/repo"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	"git.tutum.dev/medi/tutum/ares/service/contract/contract/model"
	medicine_common "git.tutum.dev/medi/tutum/ares/service/domains/api/medicine_common"
	doctor_letter_common "git.tutum.dev/medi/tutum/ares/service/domains/doctor_letter/common"
	edmp_common "git.tutum.dev/medi/tutum/ares/service/domains/edmp/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	mail_common "git.tutum.dev/medi/tutum/ares/service/mail/common"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"

	arriba_common "git.tutum.dev/medi/tutum/ares/service/arriba/common"
	api_common "git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/lab_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	timeline_common "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"

	"git.tutum.dev/medi/tutum/ares/pkg/operator"
	"github.com/golang-module/carbon"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var diagnosisFilter = []timeline_common.TimelineEntityType{
	timeline_common.TimelineEntityType_Diagnose,
	timeline_common.TimelineEntityType_Diagnose_DD,
	timeline_common.TimelineEntityType_Diagnose_AD,
}

var mapDiagnosisFilterWithBsonFilterCommand = map[timeline_common.TimelineEntityType]bson.M{
	timeline_common.TimelineEntityType_Diagnose: {
		Field_timelineEntityType: timeline_common.TimelineEntityType_Diagnose,
		Field_Command:            "D",
	},
	timeline_common.TimelineEntityType_Diagnose_DD: {
		Field_timelineEntityType: timeline_common.TimelineEntityType_Diagnose,
		Field_Command:            "DD",
	},
	timeline_common.TimelineEntityType_Diagnose_AD: {
		Field_timelineEntityType: timeline_common.TimelineEntityType_Diagnose,
		Field_Command:            "AD",
	},
}

var NoteTimelineEntryTypes = []timeline_common.TimelineEntityType{
	timeline_common.TimelineEntityType_Anamnese,
	timeline_common.TimelineEntityType_Finding,
	timeline_common.TimelineEntityType_Therapy,
	timeline_common.TimelineEntityType_Note,
}

var mapNoteFilterWithBson = map[timeline_common.TimelineEntityType]bson.M{
	timeline_common.TimelineEntityType_Anamnese: {
		Field_timelineEntityType:   timeline_common.TimelineEntityType_Note,
		Field_Payload_TemplateType: patient_encounter.ANAMNESE,
	},
	timeline_common.TimelineEntityType_Finding: {
		Field_timelineEntityType:   timeline_common.TimelineEntityType_Note,
		Field_Payload_TemplateType: patient_encounter.FINDING,
	},
	timeline_common.TimelineEntityType_Therapy: {
		Field_timelineEntityType:   timeline_common.TimelineEntityType_Note,
		Field_Payload_TemplateType: patient_encounter.THERAPY,
	},
	timeline_common.TimelineEntityType_Note: {
		Field_timelineEntityType:   timeline_common.TimelineEntityType_Note,
		Field_Payload_TemplateType: patient_encounter.NOTE,
	},
}

// Private Billing have Doctor letter type and payload.privateinvoice != null
var DoctorLetterTimelineEntryTypes = []timeline_common.TimelineEntityType{
	timeline_common.TimelineEntityType_DoctorLetter,
	timeline_common.TimelineEntityType_Private_Billing,
	timeline_common.TimelineEntityType_BG_Invoice,
}

var mapDoctorLetterFilterWithBson = map[timeline_common.TimelineEntityType]bson.M{
	timeline_common.TimelineEntityType_DoctorLetter: {
		Field_timelineEntityType:     timeline_common.TimelineEntityType_DoctorLetter,
		Field_Payload_PrivateInvoice: bson.M{operator.Eq: nil},
	},
	timeline_common.TimelineEntityType_Private_Billing: {
		Field_timelineEntityType:     timeline_common.TimelineEntityType_DoctorLetter,
		Field_Payload_PrivateInvoice: bson.M{operator.Ne: nil},
	},
	timeline_common.TimelineEntityType_BG_Invoice: {
		Field_timelineEntityType: timeline_common.TimelineEntityType_DoctorLetter,
		Field_Payload_BgInvoice:  bson.M{operator.Ne: nil},
	},
}

var gdtFilter = []timeline_common.TimelineEntityType{
	timeline_common.TimelineEntityType_GDT,
}

var mapGdtFilterWithBson = map[timeline_common.TimelineEntityType]bson.M{
	timeline_common.TimelineEntityType_GDT: {
		Field_timelineEntityType: timeline_common.TimelineEntityType_GDT,
	},
}

type TimelineEntityConstant interface {
	patient_encounter.EncounterMedicinePlanHistory |
		patient_encounter.EncounterMedicinePrescription |
		patient_encounter.EncounterHimiPrescription |
		patient_encounter.EncounterPatientMedicalData |
		patient_encounter.EncounterHeimiPrescription |
		patient_encounter.EncounterLab |
		patient_encounter.EncounterForm |
		patient_encounter.EncounterCalendarTimeline |
		patient_encounter.EncounterDiagnoseTimeline |
		patient_encounter.EncounterServiceTimeline |
		patient_encounter.EncounterNoteTimeline |
		mail_common.EmailItem |
		doctor_letter_common.DoctorLetter |
		timeline_common.ReferencePayload |
		timeline_common.EHICPayload |
		patient_encounter.EncounterPsychotherapy |
		edmp_common.EnrollmentInfo |
		edmp_common.DocumentationOverview |
		medicine_common.Medicine |
		patient_encounter.EncounterGoaService |
		diga.Prescribe |
		patient_encounter.EncounterDocumentManagement |
		patient_encounter.EncounterArriba |
		patient_encounter.EncounterGDT |
		patient_encounter.EncounterLDT |
		patient_encounter.EncounterCustomize |
		edmp_common.EdokuDocumentationOverview |
		any
}

const (
	Field_Id                                      = "_id"
	Field_IsDeleted                               = "isDeleted"
	Field_Quarter                                 = "quarter"
	Field_Year                                    = "year"
	Field_PatientId                               = "patientId"
	Field_RecentAuditLogs                         = "recentAuditLogs"
	Field_timelineEntityType                      = "timelineEntityType"
	Field_contractId                              = "contractId"
	Field_CreatedAt                               = "createdAt"
	Field_UpdatedAt                               = "updatedAt"
	Field_TreatmentDoctorId                       = "treatmentDoctorId"
	Field_Payload                                 = "payload"
	Field_EncounterCase                           = "encounterCase"
	Field_ScheinIds                               = "scheinIds"
	Field_BillingInfo                             = "billingInfo"
	Field_BillingInfo_BillingStatus               = "billingInfo.billingStatus"
	Field_CreatedAt_String                        = "createdAtString"
	Field_BillingDoctorId                         = "billingDoctorId"
	Field_ServiceMainGroup                        = "serviceMainGroup"
	Field_Code                                    = "payload.code"
	Field_Psychotherapy_ApprovalStatus            = "payload.approvalStatus"
	Field_AuditLogId                              = "auditLogId"
	Field_Status                                  = "status"
	Field_IsImported                              = "isImported"
	Field_Payload_Error_Code                      = "payload.errors.errorcode"
	Field_ICD_Code                                = "payload.code"
	Field_Description                             = "payload.description"
	Field_Certainty                               = "payload.certainty"
	Field_Laterality                              = "payload.laterality"
	Field_Command                                 = "payload.command"
	Field_MarkedTreatmentRelevant                 = "payload.markedtreatmentrelevant"
	Field_ValidUntil                              = "payload.validUntil"
	Field_DiagnoseType                            = "payload.type"
	Field_DiagnoseRunSdkrw                        = "payload.runSdkrw"
	Field_DiagnoseSuggestions                     = "payload.diagnoseSuggestions"
	Field_Payload_Errors                          = "payload.errors"
	Field_Error                                   = "errors"
	Field_AmountBilled                            = "payload.amountBilled"
	Field_ServiceCodeApproval                     = "payload.serviceCodes"
	Field_EntryIdApproval                         = "payload.entryIdApproval"
	Field_Psychotherapy_Status                    = "payload.status"
	Field_Psychotherapy_Entries                   = "payload.entries"
	Field_Psychotherapy_TakeOverId                = "payload.takeOverId"
	Field_Psychotherapy_ScheinId                  = "payload.scheinId"
	Field_Psychotherapy_TerminalServiceId         = "payload.terminateServiceId"
	Field_Selected_Date                           = "selectedDate"
	Field_Psychotherapy_ApprovalDate              = "payload.approvalDate"
	Field_EnrollmentId                            = "payload.enrollmentid"
	Field_EAUStatus                               = "payload.prescribe.eaustatus"
	Field_EAUCancellationBundleUrl                = "payload.prescribe.eaucancellationbundleurl"
	Field_Payload_PrivateInvoice                  = "payload.privateinvoice"
	Field_Payload_PrivateInvoice_PrivateBillingId = "payload.privateinvoice.privatebillingid"
	Field_Payload_BgInvoice                       = "payload.bginvoice"
	Field_Payload_BgInvoice_BillingId             = "payload.bginvoice.billingid"
	Field_Payload_PrivateInvoice_Status           = "payload.privateinvoice.status"
	Field_Payload_BgInvoice_Status                = "payload.bginvoice.status"
	Field_Payload_TemplateType                    = "payload.type"
	Field_MedicinePrescription_PrintDate          = "payload.forminfos.printdate"
	Field_MedicinePrescription_Medicines          = "payload.forminfos.medicines"
	Field_MedicineERezeptStatus                   = "erezeptitemstatus"
	Field_DoctorLetterStatus                      = "payload.status"
	Field_Sources                                 = "payload.sources"
	Field_Payload_Id                              = "payload.id"
	Field_Payload_DocumentManagement_IsRead       = "payload.isread"
	Field_Payload_DocumentManagement_ReadById     = "payload.readBy.id"
	Field_MedicineId                              = "payload.forminfos.medicines.id"
	Field_EABId                                   = "payload.eabid"
	Field_PrescribeId                             = "payload.prescribe.id"
	Field_PayloadContractId                       = "payload.prescribe.contractid"
	Field_Mail_MessageId                          = "payload.messageid"
	Field_FormInfos_Id                            = "payload.forminfos.id"
	Field_Set_FormInfos_PrintDate                 = "payload.forminfos.$.printdate"
	Field_Set_Medicine_ErezeptStatus              = "payload.forminfos.$[].medicines.$[s].erezeptitemstatus"
	Field_Set_FormInfos_TreatmentDoctorId         = "payload.forminfos.$.treatmentdoctorid"
	Field_Set_Prescribe_PrintDate                 = "payload.prescribe.printdate"
	Field_Arriba_SessionId                        = "payload.sessionId"
	Field_Arriba_CompanionFilePaths               = "payload.companionfilepaths"
	Field_Arriba_Status                           = "payload.status"
	Field_AdditionalInfos_FK                      = "payload.additionalInfos.fK"
	Field_Payload_Prescribe                       = "payload.prescribe"
	Field_Payload_Prescribe_PrintDate             = "payload.prescribe.printeddate"
	Field_Payload_ChainID                         = "chainId"
	Field_Payload_DocumentStatus                  = "payload.documentstatus"
	Field_Payload_DocumentationOverviewId         = "payload.documentationoverviewid"
	Field_Payload_DocumentEDTId                   = "payload.documentmanagementid"
	Field_Payload_DocumentManagementId            = "payload.id"
)

const FormatDate = "02.01.2006"

type AuditLog struct {
	AuditLogId *uuid.UUID                 `json:"auditLogId,omitempty" bson:"auditLogId,omitempty"`
	UserId     *uuid.UUID                 `json:"userId,omitempty" bson:"userId,omitempty"`
	Date       time.Time                  `json:"date,omitempty" bson:"date,omitempty"`
	ActionType timeline_common.ActionType `json:"actionType,omitempty" bson:"actionType,omitempty"`
}

type (
	TimelineEntity[Payload TimelineEntityConstant] struct {
		Id                            *uuid.UUID                             `bson:"_id"`
		ContractId                    *model.ContractId                      `bson:"contractId"`
		TreatmentDoctorId             uuid.UUID                              `bson:"treatmentDoctorId"`
		BillingDoctorId               *uuid.UUID                             `bson:"billingDoctorId"`
		PatientId                     uuid.UUID                              `bson:"patientId"`
		BillingInfo                   *api_common.BillingInfo                `bson:"billingInfo"`
		ScheinIds                     []uuid.UUID                            `bson:"scheinIds"`
		EncounterCase                 *patient_encounter.EncounterCase       `bson:"encounterCase"`
		TreatmentCase                 *patient_encounter.TreatmentCase       `bson:"treatmentCase"`
		Payload                       Payload                                `bson:"payload"`
		Type                          timeline_common.TimelineEntityType     `bson:"timelineEntityType"`
		RecentAuditLogs               []AuditLog                             `bson:"recentAuditLogs"` // keep 5 recent auditLogs. All auditLogs still keep in AuditLog collection
		CreatedAt                     time.Time                              `bson:"createdAt"`
		Quarter                       int                                    `bson:"quarter"`
		Year                          int                                    `bson:"year"`
		CreatedAtString               string                                 `bson:"createdAtString"` // dd.MM.yyyy
		CreatedBy                     uuid.UUID                              `bson:"createdBy"`
		IsDeleted                     bool                                   `bson:"isDeleted"`
		Errors                        []patient_encounter.EncounterItemError `bson:"errors"`
		SelectedDate                  time.Time                              `bson:"selectedDate"`
		UpdatedAt                     time.Time                              `bson:"updatedAt"`
		IsImported                    bool                                   `bson:"isImported"`
		ChainId                       *uuid.UUID                             `bson:"chainId"`
		EncounterServiceChainRaw      *string                                `bson:"encounterServiceChainRaw"`
		EncounterGoaServiceChainRaw   *string                                `bson:"encounterGoaServiceChainRaw"`
		EncounterUvGoaServiceChainRaw *string                                `bson:"encounterUvGoaServiceChainRaw"`
		AssignedToBsnrId              *uuid.UUID                             `bson:"assignedToBsnrId,omitempty"`
	}
	UpdateMailDateByIdRequest struct {
		MessageId    string
		PatientId    uuid.UUID
		UpdatedAt    time.Time
		SelectedDate *time.Time
	}
	GetLastDocumentedEntityRequest struct {
		PatientId          uuid.UUID
		Year               *int32
		Quarter            *int32
		TimelineEntityType *timeline_common.TimelineEntityType
	}
	GroupPatientIdWithTotalErrors struct {
		PatientId  uuid.UUID `bson:"_id"`
		TotalError int64     `bson:"totalError"`
	}
	TimelinePagination[T TimelineEntityConstant] struct {
		TotalPatient int32
		Data         []TimelineEntity[T]
	}
	GroupByPatientIdWithScheinByKVTimeLines struct {
		Id struct {
			PatientId uuid.UUID `bson:"patientId"`
			ScheinId  uuid.UUID `bson:"scheinId"`
		} `bson:"_id"`
	}
	FindLatestTimelineEntryRequest struct {
		PatientId         uuid.UUID
		TimelineEntryType timeline_common.TimelineEntityType
		ContractId        string
	}

	GetTimelinesByCondition struct {
		PatientId           uuid.UUID
		TimelineEntityTypes []timeline_common.TimelineEntityType
		StartTime           *int64
		EndTime             *int64
		ScheinId            uuid.UUID
	}
)

// NOTE: duplicate func in timeline_service
func getPayloadByType[T TimelineEntityConstant](payload any) *T {
	if payload == nil {
		return nil
	}
	_, ok := payload.(primitive.D) // in case get data
	if !ok {
		var result T
		err := copier.Copy(&result, payload)
		if err != nil {
			panic(err)
		}
		return &result
	}
	data, err := bson.MarshalWithRegistry(mongodb_repo.MongoRegistry, payload)
	if err != nil {
		panic(err)
	}
	var result *T
	err = bson.UnmarshalWithRegistry(mongodb_repo.MongoRegistry, data, &result)
	if err != nil {
		panic(err)
	}
	return result
}

func ToEntityTypeFromT[T, V TimelineEntityConstant](entity TimelineEntity[T]) TimelineEntity[V] {
	return TimelineEntity[V]{
		Id:                entity.Id,
		ChainId:           entity.ChainId,
		ContractId:        entity.ContractId,
		TreatmentDoctorId: entity.TreatmentDoctorId,
		BillingDoctorId:   entity.BillingDoctorId,
		PatientId:         entity.PatientId,
		BillingInfo:       entity.BillingInfo,
		ScheinIds:         entity.ScheinIds,
		EncounterCase:     entity.EncounterCase,
		TreatmentCase:     entity.TreatmentCase,
		Payload:           *getPayloadByType[V](entity.Payload),
		Type:              entity.Type,
		RecentAuditLogs:   entity.RecentAuditLogs,
		CreatedAt:         entity.CreatedAt,
		Quarter:           entity.Quarter,
		Year:              entity.Year,
		CreatedAtString:   entity.CreatedAtString,
		CreatedBy:         entity.CreatedBy,
		IsDeleted:         entity.IsDeleted,
		Errors:            entity.Errors,
		SelectedDate:      entity.SelectedDate,
		UpdatedAt:         entity.UpdatedAt,
	}
}

func (e TimelineEntity[T]) GetId() *uuid.UUID {
	return e.Id
}

func (e TimelineEntity[T]) GetBsnr() *uuid.UUID {
	return e.AssignedToBsnrId
}

func (e *TimelineEntity[T]) SetBsnr(id *uuid.UUID) {
	e.AssignedToBsnrId = id
}

func (*TimelineEntity[T]) GetPatientFieldName() string {
	return Field_PatientId
}

type TimelineEntityRepo[T TimelineEntityConstant] struct {
	mongodb.Repo[*TimelineEntity[T]]
}

func NewTimelineRepoDefaultRepository[T TimelineEntityConstant]() TimelineEntityRepo[T] {
	return TimelineEntityRepo[T]{
		mongodb.NewRepo[*TimelineEntity[T]](mongodb_repo.NewMongoDbIDBClient("tutum_mvz_db", "timeline", true)),
	}
}

type (
	GroupByQuarter[T TimelineEntityConstant] struct {
		Year             int
		Quarter          int
		TimelineEntities []TimelineEntity[T]
		IsVSST785        bool
	}
	GroupByQuarterRequest struct {
		PatientId             uuid.UUID
		FromDate              *time.Time
		ToDate                *time.Time
		TimelineEntityType    []timeline_common.TimelineEntityType
		IsSortByCategory      bool
		IsHistoryMode         bool
		ScheinId              *uuid.UUID
		Keyword               *string
		Year                  *int32
		Quarter               *int32
		IncludedIds           []uuid.UUID
		CustomizeCommands     *[]string
		TimelineEntrySettings map[string]int32
	}
	GetRequest struct {
		PatientId           uuid.UUID
		ContractId          *string
		CreatedDate         *time.Time
		EncounterCase       *string
		TreatmentDoctorId   *uuid.UUID
		TimelineEntityTypes []timeline_common.TimelineEntityType
	}
	PatientWithScheinIds struct {
		PatientId uuid.UUID
		ScheinIds []uuid.UUID
	}
	GetTimelinesForBillingByScheinIdsRequest struct {
		PatientWithScheinIds  []PatientWithScheinIds
		ExcludeEABServiceCode *bool
	}
)

func (t *TimelineEntityRepo[T]) DeleteByPatientId(ctx *titan.Context, patientId uuid.UUID) error {
	_, err := t.Delete(ctx, bson.M{
		Field_PatientId: patientId,
	})
	return err
}

func (t *TimelineEntityRepo[T]) Get(ctx *titan.Context, req GetRequest) ([]TimelineEntity[T], error) {
	filter := bson.M{
		Field_PatientId:         req.PatientId,
		Field_IsDeleted:         false,
		Field_contractId:        req.ContractId,
		Field_TreatmentDoctorId: req.TreatmentDoctorId,
	}

	if len(req.TimelineEntityTypes) > 0 {
		filter[Field_timelineEntityType] = bson.M{"$in": req.TimelineEntityTypes}
	}

	if req.CreatedDate != nil {
		beginOfDate, endOfDate := util.GetDayRange(*req.CreatedDate)
		filter[Field_Selected_Date] = bson.M{
			"$gte": beginOfDate,
			"$lte": endOfDate,
		}
	}
	return slice.ToValueTypeWithError(t.Find(ctx, filter))
}

func (t *TimelineEntityRepo[T]) GetCountValidationsByPatientIds(
	ctx *titan.Context,
	patientIds []uuid.UUID,
	selectedStartDate, selectedEndDate time.Time,
) (map[uuid.UUID]int64, error) {
	groupState := []bson.M{
		{
			"$match": bson.M{
				Field_PatientId: bson.M{
					"$in": patientIds,
				},
				Field_IsDeleted: false,
				Field_CreatedAt: bson.M{
					"$gte": selectedStartDate,
					"$lte": selectedEndDate,
				},
			},
		},
		{
			"$group": bson.M{
				"_id": "$patientId",
				"totalError": bson.M{
					"$sum": bson.M{
						"$cond": bson.A{
							bson.M{"$isArray": "$payload.errors"},
							bson.M{"$size": "$payload.errors"},
							0,
						},
					},
				},
			},
		},
	}
	var result []GroupPatientIdWithTotalErrors
	err := t.IDBClient.Aggregate(ctx, groupState, &result)
	if err != nil {
		return nil, err
	}
	if len(result) == 0 {
		return nil, nil
	}

	res := make(map[uuid.UUID]int64)
	for _, item := range result {
		res[item.PatientId] = item.TotalError
	}
	return res, nil
}

func (t *TimelineEntityRepo[T]) UpdateMany(ctx *titan.Context, req []*TimelineEntity[T], fns ...func(e TimelineEntity[T], update bson.M) bson.M) ([]TimelineEntity[T], error) {
	if len(req) == 0 {
		return nil, nil
	}

	updateModels := slice.Map(req, func(t *TimelineEntity[T]) mongo.WriteModel {
		update := bson.M{
			"payload":             t.Payload,
			repos.Field_UpdatedAt: util.Now(ctx),
			repos.Field_UpdatedBy: ctx.UserInfo().UserUUID(),
			Field_BillingInfo:     t.BillingInfo,
		}
		for _, fn := range fns {
			update = fn(*t, update)
		}

		return mongo.NewUpdateOneModel().
			SetFilter(bson.M{
				Field_Id:        t.Id,
				Field_IsDeleted: false,
			}).
			SetUpdate(bson.M{
				"$set": update,
				"$push": bson.M{
					Field_RecentAuditLogs: newAuditLog(ctx, ctx.UserInfo().UserUUID(), timeline_common.Edit),
				},
			})
	})

	if err := t.BulkWrite(ctx, updateModels, nil); err != nil {
		return nil, fmt.Errorf("update many timeline error: %w", err)
	}

	timelineIds := slice.Map(req, func(t *TimelineEntity[T]) uuid.UUID {
		return *t.Id
	})

	result, err := t.FindByIds(ctx, timelineIds)
	if err != nil {
		return nil, errors.WithMessage(err, "when update many on timeline")
	}
	return result, nil
}

func (t *TimelineEntityRepo[T]) Update(ctx *titan.Context, model TimelineEntity[T]) (*TimelineEntity[T], error) {
	return t.EditTimelineById(ctx, model)
}

func (t *TimelineEntityRepo[T]) GroupByQuarter(ctx *titan.Context, req GroupByQuarterRequest) ([]GroupByQuarter[T], error) {
	var excludeTimelineEntityType []timeline_common.TimelineEntityType

	filterOr := []bson.M{}
	filter := bson.M{
		Field_PatientId: req.PatientId,
	}

	if req.Year != nil {
		filter[Field_Year] = *req.Year
		if req.Quarter != nil {
			filter[Field_Quarter] = *req.Quarter
		}
	}

	if !req.IsHistoryMode {
		filter[Field_IsDeleted] = false
	} else {
		excludeTimelineEntityType = append(excludeTimelineEntityType, timeline_common.TimelineEntityType_Psychotherapy, timeline_common.TimelineEntityType_DoctorLetter)
	}

	filterTimelineEntityType := slice.Filter(req.TimelineEntityType, func(tet timeline_common.TimelineEntityType) bool {
		return !slice.Contains(excludeTimelineEntityType, tet) &&
			!slice.Contains(NoteTimelineEntryTypes, tet) &&
			!slice.Contains(DoctorLetterTimelineEntryTypes, tet) &&
			!slice.Contains(diagnosisFilter, tet)
	})
	newFilterOr := slice.Reduce(req.TimelineEntityType, func(acc []bson.M, cur timeline_common.TimelineEntityType) []bson.M {
		switch {
		case slice.Contains(NoteTimelineEntryTypes, cur):
			acc = append(acc, mapNoteFilterWithBson[cur])
		case slice.Contains(DoctorLetterTimelineEntryTypes, cur):
			acc = append(acc, mapDoctorLetterFilterWithBson[cur])
		case slice.Contains(diagnosisFilter, cur):
			acc = append(acc, mapDiagnosisFilterWithBsonFilterCommand[cur])
		case slice.Contains(gdtFilter, cur):
			acc = append(acc, mapGdtFilterWithBson[cur])
		}
		return acc
	}, []bson.M{})
	if req.CustomizeCommands != nil {
		filterOr = append(filterOr, bson.M{
			Field_timelineEntityType: timeline_common.TimelineEntityType_Customize,
			Field_Command: bson.M{
				operator.In: *req.CustomizeCommands,
			},
		})
		if len(newFilterOr) > 0 {
			filterOr = append(filterOr, newFilterOr...)
		}
		if len(filterTimelineEntityType) > 0 {
			filterOr = append(filterOr, bson.M{Field_timelineEntityType: bson.M{operator.In: filterTimelineEntityType}})
		}
	} else {
		if len(req.TimelineEntityType) > 0 {
			// Check if newFilterOr or filterTimelineEntityType have values we add to $or expressions
			if len(newFilterOr) > 0 {
				if len(filterTimelineEntityType) > 0 {
					filterOr = append(newFilterOr, bson.M{Field_timelineEntityType: bson.M{operator.In: filterTimelineEntityType}})
				} else {
					filterOr = newFilterOr
				}
			} else if len(filterTimelineEntityType) > 0 {
				filter[Field_timelineEntityType] = bson.M{operator.In: filterTimelineEntityType}
			}
		} else if len(excludeTimelineEntityType) > 0 {
			filter[Field_timelineEntityType] = bson.M{
				"$nin": excludeTimelineEntityType,
			}
		}
	}

	if len(req.IncludedIds) != 0 {
		filter[Field_Id] = bson.M{
			"$in": req.IncludedIds,
		}
	}

	if req.FromDate != nil && req.ToDate != nil {
		fromDate, _ := util.GetDayRange(*req.FromDate)
		_, toDate := util.GetDayRange(*req.ToDate)
		filter[Field_Selected_Date] = bson.M{
			"$gte": fromDate,
			"$lte": toDate,
		}
	} else {
		if req.FromDate != nil {
			fromDate, _ := util.GetDayRange(*req.FromDate)
			filter[Field_Selected_Date] = bson.M{
				"$gte": fromDate,
			}
		}
		if req.ToDate != nil {
			_, toDate := util.GetDayRange(*req.ToDate)
			filter[Field_Selected_Date] = bson.M{
				"$lte": toDate,
			}
		}
	}

	if len(filterOr) > 0 {
		filter["$or"] = filterOr
	}
	entities, err := t.Find(
		ctx,
		filter,
		options.Find().SetSort(bson.D{
			{
				Key:   Field_Selected_Date,
				Value: 1,
			},
			{
				Key:   Field_CreatedAt,
				Value: 1,
			},
		}),
	)
	if err != nil {
		return nil, err
	}
	if len(entities) == 0 {
		return nil, nil
	}
	if req.ScheinId != nil {
		entities = slice.Filter(entities, func(t *TimelineEntity[T]) bool {
			return slice.Contains(t.ScheinIds, *req.ScheinId) || !slice.Contains(diagnosisFilter, t.Type)
		})
	}
	groupByQuarters := make([]GroupByQuarter[T], 0)
	for _, entity := range entities {
		group, idx := slice.FindOneIndex(groupByQuarters, func(t GroupByQuarter[T]) bool {
			return t.Year == entity.Year && t.Quarter == entity.Quarter
		})
		if group == nil {
			groupByQuarters = append(groupByQuarters, GroupByQuarter[T]{
				Year:             entity.Year,
				Quarter:          entity.Quarter,
				TimelineEntities: []TimelineEntity[T]{*entity},
			})
		} else {
			groupByQuarters[idx].TimelineEntities = append(groupByQuarters[idx].TimelineEntities, *entity)
		}
	}

	if req.IsSortByCategory {
		if len(groupByQuarters) > 0 {
			for _, v := range groupByQuarters {
				sort.SliceStable(v.TimelineEntities, func(i, j int) bool {
					if util.IsSameDate(v.TimelineEntities[i].SelectedDate, v.TimelineEntities[j].SelectedDate) {
						iOrder := req.TimelineEntrySettings[getRealTypeEntity(v.TimelineEntities[i])]
						jOrder := req.TimelineEntrySettings[getRealTypeEntity(v.TimelineEntities[j])]
						return iOrder < jOrder
					}
					return v.TimelineEntities[i].SelectedDate.Before(v.TimelineEntities[j].SelectedDate)
				})
			}
		}
	}

	return groupByQuarters, nil
}

func (r *TimelineEntityRepo[T]) GetLastDocumentedEntity(ctx *titan.Context, request *GetLastDocumentedEntityRequest) (*TimelineEntity[T], error) {
	filter := bson.D{
		{Key: Field_PatientId, Value: request.PatientId},
		{Key: Field_IsDeleted, Value: false},
	}

	if request.Year != nil && request.Quarter != nil {
		filterYearQuarter := bson.A{
			bson.D{
				{Key: Field_Year, Value: bson.M{operator.Lt: request.Year}},
			},
			bson.D{
				{Key: Field_Quarter, Value: bson.M{operator.Lt: request.Quarter}},
				{Key: Field_Year, Value: request.Year},
			},
		}
		filter = append(filter, bson.E{
			Key:   operator.Or,
			Value: filterYearQuarter,
		})
	}

	if request.TimelineEntityType != nil {
		filter = append(filter, bson.E{
			Key:   Field_timelineEntityType,
			Value: *request.TimelineEntityType,
		})
	}

	return r.FindOne(ctx, filter, options.FindOne().SetSort(bson.D{{Key: Field_Selected_Date, Value: -1}}))
}

func (t *TimelineEntityRepo[T]) PaginateDiagnoseTimelineByPatient(
	ctx *titan.Context,
	page, size int64,
	yearQuarter util.YearQuarter,
) (*TimelinePagination[T], error) {
	match := bson.M{
		"$match": bson.M{
			Field_Quarter:            yearQuarter.Quarter,
			Field_Year:               yearQuarter.Year,
			Field_timelineEntityType: timeline_common.TimelineEntityType_Diagnose,
			Field_IsDeleted:          false,
		},
	}
	group := bson.M{
		"$group": bson.M{
			"_id":       "$patientId",
			"patientId": bson.M{"$first": "$patientId"},
		},
	}
	skip := bson.M{"$skip": (page - 1) * size}
	limit := bson.M{"$limit": size}
	timelines, err := t.Aggregate(ctx, bson.A{match, group, skip, limit})
	if err != nil {
		return nil, err
	}
	if len(timelines) == 0 {
		return &TimelinePagination[T]{
			TotalPatient: 0,
			Data:         nil,
		}, nil
	}

	count, err := t.AggregateCount(ctx, bson.A{match, group})
	if err != nil {
		return nil, err
	}

	return &TimelinePagination[T]{
		TotalPatient: int32(count),
		Data:         slice.ToValueType(timelines),
	}, nil
}

func (t *TimelineEntityRepo[T]) FindByPatientId(ctx *titan.Context, id uuid.UUID) ([]TimelineEntity[T], error) {
	filter := bson.M{
		Field_PatientId: id,
		Field_IsDeleted: false,
	}
	return slice.ToValueTypeWithError(t.Find(ctx, filter))
}

func (t *TimelineEntityRepo[T]) FindById(ctx *titan.Context, id uuid.UUID) (*TimelineEntity[T], error) {
	filter := bson.M{
		"_id":           id,
		Field_IsDeleted: false,
	}
	return t.FindOne(ctx, filter)
}

func (t *TimelineEntityRepo[T]) FindByIds(ctx *titan.Context, ids []uuid.UUID, opts ...*options.FindOptions) ([]TimelineEntity[T], error) {
	filter := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
		Field_IsDeleted: false,
	}
	return slice.ToValueTypeWithError(t.Find(ctx, filter, opts...))
}

func (t *TimelineEntityRepo[T]) FindByIdsNotFilterDelete(ctx *titan.Context, ids []uuid.UUID, opts ...*options.FindOptions) ([]TimelineEntity[T], error) {
	filter := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
	}
	return slice.ToValueTypeWithError(t.Find(ctx, filter, opts...))
}

func (t *TimelineEntityRepo[T]) FindByIdNotFilterDelete(ctx *titan.Context, id uuid.UUID) (*TimelineEntity[T], error) {
	filter := bson.M{
		"_id": id,
	}
	return t.FindOne(ctx, filter)
}

// EditTimelineByIds
func (t *TimelineEntityRepo[T]) EditTimelineByIds(ctx *titan.Context, entities []TimelineEntity[T]) ([]TimelineEntity[T], error) {
	return t.UpdateMany(ctx, slice.ToPointerType(entities))
}

func (t *TimelineEntityRepo[T]) UpdateLabResultStatus(ctx *titan.Context, labId uuid.UUID, labResultStatus lab_common.LabResultStatus, labFormStatus lab_common.LabFormStatus) (*TimelineEntity[T], error) {
	return t.Repo.FindOneAndUpdate(
		ctx,
		bson.M{
			Field_IsDeleted:  false,
			Field_Payload_Id: labId,
		},
		bson.M{
			"$set": bson.M{
				"payload.labresultstatus":       labResultStatus,
				"payload.labform.labformstatus": labFormStatus,
			},
		},
		options.FindOneAndUpdate().SetReturnDocument(options.After),
	)
}

type FormInfoTimeline struct {
	ContractId *string
	PatientId  uuid.UUID
	common.FormInfo
}

type ScheinIdsWithBillCheck struct {
	Id       uuid.UUID
	IsBilled bool
}

func (t *TimelineEntityRepo[T]) UpdateHimiPrintDate(ctx *titan.Context, himiPrescriptionId uuid.UUID, printDate int64) (*TimelineEntity[T], error) {
	filter := bson.M{
		Field_IsDeleted:              false,
		"payload.himiprescriptionid": himiPrescriptionId,
		Field_timelineEntityType:     timeline_common.TimelineEntityType_HimiPrescription,
	}

	update := bson.M{
		"$set": bson.M{
			"payload.printdate": printDate,
		},
	}
	return t.Repo.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (t *TimelineEntityRepo[T]) UpdateDigaPrescribe(ctx *titan.Context, id uuid.UUID, formInfo *diga.FormInfo) (*TimelineEntity[T], error) {
	filter := bson.M{
		Field_IsDeleted: false,
		Field_Id:        id,
	}

	update := bson.M{
		"$set": bson.M{
			"payload.printeddate": formInfo.PrintDate,
			"payload.formInfo":    formInfo,
		},
	}
	return t.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (t *TimelineEntityRepo[T]) UpdateHeimiPrintDate(ctx *titan.Context, heimiPrescriptionId uuid.UUID, printDate int64) (*TimelineEntity[T], error) {
	filter := bson.M{
		Field_IsDeleted:          false,
		Field_Payload_Id:         heimiPrescriptionId,
		Field_timelineEntityType: timeline_common.TimelineEntityType_HeimiPrescription,
	}

	update := bson.M{
		"$set": bson.M{
			"payload.heimiform.prescription.printdate": printDate,
		},
	}
	return t.Repo.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (t *TimelineEntityRepo[T]) GetPrescribeHimiById(ctx *titan.Context, himiprescriptionid uuid.UUID) (*TimelineEntity[T], error) {
	filter := bson.M{
		Field_IsDeleted:              false,
		"payload.himiprescriptionid": himiprescriptionid, // TODO: will change to use Id later!
		Field_timelineEntityType:     timeline_common.TimelineEntityType_HimiPrescription,
	}
	entity, err := t.Repo.FindOne(ctx, filter)
	if err != nil {
		return nil, err
	}

	if entity == nil {
		return nil, nil
	}

	return entity, nil
}

func (t *TimelineEntityRepo[T]) GetPrescribeHeimiById(ctx *titan.Context, prescribeId uuid.UUID) (*TimelineEntity[T], error) {
	filter := bson.M{
		Field_IsDeleted:          false,
		Field_Payload_Id:         prescribeId,
		Field_timelineEntityType: timeline_common.TimelineEntityType_HeimiPrescription,
	}
	entity, err := t.Repo.FindOne(ctx, filter)
	if err != nil {
		return nil, err
	}

	if entity == nil {
		return nil, nil
	}

	return entity, nil
}

// swap 5011 and 5012, bring 5012 to parent obj
func RemapAdditionalInfo(model *patient_encounter.EncounterServiceTimeline) {
	if model.AdditionalInfos == nil || len(*model.AdditionalInfos) == 0 {
		return
	}
	additionalInfos := model.AdditionalInfos
	fields5011s := slice.Filter(*additionalInfos, func(info *patient_encounter.AdditionalInfoParent) bool {
		return info.FK == "5011"
	})
	if len(fields5011s) == 0 {
		return
	}
	for _, p := range fields5011s {
		parentValue := p.Value
		for _, ch := range p.Children {
			if ch.FK != "5012" {
				continue
			}
			p.FK = ch.FK
			p.Value = ch.Value
			p.Children = slice.Filter(p.Children, func(child *patient_encounter.AdditionalInfoChild) bool {
				return child.FK != "5012"
			})
			p.Children = append([]*patient_encounter.AdditionalInfoChild{
				{
					FK:    "5011",
					Value: parentValue,
				},
			}, p.Children...)
			break
		}
	}
}

// swap 5011 and 5012, bring 5012 to parent obj
func RemapGoaAdditionalInfo(model *patient_encounter.EncounterGoaService) {
	if model.AdditionalInfos == nil || len(*model.AdditionalInfos) == 0 {
		return
	}
	additionalInfos := model.AdditionalInfos
	fields5011s := slice.Filter(*additionalInfos, func(info *patient_encounter.AdditionalInfoParent) bool {
		return info.FK == "5011"
	})
	if len(fields5011s) == 0 {
		return
	}
	for _, p := range fields5011s {
		parentValue := p.Value
		for _, ch := range p.Children {
			if ch.FK != "5012" {
				continue
			}
			p.FK = ch.FK
			p.Value = ch.Value
			p.Children = slice.Filter(p.Children, func(child *patient_encounter.AdditionalInfoChild) bool {
				return child.FK != "5012"
			})
			p.Children = append([]*patient_encounter.AdditionalInfoChild{
				{
					FK:    "5011",
					Value: parentValue,
				},
			}, p.Children...)
			break
		}
	}
}

// swap 5011 and 5012, bring 5012 to parent obj
func RemapUvGoaAdditionalInfo(model *patient_encounter.EncounterUvGoaService) {
	if model.AdditionalInfos == nil || len(*model.AdditionalInfos) == 0 {
		return
	}
	additionalInfos := model.AdditionalInfos
	fields5011s := slice.Filter(*additionalInfos, func(info *patient_encounter.AdditionalInfoParent) bool {
		return info.FK == "5011"
	})
	if len(fields5011s) == 0 {
		return
	}
	for _, p := range fields5011s {
		parentValue := p.Value
		for _, ch := range p.Children {
			if ch.FK != "5012" {
				continue
			}
			p.FK = ch.FK
			p.Value = ch.Value
			p.Children = slice.Filter(p.Children, func(child *patient_encounter.AdditionalInfoChild) bool {
				return child.FK != "5012"
			})
			p.Children = append([]*patient_encounter.AdditionalInfoChild{
				{
					FK:    "5011",
					Value: parentValue,
				},
			}, p.Children...)
			break
		}
	}
}

func remapByFunc[T TimelineEntityConstant](payload any, fs ...func(model *T)) {
	if model, ok := payload.(T); ok {
		for _, f := range fs {
			f(&model)
		}
	}
}

func (t *TimelineEntityRepo[T]) GetDoctorLetterById(ctx *titan.Context, doctorLetterId uuid.UUID) (*TimelineEntity[T], error) {
	filter := bson.M{
		Field_IsDeleted:          false,
		Field_Payload_Id:         doctorLetterId,
		Field_timelineEntityType: timeline_common.TimelineEntityType_DoctorLetter,
	}
	entity, err := t.Repo.FindOne(ctx, filter)
	if err != nil {
		return nil, err
	}

	if entity == nil {
		return nil, nil
	}

	return entity, nil
}

func setDefaultModel[T TimelineEntityConstant](ctx *titan.Context, entity *TimelineEntity[T]) error {
	if entity.Id == nil {
		entity.Id = util.NewPointer(uuid.New())
	}

	now := util.Now(ctx)
	if entity.CreatedAt.IsZero() {
		entity.CreatedAt = now
	}
	if entity.UpdatedAt.IsZero() {
		entity.UpdatedAt = now
	}
	if entity.SelectedDate.IsZero() {
		beginOfDate, _ := util.GetDayRange(now.UTC())
		entity.SelectedDate = beginOfDate
	} else {
		entity.SelectedDate = util.ConvertTimeToUTC(entity.SelectedDate)
	}

	payloadType, err := getTypeFromPayload(entity.Payload)
	if err != nil {
		return err
	}

	monthQuarter, yearQuarter := util.GetCurrentQuarter(entity.SelectedDate)
	entity.Quarter = monthQuarter
	entity.Year = yearQuarter

	entity.Type = payloadType
	entity.CreatedAtString = entity.CreatedAt.Format(FormatDate)
	if entity.CreatedBy == uuid.Nil {
		entity.CreatedBy = util.GetPointerValue(ctx.UserInfo().UserUUID())
	}
	entity.RecentAuditLogs = util.ValueOf(&entity.RecentAuditLogs, []AuditLog{})

	remapByFunc(entity.Payload, RemapGoaAdditionalInfo)
	remapByFunc(entity.Payload, RemapAdditionalInfo)
	remapByFunc(entity.Payload, RemapUvGoaAdditionalInfo)
	return nil
}

func (r *TimelineEntityRepo[T]) CreateMany(ctx *titan.Context, requests []TimelineEntity[T]) ([]TimelineEntity[T], error) {
	if len(requests) == 0 {
		return nil, nil
	}

	for i, v := range requests {
		entity := &requests[i]
		createdBy := function.If(v.CreatedBy != uuid.Nil, &v.CreatedBy, ctx.UserInfo().UserUUID())
		if len(entity.RecentAuditLogs) == 0 {
			entity.RecentAuditLogs = []AuditLog{
				newAuditLog(ctx, createdBy, timeline_common.Add),
			}
		}
		err := setDefaultModel(ctx, &requests[i])
		if err != nil {
			return nil, err
		}
	}

	return slice.ToValueTypeWithError(r.Repo.CreateMany(ctx, slice.ToPointerType(requests)))
}

// TODO: verify audit log logic with upsert action
func (r *TimelineEntityRepo[T]) Upsert(ctx *titan.Context, entity TimelineEntity[T]) (*TimelineEntity[T], error) {
	if err := setDefaultModel(ctx, &entity); err != nil {
		return nil, err
	}
	return r.Repo.Upsert(ctx, &entity)
}

func (t *TimelineEntityRepo[T]) Create(ctx *titan.Context, entity TimelineEntity[T]) (*TimelineEntity[T], error) {
	if len(entity.RecentAuditLogs) == 0 {
		entity.RecentAuditLogs = []AuditLog{
			newAuditLog(ctx, ctx.UserInfo().UserUUID(), timeline_common.Add),
		}
	}
	err := setDefaultModel(ctx, &entity)
	if err != nil {
		return nil, err
	}
	return t.Repo.Create(ctx, &entity)
}

func (t *TimelineEntityRepo[T]) EditTimelineById(ctx *titan.Context, entity TimelineEntity[T], opts ...*options.FindOneAndUpdateOptions) (*TimelineEntity[T], error) {
	if any(entity.Payload) == nil {
		return nil, nil
	}
	filter := bson.M{
		"_id":           entity.Id,
		Field_IsDeleted: false,
	}
	remapByFunc(entity.Payload, RemapGoaAdditionalInfo)
	remapByFunc(entity.Payload, RemapAdditionalInfo)
	remapByFunc(entity.Payload, RemapUvGoaAdditionalInfo)

	updateSet := bson.M{
		Field_Payload:           entity.Payload,
		Field_EncounterCase:     entity.EncounterCase,
		Field_ScheinIds:         entity.ScheinIds,
		Field_TreatmentDoctorId: entity.TreatmentDoctorId,
		repos.Field_UpdatedAt:   util.Now(ctx),
		repos.Field_UpdatedBy:   ctx.UserInfo().UserUUID(),
	}
	if entity.PatientId != uuid.Nil {
		updateSet[Field_PatientId] = entity.PatientId
	}

	if entity.SelectedDate.IsZero() {
		beginOfDate, _ := util.GetDayRange(util.Now(ctx).UTC())
		entity.SelectedDate = beginOfDate
	} else {
		monthQuarter, yearQuarter := util.GetCurrentQuarter(entity.SelectedDate)
		updateSet[Field_Quarter] = monthQuarter
		updateSet[Field_Year] = yearQuarter
		updateSet[Field_Selected_Date] = util.ConvertTimeToUTC(entity.SelectedDate)
	}

	if len(opts) == 0 {
		opts = append(opts, options.FindOneAndUpdate().SetReturnDocument(options.After))
	}
	res, err := t.Repo.FindOneAndUpdate(
		ctx,
		filter,
		bson.M{
			"$set": updateSet,
			"$push": bson.M{
				Field_RecentAuditLogs: newAuditLog(ctx, ctx.UserInfo().UserUUID(), timeline_common.Edit),
			},
		},
		opts...)
	return res, err
}

func (t *TimelineEntityRepo[T]) RestoreTimeline(ctx *titan.Context, entity TimelineEntity[T], opts ...*options.FindOneAndUpdateOptions) (*TimelineEntity[T], error) {
	if any(entity.Payload) == nil {
		return nil, nil
	}
	filter := bson.M{
		"_id": entity.Id,
	}

	updateSet := bson.M{
		Field_IsDeleted:       entity.IsDeleted,
		Field_Payload:         entity.Payload,
		Field_EncounterCase:   entity.EncounterCase,
		Field_ScheinIds:       entity.ScheinIds,
		repos.Field_UpdatedAt: util.Now(ctx),
		repos.Field_UpdatedBy: ctx.UserInfo().UserUUID(),
	}

	if !entity.SelectedDate.IsZero() {
		monthQuarter, yearQuarter := util.GetCurrentQuarter(entity.SelectedDate)
		updateSet[Field_Quarter] = monthQuarter
		updateSet[Field_Year] = yearQuarter
		updateSet[Field_Selected_Date] = entity.SelectedDate
	} else {
		entity.SelectedDate = util.ConvertTimeToUTC(entity.SelectedDate)
	}

	if len(opts) == 0 {
		opts = append(opts, options.FindOneAndUpdate().SetReturnDocument(options.After))
	}
	res, err := t.Repo.FindOneAndUpdate(
		ctx,
		filter,
		bson.M{
			"$set": updateSet,
			"$push": bson.M{
				Field_RecentAuditLogs: newAuditLog(ctx, ctx.UserInfo().UserUUID(), timeline_common.Restore),
			},
		},
		opts...)

	return res, err
}

// DeleteById soft delete and not billed
func (t *TimelineEntityRepo[T]) DeleteById(ctx *titan.Context, id uuid.UUID) (*TimelineEntity[T], error) {
	filter := bson.M{
		"_id":           id,
		Field_IsDeleted: false,
		Field_BillingInfo_BillingStatus: bson.M{
			"$nin": []string{
				string(api_common.Billed),
				// string(domain_common.PreParticipationBilled),
			},
		},
	}
	return t.Repo.FindOneAndUpdate(
		ctx,
		filter,
		bson.M{
			"$set": bson.M{
				Field_IsDeleted:       true,
				repos.Field_UpdatedAt: util.Now(ctx),
			},
			"$push": bson.M{
				Field_RecentAuditLogs: newAuditLog(ctx, ctx.UserInfo().UserUUID(), timeline_common.Remove),
			},
		},
		options.FindOneAndUpdate().SetReturnDocument(options.After),
	)
}

func (t *TimelineEntityRepo[T]) HardDeleteById(ctx *titan.Context, id uuid.UUID) error {
	_, err := t.Repo.DeleteById(ctx, id)
	return err
}

func (t *TimelineEntityRepo[T]) DeleteByIds(ctx *titan.Context, ids []uuid.UUID) ([]TimelineEntity[T], error) {
	filter := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
		Field_IsDeleted: false,
		Field_BillingInfo_BillingStatus: bson.M{
			"$nin": []string{
				string(api_common.Billed),
			},
		},
	}
	var result []TimelineEntity[T]
	_, err := t.Repo.IDBClient.UpdateMany(ctx, filter, bson.M{
		"$set": bson.M{
			Field_IsDeleted:       true,
			repos.Field_UpdatedAt: util.Now(ctx),
		},
		"$push": bson.M{
			Field_RecentAuditLogs: newAuditLog(ctx, ctx.UserInfo().UserUUID(), timeline_common.Remove),
		},
	}, &result)
	return result, err
}

func (t *TimelineEntityRepo[T]) UndoDeleteById(ctx *titan.Context, id uuid.UUID) (*TimelineEntity[T], error) {
	filter := bson.M{
		"_id":           id,
		Field_IsDeleted: true,
	}
	return t.Repo.FindOneAndUpdate(
		ctx,
		filter,
		bson.M{
			"$set": bson.M{
				Field_IsDeleted: false,
			},
			"$push": bson.M{
				Field_RecentAuditLogs: newAuditLog(ctx, ctx.UserInfo().UserUUID(), timeline_common.Restore),
			},
		},
		options.FindOneAndUpdate().SetReturnDocument(options.After),
	)
}

func getTypeFromPayload(payload any) (timeline_common.TimelineEntityType, error) {
	switch payload.(type) {
	case patient_encounter.EncounterMedicinePlanHistory:
		return timeline_common.TimelineEntityType_MedicinePlan, nil
	case patient_encounter.EncounterMedicinePrescription:
		return timeline_common.TimelineEntityType_MedicinePrescription, nil
	case patient_encounter.EncounterHimiPrescription:
		return timeline_common.TimelineEntityType_HimiPrescription, nil
	case patient_encounter.EncounterPatientMedicalData:
		return timeline_common.TimelineEntityType_PatientMedicalData, nil
	case patient_encounter.EncounterHeimiPrescription:
		return timeline_common.TimelineEntityType_HeimiPrescription, nil
	case patient_encounter.EncounterLab:
		return timeline_common.TimelineEntityType_Lab, nil
	case patient_encounter.EncounterForm:
		return timeline_common.TimelineEntityType_Form, nil
	case patient_encounter.EncounterDiagnoseTimeline:
		return timeline_common.TimelineEntityType_Diagnose, nil
	case patient_encounter.EncounterServiceTimeline:
		return timeline_common.TimelineEntityType_Service, nil
	case patient_encounter.EncounterNoteTimeline:
		return timeline_common.TimelineEntityType_Note, nil
	case patient_encounter.EncounterCalendarTimeline:
		return timeline_common.TimelineEntityType_Room, nil
	case patient_encounter.EncounterPsychotherapy:
		return timeline_common.TimelineEntityType_Psychotherapy, nil
	case mail_common.EmailItem:
		return timeline_common.TimelineEntityType_MailItem, nil
	case doctor_letter_common.DoctorLetter:
		return timeline_common.TimelineEntityType_DoctorLetter, nil
	case timeline_common.ReferencePayload:
		return timeline_common.TimelineEntityType_BillingPatient, nil
	case timeline_common.EHICPayload:
		return timeline_common.TimelineEntityType_EHIC, nil
	case edmp_common.EnrollmentInfo:
		return timeline_common.TimelineEntityType_EDMPEnrollment, nil
	case edmp_common.DocumentationOverview:
		return timeline_common.TimelineEntityType_EDMPEnrollment_Document, nil
	case edmp_common.EdokuDocumentationOverview:
		return timeline_common.TimelineEntityType_EDOKU_Document, nil
	case medicine_common.Medicine:
		return timeline_common.TimelineEntityType_Medicine, nil
	case patient_encounter.EncounterGoaService:
		return timeline_common.TimelineEntityType_Service_GOA, nil
	case patient_encounter.EncounterAppointmentTimeline:
		return timeline_common.TimelineEntityType_Calendar, nil
	case diga.Prescribe:
		return timeline_common.TimelineEntityType_Diga, nil
	case patient_encounter.EncounterDocumentManagement:
		return timeline_common.TimelineEntityType_DocumentManagement, nil
	case patient_encounter.EncounterArriba:
		return timeline_common.TimelineEntityType_Arriba, nil
	case patient_encounter.EncounterGDT:
		return timeline_common.TimelineEntityType_GDT, nil
	case patient_encounter.EncounterLDT:
		return timeline_common.TimelineEntityType_LDT, nil
	case patient_encounter.EncounterUvGoaService:
		return timeline_common.TimelineEntityType_Service_UV_GOA, nil
	case patient_encounter.EncounterCustomize:
		return timeline_common.TimelineEntityType_Customize, nil
	default:
		return "", fmt.Errorf("payload type %T is not supported", payload)
	}
}

func (t *TimelineEntityRepo[T]) GetByPatientId(ctx *titan.Context, patientId uuid.UUID) ([]TimelineEntity[T], error) {
	filter := bson.M{
		Field_IsDeleted: false,
		Field_PatientId: patientId,
	}
	return slice.ToValueTypeWithError(t.Repo.Find(ctx, filter))
}

func (t *TimelineEntityRepo[T]) GetByPatientIdAndQuarter(ctx *titan.Context, patientId uuid.UUID, quarter, year []int) ([]TimelineEntity[T], error) {
	filter := bson.D{
		{Key: Field_PatientId, Value: patientId},
		{Key: Field_Year, Value: bson.M{"$in": year}},
		{Key: Field_Quarter, Value: bson.M{"$in": quarter}},
		{Key: Field_IsDeleted, Value: false},
	}
	return slice.ToValueTypeWithError(t.Repo.Find(ctx, filter, options.Find().SetProjection(bson.M{
		Field_Code:    1,
		Field_Command: 1,
	})))
}

func (t *TimelineEntityRepo[T]) GetTimelineEntryIdsByChainId(ctx *titan.Context, chainId uuid.UUID) ([]TimelineEntity[T], error) {
	filter := bson.D{
		{Key: Field_IsDeleted, Value: false},
		{Key: Field_Payload_ChainID, Value: chainId},
	}
	return slice.ToValueTypeWithError(t.Repo.Find(ctx, filter, options.Find().SetProjection(bson.M{
		Field_Id: 1,
	})))
}

// GetTimelinesForValidation get all diagnoses & services by patient and contract, except bdt imported
func (t *TimelineEntityRepo[T]) GetTimelinesForValidation(ctx *titan.Context, patientId uuid.UUID, contractId *string) ([]TimelineEntity[T], error) {
	bsnr := ctx.UserInfo().GetBsnrId()
	quarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	yearQuarters := quarter.GetFromLastYearQuarter(8)
	yearQuarterFilters := bson.A{}
	for _, yearQuarter := range yearQuarters {
		yearQuarterFilters = append(yearQuarterFilters, bson.M{
			Field_Year:    yearQuarter.Year,
			Field_Quarter: yearQuarter.Quarter,
		})
	}
	types := []timeline_common.TimelineEntityType{
		timeline_common.TimelineEntityType_Diagnose,
		timeline_common.TimelineEntityType_Service,
		timeline_common.TimelineEntityType_Psychotherapy,
	}
	filter := bson.D{
		{Key: Field_PatientId, Value: patientId},
		{Key: operator.Or, Value: yearQuarterFilters},
		{Key: Field_IsDeleted, Value: false},
		{Key: Field_timelineEntityType, Value: bson.M{
			operator.In: types,
		}},
		{Key: Field_contractId, Value: contractId},
		{Key: Field_EncounterCase, Value: bson.M{
			operator.Ne: patient_encounter.PRE_ENROLLMENT,
		}},
		{Key: Field_IsImported, Value: false},
		{Key: repos.Field_AssignedToBsnrId, Value: bsnr},
	}

	return slice.ToValueTypeWithError(t.Repo.Find(ctx, filter, &options.FindOptions{
		Sort: bson.D{
			{Key: Field_Selected_Date, Value: 1},
			{Key: Field_CreatedAt, Value: 1},
		},
	}))
}

func (t *TimelineEntityRepo[T]) GetTimelinesForBillingByScheinIds(ctx *titan.Context, req GetTimelinesForBillingByScheinIdsRequest) ([]TimelineEntity[T], error) {
	types := []timeline_common.TimelineEntityType{
		timeline_common.TimelineEntityType_Diagnose,
		timeline_common.TimelineEntityType_Service,
		timeline_common.TimelineEntityType_MedicinePrescription,
		timeline_common.TimelineEntityType_Psychotherapy,
	}
	filter := bson.D{
		{
			Key:   Field_IsDeleted,
			Value: false,
		},
		{
			Key: Field_timelineEntityType,
			Value: bson.M{
				operator.In: types,
			},
		},
	}

	if len(req.PatientWithScheinIds) > 0 {
		patientAndScheinFilter := bson.A{}
		for _, p := range req.PatientWithScheinIds {
			patientAndScheinFilter = append(patientAndScheinFilter, bson.M{
				Field_PatientId: p.PatientId,
				Field_ScheinIds: bson.M{
					operator.In: p.ScheinIds,
				},
			})
		}
		filter = append(filter, bson.E{
			Key:   operator.Or,
			Value: patientAndScheinFilter,
		})
	}

	if util.GetPointerValue(req.ExcludeEABServiceCode) {
		filter = append(filter, bson.E{
			Key: Field_Sources,
			Value: bson.M{
				operator.Ne: patient_encounter.EAB,
			},
		})
	}

	return slice.ToValueTypeWithError(t.Repo.Find(ctx, filter))
}

func (t *TimelineEntityRepo[T]) GetAllBillableTimelineItems(ctx *titan.Context, scheins []ScheinIdsWithBillCheck) ([]TimelineEntity[T], error) {
	diagnoesAndServiceTypes := []timeline_common.TimelineEntityType{
		timeline_common.TimelineEntityType_Diagnose,
		timeline_common.TimelineEntityType_Service,
	}
	medicinePrescriptionTypes := []timeline_common.TimelineEntityType{
		timeline_common.TimelineEntityType_MedicinePrescription,
	}

	scheinIdsUnBilled := slice.Map(
		slice.Filter(scheins, func(item ScheinIdsWithBillCheck) bool {
			return !item.IsBilled
		}),
		func(item ScheinIdsWithBillCheck) uuid.UUID {
			return item.Id
		},
	)

	scheinIds := slice.Map(
		scheins,
		func(item ScheinIdsWithBillCheck) uuid.UUID {
			return item.Id
		},
	)

	filter := bson.M{
		operator.Or: bson.A{
			bson.M{
				Field_IsDeleted: false,
				Field_contractId: bson.M{
					"$ne": nil,
				},
				Field_ScheinIds: bson.M{
					"$in": scheinIdsUnBilled,
				},
				Field_timelineEntityType: bson.M{
					operator.In: diagnoesAndServiceTypes,
				},
			},
			bson.M{
				Field_IsDeleted: false,
				Field_contractId: bson.M{
					"$ne": nil,
				},
				Field_ScheinIds: bson.M{
					"$in": scheinIds,
				},
				Field_timelineEntityType: bson.M{
					operator.In: medicinePrescriptionTypes,
				},
				Field_MedicinePrescription_PrintDate: bson.M{
					"$ne": nil,
				},
				Field_BillingInfo: bson.M{
					"$exists": true, "$eq": nil,
				},
			},
			bson.M{
				Field_IsDeleted: true,
				Field_contractId: bson.M{
					"$ne": nil,
				},
				Field_ScheinIds: bson.M{
					"$in": scheinIds,
				},
				Field_timelineEntityType: bson.M{
					operator.In: medicinePrescriptionTypes,
				},
				Field_MedicinePrescription_PrintDate: bson.M{
					"$ne": nil,
				},
				Field_BillingInfo: bson.M{
					"$exists": true, "$ne": nil,
				},
			},
		},
	}
	return slice.ToValueTypeWithError(t.Repo.Find(ctx, filter))
}

func (t *TimelineEntityRepo[T]) SaveErrorsForDiagnoseAndService(ctx *titan.Context, ids []uuid.UUID, errors patient_encounter.EncounterItemError) ([]TimelineEntity[T], error) {
	filterNullPayload := bson.D{
		{Key: "_id", Value: bson.M{
			"$in": ids,
		}},
		{Key: Field_timelineEntityType, Value: bson.M{
			"$in": []timeline_common.TimelineEntityType{
				timeline_common.TimelineEntityType_Diagnose,
				timeline_common.TimelineEntityType_Service,
			},
		}},
		{Key: Field_IsDeleted, Value: false},
		{Key: Field_Payload_Errors, Value: nil},
	}

	_, err := t.Repo.IDBClient.UpdateMany(
		ctx,
		filterNullPayload,
		bson.M{
			"$set": bson.M{
				Field_Payload_Errors: []patient_encounter.EncounterItemError{},
			},
		},
		nil,
		nil,
	)
	if err != nil {
		return nil, err
	}

	filter := bson.M{
		Field_IsDeleted: false,
		Field_timelineEntityType: bson.M{
			"$in": []timeline_common.TimelineEntityType{
				timeline_common.TimelineEntityType_Diagnose,
				timeline_common.TimelineEntityType_Service,
			},
		},
		"_id": bson.M{
			"$in": ids,
		},
	}

	var result []TimelineEntity[T]
	_, err = t.Repo.IDBClient.UpdateMany(
		ctx,
		filter,
		bson.M{
			"$push": bson.M{
				Field_Payload_Errors: errors,
			},
		},
		&result,
	)
	if err != nil {
		return nil, err
	}
	return slice.ToValueTypeWithError(t.Find(ctx, filter))
}

// Waiting room
func (t *TimelineEntityRepo[T]) UpdateWaitingRoomInCalendarEntry(ctx *titan.Context, entryIds []uuid.UUID, waitingRoomName *string, acceptableWaitingTimeInMinutes int64, activeTimeMeasurement bool) error {
	updater := bson.M{}
	if waitingRoomName != nil {
		updater["payload.waitingRoomName"] = *waitingRoomName
	}
	if acceptableWaitingTimeInMinutes > 0 {
		updater["payload.acceptableWaitingTimeInMinutes"] = acceptableWaitingTimeInMinutes
	}

	updater["payload.activeTimeMeasurement"] = activeTimeMeasurement
	_, err := t.Repo.IDBClient.UpdateMany(ctx, bson.M{
		Field_Id: bson.M{
			"$in": entryIds,
		},
	}, bson.M{
		"$set": updater,
		"$push": bson.M{
			Field_RecentAuditLogs: newAuditLog(ctx, ctx.UserInfo().UserUUID(), timeline_common.Edit),
		},
	},
		nil,
		nil,
	)
	return err
}

func (t *TimelineEntityRepo[T]) CleanErrors(ctx *titan.Context, patientId uuid.UUID, contractId *string, entityType timeline_common.TimelineEntityType, scheinId *uuid.UUID) error {
	filter := bson.D{
		{Key: Field_PatientId, Value: patientId},
		{Key: Field_IsDeleted, Value: false},
		{Key: Field_EncounterCase, Value: bson.M{operator.Ne: patient_encounter.PRE_ENROLLMENT}},
	}
	switch entityType {
	case timeline_common.TimelineEntityType_Service_GOA:
		filter = append(filter, bson.E{
			Key:   Field_timelineEntityType,
			Value: timeline_common.TimelineEntityType_Service_GOA,
		})
		if scheinId != nil {
			filter = append(filter, bson.E{
				Key:   Field_ScheinIds,
				Value: scheinId,
			})
		}
	case timeline_common.TimelineEntityType_Service, timeline_common.TimelineEntityType_Diagnose:
		filter = append(filter, bson.E{
			Key: Field_timelineEntityType,
			Value: bson.M{
				"$in": []timeline_common.TimelineEntityType{
					timeline_common.TimelineEntityType_Diagnose,
					timeline_common.TimelineEntityType_Service,
				},
			},
		})
		if contractId != nil {
			filter = append(filter, bson.E{
				Key:   Field_contractId,
				Value: contractId,
			})
		}
	default:
		return fmt.Errorf("invalid entity type: %s", entityType)
	}

	var result []TimelineEntity[T]
	_, err := t.Repo.IDBClient.UpdateMany(
		ctx,
		filter,
		bson.M{
			"$set": bson.M{
				Field_Payload_Errors: []patient_encounter.EncounterItemError{},
			},
		},
		&result,
	)
	return err
}

// Goa service
func (t *TimelineEntityRepo[T]) getGoaServiceTimelines(ctx *titan.Context, filter bson.D) ([]TimelineEntity[patient_encounter.EncounterGoaService], error) {
	filter = append(filter, []bson.E{
		{
			Key:   Field_IsDeleted,
			Value: false,
		},
		{
			Key:   Field_timelineEntityType,
			Value: timeline_common.TimelineEntityType_Service_GOA,
		},
	}...)
	entities, err := t.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	if len(entities) == 0 {
		return nil, nil
	}

	goaEntities := make([]TimelineEntity[patient_encounter.EncounterGoaService], 0)
	for _, entity := range entities {
		goaEntities = append(goaEntities, ToEntityTypeFromT[T, patient_encounter.EncounterGoaService](*entity))
	}
	return goaEntities, nil
}

func (t *TimelineEntityRepo[T]) getUvGoaServiceTimelines(ctx *titan.Context, filter bson.D) ([]TimelineEntity[patient_encounter.EncounterUvGoaService], error) {
	filter = append(filter, []bson.E{
		{
			Key:   Field_IsDeleted,
			Value: false,
		},
		{
			Key:   Field_timelineEntityType,
			Value: timeline_common.TimelineEntityType_Service_UV_GOA,
		},
	}...)
	entities, err := t.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	if len(entities) == 0 {
		return nil, nil
	}

	uvGoaEntities := make([]TimelineEntity[patient_encounter.EncounterUvGoaService], 0)
	for _, entity := range entities {
		uvGoaEntities = append(uvGoaEntities, ToEntityTypeFromT[T, patient_encounter.EncounterUvGoaService](*entity))
	}
	return uvGoaEntities, nil
}

func (t *TimelineEntityRepo[T]) GetGoaServiceTimelineByGoaNumber(ctx *titan.Context, goaNumber string) ([]TimelineEntity[patient_encounter.EncounterGoaService], error) {
	filter := bson.D{
		{
			Key:   Field_ICD_Code,
			Value: goaNumber,
		},
	}
	return t.getGoaServiceTimelines(ctx, filter)
}

func (t *TimelineEntityRepo[T]) GetGoaServiceTimelinesByPatientAndSchein(ctx *titan.Context, req []PatientWithScheinIds) ([]TimelineEntity[patient_encounter.EncounterGoaService], error) {
	filter := bson.D{
		{
			Key:   Field_IsDeleted,
			Value: false,
		},
	}

	patientWithScheinIdsFilter := bson.A{}
	for _, patientWithScheinIds := range req {
		match := bson.D{
			{
				Key:   Field_PatientId,
				Value: patientWithScheinIds.PatientId,
			},
		}

		if len(patientWithScheinIds.ScheinIds) > 0 {
			match = append(match, bson.E{
				Key:   Field_ScheinIds,
				Value: bson.M{"$in": patientWithScheinIds.ScheinIds},
			})
		}
		patientWithScheinIdsFilter = append(patientWithScheinIdsFilter, match)
	}
	filter = append(filter, bson.E{
		Key:   operator.Or,
		Value: patientWithScheinIdsFilter,
	})
	return t.getGoaServiceTimelines(ctx, filter)
}

func (t *TimelineEntityRepo[T]) GetUvGoaServiceTimelinesByPatientAndSchein(ctx *titan.Context, req PatientWithScheinIds) ([]TimelineEntity[patient_encounter.EncounterUvGoaService], error) {
	filter := bson.D{
		{
			Key:   Field_IsDeleted,
			Value: false,
		},
	}
	patientWithScheinIdsFilter := bson.A{}
	match := bson.D{
		{
			Key:   Field_PatientId,
			Value: req.PatientId,
		},
	}
	if len(req.ScheinIds) > 0 {
		match = append(match, bson.E{
			Key:   Field_ScheinIds,
			Value: bson.M{"$in": req.ScheinIds},
		})
	}
	patientWithScheinIdsFilter = append(patientWithScheinIdsFilter, match)

	filter = append(filter, bson.E{
		Key:   operator.Or,
		Value: patientWithScheinIdsFilter,
	})
	return t.getUvGoaServiceTimelines(ctx, filter)
}

func (t *TimelineEntityRepo[T]) GetGroupByPatientIdsByKVTimeLines(
	ctx *titan.Context,
	quarter util.YearQuarter,
) ([]GroupByPatientIdWithScheinByKVTimeLines, error) {
	stageMatch := bson.D{
		{
			Key: "$match",
			Value: bson.M{
				Field_Quarter:            quarter.Quarter,
				Field_Year:               quarter.Year,
				Field_IsDeleted:          false,
				Field_timelineEntityType: timeline_common.TimelineEntityType_Service,
				fmt.Sprintf("%s.%s", Field_Payload, Field_ServiceMainGroup): api_common.KV,
			},
		},
	}

	stateUnwind := bson.D{
		{
			Key:   "$unwind",
			Value: fmt.Sprintf("$%s", Field_ScheinIds),
		},
	}

	stageGroup := bson.D{
		{
			Key: "$group",
			Value: bson.M{
				"_id": bson.M{
					"patientId": fmt.Sprintf("$%s", Field_PatientId),
					"scheinId":  fmt.Sprintf("$%s", Field_ScheinIds),
				},
			},
		},
	}

	stages := []bson.D{stageMatch, stateUnwind, stageGroup}
	var results []GroupByPatientIdWithScheinByKVTimeLines
	err := t.IDBClient.Aggregate(ctx, stages, &results)
	if err != nil {
		return nil, err
	}

	return results, nil
}

// Audit Log
func newAuditLog(ctx *titan.Context, userId *uuid.UUID, actionType timeline_common.ActionType) AuditLog {
	return AuditLog{
		AuditLogId: util.NewUUID(),
		UserId:     userId,
		Date:       util.Now(ctx),
		ActionType: actionType,
	}
}

func (s *TimelineEntityRepo[T]) GetByDocumentManagement(ctx *titan.Context, documentManagementId uuid.UUID, patientId *uuid.UUID) (*TimelineEntity[patient_encounter.EncounterDocumentManagement], error) {
	filter := bson.M{}
	if patientId != nil {
		filter[Field_PatientId] = *patientId
	}

	filter[Field_timelineEntityType] = timeline_common.TimelineEntityType_DocumentManagement
	filter[Field_Payload_Id] = documentManagementId
	filter[Field_IsDeleted] = false
	result, err := s.FindOne(ctx, filter)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to find timeline entry by document management id %s and patient id %s", documentManagementId, patientId)
	}
	if result == nil {
		return nil, nil
	}
	convert := ToEntityTypeFromT[T, patient_encounter.EncounterDocumentManagement](*result)
	return &convert, nil
}

type GetPatientByLabOrderRequest struct {
	OrderId string
}

// Only return value if only one match record in col
func (s *TimelineEntityRepo[T]) GetPatientByLabOrder(ctx *titan.Context, request GetPatientByLabOrderRequest) (*uuid.UUID, error) {
	filter := bson.M{
		Field_IsDeleted: false,
		Field_timelineEntityType: bson.M{
			operator.In: []timeline_common.TimelineEntityType{
				timeline_common.TimelineEntityType_LDT,
				timeline_common.TimelineEntityType_GDT,
			},
		},
		"payload.laborderid": request.OrderId,
	}
	res, err := s.Find(ctx, filter)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to find timeline entry by order id")
	}
	if len(res) == 0 {
		return nil, nil
	}
	uniqueByPatientId := slice.UniqBy(res, func(e *TimelineEntity[T]) uuid.UUID {
		return e.PatientId
	})
	if len(uniqueByPatientId) == 1 {
		return &uniqueByPatientId[0].PatientId, nil
	}
	if len(uniqueByPatientId) > 1 {
		ctx.Logger().Warn("multiple patient found by order id", "order_id", request.OrderId, "patient_ids", uniqueByPatientId)
		return nil, nil
	}
	return nil, nil
}

func (s *TimelineEntityRepo[T]) GetGdtTimelineByDocumentManagementId(ctx *titan.Context, documentManagementId uuid.UUID) (*TimelineEntity[T], error) {
	filter := bson.M{
		Field_IsDeleted:                false,
		Field_timelineEntityType:       timeline_common.TimelineEntityType_GDT,
		"payload.documentmanagementid": documentManagementId,
	}
	return s.FindOne(ctx, filter)
}

func (s *TimelineEntityRepo[T]) GetByDocumentManagementReaderId(ctx *titan.Context, readerId uuid.UUID) ([]TimelineEntity[T], error) {
	return slice.ToValueTypeWithError(s.Find(ctx, bson.M{
		Field_timelineEntityType:                  timeline_common.TimelineEntityType_DocumentManagement,
		Field_Payload_DocumentManagement_ReadById: readerId,
		Field_IsDeleted:                           false,
	}))
}

func (r *TimelineEntityRepo[T]) FindLatestTimelineEntry(ctx *titan.Context, request FindLatestTimelineEntryRequest) (*TimelineEntity[T], error) {
	filter := bson.M{
		Field_PatientId:          request.PatientId,
		Field_timelineEntityType: request.TimelineEntryType,
		Field_PayloadContractId:  request.ContractId,
	}
	return r.FindOne(ctx, filter, &options.FindOneOptions{Sort: bson.M{Field_CreatedAt: -1}})
}

func (t *TimelineEntityRepo[T]) CheckExistDiagnoseInListCodes(ctx *titan.Context, patientId uuid.UUID, yearQuarter util.YearQuarter, codes []string) (bool, error) {
	return t.Repo.Any(ctx,
		bson.M{
			Field_PatientId: patientId,
			Field_Year:      yearQuarter.Year,
			Field_Quarter:   yearQuarter.Quarter,
			Field_ICD_Code: bson.M{
				operator.In: codes,
			},
			Field_IsDeleted: false,
		})
}

func (t *TimelineEntityRepo[T]) UpdateArribaTimeline(ctx *titan.Context, req arriba_common.UpdateArribaTimelineRequest) (*TimelineEntity[T], error) {
	filter := bson.M{
		Field_PatientId:          req.PatientId,
		Field_IsDeleted:          false,
		Field_timelineEntityType: timeline_common.TimelineEntityType_Arriba,
		Field_Arriba_SessionId:   req.SessionId,
	}
	update := bson.M{
		"$set": bson.M{
			Field_Arriba_CompanionFilePaths: req.CompanionFilePaths,
			Field_Arriba_Status:             req.Status,
			repos.Field_UpdatedAt:           util.Now(ctx),
		},
	}
	return t.Repo.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (t *TimelineEntityRepo[T]) DeleteServiceChain(ctx *titan.Context, chainId uuid.UUID) ([]*TimelineEntity[T], error) {
	filter := bson.M{
		Field_Payload_ChainID: chainId,
		Field_IsDeleted:       false,
	}
	update := bson.M{
		"$set": bson.M{
			Field_IsDeleted:       true,
			repos.Field_UpdatedAt: util.Now(ctx),
		},
	}
	result, err := t.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	if len(result) == 0 {
		return nil, fmt.Errorf("service chain not found")
	}
	_, err = t.Repo.IDBClient.UpdateMany(ctx, filter, update, nil)
	return result, err
}

func (t *TimelineEntityRepo[T]) FindByChainId(ctx *titan.Context, chainId uuid.UUID) ([]*TimelineEntity[T], error) {
	filter := bson.M{
		Field_Payload_ChainID: chainId,
		Field_IsDeleted:       false,
	}
	return t.Find(ctx, filter)
}

func (t *TimelineEntityRepo[T]) GetTimelinesByCondition(ctx *titan.Context, request GetTimelinesByCondition) ([]TimelineEntity[T], error) {
	filter := bson.M{
		Field_PatientId: request.PatientId,
		Field_timelineEntityType: bson.M{
			operator.In: request.TimelineEntityTypes,
		},
		Field_IsDeleted: false,
	}

	var timelineEntityTypesNotAsignToAnySchein = []timeline_common.TimelineEntityType{
		timeline_common.TimelineEntityType_GDT,
		timeline_common.TimelineEntityType_Anamnese,
		timeline_common.TimelineEntityType_Finding,
		timeline_common.TimelineEntityType_Therapy,
		timeline_common.TimelineEntityType_Note,
		timeline_common.TimelineEntityType_Customize,
	}

	dateFilter := bson.M{}
	if request.StartTime != nil {
		startTime := util.ConvertMillisecondsToTime(*request.StartTime)
		dateFilter["$gte"] = carbon.Time2Carbon(startTime).StartOfDay().ToStdTime()
		dateFilter["$lte"] = carbon.Time2Carbon(startTime).EndOfDay().ToStdTime()
	}
	if request.EndTime != nil {
		endTime := util.ConvertMillisecondsToTime(*request.EndTime)
		dateFilter["$lte"] = carbon.Time2Carbon(endTime).EndOfDay().ToStdTime()
	}
	if len(dateFilter) > 0 {
		filter[Field_Selected_Date] = dateFilter
	}

	result, err := t.Repo.Find(ctx, filter)
	return slice.Map(
		slice.Filter(
			result,
			func(te *TimelineEntity[T]) bool {
				if te == nil {
					return false
				}
				if !slice.Contains(timelineEntityTypesNotAsignToAnySchein, te.Type) {
					return slice.Contains(te.ScheinIds, request.ScheinId)
				}
				return true
			},
		),
		func(te *TimelineEntity[T]) TimelineEntity[T] { return *te },
	), err
}

// get type follow our bussiness logic
func getRealTypeEntity[T TimelineEntityConstant](entity TimelineEntity[T]) string {
	switch entity.Type {
	case timeline_common.TimelineEntityType_Diagnose:
		payload := getPayloadByType[patient_encounter.EncounterDiagnoseTimeline](entity.Payload)
		switch payload.Type {
		case patient_encounter.DIAGNOSETYPE_ACUTE:
			return string(timeline_common.TimelineEntityType_Diagnose)
		case patient_encounter.DIAGNOSETYPE_ANAMNESTIC:
			return string(timeline_common.TimelineEntityType_Diagnose_AD)
		case patient_encounter.DIAGNOSETYPE_PERMANENT:
			return string(timeline_common.TimelineEntityType_Diagnose_DD)
		}
	case timeline_common.TimelineEntityType_Note:
		payload := getPayloadByType[patient_encounter.EncounterNoteTimeline](entity.Payload)
		switch util.GetPointerValue(payload.Type) {
		case patient_encounter.NOTE:
			return string(timeline_common.TimelineEntityType_Note)
		case patient_encounter.ANAMNESE:
			return string(timeline_common.TimelineEntityType_Anamnese)
		case patient_encounter.FINDING:
			return string(timeline_common.TimelineEntityType_Finding)
		case patient_encounter.THERAPY:
			return string(timeline_common.TimelineEntityType_Therapy)
		}
	case timeline_common.TimelineEntityType_DoctorLetter:
		payload := getPayloadByType[doctor_letter_common.DoctorLetter](entity.Payload)
		if payload.PrivateInvoice != nil {
			return string(timeline_common.TimelineEntityType_Private_Billing)
		}
	case timeline_common.TimelineEntityType_Service, timeline_common.TimelineEntityType_Service_GOA, timeline_common.TimelineEntityType_Service_UV_GOA, timeline_common.TimelineEntityType_Service_UV_GOA_Chain, timeline_common.TimelineEntityType_Service_GOA_Chain:
		return string(timeline_common.TimelineEntityType_Service_Chain)
	case timeline_common.TimelineEntityType_Customize:
		return getPayloadByType[patient_encounter.EncounterCustomize](entity.Payload).Command
	}
	return string(entity.Type)
}

func (t *TimelineEntityRepo[T]) GetAnyBlankCodeFromTimeLine(ctx *titan.Context, code string) (bool, error) {
	filter := bson.M{
		Field_Code:               code,
		Field_timelineEntityType: timeline_common.TimelineEntityType_Service,
	}

	return t.Repo.Any(ctx, filter)
}

func (t *TimelineEntityRepo[T]) IsExistCommand(ctx *titan.Context, command string) (bool, error) {
	filter := bson.M{
		Field_Command:   command,
		Field_IsDeleted: false,
	}

	return t.Repo.Any(ctx, filter)
}

// DeleteByDocumentManagementIds
func (t *TimelineEntityRepo[T]) DeleteByDocumentManagementIds(ctx *titan.Context, documentManagementIds []uuid.UUID) error {
	if len(documentManagementIds) == 0 {
		return nil
	}
	filter := bson.M{
		Field_Payload_DocumentEDTId: bson.M{
			"$in": documentManagementIds,
		},
		Field_IsDeleted:          false,
		Field_timelineEntityType: timeline_common.TimelineEntityType_LDT,
	}
	_, err := t.Repo.IDBClient.Delete(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

// get all timeline entity by document management id
func (t *TimelineEntityRepo[T]) GetAllTimelineLDTEntityByDocumentManagementId(ctx *titan.Context, documentManagementIds []uuid.UUID) ([]*TimelineEntity[patient_encounter.EncounterLDT], error) {
	filter := bson.M{
		Field_Payload_DocumentEDTId: bson.M{
			"$in": documentManagementIds,
		},
		Field_IsDeleted:          false,
		Field_timelineEntityType: timeline_common.TimelineEntityType_LDT,
	}
	result, err := t.Find(ctx, filter)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to find timeline entry by document management id")
	}
	return slice.Map(result, func(e *TimelineEntity[T]) *TimelineEntity[patient_encounter.EncounterLDT] {
		convert := ToEntityTypeFromT[T, patient_encounter.EncounterLDT](*e)
		return &convert
	}), nil
}

func (t *TimelineEntityRepo[T]) GetAllTimelineByDocumentManagementId(ctx *titan.Context, documentManagementIds []uuid.UUID) ([]*TimelineEntity[patient_encounter.EncounterDocumentManagement], error) {
	filter := bson.M{
		Field_Payload_DocumentManagementId: bson.M{
			"$in": documentManagementIds,
		},
		Field_IsDeleted:          false,
		Field_timelineEntityType: timeline_common.TimelineEntityType_DocumentManagement,
	}
	result, err := t.Find(ctx, filter)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to find timeline entry by document management id")
	}
	return slice.Map(result, func(e *TimelineEntity[T]) *TimelineEntity[patient_encounter.EncounterDocumentManagement] {
		convert := ToEntityTypeFromT[T, patient_encounter.EncounterDocumentManagement](*e)
		return &convert
	}), nil
}
