package ptv_import_history

import (
	"errors"
	"strings"

	mongodb "git.tutum.dev/medi/tutum/ares/pkg/repo"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	"github.com/google/uuid"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	ptv_import_common "git.tutum.dev/medi/tutum/ares/service/domains/ptv_import/common"
)

// Model fields definitions
const (
	Field_Id                     = "_id"
	Field_DoctorId               = "doctorId"
	Field_ContractId             = "contractId"
	Field_DocumentId             = "documentId"
	Field_AutoImportParticipants = "autoImportParticipants"
	Field_ConflictParticipants   = "conflictParticipants"
	Field_MissingParticipants    = "missingParticipants"
	Field_ImporterId             = "importerId"
	Field_CreateTime             = "createTime"
	Field_UpdateTime             = "updateTime"
	Field_Year                   = "year"
	Field_Quarter                = "quarter"
	Field_Status                 = "status"
)
const (
	Field_ParticipantDecision_Id                = "id"
	Field_ParticipantDecision_PatientId         = "patientId"
	Field_ParticipantDecision_IkNumber          = "ikNumber"
	Field_ParticipantDecision_InsuranceNumber   = "insuranceNumber"
	Field_ParticipantDecision_Status            = "status"
	Field_ParticipantDecision_FirstName         = "firstName"
	Field_ParticipantDecision_LastName          = "lastName"
	Field_ParticipantDecision_Reason            = "reason"
	Field_ParticipantDecision_Gender            = "gender"
	Field_ParticipantDecision_Dob               = "dob"
	Field_ParticipantDecision_ContractBeginDate = "contractBeginDate"
	Field_ParticipantDecision_ContractEndDate   = "contractEndDate"
	Field_ParticipantDecision_MarkAsDone        = "markAsDone"
	Field_ParticipantDecision_TypeGroupDecision = "typeGroupDecision"
)
const (
	Field_IkNumber_LocalIkNumber = "localIkNumber"
	Field_IkNumber_HpmIkNumber   = "hpmIkNumber"
)
const (
	Field_InsuranceNumber_LocalInsuranceNumber = "localInsuranceNumber"
	Field_InsuranceNumber_HpmInsuranceNumber   = "hpmInsuranceNumber"
)
const (
	Field_Status_LocalStatus = "localStatus"
	Field_Status_HpmStatus   = "hpmStatus"
)
const (
	Field_FirstName_LocalFirstName = "localFirstName"
	Field_FirstName_HpmFirstName   = "hpmFirstName"
)
const (
	Field_LastName_LocalLastName = "localLastName"
	Field_LastName_HpmLastName   = "hpmLastName"
)
const (
	Field_Reason_LocalReason = "localReason"
	Field_Reason_HpmReason   = "hpmReason"
)
const (
	Field_Gender_LocalGender = "localGender"
	Field_Gender_HpmGender   = "hpmGender"
)
const (
	Field_Dob_LocalDOB = "localDOB"
	Field_Dob_HpmDOB   = "hpmDOB"
)
const (
	Field_ContractBeginDate_LocalContractBeginDate = "localContractBeginDate"
	Field_ContractBeginDate_HpmContractBeginDate   = "hpmContractBeginDate"
)
const (
	Field_ContractEndDate_LocalContractEndDate = "localContractEndDate"
	Field_ContractEndDate_HpmContractEndDate   = "hpmContractEndDate"
)

// Repo definitions
type PtvImportHistoryDefaultRepository struct {
	Db mongodb.IDBClient
}

func NewPtvImportHistoryDefaultRepository() PtvImportHistoryDefaultRepository {
	return PtvImportHistoryDefaultRepository{
		Db: mongodb_repo.NewMongoDbIDBClient("tutum_mvz_db", "ptv_import_history", true),
	}
}

func (r *PtvImportHistoryDefaultRepository) Find(ctx *titan.Context, filter any, opts ...*options.FindOptions) ([]ptv_import_common.PtvImportHistory, error) {
	var data []ptv_import_common.PtvImportHistory
	err := r.Db.Find(ctx, filter, &data, opts...)
	if err != nil {
		return []ptv_import_common.PtvImportHistory{}, err
	}
	return data, nil
}

func (r *PtvImportHistoryDefaultRepository) FindOne(ctx *titan.Context, filter any, opts ...*options.FindOneOptions) (*ptv_import_common.PtvImportHistory, error) {
	var data ptv_import_common.PtvImportHistory
	err := r.Db.FindOne(ctx, filter, &data, opts...)
	if err != nil {
		return nil, err
	}
	// if data.Id != nil {
	// 	return &data, nil
	// }
	return nil, nil
}

func (r *PtvImportHistoryDefaultRepository) Aggregate(
	ctx *titan.Context,
	filter any,
	_ ...*options.AggregateOptions) ([]ptv_import_common.PtvImportHistory, error) {
	var entities []ptv_import_common.PtvImportHistory
	err := r.Db.Aggregate(ctx, filter, &entities)

	if err != nil {
		return []ptv_import_common.PtvImportHistory{}, err
	}

	return entities, nil
}

func (r *PtvImportHistoryDefaultRepository) FindById(ctx *titan.Context, uuid uuid.UUID) (*ptv_import_common.PtvImportHistory, error) {
	filter := bson.D{{
		Key:   Field_Id,
		Value: uuid,
	}}

	data, err := r.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	if len(data) == 0 {
		return nil, nil
	}

	return &data[0], nil
}

func (r *PtvImportHistoryDefaultRepository) GetById(ctx *titan.Context, uuid uuid.UUID) (*ptv_import_common.PtvImportHistory, error) {
	data, err := r.FindById(ctx, uuid)
	if err != nil {
		return nil, err
	}

	if data == nil {
		return nil, titan.NewRecordNotFoundException("ptv_import_common.PtvImportHistory", uuid.String(), "NOT_FOUND_RECORD")
	}
	return data, nil
}

func (r *PtvImportHistoryDefaultRepository) GetByIds(ctx *titan.Context, uuids []uuid.UUID) (data []ptv_import_common.PtvImportHistory, err error) {
	filter := bson.D{
		{
			Key: Field_Id,
			Value: bson.D{
				{
					Key:   "$in",
					Value: uuids,
				},
			},
		},
	}
	err = r.Db.Find(ctx, filter, &data)
	return data, err
}

func (r *PtvImportHistoryDefaultRepository) Create(ctx *titan.Context, request ptv_import_common.PtvImportHistory) (*ptv_import_common.PtvImportHistory, error) {
	var data ptv_import_common.PtvImportHistory
	err := r.Db.Create(ctx, request, &data)
	if err != nil {
		return nil, err
	}
	return &data, nil
}

func (r *PtvImportHistoryDefaultRepository) CreateMany(ctx *titan.Context, requests []ptv_import_common.PtvImportHistory) ([]ptv_import_common.PtvImportHistory, error) {
	var data []ptv_import_common.PtvImportHistory
	err := r.Db.Create(ctx, requests, &data)
	if err != nil {
		return make([]ptv_import_common.PtvImportHistory, 0), err
	}
	return data, nil
}

func (r *PtvImportHistoryDefaultRepository) Delete(ctx *titan.Context, filter any) (int64, error) {
	res, err := r.Db.Delete(ctx, filter)

	if err != nil {
		return 0, err
	}

	return res.DeletedCount, nil
}

func (r *PtvImportHistoryDefaultRepository) DeleteById(ctx *titan.Context, id uuid.UUID) (bool, error) {
	filter := bson.D{{Key: Field_Id, Value: id}}
	res, err := r.Delete(ctx, filter)
	if err != nil {
		return false, err
	}
	if res != 1 {
		return false, errors.New("delete_failed")
	}

	return true, nil
}

func (r *PtvImportHistoryDefaultRepository) FindOneAndUpdate(ctx *titan.Context, filter, update any, opts ...*options.FindOneAndUpdateOptions) (*ptv_import_common.PtvImportHistory, error) {
	var entity ptv_import_common.PtvImportHistory
	err := r.Db.FindOneAndUpdate(ctx, filter, update, &entity, opts...)
	if err != nil {
		return nil, err
	}
	return &entity, nil
}

func (r *PtvImportHistoryDefaultRepository) FindOneAndReplace(ctx *titan.Context, filter any, replacement ptv_import_common.PtvImportHistory, opts ...*options.FindOneAndReplaceOptions) (*ptv_import_common.PtvImportHistory, error) {
	var entity ptv_import_common.PtvImportHistory
	err := r.Db.FindOneAndReplace(ctx, filter, replacement, &entity, opts...)
	if err != nil {
		return nil, err
	}
	return &entity, nil
}

func (r *PtvImportHistoryDefaultRepository) Update(ctx *titan.Context, update ptv_import_common.PtvImportHistory) (*ptv_import_common.PtvImportHistory, error) {
	var entity ptv_import_common.PtvImportHistory
	err := r.Db.Update(ctx, update, &entity)
	if err != nil {
		return nil, err
	}
	return &entity, nil
}

func (r *PtvImportHistoryDefaultRepository) UpdateMany(ctx *titan.Context, update []ptv_import_common.PtvImportHistory) ([]ptv_import_common.PtvImportHistory, error) {
	var entities []ptv_import_common.PtvImportHistory
	err := r.Db.Update(ctx, update, &entities)
	if err != nil {
		return nil, err
	}
	return entities, nil
}

func (r *PtvImportHistoryDefaultRepository) Upsert(ctx *titan.Context, request ptv_import_common.PtvImportHistory) (*ptv_import_common.PtvImportHistory, error) {
	filter := bson.D{{Key: Field_Id, Value: request.Id}}
	opts := options.FindOneAndReplace().SetReturnDocument(options.After).SetUpsert(true)

	entity, err := r.FindOneAndReplace(ctx, filter, request, opts)

	if err != nil {
		if isDupkeyErr(err) {
			return r.FindById(ctx, request.Id)
		}
		return nil, err
	}

	// if entity.Id == nil {
	// 	return nil, errors.New("no_response_data")
	// }

	return entity, nil
}

func (r *PtvImportHistoryDefaultRepository) Count(
	ctx *titan.Context,
	filter any) (int64, error) {
	response, err := r.Db.Count(ctx, filter)

	if err != nil {
		return 0, err
	}

	return response.Count, nil
}

func (r *PtvImportHistoryDefaultRepository) GetCollectionName(ctx *titan.Context) (string, error) {
	response, err := r.Db.GetCollectionName(ctx)

	if err != nil {
		return "", err
	}

	return response, nil
}

func isDupkeyErr(err error) bool {
	err = errors.Unwrap(err)
	_err, ok := err.(*titan.ClientResponseError)
	if !ok {
		return false
	}
	if !strings.Contains(string(_err.Response.Body), "index: _id_ dup key:") {
		return false
	}

	return true
}
