// This code was autogenerated from repo/mvz/encounter_common.proto, do not edit.

package patient_encounter

import (
	"encoding/json"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	common10 "git.tutum.dev/medi/tutum/ares/service/arriba/common"

	catalog_sdav_common "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdav_common"

	common3 "git.tutum.dev/medi/tutum/ares/service/domains/api/common"

	common8 "git.tutum.dev/medi/tutum/ares/service/domains/api/document_management/common"

	common9 "git.tutum.dev/medi/tutum/ares/service/domains/api/document_type/common"

	lab_common "git.tutum.dev/medi/tutum/ares/service/domains/api/lab_common"

	master_data_common "git.tutum.dev/medi/tutum/ares/service/domains/api/master_data_common"

	patient_profile_common "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"

	common7 "git.tutum.dev/medi/tutum/ares/service/domains/appointment/common"

	common6 "git.tutum.dev/medi/tutum/ares/service/domains/doctor_letter/common"

	common4 "git.tutum.dev/medi/tutum/ares/service/domains/form/common"

	common5 "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/heimi/common"

	common2 "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/himi/himi_model/common"

	common1 "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/bmp/common"

	common "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/common"

	waiting_room_common "git.tutum.dev/medi/tutum/ares/service/domains/waiting_room/common/waiting_room_common"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type DoctorLetterTimeline struct {
	Id         *uuid.UUID          `json:"id" bson:"id"`
	SortOrder  float64             `json:"sortOrder" bson:"sortOrder"`
	Body       string              `json:"body" bson:"body"`
	TemplateId *uuid.UUID          `json:"templateId" bson:"templateId"`
	Sender     *common6.Sender     `json:"sender" bson:"sender"`
	Receiver   *common6.Receiver   `json:"receiver" bson:"receiver"`
	Variables  []*common6.Variable `json:"variables"`
}

type EncounterNoteTimeline struct {
	Id             *uuid.UUID         `json:"id" bson:"id"`
	SortOrder      float64            `json:"sortOrder" bson:"sortOrder"`
	FromUnknownRow *bool              `json:"fromUnknownRow" bson:"fromUnknownRow"`
	Note           string             `json:"note" bson:"note"`
	Command        string             `json:"command" bson:"command"`
	Sources        *Sources           `json:"sources" bson:"sources"`
	Type           *EncounterNoteType `json:"type" bson:"type"`
	AuditLog       *common3.AuditLog  `json:"auditLog"`
	SchienIds      []uuid.UUID        `json:"schienIds" bson:"schienIds"`
}

type EncounterCalendarTimeline struct {
	WaitingRoomPatient             *waiting_room_common.WaitingRoomPatient `json:"waitingRoomPatient" bson:"waitingRoomPatient"`
	WaitingRoomName                *string                                 `json:"waitingRoomName" bson:"waitingRoomName"`
	AcceptableWaitingTimeInMinutes *int64                                  `json:"acceptableWaitingTimeInMinutes" bson:"acceptableWaitingTimeInMinutes"`
	ActiveTimeMeasurement          *bool                                   `json:"activeTimeMeasurement" bson:"activeTimeMeasurement"`
}

type EncounterServiceTimeline struct {
	Code                          string                                       `json:"code"`
	Description                   string                                       `json:"description"`
	ReferralDoctorInfo            *ReferralDoctorInfo                          `json:"referralDoctorInfo" bson:"referralDoctorInfo"`
	MaterialCosts                 *MaterialCosts                               `json:"materialCosts" bson:"materialCosts"`
	CareFacility                  *CareFacility                                `json:"careFacility" bson:"careFacility"`
	FreeText                      string                                       `json:"freeText" bson:"freeText"`
	Errors                        *[]*EncounterItemError                       `json:"errors"`
	Command                       string                                       `json:"command"`
	PatientId                     *uuid.UUID                                   `json:"patientId" bson:"patientId"`
	Sources                       *Sources                                     `json:"sources"`
	IsPreParticipate              bool                                         `json:"isPreParticipate" bson:"isPreParticipate"`
	AuditLog                      *common3.AuditLog                            `json:"auditLog" bson:"auditLog"`
	Scheins                       *[]*common3.ScheinWithMainGroup              `json:"scheins" bson:"scheins"`
	ServiceMainGroup              *common3.MainGroup                           `json:"serviceMainGroup" bson:"serviceMainGroup"`
	AdditionalInfos               *[]*AdditionalInfoParent                     `json:"additionalInfos" bson:"additionalInfos"`
	AdditionalInfosRaw            *string                                      `json:"additionalInfosRaw" bson:"additionalInfosRaw"`
	ChargeSystemId                *string                                      `json:"chargeSystemId"`
	ApprovalStatus                *EncounterServiceTimelinePsychotherapyStatus `json:"approvalStatus" bson:"approvalStatus"`
	TimelineServiceSuggestionId   *uuid.UUID                                   `json:"timelineServiceSuggestionId"`
	EabId                         *uuid.UUID                                   `json:"eabId"`
	PreParticipateType            *PreParticipateType                          `json:"preParticipateType" bson:"preParticipateType"`
	IsSubmitPreParticipateSucsess *bool                                        `json:"isSubmitPreParticipateSucsess"`
	ParticipateId                 *uuid.UUID                                   `json:"participateId"`
	ServiceId                     *uuid.UUID                                   `json:"serviceId"`
	Price                         *float64                                     `json:"price"`
}

type Diagnose struct {
	Code        string     `json:"code"`
	Description string     `json:"description"`
	Certainty   Certainty  `json:"certainty"`
	TimelineId  *uuid.UUID `json:"timelineId" bson:"timelineId"`
}

type DiagnoseSuggestion struct {
	SuggestionType SuggestionType               `json:"suggestionType" bson:"suggestionType"`
	Proposal       string                       `json:"proposal"`
	Diagnoses      []*Diagnose                  `json:"diagnoses"`
	RuleId         string                       `json:"ruleId" bson:"ruleId"`
	Applied        bool                         `json:"applied"`
	Hint           string                       `json:"hint"`
	CheckTime      master_data_common.CheckTime `json:"checkTime"`
}

type EncounterDiagnoseTimeline struct {
	Code                    string                          `json:"code"`
	Description             string                          `json:"description"`
	Type                    DiagnoseType                    `json:"type"`
	StartDate               *int64                          `json:"startDate"`
	ValidUntil              *int64                          `json:"validUntil" bson:"validUntil"`
	Certainty               *Certainty                      `json:"certainty"`
	Laterality              *Laterality                     `json:"laterality"`
	Hib                     bool                            `json:"hib"`
	Mrsa                    bool                            `json:"mrsa"`
	Imported                bool                            `json:"imported"`
	FreeText                string                          `json:"freeText" bson:"freeText"`
	Errors                  *[]*EncounterItemError          `json:"errors"`
	Command                 string                          `json:"command"`
	Group                   bool                            `json:"group"`
	Sources                 *Sources                        `json:"sources"`
	MorbiRsa                *MorbiRsa                       `json:"morbiRsa" bson:"morbiRsa"`
	AuditLog                *common3.AuditLog               `json:"auditLog" bson:"auditLog"`
	Scheins                 *[]*common3.ScheinWithMainGroup `json:"scheins" bson:"schiens"`
	DiagnoseSuggestions     *[]*DiagnoseSuggestion          `json:"diagnoseSuggestions" bson:"diagnoseSuggestions"`
	RunSdkrw                RunSdkrwEnum                    `json:"runSdkrw" bson:"runSdkrw"`
	Exception               string                          `json:"exception"`
	Explanation             string                          `json:"explanation"`
	SdvaRefs                []string                        `json:"sdvaRefs" bson:"sdvaRefs"`
	MarkedTreatmentRelevant bool                            `json:"markedTreatmentRelevant"`
}

type EncounterItemError struct {
	Type            EncounterItemErrorType `json:"type"`
	Message         string                 `json:"message"`
	AkaFunction     string                 `json:"akaFunction"`
	ErrorCode       string                 `json:"errorCode"`
	MetaData        map[string]string      `json:"metaData"`
	ValidationLevel ValidationLevel        `json:"validationLevel"`
	ErrorParams     []string               `json:"errorParams"`
}

type QuarterEncounterItemError struct {
	Type    EncounterItemErrorType `json:"type"`
	Message string                 `json:"message"`
	Code    string                 `json:"code"`
}

type MorbiRsa struct {
	Chronic   bool `json:"chronic"`
	KrhNumber bool `json:"krhNumber"`
	KrhLabel  bool `json:"krhLabel"`
}

type PrescriptionDates struct {
	CreatedDate int64  `json:"createdDate"`
	ChangedDate *int64 `json:"changedDate"`
	PrintedDate *int64 `json:"printedDate"`
	DeletedDate *int64 `json:"deletedDate"`
}

type Drug struct {
	DrugId  string `json:"drugId"`
	AutIdem bool   `json:"autIdem"`
	Amount  int32  `json:"amount"`
}

type ReceiptKvDetails struct {
	DocumentRequired bool `json:"documentRequired"`
	Himi             bool `json:"himi"`
	Vaccine          bool `json:"vaccine"`
}

type Delegate struct {
	HavgNumber string `json:"havgNumber"`
	MediId     string `json:"mediId"`
	Lanr       string `json:"lanr"`
}

type AdditionalInfoParent struct {
	FK       string                 `json:"fK" bson:"fK"`
	Value    string                 `json:"value" bson:"value"`
	Children []*AdditionalInfoChild `json:"children" bson:"children"`
}

type AdditionalInfoChild struct {
	FK    string `json:"fK" bson:"fK"`
	Value string `json:"value" bson:"value"`
}

type EncounterService struct {
	Code               string                          `json:"code" bson:"code"`
	Description        string                          `json:"description" bson:"description"`
	ReferralDoctorInfo *ReferralDoctorInfo             `json:"referralDoctorInfo" bson:"referralDoctorInfo"`
	MaterialCosts      *MaterialCosts                  `json:"materialCosts" bson:"materialCosts"`
	CareFacility       *CareFacility                   `json:"careFacility" bson:"careFacility"`
	FreeText           string                          `json:"freeText" bson:"freeText"`
	Errors             *[]*EncounterItemError          `json:"errors" bson:"errors"`
	Command            string                          `json:"command" bson:"command"`
	PatientId          *uuid.UUID                      `json:"patientId" bson:"patientId"`
	Sources            *Sources                        `json:"sources" bson:"sources"`
	IsPreParticipate   bool                            `json:"isPreParticipate" bson:"isPreParticipate"`
	Scheins            *[]*common3.ScheinWithMainGroup `json:"scheins" bson:"schiens"`
	ServiceMainGroup   *common3.MainGroup              `json:"serviceMainGroup" bson:"serviceMainGroup"`
	AdditionalInfos    *[]*AdditionalInfoParent        `json:"additionalInfos" bson:"additionalInfos"`
	AdditionalInfosRaw *string                         `json:"additionalInfosRaw" bson:"additionalInfosRaw"`
}

type ReferralDoctorInfo struct {
	Bsnr         *string `json:"bsnr" bson:"bsnr"`
	Lanr         *string `json:"lanr" bson:"lanr"`
	RequiredLanr *bool   `json:"requiredLanr" bson:"requiredLanr"`
	RequiredBsnr *bool   `json:"requiredBsnr" bson:"requiredBsnr"`
}

type MaterialCosts struct {
	Required              *bool                     `json:"required" bson:"required"`
	MaterialCostsItemList *[]*MaterialCostsItemList `json:"materialCostsItemList" bson:"materialCostsItemList"`
}

type MaterialCostsItemList struct {
	Id          *uuid.UUID `json:"id" bson:"id"`
	Amount      string     `json:"amount" bson:"amount"`
	Description string     `json:"description" bson:"description"`
	Required    *bool      `json:"required" bson:"required"`
}

type CareFacility struct {
	Name     string `json:"name" bson:"name"`
	Ort      string `json:"ort" bson:"ort"`
	Required *bool  `json:"required" bson:"required"`
}

type EncounterDiagnose struct {
	Code        string                          `json:"code" bson:"code"`
	Description string                          `json:"description" bson:"description"`
	Type        DiagnoseType                    `json:"type" bson:"type"`
	ValidUntil  *int64                          `json:"validUntil" bson:"validUntil"`
	Certainty   *Certainty                      `json:"certainty" bson:"certainty"`
	Laterality  *Laterality                     `json:"laterality" bson:"laterality"`
	Hib         bool                            `json:"hib" bson:"hib"`
	Mrsa        bool                            `json:"mrsa" bson:"mrsa"`
	Imported    bool                            `json:"imported" bson:"imported"`
	FreeText    string                          `json:"freeText" bson:"freeText"`
	Errors      *[]*EncounterItemError          `json:"errors" bson:"errors"`
	Command     string                          `json:"command" bson:"command"`
	Group       bool                            `json:"group" bson:"group"`
	Sources     *Sources                        `json:"sources" bson:"sources"`
	MorbiRsa    *MorbiRsa                       `json:"morbiRsa" bson:"morbiRsa"`
	Scheins     *[]*common3.ScheinWithMainGroup `json:"scheins" bson:"schiens"`
	Exception   string                          `json:"exception"`
	Explanation string                          `json:"explanation"`
	SdvaRefs    []string                        `json:"sdvaRefs"`
}

type EncounterNote struct {
	Id             *uuid.UUID                      `json:"id" bson:"id"`
	SortOrder      float64                         `json:"sortOrder" bson:"sortOrder"`
	FromUnknownRow *bool                           `json:"fromUnknownRow" bson:"fromUnknownRow"`
	Note           string                          `json:"note" bson:"note"`
	Command        string                          `json:"command" bson:"command"`
	Sources        *Sources                        `json:"sources" bson:"sources"`
	Type           *EncounterNoteType              `json:"type" bson:"type"`
	Scheins        *[]*common3.ScheinWithMainGroup `json:"scheins" bson:"schiens"`
}

type EncounterMedicinePrescription struct {
	Id                     *uuid.UUID           `json:"id"`
	MedicinePrescriptionId *uuid.UUID           `json:"medicinePrescriptionId"`
	FormInfos              []*common.FormInfo   `json:"formInfos"`
	BillingInfo            *common3.BillingInfo `json:"billingInfo"`
	AuditLog               *common3.AuditLog    `json:"auditLog"`
	SortOrder              float64              `json:"sortOrder" bson:"sortOrder"`
	PatientId              *uuid.UUID           `json:"patientId"`
	EncounterId            *uuid.UUID           `json:"encounterId"`
}

type EncounterHimiPrescription struct {
	Id                 *uuid.UUID              `json:"id"`
	HimiPrescriptionId *uuid.UUID              `json:"himiPrescriptionId"`
	FormInfo           *common2.FormInfo       `json:"formInfo"`
	AdditionalForm     *common2.AdditionalForm `json:"additionalForm"`
	BillingInfo        *common3.BillingInfo    `json:"billingInfo"`
	AuditLog           *common3.AuditLog       `json:"auditLog"`
	PatientId          *uuid.UUID              `json:"patientId"`
	EncounterId        *uuid.UUID              `json:"encounterId"`
	PrintDate          *int64                  `json:"printDate"`
	PrescribeDate      *int64                  `json:"prescribeDate"`
	CreatedDate        int64                   `json:"createdDate"`
	DiagnoseCode       string                  `json:"diagnoseCode"`
	SecondaryDiagnore  *string                 `json:"secondaryDiagnore"`
}

type HeimiForm struct {
	HeimiPrescriptionId *uuid.UUID            `json:"heimiPrescriptionId"`
	Prescription        *common5.Prescription `json:"prescription"`
}

type EncounterHeimiPrescription struct {
	Id          *uuid.UUID           `json:"id"`
	HeimiForm   *HeimiForm           `json:"heimiForm"`
	PatientId   *uuid.UUID           `json:"patientId"`
	EncounterId *uuid.UUID           `json:"encounterId"`
	BillingInfo *common3.BillingInfo `json:"billingInfo"`
	SortOrder   float64              `json:"sortOrder" bson:"sortOrder"`
	AuditLog    *common3.AuditLog    `json:"auditLog"`
}

type EncounterForm struct {
	Id                        *uuid.UUID                 `json:"id"`
	Prescribe                 *common4.Prescribe         `json:"prescribe"`
	PatientId                 *uuid.UUID                 `json:"patientId"`
	EncounterId               *uuid.UUID                 `json:"encounterId"`
	BillingInfo               *common3.BillingInfo       `json:"billingInfo"`
	SortOrder                 float64                    `json:"sortOrder" bson:"sortOrder"`
	AuditLog                  *common3.AuditLog          `json:"auditLog"`
	IsEnrollmentForm          bool                       `json:"isEnrollmentForm"`
	EnrollmentId              *uuid.UUID                 `json:"enrollmentId"`
	EnrollmentFormType        *string                    `json:"enrollmentFormType"`
	EnrollmentPrintFormStatus *EnrollmentPrintFormStatus `json:"enrollmentPrintFormStatus"`
	BgInvoice                 *common6.BgInvoice         `json:"bgInvoice"`
}

type EncounterLab struct {
	Id              *uuid.UUID                  `json:"id"`
	LabForm         *lab_common.LabForm         `json:"labForm"`
	PatientId       *uuid.UUID                  `json:"patientId"`
	EncounterId     *uuid.UUID                  `json:"encounterId"`
	BillingInfo     *common3.BillingInfo        `json:"billingInfo"`
	SortOrder       float64                     `json:"sortOrder" bson:"sortOrder"`
	AuditLog        *common3.AuditLog           `json:"auditLog"`
	LabResultStatus *lab_common.LabResultStatus `json:"labResultStatus"`
}

type EncounterPatientMedicalData struct {
	Id          *uuid.UUID                                 `json:"id"`
	SortOrder   float64                                    `json:"sortOrder" bson:"sortOrder"`
	Old         *patient_profile_common.PatientMedicalData `json:"old"`
	New         *patient_profile_common.PatientMedicalData `json:"new"`
	PatientId   *uuid.UUID                                 `json:"patientId"`
	EncounterId *uuid.UUID                                 `json:"encounterId"`
	AuditLog    *common3.AuditLog                          `json:"auditLog"`
}

type EncounterMedicinePlanHistory struct {
	Id                 *uuid.UUID                     `json:"id"`
	MedicationPlanId   *uuid.UUID                     `json:"medicationPlanId"`
	ActionType         string                         `json:"actionType"`
	MedicineInfo       *common1.MedicationInformation `json:"medicineInfo"`
	BeforeMedicineInfo *common1.MedicationInformation `json:"beforeMedicineInfo"`
	AuditLog           *common3.AuditLog              `json:"auditLog"`
}

type EncounterEHIC struct {
	FormSetting *string                                `json:"formSetting"`
	Language    *string                                `json:"language"`
	Status      *common3.EuropeanHealthInsuranceStatus `json:"status"`
}

type ServiceCodeApproval struct {
	EntryIds       []uuid.UUID `json:"entryIds"`
	AmountBilled   int32       `json:"amountBilled"`
	AmountApproval *int32      `json:"amountApproval"`
	TerminalId     *uuid.UUID  `json:"terminalId"`
}

type EncounterPsychotherapy struct {
	RequestDate             *int64                          `json:"requestDate" bson:"requestDate"`
	ApprovalDate            int64                           `json:"approvalDate" bson:"approvalDate"`
	AmountApproval          int32                           `json:"amountApproval" bson:"amountApproval"`
	AmountBilled            int32                           `json:"amountBilled" bson:"amountBilled"`
	ServiceCodes            []string                        `json:"serviceCodes" bson:"serviceCodes"`
	ScheinId                *uuid.UUID                      `json:"scheinId" bson:"scheinId"`
	Status                  EncounterPsychotherapyStatus    `json:"status"`
	TerminateServiceId      *uuid.UUID                      `json:"terminateServiceId" bson:"terminateServiceId"`
	TakeOverId              *uuid.UUID                      `json:"takeOverId" bson:"takeOverId"`
	Entries                 map[string]*ServiceCodeApproval `json:"entries" bson:"entries"`
	ReferenceServiceCodes   []string                        `json:"referenceServiceCodes" bson:"referenceServiceCodes"`
	ReferenceAmountApproval int32                           `json:"referenceAmountApproval" bson:"referenceAmountApproval"`
}

type EncounterGoaService struct {
	Code               string                          `json:"code" validate:"required"`
	Description        string                          `json:"description" validate:"required"`
	FreeText           string                          `json:"freeText"`
	Factor             float64                         `json:"factor" validate:"required"`
	Quantity           float64                         `json:"quantity" validate:"required"`
	MaterialCosts      *MaterialCosts                  `json:"materialCosts"`
	Errors             *[]*EncounterItemError          `json:"errors"`
	Price              *float64                        `json:"price"`
	AdditionalInfos    *[]*AdditionalInfoParent        `json:"additionalInfos"`
	AdditionalInfosRaw *string                         `json:"additionalInfosRaw" bson:"additionalInfosRaw"`
	Command            string                          `json:"command"`
	Scheins            *[]*common3.ScheinWithMainGroup `json:"scheins" bson:"schiens"`
	IsChangeDefault    bool                            `json:"isChangeDefault"`
	ServiceId          *uuid.UUID                      `json:"serviceId"`
	OriginalPrice      *float64                        `json:"originalPrice"`
}

type EncounterUvGoaService struct {
	Code               string                          `json:"code" validate:"required"`
	Description        string                          `json:"description" validate:"required"`
	FreeText           string                          `json:"freeText"`
	MaterialCosts      *MaterialCosts                  `json:"materialCosts"`
	Errors             *[]*EncounterItemError          `json:"errors"`
	Price              *float64                        `json:"price"`
	AdditionalInfos    *[]*AdditionalInfoParent        `json:"additionalInfos"`
	AdditionalInfosRaw *string                         `json:"additionalInfosRaw" bson:"additionalInfosRaw"`
	Command            string                          `json:"command"`
	Scheins            *[]*common3.ScheinWithMainGroup `json:"scheins" bson:"schiens"`
	IsGeneral          bool                            `json:"isGeneral"`
	ServiceId          *uuid.UUID                      `json:"serviceId"`
}

type EncounterAppointmentTimeline struct {
	AppointmentId              string                              `json:"appointmentId" bson:"appointmentId" validate:"required"`
	AppointmentContent         *TimelineAppointmentContent         `json:"appointmentContent" bson:"appointmentContent"`
	AuditLog                   *common3.AuditLog                   `json:"auditLog" bson:"auditLog"`
	UpdatedAppointmentDateTime *common7.UpdatedAppointmentDateTime `json:"updatedAppointmentDateTime" bson:"updatedAppointmentDateTime"`
	UpdatedTreatingDoctor      *common7.UpdatedTreatingPerson      `json:"updatedTreatingDoctor" bson:"updatedTreatingDoctor"`
	UpdatedTreatingMFA         *common7.UpdatedTreatingPerson      `json:"updatedTreatingMFA" bson:"updatedTreatingMFA"`
	UpdatedTodoTypeName        *common7.UpdatedStringValue         `json:"updatedTodoTypeName" bson:"updatedTodoTypeName"`
	UpdatedMedicalDeviceName   *common7.UpdatedStringValue         `json:"updatedMedicalDeviceName" bson:"updatedMedicalDeviceName"`
	AppointmentAction          AppointmentAction                   `json:"appointmentAction" bson:"appointmentAction"`
}

type TimelineAppointmentContent struct {
	TreatingPerson    *common7.TreatingPerson `json:"treatingPerson" bson:"treatingPerson"`
	Note              string                  `json:"note" bson:"note"`
	StartTime         int64                   `json:"startTime" bson:"startTime"`
	EndTime           int64                   `json:"endTime" bson:"endTime"`
	TodoTypeName      string                  `json:"todoTypeName" bson:"todoTypeName"`
	MedicalDeviceName string                  `json:"medicalDeviceName" bson:"medicalDeviceName"`
}

type EncounterDocumentManagement struct {
	Id                *uuid.UUID                       `json:"id" bson:"id", validate:"required"`
	CompanionFileId   int64                            `json:"companionFileId" bson:"companionfileid"`
	CompanionFilePath string                           `json:"companionFilePath" bson:"companionfilepath"`
	Patient           *common8.Patient                 `json:"patient" validate:"required"`
	Sender            *catalog_sdav_common.SdavCatalog `json:"sender"`
	DocumentName      string                           `json:"documentName" bson:"documentname"`
	DocumentType      *common9.DocumentType            `json:"documentType" bson:"documenttype"`
	Description       *string                          `json:"description" bson:"description"`
	Status            common8.DocumentManagementStatus `json:"status" bson:"status"`
	ImportedDate      int64                            `json:"importedDate" bson:"importeddate"`
	ReadBy            *common8.ReadBy                  `json:"readBy" bson:"readBy"`
}

type EncounterArriba struct {
	SessionId          *uuid.UUID            `json:"sessionId" bson:"sessionId"`
	CompanionFilePaths []string              `json:"companionFilePaths" bson:"companionfilepaths"`
	Status             common10.ArribaStatus `json:"status" bson:"status"`
	ArribaId           *uuid.UUID            `json:"arribaId" bson:"arribaId"`
}

type EncounterCustomize struct {
	DocumentTypeId *uuid.UUID `json:"documentTypeId" bson:"documentTypeId"`
	Command        string     `json:"command"`
	Description    string     `json:"description"`
	SortOrder      float64    `json:"sortOrder" bson:"sortOrder"`
}

type EncounterGDT struct {
	FilePath             string         `json:"filePath"`
	FileName             string         `json:"fileName"`
	BucketName           string         `json:"bucketName"`
	Note                 string         `json:"note"`
	GdtImportSettingId   *uuid.UUID     `json:"gdtImportSettingId"`
	GdtImportSettingName string         `json:"gdtImportSettingName"`
	DocumentManagementId *uuid.UUID     `json:"documentManagementId"`
	Command              string         `json:"command"`
	ArchiveFiles         []*ArchiveFile `json:"archiveFiles"`
	LabOrderId           string         `json:"labOrderId"`
}

type EncounterLDT struct {
	FilePath             string           `json:"filePath"`
	FileName             string           `json:"fileName"`
	BucketName           string           `json:"bucketName"`
	Note                 string           `json:"note"`
	LdtImportSettingId   *uuid.UUID       `json:"ldtImportSettingId"`
	LdtImportSettingName string           `json:"ldtImportSettingName"`
	DocumentManagementId *uuid.UUID       `json:"documentManagementId"`
	Command              string           `json:"command"`
	Type                 EncounterLDTType `json:"type"`
	LabOrderId           string           `json:"labOrderId"`
}

type ArchiveFile struct {
	FileName   string `json:"fileName"`
	ObjectName string `json:"objectName"`
}

// enum definitions
type EncounterServiceTimelinePsychotherapyStatus string

const (
	IsApproval            EncounterServiceTimelinePsychotherapyStatus = "IsApproval"
	IsCompleted           EncounterServiceTimelinePsychotherapyStatus = "IsCompleted"
	HasBeenRemoveApproval EncounterServiceTimelinePsychotherapyStatus = "HasBeenRemoveApproval"
	NotAcceptedByKV       EncounterServiceTimelinePsychotherapyStatus = "NotAcceptedByKV"
)

type PreParticipateType string

const (
	UHU35 PreParticipateType = "UHU35"
	KJP4a PreParticipateType = "KJP4a"
)

type Laterality string

const (
	U Laterality = "U"
	L Laterality = "L"
	R Laterality = "R"
	B Laterality = "B"
)

type RunSdkrwEnum string

const (
	RUNSDKRWENUM_DEFAULT   RunSdkrwEnum = "DEFAULT"
	RUNSDKRWENUM_RUNNING   RunSdkrwEnum = "RUNNING"
	RUNSDKRWENUM_CANCELLED RunSdkrwEnum = "CANCELLED"
	RUNSDKRWENUM_DONE      RunSdkrwEnum = "DONE"
)

type Certainty string

const (
	G Certainty = "G"
	V Certainty = "V"
	Z Certainty = "Z"
	A Certainty = "A"
)

type EncounterNoteType string

const (
	ANAMNESE EncounterNoteType = "ANAMNESE"
	FINDING  EncounterNoteType = "FINDING"
	THERAPY  EncounterNoteType = "THERAPY"
	NOTE     EncounterNoteType = "NOTE"
)

type SuggestionType string

const (
	SUGGESTIONTYPE_ADD     SuggestionType = "SUGGESTIONTYPE_ADD"
	SUGGESTIONTYPE_DELETE  SuggestionType = "SUGGESTIONTYPE_DELETE"
	SUGGESTIONTYPE_REPLACE SuggestionType = "SUGGESTIONTYPE_REPLACE"
)

type DiagnoseType string

const (
	DIAGNOSETYPE_PERMANENT  DiagnoseType = "PERMANENT"
	DIAGNOSETYPE_ACUTE      DiagnoseType = "ACUTE"
	DIAGNOSETYPE_ANAMNESTIC DiagnoseType = "ANAMNESTIC"
)

type DiagnoseCommand string

const (
	PERMANENT_DIAGNOSE_COMMAND_DD DiagnoseCommand = "DD"
	PERMANENT_DIAGNOSE_COMMAND_DA DiagnoseCommand = "DA"
	ACUTE_DIAGNOSE_COMMAND        DiagnoseCommand = "D"
	ANAMNESTIC_DIAGNOSE_COMMAND   DiagnoseCommand = "AD"
)

type ValidationLevel string

const (
	ValidationLevelEntry     ValidationLevel = "ValidationLevelEntry"
	ValidationLevelEncounter ValidationLevel = "ValidationLevelEncounter"
	ValidationLevelQuarter   ValidationLevel = "ValidationLevelQuarter"
	ValidationLevelTimeline  ValidationLevel = "ValidationLevelTimeline"
)

type EncounterItemErrorType string

const (
	EncounterItemErrorType_error   EncounterItemErrorType = "error"
	EncounterItemErrorType_warning EncounterItemErrorType = "warning"
	EncounterItemErrorType_info    EncounterItemErrorType = "info"
)

type Sources string

const (
	Imported Sources = "Imported"
	Composer Sources = "Composer"
	Timeline Sources = "Timeline"
	EAB      Sources = "EAB"
)

type EncounterCase string

const (
	AB             EncounterCase = "AB"
	PB             EncounterCase = "PB"
	NOT            EncounterCase = "NOT"
	PRE_ENROLLMENT EncounterCase = "PRE_ENROLLMENT"
)

type TreatmentCase string

const (
	TreatmentCaseCustodian      TreatmentCase = "TreatmentCaseCustodian"
	TreatmentCaseDelegate       TreatmentCase = "TreatmentCaseDelegate"
	TreatmentCaseDeputy         TreatmentCase = "TreatmentCaseDeputy"
	TreatmentCasePreParticipate TreatmentCase = "TreatmentCasePreParticipate"
)

type EnrollmentPrintFormStatus string

const (
	EnrollmentPrintFormStatus_Generated           EnrollmentPrintFormStatus = "Generated"
	EnrollmentPrintFormStatus_Printed_Created     EnrollmentPrintFormStatus = "PrintedCreated"
	EnrollmentPrintFormStatus_Printed_Succesfully EnrollmentPrintFormStatus = "PrintedSuccesfully"
	EnrollmentPrintFormStatus_Printed_Failed      EnrollmentPrintFormStatus = "PrintedFailed"
	EnrollmentPrintFormStatus_Incorrect           EnrollmentPrintFormStatus = "Incorrect"
	EnrollmentPrintFormStatus_Handed_Over         EnrollmentPrintFormStatus = "HandedOver"
	EnrollmentPrintFormStatus_Received            EnrollmentPrintFormStatus = "Received"
)

type EncounterPsychotherapyStatus string

const (
	INPROGRESS         EncounterPsychotherapyStatus = "INPROGRESS"
	HAS_BEEN_TAKE_OVER EncounterPsychotherapyStatus = "HAS_BEEN_TAKE_OVER"
	COMPLETED          EncounterPsychotherapyStatus = "COMPLETED"
	BILLED             EncounterPsychotherapyStatus = "BILLED"
	READY_TO_BILL      EncounterPsychotherapyStatus = "READY_TO_BILL"
)

type AppointmentAction string

const (
	CREATE     AppointmentAction = "CREATE"
	RESCHEDULE AppointmentAction = "RESCHEDULE"
	CANCEL     AppointmentAction = "CANCEL"
	REMOVE     AppointmentAction = "REMOVE"
)

type EncounterLDTType string

const (
	LDT_RESULT EncounterLDTType = "LDT_RESULT"
	LDT_ORDER  EncounterLDTType = "LDT_ORDER"
)

// Define constants
const NATS_SUBJECT = "" // nats subject this service will listen to

// service event constants

// message event constants

// Define service interface -------------------------------------------------------------

// Define service proxy -------------------------------------------------------------------

// Define service router -----------------------------------------------------------------

// Define client ----------------------------------------------------------------------------------------------

// Define event Notifier -----------------------------------------------------------------------------------------
type PatientEncounterNotifier struct {
	client *titan.Client
}

func NewPatientEncounterNotifier() *PatientEncounterNotifier {
	client := titan.GetDefaultClient()
	return &PatientEncounterNotifier{client}
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type PatientEncounterSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewPatientEncounterSocketNotifier(socket *socket_api.SocketServiceClient) *PatientEncounterSocketNotifier {
	return &PatientEncounterSocketNotifier{socket}
}

// -----------------------------------------------
// Test event listener
type PatientEncounterEventListener struct {
	mux sync.Mutex
}

func (listener *PatientEncounterEventListener) Reset() {
	listener.mux.Lock()
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *PatientEncounterEventListener) Subscribe(s *titan.MessageSubscriber) {
}
