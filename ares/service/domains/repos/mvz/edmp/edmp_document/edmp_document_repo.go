package edmp_document

import (
	"time"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/edmp"
	mongodb "git.tutum.dev/medi/tutum/ares/pkg/repo"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	Field_EnrollStatus        = "enrollstatus"
	Field_EnrollmentId        = "enrollmentid"
	Field_DMPLabelingValue    = "dmplabelingvalue"
	Field_PatientId           = "patientid"
	Field_Fields              = "fields"
	Field_DocumentStatus      = "documentstatus"
	Field_DocumentType        = "documenttype"
	Field_ScheinId            = "scheinid"
	Field_DoctorId            = "doctorid"
	Field_TreatmentDoctorId   = "treatmentdoctorid"
	Field_DoctorRelationType  = "doctorrelationtype"
	Field_DocumentDate        = "documentdate"
	Field_DMPBillingFile      = "dmpbillingfile"
	Field_XPMResult           = "xpmresult"
	Field_PHQ9                = "phq9"
	Field_AdditionalContracts = "additionalcontracts"
	Field_CaseNumber          = "dmpcasenumber"
)

type EdmpEnrollmentDocumentEntity struct {
	repos.BaseEntity             `bson:",inline"`
	common.DocumentationOverview `bson:",inline"`
}

func (*EdmpEnrollmentDocumentEntity) GetPatientFieldName() string {
	return Field_PatientId
}

type EdmpEnrollmentDocumentRepo struct {
	mongodb.Repo[*EdmpEnrollmentDocumentEntity]
}

func (f *EdmpEnrollmentDocumentRepo) GetById(ctx *titan.Context, id uuid.UUID) (*EdmpEnrollmentDocumentEntity, error) {
	filter := bson.D{
		{Key: repos.Field_Id, Value: id},
		{Key: repos.Field_IsDeleted, Value: false},
	}
	return f.Repo.FindOne(ctx, filter)
}

// FindByQuater
func (f *EdmpEnrollmentDocumentRepo) FindByQuarter(
	ctx *titan.Context,
	req common.QuarterToDateRange,
	doctorId uuid.UUID,
	dmpLabelingValues []string,
) ([]EdmpEnrollmentDocumentEntity, error) {
	yearQuarter := util.YearQuarter{
		Year:     int32(req.Year),
		Quarter:  int32(req.Quarter),
		Location: ctx.RequestTimeZone(),
	}
	quarterRange, err := yearQuarter.GetQuarterDateRange()
	if err != nil {
		return nil, err
	}

	filter := bson.D{
		{Key: Field_DMPLabelingValue, Value: bson.D{{Key: "$in", Value: dmpLabelingValues}}},
		{Key: Field_DocumentDate, Value: bson.D{{Key: "$gte", Value: quarterRange.Start.UnixMilli()}, {Key: "$lt", Value: quarterRange.End.UnixMilli()}}},
		{Key: Field_DoctorId, Value: doctorId},
		{Key: Field_DocumentStatus, Value: bson.D{{Key: "$ne", Value: common.DocumentStatus_Billed}}},
		{Key: Field_DocumentType, Value: bson.D{{Key: "$nin", Value: []common.DocumentType{common.DocumentType_PHQ9_ED, common.DocumentType_PHQ9_FD}}}},
		{Key: repos.Field_IsDeleted, Value: false},
	}

	return slice.ToValueTypeWithError(f.Repo.Find(ctx, filter))
}
func (f *EdmpEnrollmentDocumentRepo) GetInQuarterByEnrollmentId(
	ctx *titan.Context,
	enrollmentId uuid.UUID,
	quarter, year int32) ([]EdmpEnrollmentDocumentEntity, error) {
	yearQuarter := util.YearQuarter{
		Year:     year,
		Quarter:  quarter,
		Location: ctx.RequestTimeZone(),
	}
	quarterRange, err := yearQuarter.GetQuarterDateRange()
	if err != nil {
		return nil, err
	}

	filter := bson.M{
		Field_EnrollmentId:    enrollmentId,
		Field_DocumentDate:    bson.M{"$gte": quarterRange.Start.UnixMilli(), "$lt": quarterRange.End.UnixMilli()},
		Field_DocumentType:    bson.M{"$nin": []common.DocumentType{common.DocumentType_PHQ9_ED, common.DocumentType_PHQ9_FD}},
		repos.Field_IsDeleted: false,
	}

	return slice.ToValueTypeWithError(f.Repo.Find(ctx, filter))
}

func (f *EdmpEnrollmentDocumentRepo) GetInQuarterByEnrollmentIds(
	ctx *titan.Context,
	enrollmentIds []uuid.UUID,
	quarter, year int32) ([]EdmpEnrollmentDocumentEntity, error) {
	if len(enrollmentIds) == 0 {
		return nil, nil
	}
	yearQuarter := util.YearQuarter{
		Year:     year,
		Quarter:  quarter,
		Location: ctx.RequestTimeZone(),
	}
	quarterRange, err := yearQuarter.GetQuarterDateRange()
	if err != nil {
		return nil, err
	}

	filter := bson.M{
		Field_EnrollmentId:    bson.M{"$in": enrollmentIds},
		Field_DocumentDate:    bson.M{"$gte": quarterRange.Start.UnixMilli(), "$lt": quarterRange.End.UnixMilli()},
		Field_DocumentType:    bson.M{"$nin": []common.DocumentType{common.DocumentType_PHQ9_ED, common.DocumentType_PHQ9_FD}},
		repos.Field_IsDeleted: false,
	}

	return slice.ToValueTypeWithError(f.Repo.Find(ctx, filter))
}

type FilterGetFunc func() bson.D

var GetActiveDocumentByType = func(enrollmentId uuid.UUID, documentType common.DocumentType, enrollStatus common.EnrollStatus) FilterGetFunc {
	return func() bson.D {
		return bson.D{
			{Key: Field_EnrollmentId, Value: enrollmentId},
			{Key: Field_DocumentType, Value: documentType},
			{Key: Field_EnrollStatus, Value: enrollStatus},
			{Key: repos.Field_IsDeleted, Value: false},
		}
	}
}

var GetByPatientWithDocumentType = func(patientId uuid.UUID, documentType common.DocumentType, dmpLabelingValue string) FilterGetFunc {
	return func() bson.D {
		return bson.D{
			{Key: Field_PatientId, Value: patientId},
			{Key: Field_DocumentType, Value: documentType},
			{Key: Field_DMPLabelingValue, Value: dmpLabelingValue},
		}
	}
}

var GetByEnrollmentIdWithDocumentType = func(enrollmentId uuid.UUID, documentType common.DocumentType) FilterGetFunc {
	return func() bson.D {
		return bson.D{
			{Key: Field_EnrollmentId, Value: enrollmentId},
			{Key: Field_DocumentType, Value: documentType},
			{Key: repos.Field_IsDeleted, Value: false},
		}
	}
}

func (f *EdmpEnrollmentDocumentRepo) GetByFilterFunc(ctx *titan.Context, filterFunc FilterGetFunc) ([]EdmpEnrollmentDocumentEntity, error) {
	filter := filterFunc()
	return slice.ToValueTypeWithError(f.Repo.Find(ctx, filter))
}

func (f *EdmpEnrollmentDocumentRepo) GetByEnrollmentIds(ctx *titan.Context, enrollmentIds []uuid.UUID) ([]EdmpEnrollmentDocumentEntity, error) {
	if len(enrollmentIds) == 0 {
		return nil, nil
	}

	filter := bson.D{
		{Key: Field_EnrollmentId, Value: bson.D{{Key: "$in", Value: enrollmentIds}}},
		{Key: repos.Field_IsDeleted, Value: false},
	}

	return slice.ToValueTypeWithError(f.Repo.Find(ctx, filter))
}

func (f *EdmpEnrollmentDocumentRepo) GetByScheinId(ctx *titan.Context, scheinId uuid.UUID) ([]EdmpEnrollmentDocumentEntity, error) {
	if len(scheinId) == 0 {
		return nil, nil
	}
	filter := bson.D{
		{Key: Field_ScheinId, Value: scheinId},
		{Key: repos.Field_IsDeleted, Value: false},
	}
	return slice.ToValueTypeWithError(f.Repo.Find(ctx, filter))
}

func (f *EdmpEnrollmentDocumentRepo) GetByEnrollmentId(ctx *titan.Context, enrollmentId uuid.UUID) ([]EdmpEnrollmentDocumentEntity, error) {
	filter := bson.D{
		{Key: Field_EnrollmentId, Value: enrollmentId},
		{Key: repos.Field_IsDeleted, Value: false},
	}

	return slice.ToValueTypeWithError(f.Repo.Find(ctx, filter))
}

func (f *EdmpEnrollmentDocumentRepo) GetByEnrollmentIdAndDocumentType(ctx *titan.Context, enrollmentId uuid.UUID, documentType common.DocumentType) ([]EdmpEnrollmentDocumentEntity, error) {
	filter := bson.D{
		{Key: Field_EnrollmentId, Value: enrollmentId},
		{Key: Field_DocumentType, Value: documentType},
		{Key: repos.Field_IsDeleted, Value: false},
	}

	sort := options.Find().SetSort(bson.D{{Key: repos.Field_CreatedAt, Value: -1}})

	return slice.ToValueTypeWithError(f.Repo.Find(ctx, filter, sort))
}

// FindByDate
func (f *EdmpEnrollmentDocumentRepo) FindDocumentNotBilledByDateRange(
	ctx *titan.Context,
	startDate, endDate time.Time) ([]EdmpEnrollmentDocumentEntity, error) {
	filter := bson.M{
		Field_DocumentDate: bson.M{
			"$gte": startDate.UnixMilli(),
			"$lt":  endDate.UnixMilli(),
		},
		Field_DocumentStatus:  bson.M{"$ne": common.DocumentStatus_Billed},
		repos.Field_IsDeleted: false,
	}

	return slice.ToValueTypeWithError(f.Repo.Find(ctx, filter))
}

// FindByPatientIds return []EdmpEnrollmentDocumentEntity
func (f *EdmpEnrollmentDocumentRepo) FindByPatientIds(ctx *titan.Context, patientIds []uuid.UUID) ([]EdmpEnrollmentDocumentEntity, error) {
	if len(patientIds) == 0 {
		return nil, nil
	}

	filter := bson.D{
		{Key: Field_PatientId, Value: bson.D{{Key: "$in", Value: patientIds}}},
		{Key: repos.Field_IsDeleted, Value: false},
	}

	documents, err := f.Repo.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	if len(documents) == 0 {
		return nil, nil
	}

	return slice.ToValueType(documents), nil
}

// GetByPatientIds return map[patientId][]EdmpEnrollmentDocumentEntity
func (f *EdmpEnrollmentDocumentRepo) GetByPatientIds(ctx *titan.Context, patientIds []uuid.UUID) (map[uuid.UUID][]EdmpEnrollmentDocumentEntity, error) {
	if len(patientIds) == 0 {
		return nil, nil
	}

	filter := bson.D{
		{Key: Field_PatientId, Value: bson.D{{Key: "$in", Value: patientIds}}},
		{Key: repos.Field_IsDeleted, Value: false},
	}
	results, err := f.Repo.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	var result = make(map[uuid.UUID][]EdmpEnrollmentDocumentEntity)
	for _, r := range results {
		result[*r.PatientId] = append(result[*r.PatientId], *r)
	}
	return result, nil
}

func (f *EdmpEnrollmentDocumentRepo) FindByDMPLabelingValuesAndDateRange(
	ctx *titan.Context,
	dmpLabelingValues []string,
	startDate, endDate time.Time) ([]EdmpEnrollmentDocumentEntity, error) {
	if len(dmpLabelingValues) == 0 {
		return nil, nil
	}

	filter := bson.D{
		{Key: Field_DMPLabelingValue, Value: bson.D{{Key: "$in", Value: dmpLabelingValues}}},
		{Key: repos.Field_IsDeleted, Value: false},
		{Key: repos.Field_CreatedAt, Value: bson.D{{Key: "$gte", Value: startDate}, {Key: "$lt", Value: endDate}}},
	}

	return slice.ToValueTypeWithError(f.Repo.Find(ctx, filter))
}

func (f *EdmpEnrollmentDocumentRepo) Create(ctx *titan.Context, enrollment common.DocumentationOverview) (*EdmpEnrollmentDocumentEntity, error) {
	id := util.NewUUID()
	enrollment.DocumentationOverviewId = id
	result, err := f.Repo.Create(ctx, &EdmpEnrollmentDocumentEntity{
		BaseEntity: repos.BaseEntity{
			Id:        id,
			CreatedAt: util.Now(ctx),
			CreatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
		},
		DocumentationOverview: enrollment,
	})
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (f *EdmpEnrollmentDocumentRepo) UpdateById(ctx *titan.Context, id uuid.UUID, model common.DocumentationOverview) (*EdmpEnrollmentDocumentEntity, error) {
	filter := bson.M{repos.Field_Id: id}
	update := bson.M{"$set": bson.M{
		Field_DocumentStatus:      model.DocumentStatus,
		Field_Fields:              model.Fields,
		Field_DocumentType:        model.DocumentType,
		Field_ScheinId:            model.ScheinId,
		Field_DoctorId:            model.DoctorId,
		Field_TreatmentDoctorId:   model.TreatmentDoctorId,
		Field_DoctorRelationType:  model.DoctorRelationType,
		Field_PHQ9:                model.PHQ9,
		Field_DocumentDate:        model.DocumentDate,
		Field_AdditionalContracts: model.AdditionalContracts,
		Field_CaseNumber:          model.DMPCaseNumber,
		repos.Field_CreatedBy:     util.GetPointerValue(ctx.UserInfo().UserUUID()),
		repos.Field_UpdatedAt:     util.Now(ctx),
	}}

	entity, err := f.Repo.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
	if err != nil {
		return nil, err
	}

	return entity, nil
}

func (f *EdmpEnrollmentDocumentRepo) UpdateDMPBillingFileById(ctx *titan.Context, id uuid.UUID, dmpBillingFile common.DMPBillingFile) (*EdmpEnrollmentDocumentEntity, error) {
	filter := bson.M{repos.Field_Id: id}
	update := bson.M{"$set": bson.M{
		Field_DMPBillingFile:  dmpBillingFile,
		repos.Field_UpdatedAt: util.Now(ctx),
		repos.Field_UpdatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
	}}

	entity, err := f.Repo.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
	if err != nil {
		return nil, err
	}

	return entity, nil
}

func (f *EdmpEnrollmentDocumentRepo) GetLatestPHQ9DocumentOverview(ctx *titan.Context, patientId uuid.UUID, createdAt *time.Time) (*EdmpEnrollmentDocumentEntity, error) {
	filter := bson.M{
		repos.Field_IsDeleted:  false,
		Field_DocumentType:     bson.M{"$in": []common.DocumentType{common.DocumentType_PHQ9_ED, common.DocumentType_PHQ9_FD}},
		Field_PatientId:        patientId,
		Field_DMPLabelingValue: common.DMPValueEnum_Depression, // For now, phq9 just valid in depression
	}

	if createdAt != nil {
		filter["createdAt"] = bson.M{"$lt": util.GetPointerValue(createdAt)}
	}

	opt := options.FindOne().SetSort(bson.M{repos.Field_CreatedAt: -1})
	document, err := f.Repo.FindOne(ctx, filter, opt)
	if err != nil {
		return nil, err
	}

	return document, nil
}

func (f *EdmpEnrollmentDocumentRepo) GetIncompleteDocumentationOverviews(ctx *titan.Context, request edmp.GetIncompleteDocumentationOverviewsRequest) ([]edmp.DocumentationOverviewInfo, error) {
	filter := bson.M{
		Field_PatientId:        request.PatientId,
		Field_DocumentType:     request.DocumentType,
		Field_DMPLabelingValue: request.DMPLabelingValue,
		Field_DocumentStatus:   common.DocumentStatus_Saved,
	}
	entities, err := f.Repo.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	documentationOverviews := make([]edmp.DocumentationOverviewInfo, 0)
	for _, entity := range entities {
		documentationOverviews = append(documentationOverviews, edmp.DocumentationOverviewInfo{
			Id:                    *entity.Id,
			DocumentationOverview: entity.DocumentationOverview,
		})
	}
	return documentationOverviews, nil
}

func (f *EdmpEnrollmentDocumentRepo) FindCompletedDocumentForPatient(ctx *titan.Context, patientId uuid.UUID, documentTypes []common.DocumentType, dmpLabelingValue string) ([]EdmpEnrollmentDocumentEntity, error) {
	filter := bson.M{
		Field_PatientId:        patientId,
		Field_DocumentType:     bson.M{"$in": documentTypes},
		Field_DMPLabelingValue: dmpLabelingValue,
		Field_DocumentStatus:   bson.M{"$ne": common.DocumentStatus_Saved},
		repos.Field_IsDeleted:  false,
	}

	return slice.ToValueTypeWithError(f.Repo.Find(ctx, filter))
}

func (f *EdmpEnrollmentDocumentRepo) GetInDateRange(
	ctx *titan.Context,
	quarter, year int32,
	doctorId uuid.UUID,
	dmpLabelingValues []string) ([]EdmpEnrollmentDocumentEntity, error) {
	yearQuarter := util.YearQuarter{
		Year:     year,
		Quarter:  quarter,
		Location: ctx.RequestTimeZone(),
	}
	quarterRange, err := yearQuarter.GetQuarterDateRange()
	if err != nil {
		return nil, err
	}

	filter := bson.M{
		Field_DMPLabelingValue: bson.M{"$in": dmpLabelingValues},
		Field_DoctorId:         doctorId,
		repos.Field_CreatedAt:  bson.D{{Key: "$gte", Value: quarterRange.Start.UnixMilli()}, {Key: "$lt", Value: quarterRange.End.UnixMilli()}},
		repos.Field_IsDeleted:  false,
	}

	return slice.ToValueTypeWithError(f.Find(ctx, filter))
}

func (f *EdmpEnrollmentDocumentRepo) FindByIds(ctx *titan.Context, ids []uuid.UUID) ([]EdmpEnrollmentDocumentEntity, error) {
	filter := bson.M{
		repos.Field_Id:        bson.M{"$in": ids},
		repos.Field_IsDeleted: false,
	}
	return slice.ToValueTypeWithError(f.Repo.Find(ctx, filter))
}

func (f *EdmpEnrollmentDocumentRepo) UpdateStatusByDocumentIds(ctx *titan.Context, ids []uuid.UUID, status common.DocumentStatus) ([]EdmpEnrollmentDocumentEntity, error) {
	filter := bson.M{
		repos.Field_Id: bson.M{"$in": ids},
	}
	update := bson.M{
		"$set": bson.M{Field_DocumentStatus: status},
	}
	var entites []EdmpEnrollmentDocumentEntity
	_, err := f.IDBClient.UpdateMany(ctx, filter, update, &entites)
	if err != nil {
		return nil, err
	}
	return entites, nil
}

// suppose for write unit test easier
func (f *EdmpEnrollmentDocumentRepo) RemoveById(ctx *titan.Context, id uuid.UUID) (bool, error) {
	_, err := f.Repo.DeleteById(ctx, id)
	if err != nil {
		return false, err
	}

	return true, nil
}

func (f *EdmpEnrollmentDocumentRepo) DeleteById(ctx *titan.Context, id uuid.UUID) error {
	filter := bson.M{
		repos.Field_Id: id,
	}
	update := bson.M{
		"$set": bson.M{
			repos.Field_IsDeleted: true,
			repos.Field_UpdatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
			repos.Field_UpdatedAt: util.Now(ctx),
		},
	}
	_, err := f.Repo.FindOneAndUpdate(ctx, filter, update)
	return err
}

func (f *EdmpEnrollmentDocumentRepo) HardDeleteById(ctx *titan.Context, id uuid.UUID) error {
	_, err := f.Repo.DeleteById(ctx, id)
	return err
}

func (f *EdmpEnrollmentDocumentRepo) RestoreById(ctx *titan.Context, id uuid.UUID) error {
	filter := bson.M{
		repos.Field_Id:        id,
		repos.Field_IsDeleted: true,
	}
	update := bson.M{
		"$set": bson.M{
			repos.Field_IsDeleted: false,
			repos.Field_UpdatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
			repos.Field_UpdatedAt: util.Now(ctx),
		},
	}
	_, err := f.Repo.FindOneAndUpdate(ctx, filter, update)
	return err
}

func NewEdmpEnrollmentDocumentRepo() *EdmpEnrollmentDocumentRepo {
	return &EdmpEnrollmentDocumentRepo{
		mongodb.NewRepo[*EdmpEnrollmentDocumentEntity](mongodb_repo.NewMongoDbIDBClient("tutum_mvz_db", "edmp_enrollment_document", true)),
	}
}
