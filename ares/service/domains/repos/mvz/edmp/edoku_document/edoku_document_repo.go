package edoku_document

import (
	"sort"
	"time"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_overview"
	"git.tutum.dev/medi/tutum/ares/pkg/operator"
	mongodb "git.tutum.dev/medi/tutum/ares/pkg/repo"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	edmp_common "git.tutum.dev/medi/tutum/ares/service/domains/edmp/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/edmp/edmp_document"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
)

const (
	Field_BsnrID          = "bsnrid"
	Field_DocumentDate    = "documentdate"
	Field_DocumentStatus  = "documentstatus"
	Field_CaseNumber      = "dmpcasenumber"
	Field_IsDeleted       = "isDeleted"
	Field_EdokuDocumentId = "documentationoverviewid"
)

type EdokuEnrollmentDocumentRepo struct {
	Repo mongodb.Repo[*edmp_document.EdmpEnrollmentDocumentEntity]
	*edmp_document.EdmpEnrollmentDocumentRepo
}

func NewEdokuEnrollmentDocumentRepo() *EdokuEnrollmentDocumentRepo {
	repo := mongodb.NewRepo[*edmp_document.EdmpEnrollmentDocumentEntity](mongodb_repo.NewMongoDbIDBClient("tutum_mvz_db", "edoku_enrollment_document", true))
	return &EdokuEnrollmentDocumentRepo{
		Repo:                       repo,
		EdmpEnrollmentDocumentRepo: &edmp_document.EdmpEnrollmentDocumentRepo{Repo: repo},
	}
}

func (r *EdokuEnrollmentDocumentRepo) GetEdokuDocumentForValidationList(ctx *titan.Context, bsnrId uuid.UUID, quarter common.YearQuarter, openPreviousQuarter bool) ([]*edmp_document.EdmpEnrollmentDocumentEntity, error) {
	// startDate, endDate, err := util.GEtQ.GetQuarterDateRange(int(quarter.Quarter), int(quarter.Year))
	var startDate, endDate time.Time
	var err error
	yearQuarter := util.YearQuarter{
		Year:     quarter.Year,
		Quarter:  quarter.Quarter,
		Location: ctx.RequestTimeZone(),
	}
	quarterRange, err := yearQuarter.GetQuarterDateRange()
	if err != nil {
		return nil, err
	}
	startDate = util.GetPointerValue(quarterRange.Start)
	endDate = util.GetPointerValue(quarterRange.End)
	if openPreviousQuarter {
		previousQuarter := util.GetPreviousQuarterFrom(4, util.YearQuarter{
			Year:    quarter.Year,
			Quarter: quarter.Quarter,
		})

		previousQuarterRange, err := previousQuarter.GetQuarterDateRange()
		if err != nil {
			return nil, err
		}
		startDate = util.GetPointerValue(previousQuarterRange.Start)
	}

	// filter
	filter := bson.M{
		Field_BsnrID: bsnrId,
		Field_DocumentStatus: bson.M{
			operator.Ne: edmp_common.DocumentStatus_Billed,
		},
		Field_DocumentDate: bson.M{
			operator.Gte: startDate.UnixMilli(),
			operator.Lte: endDate.UnixMilli(),
		},
		Field_IsDeleted: false,
	}

	documents, err := r.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	sort.SliceStable(documents, func(i, j int) bool {
		return edmp_common.DocumentStatusPriority[*documents[i].DocumentStatus] < edmp_common.DocumentStatusPriority[*documents[j].DocumentStatus]
	})
	return documents, nil
}

func (r *EdokuEnrollmentDocumentRepo) IsCaseNumberExist(ctx *titan.Context, caseNumber string, documentId *uuid.UUID) (bool, error) {
	filter := bson.M{
		Field_CaseNumber: caseNumber,
		Field_IsDeleted:  false,
	}
	if documentId != nil {
		filter[Field_EdokuDocumentId] = bson.M{
			operator.Ne: *documentId,
		}
	}
	count, err := r.Repo.Count(ctx, filter)
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (r *EdokuEnrollmentDocumentRepo) GetEdokuDocuments(ctx *titan.Context, edokuFilter patient_overview.QuickFilter) ([]*edmp_document.EdmpEnrollmentDocumentEntity, error) {
	filter := bson.M{
		Field_IsDeleted: false,
	}

	if edokuFilter == patient_overview.EHKSPatients_NotFinishedDocument {
		filter[Field_DocumentStatus] = edmp_common.DocumentStatus_Saved
	}

	return r.Find(ctx, filter)
}

func (r *EdokuEnrollmentDocumentRepo) UpdateStatusEdokuDocumentByIds(ctx *titan.Context, edokuDocumentIds []uuid.UUID, status edmp_common.DocumentStatus) error {
	filter := bson.M{
		Field_EdokuDocumentId: bson.M{
			operator.In: edokuDocumentIds,
		},
	}
	update := bson.M{
		"$set": bson.M{
			Field_DocumentStatus: status,
		},
	}

	var entities []edmp_document.EdmpEnrollmentDocumentEntity
	_, err := r.Repo.IDBClient.UpdateMany(ctx, filter, update, &entities)
	if err != nil {
		return err
	}
	return nil
}

func (r *EdokuEnrollmentDocumentRepo) DeleteByIdWithMode(ctx *titan.Context, id uuid.UUID, hasHardDelete bool) error {
	var err error
	if !hasHardDelete {
		err = r.DeleteById(ctx, id)
	} else {
		err = r.HardDeleteById(ctx, id)
	}
	return err
}
