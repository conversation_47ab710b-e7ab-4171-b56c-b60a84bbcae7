package schein

import (
	"fmt"

	"emperror.dev/errors"
	"github.com/google/uuid"
	"github.com/spf13/cast"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"

	"git.tutum.dev/medi/tutum/ares/pkg/operator"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_copy"
	private_schein_common "git.tutum.dev/medi/tutum/ares/service/domains/api/private_schein_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	timeline_common "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
)

const (
	Field_Ad4124                 = "scheinDetail.ad4124"
	Field_Psychotherapy          = "scheinDetail.psychotherapy"
	Field_Psychotherapy_Id       = "scheinDetail.psychotherapy.id"
	Field_TsvgTranferCode        = "scheinDetail.tsvgTranferCode"
	Field_InsuranceId            = "schein.insuranceid"
	Field_PatientSnapshot        = "schein.patientsnapshot"
	Field_PrivateContractGroupId = "privateScheinDetail.privatecontractgroupid"
	Field_IkNumber               = "schein.iknumber"
	Field_Quarter                = "schein.g4101Quarter"
	Field_Year                   = "schein.g4101Year"

	Field_PrivateScheinDetail_IssueDate              = "privateScheinDetail.issuedate"
	Field_PrivateScheinDetail_PrivateContractGroupId = "privateScheinDetail.privatecontractgroupid"
	Field_PrivateScheinDetail_Discount               = "privateScheinDetail.discount"
	Field_PrivateScheinDetail_DiscountUnit           = "privateScheinDetail.discountunit"
	Field_PrivateScheinDetail_InvoiceSendingType     = "privateScheinDetail.invoicesendingtype"
	Field_PrivateScheinDetail_IsVat                  = "privateScheinDetail.isvat"

	Field_BgScheinDetail_CreatedOn               = "bgScheinDetail.createdon"
	Field_BgScheinDetail_EndDate                 = "bgScheinDetail.enddate"
	Field_BgScheinDetail_PersonalAccident        = "bgScheinDetail.personalaccident"
	Field_BgScheinDetail_AccidentDate            = "bgScheinDetail.accidentdate"
	Field_BgScheinDetail_ArrivalDate             = "bgScheinDetail.arrivaldate"
	Field_BgScheinDetail_FileNumberStr           = "bgScheinDetail.filenumberstr"
	Field_BgScheinDetail_WorkingTimeStart        = "bgScheinDetail.workingtimestart"
	Field_BgScheinDetail_WorkingTimeEnd          = "bgScheinDetail.workingtimeend"
	Field_BgScheinDetail_BgType                  = "bgScheinDetail.bgtype"
	Field_BgScheinDetail_EmploymentInfo          = "bgScheinDetail.employmentInfo"
	Field_BgScheinDetail_EmploymentInfoUpdatedAt = "bgScheinDetail.employmentInfoUpdatedAt"
)

type FilterScheinParams struct {
	PatientId    *uuid.UUID  `json:"patient_id"`
	PatientIds   []uuid.UUID `json:"patient_ids"`
	G4101Quarter *int32      `json:"g_4101_quarter"`
	G4101Year    *int32      `json:"g_4101_year"`
	MainGroup    *schein_common.MainGroup
}

type ScheinQuarters struct {
	G4101Quarter int32 `json:"g_4101_quarter"`
	G4101Year    int32 `json:"g_4101_year"`
}

type GetScheinsByPatientIdQuartersParams struct {
	PatientId uuid.UUID `json:"patient_id"`
	Quarters  []ScheinQuarters
}

type CreateDefaultScheinRequest struct {
	MainGroup          string
	PatientId          uuid.UUID
	DoctorId           uuid.UUID
	ContractId         *string
	ChargeSystemId     *string
	InsuranceId        uuid.UUID
	InsuranceCompanyId string
	YearQuarter        util.YearQuarter
	AssignedToBsnrId   *uuid.UUID
	StartDate          *int64
	EndDate            *int64
}

type KeyScheinsByQuarter struct {
	Quarter int `bson:"quarter"`
	Year    int `bson:"year"`
}

type GroupScheinsByQuarter struct {
	Key     KeyScheinsByQuarter `bson:"_id"`
	Scheins []ScheinRepo        `bson:"scheins"`
	Count   int                 `bson:"count"`
}

type PatientAndMainGroupRequest struct {
	PatientId uuid.UUID
	MainGroup string
}

type TakeOverDiagnoseInfo struct {
	Id                  uuid.UUID `json:"id"`
	IsTreatmentRelevant bool      `json:"isTreatmentRelevant"`
}

type CreatePrivateScheinRequest struct {
	Schein                private_schein_common.PrivateScheinItem `json:"schein"`
	TakeOverDiagnoseInfos []TakeOverDiagnoseInfo                  `json:"takeOverDiagnoseInfos"`
	NewTakeOverDiagnosis  []timeline_common.TimelineModel         `json:"newTakeOverDiagnosis"`
}

type CreateBgScheinRequest struct {
	Schein                schein_common.BgScheinItem      `json:"schein"`
	TakeOverDiagnoseInfos []TakeOverDiagnoseInfo          `json:"takeOverDiagnoseInfos"`
	NewTakeOverDiagnosis  []timeline_common.TimelineModel `json:"newTakeOverDiagnosis"`
}

var ScheinRepoMod = submodule.Make[ScheinRepoDefaultRepository](NewScheinRepoDefaultRepository)

func (r *ScheinRepoDefaultRepository) FindByPatient(ctx *titan.Context, patientID uuid.UUID) ([]ScheinRepo, error) {
	return r.Find(ctx, bson.M{
		Field_PatientId: patientID,
		Field_IsDeleted: false,
	})
}

func (r *ScheinRepoDefaultRepository) FindByPatientIds(ctx *titan.Context, patientIds []uuid.UUID) ([]ScheinRepo, error) {
	return r.Find(ctx, bson.M{
		Field_PatientId: bson.M{
			"$in": patientIds,
		},
		Field_IsDeleted: false,
	})
}

func (r *ScheinRepoDefaultRepository) FilterSchein(ctx *titan.Context, req FilterScheinParams) ([]ScheinRepo, error) {
	hasDataFilter := req.G4101Quarter != nil || req.G4101Year != nil || req.MainGroup != nil || req.PatientId != nil
	if !hasDataFilter {
		return nil, nil
	}
	filter := bson.M{}
	patientIds := []uuid.UUID{}
	if req.PatientId != nil {
		patientIds = append(patientIds, *req.PatientId)
	}
	if len(req.PatientIds) > 0 {
		patientIds = append(patientIds, req.PatientIds...)
	}
	if len(patientIds) > 0 {
		filter[Field_PatientId] = bson.M{
			"$in": patientIds,
		}
	}
	if util.GetPointerValue(req.G4101Quarter) != 0 && util.GetPointerValue(req.G4101Year) != 0 {
		filter["schein.g4101Quarter"] = req.G4101Quarter
		filter["schein.g4101Year"] = req.G4101Year
	}
	if req.MainGroup != nil {
		filter["schein.scheinMainGroup"] = req.MainGroup
	}
	filter[Field_IsDeleted] = false
	return r.Find(ctx, filter)
}

func (r *ScheinRepoDefaultRepository) GroupScheinsByQuarter(ctx *titan.Context, date int64, bsnrIds ...uuid.UUID) ([]GroupScheinsByQuarter, error) {
	yearQuarter := util.ToYearQuarter(date)
	last4YearQuarters := yearQuarter.Last4YearQuarters()
	matchState := bson.M{
		"$match": bson.M{
			Field_IsDeleted:          false,
			Field_IsBilled:           false,
			Field_ExcludeFromBilling: false,
			fmt.Sprintf("%s.%s", Field_Schein, "scheinMainGroup"): schein_common.KV,
			"$or": function.Do(func() []primitive.M {
				orQueries := []bson.M{}
				for _, v := range last4YearQuarters {
					orQueries = append(orQueries, bson.M{
						"schein.g4101Quarter": v.Quarter,
						"schein.g4101Year":    v.Year,
					})
				}
				return orQueries
			}),
		},
	}
	if len(bsnrIds) > 0 {
		matchState["$match"].(bson.M)[Field_AssignedToBsnrId] = bson.M{
			operator.In: bsnrIds,
		}
	}
	groupState := bson.M{
		"$group": bson.M{
			"_id": bson.M{
				"year":    "$schein.g4101Year",
				"quarter": "$schein.g4101Quarter",
			},
			"scheins": bson.M{
				"$push": "$$ROOT",
			},
			"count": bson.M{
				"$sum": 1,
			},
		},
	}

	sortState := bson.M{
		"$sort": bson.D{
			{
				Key:   "_id.year",
				Value: -1,
			},
			{
				Key:   "_id.quarter",
				Value: -1,
			},
		},
	}

	states := []bson.M{
		matchState,
		groupState,
		sortState,
	}

	var result []GroupScheinsByQuarter
	err := r.Db.Aggregate(ctx, states, &result)
	return result, err
}

func (r *ScheinRepoDefaultRepository) GetAllScheinIDsByMainGroup(ctx *titan.Context, mainGroups []schein_common.MainGroup) ([]timeline_repo.ScheinIdsWithBillCheck, error) {
	filter := bson.M{
		Field_IsDeleted: false,
		"schein.scheinMainGroup": bson.M{
			"$in": mainGroups,
		},
	}

	rs, err := r.Find(ctx, filter, options.Find().SetProjection(bson.M{
		Field_Id:       1,
		Field_IsBilled: 1,
	}))

	var scheinIds []timeline_repo.ScheinIdsWithBillCheck
	for _, v := range rs {
		if v.Id != nil {
			scheinIds = append(scheinIds, timeline_repo.ScheinIdsWithBillCheck{
				Id:       *v.Id,
				IsBilled: v.IsBilled,
			})
		}
	}

	return scheinIds, err
}

type MarkBilledByScheinIdsRequest struct {
	ContractId       *string
	MainGroup        schein_common.MainGroup
	ExcludeScheinIds []uuid.UUID
	ValidScheinsIds  []uuid.UUID
}

func (r *ScheinRepoDefaultRepository) MarkBilledByScheinIds(ctx *titan.Context, req MarkBilledByScheinIdsRequest) ([]ScheinRepo, error) {
	if len(req.ValidScheinsIds) == 0 {
		return []ScheinRepo{}, nil
	}
	filter := bson.M{
		"schein.scheinMainGroup": req.MainGroup,
		Field_ExcludeFromBilling: false,
		Field_IsDeleted:          false,
		Field_IsBilled:           false,
	}

	if len(req.ValidScheinsIds) > 0 {
		filter[Field_Id] = bson.M{
			"$in": req.ValidScheinsIds,
		}
	}
	if len(req.ExcludeScheinIds) > 0 {
		filter[Field_Id] = bson.M{
			"$nin": req.ExcludeScheinIds,
		}
	}
	if req.ContractId != nil {
		filter[Field_ContractId] = *req.ContractId
	}

	var updated []ScheinRepo

	_, err := r.Db.UpdateMany(
		ctx,
		filter,
		[]bson.M{
			{
				"$set": bson.M{
					Field_IsBilled:  true,
					Field_UpdatedAt: util.NewPointer(util.NowUnixMillis(ctx)),
					Field_UpdatedBy: ctx.UserInfo().UserUUID(),
				},
			},
		},
		&updated,
	)
	if err != nil {
		return nil, err
	}

	return updated, nil
}

func (r *ScheinRepoDefaultRepository) MarkBilled(
	ctx *titan.Context,
	patientIds []uuid.UUID,
	contractId *string,
	year, quarter int32,
	mainGroup schein_common.MainGroup,
) error {
	filter := bson.M{
		Field_IsDeleted:          false,
		"schein.g4101Quarter":    quarter,
		"schein.g4101Year":       year,
		"schein.scheinMainGroup": mainGroup,
		Field_PatientId: bson.M{
			"$in": patientIds,
		},
	}
	if contractId != nil {
		filter[Field_ContractId] = *contractId
	}
	scheinRs, err := r.Find(ctx, filter)
	if err != nil {
		return err
	}

	billedSchein := slice.Map(scheinRs, func(s ScheinRepo) ScheinRepo {
		s.IsBilled = true
		s.UpdatedBy = ctx.UserInfo().UserUUID()
		s.UpdatedAt = util.NewPointer(util.NowUnixMillis(ctx))
		return s
	})

	_, err = r.UpdateMany(ctx, billedSchein)

	return err
}

func (r *ScheinRepoDefaultRepository) MarkBilledByIds(
	ctx *titan.Context,
	scheinIds []uuid.UUID,
) error {
	filter := bson.M{
		Field_IsDeleted: false,
		Field_Id: bson.M{
			"$in": scheinIds,
		},
	}
	scheinRs, err := r.Find(ctx, filter)
	if err != nil {
		return err
	}

	billedSchein := slice.Map(scheinRs, func(s ScheinRepo) ScheinRepo {
		s.IsBilled = true
		s.IsBilledFaulty = false
		s.UpdatedBy = ctx.UserInfo().UserUUID()
		s.UpdatedAt = util.NewPointer(util.NowUnixMillis(ctx))
		return s
	})

	_, err = r.UpdateMany(ctx, billedSchein)

	return err
}

func (r *ScheinRepoDefaultRepository) MarkBilledFailByIds(
	ctx *titan.Context,
	scheinIds []uuid.UUID,
) error {
	filter := bson.M{
		Field_IsDeleted: false,
		Field_Id: bson.M{
			"$in": scheinIds,
		},
	}
	scheinRs, err := r.Find(ctx, filter)
	if err != nil {
		return err
	}

	billedSchein := slice.Map(scheinRs, func(s ScheinRepo) ScheinRepo {
		s.IsBilledFaulty = true
		s.UpdatedBy = ctx.UserInfo().UserUUID()
		s.UpdatedAt = util.NewPointer(util.NowUnixMillis(ctx))
		return s
	})

	_, err = r.UpdateMany(ctx, billedSchein)

	return err
}

func (r *ScheinRepoDefaultRepository) GetLastScheinsByPatientAndMainGroup(ctx *titan.Context, req PatientAndMainGroupRequest) (*ScheinRepo, error) {
	return r.FindOne(ctx, bson.M{
		Field_PatientId:          req.PatientId,
		"schein.scheinMainGroup": req.MainGroup,
		Field_IsDeleted:          false,
	}, options.FindOne().SetSort(bson.M{
		Field_CreatedAt: -1,
	}))
}

func (r *ScheinRepoDefaultRepository) GetFirstFaultySchein(ctx *titan.Context) (*ScheinRepo, error) {
	foundSchein, err := r.FindOne(ctx, bson.M{
		Field_ContractId:     bson.M{operator.Exists: true},
		Field_IsBilledFaulty: true,
	})
	if err != nil {
		return nil, err
	}
	return foundSchein, nil
}

func (r *ScheinRepoDefaultRepository) CheckExistKVScheinCurrentQuarter(ctx *titan.Context, req FilterScheinParams) (bool, error) {
	count, err := r.Count(ctx, bson.M{
		Field_PatientId:          req.PatientId,
		"schein.g4101Quarter":    req.G4101Quarter,
		"schein.g4101Year":       req.G4101Year,
		"schein.scheinMainGroup": schein_common.KV,
		Field_ContractId:         nil,
		Field_IsDeleted:          false,
	})
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (r *ScheinRepoDefaultRepository) CreateDefaultSchein(ctx *titan.Context, reqs ...CreateDefaultScheinRequest) (*[]ScheinRepo, error) {
	if len(reqs) == 0 {
		return nil, nil
	}
	createdAt := util.NowUnixMillis(ctx)
	createdBy := util.GetPointerValue(ctx.UserInfo().UserUUID())

	newScheins := slice.Map(reqs, func(t CreateDefaultScheinRequest) ScheinRepo {
		newId := uuid.New()
		newSchein := ScheinRepo{
			Id:             &newId,
			DoctorId:       t.DoctorId,
			PatientId:      t.PatientId,
			ContractId:     t.ContractId,
			ChargeSystemId: t.ChargeSystemId,
			Schein: schein_common.Schein{
				G4101Quarter:    &t.YearQuarter.Quarter,
				G4101Year:       &t.YearQuarter.Year,
				ScheinMainGroup: t.MainGroup,
				InsuranceId:     t.InsuranceId,
			},
			CreatedBy:        createdBy,
			CreatedAt:        createdAt,
			IsDeleted:        false,
			AssignedToBsnrId: t.AssignedToBsnrId,
		}

		if t.MainGroup == string(schein_common.KV) {
			newSchein.Schein.KvScheinSubGroup = util.NewString("00")
			newSchein.Schein.KvTreatmentCase = *util.NewString("0101")
			newSchein.ScheinDetail.G4122 = util.NewString("00")                                // ABRECHNUNGSGEBIET
			newSchein.ScheinDetail.G4106 = util.NewString("00")                                // KTAB
			newSchein.ScheinDetail.G4104 = util.NewPointer(cast.ToInt32(t.InsuranceCompanyId)) // g4104 -
		} else if t.MainGroup == string(schein_common.HZV) || t.MainGroup == string(schein_common.FAV) {
			now := util.Now(ctx)
			beginQuarter := util.BeginningOfQuarter(now).UnixMilli()
			newSchein.SvScheinDetail = &schein_common.SvScheinDetail{
				OnlineParticipatedCheckDate: util.NewPointer(createdAt),
				StartDate:                   function.If(t.StartDate != nil, t.StartDate, &beginQuarter),
				EndDate:                     t.EndDate,
			}
		}
		return newSchein
	})

	scheins, err := r.CreateMany(ctx, newScheins)
	if err != nil {
		return nil, err
	}
	return &scheins, nil
}

func (r *ScheinRepoDefaultRepository) FindByIds(ctx *titan.Context, ids []uuid.UUID) ([]ScheinRepo, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	filterQuery := bson.D{{
		Key: Field_Id,
		Value: bson.M{
			"$in": ids,
		},
	}}
	scheins, err := r.Find(ctx, filterQuery)
	if err != nil {
		return nil, err
	}
	return scheins, nil
}

func (r *ScheinRepoDefaultRepository) GetScheinsNotBill(ctx *titan.Context, times []util.YearQuarter, doctorIds []uuid.UUID, contractIds []string) ([]ScheinRepo, error) {
	queries := []bson.M{}
	for _, t := range times {
		for _, d := range doctorIds {
			for _, c := range contractIds {
				queries = append(queries, bson.M{
					Field_Quarter:    t.Quarter,
					Field_Year:       t.Year,
					Field_DoctorId:   d,
					Field_ContractId: c,
				})
			}
		}
	}
	return r.Find(ctx, bson.M{
		"$or":           queries,
		Field_IsBilled:  false,
		Field_IsDeleted: false,
	})
}

type PatientIdWithFieldValue struct {
	PatientId  uuid.UUID
	FieldValue string
}

func (r *ScheinRepoDefaultRepository) GetPatientIdsByFieldName(ctx *titan.Context, fieldName string, fieldValue any) ([]PatientIdWithFieldValue, error) {
	filter := bson.M{
		fieldName: bson.M{
			"$regex":   fieldValue,
			"$options": "i",
		},
		Field_IsDeleted: false,
	}
	patientIds, err := r.Find(ctx, filter, options.Find())
	if err != nil {
		return nil, err
	}

	if len(patientIds) == 0 {
		return nil, nil
	}

	return slice.Map(patientIds, func(s ScheinRepo) PatientIdWithFieldValue {
		return PatientIdWithFieldValue{
			PatientId:  s.PatientId,
			FieldValue: util.GetPointerValue(s.ScheinDetail.Ad4124),
		}
	}), nil
}

func (r *ScheinRepoDefaultRepository) RemovePyschotherapy(ctx *titan.Context, scheinId uuid.UUID) error {
	_, err := r.FindOneAndUpdate(ctx, bson.M{
		Field_Id: scheinId,
	}, bson.M{
		"$set": bson.M{
			"scheinDetail.ps4244":                     []string{},
			"scheinDetail.isInsuranceInformedTherapy": nil,
			"scheinDetail.ps4250":                     nil,
			"scheinDetail.ps4251":                     nil,
			"scheinDetail.ps4253":                     []string{},
			"scheinDetail.ps4256":                     []string{},
			"scheinDetail.ps4235":                     nil,
			"scheinDetail.ps4299":                     nil,
			"scheinDetail.ps4247":                     nil,
			"scheinDetail.ps4245":                     nil,
			"scheinDetail.ps4246":                     nil,
			"scheinDetail.pausingStartDate":           nil,
			"scheinDetail.pausingEndDate":             nil,
			"scheinDetail.ps4254":                     nil,
			"scheinDetail.ps4252":                     nil,
			"scheinDetail.ps4257":                     nil,
			"scheinDetail.ps4255":                     nil,
			"scheinDetail.ps4234":                     false,
		},
	})
	return err
}

func (r *ScheinRepoDefaultRepository) RemovePsychotherapyById(ctx *titan.Context, psychotherapyId uuid.UUID) error {
	schein, err := r.FindOne(ctx, bson.M{
		Field_Psychotherapy_Id: psychotherapyId,
	})
	if err != nil {
		return err
	}
	if schein == nil {
		return nil
	}
	psychotherapies := schein.ScheinDetail.Psychotherapy
	schein.ScheinDetail.Psychotherapy = slice.Filter(psychotherapies, func(t schein_common.Psychotherapy) bool {
		return t.Id.String() != psychotherapyId.String()
	})
	if len(schein.ScheinDetail.Psychotherapy) == 0 {
		schein.ScheinDetail.Psychotherapy = []schein_common.Psychotherapy{}
		schein.ScheinDetail.Ps4234 = util.NewBool(false)
	}
	_, err = r.Update(ctx, *schein)
	return err
}

func (r *ScheinRepoDefaultRepository) FindTssByCode(ctx *titan.Context, code string) (*ScheinRepo, error) {
	filter := bson.M{
		Field_IsDeleted:       false,
		Field_TsvgTranferCode: code,
	}
	scheinRs, err := r.FindOne(ctx, filter)
	if err != nil {
		return nil, err
	}
	return scheinRs, nil
}

func (s ScheinRepo) GetYearQuarter() util.YearQuarter {
	return util.YearQuarter{
		Year:    *s.Schein.G4101Year,
		Quarter: *s.Schein.G4101Quarter,
	}
}

func (s *ScheinRepoDefaultRepository) GetScheinsByIdsAndPatientId(ctx *titan.Context, ids []uuid.UUID, patientId uuid.UUID) ([]ScheinRepo, error) {
	return s.Find(ctx, bson.M{
		Field_Id: bson.M{
			"$in": ids,
		},
		Field_PatientId: patientId,
	})
}

func (r ScheinRepoDefaultRepository) GetScheinByInsuranceId(ctx *titan.Context, insuranceId uuid.UUID) (*ScheinRepo, error) {
	return r.FindOne(ctx, bson.M{
		Field_InsuranceId: insuranceId,
		Field_IsDeleted:   false,
	})
}

func (r ScheinRepoDefaultRepository) GetScheinsByInsuranceId(ctx *titan.Context, insuranceId uuid.UUID) ([]ScheinRepo, error) {
	return r.Find(ctx, bson.M{
		Field_InsuranceId: insuranceId,
		Field_IsDeleted:   false,
	})
}

func (r ScheinRepoDefaultRepository) GetSvScheinsByDoctorIds(ctx *titan.Context, doctorIds []uuid.UUID, times []util.YearQuarter, contractIds []string) ([]ScheinRepo, error) {
	queries := []bson.M{}
	for _, t := range times {
		queries = append(queries, bson.M{
			"schein.g4101Quarter": t.Quarter,
			"schein.g4101Year":    t.Year,
			Field_DoctorId: bson.M{
				operator.In: doctorIds,
			},
			Field_IsDeleted:  false,
			Field_ContractId: bson.M{operator.In: contractIds},
		})
	}
	filtered := bson.D{{
		Key:   operator.Or,
		Value: queries,
	}}
	return r.Find(ctx, filtered)
}

func (r ScheinRepoDefaultRepository) GetScheinsByIkNumber(ctx *titan.Context, IkNumber int32) ([]ScheinRepo, error) {
	return r.Find(ctx, bson.M{
		Field_IkNumber:  IkNumber,
		Field_IsDeleted: false,
	})
}

func (r ScheinRepoDefaultRepository) DeletedById(ctx *titan.Context, id uuid.UUID) (*ScheinRepo, error) {
	return r.FindOneAndUpdate(ctx, bson.M{
		Field_Id: id,
	}, bson.M{
		"$set": bson.M{
			Field_IsDeleted: true,
		},
	})
}

// private schein
func (r ScheinRepoDefaultRepository) CreatePrivateSchein(ctx *titan.Context, req CreatePrivateScheinRequest) (*ScheinRepo, error) {
	// schein property
	schein := pkg_copy.CloneTo[schein_common.Schein](req.Schein)
	yearQuarter := util.ToYearQuarter(req.Schein.IssueDate)
	schein.G4101Quarter = util.NewPointer[int32](yearQuarter.Quarter)
	schein.G4101Year = util.NewPointer[int32](yearQuarter.Year)
	// private schein detail property
	privateScheinDetail := pkg_copy.CloneTo[private_schein_common.PrivateScheinDetail](req.Schein)
	// schein repo
	scheinRepo := ScheinRepo{
		Id:                  util.NewUUID(),
		DoctorId:            req.Schein.DoctorId,
		PatientId:           req.Schein.PatientId,
		Schein:              schein,
		PrivateScheinDetail: privateScheinDetail,
		ExcludeFromBilling:  req.Schein.ExcludeFromBilling,
		CreatedAt:           util.NowUnixMillis(ctx),
		CreatedBy:           util.GetPointerValue(ctx.UserInfo().UserUUID()),
		AssignedToBsnrId:    function.If(req.Schein.AssignedToBsnrId != nil, req.Schein.AssignedToBsnrId, ctx.UserInfo().GetBsnrId()),
	}
	return r.Create(ctx, scheinRepo)
}

func (r ScheinRepoDefaultRepository) UpdatePrivateSchein(ctx *titan.Context, req private_schein_common.PrivateScheinItem) (*ScheinRepo, error) {
	schein := pkg_copy.CloneTo[schein_common.Schein](req)
	yearQuarter := util.ToYearQuarter(req.IssueDate)
	schein.G4101Quarter = util.NewPointer[int32](yearQuarter.Quarter)
	schein.G4101Year = util.NewPointer[int32](yearQuarter.Year)
	filter := bson.M{
		Field_Id: req.ScheinId,
	}
	updatedValue := bson.M{
		Field_Schein:             schein,
		Field_UpdatedBy:          ctx.UserInfo().UserUUID(),
		Field_UpdatedAt:          util.NewPointer(util.NowUnixMillis(ctx)),
		Field_DoctorId:           req.DoctorId,
		Field_ExcludeFromBilling: req.ExcludeFromBilling,

		Field_PrivateScheinDetail_IssueDate:              req.IssueDate,
		Field_PrivateScheinDetail_PrivateContractGroupId: req.PrivateContractGroupId,
		Field_PrivateScheinDetail_IsVat:                  req.IsVat,
		Field_PrivateScheinDetail_InvoiceSendingType:     req.InvoiceSendingType,
		Field_PrivateScheinDetail_Discount:               req.Discount,
		Field_PrivateScheinDetail_DiscountUnit:           req.DiscountUnit,
	}
	if req.AssignedToBsnrId != nil {
		updatedValue[Field_AssignedToBsnrId] = req.AssignedToBsnrId
	}
	update := bson.M{
		"$set": updatedValue,
	}

	return r.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (r ScheinRepoDefaultRepository) DeletePrivateSchein(ctx *titan.Context, scheinId uuid.UUID) (*ScheinRepo, error) {
	filter := bson.M{
		Field_Id: scheinId,
	}
	update := bson.M{
		"$set": bson.M{
			Field_IsDeleted: true,
		},
	}
	return r.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

// bg schein
func (r ScheinRepoDefaultRepository) CreateBgSchein(ctx *titan.Context, req CreateBgScheinRequest) (*ScheinRepo, error) {
	schein := pkg_copy.CloneTo[schein_common.Schein](req.Schein)
	schein.ScheinMainGroup = string(schein_common.BG)
	yearQuarter := util.ToYearQuarter(req.Schein.CreatedOn)
	schein.G4101Quarter = util.NewPointer(yearQuarter.Quarter)
	schein.G4101Year = util.NewPointer(yearQuarter.Year)
	BgScheinDetail := pkg_copy.CloneTo[schein_common.BgScheinDetail](req.Schein)
	scheinRepo := ScheinRepo{
		Id:                 util.NewUUID(),
		DoctorId:           req.Schein.DoctorId,
		PatientId:          req.Schein.PatientId,
		Schein:             schein,
		BgScheinDetail:     BgScheinDetail,
		ExcludeFromBilling: req.Schein.ExcludeFromBilling,
		CreatedAt:          util.NowUnixMillis(ctx),
		CreatedBy:          util.GetPointerValue(ctx.UserInfo().UserUUID()),
		AssignedToBsnrId:   function.If(req.Schein.AssignedToBsnrId != nil, req.Schein.AssignedToBsnrId, ctx.UserInfo().GetBsnrId()),
	}
	return r.Create(ctx, scheinRepo)
}

func (r ScheinRepoDefaultRepository) UpdateBgSchein(ctx *titan.Context, req schein_common.BgScheinItem) (*ScheinRepo, error) {
	schein := pkg_copy.CloneTo[schein_common.Schein](req)
	yearQuarter := util.ToYearQuarter(req.CreatedOn)
	schein.G4101Quarter = util.NewPointer[int32](yearQuarter.Quarter)
	schein.G4101Year = util.NewPointer[int32](yearQuarter.Year)
	filter := bson.M{
		Field_Id: req.ScheinId,
	}
	updatedValue := bson.M{
		Field_Schein:                                 schein,
		Field_UpdatedBy:                              ctx.UserInfo().UserUUID(),
		Field_UpdatedAt:                              util.NewPointer(util.NowUnixMillis(ctx)),
		Field_DoctorId:                               req.DoctorId,
		Field_ExcludeFromBilling:                     req.ExcludeFromBilling,
		Field_BgScheinDetail_CreatedOn:               req.CreatedOn,
		Field_BgScheinDetail_EndDate:                 req.EndDate,
		Field_BgScheinDetail_PersonalAccident:        req.PersonalAccident,
		Field_BgScheinDetail_AccidentDate:            req.AccidentDate,
		Field_BgScheinDetail_ArrivalDate:             req.ArrivalDate,
		Field_BgScheinDetail_FileNumberStr:           req.FileNumberStr,
		Field_BgScheinDetail_WorkingTimeStart:        req.WorkingTimeStart,
		Field_BgScheinDetail_WorkingTimeEnd:          req.WorkingTimeEnd,
		Field_BgScheinDetail_BgType:                  req.BGType,
		Field_BgScheinDetail_EmploymentInfo:          req.EmploymentInfo,
		Field_BgScheinDetail_EmploymentInfoUpdatedAt: req.EmploymentInfoUpdatedAt,
	}
	if req.AssignedToBsnrId != nil {
		updatedValue[Field_AssignedToBsnrId] = req.AssignedToBsnrId
	}
	update := bson.M{
		"$set": updatedValue,
	}

	return r.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (r ScheinRepoDefaultRepository) DeleteBgSchein(ctx *titan.Context, scheinId uuid.UUID) (*ScheinRepo, error) {
	filter := bson.M{
		Field_Id: scheinId,
	}
	update := bson.M{
		"$set": bson.M{
			Field_IsDeleted: true,
		},
	}
	return r.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (r ScheinRepoDefaultRepository) UpdatePrivateScheinStatus(ctx *titan.Context, scheinId uuid.UUID, status schein_common.ScheinStatus) (*ScheinRepo, error) {
	filter := bson.M{
		Field_Id: scheinId,
	}
	data := bson.M{
		Field_ScheinStatus: status,
		Field_UpdatedBy:    ctx.UserInfo().UserUUID(),
		Field_UpdatedAt:    util.NewPointer(util.NowUnixMillis(ctx)),
	}
	update := bson.M{
		"$set": data,
	}
	return r.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (r ScheinRepoDefaultRepository) UpdateBgScheinStatus(ctx *titan.Context, scheinId uuid.UUID, status schein_common.ScheinStatus) (*ScheinRepo, error) {
	filter := bson.M{
		Field_Id: scheinId,
	}
	data := bson.M{
		Field_ScheinStatus: status,
		Field_UpdatedBy:    ctx.UserInfo().UserUUID(),
		Field_UpdatedAt:    util.NewPointer(util.NowUnixMillis(ctx)),
	}
	update := bson.M{
		"$set": data,
	}
	return r.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (schein *ScheinRepo) ToPrivateScheinItem() *private_schein_common.PrivateScheinItem {
	privateScheinItem := pkg_copy.CloneTo[private_schein_common.PrivateScheinItem](schein.PrivateScheinDetail)
	privateScheinItem.ScheinId = *schein.Id
	privateScheinItem.PatientId = schein.PatientId
	privateScheinItem.DoctorId = schein.DoctorId
	privateScheinItem.InsuranceId = util.NewPointer(schein.Schein.InsuranceId)
	privateScheinItem.ExcludeFromBilling = schein.ExcludeFromBilling
	privateScheinItem.ScheinMainGroup = schein_common.MainGroup(schein.Schein.ScheinMainGroup)
	privateScheinItem.MarkedAsBilled = schein.IsBilled
	privateScheinItem.ScheinStatus = util.NewPointer(schein.ScheinStatus)
	privateScheinItem.IsVat = schein.PrivateScheinDetail.IsVat
	privateScheinItem.IkNumber = schein.Schein.IkNumber
	privateScheinItem.AssignedToBsnrId = schein.AssignedToBsnrId
	return &privateScheinItem
}

func (schein *ScheinRepo) ToBgScheinItem() *schein_common.BgScheinItem {
	ScheinItem := pkg_copy.CloneTo[schein_common.BgScheinItem](schein.BgScheinDetail)
	ScheinItem.ScheinId = *schein.Id
	ScheinItem.PatientId = schein.PatientId
	ScheinItem.DoctorId = schein.DoctorId
	ScheinItem.InsuranceId = util.NewPointer(schein.Schein.InsuranceId)
	ScheinItem.ExcludeFromBilling = schein.ExcludeFromBilling
	ScheinItem.ScheinMainGroup = schein_common.MainGroup(schein.Schein.ScheinMainGroup)
	ScheinItem.MarkedAsBilled = schein.IsBilled
	ScheinItem.ScheinStatus = util.NewPointer(schein.ScheinStatus)
	ScheinItem.BGType = schein_common.BGType(schein.BgScheinDetail.BGType)
	ScheinItem.AssignedToBsnrId = schein.AssignedToBsnrId
	ScheinItem.EmploymentInfo = schein.BgScheinDetail.EmploymentInfo
	ScheinItem.IkNumber = schein.Schein.IkNumber
	return &ScheinItem
}

func (schein *ScheinRepo) IsPrivateSchein() bool {
	return schein.Schein.ScheinMainGroup == string(schein_common.PRIVATE) || schein.Schein.ScheinMainGroup == string(schein_common.IGEL)
}

func (schein *ScheinRepo) IsBgSchein() bool {
	return schein.Schein.ScheinMainGroup == string(schein_common.BG)
}

func (schein *ScheinRepo) IsSvSchein() bool {
	return schein.Schein.ScheinMainGroup == string(schein_common.FAV) || schein.Schein.ScheinMainGroup == string(schein_common.HZV)
}

func (s *ScheinRepo) IsAd4125OutOfTime(time int64) bool {
	if s == nil || s.ScheinDetail.Ad4125From == nil || s.ScheinDetail.Ad4125To == nil {
		return false
	}

	return !(*s.ScheinDetail.Ad4125From <= time && time <= *s.ScheinDetail.Ad4125To)
}

func (schein *ScheinRepoDefaultRepository) UpdateMultiplePsychotherapy(ctx *titan.Context, psychotherapies []schein_common.Psychotherapy) error {
	for _, p := range psychotherapies {
		scheinRecord, err := schein.FindOne(ctx, bson.M{
			Field_Psychotherapy_Id: *p.Id,
		})
		if err != nil {
			return errors.WithMessage(err, "find schein by psychotherapy id")
		}
		if scheinRecord == nil {
			continue
		}
		scheinRecord.ScheinDetail.Psychotherapy = slice.Map(scheinRecord.ScheinDetail.Psychotherapy, func(t schein_common.Psychotherapy) schein_common.Psychotherapy {
			if t.Id.String() == p.Id.String() {
				return p
			}
			return t
		})
		_, err = schein.Update(ctx, *scheinRecord)
		if err != nil {
			return err
		}
	}
	return nil
}

func (r *ScheinRepoDefaultRepository) GetScheinsByPatientIdQuarters(ctx *titan.Context, req GetScheinsByPatientIdQuartersParams) ([]ScheinRepo, error) {
	if len(req.Quarters) == 0 {
		return []ScheinRepo{}, nil
	}
	filter := bson.M{
		Field_PatientId: req.PatientId,
		"$or": slice.Map(req.Quarters, func(q ScheinQuarters) bson.M {
			return bson.M{
				"schein.g4101Quarter": q.G4101Quarter,
				"schein.g4101Year":    q.G4101Year,
			}
		}),
		Field_IsDeleted: false,
	}
	return r.Find(ctx, filter)
}

func (r ScheinRepoDefaultRepository) GetScheinByPrivateContractGroupId(ctx *titan.Context, privaiteContractGroupId uuid.UUID) (*ScheinRepo, error) {
	return r.FindOne(ctx, bson.M{
		Field_PrivateContractGroupId: privaiteContractGroupId,
		Field_IsDeleted:              false,
	})
}

func (r ScheinRepoDefaultRepository) DeleteScheinById(ctx *titan.Context, id uuid.UUID) error {
	_, err := r.FindOneAndUpdate(ctx, bson.D{
		{
			Key:   Field_Id,
			Value: id,
		},
	}, bson.M{
		"$set": bson.M{
			Field_IsDeleted: true,
		},
	})
	if err != nil {
		return err
	}
	return nil
}

func (r ScheinRepoDefaultRepository) GetScheinInQuarterBySelectedDate(ctx *titan.Context, selectedDate int64, patientId uuid.UUID) ([]ScheinRepo, error) {
	yearQuarter := util.ToYearQuarter(selectedDate)
	return r.Find(ctx, bson.M{
		Field_PatientId: patientId,
		Field_IsDeleted: false,
		Field_Quarter:   yearQuarter.Quarter,
		Field_Year:      yearQuarter.Year,
	})
}

func (schein ScheinRepo) ToScheinDetail() schein_common.GetScheinDetailByIdResponse {
	return schein_common.GetScheinDetailByIdResponse{
		PatientId:             schein.PatientId,
		DoctorId:              schein.DoctorId,
		ScheinMainGroup:       schein_common.MainGroup(schein.Schein.ScheinMainGroup),
		KvTreatmentCase:       schein_common.TreatmentCaseNames(schein.Schein.KvTreatmentCase),
		KvScheinSubGroup:      schein.Schein.KvScheinSubGroup,
		G4101Year:             schein.Schein.G4101Year,
		G4101Quarter:          schein.Schein.G4101Quarter,
		TariffType:            schein.Schein.TariffType,
		BgType:                schein.Schein.BgType,
		BgAccidentDate:        schein.Schein.BgAccidentDate,
		BgAccidentTime:        schein.Schein.BgAccidentTime,
		BgWorkingTimeFrom:     schein.Schein.BgWorkingTimeFrom,
		BgWorkingTimeTo:       schein.Schein.BgWorkingTimeTo,
		BgEmployerName:        schein.Schein.BgEmployerName,
		BgEmployerStreet:      schein.Schein.BgEmployerStreet,
		BgEmployerHousenumber: schein.Schein.BgEmployerHousenumber,
		BgEmployerPostcode:    schein.Schein.BgEmployerPostcode,
		BgEmployerCity:        schein.Schein.BgEmployerCity,
		BgEmployerCountry:     schein.Schein.BgEmployerCountry,
		HzvContractId:         schein.ContractId,
		ScheinDetails:         &schein.ScheinDetail,
		ScheinId:              *schein.Id,
		InsuranceId:           schein.Schein.InsuranceId,
		ChargeSystemId:        schein.ChargeSystemId,
		SvScheinDetail:        schein.SvScheinDetail,
		AssignedToBsnrId:      schein.AssignedToBsnrId,
		ExcludeFromBilling:    schein.ExcludeFromBilling,
		MarkedAsBilled:        schein.IsBilled,
	}
}
