package himi

import (
	"fmt"
	"reflect"
	"regexp"
	"sort"
	"strconv"
	"strings"

	"git.tutum.dev/medi/tutum/ares/pkg/mmi"
	"git.tutum.dev/medi/tutum/ares/service/contract/contract"
	"git.tutum.dev/medi/tutum/ares/service/contract/contract/model"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/himi/himi_model"
	util "git.tutum.dev/medi/tutum/pkg/util"
	"github.com/pkg/errors"
	"gitlab.com/silenteer-oss/titan"
	"go.opentelemetry.io/otel"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/plugin/opentelemetry/tracing"
)

const (
	queryOrtByGruppeStatement = `
	select distinct oq.ort_id, oq.bezeichnung as ort_bezeichnung, gq.gruppe_id , gq.bezeichnung as gruppe_bezeichnung
	from art_%[1]s aq inner join ort_%[1]s oq on aq.ort = oq.ort_id 
						inner join gruppe_%[1]s gq on gq.gruppe_id = aq.gruppe 
	where aq.gruppe = ? order by oq.ort_id;`
	searchUnterByGruppeOrtStatement = `
	select distinct gq.gruppe_id,
					gq.bezeichnung as gruppe_bezeichnung, 
					oq.ort_id, 
					oq.bezeichnung as ort_bezeichnung, 
					uq.unter, 
					uq.bezeichnung as unter_bezeichnung,
					uq.id
	from unter_%[1]s uq inner join gruppe_%[1]s gq on gq.gruppe_id = uq.gruppe 
							inner join ort_%[1]s oq on oq.ort_id = uq.ort 
	where uq.gruppe = ? and uq.ort = ?;
	`
	searchArtFreeTextStatement = `
		SELECT DISTINCT a.gruppe, a.ort, a.unter, a.art_id, a.bezeichnung
		FROM art_%[1]s as a
		WHERE to_tsvector(a.bezeichnung) @@ to_tsquery(?)
		UNION
		SELECT DISTINCT a.gruppe, a.ort, a.unter, a.art_id, a.bezeichnung
		FROM art_%[1]s as a
		LEFT JOIN thesaurus_%[1]s as t 
		ON t.gruppe = a.gruppe 
		AND t.ort = a.ort 
		AND t.unter = a.unter 
		AND t.art_id = a.art_id
		WHERE to_tsvector(t.schlagwort) @@ to_tsquery(?)
		UNION
		SELECT DISTINCT a.gruppe, a.ort, a.unter, a.art_id, a.bezeichnung
		FROM art_%[1]s as a
		LEFT JOIN produkt_%[1]s as p 
		ON p.gruppe = a.gruppe 
		AND p.ort = a.ort 
		AND p.unter = a.unter 
		AND p.art_id = a.art_id
		WHERE to_tsvector(p.search) @@ to_tsquery(?)
	`
	searchArtByThesaurusStatement = `
		SELECT DISTINCT a.gruppe, a.ort, a.unter, a.art_id, a.bezeichnung
		FROM art_%[1]s as a
		LEFT JOIN thesaurus_%[1]s as t 
		ON t.gruppe = a.gruppe 
		AND t.ort = a.ort 
		AND t.unter = a.unter 
		AND t.art_id = a.art_id
		WHERE t.schlagwort ILIKE ?
	`
	SearchProductByBaseAndArtStatement = `
	select 	gq.gruppe_id, 
			gq.bezeichnung as gruppe_bezeichnung, 
			oq.ort_id, 
			oq.bezeichnung as ort_bezeichnung,
			uq.id as unter_id,
			uq.unter, 
			uq.bezeichnung as unter_bezeichnung,
			aq.art_id as art,
			aq.id  as art_id,
			aq.bezeichnung as art_bezeichnung,
			pq.bezeichnung,
			pq.hersteller,
			pq.produkt,
			pq.id as product_id,
			pq.search
	from produkt_%[1]s pq inner join gruppe_%[1]s gq on gq.gruppe_id = pq.gruppe 
						inner join ort_%[1]s oq on oq.ort_id = pq.ort 
						inner join unter_%[1]s uq on uq.unter = pq.unter 
						inner join art_%[1]s aq on aq.art_id = pq.art_id 
	where 	pq.gruppe = @gruppe and 
			gq.gruppe_id = @gruppe and 
			uq.gruppe = @gruppe and
			aq.gruppe = @gruppe and
			oq.ort_id = @ort and 
			uq.ort = @ort and 
			aq.ort = @ort and
			uq.unter = @unter and
			aq.unter = @unter and 
			aq.art_id = @art
	`
	queryConcatProductNumber = `(CAST(gruppe AS TEXT) || CAST(ort AS TEXT) || CAST(unter AS TEXT) || CAST(art_id AS TEXT)) LIKE ?`
)

var himiComplianceIds = []string{
	"VSST626",
	"VSST627",
	"VSST628",
	"VSST629",
	"VSST630",
	"VSST631",
	"FORM632",
}

type HimiRepo struct {
	db              *gorm.DB
	mmiService      mmi.PharmindexProService
	mmiLicenseKey   string
	mmiUsername     string
	contractService *contract.Service
}

var spaceRegex = regexp.MustCompile(`\s+`)

// standardizedData replace space to & for full text search
func standardizedData(in string) string {
	return spaceRegex.ReplaceAllString(in, "&")
}

func NewHimirepo(
	dsn, mmiLicenseKey, mmiUsername string,
	mmiService mmi.PharmindexProService,
	contractService *contract.Service,
) (*HimiRepo, error) {
	if mmiLicenseKey == "" || mmiUsername == "" {
		return nil, errors.New("mmiLicenseKey and mmiUsername are required")
	}

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to open database")
	}

	if err := db.Use(tracing.NewPlugin(
		tracing.WithTracerProvider(otel.GetTracerProvider()),
		tracing.WithRecordStackTrace(),
	)); err != nil {
		return nil, errors.WithMessage(err, "failed to use tracing plugin")
	}
	return &HimiRepo{
		db:              db,
		mmiService:      mmiService,
		mmiLicenseKey:   mmiLicenseKey,
		mmiUsername:     mmiUsername,
		contractService: contractService,
	}, nil
}
func getTypeName(v any) string {
	kind := reflect.TypeOf(v).Kind()
	if kind == reflect.Slice {
		return strings.ToLower(reflect.TypeOf(v).Elem().Name())
	}
	return strings.ToLower(reflect.TypeOf(v).Name())
}

func getTableNameByVersion(version string, model any) string {
	tableName := getTypeName(model) + "_" + version
	return tableName
}

func (repo *HimiRepo) getTable(version string, model any) *gorm.DB {
	return repo.db.Scopes(func(d *gorm.DB) *gorm.DB {
		tableName := getTableNameByVersion(version, model)
		return d.Table(tableName)
	})
}

func (repo *HimiRepo) SearchGruppe(ctx *titan.Context, req himi_model.SearchGruppeRequest) ([]himi_model.Gruppe, error) {
	tb := repo.getTable(req.Version, himi_model.Gruppe{})
	if req.Id != nil {
		tb.Where("gruppe_id = ?", req.Id)
	}
	if req.Bezeichnung != nil {
		tb.Where("bezeichnung @@ to_tsquery(?)", standardizedData(*req.Bezeichnung))
	}
	var gruppes []himi_model.Gruppe
	err := tb.WithContext(ctx).Find(&gruppes).Error
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return gruppes, nil
}

func (repo *HimiRepo) SearchOrt(ctx *titan.Context, req himi_model.SearchOrtRequest) ([]himi_model.Ort, error) {
	tb := repo.getTable(req.Version, himi_model.Ort{})
	if req.Id != nil {
		tb.Where("ort_id = ?", req.Id)
	}
	if req.Bezeichnung != nil {
		tb.Where("bezeichnung @@ to_tsquery(?)", standardizedData(*req.Bezeichnung))
	}
	var orts []himi_model.Ort
	err := tb.WithContext(ctx).Find(&orts).Error
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return orts, nil
}

func joinProductNumber(productNumber ...*int32) string {
	var numbers []string
	for _, pn := range productNumber {
		if pn != nil {
			numbers = append(numbers, strconv.Itoa(int(*pn)))
		}
	}
	return strings.Join(numbers, "")
}

func buildTsQuery(in string) string {
	words := strings.Fields(in)
	for i, word := range words {
		words[i] = word + ":*"
	}
	return strings.Join(words, " & ")
}

func (repo *HimiRepo) SearchArt(ctx *titan.Context, req himi_model.SearchArtRequest) (arts []himi_model.Art, err error) {
	searchArtFreeTextStatementWithVersion := fmt.Sprintf(searchArtFreeTextStatement, req.Version)
	defer func() {
		if err == nil && len(arts) > 0 {
			sort.Slice(arts, func(i, j int) bool {
				if arts[i].Base.Gruppe != arts[j].Base.Gruppe {
					return arts[i].Base.Gruppe < arts[j].Base.Gruppe
				}
				if arts[i].Base.Ort != arts[j].Base.Ort {
					return arts[i].Base.Ort < arts[j].Base.Ort
				}
				if arts[i].Base.Unter != arts[j].Base.Unter {
					return arts[i].Base.Unter < arts[j].Base.Unter
				}
				return arts[i].ArtId < arts[j].ArtId
			})
		}
	}()
	searchValue := util.GetPointerValue(req.SearchValue)
	switch req.SearchArtType {
	case himi_model.SearchArtType_SearchArtType_Thesaurus:
		if searchValue == "" {
			return nil, nil
		}
		searchArtByThesaurusStatementWithVersion := fmt.Sprintf(searchArtByThesaurusStatement, req.Version)
		err := repo.db.WithContext(ctx).Raw(searchArtByThesaurusStatementWithVersion, "%"+searchValue+"%").Scan(&arts).Error
		if err != nil {
			return nil, errors.WithMessage(err, "search art by thesaurus")
		}
		return arts, nil
	case himi_model.SearchArtType_SearchArtType_Unknown:
		if req.SearchBaseFreeText != nil {
			goto searchBaseFreeText
		}
		if searchValue == "" {
			return nil, nil
		}
		description := buildTsQuery(strings.ToLower(searchValue))
		err := repo.db.WithContext(ctx).Raw(searchArtFreeTextStatementWithVersion, description, description, description).Scan(&arts).Error
		if err != nil {
			return nil, err
		}
		return arts, nil
	}
searchBaseFreeText:
	if req.SearchBaseFreeText != nil {
		artTb := repo.getTable(req.Version, himi_model.Art{})
		if req.SearchBaseFreeText.Gruppe != nil {
			artTb = artTb.Where(himi_model.Art{
				Base: himi_model.Base{
					Gruppe: *req.SearchBaseFreeText.Gruppe,
				},
			})
		}
		if req.SearchBaseFreeText.Ort != nil {
			artTb = artTb.Where(himi_model.Art{
				Base: himi_model.Base{
					Ort: *req.SearchBaseFreeText.Ort,
				},
			})
		}
		if req.SearchBaseFreeText.Unter != nil {
			artTb = artTb.Where(himi_model.Art{
				Base: himi_model.Base{
					Unter: *req.SearchBaseFreeText.Unter,
				},
			})
		}
		if req.SearchBaseFreeText.ArtId != nil {
			artTb = artTb.Where("art_id = ?", *req.SearchBaseFreeText.ArtId)
		}
		isFullBaseSearch := req.SearchBaseFreeText.Gruppe != nil && req.SearchBaseFreeText.Ort != nil && req.SearchBaseFreeText.Unter != nil
		if !isFullBaseSearch {
			productNumber := joinProductNumber(req.SearchBaseFreeText.Gruppe, req.SearchBaseFreeText.Ort, req.SearchBaseFreeText.Unter, req.SearchBaseFreeText.ArtId)
			artTb = artTb.Or(queryConcatProductNumber, fmt.Sprintf("%%%s%%", productNumber))
		}
		err := artTb.Find(&arts).Error
		if err != nil {
			return nil, err
		}
	}

	return arts, nil
}

func (repo *HimiRepo) SearchOrtByGruppe(ctx *titan.Context, req himi_model.SearchOrtByGruppeRequest) (himi_model.SearchOrtByGruppeResponse, error) {
	queryOrtByGruppeStatementWithVersion := fmt.Sprintf(queryOrtByGruppeStatement, req.Version)
	response := himi_model.SearchOrtByGruppeResponse{}
	var ortGroups []himi_model.OrtGroup
	err := repo.db.WithContext(ctx).Raw(queryOrtByGruppeStatementWithVersion, req.GruppeId).Scan(&ortGroups).Error
	if err != nil {
		return response, errors.WithStack(err)
	}
	if len(ortGroups) == 0 {
		return response, nil
	}
	response.Gruppe = himi_model.Gruppe{
		GruppeId:    ortGroups[0].GruppeId,
		Bezeichnung: ortGroups[0].GruppeBezeichnung,
	}
	for _, ortGroup := range ortGroups {
		response.Orts = append(response.Orts, himi_model.Ort{
			OrtId:       ortGroup.OrtId,
			Bezeichnung: ortGroup.OrtBezeichnung,
		})
	}
	response.Version = req.Version
	return response, nil
}

func isRequiredReason(ort, unter int32) bool {
	return ort != int32(99) || unter != int32(99)
}

func (repo *HimiRepo) SearchUnterByGruppeOrt(ctx *titan.Context, req himi_model.SearchUnterByGruppeOrtRequest) (himi_model.SearchUnterByGruppeOrtResponse, error) {
	searchUnterByGruppeOrtStatementWithVersion := fmt.Sprintf(searchUnterByGruppeOrtStatement, req.Version)
	response := himi_model.SearchUnterByGruppeOrtResponse{}
	var ortGroupUnters []himi_model.OrtGroupUnter
	err := repo.db.WithContext(ctx).Raw(searchUnterByGruppeOrtStatementWithVersion, req.GruppeId, req.OrtId).Scan(&ortGroupUnters).Error
	if err != nil {
		return response, errors.WithStack(err)
	}
	if len(ortGroupUnters) == 0 {
		return response, nil
	}
	response.Version = req.Version
	response.Gruppe = himi_model.Gruppe{
		GruppeId:    ortGroupUnters[0].GruppeId,
		Bezeichnung: ortGroupUnters[0].GruppeBezeichnung,
	}
	response.Ort = himi_model.Ort{
		OrtId:       ortGroupUnters[0].OrtId,
		Bezeichnung: ortGroupUnters[0].OrtBezeichnung,
	}

	for _, ortGroupUnter := range ortGroupUnters {
		response.Unters = append(response.Unters, himi_model.Unter{
			Base: himi_model.Base{
				Gruppe: ortGroupUnter.GruppeId,
				Ort:    ortGroupUnter.OrtId,
				Unter:  ortGroupUnter.Unter,
			},
			ID:          ortGroupUnter.ID,
			Bezeichnung: ortGroupUnter.UnterBezeichnung,
		})
	}
	return response, nil
}

func (repo *HimiRepo) SearchProductByBaseAndArt(ctx *titan.Context, req himi_model.SearchProductByBaseAndArtRequest) (*himi_model.SearchProductByBaseAndArtResponse, error) {
	SearchProductByBaseAndArtStatement := fmt.Sprintf(SearchProductByBaseAndArtStatement, req.Version)
	var productFullInfors []himi_model.ProduktFullInfo
	err := repo.db.WithContext(ctx).Raw(SearchProductByBaseAndArtStatement, map[string]any{
		"gruppe": req.Gruppe,
		"ort":    req.Ort,
		"unter":  req.Unter,
		"art":    req.ArtId,
	}).Scan(&productFullInfors).Error
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(productFullInfors) == 0 {
		return &himi_model.SearchProductByBaseAndArtResponse{
			Gruppe:   himi_model.Gruppe{},
			Ort:      himi_model.Ort{},
			Unter:    himi_model.Unter{},
			Art:      himi_model.Art{},
			Version:  req.Version,
			Products: []himi_model.Produkt{},
		}, nil
	}
	productInfor := productFullInfors[0]
	base := himi_model.Base{
		Gruppe: req.Gruppe,
		Ort:    req.Ort,
		Unter:  req.Unter,
	}
	gruppe := himi_model.Gruppe{
		GruppeId:    productInfor.GruppeId,
		Bezeichnung: productInfor.GruppeBezeichnung,
	}
	ort := himi_model.Ort{
		OrtId:       productInfor.OrtId,
		Bezeichnung: productInfor.OrtBezeichnung,
	}
	unter := himi_model.Unter{
		Base:        base,
		ID:          productInfor.UnterId,
		Bezeichnung: productInfor.UnterBezeichnung,
	}
	art := himi_model.Art{
		Base:        base,
		ID:          productInfor.ArtId,
		ArtId:       productInfor.Art,
		Bezeichnung: productInfor.Bezeichnung,
	}
	var products []himi_model.Produkt
	for _, c := range productFullInfors {
		products = append(products, himi_model.Produkt{
			Base:        base,
			ID:          c.ProductId,
			ArtId:       c.Art,
			Produkt:     c.Produkt,
			Lfnr:        c.Lfnr,
			Hersteller:  c.Hersteller,
			Bezeichnung: c.Bezeichnung,
			Search:      c.Search,
		})
	}
	return &himi_model.SearchProductByBaseAndArtResponse{
		Gruppe:   gruppe,
		Ort:      ort,
		Unter:    unter,
		Art:      art,
		Version:  req.Version,
		Products: products,
		IsRequiredReason: himi_model.IsRequiredReason{
			IsRequired: isRequiredReason(ort.OrtId, unter.Base.Unter),
		},
	}, nil
}

func (repo *HimiRepo) SearchControllableHimi(ctx *titan.Context, req himi_model.SearchControllableHimiRequest) (*himi_model.SearchControllableHimiResponse, error) {
	res, err := repo.mmiService.GetHMVProductsContext(ctx, &mmi.GetHMVProducts{
		Licensekey:            repo.mmiLicenseKey,
		Username:              repo.mmiUsername,
		Hmvclassificationcode: fmt.Sprintf("%d.%d.%d.%d", req.Base.Gruppe, req.Base.Ort, req.Base.Unter, req.ArtId),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "search controllable himi")
	}
	if res == nil || len(res.HMVPRODUCT) == 0 {
		return &himi_model.SearchControllableHimiResponse{
			Version: req.Version,
		}, nil
	}

	response := himi_model.SearchControllableHimiResponse{
		Version: req.Version,
		SteuerbareHilfsmittel: himi_model.SteuerbareHilfsmittel{
			Base:       req.Base,
			ArtId:      req.ArtId,
			Fragebogen: res.HMVPRODUCT[0].QUESTIONNAIRECODE,
		},
	}
	return &response, nil
}

func (repo *HimiRepo) SearchHimiMatchingTable(ctx *titan.Context, req himi_model.SearchHimiMatchingTableRequest) (*himi_model.SearchHimiMatchingTableResponse, error) {
	var hilfsmittelMatchingtabelle himi_model.HilfsmittelMatchingtabelle
	ok := repo.contractService.NewContractFinder(*req.ContractId).CheckComplianceIds(model.ICheckExistAnforderung{
		ComplianceIds: himiComplianceIds,
		CheckTime:     util.NowUnixMillisWithLocation(ctx, ctx.RequestTimeZone()),
	})
	if ok {
		hilfsmittelMatchingtabelle.Vertrag = util.GetStringValue(req.ContractId)
	} else {
		return &himi_model.SearchHimiMatchingTableResponse{
			HilfsmittelMatchingtabelle: nil,
			SteuerbareHilfsmittel:      nil,
			Version:                    req.Version,
		}, nil
	}
	result, err := repo.SearchControllableHimi(ctx, himi_model.SearchControllableHimiRequest{
		Base:  req.Base,
		ArtId: req.ArtId,
	})

	if err != nil {
		return nil, errors.WithMessage(err, "search himi matching table")
	}
	if result == nil {
		return &himi_model.SearchHimiMatchingTableResponse{
			HilfsmittelMatchingtabelle: &hilfsmittelMatchingtabelle,
			SteuerbareHilfsmittel:      nil,
			Version:                    req.Version,
		}, nil
	}

	return &himi_model.SearchHimiMatchingTableResponse{
		HilfsmittelMatchingtabelle: &hilfsmittelMatchingtabelle,
		SteuerbareHilfsmittel:      &result.SteuerbareHilfsmittel,
		Version:                    req.Version,
	}, nil
}
