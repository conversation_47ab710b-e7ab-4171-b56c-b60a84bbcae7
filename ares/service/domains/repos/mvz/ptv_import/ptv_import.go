package ptv_import

import (
	mongodb "git.tutum.dev/medi/tutum/ares/pkg/repo"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	ptv_import_common "git.tutum.dev/medi/tutum/ares/service/domains/ptv_import/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
)

// Model definitions
type PtvImport struct {
	repos.BaseEntity `bson:",inline"`
	// DoctorId         uuid.UUID                        `bson:"doctorId"`
	// RetrievalCode    string                           `bson:"retrievalCode"`
	// Year             int64                            `bson:"year"`
	// Quarter          int64                            `bson:"quarter"`
	PtvImport ptv_import_common.PtvImport      `bson:"inline"`
	Contract  ptv_import_common.ImportContract `bson:"inline"`
}

type PtvImportRepo struct {
	mongodb.Repo[*PtvImport]
}

func NewPtvImportDefaultRepository() PtvImportRepo {
	return PtvImportRepo{
		mongodb.NewRepo[*PtvImport](mongodb_repo.NewMongoDbIDBClient("tutum_mvz_db", "ptv_import", true)),
	}
}

// type FileInfo struct {
// 	BucketName  string `bson:"bucketName"`
// 	ObjectName  string `bson:"objectName"`
// 	CreatedDate int64  `bson:"createdDate"`
// }

const (
	Field_Id            = "_id"
	Field_DoctorId      = "doctorId"
	Field_RetrievalCode = "retrievalCode"
	Field_Year          = "year"
	Field_Quarter       = "quarter"

	Field_ImportContract_ContractId = "contractId"
	Field_ImportContract_DocumentId = "documentId"
	Field_ImportContract_FileInfo   = "fileInfo"
	Field_ImportContract_Version    = "version"
	Field_ImportContract_Status     = "status"

	Field_FileInfo_BucketName  = "bucketName"
	Field_FileInfo_ObjectName  = "objectName"
	Field_FileInfo_CreatedDate = "createdDate"
)

// HasPendingRecord checks if there's at least one record with status different from "DONE"
func (r PtvImportRepo) HasNotDoneRecord(ctx *titan.Context) (bool, error) {
	filter := bson.M{
		Field_ImportContract_Status: bson.M{"$ne": ptv_import_common.ImportContractStatus_Done},
	}

	count, err := r.Count(ctx, filter)
	if err != nil {
		return false, err
	}

	return count > 0, nil
}
