package eab

import (
	"fmt"

	"git.tutum.dev/medi/tutum/ares/pkg/operator"
	mongodb "git.tutum.dev/medi/tutum/ares/pkg/repo"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	common_api "git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/eab/common"
	qes_common "git.tutum.dev/medi/tutum/ares/service/domains/qes/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	Field_Status                     = "status"
	Field_Receiver                   = "receiver"
	Field_PatientName                = "patientname"
	Field_SignedFile                 = "signedfile"
	Field_BundleUrl                  = "bundleurl"
	Field_DoctorLetterId             = "doctorletterid"
	Field_PDFUrl                     = "pdfurl"
	Field_MailSentId                 = "mailsentid"
	Field_XMLFile                    = "xmlfile"
	Field_PatientProfile             = "patientprofile"
	Field_PatientProfile_Id          = "patientprofile.id"
	Field_PatientProfile_PatientInfo = "patientprofile.patientinfo"
)

type (
	EABEntity struct {
		repos.BaseEntity `bson:",inline"`
		common.EABModel  `bson:",inline"`
	}

	EABRepo struct {
		mongodb.Repo[*EABEntity]
	}
)

var (
	EABRepoMod = submodule.Make[*EABRepo](func() *EABRepo {
		return &EABRepo{
			mongodb.NewRepo[*EABEntity](mongodb_repo.NewMongoDbIDBClient("tutum_mvz_db", "eab", true)),
		}
	})
)

type (
	FindByFilterRequest struct {
		DateRange         *common_api.DateRange
		PaginationRequest common_api.PaginationRequest
		Statuses          []qes_common.DocumentStatus
		Query             *string
	}
	FindByStatusRequest struct {
		Status qes_common.DocumentStatus
	}

	UpdateEABStatusRequest struct {
		Id     uuid.UUID
		Status qes_common.DocumentStatus
	}
)

func (r *EABRepo) GetByStatus(ctx *titan.Context, request FindByStatusRequest) ([]EABEntity, error) {
	filter := bson.D{
		{
			Key:   Field_Status,
			Value: request.Status,
		},
		{
			Key:   repos.Field_IsDeleted,
			Value: false,
		},
	}

	return slice.ToValueTypeWithError(r.Find(ctx, filter))
}

func (r *EABRepo) FindByFilter(ctx *titan.Context, req FindByFilterRequest) ([]EABEntity, int64, error) {
	filter := bson.D{
		{Key: repos.Field_IsDeleted, Value: false},
	}

	if req.Query != nil && util.GetPointerValue(req.Query) != "" {
		filter = append(filter, bson.E{
			Key: operator.Or,
			Value: bson.A{
				bson.M{
					Field_PatientName: bson.M{
						operator.Regex: req.Query,
						"$options":     "i",
					},
				},
				bson.M{
					Field_Receiver: bson.M{
						operator.Regex: req.Query,
						"$options":     "i",
					},
				},
			},
		},
		)
	}

	if req.DateRange != nil {
		begin, _ := util.GetDayRange(util.ConvertMillisecondsToTime(req.DateRange.StartDate))
		if req.DateRange.EndDate != nil {
			_, end := util.GetDayRange(util.ConvertMillisecondsToTime(*req.DateRange.EndDate))
			filter = append(filter, bson.E{Key: repos.Field_CreatedAt, Value: bson.M{
				operator.Gte: begin,
				operator.Lte: end,
			}})
		} else {
			filter = append(filter, bson.E{Key: repos.Field_CreatedAt, Value: bson.M{
				operator.Gte: begin,
			}})
		}
	}

	if len(req.Statuses) > 0 {
		filter = append(filter, bson.E{
			Key: Field_Status,
			Value: bson.M{
				operator.In: req.Statuses,
			},
		})
	}

	sortOrder := 1
	if req.PaginationRequest.Order == common_api.DESC {
		sortOrder = -1
	}

	sortBy := repos.Field_CreatedAt
	if req.PaginationRequest.SortBy != "" {
		sortBy = req.PaginationRequest.SortBy
	}

	option := options.Find().SetSort(bson.M{sortBy: sortOrder})
	var result []EABEntity
	total, err := r.Repo.Paging(ctx, filter, &result, req.PaginationRequest.Page, req.PaginationRequest.PageSize, option)
	if err != nil {
		return nil, -1, err
	}

	return result, total, nil
}

func (r *EABRepo) Create(ctx *titan.Context, model common.EABModel) (*EABEntity, error) {
	id := uuid.New()
	now := util.Now(ctx)
	model.CreatedAt = util.NewPointer(now.UnixMilli())
	model.Id = &id
	entity := EABEntity{
		BaseEntity: repos.BaseEntity{
			Id:        &id,
			CreatedAt: now,
			CreatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
		},
		EABModel: common.EABModel{
			Id:             model.Id,
			Status:         model.Status,
			CreatedAt:      model.CreatedAt,
			XmlFile:        model.XmlFile,
			SignedFile:     model.SignedFile,
			DoctorLetterId: model.DoctorLetterId,
			PatientProfile: model.PatientProfile,
			PDFUrl:         model.PDFUrl,
			Receiver:       model.Receiver,
			MailSentId:     model.MailSentId,
			PatientName:    fmt.Sprintf("%s %s", model.PatientProfile.FirstName, model.PatientProfile.LastName),
		},
	}
	return r.Repo.Create(ctx, &entity)
}

func (r *EABRepo) Update(ctx *titan.Context, request common.EABModel) (*EABEntity, error) {
	filter := bson.M{
		repos.Field_Id: request.Id,
	}
	update := bson.M{
		"$set": bson.M{
			repos.Field_UpdatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
			repos.Field_UpdatedAt: util.Now(ctx),
			Field_DoctorLetterId:  request.DoctorLetterId,
			Field_PatientProfile:  request.PatientProfile,
			Field_XMLFile:         request.XmlFile,
		},
	}
	return r.Repo.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (r *EABRepo) FindByIds(ctx *titan.Context, ids []uuid.UUID, opts ...*options.FindOptions) ([]EABEntity, error) {
	filter := bson.D{{
		Key: repos.Field_Id,
		Value: bson.M{
			operator.In: ids,
		}},
		{
			Key:   repos.Field_IsDeleted,
			Value: false,
		},
	}

	return slice.ToValueTypeWithError(r.Repo.Find(ctx, filter, opts...))
}

func (r *EABRepo) Delete(ctx *titan.Context, ids []uuid.UUID) (int64, error) {
	filter := bson.M{
		repos.Field_Id:        bson.M{operator.In: ids},
		repos.Field_IsDeleted: false,
	}
	update := bson.M{
		operator.Set: bson.M{
			repos.Field_IsDeleted: true,
			repos.Field_UpdatedAt: util.Now(ctx),
			repos.Field_UpdatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
		},
	}
	res, err := r.IDBClient.UpdateMany(ctx, filter, update, nil, nil)
	if err != nil {
		return -1, err
	}

	if res == nil {
		return -1, nil
	}

	return res.ModifiedCount, nil
}

func (r *EABRepo) FindByDoctorLetterId(ctx *titan.Context, doctorLetterId uuid.UUID) (*EABEntity, error) {
	filter := bson.D{
		{
			Key:   Field_DoctorLetterId,
			Value: doctorLetterId,
		},
		{
			Key:   repos.Field_IsDeleted,
			Value: false,
		},
	}

	return r.Repo.FindOne(ctx, filter)
}

func (r *EABRepo) FindAllByDoctorLetterId(ctx *titan.Context, doctorLetterId uuid.UUID) (*EABEntity, error) {
	filter := bson.D{
		{
			Key:   Field_DoctorLetterId,
			Value: doctorLetterId,
		},
	}

	return r.Repo.FindOne(ctx, filter)
}

func (r *EABRepo) DeleteByFromId(ctx *titan.Context, fromId uuid.UUID) (int64, error) {
	filter := bson.M{
		Field_DoctorLetterId:  fromId,
		repos.Field_IsDeleted: false,
	}
	update := bson.M{
		operator.Set: bson.M{
			repos.Field_IsDeleted: true,
			repos.Field_UpdatedAt: util.Now(ctx),
			repos.Field_UpdatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
		},
	}
	res, err := r.IDBClient.UpdateMany(ctx, filter, update, nil, nil)
	if err != nil {
		return -1, err
	}

	if res == nil {
		return -1, nil
	}

	return res.ModifiedCount, nil
}

func (r *EABRepo) DeleteById(ctx *titan.Context, id uuid.UUID) (*EABEntity, error) {
	filter := bson.M{
		repos.Field_Id: id,
	}
	update := bson.M{
		operator.Set: bson.M{
			repos.Field_UpdatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
			repos.Field_IsDeleted: true,
		},
	}
	return r.Repo.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (r *EABRepo) RestoreById(ctx *titan.Context, id uuid.UUID) (*EABEntity, error) {
	filter := bson.M{
		repos.Field_Id: id,
	}
	update := bson.M{
		operator.Set: bson.M{
			repos.Field_UpdatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
			repos.Field_IsDeleted: false,
		},
	}
	return r.Repo.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (r *EABRepo) DeleteByDocterLetterId(ctx *titan.Context, id uuid.UUID) (*EABEntity, error) {
	filter := bson.M{
		Field_DoctorLetterId: id,
	}
	update := bson.M{
		operator.Set: bson.M{
			repos.Field_UpdatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
			repos.Field_IsDeleted: true,
		},
	}
	return r.Repo.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (r *EABRepo) UpdateStatusById(ctx *titan.Context, request UpdateEABStatusRequest) (*EABEntity, error) {
	return r.Repo.FindOneAndUpdate(ctx, bson.D{{
		Key:   repos.Field_Id,
		Value: request.Id,
	}}, bson.M{
		operator.Set: bson.M{
			repos.Field_UpdatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
			repos.Field_UpdatedAt: util.Now(ctx),
			Field_Status:          request.Status,
		},
	})
}

func (*EABRepo) GetPatientFieldName() string {
	return Field_PatientProfile_Id
}
