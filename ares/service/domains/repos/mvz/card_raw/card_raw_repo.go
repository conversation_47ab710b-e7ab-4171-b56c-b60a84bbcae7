package card_raw

import (
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/card_raw"
	mongodb "git.tutum.dev/medi/tutum/ares/pkg/repo"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	common1 "git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/card_raw/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
)

const (
	Field_importStatus   = "cardreaderpayload.card.cardinfo.importstatus"
	Field_PatientId      = "cardreaderpayload.card.cardinfo.patientid"
	Field_InsuranceId    = "cardreaderpayload.card.cardinfo.insuranceid"
	Field_Cardtypemobile = "cardreaderpayload.card.cardtypemobile"
	Field_CardTypeTI     = "cardreaderpayload.card.cardtypeti"
	Field_ReadingDate    = "cardreaderpayload.card.cardinfo.readingdate"
	Field_LastName       = "cardreaderpayload.card.cardinfo.patient.lastname"
)

type PayloadType string

const (
	PayloadType_XML  PayloadType = "xml"
	PayloadType_JSON PayloadType = "json"

	Field_ICCSN = "cardreaderpayload.card.iccsn"
)

type CardReaderPayload[T common.MobileCardInfo | common.TiCardInfo | any] struct {
	Card        T           `bson:"card"`
	Payload     string      `bson:"payload"`
	PayloadType PayloadType `bson:"payloadType"`
}

type CardRawEntity[T common.MobileCardInfo | common.TiCardInfo | any] struct {
	repos.BaseEntity  `bson:",inline"`
	CardReaderPayload CardReaderPayload[T] `bson:"cardreaderpayload"`
	Error             string               `bson:"error"`
}

type CardRawRepo[T common.MobileCardInfo | common.TiCardInfo | any] struct {
	mongodb.Repo[*CardRawEntity[T]]
}

func NewCardRawRepo[T common.MobileCardInfo | common.TiCardInfo | any]() *CardRawRepo[T] {
	return &CardRawRepo[T]{
		mongodb.NewRepo[*CardRawEntity[T]](mongodb_repo.NewMongoDbIDBClient("tutum_mvz_db", "card_raw", true)),
	}
}

func (e *CardRawRepo[T]) FindCardMobileValidByPatientId(ctx *titan.Context, patientId uuid.UUID) (*CardRawEntity[T], error) {
	query := bson.M{
		repos.Field_IsDeleted: false,
		Field_PatientId:       patientId,
		Field_Cardtypemobile: bson.M{
			"$exists": true,
		},
		"$or": []bson.M{
			{Field_importStatus: common.ImportStatus_PatientCreated},
			{Field_importStatus: common.ImportStatus_PatientUpdated},
			{Field_importStatus: common.ImportStatus_Processed},
		},
	}

	entities, err := e.Repo.Find(ctx, query, options.Find().SetSort(bson.M{repos.Field_CreatedAt: -1}))
	if err != nil {
		return nil, err
	}
	if len(entities) == 0 {
		return nil, nil
	}

	return entities[0], nil
}

func (e *CardRawRepo[T]) FindLatestTIByPatientId(ctx *titan.Context, patientId uuid.UUID) (*CardRawEntity[T], error) {
	query := bson.M{
		repos.Field_IsDeleted: false,
		Field_PatientId:       patientId,
		Field_CardTypeTI: bson.M{
			"$exists": true,
		},
	}

	entities, err := e.Repo.FindOne(ctx, query, options.FindOne().SetSort(bson.M{repos.Field_CreatedAt: -1}))
	if err != nil {
		return nil, errors.WithMessage(err, "find one failed")
	}
	if entities == nil {
		return nil, nil
	}

	return entities, nil
}

func (m *CardRawRepo[T]) GetAllMobileCardRaw(ctx *titan.Context, paginationRequest common1.PaginationRequest) ([]CardRawEntity[T], int64, error) {
	var results []CardRawEntity[T]
	filter := bson.M{
		repos.Field_IsDeleted: false,
		Field_Cardtypemobile: bson.M{
			"$exists": true,
		},
	}
	findOptions := &options.FindOptions{}
	if paginationRequest.SortBy != "" {
		order := -1
		if paginationRequest.Order == common1.ASC {
			order = 1
		}
		var fieldSort string
		switch paginationRequest.SortBy {
		case string(common.ReadingDate):
			fieldSort = Field_ReadingDate
		case string(common.Status):
			fieldSort = Field_importStatus
		case string(common.LastName):
			fieldSort = Field_LastName
		}
		findOptions.Sort = bson.D{{
			Key:   fieldSort,
			Value: order,
		}}
	}
	total, err := m.Repo.Paging(ctx, filter, &results, paginationRequest.Page, paginationRequest.PageSize, findOptions)
	if err != nil {
		return nil, -1, err
	}
	return results, total, nil
}

func (e *CardRawRepo[T]) Create(ctx *titan.Context, entity CardRawEntity[T]) (*CardRawEntity[T], error) {
	result, err := e.Repo.Create(ctx, &CardRawEntity[T]{
		BaseEntity:        repos.BaseEntity{Id: util.NewUUID(), CreatedAt: util.Now(ctx), CreatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID())},
		Error:             entity.Error,
		CardReaderPayload: entity.CardReaderPayload,
	})
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (e *CardRawRepo[T]) GetDataForImport(ctx *titan.Context, request card_raw.ImportDataRequest) ([]CardRawEntity[T], error) {
	filter := bson.M{
		repos.Field_IsDeleted: false,
		Field_importStatus:    common.ImportStatus_NewData,
	}
	if len(request.Ids) > 0 {
		filter = bson.M{
			repos.Field_IsDeleted: false,
			"$or": []bson.M{
				{
					Field_importStatus: common.ImportStatus_NewData,
				},
				{
					Field_importStatus: common.ImportStatus_ImportedFailure,
				},
			},
			"_id": bson.M{
				"$in": request.Ids,
			},
		}
	}
	results, err := e.Repo.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	return slice.ToValueType(results), nil
}

func (e *CardRawRepo[T]) GetReviewRequiredItems(ctx *titan.Context, request card_raw.ImportDataRequest) ([]CardRawEntity[T], error) {
	filter := bson.M{
		repos.Field_IsDeleted: false,
		Field_importStatus:    common.ImportStatus_ReviewRequired,
	}
	if len(request.Ids) > 0 {
		filter["_id"] = bson.M{
			"$in": request.Ids,
		}
	}
	results, err := e.Repo.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	return slice.ToValueType(results), nil
}

func (e *CardRawRepo[T]) DeleteData(ctx *titan.Context, ids []uuid.UUID) error {
	if len(ids) == 0 {
		return nil
	}
	query := bson.M{
		repos.Field_Id:        bson.M{"$in": ids},
		repos.Field_IsDeleted: false,
	}
	_, err := e.IDBClient.UpdateMany(
		ctx,
		query,
		bson.M{
			"$set": bson.M{
				repos.Field_IsDeleted: true,
				repos.Field_UpdatedAt: util.Now(ctx),
			},
		},
		nil,
		nil,
	)
	return err
}

func (e *CardRawRepo[T]) DeleteAllData(ctx *titan.Context) error {
	query := bson.M{
		repos.Field_IsDeleted: false,
		"$or": []bson.M{
			{Field_importStatus: common.ImportStatus_PatientCreated},
			{Field_importStatus: common.ImportStatus_PatientUpdated},
			{Field_importStatus: common.ImportStatus_Processed},
		},
	}

	_, err := e.IDBClient.UpdateMany(ctx, query, bson.M{
		"$set": bson.M{
			repos.Field_IsDeleted: true,
			repos.Field_UpdatedAt: util.Now(ctx),
		},
	}, nil, nil)
	return err
}

func (e *CardRawRepo[T]) UpdateStatusRaw(ctx *titan.Context, id uuid.UUID, status common.ImportStatus, patientId *uuid.UUID) error {
	query := bson.M{
		repos.Field_Id:        id,
		repos.Field_IsDeleted: false,
	}
	var filter = bson.M{
		"$set": bson.M{
			Field_importStatus:    status,
			repos.Field_UpdatedAt: util.Now(ctx),
		},
	}

	if patientId != nil {
		filter = bson.M{
			"$set": bson.M{
				Field_PatientId:       patientId,
				Field_importStatus:    status,
				repos.Field_UpdatedAt: util.Now(ctx),
			},
		}
	}

	_, err := e.FindOneAndUpdate(ctx, query, filter)
	return err
}

func (e *CardRawRepo[T]) UpdateById(ctx *titan.Context, id uuid.UUID, request CardRawEntity[T]) (*CardRawEntity[T], error) {
	filter := bson.D{
		{Key: repos.Field_Id, Value: id},
		{Key: repos.Field_IsDeleted, Value: false},
	}
	request.UpdatedAt = util.NewPointer(util.Now(ctx))
	update := bson.M{"$set": request}

	entity, err := e.Repo.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
	if err != nil {
		return nil, err
	}

	return entity, nil
}

func (e *CardRawRepo[T]) GetByIccsn(ctx *titan.Context, iccsn string) ([]CardRawEntity[T], error) {
	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	quarterRange, err := yearQuarter.GetQuarterDateRange()
	if err != nil {
		return nil, err
	}

	query := bson.M{
		Field_ICCSN: iccsn,
		Field_ReadingDate: bson.M{
			"$gte": quarterRange.Start.UnixMilli(),
			"$lte": quarterRange.End.UnixMilli(),
		},
		repos.Field_IsDeleted: false,
	}

	sort := options.Find().SetSort(bson.M{repos.Field_CreatedAt: -1})

	entities, err := e.Repo.Find(ctx, query, sort)
	if err != nil {
		return nil, err
	}
	if len(entities) == 0 {
		return nil, nil
	}

	return slice.ToValueType(entities), nil
}

func (e *CardRawRepo[T]) FindTiByPatientIdAndInsuranceId(ctx *titan.Context, patientId, insuranceId uuid.UUID, yearQuarter util.YearQuarter) (*CardRawEntity[T], error) {
	quarterRange, err := yearQuarter.GetQuarterDateRange()
	if err != nil {
		return nil, err
	}

	query := bson.M{
		repos.Field_IsDeleted: false,
		Field_PatientId:       patientId,
		Field_InsuranceId:     insuranceId,
		Field_CardTypeTI:      bson.M{"$exists": true},
		Field_ReadingDate: bson.M{
			"$gte": quarterRange.Start.UnixMilli(),
			"$lte": quarterRange.End.UnixMilli(),
		},
	}

	sort := options.Find().SetSort(bson.M{repos.Field_CreatedAt: -1})

	entities, err := e.Repo.Find(ctx, query, sort)
	if err != nil {
		return nil, err
	}
	if len(entities) == 0 {
		return nil, nil
	}

	return entities[0], nil
}

func (e *CardRawRepo[T]) FindMobileByPatientIdAndInsuranceId(ctx *titan.Context, patientId, insuranceId uuid.UUID, yearQuarter util.YearQuarter) (*CardRawEntity[T], error) {
	quarterRange, err := yearQuarter.GetQuarterDateRange()
	if err != nil {
		return nil, err
	}

	query := bson.M{
		repos.Field_IsDeleted: false,
		Field_PatientId:       patientId,
		Field_InsuranceId:     insuranceId,
		Field_Cardtypemobile:  bson.M{"$exists": true},
		Field_ReadingDate: bson.M{
			"$gte": quarterRange.Start.UnixMilli(),
			"$lte": quarterRange.End.UnixMilli(),
		},
	}

	sort := options.Find().SetSort(bson.M{repos.Field_CreatedAt: -1})

	entities, err := e.Repo.Find(ctx, query, sort)
	if err != nil {
		return nil, err
	}
	if len(entities) == 0 {
		return nil, nil
	}

	return entities[0], nil
}
