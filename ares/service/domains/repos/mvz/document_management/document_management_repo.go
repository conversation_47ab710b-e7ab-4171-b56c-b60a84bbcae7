package document_management_repo

import (
	"fmt"

	"emperror.dev/errors"
	api "git.tutum.dev/medi/tutum/ares/app/mvz/api/document_management"
	"git.tutum.dev/medi/tutum/ares/pkg/file_util"
	"git.tutum.dev/medi/tutum/ares/pkg/operator"
	"git.tutum.dev/medi/tutum/ares/pkg/repo"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdav_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/document_management/common"
	document_type_common "git.tutum.dev/medi/tutum/ares/service/domains/api/document_type/common"
	document_type_repo "git.tutum.dev/medi/tutum/ares/service/domains/document_type/repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var DocumentManagementRepoMod = submodule.Make[*DocumentManagementRepo](func() *DocumentManagementRepo {
	return &DocumentManagementRepo{
		repo.NewRepo[*DocumentManagementEntity](mongodb_repo.NewMongoDbIDBClient("tutum_mvz_db", "document_management", true)),
	}
})

const (
	Field_Status            = "status"
	Field_CompanionFileId   = "companionfileid"
	Field_CreatedAt         = "createdAt"
	Field_isDeleted         = "isDeleted"
	Field_ImportedDate      = "importeddate"
	Field_Patient_Id        = "patientid"
	Field_Sender_Id         = "senderid"
	Field_Description       = "description"
	Field_DocumentType      = "documenttype"
	Field_DocumentType_Name = "documenttype.name"
	Field_DocumentType_Id   = "documenttype.id"
	Field_DocumentName      = "documentname"
	Field_CompanionFilePath = "companionfilepath"
	Field_GdtImportModTime  = "gdtimportmodtime"
	Field_DocumentSettingId = "documentsettingid"
)

type DocumentManagementEntity struct {
	repos.BaseEntity               `bson:",inline"`
	common.DocumentManagementModel `bson:",inline"`
}

func (e DocumentManagementEntity) GetId() *uuid.UUID {
	return e.Id
}

func (*DocumentManagementEntity) GetPatientFieldName() string {
	return Field_Patient_Id
}

func (e DocumentManagementEntity) ToDocumentManagementItem(patient *common.Patient, sender *catalog_sdav_common.SdavCatalog) common.DocumentManagementItem {
	return common.DocumentManagementItem{
		Id:                *e.GetId(),
		CompanionFileId:   e.CompanionFileId,
		CompanionFilePath: e.CompanionFilePath,
		Patient:           patient,
		Sender:            sender,
		DocumentName:      file_util.GetFileName(e.DocumentName),
		DocumentType:      e.DocumentType,
		Description:       e.Description,
		Status:            e.Status,
		ImportedDate:      e.ImportedDate,
		DocumentDirPath:   file_util.GetFileDirPath(e.DocumentName),
		MetaData:          e.MetaData,
	}
}

func (e DocumentManagementEntity) ToTimeLineModel(patient *common.Patient, sender *catalog_sdav_common.SdavCatalog) timeline_repo.TimelineEntity[patient_encounter.EncounterDocumentManagement] {
	patientId := uuid.Nil
	if patient != nil {
		patientId = patient.Id
	}
	return timeline_repo.TimelineEntity[patient_encounter.EncounterDocumentManagement]{
		PatientId: patientId,
		Payload: patient_encounter.EncounterDocumentManagement{
			Id:                e.Id,
			CompanionFileId:   util.GetPointerValue(e.CompanionFileId),
			CompanionFilePath: util.GetPointerValue(e.CompanionFilePath),
			Patient:           patient,
			Sender:            sender,
			DocumentName:      e.DocumentName,
			DocumentType:      e.DocumentType,
			Description:       util.NewString(e.Description),
			Status:            e.Status,
			ImportedDate:      e.ImportedDate,
		},
	}
}

type DocumentManagementRepo struct {
	repo.Repo[*DocumentManagementEntity]
}

func (r *DocumentManagementRepo) Create(ctx *titan.Context, model common.DocumentManagementModel) (*DocumentManagementEntity, error) {
	entity := &DocumentManagementEntity{
		BaseEntity: repos.BaseEntity{
			Id:        util.NewUUID(),
			CreatedAt: util.Now(ctx),
			CreatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
		},
		DocumentManagementModel: model,
	}

	return r.Repo.Create(ctx, entity)
}

func (r *DocumentManagementRepo) UpdateStatus(ctx *titan.Context, id uuid.UUID, status common.DocumentManagementStatus, importedDate int64) (*DocumentManagementEntity, error) {
	filter := bson.M{repos.Field_Id: id}
	update := bson.M{"$set": bson.M{
		Field_Status:          status,
		Field_ImportedDate:    importedDate,
		repos.Field_CreatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
		repos.Field_UpdatedAt: util.Now(ctx),
	}}

	entity, err := r.Repo.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
	if err != nil {
		return nil, err
	}

	return entity, nil
}

func (r *DocumentManagementRepo) UpdateStatusByFile(ctx *titan.Context, fileId int64, filePath string, status common.DocumentManagementStatus) (*DocumentManagementEntity, error) {
	filter := bson.M{
		Field_CompanionFileId:   fileId,
		Field_CompanionFilePath: filePath,
	}
	update := bson.M{"$set": bson.M{
		Field_Status:          status,
		repos.Field_UpdatedAt: util.Now(ctx),
		repos.Field_UpdatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
	}}

	entity, err := r.Repo.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
	if err != nil {
		return nil, err
	}

	return entity, nil
}

type UpdateMetaDataRequest struct {
	Id           uuid.UUID
	PatientId    *uuid.UUID
	SenderId     *string
	DocumentType *document_type_common.DocumentType
	Description  *string
}

func (r *DocumentManagementRepo) UpdateMetaData(ctx *titan.Context, request UpdateMetaDataRequest) (*DocumentManagementEntity, error) {
	filter := bson.M{repos.Field_Id: request.Id}
	update := bson.M{"$set": bson.M{
		Field_Patient_Id:      request.PatientId,
		Field_Sender_Id:       request.SenderId,
		Field_Description:     request.Description,
		Field_DocumentType:    request.DocumentType,
		repos.Field_UpdatedBy: util.GetPointerValue(ctx.UserInfo().UserUUID()),
		repos.Field_UpdatedAt: util.Now(ctx),
	}}

	entity, err := r.Repo.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
	if err != nil {
		return nil, err
	}

	return entity, nil
}

func (r *DocumentManagementRepo) SoftDelete(ctx *titan.Context, documentManagementId []uuid.UUID) error {
	filter := bson.M{
		repos.Field_Id:  bson.M{operator.In: documentManagementId},
		Field_isDeleted: false,
	}
	update := bson.M{operator.Set: bson.M{
		Field_isDeleted: true,
	}}

	_, err := r.Repo.IDBClient.UpdateMany(ctx, filter, update, nil, nil)
	return err
}

func (r *DocumentManagementRepo) SoftDeleteAllFailDocuments(ctx *titan.Context) error {
	filter := bson.M{
		Field_Status:    common.DocumentManagementStatus_Failed,
		Field_isDeleted: false,
	}
	update := bson.M{operator.Set: bson.M{Field_isDeleted: true}}

	_, err := r.Repo.IDBClient.UpdateMany(ctx, filter, update, nil, nil)
	return err
}

func (r *DocumentManagementRepo) UpdateDocumentType(ctx *titan.Context, documentTypeProfile *document_type_common.DocumentType) error {
	if documentTypeProfile == nil {
		return nil
	}
	filter := bson.D{
		{Key: Field_DocumentType_Id, Value: documentTypeProfile.Id},
		{Key: repos.Field_IsDeleted, Value: false},
	}
	update := bson.M{
		"$set": bson.M{
			"documentType": documentTypeProfile,
		},
	}

	_, err := r.IDBClient.UpdateMany(ctx, filter, update, nil, nil)
	if err != nil {
		return fmt.Errorf("update document type profile failed: %w", err)
	}
	return nil
}

type GetDocumentManagementsResponse struct {
	Total int64
	Items []DocumentManagementEntity
}

func (g *GetDocumentManagementsResponse) GetSenderIds() []string {
	if g == nil {
		return nil
	}

	var senderIds []string
	for _, item := range g.Items {
		if item.SenderId != nil {
			senderIds = append(senderIds, *item.SenderId)
		}
	}

	return senderIds
}

func (g *GetDocumentManagementsResponse) GetPatientIds() []uuid.UUID {
	if g == nil {
		return nil
	}

	var patientIds []uuid.UUID
	for _, item := range g.Items {
		if item.PatientId != nil {
			patientIds = append(patientIds, util.GetPointerValue(item.PatientId))
		}
	}

	return patientIds
}

func (g *GetDocumentManagementsResponse) GetDocumentSettingIds() []uuid.UUID {
	if g == nil {
		return nil
	}

	var documentSettingIds []uuid.UUID
	for _, item := range g.Items {
		if item.DocumentSettingId != nil {
			documentSettingIds = append(documentSettingIds, *item.DocumentSettingId)
		}
	}

	return documentSettingIds
}

func (r *DocumentManagementRepo) GetDocumentManagements(ctx *titan.Context, request api.ListDocumentManagementRequest) (*GetDocumentManagementsResponse, error) {
	sortOrder := 1
	if request.Pagination != nil && request.Pagination.Order == "DESC" {
		sortOrder = -1
	}

	SortBy := Field_CreatedAt
	if request.Pagination != nil && request.Pagination.SortBy != "" {
		SortBy = request.Pagination.SortBy
	}

	filter := bson.M{
		Field_isDeleted:         false,
		Field_DocumentType_Name: bson.M{operator.Ne: document_type_repo.GdtExport},
	}

	if request.FromDate != nil {
		filter[Field_ImportedDate] = bson.M{operator.Gte: request.FromDate}
	}
	if request.ToDate != nil {
		filter[Field_ImportedDate] = bson.M{operator.Lte: request.ToDate}
	}
	if request.IsNotAssigned {
		filter[Field_Patient_Id] = bson.M{operator.Eq: nil}
	}
	if util.GetPointerValue(request.Value) != "" {
		filter = buildSearchFilter(filter, request)
	}

	if request.Status != nil {
		filter[Field_Status] = *request.Status
	}

	results := []DocumentManagementEntity{}
	total, err := r.Repo.Paging(
		ctx,
		filter,
		&results,
		request.Pagination.Page,
		request.Pagination.PageSize,
		options.Find().SetSort(bson.M{SortBy: sortOrder}),
	)
	if err != nil {
		return nil, errors.WithMessage(err, "Paging document management failed")
	}

	return &GetDocumentManagementsResponse{
		Total: total,
		Items: results,
	}, nil
}

func (r *DocumentManagementRepo) UpdateStatusByIds(ctx *titan.Context, ids []uuid.UUID, status common.DocumentManagementStatus) error {
	if len(ids) == 0 {
		return nil
	}
	filter := bson.M{repos.Field_Id: bson.M{operator.In: ids}}
	update := bson.M{"$set": bson.M{
		Field_Status:          status,
		repos.Field_UpdatedAt: util.Now(ctx),
		Field_ImportedDate:    util.NowUnixMillis(ctx),
	}}
	_, err := r.Repo.IDBClient.UpdateMany(ctx, filter, update, nil, nil)
	return err
}

func buildSearchFilter(filter primitive.M, request api.ListDocumentManagementRequest) bson.M {
	filter[operator.Or] = []bson.M{
		{
			Field_DocumentName: primitive.Regex{
				Pattern: util.GetPointerValue(request.Value),
				Options: "i",
			},
		},
		{
			Field_Description: primitive.Regex{
				Pattern: util.GetPointerValue(request.Value),
				Options: "i",
			},
		},
		{
			Field_DocumentType_Name: primitive.Regex{
				Pattern: util.GetPointerValue(request.Value),
				Options: "i",
			},
		},
	}
	if len(request.PatientIds) > 0 {
		filter[operator.Or] = append(filter[operator.Or].([]bson.M), bson.M{Field_Patient_Id: bson.M{operator.In: request.PatientIds}})
	}
	if len(request.SenderIds) > 0 {
		filter[operator.Or] = append(filter[operator.Or].([]bson.M), bson.M{Field_Sender_Id: bson.M{operator.In: request.SenderIds}})
	}

	return filter
}

func (r *DocumentManagementRepo) CountDocumentsNotSyncStatus(ctx *titan.Context) (int64, error) {
	filter := bson.M{
		Field_Status: bson.M{
			"$ne": common.DocumentManagementStatus_Completed,
		},
		Field_DocumentType_Name: "externaldocument",
		Field_isDeleted:         false,
	}
	return r.Repo.Count(ctx, filter)
}

func (r *DocumentManagementRepo) GetDocumentsNotSyncStatus(ctx *titan.Context, batch, batchSize int) ([]DocumentManagementEntity, error) {
	var results []DocumentManagementEntity
	filter := bson.M{
		Field_Status: bson.M{
			"$ne": common.DocumentManagementStatus_Completed,
		},
		Field_DocumentType_Name: "externaldocument",
		Field_isDeleted:         false,
	}
	_, err := r.Paging(ctx, filter, &results, int64(batch), int64(batchSize), nil)
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (r *DocumentManagementRepo) UpdateStatusById(ctx *titan.Context, id uuid.UUID, status common.DocumentManagementStatus) (*DocumentManagementEntity, error) {
	filter := bson.M{repos.Field_Id: id}
	update := bson.M{"$set": bson.M{
		Field_Status:          status,
		repos.Field_UpdatedAt: util.Now(ctx),
	}}
	return r.Repo.FindOneAndUpdate(ctx, filter, update, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (r *DocumentManagementRepo) IsDocumentExisted(ctx *titan.Context, documentName string, documentSettingId uuid.UUID, documentModifyTime int64) (bool, error) {
	filter := bson.M{
		Field_DocumentName:      documentName,
		Field_DocumentSettingId: documentSettingId,
		Field_GdtImportModTime:  documentModifyTime,
		repos.Field_IsDeleted:   false,
	}
	entity, err := r.Find(ctx, filter)
	if err != nil {
		return false, err
	}
	return len(entity) > 0, nil
}
