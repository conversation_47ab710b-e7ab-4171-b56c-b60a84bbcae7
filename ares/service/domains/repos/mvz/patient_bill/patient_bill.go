package patient_bill

import (
	"fmt"

	mongodb "git.tutum.dev/medi/tutum/ares/pkg/repo"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	"git.tutum.dev/medi/tutum/ares/service/domains/billing_patient/common/billing_patient_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
)

const (
	Field_DoctorId           = "doctorprofile.id"
	Field_TimelineIds        = "servicestimeline.timelineid"
	Field_ScheinId           = "schein.scheinid"
	Field_PatientId          = "patientprofile.id"
	Field_CreatedAt          = "createdat"
	Field_Service_Created_At = "servicestimeline.createdat"
)

var (
	PatientBillRepoMod = submodule.Make[*PatientBillRepo](func() *PatientBillRepo {
		return &PatientBillRepo{
			mongodb.NewRepo[*PatientBillEntity](mongodb_repo.NewMongoDbIDBClient("tutum_mvz_db", "patient_bill", true)),
		}
	})
)

type PatientBillEntity struct {
	repos.BaseEntity                           `bson:",inline"`
	billing_patient_common.BillingPatientModel `bson:",inline"`
}

type PatientBillRepo struct {
	mongodb.Repo[*PatientBillEntity]
}

func (p *PatientBillRepo) UpdatePatientProfile(ctx *titan.Context, patientId uuid.UUID, patientInfo patient_profile_common.PatientInfo) error {
	filter := bson.D{
		{Key: Field_PatientId, Value: patientId},
		{Key: repos.Field_IsDeleted, Value: false},
	}

	_, err := p.IDBClient.UpdateMany(ctx, filter, bson.D{
		{Key: "$set", Value: bson.D{
			{Key: "patientprofile.patientinfo", Value: patientInfo},
			{Key: "patientprofile.firstname", Value: patientInfo.PersonalInfo.FirstName},
			{Key: "patientprofile.lastname", Value: patientInfo.PersonalInfo.LastName},
			{Key: "patientprofile.dateofbirth", Value: patientInfo.PersonalInfo.DOB},
			{Key: repos.Field_UpdatedAt, Value: util.Now(ctx)},
			{Key: repos.Field_UpdatedBy, Value: ctx.UserInfo().UserUUID()},
		}},
	}, nil, nil)
	if err != nil {
		return fmt.Errorf("failed to update patient profile: %w", err)
	}
	return nil
}

func (p *PatientBillRepo) UpdateScheinWithDoctor(ctx *titan.Context, schein schein.ScheinRepo, doctorProfile profile.EmployeeProfileResponse) error {
	filter := bson.D{
		{Key: Field_ScheinId, Value: schein.Id},
		{Key: repos.Field_IsDeleted, Value: false},
	}

	_, err := p.IDBClient.UpdateMany(ctx, filter, bson.D{
		{Key: "$set", Value: bson.D{
			{Key: "doctorprofile", Value: doctorProfile},
			{Key: "schein.scheintreatmentcase", Value: schein.Schein.KvTreatmentCase},
			{Key: "schein.scheinsubgroup", Value: util.GetPointerValue(schein.Schein.KvScheinSubGroup)},
			{Key: "schein.doctorid", Value: schein.DoctorId},
			{Key: repos.Field_UpdatedAt, Value: util.Now(ctx)},
			{Key: repos.Field_UpdatedBy, Value: ctx.UserInfo().UserUUID()},
		}},
	}, nil, nil)
	if err != nil {
		return fmt.Errorf("failed to update patient profile: %w", err)
	}
	return nil
}

func (p *PatientBillRepo) GetByScheinAndPatient(ctx *titan.Context, scheinId, patientId uuid.UUID) (*PatientBillEntity, error) {
	filter := bson.M{
		repos.Field_IsDeleted: false,
		Field_PatientId:       patientId,
		Field_ScheinId:        scheinId,
	}
	result, err := p.Repo.FindOne(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to get patient bill by schein and patient: %w", err)
	}
	if result == nil || result.BaseEntity.Id == nil {
		return nil, nil
	}

	return result, nil
}

type GetByScheinAndPatientsRequest struct {
	ScheinId  uuid.UUID
	PatientId uuid.UUID
}

func (p *PatientBillRepo) GetByScheinAndPatients(ctx *titan.Context, req []GetByScheinAndPatientsRequest) ([]PatientBillEntity, error) {
	patientIds := slice.Map(req, func(p GetByScheinAndPatientsRequest) uuid.UUID {
		return p.PatientId
	})

	scheinIds := slice.Map(req, func(p GetByScheinAndPatientsRequest) uuid.UUID {
		return p.ScheinId
	})

	filter := bson.M{
		repos.Field_IsDeleted: false,
		Field_PatientId:       bson.M{"$in": patientIds},
		Field_ScheinId:        bson.M{"$in": scheinIds},
	}
	result, err := p.Repo.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to get patient bill by schein and patient: %w", err)
	}

	if len(result) == 0 {
		return []PatientBillEntity{}, nil
	}
	return slice.ToValueType(result), nil
}

func (p *PatientBillRepo) GetBillingPatient(ctx *titan.Context, billingDoctorId uuid.UUID, selectedDate int64, pagination common.PaginationRequest) ([]PatientBillEntity, int64, error) {
	yearQuarter := util.ToYearQuarter(selectedDate)
	quarterRange, err := yearQuarter.GetQuarterDateRange()
	if err != nil {
		return nil, -1, fmt.Errorf("failed to get patient bill: %w", err)
	}
	startTime := util.GetPointerValue(quarterRange.Start)
	endTime := util.GetPointerValue(quarterRange.End)
	filter := bson.D{
		{Key: Field_DoctorId, Value: billingDoctorId},
		{Key: "patientprofile.patientinfo.insuranceinfos.havepatientreceipt", Value: true},
		{Key: Field_Service_Created_At, Value: bson.D{
			{Key: "$gte", Value: startTime.In(ctx.RequestTimeZone()).UnixMilli()},
			{Key: "$lt", Value: endTime.In(ctx.RequestTimeZone()).UnixMilli()},
		}},
		{Key: repos.Field_IsDeleted, Value: false},
	}
	var results []PatientBillEntity
	total, err := p.Paging(ctx, filter, &results, pagination.Page, pagination.PageSize)
	if err != nil {
		return nil, -1, fmt.Errorf("failed to get patient bill: %w", err)
	}
	if len(results) == 0 {
		return []PatientBillEntity{}, 0, nil
	}

	results = slice.Map(results, func(p PatientBillEntity) PatientBillEntity {
		p.ServicesTimeline = slice.Filter(p.ServicesTimeline, func(s billing_patient_common.ServiceTimeline) bool {
			return s.CreatedAt >= startTime.In(ctx.RequestTimeZone()).UnixMilli() && s.CreatedAt < endTime.In(ctx.RequestTimeZone()).UnixMilli()
		})
		return p
	})

	return results, total, nil
}

func (p *PatientBillRepo) FindByIdsWithQuarter(ctx *titan.Context, patientBillingIds []uuid.UUID, selectedDate int64) ([]PatientBillEntity, error) {
	yearQuarter := util.ToYearQuarter(selectedDate)
	quarterRange, err := yearQuarter.GetQuarterDateRange()
	if err != nil {
		return nil, fmt.Errorf("failed to get patient bill: %w", err)
	}
	startTime := util.GetPointerValue(quarterRange.Start)
	endTime := util.GetPointerValue(quarterRange.End)
	filter := bson.D{
		{Key: repos.Field_Id, Value: bson.D{
			{Key: "$in", Value: patientBillingIds},
		}},
		{Key: Field_Service_Created_At, Value: bson.D{
			{Key: "$gte", Value: startTime.In(ctx.RequestTimeZone()).UnixMilli()},
			{Key: "$lt", Value: endTime.In(ctx.RequestTimeZone()).UnixMilli()},
		}},
		{Key: repos.Field_IsDeleted, Value: false},
	}
	results, err := p.Find(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to get patient bill: %w", err)
	}
	if len(results) == 0 {
		return nil, nil
	}

	results = slice.Map(results, func(p *PatientBillEntity) *PatientBillEntity {
		p.ServicesTimeline = slice.Filter(p.ServicesTimeline, func(s billing_patient_common.ServiceTimeline) bool {
			return s.CreatedAt >= startTime.In(ctx.RequestTimeZone()).UnixMilli() && s.CreatedAt < endTime.In(ctx.RequestTimeZone()).UnixMilli()
		})
		slice.SortBy(p.ServicesTimeline, func(a, b billing_patient_common.ServiceTimeline) bool {
			return a.CreatedAt < b.CreatedAt
		})
		return p
	})

	return slice.ToValueType(results), nil
}
