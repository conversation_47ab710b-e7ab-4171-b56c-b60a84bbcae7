package sdkvca

import (
	_ "embed"
	"encoding/xml"

	"git.tutum.dev/medi/tutum/ares/service/domains/api/master_data_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/pkg/okv"
	version_info_common "git.tutum.dev/medi/tutum/ares/service/domains/version_info/common"
	"git.tutum.dev/medi/tutum/ares/share/config"
	"github.com/pkg/errors"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
)

const (
	ONE_CLICK = "1CLICK"
	HKS       = "HKS"
)

var (
	//go:embed kvca_V1.20_1.2.276.0.76.3.1.64_tf+2025q3_nr+2.xml
	kvcaProdB []byte
	//go:embed kvca_V1.20_1.2.276.0.76.3.1.64_tf.xml
	kvcaTestB []byte
)

type (
	OneClick struct {
		KvMail       string
		KvTestMail   string
		KimEmail     string
		KimTestEmail string
	}
	EHKS struct {
		KimEmail     string
		KimTestEmail string
		KvEmail      string
		KvTestEmail  string
	}
)

type GetSdkvcaDataFunc func(ctx *titan.Context) (*SDKVCAData, error)
type SDKVCAData struct {
	mapBSNRWithOneClick map[string]OneClick
	mapBSNRWithEHKS     map[string]EHKS
	Version             version_info_common.VersionInfo
}

var GetSdkvcaDataFuncMod = submodule.Make[GetSdkvcaDataFunc](func(getRealTimeConfig config.GetRealtimeConfigFunc) GetSdkvcaDataFunc {
	return func(ctx *titan.Context) (*SDKVCAData, error) {
		realtimeConfig, err := getRealTimeConfig(ctx)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get realtime config")
		}

		b := kvcaProdB
		if realtimeConfig.SdkvcaTest {
			b = kvcaTestB
		}

		var model Ehd
		err = xml.Unmarshal(b, &model)
		if err != nil {
			panic(err)
		}
		sdkvcaData := &SDKVCAData{
			mapBSNRWithOneClick: make(map[string]OneClick),
			mapBSNRWithEHKS:     make(map[string]EHKS),
			Version: version_info_common.VersionInfo{
				Version:        model.Header.Interface.Version.V,
				Validity:       model.Header.ServiceTmr.V,
				MasterDataType: master_data_common.SDKVCA,
			},
		}

		for _, v := range model.Body.InstitutionListe.Kv {
			for _, kv := range v.Anwendungsliste.Anwendung {
				if kv.ID.EX == ONE_CLICK {
					sdkvcaData.mapBSNRWithOneClick[v.ID.EX] = OneClick{
						KvMail:       kv.Email.V,
						KvTestMail:   kv.TestEmail.V,
						KimEmail:     kv.KimEmail.V,
						KimTestEmail: kv.KimTestEmail.V,
					}
				}

				if kv.ID.EX == HKS {
					sdkvcaData.mapBSNRWithEHKS[v.ID.EX] = EHKS{
						KimEmail:     kv.KimEmail.V,
						KimTestEmail: kv.KimTestEmail.V,
						KvEmail:      kv.Email.V,
						KvTestEmail:  kv.TestEmail.V,
					}
				}
			}
		}

		return sdkvcaData, nil
	}
}, config.GetRealtimeConfigFuncMod)

func getMail[T any](bsnr string, mapData map[string]T) (*T, error) {
	if len(bsnr) < 2 {
		return nil, errors.New("bsnr is not valid")
	}

	okvValue, err := okv.GetOkvByBsnr(bsnr)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get okv value")
	}

	if okvValue == "" {
		return nil, errors.New("okv is not valid")
	}

	data, ok := mapData[okvValue]
	if !ok {
		return nil, nil
	}

	return &data, nil
}

func (r *SDKVCAData) GetOneClickMail(bsnr string) (*OneClick, error) {
	return getMail(bsnr, r.mapBSNRWithOneClick)
}

func (r *SDKVCAData) GetHksMail(bsnr string) (*EHKS, error) {
	return getMail(bsnr, r.mapBSNRWithEHKS)
}
