package patient

import (
	"emperror.dev/errors"
	mongodb "git.tutum.dev/medi/tutum/ares/pkg/repo"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	"go.mongodb.org/mongo-driver/bson"

	patient_profile_common "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient"

	goff "gitlab.com/silenteer-oss/goff"
	"gitlab.com/silenteer-oss/titan"
)

var _ goff.UUID

type PatientProfile struct {
	Id                           *uuid.UUID                                 `bson:"_id"`
	FirstName                    string                                     `bson:"firstName"`
	LastName                     string                                     `bson:"lastName"`
	DateOfBirth                  int64                                      `bson:"dateOfBirth"`
	Email                        string                                     `bson:"email"`
	ListHpmInformation           []*HpmInformation                          `bson:"listHpmInformation,omitempty"`
	PatientMedicalData           *patient_profile_common.PatientMedicalData `bson:"patientMedicalData"`
	PatientInfo                  *patient_profile_common.PatientInfo        `bson:"patientInfo"`
	CreatedAt                    int64                                      `bson:"createdAt"`
	UpdatedAt                    *int64                                     `bson:"updatedAt"`
	CreatedBy                    *uuid.UUID                                 `bson:"createdBy"`
	UpdatedBy                    *uuid.UUID                                 `bson:"updatedBy"`
	EmploymentInfoUpdatedAt      *int64                                     `bson:"employmentInfoUpdatedAt"`
	MedicalDataUpdatedAt         *int64                                     `bson:"medicalDataUpdatedAt"`
	repos.PatientFieldNameGetter `bson:"-"`
	repos.BsnrGetterSetter       `bson:"-"`
}

func (p PatientProfile) GetId() *uuid.UUID {
	return p.Id
}

type HpmInformation struct {
	CheckedDate int64                `bson:"checkedDate"`
	ContractId  string               `bson:"contractId"`
	Status      HpmInformationStatus `bson:"status"`
}

// Enum definitions
type WorkActivity1 string

const (
	Physical WorkActivity1 = "Physical"
	Mental   WorkActivity1 = "Mental"
)

type WorkActivity2 string

const (
	Standing WorkActivity2 = "Standing"
	Sitting  WorkActivity2 = "Sitting"
)

type IsEmployeeAnswer string

const (
	No  IsEmployeeAnswer = "No"
	Yes IsEmployeeAnswer = "Yes"
)

type HpmInformationStatus string

const (
	InActive HpmInformationStatus = "InActive"
	Active   HpmInformationStatus = "Active"
)

// Model fields definitions
const (
	Field_Id                        = "_id"
	Field_FirstName                 = "firstName"
	Field_LastName                  = "lastName"
	Field_DateOfBirth               = "dateOfBirth"
	Field_Email                     = "email"
	Field_ListHpmInformation        = "listHpmInformation"
	Field_PatientMedicalData        = "patientMedicalData"
	Field_PatientMedicalData_Height = "patientMedicalData.height"
	Field_PatientMedicalData_Weight = "patientMedicalData.weight"
	Field_PatientInfo               = "patientInfo"
	Field_CreatedAt                 = "createdAt"
	Field_UpdatedAt                 = "updatedAt"
	Field_CreatedBy                 = "createdBy"
	Field_UpdatedBy                 = "updatedBy"
	Field_EmploymentInfoUpdatedAt   = "employmentInfoUpdatedAt"
	Field_MedicalDataUpdatedAt      = "medicalDataUpdatedAt"
	Field_CollectionSequenceNumber  = "collectionSequenceNumber"
)
const (
	Field_HpmInformation_CheckedDate = "checkedDate"
	Field_HpmInformation_ContractId  = "contractId"
	Field_HpmInformation_Status      = "status"
)

// Repo definitions
type (
	PatientProfileDefaultRepository struct {
		mongodb.Repo[*PatientProfile]
	}
	DuplicatePatientInfo struct {
		PatientId       uuid.UUID `bson:"_id" json:"patientId"`
		FirstName       string    `bson:"firstName" json:"firstName"`
		LastName        string    `bson:"lastName" json:"lastName"`
		DateOfBirth     int64     `bson:"dateOfBirth" json:"dateOfBirth"`
		InsuranceNumber string    `bson:"insuranceNumber" json:"insuranceNumber"`
		PatientType     string    `bson:"patientType" json:"patientType"`
	}
	FindDuplicatePatientsRequest struct {
		PatientIds []uuid.UUID `validate:"required"`
	}
)

var PatientProfileDefaultRepositoryMod = submodule.Make[PatientProfileDefaultRepository](NewPatientProfileDefaultRepository)

func NewPatientProfileDefaultRepository() PatientProfileDefaultRepository {
	return PatientProfileDefaultRepository{
		mongodb.NewRepo[*PatientProfile](mongodb_repo.NewMongoDbIDBClient("tutum_profile_db", "patient_profile", true)),
	}
}

func (r PatientProfileDefaultRepository) FindDuplicatePatients(ctx *titan.Context, request *FindDuplicatePatientsRequest) ([]DuplicatePatientInfo, error) {
	firstNameField := Field_FirstName
	lastNameField := Field_LastName
	dateOfBirthField := Field_DateOfBirth

	matchStep := bson.M{
		repos.Field_IsDeleted: bson.M{
			"$ne": true,
		},
	}

	if len(request.PatientIds) > 0 {
		matchStep["_id"] = bson.M{
			"$in": request.PatientIds,
		}
	}

	pipeline := []bson.M{
		// Unwind the insurance infos array to work with individual insurance records
		{
			"$unwind": "$" + Field_PatientInfo,
		},
		// Filter out documents with empty or null insurance numbers
		{
			"$match": matchStep,
		},
		// Group by insurance number, first name, and last name (excluding dateOfBirth from grouping)
		{
			"$group": bson.M{
				"_id": bson.M{
					"firstName":   "$" + firstNameField,
					"lastName":    "$" + lastNameField,
					"dateOfBirth": "$" + dateOfBirthField,
				},
				"patientIds": bson.M{
					"$addToSet": "$" + patient.Field_Id,
				},
			},
		},
		// Filter groups that have more than one patient (duplicates) - fixed syntax
		{
			"$match": bson.M{
				"$expr": bson.M{
					"$gt": []any{
						bson.M{"$size": "$patientIds"},
						1,
					},
				},
			},
		},
		// Unwind patient IDs to get individual patient documents
		{
			"$unwind": "$patientIds",
		},
		// Lookup the full patient documents
		{
			"$lookup": bson.M{
				"from":         r.MustGetCollectionName(ctx),
				"localField":   "patientIds",
				"foreignField": Field_Id,
				"as":           "patient",
			},
		},
		// Unwind the patient array (should contain exactly one document)
		{
			"$unwind": "$patient",
		},
		// Replace root with selected patient fields
		{
			"$replaceRoot": bson.M{
				"newRoot": bson.M{
					"_id":         "$patient._id",
					"firstName":   "$patient.firstName",
					"lastName":    "$patient.lastName",
					"dateOfBirth": "$patient.dateOfBirth",
					"patientType": "$patient.patientInfo.genericinfo.patienttype",
				},
			},
		},
	}

	var results []DuplicatePatientInfo
	err := r.IDBClient.Aggregate(ctx, pipeline, &results)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to find patients with first name, last name and date of birth")
	}

	return results, nil
}
