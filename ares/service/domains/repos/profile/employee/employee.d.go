// This code was autogenerated from repo/profile/employee.proto, do not edit.

package employee

import (
	"errors"
	mongodb "git.tutum.dev/medi/tutum/ares/pkg/repo"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	"github.com/google/uuid"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
	"strings"
	"time"

	admin_bff "git.tutum.dev/medi/tutum/ares/app/admin/api/admin_bff"

	common "git.tutum.dev/medi/tutum/ares/service/domains/api/common"

	patient_profile_common "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"

	goff "gitlab.com/silenteer-oss/goff"
)

var _ goff.UUID
var _ time.Time

// Model definitions
type EmployeeProfile struct {
	Id                     *uuid.UUID                             `bson:"_id"`
	FirstName              string                                 `bson:"firstName" validate:"trim"`
	LastName               string                                 `bson:"lastName" validate:"trim"`
	Dob                    *int64                                 `bson:"dob,omitempty"`
	Salutation             *patient_profile_common.Salutation     `bson:"salutation,omitempty"`
	Phone                  *string                                `bson:"phone,omitempty"`
	ZipCode                string                                 `bson:"zipCode,omitempty"`
	Street                 string                                 `bson:"street,omitempty"`
	Email                  *string                                `bson:"email,omitempty"`
	Fax                    string                                 `bson:"fax,omitempty"`
	MobilePhone            string                                 `bson:"mobilePhone,omitempty"`
	Address                *string                                `bson:"address,omitempty"`
	Title                  *string                                `bson:"title"`
	Lanr                   *string                                `bson:"lanr"`
	Bsnr                   string                                 `bson:"bsnr"`
	HavgId                 *string                                `bson:"havgId"`
	HavgVpId               *string                                `bson:"havgVpId"`
	MediverbundId          *string                                `bson:"mediverbundId"`
	MediverbundVpId        *string                                `bson:"mediverbundVpId"`
	AreaOfExpertise        *[]string                              `bson:"areaOfExpertise"`
	CreatedDate            int64                                  `bson:"createdDate"`
	Okv                    *string                                `bson:"okv"`
	HasHzvContracts        bool                                   `bson:"hasHzvContracts"`
	HasFavContracts        bool                                   `bson:"hasFavContracts"`
	AdditionalName         *patient_profile_common.AdditionalName `bson:"additionalName"`
	IntendWord             *patient_profile_common.IntendWord     `bson:"intendWord"`
	Initial                string                                 `bson:"initial"`
	DmpPrograms            *[]string                              `bson:"dmpPrograms"`
	JobDescription         *string                                `bson:"jobDescription"`
	MarkAsBillingDoctor    bool                                   `bson:"markAsBillingDoctor"`
	BsnrId                 *uuid.UUID                             `bson:"bsnrId"`
	PseudoLanr             *string                                `bson:"pseudoLanr"`
	TeamNumbers            *[]string                              `bson:"teamNumbers"`
	DoctorStamp            string                                 `bson:"doctorStamp"`
	BsnrPracticeStamp      string                                 `bson:"bsnrPracticeStamp"`
	BankInformations       []*common.BankInformation              `bson:"bankInformations"`
	MarkAsEmployedDoctor   bool                                   `bson:"markAsEmployedDoctor"`
	ResponsibleDoctorId    *uuid.UUID                             `bson:"responsibleDoctorId"`
	RepresentativeDoctorId *uuid.UUID                             `bson:"representativeDoctorId"`
	BsnrName               *string                                `bson:"bsnrName"`
	BsnrStreet             string                                 `bson:"bsnrStreet"`
	BsnrNumber             string                                 `bson:"bsnrNumber"`
	BsnrPostCode           string                                 `bson:"bsnrPostCode"`
	BsnrPhoneNumber        string                                 `bson:"bsnrPhoneNumber"`
	BsnrFaxNumber          string                                 `bson:"bsnrFaxNumber"`
	ExternalId             string                                 `bson:"externalId"`
	IsParticipationActive  bool                                   `bson:"isParticipationActive"`
	BsnrFacilityType       string                                 `bson:"bsnrFacilityType"`
	Types                  []common.UserType                      `bson:"types"`
	DeviceId               *uuid.UUID                             `bson:"deviceId"`
	UserName               string                                 `bson:"userName"`
	HpmEndpoint            string                                 `bson:"hpmEndpoint"`
	HzvContracts           []*admin_bff.Contract                  `bson:"hzvContracts"`
	FavContracts           []*admin_bff.Contract                  `bson:"favContracts"`
	BsnrIds                []uuid.UUID                            `bson:"bsnrIds"`
	Bsnrs                  []string                               `bson:"bsnrs"`
	EHKSType               *common.EHKSType                       `bson:"eHKSType"`
	IsDoctor               bool                                   `bson:"isDoctor"`
}

type Contract struct {
	ContractId string `bson:"contractId"`
	StartDate  int64  `bson:"startDate"`
	EndDate    *int64 `bson:"endDate"`
}

// Enum definitions

// Model fields definitions
const (
	Field_Id                     = "_id"
	Field_FirstName              = "firstName"
	Field_LastName               = "lastName"
	Field_Dob                    = "dob"
	Field_Salutation             = "salutation"
	Field_Phone                  = "phone"
	Field_ZipCode                = "zipCode"
	Field_Street                 = "street"
	Field_Email                  = "email"
	Field_Fax                    = "fax"
	Field_MobilePhone            = "mobilePhone"
	Field_Address                = "address"
	Field_Title                  = "title"
	Field_Lanr                   = "lanr"
	Field_Bsnr                   = "bsnr"
	Field_HavgId                 = "havgId"
	Field_HavgVpId               = "havgVpId"
	Field_MediverbundId          = "mediverbundId"
	Field_MediverbundVpId        = "mediverbundVpId"
	Field_AreaOfExpertise        = "areaOfExpertise"
	Field_CreatedDate            = "createdDate"
	Field_Okv                    = "okv"
	Field_HasHzvContracts        = "hasHzvContracts"
	Field_HasFavContracts        = "hasFavContracts"
	Field_AdditionalName         = "additionalName"
	Field_IntendWord             = "intendWord"
	Field_Initial                = "initial"
	Field_DmpPrograms            = "dmpPrograms"
	Field_JobDescription         = "jobDescription"
	Field_MarkAsBillingDoctor    = "markAsBillingDoctor"
	Field_BsnrId                 = "bsnrId"
	Field_PseudoLanr             = "pseudoLanr"
	Field_TeamNumbers            = "teamNumbers"
	Field_DoctorStamp            = "doctorStamp"
	Field_BsnrPracticeStamp      = "bsnrPracticeStamp"
	Field_BankInformations       = "bankInformations"
	Field_MarkAsEmployedDoctor   = "markAsEmployedDoctor"
	Field_ResponsibleDoctorId    = "responsibleDoctorId"
	Field_RepresentativeDoctorId = "representativeDoctorId"
	Field_BsnrName               = "bsnrName"
	Field_BsnrStreet             = "bsnrStreet"
	Field_BsnrNumber             = "bsnrNumber"
	Field_BsnrPostCode           = "bsnrPostCode"
	Field_BsnrPhoneNumber        = "bsnrPhoneNumber"
	Field_BsnrFaxNumber          = "bsnrFaxNumber"
	Field_ExternalId             = "externalId"
	Field_IsParticipationActive  = "isParticipationActive"
	Field_BsnrFacilityType       = "bsnrFacilityType"
	Field_Types                  = "types"
	Field_DeviceId               = "deviceId"
	Field_UserName               = "userName"
	Field_HpmEndpoint            = "hpmEndpoint"
	Field_HzvContracts           = "hzvContracts"
	Field_FavContracts           = "favContracts"
	Field_BsnrIds                = "bsnrIds"
	Field_Bsnrs                  = "bsnrs"
	Field_EHKSType               = "eHKSType"
	Field_IsDoctor               = "isDoctor"
)
const (
	Field_Contract_ContractId = "contractId"
	Field_Contract_StartDate  = "startDate"
	Field_Contract_EndDate    = "endDate"
)

// Repo definitions
type EmployeeProfileDefaultRepository struct {
	Db mongodb.IDBClient
}

func NewEmployeeProfileDefaultRepository() EmployeeProfileDefaultRepository {
	return EmployeeProfileDefaultRepository{
		Db: mongodb_repo.NewMongoDbIDBClient("tutum_profile_db", "employee_profile", true),
	}
}

func (r *EmployeeProfileDefaultRepository) Find(ctx *titan.Context, filter interface{}, opts ...*options.FindOptions) ([]EmployeeProfile, error) {
	var data []EmployeeProfile
	err := r.Db.Find(ctx, filter, &data, opts...)
	if err != nil {
		return []EmployeeProfile{}, err
	}
	return data, nil
}

func (r *EmployeeProfileDefaultRepository) FindOne(ctx *titan.Context, filter interface{}, opts ...*options.FindOneOptions) (*EmployeeProfile, error) {
	var data EmployeeProfile
	err := r.Db.FindOne(ctx, filter, &data, opts...)
	if err != nil {
		return nil, err
	}
	if data.Id != nil {
		return &data, nil
	}
	return nil, nil
}

func (r *EmployeeProfileDefaultRepository) Aggregate(
	ctx *titan.Context,
	filter interface{},
	opts ...*options.AggregateOptions) ([]EmployeeProfile, error) {

	var entities []EmployeeProfile
	err := r.Db.Aggregate(ctx, filter, &entities)

	if err != nil {
		return []EmployeeProfile{}, err
	}

	return entities, nil
}

func (r *EmployeeProfileDefaultRepository) FindById(ctx *titan.Context, uuid uuid.UUID) (*EmployeeProfile, error) {
	filter := bson.D{{
		Key:   Field_Id,
		Value: uuid,
	}}

	data, err := r.Find(ctx, filter)
	if err != nil {
		return nil, err
	}

	if len(data) == 0 {
		return nil, nil
	}

	return &data[0], nil
}

func (r *EmployeeProfileDefaultRepository) GetById(ctx *titan.Context, uuid uuid.UUID) (*EmployeeProfile, error) {
	data, err := r.FindById(ctx, uuid)
	if err != nil {
		return nil, err
	}

	if data == nil {
		return nil, titan.NewRecordNotFoundException("EmployeeProfile", uuid.String(), "NOT_FOUND_RECORD")
	}
	return data, nil
}

func (r *EmployeeProfileDefaultRepository) GetByIds(ctx *titan.Context, uuids []uuid.UUID) (data []EmployeeProfile, err error) {
	filter := bson.D{
		{
			Key: Field_Id,
			Value: bson.D{
				{
					Key:   "$in",
					Value: uuids,
				},
			},
		},
	}
	err = r.Db.Find(ctx, filter, &data)
	return data, err
}

func (r *EmployeeProfileDefaultRepository) Create(ctx *titan.Context, request EmployeeProfile) (*EmployeeProfile, error) {
	var data EmployeeProfile
	err := r.Db.Create(ctx, request, &data)
	if err != nil {
		return nil, err
	}
	return &data, nil
}

func (r *EmployeeProfileDefaultRepository) CreateMany(ctx *titan.Context, requests []EmployeeProfile) ([]EmployeeProfile, error) {
	var data []EmployeeProfile
	err := r.Db.Create(ctx, requests, &data)
	if err != nil {
		return make([]EmployeeProfile, 0), err
	}
	return data, nil
}

func (r *EmployeeProfileDefaultRepository) Delete(ctx *titan.Context, filter interface{}) (int64, error) {
	res, err := r.Db.Delete(ctx, filter)

	if err != nil {
		return 0, err
	}

	return res.DeletedCount, nil
}

func (r *EmployeeProfileDefaultRepository) DeleteById(ctx *titan.Context, id uuid.UUID) (bool, error) {
	filter := bson.D{{Key: Field_Id, Value: id}}
	res, err := r.Delete(ctx, filter)
	if err != nil {
		return false, err
	}
	if res != 1 {
		return false, errors.New("delete_failed")
	}

	return true, nil
}

func (r *EmployeeProfileDefaultRepository) FindOneAndUpdate(ctx *titan.Context, filter interface{}, update interface{}, opts ...*options.FindOneAndUpdateOptions) (*EmployeeProfile, error) {
	var entity EmployeeProfile
	err := r.Db.FindOneAndUpdate(ctx, filter, update, &entity, opts...)
	if err != nil {
		return nil, err
	}
	return &entity, nil
}

func (r *EmployeeProfileDefaultRepository) FindOneAndReplace(ctx *titan.Context, filter interface{}, replacement EmployeeProfile, opts ...*options.FindOneAndReplaceOptions) (*EmployeeProfile, error) {
	var entity EmployeeProfile
	err := r.Db.FindOneAndReplace(ctx, filter, replacement, &entity, opts...)
	if err != nil {
		return nil, err
	}
	return &entity, nil
}

func (r *EmployeeProfileDefaultRepository) Update(ctx *titan.Context, update EmployeeProfile) (*EmployeeProfile, error) {
	var entity EmployeeProfile
	err := r.Db.Update(ctx, update, &entity)
	if err != nil {
		return nil, err
	}
	return &entity, nil
}

func (r *EmployeeProfileDefaultRepository) UpdateMany(ctx *titan.Context, update []EmployeeProfile) ([]EmployeeProfile, error) {
	var entities []EmployeeProfile
	err := r.Db.Update(ctx, update, &entities)
	if err != nil {
		return nil, err
	}
	return entities, nil
}

func (r *EmployeeProfileDefaultRepository) Upsert(ctx *titan.Context, request EmployeeProfile) (*EmployeeProfile, error) {
	filter := bson.D{{Key: Field_Id, Value: request.Id}}
	opts := options.FindOneAndReplace().SetReturnDocument(options.After).SetUpsert(true)

	entity, err := r.FindOneAndReplace(ctx, filter, request, opts)

	if err != nil {
		if isDupkeyErr(err) {
			return r.FindById(ctx, *request.Id)
		}
		return nil, err
	}

	if entity.Id == nil {
		return nil, errors.New("no_response_data")
	}

	return entity, nil
}

func (r *EmployeeProfileDefaultRepository) Count(
	ctx *titan.Context,
	filter interface{}) (int64, error) {

	response, err := r.Db.Count(ctx, filter)

	if err != nil {
		return 0, err
	}

	return response.Count, nil
}

func (r *EmployeeProfileDefaultRepository) GetCollectionName(ctx *titan.Context) (string, error) {
	response, err := r.Db.GetCollectionName(ctx)

	if err != nil {
		return "", err
	}

	return response, nil
}

func isDupkeyErr(err error) bool {
	err = errors.Unwrap(err)
	_err, ok := err.(*titan.ClientResponseError)
	if !ok {
		return false
	}
	if !strings.Contains(string(_err.Response.Body), "index: _id_ dup key:") {
		return false
	}

	return true
}
