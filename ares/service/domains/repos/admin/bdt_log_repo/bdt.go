package bdt_log_repo

import (
	"git.tutum.dev/medi/tutum/ares/pkg/operator"
	mongodb "git.tutum.dev/medi/tutum/ares/pkg/repo"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	bdt_model "git.tutum.dev/medi/tutum/ares/pkg/xdt/bdt/model"
	"git.tutum.dev/medi/tutum/ares/service/domains/bdt_log/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
)

const (
	Field_Id          = "_id"
	Field_Status      = "status"
	Field_TotalRaws   = "totalraws"
	Field_ReadRaws    = "readraws"
	Field_Percent     = "percent"
	Field_DataContext = "dataContext"
)

type (
	BdtDataContext struct {
		HeaderData       *bdt_model.BDTHeader                 `json:"headerData"`
		ScheinMap        map[string][]bdt_model.Schein        `json:"scheinMap"`
		PrivateScheinMap map[string][]bdt_model.PrivateSchein `json:"privateScheinMap"`
		BGScheinMap      map[string][]bdt_model.BGSchein      `json:"bgScheinMap"`
		Employees        []bdt_model.BDTEmployee              `json:"employees"`
		PatientIdMap     map[string]*uuid.UUID                `json:"patientIdMap"`
		ScheinIdMap      map[string]*uuid.UUID                `json:"scheinIdMap"`
		ScheinNumberMap  map[string][]string                  `json:"scheinNumberMap"`
	}
	BdtLogEntity struct {
		repos.BaseEntity   `bson:",inline"`
		common.BdtLogModel `bson:",inline"`
		DataContext        *BdtDataContext `bson:"dataContext,omitempty"`
	}
	BdtLogRepo struct {
		mongodb.Repo[*BdtLogEntity]
	}
)

var BdtLogRepoMod = submodule.Make[*BdtLogRepo](func() *BdtLogRepo {
	return &BdtLogRepo{
		mongodb.NewRepo[*BdtLogEntity](mongodb_repo.NewMongoDbIDBClient("tutum_admin_db", "bdt_log", true)),
	}
})

func (r *BdtLogRepo) GetCurrentProcess(ctx *titan.Context) (*BdtLogEntity, error) {
	return r.FindOne(ctx, bson.M{
		Field_Status: common.Status_Processing,
	})
}

func (r *BdtLogRepo) Create(ctx *titan.Context, model common.BdtLogModel) (*BdtLogEntity, error) {
	id := uuid.New()
	now := util.Now(ctx)

	entity := BdtLogEntity{
		BaseEntity: repos.BaseEntity{
			Id:        &id,
			CreatedAt: now,
		},
		BdtLogModel: model,
	}
	return r.Repo.Create(ctx, &entity)
}

func (r *BdtLogRepo) UpdateTotalRaws(ctx *titan.Context, totalRaws int64) (*BdtLogEntity, error) {
	return r.FindOneAndUpdate(ctx, bson.M{
		Field_Status: common.Status_Processing,
	}, bson.M{
		operator.Set: bson.M{
			Field_TotalRaws:       totalRaws,
			repos.Field_UpdatedAt: util.Now(ctx),
		},
	})
}

func (r *BdtLogRepo) UpdateDataContext(ctx *titan.Context, dataContext *BdtDataContext, readRaws int64) (*BdtLogEntity, error) {
	return r.FindOneAndUpdate(ctx, bson.M{
		Field_Status: common.Status_Processing,
	}, bson.M{
		operator.Set: bson.M{
			Field_DataContext:     dataContext,
			Field_ReadRaws:        readRaws,
			repos.Field_UpdatedAt: util.Now(ctx),
		},
	})
}

func (r *BdtLogRepo) MarkAsDone(ctx *titan.Context) (*BdtLogEntity, error) {
	return r.FindOneAndUpdate(ctx, bson.M{
		Field_Status: common.Status_Processing,
	}, bson.M{
		operator.Set: bson.M{
			Field_DataContext:     nil,
			Field_Status:          common.Status_Success,
			repos.Field_IsDeleted: true,
			repos.Field_UpdatedAt: util.Now(ctx),
		},
	})
}
