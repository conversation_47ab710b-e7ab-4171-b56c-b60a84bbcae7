package bdt_log_repo

import (
	bdt_model "git.tutum.dev/medi/tutum/ares/pkg/xdt/bdt/model"
	"github.com/google/uuid"

	"golang.org/x/text/transform"
)

func (bdLog *BdtLogEntity) ToBdtDataContext() (res *bdt_model.DataContext, readRaws int64) {
	if bdLog == nil || bdLog.DataContext == nil {
		return &bdt_model.DataContext{
			ScheinMap:        make(map[string][]bdt_model.Schein),
			PrivateScheinMap: make(map[string][]bdt_model.PrivateSchein),
			BGScheinMap:      make(map[string][]bdt_model.BGSchein),
			Config: bdt_model.Config{
				Decoder: transform.Nop,
			},
			ScheinIdMap:     make(map[string]*uuid.UUID),
			ScheinNumberMap: make(map[string][]string),
			PatientIdMap:    make(map[string]*uuid.UUID),
		}, 0
	}

	data := &bdt_model.DataContext{
		HeaderData:       bdLog.DataContext.HeaderData,
		ScheinMap:        bdLog.DataContext.ScheinMap,
		PrivateScheinMap: bdLog.DataContext.PrivateScheinMap,
		BGScheinMap:      bdLog.DataContext.BGScheinMap,
		Employees:        bdLog.DataContext.Employees,
		PatientIdMap:     bdLog.DataContext.PatientIdMap,
		ScheinNumberMap:  bdLog.DataContext.ScheinNumberMap,
		ScheinIdMap:      bdLog.DataContext.ScheinIdMap,
	}
	data.Config = data.HeaderData.SetUpConfig()
	return data, bdLog.ReadRaws
}
