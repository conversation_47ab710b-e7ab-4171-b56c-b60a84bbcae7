package combinator

import (
	bg_billing_repo "git.tutum.dev/medi/tutum/ares/service/domains/bg_billing/repo"
	private_billing_repo "git.tutum.dev/medi/tutum/ares/service/domains/private_billing/repo"
	mail_item_inbox_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mail/mail_item_inbox_repo"
	mail_item_sent_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mail/mail_item_sent_repo"
	daily_list_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/daily_list"
	document_management_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/document_management"
	eab_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/eab"
	eab_service_history_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/eab_service_history"
	eau_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/eau"
	edmp_encrollment_document_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/edmp/edmp_document"
	edmp_enrollment_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/edmp/edmp_enrollment"
	edoku_enrollment_document_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/edmp/edoku_document"
	patient_enrollment_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/enrollment"
	prescribed_heimi_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/heimi"
	himi_prescription_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/himi/himi_model/prescription"
	hpm_check_history_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/hpm_check_history"
	bmp_medication_plan_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/bmp"
	prescribed_medication_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/prescribed"
	medicine_prescription_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/prescription"
	medicine_shoppingbag_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/shoppingbag"
	patient_bill_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_bill"
	patient_log_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_log"
	schein_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	viewed_patient_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/viewed_patient"
	waiting_room_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/waiting_room"
	patient_medical_history_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient_medical_history"
	timeline_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
)

type MapCollectionWithPatientId struct {
	CollectionName  string
	Field_PatientId string
}

type MapCollectionWithPatientIds struct {
	CollectionName   string
	Field_PatientIds string
}

type MapCollectionWithPatientNumber struct {
	CollectionName      string
	Field_PatientNumber string
	Field_PatientId     string
}

type MapCollectionWithPatientInfo struct {
	CollectionName    string
	Field_PatientInfo string
	Field_PatientId   string
}

var (
	collectionNameWithPatientId = []MapCollectionWithPatientId{
		{
			CollectionName:  "timeline",
			Field_PatientId: timeline_repo.Field_PatientId,
		},
		{
			CollectionName:  "patient_medical_history",
			Field_PatientId: patient_medical_history_repo.Field_PatientId,
		},
		{
			CollectionName:  "prescribed_medication",
			Field_PatientId: prescribed_medication_repo.Field_PatientId,
		},
		{
			CollectionName:  "prescribed_heimi",
			Field_PatientId: prescribed_heimi_repo.Field_PatientId,
		},
		{
			CollectionName:  "medicine_prescription",
			Field_PatientId: medicine_prescription_repo.Field_PatientId,
		},
		{
			CollectionName:  "viewed_patient",
			Field_PatientId: viewed_patient_repo.Field_PatientId,
		},
		{
			CollectionName:  "bmp_medication_plan",
			Field_PatientId: bmp_medication_plan_repo.Field_PatientId,
		},
		{
			CollectionName:  "hpm_check_history",
			Field_PatientId: hpm_check_history_repo.Field_PatientId,
		},
		{
			CollectionName:  "mail_item_inbox",
			Field_PatientId: mail_item_inbox_repo.Field_PatientId,
		},
		{
			CollectionName:  "mail_item_sent",
			Field_PatientId: mail_item_sent_repo.Field_PatientId,
		},
		{
			CollectionName:  "medicine_shoppingbag",
			Field_PatientId: medicine_shoppingbag_repo.Field_PatientId,
		},
		{
			CollectionName:  "patient_enrollment",
			Field_PatientId: patient_enrollment_repo.Field_PatientId,
		},
		{
			CollectionName:  "document_management",
			Field_PatientId: document_management_repo.Field_Patient_Id,
		},
		{
			CollectionName:  "himi_prescription",
			Field_PatientId: himi_prescription_repo.Field_PatientId,
		},
		{
			CollectionName:  "edmp_enrollment_document",
			Field_PatientId: edmp_encrollment_document_repo.Field_PatientId,
		},
		{
			CollectionName:  "edmp_enrollment",
			Field_PatientId: edmp_enrollment_repo.Field_PatientId,
		},
		{
			CollectionName:  "edoku_enrollment_document",
			Field_PatientId: edoku_enrollment_document_repo.Field_PatientId,
		},
		{
			CollectionName:  "patient_log",
			Field_PatientId: patient_log_repo.Field_PatientId,
		},
		{
			CollectionName:  "eab_service_history",
			Field_PatientId: eab_service_history_repo.Field_PatientId,
		},
		{
			CollectionName:  "schein",
			Field_PatientId: schein_repo.Field_PatientId,
		},
	}
	collectionNameWithPatientIds = []MapCollectionWithPatientIds{
		{
			CollectionName:   "waiting_room",
			Field_PatientIds: waiting_room_repo.Field_Patient_Ids,
		},
	}
	collectionNameWithPatientNumber = []MapCollectionWithPatientNumber{
		{
			CollectionName:      "bg_billing_history",
			Field_PatientNumber: bg_billing_repo.Field_Patient_Number,
			Field_PatientId:     bg_billing_repo.Field_Patient_PatientId,
		},
		{
			CollectionName:      "private_billing",
			Field_PatientNumber: private_billing_repo.Field_Patient_PatientNumber,
			Field_PatientId:     private_billing_repo.Field_Patient_PatientId,
		},
	}
	collectionNameWithPatientInfo = []MapCollectionWithPatientInfo{
		{
			CollectionName:    "daily_list",
			Field_PatientInfo: daily_list_repo.Field_PatientInfo,
			Field_PatientId:   daily_list_repo.Field_PatientId,
		},
		{
			CollectionName:    "eau",
			Field_PatientInfo: eau_repo.Field_PatientInfo,
			Field_PatientId:   eau_repo.Field_PatientId,
		},
		{
			CollectionName:    "patient_bill",
			Field_PatientInfo: patient_bill_repo.Field_PatientInfo,
			Field_PatientId:   patient_bill_repo.Field_PatientId,
		},
		{
			CollectionName:    "eab",
			Field_PatientInfo: eab_repo.Field_PatientProfile_Id,
			Field_PatientId:   eab_repo.Field_PatientProfile_PatientInfo,
		},
	}
)
