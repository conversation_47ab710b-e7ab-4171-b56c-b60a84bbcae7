package combinator

import (
	"fmt"
	"strconv"
	"sync"

	"emperror.dev/errors"
	patient_profile_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_profile"
	"git.tutum.dev/medi/tutum/ares/app/mvz/patient_profile"
	"git.tutum.dev/medi/tutum/ares/pkg/repo/mongodb_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common/patientfile"
	patient_service "git.tutum.dev/medi/tutum/ares/service/domains/api/patient"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/app"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	schein_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	Field_PatientProfile_PatientInfo = "patientprofile.patientinfo"
	Field_PatientId                  = "patientId"
)

type (
	Combinator struct {
		scheinRepo           schein_repo.ScheinRepoDefaultRepository
		patientStatusService patient_service.PatientService
		mongoClient          *mongo.Client
		patientProfileRepo   *patient_profile.PatientProfileRepository
	}
	CombineRequest struct {
		TargetPatientId      uuid.UUID   `validate:"required"`
		DuplicatedPatientIds []uuid.UUID `validate:"required"`
	}
)

var CombinatorMod = submodule.Make[*Combinator](func(mongoClient *mongo.Client, patientProfileRepo *patient_profile.PatientProfileRepository, scheinRepo schein_repo.ScheinRepoDefaultRepository, patientStatusService patient_service.PatientService) *Combinator {
	return &Combinator{
		mongoClient:          mongoClient,
		patientProfileRepo:   patientProfileRepo,
		scheinRepo:           scheinRepo,
		patientStatusService: patientStatusService,
	}
}, mongodb_repo.MongoDbClientMod, patient.PatientProfileDefaultRepositoryMod, patient_profile.PatientProfileRepositoryMod, schein_repo.ScheinRepoMod, app.PatientServiceMod)

func (c *Combinator) CombinePatients(ctx *titan.Context, req *CombineRequest) error {
	duplicatedPatientIds := req.DuplicatedPatientIds
	if !slice.Contain(duplicatedPatientIds, req.TargetPatientId) {
		duplicatedPatientIds = append(duplicatedPatientIds, req.TargetPatientId)
	}

	if len(duplicatedPatientIds) < 2 {
		return errors.New("must have at least 2 patients to combine")
	}

	return c.doCombine(ctx, req.TargetPatientId, duplicatedPatientIds)
}

func (c *Combinator) createNewPatient(ctx *titan.Context, targetPatientId uuid.UUID, oldPatientIds []uuid.UUID) (*patient_profile_api.CreatePatientProfileV2Response, error) {
	filter := bson.D{
		{
			Key: repos.Field_Id,
			Value: bson.D{
				{
					Key:   "$in",
					Value: oldPatientIds,
				},
			},
		},
	}
	patientProfiles := []patient.PatientProfile{}
	err := c.patientProfileRepo.IDBClient.Find(ctx, filter, &patientProfiles, &options.FindOptions{
		Sort: bson.D{
			{
				Key:   patient.Field_UpdatedAt,
				Value: -1, // Sort by updated at descending
			},
			{
				Key:   patient.Field_CreatedAt,
				Value: -1, // Sort by created at descending
			},
		},
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patient profiles")
	}

	patientInfo := patient_profile_common.PatientInfo{}
	insuranceInfos := []patient_profile_common.InsuranceInfo{}
	for _, patientProfile := range patientProfiles {
		insuranceInfos = append(insuranceInfos, patientProfile.PatientInfo.InsuranceInfos...)
		if patientProfile.Id != nil && patientProfile.Id.String() == targetPatientId.String() {
			patientInfo = *patientProfile.PatientInfo
			continue
		}
	}

	patient, err := c.patientStatusService.Create(ctx, &patient_service.PatientCreateRequest{
		Status: patientfile.Activate,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "error to create patient status")
	}

	if patient == nil || patient.Id == nil {
		return nil, errors.New("failed to create patient")
	}

	newPatientId := *patient.Id
	patientInfo.InsuranceInfos = []patient_profile_common.InsuranceInfo{}
	createdPatientInfo, err := c.patientProfileRepo.CreatePatientProfileV2(ctx, newPatientId, patientInfo)
	if err != nil {
		return nil, errors.WithMessage(err, "error to create patient profile")
	}

	if createdPatientInfo == nil {
		return nil, errors.New("failed to create patient profile")
	}

	if err := c.combineInsuranceInfo(ctx, newPatientId, oldPatientIds, insuranceInfos); err != nil {
		return nil, errors.WithMessage(err, "failed to combine insurance info")
	}

	return &patient_profile_api.CreatePatientProfileV2Response{
		Id:          &newPatientId,
		PatientInfo: createdPatientInfo,
	}, nil
}

func (c *Combinator) combineInsuranceInfo(ctx *titan.Context, newPatientId uuid.UUID, oldPatientIds []uuid.UUID, updatedInsuranceInfo []patient_profile_common.InsuranceInfo) error {
	if len(updatedInsuranceInfo) == 0 {
		return nil
	}

	mergedInsuranceInfos, mapNewInsurance := patient_profile_common.MergeInsuranceInfo(updatedInsuranceInfo)
	updatedInsuranceResponse, err := c.patientProfileRepo.IDBClient.UpdateMany(ctx,
		bson.M{
			patient.Field_Id: newPatientId,
		},
		bson.M{
			"$set": bson.M{fmt.Sprintf("%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_InsuranceInfos): mergedInsuranceInfos},
		},
		nil,
	)
	if err != nil {
		return errors.WithMessage(err, "failed to update patient profile")
	}

	if updatedInsuranceResponse == nil {
		return errors.New("updatedInsuranceResponse is empty")
	}

	if len(mapNewInsurance) == 0 {
		return nil
	}

	for newInsuranceId, oldInsuranceIds := range mapNewInsurance {
		for _, oldPatientId := range oldPatientIds {
			updatedScheinResponse, err := c.scheinRepo.Db.UpdateMany(ctx, bson.M{
				schein_repo.Field_PatientId: oldPatientId,
				schein_repo.Field_InsuranceId: bson.M{
					"$in": oldInsuranceIds,
				},
			}, bson.M{
				"$set": bson.M{
					schein_repo.Field_InsuranceId: newInsuranceId,
					schein_repo.Field_PatientId:   newPatientId,
				},
			},
				nil,
			)
			if err != nil {
				return errors.WithMessage(err, "failed to update schein entities")
			}

			if updatedScheinResponse == nil {
				return errors.New("updatedScheinResponse is empty")
			}
		}
	}

	return nil
}

func (*Combinator) combineDataWithPatientId(ctx *titan.Context, oldPatientIds []uuid.UUID, newPatientId uuid.UUID) error {
	for _, collection := range collectionNameWithPatientId {
		r := mongodb_repo.NewMongoDbIDBClient("tutum_mvz_db", collection.CollectionName, true)
		updateResponse, err := r.UpdateMany(ctx,
			bson.M{
				collection.Field_PatientId: bson.M{
					"$in": oldPatientIds,
				},
			},
			bson.M{
				"$set": bson.M{
					collection.Field_PatientId: newPatientId,
				},
			},
			nil,
		)
		if err != nil {
			return errors.WithMessage(err, fmt.Sprintf("failed to update %s", collection.CollectionName))
		}

		if updateResponse == nil {
			return errors.New(fmt.Sprintf("updateResponse is empty %s", collection.CollectionName))
		}
	}

	return nil
}

func (*Combinator) combineDataWithPatientIds(ctx *titan.Context, newPatientId uuid.UUID, oldPatientIds []uuid.UUID) error {
	for _, collection := range collectionNameWithPatientIds {
		r := mongodb_repo.NewMongoDbIDBClient("tutum_mvz_db", collection.CollectionName, true)

		// First pull out old patient IDs
		pullResponse, err := r.UpdateMany(ctx,
			bson.M{
				collection.Field_PatientIds: bson.M{
					"$in": oldPatientIds,
				},
			},
			bson.M{
				"$pull": bson.M{
					collection.Field_PatientIds: bson.M{
						"$in": oldPatientIds,
					},
				},
			},
			nil,
		)
		if err != nil {
			return errors.WithMessage(err, fmt.Sprintf("failed to pull old IDs from %s", collection.CollectionName))
		}
		if pullResponse == nil {
			return errors.New(fmt.Sprintf("pullResponse is empty %s", collection.CollectionName))
		}

		// Then push new patient ID
		pushResponse, err := r.UpdateMany(ctx,
			bson.M{
				collection.Field_PatientIds: bson.M{
					"$in": oldPatientIds,
				},
			},
			bson.M{
				"$push": bson.M{
					collection.Field_PatientIds: newPatientId,
				},
			},
			nil,
		)
		if err != nil {
			return errors.WithMessage(err, fmt.Sprintf("failed to push new ID to %s", collection.CollectionName))
		}
		if pushResponse == nil {
			return errors.New(fmt.Sprintf("pushResponse is empty %s", collection.CollectionName))
		}
	}

	return nil
}

func (*Combinator) combineDataWithPatientNumber(ctx *titan.Context, newPatientId uuid.UUID, oldPatientIds []uuid.UUID, patientNumber int32) error {
	patientNumberString := strconv.Itoa(int(patientNumber))
	for _, collection := range collectionNameWithPatientNumber {
		r := mongodb_repo.NewMongoDbIDBClient("tutum_mvz_db", collection.CollectionName, true)
		updateResponse, err := r.UpdateMany(ctx,
			bson.M{
				collection.Field_PatientId: bson.M{
					"$in": oldPatientIds,
				},
			},
			bson.M{
				"$set": bson.M{
					collection.Field_PatientId:     newPatientId,
					collection.Field_PatientNumber: patientNumberString,
				},
			},
			nil,
		)
		if err != nil {
			return errors.WithMessage(err, fmt.Sprintf("failed to update %s", collection.CollectionName))
		}

		if updateResponse == nil {
			return errors.New(fmt.Sprintf("updateResponse is empty %s", collection.CollectionName))
		}
	}

	return nil
}

func (*Combinator) combineDataWithPatientInfo(ctx *titan.Context, newPatientId uuid.UUID, oldPatientIds []uuid.UUID, newPatientInfo *patient_profile_common.PatientInfo) error {
	for _, collection := range collectionNameWithPatientInfo {
		r := mongodb_repo.NewMongoDbIDBClient("tutum_mvz_db", collection.CollectionName, true)
		updateResponse, err := r.UpdateMany(ctx,
			bson.M{
				collection.Field_PatientId: bson.M{
					"$in": oldPatientIds,
				},
			},
			bson.M{
				"$set": bson.M{
					collection.Field_PatientId:   newPatientId,
					collection.Field_PatientInfo: newPatientInfo,
				},
			},
			nil,
		)
		if err != nil {
			return errors.WithMessage(err, fmt.Sprintf("failed to update %s", collection.CollectionName))
		}

		if updateResponse == nil {
			return errors.New(fmt.Sprintf("updateResponse is empty %s", collection.CollectionName))
		}
	}

	return nil
}

func (c *Combinator) deletePatients(ctx *titan.Context, patientIds []uuid.UUID) error {
	updatedResponse, err := c.patientProfileRepo.IDBClient.UpdateMany(ctx,
		bson.M{
			patient.Field_Id: bson.M{
				"$in": patientIds,
			},
		},
		bson.M{
			"$set": bson.M{
				repos.Field_IsDeleted: true,
			},
		},
		nil,
	)
	if err != nil {
		return errors.WithMessage(err, "failed to soft delete patient")
	}

	if updatedResponse == nil || updatedResponse.ModifiedCount != int64(len(patientIds)) {
		return errors.New("updatedResponse.MatchedCount is not equal to len(patientIds)")
	}

	return nil
}

func (c *Combinator) doCombine(ctx *titan.Context, targetPatientId uuid.UUID, duplicatedPatientIds []uuid.UUID) error {
	if len(duplicatedPatientIds) < 2 {
		return errors.New("must have at least 2 patients to combine")
	}

	session, err := c.mongoClient.StartSession()
	if err != nil {
		return fmt.Errorf("failed to start new DB session: %w", err)
	}
	defer session.EndSession(ctx)

	callback := func(sessCtx mongo.SessionContext) (any, error) {
		ctx := titan.NewContext(sessCtx)

		patient, err := c.createNewPatient(ctx, targetPatientId, duplicatedPatientIds)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to create new patient")
		}

		if patient == nil {
			return nil, errors.New("failed to create new patient")
		}

		newPatientId := *patient.Id
		patientInfo := patient.PatientInfo
		patientNumber := patientInfo.PatientNumber

		wg := sync.WaitGroup{}
		errChan := make(chan error, 5)
		wg.Add(6)

		go func() {
			if err := c.combineInsuranceInfo(ctx, newPatientId, duplicatedPatientIds, patientInfo.InsuranceInfos); err != nil {
				errChan <- errors.WithMessage(err, "failed to combine insurance info")
			}
			defer wg.Done()
		}()

		go func() {
			if err := c.combineDataWithPatientId(ctx, duplicatedPatientIds, newPatientId); err != nil {
				errChan <- errors.WithMessage(err, "failed to combine data with patient id")
			}
			defer wg.Done()
		}()

		go func() {
			if err := c.combineDataWithPatientIds(ctx, newPatientId, duplicatedPatientIds); err != nil {
				errChan <- errors.WithMessage(err, "failed to combine data with patient ids")
			}
			defer wg.Done()
		}()

		go func() {
			if err := c.combineDataWithPatientNumber(ctx, newPatientId, duplicatedPatientIds, patientNumber); err != nil {
				errChan <- errors.WithMessage(err, "failed to combine data with patient number")
			}
			defer wg.Done()
		}()

		go func() {
			if err := c.combineDataWithPatientInfo(ctx, newPatientId, duplicatedPatientIds, patientInfo); err != nil {
				errChan <- errors.WithMessage(err, "failed to combine data with patient info")
			}
			defer wg.Done()
		}()

		go func() {
			if err := c.deletePatients(ctx, duplicatedPatientIds); err != nil {
				errChan <- errors.WithMessage(err, "failed to delete patients")
			}
			defer wg.Done()
		}()

		wg.Wait()

		select {
		case err := <-errChan:
			return nil, err
		default:
		}

		return nil, nil
	}

	if _, err := session.WithTransaction(ctx, callback); err != nil {
		return fmt.Errorf("failed to execute transaction: %w", err)
	}

	return nil
}
