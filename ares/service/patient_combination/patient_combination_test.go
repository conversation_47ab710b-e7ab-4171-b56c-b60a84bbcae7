package patient_combination_test

import (
	"fmt"
	"testing"

	ares_test "git.tutum.dev/medi/tutum/ares/pkg/test"
	"git.tutum.dev/medi/tutum/ares/pkg/test/data"
	"git.tutum.dev/medi/tutum/ares/pkg/test/fixtures"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/patient_combination/combinator"
	"git.tutum.dev/medi/tutum/ares/service/patient_combination/finder"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	fixtures2 "gitlab.com/silenteer-oss/hestia/test/fixtures"
)

func clonePatient(patient *data.Patient) *data.Patient {
	id := uuid.New()
	patientCopy := *patient
	patientCopy.Profile.Id = util.NewPointer(id)
	patientCopy.PatientId = id
	return &patientCopy
}

type PatientCombinationTestSuite struct {
	ares_test.WithDefaultInfraTestSuite
	mvzFixture    *fixtures2.CareProviderFixture
	doctor        *data.Doctor
	patient1      *data.Patient
	patient2      *data.Patient
	patient3      *data.Patient
	patient4      *data.Patient
	patient5      *data.Patient
	patient6      *data.Patient
	patientFinder *finder.Finder
	combinator    *combinator.Combinator
}

func TestPatientCombinationTestSuite(t *testing.T) {
	fakeDoctor := fixtures.Doctor_CatharineRobel

	patient1 := clonePatient(fixtures.Patient_HasValidKvInsurance)
	patient2 := clonePatient(fixtures.Patient_HasValidKvInsurance)
	patient3 := clonePatient(fixtures.Patient_HasValidKvInsurance)

	patientInfo3 := *patient3.Profile.PatientInfo
	patientInfo3.InsuranceInfos = []patient_profile_common.InsuranceInfo{}
	patientInfo3.GenericInfo.PatientType = patient_profile_common.PatientType_Private
	patient3.Profile.PatientInfo = &patientInfo3

	patient4 := clonePatient(fixtures.Patient_HasValidKvInsurance)
	patient5 := clonePatient(fixtures.Patient_HasValidKvInsurance)
	patient6 := clonePatient(fixtures.Patient_HasValidKvInsurance)

	patientFixture := &fixtures.PatientFixture{
		Patients: []*data.Patient{
			patient1,
			patient2,
			patient3,
			patient4,
			patient5,
			patient6,
		},
	}
	mvzFixture := fixtures.MvzFixture_521111100.With(patientFixture).ToCareProviderFixture()

	s := &PatientCombinationTestSuite{
		mvzFixture:    mvzFixture,
		doctor:        fakeDoctor,
		patient1:      patient1,
		patient2:      patient2,
		patient3:      patient3,
		patient4:      patient4,
		patient5:      patient5,
		patient6:      patient6,
		patientFinder: finder.FinderMod.Resolve(),
		combinator:    combinator.CombinatorMod.Resolve(),
	}
	s.SetFixtures(mvzFixture)
	suite.Run(t, s)
}

func (s *PatientCombinationTestSuite) TestFindDuplicatePatients() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	careProviderId := ctx.UserInfo().CareProviderId.String()
	fmt.Println("careProviderId", careProviderId)
	patientMap, err := s.patientFinder.FindDuplicatePatients(ctx, &finder.FindDuplicatePatientsRequest{
		PatientIds: []uuid.UUID{s.patient1.PatientId, s.patient2.PatientId, s.patient3.PatientId},
	})
	require.Nil(t, err)
	require.NotNil(t, patientMap)
	require.Equal(t, len(patientMap), 1)
	for _, patientIds := range patientMap {
		require.Len(t, patientIds, 3)
		require.Contains(t, patientIds, s.patient1.PatientId)
		require.Contains(t, patientIds, s.patient2.PatientId)
		require.Contains(t, patientIds, s.patient3.PatientId)
	}
}

func (s *PatientCombinationTestSuite) TestCombinePatients() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	err := s.combinator.CombinePatients(ctx, &combinator.CombineRequest{
		TargetPatientId:      s.patient4.PatientId,
		DuplicatedPatientIds: []uuid.UUID{s.patient5.PatientId, s.patient6.PatientId},
	})
	require.Nil(t, err)

	patientMap, err := s.patientFinder.FindDuplicatePatients(ctx, &finder.FindDuplicatePatientsRequest{
		PatientIds: []uuid.UUID{s.patient4.PatientId, s.patient5.PatientId, s.patient6.PatientId},
	})
	require.Nil(t, err)
	require.Nil(t, patientMap)

}
