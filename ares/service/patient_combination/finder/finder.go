package finder

import (
	"fmt"

	patient_profile_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
)

type (
	Finder struct {
		patientProfileRepo patient_profile_repo.PatientProfileDefaultRepository
	}
	FindDuplicatePatientsRequest struct {
		PatientIds []uuid.UUID `validate:"required"`
	}
)

var FinderMod = submodule.Make[*Finder](func(patientProfileRepo patient_profile_repo.PatientProfileDefaultRepository) *Finder {
	return &Finder{
		patientProfileRepo: patientProfileRepo,
	}
}, patient_profile_repo.PatientProfileDefaultRepositoryMod)

func (f *Finder) FindDuplicatePatients(ctx *titan.Context, req *FindDuplicatePatientsRequest) (map[string][]uuid.UUID, error) {
	duplicatedPatients, err := f.patientProfileRepo.FindDuplicatePatients(ctx, &patient_profile_repo.FindDuplicatePatientsRequest{
		PatientIds: req.PatientIds,
	})
	if err != nil {
		return nil, err
	}

	if len(duplicatedPatients) == 0 {
		return nil, nil
	}

	patientMap := make(map[string][]uuid.UUID)
	for _, patient := range duplicatedPatients {
		key := fmt.Sprintf("%s-%s-%d", patient.FirstName, patient.LastName, patient.DateOfBirth)
		patientMap[key] = append(patientMap[key], patient.PatientId)
	}

	return patientMap, nil
}
