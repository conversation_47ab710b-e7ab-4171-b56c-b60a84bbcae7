package schein

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"time"

	"emperror.dev/errors"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/spf13/cast"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	catalog_sdik_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_sdik"
	catalog_sdkt_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_sdkt"
	api_patient_profile "git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_profile"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/schein"
	settingApi "git.tutum.dev/medi/tutum/ares/app/mvz/api/settings"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/app/mvz/patient_profile"
	"git.tutum.dev/medi/tutum/ares/pkg/hook"
	"git.tutum.dev/medi/tutum/ares/pkg/operator"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_copy"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	"git.tutum.dev/medi/tutum/ares/pkg/validator"
	bsnr_service "git.tutum.dev/medi/tutum/ares/service/bsnr"
	"git.tutum.dev/medi/tutum/ares/service/contract/contract"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/billing"
	billing_common "git.tutum.dev/medi/tutum/ares/service/domains/api/billing_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdkt_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	patient_participation_api "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_participation"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	private_schein_common "git.tutum.dev/medi/tutum/ares/service/domains/api/private_schein_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"
	sdkv "git.tutum.dev/medi/tutum/ares/service/domains/api/sdkv"
	bg_billing_repo "git.tutum.dev/medi/tutum/ares/service/domains/bg_billing/repo"
	bg_billing_service "git.tutum.dev/medi/tutum/ares/service/domains/bg_billing/service"
	card_raw_service "git.tutum.dev/medi/tutum/ares/service/domains/card_raw/service"
	catalog_goa_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_goa"
	catalog_sdkt_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_sdkt"
	edmp_service "git.tutum.dev/medi/tutum/ares/service/domains/edmp/service"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	priv_billing_service "git.tutum.dev/medi/tutum/ares/service/domains/private_billing/service"
	pcg_service "git.tutum.dev/medi/tutum/ares/service/domains/private_contract_group/service"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/doctor_participate"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_participation"
	scheinRepo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	sdkv_service "git.tutum.dev/medi/tutum/ares/service/domains/sdkv"
	settings_common "git.tutum.dev/medi/tutum/ares/service/domains/settings/common"
	timeline_common "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/timeline_service"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/validation_timeline"
	"git.tutum.dev/medi/tutum/ares/service/rezidiv"
	"git.tutum.dev/medi/tutum/ares/service/settings"
	"git.tutum.dev/medi/tutum/ares/share"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/hestia/socket_service/api"
)

type ScheinService struct {
	scheinRepo                   scheinRepo.ScheinRepoDefaultRepository
	fieldService                 *FieldService
	patientProfileService        *patient_profile.PatientProfileBffImpl
	employeeProfileService       profile.EmployeeProfileService
	catalogSdktService           *catalog_sdkt_service.CatalogSdktService
	notifier                     *schein.ScheinSocketNotifier
	patientParticipationRepo     patient_participation.PatientParticipationDefaultRepository
	doctorParticipateRepo        doctor_participate.DoctorParticipateDefaultRepository
	contractService              *contract.Service
	timelineService              *timeline_service.TimelineService[patient_encounter.EncounterServiceTimeline]
	timelineDiagnoseService      *timeline_service.TimelineService[patient_encounter.EncounterDiagnoseTimeline]
	timelinePsychotherapyService *timeline_service.TimelineService[patient_encounter.EncounterPsychotherapy]
	rezidivRepo                  *rezidiv.RezidivList
	validationTimelineService    *validation_timeline.TimelineValidationService
	OnUpdate                     *hook.InMemoryHook[scheinRepo.ScheinRepo]
	edmpService                  *edmp_service.EDMPService
	sdkvService                  *sdkv_service.SDKVService
	pcgService                   *pcg_service.Service
	privBillingService           *priv_billing_service.Service
	cardRawService               *card_raw_service.CardRawService
	bgBillingService             *bg_billing_service.Service
	isDummyVknr                  patient_profile_common.IsDummyVknrFunc
	bgBillingRepo                *bg_billing_repo.BgBillingRepo
	bsnrService                  *bsnr_service.BSNRService
	catalogGoaService            *catalog_goa_service.CatalogGoaService
}

var ScheinServiceMod = submodule.Make[*ScheinService](func(
	patientProfile *patient_profile.PatientProfileBffImpl,
	fieldService *FieldService,
	socketClient *api.SocketServiceClient,
	rezidivRepo *rezidiv.RezidivList,
	timelineService *timeline_service.TimelineService[patient_encounter.EncounterServiceTimeline],
	timelineDiagnoseService *timeline_service.TimelineService[patient_encounter.EncounterDiagnoseTimeline],
	timelineEncounterPsychotherapyService *timeline_service.TimelineService[patient_encounter.EncounterPsychotherapy],
	validationTimelineService *validation_timeline.TimelineValidationService,
	contractService *contract.Service,
	catalogSdktService *catalog_sdkt_service.CatalogSdktService,
	edmpService *edmp_service.EDMPService,
	sdkvService *sdkv_service.SDKVService,
	pcgService *pcg_service.Service,
	cardRawService *card_raw_service.CardRawService,
	privBillingService *priv_billing_service.Service,
	employeeProfileServiceClient profile.EmployeeProfileService,
	bgBillingService *bg_billing_service.Service,
	isDummyVknr patient_profile_common.IsDummyVknrFunc,
	bgBillingRepo *bg_billing_repo.BgBillingRepo,
	bsnrService *bsnr_service.BSNRService,
	catalogGoaService *catalog_goa_service.CatalogGoaService,
) *ScheinService {
	notifier := schein.NewScheinSocketNotifier(socketClient)

	doctorParticipateRepo := doctor_participate.NewDoctorParticipateDefaultRepository()

	scheinService := &ScheinService{
		scheinRepo:                   scheinRepo.NewScheinRepoDefaultRepository(),
		fieldService:                 fieldService,
		patientProfileService:        patientProfile,
		employeeProfileService:       employeeProfileServiceClient,
		catalogSdktService:           catalogSdktService,
		notifier:                     notifier,
		patientParticipationRepo:     patient_participation.NewPatientParticipationDefaultRepository(),
		doctorParticipateRepo:        doctorParticipateRepo,
		contractService:              contractService,
		timelineService:              timelineService,
		timelineDiagnoseService:      timelineDiagnoseService,
		rezidivRepo:                  rezidivRepo,
		timelinePsychotherapyService: timelineEncounterPsychotherapyService,
		validationTimelineService:    validationTimelineService,
		OnUpdate:                     hook.NewInMemoryEvent[scheinRepo.ScheinRepo](),
		edmpService:                  edmpService,
		sdkvService:                  sdkvService,
		pcgService:                   pcgService,
		privBillingService:           privBillingService,
		cardRawService:               cardRawService,
		bgBillingService:             bgBillingService,
		isDummyVknr:                  isDummyVknr,
		bgBillingRepo:                bgBillingRepo,
		bsnrService:                  bsnrService,
		catalogGoaService:            catalogGoaService,
	}
	scheinService.patientProfileService.CUDHook.RegisterOnUpdateFunc(func(ctx *titan.Context, um api_patient_profile.EventPatientProfileChange) error {
		return nil
		updatedAt := function.If(um.ReadingDate == 0, util.NowUnixMillis(ctx), um.ReadingDate)
		return scheinService.updatePatientSnapshot(ctx, schein_common.PatientSnapshot{
			PatientId:   *um.PatientId,
			PatientInfo: *um.PatientInfo,
			UpdatedAt:   updatedAt,
		})
	})

	return scheinService
},
	patient_profile.PatientProfileServiceMod,
	FieldServiceMod,
	config.SocketServiceClientMod,
	rezidiv.RezidivRepoMod,
	timeline_service.TimelineServiceEncounterMod,
	timeline_service.TimelineServiceDiagnosisMod,
	timeline_service.TimelineServicePsychotherapyMod,
	validation_timeline.TimelineValidationServiceMod,
	contract.ContractServiceMod,
	edmp_service.EDMPServiceMod,
	sdkv_service.ServiceMod,
	pcg_service.PrivateContractServiceMod,
	card_raw_service.CardRawServiceMod,
	priv_billing_service.PrivateBillingServiceMod,
	share.EmployeeProfileServiceMod,
	bg_billing_service.BgBillingServiceMod,
	patient_profile_common.IsDummyVknrFuncMod,
	bg_billing_repo.BgBillingRepoMod,
	bsnr_service.BSNRServiceMod,
	catalog_sdkt_service.CatalogSdktServiceMod,
	catalog_goa_service.CatalogGoaServiceMod,
)

type MarkBillRequest struct {
	MainGroup        schein_common.MainGroup
	ValidScheinsIds  []uuid.UUID
	ContractId       *string
	ExcludeScheinIds []uuid.UUID
}
type GetSvScheins struct {
	DoctorIds      []uuid.UUID
	Times          []billing_common.YearQuarter
	ContractIds    []string
	IgnoreBillings []billing.IgnoreBilling
}

type CreateHzvForBilledScheinRequest struct {
	PatientIds []uuid.UUID              `json:"patientId" validate:"required"`
	Quarter    *common.YearQuarter      `json:"quarter"`
	MainGroup  *schein_common.MainGroup `json:"mainGroup"`
}

// ToggleMarkBill implements schein.ScheinApp
func (srv *ScheinService) MarkBill(ctx *titan.Context, request MarkBillRequest) error {
	updated, err := srv.scheinRepo.MarkBilledByScheinIds(ctx, scheinRepo.MarkBilledByScheinIdsRequest{
		ContractId:       request.ContractId,
		MainGroup:        request.MainGroup,
		ExcludeScheinIds: request.ExcludeScheinIds,
		ValidScheinsIds:  request.ValidScheinsIds,
	})
	if err != nil {
		return err
	}
	if len(updated) == 0 {
		return nil
	}

	psychotherapyIds := []uuid.UUID{}
	for _, s := range updated {
		for _, p := range s.ScheinDetail.Psychotherapy {
			if p.Id != nil {
				psychotherapyIds = append(psychotherapyIds, *p.Id)
			}
		}
	}

	if _, err := srv.timelinePsychotherapyService.UpdatePsychotherapyStatusByIds(
		ctx,
		psychotherapyIds,
		[]patient_encounter.EncounterPsychotherapyStatus{
			patient_encounter.READY_TO_BILL,
		}, patient_encounter.BILLED); err != nil {
		return fmt.Errorf("update psychotherapy status error: %w", err)
	}

	return srv.notifier.NotifyClientScheinChanged(ctx, &schein.EventScheinChanged{
		Data: schein.ScheinChangedResponse{
			PatientId: updated[0].PatientId,
		},
	})
}

func (srv *ScheinService) GetKTABs(ctx *titan.Context, request schein.GetKTABsRequest) (*schein.GetKTABsResponse, error) {
	if request.VKNR == "" {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_VknrIsRequired, "Missing vknr")
	}

	request.VKNR = util.LeftPad(request.VKNR, 5, '0')
	now := util.NowUnixMillis(ctx)
	res, err := srv.catalogSdktService.GetSdktCatalogByVknr(ctx, &catalog_sdkt_api.GetSdktCatalogByVknrRequest{
		Vknr:         request.VKNR,
		SelectedDate: &now,
	})
	if err != nil {
		return nil, err
	}

	if res == nil || res.Data == nil || len(res.Data.KTABs) == 0 {
		return nil, nil
	}

	sdkt := res.Data
	yearQuarter := util.YearQuarter{
		Year:    request.Year,
		Quarter: request.Quarter,
	}

	sdkvRes, err := srv.sdkvService.GetSDKVByBsnr(ctx, sdkv.GetSDKVByBsnrRequest{
		Bsnr:         request.Bsnr,
		SelectedDate: yearQuarter.StartTime(),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	sdkvKTabs := []string{}
	if sdkvRes != nil {
		sdkvKTabs = sdkvRes.Data.Get4106()
	}

	ktabsFiltered := slice.Filter(sdkt.KTABs, func(k *catalog_sdkt_common.KTAB) bool {
		return slice.Contains(sdkvKTabs, string(k.Value))
	})
	ktabvalues := slice.Map(ktabsFiltered, func(k *catalog_sdkt_common.KTAB) catalog_sdkt_common.KTABValue {
		return k.Value
	})

	result := schein.GetKTABsResponse{
		KTABValue: ktabvalues,
	}

	if srv.isDummyVknr(request.VKNR) { // TODO: card test
		return &result, nil
	}

	switch request.SpecialGroup {
	case patient_profile_common.SpecialGroup_00:
		result.KTABValue = slice.Filter(result.KTABValue, func(k catalog_sdkt_common.KTABValue) bool {
			return k == catalog_sdkt_common.KTAB_00 || k == catalog_sdkt_common.KTAB_09
		})
		return &result, nil
	case patient_profile_common.SpecialGroup_04:
		result.KTABValue = slice.Filter(result.KTABValue, func(k catalog_sdkt_common.KTABValue) bool {
			return k == catalog_sdkt_common.KTAB_00 || k == catalog_sdkt_common.KTAB_09
		})
		return &result, nil
	case patient_profile_common.SpecialGroup_06:
		result.KTABValue = slice.Filter(result.KTABValue, func(k catalog_sdkt_common.KTABValue) bool {
			return k == catalog_sdkt_common.KTAB_02 || k == catalog_sdkt_common.KTAB_09
		})
		return &result, nil
	case patient_profile_common.SpecialGroup_07, patient_profile_common.SpecialGroup_08:
		result.KTABValue = slice.Filter(result.KTABValue, func(k catalog_sdkt_common.KTABValue) bool {
			return k == catalog_sdkt_common.KTAB_01 || k == catalog_sdkt_common.KTAB_09
		})
		return &result, nil
	case patient_profile_common.SpecialGroup_09:
		return &result, nil
	}

	return &schein.GetKTABsResponse{
		KTABValue: ktabvalues,
	}, nil
}

func getMainGroup(contractType *common.ContractType) schein_common.MainGroup {
	mainGroup := schein_common.KV

	if *contractType == common.ContractType_HouseDoctorCare {
		mainGroup = schein_common.HZV
	}

	if *contractType == common.ContractType_SpecialistCare {
		mainGroup = schein_common.FAV
	}
	return mainGroup
}

// main SV Schein creation logic
// handle create SV schein when patient participation status is active or requested
func (srv *ScheinService) CreateSvScheins(ctx *titan.Context, request schein_common.CreateSvScheinRequest) error {
	allowPPStatus := []patient_participation_api.PatientParticipationStatus{patient_participation_api.PatientParticipation_Active, patient_participation_api.PatientParticipation_Requested, patient_participation_api.PatientParticipation_Terminated}

	setting, err := srv.GetSetting(ctx)
	if err != nil {
		return errors.Wrap(err, "get schein Setting error")
	}

	handlerSvScheins := func(pp patient_participation_api.PatientParticipation) error {
		if !slice.Contains(allowPPStatus, pp.Status) || pp.StartDate == nil {
			return nil
		}
		doctor, err := srv.employeeProfileService.GetEmployeeProfileById(ctx, &profile.EmployeeProfileGetRequest{
			OriginalId: pp.DoctorId,
		})
		if err != nil {
			return errors.Wrap(err, "get employee profile error")
		}
		doctorParticipate, err := srv.doctorParticipateRepo.Find(ctx, bson.M{
			doctor_participate.Field_DoctorId: *pp.DoctorId,
		})
		if err != nil {
			return errors.Wrap(err, "get doctor participate error")
		}
		doctorParticipate = slice.Filter(doctorParticipate, func(d doctor_participate.DoctorParticipate) bool {
			isActive := d.Status == doctor_participate.DoctorParticipateStatus_Active
			isSameContract := d.ContractId != nil && *d.ContractId == pp.ContractId
			isSameChargeSystem := d.ChargeSystemId == pp.ChargeSystemId
			isExplicitParticipant := d.ParentId == nil
			return isActive && isExplicitParticipant && isSameContract && isSameChargeSystem
		})
		if len(doctorParticipate) == 0 {
			return nil
		}

		quarters := util.CalculateQuarters(*pp.StartDate, util.NowUnixMillis(ctx), ctx.RequestTimeZone())
		currentQuarter := util.ToYearQuarter(ctx.RequestCurrentTime().UnixMilli(), ctx.RequestTimeZone())

		yearQuarters := currentQuarter.Backward(int(quarters))

		callback := func(ctx *titan.Context) (any, error) {
			scheinsDb, err := srv.scheinRepo.GetScheinsByPatientIdQuarters(ctx, scheinRepo.GetScheinsByPatientIdQuartersParams{
				PatientId: request.PatientId,
				Quarters: slice.Map(yearQuarters, func(y util.YearQuarter) scheinRepo.ScheinQuarters {
					return scheinRepo.ScheinQuarters{
						G4101Quarter: y.Quarter,
						G4101Year:    y.Year,
					}
				}),
			})
			if err != nil {
				return nil, err
			}

			notExistYearQuarters := slice.Filter(yearQuarters, func(y util.YearQuarter) bool {
				scheinExisted := slice.FindOne(scheinsDb, func(s scheinRepo.ScheinRepo) bool {
					return s.GetYearQuarter().Year == y.Year &&
						s.GetYearQuarter().Quarter == y.Quarter &&
						s.ContractId != nil && *s.ContractId == pp.ContractId &&
						s.ChargeSystemId != nil && *s.ChargeSystemId == pp.ChargeSystemId
				})
				return scheinExisted == nil
			})
			tmp := slice.Map(notExistYearQuarters, func(n util.YearQuarter) scheinRepo.CreateDefaultScheinRequest {
				mainGroup := string(getMainGroup(&pp.ContractType))

				return scheinRepo.CreateDefaultScheinRequest{
					MainGroup:          mainGroup,
					PatientId:          request.PatientId,
					DoctorId:           *pp.DoctorId,
					ContractId:         util.NewPointer(pp.ContractId),
					ChargeSystemId:     util.NewPointer(pp.ChargeSystemId),
					InsuranceId:        request.InsuranceInfo.Id,
					InsuranceCompanyId: request.InsuranceInfo.InsuranceCompanyId,
					YearQuarter:        n,
					AssignedToBsnrId:   doctor.BsnrId,
					StartDate:          request.StartDate,
					EndDate:            request.EndDate,
				}
			})
			if len(tmp) > 0 {
				_, err = srv.scheinRepo.CreateDefaultSchein(ctx, tmp...)
				if err != nil {
					return nil, errors.Wrap(err, "create default schein error")
				}
			}

			existingScheins := slice.Filter(scheinsDb, func(s scheinRepo.ScheinRepo) bool {
				scheinExisted := slice.FindOne(yearQuarters, func(t util.YearQuarter) bool {
					return s.GetYearQuarter().Year == t.Year &&
						s.GetYearQuarter().Quarter == t.Quarter &&
						s.ContractId != nil && *s.ContractId == pp.ContractId &&
						s.ChargeSystemId != nil && *s.ChargeSystemId == pp.ChargeSystemId
				})
				return scheinExisted != nil
			})
			if len(existingScheins) > 0 {
				_, err = srv.scheinRepo.UpdateMany(ctx,
					slice.Map(existingScheins, func(s scheinRepo.ScheinRepo) scheinRepo.ScheinRepo {
						s.DoctorId = request.DoctorId

						if request.StartDate != nil && s.SvScheinDetail != nil {
							s.SvScheinDetail.StartDate = request.StartDate
						}

						if request.EndDate != nil && s.SvScheinDetail != nil {
							s.SvScheinDetail.EndDate = function.If(*request.EndDate == 0, nil, request.EndDate)
						}
						return s
					}))
				if err != nil {
					return nil, errors.Wrap(err, "update schein error")
				}
			}
			return nil, nil
		}

		// tx
		_, err = srv.scheinRepo.Db.WithTransaction(ctx, callback)
		if err != nil {
			return err
		}

		return nil
	}

	if setting.AutoCreatekvscheinHzvfav {
		lstActiveParticipations := slice.Filter(request.PatientParticipations, func(p patient_participation_api.PatientParticipation) bool {
			return p.Status == patient_participation_api.PatientParticipation_Active
		})
		scheinsCurrentQuarter, err := srv.scheinRepo.FilterSchein(ctx, scheinRepo.FilterScheinParams{
			PatientId:    util.NewPointer(request.PatientId),
			G4101Quarter: util.NewPointer(util.NowQuarter(ctx)),
			G4101Year:    util.NewPointer(util.NowYear(ctx)),
		})
		if err != nil {
			return err
		}
		existKVSchein := slice.Any(scheinsCurrentQuarter, func(s scheinRepo.ScheinRepo) bool {
			return s.Schein.ScheinMainGroup == string(schein_common.KV)
		})
		if existKVSchein {
			doctorId := request.DoctorId
			if len(lstActiveParticipations) > 0 {
				doctorId = *lstActiveParticipations[0].DoctorId
			}
			doctor, err := srv.employeeProfileService.GetEmployeeProfileById(ctx, &profile.EmployeeProfileGetRequest{
				OriginalId: &doctorId,
			})
			if err != nil {
				return errors.Wrap(err, "get employee profile error")
			}
			scheins, err := srv.scheinRepo.CreateDefaultSchein(ctx, scheinRepo.CreateDefaultScheinRequest{
				MainGroup:          string(schein_common.KV),
				PatientId:          request.PatientId,
				DoctorId:           doctorId,
				ContractId:         nil,
				InsuranceId:        request.InsuranceInfo.Id,
				InsuranceCompanyId: request.InsuranceInfo.InsuranceCompanyId,
				AssignedToBsnrId:   doctor.BsnrId,
				StartDate:          request.StartDate,
			})
			if err != nil {
				return errors.Wrap(err, "create default schein error")
			}
			for _, schein := range *scheins {
				err := srv.CreateServiceCode88194Automatic(ctx, schein.Id, nil, request.PatientId, doctorId)
				if err != nil {
					return err
				}
			}
			err = srv.HandleEventScheinChanged(ctx, schein.EventScheinChanged{
				Data: schein.ScheinChangedResponse{
					PatientId: request.PatientId,
				},
			})
			if err != nil {
				return err
			}
		}
	}

	for _, v := range request.PatientParticipations {
		err := handlerSvScheins(v)
		if err != nil {
			return err
		}
	}
	return nil
}

func (srv *ScheinService) CreateSvSchein(ctx *titan.Context, request scheinRepo.CreateDefaultScheinRequest) error {
	currentQuarter := util.ToYearQuarter(ctx.RequestCurrentTime().UnixMilli())
	scheins, err := srv.scheinRepo.GetScheinsByInsuranceId(ctx, request.InsuranceId)
	if err != nil {
		return err
	}

	svScheinInQuarter := slice.FindOne(scheins, func(schein scheinRepo.ScheinRepo) bool {
		isSvSchein := schein.Schein.ScheinMainGroup == string(schein_common.HZV) || schein.Schein.ScheinMainGroup == string(schein_common.FAV)
		isSameYearQuarter := currentQuarter.IsEqual(schein.GetYearQuarter())
		return isSvSchein && isSameYearQuarter
	})

	if svScheinInQuarter == nil {
		newDefaultScheins := []scheinRepo.CreateDefaultScheinRequest{request}
		_, err = srv.scheinRepo.CreateDefaultSchein(ctx, newDefaultScheins...)
		if err != nil {
			return errors.Wrap(err, "create default schein error")
		}
	}

	return srv.HandleEventScheinChanged(ctx, schein.EventScheinChanged{
		Data: schein.ScheinChangedResponse{
			PatientId: request.PatientId,
		},
	})
}

func (srv *ScheinService) IsValid(ctx *titan.Context, req schein.IsValidRequest) (*schein.IsValidResponse, error) {
	validateErr := map[string]common.FieldError{}

	patientProfile, err := srv.patientProfileService.GetProfileById(ctx, profile.GetByIdRequest{
		PatientId: &req.CreateScheinRequest.PatientId,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "get patient profile error")
	}

	// Validate doctorId
	var doctor *profile.EmployeeProfileResponse
	if req.CreateScheinRequest.DoctorId != nil {
		doctor, err = srv.employeeProfileService.GetEmployeeProfileById(ctx, &profile.EmployeeProfileGetRequest{
			OriginalId: req.CreateScheinRequest.DoctorId,
			BsnrId:     req.CreateScheinRequest.AssignedToBsnrId,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "get doctor profile error")
		}

		fieldError := doctorRequire(doctor)
		if fieldError != nil {
			return &schein.IsValidResponse{
				Error: &map[string]common.FieldError{
					fieldError.FieldName: *fieldError,
				},
			}, nil
		}

		missingBSNR := doctorMissingBNSR(doctor)
		if missingBSNR {
			return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_BSNR_NOT_FOUND, "")
		}
	}

	patientInsurance := slice.FindOne(req.Insurances, func(i patient_profile_common.InsuranceInfo) bool {
		return i.Id.String() == req.CreateScheinRequest.InsuranceId.String()
	})
	if patientInsurance == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Schein_Validation_Patient_Insurance_Not_Found)
	}

	// TODO: why not check the insurance valid with quarter of schein?
	ikNumber := patientInsurance.GetIkNumberString()
	matchedCostUnitRes, err := srv.catalogSdktService.SearchSdktByIKNumber(ctx, &catalog_sdkt_api.SearchSdktByIKNumberRequest{
		Value:        ikNumber,
		SelectedDate: util.NowUnixMillis(ctx),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "search cost unit by ik number error")
	}
	if matchedCostUnitRes == nil || matchedCostUnitRes.Data == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Cost_Unit_Not_Found, "")
	}
	matchedCostUnit := matchedCostUnitRes.Data
	vknrGroup := matchedCostUnit.VknrGroup

	// Validate doctor
	currentKvRegion := ""
	if doctor != nil {
		validateErr, err = srv.fieldService.Validate(ctx, ValidateRequest{
			Request:        req.CreateScheinRequest,
			Gender:         patientProfile.PatientInfo.PersonalInfo.Gender,
			Bsnr:           doctor.Bsnr,
			VknrGroup:      vknrGroup,
			Vknr:           matchedCostUnit.Vknr,
			PatientProfile: patientProfile,
			Insurances:     req.Insurances,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "validate field error")
		}

		currentKvRegion = doctor.GetOKV()
	}

	// do the validation here
	currentTime := util.NowUnixMillis(ctx)
	yearQuarter := util.YearQuarter{
		Year:    req.CreateScheinRequest.G4101Year,
		Quarter: req.CreateScheinRequest.G4101Quarter,
	}
	scheinStartTime := *yearQuarter.StartTime()
	scheinEndTime := *yearQuarter.EndTime()

	fieldInsuranceError := insuranceShouldInQuarter(yearQuarter)(patientInsurance)
	if fieldInsuranceError != nil {
		validateErr[fieldInsuranceError.Field] = *fieldInsuranceError
		return &schein.IsValidResponse{
			Error: &validateErr,
		}, nil
	}

	var getScheinByIdError error
	validationErrorRunner := new(FieldValidationRunner)
	tSSValidationModel := TSSValidationModel{
		TsvgContactType: req.CreateScheinRequest.ScheinDetails.TsvgContactType,
		TsvgInfor:       req.CreateScheinRequest.ScheinDetails.TsvgInfor,
		TsvgTranferCode: req.CreateScheinRequest.ScheinDetails.TsvgTranferCode,
		TsvgContactDate: req.CreateScheinRequest.ScheinDetails.TsvgContactDate,
		scheinGetter: func(u uuid.UUID, f func(*scheinRepo.ScheinRepo)) {
			schein, err := srv.scheinRepo.GetById(ctx, u)
			if err != nil {
				getScheinByIdError = err
				return
			}
			f(schein)
		},
	}
	if getScheinByIdError != nil {
		return nil, errors.WithMessage(getScheinByIdError, "get schein by id error")
	}

	if tSSValidationModel.canGetTssSchein() {
		scheinHasTsvgTranferCode, err := srv.scheinRepo.FindTssByCode(ctx, *req.CreateScheinRequest.ScheinDetails.TsvgTranferCode)
		if err != nil {
			return nil, errors.WithMessage(err, "find tss schein by code error")
		}
		tSSValidationModel.scheinHasTsvgTranferCode = scheinHasTsvgTranferCode
	}

	validationErrorRunner.SetRunners(
		func() *common.FieldError {
			return costUnitIsNotAvailableInKvRegion(matchedCostUnit.RestrictKvRegions)(currentKvRegion)
		},
		func() *common.FieldError {
			return costUnitIsTerminated(scheinStartTime, scheinEndTime)(matchedCostUnit)
		},
		func() *common.FieldError {
			return theBillingAreaShouldNotOutOfValidityDateRange(matchedCostUnit, scheinStartTime)(req.CreateScheinRequest.ScheinDetails.G4106)
		},
		func() *common.FieldError {
			return costUnitShouldNotExpired(currentTime)(matchedCostUnit)
		},
		func() *common.FieldError {
			return costUnitShouldNotExpiredByIKNumbers(matchedCostUnit, currentTime)(patientInsurance.IkNumber)
		},
		func() *common.FieldError {
			return warningTsvgContactDate(tSSValidationModel)
		},
		func() *common.FieldError {
			return codeExists(req.CreateScheinRequest.ScheinId)(tSSValidationModel)
		},
	)

	for _, v := range validationErrorRunner.Execute() {
		validateErr[v.FieldName] = v
	}

	if len(validateErr) > 0 {
		return &schein.IsValidResponse{
			Error: &validateErr,
		}, nil
	}
	return nil, nil
}

func (srv *ScheinService) HandleScheinTakeOverDiagnoses(ctx *titan.Context, scheinRepo scheinRepo.ScheinRepo, takeOverDiagnoseInfos []schein.TakeOverDiagnoseInfo, newTakeOverDiagnosis []timeline_common.TimelineModel) error {
	takeOverDiagnosis := []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{}
	updatedDiagnosis := []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{}
	// Case for existed diagnosis timeline
	if len(takeOverDiagnoseInfos) > 0 {
		takeOverDiagnoseIds := slice.Map(takeOverDiagnoseInfos, func(info schein.TakeOverDiagnoseInfo) uuid.UUID {
			return info.Id
		})

		res, err := srv.timelineDiagnoseService.FindByIds(ctx, takeOverDiagnoseIds)
		if err != nil {
			return fmt.Errorf("find take over diagnoses error: %w", err)
		}

		for index, item := range res {
			newItem := item
			newItem.Payload.MarkedTreatmentRelevant = takeOverDiagnoseInfos[index].IsTreatmentRelevant
			takeOverDiagnosis = append(takeOverDiagnosis, newItem)
			// Store scheinId which take over by this timeline entry
			updatedDiagnosis = append(updatedDiagnosis, item)
		}
	}
	// Case for non-existed diagnosis timeline and create new
	if len(newTakeOverDiagnosis) > 0 {
		entities := slice.Map(newTakeOverDiagnosis, func(item timeline_common.TimelineModel) timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline] {
			return timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
				ContractId:        scheinRepo.ContractId,
				TreatmentDoctorId: item.TreatmentDoctorId,
				BillingDoctorId:   item.BillingDoctorId,
				PatientId:         item.PatientId,
				ScheinIds:         item.ScheinIds,
				EncounterCase:     item.EncounterCase,
				TreatmentCase:     item.TreatmentCase,
				Payload:           *item.EncounterDiagnoseTimeline,
				SelectedDate:      util.ConvertMillisecondsToTime(item.SelectedDate, ctx.RequestTimeZone()),
			}
		})
		takeOverDiagnosis = append(takeOverDiagnosis, entities...)
	}

	if len(takeOverDiagnosis) == 0 {
		return nil
	}

	if _, err := srv.timelineDiagnoseService.UpdateMany(ctx, updatedDiagnosis); err != nil {
		return fmt.Errorf("failed to update diagnosis take over: %w", err)
	}

	if _, err := srv.timelineDiagnoseService.TakeOverScheinDiagnoses(ctx, &scheinRepo, takeOverDiagnosis, map[string]bool{}); err != nil {
		return fmt.Errorf("take over schein diagnoses error: %w", err)
	}
	return nil
}

func (srv *ScheinService) TakeOverDiagnosisByScheinId(ctx *titan.Context, request schein.TakeOverDiagnosisByScheinIdRequest) error {
	schein, err := srv.scheinRepo.FindById(ctx, request.ScheinId)
	if err != nil {
		return err
	}

	if schein == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Schein_Not_Found)
	}

	err = srv.HandleScheinTakeOverDiagnoses(ctx, *schein, request.TakeOverDiagnoseInfos, request.NewTakeOverDiagnosis)
	if err != nil {
		return fmt.Errorf("handle take over diagnoses error: %w", err)
	}

	return nil
}

func (srv *ScheinService) updatePatientSnapshot(ctx *titan.Context, patientSnapshot schein_common.PatientSnapshot) error {
	quarter := util.ToYearQuarter(patientSnapshot.UpdatedAt)
	filter := bson.M{
		"schein.g4101Quarter":      quarter.Quarter,
		"schein.g4101Year":         quarter.Year,
		scheinRepo.Field_PatientId: patientSnapshot.PatientId,
		scheinRepo.Field_IsDeleted: false,
	}
	update := bson.M{
		"$set": bson.M{
			scheinRepo.Field_PatientSnapshot: patientSnapshot,
			scheinRepo.Field_UpdatedAt:       util.NowUnixMillis(ctx),
		},
	}
	_, err := srv.scheinRepo.Db.UpdateMany(
		ctx,
		filter,
		update,
		nil,
		nil,
	)
	if err != nil {
		return errors.WithMessage(err, "update patient snapshot error")
	}
	return nil
}

func (srv *ScheinService) CreateSchein(ctx *titan.Context, req schein.CreateScheinRequest) (*schein.CreateScheinResponse, error) {
	patientProfile, err := srv.patientProfileService.GetProfileById(ctx, profile.GetByIdRequest{
		PatientId: &req.PatientId,
	})
	if err != nil {
		return nil, err
	}
	if patientProfile == nil || patientProfile.Id == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Patient_Not_Found, "")
	}
	activeinsurance := patientProfile.PatientInfo.GetInsurance(req.InsuranceId)
	if activeinsurance == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_NotActive_InsuranceInfo, "")
	}
	currentQuarter := req.GetYearQuarter()
	isInsuranceValid := activeinsurance.IsValidWithQuarter(*currentQuarter)
	if !isInsuranceValid {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_InsuranceInfo_Not_Valid_In_Quarter, "")
	}
	now := util.NowUnixMillis(ctx)
	res, err := srv.catalogSdktService.GetSdktCatalogByVknr(ctx, &catalog_sdkt_api.GetSdktCatalogByVknrRequest{
		Vknr:         activeinsurance.InsuranceCompanyId,
		SelectedDate: &now,
	})
	if err != nil {
		return nil, err
	}

	if res == nil || res.Data == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_InsuranceInfo_Not_Valid_In_Quarter, "")
	}

	sdkt := res.Data

	assignedToBsnrId := *ctx.UserInfo().GetBsnrId()
	if req.AssignedToBsnrId != nil {
		assignedToBsnrId = *req.AssignedToBsnrId
	}
	doctorId := *ctx.UserInfo().UserUUID()
	if req.DoctorId != nil {
		doctorId = *req.DoctorId
	}
	bsnr, err := srv.bsnrService.GetById(ctx, assignedToBsnrId)
	if err != nil {
		return nil, fmt.Errorf("failed to get bsnr by id: %w", err)
	}

	notFilteredValidateErr, err := srv.fieldService.Validate(ctx, ValidateRequest{
		Request:        req,
		Gender:         patientProfile.PatientInfo.PersonalInfo.Gender,
		Bsnr:           bsnr.Code,
		VknrGroup:      sdkt.VknrGroup,
		PatientProfile: patientProfile,
		Vknr:           sdkt.Vknr,
		Insurances:     []patient_profile_common.InsuranceInfo{},
	})
	if err != nil {
		return nil, err
	}

	validateErr := map[string]common.FieldError{}
	for k, v := range notFilteredValidateErr {
		if v.ValidationType != common.ValidationType_Notice {
			validateErr[k] = v
		}
	}
	if len(validateErr) > 0 {
		return &schein.CreateScheinResponse{
			FieldErrors: validateErr,
		}, nil
	}
	mainGroup := string(req.ScheinMainGroup)
	if req.KvTreatmentCase == schein_common.TCBgCase || req.KvTreatmentCase == schein_common.TCPrivateCase {
		mainGroup = string(req.KvTreatmentCase)
	} else if mainGroup == "" {
		mainGroup = string(schein_common.KV)
	}
	newId := uuid.New()
	var scheinModel schein_common.Schein
	if err := copier.Copy(&scheinModel, req); err != nil {
		return nil, err
	}
	scheinModel.ScheinMainGroup = mainGroup

	var scheinDetail schein_common.ScheinDetail
	if req.ScheinDetails != nil {
		if err := copier.Copy(&scheinDetail, req.ScheinDetails); err != nil {
			return nil, err
		}
	}
	scheinDetail.Psychotherapy = slice.Map(scheinDetail.Psychotherapy, func(t schein_common.Psychotherapy) schein_common.Psychotherapy {
		if t.Id == nil {
			t.Id = util.NewUUID()
		}
		return t
	})

	scheinRepo := scheinRepo.ScheinRepo{
		Id:                 &newId,
		DoctorId:           doctorId,
		PatientId:          req.PatientId,
		ContractId:         req.HzvContractId,
		Schein:             scheinModel,
		ScheinDetail:       scheinDetail,
		CreatedAt:          util.NowUnixMillis(ctx),
		CreatedBy:          util.GetPointerValue(ctx.UserInfo().UserUUID()),
		ExcludeFromBilling: req.ExcludeFromBilling,
		AssignedToBsnrId:   &assignedToBsnrId,
	}

	setting, err := srv.GetSetting(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	scheinRepoCreated, err := srv.scheinRepo.Create(ctx, scheinRepo)
	if err != nil {
		return nil, err
	}

	if scheinRepoCreated == nil || scheinRepoCreated.Id == nil {
		return nil, errors.New("create schein error")
	}

	if setting.AssignDiagnosisCreatedschein == schein_common.AssigndiagnosisManually {
		if err := srv.HandleScheinTakeOverDiagnoses(ctx, scheinRepo, req.TakeOverDiagnoseInfos, req.NewTakeOverDiagnosis); err != nil {
			return nil, fmt.Errorf("handle take over diagnoses error: %w", err)
		}
	}

	if setting.AssignDiagnosisCreatedschein == schein_common.AssigndiagnosisAll {
		if err = srv.timelineService.AssignScheinToDiagnosesByQuarter(ctx, req.PatientId, *scheinRepoCreated.Id, req.G4101Quarter, req.G4101Year); err != nil {
			return nil, fmt.Errorf("assign schein to diagnoses by quarter error: %w", err)
		}
	}

	if err := srv.timelinePsychotherapyService.HandlePsychotherapies(ctx, scheinRepoCreated); err != nil {
		return nil, fmt.Errorf("handle psychotherapies error: %w", err)
	}

	if err := srv.CreateServiceCode88194Automatic(ctx, scheinRepoCreated.Id, req.HzvContractId, req.PatientId, doctorId); err != nil {
		return nil, fmt.Errorf("create service code 88194 automatic error: %w", err)
	}

	var relevantInsuranceInfo patient_profile_common.InsuranceInfo
	for _, insur := range patientProfile.PatientInfo.InsuranceInfos {
		if insur.Id == scheinRepoCreated.Schein.InsuranceId {
			relevantInsuranceInfo = insur
		}
	}

	if err := srv.validationTimelineService.RunValidation(ctx, req.PatientId, scheinRepoCreated.ContractId, timeline_common.TimelineEntityType_Service, nil); err != nil {
		return nil, fmt.Errorf("run validation error when create schein: %w", err)
	}

	if err := srv.updatePatientSnapshot(ctx, schein_common.PatientSnapshot{
		PatientId:   req.PatientId,
		PatientInfo: *patientProfile.PatientInfo,
		UpdatedAt:   util.NowUnixMillis(ctx),
	}); err != nil {
		return nil, errors.WithMessage(err, "update patient snapshot error when create schein")
	}

	return &schein.CreateScheinResponse{
			ScheinItem: &schein_common.ScheinItem{
				ScheinId:           *scheinRepoCreated.Id,
				ScheinMainGroup:    schein_common.MainGroup(scheinRepoCreated.Schein.ScheinMainGroup),
				KvTreatmentCase:    schein_common.TreatmentCaseNames(scheinRepoCreated.Schein.KvTreatmentCase),
				KvScheinSubGroup:   scheinRepoCreated.Schein.KvScheinSubGroup,
				G4101Year:          scheinRepoCreated.Schein.G4101Year,
				G4101Quarter:       scheinRepoCreated.Schein.G4101Quarter,
				HzvContractId:      scheinRepoCreated.ContractId,
				CreatedTime:        scheinRepoCreated.CreatedAt,
				MarkedAsBilled:     scheinRepoCreated.IsBilled,
				G4110:              relevantInsuranceInfo.EndDate,
				InsuranceId:        scheinRepoCreated.Schein.InsuranceId,
				ExcludeFromBilling: scheinRepoCreated.ExcludeFromBilling,
				ChargeSystemId:     scheinRepoCreated.ChargeSystemId,
				ScheinDetail:       scheinRepoCreated.ScheinDetail,
				DoctorId:           scheinRepo.DoctorId,
			},
		}, srv.HandleEventCreateRemoveSchein(ctx, schein.EventCreateRemoveSchein{
			Data: schein.ScheinChangedResponse{
				PatientId: req.PatientId,
			},
		})
}

func (srv *ScheinService) TakeOverScheinDiagnosis(ctx *titan.Context, req schein.TakeOverScheinDiagnisToRequest) error {
	scheinRepo, err := srv.scheinRepo.GetById(ctx, req.ScheinId)
	if err != nil {
		return err
	}

	if scheinRepo == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Schein_Not_Found)
	}

	err = srv.HandleScheinTakeOverDiagnoses(ctx, *scheinRepo, req.TakeOverDiagnoseInfos, req.NewTakeOverDiagnosis)
	if err != nil {
		return err
	}

	return nil
}

func (srv *ScheinService) GetScheinDetailById(ctx *titan.Context, request schein_common.GetScheinDetailByIdRequest) (*schein_common.GetScheinDetailByIdResponse, error) {
	result, err := srv.scheinRepo.FindById(ctx, request.ScheinId)
	if err != nil {
		return nil, err
	}

	if result == nil {
		return nil, nil
	}

	return util.NewPointer(result.ToScheinDetail()), nil
}

func (srv *ScheinService) GetScheinDetailByIds(ctx *titan.Context, request schein_common.GetScheinDetailByIdsRequest) (*schein_common.GetScheinDetailByIdsResponse, error) {
	scheins, err := srv.scheinRepo.FindByIds(ctx, request.ScheinIds)
	if err != nil {
		return nil, err
	}

	if len(scheins) == 0 {
		return nil, nil
	}
	result := schein_common.GetScheinDetailByIdsResponse{}
	for _, schein := range scheins {
		data := &schein_common.GetScheinDetailByIdResponse{
			PatientId:             schein.PatientId,
			DoctorId:              schein.DoctorId,
			ScheinMainGroup:       schein_common.MainGroup(schein.Schein.ScheinMainGroup),
			KvTreatmentCase:       schein_common.TreatmentCaseNames(schein.Schein.KvTreatmentCase),
			KvScheinSubGroup:      schein.Schein.KvScheinSubGroup,
			G4101Year:             schein.Schein.G4101Year,
			G4101Quarter:          schein.Schein.G4101Quarter,
			TariffType:            schein.Schein.TariffType,
			BgType:                schein.Schein.BgType,
			BgAccidentDate:        schein.Schein.BgAccidentDate,
			BgAccidentTime:        schein.Schein.BgAccidentTime,
			BgWorkingTimeFrom:     schein.Schein.BgWorkingTimeFrom,
			BgWorkingTimeTo:       schein.Schein.BgWorkingTimeTo,
			BgEmployerName:        schein.Schein.BgEmployerName,
			BgEmployerStreet:      schein.Schein.BgEmployerStreet,
			BgEmployerHousenumber: schein.Schein.BgEmployerHousenumber,
			BgEmployerPostcode:    schein.Schein.BgEmployerPostcode,
			BgEmployerCity:        schein.Schein.BgEmployerCity,
			BgEmployerCountry:     schein.Schein.BgEmployerCountry,
			HzvContractId:         schein.ContractId,
			ScheinDetails:         &schein.ScheinDetail,
			ScheinId:              *schein.Id,
			InsuranceId:           schein.Schein.InsuranceId,
		}
		result.Data = append(result.Data, *data)
	}
	return &result, nil
}

func (srv *ScheinService) UpdateSchein(ctx *titan.Context, request schein_common.UpdateScheinRequest) error {
	updateModel := scheinRepo.ScheinRepo{}
	if err := copier.Copy(&updateModel.Schein, request); err != nil {
		return err
	}

	updateModel.ScheinDetail.Psychotherapy = request.ScheinDetails.Psychotherapy
	if request.ScheinDetails != nil {
		if err := copier.Copy(&updateModel.ScheinDetail, request.ScheinDetails); err != nil {
			return err
		}
	}
	// TODO:PSYCHOTHERAPY add id if have no
	for i := range updateModel.ScheinDetail.Psychotherapy {
		if updateModel.ScheinDetail.Psychotherapy[i].Id == nil {
			updateModel.ScheinDetail.Psychotherapy[i].Id = util.NewUUID()
		}
	}

	old, err := srv.scheinRepo.FindById(ctx, request.ScheinId)
	if err != nil {
		return errors.WithMessage(err, "find schein by id error")
	}

	if old == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Schein_Not_Found)
	}

	if old.Schein.PatientSnapshot != nil {
		insuranceValidationSnapshot := &InsuranceSnapshotValidation{
			InsuranceId:       request.InsuranceId,
			patientInsurances: old.Schein.PatientSnapshot.PatientInfo.GetInsuranceIds(),
			ctx:               ctx,
		}
		if err := validator.RunWithoutParam[InsuranceSnapshotValidation](insuranceValidationSnapshot); err != nil {
			return err
		}
		updateModel.Schein.PatientSnapshot = old.Schein.PatientSnapshot
	}
	cached, err := srv.scheinRepo.FindOneAndUpdate(ctx, bson.M{
		scheinRepo.Field_Id: request.ScheinId,
	}, bson.M{
		"$set": bson.M{
			scheinRepo.Field_DoctorId:           request.DoctorId,
			scheinRepo.Field_AssignedToBsnrId:   request.AssignedToBsnrId,
			scheinRepo.Field_ContractId:         request.HzvContractId,
			scheinRepo.Field_Schein:             updateModel.Schein,
			scheinRepo.Field_ScheinDetail:       updateModel.ScheinDetail,
			scheinRepo.Field_UpdatedBy:          ctx.UserInfo().UserUUID(),
			scheinRepo.Field_UpdatedAt:          util.NowUnixMillis(ctx),
			scheinRepo.Field_ExcludeFromBilling: request.ExcludeFromBilling,
		},
	}, options.FindOneAndUpdate().SetReturnDocument(options.Before))
	if err != nil {
		return err
	}
	scheinUpdated, err := srv.scheinRepo.FindById(ctx, request.ScheinId)
	if err != nil {
		return fmt.Errorf("find schein by id error: %w", err)
	}

	if scheinUpdated == nil || scheinUpdated.Id == nil {
		return nil
	}
	if cached != nil {
		if !reflect.DeepEqual(cached.ScheinDetail.Psychotherapy, scheinUpdated.ScheinDetail.Psychotherapy) {
			if err := srv.timelinePsychotherapyService.UpdatePsychotherapy(ctx, &timeline_service.UpdatePsychotherapyRequest{
				PatientId:           scheinUpdated.PatientId,
				ScheinId:            *scheinUpdated.Id,
				Psychotherapy:       scheinUpdated.ScheinDetail.Psychotherapy,
				Ps4234:              scheinUpdated.ScheinDetail.Ps4234,
				IsDelete:            false,
				CachedPsychotherapy: cached.ScheinDetail.Psychotherapy,
				ScheinUpdated:       *scheinUpdated,
			}); err != nil {
				return fmt.Errorf("update psychotherapy error: %w", err)
			}
		}
	}

	if err := srv.OnUpdate.Execute(ctx, *scheinUpdated); err != nil {
		return fmt.Errorf("on update schein error: %w", err)
	}

	err = srv.validationTimelineService.RunValidation(ctx, request.PatientId, scheinUpdated.ContractId, timeline_common.TimelineEntityType_Service, nil)
	if err != nil {
		return fmt.Errorf("run validation error when update schein: %w", err)
	}

	return srv.notifier.NotifyCareProviderScheinChanged(ctx, &schein.EventScheinChanged{
		EventName: schein.EVENT_UpdateSchein,
		Data: schein.ScheinChangedResponse{
			PatientId: request.PatientId,
			ScheinId:  *scheinUpdated.Id,
		},
	})
}

func (srv *ScheinService) updateScheinBgFromMarkUnbilled(ctx *titan.Context, scheinId uuid.UUID) error {
	// update bg schein unbilled
	scheinRepoItem, err := srv.scheinRepo.UpdateBgScheinStatus(ctx, scheinId, schein_common.ScheinStatus_Normal)
	if err != nil {
		return err
	}
	// update bg billing history
	res, err := srv.bgBillingService.UpdateBgBillingFromMarkScheinUnbilled(ctx, scheinId)
	if err != nil {
		return err
	}
	// update invoice number in schein repo
	scheinRepoItem.BgScheinDetail.InvoiceNumber = &res.InvoiceNumber
	_, err = srv.scheinRepo.Update(ctx, *scheinRepoItem)
	if err != nil {
		return err
	}
	return nil
}

func (srv *ScheinService) updateScheinPrivateFromMarkUnbilled(ctx *titan.Context, scheinId uuid.UUID) error {
	scheinRepoItem, err := srv.scheinRepo.UpdatePrivateScheinStatus(ctx, scheinId, schein_common.ScheinStatus_Normal)
	if err != nil {
		return err
	}
	// update private billing history
	res, err := srv.privBillingService.UpdatePrivateBillingFromMarkScheinUnBilled(ctx, scheinId)
	if err != nil {
		return err
	}
	// update invoice number in schein repo
	scheinRepoItem.PrivateScheinDetail.InvoiceNumber = &res.InvoiceNumber
	_, err = srv.scheinRepo.Update(ctx, *scheinRepoItem)
	if err != nil {
		return err
	}
	return nil
}

func (srv *ScheinService) unMarkBilledForMedicinePrescription(ctx *titan.Context, scheinId uuid.UUID) error {
	filter := bson.M{
		timeline_repo.Field_ScheinIds: bson.M{
			"$in": []uuid.UUID{scheinId},
		},
	}

	timelines, err := srv.timelineService.Find(ctx, filter)
	if err != nil {
		return err
	}

	timelinesIds := []uuid.UUID{}

	for _, tl := range timelines {
		timelinesIds = append(timelinesIds, *tl.Id)
	}

	if len(timelinesIds) > 0 {
		if _, err := srv.timelineService.UpdateManyBillingInforForMedicinePrescription(ctx, nil, timelinesIds); err != nil {
			return err
		}
	}

	return nil
}

func (srv *ScheinService) updateScheinKvFromMarkUnbilled(ctx *titan.Context, scheinId uuid.UUID) error {
	updated, err := srv.scheinRepo.FindOneAndUpdate(ctx, bson.M{
		scheinRepo.Field_Id: scheinId,
	}, bson.M{
		"$set": bson.M{
			scheinRepo.Field_IsBilled:  false,
			scheinRepo.Field_UpdatedBy: ctx.UserInfo().UserUUID(),
			scheinRepo.Field_UpdatedAt: util.NowUnixMillis(ctx),
		},
	}, options.FindOneAndUpdate().SetReturnDocument(options.After))
	if err != nil {
		return err
	}

	if updated.Schein.ScheinMainGroup == string(schein_common.HZV) || updated.Schein.ScheinMainGroup == string(schein_common.FAV) {
		if err := srv.unMarkBilledForMedicinePrescription(ctx, scheinId); err != nil {
			return fmt.Errorf("unmark billed for medicine prescription error: %w", err)
		}
	}

	psychotherapyIds := []uuid.UUID{}
	for _, p := range updated.ScheinDetail.Psychotherapy {
		if p.Id != nil {
			psychotherapyIds = append(psychotherapyIds, *p.Id)
		}
	}
	if _, err := srv.timelinePsychotherapyService.UpdatePsychotherapyStatusByIds(ctx, psychotherapyIds, []patient_encounter.EncounterPsychotherapyStatus{
		patient_encounter.BILLED,
		patient_encounter.COMPLETED,
	}, patient_encounter.READY_TO_BILL); err != nil {
		return fmt.Errorf("update psychotherapy status error: %w", err)
	}
	return nil
}

func (srv *ScheinService) MarkNotBilled(ctx *titan.Context, request schein_common.MarkNotBilledRequest) error {
	entity, err := srv.scheinRepo.FindById(ctx, request.ScheinId)
	if err != nil {
		return err
	}
	if entity == nil || entity.Id == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Schein_Not_Found, "")
	}

	switch entity.Schein.ScheinMainGroup {
	case string(schein_common.PRIVATE), string(schein_common.IGEL):
		err = srv.updateScheinPrivateFromMarkUnbilled(ctx, request.ScheinId)
		if err != nil {
			return err
		}
	case string(schein_common.BG):
		err = srv.updateScheinBgFromMarkUnbilled(ctx, request.ScheinId)
		if err != nil {
			return err
		}
	default:
		err = srv.updateScheinKvFromMarkUnbilled(ctx, request.ScheinId)
		if err != nil {
			return err
		}
	}
	return nil
}

func (srv *ScheinService) MarkNotBilledByIds(ctx *titan.Context, ids []uuid.UUID) error {
	filter := bson.M{
		scheinRepo.Field_Id: bson.M{
			"$in": ids,
		},
	}
	updator := bson.M{
		"$set": bson.M{
			scheinRepo.Field_IsBilled:  false,
			scheinRepo.Field_UpdatedBy: ctx.UserInfo().UserUUID(),
			scheinRepo.Field_UpdatedAt: util.NowUnixMillis(ctx),
		},
	}

	var result []*scheinRepo.ScheinRepo
	_, err := srv.scheinRepo.Db.UpdateMany(ctx, filter, updator, &result)
	if err != nil {
		return err
	}

	psychotherapyIds := []uuid.UUID{}
	for _, schein := range result {
		for _, p := range schein.ScheinDetail.Psychotherapy {
			if p.Id != nil {
				psychotherapyIds = append(psychotherapyIds, *p.Id)
			}
		}
	}

	if _, err := srv.timelinePsychotherapyService.UpdatePsychotherapyStatusByIds(ctx, psychotherapyIds, []patient_encounter.EncounterPsychotherapyStatus{
		patient_encounter.BILLED,
		patient_encounter.COMPLETED,
	}, patient_encounter.READY_TO_BILL); err != nil {
		return fmt.Errorf("update psychotherapy status error: %w", err)
	}

	return nil
}

func (srv *ScheinService) MarkBilledByIds(ctx *titan.Context, ids []uuid.UUID) error {
	filter := bson.M{
		scheinRepo.Field_Id: bson.M{
			"$in": ids,
		},
	}
	updator := bson.M{
		"$set": bson.M{
			scheinRepo.Field_IsBilled:  true,
			scheinRepo.Field_UpdatedBy: ctx.UserInfo().UserUUID(),
			scheinRepo.Field_UpdatedAt: util.NowUnixMillis(ctx),
		},
	}

	var result []*scheinRepo.ScheinRepo
	_, err := srv.scheinRepo.Db.UpdateMany(ctx, filter, updator, &result)
	if err != nil {
		return err
	}

	psychotherapyIds := []uuid.UUID{}
	for _, schein := range result {
		for _, p := range schein.ScheinDetail.Psychotherapy {
			if p.Id != nil {
				psychotherapyIds = append(psychotherapyIds, *p.Id)
			}
		}
	}

	if _, err := srv.timelinePsychotherapyService.UpdatePsychotherapyStatusByIds(ctx, psychotherapyIds, []patient_encounter.EncounterPsychotherapyStatus{
		patient_encounter.READY_TO_BILL,
	}, patient_encounter.BILLED); err != nil {
		return fmt.Errorf("update psychotherapy status error: %w", err)
	}

	return nil
}

func (srv *ScheinService) DeleteSchein(ctx *titan.Context, request schein_common.DeleteScheinRequest) error {
	totalTimeline, err := srv.timelineService.CountByScheinId(ctx, request.PatientId, request.ScheinId)
	if err != nil {
		return fmt.Errorf("count timeline by schein id: %w", err)
	}
	if totalTimeline > 0 {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_ScheinHasTimeline, "")
	}

	activeDocuments, err := srv.edmpService.GetActiveDocumentsByScheinId(ctx, request.ScheinId, request.PatientId)
	if err != nil {
		return fmt.Errorf("get active documents by patient id: %w", err)
	}
	if len(activeDocuments) > 0 {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_ScheinHasActiveDocuments, "")
	}

	deletedSchein, err := srv.scheinRepo.FindOneAndUpdate(ctx, bson.M{
		scheinRepo.Field_PatientId: request.PatientId,
		scheinRepo.Field_Id:        request.ScheinId,
	}, bson.M{
		"$set": bson.M{
			scheinRepo.Field_IsDeleted: true,
			scheinRepo.Field_UpdatedBy: ctx.UserInfo().UserUUID(),
			scheinRepo.Field_UpdatedAt: util.NowUnixMillis(ctx),
		},
	}, &options.FindOneAndUpdateOptions{
		ReturnDocument: util.NewPointer(options.After),
	})
	if err != nil {
		return err
	}

	// delete billing
	err = srv.privBillingService.DeletePrivateBillingByScheinId(ctx, request.ScheinId)
	if err != nil {
		return err
	}

	err = srv.bgBillingService.DeleteBgBillingByScheinId(ctx, request.ScheinId)
	if err != nil {
		return err
	}

	if deletedSchein != nil {
		err = srv.timelinePsychotherapyService.UpdatePsychotherapy(ctx, &timeline_service.UpdatePsychotherapyRequest{
			PatientId:     deletedSchein.PatientId,
			ScheinId:      *deletedSchein.Id,
			IsDelete:      true,
			Psychotherapy: deletedSchein.ScheinDetail.Psychotherapy,
			Ps4234:        deletedSchein.ScheinDetail.Ps4234,
		})
		if err != nil {
			return fmt.Errorf("update psychotherapy error: %w", err)
		}

		if err := srv.validationTimelineService.RunValidation(ctx, deletedSchein.PatientId, deletedSchein.ContractId, timeline_common.TimelineEntityType_Service, nil); err != nil {
			return fmt.Errorf("run validation error when delete schein: %w", err)
		}
	}
	return srv.HandleEventCreateRemoveSchein(ctx, schein.EventCreateRemoveSchein{
		Data: schein.ScheinChangedResponse{
			PatientId: request.PatientId,
		},
	})
}

func (srv *ScheinService) GetScheinDetail(ctx *titan.Context, request schein_common.GetScheinDetailRequest) (*schein_common.GetScheinDetailResponse, error) {
	res, err := srv.scheinRepo.FindByPatient(ctx, request.PatientId)
	if err != nil {
		return nil, err
	}

	scheinIds := slice.Map(res, func(t scheinRepo.ScheinRepo) uuid.UUID {
		return *t.Id
	})

	return &schein_common.GetScheinDetailResponse{
		ScheinIds: scheinIds,
	}, nil
}

func (srv *ScheinService) GetFields(ctx *titan.Context, request schein_common.GetFieldsRequest) (*schein_common.GetFieldsResponse, error) {
	var gender *patient_profile_common.Gender
	if request.PatientId != nil {
		patientProfile, err := srv.patientProfileService.GetProfileById(ctx, profile.GetByIdRequest{
			PatientId: request.PatientId,
		})
		if err != nil {
			return nil, err
		}

		gender = &patientProfile.PatientInfo.PersonalInfo.Gender
	}

	caseFields := srv.fieldService.GetFields(string(request.TreatmentCase), gender)
	if caseFields == nil {
		return nil, nil
	}

	subGroupsStr := []schein_common.SubGroupRules{}
	for _, v := range caseFields.SubGroups {
		rulesStrArr := slice.Map(v.Rules, func(v1 json.Token) string {
			res2B, _ := json.Marshal(v1)
			return string(res2B)
		})
		subGroupsStr = append(subGroupsStr, schein_common.SubGroupRules{
			Code:  v.Code,
			Rules: rulesStrArr,
		})
	}

	specicalRulesStrArr := slice.Map(caseFields.SpecialRules, func(t json.Token) string {
		res2B, _ := json.Marshal(t)
		return string(res2B)
	})

	rs := &schein_common.GetFieldsResponse{CaseFields: schein_common.CaseFields{
		Code:         caseFields.Code,
		Fields:       caseFields.Fields,
		SubGroups:    subGroupsStr,
		SpecialRules: specicalRulesStrArr,
	}}

	return rs, nil
}

func (*ScheinService) ToScheinItem(scheinRepo *scheinRepo.ScheinRepo) schein_common.ScheinItem {
	invoiceNumber := scheinRepo.PrivateScheinDetail.InvoiceNumber

	if scheinRepo.BgScheinDetail.InvoiceNumber != nil {
		invoiceNumber = scheinRepo.BgScheinDetail.InvoiceNumber
	}

	isGeneral := scheinRepo.BgScheinDetail.BGType == schein_common.GereralAction

	var issueDate *int64
	if scheinRepo.Schein.ScheinMainGroup == string(schein_common.PRIVATE) || scheinRepo.Schein.ScheinMainGroup == string(schein_common.IGEL) {
		issueDate = &scheinRepo.PrivateScheinDetail.IssueDate
	} else if scheinRepo.Schein.ScheinMainGroup == string(schein_common.BG) {
		issueDate = &scheinRepo.BgScheinDetail.CreatedOn
	}

	return schein_common.ScheinItem{
		ScheinId:           *scheinRepo.Id,
		ScheinMainGroup:    schein_common.MainGroup(scheinRepo.Schein.ScheinMainGroup),
		KvTreatmentCase:    schein_common.TreatmentCaseNames(scheinRepo.Schein.KvTreatmentCase),
		KvScheinSubGroup:   scheinRepo.Schein.KvScheinSubGroup,
		G4101Year:          scheinRepo.Schein.G4101Year,
		G4101Quarter:       scheinRepo.Schein.G4101Quarter,
		HzvContractId:      scheinRepo.ContractId,
		CreatedTime:        scheinRepo.CreatedAt,
		MarkedAsBilled:     scheinRepo.IsBilled,
		InsuranceId:        scheinRepo.Schein.InsuranceId,
		ExcludeFromBilling: scheinRepo.ExcludeFromBilling,
		UpdatedBy:          scheinRepo.UpdatedBy,
		UpdatedAt:          scheinRepo.UpdatedAt,
		ChargeSystemId:     scheinRepo.ChargeSystemId,
		TsvgContactType:    scheinRepo.ScheinDetail.TsvgContactType,
		TsvgInfor:          scheinRepo.ScheinDetail.TsvgInfor,
		TsvgTranferCode:    scheinRepo.ScheinDetail.TsvgTranferCode,
		TsvgContactDate:    scheinRepo.ScheinDetail.TsvgContactDate,
		ScheinDetail:       scheinRepo.ScheinDetail,
		IsTechnicalSchein:  scheinRepo.IsTechnicalSchein,
		DoctorId:           scheinRepo.DoctorId,
		IssueDate:          issueDate,
		ScheinStatus:       util.NewPointer(scheinRepo.ScheinStatus),
		InvoiceNumber:      invoiceNumber,
		ReferralDoctor:     scheinRepo.ReferralDoctor,
		IsGeneral:          &isGeneral,
		AccidentDate:       &scheinRepo.BgScheinDetail.AccidentDate,
		JobOccupation:      &scheinRepo.BgScheinDetail.EmploymentInfo.Occupation,
		ArrivalDate:        scheinRepo.BgScheinDetail.ArrivalDate,
		WorkingTimeStart:   scheinRepo.BgScheinDetail.WorkingTimeStart,
		WorkingTimeEnd:     scheinRepo.BgScheinDetail.WorkingTimeEnd,
		CompanyAddress:     scheinRepo.BgScheinDetail.EmploymentInfo.CompanyAddress,
		BgScheinDetail:     &scheinRepo.BgScheinDetail,
	}
}

func (srv *ScheinService) GetScheinsOverview(ctx *titan.Context, request schein_common.GetScheinsOverviewRequest) (*schein_common.GetScheinsOverviewResponse, error) {
	var (
		year    *int32
		quarter *int32
	)
	if request.Quarter != nil {
		year = &request.Quarter.Year
		quarter = &request.Quarter.Quarter
	}
	scheinsByPatient, err := srv.scheinRepo.FilterSchein(ctx, scheinRepo.FilterScheinParams{
		PatientId:    util.NewPointer(request.PatientId),
		G4101Quarter: quarter,
		G4101Year:    year,
		MainGroup:    request.MainGroup,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "get scheins by patient id quarter error")
	}

	patientProfile, err := srv.patientProfileService.GetProfileById(ctx, profile.GetByIdRequest{
		PatientId: &request.PatientId,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patient profile")
	}

	privateIKnumbers := make([]string, 0)
	for i := range patientProfile.PatientInfo.InsuranceInfos {
		insurance := &patientProfile.PatientInfo.InsuranceInfos[i]
		if insurance.InsuranceType == patient_profile_common.Private {
			privateIKnumbers = append(privateIKnumbers, strconv.Itoa(int(insurance.IkNumber)))
		}
	}

	if len(privateIKnumbers) != 0 {
		getTerminateInfoOfListSdikRes, err := srv.fieldService.catalogSdikService.GetTerminateInfoOfListSdik(ctx, &catalog_sdik_api.GetTerminateInfoOfListSdikRequest{
			IkNumbers: privateIKnumbers,
		})
		if err != nil {
			return nil, err
		}
		if getTerminateInfoOfListSdikRes != nil && len(getTerminateInfoOfListSdikRes.TerminateInfo) > 0 {
			mapTerminateInfo := getTerminateInfoOfListSdikRes.TerminateInfo
			for i := range patientProfile.PatientInfo.InsuranceInfos {
				insurance := &patientProfile.PatientInfo.InsuranceInfos[i]
				if insurance.InsuranceType == patient_profile_common.Private {
					ikNumber := strconv.Itoa(int(insurance.IkNumber))
					if isTerminated, ok := mapTerminateInfo[ikNumber]; ok {
						insurance.IsTerminated = util.NewBool(isTerminated)
					}
				}
			}
		}
	}

	scheinItems := slice.Map(scheinsByPatient, func(t scheinRepo.ScheinRepo) schein_common.ScheinItem {
		var relevantInsuranceInfo patient_profile_common.InsuranceInfo
		for i := range patientProfile.PatientInfo.InsuranceInfos {
			insur := &patientProfile.PatientInfo.InsuranceInfos[i]
			if insur.Id == t.Schein.InsuranceId {
				if t.ContractId != nil {
					contract := srv.contractService.GetContractDetailById(*t.ContractId)
					tel, fax := contract.GetContact(ctx, strconv.FormatInt(int64(insur.IkNumber), 10))
					insur.Tel = &tel
					insur.Fax = &fax
				}
				relevantInsuranceInfo = *insur
			}
		}

		scheinItem := srv.ToScheinItem(&t)
		scheinItem.G4110 = relevantInsuranceInfo.EndDate

		return scheinItem
	})
	return &schein_common.GetScheinsOverviewResponse{
		ScheinItems:   scheinItems,
		InsuranceInfo: patientProfile.PatientInfo.InsuranceInfos,
	}, nil
}

func (srv *ScheinService) CheckExistKVScheinCurrentQuarter(ctx *titan.Context, request schein_common.CheckExistKVScheinCurrentQuarterRequest) (*schein_common.CheckExistKVScheinCurrentQuarterResponse, error) {
	req := scheinRepo.FilterScheinParams{
		PatientId:    util.NewPointer(request.PatientId),
		G4101Quarter: util.NewPointer(util.NowQuarter(ctx)),
		G4101Year:    util.NewPointer(util.NowYear(ctx)),
	}
	if request.Quarter != nil {
		req.G4101Quarter = request.Quarter
	}
	if request.Year != nil {
		req.G4101Year = request.Year
	}
	have, err := srv.scheinRepo.CheckExistKVScheinCurrentQuarter(ctx, req)
	if err != nil {
		return nil, err
	}

	return &schein_common.CheckExistKVScheinCurrentQuarterResponse{
		IsExist: have,
	}, nil
}

func (srv *ScheinService) HandleEventScheinChanged(ctx *titan.Context, request schein.EventScheinChanged) error {
	data := schein.EventScheinChanged{
		Data: schein.ScheinChangedResponse{
			PatientId: request.Data.PatientId,
			ScheinId:  request.Data.ScheinId,
		},
	}

	return srv.notifier.NotifyCareProviderScheinChanged(ctx, &data)
}

func (srv *ScheinService) HandleEventCreateRemoveSchein(ctx *titan.Context, request schein.EventCreateRemoveSchein) error {
	data := schein.EventCreateRemoveSchein{
		Data: schein.ScheinChangedResponse{
			PatientId: request.Data.PatientId,
		},
	}
	return srv.notifier.NotifyCareProviderCreateRemoveSchein(ctx, &data)
}

func (*ScheinService) GetSetting(ctx *titan.Context) (*schein_common.GetSettingResponse, error) {
	getsettingFunc, err := settings.GetSettingsFlow.SafeResolve()
	if err != nil {
		return nil, fmt.Errorf("get setting error: %w", err)
	}
	settingsRs, err := getsettingFunc(ctx, settings.SettingsRequest{
		Feature: settings_common.SettingsFeatures_SCHEIN,
	})
	if err != nil {
		return nil, err
	}

	// init default schein setting if null
	if settingsRs == nil || len(settingsRs.Settings) == 0 {
		return &schein_common.GetSettingResponse{
			AssignDiagnosisCreatedschein:      schein_common.AssigndiagnosisManually,
			PreviouslySelected:                false,
			DefaultTreatmentcase:              util.NewString(string(schein_common.TCKvOutpatient)),
			DefaultSubgroup:                   util.NewString("00"),
			AutoCreatekvscheinHzvfav:          false,
			ComposerAutoSelectscheinDiagnosis: true,
			ShowWhenPatientCardIsRead:         "everyTime",
			IncludeCurrentPatient:             false,
			ShowHintSpecialGroup09:            true,
			HideHintForTfSG:                   false,
		}, nil
	}

	settingValues := settingsRs.Settings
	return &schein_common.GetSettingResponse{
		AssignDiagnosisCreatedschein:      schein_common.Assigndiagnosis(settingValues[string(settingApi.AssignDiagnosisCreatedschein)]),
		PreviouslySelected:                cast.ToBool(settingValues[string(settingApi.PreviouslySelected)]),
		DefaultTreatmentcase:              util.NewString(settingValues[string(settingApi.DefaultTreatmentcase)]),
		DefaultSubgroup:                   util.NewString(settingValues[string(settingApi.DefaultSubgroup)]),
		AutoCreatekvscheinHzvfav:          cast.ToBool(settingValues[string(settingApi.AutoCreateKVScheinHzvFav)]),
		ComposerAutoSelectscheinDiagnosis: cast.ToBool(settingValues[string(settingApi.ComposerAutoSelectscheinDiagnosis)]),
		ShowWhenPatientCardIsRead:         settingValues[string(settingApi.ShowWhenPatientCardIsRead)],
		IncludeCurrentPatient:             cast.ToBool(settingValues[string(settingApi.IncludeCurrentPatient)]),
		ShowHintSpecialGroup09:            cast.ToBool(settingValues[string(settingApi.ShowHintSpecialGroup09)]),
		HideHintForTfSG:                   cast.ToBool(settingValues[string(settingApi.HideHintForTfSG)]),
	}, nil
}

func (*ScheinService) SaveSetting(ctx *titan.Context, request schein_common.SaveSettingRequest) error {
	setting := map[string]string{}
	setting[string(settingApi.AssignDiagnosisCreatedschein)] = string(request.AssignDiagnosisCreatedschein)
	setting[string(settingApi.PreviouslySelected)] = cast.ToString(request.PreviouslySelected)
	if request.DefaultTreatmentcase != nil {
		setting[string(settingApi.DefaultTreatmentcase)] = *request.DefaultTreatmentcase
	}
	if request.DefaultSubgroup != nil {
		setting[string(settingApi.DefaultSubgroup)] = *request.DefaultSubgroup
	}
	setting[string(settingApi.AutoCreateKVScheinHzvFav)] = cast.ToString(request.AutoCreatekvscheinHzvfav)
	setting[string(settingApi.ComposerAutoSelectscheinDiagnosis)] = cast.ToString(request.ComposerAutoSelectscheinDiagnosis)
	setting[string(settingApi.ShowWhenPatientCardIsRead)] = cast.ToString(request.ShowWhenPatientCardIsRead)
	setting[string(settingApi.IncludeCurrentPatient)] = cast.ToString(request.IncludeCurrentPatient)
	setting[string(settingApi.ShowHintSpecialGroup09)] = cast.ToString(request.ShowHintSpecialGroup09)
	setting[string(settingApi.HideHintForTfSG)] = cast.ToString(request.HideHintForTfSG)

	saveSettingFlow, err := settings.SaveSettingFlow.SafeResolve()
	if err != nil {
		return fmt.Errorf("get save setting flow error: %w", err)
	}

	return saveSettingFlow(ctx, settingApi.SaveSettingsRequest{
		Feature:  settings_common.SettingsFeatures_SCHEIN,
		Settings: setting,
	})
}

func (srv *ScheinService) GetSelectedTreatmentCaseSubgroup(ctx *titan.Context, request schein_common.GetSelectedTreatmentCaseSubgroupRequest) (*schein_common.GetSelectedTreatmentCaseSubgroupResponse, error) {
	rs := &schein_common.GetSelectedTreatmentCaseSubgroupResponse{}
	setting, err := srv.GetSetting(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if setting.PreviouslySelected {
		schein, err := srv.scheinRepo.GetLastScheinsByPatientAndMainGroup(ctx, scheinRepo.PatientAndMainGroupRequest{
			PatientId: request.PatientId,
			MainGroup: string(schein_common.KV),
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if schein == nil {
			if setting.DefaultTreatmentcase != nil {
				rs.KvTreatmentCase = schein_common.TreatmentCaseNames(*setting.DefaultTreatmentcase)
			}
			rs.KvScheinSubGroup = setting.DefaultSubgroup
			return rs, nil
		}

		rs.KvTreatmentCase = schein_common.TreatmentCaseNames(schein.Schein.KvTreatmentCase)
		rs.KvScheinSubGroup = schein.Schein.KvScheinSubGroup
	} else {
		if setting.DefaultTreatmentcase != nil {
			rs.KvTreatmentCase = schein_common.TreatmentCaseNames(*setting.DefaultTreatmentcase)
		}
		rs.KvScheinSubGroup = setting.DefaultSubgroup
	}
	return rs, nil
}

func (*ScheinService) GetOrderList(ctx *titan.Context) (*schein_common.GetOrderListResponse, error) {
	getSettingsFlow, err := settings.GetSettingsFlow.SafeResolve()
	if err != nil {
		return nil, fmt.Errorf("get setting flow error: %w", err)
	}
	settingsRs, err := getSettingsFlow(ctx, settings.SettingsRequest{
		Feature: settings_common.SettingsFeatures_SCHEIN_I4205,
	})
	if err != nil {
		return nil, fmt.Errorf("get setting error: %w", err)
	}

	if settingsRs == nil || len(settingsRs.Settings) == 0 {
		return &schein_common.GetOrderListResponse{
			OrderValues: []schein_common.OrderValue{},
		}, nil
	}

	m := []schein_common.OrderValue{}
	settingValues := settingsRs.Settings
	itemsString := settingValues[string(settingApi.I4205Items)]
	err = json.Unmarshal([]byte(itemsString), &m)
	if err != nil {
		return nil, err
	}
	return &schein_common.GetOrderListResponse{
		OrderValues: m,
	}, nil
}

func (*ScheinService) SaveOrderList(ctx *titan.Context, request schein_common.SaveOrderListRequest) error {
	setting := map[string]string{}
	data, err := json.Marshal(request.OrderValues)
	if err != nil {
		return err
	}

	setting[string(settingApi.I4205Items)] = string(data)

	saveSettingFlow, err := settings.SaveSettingFlow.SafeResolve()
	if err != nil {
		return fmt.Errorf("get save setting flow error: %w", err)
	}

	return saveSettingFlow(ctx, settingApi.SaveSettingsRequest{
		Feature:  settings_common.SettingsFeatures_SCHEIN_I4205,
		Settings: setting,
	})
}

// CreateServiceCode88194Automatic creates service for KV patient which having Hzv contract
func (srv *ScheinService) CreateServiceCode88194Automatic(
	ctx *titan.Context,
	scheinId *uuid.UUID,
	contractId *string,
	patientId uuid.UUID,
	doctorId uuid.UUID,
) error {
	patientParticipations, err := srv.patientParticipationRepo.Count(ctx, bson.M{
		patient_participation.Field_PatientId: patientId,
		patient_participation.Field_Status:    patient_participation.PatientParticipationStatus_Active,
	})
	if err != nil {
		return err
	}

	var scheinToCreate []*common.ScheinWithMainGroup
	scheinToCreate = append(scheinToCreate, &common.ScheinWithMainGroup{
		ScheinId: scheinId,
		Group:    common.KV,
	})
	if patientParticipations > 0 {
		_, err := srv.timelineService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
			ContractId:        contractId,
			TreatmentDoctorId: doctorId,
			BillingDoctorId:   &doctorId,
			PatientId:         patientId,
			ScheinIds:         []uuid.UUID{*scheinId},
			EncounterCase:     util.NewPointer(patient_encounter.AB),
			Payload: patient_encounter.EncounterServiceTimeline{
				Code:               "88194",
				Description:        "Kennzeichnung von selektivvertraglichen/knappschaftsärztlichen Behandlungsfällen gemäß Nr. 11 der Präambel 3.1",
				PatientId:          &patientId,
				FreeText:           "(88194) Kennzeichnung von selektivvertraglichen/knappschaftsärztlichen Behandlungsfällen gemäß Nr. 11 der Präambel 3.1",
				ReferralDoctorInfo: nil,
				MaterialCosts:      nil,
				CareFacility:       nil,
				Command:            "L",
				Sources:            util.NewPointer(patient_encounter.Composer),
				ServiceMainGroup:   util.NewPointer(common.KV),
				Scheins:            &scheinToCreate,
				ApprovalStatus:     util.NewPointer(patient_encounter.IsApproval),
			},
		},
		)
		if err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func (srv *ScheinService) GetSubGroupFromMasterData(ctx *titan.Context, request schein.GetSubGroupFromMasterDataRequest) (*schein.GetSubGroupFromMasterDataResponse, error) {
	subgroups, err := srv.fieldService.sdkvService.GetSubGroupByBsnr(ctx, sdkv.GetSDKVByBsnrRequest{Bsnr: request.Bsnr})
	if err != nil {
		return nil, err
	}
	if subgroups == nil {
		return nil, nil
	}
	return &schein.GetSubGroupFromMasterDataResponse{
		Keys: subgroups.Data,
	}, nil
}

func (srv *ScheinService) GetBillingAreaFromMasterData(ctx *titan.Context, request schein.GetBillingAreaFromMasterDataRequest) (*schein.GetBillingAreaFromMasterDataResponse, error) {
	ps4122s, err := srv.fieldService.sdkvService.Get4122(ctx, sdkv.Get4122Request{
		Bsnr:     request.Bsnr,
		Subgroup: request.Subgroup,
	})
	if ps4122s == nil {
		return nil, nil
	}
	return &schein.GetBillingAreaFromMasterDataResponse{
		Keys: ps4122s.Data,
	}, err
}

func (srv *ScheinService) GetRezidivList(_ *titan.Context) (*schein.GetRezidivListResponse, error) {
	rezidivList := srv.rezidivRepo.GetAllRezidivList()
	return &schein.GetRezidivListResponse{
		Data: slice.Map(rezidivList, func(t rezidiv.Rezidiv) schein.Rezidiv {
			return schein.Rezidiv{
				Code:    t.Code,
				Content: t.Content,
			}
		}),
	}, nil
}

func (app *ScheinService) GetPsychotherapyById(ctx *titan.Context, request schein.GetPsychotherapyByIdRequest) (*schein.GetPsychotherapyByIdResponse, error) {
	result := schein.GetPsychotherapyByIdResponse{}
	schein, err := app.scheinRepo.FindOne(ctx, bson.M{
		scheinRepo.Field_Psychotherapy_Id: request.PsychotherapyId,
	})
	if err != nil {
		return nil, err
	}
	if schein == nil {
		return nil, nil
	}
	psychotherapy := slice.FindOne(schein.ScheinDetail.Psychotherapy, func(p schein_common.Psychotherapy) bool {
		return p.Id.String() == request.PsychotherapyId.String()
	})
	if psychotherapy == nil {
		return nil, nil
	}
	result.Data = *psychotherapy
	return &result, nil
}

func (app *ScheinService) GetScheinByInsuranceId(ctx *titan.Context, request schein.GetScheinByInsuranceIdRequest) (*schein.GetScheinByInsuranceIdResponse, error) {
	scheinItem, err := app.scheinRepo.GetScheinByInsuranceId(ctx, request.InsuranceId)
	if err != nil {
		return nil, fmt.Errorf("failed to get schein by insurance id %v", request.InsuranceId)
	}
	if scheinItem == nil || scheinItem.Id == nil {
		return nil, nil
	}

	return &schein.GetScheinByInsuranceIdResponse{
		ScheinItem: schein_common.ScheinItem{
			ScheinId:           *scheinItem.Id,
			ScheinMainGroup:    schein_common.MainGroup(scheinItem.Schein.ScheinMainGroup),
			KvTreatmentCase:    schein_common.TreatmentCaseNames(scheinItem.Schein.KvTreatmentCase),
			KvScheinSubGroup:   scheinItem.Schein.KvScheinSubGroup,
			G4101Year:          scheinItem.Schein.G4101Year,
			G4101Quarter:       scheinItem.Schein.G4101Quarter,
			HzvContractId:      scheinItem.ContractId,
			CreatedTime:        scheinItem.CreatedAt,
			MarkedAsBilled:     scheinItem.IsBilled,
			InsuranceId:        scheinItem.Schein.InsuranceId,
			ExcludeFromBilling: scheinItem.ExcludeFromBilling,
			ChargeSystemId:     scheinItem.ChargeSystemId,
			ScheinDetail:       scheinItem.ScheinDetail,
			DoctorId:           scheinItem.DoctorId,
		},
	}, nil
}

func (app *ScheinService) GetScheinsByInsuranceId(ctx *titan.Context, request schein.GetScheinByInsuranceIdRequest) (*schein.GetScheinsByInsuranceIdResponse, error) {
	scheinItems, err := app.scheinRepo.GetScheinsByInsuranceId(ctx, request.InsuranceId)
	if err != nil {
		return nil, fmt.Errorf("failed to get scheins by insurance id %v", request.InsuranceId)
	}
	return &schein.GetScheinsByInsuranceIdResponse{
		ScheinItems: slice.Map(scheinItems, func(scheinItem scheinRepo.ScheinRepo) schein_common.ScheinItem {
			return schein_common.ScheinItem{
				ScheinId:           *scheinItem.Id,
				ScheinMainGroup:    schein_common.MainGroup(scheinItem.Schein.ScheinMainGroup),
				KvTreatmentCase:    schein_common.TreatmentCaseNames(scheinItem.Schein.KvTreatmentCase),
				KvScheinSubGroup:   scheinItem.Schein.KvScheinSubGroup,
				G4101Year:          scheinItem.Schein.G4101Year,
				G4101Quarter:       scheinItem.Schein.G4101Quarter,
				HzvContractId:      scheinItem.ContractId,
				CreatedTime:        scheinItem.CreatedAt,
				MarkedAsBilled:     scheinItem.IsBilled,
				InsuranceId:        scheinItem.Schein.InsuranceId,
				ExcludeFromBilling: scheinItem.ExcludeFromBilling,
				ChargeSystemId:     scheinItem.ChargeSystemId,
				ScheinDetail:       scheinItem.ScheinDetail,
				DoctorId:           scheinItem.DoctorId,
			}
		}),
	}, nil
}

func (app *ScheinService) GetSvScheinsByDoctorIds(ctx *titan.Context, req GetSvScheins) ([]schein_common.GetScheinDetailByIdResponse, error) {
	yearQuarters := make([]util.YearQuarter, 0, len(req.Times))
	for _, time := range req.Times {
		yearQuarters = append(yearQuarters, util.YearQuarter{
			Year:    time.Year,
			Quarter: time.Quarter,
		})
	}
	scheins, err := app.scheinRepo.GetSvScheinsByDoctorIds(ctx, req.DoctorIds, yearQuarters, req.ContractIds)
	if err != nil {
		return nil, fmt.Errorf("failed to get scheins by doctor ids %v", req.DoctorIds)
	}
	result := slice.Reduce(scheins, func(curr []schein_common.GetScheinDetailByIdResponse, next scheinRepo.ScheinRepo) []schein_common.GetScheinDetailByIdResponse {
		schein := next.ToScheinDetail()
		isIgnore := false
		for _, ignore := range req.IgnoreBillings {
			isSameContract := *schein.HzvContractId == ignore.ContractId
			isSameYearQuarter := *schein.G4101Quarter == ignore.YearQuarter.Quarter && *schein.G4101Year == ignore.YearQuarter.Year
			isSameDoctor := schein.DoctorId.String() == ignore.DoctorId.String()
			isSamePatient := schein.PatientId.String() == ignore.PatientId.String()

			if isSameContract &&
				isSameYearQuarter &&
				isSameDoctor &&
				isSamePatient {
				isIgnore = true
				break
			}
		}
		if isIgnore {
			return curr
		}
		return append(curr, schein)
	}, []schein_common.GetScheinDetailByIdResponse{})
	return result, nil
}

func (app *ScheinService) GetScheinById(ctx *titan.Context, id uuid.UUID) (*schein_common.Schein, error) {
	scheinRepo, err := app.scheinRepo.FindOne(ctx, bson.M{
		scheinRepo.Field_Id:        id,
		scheinRepo.Field_IsDeleted: false,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get schein by id")
	}
	if scheinRepo == nil {
		return nil, nil
	}
	schein := pkg_copy.CloneTo[schein_common.Schein](scheinRepo.Schein)
	return &schein, nil
}

type FullScheinInfo struct {
	Id                  *uuid.UUID
	DoctorId            uuid.UUID
	PatientId           uuid.UUID
	ContractId          *string
	Schein              schein_common.Schein
	ScheinDetail        schein_common.ScheinDetail
	UpdatedBy           *uuid.UUID
	CreatedBy           uuid.UUID
	CreatedAt           int64
	UpdatedAt           *int64
	IsDeleted           bool
	IsBilled            bool
	ExcludeFromBilling  bool
	ChargeSystemId      *string
	IsBilledFaulty      bool
	IsTechnicalSchein   *bool
	PrivateScheinDetail private_schein_common.PrivateScheinDetail
	ScheinStatus        schein_common.ScheinStatus
	ReferralDoctor      *schein_common.ReferralDoctor
}

func (app *ScheinService) GetFullScheinById(ctx *titan.Context, id uuid.UUID) (*FullScheinInfo, error) {
	scheinRepo, err := app.scheinRepo.FindOne(ctx, bson.M{
		scheinRepo.Field_Id:        id,
		scheinRepo.Field_IsDeleted: false,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get schein by id")
	}
	if scheinRepo == nil {
		return nil, nil
	}
	schein := pkg_copy.CloneTo[FullScheinInfo](scheinRepo)
	return &schein, nil
}

func (app *ScheinService) GetScheinByIds(ctx *titan.Context, ids ...uuid.UUID) ([]schein_common.Schein, error) {
	scheinRepos, err := app.scheinRepo.Find(ctx, bson.M{
		scheinRepo.Field_Id:        bson.M{"$in": ids},
		scheinRepo.Field_IsDeleted: false,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get schein by id")
	}
	if len(scheinRepos) == 0 {
		return nil, nil
	}

	results := make([]schein_common.Schein, 0, len(scheinRepos))
	for _, schein := range scheinRepos {
		results = append(results, pkg_copy.CloneTo[schein_common.Schein](schein.Schein))
	}
	return results, nil
}

func (srv *ScheinService) RevertTechnicalSchein(ctx *titan.Context, request schein.RevertTechnicalScheinRequest) error {
	updated, err := srv.scheinRepo.FindOneAndUpdate(ctx, bson.M{
		scheinRepo.Field_Id: request.ScheinId,
	}, bson.M{
		"$set": bson.M{
			scheinRepo.Field_IsTechnicalSchein: nil,
			scheinRepo.Field_UpdatedBy:         ctx.UserInfo().UserUUID(),
			scheinRepo.Field_UpdatedAt:         util.NowUnixMillis(ctx),
		},
	})
	if err != nil {
		return err
	}
	err = srv.notifier.NotifyClientScheinChanged(ctx, &schein.EventScheinChanged{
		Data: schein.ScheinChangedResponse{
			PatientId: updated.PatientId,
		},
	})
	return err
}

type GetInsuranceResponse struct {
	InsuranceInfo *patient_profile_common.InsuranceInfo
}

func (srv *ScheinService) GetPatientInsurance(ctx *titan.Context, scheidnId uuid.UUID) (*GetInsuranceResponse, error) {
	res, err := srv.scheinRepo.GetById(ctx, scheidnId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get schein by id")
	}

	if res == nil || res.IsDeleted {
		return nil, nil
	}

	patientProfile, err := srv.patientProfileService.GetProfileById(ctx, profile.GetByIdRequest{
		PatientId: &res.PatientId,
	})

	if patientProfile == nil || patientProfile.PatientInfo == nil {
		return nil, nil
	}
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patient profile")
	}

	return &GetInsuranceResponse{
		InsuranceInfo: patientProfile.PatientInfo.GetInsurance(res.Schein.InsuranceId),
	}, nil
}

func (app *ScheinService) GetScheinItemById(ctx *titan.Context, id uuid.UUID) (*schein_common.ScheinItem, error) {
	scheinRepo, err := app.scheinRepo.FindOne(ctx, bson.M{
		scheinRepo.Field_Id:        id,
		scheinRepo.Field_IsDeleted: false,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get schein by id")
	}

	if scheinRepo == nil {
		return nil, nil
	}

	scheinItem := app.ToScheinItem(scheinRepo)
	return &scheinItem, nil
}

func (app *ScheinService) GetScheinItemByIds(ctx *titan.Context, ids []uuid.UUID) ([]schein_common.ScheinItem, error) {
	scheinRepos, err := app.scheinRepo.Find(ctx, bson.M{
		scheinRepo.Field_Id: bson.M{
			"$in": ids,
		},
		scheinRepo.Field_IsDeleted: false,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get schein by id")
	}

	if scheinRepos == nil {
		return nil, nil
	}

	scheinItems := slice.Map(scheinRepos, func(scheinRepo scheinRepo.ScheinRepo) schein_common.ScheinItem {
		scheinItem := app.ToScheinItem(&scheinRepo)
		return scheinItem
	})

	return scheinItems, nil
}

func (srv *ScheinService) CreateSvScheinAutomaticly(ctx *titan.Context, request schein_common.CreateSvScheinAutomaticlyRequest) (*schein_common.CreateSvScheinAutomaticlyResponse, error) {
	scheins, err := srv.scheinRepo.GetByIds(ctx, request.ReferenceScheinIds)
	if err != nil {
		return nil, err
	}
	newScheins := []scheinRepo.ScheinRepo{}
	for _, schein := range scheins {
		if schein.Schein.ScheinMainGroup == string(schein_common.FAV) {
			continue
		}
		yearQuarter := util.ToYearQuarter(request.SelectedDate)
		newSchein := schein
		newSchein.Schein.G4101Quarter = &yearQuarter.Quarter
		newSchein.Schein.G4101Year = &yearQuarter.Year
		newSchein.Id = util.NewUUID()
		newSchein.ReferralDoctor = nil
		newSchein.CreatedAt = util.NowUnixMillis(ctx)
		newSchein.IsBilled = false
		newSchein.CreatedBy = function.Do(func() uuid.UUID {
			if ctx.UserInfo().UserUUID() != nil {
				return util.GetPointerValue(ctx.UserInfo().UserUUID())
			}
			return schein.CreatedBy
		})
		newScheins = append(newScheins, newSchein)
	}
	if len(newScheins) == 0 {
		return nil, nil
	}
	resp, err := srv.scheinRepo.CreateMany(ctx, newScheins)
	if err != nil {
		return nil, err
	}
	return &schein_common.CreateSvScheinAutomaticlyResponse{
		ScheinIds: slice.Map(resp, func(s scheinRepo.ScheinRepo) uuid.UUID { return *s.Id }),
	}, nil
}

func (srv *ScheinService) CreateSvScheinFromReference(ctx *titan.Context, request schein_common.CreateSvScheinFromReferenceRequest) (*schein_common.CreateSvScheinFromReferenceResponse, error) {
	schein, err := srv.scheinRepo.GetById(ctx, request.ReferenceScheinId)
	if err != nil {
		return nil, err
	}
	yearQuarter := util.ToYearQuarter(request.SelectedDate)
	newSchein := schein
	newSchein.Schein.G4101Quarter = &yearQuarter.Quarter
	newSchein.Schein.G4101Year = &yearQuarter.Year
	newSchein.Id = util.NewUUID()
	newSchein.ReferralDoctor = nil
	newSchein.CreatedAt = util.NowUnixMillis(ctx)
	newSchein.IsBilled = false
	newSchein.DoctorId = request.DoctorId
	newSchein.ChargeSystemId = &request.ChargeSystemId
	newSchein.SvScheinDetail = &schein_common.SvScheinDetail{
		StartDate:                   request.StartDate,
		EndDate:                     request.EndDate,
		OnlineParticipatedCheckDate: &request.SelectedDate,
	}
	newSchein.CreatedBy = function.Do(func() uuid.UUID {
		if ctx.UserInfo().UserUUID() != nil {
			return util.GetPointerValue(ctx.UserInfo().UserUUID())
		}
		return schein.CreatedBy
	})
	resp, err := srv.scheinRepo.Create(ctx, *newSchein)
	if err != nil {
		return nil, err
	}
	return &schein_common.CreateSvScheinFromReferenceResponse{
		ScheinId: *resp.Id,
	}, nil
}

func (srv *ScheinService) CreateSvScheinManually(ctx *titan.Context, request schein_common.CreateSvScheinManuallyRequest) (*schein_common.CreateSvScheinManuallyResponse, error) {
	yearQuarter := util.ToYearQuarter(request.SelectedDate)
	schein := scheinRepo.ScheinRepo{
		Id:               util.NewUUID(),
		CreatedAt:        util.NowUnixMillis(ctx),
		CreatedBy:        util.GetPointerValue(ctx.UserInfo().UserUUID()),
		IsDeleted:        false,
		IsBilled:         false,
		PatientId:        request.PatientId,
		DoctorId:         request.DoctorId,
		ChargeSystemId:   &request.ChargeSystemId,
		ContractId:       &request.ContractId,
		AssignedToBsnrId: request.AssignedToBsnrId,
		Schein: schein_common.Schein{
			ScheinMainGroup: string(request.ScheinMainGroup),
			G4101Quarter:    &yearQuarter.Quarter,
			G4101Year:       &yearQuarter.Year,
			InsuranceId:     request.InsuranceId,
		},
		SvScheinDetail: &schein_common.SvScheinDetail{
			StartDate:                   request.StartDate,
			EndDate:                     request.EndDate,
			OnlineParticipatedCheckDate: &request.SelectedDate,
		},
	}
	resp, err := srv.scheinRepo.Create(ctx, schein)
	if err != nil {
		return nil, err
	}
	return &schein_common.CreateSvScheinManuallyResponse{
		ScheinId: *resp.Id,
	}, nil
}

func (srv *ScheinService) UpdateSvSchein(ctx *titan.Context, request schein_common.UpdateSvScheinRequest) (*schein_common.UpdateSvScheinResponse, error) {
	schein, err := srv.scheinRepo.GetById(ctx, request.ScheinId)
	if err != nil {
		return nil, err
	}
	yearQuarter := util.ToYearQuarter(request.SelectedDate)
	schein.Schein.G4101Quarter = &yearQuarter.Quarter
	schein.Schein.G4101Year = &yearQuarter.Year
	schein.ReferralDoctor = nil
	schein.CreatedAt = util.NowUnixMillis(ctx)
	schein.IsBilled = false
	schein.DoctorId = request.DoctorId
	schein.AssignedToBsnrId = request.AssignedToBsnrId
	schein.ChargeSystemId = &request.ChargeSystemId
	schein.SvScheinDetail = &schein_common.SvScheinDetail{
		StartDate:                   request.StartDate,
		EndDate:                     request.EndDate,
		OnlineParticipatedCheckDate: &request.SelectedDate,
	}
	schein.UpdatedBy = function.Do(func() *uuid.UUID {
		if ctx.UserInfo().UserUUID() != nil {
			return ctx.UserInfo().UserUUID()
		}
		return schein.UpdatedBy
	})
	schein.UpdatedAt = util.NewPointer(util.UnixMillis(time.Now()))
	resp, err := srv.scheinRepo.Update(ctx, *schein)
	if err != nil {
		return nil, err
	}
	return &schein_common.UpdateSvScheinResponse{
		ScheinId: *resp.Id,
	}, nil
}

func (srv *ScheinService) GetPatientCountryCode(ctx *titan.Context, scheinId uuid.UUID) (*string, error) {
	scheinInfo, err := srv.scheinRepo.GetById(ctx, scheinId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get schein by id")
	}
	if scheinInfo == nil || scheinInfo.Id == nil {
		return nil, nil
	}

	patientProfile, err := srv.patientProfileService.GetProfileById(ctx, profile.GetByIdRequest{
		PatientId: &scheinInfo.PatientId,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patient profile")
	}
	if patientProfile == nil || patientProfile.Id == nil {
		return nil, nil
	}

	patientId := scheinInfo.PatientId
	insuranceId := scheinInfo.Schein.InsuranceId
	yearQuarter := scheinInfo.GetYearQuarter()
	patientCountryCode := patientProfile.PatientInfo.AddressInfo.Address.CountryCode

	patientCardInfo, err := srv.cardRawService.GetPatientInfoByPatientIdAndInsuranceId(ctx, patientId, insuranceId, yearQuarter)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patient card info")
	}

	if patientCardInfo == nil {
		return patientCountryCode, nil
	}

	return patientCardInfo.AddressInfo.Address.CountryCode, nil
}

func (srv *ScheinService) GetPatientPostOfficeCountryCode(ctx *titan.Context, scheinId uuid.UUID) (*string, error) {
	scheinInfo, err := srv.scheinRepo.GetById(ctx, scheinId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get schein by id")
	}
	if scheinInfo == nil || scheinInfo.Id == nil {
		return nil, nil
	}

	patientProfile, err := srv.patientProfileService.GetProfileById(ctx, profile.GetByIdRequest{
		PatientId: &scheinInfo.PatientId,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patient profile")
	}
	if patientProfile == nil || patientProfile.Id == nil {
		return nil, nil
	}

	patientId := scheinInfo.PatientId
	insuranceId := scheinInfo.Schein.InsuranceId
	yearQuarter := scheinInfo.GetYearQuarter()
	patientPostOfficeCountryCode := patientProfile.PatientInfo.PostOfficeBox.CountryCode

	patientCardInfo, err := srv.cardRawService.GetPatientInfoByPatientIdAndInsuranceId(ctx, patientId, insuranceId, yearQuarter)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patient card info")
	}

	if patientCardInfo == nil {
		return &patientPostOfficeCountryCode, nil
	}

	return &patientCardInfo.PostOfficeBox.CountryCode, nil
}

func (srv *ScheinService) GetPatientCardInfoBySheinId(ctx *titan.Context, scheinId uuid.UUID) (*patient_profile_common.PatientInfo, error) {
	scheinInfo, err := srv.scheinRepo.GetById(ctx, scheinId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get schein by id")
	}
	if scheinInfo == nil || scheinInfo.Id == nil {
		return nil, nil
	}

	patientCardInfo, err := srv.cardRawService.GetPatientInfoByPatientIdAndInsuranceId(ctx, scheinInfo.PatientId, scheinInfo.Schein.InsuranceId, scheinInfo.GetYearQuarter())
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patient card info")
	}

	return patientCardInfo, nil
}

func (srv *ScheinService) MarkAsReferral(ctx *titan.Context, markAsReferralRequest schein_common.MarkAsReferralRequest) error {
	scheinInfo, err := srv.scheinRepo.FindOneAndUpdate(ctx,
		bson.M{scheinRepo.Field_Id: markAsReferralRequest.ScheinId},
		bson.M{"$set": bson.M{
			scheinRepo.Field_ReferralDoctor: markAsReferralRequest.ReferralDoctor,
			scheinRepo.Field_UpdatedBy:      ctx.UserInfo().UserUUID(),
			scheinRepo.Field_UpdatedAt:      util.NowUnixMillis(ctx),
		}})
	if err != nil {
		return err
	}
	err = srv.validationTimelineService.RunValidation(ctx, scheinInfo.PatientId, scheinInfo.ContractId, timeline_common.TimelineEntityType_Service, scheinInfo.Id)
	if err != nil {
		return err
	}
	return srv.notifier.NotifyCareProviderScheinChanged(ctx, &schein.EventScheinChanged{
		Data: schein.ScheinChangedResponse{
			PatientId: scheinInfo.PatientId,
		},
	})
}

func (srv *ScheinService) RemoveReferral(ctx *titan.Context, removeReferralRequest schein_common.RemoveReferralRequest) error {
	scheinInfo, err := srv.scheinRepo.FindOneAndUpdate(ctx,
		bson.M{scheinRepo.Field_Id: removeReferralRequest.ScheinId},
		bson.M{"$set": bson.M{
			scheinRepo.Field_ReferralDoctor: nil,
			scheinRepo.Field_UpdatedBy:      ctx.UserInfo().UserUUID(),
			scheinRepo.Field_UpdatedAt:      util.NowUnixMillis(ctx),
		}})
	if err != nil {
		return err
	}
	err = srv.validationTimelineService.RunValidation(ctx, scheinInfo.PatientId, scheinInfo.ContractId, timeline_common.TimelineEntityType_Service, scheinInfo.Id)
	if err != nil {
		return err
	}
	return srv.notifier.NotifyCareProviderScheinChanged(ctx, &schein.EventScheinChanged{
		Data: schein.ScheinChangedResponse{
			PatientId: scheinInfo.PatientId,
		},
	})
}

func (srv *ScheinService) UpdateScheinDoctor(ctx *titan.Context, scheinId, doctorId uuid.UUID) error {
	scheinItem, err := srv.scheinRepo.FindOneAndUpdate(ctx,
		bson.M{scheinRepo.Field_Id: scheinId},
		bson.M{operator.Set: bson.M{
			scheinRepo.Field_DoctorId:  doctorId,
			scheinRepo.Field_UpdatedBy: ctx.UserInfo().UserUUID(),
			scheinRepo.Field_UpdatedAt: util.NowUnixMillis(ctx),
		}})

	if err != nil {
		return err
	}

	return srv.notifier.NotifyCareProviderScheinChanged(ctx, &schein.EventScheinChanged{
		Data: schein.ScheinChangedResponse{
			PatientId: scheinItem.PatientId,
		}})
}

func (srv *ScheinService) CheckDummyVknr(ctx *titan.Context, req schein.CheckDummyVknrRequest) (*schein.CheckDummyVknrResponse, error) {
	scheinItem, err := srv.scheinRepo.FindById(ctx, req.ScheinId)
	if err != nil {
		return nil, err
	}

	if scheinItem == nil {
		return nil, nil
	}

	vknr := fmt.Sprint(scheinItem.ScheinDetail.G4104)
	if vknr == "" {
		return nil, nil
	}

	return &schein.CheckDummyVknrResponse{
		IsDummy: srv.isDummyVknr(vknr),
	}, nil
}

func (srv *ScheinService) GetTotalScheins(ctx *titan.Context, request schein.GetTotalScheinsRequest) (*schein.GetTotalScheinsResponse, error) {
	scheins, err := srv.scheinRepo.FindByPatient(ctx, request.PatientId)
	if err != nil {
		return nil, err
	}

	totalScheins := int32(0)
	for _, s := range scheins {
		scheinItem := srv.ToScheinItem(&s)
		vknr := fmt.Sprint(scheinItem.ScheinDetail.G4104)
		if vknr == "" || srv.isDummyVknr(vknr) {
			continue
		}

		if scheinItem.IsBilled() {
			continue
		}

		totalScheins++
	}

	return &schein.GetTotalScheinsResponse{
		TotalScheins: totalScheins,
	}, nil
}

func (srv *ScheinService) FilterInvalidInsurance(ctx *titan.Context, req []scheinRepo.ScheinRepo) ([]scheinRepo.ScheinRepo, error) {
	scheinsRes := make([]scheinRepo.ScheinRepo, 0)
	for _, schein := range req {
		g4104String := strconv.Itoa(int(cast.ToInt32(schein.ScheinDetail.G4104)))
		vknr := util.LeftPad(g4104String, 5, '0')
		now := util.NowUnixMillis(ctx)
		insurance, err := srv.catalogSdktService.GetSdktCatalogByVknr(ctx, &catalog_sdkt_api.GetSdktCatalogByVknrRequest{
			Vknr:         vknr,
			SelectedDate: &now,
		})
		if err != nil {
			return nil, err
		}

		if insurance == nil {
			continue
		}

		scheinsRes = append(scheinsRes, schein)
	}

	return scheinsRes, nil
}
