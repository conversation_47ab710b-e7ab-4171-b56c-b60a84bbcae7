// This code was autogenerated from service/feature_flag/common/feature_flag.proto, do not edit.

package common

import (
	"encoding/json"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions

// enum definitions
type FeatureFlagKey string

const (
	FeatureFlagKey_SV                 FeatureFlagKey = "FF_MVZ_SV"
	FeatureFlagKey_SystemDate         FeatureFlagKey = "FF_ADMIN_SYSTEMDATE"
	FeatureFlagKey_1ClickBilling      FeatureFlagKey = "FF_MVZ_1CLICKBILLING"
	FeatureFlagKey_HZV_PTV_TESTMODE   FeatureFlagKey = "FF_MVZ_HZV_PTV_TESTMODE"
	FeatureFlagKey_HPM_TESTMODE       FeatureFlagKey = "FF_HPM_TESTMODE"
	FeatureFlagKey_REPORT             FeatureFlagKey = "FF_REPORT"
	FeatureFlagKey_EHKS               FeatureFlagKey = "FF_EHKS"
	FeatureFlagKey_PVS_BILLING        FeatureFlagKey = "FF_PVS_BILLING"
	FeatureFlagKey_PREVENTIVE_BILLING FeatureFlagKey = "FF_PREVENTIVE_BILLING"
)

// Array of enum values
var FeatureFlagKeyArr = []FeatureFlagKey{
	FeatureFlagKey_SV,
	FeatureFlagKey_SystemDate,
	FeatureFlagKey_1ClickBilling,
	FeatureFlagKey_HZV_PTV_TESTMODE,
	FeatureFlagKey_HPM_TESTMODE,
	FeatureFlagKey_REPORT,
	FeatureFlagKey_EHKS,
	FeatureFlagKey_PVS_BILLING,
	FeatureFlagKey_PREVENTIVE_BILLING,
}

// Define constants
const NATS_SUBJECT = "" // nats subject this service will listen to

// service event constants

// message event constants

// Define service interface -------------------------------------------------------------

// Define service proxy -------------------------------------------------------------------

// Define service router -----------------------------------------------------------------

// Define client ----------------------------------------------------------------------------------------------

// Define event Notifier -----------------------------------------------------------------------------------------
type CommonNotifier struct {
	client *titan.Client
}

func NewCommonNotifier() *CommonNotifier {
	client := titan.GetDefaultClient()
	return &CommonNotifier{client}
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type CommonSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewCommonSocketNotifier(socket *socket_api.SocketServiceClient) *CommonSocketNotifier {
	return &CommonSocketNotifier{socket}
}

// -----------------------------------------------
// Test event listener
type CommonEventListener struct {
	mux sync.Mutex
}

func (listener *CommonEventListener) Reset() {
	listener.mux.Lock()
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *CommonEventListener) Subscribe(s *titan.MessageSubscriber) {
}
