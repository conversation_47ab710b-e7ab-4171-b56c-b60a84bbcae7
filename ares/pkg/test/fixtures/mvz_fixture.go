package fixtures

import (
	"log"
	"strconv"

	"emperror.dev/errors"
	"git.tutum.dev/medi/tutum/ares/pkg/masterdata"
	"git.tutum.dev/medi/tutum/ares/pkg/test/data"
	commonApi "git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter/encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/sysadmin/device"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/brianvoe/gofakeit/v5"
	"github.com/google/uuid"
	"github.com/imdario/mergo"
	"gitlab.com/silenteer-oss/hestia/test/fixtures"
	"gitlab.com/silenteer-oss/titan"
)

type FixedData struct {
	Doctors               []*data.Doctor               `json:"doctors,omitempty"`
	DoctorParticipations  []*data.DoctorParticipation  `json:"doctorParticipations,omitempty"`
	Nurses                []*data.Nurse                `json:"nurses,omitempty"`
	Patients              []*data.Patient              `json:"patients,omitempty"`
	PatientParticipations []*data.PatientParticipation `json:"patientParticipations,omitempty"`
	Encounters            []*Encounter                 `json:"encounters,omitempty"`
	EncounterServices     []*EncounterService          `json:"encounterServices,omitempty"`
	EncounterDiagnoses    []*EncounterDiagnose         `json:"encounterDiagnose,omitempty"`
	PatientOverviews      []*data.PatientOverview      `json:"patientOverviews,omitempty"`
	BmpMedication         *data.BmpMedication          `json:"bmpMedications,omitempty"`
	Scheins               []schein.ScheinRepo          `json:"scheins,omitempty"`
	Bsnr                  *bsnr_repo.BSNR              `json:"bsnr,omitempty"`
	BsnrObj               *bsnr_repo.BSNR              `json:"bsnrObj,omitempty"`
}
type FixedDataFixture interface {
	GetFixedData() *FixedData
}

type Encounter struct {
	Data     *encounter.Encounter `json:"data,omitempty"`
	AuditLog *commonApi.AuditLog  `auditLog:"data,omitempty"`
}

type EncounterDiagnose struct {
	EncounterId *uuid.UUID                           `json:"encounterId,omitempty"`
	Data        *patient_encounter.EncounterDiagnose `json:"data,omitempty"`
	AuditLog    *commonApi.AuditLog                  `auditLog:"data,omitempty"`
}

type EncounterService struct {
	EncounterId *uuid.UUID                          `json:"encounterId,omitempty"`
	Data        *patient_encounter.EncounterService `json:"data,omitempty"`
	AuditLog    *commonApi.AuditLog                 `auditLog:"data,omitempty"`
}

type MvzFixture struct {
	Bsnr                 string
	BsnrObj              *bsnr_repo.BSNR
	Doctors              []*data.Doctor
	DoctorParticipations []*data.DoctorParticipation
	extensions           []fixtures.FixtureInterface
	fixtures.AbstractReferenceFixture
}

func (m *MvzFixture) WithBSNR(bsnr bsnr_repo.BSNR) *MvzFixture {
	for i := range m.Doctors {
		m.Doctors[i].Bsnr = bsnr.Code
		m.Doctors[i].BsnrId = bsnr.Id
		if bsnr.Id != nil {
			m.Doctors[i].BsnrIds = []uuid.UUID{*bsnr.Id}
		}
		m.Doctors[i].Bsnrs = []string{bsnr.Code}
	}

	for i := range m.DoctorParticipations {
		m.DoctorParticipations[i].Doctor.Bsnr = bsnr.Code
		m.DoctorParticipations[i].Doctor.BsnrId = bsnr.Id
	}

	m.extensions = append(m.extensions, &BsnrFixture{
		BsnrPractices: []data.BsnrPractice{
			{Bsnr: bsnr},
		},
	})
	m.BsnrObj = &bsnr
	return m
}

func (m *MvzFixture) GetFixedData() *FixedData {
	result := FixedData{
		Doctors:              m.Doctors,
		DoctorParticipations: m.DoctorParticipations,
		Bsnr:                 &bsnr_repo.BSNR{},
	}

	for _, extension := range m.extensions {
		v, ok := extension.(FixedDataFixture)
		if ok {
			fixedData := v.GetFixedData()

			err := mergo.Merge(&result, fixedData)
			if err != nil {
				log.Fatal("Cannot get merge fixed data:", err)
			}
		}
	}

	result.BsnrObj = m.BsnrObj
	return &result
}

func (m *MvzFixture) WithDoctors(doctors ...*data.Doctor) *MvzFixture {
	for _, v := range doctors {
		for _, d := range m.Doctors {
			if v.AccountId != d.AccountId {
				m.Doctors = append(m.Doctors, v)
			}
		}
	}
	return m
}

func (m *MvzFixture) WithDoctorParticipation(doctorParticipations ...*data.DoctorParticipation) *MvzFixture {
	for _, v := range doctorParticipations {
		for _, d := range m.Doctors {
			if v.Doctor.AccountId != d.AccountId {
				m.Doctors = append(m.Doctors, v.Doctor)
			}
			m.DoctorParticipations = append(m.DoctorParticipations, v)
		}
	}
	return m
}

func (m *MvzFixture) SetUp(ctx *titan.Context) error {
	var err error

	userInfo := ctx.UserInfo()
	if userInfo == nil {
		return errors.WithMessage(err, "MvzFixture must be run under care provider context")
	}

	careProvider := m.GetCareProvider(ctx, userInfo.CareProviderId.String())
	if careProvider == nil {
		return errors.WithMessage(err, "missing care provider in the context")
	}

	err = data.UpsertDoctors(careProvider, m.Doctors...)
	if err != nil {
		return errors.WithMessage(err, "error on create doctor")
	}

	err = data.UpsertDoctorParticipations(ctx, m.DoctorParticipations...)
	if err != nil {
		return errors.WithMessage(err, "error on create doctor participation")
	}

	return nil
}

func (m *MvzFixture) TearDown(ctx *titan.Context) error {
	if ctx.UserInfo() == nil || ctx.UserInfo().CareProviderId.String() == "" {
		return nil
	}

	apiKeyService, err := masterdata.ApiKeyServiceMod.SafeResolve()
	if err != nil {
		return errors.WithMessage(err, "failed to resolve api key service")
	}

	careProviderId := ctx.UserInfo().CareProviderId.String()
	if err := apiKeyService.DeleteCareProvider(ctx, &masterdata.DeleteCareProviderRequest{
		CareProviderId: careProviderId,
	}); err != nil {
		return errors.WithMessage(err, "failed to drop api key")
	}

	return nil
}

func (m *MvzFixture) ToCareProviderFixture() *fixtures.CareProviderFixture {
	return m.toCareProviderFixtureWithId(nil)
}

func (m *MvzFixture) toCareProviderFixtureWithId(createProviderId *uuid.UUID) *fixtures.CareProviderFixture {
	if createProviderId == nil {
		createProviderId = util.NewPointer(uuid.New())
	}
	seed, _ := strconv.ParseInt(m.Bsnr, 10, 64)
	gofakeit.Seed(seed)
	mvzName := "Praxis " + gofakeit.Name()

	careProvider := data.NewCareProvider(*createProviderId, mvzName)

	dependencies := []fixtures.FixtureInterface{m}

	adminDeviceFixtures := &AdminDeviceFixture{
		AdminDevices: []device.AdminDevice{
			{
				Id:             util.NewPointer(careProvider.GetDeviceId()),
				DeviceName:     mvzName,
				CareProviderId: util.NewPointer(careProvider.GetId()),
				Identity:       "Identity",
				AddedBy:        "fixture",
				CreateTime:     util.NowUnixMillis(nil),
			},
		},
	}

	dependencies = append(dependencies, m.extensions...)
	dependencies = append(dependencies, adminDeviceFixtures)

	return fixtures.NewCareProviderFixtureWithSeed(careProvider, m.Bsnr, seed, dependencies...)
}
func (m *MvzFixture) ToCareProviderFixtureWithId(createProviderId uuid.UUID) *fixtures.CareProviderFixture {
	return m.toCareProviderFixtureWithId(&createProviderId)
}

func (m *MvzFixture) With(extensions ...fixtures.FixtureInterface) *MvzFixture {
	return &MvzFixture{
		Bsnr:                 m.Bsnr,
		Doctors:              m.Doctors,
		DoctorParticipations: m.DoctorParticipations,
		extensions:           append(m.extensions, extensions...),
	}
}

type PatientFixture struct {
	Patients              []*data.Patient
	PatientParticipations []*data.PatientParticipation
	fixtures.AbstractReferenceFixture
}

func (p *PatientFixture) GetFixedData() *FixedData {
	return &FixedData{
		Patients:              p.Patients,
		PatientParticipations: p.PatientParticipations,
	}
}

func (p *PatientFixture) SetUp(ctx *titan.Context) error {
	var err error

	userInfo := ctx.UserInfo()
	if userInfo == nil {
		return errors.New("PatientFixture must be run under care provider context")
	}
	careProvider := p.GetCareProvider(ctx, userInfo.CareProviderId.String())
	if careProvider == nil {
		return errors.New("missing care provider in the context")
	}

	err = data.UpsertPatients(careProvider, p.Patients...)
	if err != nil {
		return errors.New("error on create patients")
	}

	err = data.UpsertPatientParticipations(ctx, p.PatientParticipations...)
	if err != nil {
		return errors.New("error on create patients participation")
	}

	return nil
}

func (p *PatientFixture) TearDown(ctx *titan.Context) error {
	return nil
}

type EncounterFixture struct {
	Encounters         []*Encounter
	EncounterDiagnoses []*EncounterDiagnose
	EncounterServices  []*EncounterService
	fixtures.AbstractReferenceFixture
}

func (f *EncounterFixture) GetFixedData() *FixedData {
	return &FixedData{
		Encounters:         f.Encounters,
		EncounterServices:  f.EncounterServices,
		EncounterDiagnoses: f.EncounterDiagnoses,
	}
}

func (f *EncounterFixture) SetUp(ctx *titan.Context) error {
	userInfo := ctx.UserInfo()
	if userInfo == nil {
		return errors.New("EncounterFixture must be run under care provider context")
	}
	careProvider := f.GetCareProvider(ctx, userInfo.CareProviderId.String())
	if careProvider == nil {
		return errors.New("missing care provider in the context")
	}

	return nil
}

func (f *EncounterFixture) TearDown(ctx *titan.Context) error {
	return nil
}

type PatientOverviewFixture struct {
	Patients              []*data.Patient
	PatientParticipations []*data.PatientParticipation
	PatientOverviews      []*data.PatientOverview
	fixtures.AbstractReferenceFixture
}

func (f *PatientOverviewFixture) GetFixedData() *FixedData {
	return &FixedData{
		PatientOverviews: f.PatientOverviews,
	}
}

func (f *PatientOverviewFixture) SetUp(ctx *titan.Context) error {
	var err error

	userInfo := ctx.UserInfo()
	if userInfo == nil {
		return errors.New("EncounterFixture must be run under care provider context")
	}
	careProvider := f.GetCareProvider(ctx, userInfo.CareProviderId.String())
	if careProvider == nil {
		return errors.New("missing care provider in the context")
	}

	err = data.UpsertPatients(careProvider, f.Patients...)
	if err != nil {
		return errors.WithMessage(err, "error on create patient")
	}

	err = data.UpsertPatientParticipations(ctx, f.PatientParticipations...)
	if err != nil {
		return errors.WithMessage(err, "error on create patient participation")
	}

	if f.PatientOverviews != nil {
		err = data.UpsertPatientOverviews(ctx, f.PatientOverviews...)
		if err != nil {
			return errors.WithMessage(err, "error on upsert encounter")
		}
	}

	return nil
}

func (f *PatientOverviewFixture) TearDown(ctx *titan.Context) error {
	return nil
}

type MenuFixture struct {
	Doctors          []*data.Doctor
	Patients         []*data.Patient
	PatientOverviews []*data.PatientOverview
	fixtures.AbstractReferenceFixture
}

func (m *MenuFixture) GetFixedData() *FixedData {
	return &FixedData{
		Patients: m.Patients,
	}
}

func (m *MenuFixture) SetUp(ctx *titan.Context) error {
	var err error

	userInfo := ctx.UserInfo()
	if userInfo == nil {
		return errors.New("MvzFixture must be run under care provider context")
	}
	careProvider := m.GetCareProvider(ctx, userInfo.CareProviderId.String())
	if careProvider == nil {
		return errors.New("missing care provider in the context")
	}

	err = data.UpsertPatients(careProvider, m.Patients...)
	if err != nil {
		return errors.New("error on create patients")
	}

	err = data.UpsertPatientParticipations(ctx, m.GetFixedData().PatientParticipations...)
	if err != nil {
		return errors.New("error on create patientParticipations")
	}

	err = data.UpsertDoctors(careProvider, m.Doctors...)
	if err != nil {
		return errors.New("error on create doctor")
	}

	err = data.UpsertDoctorParticipations(ctx, m.GetFixedData().DoctorParticipations...)
	if err != nil {
		return errors.New("error on create DoctorParticipations")
	}

	err = data.UpsertPatientOverviews(ctx, m.PatientOverviews...)
	if err != nil {
		return errors.WithMessage(err, "error on upsert encounter")
	}

	return nil
}

func (m *MenuFixture) TearDown(ctx *titan.Context) error {
	return nil
}
