// This code was autogenerated from tools/test-data-parsing, do not edit.

package fixtures

import (
	"strconv"
	"time"

	"git.tutum.dev/medi/tutum/ares/pkg/test/data"
	"git.tutum.dev/medi/tutum/ares/service/contract/contract/model"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_participation"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/brianvoe/gofakeit/v5"
	"github.com/google/uuid"
)

type PatientParticipationWithoutDoctor struct {
	Patient        *data.Patient
	ContractId     model.ContractId
	ChargeSystemId string
	IkNumber       int32
	StartDate      *int64
	EndDate        *int64
}

func (p *PatientParticipationWithoutDoctor) WithHzvDoctor(doctor *data.Doctor, doctorFunctionType patient_participation.DoctorFunctionType) *data.PatientParticipation {
	return &data.PatientParticipation{
		Doctor:             doctor,
		Patient:            p.Patient,
		ContractId:         p.ContractId,
		ChargeSystemId:     p.ChargeSystemId,
		IkNumber:           p.IkNumber,
		Status:             patient_participation.PatientParticipationStatus_Active,
		DoctorFunctionType: doctorFunctionType,
		ContractType:       model.ContractType_HouseDoctorCare,
		StartDate:          p.StartDate,
		EndDate:            p.EndDate,
	}
}

func (p *PatientParticipationWithoutDoctor) WithFavDoctor(doctor *data.Doctor, doctorFunctionType patient_participation.DoctorFunctionType) *data.PatientParticipation {
	return &data.PatientParticipation{
		Doctor:             doctor,
		Patient:            p.Patient,
		ContractId:         p.ContractId,
		ChargeSystemId:     p.ChargeSystemId,
		IkNumber:           p.IkNumber,
		Status:             patient_participation.PatientParticipationStatus_Active,
		DoctorFunctionType: doctorFunctionType,
		ContractType:       model.ContractType_SpecialistCare,
		StartDate:          p.StartDate,
		EndDate:            p.EndDate,
	}
}

func toInt64Pointer(i int64) *int64 {
	return &i
}

func toStringPointer(str string) *string {
	return &str
}

func toDateOfBirth(dob int64) patient_profile_common.DateOfBirth {
	time := time.Unix(0, dob*int64(time.Millisecond))
	date := int32(time.Day())
	month := int32(time.Month())
	year := int32(time.Year())

	return patient_profile_common.DateOfBirth{
		Date:  &date,
		Month: &month,
		Year:  &year,
	}
}

var (
	Doctor_CadeLarkin                *data.Doctor
	Doctor_CatharineRobel            *data.Doctor
	Doctor_KadenCasper               *data.Doctor
	Doctor_RomaWest                  *data.Doctor
	Doctor_AleenBoyle                *data.Doctor
	Doctor_SadieGusikowski_616123400 *data.Doctor
	Doctor_BulahHaley                *data.Doctor
	Doctor_TyreseKoepp               *data.Doctor
	Doctor_GraceBogan                *data.Doctor
	Doctor_LudwigKub                 *data.Doctor
	Doctor_MohammedCollier           *data.Doctor
	Doctor_PrinceKing                *data.Doctor
	Doctor_NatalieNicolas            *data.Doctor
	Doctor_EwaldMcGlynn              *data.Doctor
	Doctor_MaudieHarris              *data.Doctor
	Doctor_BeulahHomenick            *data.Doctor
	Doctor_ColtMurphy                *data.Doctor
	Doctor_BerneiceCassin            *data.Doctor
	Doctor_SuzanneGerhold            *data.Doctor
	Doctor_AnnaMurray                *data.Doctor
	Doctor_JacklynHalvorson          *data.Doctor
	Doctor_DarioZemlak               *data.Doctor
	Doctor_LeonoraFisher             *data.Doctor
	Doctor_AileenMueller             *data.Doctor
	Doctor_JanieKeebler              *data.Doctor
	Doctor_OdessaAltenwerth          *data.Doctor
	Doctor_MckenzieChamplin          *data.Doctor
	Doctor_VincenzaStehr             *data.Doctor
	Doctor_RemingtonGislason         *data.Doctor
	Doctor_SharonLang                *data.Doctor
	Doctor_JordyVeum                 *data.Doctor
	Doctor_NoraWalker                *data.Doctor
	Doctor_AlessandraHowell          *data.Doctor
	Doctor_RosaJacobson              *data.Doctor
	Doctor_DollyBoyle                *data.Doctor
	Doctor_RosalindBoehm             *data.Doctor
	Doctor_AnaisMurazik              *data.Doctor
	Doctor_MartaRosenbaum            *data.Doctor
	Doctor_SkylaRowe                 *data.Doctor
	Doctor_MandyLeuschke             *data.Doctor
	Doctor_ColbyHowe                 *data.Doctor
	Doctor_JazlynLeffler             *data.Doctor
	Doctor_EstrellaReichert          *data.Doctor
	Doctor_DevonteFay                *data.Doctor
	Doctor_EudoraBarton              *data.Doctor
	Doctor_KaleyKrajcik              *data.Doctor
	Doctor_ElijahNienow              *data.Doctor
	Doctor_ChelseyLubowitz           *data.Doctor
	Doctor_DorianKulas               *data.Doctor
	Doctor_ClaudineBernhard          *data.Doctor

	DoctorParticipation_CadeLarkin_AOK_FA_GASTRO_BW           *data.DoctorParticipation
	DoctorParticipation_CadeLarkin_AOK_FA_KARDIO_BW           *data.DoctorParticipation
	DoctorParticipation_CadeLarkin_AOK_FA_NEPHRO_BW           *data.DoctorParticipation
	DoctorParticipation_CadeLarkin_AOK_FA_NPPP_BW             *data.DoctorParticipation
	DoctorParticipation_CadeLarkin_AOK_FA_OC_BW               *data.DoctorParticipation
	DoctorParticipation_CadeLarkin_AOK_FA_PNEUMO_BW           *data.DoctorParticipation
	DoctorParticipation_CadeLarkin_AOK_FA_URO_BW              *data.DoctorParticipation
	DoctorParticipation_CatharineRobel_AOK_BW_IV_P            *data.DoctorParticipation
	DoctorParticipation_KadenCasper_AOK_BW_IV_P               *data.DoctorParticipation
	DoctorParticipation_RomaWest_AOK_FA_GASTRO_BW             *data.DoctorParticipation
	DoctorParticipation_RomaWest_AOK_FA_KARDIO_BW             *data.DoctorParticipation
	DoctorParticipation_RomaWest_AOK_FA_NEPHRO_BW             *data.DoctorParticipation
	DoctorParticipation_RomaWest_AOK_FA_NPPP_BW               *data.DoctorParticipation
	DoctorParticipation_RomaWest_AOK_FA_OC_BW                 *data.DoctorParticipation
	DoctorParticipation_RomaWest_AOK_FA_PNEUMO_BW             *data.DoctorParticipation
	DoctorParticipation_RomaWest_AOK_FA_URO_BW                *data.DoctorParticipation
	DoctorParticipation_CadeLarkin_BKK_BOSCH_FaV              *data.DoctorParticipation
	DoctorParticipation_CadeLarkin_BKK_VAG_FaV                *data.DoctorParticipation
	DoctorParticipation_AleenBoyle_AOK_BW_IV_P                *data.DoctorParticipation
	DoctorParticipation_SadieGusikowski_616123400_BKK_VAG_FaV *data.DoctorParticipation
	DoctorParticipation_CadeLarkin_MEDI_FA_PT_BW              *data.DoctorParticipation
	DoctorParticipation_CadeLarkin_EK_FA_DIA_BW               *data.DoctorParticipation
	DoctorParticipation_BulahHaley_AOK_BY_HZV_S15             *data.DoctorParticipation
	DoctorParticipation_TyreseKoepp_AOK_BY_HZV_S15            *data.DoctorParticipation
	DoctorParticipation_GraceBogan_AOK_BY_HZV_S15             *data.DoctorParticipation
	DoctorParticipation_LudwigKub_AOK_HE_HZV                  *data.DoctorParticipation
	DoctorParticipation_MohammedCollier_AOK_HE_HZV            *data.DoctorParticipation
	DoctorParticipation_PrinceKing_AOK_HE_HZV                 *data.DoctorParticipation
	DoctorParticipation_NatalieNicolas_AOK_HH_HZV             *data.DoctorParticipation
	DoctorParticipation_EwaldMcGlynn_AOK_HH_HZV               *data.DoctorParticipation
	DoctorParticipation_MaudieHarris_AOK_HH_HZV               *data.DoctorParticipation
	DoctorParticipation_BeulahHomenick_AOK_IKK_BLN_HZV        *data.DoctorParticipation
	DoctorParticipation_ColtMurphy_AOK_IKK_BLN_HZV            *data.DoctorParticipation
	DoctorParticipation_BerneiceCassin_AOK_IKK_BLN_HZV        *data.DoctorParticipation
	DoctorParticipation_SuzanneGerhold_AOK_NO_HZV             *data.DoctorParticipation
	DoctorParticipation_AnnaMurray_AOK_NO_HZV                 *data.DoctorParticipation
	DoctorParticipation_JacklynHalvorson_AOK_NO_HZV           *data.DoctorParticipation
	DoctorParticipation_DarioZemlak_AOK_PLUS_HZV              *data.DoctorParticipation
	DoctorParticipation_LeonoraFisher_AOK_PLUS_HZV            *data.DoctorParticipation
	DoctorParticipation_AileenMueller_AOK_PLUS_HZV            *data.DoctorParticipation
	DoctorParticipation_JanieKeebler_AOK_WL_HZV               *data.DoctorParticipation
	DoctorParticipation_OdessaAltenwerth_AOK_WL_HZV           *data.DoctorParticipation
	DoctorParticipation_MckenzieChamplin_AOK_WL_HZV           *data.DoctorParticipation
	DoctorParticipation_CatharineRobel_AWH_01                 *data.DoctorParticipation
	DoctorParticipation_KadenCasper_AWH_01                    *data.DoctorParticipation
	DoctorParticipation_AleenBoyle_AWH_01                     *data.DoctorParticipation
	DoctorParticipation_VincenzaStehr_AWH_01                  *data.DoctorParticipation
	DoctorParticipation_RemingtonGislason_AWH_01              *data.DoctorParticipation
	DoctorParticipation_CatharineRobel_BKK_BOSCH_BW           *data.DoctorParticipation
	DoctorParticipation_KadenCasper_BKK_BOSCH_BW              *data.DoctorParticipation
	DoctorParticipation_AleenBoyle_BKK_BOSCH_BW               *data.DoctorParticipation
	DoctorParticipation_CatharineRobel_BKK_BW_HZV             *data.DoctorParticipation
	DoctorParticipation_KadenCasper_BKK_BW_HZV                *data.DoctorParticipation
	DoctorParticipation_AleenBoyle_BKK_BW_HZV                 *data.DoctorParticipation
	DoctorParticipation_BulahHaley_BKK_BY_HZV                 *data.DoctorParticipation
	DoctorParticipation_TyreseKoepp_BKK_BY_HZV                *data.DoctorParticipation
	DoctorParticipation_GraceBogan_BKK_BY_HZV                 *data.DoctorParticipation
	DoctorParticipation_SharonLang_BKK_GWQ_HZV                *data.DoctorParticipation
	DoctorParticipation_JordyVeum_BKK_GWQ_HZV                 *data.DoctorParticipation
	DoctorParticipation_NoraWalker_BKK_GWQ_HZV                *data.DoctorParticipation
	DoctorParticipation_NatalieNicolas_BKK_GWQ_HZV            *data.DoctorParticipation
	DoctorParticipation_EwaldMcGlynn_BKK_GWQ_HZV              *data.DoctorParticipation
	DoctorParticipation_MaudieHarris_BKK_GWQ_HZV              *data.DoctorParticipation
	DoctorParticipation_AlessandraHowell_BKK_GWQ_HZV          *data.DoctorParticipation
	DoctorParticipation_RosaJacobson_BKK_GWQ_HZV              *data.DoctorParticipation
	DoctorParticipation_DollyBoyle_BKK_GWQ_HZV                *data.DoctorParticipation
	DoctorParticipation_RosalindBoehm_BKK_GWQ_HZV             *data.DoctorParticipation
	DoctorParticipation_AnaisMurazik_BKK_GWQ_HZV              *data.DoctorParticipation
	DoctorParticipation_MartaRosenbaum_BKK_GWQ_HZV            *data.DoctorParticipation
	DoctorParticipation_LudwigKub_BKK_GWQ_HZV                 *data.DoctorParticipation
	DoctorParticipation_MohammedCollier_BKK_GWQ_HZV           *data.DoctorParticipation
	DoctorParticipation_PrinceKing_BKK_GWQ_HZV                *data.DoctorParticipation
	DoctorParticipation_SkylaRowe_BKK_GWQ_HZV                 *data.DoctorParticipation
	DoctorParticipation_MandyLeuschke_BKK_GWQ_HZV             *data.DoctorParticipation
	DoctorParticipation_ColbyHowe_BKK_GWQ_HZV                 *data.DoctorParticipation
	DoctorParticipation_BeulahHomenick_BKK_GWQ_HZV            *data.DoctorParticipation
	DoctorParticipation_ColtMurphy_BKK_GWQ_HZV                *data.DoctorParticipation
	DoctorParticipation_BerneiceCassin_BKK_GWQ_HZV            *data.DoctorParticipation
	DoctorParticipation_JazlynLeffler_BKK_GWQ_HZV             *data.DoctorParticipation
	DoctorParticipation_EstrellaReichert_BKK_GWQ_HZV          *data.DoctorParticipation
	DoctorParticipation_DevonteFay_BKK_GWQ_HZV                *data.DoctorParticipation
	DoctorParticipation_DarioZemlak_BKK_GWQ_HZV               *data.DoctorParticipation
	DoctorParticipation_LeonoraFisher_BKK_GWQ_HZV             *data.DoctorParticipation
	DoctorParticipation_AileenMueller_BKK_GWQ_HZV             *data.DoctorParticipation
	DoctorParticipation_SuzanneGerhold_BKK_NO_HZV             *data.DoctorParticipation
	DoctorParticipation_AnnaMurray_BKK_NO_HZV                 *data.DoctorParticipation
	DoctorParticipation_JacklynHalvorson_BKK_NO_HZV           *data.DoctorParticipation
	DoctorParticipation_SharonLang_BKK_SPECTRUM_HZV           *data.DoctorParticipation
	DoctorParticipation_JordyVeum_BKK_SPECTRUM_HZV            *data.DoctorParticipation
	DoctorParticipation_NoraWalker_BKK_SPECTRUM_HZV           *data.DoctorParticipation
	DoctorParticipation_NatalieNicolas_BKK_SPECTRUM_HZV       *data.DoctorParticipation
	DoctorParticipation_EwaldMcGlynn_BKK_SPECTRUM_HZV         *data.DoctorParticipation
	DoctorParticipation_MaudieHarris_BKK_SPECTRUM_HZV         *data.DoctorParticipation
	DoctorParticipation_AlessandraHowell_BKK_SPECTRUM_HZV     *data.DoctorParticipation
	DoctorParticipation_RosaJacobson_BKK_SPECTRUM_HZV         *data.DoctorParticipation
	DoctorParticipation_DollyBoyle_BKK_SPECTRUM_HZV           *data.DoctorParticipation
	DoctorParticipation_JanieKeebler_BKK_SPECTRUM_HZV         *data.DoctorParticipation
	DoctorParticipation_OdessaAltenwerth_BKK_SPECTRUM_HZV     *data.DoctorParticipation
	DoctorParticipation_MckenzieChamplin_BKK_SPECTRUM_HZV     *data.DoctorParticipation
	DoctorParticipation_SuzanneGerhold_BKK_SPECTRUM_HZV       *data.DoctorParticipation
	DoctorParticipation_AnnaMurray_BKK_SPECTRUM_HZV           *data.DoctorParticipation
	DoctorParticipation_JacklynHalvorson_BKK_SPECTRUM_HZV     *data.DoctorParticipation
	DoctorParticipation_LudwigKub_BKK_SPECTRUM_HZV            *data.DoctorParticipation
	DoctorParticipation_MohammedCollier_BKK_SPECTRUM_HZV      *data.DoctorParticipation
	DoctorParticipation_PrinceKing_BKK_SPECTRUM_HZV           *data.DoctorParticipation
	DoctorParticipation_SkylaRowe_BKK_SPECTRUM_HZV            *data.DoctorParticipation
	DoctorParticipation_MandyLeuschke_BKK_SPECTRUM_HZV        *data.DoctorParticipation
	DoctorParticipation_ColbyHowe_BKK_SPECTRUM_HZV            *data.DoctorParticipation
	DoctorParticipation_BeulahHomenick_BKK_SPECTRUM_HZV       *data.DoctorParticipation
	DoctorParticipation_ColtMurphy_BKK_SPECTRUM_HZV           *data.DoctorParticipation
	DoctorParticipation_BerneiceCassin_BKK_SPECTRUM_HZV       *data.DoctorParticipation
	DoctorParticipation_JazlynLeffler_BKK_SPECTRUM_HZV        *data.DoctorParticipation
	DoctorParticipation_EstrellaReichert_BKK_SPECTRUM_HZV     *data.DoctorParticipation
	DoctorParticipation_DevonteFay_BKK_SPECTRUM_HZV           *data.DoctorParticipation
	DoctorParticipation_DarioZemlak_BKK_SPECTRUM_HZV          *data.DoctorParticipation
	DoctorParticipation_LeonoraFisher_BKK_SPECTRUM_HZV        *data.DoctorParticipation
	DoctorParticipation_AileenMueller_BKK_SPECTRUM_HZV        *data.DoctorParticipation
	DoctorParticipation_CatharineRobel_BKK_VAG_BW             *data.DoctorParticipation
	DoctorParticipation_KadenCasper_BKK_VAG_BW                *data.DoctorParticipation
	DoctorParticipation_AleenBoyle_BKK_VAG_BW                 *data.DoctorParticipation
	DoctorParticipation_JanieKeebler_BKK_WL_HZV               *data.DoctorParticipation
	DoctorParticipation_OdessaAltenwerth_BKK_WL_HZV           *data.DoctorParticipation
	DoctorParticipation_MckenzieChamplin_BKK_WL_HZV           *data.DoctorParticipation
	DoctorParticipation_SkylaRowe_DAK_HZV                     *data.DoctorParticipation
	DoctorParticipation_MandyLeuschke_DAK_HZV                 *data.DoctorParticipation
	DoctorParticipation_ColbyHowe_DAK_HZV                     *data.DoctorParticipation
	DoctorParticipation_BeulahHomenick_EK_BLN_HZV             *data.DoctorParticipation
	DoctorParticipation_ColtMurphy_EK_BLN_HZV                 *data.DoctorParticipation
	DoctorParticipation_BerneiceCassin_EK_BLN_HZV             *data.DoctorParticipation
	DoctorParticipation_CatharineRobel_EK_BW_HZV              *data.DoctorParticipation
	DoctorParticipation_KadenCasper_EK_BW_HZV                 *data.DoctorParticipation
	DoctorParticipation_AleenBoyle_EK_BW_HZV                  *data.DoctorParticipation
	DoctorParticipation_BulahHaley_EK_BY_HZV_S12              *data.DoctorParticipation
	DoctorParticipation_TyreseKoepp_EK_BY_HZV_S12             *data.DoctorParticipation
	DoctorParticipation_GraceBogan_EK_BY_HZV_S12              *data.DoctorParticipation
	DoctorParticipation_AlessandraHowell_EK_HB_HZV            *data.DoctorParticipation
	DoctorParticipation_RosaJacobson_EK_HB_HZV                *data.DoctorParticipation
	DoctorParticipation_DollyBoyle_EK_HB_HZV                  *data.DoctorParticipation
	DoctorParticipation_NatalieNicolas_EK_HH_HZV              *data.DoctorParticipation
	DoctorParticipation_EwaldMcGlynn_EK_HH_HZV                *data.DoctorParticipation
	DoctorParticipation_MaudieHarris_EK_HH_HZV                *data.DoctorParticipation
	DoctorParticipation_SuzanneGerhold_EK_NO_HZV              *data.DoctorParticipation
	DoctorParticipation_AnnaMurray_EK_NO_HZV                  *data.DoctorParticipation
	DoctorParticipation_JacklynHalvorson_EK_NO_HZV            *data.DoctorParticipation
	DoctorParticipation_SkylaRowe_EK_RLP_HZV                  *data.DoctorParticipation
	DoctorParticipation_MandyLeuschke_EK_RLP_HZV              *data.DoctorParticipation
	DoctorParticipation_ColbyHowe_EK_RLP_HZV                  *data.DoctorParticipation
	DoctorParticipation_SharonLang_EK_SH_HZV                  *data.DoctorParticipation
	DoctorParticipation_JordyVeum_EK_SH_HZV                   *data.DoctorParticipation
	DoctorParticipation_NoraWalker_EK_SH_HZV                  *data.DoctorParticipation
	DoctorParticipation_JazlynLeffler_EK_SL_HZV               *data.DoctorParticipation
	DoctorParticipation_EstrellaReichert_EK_SL_HZV            *data.DoctorParticipation
	DoctorParticipation_DevonteFay_EK_SL_HZV                  *data.DoctorParticipation
	DoctorParticipation_DarioZemlak_EK_SN_HZV                 *data.DoctorParticipation
	DoctorParticipation_LeonoraFisher_EK_SN_HZV               *data.DoctorParticipation
	DoctorParticipation_AileenMueller_EK_SN_HZV               *data.DoctorParticipation
	DoctorParticipation_JanieKeebler_EK_WL_HZV                *data.DoctorParticipation
	DoctorParticipation_OdessaAltenwerth_EK_WL_HZV            *data.DoctorParticipation
	DoctorParticipation_MckenzieChamplin_EK_WL_HZV            *data.DoctorParticipation
	DoctorParticipation_NatalieNicolas_HKK_HZV_NORD           *data.DoctorParticipation
	DoctorParticipation_EwaldMcGlynn_HKK_HZV_NORD             *data.DoctorParticipation
	DoctorParticipation_MaudieHarris_HKK_HZV_NORD             *data.DoctorParticipation
	DoctorParticipation_AlessandraHowell_HKK_HZV_NORD         *data.DoctorParticipation
	DoctorParticipation_RosaJacobson_HKK_HZV_NORD             *data.DoctorParticipation
	DoctorParticipation_DollyBoyle_HKK_HZV_NORD               *data.DoctorParticipation
	DoctorParticipation_CatharineRobel_IKK_CL_BW_HZV          *data.DoctorParticipation
	DoctorParticipation_KadenCasper_IKK_CL_BW_HZV             *data.DoctorParticipation
	DoctorParticipation_AleenBoyle_IKK_CL_BW_HZV              *data.DoctorParticipation
	DoctorParticipation_CatharineRobel_LKK_BW_HZV             *data.DoctorParticipation
	DoctorParticipation_KadenCasper_LKK_BW_HZV                *data.DoctorParticipation
	DoctorParticipation_AleenBoyle_LKK_BW_HZV                 *data.DoctorParticipation
	DoctorParticipation_BulahHaley_LKK_BY_HZV                 *data.DoctorParticipation
	DoctorParticipation_TyreseKoepp_LKK_BY_HZV                *data.DoctorParticipation
	DoctorParticipation_GraceBogan_LKK_BY_HZV                 *data.DoctorParticipation
	DoctorParticipation_LudwigKub_LKK_HZV                     *data.DoctorParticipation
	DoctorParticipation_MohammedCollier_LKK_HZV               *data.DoctorParticipation
	DoctorParticipation_PrinceKing_LKK_HZV                    *data.DoctorParticipation
	DoctorParticipation_SkylaRowe_LKK_HZV                     *data.DoctorParticipation
	DoctorParticipation_MandyLeuschke_LKK_HZV                 *data.DoctorParticipation
	DoctorParticipation_ColbyHowe_LKK_HZV                     *data.DoctorParticipation
	DoctorParticipation_SuzanneGerhold_LKK_NO_HZV             *data.DoctorParticipation
	DoctorParticipation_AnnaMurray_LKK_NO_HZV                 *data.DoctorParticipation
	DoctorParticipation_JacklynHalvorson_LKK_NO_HZV           *data.DoctorParticipation
	DoctorParticipation_JanieKeebler_LKK_WL_HZV               *data.DoctorParticipation
	DoctorParticipation_OdessaAltenwerth_LKK_WL_HZV           *data.DoctorParticipation
	DoctorParticipation_MckenzieChamplin_LKK_WL_HZV           *data.DoctorParticipation
	DoctorParticipation_CatharineRobel_RV_KBS_BW_HZV          *data.DoctorParticipation
	DoctorParticipation_KadenCasper_RV_KBS_BW_HZV             *data.DoctorParticipation
	DoctorParticipation_AleenBoyle_RV_KBS_BW_HZV              *data.DoctorParticipation
	DoctorParticipation_SuzanneGerhold_RV_KBS_NO_HZV          *data.DoctorParticipation
	DoctorParticipation_AnnaMurray_RV_KBS_NO_HZV              *data.DoctorParticipation
	DoctorParticipation_JacklynHalvorson_RV_KBS_NO_HZV        *data.DoctorParticipation
	DoctorParticipation_JanieKeebler_RV_KBS_WL_HZV            *data.DoctorParticipation
	DoctorParticipation_OdessaAltenwerth_RV_KBS_WL_HZV        *data.DoctorParticipation
	DoctorParticipation_MckenzieChamplin_RV_KBS_WL_HZV        *data.DoctorParticipation
	DoctorParticipation_NatalieNicolas_SI_IKK_HZV             *data.DoctorParticipation
	DoctorParticipation_EwaldMcGlynn_SI_IKK_HZV               *data.DoctorParticipation
	DoctorParticipation_MaudieHarris_SI_IKK_HZV               *data.DoctorParticipation
	DoctorParticipation_RosalindBoehm_SI_IKK_HZV              *data.DoctorParticipation
	DoctorParticipation_AnaisMurazik_SI_IKK_HZV               *data.DoctorParticipation
	DoctorParticipation_MartaRosenbaum_SI_IKK_HZV             *data.DoctorParticipation
	DoctorParticipation_JanieKeebler_SI_IKK_HZV               *data.DoctorParticipation
	DoctorParticipation_OdessaAltenwerth_SI_IKK_HZV           *data.DoctorParticipation
	DoctorParticipation_MckenzieChamplin_SI_IKK_HZV           *data.DoctorParticipation
	DoctorParticipation_SuzanneGerhold_SI_IKK_HZV             *data.DoctorParticipation
	DoctorParticipation_AnnaMurray_SI_IKK_HZV                 *data.DoctorParticipation
	DoctorParticipation_JacklynHalvorson_SI_IKK_HZV           *data.DoctorParticipation
	DoctorParticipation_LudwigKub_SI_IKK_HZV                  *data.DoctorParticipation
	DoctorParticipation_MohammedCollier_SI_IKK_HZV            *data.DoctorParticipation
	DoctorParticipation_PrinceKing_SI_IKK_HZV                 *data.DoctorParticipation
	DoctorParticipation_BulahHaley_SI_IKK_HZV                 *data.DoctorParticipation
	DoctorParticipation_TyreseKoepp_SI_IKK_HZV                *data.DoctorParticipation
	DoctorParticipation_GraceBogan_SI_IKK_HZV                 *data.DoctorParticipation
	DoctorParticipation_BeulahHomenick_SI_IKK_HZV             *data.DoctorParticipation
	DoctorParticipation_ColtMurphy_SI_IKK_HZV                 *data.DoctorParticipation
	DoctorParticipation_BerneiceCassin_SI_IKK_HZV             *data.DoctorParticipation
	DoctorParticipation_EudoraBarton_SI_IKK_HZV               *data.DoctorParticipation
	DoctorParticipation_KaleyKrajcik_SI_IKK_HZV               *data.DoctorParticipation
	DoctorParticipation_ElijahNienow_SI_IKK_HZV               *data.DoctorParticipation
	DoctorParticipation_DarioZemlak_SI_IKK_HZV                *data.DoctorParticipation
	DoctorParticipation_LeonoraFisher_SI_IKK_HZV              *data.DoctorParticipation
	DoctorParticipation_AileenMueller_SI_IKK_HZV              *data.DoctorParticipation
	DoctorParticipation_SharonLang_TK_HZV                     *data.DoctorParticipation
	DoctorParticipation_JordyVeum_TK_HZV                      *data.DoctorParticipation
	DoctorParticipation_NoraWalker_TK_HZV                     *data.DoctorParticipation
	DoctorParticipation_NatalieNicolas_TK_HZV                 *data.DoctorParticipation
	DoctorParticipation_EwaldMcGlynn_TK_HZV                   *data.DoctorParticipation
	DoctorParticipation_MaudieHarris_TK_HZV                   *data.DoctorParticipation
	DoctorParticipation_AlessandraHowell_TK_HZV               *data.DoctorParticipation
	DoctorParticipation_RosaJacobson_TK_HZV                   *data.DoctorParticipation
	DoctorParticipation_DollyBoyle_TK_HZV                     *data.DoctorParticipation
	DoctorParticipation_RosalindBoehm_TK_HZV                  *data.DoctorParticipation
	DoctorParticipation_AnaisMurazik_TK_HZV                   *data.DoctorParticipation
	DoctorParticipation_MartaRosenbaum_TK_HZV                 *data.DoctorParticipation
	DoctorParticipation_JanieKeebler_TK_HZV                   *data.DoctorParticipation
	DoctorParticipation_OdessaAltenwerth_TK_HZV               *data.DoctorParticipation
	DoctorParticipation_MckenzieChamplin_TK_HZV               *data.DoctorParticipation
	DoctorParticipation_SuzanneGerhold_TK_HZV                 *data.DoctorParticipation
	DoctorParticipation_AnnaMurray_TK_HZV                     *data.DoctorParticipation
	DoctorParticipation_JacklynHalvorson_TK_HZV               *data.DoctorParticipation
	DoctorParticipation_LudwigKub_TK_HZV                      *data.DoctorParticipation
	DoctorParticipation_MohammedCollier_TK_HZV                *data.DoctorParticipation
	DoctorParticipation_PrinceKing_TK_HZV                     *data.DoctorParticipation
	DoctorParticipation_SkylaRowe_TK_HZV                      *data.DoctorParticipation
	DoctorParticipation_MandyLeuschke_TK_HZV                  *data.DoctorParticipation
	DoctorParticipation_ColbyHowe_TK_HZV                      *data.DoctorParticipation
	DoctorParticipation_BulahHaley_TK_HZV                     *data.DoctorParticipation
	DoctorParticipation_TyreseKoepp_TK_HZV                    *data.DoctorParticipation
	DoctorParticipation_GraceBogan_TK_HZV                     *data.DoctorParticipation
	DoctorParticipation_BeulahHomenick_TK_HZV                 *data.DoctorParticipation
	DoctorParticipation_ColtMurphy_TK_HZV                     *data.DoctorParticipation
	DoctorParticipation_BerneiceCassin_TK_HZV                 *data.DoctorParticipation
	DoctorParticipation_JazlynLeffler_TK_HZV                  *data.DoctorParticipation
	DoctorParticipation_EstrellaReichert_TK_HZV               *data.DoctorParticipation
	DoctorParticipation_DevonteFay_TK_HZV                     *data.DoctorParticipation
	DoctorParticipation_DarioZemlak_TK_HZV                    *data.DoctorParticipation
	DoctorParticipation_LeonoraFisher_TK_HZV                  *data.DoctorParticipation
	DoctorParticipation_AileenMueller_TK_HZV                  *data.DoctorParticipation
	DoctorParticipation_LudwigKub_EK_HE_HZV                   *data.DoctorParticipation
	DoctorParticipation_MohammedCollier_EK_HE_HZV             *data.DoctorParticipation
	DoctorParticipation_PrinceKing_EK_HE_HZV                  *data.DoctorParticipation
	DoctorParticipation_AlessandraHowell_SI_IKK_HZV           *data.DoctorParticipation
	DoctorParticipation_RosaJacobson_SI_IKK_HZV               *data.DoctorParticipation
	DoctorParticipation_DollyBoyle_SI_IKK_HZV                 *data.DoctorParticipation
	DoctorParticipation_SharonLang_SI_IKK_HZV                 *data.DoctorParticipation
	DoctorParticipation_JordyVeum_SI_IKK_HZV                  *data.DoctorParticipation
	DoctorParticipation_NoraWalker_SI_IKK_HZV                 *data.DoctorParticipation
	DoctorParticipation_SharonLang_AOK_SH_HZV                 *data.DoctorParticipation
	DoctorParticipation_JordyVeum_AOK_SH_HZV                  *data.DoctorParticipation
	DoctorParticipation_NoraWalker_AOK_SH_HZV                 *data.DoctorParticipation
	DoctorParticipation_SkylaRowe_SI_IKK_HZV                  *data.DoctorParticipation
	DoctorParticipation_MandyLeuschke_SI_IKK_HZV              *data.DoctorParticipation
	DoctorParticipation_ColbyHowe_SI_IKK_HZV                  *data.DoctorParticipation
	DoctorParticipation_JazlynLeffler_SI_IKK_HZV              *data.DoctorParticipation
	DoctorParticipation_EstrellaReichert_SI_IKK_HZV           *data.DoctorParticipation
	DoctorParticipation_DevonteFay_SI_IKK_HZV                 *data.DoctorParticipation
	DoctorParticipation_ChelseyLubowitz_SI_IKK_HZV            *data.DoctorParticipation
	DoctorParticipation_DorianKulas_SI_IKK_HZV                *data.DoctorParticipation
	DoctorParticipation_ClaudineBernhard_SI_IKK_HZV           *data.DoctorParticipation
	DoctorParticipation_EudoraBarton_TK_HZV                   *data.DoctorParticipation
	DoctorParticipation_KaleyKrajcik_TK_HZV                   *data.DoctorParticipation
	DoctorParticipation_ElijahNienow_TK_HZV                   *data.DoctorParticipation
	DoctorParticipation_EudoraBarton_BKK_GWQ_HZV              *data.DoctorParticipation
	DoctorParticipation_KaleyKrajcik_BKK_GWQ_HZV              *data.DoctorParticipation
	DoctorParticipation_ElijahNienow_BKK_GWQ_HZV              *data.DoctorParticipation
	DoctorParticipation_EudoraBarton_BKK_SPECTRUM_HZV         *data.DoctorParticipation
	DoctorParticipation_KaleyKrajcik_BKK_SPECTRUM_HZV         *data.DoctorParticipation
	DoctorParticipation_ElijahNienow_BKK_SPECTRUM_HZV         *data.DoctorParticipation
	DoctorParticipation_LudwigKub_BKK_VAG_HE                  *data.DoctorParticipation
	DoctorParticipation_MohammedCollier_BKK_VAG_HE            *data.DoctorParticipation
	DoctorParticipation_PrinceKing_BKK_VAG_HE                 *data.DoctorParticipation
	DoctorParticipation_DarioZemlak_RV_KBS_SN_HZV             *data.DoctorParticipation
	DoctorParticipation_LeonoraFisher_RV_KBS_SN_HZV           *data.DoctorParticipation
	DoctorParticipation_AileenMueller_RV_KBS_SN_HZV           *data.DoctorParticipation
	DoctorParticipation_SkylaRowe_AOK_RP_HZV                  *data.DoctorParticipation
	DoctorParticipation_MandyLeuschke_AOK_RP_HZV              *data.DoctorParticipation
	DoctorParticipation_ColbyHowe_AOK_RP_HZV                  *data.DoctorParticipation
	DoctorParticipation_RosalindBoehm_BKK_SPECTRUM_HZV        *data.DoctorParticipation
	DoctorParticipation_AnaisMurazik_BKK_SPECTRUM_HZV         *data.DoctorParticipation
	DoctorParticipation_MartaRosenbaum_BKK_SPECTRUM_HZV       *data.DoctorParticipation
	DoctorParticipation_ChelseyLubowitz_BKK_SPECTRUM_HZV      *data.DoctorParticipation
	DoctorParticipation_DorianKulas_BKK_SPECTRUM_HZV          *data.DoctorParticipation
	DoctorParticipation_ClaudineBernhard_BKK_SPECTRUM_HZV     *data.DoctorParticipation
	DoctorParticipation_ChelseyLubowitz_BKK_GWQ_HZV           *data.DoctorParticipation
	DoctorParticipation_DorianKulas_BKK_GWQ_HZV               *data.DoctorParticipation
	DoctorParticipation_ClaudineBernhard_BKK_GWQ_HZV          *data.DoctorParticipation
	DoctorParticipation_ChelseyLubowitz_TK_HZV                *data.DoctorParticipation
	DoctorParticipation_DorianKulas_TK_HZV                    *data.DoctorParticipation
	DoctorParticipation_ClaudineBernhard_TK_HZV               *data.DoctorParticipation
	DoctorParticipation_JazlynLeffler_AOK_SL_HZV              *data.DoctorParticipation
	DoctorParticipation_EstrellaReichert_AOK_SL_HZV           *data.DoctorParticipation
	DoctorParticipation_DevonteFay_AOK_SL_HZV                 *data.DoctorParticipation

	MvzFixture_011111100 *MvzFixture
	MvzFixture_012222200 *MvzFixture
	MvzFixture_021111100 *MvzFixture
	MvzFixture_022222200 *MvzFixture
	MvzFixture_031111100 *MvzFixture
	MvzFixture_032222200 *MvzFixture
	MvzFixture_171111100 *MvzFixture
	MvzFixture_172222200 *MvzFixture
	MvzFixture_201111100 *MvzFixture
	MvzFixture_202222200 *MvzFixture
	MvzFixture_381111100 *MvzFixture
	MvzFixture_382222200 *MvzFixture
	MvzFixture_461111100 *MvzFixture
	MvzFixture_462222200 *MvzFixture
	MvzFixture_511111100 *MvzFixture
	MvzFixture_512222200 *MvzFixture
	MvzFixture_521111100 *MvzFixture
	MvzFixture_522222200 *MvzFixture
	MvzFixture_528888800 *MvzFixture
	MvzFixture_616123400 *MvzFixture
	MvzFixture_616321400 *MvzFixture
	MvzFixture_711111100 *MvzFixture
	MvzFixture_713333300 *MvzFixture
	MvzFixture_721111100 *MvzFixture
	MvzFixture_723333300 *MvzFixture
	MvzFixture_731111100 *MvzFixture
	MvzFixture_732222200 *MvzFixture
	MvzFixture_831111100 *MvzFixture
	MvzFixture_832222200 *MvzFixture
	MvzFixture_931111100 *MvzFixture
	MvzFixture_932222200 *MvzFixture
	MvzFixture_981111100 *MvzFixture
	MvzFixture_982222200 *MvzFixture

	Patient_FabianGroßburg                               *data.Patient
	PatientParticipation_FabianGroßburg_AOK_FA_GASTRO_BW *PatientParticipationWithoutDoctor
	PatientParticipation_FabianGroßburg_AOK_FA_KARDIO_BW *PatientParticipationWithoutDoctor
	PatientParticipation_FabianGroßburg_AOK_FA_NEPHRO_BW *PatientParticipationWithoutDoctor
	PatientParticipation_FabianGroßburg_AOK_FA_NPPP_BW   *PatientParticipationWithoutDoctor
	PatientParticipation_FabianGroßburg_AOK_FA_OC_BW     *PatientParticipationWithoutDoctor
	PatientParticipation_FabianGroßburg_AOK_FA_PNEUMO_BW *PatientParticipationWithoutDoctor
	PatientParticipation_FabianGroßburg_AOK_FA_URO_BW    *PatientParticipationWithoutDoctor
	PatientParticipation_FabianGroßburg_AWH_01           *PatientParticipationWithoutDoctor

	Patient_MariaMühlenberg                               *data.Patient
	PatientParticipation_MariaMühlenberg_AOK_FA_GASTRO_BW *PatientParticipationWithoutDoctor
	PatientParticipation_MariaMühlenberg_AOK_FA_KARDIO_BW *PatientParticipationWithoutDoctor
	PatientParticipation_MariaMühlenberg_AOK_FA_NEPHRO_BW *PatientParticipationWithoutDoctor
	PatientParticipation_MariaMühlenberg_AOK_FA_NPPP_BW   *PatientParticipationWithoutDoctor
	PatientParticipation_MariaMühlenberg_AOK_FA_OC_BW     *PatientParticipationWithoutDoctor
	PatientParticipation_MariaMühlenberg_AOK_FA_PNEUMO_BW *PatientParticipationWithoutDoctor
	PatientParticipation_MariaMühlenberg_AOK_FA_URO_BW    *PatientParticipationWithoutDoctor
	PatientParticipation_MariaMühlenberg_AWH_01           *PatientParticipationWithoutDoctor

	Patient_PeterSchlößer                               *data.Patient
	PatientParticipation_PeterSchlößer_AOK_FA_GASTRO_BW *PatientParticipationWithoutDoctor
	PatientParticipation_PeterSchlößer_AOK_FA_KARDIO_BW *PatientParticipationWithoutDoctor
	PatientParticipation_PeterSchlößer_AOK_FA_NEPHRO_BW *PatientParticipationWithoutDoctor
	PatientParticipation_PeterSchlößer_AOK_FA_NPPP_BW   *PatientParticipationWithoutDoctor
	PatientParticipation_PeterSchlößer_AOK_FA_OC_BW     *PatientParticipationWithoutDoctor
	PatientParticipation_PeterSchlößer_AOK_FA_PNEUMO_BW *PatientParticipationWithoutDoctor
	PatientParticipation_PeterSchlößer_AOK_FA_URO_BW    *PatientParticipationWithoutDoctor
	PatientParticipation_PeterSchlößer_AWH_01           *PatientParticipationWithoutDoctor

	Patient_MagdaleneHaßlöcher                            *data.Patient
	PatientParticipation_MagdaleneHaßlöcher_BKK_BOSCH_FaV *PatientParticipationWithoutDoctor
	PatientParticipation_MagdaleneHaßlöcher_BKK_BOSCH_BW  *PatientParticipationWithoutDoctor

	Patient_TomBöttcher                            *data.Patient
	PatientParticipation_TomBöttcher_BKK_VAG_FaV   *PatientParticipationWithoutDoctor
	PatientParticipation_TomBöttcher_MEDI_FA_PT_BW *PatientParticipationWithoutDoctor
	PatientParticipation_TomBöttcher_BKK_VAG_BW    *PatientParticipationWithoutDoctor

	Patient_JulianBergmann                            *data.Patient
	PatientParticipation_JulianBergmann_MEDI_FA_PT_BW *PatientParticipationWithoutDoctor
	PatientParticipation_JulianBergmann_EK_FA_DIA_BW  *PatientParticipationWithoutDoctor
	PatientParticipation_JulianBergmann_DAK_HZV       *PatientParticipationWithoutDoctor

	Patient_PascalFaber                            *data.Patient
	PatientParticipation_PascalFaber_MEDI_FA_PT_BW *PatientParticipationWithoutDoctor
	PatientParticipation_PascalFaber_TK_HZV        *PatientParticipationWithoutDoctor

	Patient_MaritaFangl                             *data.Patient
	PatientParticipation_MaritaFangl_AOK_BY_HZV_S15 *PatientParticipationWithoutDoctor

	Patient_ThomasDonaufelder                             *data.Patient
	PatientParticipation_ThomasDonaufelder_AOK_BY_HZV_S15 *PatientParticipationWithoutDoctor

	Patient_RobinHackl                             *data.Patient
	PatientParticipation_RobinHackl_AOK_BY_HZV_S15 *PatientParticipationWithoutDoctor

	Patient_GabrielFriedrich                         *data.Patient
	PatientParticipation_GabrielFriedrich_AOK_HE_HZV *PatientParticipationWithoutDoctor

	Patient_AnnaRottland                         *data.Patient
	PatientParticipation_AnnaRottland_AOK_HE_HZV *PatientParticipationWithoutDoctor

	Patient_JuttaAltendorf                         *data.Patient
	PatientParticipation_JuttaAltendorf_AOK_HE_HZV *PatientParticipationWithoutDoctor

	Patient_AndreasGehlen                         *data.Patient
	PatientParticipation_AndreasGehlen_AOK_HH_HZV *PatientParticipationWithoutDoctor

	Patient_TimReichel                         *data.Patient
	PatientParticipation_TimReichel_AOK_HH_HZV *PatientParticipationWithoutDoctor

	Patient_WaltraudHummel                         *data.Patient
	PatientParticipation_WaltraudHummel_AOK_HH_HZV *PatientParticipationWithoutDoctor

	Patient_UdoBorowsky                              *data.Patient
	PatientParticipation_UdoBorowsky_AOK_IKK_BLN_HZV *PatientParticipationWithoutDoctor

	Patient_SusanSchmidt                              *data.Patient
	PatientParticipation_SusanSchmidt_AOK_IKK_BLN_HZV *PatientParticipationWithoutDoctor

	Patient_AlfredTest                              *data.Patient
	PatientParticipation_AlfredTest_AOK_IKK_BLN_HZV *PatientParticipationWithoutDoctor

	Patient_LucaRey                         *data.Patient
	PatientParticipation_LucaRey_AOK_NO_HZV *PatientParticipationWithoutDoctor

	Patient_JakobWeller                         *data.Patient
	PatientParticipation_JakobWeller_AOK_NO_HZV *PatientParticipationWithoutDoctor

	Patient_TheaGrunert                         *data.Patient
	PatientParticipation_TheaGrunert_AOK_NO_HZV *PatientParticipationWithoutDoctor

	Patient_MikaFessbrecht                           *data.Patient
	PatientParticipation_MikaFessbrecht_AOK_PLUS_HZV *PatientParticipationWithoutDoctor

	Patient_LilianeHagenbeck                           *data.Patient
	PatientParticipation_LilianeHagenbeck_AOK_PLUS_HZV *PatientParticipationWithoutDoctor

	Patient_BorisTissler                           *data.Patient
	PatientParticipation_BorisTissler_AOK_PLUS_HZV *PatientParticipationWithoutDoctor

	Patient_SabrinaSauer                         *data.Patient
	PatientParticipation_SabrinaSauer_AOK_WL_HZV *PatientParticipationWithoutDoctor

	Patient_RosemarieWinter                         *data.Patient
	PatientParticipation_RosemarieWinter_AOK_WL_HZV *PatientParticipationWithoutDoctor

	Patient_TristanLaubach                         *data.Patient
	PatientParticipation_TristanLaubach_AOK_WL_HZV *PatientParticipationWithoutDoctor

	Patient_ErnaMüßgen                     *data.Patient
	PatientParticipation_ErnaMüßgen_AWH_01 *PatientParticipationWithoutDoctor

	Patient_SieglindeGracht                     *data.Patient
	PatientParticipation_SieglindeGracht_AWH_01 *PatientParticipationWithoutDoctor

	Patient_FinnJasper                     *data.Patient
	PatientParticipation_FinnJasper_AWH_01 *PatientParticipationWithoutDoctor

	Patient_LeonHeinzel                     *data.Patient
	PatientParticipation_LeonHeinzel_AWH_01 *PatientParticipationWithoutDoctor

	Patient_HerbertBrügel                           *data.Patient
	PatientParticipation_HerbertBrügel_BKK_BOSCH_BW *PatientParticipationWithoutDoctor

	Patient_RitaRohr                         *data.Patient
	PatientParticipation_RitaRohr_BKK_BW_HZV *PatientParticipationWithoutDoctor

	Patient_KlausKiebel                         *data.Patient
	PatientParticipation_KlausKiebel_BKK_BW_HZV *PatientParticipationWithoutDoctor

	Patient_BerndSchuster                         *data.Patient
	PatientParticipation_BerndSchuster_BKK_BW_HZV *PatientParticipationWithoutDoctor

	Patient_JürgenDietrich                         *data.Patient
	PatientParticipation_JürgenDietrich_BKK_BY_HZV *PatientParticipationWithoutDoctor

	Patient_DirkEmmerich                         *data.Patient
	PatientParticipation_DirkEmmerich_BKK_BY_HZV *PatientParticipationWithoutDoctor

	Patient_StefanieVogel                         *data.Patient
	PatientParticipation_StefanieVogel_BKK_BY_HZV *PatientParticipationWithoutDoctor

	Patient_MeikeFrost                          *data.Patient
	PatientParticipation_MeikeFrost_BKK_GWQ_HZV *PatientParticipationWithoutDoctor

	Patient_RosemarieSauger                          *data.Patient
	PatientParticipation_RosemarieSauger_BKK_GWQ_HZV *PatientParticipationWithoutDoctor

	Patient_MoritzDachsler                          *data.Patient
	PatientParticipation_MoritzDachsler_BKK_GWQ_HZV *PatientParticipationWithoutDoctor

	Patient_LudwigJakobs                         *data.Patient
	PatientParticipation_LudwigJakobs_BKK_NO_HZV *PatientParticipationWithoutDoctor

	Patient_IsoldeKleinmann                         *data.Patient
	PatientParticipation_IsoldeKleinmann_BKK_NO_HZV *PatientParticipationWithoutDoctor

	Patient_PetraAst                         *data.Patient
	PatientParticipation_PetraAst_BKK_NO_HZV *PatientParticipationWithoutDoctor

	Patient_FraukeHempel                               *data.Patient
	PatientParticipation_FraukeHempel_BKK_SPECTRUM_HZV *PatientParticipationWithoutDoctor

	Patient_MaxNeubauer                               *data.Patient
	PatientParticipation_MaxNeubauer_BKK_SPECTRUM_HZV *PatientParticipationWithoutDoctor

	Patient_CilliWeinur                               *data.Patient
	PatientParticipation_CilliWeinur_BKK_SPECTRUM_HZV *PatientParticipationWithoutDoctor

	Patient_MargareteFinck                         *data.Patient
	PatientParticipation_MargareteFinck_BKK_VAG_BW *PatientParticipationWithoutDoctor

	Patient_GotthardGeisinger                         *data.Patient
	PatientParticipation_GotthardGeisinger_BKK_WL_HZV *PatientParticipationWithoutDoctor

	Patient_MarieKurmann                         *data.Patient
	PatientParticipation_MarieKurmann_BKK_WL_HZV *PatientParticipationWithoutDoctor

	Patient_LauraLob                         *data.Patient
	PatientParticipation_LauraLob_BKK_WL_HZV *PatientParticipationWithoutDoctor

	Patient_LieselotteWeiss                      *data.Patient
	PatientParticipation_LieselotteWeiss_DAK_HZV *PatientParticipationWithoutDoctor

	Patient_AstridEllebracht                      *data.Patient
	PatientParticipation_AstridEllebracht_DAK_HZV *PatientParticipationWithoutDoctor

	Patient_TomasMann                         *data.Patient
	PatientParticipation_TomasMann_EK_BLN_HZV *PatientParticipationWithoutDoctor

	Patient_FridaBaumgart                         *data.Patient
	PatientParticipation_FridaBaumgart_EK_BLN_HZV *PatientParticipationWithoutDoctor

	Patient_PeterMagnusen                         *data.Patient
	PatientParticipation_PeterMagnusen_EK_BLN_HZV *PatientParticipationWithoutDoctor

	Patient_ArneAalkaten                        *data.Patient
	PatientParticipation_ArneAalkaten_EK_BW_HZV *PatientParticipationWithoutDoctor

	Patient_BennoBriedel                        *data.Patient
	PatientParticipation_BennoBriedel_EK_BW_HZV *PatientParticipationWithoutDoctor

	Patient_BärbelSassen                            *data.Patient
	PatientParticipation_BärbelSassen_EK_BY_S12_HZV *PatientParticipationWithoutDoctor

	Patient_SilkeFliege                            *data.Patient
	PatientParticipation_SilkeFliege_EK_BY_S12_HZV *PatientParticipationWithoutDoctor

	Patient_PatrickWessler                            *data.Patient
	PatientParticipation_PatrickWessler_EK_BY_S12_HZV *PatientParticipationWithoutDoctor

	Patient_MonikaMann                        *data.Patient
	PatientParticipation_MonikaMann_EK_HB_HZV *PatientParticipationWithoutDoctor

	Patient_PeterPeters                        *data.Patient
	PatientParticipation_PeterPeters_EK_HB_HZV *PatientParticipationWithoutDoctor

	Patient_FridaBunge                        *data.Patient
	PatientParticipation_FridaBunge_EK_HB_HZV *PatientParticipationWithoutDoctor

	Patient_RuthPfaff                        *data.Patient
	PatientParticipation_RuthPfaff_EK_HH_HZV *PatientParticipationWithoutDoctor

	Patient_SandraSchaaf                        *data.Patient
	PatientParticipation_SandraSchaaf_EK_HH_HZV *PatientParticipationWithoutDoctor

	Patient_MarvinJostes                        *data.Patient
	PatientParticipation_MarvinJostes_EK_HH_HZV *PatientParticipationWithoutDoctor

	Patient_TomMarkwardt                        *data.Patient
	PatientParticipation_TomMarkwardt_EK_NO_HZV *PatientParticipationWithoutDoctor

	Patient_BrigitteHelmer                        *data.Patient
	PatientParticipation_BrigitteHelmer_EK_NO_HZV *PatientParticipationWithoutDoctor

	Patient_FranzFachinger                        *data.Patient
	PatientParticipation_FranzFachinger_EK_NO_HZV *PatientParticipationWithoutDoctor

	Patient_IngeborgTutte                         *data.Patient
	PatientParticipation_IngeborgTutte_EK_RLP_HZV *PatientParticipationWithoutDoctor

	Patient_GabrielArnesen                         *data.Patient
	PatientParticipation_GabrielArnesen_EK_RLP_HZV *PatientParticipationWithoutDoctor

	Patient_TimoSchmidt                         *data.Patient
	PatientParticipation_TimoSchmidt_EK_RLP_HZV *PatientParticipationWithoutDoctor

	Patient_ErwinPoth                        *data.Patient
	PatientParticipation_ErwinPoth_EK_SH_HZV *PatientParticipationWithoutDoctor

	Patient_MaximilianBlasberg                        *data.Patient
	PatientParticipation_MaximilianBlasberg_EK_SH_HZV *PatientParticipationWithoutDoctor

	Patient_SonjaGlock                        *data.Patient
	PatientParticipation_SonjaGlock_EK_SH_HZV *PatientParticipationWithoutDoctor

	Patient_JosefReitmann                        *data.Patient
	PatientParticipation_JosefReitmann_EK_SL_HZV *PatientParticipationWithoutDoctor

	Patient_SilviaSenser                        *data.Patient
	PatientParticipation_SilviaSenser_EK_SL_HZV *PatientParticipationWithoutDoctor

	Patient_FelixJohnen                        *data.Patient
	PatientParticipation_FelixJohnen_EK_SL_HZV *PatientParticipationWithoutDoctor

	Patient_ManfredStocker                        *data.Patient
	PatientParticipation_ManfredStocker_EK_SN_HZV *PatientParticipationWithoutDoctor

	Patient_ClaudiaBaumgarten                        *data.Patient
	PatientParticipation_ClaudiaBaumgarten_EK_SN_HZV *PatientParticipationWithoutDoctor

	Patient_CarmenWetter                        *data.Patient
	PatientParticipation_CarmenWetter_EK_SN_HZV *PatientParticipationWithoutDoctor

	Patient_FranzMonk                        *data.Patient
	PatientParticipation_FranzMonk_EK_WL_HZV *PatientParticipationWithoutDoctor

	Patient_GudrunHelmer                        *data.Patient
	PatientParticipation_GudrunHelmer_EK_WL_HZV *PatientParticipationWithoutDoctor

	Patient_KlausMarkwardt                        *data.Patient
	PatientParticipation_KlausMarkwardt_EK_WL_HZV *PatientParticipationWithoutDoctor

	Patient_AxelRichter                           *data.Patient
	PatientParticipation_AxelRichter_HKK_HZV_NORD *PatientParticipationWithoutDoctor

	Patient_SvenjaKrug                           *data.Patient
	PatientParticipation_SvenjaKrug_HKK_HZV_NORD *PatientParticipationWithoutDoctor

	Patient_ErnstNomer                           *data.Patient
	PatientParticipation_ErnstNomer_HKK_HZV_NORD *PatientParticipationWithoutDoctor

	Patient_HansMustermann                            *data.Patient
	PatientParticipation_HansMustermann_IKK_CL_BW_HZV *PatientParticipationWithoutDoctor

	Patient_MaxMustermann                            *data.Patient
	PatientParticipation_MaxMustermann_IKK_CL_BW_HZV *PatientParticipationWithoutDoctor

	Patient_AnnemarieAdam                         *data.Patient
	PatientParticipation_AnnemarieAdam_LKK_BW_HZV *PatientParticipationWithoutDoctor

	Patient_PeterBürger                         *data.Patient
	PatientParticipation_PeterBürger_LKK_BY_HZV *PatientParticipationWithoutDoctor

	Patient_MandyZimmer                         *data.Patient
	PatientParticipation_MandyZimmer_LKK_BY_HZV *PatientParticipationWithoutDoctor

	Patient_LucasMöller                         *data.Patient
	PatientParticipation_LucasMöller_LKK_BY_HZV *PatientParticipationWithoutDoctor

	Patient_ReneDankert                      *data.Patient
	PatientParticipation_ReneDankert_LKK_HZV *PatientParticipationWithoutDoctor

	Patient_SimonRalke                      *data.Patient
	PatientParticipation_SimonRalke_LKK_HZV *PatientParticipationWithoutDoctor

	Patient_LuiseLampertinger                      *data.Patient
	PatientParticipation_LuiseLampertinger_LKK_HZV *PatientParticipationWithoutDoctor

	Patient_TheobaldKleinmann                         *data.Patient
	PatientParticipation_TheobaldKleinmann_LKK_NO_HZV *PatientParticipationWithoutDoctor

	Patient_RegineSchmadtke                         *data.Patient
	PatientParticipation_RegineSchmadtke_LKK_NO_HZV *PatientParticipationWithoutDoctor

	Patient_TobiasSchmadtke                         *data.Patient
	PatientParticipation_TobiasSchmadtke_LKK_NO_HZV *PatientParticipationWithoutDoctor

	Patient_TomNeurer                         *data.Patient
	PatientParticipation_TomNeurer_LKK_WL_HZV *PatientParticipationWithoutDoctor

	Patient_KonradKönig                         *data.Patient
	PatientParticipation_KonradKönig_LKK_WL_HZV *PatientParticipationWithoutDoctor

	Patient_ElkeEhlert                         *data.Patient
	PatientParticipation_ElkeEhlert_LKK_WL_HZV *PatientParticipationWithoutDoctor

	Patient_HildeWeber                            *data.Patient
	PatientParticipation_HildeWeber_RV_KBS_BW_HZV *PatientParticipationWithoutDoctor

	Patient_PeterHanses                            *data.Patient
	PatientParticipation_PeterHanses_RV_KBS_BW_HZV *PatientParticipationWithoutDoctor

	Patient_CarolaBley                            *data.Patient
	PatientParticipation_CarolaBley_RV_KBS_NO_HZV *PatientParticipationWithoutDoctor

	Patient_ElfriedeZantis                            *data.Patient
	PatientParticipation_ElfriedeZantis_RV_KBS_NO_HZV *PatientParticipationWithoutDoctor

	Patient_DominikPauli                            *data.Patient
	PatientParticipation_DominikPauli_RV_KBS_NO_HZV *PatientParticipationWithoutDoctor

	Patient_TheoKrons                            *data.Patient
	PatientParticipation_TheoKrons_RV_KBS_WL_HZV *PatientParticipationWithoutDoctor

	Patient_PamelaAbenden                            *data.Patient
	PatientParticipation_PamelaAbenden_RV_KBS_WL_HZV *PatientParticipationWithoutDoctor

	Patient_TanjaKoch                            *data.Patient
	PatientParticipation_TanjaKoch_RV_KBS_WL_HZV *PatientParticipationWithoutDoctor

	Patient_MustafaBagriyanik                         *data.Patient
	PatientParticipation_MustafaBagriyanik_SI_IKK_HZV *PatientParticipationWithoutDoctor

	Patient_RosemarieHaug                         *data.Patient
	PatientParticipation_RosemarieHaug_SI_IKK_HZV *PatientParticipationWithoutDoctor

	Patient_NorbertWerner                         *data.Patient
	PatientParticipation_NorbertWerner_SI_IKK_HZV *PatientParticipationWithoutDoctor

	Patient_MarkusKornfeld                         *data.Patient
	PatientParticipation_MarkusKornfeld_SI_IKK_HZV *PatientParticipationWithoutDoctor

	Patient_MariannePulver                     *data.Patient
	PatientParticipation_MariannePulver_TK_HZV *PatientParticipationWithoutDoctor

	Patient_EduardKabel                     *data.Patient
	PatientParticipation_EduardKabel_TK_HZV *PatientParticipationWithoutDoctor

	Patient_GiselaReuter                        *data.Patient
	PatientParticipation_GiselaReuter_EK_HE_HZV *PatientParticipationWithoutDoctor

	Patient_FrankGross                        *data.Patient
	PatientParticipation_FrankGross_EK_HE_HZV *PatientParticipationWithoutDoctor

	Patient_AlissaWalder                        *data.Patient
	PatientParticipation_AlissaWalder_EK_HE_HZV *PatientParticipationWithoutDoctor

	Patient_PhillippTressner                         *data.Patient
	PatientParticipation_PhillippTressner_BKK_BW_HZV *PatientParticipationWithoutDoctor

	Patient_CäciliaJost                         *data.Patient
	PatientParticipation_CäciliaJost_AOK_SH_HZV *PatientParticipationWithoutDoctor

	Patient_BenjaminGeser                         *data.Patient
	PatientParticipation_BenjaminGeser_AOK_SH_HZV *PatientParticipationWithoutDoctor

	Patient_FlorianKleist                         *data.Patient
	PatientParticipation_FlorianKleist_AOK_SH_HZV *PatientParticipationWithoutDoctor

	Patient_IlenaSchrippe                         *data.Patient
	PatientParticipation_IlenaSchrippe_BKK_VAG_HE *PatientParticipationWithoutDoctor

	Patient_ErichSchommbach                         *data.Patient
	PatientParticipation_ErichSchommbach_BKK_VAG_HE *PatientParticipationWithoutDoctor

	Patient_HeintjeSchmolke                         *data.Patient
	PatientParticipation_HeintjeSchmolke_BKK_VAG_HE *PatientParticipationWithoutDoctor

	Patient_RainerEngels                            *data.Patient
	PatientParticipation_RainerEngels_RV_KBS_SN_HZV *PatientParticipationWithoutDoctor

	Patient_VerenaFremann                            *data.Patient
	PatientParticipation_VerenaFremann_RV_KBS_SN_HZV *PatientParticipationWithoutDoctor

	Patient_LinaGraubach                            *data.Patient
	PatientParticipation_LinaGraubach_RV_KBS_SN_HZV *PatientParticipationWithoutDoctor

	Patient_LiesbethUrner                         *data.Patient
	PatientParticipation_LiesbethUrner_AOK_RP_HZV *PatientParticipationWithoutDoctor

	Patient_HolgerKromer                         *data.Patient
	PatientParticipation_HolgerKromer_AOK_RP_HZV *PatientParticipationWithoutDoctor

	Patient_LuanForstberg                         *data.Patient
	PatientParticipation_LuanForstberg_AOK_RP_HZV *PatientParticipationWithoutDoctor

	Patient_FranzKranzer                         *data.Patient
	PatientParticipation_FranzKranzer_BKK_BW_HZV *PatientParticipationWithoutDoctor

	Patient_MarliesFortzacher                         *data.Patient
	PatientParticipation_MarliesFortzacher_AOK_SL_HZV *PatientParticipationWithoutDoctor

	Patient_ClemensHuber                         *data.Patient
	PatientParticipation_ClemensHuber_AOK_SL_HZV *PatientParticipationWithoutDoctor

	Patient_MauriceKohne                         *data.Patient
	PatientParticipation_MauriceKohne_AOK_SL_HZV *PatientParticipationWithoutDoctor

	Patient_NicoGrotl                         *data.Patient
	PatientParticipation_NicoGrotl_BKK_BY_HZV *PatientParticipationWithoutDoctor
)

func init() {
	var seed int64

	seed, _ = strconv.ParseInt("10101010", 10, 64)
	gofakeit.Seed(seed)
	Doctor_CadeLarkin = data.FakeDoctor()
	Doctor_CadeLarkin.Bsnr = "616123400"
	Doctor_CadeLarkin.Lanr = "999991101"
	Doctor_CadeLarkin.HavgId = ""
	Doctor_CadeLarkin.Okv = "52"
	Doctor_CadeLarkin.MediverbundId = "10101010"
	Doctor_CadeLarkin.HasHzvContracts = false
	Doctor_CadeLarkin.MediverbundVpId = util.NewString("FCIQZUAAOO")

	seed, _ = strconv.ParseInt("85714", 10, 64)
	gofakeit.Seed(seed)
	Doctor_CatharineRobel = data.FakeDoctor()
	Doctor_CatharineRobel.Bsnr = "521111100"
	Doctor_CatharineRobel.Lanr = "999999901"
	Doctor_CatharineRobel.HavgId = "85714"
	Doctor_CatharineRobel.Okv = "52"
	Doctor_CatharineRobel.MediverbundId = ""
	Doctor_CatharineRobel.HasHzvContracts = true
	Doctor_CatharineRobel.HavgVpId = util.NewString("H2JHACAAT7")
	Doctor_CatharineRobel.MediverbundVpId = util.NewString("H2JHACAAT7")
	Doctor_CatharineRobel.BsnrId = util.NewPointer(uuid.MustParse("*************-0000-0000-000000000000"))

	seed, _ = strconv.ParseInt("86680", 10, 64)
	gofakeit.Seed(seed)
	Doctor_KadenCasper = data.FakeDoctor()
	Doctor_KadenCasper.Bsnr = "522222200"
	Doctor_KadenCasper.Lanr = "999998801"
	Doctor_KadenCasper.HavgId = "86680"
	Doctor_KadenCasper.Okv = "52"
	Doctor_KadenCasper.MediverbundId = ""
	Doctor_KadenCasper.HasHzvContracts = false
	Doctor_KadenCasper.HavgVpId = util.NewString("HTBJACAAFO")

	seed, _ = strconv.ParseInt("10101011", 10, 64)
	gofakeit.Seed(seed)
	Doctor_RomaWest = data.FakeDoctor()
	Doctor_RomaWest.Bsnr = "616321400"
	Doctor_RomaWest.Lanr = "999992201"
	Doctor_RomaWest.HavgId = ""
	Doctor_RomaWest.Okv = "52"
	Doctor_RomaWest.MediverbundId = "10101011"
	Doctor_RomaWest.HasHzvContracts = false
	Doctor_RomaWest.MediverbundVpId = util.NewString("FCMQZUAAKO")

	seed, _ = strconv.ParseInt("87353", 10, 64)
	gofakeit.Seed(seed)
	Doctor_AleenBoyle = data.FakeDoctor()
	Doctor_AleenBoyle.Bsnr = "522222200"
	Doctor_AleenBoyle.Lanr = "999997701"
	Doctor_AleenBoyle.HavgId = "87353"
	Doctor_AleenBoyle.Okv = "52"
	Doctor_AleenBoyle.MediverbundId = ""
	Doctor_AleenBoyle.HasHzvContracts = false
	Doctor_AleenBoyle.HavgVpId = util.NewString("HHFKQCAABT")

	seed, _ = strconv.ParseInt("10101011", 10, 64)
	gofakeit.Seed(seed)
	Doctor_SadieGusikowski_616123400 = data.FakeDoctor()
	Doctor_SadieGusikowski_616123400.Bsnr = "616123400"
	Doctor_SadieGusikowski_616123400.Lanr = "999992201"
	Doctor_SadieGusikowski_616123400.HavgId = ""
	Doctor_SadieGusikowski_616123400.Okv = "52"
	Doctor_SadieGusikowski_616123400.MediverbundId = "10101011"
	Doctor_SadieGusikowski_616123400.HasHzvContracts = false
	Doctor_SadieGusikowski_616123400.MediverbundVpId = util.NewString("FCMQZUAAKO")

	seed, _ = strconv.ParseInt("00202", 10, 64)
	gofakeit.Seed(seed)
	Doctor_BulahHaley = data.FakeDoctor()
	Doctor_BulahHaley.Bsnr = "711111100"
	Doctor_BulahHaley.Lanr = "999990001"
	Doctor_BulahHaley.HavgId = "00202"
	Doctor_BulahHaley.Okv = "71"
	Doctor_BulahHaley.MediverbundId = ""
	Doctor_BulahHaley.HasHzvContracts = false
	Doctor_BulahHaley.HavgVpId = util.NewString("HZIAA5O")

	seed, _ = strconv.ParseInt("00216", 10, 64)
	gofakeit.Seed(seed)
	Doctor_TyreseKoepp = data.FakeDoctor()
	Doctor_TyreseKoepp.Bsnr = "711111100"
	Doctor_TyreseKoepp.Lanr = "999966801"
	Doctor_TyreseKoepp.HavgId = "00216"
	Doctor_TyreseKoepp.Okv = "71"
	Doctor_TyreseKoepp.MediverbundId = ""
	Doctor_TyreseKoepp.HasHzvContracts = false
	Doctor_TyreseKoepp.HavgVpId = util.NewString("H3AAAX7")

	seed, _ = strconv.ParseInt("00226", 10, 64)
	gofakeit.Seed(seed)
	Doctor_GraceBogan = data.FakeDoctor()
	Doctor_GraceBogan.Bsnr = "713333300"
	Doctor_GraceBogan.Lanr = "999954001"
	Doctor_GraceBogan.HavgId = "00226"
	Doctor_GraceBogan.Okv = "71"
	Doctor_GraceBogan.MediverbundId = ""
	Doctor_GraceBogan.HasHzvContracts = false
	Doctor_GraceBogan.HavgVpId = util.NewString("H4IAAFU")

	seed, _ = strconv.ParseInt("00200", 10, 64)
	gofakeit.Seed(seed)
	Doctor_LudwigKub = data.FakeDoctor()
	Doctor_LudwigKub.Bsnr = "461111100"
	Doctor_LudwigKub.Lanr = "999978601"
	Doctor_LudwigKub.HavgId = "00200"
	Doctor_LudwigKub.Okv = "46"
	Doctor_LudwigKub.MediverbundId = ""
	Doctor_LudwigKub.HasHzvContracts = false
	Doctor_LudwigKub.HavgVpId = util.NewString("HZAAAQC")

	seed, _ = strconv.ParseInt("00224", 10, 64)
	gofakeit.Seed(seed)
	Doctor_MohammedCollier = data.FakeDoctor()
	Doctor_MohammedCollier.Bsnr = "462222200"
	Doctor_MohammedCollier.Lanr = "999956201"
	Doctor_MohammedCollier.HavgId = "00224"
	Doctor_MohammedCollier.Okv = "46"
	Doctor_MohammedCollier.MediverbundId = ""
	Doctor_MohammedCollier.HasHzvContracts = false
	Doctor_MohammedCollier.HavgVpId = util.NewString("H4AAAFK")

	seed, _ = strconv.ParseInt("00225", 10, 64)
	gofakeit.Seed(seed)
	Doctor_PrinceKing = data.FakeDoctor()
	Doctor_PrinceKing.Bsnr = "462222200"
	Doctor_PrinceKing.Lanr = "999955101"
	Doctor_PrinceKing.HavgId = "00225"
	Doctor_PrinceKing.Okv = "46"
	Doctor_PrinceKing.MediverbundId = ""
	Doctor_PrinceKing.HasHzvContracts = false
	Doctor_PrinceKing.HavgVpId = util.NewString("H4EAALV")

	seed, _ = strconv.ParseInt("00206", 10, 64)
	gofakeit.Seed(seed)
	Doctor_NatalieNicolas = data.FakeDoctor()
	Doctor_NatalieNicolas.Bsnr = "021111100"
	Doctor_NatalieNicolas.Lanr = "999986001"
	Doctor_NatalieNicolas.HavgId = "00206"
	Doctor_NatalieNicolas.Okv = "02"
	Doctor_NatalieNicolas.MediverbundId = ""
	Doctor_NatalieNicolas.HasHzvContracts = false
	Doctor_NatalieNicolas.HavgVpId = util.NewString("HZYAAST")

	seed, _ = strconv.ParseInt("00238", 10, 64)
	gofakeit.Seed(seed)
	Doctor_EwaldMcGlynn = data.FakeDoctor()
	Doctor_EwaldMcGlynn.Bsnr = "022222200"
	Doctor_EwaldMcGlynn.Lanr = "999937101"
	Doctor_EwaldMcGlynn.HavgId = "00238"
	Doctor_EwaldMcGlynn.Okv = "02"
	Doctor_EwaldMcGlynn.MediverbundId = ""
	Doctor_EwaldMcGlynn.HasHzvContracts = false
	Doctor_EwaldMcGlynn.HavgVpId = util.NewString("H5YAAFV")

	seed, _ = strconv.ParseInt("00239", 10, 64)
	gofakeit.Seed(seed)
	Doctor_MaudieHarris = data.FakeDoctor()
	Doctor_MaudieHarris.Bsnr = "022222200"
	Doctor_MaudieHarris.Lanr = "999936001"
	Doctor_MaudieHarris.HavgId = "00239"
	Doctor_MaudieHarris.Okv = "02"
	Doctor_MaudieHarris.MediverbundId = ""
	Doctor_MaudieHarris.HasHzvContracts = false
	Doctor_MaudieHarris.HavgVpId = util.NewString("H54AA7A")

	seed, _ = strconv.ParseInt("00209", 10, 64)
	gofakeit.Seed(seed)
	Doctor_BeulahHomenick = data.FakeDoctor()
	Doctor_BeulahHomenick.Bsnr = "721111100"
	Doctor_BeulahHomenick.Lanr = "999983701"
	Doctor_BeulahHomenick.HavgId = "00209"
	Doctor_BeulahHomenick.Okv = "72"
	Doctor_BeulahHomenick.MediverbundId = ""
	Doctor_BeulahHomenick.HasHzvContracts = false
	Doctor_BeulahHomenick.HavgVpId = util.NewString("H2EAAF6")

	seed, _ = strconv.ParseInt("00217", 10, 64)
	gofakeit.Seed(seed)
	Doctor_ColtMurphy = data.FakeDoctor()
	Doctor_ColtMurphy.Bsnr = "721111100"
	Doctor_ColtMurphy.Lanr = "999965701"
	Doctor_ColtMurphy.HavgId = "00217"
	Doctor_ColtMurphy.Okv = "72"
	Doctor_ColtMurphy.MediverbundId = ""
	Doctor_ColtMurphy.HasHzvContracts = false
	Doctor_ColtMurphy.HavgVpId = util.NewString("H3EAA5K")

	seed, _ = strconv.ParseInt("00231", 10, 64)
	gofakeit.Seed(seed)
	Doctor_BerneiceCassin = data.FakeDoctor()
	Doctor_BerneiceCassin.Bsnr = "723333300"
	Doctor_BerneiceCassin.Lanr = "999948801"
	Doctor_BerneiceCassin.HavgId = "00231"
	Doctor_BerneiceCassin.Okv = "72"
	Doctor_BerneiceCassin.MediverbundId = ""
	Doctor_BerneiceCassin.HasHzvContracts = false
	Doctor_BerneiceCassin.HavgVpId = util.NewString("H44AADK")

	seed, _ = strconv.ParseInt("00203", 10, 64)
	gofakeit.Seed(seed)
	Doctor_SuzanneGerhold = data.FakeDoctor()
	Doctor_SuzanneGerhold.Bsnr = "381111100"
	Doctor_SuzanneGerhold.Lanr = "999981501"
	Doctor_SuzanneGerhold.HavgId = "00203"
	Doctor_SuzanneGerhold.Okv = "38"
	Doctor_SuzanneGerhold.MediverbundId = ""
	Doctor_SuzanneGerhold.HasHzvContracts = false
	Doctor_SuzanneGerhold.HavgVpId = util.NewString("HZMAA3R")

	seed, _ = strconv.ParseInt("00222", 10, 64)
	gofakeit.Seed(seed)
	Doctor_AnnaMurray = data.FakeDoctor()
	Doctor_AnnaMurray.Bsnr = "382222200"
	Doctor_AnnaMurray.Lanr = "999958401"
	Doctor_AnnaMurray.HavgId = "00222"
	Doctor_AnnaMurray.Okv = "38"
	Doctor_AnnaMurray.MediverbundId = ""
	Doctor_AnnaMurray.HasHzvContracts = false
	Doctor_AnnaMurray.HavgVpId = util.NewString("H3YAAHB")

	seed, _ = strconv.ParseInt("00223", 10, 64)
	gofakeit.Seed(seed)
	Doctor_JacklynHalvorson = data.FakeDoctor()
	Doctor_JacklynHalvorson.Bsnr = "382222200"
	Doctor_JacklynHalvorson.Lanr = "999957301"
	Doctor_JacklynHalvorson.HavgId = "00223"
	Doctor_JacklynHalvorson.Okv = "38"
	Doctor_JacklynHalvorson.MediverbundId = ""
	Doctor_JacklynHalvorson.HasHzvContracts = false
	Doctor_JacklynHalvorson.HavgVpId = util.NewString("H34AATQ")

	seed, _ = strconv.ParseInt("00208", 10, 64)
	gofakeit.Seed(seed)
	Doctor_DarioZemlak = data.FakeDoctor()
	Doctor_DarioZemlak.Bsnr = "981111100"
	Doctor_DarioZemlak.Lanr = "999984801"
	Doctor_DarioZemlak.HavgId = "00208"
	Doctor_DarioZemlak.Okv = "98"
	Doctor_DarioZemlak.MediverbundId = ""
	Doctor_DarioZemlak.HasHzvContracts = false
	Doctor_DarioZemlak.HavgVpId = util.NewString("H2AAAXR")

	seed, _ = strconv.ParseInt("00218", 10, 64)
	gofakeit.Seed(seed)
	Doctor_LeonoraFisher = data.FakeDoctor()
	Doctor_LeonoraFisher.Bsnr = "981111100"
	Doctor_LeonoraFisher.Lanr = "999964601"
	Doctor_LeonoraFisher.HavgId = "00218"
	Doctor_LeonoraFisher.Okv = "98"
	Doctor_LeonoraFisher.MediverbundId = ""
	Doctor_LeonoraFisher.HasHzvContracts = false
	Doctor_LeonoraFisher.HavgVpId = util.NewString("H3IAADY")

	seed, _ = strconv.ParseInt("00228", 10, 64)
	gofakeit.Seed(seed)
	Doctor_AileenMueller = data.FakeDoctor()
	Doctor_AileenMueller.Bsnr = "982222200"
	Doctor_AileenMueller.Lanr = "999952801"
	Doctor_AileenMueller.HavgId = "00228"
	Doctor_AileenMueller.Okv = "98"
	Doctor_AileenMueller.MediverbundId = ""
	Doctor_AileenMueller.HasHzvContracts = false
	Doctor_AileenMueller.HavgVpId = util.NewString("H4QAANJ")

	seed, _ = strconv.ParseInt("00204", 10, 64)
	gofakeit.Seed(seed)
	Doctor_JanieKeebler = data.FakeDoctor()
	Doctor_JanieKeebler.Bsnr = "201111100"
	Doctor_JanieKeebler.Lanr = "999988201"
	Doctor_JanieKeebler.HavgId = "00204"
	Doctor_JanieKeebler.Okv = "20"
	Doctor_JanieKeebler.MediverbundId = ""
	Doctor_JanieKeebler.HasHzvContracts = false
	Doctor_JanieKeebler.HavgVpId = util.NewString("HZQAAHH")

	seed, _ = strconv.ParseInt("00220", 10, 64)
	gofakeit.Seed(seed)
	Doctor_OdessaAltenwerth = data.FakeDoctor()
	Doctor_OdessaAltenwerth.Bsnr = "202222200"
	Doctor_OdessaAltenwerth.Lanr = "999961301"
	Doctor_OdessaAltenwerth.HavgId = "00220"
	Doctor_OdessaAltenwerth.Okv = "20"
	Doctor_OdessaAltenwerth.MediverbundId = ""
	Doctor_OdessaAltenwerth.HasHzvContracts = false
	Doctor_OdessaAltenwerth.HavgVpId = util.NewString("H3QAAWZ")

	seed, _ = strconv.ParseInt("00221", 10, 64)
	gofakeit.Seed(seed)
	Doctor_MckenzieChamplin = data.FakeDoctor()
	Doctor_MckenzieChamplin.Bsnr = "202222200"
	Doctor_MckenzieChamplin.Lanr = "999959501"
	Doctor_MckenzieChamplin.HavgId = "00221"
	Doctor_MckenzieChamplin.Okv = "20"
	Doctor_MckenzieChamplin.MediverbundId = ""
	Doctor_MckenzieChamplin.HasHzvContracts = false
	Doctor_MckenzieChamplin.HavgVpId = util.NewString("H3UAA5G")

	seed, _ = strconv.ParseInt("87408", 10, 64)
	gofakeit.Seed(seed)
	Doctor_VincenzaStehr = data.FakeDoctor()
	Doctor_VincenzaStehr.Bsnr = "521111100"
	Doctor_VincenzaStehr.Lanr = "999995501"
	Doctor_VincenzaStehr.HavgId = "87408"
	Doctor_VincenzaStehr.Okv = "52"
	Doctor_VincenzaStehr.MediverbundId = ""
	Doctor_VincenzaStehr.HasHzvContracts = false
	Doctor_VincenzaStehr.HavgVpId = util.NewString("HOBKQCAA7R")

	seed, _ = strconv.ParseInt("00215", 10, 64)
	gofakeit.Seed(seed)
	Doctor_RemingtonGislason = data.FakeDoctor()
	Doctor_RemingtonGislason.Bsnr = "528888800"
	Doctor_RemingtonGislason.Lanr = "999967901"
	Doctor_RemingtonGislason.HavgId = "00215"
	Doctor_RemingtonGislason.Okv = "52"
	Doctor_RemingtonGislason.MediverbundId = ""
	Doctor_RemingtonGislason.HasHzvContracts = false
	Doctor_RemingtonGislason.HavgVpId = util.NewString("H24AAAE")

	seed, _ = strconv.ParseInt("00240", 10, 64)
	gofakeit.Seed(seed)
	Doctor_SharonLang = data.FakeDoctor()
	Doctor_SharonLang.Bsnr = "011111100"
	Doctor_SharonLang.Lanr = "999935901"
	Doctor_SharonLang.HavgId = "00240"
	Doctor_SharonLang.Okv = "01"
	Doctor_SharonLang.MediverbundId = ""
	Doctor_SharonLang.HasHzvContracts = false
	Doctor_SharonLang.HavgVpId = util.NewString("H6AAAK6")

	seed, _ = strconv.ParseInt("00241", 10, 64)
	gofakeit.Seed(seed)
	Doctor_JordyVeum = data.FakeDoctor()
	Doctor_JordyVeum.Bsnr = "011111100"
	Doctor_JordyVeum.Lanr = "999933701"
	Doctor_JordyVeum.HavgId = "00241"
	Doctor_JordyVeum.Okv = "01"
	Doctor_JordyVeum.MediverbundId = ""
	Doctor_JordyVeum.HasHzvContracts = false
	Doctor_JordyVeum.HavgVpId = util.NewString("H6EAA2K")

	seed, _ = strconv.ParseInt("00205", 10, 64)
	gofakeit.Seed(seed)
	Doctor_NoraWalker = data.FakeDoctor()
	Doctor_NoraWalker.Bsnr = "012222200"
	Doctor_NoraWalker.Lanr = "999987101"
	Doctor_NoraWalker.HavgId = "00205"
	Doctor_NoraWalker.Okv = "01"
	Doctor_NoraWalker.MediverbundId = ""
	Doctor_NoraWalker.HasHzvContracts = false
	Doctor_NoraWalker.HavgVpId = util.NewString("HZUAAJX")

	seed, _ = strconv.ParseInt("00207", 10, 64)
	gofakeit.Seed(seed)
	Doctor_AlessandraHowell = data.FakeDoctor()
	Doctor_AlessandraHowell.Bsnr = "031111100"
	Doctor_AlessandraHowell.Lanr = "999926401"
	Doctor_AlessandraHowell.HavgId = "00207"
	Doctor_AlessandraHowell.Okv = "03"
	Doctor_AlessandraHowell.MediverbundId = ""
	Doctor_AlessandraHowell.HasHzvContracts = false
	Doctor_AlessandraHowell.HavgVpId = util.NewString("HZ4AALE")

	seed, _ = strconv.ParseInt("00236", 10, 64)
	gofakeit.Seed(seed)
	Doctor_RosaJacobson = data.FakeDoctor()
	Doctor_RosaJacobson.Bsnr = "032222200"
	Doctor_RosaJacobson.Lanr = "999940001"
	Doctor_RosaJacobson.HavgId = "00236"
	Doctor_RosaJacobson.Okv = "03"
	Doctor_RosaJacobson.MediverbundId = ""
	Doctor_RosaJacobson.HasHzvContracts = false
	Doctor_RosaJacobson.HavgVpId = util.NewString("H5QAAEE")

	seed, _ = strconv.ParseInt("00237", 10, 64)
	gofakeit.Seed(seed)
	Doctor_DollyBoyle = data.FakeDoctor()
	Doctor_DollyBoyle.Bsnr = "032222200"
	Doctor_DollyBoyle.Lanr = "999939301"
	Doctor_DollyBoyle.HavgId = "00237"
	Doctor_DollyBoyle.Okv = "03"
	Doctor_DollyBoyle.MediverbundId = ""
	Doctor_DollyBoyle.HasHzvContracts = false
	Doctor_DollyBoyle.HavgVpId = util.NewString("H5UAA4G")

	seed, _ = strconv.ParseInt("00219", 10, 64)
	gofakeit.Seed(seed)
	Doctor_RosalindBoehm = data.FakeDoctor()
	Doctor_RosalindBoehm.Bsnr = "171111100"
	Doctor_RosalindBoehm.Lanr = "999963501"
	Doctor_RosalindBoehm.HavgId = "00219"
	Doctor_RosalindBoehm.Okv = "17"
	Doctor_RosalindBoehm.MediverbundId = ""
	Doctor_RosalindBoehm.HasHzvContracts = false
	Doctor_RosalindBoehm.HavgVpId = util.NewString("H3MAAX5")

	seed, _ = strconv.ParseInt("00234", 10, 64)
	gofakeit.Seed(seed)
	Doctor_AnaisMurazik = data.FakeDoctor()
	Doctor_AnaisMurazik.Bsnr = "172222200"
	Doctor_AnaisMurazik.Lanr = "999942201"
	Doctor_AnaisMurazik.HavgId = "00234"
	Doctor_AnaisMurazik.Okv = "17"
	Doctor_AnaisMurazik.MediverbundId = ""
	Doctor_AnaisMurazik.HasHzvContracts = false
	Doctor_AnaisMurazik.HavgVpId = util.NewString("H5IAAQB")

	seed, _ = strconv.ParseInt("00235", 10, 64)
	gofakeit.Seed(seed)
	Doctor_MartaRosenbaum = data.FakeDoctor()
	Doctor_MartaRosenbaum.Bsnr = "172222200"
	Doctor_MartaRosenbaum.Lanr = "999941101"
	Doctor_MartaRosenbaum.HavgId = "00235"
	Doctor_MartaRosenbaum.Okv = "17"
	Doctor_MartaRosenbaum.MediverbundId = ""
	Doctor_MartaRosenbaum.HasHzvContracts = false
	Doctor_MartaRosenbaum.HavgVpId = util.NewString("H5MAABD")

	seed, _ = strconv.ParseInt("00201", 10, 64)
	gofakeit.Seed(seed)
	Doctor_SkylaRowe = data.FakeDoctor()
	Doctor_SkylaRowe.Bsnr = "511111100"
	Doctor_SkylaRowe.Lanr = "999991101"
	Doctor_SkylaRowe.HavgId = "00201"
	Doctor_SkylaRowe.Okv = "51"
	Doctor_SkylaRowe.MediverbundId = ""
	Doctor_SkylaRowe.HasHzvContracts = false
	Doctor_SkylaRowe.HavgVpId = util.NewString("HZEAAC6")

	seed, _ = strconv.ParseInt("00232", 10, 64)
	gofakeit.Seed(seed)
	Doctor_MandyLeuschke = data.FakeDoctor()
	Doctor_MandyLeuschke.Bsnr = "512222200"
	Doctor_MandyLeuschke.Lanr = "999947701"
	Doctor_MandyLeuschke.HavgId = "00232"
	Doctor_MandyLeuschke.Okv = "51"
	Doctor_MandyLeuschke.MediverbundId = ""
	Doctor_MandyLeuschke.HasHzvContracts = false
	Doctor_MandyLeuschke.HavgVpId = util.NewString("H5AAAIX")

	seed, _ = strconv.ParseInt("00233", 10, 64)
	gofakeit.Seed(seed)
	Doctor_ColbyHowe = data.FakeDoctor()
	Doctor_ColbyHowe.Bsnr = "512222200"
	Doctor_ColbyHowe.Lanr = "999946601"
	Doctor_ColbyHowe.HavgId = "00233"
	Doctor_ColbyHowe.Okv = "51"
	Doctor_ColbyHowe.MediverbundId = ""
	Doctor_ColbyHowe.HasHzvContracts = false
	Doctor_ColbyHowe.HavgVpId = util.NewString("H5EAAY2")

	seed, _ = strconv.ParseInt("00244", 10, 64)
	gofakeit.Seed(seed)
	Doctor_JazlynLeffler = data.FakeDoctor()
	Doctor_JazlynLeffler.Bsnr = "731111100"
	Doctor_JazlynLeffler.Lanr = "999928601"
	Doctor_JazlynLeffler.HavgId = "00244"
	Doctor_JazlynLeffler.Okv = "73"
	Doctor_JazlynLeffler.MediverbundId = ""
	Doctor_JazlynLeffler.HasHzvContracts = false
	Doctor_JazlynLeffler.HavgVpId = util.NewString("H6QAAGY")

	seed, _ = strconv.ParseInt("00245", 10, 64)
	gofakeit.Seed(seed)
	Doctor_EstrellaReichert = data.FakeDoctor()
	Doctor_EstrellaReichert.Bsnr = "732222200"
	Doctor_EstrellaReichert.Lanr = "999927501"
	Doctor_EstrellaReichert.HavgId = "00245"
	Doctor_EstrellaReichert.Okv = "73"
	Doctor_EstrellaReichert.MediverbundId = ""
	Doctor_EstrellaReichert.HasHzvContracts = false
	Doctor_EstrellaReichert.HavgVpId = util.NewString("H6UAAC7")

	seed, _ = strconv.ParseInt("00246", 10, 64)
	gofakeit.Seed(seed)
	Doctor_DevonteFay = data.FakeDoctor()
	Doctor_DevonteFay.Bsnr = "732222200"
	Doctor_DevonteFay.Lanr = "999929701"
	Doctor_DevonteFay.HavgId = "00246"
	Doctor_DevonteFay.Okv = "73"
	Doctor_DevonteFay.MediverbundId = ""
	Doctor_DevonteFay.HasHzvContracts = false
	Doctor_DevonteFay.HavgVpId = util.NewString("H6YAACX")

	seed, _ = strconv.ParseInt("00227", 10, 64)
	gofakeit.Seed(seed)
	Doctor_EudoraBarton = data.FakeDoctor()
	Doctor_EudoraBarton.Bsnr = "931111100"
	Doctor_EudoraBarton.Lanr = "999953901"
	Doctor_EudoraBarton.HavgId = "00227"
	Doctor_EudoraBarton.Okv = "93"
	Doctor_EudoraBarton.MediverbundId = ""
	Doctor_EudoraBarton.HasHzvContracts = false
	Doctor_EudoraBarton.HavgVpId = util.NewString("H4MAAFQ")

	seed, _ = strconv.ParseInt("00229", 10, 64)
	gofakeit.Seed(seed)
	Doctor_KaleyKrajcik = data.FakeDoctor()
	Doctor_KaleyKrajcik.Bsnr = "932222200"
	Doctor_KaleyKrajcik.Lanr = "999950601"
	Doctor_KaleyKrajcik.HavgId = "00229"
	Doctor_KaleyKrajcik.Okv = "93"
	Doctor_KaleyKrajcik.MediverbundId = ""
	Doctor_KaleyKrajcik.HasHzvContracts = false
	Doctor_KaleyKrajcik.HavgVpId = util.NewString("H4UAAIZ")

	seed, _ = strconv.ParseInt("00230", 10, 64)
	gofakeit.Seed(seed)
	Doctor_ElijahNienow = data.FakeDoctor()
	Doctor_ElijahNienow.Bsnr = "932222200"
	Doctor_ElijahNienow.Lanr = "999949901"
	Doctor_ElijahNienow.HavgId = "00230"
	Doctor_ElijahNienow.Okv = "93"
	Doctor_ElijahNienow.MediverbundId = ""
	Doctor_ElijahNienow.HasHzvContracts = false
	Doctor_ElijahNienow.HavgVpId = util.NewString("H4YAAZC")

	seed, _ = strconv.ParseInt("00247", 10, 64)
	gofakeit.Seed(seed)
	Doctor_ChelseyLubowitz = data.FakeDoctor()
	Doctor_ChelseyLubowitz.Bsnr = "831111100"
	Doctor_ChelseyLubowitz.Lanr = "999924201"
	Doctor_ChelseyLubowitz.HavgId = "00247"
	Doctor_ChelseyLubowitz.Okv = "83"
	Doctor_ChelseyLubowitz.MediverbundId = ""
	Doctor_ChelseyLubowitz.HasHzvContracts = false
	Doctor_ChelseyLubowitz.HavgVpId = util.NewString("H64AAT6")

	seed, _ = strconv.ParseInt("00248", 10, 64)
	gofakeit.Seed(seed)
	Doctor_DorianKulas = data.FakeDoctor()
	Doctor_DorianKulas.Bsnr = "832222200"
	Doctor_DorianKulas.Lanr = "999922001"
	Doctor_DorianKulas.HavgId = "00248"
	Doctor_DorianKulas.Okv = "83"
	Doctor_DorianKulas.MediverbundId = ""
	Doctor_DorianKulas.HasHzvContracts = false
	Doctor_DorianKulas.HavgVpId = util.NewString("H7AAA7D")

	seed, _ = strconv.ParseInt("00249", 10, 64)
	gofakeit.Seed(seed)
	Doctor_ClaudineBernhard = data.FakeDoctor()
	Doctor_ClaudineBernhard.Bsnr = "832222200"
	Doctor_ClaudineBernhard.Lanr = "999920801"
	Doctor_ClaudineBernhard.HavgId = "00249"
	Doctor_ClaudineBernhard.Okv = "83"
	Doctor_ClaudineBernhard.MediverbundId = ""
	Doctor_ClaudineBernhard.HasHzvContracts = false
	Doctor_ClaudineBernhard.HavgVpId = util.NewString("H7EAAQR")

	DoctorParticipation_CadeLarkin_AOK_FA_GASTRO_BW = &data.DoctorParticipation{
		Doctor:         Doctor_CadeLarkin,
		ContractId:     "AOK_FA_GASTRO_BW",
		ChargeSystemId: "AOK_FA_GASTRO_BW",
		Type:           "FachaerztlicheVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_CadeLarkin_AOK_FA_KARDIO_BW = &data.DoctorParticipation{
		Doctor:         Doctor_CadeLarkin,
		ContractId:     "AOK_FA_KARDIO_BW",
		ChargeSystemId: "AOK_FA_KARDIO_BW",
		Type:           "FachaerztlicheVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_CadeLarkin_AOK_FA_NEPHRO_BW = &data.DoctorParticipation{
		Doctor:         Doctor_CadeLarkin,
		ContractId:     "AOK_FA_NEPHRO_BW",
		ChargeSystemId: "AOK_FA_NEPHRO_BW",
		Type:           "FachaerztlicheVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_CadeLarkin_AOK_FA_NPPP_BW = &data.DoctorParticipation{
		Doctor:         Doctor_CadeLarkin,
		ContractId:     "AOK_FA_NPPP_BW",
		ChargeSystemId: "AOK_FA_NPPP_BW",
		Type:           "FachaerztlicheVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_CadeLarkin_AOK_FA_OC_BW = &data.DoctorParticipation{
		Doctor:         Doctor_CadeLarkin,
		ContractId:     "AOK_FA_OC_BW",
		ChargeSystemId: "AOK_FA_OC_BW",
		Type:           "FachaerztlicheVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_CadeLarkin_AOK_FA_PNEUMO_BW = &data.DoctorParticipation{
		Doctor:         Doctor_CadeLarkin,
		ContractId:     "AOK_FA_PNEUMO_BW",
		ChargeSystemId: "AOK_FA_PNEUMO_BW",
		Type:           "FachaerztlicheVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_CadeLarkin_AOK_FA_URO_BW = &data.DoctorParticipation{
		Doctor:         Doctor_CadeLarkin,
		ContractId:     "AOK_FA_URO_BW",
		ChargeSystemId: "AOK_FA_URO_BW",
		Type:           "FachaerztlicheVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_CatharineRobel_AOK_BW_IV_P = &data.DoctorParticipation{
		Doctor:         Doctor_CatharineRobel,
		ContractId:     "AOK_BW_IV_P",
		ChargeSystemId: "AOK_BW_IV_P",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_KadenCasper_AOK_BW_IV_P = &data.DoctorParticipation{
		Doctor:         Doctor_KadenCasper,
		ContractId:     "AOK_BW_IV_P",
		ChargeSystemId: "AOK_BW_IV_P",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_RomaWest_AOK_FA_GASTRO_BW = &data.DoctorParticipation{
		Doctor:         Doctor_RomaWest,
		ContractId:     "AOK_FA_GASTRO_BW",
		ChargeSystemId: "AOK_FA_GASTRO_BW",
		Type:           "FachaerztlicheVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_RomaWest_AOK_FA_KARDIO_BW = &data.DoctorParticipation{
		Doctor:         Doctor_RomaWest,
		ContractId:     "AOK_FA_KARDIO_BW",
		ChargeSystemId: "AOK_FA_KARDIO_BW",
		Type:           "FachaerztlicheVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_RomaWest_AOK_FA_NEPHRO_BW = &data.DoctorParticipation{
		Doctor:         Doctor_RomaWest,
		ContractId:     "AOK_FA_NEPHRO_BW",
		ChargeSystemId: "AOK_FA_NEPHRO_BW",
		Type:           "FachaerztlicheVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_RomaWest_AOK_FA_NPPP_BW = &data.DoctorParticipation{
		Doctor:         Doctor_RomaWest,
		ContractId:     "AOK_FA_NPPP_BW",
		ChargeSystemId: "AOK_FA_NPPP_BW",
		Type:           "FachaerztlicheVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_RomaWest_AOK_FA_OC_BW = &data.DoctorParticipation{
		Doctor:         Doctor_RomaWest,
		ContractId:     "AOK_FA_OC_BW",
		ChargeSystemId: "AOK_FA_OC_BW",
		Type:           "FachaerztlicheVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_RomaWest_AOK_FA_PNEUMO_BW = &data.DoctorParticipation{
		Doctor:         Doctor_RomaWest,
		ContractId:     "AOK_FA_PNEUMO_BW",
		ChargeSystemId: "AOK_FA_PNEUMO_BW",
		Type:           "FachaerztlicheVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_RomaWest_AOK_FA_URO_BW = &data.DoctorParticipation{
		Doctor:         Doctor_RomaWest,
		ContractId:     "AOK_FA_URO_BW",
		ChargeSystemId: "AOK_FA_URO_BW",
		Type:           "FachaerztlicheVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_CadeLarkin_BKK_BOSCH_FaV = &data.DoctorParticipation{
		Doctor:         Doctor_CadeLarkin,
		ContractId:     "BKK_BOSCH_FaV",
		ChargeSystemId: "BKK_BOSCH_FaV",
		Type:           "FachaerztlicheVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_CadeLarkin_BKK_VAG_FaV = &data.DoctorParticipation{
		Doctor:         Doctor_CadeLarkin,
		ContractId:     "BKK_VAG_FaV",
		ChargeSystemId: "BKK_VAG_FaV",
		Type:           "FachaerztlicheVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AleenBoyle_AOK_BW_IV_P = &data.DoctorParticipation{
		Doctor:         Doctor_AleenBoyle,
		ContractId:     "AOK_BW_IV_P",
		ChargeSystemId: "AOK_BW_IV_P",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SadieGusikowski_616123400_BKK_VAG_FaV = &data.DoctorParticipation{
		Doctor:         Doctor_SadieGusikowski_616123400,
		ContractId:     "BKK_VAG_FaV",
		ChargeSystemId: "BKK_VAG_FaV",
		Type:           "FachaerztlicheVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_CadeLarkin_MEDI_FA_PT_BW = &data.DoctorParticipation{
		Doctor:         Doctor_CadeLarkin,
		ContractId:     "MEDI_FA_PT_BW",
		ChargeSystemId: "MEDI_FA_PT_BW",
		Type:           "FachaerztlicheVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_CadeLarkin_EK_FA_DIA_BW = &data.DoctorParticipation{
		Doctor:         Doctor_CadeLarkin,
		ContractId:     "EK_FA_DIA_BW",
		ChargeSystemId: "EK_FA_DIA_BW",
		Type:           "FachaerztlicheVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_BulahHaley_AOK_BY_HZV_S15 = &data.DoctorParticipation{
		Doctor:         Doctor_BulahHaley,
		ContractId:     "AOK_BY_HZV_S15",
		ChargeSystemId: "AOK_BY_HZV_S15",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_TyreseKoepp_AOK_BY_HZV_S15 = &data.DoctorParticipation{
		Doctor:         Doctor_TyreseKoepp,
		ContractId:     "AOK_BY_HZV_S15",
		ChargeSystemId: "AOK_BY_HZV_S15",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_GraceBogan_AOK_BY_HZV_S15 = &data.DoctorParticipation{
		Doctor:         Doctor_GraceBogan,
		ContractId:     "AOK_BY_HZV_S15",
		ChargeSystemId: "AOK_BY_HZV_S15",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_LudwigKub_AOK_HE_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_LudwigKub,
		ContractId:     "AOK_HE_HZV",
		ChargeSystemId: "AOK_HE_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MohammedCollier_AOK_HE_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MohammedCollier,
		ContractId:     "AOK_HE_HZV",
		ChargeSystemId: "AOK_HE_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_PrinceKing_AOK_HE_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_PrinceKing,
		ContractId:     "AOK_HE_HZV",
		ChargeSystemId: "AOK_HE_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_NatalieNicolas_AOK_HH_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_NatalieNicolas,
		ContractId:     "AOK_HH_HZV",
		ChargeSystemId: "AOK_HH_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_EwaldMcGlynn_AOK_HH_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_EwaldMcGlynn,
		ContractId:     "AOK_HH_HZV",
		ChargeSystemId: "AOK_HH_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MaudieHarris_AOK_HH_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MaudieHarris,
		ContractId:     "AOK_HH_HZV",
		ChargeSystemId: "AOK_HH_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_BeulahHomenick_AOK_IKK_BLN_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_BeulahHomenick,
		ContractId:     "AOK_IKK_BLN_HZV",
		ChargeSystemId: "AOK_IKK_BLN_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ColtMurphy_AOK_IKK_BLN_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ColtMurphy,
		ContractId:     "AOK_IKK_BLN_HZV",
		ChargeSystemId: "AOK_IKK_BLN_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_BerneiceCassin_AOK_IKK_BLN_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_BerneiceCassin,
		ContractId:     "AOK_IKK_BLN_HZV",
		ChargeSystemId: "AOK_IKK_BLN_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SuzanneGerhold_AOK_NO_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SuzanneGerhold,
		ContractId:     "AOK_NO_HZV",
		ChargeSystemId: "AOK_NO_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AnnaMurray_AOK_NO_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AnnaMurray,
		ContractId:     "AOK_NO_HZV",
		ChargeSystemId: "AOK_NO_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JacklynHalvorson_AOK_NO_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JacklynHalvorson,
		ContractId:     "AOK_NO_HZV",
		ChargeSystemId: "AOK_NO_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DarioZemlak_AOK_PLUS_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DarioZemlak,
		ContractId:     "AOK_PLUS_HZV",
		ChargeSystemId: "AOK_PLUS_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_LeonoraFisher_AOK_PLUS_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_LeonoraFisher,
		ContractId:     "AOK_PLUS_HZV",
		ChargeSystemId: "AOK_PLUS_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AileenMueller_AOK_PLUS_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AileenMueller,
		ContractId:     "AOK_PLUS_HZV",
		ChargeSystemId: "AOK_PLUS_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JanieKeebler_AOK_WL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JanieKeebler,
		ContractId:     "AOK_WL_HZV",
		ChargeSystemId: "AOK_WL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_OdessaAltenwerth_AOK_WL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_OdessaAltenwerth,
		ContractId:     "AOK_WL_HZV",
		ChargeSystemId: "AOK_WL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MckenzieChamplin_AOK_WL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MckenzieChamplin,
		ContractId:     "AOK_WL_HZV",
		ChargeSystemId: "AOK_WL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_CatharineRobel_AWH_01 = &data.DoctorParticipation{
		Doctor:         Doctor_CatharineRobel,
		ContractId:     "AWH_01",
		ChargeSystemId: "AWH_01",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_KadenCasper_AWH_01 = &data.DoctorParticipation{
		Doctor:         Doctor_KadenCasper,
		ContractId:     "AWH_01",
		ChargeSystemId: "AWH_01",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AleenBoyle_AWH_01 = &data.DoctorParticipation{
		Doctor:         Doctor_AleenBoyle,
		ContractId:     "AWH_01",
		ChargeSystemId: "AWH_01",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_VincenzaStehr_AWH_01 = &data.DoctorParticipation{
		Doctor:         Doctor_VincenzaStehr,
		ContractId:     "AWH_01",
		ChargeSystemId: "AOK_BW_BVKJ",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_RemingtonGislason_AWH_01 = &data.DoctorParticipation{
		Doctor:         Doctor_RemingtonGislason,
		ContractId:     "AWH_01",
		ChargeSystemId: "AOK_BW_BVKJ",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_CatharineRobel_BKK_BOSCH_BW = &data.DoctorParticipation{
		Doctor:         Doctor_CatharineRobel,
		ContractId:     "BKK_BOSCH_BW",
		ChargeSystemId: "BKK_BOSCH_BW",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_KadenCasper_BKK_BOSCH_BW = &data.DoctorParticipation{
		Doctor:         Doctor_KadenCasper,
		ContractId:     "BKK_BOSCH_BW",
		ChargeSystemId: "BKK_BOSCH_BW",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AleenBoyle_BKK_BOSCH_BW = &data.DoctorParticipation{
		Doctor:         Doctor_AleenBoyle,
		ContractId:     "BKK_BOSCH_BW",
		ChargeSystemId: "BKK_BOSCH_BW",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_CatharineRobel_BKK_BW_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_CatharineRobel,
		ContractId:     "BKK_BW_HZV",
		ChargeSystemId: "BKK_BW_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_KadenCasper_BKK_BW_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_KadenCasper,
		ContractId:     "BKK_BW_HZV",
		ChargeSystemId: "BKK_BW_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AleenBoyle_BKK_BW_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AleenBoyle,
		ContractId:     "BKK_BW_HZV",
		ChargeSystemId: "BKK_BW_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_BulahHaley_BKK_BY_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_BulahHaley,
		ContractId:     "BKK_BY_HZV",
		ChargeSystemId: "BKK_BY_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_TyreseKoepp_BKK_BY_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_TyreseKoepp,
		ContractId:     "BKK_BY_HZV",
		ChargeSystemId: "BKK_BY_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_GraceBogan_BKK_BY_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_GraceBogan,
		ContractId:     "BKK_BY_HZV",
		ChargeSystemId: "BKK_BY_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SharonLang_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SharonLang,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JordyVeum_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JordyVeum,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_NoraWalker_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_NoraWalker,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_NatalieNicolas_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_NatalieNicolas,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_EwaldMcGlynn_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_EwaldMcGlynn,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MaudieHarris_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MaudieHarris,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AlessandraHowell_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AlessandraHowell,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_RosaJacobson_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_RosaJacobson,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DollyBoyle_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DollyBoyle,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_RosalindBoehm_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_RosalindBoehm,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AnaisMurazik_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AnaisMurazik,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MartaRosenbaum_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MartaRosenbaum,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_LudwigKub_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_LudwigKub,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MohammedCollier_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MohammedCollier,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_PrinceKing_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_PrinceKing,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SkylaRowe_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SkylaRowe,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MandyLeuschke_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MandyLeuschke,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ColbyHowe_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ColbyHowe,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_BeulahHomenick_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_BeulahHomenick,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ColtMurphy_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ColtMurphy,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_BerneiceCassin_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_BerneiceCassin,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JazlynLeffler_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JazlynLeffler,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_EstrellaReichert_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_EstrellaReichert,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DevonteFay_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DevonteFay,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DarioZemlak_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DarioZemlak,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_LeonoraFisher_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_LeonoraFisher,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AileenMueller_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AileenMueller,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SuzanneGerhold_BKK_NO_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SuzanneGerhold,
		ContractId:     "BKK_NO_HZV",
		ChargeSystemId: "BKK_NO_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AnnaMurray_BKK_NO_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AnnaMurray,
		ContractId:     "BKK_NO_HZV",
		ChargeSystemId: "BKK_NO_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JacklynHalvorson_BKK_NO_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JacklynHalvorson,
		ContractId:     "BKK_NO_HZV",
		ChargeSystemId: "BKK_NO_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SharonLang_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SharonLang,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JordyVeum_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JordyVeum,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_NoraWalker_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_NoraWalker,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_NatalieNicolas_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_NatalieNicolas,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_EwaldMcGlynn_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_EwaldMcGlynn,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MaudieHarris_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MaudieHarris,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AlessandraHowell_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AlessandraHowell,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_RosaJacobson_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_RosaJacobson,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DollyBoyle_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DollyBoyle,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JanieKeebler_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JanieKeebler,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_OdessaAltenwerth_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_OdessaAltenwerth,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MckenzieChamplin_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MckenzieChamplin,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SuzanneGerhold_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SuzanneGerhold,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AnnaMurray_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AnnaMurray,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JacklynHalvorson_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JacklynHalvorson,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_LudwigKub_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_LudwigKub,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MohammedCollier_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MohammedCollier,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_PrinceKing_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_PrinceKing,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SkylaRowe_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SkylaRowe,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MandyLeuschke_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MandyLeuschke,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ColbyHowe_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ColbyHowe,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_BeulahHomenick_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_BeulahHomenick,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ColtMurphy_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ColtMurphy,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_BerneiceCassin_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_BerneiceCassin,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JazlynLeffler_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JazlynLeffler,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_EstrellaReichert_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_EstrellaReichert,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DevonteFay_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DevonteFay,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DarioZemlak_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DarioZemlak,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_LeonoraFisher_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_LeonoraFisher,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AileenMueller_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AileenMueller,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_CatharineRobel_BKK_VAG_BW = &data.DoctorParticipation{
		Doctor:         Doctor_CatharineRobel,
		ContractId:     "BKK_VAG_BW",
		ChargeSystemId: "BKK_VAG_BW",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_KadenCasper_BKK_VAG_BW = &data.DoctorParticipation{
		Doctor:         Doctor_KadenCasper,
		ContractId:     "BKK_VAG_BW",
		ChargeSystemId: "BKK_VAG_BW",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AleenBoyle_BKK_VAG_BW = &data.DoctorParticipation{
		Doctor:         Doctor_AleenBoyle,
		ContractId:     "BKK_VAG_BW",
		ChargeSystemId: "BKK_VAG_BW",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JanieKeebler_BKK_WL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JanieKeebler,
		ContractId:     "BKK_WL_HZV",
		ChargeSystemId: "BKK_WL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_OdessaAltenwerth_BKK_WL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_OdessaAltenwerth,
		ContractId:     "BKK_WL_HZV",
		ChargeSystemId: "BKK_WL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MckenzieChamplin_BKK_WL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MckenzieChamplin,
		ContractId:     "BKK_WL_HZV",
		ChargeSystemId: "BKK_WL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SkylaRowe_DAK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SkylaRowe,
		ContractId:     "DAK_HZV",
		ChargeSystemId: "DAK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MandyLeuschke_DAK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MandyLeuschke,
		ContractId:     "DAK_HZV",
		ChargeSystemId: "DAK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ColbyHowe_DAK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ColbyHowe,
		ContractId:     "DAK_HZV",
		ChargeSystemId: "DAK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_BeulahHomenick_EK_BLN_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_BeulahHomenick,
		ContractId:     "EK_BLN_HZV",
		ChargeSystemId: "EK_BLN_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ColtMurphy_EK_BLN_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ColtMurphy,
		ContractId:     "EK_BLN_HZV",
		ChargeSystemId: "EK_BLN_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_BerneiceCassin_EK_BLN_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_BerneiceCassin,
		ContractId:     "EK_BLN_HZV",
		ChargeSystemId: "EK_BLN_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_CatharineRobel_EK_BW_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_CatharineRobel,
		ContractId:     "EK_BW_HZV",
		ChargeSystemId: "EK_BW_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_KadenCasper_EK_BW_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_KadenCasper,
		ContractId:     "EK_BW_HZV",
		ChargeSystemId: "EK_BW_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AleenBoyle_EK_BW_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AleenBoyle,
		ContractId:     "EK_BW_HZV",
		ChargeSystemId: "EK_BW_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_BulahHaley_EK_BY_HZV_S12 = &data.DoctorParticipation{
		Doctor:         Doctor_BulahHaley,
		ContractId:     "EK_BY_HZV_S12",
		ChargeSystemId: "EK_BY_HZV_S12",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_TyreseKoepp_EK_BY_HZV_S12 = &data.DoctorParticipation{
		Doctor:         Doctor_TyreseKoepp,
		ContractId:     "EK_BY_HZV_S12",
		ChargeSystemId: "EK_BY_HZV_S12",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_GraceBogan_EK_BY_HZV_S12 = &data.DoctorParticipation{
		Doctor:         Doctor_GraceBogan,
		ContractId:     "EK_BY_HZV_S12",
		ChargeSystemId: "EK_BY_HZV_S12",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AlessandraHowell_EK_HB_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AlessandraHowell,
		ContractId:     "EK_HB_HZV",
		ChargeSystemId: "EK_HB_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_RosaJacobson_EK_HB_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_RosaJacobson,
		ContractId:     "EK_HB_HZV",
		ChargeSystemId: "EK_HB_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DollyBoyle_EK_HB_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DollyBoyle,
		ContractId:     "EK_HB_HZV",
		ChargeSystemId: "EK_HB_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_NatalieNicolas_EK_HH_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_NatalieNicolas,
		ContractId:     "EK_HH_HZV",
		ChargeSystemId: "EK_HH_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_EwaldMcGlynn_EK_HH_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_EwaldMcGlynn,
		ContractId:     "EK_HH_HZV",
		ChargeSystemId: "EK_HH_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MaudieHarris_EK_HH_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MaudieHarris,
		ContractId:     "EK_HH_HZV",
		ChargeSystemId: "EK_HH_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SuzanneGerhold_EK_NO_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SuzanneGerhold,
		ContractId:     "EK_NO_HZV",
		ChargeSystemId: "EK_NO_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AnnaMurray_EK_NO_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AnnaMurray,
		ContractId:     "EK_NO_HZV",
		ChargeSystemId: "EK_NO_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JacklynHalvorson_EK_NO_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JacklynHalvorson,
		ContractId:     "EK_NO_HZV",
		ChargeSystemId: "EK_NO_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SkylaRowe_EK_RLP_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SkylaRowe,
		ContractId:     "EK_RLP_HZV",
		ChargeSystemId: "EK_RLP_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MandyLeuschke_EK_RLP_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MandyLeuschke,
		ContractId:     "EK_RLP_HZV",
		ChargeSystemId: "EK_RLP_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ColbyHowe_EK_RLP_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ColbyHowe,
		ContractId:     "EK_RLP_HZV",
		ChargeSystemId: "EK_RLP_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SharonLang_EK_SH_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SharonLang,
		ContractId:     "EK_SH_HZV",
		ChargeSystemId: "EK_SH_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JordyVeum_EK_SH_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JordyVeum,
		ContractId:     "EK_SH_HZV",
		ChargeSystemId: "EK_SH_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_NoraWalker_EK_SH_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_NoraWalker,
		ContractId:     "EK_SH_HZV",
		ChargeSystemId: "EK_SH_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JazlynLeffler_EK_SL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JazlynLeffler,
		ContractId:     "EK_SL_HZV",
		ChargeSystemId: "EK_SL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_EstrellaReichert_EK_SL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_EstrellaReichert,
		ContractId:     "EK_SL_HZV",
		ChargeSystemId: "EK_SL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DevonteFay_EK_SL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DevonteFay,
		ContractId:     "EK_SL_HZV",
		ChargeSystemId: "EK_SL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DarioZemlak_EK_SN_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DarioZemlak,
		ContractId:     "EK_SN_HZV",
		ChargeSystemId: "EK_SN_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_LeonoraFisher_EK_SN_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_LeonoraFisher,
		ContractId:     "EK_SN_HZV",
		ChargeSystemId: "EK_SN_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AileenMueller_EK_SN_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AileenMueller,
		ContractId:     "EK_SN_HZV",
		ChargeSystemId: "EK_SN_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JanieKeebler_EK_WL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JanieKeebler,
		ContractId:     "EK_WL_HZV",
		ChargeSystemId: "EK_WL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_OdessaAltenwerth_EK_WL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_OdessaAltenwerth,
		ContractId:     "EK_WL_HZV",
		ChargeSystemId: "EK_WL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MckenzieChamplin_EK_WL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MckenzieChamplin,
		ContractId:     "EK_WL_HZV",
		ChargeSystemId: "EK_WL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_NatalieNicolas_HKK_HZV_NORD = &data.DoctorParticipation{
		Doctor:         Doctor_NatalieNicolas,
		ContractId:     "HKK_HZV_NORD",
		ChargeSystemId: "HKK_HZV_NORD",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_EwaldMcGlynn_HKK_HZV_NORD = &data.DoctorParticipation{
		Doctor:         Doctor_EwaldMcGlynn,
		ContractId:     "HKK_HZV_NORD",
		ChargeSystemId: "HKK_HZV_NORD",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MaudieHarris_HKK_HZV_NORD = &data.DoctorParticipation{
		Doctor:         Doctor_MaudieHarris,
		ContractId:     "HKK_HZV_NORD",
		ChargeSystemId: "HKK_HZV_NORD",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AlessandraHowell_HKK_HZV_NORD = &data.DoctorParticipation{
		Doctor:         Doctor_AlessandraHowell,
		ContractId:     "HKK_HZV_NORD",
		ChargeSystemId: "HKK_HZV_NORD",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_RosaJacobson_HKK_HZV_NORD = &data.DoctorParticipation{
		Doctor:         Doctor_RosaJacobson,
		ContractId:     "HKK_HZV_NORD",
		ChargeSystemId: "HKK_HZV_NORD",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DollyBoyle_HKK_HZV_NORD = &data.DoctorParticipation{
		Doctor:         Doctor_DollyBoyle,
		ContractId:     "HKK_HZV_NORD",
		ChargeSystemId: "HKK_HZV_NORD",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_CatharineRobel_IKK_CL_BW_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_CatharineRobel,
		ContractId:     "IKK_CL_BW_HZV",
		ChargeSystemId: "IKK_CL_BW_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_KadenCasper_IKK_CL_BW_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_KadenCasper,
		ContractId:     "IKK_CL_BW_HZV",
		ChargeSystemId: "IKK_CL_BW_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AleenBoyle_IKK_CL_BW_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AleenBoyle,
		ContractId:     "IKK_CL_BW_HZV",
		ChargeSystemId: "IKK_CL_BW_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_CatharineRobel_LKK_BW_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_CatharineRobel,
		ContractId:     "LKK_BW_HZV",
		ChargeSystemId: "LKK_BW_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_KadenCasper_LKK_BW_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_KadenCasper,
		ContractId:     "LKK_BW_HZV",
		ChargeSystemId: "LKK_BW_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AleenBoyle_LKK_BW_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AleenBoyle,
		ContractId:     "LKK_BW_HZV",
		ChargeSystemId: "LKK_BW_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_BulahHaley_LKK_BY_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_BulahHaley,
		ContractId:     "LKK_BY_HZV",
		ChargeSystemId: "LKK_BY_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_TyreseKoepp_LKK_BY_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_TyreseKoepp,
		ContractId:     "LKK_BY_HZV",
		ChargeSystemId: "LKK_BY_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_GraceBogan_LKK_BY_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_GraceBogan,
		ContractId:     "LKK_BY_HZV",
		ChargeSystemId: "LKK_BY_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_LudwigKub_LKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_LudwigKub,
		ContractId:     "LKK_HZV",
		ChargeSystemId: "LKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MohammedCollier_LKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MohammedCollier,
		ContractId:     "LKK_HZV",
		ChargeSystemId: "LKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_PrinceKing_LKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_PrinceKing,
		ContractId:     "LKK_HZV",
		ChargeSystemId: "LKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SkylaRowe_LKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SkylaRowe,
		ContractId:     "LKK_HZV",
		ChargeSystemId: "LKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MandyLeuschke_LKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MandyLeuschke,
		ContractId:     "LKK_HZV",
		ChargeSystemId: "LKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ColbyHowe_LKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ColbyHowe,
		ContractId:     "LKK_HZV",
		ChargeSystemId: "LKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SuzanneGerhold_LKK_NO_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SuzanneGerhold,
		ContractId:     "LKK_NO_HZV",
		ChargeSystemId: "LKK_NO_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AnnaMurray_LKK_NO_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AnnaMurray,
		ContractId:     "LKK_NO_HZV",
		ChargeSystemId: "LKK_NO_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JacklynHalvorson_LKK_NO_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JacklynHalvorson,
		ContractId:     "LKK_NO_HZV",
		ChargeSystemId: "LKK_NO_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JanieKeebler_LKK_WL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JanieKeebler,
		ContractId:     "LKK_WL_HZV",
		ChargeSystemId: "LKK_WL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_OdessaAltenwerth_LKK_WL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_OdessaAltenwerth,
		ContractId:     "LKK_WL_HZV",
		ChargeSystemId: "LKK_WL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MckenzieChamplin_LKK_WL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MckenzieChamplin,
		ContractId:     "LKK_WL_HZV",
		ChargeSystemId: "LKK_WL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_CatharineRobel_RV_KBS_BW_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_CatharineRobel,
		ContractId:     "RV_KBS_BW_HZV",
		ChargeSystemId: "RV_KBS_BW_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_KadenCasper_RV_KBS_BW_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_KadenCasper,
		ContractId:     "RV_KBS_BW_HZV",
		ChargeSystemId: "RV_KBS_BW_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AleenBoyle_RV_KBS_BW_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AleenBoyle,
		ContractId:     "RV_KBS_BW_HZV",
		ChargeSystemId: "RV_KBS_BW_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SuzanneGerhold_RV_KBS_NO_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SuzanneGerhold,
		ContractId:     "RV_KBS_NO_HZV",
		ChargeSystemId: "RV_KBS_NO_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AnnaMurray_RV_KBS_NO_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AnnaMurray,
		ContractId:     "RV_KBS_NO_HZV",
		ChargeSystemId: "RV_KBS_NO_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JacklynHalvorson_RV_KBS_NO_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JacklynHalvorson,
		ContractId:     "RV_KBS_NO_HZV",
		ChargeSystemId: "RV_KBS_NO_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JanieKeebler_RV_KBS_WL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JanieKeebler,
		ContractId:     "RV_KBS_WL_HZV",
		ChargeSystemId: "RV_KBS_WL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_OdessaAltenwerth_RV_KBS_WL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_OdessaAltenwerth,
		ContractId:     "RV_KBS_WL_HZV",
		ChargeSystemId: "RV_KBS_WL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MckenzieChamplin_RV_KBS_WL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MckenzieChamplin,
		ContractId:     "RV_KBS_WL_HZV",
		ChargeSystemId: "RV_KBS_WL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_NatalieNicolas_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_NatalieNicolas,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_EwaldMcGlynn_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_EwaldMcGlynn,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MaudieHarris_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MaudieHarris,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_RosalindBoehm_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_RosalindBoehm,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AnaisMurazik_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AnaisMurazik,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MartaRosenbaum_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MartaRosenbaum,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JanieKeebler_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JanieKeebler,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_OdessaAltenwerth_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_OdessaAltenwerth,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MckenzieChamplin_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MckenzieChamplin,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SuzanneGerhold_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SuzanneGerhold,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AnnaMurray_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AnnaMurray,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JacklynHalvorson_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JacklynHalvorson,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_LudwigKub_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_LudwigKub,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MohammedCollier_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MohammedCollier,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_PrinceKing_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_PrinceKing,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_BulahHaley_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_BulahHaley,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_TyreseKoepp_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_TyreseKoepp,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_GraceBogan_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_GraceBogan,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_BeulahHomenick_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_BeulahHomenick,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ColtMurphy_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ColtMurphy,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_BerneiceCassin_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_BerneiceCassin,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_EudoraBarton_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_EudoraBarton,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_KaleyKrajcik_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_KaleyKrajcik,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ElijahNienow_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ElijahNienow,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DarioZemlak_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DarioZemlak,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_LeonoraFisher_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_LeonoraFisher,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AileenMueller_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AileenMueller,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SharonLang_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SharonLang,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JordyVeum_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JordyVeum,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_NoraWalker_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_NoraWalker,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_NatalieNicolas_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_NatalieNicolas,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_EwaldMcGlynn_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_EwaldMcGlynn,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MaudieHarris_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MaudieHarris,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AlessandraHowell_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AlessandraHowell,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_RosaJacobson_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_RosaJacobson,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DollyBoyle_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DollyBoyle,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_RosalindBoehm_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_RosalindBoehm,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AnaisMurazik_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AnaisMurazik,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MartaRosenbaum_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MartaRosenbaum,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JanieKeebler_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JanieKeebler,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_OdessaAltenwerth_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_OdessaAltenwerth,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MckenzieChamplin_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MckenzieChamplin,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SuzanneGerhold_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SuzanneGerhold,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AnnaMurray_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AnnaMurray,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JacklynHalvorson_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JacklynHalvorson,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_LudwigKub_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_LudwigKub,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MohammedCollier_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MohammedCollier,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_PrinceKing_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_PrinceKing,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SkylaRowe_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SkylaRowe,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MandyLeuschke_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MandyLeuschke,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ColbyHowe_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ColbyHowe,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_BulahHaley_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_BulahHaley,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_TyreseKoepp_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_TyreseKoepp,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_GraceBogan_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_GraceBogan,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_BeulahHomenick_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_BeulahHomenick,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ColtMurphy_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ColtMurphy,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_BerneiceCassin_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_BerneiceCassin,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JazlynLeffler_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JazlynLeffler,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_EstrellaReichert_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_EstrellaReichert,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DevonteFay_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DevonteFay,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DarioZemlak_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DarioZemlak,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_LeonoraFisher_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_LeonoraFisher,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AileenMueller_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AileenMueller,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_LudwigKub_EK_HE_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_LudwigKub,
		ContractId:     "EK_HE_HZV",
		ChargeSystemId: "EK_HE_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MohammedCollier_EK_HE_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MohammedCollier,
		ContractId:     "EK_HE_HZV",
		ChargeSystemId: "EK_HE_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_PrinceKing_EK_HE_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_PrinceKing,
		ContractId:     "EK_HE_HZV",
		ChargeSystemId: "EK_HE_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AlessandraHowell_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AlessandraHowell,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_RosaJacobson_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_RosaJacobson,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DollyBoyle_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DollyBoyle,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SharonLang_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SharonLang,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JordyVeum_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JordyVeum,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_NoraWalker_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_NoraWalker,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SharonLang_AOK_SH_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SharonLang,
		ContractId:     "AOK_SH_HZV",
		ChargeSystemId: "AOK_SH_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JordyVeum_AOK_SH_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JordyVeum,
		ContractId:     "AOK_SH_HZV",
		ChargeSystemId: "AOK_SH_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_NoraWalker_AOK_SH_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_NoraWalker,
		ContractId:     "AOK_SH_HZV",
		ChargeSystemId: "AOK_SH_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SkylaRowe_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SkylaRowe,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MandyLeuschke_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MandyLeuschke,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ColbyHowe_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ColbyHowe,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JazlynLeffler_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JazlynLeffler,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_EstrellaReichert_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_EstrellaReichert,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DevonteFay_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DevonteFay,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ChelseyLubowitz_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ChelseyLubowitz,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DorianKulas_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DorianKulas,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ClaudineBernhard_SI_IKK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ClaudineBernhard,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_EudoraBarton_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_EudoraBarton,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_KaleyKrajcik_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_KaleyKrajcik,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ElijahNienow_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ElijahNienow,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_EudoraBarton_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_EudoraBarton,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_KaleyKrajcik_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_KaleyKrajcik,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ElijahNienow_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ElijahNienow,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_EudoraBarton_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_EudoraBarton,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_KaleyKrajcik_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_KaleyKrajcik,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ElijahNienow_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ElijahNienow,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_LudwigKub_BKK_VAG_HE = &data.DoctorParticipation{
		Doctor:         Doctor_LudwigKub,
		ContractId:     "BKK_VAG_HE",
		ChargeSystemId: "BKK_VAG_HE",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MohammedCollier_BKK_VAG_HE = &data.DoctorParticipation{
		Doctor:         Doctor_MohammedCollier,
		ContractId:     "BKK_VAG_HE",
		ChargeSystemId: "BKK_VAG_HE",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_PrinceKing_BKK_VAG_HE = &data.DoctorParticipation{
		Doctor:         Doctor_PrinceKing,
		ContractId:     "BKK_VAG_HE",
		ChargeSystemId: "BKK_VAG_HE",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DarioZemlak_RV_KBS_SN_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DarioZemlak,
		ContractId:     "RV_KBS_SN_HZV",
		ChargeSystemId: "RV_KBS_SN_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_LeonoraFisher_RV_KBS_SN_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_LeonoraFisher,
		ContractId:     "RV_KBS_SN_HZV",
		ChargeSystemId: "RV_KBS_SN_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AileenMueller_RV_KBS_SN_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AileenMueller,
		ContractId:     "RV_KBS_SN_HZV",
		ChargeSystemId: "RV_KBS_SN_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_SkylaRowe_AOK_RP_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_SkylaRowe,
		ContractId:     "AOK_RP_HZV",
		ChargeSystemId: "AOK_RP_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MandyLeuschke_AOK_RP_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MandyLeuschke,
		ContractId:     "AOK_RP_HZV",
		ChargeSystemId: "AOK_RP_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ColbyHowe_AOK_RP_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ColbyHowe,
		ContractId:     "AOK_RP_HZV",
		ChargeSystemId: "AOK_RP_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_RosalindBoehm_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_RosalindBoehm,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_AnaisMurazik_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_AnaisMurazik,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_MartaRosenbaum_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_MartaRosenbaum,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ChelseyLubowitz_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ChelseyLubowitz,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DorianKulas_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DorianKulas,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ClaudineBernhard_BKK_SPECTRUM_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ClaudineBernhard,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ChelseyLubowitz_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ChelseyLubowitz,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DorianKulas_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DorianKulas,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ClaudineBernhard_BKK_GWQ_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ClaudineBernhard,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ChelseyLubowitz_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ChelseyLubowitz,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DorianKulas_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DorianKulas,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_ClaudineBernhard_TK_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_ClaudineBernhard,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_JazlynLeffler_AOK_SL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_JazlynLeffler,
		ContractId:     "AOK_SL_HZV",
		ChargeSystemId: "AOK_SL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_EstrellaReichert_AOK_SL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_EstrellaReichert,
		ContractId:     "AOK_SL_HZV",
		ChargeSystemId: "AOK_SL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	DoctorParticipation_DevonteFay_AOK_SL_HZV = &data.DoctorParticipation{
		Doctor:         Doctor_DevonteFay,
		ContractId:     "AOK_SL_HZV",
		ChargeSystemId: "AOK_SL_HZV",
		Type:           "HausarztzentrierteVersorgung",
		StartDate:      0,
		EndDate:        4123985336000,
		Status:         "Active",
	}

	hpmConfig := bsnr_common.HpmConfig{
		Endpoint: "http://*************:22220",
	}

	MvzFixture_011111100 = &MvzFixture{
		Bsnr: "011111100",
		Doctors: []*data.Doctor{
			Doctor_SharonLang,
			Doctor_JordyVeum,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_SharonLang_BKK_GWQ_HZV,
			DoctorParticipation_JordyVeum_BKK_GWQ_HZV,
			DoctorParticipation_SharonLang_BKK_SPECTRUM_HZV,
			DoctorParticipation_JordyVeum_BKK_SPECTRUM_HZV,
			DoctorParticipation_SharonLang_EK_SH_HZV,
			DoctorParticipation_JordyVeum_EK_SH_HZV,
			DoctorParticipation_SharonLang_TK_HZV,
			DoctorParticipation_JordyVeum_TK_HZV,
			DoctorParticipation_SharonLang_SI_IKK_HZV,
			DoctorParticipation_JordyVeum_SI_IKK_HZV,
			DoctorParticipation_SharonLang_AOK_SH_HZV,
			DoctorParticipation_JordyVeum_AOK_SH_HZV,
		},
	}
	Bsnr_011111100 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "011111100",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_011111100 = MvzFixture_011111100.WithBSNR(Bsnr_011111100)
	_ = MvzFixture_011111100

	MvzFixture_012222200 = &MvzFixture{
		Bsnr: "012222200",
		Doctors: []*data.Doctor{
			Doctor_NoraWalker,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_NoraWalker_BKK_GWQ_HZV,
			DoctorParticipation_NoraWalker_BKK_SPECTRUM_HZV,
			DoctorParticipation_NoraWalker_EK_SH_HZV,
			DoctorParticipation_NoraWalker_TK_HZV,
			DoctorParticipation_NoraWalker_SI_IKK_HZV,
			DoctorParticipation_NoraWalker_AOK_SH_HZV,
		},
	}
	Bsnr_012222200 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "012222200",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_012222200 = MvzFixture_012222200.WithBSNR(Bsnr_012222200)
	_ = MvzFixture_012222200

	MvzFixture_021111100 = &MvzFixture{
		Bsnr: "021111100",
		Doctors: []*data.Doctor{
			Doctor_NatalieNicolas,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_NatalieNicolas_AOK_HH_HZV,
			DoctorParticipation_NatalieNicolas_BKK_GWQ_HZV,
			DoctorParticipation_NatalieNicolas_BKK_SPECTRUM_HZV,
			DoctorParticipation_NatalieNicolas_EK_HH_HZV,
			DoctorParticipation_NatalieNicolas_HKK_HZV_NORD,
			DoctorParticipation_NatalieNicolas_SI_IKK_HZV,
			DoctorParticipation_NatalieNicolas_TK_HZV,
		},
	}
	Bsnr_021111100 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "021111100",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_021111100 = MvzFixture_021111100.WithBSNR(Bsnr_021111100)
	_ = MvzFixture_021111100

	MvzFixture_022222200 = &MvzFixture{
		Bsnr: "022222200",
		Doctors: []*data.Doctor{
			Doctor_EwaldMcGlynn,
			Doctor_MaudieHarris,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_EwaldMcGlynn_AOK_HH_HZV,
			DoctorParticipation_MaudieHarris_AOK_HH_HZV,
			DoctorParticipation_EwaldMcGlynn_BKK_GWQ_HZV,
			DoctorParticipation_MaudieHarris_BKK_GWQ_HZV,
			DoctorParticipation_EwaldMcGlynn_BKK_SPECTRUM_HZV,
			DoctorParticipation_MaudieHarris_BKK_SPECTRUM_HZV,
			DoctorParticipation_EwaldMcGlynn_EK_HH_HZV,
			DoctorParticipation_MaudieHarris_EK_HH_HZV,
			DoctorParticipation_EwaldMcGlynn_HKK_HZV_NORD,
			DoctorParticipation_MaudieHarris_HKK_HZV_NORD,
			DoctorParticipation_EwaldMcGlynn_SI_IKK_HZV,
			DoctorParticipation_MaudieHarris_SI_IKK_HZV,
			DoctorParticipation_EwaldMcGlynn_TK_HZV,
			DoctorParticipation_MaudieHarris_TK_HZV,
		},
	}
	Bsnr_022222200 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "022222200",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_022222200 = MvzFixture_022222200.WithBSNR(Bsnr_022222200)
	_ = MvzFixture_022222200

	MvzFixture_031111100 = &MvzFixture{
		Bsnr: "031111100",
		Doctors: []*data.Doctor{
			Doctor_AlessandraHowell,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_AlessandraHowell_BKK_GWQ_HZV,
			DoctorParticipation_AlessandraHowell_BKK_SPECTRUM_HZV,
			DoctorParticipation_AlessandraHowell_EK_HB_HZV,
			DoctorParticipation_AlessandraHowell_HKK_HZV_NORD,
			DoctorParticipation_AlessandraHowell_TK_HZV,
			DoctorParticipation_AlessandraHowell_SI_IKK_HZV,
		},
	}
	Bsnr_031111100 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "031111100",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
	}
	MvzFixture_031111100 = MvzFixture_031111100.WithBSNR(Bsnr_031111100)
	_ = MvzFixture_031111100

	MvzFixture_032222200 = &MvzFixture{
		Bsnr: "032222200",
		Doctors: []*data.Doctor{
			Doctor_RosaJacobson,
			Doctor_DollyBoyle,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_RosaJacobson_BKK_GWQ_HZV,
			DoctorParticipation_DollyBoyle_BKK_GWQ_HZV,
			DoctorParticipation_RosaJacobson_BKK_SPECTRUM_HZV,
			DoctorParticipation_DollyBoyle_BKK_SPECTRUM_HZV,
			DoctorParticipation_RosaJacobson_EK_HB_HZV,
			DoctorParticipation_DollyBoyle_EK_HB_HZV,
			DoctorParticipation_RosaJacobson_HKK_HZV_NORD,
			DoctorParticipation_DollyBoyle_HKK_HZV_NORD,
			DoctorParticipation_RosaJacobson_TK_HZV,
			DoctorParticipation_DollyBoyle_TK_HZV,
			DoctorParticipation_RosaJacobson_SI_IKK_HZV,
			DoctorParticipation_DollyBoyle_SI_IKK_HZV,
		},
	}
	Bsnr_032222200 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "032222200",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_032222200 = MvzFixture_032222200.WithBSNR(Bsnr_032222200)
	_ = MvzFixture_032222200

	MvzFixture_171111100 = &MvzFixture{
		Bsnr: "171111100",
		Doctors: []*data.Doctor{
			Doctor_RosalindBoehm,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_RosalindBoehm_BKK_GWQ_HZV,
			DoctorParticipation_RosalindBoehm_SI_IKK_HZV,
			DoctorParticipation_RosalindBoehm_TK_HZV,
			DoctorParticipation_RosalindBoehm_BKK_SPECTRUM_HZV,
		},
	}
	Bsnr_171111100 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "171111100",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_171111100 = MvzFixture_171111100.WithBSNR(Bsnr_171111100)
	_ = MvzFixture_171111100

	MvzFixture_172222200 = &MvzFixture{
		Bsnr: "172222200",
		Doctors: []*data.Doctor{
			Doctor_AnaisMurazik,
			Doctor_MartaRosenbaum,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_AnaisMurazik_BKK_GWQ_HZV,
			DoctorParticipation_MartaRosenbaum_BKK_GWQ_HZV,
			DoctorParticipation_AnaisMurazik_SI_IKK_HZV,
			DoctorParticipation_MartaRosenbaum_SI_IKK_HZV,
			DoctorParticipation_AnaisMurazik_TK_HZV,
			DoctorParticipation_MartaRosenbaum_TK_HZV,
			DoctorParticipation_AnaisMurazik_BKK_SPECTRUM_HZV,
			DoctorParticipation_MartaRosenbaum_BKK_SPECTRUM_HZV,
		},
	}
	Bsnr_172222200 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "172222200",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_172222200 = MvzFixture_172222200.WithBSNR(Bsnr_172222200)
	_ = MvzFixture_172222200

	MvzFixture_201111100 = &MvzFixture{
		Bsnr: "201111100",
		Doctors: []*data.Doctor{
			Doctor_JanieKeebler,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_JanieKeebler_AOK_WL_HZV,
			DoctorParticipation_JanieKeebler_BKK_SPECTRUM_HZV,
			DoctorParticipation_JanieKeebler_BKK_WL_HZV,
			DoctorParticipation_JanieKeebler_EK_WL_HZV,
			DoctorParticipation_JanieKeebler_LKK_WL_HZV,
			DoctorParticipation_JanieKeebler_RV_KBS_WL_HZV,
			DoctorParticipation_JanieKeebler_SI_IKK_HZV,
			DoctorParticipation_JanieKeebler_TK_HZV,
		},
	}
	Bsnr_201111100 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "201111100",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_201111100 = MvzFixture_201111100.WithBSNR(Bsnr_201111100)
	_ = MvzFixture_201111100

	MvzFixture_202222200 = &MvzFixture{
		Bsnr: "202222200",
		Doctors: []*data.Doctor{
			Doctor_OdessaAltenwerth,
			Doctor_MckenzieChamplin,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_OdessaAltenwerth_AOK_WL_HZV,
			DoctorParticipation_MckenzieChamplin_AOK_WL_HZV,
			DoctorParticipation_OdessaAltenwerth_BKK_SPECTRUM_HZV,
			DoctorParticipation_MckenzieChamplin_BKK_SPECTRUM_HZV,
			DoctorParticipation_OdessaAltenwerth_BKK_WL_HZV,
			DoctorParticipation_MckenzieChamplin_BKK_WL_HZV,
			DoctorParticipation_OdessaAltenwerth_EK_WL_HZV,
			DoctorParticipation_MckenzieChamplin_EK_WL_HZV,
			DoctorParticipation_OdessaAltenwerth_LKK_WL_HZV,
			DoctorParticipation_MckenzieChamplin_LKK_WL_HZV,
			DoctorParticipation_OdessaAltenwerth_RV_KBS_WL_HZV,
			DoctorParticipation_MckenzieChamplin_RV_KBS_WL_HZV,
			DoctorParticipation_OdessaAltenwerth_SI_IKK_HZV,
			DoctorParticipation_MckenzieChamplin_SI_IKK_HZV,
			DoctorParticipation_OdessaAltenwerth_TK_HZV,
			DoctorParticipation_MckenzieChamplin_TK_HZV,
		},
	}
	Bsnr_202222200 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "202222200",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_202222200 = MvzFixture_202222200.WithBSNR(Bsnr_202222200)
	_ = MvzFixture_202222200

	MvzFixture_381111100 = &MvzFixture{
		Bsnr: "381111100",
		Doctors: []*data.Doctor{
			Doctor_SuzanneGerhold,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_SuzanneGerhold_AOK_NO_HZV,
			DoctorParticipation_SuzanneGerhold_BKK_NO_HZV,
			DoctorParticipation_SuzanneGerhold_BKK_SPECTRUM_HZV,
			DoctorParticipation_SuzanneGerhold_EK_NO_HZV,
			DoctorParticipation_SuzanneGerhold_LKK_NO_HZV,
			DoctorParticipation_SuzanneGerhold_RV_KBS_NO_HZV,
			DoctorParticipation_SuzanneGerhold_SI_IKK_HZV,
			DoctorParticipation_SuzanneGerhold_TK_HZV,
		},
	}
	Bsnr_381111100 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "381111100",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_381111100 = MvzFixture_381111100.WithBSNR(Bsnr_381111100)
	_ = MvzFixture_381111100

	MvzFixture_382222200 = &MvzFixture{
		Bsnr: "382222200",
		Doctors: []*data.Doctor{
			Doctor_AnnaMurray,
			Doctor_JacklynHalvorson,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_AnnaMurray_AOK_NO_HZV,
			DoctorParticipation_JacklynHalvorson_AOK_NO_HZV,
			DoctorParticipation_AnnaMurray_BKK_NO_HZV,
			DoctorParticipation_JacklynHalvorson_BKK_NO_HZV,
			DoctorParticipation_AnnaMurray_BKK_SPECTRUM_HZV,
			DoctorParticipation_JacklynHalvorson_BKK_SPECTRUM_HZV,
			DoctorParticipation_AnnaMurray_EK_NO_HZV,
			DoctorParticipation_JacklynHalvorson_EK_NO_HZV,
			DoctorParticipation_AnnaMurray_LKK_NO_HZV,
			DoctorParticipation_JacklynHalvorson_LKK_NO_HZV,
			DoctorParticipation_AnnaMurray_RV_KBS_NO_HZV,
			DoctorParticipation_JacklynHalvorson_RV_KBS_NO_HZV,
			DoctorParticipation_AnnaMurray_SI_IKK_HZV,
			DoctorParticipation_JacklynHalvorson_SI_IKK_HZV,
			DoctorParticipation_AnnaMurray_TK_HZV,
			DoctorParticipation_JacklynHalvorson_TK_HZV,
		},
	}
	Bsnr_382222200 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "382222200",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_382222200 = MvzFixture_382222200.WithBSNR(Bsnr_382222200)
	_ = MvzFixture_382222200

	MvzFixture_461111100 = &MvzFixture{
		Bsnr: "461111100",
		Doctors: []*data.Doctor{
			Doctor_LudwigKub,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_LudwigKub_AOK_HE_HZV,
			DoctorParticipation_LudwigKub_BKK_GWQ_HZV,
			DoctorParticipation_LudwigKub_BKK_SPECTRUM_HZV,
			DoctorParticipation_LudwigKub_LKK_HZV,
			DoctorParticipation_LudwigKub_SI_IKK_HZV,
			DoctorParticipation_LudwigKub_TK_HZV,
			DoctorParticipation_LudwigKub_EK_HE_HZV,
			DoctorParticipation_LudwigKub_BKK_VAG_HE,
		},
	}
	Bsnr_461111100 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "461111100",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_461111100 = MvzFixture_461111100.WithBSNR(Bsnr_461111100)
	_ = MvzFixture_461111100

	MvzFixture_462222200 = &MvzFixture{
		Bsnr: "462222200",
		Doctors: []*data.Doctor{
			Doctor_MohammedCollier,
			Doctor_PrinceKing,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_MohammedCollier_AOK_HE_HZV,
			DoctorParticipation_PrinceKing_AOK_HE_HZV,
			DoctorParticipation_MohammedCollier_BKK_GWQ_HZV,
			DoctorParticipation_PrinceKing_BKK_GWQ_HZV,
			DoctorParticipation_MohammedCollier_BKK_SPECTRUM_HZV,
			DoctorParticipation_PrinceKing_BKK_SPECTRUM_HZV,
			DoctorParticipation_MohammedCollier_LKK_HZV,
			DoctorParticipation_PrinceKing_LKK_HZV,
			DoctorParticipation_MohammedCollier_SI_IKK_HZV,
			DoctorParticipation_PrinceKing_SI_IKK_HZV,
			DoctorParticipation_MohammedCollier_TK_HZV,
			DoctorParticipation_PrinceKing_TK_HZV,
			DoctorParticipation_MohammedCollier_EK_HE_HZV,
			DoctorParticipation_PrinceKing_EK_HE_HZV,
			DoctorParticipation_MohammedCollier_BKK_VAG_HE,
			DoctorParticipation_PrinceKing_BKK_VAG_HE,
		},
	}
	Bsnr_462222200 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "462222200",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_462222200 = MvzFixture_462222200.WithBSNR(Bsnr_462222200)
	_ = MvzFixture_462222200

	MvzFixture_511111100 = &MvzFixture{
		Bsnr: "511111100",
		Doctors: []*data.Doctor{
			Doctor_SkylaRowe,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_SkylaRowe_BKK_GWQ_HZV,
			DoctorParticipation_SkylaRowe_BKK_SPECTRUM_HZV,
			DoctorParticipation_SkylaRowe_DAK_HZV,
			DoctorParticipation_SkylaRowe_EK_RLP_HZV,
			DoctorParticipation_SkylaRowe_LKK_HZV,
			DoctorParticipation_SkylaRowe_TK_HZV,
			DoctorParticipation_SkylaRowe_SI_IKK_HZV,
			DoctorParticipation_SkylaRowe_AOK_RP_HZV,
		},
	}
	Bsnr_511111100 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "511111100",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_511111100 = MvzFixture_511111100.WithBSNR(Bsnr_511111100)
	_ = MvzFixture_511111100

	MvzFixture_512222200 = &MvzFixture{
		Bsnr: "512222200",
		Doctors: []*data.Doctor{
			Doctor_MandyLeuschke,
			Doctor_ColbyHowe,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_MandyLeuschke_BKK_GWQ_HZV,
			DoctorParticipation_ColbyHowe_BKK_GWQ_HZV,
			DoctorParticipation_MandyLeuschke_BKK_SPECTRUM_HZV,
			DoctorParticipation_ColbyHowe_BKK_SPECTRUM_HZV,
			DoctorParticipation_MandyLeuschke_DAK_HZV,
			DoctorParticipation_ColbyHowe_DAK_HZV,
			DoctorParticipation_MandyLeuschke_EK_RLP_HZV,
			DoctorParticipation_ColbyHowe_EK_RLP_HZV,
			DoctorParticipation_MandyLeuschke_LKK_HZV,
			DoctorParticipation_ColbyHowe_LKK_HZV,
			DoctorParticipation_MandyLeuschke_TK_HZV,
			DoctorParticipation_ColbyHowe_TK_HZV,
			DoctorParticipation_MandyLeuschke_SI_IKK_HZV,
			DoctorParticipation_ColbyHowe_SI_IKK_HZV,
			DoctorParticipation_MandyLeuschke_AOK_RP_HZV,
			DoctorParticipation_ColbyHowe_AOK_RP_HZV,
		},
	}
	Bsnr_512222200 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "512222200",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_512222200 = MvzFixture_512222200.WithBSNR(Bsnr_512222200)
	_ = MvzFixture_512222200

	MvzFixture_521111100 = &MvzFixture{
		Bsnr: "521111100",
		Doctors: []*data.Doctor{
			Doctor_CatharineRobel,
			Doctor_VincenzaStehr,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_CatharineRobel_AOK_BW_IV_P,
			DoctorParticipation_CatharineRobel_AWH_01,
			DoctorParticipation_VincenzaStehr_AWH_01,
			DoctorParticipation_CatharineRobel_BKK_BOSCH_BW,
			DoctorParticipation_CatharineRobel_BKK_BW_HZV,
			DoctorParticipation_CatharineRobel_BKK_VAG_BW,
			DoctorParticipation_CatharineRobel_EK_BW_HZV,
			DoctorParticipation_CatharineRobel_IKK_CL_BW_HZV,
			DoctorParticipation_CatharineRobel_LKK_BW_HZV,
			DoctorParticipation_CatharineRobel_RV_KBS_BW_HZV,
		},
	}
	Bsnr_521111100 := bsnr_repo.BSNR{
		Id:           util.NewPointer(uuid.MustParse("*************-0000-0000-000000000000")),
		Code:         "521111100",
		Name:         "Hospital BSNR",
		FacilityType: bsnr_common.FacilityType_Hospital,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_521111100 = MvzFixture_521111100.WithBSNR(Bsnr_521111100)
	_ = MvzFixture_521111100

	MvzFixture_522222200 = &MvzFixture{
		Bsnr: "522222200",
		Doctors: []*data.Doctor{
			Doctor_KadenCasper,
			Doctor_AleenBoyle,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_KadenCasper_AOK_BW_IV_P,
			DoctorParticipation_AleenBoyle_AOK_BW_IV_P,
			DoctorParticipation_KadenCasper_AWH_01,
			DoctorParticipation_AleenBoyle_AWH_01,
			DoctorParticipation_KadenCasper_BKK_BOSCH_BW,
			DoctorParticipation_AleenBoyle_BKK_BOSCH_BW,
			DoctorParticipation_KadenCasper_BKK_BW_HZV,
			DoctorParticipation_AleenBoyle_BKK_BW_HZV,
			DoctorParticipation_KadenCasper_BKK_VAG_BW,
			DoctorParticipation_AleenBoyle_BKK_VAG_BW,
			DoctorParticipation_KadenCasper_EK_BW_HZV,
			DoctorParticipation_AleenBoyle_EK_BW_HZV,
			DoctorParticipation_KadenCasper_IKK_CL_BW_HZV,
			DoctorParticipation_AleenBoyle_IKK_CL_BW_HZV,
			DoctorParticipation_KadenCasper_LKK_BW_HZV,
			DoctorParticipation_AleenBoyle_LKK_BW_HZV,
			DoctorParticipation_KadenCasper_RV_KBS_BW_HZV,
			DoctorParticipation_AleenBoyle_RV_KBS_BW_HZV,
		},
	}
	Bsnr_522222200 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "522222200",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_522222200 = MvzFixture_522222200.WithBSNR(Bsnr_522222200)
	_ = MvzFixture_522222200

	MvzFixture_528888800 = &MvzFixture{
		Bsnr: "528888800",
		Doctors: []*data.Doctor{
			Doctor_RemingtonGislason,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_RemingtonGislason_AWH_01,
		},
	}
	Bsnr_528888800 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "528888800",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_528888800 = MvzFixture_528888800.WithBSNR(Bsnr_528888800)
	_ = MvzFixture_528888800

	MvzFixture_616123400 = &MvzFixture{
		Bsnr: "616123400",
		Doctors: []*data.Doctor{
			Doctor_CadeLarkin,
			Doctor_SadieGusikowski_616123400,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_CadeLarkin_AOK_FA_GASTRO_BW,
			DoctorParticipation_CadeLarkin_AOK_FA_KARDIO_BW,
			DoctorParticipation_CadeLarkin_AOK_FA_NEPHRO_BW,
			DoctorParticipation_CadeLarkin_AOK_FA_NPPP_BW,
			DoctorParticipation_CadeLarkin_AOK_FA_OC_BW,
			DoctorParticipation_CadeLarkin_AOK_FA_PNEUMO_BW,
			DoctorParticipation_CadeLarkin_AOK_FA_URO_BW,
			DoctorParticipation_CadeLarkin_BKK_BOSCH_FaV,
			DoctorParticipation_CadeLarkin_BKK_VAG_FaV,
			DoctorParticipation_SadieGusikowski_616123400_BKK_VAG_FaV,
			DoctorParticipation_CadeLarkin_MEDI_FA_PT_BW,
			DoctorParticipation_CadeLarkin_EK_FA_DIA_BW,
		},
	}
	Bsnr_616123400 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "616123400",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_616123400 = MvzFixture_616123400.WithBSNR(Bsnr_616123400)
	_ = MvzFixture_616123400

	MvzFixture_616321400 = &MvzFixture{
		Bsnr: "616321400",
		Doctors: []*data.Doctor{
			Doctor_RomaWest,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_RomaWest_AOK_FA_GASTRO_BW,
			DoctorParticipation_RomaWest_AOK_FA_KARDIO_BW,
			DoctorParticipation_RomaWest_AOK_FA_NEPHRO_BW,
			DoctorParticipation_RomaWest_AOK_FA_NPPP_BW,
			DoctorParticipation_RomaWest_AOK_FA_OC_BW,
			DoctorParticipation_RomaWest_AOK_FA_PNEUMO_BW,
			DoctorParticipation_RomaWest_AOK_FA_URO_BW,
		},
	}
	Bsnr_616321400 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "616321400",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_616321400 = MvzFixture_616321400.WithBSNR(Bsnr_616321400)
	_ = MvzFixture_616321400

	MvzFixture_711111100 = &MvzFixture{
		Bsnr: "711111100",
		Doctors: []*data.Doctor{
			Doctor_BulahHaley,
			Doctor_TyreseKoepp,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_BulahHaley_AOK_BY_HZV_S15,
			DoctorParticipation_TyreseKoepp_AOK_BY_HZV_S15,
			DoctorParticipation_BulahHaley_BKK_BY_HZV,
			DoctorParticipation_TyreseKoepp_BKK_BY_HZV,
			DoctorParticipation_BulahHaley_EK_BY_HZV_S12,
			DoctorParticipation_TyreseKoepp_EK_BY_HZV_S12,
			DoctorParticipation_BulahHaley_LKK_BY_HZV,
			DoctorParticipation_TyreseKoepp_LKK_BY_HZV,
			DoctorParticipation_BulahHaley_SI_IKK_HZV,
			DoctorParticipation_TyreseKoepp_SI_IKK_HZV,
			DoctorParticipation_BulahHaley_TK_HZV,
			DoctorParticipation_TyreseKoepp_TK_HZV,
		},
	}
	Bsnr_711111100 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "711111100",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_711111100 = MvzFixture_711111100.WithBSNR(Bsnr_711111100)
	_ = MvzFixture_711111100

	MvzFixture_713333300 = &MvzFixture{
		Bsnr: "713333300",
		Doctors: []*data.Doctor{
			Doctor_GraceBogan,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_GraceBogan_AOK_BY_HZV_S15,
			DoctorParticipation_GraceBogan_BKK_BY_HZV,
			DoctorParticipation_GraceBogan_EK_BY_HZV_S12,
			DoctorParticipation_GraceBogan_LKK_BY_HZV,
			DoctorParticipation_GraceBogan_SI_IKK_HZV,
			DoctorParticipation_GraceBogan_TK_HZV,
		},
	}
	Bsnr_713333300 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "713333300",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
	}
	MvzFixture_713333300 = MvzFixture_713333300.WithBSNR(Bsnr_713333300)
	_ = MvzFixture_713333300

	MvzFixture_721111100 = &MvzFixture{
		Bsnr: "721111100",
		Doctors: []*data.Doctor{
			Doctor_BeulahHomenick,
			Doctor_ColtMurphy,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_BeulahHomenick_AOK_IKK_BLN_HZV,
			DoctorParticipation_ColtMurphy_AOK_IKK_BLN_HZV,
			DoctorParticipation_BeulahHomenick_BKK_GWQ_HZV,
			DoctorParticipation_ColtMurphy_BKK_GWQ_HZV,
			DoctorParticipation_BeulahHomenick_BKK_SPECTRUM_HZV,
			DoctorParticipation_ColtMurphy_BKK_SPECTRUM_HZV,
			DoctorParticipation_BeulahHomenick_EK_BLN_HZV,
			DoctorParticipation_ColtMurphy_EK_BLN_HZV,
			DoctorParticipation_BeulahHomenick_SI_IKK_HZV,
			DoctorParticipation_ColtMurphy_SI_IKK_HZV,
			DoctorParticipation_BeulahHomenick_TK_HZV,
			DoctorParticipation_ColtMurphy_TK_HZV,
		},
	}
	Bsnr_721111100 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "721111100",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_721111100 = MvzFixture_721111100.WithBSNR(Bsnr_721111100)
	_ = MvzFixture_721111100

	MvzFixture_723333300 = &MvzFixture{
		Bsnr: "723333300",
		Doctors: []*data.Doctor{
			Doctor_BerneiceCassin,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_BerneiceCassin_AOK_IKK_BLN_HZV,
			DoctorParticipation_BerneiceCassin_BKK_GWQ_HZV,
			DoctorParticipation_BerneiceCassin_BKK_SPECTRUM_HZV,
			DoctorParticipation_BerneiceCassin_EK_BLN_HZV,
			DoctorParticipation_BerneiceCassin_SI_IKK_HZV,
			DoctorParticipation_BerneiceCassin_TK_HZV,
		},
	}
	Bsnr_723333300 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "723333300",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_723333300 = MvzFixture_723333300.WithBSNR(Bsnr_723333300)
	_ = MvzFixture_723333300

	MvzFixture_731111100 = &MvzFixture{
		Bsnr: "731111100",
		Doctors: []*data.Doctor{
			Doctor_JazlynLeffler,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_JazlynLeffler_BKK_GWQ_HZV,
			DoctorParticipation_JazlynLeffler_BKK_SPECTRUM_HZV,
			DoctorParticipation_JazlynLeffler_EK_SL_HZV,
			DoctorParticipation_JazlynLeffler_TK_HZV,
			DoctorParticipation_JazlynLeffler_SI_IKK_HZV,
			DoctorParticipation_JazlynLeffler_AOK_SL_HZV,
		},
	}
	Bsnr_731111100 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "731111100",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_731111100 = MvzFixture_731111100.WithBSNR(Bsnr_731111100)
	_ = MvzFixture_731111100

	MvzFixture_732222200 = &MvzFixture{
		Bsnr: "732222200",
		Doctors: []*data.Doctor{
			Doctor_EstrellaReichert,
			Doctor_DevonteFay,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_EstrellaReichert_BKK_GWQ_HZV,
			DoctorParticipation_DevonteFay_BKK_GWQ_HZV,
			DoctorParticipation_EstrellaReichert_BKK_SPECTRUM_HZV,
			DoctorParticipation_DevonteFay_BKK_SPECTRUM_HZV,
			DoctorParticipation_EstrellaReichert_EK_SL_HZV,
			DoctorParticipation_DevonteFay_EK_SL_HZV,
			DoctorParticipation_EstrellaReichert_TK_HZV,
			DoctorParticipation_DevonteFay_TK_HZV,
			DoctorParticipation_EstrellaReichert_SI_IKK_HZV,
			DoctorParticipation_DevonteFay_SI_IKK_HZV,
			DoctorParticipation_EstrellaReichert_AOK_SL_HZV,
			DoctorParticipation_DevonteFay_AOK_SL_HZV,
		},
	}
	Bsnr_732222200 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "732222200",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_732222200 = MvzFixture_732222200.WithBSNR(Bsnr_732222200)
	_ = MvzFixture_732222200

	MvzFixture_831111100 = &MvzFixture{
		Bsnr: "831111100",
		Doctors: []*data.Doctor{
			Doctor_ChelseyLubowitz,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_ChelseyLubowitz_SI_IKK_HZV,
			DoctorParticipation_ChelseyLubowitz_BKK_SPECTRUM_HZV,
			DoctorParticipation_ChelseyLubowitz_BKK_GWQ_HZV,
			DoctorParticipation_ChelseyLubowitz_TK_HZV,
		},
	}
	Bsnr_831111100 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "831111100",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_831111100 = MvzFixture_831111100.WithBSNR(Bsnr_831111100)
	_ = MvzFixture_831111100

	MvzFixture_832222200 = &MvzFixture{
		Bsnr: "832222200",
		Doctors: []*data.Doctor{
			Doctor_DorianKulas,
			Doctor_ClaudineBernhard,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_DorianKulas_SI_IKK_HZV,
			DoctorParticipation_ClaudineBernhard_SI_IKK_HZV,
			DoctorParticipation_DorianKulas_BKK_SPECTRUM_HZV,
			DoctorParticipation_ClaudineBernhard_BKK_SPECTRUM_HZV,
			DoctorParticipation_DorianKulas_BKK_GWQ_HZV,
			DoctorParticipation_ClaudineBernhard_BKK_GWQ_HZV,
			DoctorParticipation_DorianKulas_TK_HZV,
			DoctorParticipation_ClaudineBernhard_TK_HZV,
		},
	}
	Bsnr_832222200 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "832222200",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_832222200 = MvzFixture_832222200.WithBSNR(Bsnr_832222200)
	_ = MvzFixture_832222200

	MvzFixture_931111100 = &MvzFixture{
		Bsnr: "931111100",
		Doctors: []*data.Doctor{
			Doctor_EudoraBarton,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_EudoraBarton_SI_IKK_HZV,
			DoctorParticipation_EudoraBarton_TK_HZV,
			DoctorParticipation_EudoraBarton_BKK_GWQ_HZV,
			DoctorParticipation_EudoraBarton_BKK_SPECTRUM_HZV,
		},
	}
	Bsnr_931111100 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "931111100",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_931111100 = MvzFixture_931111100.WithBSNR(Bsnr_931111100)
	_ = MvzFixture_931111100

	MvzFixture_932222200 = &MvzFixture{
		Bsnr: "932222200",
		Doctors: []*data.Doctor{
			Doctor_KaleyKrajcik,
			Doctor_ElijahNienow,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_KaleyKrajcik_SI_IKK_HZV,
			DoctorParticipation_ElijahNienow_SI_IKK_HZV,
			DoctorParticipation_KaleyKrajcik_TK_HZV,
			DoctorParticipation_ElijahNienow_TK_HZV,
			DoctorParticipation_KaleyKrajcik_BKK_GWQ_HZV,
			DoctorParticipation_ElijahNienow_BKK_GWQ_HZV,
			DoctorParticipation_KaleyKrajcik_BKK_SPECTRUM_HZV,
			DoctorParticipation_ElijahNienow_BKK_SPECTRUM_HZV,
		},
	}
	Bsnr_932222200 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "932222200",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_932222200 = MvzFixture_932222200.WithBSNR(Bsnr_932222200)
	_ = MvzFixture_932222200

	MvzFixture_981111100 = &MvzFixture{
		Bsnr: "981111100",
		Doctors: []*data.Doctor{
			Doctor_DarioZemlak,
			Doctor_LeonoraFisher,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_DarioZemlak_AOK_PLUS_HZV,
			DoctorParticipation_LeonoraFisher_AOK_PLUS_HZV,
			DoctorParticipation_DarioZemlak_BKK_GWQ_HZV,
			DoctorParticipation_LeonoraFisher_BKK_GWQ_HZV,
			DoctorParticipation_DarioZemlak_BKK_SPECTRUM_HZV,
			DoctorParticipation_LeonoraFisher_BKK_SPECTRUM_HZV,
			DoctorParticipation_DarioZemlak_EK_SN_HZV,
			DoctorParticipation_LeonoraFisher_EK_SN_HZV,
			DoctorParticipation_DarioZemlak_SI_IKK_HZV,
			DoctorParticipation_LeonoraFisher_SI_IKK_HZV,
			DoctorParticipation_DarioZemlak_TK_HZV,
			DoctorParticipation_LeonoraFisher_TK_HZV,
			DoctorParticipation_DarioZemlak_RV_KBS_SN_HZV,
			DoctorParticipation_LeonoraFisher_RV_KBS_SN_HZV,
		},
	}
	Bsnr_981111100 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "981111100",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_981111100 = MvzFixture_981111100.WithBSNR(Bsnr_981111100)
	_ = MvzFixture_981111100

	MvzFixture_982222200 = &MvzFixture{
		Bsnr: "982222200",
		Doctors: []*data.Doctor{
			Doctor_AileenMueller,
		},
		DoctorParticipations: []*data.DoctorParticipation{
			DoctorParticipation_AileenMueller_AOK_PLUS_HZV,
			DoctorParticipation_AileenMueller_BKK_GWQ_HZV,
			DoctorParticipation_AileenMueller_BKK_SPECTRUM_HZV,
			DoctorParticipation_AileenMueller_EK_SN_HZV,
			DoctorParticipation_AileenMueller_SI_IKK_HZV,
			DoctorParticipation_AileenMueller_TK_HZV,
			DoctorParticipation_AileenMueller_RV_KBS_SN_HZV,
		},
	}
	Bsnr_982222200 := bsnr_repo.BSNR{
		Id:           util.NewUUID(),
		Code:         "982222200",
		Name:         "Practice BSNR",
		FacilityType: bsnr_common.FacilityType_Practice,
		HpmConfig:    hpmConfig,
	}
	MvzFixture_982222200 = MvzFixture_982222200.WithBSNR(Bsnr_982222200)
	_ = MvzFixture_982222200

	gofakeit.Seed(************)
	Patient_FabianGroßburg = data.FakePatient()
	Patient_FabianGroßburg.Profile.FirstName = "Fabian"
	Patient_FabianGroßburg.Profile.PatientInfo.PersonalInfo.FirstName = "Fabian"
	Patient_FabianGroßburg.Profile.LastName = "Großburg"
	Patient_FabianGroßburg.Profile.PatientInfo.PersonalInfo.LastName = "Großburg"
	Patient_FabianGroßburg.Profile.DateOfBirth = ************
	Patient_FabianGroßburg.Profile.PatientInfo.PersonalInfo.DOB = ************
	Patient_FabianGroßburg.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(************)
	Patient_FabianGroßburg.Profile.PatientInfo.PatientNumber = 0 + 1
	Patient_FabianGroßburg.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_FabianGroßburg.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("A000200018")
	Patient_FabianGroßburg.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108018110
	Patient_FabianGroßburg.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_FabianGroßburg_AOK_FA_GASTRO_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_FabianGroßburg,
		ContractId:     "AOK_FA_GASTRO_BW",
		ChargeSystemId: "AOK_FA_GASTRO_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1277942400000),
	}
	_ = PatientParticipation_FabianGroßburg_AOK_FA_GASTRO_BW
	PatientParticipation_FabianGroßburg_AOK_FA_KARDIO_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_FabianGroßburg,
		ContractId:     "AOK_FA_KARDIO_BW",
		ChargeSystemId: "AOK_FA_KARDIO_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1277942400000),
	}
	_ = PatientParticipation_FabianGroßburg_AOK_FA_KARDIO_BW
	PatientParticipation_FabianGroßburg_AOK_FA_NEPHRO_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_FabianGroßburg,
		ContractId:     "AOK_FA_NEPHRO_BW",
		ChargeSystemId: "AOK_FA_NEPHRO_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1277942400000),
	}
	_ = PatientParticipation_FabianGroßburg_AOK_FA_NEPHRO_BW
	PatientParticipation_FabianGroßburg_AOK_FA_NPPP_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_FabianGroßburg,
		ContractId:     "AOK_FA_NPPP_BW",
		ChargeSystemId: "AOK_FA_NPPP_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1277942400000),
	}
	_ = PatientParticipation_FabianGroßburg_AOK_FA_NPPP_BW
	PatientParticipation_FabianGroßburg_AOK_FA_OC_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_FabianGroßburg,
		ContractId:     "AOK_FA_OC_BW",
		ChargeSystemId: "AOK_FA_OC_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1277942400000),
	}
	_ = PatientParticipation_FabianGroßburg_AOK_FA_OC_BW
	PatientParticipation_FabianGroßburg_AOK_FA_PNEUMO_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_FabianGroßburg,
		ContractId:     "AOK_FA_PNEUMO_BW",
		ChargeSystemId: "AOK_FA_PNEUMO_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1277942400000),
	}
	_ = PatientParticipation_FabianGroßburg_AOK_FA_PNEUMO_BW
	PatientParticipation_FabianGroßburg_AOK_FA_URO_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_FabianGroßburg,
		ContractId:     "AOK_FA_URO_BW",
		ChargeSystemId: "AOK_FA_URO_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1277942400000),
	}
	_ = PatientParticipation_FabianGroßburg_AOK_FA_URO_BW
	PatientParticipation_FabianGroßburg_AWH_01 = &PatientParticipationWithoutDoctor{
		Patient:        Patient_FabianGroßburg,
		ContractId:     "AWH_01",
		ChargeSystemId: "AWH_01",
		IkNumber:       108018007,
		StartDate:      toInt64Pointer(1230768000000),
	}
	_ = PatientParticipation_FabianGroßburg_AWH_01

	gofakeit.Seed(650000100016)
	Patient_MariaMühlenberg = data.FakePatient()
	Patient_MariaMühlenberg.Profile.FirstName = "Maria"
	Patient_MariaMühlenberg.Profile.PatientInfo.PersonalInfo.FirstName = "Maria"
	Patient_MariaMühlenberg.Profile.LastName = "Mühlenberg"
	Patient_MariaMühlenberg.Profile.PatientInfo.PersonalInfo.LastName = "Mühlenberg"
	Patient_MariaMühlenberg.Profile.DateOfBirth = 928972800000
	Patient_MariaMühlenberg.Profile.PatientInfo.PersonalInfo.DOB = 928972800000
	Patient_MariaMühlenberg.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(928972800000)
	Patient_MariaMühlenberg.Profile.PatientInfo.PatientNumber = 1 + 1
	Patient_MariaMühlenberg.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_MariaMühlenberg.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("A000100016")
	Patient_MariaMühlenberg.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108018110
	Patient_MariaMühlenberg.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_MariaMühlenberg_AOK_FA_GASTRO_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MariaMühlenberg,
		ContractId:     "AOK_FA_GASTRO_BW",
		ChargeSystemId: "AOK_FA_GASTRO_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1333238400000),
	}
	_ = PatientParticipation_MariaMühlenberg_AOK_FA_GASTRO_BW
	PatientParticipation_MariaMühlenberg_AOK_FA_KARDIO_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MariaMühlenberg,
		ContractId:     "AOK_FA_KARDIO_BW",
		ChargeSystemId: "AOK_FA_KARDIO_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1333238400000),
	}
	_ = PatientParticipation_MariaMühlenberg_AOK_FA_KARDIO_BW
	PatientParticipation_MariaMühlenberg_AOK_FA_NEPHRO_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MariaMühlenberg,
		ContractId:     "AOK_FA_NEPHRO_BW",
		ChargeSystemId: "AOK_FA_NEPHRO_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1333238400000),
	}
	_ = PatientParticipation_MariaMühlenberg_AOK_FA_NEPHRO_BW
	PatientParticipation_MariaMühlenberg_AOK_FA_NPPP_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MariaMühlenberg,
		ContractId:     "AOK_FA_NPPP_BW",
		ChargeSystemId: "AOK_FA_NPPP_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1333238400000),
	}
	_ = PatientParticipation_MariaMühlenberg_AOK_FA_NPPP_BW
	PatientParticipation_MariaMühlenberg_AOK_FA_OC_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MariaMühlenberg,
		ContractId:     "AOK_FA_OC_BW",
		ChargeSystemId: "AOK_FA_OC_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1333238400000),
	}
	_ = PatientParticipation_MariaMühlenberg_AOK_FA_OC_BW
	PatientParticipation_MariaMühlenberg_AOK_FA_PNEUMO_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MariaMühlenberg,
		ContractId:     "AOK_FA_PNEUMO_BW",
		ChargeSystemId: "AOK_FA_PNEUMO_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1333238400000),
	}
	_ = PatientParticipation_MariaMühlenberg_AOK_FA_PNEUMO_BW
	PatientParticipation_MariaMühlenberg_AOK_FA_URO_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MariaMühlenberg,
		ContractId:     "AOK_FA_URO_BW",
		ChargeSystemId: "AOK_FA_URO_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1333238400000),
	}
	_ = PatientParticipation_MariaMühlenberg_AOK_FA_URO_BW
	PatientParticipation_MariaMühlenberg_AWH_01 = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MariaMühlenberg,
		ContractId:     "AWH_01",
		ChargeSystemId: "AWH_01",
		IkNumber:       108018007,
		StartDate:      toInt64Pointer(1230768000000),
	}
	_ = PatientParticipation_MariaMühlenberg_AWH_01

	gofakeit.Seed(720768317540)
	Patient_PeterSchlößer = data.FakePatient()
	Patient_PeterSchlößer.Profile.FirstName = "Peter"
	Patient_PeterSchlößer.Profile.PatientInfo.PersonalInfo.FirstName = "Peter"
	Patient_PeterSchlößer.Profile.LastName = "Schlößer"
	Patient_PeterSchlößer.Profile.PatientInfo.PersonalInfo.LastName = "Schlößer"
	Patient_PeterSchlößer.Profile.DateOfBirth = -103075200000
	Patient_PeterSchlößer.Profile.PatientInfo.PersonalInfo.DOB = -103075200000
	Patient_PeterSchlößer.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-103075200000)
	Patient_PeterSchlößer.Profile.PatientInfo.PatientNumber = 2 + 1
	Patient_PeterSchlößer.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_PeterSchlößer.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("H768317540")
	Patient_PeterSchlößer.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108018110
	Patient_PeterSchlößer.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_PeterSchlößer_AOK_FA_GASTRO_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_PeterSchlößer,
		ContractId:     "AOK_FA_GASTRO_BW",
		ChargeSystemId: "AOK_FA_GASTRO_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1230768000000),
	}
	_ = PatientParticipation_PeterSchlößer_AOK_FA_GASTRO_BW
	PatientParticipation_PeterSchlößer_AOK_FA_KARDIO_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_PeterSchlößer,
		ContractId:     "AOK_FA_KARDIO_BW",
		ChargeSystemId: "AOK_FA_KARDIO_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1230768000000),
	}
	_ = PatientParticipation_PeterSchlößer_AOK_FA_KARDIO_BW
	PatientParticipation_PeterSchlößer_AOK_FA_NEPHRO_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_PeterSchlößer,
		ContractId:     "AOK_FA_NEPHRO_BW",
		ChargeSystemId: "AOK_FA_NEPHRO_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1230768000000),
	}
	_ = PatientParticipation_PeterSchlößer_AOK_FA_NEPHRO_BW
	PatientParticipation_PeterSchlößer_AOK_FA_NPPP_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_PeterSchlößer,
		ContractId:     "AOK_FA_NPPP_BW",
		ChargeSystemId: "AOK_FA_NPPP_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1230768000000),
	}
	_ = PatientParticipation_PeterSchlößer_AOK_FA_NPPP_BW
	PatientParticipation_PeterSchlößer_AOK_FA_OC_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_PeterSchlößer,
		ContractId:     "AOK_FA_OC_BW",
		ChargeSystemId: "AOK_FA_OC_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1230768000000),
	}
	_ = PatientParticipation_PeterSchlößer_AOK_FA_OC_BW
	PatientParticipation_PeterSchlößer_AOK_FA_PNEUMO_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_PeterSchlößer,
		ContractId:     "AOK_FA_PNEUMO_BW",
		ChargeSystemId: "AOK_FA_PNEUMO_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1230768000000),
	}
	_ = PatientParticipation_PeterSchlößer_AOK_FA_PNEUMO_BW
	PatientParticipation_PeterSchlößer_AOK_FA_URO_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_PeterSchlößer,
		ContractId:     "AOK_FA_URO_BW",
		ChargeSystemId: "AOK_FA_URO_BW",
		IkNumber:       108018110,
		StartDate:      toInt64Pointer(1230768000000),
	}
	_ = PatientParticipation_PeterSchlößer_AOK_FA_URO_BW
	PatientParticipation_PeterSchlößer_AWH_01 = &PatientParticipationWithoutDoctor{
		Patient:        Patient_PeterSchlößer,
		ContractId:     "AWH_01",
		ChargeSystemId: "AWH_01",
		IkNumber:       108018007,
		StartDate:      toInt64Pointer(1230768000000),
	}
	_ = PatientParticipation_PeterSchlößer_AWH_01

	gofakeit.Seed(790780870733)
	Patient_MagdaleneHaßlöcher = data.FakePatient()
	Patient_MagdaleneHaßlöcher.Profile.FirstName = "Magdalene"
	Patient_MagdaleneHaßlöcher.Profile.PatientInfo.PersonalInfo.FirstName = "Magdalene"
	Patient_MagdaleneHaßlöcher.Profile.LastName = "Haßlöcher"
	Patient_MagdaleneHaßlöcher.Profile.PatientInfo.PersonalInfo.LastName = "Haßlöcher"
	Patient_MagdaleneHaßlöcher.Profile.DateOfBirth = -1201478400000
	Patient_MagdaleneHaßlöcher.Profile.PatientInfo.PersonalInfo.DOB = -1201478400000
	Patient_MagdaleneHaßlöcher.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-1201478400000)
	Patient_MagdaleneHaßlöcher.Profile.PatientInfo.PatientNumber = 3 + 1
	Patient_MagdaleneHaßlöcher.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_MagdaleneHaßlöcher.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("O780870733")
	Patient_MagdaleneHaßlöcher.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108036123
	Patient_MagdaleneHaßlöcher.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_MagdaleneHaßlöcher_BKK_BOSCH_FaV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MagdaleneHaßlöcher,
		ContractId:     "BKK_BOSCH_FaV",
		ChargeSystemId: "BKK_BOSCH_FaV",
		IkNumber:       108036123,
		StartDate:      toInt64Pointer(1333238400000),
	}
	_ = PatientParticipation_MagdaleneHaßlöcher_BKK_BOSCH_FaV
	PatientParticipation_MagdaleneHaßlöcher_BKK_BOSCH_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MagdaleneHaßlöcher,
		ContractId:     "BKK_BOSCH_BW",
		ChargeSystemId: "BKK_BOSCH_BW",
		IkNumber:       108036123,
		StartDate:      toInt64Pointer(1254355200000),
	}
	_ = PatientParticipation_MagdaleneHaßlöcher_BKK_BOSCH_BW

	gofakeit.Seed(660113113225)
	Patient_TomBöttcher = data.FakePatient()
	Patient_TomBöttcher.Profile.FirstName = "Tom"
	Patient_TomBöttcher.Profile.PatientInfo.PersonalInfo.FirstName = "Tom"
	Patient_TomBöttcher.Profile.LastName = "Böttcher"
	Patient_TomBöttcher.Profile.PatientInfo.PersonalInfo.LastName = "Böttcher"
	Patient_TomBöttcher.Profile.DateOfBirth = 309744000000
	Patient_TomBöttcher.Profile.PatientInfo.PersonalInfo.DOB = 309744000000
	Patient_TomBöttcher.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(309744000000)
	Patient_TomBöttcher.Profile.PatientInfo.PatientNumber = 4 + 1
	Patient_TomBöttcher.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_TomBöttcher.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("B113113225")
	Patient_TomBöttcher.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108035612
	Patient_TomBöttcher.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_TomBöttcher_BKK_VAG_FaV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_TomBöttcher,
		ContractId:     "BKK_VAG_FaV",
		ChargeSystemId: "BKK_VAG_FaV",
		IkNumber:       108035612,
		StartDate:      toInt64Pointer(1467331200000),
	}
	_ = PatientParticipation_TomBöttcher_BKK_VAG_FaV
	PatientParticipation_TomBöttcher_MEDI_FA_PT_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_TomBöttcher,
		ContractId:     "MEDI_FA_PT_BW",
		ChargeSystemId: "MEDI_FA_PT_BW",
		IkNumber:       108035612,
		StartDate:      toInt64Pointer(1554076800000),
	}
	_ = PatientParticipation_TomBöttcher_MEDI_FA_PT_BW
	PatientParticipation_TomBöttcher_BKK_VAG_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_TomBöttcher,
		ContractId:     "BKK_VAG_BW",
		ChargeSystemId: "BKK_VAG_BW",
		IkNumber:       108035612,
		StartDate:      toInt64Pointer(1254355200000),
	}
	_ = PatientParticipation_TomBöttcher_BKK_VAG_BW

	gofakeit.Seed(800066276262)
	Patient_JulianBergmann = data.FakePatient()
	Patient_JulianBergmann.Profile.FirstName = "Julian"
	Patient_JulianBergmann.Profile.PatientInfo.PersonalInfo.FirstName = "Julian"
	Patient_JulianBergmann.Profile.LastName = "Bergmann"
	Patient_JulianBergmann.Profile.PatientInfo.PersonalInfo.LastName = "Bergmann"
	Patient_JulianBergmann.Profile.DateOfBirth = 693100800000
	Patient_JulianBergmann.Profile.PatientInfo.PersonalInfo.DOB = 693100800000
	Patient_JulianBergmann.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(693100800000)
	Patient_JulianBergmann.Profile.PatientInfo.PatientNumber = 5 + 1
	Patient_JulianBergmann.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_JulianBergmann.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("P066276262")
	Patient_JulianBergmann.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101560000
	Patient_JulianBergmann.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_JulianBergmann_MEDI_FA_PT_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_JulianBergmann,
		ContractId:     "MEDI_FA_PT_BW",
		ChargeSystemId: "MEDI_FA_PT_BW",
		IkNumber:       101560000,
		StartDate:      toInt64Pointer(1554076800000),
	}
	_ = PatientParticipation_JulianBergmann_MEDI_FA_PT_BW
	PatientParticipation_JulianBergmann_EK_FA_DIA_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_JulianBergmann,
		ContractId:     "EK_FA_DIA_BW",
		ChargeSystemId: "EK_FA_DIA_BW",
		IkNumber:       101560000,
		StartDate:      toInt64Pointer(1554076800000),
	}
	_ = PatientParticipation_JulianBergmann_EK_FA_DIA_BW
	PatientParticipation_JulianBergmann_DAK_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_JulianBergmann,
		ContractId:     "DAK_HZV",
		ChargeSystemId: "DAK_HZV",
		IkNumber:       101560000,
		StartDate:      toInt64Pointer(1412121600000),
	}
	_ = PatientParticipation_JulianBergmann_DAK_HZV

	gofakeit.Seed(870716745383)
	Patient_PascalFaber = data.FakePatient()
	Patient_PascalFaber.Profile.FirstName = "Pascal"
	Patient_PascalFaber.Profile.PatientInfo.PersonalInfo.FirstName = "Pascal"
	Patient_PascalFaber.Profile.LastName = "Faber"
	Patient_PascalFaber.Profile.PatientInfo.PersonalInfo.LastName = "Faber"
	Patient_PascalFaber.Profile.DateOfBirth = 507686400000
	Patient_PascalFaber.Profile.PatientInfo.PersonalInfo.DOB = 507686400000
	Patient_PascalFaber.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(507686400000)
	Patient_PascalFaber.Profile.PatientInfo.PatientNumber = 6 + 1
	Patient_PascalFaber.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_PascalFaber.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("W716745383")
	Patient_PascalFaber.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101575519
	Patient_PascalFaber.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_PascalFaber_MEDI_FA_PT_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_PascalFaber,
		ContractId:     "MEDI_FA_PT_BW",
		ChargeSystemId: "MEDI_FA_PT_BW",
		IkNumber:       101575519,
		StartDate:      toInt64Pointer(1554076800000),
	}
	_ = PatientParticipation_PascalFaber_MEDI_FA_PT_BW
	PatientParticipation_PascalFaber_TK_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_PascalFaber,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		IkNumber:       101575519,
		StartDate:      toInt64Pointer(1333238400000),
	}
	_ = PatientParticipation_PascalFaber_TK_HZV

	gofakeit.Seed(660237323080)
	Patient_MaritaFangl = data.FakePatient()
	Patient_MaritaFangl.Profile.FirstName = "Marita"
	Patient_MaritaFangl.Profile.PatientInfo.PersonalInfo.FirstName = "Marita"
	Patient_MaritaFangl.Profile.LastName = "Fangl"
	Patient_MaritaFangl.Profile.PatientInfo.PersonalInfo.LastName = "Fangl"
	Patient_MaritaFangl.Profile.DateOfBirth = -145929600000
	Patient_MaritaFangl.Profile.PatientInfo.PersonalInfo.DOB = -145929600000
	Patient_MaritaFangl.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-145929600000)
	Patient_MaritaFangl.Profile.PatientInfo.PatientNumber = 7 + 1
	Patient_MaritaFangl.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_MaritaFangl.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("B237323080")
	Patient_MaritaFangl.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108310400
	Patient_MaritaFangl.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_MaritaFangl_AOK_BY_HZV_S15 = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MaritaFangl,
		ContractId:     "AOK_BY_HZV_S15",
		ChargeSystemId: "AOK_BY_HZV_S15",
		IkNumber:       108310400,
		StartDate:      toInt64Pointer(1427846400000),
	}
	_ = PatientParticipation_MaritaFangl_AOK_BY_HZV_S15

	gofakeit.Seed(810455600757)
	Patient_ThomasDonaufelder = data.FakePatient()
	Patient_ThomasDonaufelder.Profile.FirstName = "Thomas"
	Patient_ThomasDonaufelder.Profile.PatientInfo.PersonalInfo.FirstName = "Thomas"
	Patient_ThomasDonaufelder.Profile.LastName = "Donaufelder"
	Patient_ThomasDonaufelder.Profile.PatientInfo.PersonalInfo.LastName = "Donaufelder"
	Patient_ThomasDonaufelder.Profile.DateOfBirth = 281318400000
	Patient_ThomasDonaufelder.Profile.PatientInfo.PersonalInfo.DOB = 281318400000
	Patient_ThomasDonaufelder.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(281318400000)
	Patient_ThomasDonaufelder.Profile.PatientInfo.PatientNumber = 8 + 1
	Patient_ThomasDonaufelder.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_ThomasDonaufelder.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("Q455600757")
	Patient_ThomasDonaufelder.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108310400
	Patient_ThomasDonaufelder.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_ThomasDonaufelder_AOK_BY_HZV_S15 = &PatientParticipationWithoutDoctor{
		Patient:        Patient_ThomasDonaufelder,
		ContractId:     "AOK_BY_HZV_S15",
		ChargeSystemId: "AOK_BY_HZV_S15",
		IkNumber:       108310400,
		StartDate:      toInt64Pointer(1427846400000),
	}
	_ = PatientParticipation_ThomasDonaufelder_AOK_BY_HZV_S15

	gofakeit.Seed(880638086557)
	Patient_RobinHackl = data.FakePatient()
	Patient_RobinHackl.Profile.FirstName = "Robin"
	Patient_RobinHackl.Profile.PatientInfo.PersonalInfo.FirstName = "Robin"
	Patient_RobinHackl.Profile.LastName = "Hackl"
	Patient_RobinHackl.Profile.PatientInfo.PersonalInfo.LastName = "Hackl"
	Patient_RobinHackl.Profile.DateOfBirth = 493948800000
	Patient_RobinHackl.Profile.PatientInfo.PersonalInfo.DOB = 493948800000
	Patient_RobinHackl.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(493948800000)
	Patient_RobinHackl.Profile.PatientInfo.PatientNumber = 9 + 1
	Patient_RobinHackl.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_RobinHackl.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("X638086557")
	Patient_RobinHackl.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108310400
	Patient_RobinHackl.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_RobinHackl_AOK_BY_HZV_S15 = &PatientParticipationWithoutDoctor{
		Patient:        Patient_RobinHackl,
		ContractId:     "AOK_BY_HZV_S15",
		ChargeSystemId: "AOK_BY_HZV_S15",
		IkNumber:       108310400,
		StartDate:      toInt64Pointer(1427846400000),
	}
	_ = PatientParticipation_RobinHackl_AOK_BY_HZV_S15

	gofakeit.Seed(730581650620)
	Patient_GabrielFriedrich = data.FakePatient()
	Patient_GabrielFriedrich.Profile.FirstName = "Gabriel"
	Patient_GabrielFriedrich.Profile.PatientInfo.PersonalInfo.FirstName = "Gabriel"
	Patient_GabrielFriedrich.Profile.LastName = "Friedrich"
	Patient_GabrielFriedrich.Profile.PatientInfo.PersonalInfo.LastName = "Friedrich"
	Patient_GabrielFriedrich.Profile.DateOfBirth = 783388800000
	Patient_GabrielFriedrich.Profile.PatientInfo.PersonalInfo.DOB = 783388800000
	Patient_GabrielFriedrich.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(783388800000)
	Patient_GabrielFriedrich.Profile.PatientInfo.PatientNumber = 10 + 1
	Patient_GabrielFriedrich.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_GabrielFriedrich.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("I581650620")
	Patient_GabrielFriedrich.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 105313145
	Patient_GabrielFriedrich.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_GabrielFriedrich_AOK_HE_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_GabrielFriedrich,
		ContractId:     "AOK_HE_HZV",
		ChargeSystemId: "AOK_HE_HZV",
		IkNumber:       105313145,
		StartDate:      toInt64Pointer(1356998400000),
	}
	_ = PatientParticipation_GabrielFriedrich_AOK_HE_HZV

	gofakeit.Seed(85030811384)
	Patient_AnnaRottland = data.FakePatient()
	Patient_AnnaRottland.Profile.FirstName = "Anna"
	Patient_AnnaRottland.Profile.PatientInfo.PersonalInfo.FirstName = "Anna"
	Patient_AnnaRottland.Profile.LastName = "Rottland"
	Patient_AnnaRottland.Profile.PatientInfo.PersonalInfo.LastName = "Rottland"
	Patient_AnnaRottland.Profile.DateOfBirth = -1265587200000
	Patient_AnnaRottland.Profile.PatientInfo.PersonalInfo.DOB = -1265587200000
	Patient_AnnaRottland.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-1265587200000)
	Patient_AnnaRottland.Profile.PatientInfo.PatientNumber = 11 + 1
	Patient_AnnaRottland.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_AnnaRottland.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("U30811384")
	Patient_AnnaRottland.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 105313145
	Patient_AnnaRottland.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_AnnaRottland_AOK_HE_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_AnnaRottland,
		ContractId:     "AOK_HE_HZV",
		ChargeSystemId: "AOK_HE_HZV",
		IkNumber:       105313145,
		StartDate:      toInt64Pointer(1356998400000),
	}
	_ = PatientParticipation_AnnaRottland_AOK_HE_HZV

	gofakeit.Seed(870467449015)
	Patient_JuttaAltendorf = data.FakePatient()
	Patient_JuttaAltendorf.Profile.FirstName = "Jutta"
	Patient_JuttaAltendorf.Profile.PatientInfo.PersonalInfo.FirstName = "Jutta"
	Patient_JuttaAltendorf.Profile.LastName = "Altendorf"
	Patient_JuttaAltendorf.Profile.PatientInfo.PersonalInfo.LastName = "Altendorf"
	Patient_JuttaAltendorf.Profile.DateOfBirth = 124416000000
	Patient_JuttaAltendorf.Profile.PatientInfo.PersonalInfo.DOB = 124416000000
	Patient_JuttaAltendorf.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(124416000000)
	Patient_JuttaAltendorf.Profile.PatientInfo.PatientNumber = 12 + 1
	Patient_JuttaAltendorf.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_JuttaAltendorf.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("W467449015")
	Patient_JuttaAltendorf.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 105313145
	Patient_JuttaAltendorf.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_JuttaAltendorf_AOK_HE_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_JuttaAltendorf,
		ContractId:     "AOK_HE_HZV",
		ChargeSystemId: "AOK_HE_HZV",
		IkNumber:       105313145,
		StartDate:      toInt64Pointer(1364774400000),
	}
	_ = PatientParticipation_JuttaAltendorf_AOK_HE_HZV

	gofakeit.Seed(690486968834)
	Patient_AndreasGehlen = data.FakePatient()
	Patient_AndreasGehlen.Profile.FirstName = "Andreas"
	Patient_AndreasGehlen.Profile.PatientInfo.PersonalInfo.FirstName = "Andreas"
	Patient_AndreasGehlen.Profile.LastName = "Gehlen"
	Patient_AndreasGehlen.Profile.PatientInfo.PersonalInfo.LastName = "Gehlen"
	Patient_AndreasGehlen.Profile.DateOfBirth = 446428800000
	Patient_AndreasGehlen.Profile.PatientInfo.PersonalInfo.DOB = 446428800000
	Patient_AndreasGehlen.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(446428800000)
	Patient_AndreasGehlen.Profile.PatientInfo.PatientNumber = 13 + 1
	Patient_AndreasGehlen.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_AndreasGehlen.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("E486968834")
	Patient_AndreasGehlen.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101519213
	Patient_AndreasGehlen.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_AndreasGehlen_AOK_HH_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_AndreasGehlen,
		ContractId:     "AOK_HH_HZV",
		ChargeSystemId: "AOK_HH_HZV",
		IkNumber:       101519213,
		StartDate:      toInt64Pointer(1443657600000),
	}
	_ = PatientParticipation_AndreasGehlen_AOK_HH_HZV

	gofakeit.Seed(730920300883)
	Patient_TimReichel = data.FakePatient()
	Patient_TimReichel.Profile.FirstName = "Tim"
	Patient_TimReichel.Profile.PatientInfo.PersonalInfo.FirstName = "Tim"
	Patient_TimReichel.Profile.LastName = "Reichel"
	Patient_TimReichel.Profile.PatientInfo.PersonalInfo.LastName = "Reichel"
	Patient_TimReichel.Profile.DateOfBirth = 1003017600000
	Patient_TimReichel.Profile.PatientInfo.PersonalInfo.DOB = 1003017600000
	Patient_TimReichel.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(1003017600000)
	Patient_TimReichel.Profile.PatientInfo.PatientNumber = 14 + 1
	Patient_TimReichel.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_TimReichel.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("I920300883")
	Patient_TimReichel.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 104212505
	Patient_TimReichel.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_TimReichel_AOK_HH_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_TimReichel,
		ContractId:     "AOK_HH_HZV",
		ChargeSystemId: "AOK_HH_HZV",
		IkNumber:       104212505,
		StartDate:      toInt64Pointer(1443657600000),
	}
	_ = PatientParticipation_TimReichel_AOK_HH_HZV

	gofakeit.Seed(750641247278)
	Patient_WaltraudHummel = data.FakePatient()
	Patient_WaltraudHummel.Profile.FirstName = "Waltraud"
	Patient_WaltraudHummel.Profile.PatientInfo.PersonalInfo.FirstName = "Waltraud"
	Patient_WaltraudHummel.Profile.LastName = "Hummel"
	Patient_WaltraudHummel.Profile.PatientInfo.PersonalInfo.LastName = "Hummel"
	Patient_WaltraudHummel.Profile.DateOfBirth = -656121600000
	Patient_WaltraudHummel.Profile.PatientInfo.PersonalInfo.DOB = -656121600000
	Patient_WaltraudHummel.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-656121600000)
	Patient_WaltraudHummel.Profile.PatientInfo.PatientNumber = 15 + 1
	Patient_WaltraudHummel.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_WaltraudHummel.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("K641247278")
	Patient_WaltraudHummel.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101519213
	Patient_WaltraudHummel.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_WaltraudHummel_AOK_HH_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_WaltraudHummel,
		ContractId:     "AOK_HH_HZV",
		ChargeSystemId: "AOK_HH_HZV",
		IkNumber:       101519213,
		StartDate:      toInt64Pointer(1443657600000),
	}
	_ = PatientParticipation_WaltraudHummel_AOK_HH_HZV

	gofakeit.Seed(730503465178)
	Patient_UdoBorowsky = data.FakePatient()
	Patient_UdoBorowsky.Profile.FirstName = "Udo"
	Patient_UdoBorowsky.Profile.PatientInfo.PersonalInfo.FirstName = "Udo"
	Patient_UdoBorowsky.Profile.LastName = "Borowsky"
	Patient_UdoBorowsky.Profile.PatientInfo.PersonalInfo.LastName = "Borowsky"
	Patient_UdoBorowsky.Profile.DateOfBirth = -913420800000
	Patient_UdoBorowsky.Profile.PatientInfo.PersonalInfo.DOB = -913420800000
	Patient_UdoBorowsky.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-913420800000)
	Patient_UdoBorowsky.Profile.PatientInfo.PatientNumber = 16 + 1
	Patient_UdoBorowsky.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_UdoBorowsky.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("I503465178")
	Patient_UdoBorowsky.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 100602360
	Patient_UdoBorowsky.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_UdoBorowsky_AOK_IKK_BLN_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_UdoBorowsky,
		ContractId:     "AOK_IKK_BLN_HZV",
		ChargeSystemId: "AOK_IKK_BLN_HZV",
		IkNumber:       100602360,
		StartDate:      toInt64Pointer(1285891200000),
	}
	_ = PatientParticipation_UdoBorowsky_AOK_IKK_BLN_HZV

	gofakeit.Seed(760845590069)
	Patient_SusanSchmidt = data.FakePatient()
	Patient_SusanSchmidt.Profile.FirstName = "Susan"
	Patient_SusanSchmidt.Profile.PatientInfo.PersonalInfo.FirstName = "Susan"
	Patient_SusanSchmidt.Profile.LastName = "Schmidt"
	Patient_SusanSchmidt.Profile.PatientInfo.PersonalInfo.LastName = "Schmidt"
	Patient_SusanSchmidt.Profile.DateOfBirth = -301449600000
	Patient_SusanSchmidt.Profile.PatientInfo.PersonalInfo.DOB = -301449600000
	Patient_SusanSchmidt.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-301449600000)
	Patient_SusanSchmidt.Profile.PatientInfo.PatientNumber = 17 + 1
	Patient_SusanSchmidt.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_SusanSchmidt.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("L845590069")
	Patient_SusanSchmidt.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 109519005
	Patient_SusanSchmidt.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_SusanSchmidt_AOK_IKK_BLN_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_SusanSchmidt,
		ContractId:     "AOK_IKK_BLN_HZV",
		ChargeSystemId: "AOK_IKK_BLN_HZV",
		IkNumber:       109519005,
		StartDate:      toInt64Pointer(1285891200000),
	}
	_ = PatientParticipation_SusanSchmidt_AOK_IKK_BLN_HZV

	gofakeit.Seed(790849582784)
	Patient_AlfredTest = data.FakePatient()
	Patient_AlfredTest.Profile.FirstName = "Alfred"
	Patient_AlfredTest.Profile.PatientInfo.PersonalInfo.FirstName = "Alfred"
	Patient_AlfredTest.Profile.LastName = "Test"
	Patient_AlfredTest.Profile.PatientInfo.PersonalInfo.LastName = "Test"
	Patient_AlfredTest.Profile.DateOfBirth = 11404800000
	Patient_AlfredTest.Profile.PatientInfo.PersonalInfo.DOB = 11404800000
	Patient_AlfredTest.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(11404800000)
	Patient_AlfredTest.Profile.PatientInfo.PatientNumber = 18 + 1
	Patient_AlfredTest.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_AlfredTest.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("O849582784")
	Patient_AlfredTest.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 109519005
	Patient_AlfredTest.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_AlfredTest_AOK_IKK_BLN_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_AlfredTest,
		ContractId:     "AOK_IKK_BLN_HZV",
		ChargeSystemId: "AOK_IKK_BLN_HZV",
		IkNumber:       109519005,
		StartDate:      toInt64Pointer(1285891200000),
	}
	_ = PatientParticipation_AlfredTest_AOK_IKK_BLN_HZV

	gofakeit.Seed(690656650901)
	Patient_LucaRey = data.FakePatient()
	Patient_LucaRey.Profile.FirstName = "Luca"
	Patient_LucaRey.Profile.PatientInfo.PersonalInfo.FirstName = "Luca"
	Patient_LucaRey.Profile.LastName = "Rey"
	Patient_LucaRey.Profile.PatientInfo.PersonalInfo.LastName = "Rey"
	Patient_LucaRey.Profile.DateOfBirth = 1065225600000
	Patient_LucaRey.Profile.PatientInfo.PersonalInfo.DOB = 1065225600000
	Patient_LucaRey.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(1065225600000)
	Patient_LucaRey.Profile.PatientInfo.PatientNumber = 19 + 1
	Patient_LucaRey.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_LucaRey.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("E656650901")
	Patient_LucaRey.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101519213
	Patient_LucaRey.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_LucaRey_AOK_NO_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_LucaRey,
		ContractId:     "AOK_NO_HZV",
		ChargeSystemId: "AOK_NO_HZV",
		IkNumber:       101519213,
		StartDate:      toInt64Pointer(1349049600000),
	}
	_ = PatientParticipation_LucaRey_AOK_NO_HZV

	gofakeit.Seed(810780622821)
	Patient_JakobWeller = data.FakePatient()
	Patient_JakobWeller.Profile.FirstName = "Jakob"
	Patient_JakobWeller.Profile.PatientInfo.PersonalInfo.FirstName = "Jakob"
	Patient_JakobWeller.Profile.LastName = "Weller"
	Patient_JakobWeller.Profile.PatientInfo.PersonalInfo.LastName = "Weller"
	Patient_JakobWeller.Profile.DateOfBirth = 495417600000
	Patient_JakobWeller.Profile.PatientInfo.PersonalInfo.DOB = 495417600000
	Patient_JakobWeller.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(495417600000)
	Patient_JakobWeller.Profile.PatientInfo.PatientNumber = 20 + 1
	Patient_JakobWeller.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_JakobWeller.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("Q780622821")
	Patient_JakobWeller.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101519213
	Patient_JakobWeller.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_JakobWeller_AOK_NO_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_JakobWeller,
		ContractId:     "AOK_NO_HZV",
		ChargeSystemId: "AOK_NO_HZV",
		IkNumber:       101519213,
		StartDate:      toInt64Pointer(1356998400000),
	}
	_ = PatientParticipation_JakobWeller_AOK_NO_HZV

	gofakeit.Seed(840280937174)
	Patient_TheaGrunert = data.FakePatient()
	Patient_TheaGrunert.Profile.FirstName = "Thea"
	Patient_TheaGrunert.Profile.PatientInfo.PersonalInfo.FirstName = "Thea"
	Patient_TheaGrunert.Profile.LastName = "Grunert"
	Patient_TheaGrunert.Profile.PatientInfo.PersonalInfo.LastName = "Grunert"
	Patient_TheaGrunert.Profile.DateOfBirth = -460771200000
	Patient_TheaGrunert.Profile.PatientInfo.PersonalInfo.DOB = -460771200000
	Patient_TheaGrunert.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-460771200000)
	Patient_TheaGrunert.Profile.PatientInfo.PatientNumber = 21 + 1
	Patient_TheaGrunert.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_TheaGrunert.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("T280937174")
	Patient_TheaGrunert.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101519213
	Patient_TheaGrunert.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_TheaGrunert_AOK_NO_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_TheaGrunert,
		ContractId:     "AOK_NO_HZV",
		ChargeSystemId: "AOK_NO_HZV",
		IkNumber:       101519213,
		StartDate:      toInt64Pointer(1349049600000),
	}
	_ = PatientParticipation_TheaGrunert_AOK_NO_HZV

	gofakeit.Seed(700755930008)
	Patient_MikaFessbrecht = data.FakePatient()
	Patient_MikaFessbrecht.Profile.FirstName = "Mika"
	Patient_MikaFessbrecht.Profile.PatientInfo.PersonalInfo.FirstName = "Mika"
	Patient_MikaFessbrecht.Profile.LastName = "Fessbrecht"
	Patient_MikaFessbrecht.Profile.PatientInfo.PersonalInfo.LastName = "Fessbrecht"
	Patient_MikaFessbrecht.Profile.DateOfBirth = 1154736000000
	Patient_MikaFessbrecht.Profile.PatientInfo.PersonalInfo.DOB = 1154736000000
	Patient_MikaFessbrecht.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(1154736000000)
	Patient_MikaFessbrecht.Profile.PatientInfo.PatientNumber = 22 + 1
	Patient_MikaFessbrecht.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_MikaFessbrecht.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("F755930008")
	Patient_MikaFessbrecht.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 107299005
	Patient_MikaFessbrecht.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_MikaFessbrecht_AOK_PLUS_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MikaFessbrecht,
		ContractId:     "AOK_PLUS_HZV",
		ChargeSystemId: "AOK_PLUS_HZV",
		IkNumber:       107299005,
		StartDate:      toInt64Pointer(1451606400000),
	}
	_ = PatientParticipation_MikaFessbrecht_AOK_PLUS_HZV

	gofakeit.Seed(820961554070)
	Patient_LilianeHagenbeck = data.FakePatient()
	Patient_LilianeHagenbeck.Profile.FirstName = "Liliane"
	Patient_LilianeHagenbeck.Profile.PatientInfo.PersonalInfo.FirstName = "Liliane"
	Patient_LilianeHagenbeck.Profile.LastName = "Hagenbeck"
	Patient_LilianeHagenbeck.Profile.PatientInfo.PersonalInfo.LastName = "Hagenbeck"
	Patient_LilianeHagenbeck.Profile.DateOfBirth = -1128384000000
	Patient_LilianeHagenbeck.Profile.PatientInfo.PersonalInfo.DOB = -1128384000000
	Patient_LilianeHagenbeck.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-1128384000000)
	Patient_LilianeHagenbeck.Profile.PatientInfo.PatientNumber = 23 + 1
	Patient_LilianeHagenbeck.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_LilianeHagenbeck.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("R961554070")
	Patient_LilianeHagenbeck.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 107299005
	Patient_LilianeHagenbeck.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_LilianeHagenbeck_AOK_PLUS_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_LilianeHagenbeck,
		ContractId:     "AOK_PLUS_HZV",
		ChargeSystemId: "AOK_PLUS_HZV",
		IkNumber:       107299005,
		StartDate:      toInt64Pointer(1451606400000),
	}
	_ = PatientParticipation_LilianeHagenbeck_AOK_PLUS_HZV

	gofakeit.Seed(840652450891)
	Patient_BorisTissler = data.FakePatient()
	Patient_BorisTissler.Profile.FirstName = "Boris"
	Patient_BorisTissler.Profile.PatientInfo.PersonalInfo.FirstName = "Boris"
	Patient_BorisTissler.Profile.LastName = "Tissler"
	Patient_BorisTissler.Profile.PatientInfo.PersonalInfo.LastName = "Tissler"
	Patient_BorisTissler.Profile.DateOfBirth = 500083200000
	Patient_BorisTissler.Profile.PatientInfo.PersonalInfo.DOB = 500083200000
	Patient_BorisTissler.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(500083200000)
	Patient_BorisTissler.Profile.PatientInfo.PatientNumber = 24 + 1
	Patient_BorisTissler.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_BorisTissler.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("T652450891")
	Patient_BorisTissler.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 107299005
	Patient_BorisTissler.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_BorisTissler_AOK_PLUS_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_BorisTissler,
		ContractId:     "AOK_PLUS_HZV",
		ChargeSystemId: "AOK_PLUS_HZV",
		IkNumber:       107299005,
		StartDate:      toInt64Pointer(1451606400000),
	}
	_ = PatientParticipation_BorisTissler_AOK_PLUS_HZV

	gofakeit.Seed(740294494140)
	Patient_SabrinaSauer = data.FakePatient()
	Patient_SabrinaSauer.Profile.FirstName = "Sabrina"
	Patient_SabrinaSauer.Profile.PatientInfo.PersonalInfo.FirstName = "Sabrina"
	Patient_SabrinaSauer.Profile.LastName = "Sauer"
	Patient_SabrinaSauer.Profile.PatientInfo.PersonalInfo.LastName = "Sauer"
	Patient_SabrinaSauer.Profile.DateOfBirth = 460684800000
	Patient_SabrinaSauer.Profile.PatientInfo.PersonalInfo.DOB = 460684800000
	Patient_SabrinaSauer.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(460684800000)
	Patient_SabrinaSauer.Profile.PatientInfo.PatientNumber = 25 + 1
	Patient_SabrinaSauer.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_SabrinaSauer.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("J294494140")
	Patient_SabrinaSauer.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101317004
	Patient_SabrinaSauer.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_SabrinaSauer_AOK_WL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_SabrinaSauer,
		ContractId:     "AOK_WL_HZV",
		ChargeSystemId: "AOK_WL_HZV",
		IkNumber:       101317004,
		StartDate:      toInt64Pointer(1349049600000),
	}
	_ = PatientParticipation_SabrinaSauer_AOK_WL_HZV

	gofakeit.Seed(770751872446)
	Patient_RosemarieWinter = data.FakePatient()
	Patient_RosemarieWinter.Profile.FirstName = "Rosemarie"
	Patient_RosemarieWinter.Profile.PatientInfo.PersonalInfo.FirstName = "Rosemarie"
	Patient_RosemarieWinter.Profile.LastName = "Winter"
	Patient_RosemarieWinter.Profile.PatientInfo.PersonalInfo.LastName = "Winter"
	Patient_RosemarieWinter.Profile.DateOfBirth = -1066089600000
	Patient_RosemarieWinter.Profile.PatientInfo.PersonalInfo.DOB = -1066089600000
	Patient_RosemarieWinter.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-1066089600000)
	Patient_RosemarieWinter.Profile.PatientInfo.PatientNumber = 26 + 1
	Patient_RosemarieWinter.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_RosemarieWinter.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("M751872446")
	Patient_RosemarieWinter.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101317004
	Patient_RosemarieWinter.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_RosemarieWinter_AOK_WL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_RosemarieWinter,
		ContractId:     "AOK_WL_HZV",
		ChargeSystemId: "AOK_WL_HZV",
		IkNumber:       101317004,
		StartDate:      toInt64Pointer(1349049600000),
	}
	_ = PatientParticipation_RosemarieWinter_AOK_WL_HZV

	gofakeit.Seed(830944252345)
	Patient_TristanLaubach = data.FakePatient()
	Patient_TristanLaubach.Profile.FirstName = "Tristan"
	Patient_TristanLaubach.Profile.PatientInfo.PersonalInfo.FirstName = "Tristan"
	Patient_TristanLaubach.Profile.LastName = "Laubach"
	Patient_TristanLaubach.Profile.PatientInfo.PersonalInfo.LastName = "Laubach"
	Patient_TristanLaubach.Profile.DateOfBirth = -36806400000
	Patient_TristanLaubach.Profile.PatientInfo.PersonalInfo.DOB = -36806400000
	Patient_TristanLaubach.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-36806400000)
	Patient_TristanLaubach.Profile.PatientInfo.PatientNumber = 27 + 1
	Patient_TristanLaubach.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_TristanLaubach.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("S944252345")
	Patient_TristanLaubach.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101317004
	Patient_TristanLaubach.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_TristanLaubach_AOK_WL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_TristanLaubach,
		ContractId:     "AOK_WL_HZV",
		ChargeSystemId: "AOK_WL_HZV",
		IkNumber:       101317004,
		StartDate:      toInt64Pointer(1356998400000),
	}
	_ = PatientParticipation_TristanLaubach_AOK_WL_HZV

	gofakeit.Seed(870336668136)
	Patient_ErnaMüßgen = data.FakePatient()
	Patient_ErnaMüßgen.Profile.FirstName = "Erna"
	Patient_ErnaMüßgen.Profile.PatientInfo.PersonalInfo.FirstName = "Erna"
	Patient_ErnaMüßgen.Profile.LastName = "Müßgen"
	Patient_ErnaMüßgen.Profile.PatientInfo.PersonalInfo.LastName = "Müßgen"
	Patient_ErnaMüßgen.Profile.DateOfBirth = -**********
	Patient_ErnaMüßgen.Profile.PatientInfo.PersonalInfo.DOB = -**********
	Patient_ErnaMüßgen.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-**********)
	Patient_ErnaMüßgen.Profile.PatientInfo.PatientNumber = 28 + 1
	Patient_ErnaMüßgen.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_ErnaMüßgen.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("W336668136")
	Patient_ErnaMüßgen.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108018007
	Patient_ErnaMüßgen.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_ErnaMüßgen_AWH_01 = &PatientParticipationWithoutDoctor{
		Patient:        Patient_ErnaMüßgen,
		ContractId:     "AWH_01",
		ChargeSystemId: "AWH_01",
		IkNumber:       108018007,
		StartDate:      toInt64Pointer(1222819200000),
	}
	_ = PatientParticipation_ErnaMüßgen_AWH_01

	gofakeit.Seed(900081075658)
	Patient_SieglindeGracht = data.FakePatient()
	Patient_SieglindeGracht.Profile.FirstName = "Sieglinde"
	Patient_SieglindeGracht.Profile.PatientInfo.PersonalInfo.FirstName = "Sieglinde"
	Patient_SieglindeGracht.Profile.LastName = "Gracht"
	Patient_SieglindeGracht.Profile.PatientInfo.PersonalInfo.LastName = "Gracht"
	Patient_SieglindeGracht.Profile.DateOfBirth = -1007769600000
	Patient_SieglindeGracht.Profile.PatientInfo.PersonalInfo.DOB = -1007769600000
	Patient_SieglindeGracht.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-1007769600000)
	Patient_SieglindeGracht.Profile.PatientInfo.PatientNumber = 29 + 1
	Patient_SieglindeGracht.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_SieglindeGracht.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("Z081075658")
	Patient_SieglindeGracht.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108018007
	Patient_SieglindeGracht.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_SieglindeGracht_AWH_01 = &PatientParticipationWithoutDoctor{
		Patient:        Patient_SieglindeGracht,
		ContractId:     "AWH_01",
		ChargeSystemId: "AWH_01",
		IkNumber:       108018007,
		StartDate:      toInt64Pointer(1222819200000),
	}
	_ = PatientParticipation_SieglindeGracht_AWH_01

	gofakeit.Seed(660119767279)
	Patient_FinnJasper = data.FakePatient()
	Patient_FinnJasper.Profile.FirstName = "Finn"
	Patient_FinnJasper.Profile.PatientInfo.PersonalInfo.FirstName = "Finn"
	Patient_FinnJasper.Profile.LastName = "Jasper"
	Patient_FinnJasper.Profile.PatientInfo.PersonalInfo.LastName = "Jasper"
	Patient_FinnJasper.Profile.DateOfBirth = 1224201600000
	Patient_FinnJasper.Profile.PatientInfo.PersonalInfo.DOB = 1224201600000
	Patient_FinnJasper.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(1224201600000)
	Patient_FinnJasper.Profile.PatientInfo.PatientNumber = 30 + 1
	Patient_FinnJasper.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_FinnJasper.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("B119767279")
	Patient_FinnJasper.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108018007
	Patient_FinnJasper.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_FinnJasper_AWH_01 = &PatientParticipationWithoutDoctor{
		Patient:        Patient_FinnJasper,
		ContractId:     "AWH_01",
		ChargeSystemId: "BVKJ",
		IkNumber:       108018007,
		StartDate:      toInt64Pointer(1388534400000),
	}
	_ = PatientParticipation_FinnJasper_AWH_01

	gofakeit.Seed(840907330577)
	Patient_LeonHeinzel = data.FakePatient()
	Patient_LeonHeinzel.Profile.FirstName = "Leon"
	Patient_LeonHeinzel.Profile.PatientInfo.PersonalInfo.FirstName = "Leon"
	Patient_LeonHeinzel.Profile.LastName = "Heinzel"
	Patient_LeonHeinzel.Profile.PatientInfo.PersonalInfo.LastName = "Heinzel"
	Patient_LeonHeinzel.Profile.DateOfBirth = 1117238400000
	Patient_LeonHeinzel.Profile.PatientInfo.PersonalInfo.DOB = 1117238400000
	Patient_LeonHeinzel.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(1117238400000)
	Patient_LeonHeinzel.Profile.PatientInfo.PatientNumber = 31 + 1
	Patient_LeonHeinzel.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_LeonHeinzel.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("T907330577")
	Patient_LeonHeinzel.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108018007
	Patient_LeonHeinzel.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_LeonHeinzel_AWH_01 = &PatientParticipationWithoutDoctor{
		Patient:        Patient_LeonHeinzel,
		ContractId:     "AWH_01",
		ChargeSystemId: "BVKJ",
		IkNumber:       108018007,
		StartDate:      toInt64Pointer(1388534400000),
	}
	_ = PatientParticipation_LeonHeinzel_AWH_01

	gofakeit.Seed(900258486450)
	Patient_HerbertBrügel = data.FakePatient()
	Patient_HerbertBrügel.Profile.FirstName = "Herbert"
	Patient_HerbertBrügel.Profile.PatientInfo.PersonalInfo.FirstName = "Herbert"
	Patient_HerbertBrügel.Profile.LastName = "Brügel"
	Patient_HerbertBrügel.Profile.PatientInfo.PersonalInfo.LastName = "Brügel"
	Patient_HerbertBrügel.Profile.DateOfBirth = -856569600000
	Patient_HerbertBrügel.Profile.PatientInfo.PersonalInfo.DOB = -856569600000
	Patient_HerbertBrügel.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-856569600000)
	Patient_HerbertBrügel.Profile.PatientInfo.PatientNumber = 32 + 1
	Patient_HerbertBrügel.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_HerbertBrügel.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("Z258486450")
	Patient_HerbertBrügel.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108036123
	Patient_HerbertBrügel.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_HerbertBrügel_BKK_BOSCH_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_HerbertBrügel,
		ContractId:     "BKK_BOSCH_BW",
		ChargeSystemId: "BKK_BOSCH_BW",
		IkNumber:       108036123,
		StartDate:      toInt64Pointer(1254355200000),
	}
	_ = PatientParticipation_HerbertBrügel_BKK_BOSCH_BW

	gofakeit.Seed(680891634962)
	Patient_RitaRohr = data.FakePatient()
	Patient_RitaRohr.Profile.FirstName = "Rita"
	Patient_RitaRohr.Profile.PatientInfo.PersonalInfo.FirstName = "Rita"
	Patient_RitaRohr.Profile.LastName = "Rohr"
	Patient_RitaRohr.Profile.PatientInfo.PersonalInfo.LastName = "Rohr"
	Patient_RitaRohr.Profile.DateOfBirth = -138499200000
	Patient_RitaRohr.Profile.PatientInfo.PersonalInfo.DOB = -138499200000
	Patient_RitaRohr.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-138499200000)
	Patient_RitaRohr.Profile.PatientInfo.PatientNumber = 33 + 1
	Patient_RitaRohr.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_RitaRohr.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("D891634962")
	Patient_RitaRohr.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 102192471
	Patient_RitaRohr.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_RitaRohr_BKK_BW_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_RitaRohr,
		ContractId:     "BKK_BW_HZV",
		ChargeSystemId: "BKK_BW_HZV",
		IkNumber:       102192471,
		StartDate:      toInt64Pointer(1293840000000),
	}
	_ = PatientParticipation_RitaRohr_BKK_BW_HZV

	gofakeit.Seed(730973919222)
	Patient_KlausKiebel = data.FakePatient()
	Patient_KlausKiebel.Profile.FirstName = "Klaus"
	Patient_KlausKiebel.Profile.PatientInfo.PersonalInfo.FirstName = "Klaus"
	Patient_KlausKiebel.Profile.LastName = "Kiebel"
	Patient_KlausKiebel.Profile.PatientInfo.PersonalInfo.LastName = "Kiebel"
	Patient_KlausKiebel.Profile.DateOfBirth = -732499200000
	Patient_KlausKiebel.Profile.PatientInfo.PersonalInfo.DOB = -732499200000
	Patient_KlausKiebel.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-732499200000)
	Patient_KlausKiebel.Profile.PatientInfo.PatientNumber = 34 + 1
	Patient_KlausKiebel.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_KlausKiebel.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("I973919222")
	Patient_KlausKiebel.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101520147
	Patient_KlausKiebel.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_KlausKiebel_BKK_BW_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_KlausKiebel,
		ContractId:     "BKK_BW_HZV",
		ChargeSystemId: "BKK_BW_HZV",
		IkNumber:       101520147,
		StartDate:      toInt64Pointer(1293840000000),
		EndDate:        toInt64Pointer(1427760000000),
	}
	_ = PatientParticipation_KlausKiebel_BKK_BW_HZV

	gofakeit.Seed(830583690159)
	Patient_BerndSchuster = data.FakePatient()
	Patient_BerndSchuster.Profile.FirstName = "Bernd"
	Patient_BerndSchuster.Profile.PatientInfo.PersonalInfo.FirstName = "Bernd"
	Patient_BerndSchuster.Profile.LastName = "Schuster"
	Patient_BerndSchuster.Profile.PatientInfo.PersonalInfo.LastName = "Schuster"
	Patient_BerndSchuster.Profile.DateOfBirth = 165024000000
	Patient_BerndSchuster.Profile.PatientInfo.PersonalInfo.DOB = 165024000000
	Patient_BerndSchuster.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(165024000000)
	Patient_BerndSchuster.Profile.PatientInfo.PatientNumber = 35 + 1
	Patient_BerndSchuster.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_BerndSchuster.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("S583690159")
	Patient_BerndSchuster.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108836600
	Patient_BerndSchuster.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_BerndSchuster_BKK_BW_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_BerndSchuster,
		ContractId:     "BKK_BW_HZV",
		ChargeSystemId: "BKK_BW_HZV",
		IkNumber:       108836600,
		StartDate:      toInt64Pointer(1293840000000),
	}
	_ = PatientParticipation_BerndSchuster_BKK_BW_HZV

	gofakeit.Seed(690349708837)
	Patient_JürgenDietrich = data.FakePatient()
	Patient_JürgenDietrich.Profile.FirstName = "Jürgen"
	Patient_JürgenDietrich.Profile.PatientInfo.PersonalInfo.FirstName = "Jürgen"
	Patient_JürgenDietrich.Profile.LastName = "Dietrich"
	Patient_JürgenDietrich.Profile.PatientInfo.PersonalInfo.LastName = "Dietrich"
	Patient_JürgenDietrich.Profile.DateOfBirth = -316569600000
	Patient_JürgenDietrich.Profile.PatientInfo.PersonalInfo.DOB = -316569600000
	Patient_JürgenDietrich.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-316569600000)
	Patient_JürgenDietrich.Profile.PatientInfo.PatientNumber = 36 + 1
	Patient_JürgenDietrich.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_JürgenDietrich.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("E349708837")
	Patient_JürgenDietrich.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108030775
	Patient_JürgenDietrich.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_JürgenDietrich_BKK_BY_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_JürgenDietrich,
		ContractId:     "BKK_BY_HZV",
		ChargeSystemId: "BKK_BY_HZV",
		IkNumber:       108030775,
		StartDate:      toInt64Pointer(1277942400000),
	}
	_ = PatientParticipation_JürgenDietrich_BKK_BY_HZV

	gofakeit.Seed(750492744106)
	Patient_DirkEmmerich = data.FakePatient()
	Patient_DirkEmmerich.Profile.FirstName = "Dirk"
	Patient_DirkEmmerich.Profile.PatientInfo.PersonalInfo.FirstName = "Dirk"
	Patient_DirkEmmerich.Profile.LastName = "Emmerich"
	Patient_DirkEmmerich.Profile.PatientInfo.PersonalInfo.LastName = "Emmerich"
	Patient_DirkEmmerich.Profile.DateOfBirth = -266457600000
	Patient_DirkEmmerich.Profile.PatientInfo.PersonalInfo.DOB = -266457600000
	Patient_DirkEmmerich.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-266457600000)
	Patient_DirkEmmerich.Profile.PatientInfo.PatientNumber = 37 + 1
	Patient_DirkEmmerich.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_DirkEmmerich.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("K492744106")
	Patient_DirkEmmerich.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108534160
	Patient_DirkEmmerich.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_DirkEmmerich_BKK_BY_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_DirkEmmerich,
		ContractId:     "BKK_BY_HZV",
		ChargeSystemId: "BKK_BY_HZV",
		IkNumber:       108534160,
		StartDate:      toInt64Pointer(1277942400000),
	}
	_ = PatientParticipation_DirkEmmerich_BKK_BY_HZV

	gofakeit.Seed(810121907234)
	Patient_StefanieVogel = data.FakePatient()
	Patient_StefanieVogel.Profile.FirstName = "Stefanie"
	Patient_StefanieVogel.Profile.PatientInfo.PersonalInfo.FirstName = "Stefanie"
	Patient_StefanieVogel.Profile.LastName = "Vogel"
	Patient_StefanieVogel.Profile.PatientInfo.PersonalInfo.LastName = "Vogel"
	Patient_StefanieVogel.Profile.DateOfBirth = 61171200000
	Patient_StefanieVogel.Profile.PatientInfo.PersonalInfo.DOB = 61171200000
	Patient_StefanieVogel.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(61171200000)
	Patient_StefanieVogel.Profile.PatientInfo.PatientNumber = 38 + 1
	Patient_StefanieVogel.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_StefanieVogel.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("Q121907234")
	Patient_StefanieVogel.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108035612
	Patient_StefanieVogel.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_StefanieVogel_BKK_BY_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_StefanieVogel,
		ContractId:     "BKK_BY_HZV",
		ChargeSystemId: "BKK_BY_HZV",
		IkNumber:       108035612,
		StartDate:      toInt64Pointer(1277942400000),
	}
	_ = PatientParticipation_StefanieVogel_BKK_BY_HZV

	gofakeit.Seed(660767876984)
	Patient_MeikeFrost = data.FakePatient()
	Patient_MeikeFrost.Profile.FirstName = "Meike"
	Patient_MeikeFrost.Profile.PatientInfo.PersonalInfo.FirstName = "Meike"
	Patient_MeikeFrost.Profile.LastName = "Frost"
	Patient_MeikeFrost.Profile.PatientInfo.PersonalInfo.LastName = "Frost"
	Patient_MeikeFrost.Profile.DateOfBirth = 141436800000
	Patient_MeikeFrost.Profile.PatientInfo.PersonalInfo.DOB = 141436800000
	Patient_MeikeFrost.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(141436800000)
	Patient_MeikeFrost.Profile.PatientInfo.PatientNumber = 39 + 1
	Patient_MeikeFrost.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_MeikeFrost.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("B767876984")
	Patient_MeikeFrost.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108030775
	Patient_MeikeFrost.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_MeikeFrost_BKK_GWQ_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MeikeFrost,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		IkNumber:       108030775,
		StartDate:      toInt64Pointer(1420070400000),
	}
	_ = PatientParticipation_MeikeFrost_BKK_GWQ_HZV

	gofakeit.Seed(660854421415)
	Patient_RosemarieSauger = data.FakePatient()
	Patient_RosemarieSauger.Profile.FirstName = "Rosemarie"
	Patient_RosemarieSauger.Profile.PatientInfo.PersonalInfo.FirstName = "Rosemarie"
	Patient_RosemarieSauger.Profile.LastName = "Sauger"
	Patient_RosemarieSauger.Profile.PatientInfo.PersonalInfo.LastName = "Sauger"
	Patient_RosemarieSauger.Profile.DateOfBirth = -1038700800000
	Patient_RosemarieSauger.Profile.PatientInfo.PersonalInfo.DOB = -1038700800000
	Patient_RosemarieSauger.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-1038700800000)
	Patient_RosemarieSauger.Profile.PatientInfo.PatientNumber = 40 + 1
	Patient_RosemarieSauger.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_RosemarieSauger.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("B854421415")
	Patient_RosemarieSauger.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 102129930
	Patient_RosemarieSauger.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_RosemarieSauger_BKK_GWQ_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_RosemarieSauger,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		IkNumber:       102129930,
		StartDate:      toInt64Pointer(1420070400000),
	}
	_ = PatientParticipation_RosemarieSauger_BKK_GWQ_HZV

	gofakeit.Seed(840992947024)
	Patient_MoritzDachsler = data.FakePatient()
	Patient_MoritzDachsler.Profile.FirstName = "Moritz"
	Patient_MoritzDachsler.Profile.PatientInfo.PersonalInfo.FirstName = "Moritz"
	Patient_MoritzDachsler.Profile.LastName = "Dachsler"
	Patient_MoritzDachsler.Profile.PatientInfo.PersonalInfo.LastName = "Dachsler"
	Patient_MoritzDachsler.Profile.DateOfBirth = 762566400000
	Patient_MoritzDachsler.Profile.PatientInfo.PersonalInfo.DOB = 762566400000
	Patient_MoritzDachsler.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(762566400000)
	Patient_MoritzDachsler.Profile.PatientInfo.PatientNumber = 41 + 1
	Patient_MoritzDachsler.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_MoritzDachsler.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("T992947024")
	Patient_MoritzDachsler.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108433248
	Patient_MoritzDachsler.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_MoritzDachsler_BKK_GWQ_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MoritzDachsler,
		ContractId:     "BKK_GWQ_HZV",
		ChargeSystemId: "BKK_GWQ_HZV",
		IkNumber:       108433248,
		StartDate:      toInt64Pointer(1420070400000),
	}
	_ = PatientParticipation_MoritzDachsler_BKK_GWQ_HZV

	gofakeit.Seed(650292950074)
	Patient_LudwigJakobs = data.FakePatient()
	Patient_LudwigJakobs.Profile.FirstName = "Ludwig"
	Patient_LudwigJakobs.Profile.PatientInfo.PersonalInfo.FirstName = "Ludwig"
	Patient_LudwigJakobs.Profile.LastName = "Jakobs"
	Patient_LudwigJakobs.Profile.PatientInfo.PersonalInfo.LastName = "Jakobs"
	Patient_LudwigJakobs.Profile.DateOfBirth = 621302400000
	Patient_LudwigJakobs.Profile.PatientInfo.PersonalInfo.DOB = 621302400000
	Patient_LudwigJakobs.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(621302400000)
	Patient_LudwigJakobs.Profile.PatientInfo.PatientNumber = 42 + 1
	Patient_LudwigJakobs.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_LudwigJakobs.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("A292950074")
	Patient_LudwigJakobs.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 104491718
	Patient_LudwigJakobs.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_LudwigJakobs_BKK_NO_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_LudwigJakobs,
		ContractId:     "BKK_NO_HZV",
		ChargeSystemId: "BKK_NO_HZV",
		IkNumber:       104491718,
		StartDate:      toInt64Pointer(1349049600000),
	}
	_ = PatientParticipation_LudwigJakobs_BKK_NO_HZV

	gofakeit.Seed(770407940227)
	Patient_IsoldeKleinmann = data.FakePatient()
	Patient_IsoldeKleinmann.Profile.FirstName = "Isolde"
	Patient_IsoldeKleinmann.Profile.PatientInfo.PersonalInfo.FirstName = "Isolde"
	Patient_IsoldeKleinmann.Profile.LastName = "Kleinmann"
	Patient_IsoldeKleinmann.Profile.PatientInfo.PersonalInfo.LastName = "Kleinmann"
	Patient_IsoldeKleinmann.Profile.DateOfBirth = -860716800000
	Patient_IsoldeKleinmann.Profile.PatientInfo.PersonalInfo.DOB = -860716800000
	Patient_IsoldeKleinmann.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-860716800000)
	Patient_IsoldeKleinmann.Profile.PatientInfo.PatientNumber = 43 + 1
	Patient_IsoldeKleinmann.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_IsoldeKleinmann.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("M407940227")
	Patient_IsoldeKleinmann.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108433248
	Patient_IsoldeKleinmann.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_IsoldeKleinmann_BKK_NO_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_IsoldeKleinmann,
		ContractId:     "BKK_NO_HZV",
		ChargeSystemId: "BKK_NO_HZV",
		IkNumber:       108433248,
		StartDate:      toInt64Pointer(1349049600000),
	}
	_ = PatientParticipation_IsoldeKleinmann_BKK_NO_HZV

	gofakeit.Seed(840982978270)
	Patient_PetraAst = data.FakePatient()
	Patient_PetraAst.Profile.FirstName = "Petra"
	Patient_PetraAst.Profile.PatientInfo.PersonalInfo.FirstName = "Petra"
	Patient_PetraAst.Profile.LastName = "Ast"
	Patient_PetraAst.Profile.PatientInfo.PersonalInfo.LastName = "Ast"
	Patient_PetraAst.Profile.DateOfBirth = -181699200000
	Patient_PetraAst.Profile.PatientInfo.PersonalInfo.DOB = -181699200000
	Patient_PetraAst.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-181699200000)
	Patient_PetraAst.Profile.PatientInfo.PatientNumber = 44 + 1
	Patient_PetraAst.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_PetraAst.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("T982978270")
	Patient_PetraAst.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 107835333
	Patient_PetraAst.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_PetraAst_BKK_NO_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_PetraAst,
		ContractId:     "BKK_NO_HZV",
		ChargeSystemId: "BKK_NO_HZV",
		IkNumber:       107835333,
		StartDate:      toInt64Pointer(1356998400000),
	}
	_ = PatientParticipation_PetraAst_BKK_NO_HZV

	gofakeit.Seed(680508824539)
	Patient_FraukeHempel = data.FakePatient()
	Patient_FraukeHempel.Profile.FirstName = "Frauke"
	Patient_FraukeHempel.Profile.PatientInfo.PersonalInfo.FirstName = "Frauke"
	Patient_FraukeHempel.Profile.LastName = "Hempel"
	Patient_FraukeHempel.Profile.PatientInfo.PersonalInfo.LastName = "Hempel"
	Patient_FraukeHempel.Profile.DateOfBirth = 515894400000
	Patient_FraukeHempel.Profile.PatientInfo.PersonalInfo.DOB = 515894400000
	Patient_FraukeHempel.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(515894400000)
	Patient_FraukeHempel.Profile.PatientInfo.PatientNumber = 45 + 1
	Patient_FraukeHempel.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_FraukeHempel.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("D508824539")
	Patient_FraukeHempel.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 102122660
	Patient_FraukeHempel.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_FraukeHempel_BKK_SPECTRUM_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_FraukeHempel,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		IkNumber:       102122660,
		StartDate:      toInt64Pointer(1443657600000),
	}
	_ = PatientParticipation_FraukeHempel_BKK_SPECTRUM_HZV

	gofakeit.Seed(710628589809)
	Patient_MaxNeubauer = data.FakePatient()
	Patient_MaxNeubauer.Profile.FirstName = "Max"
	Patient_MaxNeubauer.Profile.PatientInfo.PersonalInfo.FirstName = "Max"
	Patient_MaxNeubauer.Profile.LastName = "Neubauer"
	Patient_MaxNeubauer.Profile.PatientInfo.PersonalInfo.LastName = "Neubauer"
	Patient_MaxNeubauer.Profile.DateOfBirth = 1103155200000
	Patient_MaxNeubauer.Profile.PatientInfo.PersonalInfo.DOB = 1103155200000
	Patient_MaxNeubauer.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(1103155200000)
	Patient_MaxNeubauer.Profile.PatientInfo.PatientNumber = 46 + 1
	Patient_MaxNeubauer.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_MaxNeubauer.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("G628589809")
	Patient_MaxNeubauer.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 106492393
	Patient_MaxNeubauer.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_MaxNeubauer_BKK_SPECTRUM_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MaxNeubauer,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		IkNumber:       106492393,
		StartDate:      toInt64Pointer(1443657600000),
	}
	_ = PatientParticipation_MaxNeubauer_BKK_SPECTRUM_HZV

	gofakeit.Seed(870748433919)
	Patient_CilliWeinur = data.FakePatient()
	Patient_CilliWeinur.Profile.FirstName = "Cilli"
	Patient_CilliWeinur.Profile.PatientInfo.PersonalInfo.FirstName = "Cilli"
	Patient_CilliWeinur.Profile.LastName = "Weinur"
	Patient_CilliWeinur.Profile.PatientInfo.PersonalInfo.LastName = "Weinur"
	Patient_CilliWeinur.Profile.DateOfBirth = -1355356800000
	Patient_CilliWeinur.Profile.PatientInfo.PersonalInfo.DOB = -1355356800000
	Patient_CilliWeinur.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-1355356800000)
	Patient_CilliWeinur.Profile.PatientInfo.PatientNumber = 47 + 1
	Patient_CilliWeinur.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_CilliWeinur.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("W748433919")
	Patient_CilliWeinur.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 103726081
	Patient_CilliWeinur.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_CilliWeinur_BKK_SPECTRUM_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_CilliWeinur,
		ContractId:     "BKK_SPECTRUM_HZV",
		ChargeSystemId: "BKK_SPECTRUM_HZV",
		IkNumber:       103726081,
		StartDate:      toInt64Pointer(1443657600000),
		EndDate:        toInt64Pointer(1585612800000),
	}
	_ = PatientParticipation_CilliWeinur_BKK_SPECTRUM_HZV

	gofakeit.Seed(670245245430)
	Patient_MargareteFinck = data.FakePatient()
	Patient_MargareteFinck.Profile.FirstName = "Margarete"
	Patient_MargareteFinck.Profile.PatientInfo.PersonalInfo.FirstName = "Margarete"
	Patient_MargareteFinck.Profile.LastName = "Finck"
	Patient_MargareteFinck.Profile.PatientInfo.PersonalInfo.LastName = "Finck"
	Patient_MargareteFinck.Profile.DateOfBirth = -103075200000
	Patient_MargareteFinck.Profile.PatientInfo.PersonalInfo.DOB = -103075200000
	Patient_MargareteFinck.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-103075200000)
	Patient_MargareteFinck.Profile.PatientInfo.PatientNumber = 48 + 1
	Patient_MargareteFinck.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_MargareteFinck.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("C245245430")
	Patient_MargareteFinck.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108034508
	Patient_MargareteFinck.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_MargareteFinck_BKK_VAG_BW = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MargareteFinck,
		ContractId:     "BKK_VAG_BW",
		ChargeSystemId: "BKK_VAG_BW",
		IkNumber:       108034508,
		StartDate:      toInt64Pointer(1254355200000),
	}
	_ = PatientParticipation_MargareteFinck_BKK_VAG_BW

	gofakeit.Seed(670400209522)
	Patient_GotthardGeisinger = data.FakePatient()
	Patient_GotthardGeisinger.Profile.FirstName = "Gotthard"
	Patient_GotthardGeisinger.Profile.PatientInfo.PersonalInfo.FirstName = "Gotthard"
	Patient_GotthardGeisinger.Profile.LastName = "Geisinger"
	Patient_GotthardGeisinger.Profile.PatientInfo.PersonalInfo.LastName = "Geisinger"
	Patient_GotthardGeisinger.Profile.DateOfBirth = -662342400000
	Patient_GotthardGeisinger.Profile.PatientInfo.PersonalInfo.DOB = -662342400000
	Patient_GotthardGeisinger.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-662342400000)
	Patient_GotthardGeisinger.Profile.PatientInfo.PatientNumber = 49 + 1
	Patient_GotthardGeisinger.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_GotthardGeisinger.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("C400209522")
	Patient_GotthardGeisinger.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108433248
	Patient_GotthardGeisinger.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_GotthardGeisinger_BKK_WL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_GotthardGeisinger,
		ContractId:     "BKK_WL_HZV",
		ChargeSystemId: "BKK_WL_HZV",
		IkNumber:       108433248,
		StartDate:      toInt64Pointer(1349049600000),
	}
	_ = PatientParticipation_GotthardGeisinger_BKK_WL_HZV

	gofakeit.Seed(790570258871)
	Patient_MarieKurmann = data.FakePatient()
	Patient_MarieKurmann.Profile.FirstName = "Marie"
	Patient_MarieKurmann.Profile.PatientInfo.PersonalInfo.FirstName = "Marie"
	Patient_MarieKurmann.Profile.LastName = "Kurmann"
	Patient_MarieKurmann.Profile.PatientInfo.PersonalInfo.LastName = "Kurmann"
	Patient_MarieKurmann.Profile.DateOfBirth = -113011200000
	Patient_MarieKurmann.Profile.PatientInfo.PersonalInfo.DOB = -113011200000
	Patient_MarieKurmann.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-113011200000)
	Patient_MarieKurmann.Profile.PatientInfo.PatientNumber = 50 + 1
	Patient_MarieKurmann.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_MarieKurmann.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("O570258871")
	Patient_MarieKurmann.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 107835333
	Patient_MarieKurmann.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_MarieKurmann_BKK_WL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MarieKurmann,
		ContractId:     "BKK_WL_HZV",
		ChargeSystemId: "BKK_WL_HZV",
		IkNumber:       107835333,
		StartDate:      toInt64Pointer(1356998400000),
	}
	_ = PatientParticipation_MarieKurmann_BKK_WL_HZV

	gofakeit.Seed(820350401394)
	Patient_LauraLob = data.FakePatient()
	Patient_LauraLob.Profile.FirstName = "Laura"
	Patient_LauraLob.Profile.PatientInfo.PersonalInfo.FirstName = "Laura"
	Patient_LauraLob.Profile.LastName = "Lob"
	Patient_LauraLob.Profile.PatientInfo.PersonalInfo.LastName = "Lob"
	Patient_LauraLob.Profile.DateOfBirth = 721180800000
	Patient_LauraLob.Profile.PatientInfo.PersonalInfo.DOB = 721180800000
	Patient_LauraLob.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(721180800000)
	Patient_LauraLob.Profile.PatientInfo.PatientNumber = 51 + 1
	Patient_LauraLob.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_LauraLob.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("R350401394")
	Patient_LauraLob.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 104491718
	Patient_LauraLob.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_LauraLob_BKK_WL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_LauraLob,
		ContractId:     "BKK_WL_HZV",
		ChargeSystemId: "BKK_WL_HZV",
		IkNumber:       104491718,
		StartDate:      toInt64Pointer(1349049600000),
	}
	_ = PatientParticipation_LauraLob_BKK_WL_HZV

	gofakeit.Seed(750051189384)
	Patient_LieselotteWeiss = data.FakePatient()
	Patient_LieselotteWeiss.Profile.FirstName = "Lieselotte"
	Patient_LieselotteWeiss.Profile.PatientInfo.PersonalInfo.FirstName = "Lieselotte"
	Patient_LieselotteWeiss.Profile.LastName = "Weiss"
	Patient_LieselotteWeiss.Profile.PatientInfo.PersonalInfo.LastName = "Weiss"
	Patient_LieselotteWeiss.Profile.DateOfBirth = -955065600000
	Patient_LieselotteWeiss.Profile.PatientInfo.PersonalInfo.DOB = -955065600000
	Patient_LieselotteWeiss.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-955065600000)
	Patient_LieselotteWeiss.Profile.PatientInfo.PatientNumber = 52 + 1
	Patient_LieselotteWeiss.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_LieselotteWeiss.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("K051189384")
	Patient_LieselotteWeiss.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101560000
	Patient_LieselotteWeiss.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_LieselotteWeiss_DAK_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_LieselotteWeiss,
		ContractId:     "DAK_HZV",
		ChargeSystemId: "DAK_HZV",
		IkNumber:       101560000,
		StartDate:      toInt64Pointer(1412121600000),
	}
	_ = PatientParticipation_LieselotteWeiss_DAK_HZV

	gofakeit.Seed(860414327264)
	Patient_AstridEllebracht = data.FakePatient()
	Patient_AstridEllebracht.Profile.FirstName = "Astrid"
	Patient_AstridEllebracht.Profile.PatientInfo.PersonalInfo.FirstName = "Astrid"
	Patient_AstridEllebracht.Profile.LastName = "Ellebracht"
	Patient_AstridEllebracht.Profile.PatientInfo.PersonalInfo.LastName = "Ellebracht"
	Patient_AstridEllebracht.Profile.DateOfBirth = -148867200000
	Patient_AstridEllebracht.Profile.PatientInfo.PersonalInfo.DOB = -148867200000
	Patient_AstridEllebracht.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-148867200000)
	Patient_AstridEllebracht.Profile.PatientInfo.PatientNumber = 53 + 1
	Patient_AstridEllebracht.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_AstridEllebracht.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("V414327264")
	Patient_AstridEllebracht.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101560000
	Patient_AstridEllebracht.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_AstridEllebracht_DAK_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_AstridEllebracht,
		ContractId:     "DAK_HZV",
		ChargeSystemId: "DAK_HZV",
		IkNumber:       101560000,
		StartDate:      toInt64Pointer(1412121600000),
	}
	_ = PatientParticipation_AstridEllebracht_DAK_HZV

	gofakeit.Seed(760152902053)
	Patient_TomasMann = data.FakePatient()
	Patient_TomasMann.Profile.FirstName = "Tomas"
	Patient_TomasMann.Profile.PatientInfo.PersonalInfo.FirstName = "Tomas"
	Patient_TomasMann.Profile.LastName = "Mann"
	Patient_TomasMann.Profile.PatientInfo.PersonalInfo.LastName = "Mann"
	Patient_TomasMann.Profile.DateOfBirth = **********
	Patient_TomasMann.Profile.PatientInfo.PersonalInfo.DOB = **********
	Patient_TomasMann.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(**********)
	Patient_TomasMann.Profile.PatientInfo.PatientNumber = 54 + 1
	Patient_TomasMann.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_TomasMann.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("L152902053")
	Patient_TomasMann.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108079808
	Patient_TomasMann.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_TomasMann_EK_BLN_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_TomasMann,
		ContractId:     "EK_BLN_HZV",
		ChargeSystemId: "EK_BLN_HZV",
		IkNumber:       108079808,
		StartDate:      toInt64Pointer(1388534400000),
	}
	_ = PatientParticipation_TomasMann_EK_BLN_HZV

	gofakeit.Seed(780147203094)
	Patient_FridaBaumgart = data.FakePatient()
	Patient_FridaBaumgart.Profile.FirstName = "Frida"
	Patient_FridaBaumgart.Profile.PatientInfo.PersonalInfo.FirstName = "Frida"
	Patient_FridaBaumgart.Profile.LastName = "Baumgart"
	Patient_FridaBaumgart.Profile.PatientInfo.PersonalInfo.LastName = "Baumgart"
	Patient_FridaBaumgart.Profile.DateOfBirth = -852768000000
	Patient_FridaBaumgart.Profile.PatientInfo.PersonalInfo.DOB = -852768000000
	Patient_FridaBaumgart.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-852768000000)
	Patient_FridaBaumgart.Profile.PatientInfo.PatientNumber = 55 + 1
	Patient_FridaBaumgart.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_FridaBaumgart.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("N147203094")
	Patient_FridaBaumgart.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101560000
	Patient_FridaBaumgart.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_FridaBaumgart_EK_BLN_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_FridaBaumgart,
		ContractId:     "EK_BLN_HZV",
		ChargeSystemId: "EK_BLN_HZV",
		IkNumber:       101560000,
		StartDate:      toInt64Pointer(1380585600000),
	}
	_ = PatientParticipation_FridaBaumgart_EK_BLN_HZV

	gofakeit.Seed(790422863797)
	Patient_PeterMagnusen = data.FakePatient()
	Patient_PeterMagnusen.Profile.FirstName = "Peter"
	Patient_PeterMagnusen.Profile.PatientInfo.PersonalInfo.FirstName = "Peter"
	Patient_PeterMagnusen.Profile.LastName = "Magnusen"
	Patient_PeterMagnusen.Profile.PatientInfo.PersonalInfo.LastName = "Magnusen"
	Patient_PeterMagnusen.Profile.DateOfBirth = 804470400000
	Patient_PeterMagnusen.Profile.PatientInfo.PersonalInfo.DOB = 804470400000
	Patient_PeterMagnusen.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(804470400000)
	Patient_PeterMagnusen.Profile.PatientInfo.PatientNumber = 56 + 1
	Patient_PeterMagnusen.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_PeterMagnusen.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("O422863797")
	Patient_PeterMagnusen.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 104940005
	Patient_PeterMagnusen.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_PeterMagnusen_EK_BLN_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_PeterMagnusen,
		ContractId:     "EK_BLN_HZV",
		ChargeSystemId: "EK_BLN_HZV",
		IkNumber:       104940005,
		StartDate:      toInt64Pointer(1396310400000),
	}
	_ = PatientParticipation_PeterMagnusen_EK_BLN_HZV

	gofakeit.Seed(760787207637)
	Patient_ArneAalkaten = data.FakePatient()
	Patient_ArneAalkaten.Profile.FirstName = "Arne"
	Patient_ArneAalkaten.Profile.PatientInfo.PersonalInfo.FirstName = "Arne"
	Patient_ArneAalkaten.Profile.LastName = "Aalkaten"
	Patient_ArneAalkaten.Profile.PatientInfo.PersonalInfo.LastName = "Aalkaten"
	Patient_ArneAalkaten.Profile.DateOfBirth = 24364800000
	Patient_ArneAalkaten.Profile.PatientInfo.PersonalInfo.DOB = 24364800000
	Patient_ArneAalkaten.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(24364800000)
	Patient_ArneAalkaten.Profile.PatientInfo.PatientNumber = 57 + 1
	Patient_ArneAalkaten.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_ArneAalkaten.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("L787207637")
	Patient_ArneAalkaten.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101560000
	Patient_ArneAalkaten.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_ArneAalkaten_EK_BW_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_ArneAalkaten,
		ContractId:     "EK_BW_HZV",
		ChargeSystemId: "EK_BW_HZV",
		IkNumber:       101560000,
		StartDate:      toInt64Pointer(1285891200000),
	}
	_ = PatientParticipation_ArneAalkaten_EK_BW_HZV

	gofakeit.Seed(870263625418)
	Patient_BennoBriedel = data.FakePatient()
	Patient_BennoBriedel.Profile.FirstName = "Benno"
	Patient_BennoBriedel.Profile.PatientInfo.PersonalInfo.FirstName = "Benno"
	Patient_BennoBriedel.Profile.LastName = "Briedel"
	Patient_BennoBriedel.Profile.PatientInfo.PersonalInfo.LastName = "Briedel"
	Patient_BennoBriedel.Profile.DateOfBirth = -**********
	Patient_BennoBriedel.Profile.PatientInfo.PersonalInfo.DOB = -**********
	Patient_BennoBriedel.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-**********)
	Patient_BennoBriedel.Profile.PatientInfo.PatientNumber = 58 + 1
	Patient_BennoBriedel.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_BennoBriedel.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("W263625418")
	Patient_BennoBriedel.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101575519
	Patient_BennoBriedel.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_BennoBriedel_EK_BW_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_BennoBriedel,
		ContractId:     "EK_BW_HZV",
		ChargeSystemId: "EK_BW_HZV",
		IkNumber:       101575519,
		StartDate:      toInt64Pointer(1285891200000),
	}
	_ = PatientParticipation_BennoBriedel_EK_BW_HZV

	gofakeit.Seed(840013679412)
	Patient_BärbelSassen = data.FakePatient()
	Patient_BärbelSassen.Profile.FirstName = "Bärbel"
	Patient_BärbelSassen.Profile.PatientInfo.PersonalInfo.FirstName = "Bärbel"
	Patient_BärbelSassen.Profile.LastName = "Sassen"
	Patient_BärbelSassen.Profile.PatientInfo.PersonalInfo.LastName = "Sassen"
	Patient_BärbelSassen.Profile.DateOfBirth = -562550400000
	Patient_BärbelSassen.Profile.PatientInfo.PersonalInfo.DOB = -562550400000
	Patient_BärbelSassen.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-562550400000)
	Patient_BärbelSassen.Profile.PatientInfo.PatientNumber = 59 + 1
	Patient_BärbelSassen.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_BärbelSassen.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("T013679412")
	Patient_BärbelSassen.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 102171012
	Patient_BärbelSassen.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_BärbelSassen_EK_BY_S12_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_BärbelSassen,
		ContractId:     "EK_BY_S12_HZV",
		ChargeSystemId: "EK_BY_S12_HZV",
		IkNumber:       102171012,
		StartDate:      toInt64Pointer(1341100800000),
	}
	_ = PatientParticipation_BärbelSassen_EK_BY_S12_HZV

	gofakeit.Seed(860033848508)
	Patient_SilkeFliege = data.FakePatient()
	Patient_SilkeFliege.Profile.FirstName = "Silke"
	Patient_SilkeFliege.Profile.PatientInfo.PersonalInfo.FirstName = "Silke"
	Patient_SilkeFliege.Profile.LastName = "Fliege"
	Patient_SilkeFliege.Profile.PatientInfo.PersonalInfo.LastName = "Fliege"
	Patient_SilkeFliege.Profile.DateOfBirth = 124156800000
	Patient_SilkeFliege.Profile.PatientInfo.PersonalInfo.DOB = 124156800000
	Patient_SilkeFliege.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(124156800000)
	Patient_SilkeFliege.Profile.PatientInfo.PatientNumber = 60 + 1
	Patient_SilkeFliege.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_SilkeFliege.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("V033848508")
	Patient_SilkeFliege.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101560000
	Patient_SilkeFliege.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_SilkeFliege_EK_BY_S12_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_SilkeFliege,
		ContractId:     "EK_BY_S12_HZV",
		ChargeSystemId: "EK_BY_S12_HZV",
		IkNumber:       101560000,
		StartDate:      toInt64Pointer(1349049600000),
	}
	_ = PatientParticipation_SilkeFliege_EK_BY_S12_HZV

	gofakeit.Seed(880589695749)
	Patient_PatrickWessler = data.FakePatient()
	Patient_PatrickWessler.Profile.FirstName = "Patrick"
	Patient_PatrickWessler.Profile.PatientInfo.PersonalInfo.FirstName = "Patrick"
	Patient_PatrickWessler.Profile.LastName = "Wessler"
	Patient_PatrickWessler.Profile.PatientInfo.PersonalInfo.LastName = "Wessler"
	Patient_PatrickWessler.Profile.DateOfBirth = 474940800000
	Patient_PatrickWessler.Profile.PatientInfo.PersonalInfo.DOB = 474940800000
	Patient_PatrickWessler.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(474940800000)
	Patient_PatrickWessler.Profile.PatientInfo.PatientNumber = 61 + 1
	Patient_PatrickWessler.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_PatrickWessler.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("X589695749")
	Patient_PatrickWessler.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 104940005
	Patient_PatrickWessler.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_PatrickWessler_EK_BY_S12_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_PatrickWessler,
		ContractId:     "EK_BY_S12_HZV",
		ChargeSystemId: "EK_BY_S12_HZV",
		IkNumber:       104940005,
		StartDate:      toInt64Pointer(1341100800000),
	}
	_ = PatientParticipation_PatrickWessler_EK_BY_S12_HZV

	gofakeit.Seed(780025833737)
	Patient_MonikaMann = data.FakePatient()
	Patient_MonikaMann.Profile.FirstName = "Monika"
	Patient_MonikaMann.Profile.PatientInfo.PersonalInfo.FirstName = "Monika"
	Patient_MonikaMann.Profile.LastName = "Mann"
	Patient_MonikaMann.Profile.PatientInfo.PersonalInfo.LastName = "Mann"
	Patient_MonikaMann.Profile.DateOfBirth = **********
	Patient_MonikaMann.Profile.PatientInfo.PersonalInfo.DOB = **********
	Patient_MonikaMann.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(**********)
	Patient_MonikaMann.Profile.PatientInfo.PatientNumber = 62 + 1
	Patient_MonikaMann.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_MonikaMann.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("N025833737")
	Patient_MonikaMann.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101570104
	Patient_MonikaMann.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_MonikaMann_EK_HB_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MonikaMann,
		ContractId:     "EK_HB_HZV",
		ChargeSystemId: "EK_HB_HZV",
		IkNumber:       101570104,
		StartDate:      toInt64Pointer(1356998400000),
		EndDate:        toInt64Pointer(1546214400000),
	}
	_ = PatientParticipation_MonikaMann_EK_HB_HZV

	gofakeit.Seed(870444397384)
	Patient_PeterPeters = data.FakePatient()
	Patient_PeterPeters.Profile.FirstName = "Peter"
	Patient_PeterPeters.Profile.PatientInfo.PersonalInfo.FirstName = "Peter"
	Patient_PeterPeters.Profile.LastName = "Peters"
	Patient_PeterPeters.Profile.PatientInfo.PersonalInfo.LastName = "Peters"
	Patient_PeterPeters.Profile.DateOfBirth = 804470400000
	Patient_PeterPeters.Profile.PatientInfo.PersonalInfo.DOB = 804470400000
	Patient_PeterPeters.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(804470400000)
	Patient_PeterPeters.Profile.PatientInfo.PatientNumber = 63 + 1
	Patient_PeterPeters.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_PeterPeters.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("W444397384")
	Patient_PeterPeters.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 104940005
	Patient_PeterPeters.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_PeterPeters_EK_HB_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_PeterPeters,
		ContractId:     "EK_HB_HZV",
		ChargeSystemId: "EK_HB_HZV",
		IkNumber:       104940005,
		StartDate:      toInt64Pointer(1356998400000),
	}
	_ = PatientParticipation_PeterPeters_EK_HB_HZV

	gofakeit.Seed(900608932981)
	Patient_FridaBunge = data.FakePatient()
	Patient_FridaBunge.Profile.FirstName = "Frida"
	Patient_FridaBunge.Profile.PatientInfo.PersonalInfo.FirstName = "Frida"
	Patient_FridaBunge.Profile.LastName = "Bunge"
	Patient_FridaBunge.Profile.PatientInfo.PersonalInfo.LastName = "Bunge"
	Patient_FridaBunge.Profile.DateOfBirth = -852768000000
	Patient_FridaBunge.Profile.PatientInfo.PersonalInfo.DOB = -852768000000
	Patient_FridaBunge.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-852768000000)
	Patient_FridaBunge.Profile.PatientInfo.PatientNumber = 64 + 1
	Patient_FridaBunge.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_FridaBunge.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("Z608932981")
	Patient_FridaBunge.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101560000
	Patient_FridaBunge.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_FridaBunge_EK_HB_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_FridaBunge,
		ContractId:     "EK_HB_HZV",
		ChargeSystemId: "EK_HB_HZV",
		IkNumber:       101560000,
		StartDate:      toInt64Pointer(1356998400000),
	}
	_ = PatientParticipation_FridaBunge_EK_HB_HZV

	gofakeit.Seed(650923815490)
	Patient_RuthPfaff = data.FakePatient()
	Patient_RuthPfaff.Profile.FirstName = "Ruth"
	Patient_RuthPfaff.Profile.PatientInfo.PersonalInfo.FirstName = "Ruth"
	Patient_RuthPfaff.Profile.LastName = "Pfaff"
	Patient_RuthPfaff.Profile.PatientInfo.PersonalInfo.LastName = "Pfaff"
	Patient_RuthPfaff.Profile.DateOfBirth = -992476800000
	Patient_RuthPfaff.Profile.PatientInfo.PersonalInfo.DOB = -992476800000
	Patient_RuthPfaff.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-992476800000)
	Patient_RuthPfaff.Profile.PatientInfo.PatientNumber = 65 + 1
	Patient_RuthPfaff.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_RuthPfaff.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("A923815490")
	Patient_RuthPfaff.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101570104
	Patient_RuthPfaff.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_RuthPfaff_EK_HH_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_RuthPfaff,
		ContractId:     "EK_HH_HZV",
		ChargeSystemId: "EK_HH_HZV",
		IkNumber:       101570104,
		StartDate:      toInt64Pointer(1420070400000),
		EndDate:        toInt64Pointer(1546214400000),
	}
	_ = PatientParticipation_RuthPfaff_EK_HH_HZV

	gofakeit.Seed(790482332806)
	Patient_SandraSchaaf = data.FakePatient()
	Patient_SandraSchaaf.Profile.FirstName = "Sandra"
	Patient_SandraSchaaf.Profile.PatientInfo.PersonalInfo.FirstName = "Sandra"
	Patient_SandraSchaaf.Profile.LastName = "Schaaf"
	Patient_SandraSchaaf.Profile.PatientInfo.PersonalInfo.LastName = "Schaaf"
	Patient_SandraSchaaf.Profile.DateOfBirth = 114912000000
	Patient_SandraSchaaf.Profile.PatientInfo.PersonalInfo.DOB = 114912000000
	Patient_SandraSchaaf.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(114912000000)
	Patient_SandraSchaaf.Profile.PatientInfo.PatientNumber = 66 + 1
	Patient_SandraSchaaf.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_SandraSchaaf.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("O482332806")
	Patient_SandraSchaaf.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 104940005
	Patient_SandraSchaaf.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_SandraSchaaf_EK_HH_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_SandraSchaaf,
		ContractId:     "EK_HH_HZV",
		ChargeSystemId: "EK_HH_HZV",
		IkNumber:       104940005,
		StartDate:      toInt64Pointer(1420070400000),
	}
	_ = PatientParticipation_SandraSchaaf_EK_HH_HZV

	gofakeit.Seed(850901114575)
	Patient_MarvinJostes = data.FakePatient()
	Patient_MarvinJostes.Profile.FirstName = "Marvin"
	Patient_MarvinJostes.Profile.PatientInfo.PersonalInfo.FirstName = "Marvin"
	Patient_MarvinJostes.Profile.LastName = "Jostes"
	Patient_MarvinJostes.Profile.PatientInfo.PersonalInfo.LastName = "Jostes"
	Patient_MarvinJostes.Profile.DateOfBirth = 790300800000
	Patient_MarvinJostes.Profile.PatientInfo.PersonalInfo.DOB = 790300800000
	Patient_MarvinJostes.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(790300800000)
	Patient_MarvinJostes.Profile.PatientInfo.PatientNumber = 67 + 1
	Patient_MarvinJostes.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_MarvinJostes.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("U901114575")
	Patient_MarvinJostes.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 105830016
	Patient_MarvinJostes.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_MarvinJostes_EK_HH_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MarvinJostes,
		ContractId:     "EK_HH_HZV",
		ChargeSystemId: "EK_HH_HZV",
		IkNumber:       105830016,
		StartDate:      toInt64Pointer(1420070400000),
	}
	_ = PatientParticipation_MarvinJostes_EK_HH_HZV

	gofakeit.Seed(690195754220)
	Patient_TomMarkwardt = data.FakePatient()
	Patient_TomMarkwardt.Profile.FirstName = "Tom"
	Patient_TomMarkwardt.Profile.PatientInfo.PersonalInfo.FirstName = "Tom"
	Patient_TomMarkwardt.Profile.LastName = "Markwardt"
	Patient_TomMarkwardt.Profile.PatientInfo.PersonalInfo.LastName = "Markwardt"
	Patient_TomMarkwardt.Profile.DateOfBirth = 1230768000000
	Patient_TomMarkwardt.Profile.PatientInfo.PersonalInfo.DOB = 1230768000000
	Patient_TomMarkwardt.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(1230768000000)
	Patient_TomMarkwardt.Profile.PatientInfo.PatientNumber = 68 + 1
	Patient_TomMarkwardt.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_TomMarkwardt.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("E195754220")
	Patient_TomMarkwardt.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101560000
	Patient_TomMarkwardt.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_TomMarkwardt_EK_NO_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_TomMarkwardt,
		ContractId:     "EK_NO_HZV",
		ChargeSystemId: "EK_NO_HZV",
		IkNumber:       101560000,
		StartDate:      toInt64Pointer(1349049600000),
	}
	_ = PatientParticipation_TomMarkwardt_EK_NO_HZV

	gofakeit.Seed(790047614050)
	Patient_BrigitteHelmer = data.FakePatient()
	Patient_BrigitteHelmer.Profile.FirstName = "Brigitte"
	Patient_BrigitteHelmer.Profile.PatientInfo.PersonalInfo.FirstName = "Brigitte"
	Patient_BrigitteHelmer.Profile.LastName = "Helmer"
	Patient_BrigitteHelmer.Profile.PatientInfo.PersonalInfo.LastName = "Helmer"
	Patient_BrigitteHelmer.Profile.DateOfBirth = -189475200000
	Patient_BrigitteHelmer.Profile.PatientInfo.PersonalInfo.DOB = -189475200000
	Patient_BrigitteHelmer.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-189475200000)
	Patient_BrigitteHelmer.Profile.PatientInfo.PatientNumber = 69 + 1
	Patient_BrigitteHelmer.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_BrigitteHelmer.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("O047614050")
	Patient_BrigitteHelmer.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101570104
	Patient_BrigitteHelmer.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_BrigitteHelmer_EK_NO_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_BrigitteHelmer,
		ContractId:     "EK_NO_HZV",
		ChargeSystemId: "EK_NO_HZV",
		IkNumber:       101570104,
		StartDate:      toInt64Pointer(1349049600000),
		EndDate:        toInt64Pointer(1546214400000),
	}
	_ = PatientParticipation_BrigitteHelmer_EK_NO_HZV

	gofakeit.Seed(800598591818)
	Patient_FranzFachinger = data.FakePatient()
	Patient_FranzFachinger.Profile.FirstName = "Franz"
	Patient_FranzFachinger.Profile.PatientInfo.PersonalInfo.FirstName = "Franz"
	Patient_FranzFachinger.Profile.LastName = "Fachinger"
	Patient_FranzFachinger.Profile.PatientInfo.PersonalInfo.LastName = "Fachinger"
	Patient_FranzFachinger.Profile.DateOfBirth = 110332800000
	Patient_FranzFachinger.Profile.PatientInfo.PersonalInfo.DOB = 110332800000
	Patient_FranzFachinger.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(110332800000)
	Patient_FranzFachinger.Profile.PatientInfo.PatientNumber = 70 + 1
	Patient_FranzFachinger.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_FranzFachinger.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("P598591818")
	Patient_FranzFachinger.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101570104
	Patient_FranzFachinger.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_FranzFachinger_EK_NO_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_FranzFachinger,
		ContractId:     "EK_NO_HZV",
		ChargeSystemId: "EK_NO_HZV",
		IkNumber:       101570104,
		StartDate:      toInt64Pointer(1356998400000),
		EndDate:        toInt64Pointer(1546214400000),
	}
	_ = PatientParticipation_FranzFachinger_EK_NO_HZV

	gofakeit.Seed(650711021475)
	Patient_IngeborgTutte = data.FakePatient()
	Patient_IngeborgTutte.Profile.FirstName = "Ingeborg"
	Patient_IngeborgTutte.Profile.PatientInfo.PersonalInfo.FirstName = "Ingeborg"
	Patient_IngeborgTutte.Profile.LastName = "Tutte"
	Patient_IngeborgTutte.Profile.PatientInfo.PersonalInfo.LastName = "Tutte"
	Patient_IngeborgTutte.Profile.DateOfBirth = -936230400000
	Patient_IngeborgTutte.Profile.PatientInfo.PersonalInfo.DOB = -936230400000
	Patient_IngeborgTutte.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-936230400000)
	Patient_IngeborgTutte.Profile.PatientInfo.PatientNumber = 71 + 1
	Patient_IngeborgTutte.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_IngeborgTutte.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("A711021475")
	Patient_IngeborgTutte.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101570104
	Patient_IngeborgTutte.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_IngeborgTutte_EK_RLP_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_IngeborgTutte,
		ContractId:     "EK_RLP_HZV",
		ChargeSystemId: "EK_RLP_HZV",
		IkNumber:       101570104,
		StartDate:      toInt64Pointer(1435708800000),
		EndDate:        toInt64Pointer(1546214400000),
	}
	_ = PatientParticipation_IngeborgTutte_EK_RLP_HZV

	gofakeit.Seed(710811881458)
	Patient_GabrielArnesen = data.FakePatient()
	Patient_GabrielArnesen.Profile.FirstName = "Gabriel"
	Patient_GabrielArnesen.Profile.PatientInfo.PersonalInfo.FirstName = "Gabriel"
	Patient_GabrielArnesen.Profile.LastName = "Arnesen"
	Patient_GabrielArnesen.Profile.PatientInfo.PersonalInfo.LastName = "Arnesen"
	Patient_GabrielArnesen.Profile.DateOfBirth = 405388800000
	Patient_GabrielArnesen.Profile.PatientInfo.PersonalInfo.DOB = 405388800000
	Patient_GabrielArnesen.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(405388800000)
	Patient_GabrielArnesen.Profile.PatientInfo.PatientNumber = 72 + 1
	Patient_GabrielArnesen.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_GabrielArnesen.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("G811881458")
	Patient_GabrielArnesen.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 103170002
	Patient_GabrielArnesen.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_GabrielArnesen_EK_RLP_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_GabrielArnesen,
		ContractId:     "EK_RLP_HZV",
		ChargeSystemId: "EK_RLP_HZV",
		IkNumber:       103170002,
		StartDate:      toInt64Pointer(1435708800000),
		EndDate:        toInt64Pointer(1546214400000),
	}
	_ = PatientParticipation_GabrielArnesen_EK_RLP_HZV

	gofakeit.Seed(820966329546)
	Patient_TimoSchmidt = data.FakePatient()
	Patient_TimoSchmidt.Profile.FirstName = "Timo"
	Patient_TimoSchmidt.Profile.PatientInfo.PersonalInfo.FirstName = "Timo"
	Patient_TimoSchmidt.Profile.LastName = "Schmidt"
	Patient_TimoSchmidt.Profile.PatientInfo.PersonalInfo.LastName = "Schmidt"
	Patient_TimoSchmidt.Profile.DateOfBirth = 850435200000
	Patient_TimoSchmidt.Profile.PatientInfo.PersonalInfo.DOB = 850435200000
	Patient_TimoSchmidt.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(850435200000)
	Patient_TimoSchmidt.Profile.PatientInfo.PatientNumber = 73 + 1
	Patient_TimoSchmidt.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_TimoSchmidt.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("R966329546")
	Patient_TimoSchmidt.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 104940005
	Patient_TimoSchmidt.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_TimoSchmidt_EK_RLP_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_TimoSchmidt,
		ContractId:     "EK_RLP_HZV",
		ChargeSystemId: "EK_RLP_HZV",
		IkNumber:       104940005,
		StartDate:      toInt64Pointer(1435708800000),
	}
	_ = PatientParticipation_TimoSchmidt_EK_RLP_HZV

	gofakeit.Seed(740192218789)
	Patient_ErwinPoth = data.FakePatient()
	Patient_ErwinPoth.Profile.FirstName = "Erwin"
	Patient_ErwinPoth.Profile.PatientInfo.PersonalInfo.FirstName = "Erwin"
	Patient_ErwinPoth.Profile.LastName = "Poth"
	Patient_ErwinPoth.Profile.PatientInfo.PersonalInfo.LastName = "Poth"
	Patient_ErwinPoth.Profile.DateOfBirth = -1018569600000
	Patient_ErwinPoth.Profile.PatientInfo.PersonalInfo.DOB = -1018569600000
	Patient_ErwinPoth.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-1018569600000)
	Patient_ErwinPoth.Profile.PatientInfo.PatientNumber = 74 + 1
	Patient_ErwinPoth.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_ErwinPoth.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("J192218789")
	Patient_ErwinPoth.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101570104
	Patient_ErwinPoth.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_ErwinPoth_EK_SH_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_ErwinPoth,
		ContractId:     "EK_SH_HZV",
		ChargeSystemId: "EK_SH_HZV",
		IkNumber:       101570104,
		StartDate:      toInt64Pointer(1420070400000),
		EndDate:        toInt64Pointer(1546214400000),
	}
	_ = PatientParticipation_ErwinPoth_EK_SH_HZV

	gofakeit.Seed(760823366702)
	Patient_MaximilianBlasberg = data.FakePatient()
	Patient_MaximilianBlasberg.Profile.FirstName = "Maximilian"
	Patient_MaximilianBlasberg.Profile.PatientInfo.PersonalInfo.FirstName = "Maximilian"
	Patient_MaximilianBlasberg.Profile.LastName = "Blasberg"
	Patient_MaximilianBlasberg.Profile.PatientInfo.PersonalInfo.LastName = "Blasberg"
	Patient_MaximilianBlasberg.Profile.DateOfBirth = 816566400000
	Patient_MaximilianBlasberg.Profile.PatientInfo.PersonalInfo.DOB = 816566400000
	Patient_MaximilianBlasberg.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(816566400000)
	Patient_MaximilianBlasberg.Profile.PatientInfo.PatientNumber = 75 + 1
	Patient_MaximilianBlasberg.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_MaximilianBlasberg.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("L823366702")
	Patient_MaximilianBlasberg.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 105830016
	Patient_MaximilianBlasberg.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_MaximilianBlasberg_EK_SH_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MaximilianBlasberg,
		ContractId:     "EK_SH_HZV",
		ChargeSystemId: "EK_SH_HZV",
		IkNumber:       105830016,
		StartDate:      toInt64Pointer(1420070400000),
	}
	_ = PatientParticipation_MaximilianBlasberg_EK_SH_HZV

	gofakeit.Seed(820097851554)
	Patient_SonjaGlock = data.FakePatient()
	Patient_SonjaGlock.Profile.FirstName = "Sonja"
	Patient_SonjaGlock.Profile.PatientInfo.PersonalInfo.FirstName = "Sonja"
	Patient_SonjaGlock.Profile.LastName = "Glock"
	Patient_SonjaGlock.Profile.PatientInfo.PersonalInfo.LastName = "Glock"
	Patient_SonjaGlock.Profile.DateOfBirth = 185155200000
	Patient_SonjaGlock.Profile.PatientInfo.PersonalInfo.DOB = 185155200000
	Patient_SonjaGlock.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(185155200000)
	Patient_SonjaGlock.Profile.PatientInfo.PatientNumber = 76 + 1
	Patient_SonjaGlock.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_SonjaGlock.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("R097851554")
	Patient_SonjaGlock.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 104940005
	Patient_SonjaGlock.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_SonjaGlock_EK_SH_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_SonjaGlock,
		ContractId:     "EK_SH_HZV",
		ChargeSystemId: "EK_SH_HZV",
		IkNumber:       104940005,
		StartDate:      toInt64Pointer(1420070400000),
	}
	_ = PatientParticipation_SonjaGlock_EK_SH_HZV

	gofakeit.Seed(850592122972)
	Patient_JosefReitmann = data.FakePatient()
	Patient_JosefReitmann.Profile.FirstName = "Josef"
	Patient_JosefReitmann.Profile.PatientInfo.PersonalInfo.FirstName = "Josef"
	Patient_JosefReitmann.Profile.LastName = "Reitmann"
	Patient_JosefReitmann.Profile.PatientInfo.PersonalInfo.LastName = "Reitmann"
	Patient_JosefReitmann.Profile.DateOfBirth = -599097600000
	Patient_JosefReitmann.Profile.PatientInfo.PersonalInfo.DOB = -599097600000
	Patient_JosefReitmann.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-599097600000)
	Patient_JosefReitmann.Profile.PatientInfo.PatientNumber = 77 + 1
	Patient_JosefReitmann.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_JosefReitmann.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("U592122972")
	Patient_JosefReitmann.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101560000
	Patient_JosefReitmann.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_JosefReitmann_EK_SL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_JosefReitmann,
		ContractId:     "EK_SL_HZV",
		ChargeSystemId: "EK_SL_HZV",
		IkNumber:       101560000,
		StartDate:      toInt64Pointer(1459468800000),
	}
	_ = PatientParticipation_JosefReitmann_EK_SL_HZV

	gofakeit.Seed(860409206501)
	Patient_SilviaSenser = data.FakePatient()
	Patient_SilviaSenser.Profile.FirstName = "Silvia"
	Patient_SilviaSenser.Profile.PatientInfo.PersonalInfo.FirstName = "Silvia"
	Patient_SilviaSenser.Profile.LastName = "Senser"
	Patient_SilviaSenser.Profile.PatientInfo.PersonalInfo.LastName = "Senser"
	Patient_SilviaSenser.Profile.DateOfBirth = 495763200000
	Patient_SilviaSenser.Profile.PatientInfo.PersonalInfo.DOB = 495763200000
	Patient_SilviaSenser.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(495763200000)
	Patient_SilviaSenser.Profile.PatientInfo.PatientNumber = 78 + 1
	Patient_SilviaSenser.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_SilviaSenser.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("V409206501")
	Patient_SilviaSenser.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101570104
	Patient_SilviaSenser.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_SilviaSenser_EK_SL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_SilviaSenser,
		ContractId:     "EK_SL_HZV",
		ChargeSystemId: "EK_SL_HZV",
		IkNumber:       101570104,
		StartDate:      toInt64Pointer(1459468800000),
		EndDate:        toInt64Pointer(1546214400000),
	}
	_ = PatientParticipation_SilviaSenser_EK_SL_HZV

	gofakeit.Seed(770304970240)
	Patient_FelixJohnen = data.FakePatient()
	Patient_FelixJohnen.Profile.FirstName = "Felix"
	Patient_FelixJohnen.Profile.PatientInfo.PersonalInfo.FirstName = "Felix"
	Patient_FelixJohnen.Profile.LastName = "Johnen"
	Patient_FelixJohnen.Profile.PatientInfo.PersonalInfo.LastName = "Johnen"
	Patient_FelixJohnen.Profile.DateOfBirth = 936835200000
	Patient_FelixJohnen.Profile.PatientInfo.PersonalInfo.DOB = 936835200000
	Patient_FelixJohnen.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(936835200000)
	Patient_FelixJohnen.Profile.PatientInfo.PatientNumber = 79 + 1
	Patient_FelixJohnen.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_FelixJohnen.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("M304970240")
	Patient_FelixJohnen.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 105830016
	Patient_FelixJohnen.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_FelixJohnen_EK_SL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_FelixJohnen,
		ContractId:     "EK_SL_HZV",
		ChargeSystemId: "EK_SL_HZV",
		IkNumber:       105830016,
		StartDate:      toInt64Pointer(1459468800000),
	}
	_ = PatientParticipation_FelixJohnen_EK_SL_HZV

	gofakeit.Seed(680948058791)
	Patient_ManfredStocker = data.FakePatient()
	Patient_ManfredStocker.Profile.FirstName = "Manfred"
	Patient_ManfredStocker.Profile.PatientInfo.PersonalInfo.FirstName = "Manfred"
	Patient_ManfredStocker.Profile.LastName = "Stocker"
	Patient_ManfredStocker.Profile.PatientInfo.PersonalInfo.LastName = "Stocker"
	Patient_ManfredStocker.Profile.DateOfBirth = **********
	Patient_ManfredStocker.Profile.PatientInfo.PersonalInfo.DOB = **********
	Patient_ManfredStocker.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(**********)
	Patient_ManfredStocker.Profile.PatientInfo.PatientNumber = 80 + 1
	Patient_ManfredStocker.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_ManfredStocker.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("D948058791")
	Patient_ManfredStocker.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101570104
	Patient_ManfredStocker.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_ManfredStocker_EK_SN_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_ManfredStocker,
		ContractId:     "EK_SN_HZV",
		ChargeSystemId: "EK_SN_HZV",
		IkNumber:       101570104,
		StartDate:      toInt64Pointer(1396310400000),
		EndDate:        toInt64Pointer(1546214400000),
	}
	_ = PatientParticipation_ManfredStocker_EK_SN_HZV

	gofakeit.Seed(690391376380)
	Patient_ClaudiaBaumgarten = data.FakePatient()
	Patient_ClaudiaBaumgarten.Profile.FirstName = "Claudia"
	Patient_ClaudiaBaumgarten.Profile.PatientInfo.PersonalInfo.FirstName = "Claudia"
	Patient_ClaudiaBaumgarten.Profile.LastName = "Baumgarten"
	Patient_ClaudiaBaumgarten.Profile.PatientInfo.PersonalInfo.LastName = "Baumgarten"
	Patient_ClaudiaBaumgarten.Profile.DateOfBirth = -279331200000
	Patient_ClaudiaBaumgarten.Profile.PatientInfo.PersonalInfo.DOB = -279331200000
	Patient_ClaudiaBaumgarten.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-279331200000)
	Patient_ClaudiaBaumgarten.Profile.PatientInfo.PatientNumber = 81 + 1
	Patient_ClaudiaBaumgarten.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_ClaudiaBaumgarten.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("E391376380")
	Patient_ClaudiaBaumgarten.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101560000
	Patient_ClaudiaBaumgarten.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_ClaudiaBaumgarten_EK_SN_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_ClaudiaBaumgarten,
		ContractId:     "EK_SN_HZV",
		ChargeSystemId: "EK_SN_HZV",
		IkNumber:       101560000,
		StartDate:      toInt64Pointer(1396310400000),
	}
	_ = PatientParticipation_ClaudiaBaumgarten_EK_SN_HZV

	gofakeit.Seed(850281094672)
	Patient_CarmenWetter = data.FakePatient()
	Patient_CarmenWetter.Profile.FirstName = "Carmen"
	Patient_CarmenWetter.Profile.PatientInfo.PersonalInfo.FirstName = "Carmen"
	Patient_CarmenWetter.Profile.LastName = "Wetter"
	Patient_CarmenWetter.Profile.PatientInfo.PersonalInfo.LastName = "Wetter"
	Patient_CarmenWetter.Profile.DateOfBirth = 206409600000
	Patient_CarmenWetter.Profile.PatientInfo.PersonalInfo.DOB = 206409600000
	Patient_CarmenWetter.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(206409600000)
	Patient_CarmenWetter.Profile.PatientInfo.PatientNumber = 82 + 1
	Patient_CarmenWetter.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_CarmenWetter.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("U281094672")
	Patient_CarmenWetter.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 105830016
	Patient_CarmenWetter.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_CarmenWetter_EK_SN_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_CarmenWetter,
		ContractId:     "EK_SN_HZV",
		ChargeSystemId: "EK_SN_HZV",
		IkNumber:       105830016,
		StartDate:      toInt64Pointer(1396310400000),
	}
	_ = PatientParticipation_CarmenWetter_EK_SN_HZV

	gofakeit.Seed(670206810293)
	Patient_FranzMonk = data.FakePatient()
	Patient_FranzMonk.Profile.FirstName = "Franz"
	Patient_FranzMonk.Profile.PatientInfo.PersonalInfo.FirstName = "Franz"
	Patient_FranzMonk.Profile.LastName = "Monk"
	Patient_FranzMonk.Profile.PatientInfo.PersonalInfo.LastName = "Monk"
	Patient_FranzMonk.Profile.DateOfBirth = 110332800000
	Patient_FranzMonk.Profile.PatientInfo.PersonalInfo.DOB = 110332800000
	Patient_FranzMonk.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(110332800000)
	Patient_FranzMonk.Profile.PatientInfo.PatientNumber = 83 + 1
	Patient_FranzMonk.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_FranzMonk.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("C206810293")
	Patient_FranzMonk.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101570104
	Patient_FranzMonk.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_FranzMonk_EK_WL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_FranzMonk,
		ContractId:     "EK_WL_HZV",
		ChargeSystemId: "EK_WL_HZV",
		IkNumber:       101570104,
		StartDate:      toInt64Pointer(1349049600000),
		EndDate:        toInt64Pointer(1546214400000),
	}
	_ = PatientParticipation_FranzMonk_EK_WL_HZV

	gofakeit.Seed(690653597158)
	Patient_GudrunHelmer = data.FakePatient()
	Patient_GudrunHelmer.Profile.FirstName = "Gudrun"
	Patient_GudrunHelmer.Profile.PatientInfo.PersonalInfo.FirstName = "Gudrun"
	Patient_GudrunHelmer.Profile.LastName = "Helmer"
	Patient_GudrunHelmer.Profile.PatientInfo.PersonalInfo.LastName = "Helmer"
	Patient_GudrunHelmer.Profile.DateOfBirth = -505008000000
	Patient_GudrunHelmer.Profile.PatientInfo.PersonalInfo.DOB = -505008000000
	Patient_GudrunHelmer.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-505008000000)
	Patient_GudrunHelmer.Profile.PatientInfo.PatientNumber = 84 + 1
	Patient_GudrunHelmer.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_GudrunHelmer.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("E653597158")
	Patient_GudrunHelmer.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101560000
	Patient_GudrunHelmer.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_GudrunHelmer_EK_WL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_GudrunHelmer,
		ContractId:     "EK_WL_HZV",
		ChargeSystemId: "EK_WL_HZV",
		IkNumber:       101560000,
		StartDate:      toInt64Pointer(1349049600000),
	}
	_ = PatientParticipation_GudrunHelmer_EK_WL_HZV

	gofakeit.Seed(850255873329)
	Patient_KlausMarkwardt = data.FakePatient()
	Patient_KlausMarkwardt.Profile.FirstName = "Klaus"
	Patient_KlausMarkwardt.Profile.PatientInfo.PersonalInfo.FirstName = "Klaus"
	Patient_KlausMarkwardt.Profile.LastName = "Markwardt"
	Patient_KlausMarkwardt.Profile.PatientInfo.PersonalInfo.LastName = "Markwardt"
	Patient_KlausMarkwardt.Profile.DateOfBirth = 1233273600000
	Patient_KlausMarkwardt.Profile.PatientInfo.PersonalInfo.DOB = 1233273600000
	Patient_KlausMarkwardt.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(1233273600000)
	Patient_KlausMarkwardt.Profile.PatientInfo.PatientNumber = 85 + 1
	Patient_KlausMarkwardt.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_KlausMarkwardt.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("U255873329")
	Patient_KlausMarkwardt.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101570104
	Patient_KlausMarkwardt.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_KlausMarkwardt_EK_WL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_KlausMarkwardt,
		ContractId:     "EK_WL_HZV",
		ChargeSystemId: "EK_WL_HZV",
		IkNumber:       101570104,
		StartDate:      toInt64Pointer(1349049600000),
		EndDate:        toInt64Pointer(1546214400000),
	}
	_ = PatientParticipation_KlausMarkwardt_EK_WL_HZV

	gofakeit.Seed(680902210713)
	Patient_AxelRichter = data.FakePatient()
	Patient_AxelRichter.Profile.FirstName = "Axel"
	Patient_AxelRichter.Profile.PatientInfo.PersonalInfo.FirstName = "Axel"
	Patient_AxelRichter.Profile.LastName = "Richter"
	Patient_AxelRichter.Profile.PatientInfo.PersonalInfo.LastName = "Richter"
	Patient_AxelRichter.Profile.DateOfBirth = -48643200000
	Patient_AxelRichter.Profile.PatientInfo.PersonalInfo.DOB = -48643200000
	Patient_AxelRichter.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-48643200000)
	Patient_AxelRichter.Profile.PatientInfo.PatientNumber = 86 + 1
	Patient_AxelRichter.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_AxelRichter.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("D902210713")
	Patient_AxelRichter.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 103170002
	Patient_AxelRichter.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_AxelRichter_HKK_HZV_NORD = &PatientParticipationWithoutDoctor{
		Patient:        Patient_AxelRichter,
		ContractId:     "HKK_HZV_NORD",
		ChargeSystemId: "HKK_HZV_NORD",
		IkNumber:       103170002,
		StartDate:      toInt64Pointer(1412121600000),
	}
	_ = PatientParticipation_AxelRichter_HKK_HZV_NORD

	gofakeit.Seed(730202173073)
	Patient_SvenjaKrug = data.FakePatient()
	Patient_SvenjaKrug.Profile.FirstName = "Svenja"
	Patient_SvenjaKrug.Profile.PatientInfo.PersonalInfo.FirstName = "Svenja"
	Patient_SvenjaKrug.Profile.LastName = "Krug"
	Patient_SvenjaKrug.Profile.PatientInfo.PersonalInfo.LastName = "Krug"
	Patient_SvenjaKrug.Profile.DateOfBirth = 595728000000
	Patient_SvenjaKrug.Profile.PatientInfo.PersonalInfo.DOB = 595728000000
	Patient_SvenjaKrug.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(595728000000)
	Patient_SvenjaKrug.Profile.PatientInfo.PatientNumber = 87 + 1
	Patient_SvenjaKrug.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_SvenjaKrug.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("I202173073")
	Patient_SvenjaKrug.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 103170002
	Patient_SvenjaKrug.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_SvenjaKrug_HKK_HZV_NORD = &PatientParticipationWithoutDoctor{
		Patient:        Patient_SvenjaKrug,
		ContractId:     "HKK_HZV_NORD",
		ChargeSystemId: "HKK_HZV_NORD",
		IkNumber:       103170002,
		StartDate:      toInt64Pointer(1412121600000),
	}
	_ = PatientParticipation_SvenjaKrug_HKK_HZV_NORD

	gofakeit.Seed(800158702940)
	Patient_ErnstNomer = data.FakePatient()
	Patient_ErnstNomer.Profile.FirstName = "Ernst"
	Patient_ErnstNomer.Profile.PatientInfo.PersonalInfo.FirstName = "Ernst"
	Patient_ErnstNomer.Profile.LastName = "Nomer"
	Patient_ErnstNomer.Profile.PatientInfo.PersonalInfo.LastName = "Nomer"
	Patient_ErnstNomer.Profile.DateOfBirth = -821145600000
	Patient_ErnstNomer.Profile.PatientInfo.PersonalInfo.DOB = -821145600000
	Patient_ErnstNomer.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-821145600000)
	Patient_ErnstNomer.Profile.PatientInfo.PatientNumber = 88 + 1
	Patient_ErnstNomer.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_ErnstNomer.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("P158702940")
	Patient_ErnstNomer.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 103170002
	Patient_ErnstNomer.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_ErnstNomer_HKK_HZV_NORD = &PatientParticipationWithoutDoctor{
		Patient:        Patient_ErnstNomer,
		ContractId:     "HKK_HZV_NORD",
		ChargeSystemId: "HKK_HZV_NORD",
		IkNumber:       103170002,
		StartDate:      toInt64Pointer(1412121600000),
	}
	_ = PatientParticipation_ErnstNomer_HKK_HZV_NORD

	gofakeit.Seed(660565235726)
	Patient_HansMustermann = data.FakePatient()
	Patient_HansMustermann.Profile.FirstName = "Hans"
	Patient_HansMustermann.Profile.PatientInfo.PersonalInfo.FirstName = "Hans"
	Patient_HansMustermann.Profile.LastName = "Mustermann"
	Patient_HansMustermann.Profile.PatientInfo.PersonalInfo.LastName = "Mustermann"
	Patient_HansMustermann.Profile.DateOfBirth = -103075200000
	Patient_HansMustermann.Profile.PatientInfo.PersonalInfo.DOB = -103075200000
	Patient_HansMustermann.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-103075200000)
	Patient_HansMustermann.Profile.PatientInfo.PatientNumber = 89 + 1
	Patient_HansMustermann.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_HansMustermann.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("B565235726")
	Patient_HansMustermann.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108001963
	Patient_HansMustermann.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_HansMustermann_IKK_CL_BW_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_HansMustermann,
		ContractId:     "IKK_CL_BW_HZV",
		ChargeSystemId: "IKK_CL_BW_HZV",
		IkNumber:       108001963,
		StartDate:      toInt64Pointer(1277942400000),
	}
	_ = PatientParticipation_HansMustermann_IKK_CL_BW_HZV

	gofakeit.Seed(760020021256)
	Patient_MaxMustermann = data.FakePatient()
	Patient_MaxMustermann.Profile.FirstName = "Max"
	Patient_MaxMustermann.Profile.PatientInfo.PersonalInfo.FirstName = "Max"
	Patient_MaxMustermann.Profile.LastName = "Mustermann"
	Patient_MaxMustermann.Profile.PatientInfo.PersonalInfo.LastName = "Mustermann"
	Patient_MaxMustermann.Profile.DateOfBirth = 309744000000
	Patient_MaxMustermann.Profile.PatientInfo.PersonalInfo.DOB = 309744000000
	Patient_MaxMustermann.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(309744000000)
	Patient_MaxMustermann.Profile.PatientInfo.PatientNumber = 90 + 1
	Patient_MaxMustermann.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_MaxMustermann.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("L020021256")
	Patient_MaxMustermann.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108001963
	Patient_MaxMustermann.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_MaxMustermann_IKK_CL_BW_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MaxMustermann,
		ContractId:     "IKK_CL_BW_HZV",
		ChargeSystemId: "IKK_CL_BW_HZV",
		IkNumber:       108001963,
		StartDate:      toInt64Pointer(1277942400000),
	}
	_ = PatientParticipation_MaxMustermann_IKK_CL_BW_HZV

	gofakeit.Seed(740118523280)
	Patient_AnnemarieAdam = data.FakePatient()
	Patient_AnnemarieAdam.Profile.FirstName = "Annemarie"
	Patient_AnnemarieAdam.Profile.PatientInfo.PersonalInfo.FirstName = "Annemarie"
	Patient_AnnemarieAdam.Profile.LastName = "Adam"
	Patient_AnnemarieAdam.Profile.PatientInfo.PersonalInfo.LastName = "Adam"
	Patient_AnnemarieAdam.Profile.DateOfBirth = 31449600000
	Patient_AnnemarieAdam.Profile.PatientInfo.PersonalInfo.DOB = 31449600000
	Patient_AnnemarieAdam.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(31449600000)
	Patient_AnnemarieAdam.Profile.PatientInfo.PatientNumber = 91 + 1
	Patient_AnnemarieAdam.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_AnnemarieAdam.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("J118523280")
	Patient_AnnemarieAdam.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108008880
	Patient_AnnemarieAdam.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_AnnemarieAdam_LKK_BW_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_AnnemarieAdam,
		ContractId:     "LKK_BW_HZV",
		ChargeSystemId: "LKK_BW_HZV",
		IkNumber:       108008880,
		StartDate:      toInt64Pointer(1325376000000),
	}
	_ = PatientParticipation_AnnemarieAdam_LKK_BW_HZV

	gofakeit.Seed(660606974683)
	Patient_PeterBürger = data.FakePatient()
	Patient_PeterBürger.Profile.FirstName = "Peter"
	Patient_PeterBürger.Profile.PatientInfo.PersonalInfo.FirstName = "Peter"
	Patient_PeterBürger.Profile.LastName = "Bürger"
	Patient_PeterBürger.Profile.PatientInfo.PersonalInfo.LastName = "Bürger"
	Patient_PeterBürger.Profile.DateOfBirth = 112233600000
	Patient_PeterBürger.Profile.PatientInfo.PersonalInfo.DOB = 112233600000
	Patient_PeterBürger.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(112233600000)
	Patient_PeterBürger.Profile.PatientInfo.PatientNumber = 92 + 1
	Patient_PeterBürger.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_PeterBürger.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("B606974683")
	Patient_PeterBürger.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108608820
	Patient_PeterBürger.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_PeterBürger_LKK_BY_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_PeterBürger,
		ContractId:     "LKK_BY_HZV",
		ChargeSystemId: "LKK_BY_HZV",
		IkNumber:       108608820,
		StartDate:      toInt64Pointer(1270080000000),
	}
	_ = PatientParticipation_PeterBürger_LKK_BY_HZV

	gofakeit.Seed(670443760723)
	Patient_MandyZimmer = data.FakePatient()
	Patient_MandyZimmer.Profile.FirstName = "Mandy"
	Patient_MandyZimmer.Profile.PatientInfo.PersonalInfo.FirstName = "Mandy"
	Patient_MandyZimmer.Profile.LastName = "Zimmer"
	Patient_MandyZimmer.Profile.PatientInfo.PersonalInfo.LastName = "Zimmer"
	Patient_MandyZimmer.Profile.DateOfBirth = -191289600000
	Patient_MandyZimmer.Profile.PatientInfo.PersonalInfo.DOB = -191289600000
	Patient_MandyZimmer.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-191289600000)
	Patient_MandyZimmer.Profile.PatientInfo.PatientNumber = 93 + 1
	Patient_MandyZimmer.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_MandyZimmer.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("C443760723")
	Patient_MandyZimmer.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 109008837
	Patient_MandyZimmer.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_MandyZimmer_LKK_BY_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MandyZimmer,
		ContractId:     "LKK_BY_HZV",
		ChargeSystemId: "LKK_BY_HZV",
		IkNumber:       109008837,
		StartDate:      toInt64Pointer(1270080000000),
	}
	_ = PatientParticipation_MandyZimmer_LKK_BY_HZV

	gofakeit.Seed(800242868727)
	Patient_LucasMöller = data.FakePatient()
	Patient_LucasMöller.Profile.FirstName = "Lucas"
	Patient_LucasMöller.Profile.PatientInfo.PersonalInfo.FirstName = "Lucas"
	Patient_LucasMöller.Profile.LastName = "Möller"
	Patient_LucasMöller.Profile.PatientInfo.PersonalInfo.LastName = "Möller"
	Patient_LucasMöller.Profile.DateOfBirth = 1216857600000
	Patient_LucasMöller.Profile.PatientInfo.PersonalInfo.DOB = 1216857600000
	Patient_LucasMöller.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(1216857600000)
	Patient_LucasMöller.Profile.PatientInfo.PatientNumber = 94 + 1
	Patient_LucasMöller.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_LucasMöller.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("P242868727")
	Patient_LucasMöller.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 109008837
	Patient_LucasMöller.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_LucasMöller_LKK_BY_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_LucasMöller,
		ContractId:     "LKK_BY_HZV",
		ChargeSystemId: "LKK_BY_HZV",
		IkNumber:       109008837,
		StartDate:      toInt64Pointer(1270080000000),
	}
	_ = PatientParticipation_LucasMöller_LKK_BY_HZV

	gofakeit.Seed(680998031567)
	Patient_ReneDankert = data.FakePatient()
	Patient_ReneDankert.Profile.FirstName = "Rene"
	Patient_ReneDankert.Profile.PatientInfo.PersonalInfo.FirstName = "Rene"
	Patient_ReneDankert.Profile.LastName = "Dankert"
	Patient_ReneDankert.Profile.PatientInfo.PersonalInfo.LastName = "Dankert"
	Patient_ReneDankert.Profile.DateOfBirth = 280022400000
	Patient_ReneDankert.Profile.PatientInfo.PersonalInfo.DOB = 280022400000
	Patient_ReneDankert.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(280022400000)
	Patient_ReneDankert.Profile.PatientInfo.PatientNumber = 95 + 1
	Patient_ReneDankert.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_ReneDankert.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("D998031567")
	Patient_ReneDankert.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 105508890
	Patient_ReneDankert.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_ReneDankert_LKK_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_ReneDankert,
		ContractId:     "LKK_HZV",
		ChargeSystemId: "LKK_HZV",
		IkNumber:       105508890,
		StartDate:      toInt64Pointer(1420070400000),
	}
	_ = PatientParticipation_ReneDankert_LKK_HZV

	gofakeit.Seed(700182143447)
	Patient_SimonRalke = data.FakePatient()
	Patient_SimonRalke.Profile.FirstName = "Simon"
	Patient_SimonRalke.Profile.PatientInfo.PersonalInfo.FirstName = "Simon"
	Patient_SimonRalke.Profile.LastName = "Ralke"
	Patient_SimonRalke.Profile.PatientInfo.PersonalInfo.LastName = "Ralke"
	Patient_SimonRalke.Profile.DateOfBirth = 836092800000
	Patient_SimonRalke.Profile.PatientInfo.PersonalInfo.DOB = 836092800000
	Patient_SimonRalke.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(836092800000)
	Patient_SimonRalke.Profile.PatientInfo.PatientNumber = 96 + 1
	Patient_SimonRalke.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_SimonRalke.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("F182143447")
	Patient_SimonRalke.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 105508787
	Patient_SimonRalke.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_SimonRalke_LKK_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_SimonRalke,
		ContractId:     "LKK_HZV",
		ChargeSystemId: "LKK_HZV",
		IkNumber:       105508787,
		StartDate:      toInt64Pointer(1420070400000),
	}
	_ = PatientParticipation_SimonRalke_LKK_HZV

	gofakeit.Seed(840332661131)
	Patient_LuiseLampertinger = data.FakePatient()
	Patient_LuiseLampertinger.Profile.FirstName = "Luise"
	Patient_LuiseLampertinger.Profile.PatientInfo.PersonalInfo.FirstName = "Luise"
	Patient_LuiseLampertinger.Profile.LastName = "Lampertinger"
	Patient_LuiseLampertinger.Profile.PatientInfo.PersonalInfo.LastName = "Lampertinger"
	Patient_LuiseLampertinger.Profile.DateOfBirth = -1047513600000
	Patient_LuiseLampertinger.Profile.PatientInfo.PersonalInfo.DOB = -1047513600000
	Patient_LuiseLampertinger.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-1047513600000)
	Patient_LuiseLampertinger.Profile.PatientInfo.PatientNumber = 97 + 1
	Patient_LuiseLampertinger.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_LuiseLampertinger.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("T332661131")
	Patient_LuiseLampertinger.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 105508787
	Patient_LuiseLampertinger.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_LuiseLampertinger_LKK_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_LuiseLampertinger,
		ContractId:     "LKK_HZV",
		ChargeSystemId: "LKK_HZV",
		IkNumber:       105508787,
		StartDate:      toInt64Pointer(1420070400000),
	}
	_ = PatientParticipation_LuiseLampertinger_LKK_HZV

	gofakeit.Seed(720497386701)
	Patient_TheobaldKleinmann = data.FakePatient()
	Patient_TheobaldKleinmann.Profile.FirstName = "Theobald"
	Patient_TheobaldKleinmann.Profile.PatientInfo.PersonalInfo.FirstName = "Theobald"
	Patient_TheobaldKleinmann.Profile.LastName = "Kleinmann"
	Patient_TheobaldKleinmann.Profile.PatientInfo.PersonalInfo.LastName = "Kleinmann"
	Patient_TheobaldKleinmann.Profile.DateOfBirth = -983232000000
	Patient_TheobaldKleinmann.Profile.PatientInfo.PersonalInfo.DOB = -983232000000
	Patient_TheobaldKleinmann.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-983232000000)
	Patient_TheobaldKleinmann.Profile.PatientInfo.PatientNumber = 98 + 1
	Patient_TheobaldKleinmann.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_TheobaldKleinmann.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("H497386701")
	Patient_TheobaldKleinmann.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 103708773
	Patient_TheobaldKleinmann.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_TheobaldKleinmann_LKK_NO_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_TheobaldKleinmann,
		ContractId:     "LKK_NO_HZV",
		ChargeSystemId: "LKK_NO_HZV",
		IkNumber:       103708773,
		StartDate:      toInt64Pointer(1349049600000),
	}
	_ = PatientParticipation_TheobaldKleinmann_LKK_NO_HZV

	gofakeit.Seed(730199589119)
	Patient_RegineSchmadtke = data.FakePatient()
	Patient_RegineSchmadtke.Profile.FirstName = "Regine"
	Patient_RegineSchmadtke.Profile.PatientInfo.PersonalInfo.FirstName = "Regine"
	Patient_RegineSchmadtke.Profile.LastName = "Schmadtke"
	Patient_RegineSchmadtke.Profile.PatientInfo.PersonalInfo.LastName = "Schmadtke"
	Patient_RegineSchmadtke.Profile.DateOfBirth = -33523200000
	Patient_RegineSchmadtke.Profile.PatientInfo.PersonalInfo.DOB = -33523200000
	Patient_RegineSchmadtke.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-33523200000)
	Patient_RegineSchmadtke.Profile.PatientInfo.PatientNumber = 99 + 1
	Patient_RegineSchmadtke.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_RegineSchmadtke.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("I199589119")
	Patient_RegineSchmadtke.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 103708773
	Patient_RegineSchmadtke.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_RegineSchmadtke_LKK_NO_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_RegineSchmadtke,
		ContractId:     "LKK_NO_HZV",
		ChargeSystemId: "LKK_NO_HZV",
		IkNumber:       103708773,
		StartDate:      toInt64Pointer(1356998400000),
	}
	_ = PatientParticipation_RegineSchmadtke_LKK_NO_HZV

	gofakeit.Seed(850350827331)
	Patient_TobiasSchmadtke = data.FakePatient()
	Patient_TobiasSchmadtke.Profile.FirstName = "Tobias"
	Patient_TobiasSchmadtke.Profile.PatientInfo.PersonalInfo.FirstName = "Tobias"
	Patient_TobiasSchmadtke.Profile.LastName = "Schmadtke"
	Patient_TobiasSchmadtke.Profile.PatientInfo.PersonalInfo.LastName = "Schmadtke"
	Patient_TobiasSchmadtke.Profile.DateOfBirth = 490147200000
	Patient_TobiasSchmadtke.Profile.PatientInfo.PersonalInfo.DOB = 490147200000
	Patient_TobiasSchmadtke.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(490147200000)
	Patient_TobiasSchmadtke.Profile.PatientInfo.PatientNumber = 100 + 1
	Patient_TobiasSchmadtke.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_TobiasSchmadtke.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("U350827331")
	Patient_TobiasSchmadtke.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 103708773
	Patient_TobiasSchmadtke.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_TobiasSchmadtke_LKK_NO_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_TobiasSchmadtke,
		ContractId:     "LKK_NO_HZV",
		ChargeSystemId: "LKK_NO_HZV",
		IkNumber:       103708773,
		StartDate:      toInt64Pointer(1349049600000),
	}
	_ = PatientParticipation_TobiasSchmadtke_LKK_NO_HZV

	gofakeit.Seed(780419788129)
	Patient_TomNeurer = data.FakePatient()
	Patient_TomNeurer.Profile.FirstName = "Tom"
	Patient_TomNeurer.Profile.PatientInfo.PersonalInfo.FirstName = "Tom"
	Patient_TomNeurer.Profile.LastName = "Neurer"
	Patient_TomNeurer.Profile.PatientInfo.PersonalInfo.LastName = "Neurer"
	Patient_TomNeurer.Profile.DateOfBirth = 720835200000
	Patient_TomNeurer.Profile.PatientInfo.PersonalInfo.DOB = 720835200000
	Patient_TomNeurer.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(720835200000)
	Patient_TomNeurer.Profile.PatientInfo.PatientNumber = 101 + 1
	Patient_TomNeurer.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_TomNeurer.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("N419788129")
	Patient_TomNeurer.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 103708773
	Patient_TomNeurer.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_TomNeurer_LKK_WL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_TomNeurer,
		ContractId:     "LKK_WL_HZV",
		ChargeSystemId: "LKK_WL_HZV",
		IkNumber:       103708773,
		StartDate:      toInt64Pointer(1349049600000),
	}
	_ = PatientParticipation_TomNeurer_LKK_WL_HZV

	gofakeit.Seed(870680950690)
	Patient_KonradKönig = data.FakePatient()
	Patient_KonradKönig.Profile.FirstName = "Konrad"
	Patient_KonradKönig.Profile.PatientInfo.PersonalInfo.FirstName = "Konrad"
	Patient_KonradKönig.Profile.LastName = "König"
	Patient_KonradKönig.Profile.PatientInfo.PersonalInfo.LastName = "König"
	Patient_KonradKönig.Profile.DateOfBirth = -820886400000
	Patient_KonradKönig.Profile.PatientInfo.PersonalInfo.DOB = -820886400000
	Patient_KonradKönig.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-820886400000)
	Patient_KonradKönig.Profile.PatientInfo.PatientNumber = 102 + 1
	Patient_KonradKönig.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_KonradKönig.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("W680950690")
	Patient_KonradKönig.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 103708773
	Patient_KonradKönig.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_KonradKönig_LKK_WL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_KonradKönig,
		ContractId:     "LKK_WL_HZV",
		ChargeSystemId: "LKK_WL_HZV",
		IkNumber:       103708773,
		StartDate:      toInt64Pointer(1349049600000),
	}
	_ = PatientParticipation_KonradKönig_LKK_WL_HZV

	gofakeit.Seed(900253807849)
	Patient_ElkeEhlert = data.FakePatient()
	Patient_ElkeEhlert.Profile.FirstName = "Elke"
	Patient_ElkeEhlert.Profile.PatientInfo.PersonalInfo.FirstName = "Elke"
	Patient_ElkeEhlert.Profile.LastName = "Ehlert"
	Patient_ElkeEhlert.Profile.PatientInfo.PersonalInfo.LastName = "Ehlert"
	Patient_ElkeEhlert.Profile.DateOfBirth = 20131200000
	Patient_ElkeEhlert.Profile.PatientInfo.PersonalInfo.DOB = 20131200000
	Patient_ElkeEhlert.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(20131200000)
	Patient_ElkeEhlert.Profile.PatientInfo.PatientNumber = 103 + 1
	Patient_ElkeEhlert.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_ElkeEhlert.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("Z253807849")
	Patient_ElkeEhlert.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 103708773
	Patient_ElkeEhlert.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_ElkeEhlert_LKK_WL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_ElkeEhlert,
		ContractId:     "LKK_WL_HZV",
		ChargeSystemId: "LKK_WL_HZV",
		IkNumber:       103708773,
		StartDate:      toInt64Pointer(1356998400000),
	}
	_ = PatientParticipation_ElkeEhlert_LKK_WL_HZV

	gofakeit.Seed(770419530588)
	Patient_HildeWeber = data.FakePatient()
	Patient_HildeWeber.Profile.FirstName = "Hilde"
	Patient_HildeWeber.Profile.PatientInfo.PersonalInfo.FirstName = "Hilde"
	Patient_HildeWeber.Profile.LastName = "Weber"
	Patient_HildeWeber.Profile.PatientInfo.PersonalInfo.LastName = "Weber"
	Patient_HildeWeber.Profile.DateOfBirth = 48038400000
	Patient_HildeWeber.Profile.PatientInfo.PersonalInfo.DOB = 48038400000
	Patient_HildeWeber.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(48038400000)
	Patient_HildeWeber.Profile.PatientInfo.PatientNumber = 104 + 1
	Patient_HildeWeber.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_HildeWeber.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("M419530588")
	Patient_HildeWeber.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 109905003
	Patient_HildeWeber.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_HildeWeber_RV_KBS_BW_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_HildeWeber,
		ContractId:     "RV_KBS_BW_HZV",
		ChargeSystemId: "RV_KBS_BW_HZV",
		IkNumber:       109905003,
		StartDate:      toInt64Pointer(1293840000000),
	}
	_ = PatientParticipation_HildeWeber_RV_KBS_BW_HZV

	gofakeit.Seed(890096738229)
	Patient_PeterHanses = data.FakePatient()
	Patient_PeterHanses.Profile.FirstName = "Peter"
	Patient_PeterHanses.Profile.PatientInfo.PersonalInfo.FirstName = "Peter"
	Patient_PeterHanses.Profile.LastName = "Hanses"
	Patient_PeterHanses.Profile.PatientInfo.PersonalInfo.LastName = "Hanses"
	Patient_PeterHanses.Profile.DateOfBirth = -865814400000
	Patient_PeterHanses.Profile.PatientInfo.PersonalInfo.DOB = -865814400000
	Patient_PeterHanses.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-865814400000)
	Patient_PeterHanses.Profile.PatientInfo.PatientNumber = 105 + 1
	Patient_PeterHanses.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_PeterHanses.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("Y096738229")
	Patient_PeterHanses.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 109905003
	Patient_PeterHanses.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_PeterHanses_RV_KBS_BW_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_PeterHanses,
		ContractId:     "RV_KBS_BW_HZV",
		ChargeSystemId: "RV_KBS_BW_HZV",
		IkNumber:       109905003,
		StartDate:      toInt64Pointer(1293840000000),
	}
	_ = PatientParticipation_PeterHanses_RV_KBS_BW_HZV

	gofakeit.Seed(710446279027)
	Patient_CarolaBley = data.FakePatient()
	Patient_CarolaBley.Profile.FirstName = "Carola"
	Patient_CarolaBley.Profile.PatientInfo.PersonalInfo.FirstName = "Carola"
	Patient_CarolaBley.Profile.LastName = "Bley"
	Patient_CarolaBley.Profile.PatientInfo.PersonalInfo.LastName = "Bley"
	Patient_CarolaBley.Profile.DateOfBirth = -214099200000
	Patient_CarolaBley.Profile.PatientInfo.PersonalInfo.DOB = -214099200000
	Patient_CarolaBley.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-214099200000)
	Patient_CarolaBley.Profile.PatientInfo.PatientNumber = 106 + 1
	Patient_CarolaBley.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_CarolaBley.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("G446279027")
	Patient_CarolaBley.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 109905003
	Patient_CarolaBley.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_CarolaBley_RV_KBS_NO_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_CarolaBley,
		ContractId:     "RV_KBS_NO_HZV",
		ChargeSystemId: "RV_KBS_NO_HZV",
		IkNumber:       109905003,
		StartDate:      toInt64Pointer(1356998400000),
	}
	_ = PatientParticipation_CarolaBley_RV_KBS_NO_HZV

	gofakeit.Seed(850226941138)
	Patient_ElfriedeZantis = data.FakePatient()
	Patient_ElfriedeZantis.Profile.FirstName = "Elfriede"
	Patient_ElfriedeZantis.Profile.PatientInfo.PersonalInfo.FirstName = "Elfriede"
	Patient_ElfriedeZantis.Profile.LastName = "Zantis"
	Patient_ElfriedeZantis.Profile.PatientInfo.PersonalInfo.LastName = "Zantis"
	Patient_ElfriedeZantis.Profile.DateOfBirth = -721094400000
	Patient_ElfriedeZantis.Profile.PatientInfo.PersonalInfo.DOB = -721094400000
	Patient_ElfriedeZantis.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-721094400000)
	Patient_ElfriedeZantis.Profile.PatientInfo.PatientNumber = 107 + 1
	Patient_ElfriedeZantis.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_ElfriedeZantis.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("U226941138")
	Patient_ElfriedeZantis.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 109905003
	Patient_ElfriedeZantis.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_ElfriedeZantis_RV_KBS_NO_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_ElfriedeZantis,
		ContractId:     "RV_KBS_NO_HZV",
		ChargeSystemId: "RV_KBS_NO_HZV",
		IkNumber:       109905003,
		StartDate:      toInt64Pointer(1349049600000),
	}
	_ = PatientParticipation_ElfriedeZantis_RV_KBS_NO_HZV

	gofakeit.Seed(870925483642)
	Patient_DominikPauli = data.FakePatient()
	Patient_DominikPauli.Profile.FirstName = "Dominik"
	Patient_DominikPauli.Profile.PatientInfo.PersonalInfo.FirstName = "Dominik"
	Patient_DominikPauli.Profile.LastName = "Pauli"
	Patient_DominikPauli.Profile.PatientInfo.PersonalInfo.LastName = "Pauli"
	Patient_DominikPauli.Profile.DateOfBirth = 753580800000
	Patient_DominikPauli.Profile.PatientInfo.PersonalInfo.DOB = 753580800000
	Patient_DominikPauli.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(753580800000)
	Patient_DominikPauli.Profile.PatientInfo.PatientNumber = 108 + 1
	Patient_DominikPauli.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_DominikPauli.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("W925483642")
	Patient_DominikPauli.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 109905003
	Patient_DominikPauli.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_DominikPauli_RV_KBS_NO_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_DominikPauli,
		ContractId:     "RV_KBS_NO_HZV",
		ChargeSystemId: "RV_KBS_NO_HZV",
		IkNumber:       109905003,
		StartDate:      toInt64Pointer(1349049600000),
	}
	_ = PatientParticipation_DominikPauli_RV_KBS_NO_HZV

	gofakeit.Seed(770263248998)
	Patient_TheoKrons = data.FakePatient()
	Patient_TheoKrons.Profile.FirstName = "Theo"
	Patient_TheoKrons.Profile.PatientInfo.PersonalInfo.FirstName = "Theo"
	Patient_TheoKrons.Profile.LastName = "Krons"
	Patient_TheoKrons.Profile.PatientInfo.PersonalInfo.LastName = "Krons"
	Patient_TheoKrons.Profile.DateOfBirth = -1034985600000
	Patient_TheoKrons.Profile.PatientInfo.PersonalInfo.DOB = -1034985600000
	Patient_TheoKrons.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-1034985600000)
	Patient_TheoKrons.Profile.PatientInfo.PatientNumber = 109 + 1
	Patient_TheoKrons.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_TheoKrons.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("M263248998")
	Patient_TheoKrons.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 109905003
	Patient_TheoKrons.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_TheoKrons_RV_KBS_WL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_TheoKrons,
		ContractId:     "RV_KBS_WL_HZV",
		ChargeSystemId: "RV_KBS_WL_HZV",
		IkNumber:       109905003,
		StartDate:      toInt64Pointer(1349049600000),
	}
	_ = PatientParticipation_TheoKrons_RV_KBS_WL_HZV

	gofakeit.Seed(780501879426)
	Patient_PamelaAbenden = data.FakePatient()
	Patient_PamelaAbenden.Profile.FirstName = "Pamela"
	Patient_PamelaAbenden.Profile.PatientInfo.PersonalInfo.FirstName = "Pamela"
	Patient_PamelaAbenden.Profile.LastName = "Abenden"
	Patient_PamelaAbenden.Profile.PatientInfo.PersonalInfo.LastName = "Abenden"
	Patient_PamelaAbenden.Profile.DateOfBirth = 444700800000
	Patient_PamelaAbenden.Profile.PatientInfo.PersonalInfo.DOB = 444700800000
	Patient_PamelaAbenden.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(444700800000)
	Patient_PamelaAbenden.Profile.PatientInfo.PatientNumber = 110 + 1
	Patient_PamelaAbenden.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_PamelaAbenden.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("N501879426")
	Patient_PamelaAbenden.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 109905003
	Patient_PamelaAbenden.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_PamelaAbenden_RV_KBS_WL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_PamelaAbenden,
		ContractId:     "RV_KBS_WL_HZV",
		ChargeSystemId: "RV_KBS_WL_HZV",
		IkNumber:       109905003,
		StartDate:      toInt64Pointer(1349049600000),
	}
	_ = PatientParticipation_PamelaAbenden_RV_KBS_WL_HZV

	gofakeit.Seed(870659787610)
	Patient_TanjaKoch = data.FakePatient()
	Patient_TanjaKoch.Profile.FirstName = "Tanja"
	Patient_TanjaKoch.Profile.PatientInfo.PersonalInfo.FirstName = "Tanja"
	Patient_TanjaKoch.Profile.LastName = "Koch"
	Patient_TanjaKoch.Profile.PatientInfo.PersonalInfo.LastName = "Koch"
	Patient_TanjaKoch.Profile.DateOfBirth = 105148800000
	Patient_TanjaKoch.Profile.PatientInfo.PersonalInfo.DOB = 105148800000
	Patient_TanjaKoch.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(105148800000)
	Patient_TanjaKoch.Profile.PatientInfo.PatientNumber = 111 + 1
	Patient_TanjaKoch.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_TanjaKoch.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("W659787610")
	Patient_TanjaKoch.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 109905003
	Patient_TanjaKoch.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_TanjaKoch_RV_KBS_WL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_TanjaKoch,
		ContractId:     "RV_KBS_WL_HZV",
		ChargeSystemId: "RV_KBS_WL_HZV",
		IkNumber:       109905003,
		StartDate:      toInt64Pointer(1356998400000),
	}
	_ = PatientParticipation_TanjaKoch_RV_KBS_WL_HZV

	gofakeit.Seed(750456041721)
	Patient_MustafaBagriyanik = data.FakePatient()
	Patient_MustafaBagriyanik.Profile.FirstName = "Mustafa"
	Patient_MustafaBagriyanik.Profile.PatientInfo.PersonalInfo.FirstName = "Mustafa"
	Patient_MustafaBagriyanik.Profile.LastName = "Bagriyanik"
	Patient_MustafaBagriyanik.Profile.PatientInfo.PersonalInfo.LastName = "Bagriyanik"
	Patient_MustafaBagriyanik.Profile.DateOfBirth = -123552000000
	Patient_MustafaBagriyanik.Profile.PatientInfo.PersonalInfo.DOB = -123552000000
	Patient_MustafaBagriyanik.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-123552000000)
	Patient_MustafaBagriyanik.Profile.PatientInfo.PatientNumber = 112 + 1
	Patient_MustafaBagriyanik.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_MustafaBagriyanik.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("K456041721")
	Patient_MustafaBagriyanik.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 103500693
	Patient_MustafaBagriyanik.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_MustafaBagriyanik_SI_IKK_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MustafaBagriyanik,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		IkNumber:       103500693,
		StartDate:      toInt64Pointer(1285891200000),
	}
	_ = PatientParticipation_MustafaBagriyanik_SI_IKK_HZV

	gofakeit.Seed(820156397362)
	Patient_RosemarieHaug = data.FakePatient()
	Patient_RosemarieHaug.Profile.FirstName = "Rosemarie"
	Patient_RosemarieHaug.Profile.PatientInfo.PersonalInfo.FirstName = "Rosemarie"
	Patient_RosemarieHaug.Profile.LastName = "Haug"
	Patient_RosemarieHaug.Profile.PatientInfo.PersonalInfo.LastName = "Haug"
	Patient_RosemarieHaug.Profile.DateOfBirth = -759628800000
	Patient_RosemarieHaug.Profile.PatientInfo.PersonalInfo.DOB = -759628800000
	Patient_RosemarieHaug.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-759628800000)
	Patient_RosemarieHaug.Profile.PatientInfo.PatientNumber = 113 + 1
	Patient_RosemarieHaug.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_RosemarieHaug.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("R156397362")
	Patient_RosemarieHaug.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 103500693
	Patient_RosemarieHaug.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_RosemarieHaug_SI_IKK_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_RosemarieHaug,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		IkNumber:       103500693,
		StartDate:      toInt64Pointer(1285891200000),
	}
	_ = PatientParticipation_RosemarieHaug_SI_IKK_HZV

	gofakeit.Seed(830790123158)
	Patient_NorbertWerner = data.FakePatient()
	Patient_NorbertWerner.Profile.FirstName = "Norbert"
	Patient_NorbertWerner.Profile.PatientInfo.PersonalInfo.FirstName = "Norbert"
	Patient_NorbertWerner.Profile.LastName = "Werner"
	Patient_NorbertWerner.Profile.PatientInfo.PersonalInfo.LastName = "Werner"
	Patient_NorbertWerner.Profile.DateOfBirth = -832377600000
	Patient_NorbertWerner.Profile.PatientInfo.PersonalInfo.DOB = -832377600000
	Patient_NorbertWerner.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-832377600000)
	Patient_NorbertWerner.Profile.PatientInfo.PatientNumber = 114 + 1
	Patient_NorbertWerner.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_NorbertWerner.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("S790123158")
	Patient_NorbertWerner.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 103500693
	Patient_NorbertWerner.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_NorbertWerner_SI_IKK_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_NorbertWerner,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		IkNumber:       103500693,
		StartDate:      toInt64Pointer(1285891200000),
	}
	_ = PatientParticipation_NorbertWerner_SI_IKK_HZV

	gofakeit.Seed(840000405215)
	Patient_MarkusKornfeld = data.FakePatient()
	Patient_MarkusKornfeld.Profile.FirstName = "Markus"
	Patient_MarkusKornfeld.Profile.PatientInfo.PersonalInfo.FirstName = "Markus"
	Patient_MarkusKornfeld.Profile.LastName = "Kornfeld"
	Patient_MarkusKornfeld.Profile.PatientInfo.PersonalInfo.LastName = "Kornfeld"
	Patient_MarkusKornfeld.Profile.DateOfBirth = 398908800000
	Patient_MarkusKornfeld.Profile.PatientInfo.PersonalInfo.DOB = 398908800000
	Patient_MarkusKornfeld.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(398908800000)
	Patient_MarkusKornfeld.Profile.PatientInfo.PatientNumber = 115 + 1
	Patient_MarkusKornfeld.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_MarkusKornfeld.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("T000405215")
	Patient_MarkusKornfeld.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 103500693
	Patient_MarkusKornfeld.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_MarkusKornfeld_SI_IKK_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MarkusKornfeld,
		ContractId:     "SI_IKK_HZV",
		ChargeSystemId: "SI_IKK_HZV",
		IkNumber:       103500693,
		StartDate:      toInt64Pointer(1380585600000),
	}
	_ = PatientParticipation_MarkusKornfeld_SI_IKK_HZV

	gofakeit.Seed(750521258922)
	Patient_MariannePulver = data.FakePatient()
	Patient_MariannePulver.Profile.FirstName = "Marianne"
	Patient_MariannePulver.Profile.PatientInfo.PersonalInfo.FirstName = "Marianne"
	Patient_MariannePulver.Profile.LastName = "Pulver"
	Patient_MariannePulver.Profile.PatientInfo.PersonalInfo.LastName = "Pulver"
	Patient_MariannePulver.Profile.DateOfBirth = -318297600000
	Patient_MariannePulver.Profile.PatientInfo.PersonalInfo.DOB = -318297600000
	Patient_MariannePulver.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-318297600000)
	Patient_MariannePulver.Profile.PatientInfo.PatientNumber = 116 + 1
	Patient_MariannePulver.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_MariannePulver.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("K521258922")
	Patient_MariannePulver.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101575519
	Patient_MariannePulver.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_MariannePulver_TK_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MariannePulver,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		IkNumber:       101575519,
		StartDate:      toInt64Pointer(1285891200000),
	}
	_ = PatientParticipation_MariannePulver_TK_HZV

	gofakeit.Seed(820319394512)
	Patient_EduardKabel = data.FakePatient()
	Patient_EduardKabel.Profile.FirstName = "Eduard"
	Patient_EduardKabel.Profile.PatientInfo.PersonalInfo.FirstName = "Eduard"
	Patient_EduardKabel.Profile.LastName = "Kabel"
	Patient_EduardKabel.Profile.PatientInfo.PersonalInfo.LastName = "Kabel"
	Patient_EduardKabel.Profile.DateOfBirth = -1158969600000
	Patient_EduardKabel.Profile.PatientInfo.PersonalInfo.DOB = -1158969600000
	Patient_EduardKabel.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-1158969600000)
	Patient_EduardKabel.Profile.PatientInfo.PatientNumber = 117 + 1
	Patient_EduardKabel.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_EduardKabel.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("R319394512")
	Patient_EduardKabel.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101575519
	Patient_EduardKabel.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_EduardKabel_TK_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_EduardKabel,
		ContractId:     "TK_HZV",
		ChargeSystemId: "TK_HZV",
		IkNumber:       101575519,
		StartDate:      toInt64Pointer(1285891200000),
	}
	_ = PatientParticipation_EduardKabel_TK_HZV

	gofakeit.Seed(650962985131)
	Patient_GiselaReuter = data.FakePatient()
	Patient_GiselaReuter.Profile.FirstName = "Gisela"
	Patient_GiselaReuter.Profile.PatientInfo.PersonalInfo.FirstName = "Gisela"
	Patient_GiselaReuter.Profile.LastName = "Reuter"
	Patient_GiselaReuter.Profile.PatientInfo.PersonalInfo.LastName = "Reuter"
	Patient_GiselaReuter.Profile.DateOfBirth = -592790400000
	Patient_GiselaReuter.Profile.PatientInfo.PersonalInfo.DOB = -592790400000
	Patient_GiselaReuter.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-592790400000)
	Patient_GiselaReuter.Profile.PatientInfo.PatientNumber = 118 + 1
	Patient_GiselaReuter.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_GiselaReuter.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("A962985131")
	Patient_GiselaReuter.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 102171012
	Patient_GiselaReuter.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_GiselaReuter_EK_HE_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_GiselaReuter,
		ContractId:     "EK_HE_HZV",
		ChargeSystemId: "EK_HE_HZV",
		IkNumber:       102171012,
		StartDate:      toInt64Pointer(1467331200000),
		EndDate:        toInt64Pointer(1538265600000),
	}
	_ = PatientParticipation_GiselaReuter_EK_HE_HZV

	gofakeit.Seed(860919297713)
	Patient_FrankGross = data.FakePatient()
	Patient_FrankGross.Profile.FirstName = "Frank"
	Patient_FrankGross.Profile.PatientInfo.PersonalInfo.FirstName = "Frank"
	Patient_FrankGross.Profile.LastName = "Gross"
	Patient_FrankGross.Profile.PatientInfo.PersonalInfo.LastName = "Gross"
	Patient_FrankGross.Profile.DateOfBirth = 628992000000
	Patient_FrankGross.Profile.PatientInfo.PersonalInfo.DOB = 628992000000
	Patient_FrankGross.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(628992000000)
	Patient_FrankGross.Profile.PatientInfo.PatientNumber = 119 + 1
	Patient_FrankGross.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_FrankGross.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("V919297713")
	Patient_FrankGross.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 103170002
	Patient_FrankGross.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_FrankGross_EK_HE_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_FrankGross,
		ContractId:     "EK_HE_HZV",
		ChargeSystemId: "EK_HE_HZV",
		IkNumber:       103170002,
		StartDate:      toInt64Pointer(1467331200000),
		EndDate:        toInt64Pointer(1546214400000),
	}
	_ = PatientParticipation_FrankGross_EK_HE_HZV

	gofakeit.Seed(710266287496)
	Patient_AlissaWalder = data.FakePatient()
	Patient_AlissaWalder.Profile.FirstName = "Alissa"
	Patient_AlissaWalder.Profile.PatientInfo.PersonalInfo.FirstName = "Alissa"
	Patient_AlissaWalder.Profile.LastName = "Walder"
	Patient_AlissaWalder.Profile.PatientInfo.PersonalInfo.LastName = "Walder"
	Patient_AlissaWalder.Profile.DateOfBirth = 1218240000000
	Patient_AlissaWalder.Profile.PatientInfo.PersonalInfo.DOB = 1218240000000
	Patient_AlissaWalder.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(1218240000000)
	Patient_AlissaWalder.Profile.PatientInfo.PatientNumber = 120 + 1
	Patient_AlissaWalder.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_AlissaWalder.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("G266287496")
	Patient_AlissaWalder.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 104940005
	Patient_AlissaWalder.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_AlissaWalder_EK_HE_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_AlissaWalder,
		ContractId:     "EK_HE_HZV",
		ChargeSystemId: "EK_HE_HZV",
		IkNumber:       104940005,
		StartDate:      toInt64Pointer(1467331200000),
	}
	_ = PatientParticipation_AlissaWalder_EK_HE_HZV

	gofakeit.Seed(710584595976)
	Patient_PhillippTressner = data.FakePatient()
	Patient_PhillippTressner.Profile.FirstName = "Phillipp"
	Patient_PhillippTressner.Profile.PatientInfo.PersonalInfo.FirstName = "Phillipp"
	Patient_PhillippTressner.Profile.LastName = "Tressner"
	Patient_PhillippTressner.Profile.PatientInfo.PersonalInfo.LastName = "Tressner"
	Patient_PhillippTressner.Profile.DateOfBirth = 485308800000
	Patient_PhillippTressner.Profile.PatientInfo.PersonalInfo.DOB = 485308800000
	Patient_PhillippTressner.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(485308800000)
	Patient_PhillippTressner.Profile.PatientInfo.PatientNumber = 121 + 1
	Patient_PhillippTressner.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_PhillippTressner.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("G584595976")
	Patient_PhillippTressner.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108534160
	Patient_PhillippTressner.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_PhillippTressner_BKK_BW_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_PhillippTressner,
		ContractId:     "BKK_BW_HZV",
		ChargeSystemId: "BKK_BW_HZV",
		IkNumber:       108534160,
		StartDate:      toInt64Pointer(1293840000000),
	}
	_ = PatientParticipation_PhillippTressner_BKK_BW_HZV

	gofakeit.Seed(790473823807)
	Patient_CäciliaJost = data.FakePatient()
	Patient_CäciliaJost.Profile.FirstName = "Cäcilia"
	Patient_CäciliaJost.Profile.PatientInfo.PersonalInfo.FirstName = "Cäcilia"
	Patient_CäciliaJost.Profile.LastName = "Jost"
	Patient_CäciliaJost.Profile.PatientInfo.PersonalInfo.LastName = "Jost"
	Patient_CäciliaJost.Profile.DateOfBirth = -636249600000
	Patient_CäciliaJost.Profile.PatientInfo.PersonalInfo.DOB = -636249600000
	Patient_CäciliaJost.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-636249600000)
	Patient_CäciliaJost.Profile.PatientInfo.PatientNumber = 122 + 1
	Patient_CäciliaJost.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_CäciliaJost.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("O473823807")
	Patient_CäciliaJost.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101317004
	Patient_CäciliaJost.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_CäciliaJost_AOK_SH_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_CäciliaJost,
		ContractId:     "AOK_SH_HZV",
		ChargeSystemId: "AOK_SH_HZV",
		IkNumber:       101317004,
		StartDate:      toInt64Pointer(1475280000000),
	}
	_ = PatientParticipation_CäciliaJost_AOK_SH_HZV

	gofakeit.Seed(870134049279)
	Patient_BenjaminGeser = data.FakePatient()
	Patient_BenjaminGeser.Profile.FirstName = "Benjamin"
	Patient_BenjaminGeser.Profile.PatientInfo.PersonalInfo.FirstName = "Benjamin"
	Patient_BenjaminGeser.Profile.LastName = "Geser"
	Patient_BenjaminGeser.Profile.PatientInfo.PersonalInfo.LastName = "Geser"
	Patient_BenjaminGeser.Profile.DateOfBirth = 661910400000
	Patient_BenjaminGeser.Profile.PatientInfo.PersonalInfo.DOB = 661910400000
	Patient_BenjaminGeser.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(661910400000)
	Patient_BenjaminGeser.Profile.PatientInfo.PatientNumber = 123 + 1
	Patient_BenjaminGeser.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_BenjaminGeser.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("W134049279")
	Patient_BenjaminGeser.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101317004
	Patient_BenjaminGeser.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_BenjaminGeser_AOK_SH_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_BenjaminGeser,
		ContractId:     "AOK_SH_HZV",
		ChargeSystemId: "AOK_SH_HZV",
		IkNumber:       101317004,
		StartDate:      toInt64Pointer(1475280000000),
	}
	_ = PatientParticipation_BenjaminGeser_AOK_SH_HZV

	gofakeit.Seed(740033235489)
	Patient_FlorianKleist = data.FakePatient()
	Patient_FlorianKleist.Profile.FirstName = "Florian"
	Patient_FlorianKleist.Profile.PatientInfo.PersonalInfo.FirstName = "Florian"
	Patient_FlorianKleist.Profile.LastName = "Kleist"
	Patient_FlorianKleist.Profile.PatientInfo.PersonalInfo.LastName = "Kleist"
	Patient_FlorianKleist.Profile.DateOfBirth = 1133308800000
	Patient_FlorianKleist.Profile.PatientInfo.PersonalInfo.DOB = 1133308800000
	Patient_FlorianKleist.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(1133308800000)
	Patient_FlorianKleist.Profile.PatientInfo.PatientNumber = 124 + 1
	Patient_FlorianKleist.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_FlorianKleist.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("J033235489")
	Patient_FlorianKleist.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 101317004
	Patient_FlorianKleist.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_FlorianKleist_AOK_SH_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_FlorianKleist,
		ContractId:     "AOK_SH_HZV",
		ChargeSystemId: "AOK_SH_HZV",
		IkNumber:       101317004,
		StartDate:      toInt64Pointer(1475280000000),
	}
	_ = PatientParticipation_FlorianKleist_AOK_SH_HZV

	gofakeit.Seed(800578295943)
	Patient_IlenaSchrippe = data.FakePatient()
	Patient_IlenaSchrippe.Profile.FirstName = "Ilena"
	Patient_IlenaSchrippe.Profile.PatientInfo.PersonalInfo.FirstName = "Ilena"
	Patient_IlenaSchrippe.Profile.LastName = "Schrippe"
	Patient_IlenaSchrippe.Profile.PatientInfo.PersonalInfo.LastName = "Schrippe"
	Patient_IlenaSchrippe.Profile.DateOfBirth = 201744000000
	Patient_IlenaSchrippe.Profile.PatientInfo.PersonalInfo.DOB = 201744000000
	Patient_IlenaSchrippe.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(201744000000)
	Patient_IlenaSchrippe.Profile.PatientInfo.PatientNumber = 125 + 1
	Patient_IlenaSchrippe.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_IlenaSchrippe.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("P578295943")
	Patient_IlenaSchrippe.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 102129930
	Patient_IlenaSchrippe.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_IlenaSchrippe_BKK_VAG_HE = &PatientParticipationWithoutDoctor{
		Patient:        Patient_IlenaSchrippe,
		ContractId:     "BKK_VAG_HE",
		ChargeSystemId: "BKK_VAG_HE",
		IkNumber:       102129930,
		StartDate:      toInt64Pointer(1514764800000),
	}
	_ = PatientParticipation_IlenaSchrippe_BKK_VAG_HE

	gofakeit.Seed(880585214760)
	Patient_ErichSchommbach = data.FakePatient()
	Patient_ErichSchommbach.Profile.FirstName = "Erich"
	Patient_ErichSchommbach.Profile.PatientInfo.PersonalInfo.FirstName = "Erich"
	Patient_ErichSchommbach.Profile.LastName = "Schommbach"
	Patient_ErichSchommbach.Profile.PatientInfo.PersonalInfo.LastName = "Schommbach"
	Patient_ErichSchommbach.Profile.DateOfBirth = -1417219200000
	Patient_ErichSchommbach.Profile.PatientInfo.PersonalInfo.DOB = -1417219200000
	Patient_ErichSchommbach.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-1417219200000)
	Patient_ErichSchommbach.Profile.PatientInfo.PatientNumber = 126 + 1
	Patient_ErichSchommbach.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_ErichSchommbach.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("X585214760")
	Patient_ErichSchommbach.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 104491707
	Patient_ErichSchommbach.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_ErichSchommbach_BKK_VAG_HE = &PatientParticipationWithoutDoctor{
		Patient:        Patient_ErichSchommbach,
		ContractId:     "BKK_VAG_HE",
		ChargeSystemId: "BKK_VAG_HE",
		IkNumber:       104491707,
		StartDate:      toInt64Pointer(1514764800000),
	}
	_ = PatientParticipation_ErichSchommbach_BKK_VAG_HE

	gofakeit.Seed(710629889200)
	Patient_HeintjeSchmolke = data.FakePatient()
	Patient_HeintjeSchmolke.Profile.FirstName = "Heintje"
	Patient_HeintjeSchmolke.Profile.PatientInfo.PersonalInfo.FirstName = "Heintje"
	Patient_HeintjeSchmolke.Profile.LastName = "Schmolke"
	Patient_HeintjeSchmolke.Profile.PatientInfo.PersonalInfo.LastName = "Schmolke"
	Patient_HeintjeSchmolke.Profile.DateOfBirth = 1228176000000
	Patient_HeintjeSchmolke.Profile.PatientInfo.PersonalInfo.DOB = 1228176000000
	Patient_HeintjeSchmolke.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(1228176000000)
	Patient_HeintjeSchmolke.Profile.PatientInfo.PatientNumber = 127 + 1
	Patient_HeintjeSchmolke.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_HeintjeSchmolke.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("G629889200")
	Patient_HeintjeSchmolke.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 103725364
	Patient_HeintjeSchmolke.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_HeintjeSchmolke_BKK_VAG_HE = &PatientParticipationWithoutDoctor{
		Patient:        Patient_HeintjeSchmolke,
		ContractId:     "BKK_VAG_HE",
		ChargeSystemId: "BKK_VAG_HE",
		IkNumber:       103725364,
		StartDate:      toInt64Pointer(1514764800000),
	}
	_ = PatientParticipation_HeintjeSchmolke_BKK_VAG_HE

	gofakeit.Seed(650257725428)
	Patient_RainerEngels = data.FakePatient()
	Patient_RainerEngels.Profile.FirstName = "Rainer"
	Patient_RainerEngels.Profile.PatientInfo.PersonalInfo.FirstName = "Rainer"
	Patient_RainerEngels.Profile.LastName = "Engels"
	Patient_RainerEngels.Profile.PatientInfo.PersonalInfo.LastName = "Engels"
	Patient_RainerEngels.Profile.DateOfBirth = -553564800000
	Patient_RainerEngels.Profile.PatientInfo.PersonalInfo.DOB = -553564800000
	Patient_RainerEngels.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-553564800000)
	Patient_RainerEngels.Profile.PatientInfo.PatientNumber = 128 + 1
	Patient_RainerEngels.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_RainerEngels.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("A257725428")
	Patient_RainerEngels.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 109905003
	Patient_RainerEngels.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_RainerEngels_RV_KBS_SN_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_RainerEngels,
		ContractId:     "RV_KBS_SN_HZV",
		ChargeSystemId: "RV_KBS_SN_HZV",
		IkNumber:       109905003,
		StartDate:      toInt64Pointer(1522540800000),
	}
	_ = PatientParticipation_RainerEngels_RV_KBS_SN_HZV

	gofakeit.Seed(840341012237)
	Patient_VerenaFremann = data.FakePatient()
	Patient_VerenaFremann.Profile.FirstName = "Verena"
	Patient_VerenaFremann.Profile.PatientInfo.PersonalInfo.FirstName = "Verena"
	Patient_VerenaFremann.Profile.LastName = "Fremann"
	Patient_VerenaFremann.Profile.PatientInfo.PersonalInfo.LastName = "Fremann"
	Patient_VerenaFremann.Profile.DateOfBirth = 357782400000
	Patient_VerenaFremann.Profile.PatientInfo.PersonalInfo.DOB = 357782400000
	Patient_VerenaFremann.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(357782400000)
	Patient_VerenaFremann.Profile.PatientInfo.PatientNumber = 129 + 1
	Patient_VerenaFremann.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_VerenaFremann.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("T341012237")
	Patient_VerenaFremann.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 109905003
	Patient_VerenaFremann.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_VerenaFremann_RV_KBS_SN_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_VerenaFremann,
		ContractId:     "RV_KBS_SN_HZV",
		ChargeSystemId: "RV_KBS_SN_HZV",
		IkNumber:       109905003,
		StartDate:      toInt64Pointer(1522540800000),
	}
	_ = PatientParticipation_VerenaFremann_RV_KBS_SN_HZV

	gofakeit.Seed(890257136583)
	Patient_LinaGraubach = data.FakePatient()
	Patient_LinaGraubach.Profile.FirstName = "Lina"
	Patient_LinaGraubach.Profile.PatientInfo.PersonalInfo.FirstName = "Lina"
	Patient_LinaGraubach.Profile.LastName = "Graubach"
	Patient_LinaGraubach.Profile.PatientInfo.PersonalInfo.LastName = "Graubach"
	Patient_LinaGraubach.Profile.DateOfBirth = 1219449600000
	Patient_LinaGraubach.Profile.PatientInfo.PersonalInfo.DOB = 1219449600000
	Patient_LinaGraubach.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(1219449600000)
	Patient_LinaGraubach.Profile.PatientInfo.PatientNumber = 130 + 1
	Patient_LinaGraubach.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_LinaGraubach.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("Y257136583")
	Patient_LinaGraubach.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 109905003
	Patient_LinaGraubach.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_LinaGraubach_RV_KBS_SN_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_LinaGraubach,
		ContractId:     "RV_KBS_SN_HZV",
		ChargeSystemId: "RV_KBS_SN_HZV",
		IkNumber:       109905003,
		StartDate:      toInt64Pointer(1522540800000),
	}
	_ = PatientParticipation_LinaGraubach_RV_KBS_SN_HZV

	gofakeit.Seed(900278869382)
	Patient_LiesbethUrner = data.FakePatient()
	Patient_LiesbethUrner.Profile.FirstName = "Liesbeth"
	Patient_LiesbethUrner.Profile.PatientInfo.PersonalInfo.FirstName = "Liesbeth"
	Patient_LiesbethUrner.Profile.LastName = "Urner"
	Patient_LiesbethUrner.Profile.PatientInfo.PersonalInfo.LastName = "Urner"
	Patient_LiesbethUrner.Profile.DateOfBirth = -1131321600000
	Patient_LiesbethUrner.Profile.PatientInfo.PersonalInfo.DOB = -1131321600000
	Patient_LiesbethUrner.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-1131321600000)
	Patient_LiesbethUrner.Profile.PatientInfo.PatientNumber = 131 + 1
	Patient_LiesbethUrner.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_LiesbethUrner.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("Z278869382")
	Patient_LiesbethUrner.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 107310373
	Patient_LiesbethUrner.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_LiesbethUrner_AOK_RP_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_LiesbethUrner,
		ContractId:     "AOK_RP_HZV",
		ChargeSystemId: "AOK_RP_HZV",
		IkNumber:       107310373,
		StartDate:      toInt64Pointer(1522540800000),
	}
	_ = PatientParticipation_LiesbethUrner_AOK_RP_HZV

	gofakeit.Seed(690762337476)
	Patient_HolgerKromer = data.FakePatient()
	Patient_HolgerKromer.Profile.FirstName = "Holger"
	Patient_HolgerKromer.Profile.PatientInfo.PersonalInfo.FirstName = "Holger"
	Patient_HolgerKromer.Profile.LastName = "Kromer"
	Patient_HolgerKromer.Profile.PatientInfo.PersonalInfo.LastName = "Kromer"
	Patient_HolgerKromer.Profile.DateOfBirth = 395193600000
	Patient_HolgerKromer.Profile.PatientInfo.PersonalInfo.DOB = 395193600000
	Patient_HolgerKromer.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(395193600000)
	Patient_HolgerKromer.Profile.PatientInfo.PatientNumber = 132 + 1
	Patient_HolgerKromer.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_HolgerKromer.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("E762337476")
	Patient_HolgerKromer.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 107310373
	Patient_HolgerKromer.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_HolgerKromer_AOK_RP_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_HolgerKromer,
		ContractId:     "AOK_RP_HZV",
		ChargeSystemId: "AOK_RP_HZV",
		IkNumber:       107310373,
		StartDate:      toInt64Pointer(1522540800000),
	}
	_ = PatientParticipation_HolgerKromer_AOK_RP_HZV

	gofakeit.Seed(830241692209)
	Patient_LuanForstberg = data.FakePatient()
	Patient_LuanForstberg.Profile.FirstName = "Luan"
	Patient_LuanForstberg.Profile.PatientInfo.PersonalInfo.FirstName = "Luan"
	Patient_LuanForstberg.Profile.LastName = "Forstberg"
	Patient_LuanForstberg.Profile.PatientInfo.PersonalInfo.LastName = "Forstberg"
	Patient_LuanForstberg.Profile.DateOfBirth = 1252022400000
	Patient_LuanForstberg.Profile.PatientInfo.PersonalInfo.DOB = 1252022400000
	Patient_LuanForstberg.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(1252022400000)
	Patient_LuanForstberg.Profile.PatientInfo.PatientNumber = 133 + 1
	Patient_LuanForstberg.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_LuanForstberg.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("S241692209")
	Patient_LuanForstberg.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 107310373
	Patient_LuanForstberg.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_LuanForstberg_AOK_RP_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_LuanForstberg,
		ContractId:     "AOK_RP_HZV",
		ChargeSystemId: "AOK_RP_HZV",
		IkNumber:       107310373,
		StartDate:      toInt64Pointer(1522540800000),
	}
	_ = PatientParticipation_LuanForstberg_AOK_RP_HZV

	gofakeit.Seed(690900774354)
	Patient_FranzKranzer = data.FakePatient()
	Patient_FranzKranzer.Profile.FirstName = "Franz"
	Patient_FranzKranzer.Profile.PatientInfo.PersonalInfo.FirstName = "Franz"
	Patient_FranzKranzer.Profile.LastName = "Kranzer"
	Patient_FranzKranzer.Profile.PatientInfo.PersonalInfo.LastName = "Kranzer"
	Patient_FranzKranzer.Profile.DateOfBirth = -732326400000
	Patient_FranzKranzer.Profile.PatientInfo.PersonalInfo.DOB = -732326400000
	Patient_FranzKranzer.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-732326400000)
	Patient_FranzKranzer.Profile.PatientInfo.PatientNumber = 134 + 1
	Patient_FranzKranzer.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_FranzKranzer.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("E900774354")
	Patient_FranzKranzer.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 103720893
	Patient_FranzKranzer.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_FranzKranzer_BKK_BW_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_FranzKranzer,
		ContractId:     "BKK_BW_HZV",
		ChargeSystemId: "BKK_BW_HZV",
		IkNumber:       103720893,
		StartDate:      toInt64Pointer(1293840000000),
	}
	_ = PatientParticipation_FranzKranzer_BKK_BW_HZV

	gofakeit.Seed(810756590784)
	Patient_MarliesFortzacher = data.FakePatient()
	Patient_MarliesFortzacher.Profile.FirstName = "Marlies"
	Patient_MarliesFortzacher.Profile.PatientInfo.PersonalInfo.FirstName = "Marlies"
	Patient_MarliesFortzacher.Profile.LastName = "Fortzacher"
	Patient_MarliesFortzacher.Profile.PatientInfo.PersonalInfo.LastName = "Fortzacher"
	Patient_MarliesFortzacher.Profile.DateOfBirth = -934070400000
	Patient_MarliesFortzacher.Profile.PatientInfo.PersonalInfo.DOB = -934070400000
	Patient_MarliesFortzacher.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(-934070400000)
	Patient_MarliesFortzacher.Profile.PatientInfo.PatientNumber = 135 + 1
	Patient_MarliesFortzacher.Profile.PatientInfo.PersonalInfo.Gender = "W"
	Patient_MarliesFortzacher.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("Q756590784")
	Patient_MarliesFortzacher.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 107310373
	Patient_MarliesFortzacher.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_MarliesFortzacher_AOK_SL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MarliesFortzacher,
		ContractId:     "AOK_SL_HZV",
		ChargeSystemId: "AOK_SL_HZV",
		IkNumber:       107310373,
		StartDate:      toInt64Pointer(1617235200000),
	}
	_ = PatientParticipation_MarliesFortzacher_AOK_SL_HZV

	gofakeit.Seed(800893109031)
	Patient_ClemensHuber = data.FakePatient()
	Patient_ClemensHuber.Profile.FirstName = "Clemens"
	Patient_ClemensHuber.Profile.PatientInfo.PersonalInfo.FirstName = "Clemens"
	Patient_ClemensHuber.Profile.LastName = "Huber"
	Patient_ClemensHuber.Profile.PatientInfo.PersonalInfo.LastName = "Huber"
	Patient_ClemensHuber.Profile.DateOfBirth = 432950400000
	Patient_ClemensHuber.Profile.PatientInfo.PersonalInfo.DOB = 432950400000
	Patient_ClemensHuber.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(432950400000)
	Patient_ClemensHuber.Profile.PatientInfo.PatientNumber = 136 + 1
	Patient_ClemensHuber.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_ClemensHuber.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("P893109031")
	Patient_ClemensHuber.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 107310373
	Patient_ClemensHuber.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_ClemensHuber_AOK_SL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_ClemensHuber,
		ContractId:     "AOK_SL_HZV",
		ChargeSystemId: "AOK_SL_HZV",
		IkNumber:       107310373,
		StartDate:      toInt64Pointer(1617235200000),
	}
	_ = PatientParticipation_ClemensHuber_AOK_SL_HZV

	gofakeit.Seed(900526235313)
	Patient_MauriceKohne = data.FakePatient()
	Patient_MauriceKohne.Profile.FirstName = "Maurice"
	Patient_MauriceKohne.Profile.PatientInfo.PersonalInfo.FirstName = "Maurice"
	Patient_MauriceKohne.Profile.LastName = "Kohne"
	Patient_MauriceKohne.Profile.PatientInfo.PersonalInfo.LastName = "Kohne"
	Patient_MauriceKohne.Profile.DateOfBirth = 1228262400000
	Patient_MauriceKohne.Profile.PatientInfo.PersonalInfo.DOB = 1228262400000
	Patient_MauriceKohne.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(1228262400000)
	Patient_MauriceKohne.Profile.PatientInfo.PatientNumber = 137 + 1
	Patient_MauriceKohne.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_MauriceKohne.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("Z526235313")
	Patient_MauriceKohne.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 107310373
	Patient_MauriceKohne.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_MauriceKohne_AOK_SL_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_MauriceKohne,
		ContractId:     "AOK_SL_HZV",
		ChargeSystemId: "AOK_SL_HZV",
		IkNumber:       107310373,
		StartDate:      toInt64Pointer(1617235200000),
	}
	_ = PatientParticipation_MauriceKohne_AOK_SL_HZV

	gofakeit.Seed(790448622328)
	Patient_NicoGrotl = data.FakePatient()
	Patient_NicoGrotl.Profile.FirstName = "Nico"
	Patient_NicoGrotl.Profile.PatientInfo.PersonalInfo.FirstName = "Nico"
	Patient_NicoGrotl.Profile.LastName = "Grotl"
	Patient_NicoGrotl.Profile.PatientInfo.PersonalInfo.LastName = "Grotl"
	Patient_NicoGrotl.Profile.DateOfBirth = 440294400000
	Patient_NicoGrotl.Profile.PatientInfo.PersonalInfo.DOB = 440294400000
	Patient_NicoGrotl.Profile.PatientInfo.PersonalInfo.DateOfBirth = toDateOfBirth(440294400000)
	Patient_NicoGrotl.Profile.PatientInfo.PatientNumber = 138 + 1
	Patient_NicoGrotl.Profile.PatientInfo.PersonalInfo.Gender = "M"
	Patient_NicoGrotl.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = toStringPointer("O448622328")
	Patient_NicoGrotl.Profile.PatientInfo.InsuranceInfos[0].IkNumber = 108023388
	Patient_NicoGrotl.Profile.PatientInfo.InsuranceInfos[0].IsActive = true
	PatientParticipation_NicoGrotl_BKK_BY_HZV = &PatientParticipationWithoutDoctor{
		Patient:        Patient_NicoGrotl,
		ContractId:     "BKK_BY_HZV",
		ChargeSystemId: "BKK_BY_HZV",
		IkNumber:       108023388,
		StartDate:      toInt64Pointer(1640995200000),
	}
	_ = PatientParticipation_NicoGrotl_BKK_BY_HZV

}
