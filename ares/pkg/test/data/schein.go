package data

import (
	"time"

	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/brianvoe/gofakeit/v5"
	"github.com/google/uuid"
)

func CreateFakeSchein(doctorId, patientId uuid.UUID) schein.ScheinRepo {
	var scheinRepo schein.ScheinRepo

	quater, year := util.GetCurrentQuarter(time.Now())
	gofakeit.Struct(&scheinRepo)
	scheinRepo.Id = util.NewPointer(uuid.New())
	scheinRepo.DoctorId = doctorId
	scheinRepo.PatientId = patientId
	scheinRepo.IsBilled = false
	scheinRepo.IsDeleted = false
	scheinRepo.Schein.G4101Quarter = util.NewPointer(int32(quater))
	scheinRepo.Schein.G4101Year = util.NewPointer(int32(year))
	if scheinRepo.ContractId != nil {
		scheinRepo.Schein.ScheinMainGroup = "HZV"
		scheinRepo.ContractId = util.NewPointer("AWH_01")
	}
	return scheinRepo
}

func CreateFakeScheinForBilling(doctorId, patientId uuid.UUID) schein.ScheinRepo {
	var scheinRepo schein.ScheinRepo
	scheinRepo.Id = util.NewPointer(uuid.New())
	scheinRepo.DoctorId = doctorId
	scheinRepo.PatientId = patientId
	scheinRepo.IsBilled = false
	scheinRepo.IsDeleted = false
	scheinRepo.CreatedAt = time.Now().Unix()
	scheinRepo.Schein.KvScheinSubGroup = util.NewPointer("123")
	return scheinRepo
}
