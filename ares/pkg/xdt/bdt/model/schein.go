package bdt_model

import (
	"strconv"
	"time"

	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdkt_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	card_service_common "git.tutum.dev/medi/tutum/ares/service/domains/card_service/common"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"gitlab.com/silenteer-oss/titan"
)

// note: Properties with Un prefix means unknown properties just leave it there to map value to model
type Schein struct {
	Type   string `field:"8000"`
	Length string `field:"8100"` //length of lines
	// Software      string `field:"5xxx"`
	BsnrCode      string   `field:"5098"`
	Lanr          string   `field:"5099"`
	Un9901s       []string `field:"9901"`
	PatientNumber string   `field:"3000"` //patientnumber
	// CaseNumber string `field:"3002"` // don't use for now
	ScheinNumber string `field:"3003"`
	CardType     string `field:"3004"` // map to fromcardtype, check if cardType diff from 3, always EGK
	// CodeNumber string `field:"3005"` // for SADT-Scheinen
	CdmVersion         string           `field:"3006"` // in patient profile
	ProofOfInsurance   ProofOfInsurance `field:"3010"`
	AdditionalName     string           `field:"3100"` // addition name have array value
	IntendWord         string           `field:"3120"`
	LastName           string           `field:"3101"`
	FirstName          string           `field:"3102"`
	DateOfBirth        string           `field:"3103"`
	Title              string           `field:"3104"`
	InsuranceNumber    string           `field:"3105"` // kvk //map either 3105 or 3119 to insurancenumber
	InsuranceNumberEGK string           `field:"3119"` // egk

	Wohnort                       string `field:"3106"` // no available
	Street                        string `field:"3107"`
	Number                        string `field:"3109"`
	AdditionalAddressInfo         string `field:"3115"`
	PostCode                      string `field:"3112"`
	CountryCode                   string `field:"3114"`
	City                          string `field:"3113"`
	PostOfficeBoxPostCode         string `field:"3121"`
	PostOfficeBoxPlaceOfResidence string `field:"3122"`
	PostOfficeBoxOfficeBox        string `field:"3123"`
	PostOfficeBoxCountryCode      string `field:"3124"`
	Wop                           string `field:"3116"`
	InsuranceStatus               string `field:"3108"`
	// Gender                        string `field:"3110"` // <=2.8 recheck if file has 2 duplicate field gender, for now we assume only 1 field
	Gender string `field:"3110"` //2 field duplicate >2.9
	Un3200 string `field:"3200"` // no available
	Un3211 string `field:"3211"`
	Un3201 string `field:"3201"`
	Un3202 string `field:"3202"` // Vorname des Vers.
	Un3203 string `field:"3203"` // Geburtsdatum des Vers.
	Un3204 string `field:"3204"` // Wohnort des Vers.
	Un3205 string `field:"3205"` // Straße des Vers.
	Un3212 string `field:"3212"` // Hausnummer des Vers.
	Un3213 string `field:"3213"` // Anschriftenzusatz des Vers.
	Un3206 string `field:"3206"` // Titel des Vers.
	Un3207 string `field:"3207"` // PLZ des Vers.
	Un3209 string `field:"3209"` // Wohnort des Vers
	Un3214 string `field:"3214"` // Wohnsitz-Ländercode des Vers.
	Un3210 string `field:"3210"` // Geschlecht des Vers. <2.8
	// Un3210             string `field:"3210"` // Geschlecht des Vers. > 2.8
	Un3221             string `field:"3221"` // Postfach PLZ des Vers.
	Un3222             string `field:"3222"` // Postfach Ort des Vers.
	Un3223             string `field:"3223"` // Postfach des Vers.
	Un3224             string `field:"3224"` // Postfach-Land des Vers.
	TreatmentDoctor    string `field:"3635"` // Arztzuordung
	YearQuarter        string `field:"4101"` //QJJJJ split to save in db
	G4102              string `field:"4102" `
	TsvgContactType    string `field:"4103"` // <=2.8 no available in garrio pro
	G4104              string `field:"4104"`
	G4106              string `field:"4106"`
	BillingType        string `field:"4107"` // not available yet
	RegistrationNumber string `field:"4108"`
	ReadCardDate       string `field:"4109"`
	StartDate          string `field:"4133"`
	EndDate            string `field:"4110"` //>2.9 length 8 // implement validation to check if 4109 exist this field will be mandatory and vice versa
	// EndDate     string `field:"4110"` //<2.9 length 4 check if version < 2.9 parse to last date of month
	Un9901               string   `field:"9901"` // TODO: don't know why this field exist in EVA and don't follow bdt standard
	IkNumber             string   `field:"4111"`
	InsuranceCompanyName string   `field:"4134"` // map to insuranceSnapshot.InsuranceCompanyName
	InsuranceType        string   `field:"4112"` // not available
	StatusSupplement     string   `field:"4113"` // in case < 2.9 and include special group, dmp labeling, base on card_service_common.KVKData
	SpecialGroup         string   `field:"4131"`
	DMPLabeling          string   `field:"4132"`
	FeeCatalogue         string   `field:"4121"`
	G4122                string   `field:"4122"`
	Ad4123               string   `field:"4123"`
	Ad4124               string   `field:"4124"`
	Ad4125FromTo         string   `field:"4125"` // need to split to 2 field yyyymmddyyyymmdd startDateEnđate
	Ad4126               string   `field:"4126"`
	Ad4125From           string   `field:"4150"`
	Ad4125To             string   `field:"4151"`
	Un4152               string   `field:"4152"`
	Ad4202               string   `field:"4202"`
	Ad4204               string   `field:"4204"`
	Re4205               string   `field:"4205"`
	Ad4206               string   `field:"4206"`
	Re4207               []string `field:"4207"`
	Re4208               []string `field:"4208"`
	Re4209               []string `field:"4209"`

	Un4210 string `field:"4210"` // don't exist in sample file
	Un4211 string `field:"4211"`
	Un4212 string `field:"4212"`
	Un4213 string `field:"4213"`
	Re4217 Re4217 `field:"4217"` // either exists Re4217 or Re4225
	Re4225 Re4225 `field:"4225"` // either exists Re4217 or Re4225

	Re4218 Re4218 `field:"4218"` //bei 0102 und 0103 only available in 0102 und 0103 schein
	Re4226 Re4226 `field:"4226"`

	Re4219 string `field:"4219"`
	Re4220 string `field:"4220"`
	Re4221 string `field:"4221"`

	Un4222 string `field:"4222"` // don't exist in sample file
	Un4223 string `field:"4223"`

	Re4229           string      `field:"4229"`
	Re4233           []string    `field:"4233"`
	Ps4234           Ps4234      `field:"4234"`
	Ps4236           string      `field:"4236"`
	KvScheinSubGroup string      `field:"4239"`
	TsvgContact      TsvgContact `field:"4103"`

	// following fields are only available in hzv schein
	ModelId                  string `field:"4240"`
	AbbreviationCashRegister string `field:"4249"`
	HavgId                   string `field:"4250"`
	MediIdDoctor             string `field:"4251"`
	LanrDoctor               string `field:"4252"` // not available

	Re4243 string `field:"4243"`
	Un0900 string `field:"0900"` // don't exist in sample file
	Un0901 string `field:"0901"`
	Un0902 string `field:"0902"`
	Un0903 string `field:"0903"`
	Un0904 string `field:"0904"`
	Un0905 string `field:"0905"`
	Un0906 string `field:"0906"`
	Un0907 string `field:"0907"`

	// 5xxx string // not available
	Di5000s []Di5000 `field:"5000"`
	Di6200s []Di6200 `field:"6200"`
}

type Re4217 struct {
	Re4217 string `field:"4217"`
	Re4241 string `field:"4241"`
}

type Re4225 struct {
	Re4225 string `field:"4225"`
	Re4241 string `field:"4241"`
	Re4248 string `field:"4248"`
}

type Re4218 struct {
	Re4218 string `field:"4218"`
	Re4242 string `field:"4242"`
}
type Re4226 struct {
	Re4226 string `field:"4226"`
	Re4242 string `field:"4242"`
	Re4249 string `field:"4249"`
}

type Ps4234 struct {
	Ps4234 string   `field:"4234"`
	Ps4235 []Ps4235 `field:"4235"`
}

type Ps4235 struct {
	Ps4235                       string                        `field:"4235"`
	Ps4299                       string                        `field:"4299"`
	Ps4247                       string                        `field:"4247"`
	GroupServicesCodeBefore2017s []GroupServicesCodeBefore2017 `field:"4244"`
	Ps4250                       Ps4250                        `field:"4250"`
	Ps4252                       string                        `field:"4252"` //{Name: "4252", Value: util.NumberToString(p.Ps4245)},
	GroupServiceCodes            []GroupServiceCode            `field:"4253"`
	Ps4255                       string                        `field:"4255"`
	CareGiverServiceCodes        []CareGiverServiceCode        `field:"4256"`
}

type Di5000 struct {
	SelectedDate string   `field:"5000"` // yyyymmdd
	Di5001       []Di5001 `field:"5001"` // GNR
}
type TsvgContact struct {
	TsvgContactType string `field:"4103"`
	TsvgTranferCode string `field:"4114"`
	TsvgContactDate string `field:"4115"`
	TsvgInfor       string `field:"4105"`
}

// add all to payload.additionInfors
type Di5001 struct {
	Di5001 string   `field:"5001"` // payload.code timeline
	Di5002 []string `field:"5002"` // payload.additionInfors join ""
	Di5003 []string `field:"5003"` // payload.additionInfors join ""
	Un5004 string   `field:"5004"` // don't exist in sample file
	Di5005 string   `field:"5005"` // pad 0 in front of this number to 3 chars follow our app
	Di5006 string   `field:"5006"`
	Un5007 string   `field:"5007"` // don't exist in sample file
	Di5008 string   `field:"5008"`
	Di5009 []string `field:"5009"`
	Di5010 string   `field:"5010"`
	Di5012 []Di5012 `field:"5012"`
	Di5013 string   `field:"5013"`
	Di5015 []string `field:"5015"`
	Di5016 []string `field:"5016"`
	Di5017 []string `field:"5017"`
	Di5018 string   `field:"5018"`
	Di5019 []string `field:"5019"`
	Di5020 Di5020   `field:"5020"`
	Di5023 string   `field:"5023"`
	Di5024 string   `field:"5024"`
	Di5025 string   `field:"5025"`
	Di5026 string   `field:"5026"`
	Un5027 []string `field:"5027"` // don't exist in sample file
	Un5028 []string `field:"5028"` // don't exist in sample file
	Di5034 string   `field:"5034"`
	Di5035 []Di5035 `field:"5035"`
	Di5036 []string `field:"5036"`
	Di5037 string   `field:"5037"`
	Di5038 []string `field:"5038"`
	Un5039 string   `field:"5039"`
	Di5040 string   `field:"5040"`
	Di5042 Di5042   `field:"5042"`
	Un5044 string   `field:"5044"`
	Di5070 []Di5070 `field:"5070"`
	Di5071 []Di5071 `field:"5071"`
	Di5098 string   `field:"5098"`
	Di5099 string   `field:"5099"`
	Di5101 string   `field:"5101"`
	Di5100 string   `field:"5100"`
	Un9901 []string `field:"9901"`
}

type Di5012 struct {
	Di5012 string   `field:"5012"`
	Di5011 []string // join "" to 1 record
	Di5074 string   `field:"5074"`
	Di5075 string   `field:"5075"`
}
type Di5020 struct {
	Di5020 string `field:"5020"`
	Di5021 string `field:"5021"`
}
type Di5035 struct {
	Di5035 string `field:"5035"`
	Di5041 string `field:"5041"`
}
type Di5042 struct {
	Di5042 string `field:"5042"`
	Di5043 string `field:"5043"`
}
type Di5070 struct {
	Di5070 string   `field:"5070"`
	Di5072 []string `field:"5072"`
}
type Di5071 struct {
	Di5071 string   `field:"5071"`
	Di5073 []string `field:"5073"`
}

type Di6200 struct {
	SelectedDate string `field:"6200"`
	// 5xxx string
	Di5098 string   `field:"5098"`
	Di5099 string   `field:"5099"`
	Un9901 []string `field:"9901"`
	Di6000 string   `field:"6000"`
	Di6001 Di6001   `field:"6001"`
	// 5xxx string
	// 5xxx string
	Di3672 string `field:"3672"`
	Di3673 Di3673 `field:"3673"`
	Di3678 string `field:"3678"`
	// 5xxx string // to many 5xxx field
}

type Di6001 struct {
	Di6001 string   `field:"6001"` // Code of diagnose time line
	Di6003 string   `field:"6003"` // certainty
	Di6004 string   `field:"6004"` // localization
	Di6006 []string `field:"6006"` // explanation join "" to 1 field
	Di6008 []string `field:"6008"` // exception same above
	Di5098 string   `field:"5098"`
	Di5099 string   `field:"5099"`
	Un9901 []string `field:"9901"`
}

type Di3673 struct {
	Di3673 string   `field:"3673"` // diagnose_dd code
	Di3674 string   `field:"3674"` // diagnose_dd certainty
	Di3675 string   `field:"3675"` // diagnose_dd localization
	Di3676 []string `field:"3676"` // diagnose_dd explanation
	Di3677 []string `field:"3677"` // diagnose_dd exception
}
type GroupServicesCodeBefore2017 struct {
	ServiceCode    string `field:"4244"`
	AmountApproval string `field:"4245"`
	AmountBilled   string `field:"4246"`
}

type Ps4250 struct {
	Ps4250 string `field:"4250"`
	Ps4251 string `field:"4251"`
}
type GroupServiceCode struct {
	GroupServiceCode string `field:"4253"`
	AmountBilled     string `field:"4254"`
}
type CareGiverServiceCode struct {
	CareGiverServiceCode string `field:"4256"`
	AmountBilled         string `field:"4257"`
}

type ProofOfInsurance struct {
	OnlineCheckDate string `field:"3010"`
	ResultCode      string `field:"3011"`
	ErrorCode       string `field:"3012"`
	CheckDigit      string `field:"3013"`
}

func (s Schein) GetInsuranceInfo(ctx *titan.Context, context DataContext) *patient_profile_common.InsuranceInfo {
	dateFormat := context.Config.DateTimeFormat
	config := context.Config
	if s.InsuranceNumber != "" ||
		s.InsuranceNumberEGK != "" ||
		s.IkNumber != "" ||
		s.InsuranceCompanyName != "" {
		readCardDateInt, _ := util.ConvertStringToMillisecond(s.ReadCardDate, dateFormat)
		var startDateInt, endDateInt int64
		var readCardDatas []patient_profile_common.ReadCardModel
		insuranceNumber := function.If(s.InsuranceNumber != "", s.InsuranceNumber, s.InsuranceNumberEGK)
		if config.Version >= "2.9" {
			startDateInt, _ = util.ConvertStringToMillisecond(s.StartDate, dateFormat)
			endDateInt, _ = util.ConvertStringToMillisecond(s.EndDate, dateFormat)
		} else {
			endDateTime, endDateTimeErr := time.Parse(util.MMYY, s.EndDate)
			if endDateTimeErr == nil {
				endOfMonthTime := util.EndOfMonth(endDateTime)
				endDateInt = util.ConvertTimeToMiliSecond(endOfMonthTime, ctx.RequestTimeZone())
			}
		}
		if readCardDateInt != 0 {
			readCardDatas = append(readCardDatas, patient_profile_common.ReadCardModel{
				CdmVersion:         s.CdmVersion,
				RegistrationNumber: &s.RegistrationNumber,
				ReadCardDate:       readCardDateInt,
			})
		}
		specialGroup, specialGroupOk := SPECIAL_GROUP_MAPPER[s.StatusSupplement]
		DMPLabeling, DMPLabelingOk := card_service_common.MAP_DMP_LABELING[s.StatusSupplement]
		if specialGroupOk {
			DMPLabeling = "00"
		} else if DMPLabelingOk {
			specialGroup = patient_profile_common.SpecialGroup_00
		} else {
			specialGroup = patient_profile_common.SpecialGroup_00
			DMPLabeling = "00"
		}
		if config.Version >= "2.9" {
			specialGroup = function.If(s.SpecialGroup != "", patient_profile_common.SpecialGroupDescription(s.SpecialGroup), patient_profile_common.SpecialGroup_00)
			DMPLabeling = function.If(s.DMPLabeling != "", s.DMPLabeling, "00")
		}
		newInsurance := patient_profile_common.InsuranceInfo{
			Id:                   uuid.New(),
			ReadCardDatas:        readCardDatas,
			InsuranceNumber:      &insuranceNumber,
			Wop:                  s.Wop,
			InsuranceStatus:      patient_profile_common.InsuranceStatus(s.InsuranceStatus),
			StartDate:            function.If(startDateInt != 0, &startDateInt, nil),
			EndDate:              function.If(endDateInt != 0, &endDateInt, nil),
			IkNumber:             s.GetIkNumber(),
			InsuranceCompanyName: s.InsuranceCompanyName,
			InsuranceCompanyId:   s.G4104,
			SpecialGroup:         specialGroup,
			DMPLabeling:          DMPLabeling,
			FeeCatalogue:         catalog_sdkt_common.FeeCatalogue(s.FeeCatalogue),
			IsActive:             true,
			InsuranceType:        patient_profile_common.Public,
		}

		return &newInsurance
	}
	return nil
}

func (s Schein) GetIkNumber() int32 {
	if len(s.IkNumber) == 7 {
		s.IkNumber = "10" + s.IkNumber
	}
	ikNumber, _ := strconv.ParseInt(s.IkNumber, 10, 32)

	return int32(ikNumber)
}
