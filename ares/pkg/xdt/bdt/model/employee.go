package bdt_model

import (
	"git.tutum.dev/medi/tutum/ares/app/admin/api/admin_bff"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_common"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"github.com/google/uuid"
)

type BDTEmployee struct {
	FirstName string
	LastName  string
	Id        uuid.UUID
	Title     string
	Initial   string
	Bsnrs     []bsnr_common.BSNR
	Lanr      string
	Contracts []*admin_bff.Contract
}

type EmployeeSlice []BDTEmployee

func (*BDTEmployee) GetSalutation() string {
	return ""
}

func (r *BDTEmployee) GetTitle() string {
	if r == nil {
		return ""
	}
	return r.Title
}

func (*BDTEmployee) GetAdditionalName() string {
	return ""
}

func (r *BDTEmployee) GetFirstName() string {
	if r == nil {
		return ""
	}
	return r.FirstName
}

func (*BDTEmployee) GetIntentWord() string {
	return ""
}

func (r *BDTEmployee) GetLastName() string {
	if r == nil {
		return ""
	}
	return r.LastName
}

func (r *BDTEmployee) UpsertContract(contract *admin_bff.Contract) {
	if r == nil {
		return
	}
	if slice.FindOne(r.Contracts, func(c *admin_bff.Contract) bool {
		return c.ContractId == contract.ContractId
	}) != nil {
		return
	}
	r.Contracts = append(r.Contracts, contract)
}
