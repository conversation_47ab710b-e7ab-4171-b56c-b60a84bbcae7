package bdt_model

import (
	"strings"

	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_common"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"github.com/google/uuid"
)

type DataContext struct {
	HeaderData       *BDTHeader                 `json:"headerData"`
	Config           Config                     `json:"config"`
	EmployeeId       *uuid.UUID                 `json:"employeeId"`
	Employees        []BDTEmployee              `json:"employees"`
	ScheinMap        map[string][]Schein        `json:"scheinMap"`
	PrivateScheinMap map[string][]PrivateSchein `json:"privateScheinMap"`
	BGScheinMap      map[string][]BGSchein      `json:"bgScheinMap"`
	PatientIdMap     map[string]*uuid.UUID      `json:"patientIdMap"`
	ScheinIdMap      map[string]*uuid.UUID      `json:"scheinIdMap"`     // scheinNumber -> scheinId in repo
	ScheinNumberMap  map[string][]string        `json:"scheinNumberMap"` // patientNumber : scheinNumber[]
	BsnrIds          []*uuid.UUID               `json:"bsnrIds"`
}

func (d *DataContext) SearchTreatmentDoctor(name string) *BDTEmployee {
	return slice.FindOne(d.Employees, func(employee BDTEmployee) bool {
		return strings.Contains(name, employee.FirstName) && strings.Contains(name, employee.LastName)
	})
}

func (d *DataContext) SearchDoctorByLanr(lanr string) *BDTEmployee {
	return slice.FindOne(d.Employees, func(employee BDTEmployee) bool {
		return employee.Lanr == lanr
	})
}

func (e *BDTEmployee) GetBsnrIdByCode(bsnrCode string) *uuid.UUID {
	if e == nil {
		return nil
	}
	bsnr := slice.FindOne(e.Bsnrs, func(bsnr bsnr_common.BSNR) bool {
		return bsnr.Code == bsnrCode
	})
	if bsnr == nil {
		return nil
	}
	return bsnr.Id
}

func (d *DataContext) UpsertEmployee(emp BDTEmployee) {
	if d == nil {
		return
	}
	_, index := slice.FindOneIndex(d.Employees, func(e BDTEmployee) bool {
		return e.Id == emp.Id
	})
	if index == -1 {
		d.Employees = append(d.Employees, emp)
	} else {
		d.Employees[index] = emp
	}
}
