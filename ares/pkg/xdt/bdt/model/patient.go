package bdt_model

import (
	"strconv"
	"strings"
	"time"

	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"gitlab.com/silenteer-oss/titan"
)

var GenderMapper = map[string]patient_profile_common.Gender{
	"1": patient_profile_common.M,
	"2": patient_profile_common.W,
	"3": patient_profile_common.X,
	"M": patient_profile_common.M,
	"W": patient_profile_common.W,
	"U": patient_profile_common.U,
	"X": patient_profile_common.X,
	"D": patient_profile_common.D,
}

var SPECIAL_GROUP_MAPPER = map[string]patient_profile_common.SpecialGroupDescription{
	"1": patient_profile_common.SpecialGroup_00,
	"4": patient_profile_common.SpecialGroup_04,
	"6": patient_profile_common.SpecialGroup_06,
	"7": patient_profile_common.SpecialGroup_07,
	"8": patient_profile_common.SpecialGroup_08,
	"9": patient_profile_common.SpecialGroup_00,
}

// Type        string       `field:"8000"`
// Length      string       `field:"8100"`
type PatientData struct {
	Type   string `field:"8000"`
	Length string `field:"8100"`
	// ArztkennzeichnungVOR           string  `field:"5xxx"`
	BsnrCode           string   `field:"5098"`
	Lanr               string   `field:"5099"`
	Un9901s            []string `field:"9901"`
	PatientNumber      string   `field:"3000"`
	Un3001             string   `field:"3001"`
	CardType           string   `field:"3004"`
	CdmVersion         string   `field:"3006"`
	AdditionalName     string   `field:"3100"`
	IntendWord         string   `field:"3120"`
	LastName           string   `field:"3101"`
	FirstName          string   `field:"3102"`
	DateOfBirth        string   `field:"3103"`
	Title              string   `field:"3104"`
	InsuranceNumber    string   `field:"3105"` // kvk //map either 3105 or 3119 to insurancenumber
	InsuranceNumberEGK string   `field:"3119"`

	Wohnort                       string `field:"3106"` // no available
	Street                        string `field:"3107"`
	Number                        string `field:"3109"`
	AdditionalAddressInfo         string `field:"3115"`
	PostCode                      string `field:"3112"`
	CountryCode                   string `field:"3114"`
	City                          string `field:"3113"`
	PostOfficeBoxPostCode         string `field:"3121"`
	PostOfficeBoxPlaceOfResidence string `field:"3122"`
	PostOfficeBoxOfficeBox        string `field:"3123"`
	PostOfficeBoxCountryCode      string `field:"3124"`
	Wop                           string `field:"3116"`
	InsuranceStatus               string `field:"3108"`
	// Gender                        string `field:"3110"` // <=2.8 recheck if file has 2 duplicate field gender, for now we assume only 1 field
	Gender string `field:"3110"` //2 field duplicate >2.9
	Un3200 string `field:"3200"` // no available
	Un3211 string `field:"3211"`
	Un3201 string `field:"3201"`
	Un3202 string `field:"3202"` // Vorname des Vers.
	Un3203 string `field:"3203"` // Geburtsdatum des Vers.
	Un3204 string `field:"3204"` // Wohnort des Vers.
	Un3205 string `field:"3205"` // Straße des Vers.
	Un3212 string `field:"3212"` // Hausnummer des Vers.
	Un3213 string `field:"3213"` // Anschriftenzusatz des Vers.
	Un3206 string `field:"3206"` // Titel des Vers.
	Un3207 string `field:"3207"` // PLZ des Vers.
	Un3209 string `field:"3209"` // Wohnort des Vers
	Un3214 string `field:"3214"` // Wohnsitz-Ländercode des Vers.
	Un3210 string `field:"3210"` // Geschlecht des Vers. <2.8
	// Un3210             string `field:"3210"` // Geschlecht des Vers. > 2.8
	Un3221 string `field:"3221"` // Postfach PLZ des Vers.
	Un3222 string `field:"3222"` // Postfach Ort des Vers.
	Un3223 string `field:"3223"` // Postfach des Vers.
	Un3224 string `field:"3224"` // Postfach-Land des Vers.

	Roentgennummer           string   `field:"3601"`
	Archivnummer             string   `field:"3602"`
	BGNummer                 string   `field:"3603"`
	DatumPatientSeit         string   `field:"3610"`
	DatumVersicherungsbeginn string   `field:"3612"`
	Occupation               string   `field:"3620"`
	Employer                 string   `field:"3625"`
	PhoneNumbers             []string `field:"3626"`
	Nationality              string   `field:"3627"`
	Language                 string   `field:"3628"`
	Bsnr_Lanr                string   `field:"3630"` //BBBBBBBBB-LLLLLLLL

	Distance        string    `field:"3631"` //addressInfo.address.distance
	Contacts        []Contact `field:"3632"`
	TreatmentDoctor string    `field:"3635"`
	Un3637          string    `field:"3637"`
	Un3638          string    `field:"3638"`
	Un3639          string    `field:"3639"`
	Un3640          string    `field:"3640"`
	Un3645          string    `field:"3645"`

	Di3649 []DiagnosisDD `field:"3649"`
	Un3651 string        `field:"3651"`
	// Un5xxx  string `field:"5xxx"`
	Un3652 string `field:"3652"`
	Un0800 string `field:"0800"`
	Un0801 string `field:"0801"`
	Un0802 string `field:"0802"`
	Un0803 string `field:"0803"`
	Un0805 string `field:"0805"`

	Un0806 string `field:"0806"`
	Un0807 string `field:"0807"`
	Un0808 string `field:"0808"`
	Un0809 string `field:"0809"`
	Un0810 string `field:"0810"`
	Un0811 string `field:"0811"`
	Un0812 string `field:"0812"`
	Un0813 string `field:"0813"`
	// Un5xxx  string `field:"5xxx"`

	Un3653              string          `field:"3653"`
	Un3654              string          `field:"3654"`
	Un3655              string          `field:"3655"`
	Un3656              string          `field:"3656"`
	Un3657              string          `field:"3657"`
	Un3658              string          `field:"3658"`
	Un3659              string          `field:"3659"`
	Un3660              string          `field:"3660"`
	Un3661              string          `field:"3661"`
	Un3662              string          `field:"3662"`
	AmountOfBirth       string          `field:"3664"`
	AmountOfChildren    string          `field:"3666"`
	AmountOfPregnancies string          `field:"3668"`
	Un3669              string          `field:"3669"`
	Un3670              string          `field:"3670"`
	Un3672              string          `field:"3672"`
	Un3673              string          `field:"3673"`
	CustomField3700     CustomField3700 `field:"3700"`
	CustomField3702     CustomField3702 `field:"3702"`
	CustomField3704     CustomField3704 `field:"3704"`
	CustomField3706     CustomField3706 `field:"3706"`
	CustomField3708     CustomField3708 `field:"3708"`
	CustomField3710     CustomField3710 `field:"3710"`
	CustomField3712     CustomField3712 `field:"3712"`
	CustomField3714     CustomField3714 `field:"3714"`
	CustomField3716     CustomField3716 `field:"3716"`
	CustomField3718     CustomField3718 `field:"3718"`
	CustomField3720     CustomField3720 `field:"3720"`
	CustomField3722     CustomField3722 `field:"3722"`
	CustomField3724     CustomField3724 `field:"3724"`
	CustomField3726     CustomField3726 `field:"3726"`
	CustomField3728     CustomField3728 `field:"3728"`
	Un3710              string          `field:"3710"`
	Un3711              string          `field:"3711"`
	Un3712              string          `field:"3712"`
	Un3713              string          `field:"3713"`
	Un3714              string          `field:"3714"`
	Un3715              string          `field:"3715"`
	Un3716              string          `field:"3716"`
	Un3717              string          `field:"3717"`
	Un3718              string          `field:"3718"`
	Un3719              string          `field:"3719"`
	Un3720              string          `field:"3720"`
	Un3721              string          `field:"3721"`
	Un3722              string          `field:"3722"`
	Un3723              string          `field:"3723"`
	Un3724              string          `field:"3724"`
	Un3725              string          `field:"3725"`
	Un3726              string          `field:"3726"`
	Un3727              string          `field:"3727"`
	Un3728              string          `field:"3728"`
	Un3729              string          `field:"3729"`
	Raucheranamnese     string          `field:"3917"`
	Rauchertyp          string          `field:"3918"` // except JN remaining mean smoker
	G4104               string          `field:"4104"` // InsuranceCompanyId
	G4106               string          `field:"4106"`
	BillingType         string          `field:"4107"` // not available yet
	RegistrationNumber  string          `field:"4108"`
	ReadCardDate        string          `field:"4109"`
	StartDate           string          `field:"4133"`
	EndDate             string          `field:"4110"` //>2.9 length 8 // implement validation to check if 4109 exist this field will be mandatory and vice versa
	// BisDatumDerGueltigkeit         string `field:"4110"`
	IkNumber             string `field:"4111"`
	InsuranceCompanyName string `field:"4134"` // map to insuranceSnapshot.InsuranceCompanyName
	InsuranceType        string `field:"4112"` // not available
	StatusSupplement     string `field:"4113"` // in case < 2.9 and include special group, dmp labeling, base on card_service_common.KVKData
	SpecialGroup         string `field:"4131"`
	DMPLabeling          string `field:"4132"`
	FeeCatalogue         string `field:"4121"`
	Un4130               string `field:"4130"`
	Un4131               string `field:"4131"`
	Un4132               string `field:"4132"`
	Un4370               string `field:"4370"`
	Un4371               string `field:"4371"`
	Un4372               string `field:"4372"`
	Un4135               string `field:"4135"`
	Un4206               string `field:"4206"`
	Un4225               string `field:"4225"`
	Un4226               string `field:"4226"`
	Un4222               string `field:"4222"`
	Un4223               string `field:"4223"`
	Un4229               string `field:"4229"`
	Un4580               string `field:"4580"`
	BloodPressure        string `field:"6230"`
	Un0920               string `field:"0920"`
	Un0921               string `field:"0921"`
	Un0922               string `field:"0922"`
	Un0923               string `field:"0923"`
	Un0924               string `field:"0924"`
	Un0925               string `field:"0925"`
	Un0926               string `field:"0926"`
	Un0927               string `field:"0927"`
	Un0928               string `field:"0928"`
	Un0929               string `field:"0929"`
	Un0930               string `field:"0930"`
	Un0931               string `field:"0931"`
	// TNBID                          string `field:"5xxx"`
}

type DiagnosisDD struct {
	SelectedDate string   `field:"3649"`
	BsnrCode     string   `field:"5098"`
	Lanr         string   `field:"5099"`
	Un9901s      []string `field:"9901"`
	Di3650       string   `field:"3650"`
	Di6001       Di6001   `field:"6001"`
	Un0830       string   `field:"0830"` // 2 = "AD", 0,1 = "DD"
	// Un5xxx  string `field:"5xxx"`
}

// CustomField represents a pair of related fields
type CustomField struct {
	FirstField  string
	SecondField string
}

// Custom field structs with proper field tags
type CustomField3700 struct {
	FirstField  string `field:"3700"`
	SecondField string `field:"3701"`
}

type CustomField3702 struct {
	FirstField  string `field:"3702"`
	SecondField string `field:"3703"`
}

type CustomField3704 struct {
	FirstField  string `field:"3704"`
	SecondField string `field:"3705"`
}

type CustomField3706 struct {
	FirstField  string `field:"3706"`
	SecondField string `field:"3707"`
}

type CustomField3708 struct {
	FirstField  string `field:"3708"`
	SecondField string `field:"3709"`
}

type CustomField3710 struct {
	FirstField  string `field:"3710"`
	SecondField string `field:"3711"`
}

type CustomField3712 struct {
	FirstField  string `field:"3712"`
	SecondField string `field:"3713"`
}

type CustomField3714 struct {
	FirstField  string `field:"3714"`
	SecondField string `field:"3715"`
}

type CustomField3716 struct {
	FirstField  string `field:"3716"`
	SecondField string `field:"3717"`
}

type CustomField3718 struct {
	FirstField  string `field:"3718"`
	SecondField string `field:"3719"`
}

type CustomField3720 struct {
	FirstField  string `field:"3720"`
	SecondField string `field:"3721"`
}

type CustomField3722 struct {
	FirstField  string `field:"3722"`
	SecondField string `field:"3723"`
}

type CustomField3724 struct {
	FirstField  string `field:"3724"`
	SecondField string `field:"3725"`
}

type CustomField3726 struct {
	FirstField  string `field:"3726"`
	SecondField string `field:"3727"`
}

type CustomField3728 struct {
	FirstField  string `field:"3728"`
	SecondField string `field:"3729"`
}

type Contact struct {
	ContactType string   `field:"3632"`
	Value       []string `field:"3633"`
}

type CustomFieldPatientData struct {
	Status    string `field:"3700"`
	BirthName string `field:"3701"`
	Cave      string `field:"3702"`
	Address   string `field:"3703"`
}

func (pd *PatientData) MapField3700To3729() CustomFieldPatientData {
	// Convert all CustomField structs to base CustomField type
	fields := []CustomField{
		{FirstField: pd.CustomField3700.FirstField, SecondField: pd.CustomField3700.SecondField},
		{FirstField: pd.CustomField3702.FirstField, SecondField: pd.CustomField3702.SecondField},
		{FirstField: pd.CustomField3704.FirstField, SecondField: pd.CustomField3704.SecondField},
		{FirstField: pd.CustomField3706.FirstField, SecondField: pd.CustomField3706.SecondField},
		{FirstField: pd.CustomField3708.FirstField, SecondField: pd.CustomField3708.SecondField},
		{FirstField: pd.CustomField3710.FirstField, SecondField: pd.CustomField3710.SecondField},
		{FirstField: pd.CustomField3712.FirstField, SecondField: pd.CustomField3712.SecondField},
		{FirstField: pd.CustomField3714.FirstField, SecondField: pd.CustomField3714.SecondField},
		{FirstField: pd.CustomField3716.FirstField, SecondField: pd.CustomField3716.SecondField},
		{FirstField: pd.CustomField3718.FirstField, SecondField: pd.CustomField3718.SecondField},
		{FirstField: pd.CustomField3720.FirstField, SecondField: pd.CustomField3720.SecondField},
		{FirstField: pd.CustomField3722.FirstField, SecondField: pd.CustomField3722.SecondField},
		{FirstField: pd.CustomField3724.FirstField, SecondField: pd.CustomField3724.SecondField},
		{FirstField: pd.CustomField3726.FirstField, SecondField: pd.CustomField3726.SecondField},
		{FirstField: pd.CustomField3728.FirstField, SecondField: pd.CustomField3728.SecondField},
	}

	customFieldPatientData := CustomFieldPatientData{}
	// Filter out empty fields
	for _, field := range fields {
		if field.FirstField == "" && field.SecondField == "" {
			continue
		}

		fieldName := field.FirstField
		fieldValue := field.SecondField

		switch fieldName {
		case "Status":
			customFieldPatientData.Status = fieldValue
		case "Geburtsname":
			customFieldPatientData.BirthName = fieldValue
		case "Kommentar":
			if customFieldPatientData.Cave == "" {
				customFieldPatientData.Cave = fieldValue
			} else {
				customFieldPatientData.Cave = customFieldPatientData.Cave + ", " + fieldValue
			}
		case "Info":
			customFieldPatientData.Address = fieldValue
		}
	}

	return customFieldPatientData
}

func (pd *PatientData) ToPatientInfo(ctx *titan.Context, dataContext *DataContext) (*patient_profile_common.PatientInfo, error) {
	dateFormat := dataContext.Config.DateTimeFormat
	patientType := patient_profile_common.PatientType_Private
	contactInfo := patient_profile_common.ContactInfo{}

	if len(pd.PhoneNumbers) > 0 {
		contactInfo.PrimaryContactNumber = &pd.PhoneNumbers[0]
	}
	if len(pd.PhoneNumbers) > 1 {
		contactInfo.FurtherContactNumber = pd.PhoneNumbers[1:]
	}

	var treatmentDoctorId *uuid.UUID
	treatmentDoctor := dataContext.SearchTreatmentDoctor(pd.TreatmentDoctor)
	if treatmentDoctor != nil {
		treatmentDoctorId = &treatmentDoctor.Id
	}

	customFieldPatientData := pd.MapField3700To3729()
	contactInfo.Address = &customFieldPatientData.Address
	return &patient_profile_common.PatientInfo{
		InsuranceInfos: []patient_profile_common.InsuranceInfo{},
		GenericInfo: patient_profile_common.GenericInfo{
			PatientType: patientType,
		},
		PersonalInfo: patient_profile_common.PersonalInfo{
			IntendWord:      (*patient_profile_common.IntendWord)(&pd.IntendWord),
			AdditionalNames: pd.GetAdditionalNames(),
			LastName:        pd.LastName,
			FirstName:       pd.FirstName,
			DateOfBirth:     pd.ExtractDateOfBirth(dateFormat),
			Title:           &pd.Title,
			Gender:          GenderMapper[pd.Gender],
			Status:          &customFieldPatientData.Status,
			BirthName:       &customFieldPatientData.BirthName,
		},
		AddressInfo: patient_profile_common.AddressInfo{
			Address: patient_profile_common.Address{
				Street:                &pd.Street,
				Number:                &pd.Number,
				AdditionalAddressInfo: &pd.AdditionalAddressInfo,
				PostCode:              pd.PostCode,
				CountryCode:           &pd.CountryCode,
				City:                  &pd.City,
			},
		},
		PostOfficeBox: patient_profile_common.PostOfficeBox{
			PostCode:         pd.PostOfficeBoxPostCode,
			PlaceOfResidence: pd.PostOfficeBoxPlaceOfResidence,
			OfficeBox:        pd.PostOfficeBoxOfficeBox,
			CountryCode:      pd.PostOfficeBoxCountryCode,
		},
		ContactInfo: contactInfo,
		EmploymentInfo: patient_profile_common.EmploymentInfo{
			Occupation: pd.Occupation,
			CompanyAddress: patient_profile_common.CompanyAddress{
				Employer: func() *string {
					if pd.Employer != "" {
						return &pd.Employer
					}
					return nil
				}(),
			},
			IsEmployed: function.If(pd.Occupation != "", true, false),
		},
		DoctorInfo: patient_profile_common.DoctorInfo{
			TreatmentDoctorId: treatmentDoctorId,
		},
		OtherInfo: patient_profile_common.OtherInfo{
			Cave: customFieldPatientData.Cave,
		},
	}, nil
}

func (pd *PatientData) ExtractDateOfBirth(dateFormat string) patient_profile_common.DateOfBirth {
	dateString := pd.DateOfBirth
	date, err := time.Parse(dateFormat, dateString)

	if err != nil && dateFormat == util.YYYYMMDD {
		year, err := strconv.ParseInt(util.SubString(dateString, 0, 4), 10, 32)
		if err != nil {
			return patient_profile_common.DateOfBirth{}
		}
		month, err := strconv.ParseInt(util.SubString(dateString, 4, 2), 10, 32)
		if err != nil {
			return patient_profile_common.DateOfBirth{}
		}
		day, err := strconv.ParseInt(util.SubString(dateString, 6, 2), 10, 32)
		if err != nil {
			return patient_profile_common.DateOfBirth{}
		}
		return patient_profile_common.DateOfBirth{
			Year:       function.If(year != 0, util.NewPointer(int32(year)), nil),
			Month:      function.If(month != 0, util.NewPointer(int32(month)), nil),
			Date:       function.If(day != 0, util.NewPointer(int32(day)), nil),
			IsValidDOB: false,
		}
	} else if err != nil && dateFormat == util.DDMMYYYY {
		year, err := strconv.ParseInt(util.SubString(dateString, 4, 4), 10, 32)
		if err != nil {
			return patient_profile_common.DateOfBirth{}
		}
		month, err := strconv.ParseInt(util.SubString(dateString, 2, 2), 10, 32)
		if err != nil {
			return patient_profile_common.DateOfBirth{}
		}
		day, err := strconv.ParseInt(util.SubString(dateString, 0, 2), 10, 32)
		if err != nil {
			return patient_profile_common.DateOfBirth{}
		}
		return patient_profile_common.DateOfBirth{
			Year:       function.If(year != 0, util.NewPointer(int32(year)), nil),
			Month:      function.If(month != 0, util.NewPointer(int32(month)), nil),
			Date:       function.If(day != 0, util.NewPointer(int32(day)), nil),
			IsValidDOB: false,
		}
	}

	year, month, day := date.Date()

	return patient_profile_common.DateOfBirth{
		Year:       function.If(year != 0, util.NewPointer(int32(year)), nil),
		Month:      function.If(month != 0, util.NewPointer(int32(month)), nil),
		Date:       function.If(day != 0, util.NewPointer(int32(day)), nil),
		IsValidDOB: true,
	}
}

func (pd *PatientData) ToPatientMedicalData() *patient_profile_common.PatientMedicalData {
	if pd.AmountOfBirth != "" || pd.AmountOfChildren != "" || pd.AmountOfPregnancies != "" || pd.BloodPressure != "" || pd.Rauchertyp != "" {
		// Create medical data of patient
		AmountOfBirth, AmountOfBirthErr := strconv.Atoi(pd.AmountOfBirth)
		amountOfChildren, amountOfChildrenErr := strconv.ParseInt(pd.AmountOfChildren, 10, 32)
		AmountOfPregnancies, AmountOfPregnanciesErr := strconv.Atoi(pd.AmountOfPregnancies)
		return &patient_profile_common.PatientMedicalData{
			BloodPressure:       function.If(pd.BloodPressure != "", &pd.BloodPressure, nil),
			AmountOfBirth:       function.If(AmountOfBirthErr == nil, util.NewPointer(int64(AmountOfBirth)), nil),
			AmountOfChildren:    function.If(amountOfChildrenErr == nil, util.NewPointer(int32(amountOfChildren)), nil),
			AmountOfPregnancies: function.If(AmountOfPregnanciesErr == nil, util.NewPointer(int64(AmountOfPregnancies)), nil),
			IsSmoker:            function.If(pd.Rauchertyp != "JN", util.NewPointer(true), nil),
		}
	}

	return nil
}

var AdditionalNames = []patient_profile_common.AdditionalName{
	patient_profile_common.AdditionalName_Bar,
	patient_profile_common.AdditionalName_Baron,
	patient_profile_common.AdditionalName_Baroness,
	patient_profile_common.AdditionalName_Baronesse,
	patient_profile_common.AdditionalName_Baronin,
	patient_profile_common.AdditionalName_Brand,
	patient_profile_common.AdditionalName_Burggraf,
	patient_profile_common.AdditionalName_Burggrafin,
	patient_profile_common.AdditionalName_Condesa,
	patient_profile_common.AdditionalName_Earl,
	patient_profile_common.AdditionalName_Edle,
	patient_profile_common.AdditionalName_Edler,
	patient_profile_common.AdditionalName_Erbgraf,
	patient_profile_common.AdditionalName_Erbgrafin,
	patient_profile_common.AdditionalName_Erbprinz,
	patient_profile_common.AdditionalName_Erbprinzessin,
	patient_profile_common.AdditionalName_Ffr,
	patient_profile_common.AdditionalName_Freifr,
	patient_profile_common.AdditionalName_Freifraulein,
	patient_profile_common.AdditionalName_Freifrau,
	patient_profile_common.AdditionalName_Freih,
	patient_profile_common.AdditionalName_Freiherr,
	patient_profile_common.AdditionalName_Freiin,
	patient_profile_common.AdditionalName_Frf,
	patient_profile_common.AdditionalName_FrfDot,
	patient_profile_common.AdditionalName_Frfr,
	patient_profile_common.AdditionalName_FrfrDot,
	patient_profile_common.AdditionalName_Frh,
	patient_profile_common.AdditionalName_FrhDot,
	patient_profile_common.AdditionalName_Frhr,
	patient_profile_common.AdditionalName_FrhrDot,
	patient_profile_common.AdditionalName_Fst,
	patient_profile_common.AdditionalName_FstDot,
	patient_profile_common.AdditionalName_Fstn,
	patient_profile_common.AdditionalName_FstnDot,
	patient_profile_common.AdditionalName_Furst,
	patient_profile_common.AdditionalName_Furstin,
	patient_profile_common.AdditionalName_Gr,
	patient_profile_common.AdditionalName_Graf,
	patient_profile_common.AdditionalName_Grafin,
	patient_profile_common.AdditionalName_Grf,
	patient_profile_common.AdditionalName_Grfn,
	patient_profile_common.AdditionalName_Grossherzog,
	patient_profile_common.AdditionalName_GroBherzog,
	patient_profile_common.AdditionalName_Grossherzogin,
	patient_profile_common.AdditionalName_GroBherzogin,
	patient_profile_common.AdditionalName_Herzog,
	patient_profile_common.AdditionalName_Herzogin,
	patient_profile_common.AdditionalName_Jhr,
	patient_profile_common.AdditionalName_JhrDot,
	patient_profile_common.AdditionalName_Jonkheer,
	patient_profile_common.AdditionalName_Junker,
	patient_profile_common.AdditionalName_Landgraf,
	patient_profile_common.AdditionalName_Landgrafin,
	patient_profile_common.AdditionalName_Markgraf,
	patient_profile_common.AdditionalName_Markgrafin,
	patient_profile_common.AdditionalName_Marques,
	patient_profile_common.AdditionalName_Marquis,
	patient_profile_common.AdditionalName_Marschall,
	patient_profile_common.AdditionalName_Ostoja,
	patient_profile_common.AdditionalName_Prinz,
	patient_profile_common.AdditionalName_Prinzessin,
	patient_profile_common.AdditionalName_Przin,
	patient_profile_common.AdditionalName_Rabe,
	patient_profile_common.AdditionalName_Reichsgraf,
	patient_profile_common.AdditionalName_Reichsgrafin,
	patient_profile_common.AdditionalName_Ritter,
	patient_profile_common.AdditionalName_Rr,
	patient_profile_common.AdditionalName_Truchsess,
	patient_profile_common.AdditionalName_TruchseB,
}

func (pd *PatientData) GetAdditionalNames() []patient_profile_common.AdditionalName {
	if pd.AdditionalName == "" {
		return []patient_profile_common.AdditionalName{}
	}

	parts := strings.Split(pd.AdditionalName, " ")
	var res []patient_profile_common.AdditionalName
	for _, part := range parts {
		for _, additionalName := range AdditionalNames {
			if strings.EqualFold(part, string(additionalName)) {
				res = append(res, additionalName)
			}
		}
	}

	return res
}
