package bdt_model

type Treatment struct {
	Type          string   `field:"8000"`
	Length        string   `field:"8100"`
	PatientNumber string   `field:"3000"`
	Height        string   `field:"3622"`
	Weight        string   `field:"3623"`
	Un6200        []Un6200 `field:"6200"`
}
type Un6200 struct {
	Un6200   string   `field:"6200"`
	BsnrCode string   `field:"5098"`
	Lanr     string   `field:"5099"`
	Un9901   []string `field:"9901"`
	// 5xxx       string   `field:"5xxx"`
	ActualDiagnosis        []string                `field:"6205"`
	Di6001                 Di6001                  `field:"6001"`
	Medicines              []Medicine              `field:"6210"`
	Un6211                 []Medicine6211          `field:"6211"`
	Un6212                 []Medicine6212          `field:"6212"`
	MedicinesGRez          []MedicineGRez          `field:"6213"`
	MedicinesTPrescription []MedicineTPrescription `field:"6214"`
	MedicinesKRez_6215     []MedicineKRez_6215     `field:"6215"`
	Un6220                 []string                `field:"6220"`
	Un6221                 []string                `field:"6221"`
	Un6222                 []string                `field:"6222"`
	Un6225                 []string                `field:"6225"`
	Un6230                 []string                `field:"6230"`
	Un6240                 []string                `field:"6240"`
	Un6260                 []string                `field:"6260"`
	Un6265                 []string                `field:"6265"`
	Un6280                 []Un6280                `field:"6280"`
	Un6285                 []Un6285                `field:"6285"`
	Un6290                 []Un6290                `field:"6290"`
	Un6295                 []Un6295                `field:"6295"`
	Un6300                 []Un6300                `field:"6300"`
	Un6306                 []Un6306                `field:"6306"`
	Un6308                 []Un6308                `field:"6308"`
	Un6310s                []Un6310                `field:"6310"`
	Un6325                 []Un6325                `field:"6325"`
	Un6330                 string                  `field:"6330"`
	Un6331                 []string                `field:"6331"`
	Un6332                 string                  `field:"6332"`
	Un6333                 []string                `field:"6333"`
	Un6334                 []Un6334                `field:"6334"`
	Un6336                 []Un6336                `field:"6336"`
	Un6338                 []Un6338                `field:"6338"`
	Un6340                 []Un6340                `field:"6340"`
	Un6342                 []Un6342                `field:"6342"`
	Un6344                 []Un6344                `field:"6344"`
	Un6346                 []Un6346                `field:"6346"`
	Un6348                 []Un6348                `field:"6348"`
	Un6350                 []Un6350                `field:"6350"`
	Un6352                 []Un6352                `field:"6352"`
	Un6354                 []Un6354                `field:"6354"`
	Un6356                 []Un6356                `field:"6356"`
	Un6358                 []Un6358                `field:"6358"`
	Un6360                 []Un6360                `field:"6360"`
	Un6362                 []Un6362                `field:"6362"`
	Un6364                 []Un6364                `field:"6364"`
	Un6366                 []Un6366                `field:"6366"`
	Un6368                 []Un6368                `field:"6368"`
	Un6370                 []Un6370                `field:"6370"`
	Un6372                 []Un6372                `field:"6372"`
	Un6374                 []Un6374                `field:"6374"`
	Un6376                 []Un6376                `field:"6376"`
	Un6378                 []Un6378                `field:"6378"`
	Un6380                 []Un6380                `field:"6380"`
	Un6382                 []Un6382                `field:"6382"`
	Un6384                 []Un6384                `field:"6384"`
	Un6386                 []Un6386                `field:"6386"`
	Un6388                 []Un6388                `field:"6388"`
	Un6390                 []Un6390                `field:"6390"`
	Un6392                 []Un6392                `field:"6392"`
	Un6394                 []Un6394                `field:"6394"`
	Un6396                 []Un6396                `field:"6396"`
	Un6398                 []Un6398                `field:"6398"`
	Un6411                 string                  `field:"6411"`
	Un6412                 string                  `field:"6412"`
	Un6413                 string                  `field:"6413"`
	Un6414                 string                  `field:"6414"`
	Un6415                 string                  `field:"6415"`
	Un6416                 string                  `field:"6416"`
	Un6417                 string                  `field:"6417"`
	Un6418                 string                  `field:"6418"`
	Un8401                 []Un8401                `field:"8401"`
	Un8410                 []Un8410                `field:"8410"`
	Un8490                 []string                `field:"8490"`
	Un8990                 string                  `field:"8990"`
	//5xxx          string        `field:"5xxx"`
}

type Medicine struct {
	Name string `field:"6210"`
	MedicineCommon
}
type MedicineCommon struct {
	Pzn                string   `field:"0800"`
	Price              string   `field:"0801"`
	MedicationType     string   `field:"0802"`
	CurrentFormType    string   `field:"0803"`
	Nop                string   `field:"0805"`
	Isfreecopayment    string   `field:"0806"` // checkbox_gebuhrfrei will be true if age of patient larger than 18
	AutIdem1           string   `field:"0807"` // checkbox_autIdem1
	Quantity           string   `field:"0808"`
	Dosageform         string   `field:"0809"`
	Un0810             string   `field:"0810"` // ?? don't know
	IntakeIntervalText string   `field:"0811"`
	Un0812             []string `field:"0812"` // heimi DIAGNOSIS additional info ?? dont know
	CreatedAt          string   `field:"0813"` // createdAt
	Un0814             string   `field:"0814"`
	Un0815             string   `field:"0815"`
	Un0816             string   `field:"0816"`
	ScheinNumber       string   `field:"0820"` // schein number
	Un5098             string   `field:"5098"`
	Un5099             string   `field:"5099"`
	Un9901             []string `field:"9901"`
}

type MedicineKRez_6210 struct {
	Name string `field:"6210"`
	MedicineCommon
}
type Medicine6211 struct {
	Name string `field:"6211"`
	MedicineCommon
}

type Medicine6212 struct {
	Name string `field:"6212"`
	MedicineCommon
}
type MedicineGRez struct {
	Name string `field:"6213"`
	MedicineCommon
}

type MedicineTPrescription struct {
	Name string `field:"6214"`
	MedicineCommon
}

type MedicineKRez_6215 struct {
	Name string `field:"6215"`
	MedicineCommon
}

type Un6280 struct {
	Un6280 []string `field:"6280"`
	Un5098 string   `field:"5098"`
	Un5099 string   `field:"5099"`
	Un9901 []string `field:"9901"`
}
type Un6285 struct {
	Un6285 string   `field:"6285"`
	Un6286 []string `field:"6286"`
}
type Un6290 struct {
	Un6290 string   `field:"6290"`
	Un6291 []string `field:"6291"`
	Un5098 string   `field:"5098"`
	Un5099 string   `field:"5099"`
	Un9901 []string `field:"9901"`
}
type Un6295 struct {
	Un6295 string   `field:"6295"`
	Un6296 []Un6296 `field:"6296"`
}
type Un6296 struct {
	Un6296 string   `field:"6296"`
	Un6297 []string `field:"6297"`
}
type Un6300 struct {
	Un6300 string   `field:"6300"`
	Un6301 []string `field:"6301"`
}
type Un6306 struct {
	Un6306 string   `field:"6306"`
	Un6307 []string `field:"6307"`
}
type Un6308 struct {
	Un6308 string `field:"6308"`
	Un6309 string `field:"6309"`
}
type Un6310 struct {
	Un6310 string   `field:"6310"`
	Un6311 string   `field:"6311"`
	Un6312 string   `field:"6312"`
	Un6313 string   `field:"6313"`
	Un6314 string   `field:"6314"`
	Un6320 string   `field:"6320"`
	Un6321 string   `field:"6321"`
	Un6322 string   `field:"6322"`
	Un5098 string   `field:"5098"`
	Un5099 string   `field:"5099"`
	Un9901 []string `field:"9901"`
}

type Un6325 struct {
	Un6325 string `field:"6325"`
	Un6326 string `field:"6326"`
	Un6327 string `field:"6327"`
	Un6328 string `field:"6328"`
}
type Un6334 struct {
	Un6334 string   `field:"6334"`
	Un6335 []string `field:"6335"`
}

type Un6336 struct {
	Un6336 string   `field:"6336"`
	Un6337 []string `field:"6337"`
}

type Un6338 struct {
	Un6338 string   `field:"6338"`
	Un6339 []string `field:"6339"`
}

type Un6340 struct {
	Un6340 string   `field:"6340"`
	Un6341 []string `field:"6341"`
}

type Un6342 struct {
	Un6342 string   `field:"6342"`
	Un6343 []string `field:"6343"`
}

type Un6344 struct {
	Un6344 string   `field:"6344"`
	Un6345 []string `field:"6345"`
}

type Un6346 struct {
	Un6346 string   `field:"6346"`
	Un6347 []string `field:"6347"`
}

type Un6348 struct {
	Un6348 string   `field:"6348"`
	Un6349 []string `field:"6349"`
}

type Un6350 struct {
	Un6350 string   `field:"6350"`
	Un6351 []string `field:"6351"`
}

type Un6352 struct {
	Un6352 string   `field:"6352"`
	Un6353 []string `field:"6353"`
}

type Un6354 struct {
	Un6354 string   `field:"6354"`
	Un6355 []string `field:"6355"`
}

type Un6356 struct {
	Un6356 string   `field:"6356"`
	Un6357 []string `field:"6357"`
}

type Un6358 struct {
	Un6358 string   `field:"6358"`
	Un6359 []string `field:"6359"`
}

type Un6360 struct {
	Un6360 string   `field:"6360"`
	Un6361 []string `field:"6361"`
}

type Un6362 struct {
	Un6362 string   `field:"6362"`
	Un6363 []string `field:"6363"`
}

type Un6364 struct {
	Un6364 string   `field:"6364"`
	Un6365 []string `field:"6365"`
}

type Un6366 struct {
	Un6366 string   `field:"6366"`
	Un6367 []string `field:"6367"`
}

type Un6368 struct {
	Un6368 string   `field:"6368"`
	Un6369 []string `field:"6369"`
}

type Un6370 struct {
	Un6370 string   `field:"6370"`
	Un6371 []string `field:"6371"`
}

type Un6372 struct {
	Un6372 string   `field:"6372"`
	Un6373 []string `field:"6373"`
}

type Un6374 struct {
	Un6374 string   `field:"6374"`
	Un6375 []string `field:"6375"`
}

type Un6376 struct {
	Un6376 string   `field:"6376"`
	Un6377 []string `field:"6377"`
}

type Un6378 struct {
	Un6378 string   `field:"6378"`
	Un6379 []string `field:"6379"`
}

type Un6380 struct {
	Un6380 string   `field:"6380"`
	Un6381 []string `field:"6381"`
}

type Un6382 struct {
	Un6382 string   `field:"6382"`
	Un6383 []string `field:"6383"`
}

type Un6384 struct {
	Un6384 string   `field:"6384"`
	Un6385 []string `field:"6385"`
}

type Un6386 struct {
	Un6386 string   `field:"6386"`
	Un6387 []string `field:"6387"`
}

type Un6388 struct {
	Un6388 string   `field:"6388"`
	Un6389 []string `field:"6389"`
}

type Un6390 struct {
	Un6390 string   `field:"6390"`
	Un6391 []string `field:"6391"`
}

type Un6392 struct {
	Un6392 string   `field:"6392"`
	Un6393 []string `field:"6393"`
}

type Un6394 struct {
	Un6394 string   `field:"6394"`
	Un6395 []string `field:"6395"`
}

type Un6396 struct {
	Un6396 string   `field:"6396"`
	Un6397 []string `field:"6397"`
}

type Un6398 struct {
	Un6398 string   `field:"6398"`
	Un6399 []string `field:"6399"`
}
type Un8401 struct {
	Un8401 string   `field:"8401"`
	Un8402 []string `field:"8402"`
	Un8410 []Un8410 `field:"8410"`
}
type Un8410 struct {
	Un8410 string   `field:"8410"`
	Un8411 string   `field:"8411"`
	Un8418 string   `field:"8418"`
	Un8420 string   `field:"8420"`
	Un8421 string   `field:"8421"`
	Un8422 string   `field:"8422"`
	Un8428 string   `field:"8428"`
	Un8429 string   `field:"8429"`
	Un8430 string   `field:"8430"`
	Un8431 string   `field:"8431"`
	Un8432 string   `field:"8432"`
	Un8433 string   `field:"8433"`
	Un8440 string   `field:"8440"`
	Un8441 string   `field:"8441"`
	Un8442 string   `field:"8442"`
	Un8443 string   `field:"8443"`
	Un8444 string   `field:"8444"`
	Un8445 string   `field:"8445"`
	Un8446 string   `field:"8446"`
	Un8447 string   `field:"8447"`
	Un8460 []string `field:"8460"`
	Un8461 string   `field:"8461"`
	Un8462 string   `field:"8462"`
	Un8470 []string `field:"8470"`
	Un8480 []string `field:"8480"`
	Un8520 string   `field:"8520"`
	Un8521 string   `field:"8521"`
}
