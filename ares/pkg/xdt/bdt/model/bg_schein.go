package bdt_model

type BGSchein struct {
	Type   string `field:"8000"`
	Length string `field:"8100"`
	// Software      string `field:"5xxx"`
	BsnrCode           string   `field:"5098"`
	Lanr               string   `field:"5099"`
	Un9901             []string `field:"9901"`
	Un3002             string   `field:"3002"` // Case number
	ScheinNumber       string   `field:"3003"` // Scheinnummer in bdt the field come after patient number
	PatientNumber      string   `field:"3000"`
	AdditionalName     string   `field:"3100"`
	IntendWord         string   `field:"3120"`
	LastName           string   `field:"3101"`
	FirstName          string   `field:"3102"`
	DateOfBirth        string   `field:"3103"`
	Title              string   `field:"3104"`
	InsuranceNumber    string   `field:"3105"` // kvk //map either 3105 or 3119 to insurancenumber
	InsuranceNumberEGK string   `field:"3119"` // egk

	Wohnort                       string `field:"3106"` // no available
	Street                        string `field:"3107"`
	Number                        string `field:"3109"`
	AdditionalAddressInfo         string `field:"3115"`
	PostCode                      string `field:"3112"`
	CountryCode                   string `field:"3114"`
	City                          string `field:"3113"`
	PostOfficeBoxPostCode         string `field:"3121"`
	PostOfficeBoxPlaceOfResidence string `field:"3122"`
	PostOfficeBoxOfficeBox        string `field:"3123"`
	PostOfficeBoxCountryCode      string `field:"3124"`
	Wop                           string `field:"3116"`
	InsuranceStatus               string `field:"3108"`
	// Gender                        string `field:"3110"` // <=2.8 recheck if file has 2 duplicate field gender, for now we assume only 1 field
	Gender    string `field:"3110"` //2 field duplicate >2.9
	Employer  string `field:"3150"` // no available
	UVCarrier string `field:"3152"` // no available

	Un3200 string `field:"3200"` // no available
	Un3211 string `field:"3211"`
	Un3201 string `field:"3201"`
	Un3202 string `field:"3202"` // Vorname des Vers.
	Un3203 string `field:"3203"` // Geburtsdatum des Vers.
	Un3204 string `field:"3204"` // Wohnort des Vers.
	Un3205 string `field:"3205"` // Straße des Vers.
	Un3212 string `field:"3212"` // Hausnummer des Vers.
	Un3213 string `field:"3213"` // Anschriftenzusatz des Vers.
	Un3206 string `field:"3206"` // Titel des Vers.
	Un3207 string `field:"3207"` // PLZ des Vers.
	Un3209 string `field:"3209"` // Wohnort des Vers
	Un3214 string `field:"3214"` // Wohnsitz-Ländercode des Vers.
	Un3210 string `field:"3210"` // Geschlecht des Vers. <2.8
	// Un3210             string `field:"3210"` // Geschlecht des Vers. > 2.8
	Un3221           string `field:"3221"` // Postfach PLZ des Vers.
	Un3222           string `field:"3222"` // Postfach Ort des Vers.
	Un3223           string `field:"3223"` // Postfach des Vers.
	Un3224           string `field:"3224"` // Postfach-Land des Vers.
	TreatmentDoctor  string `field:"3635"` // Arztzuordung
	YearQuarter      string `field:"4101"` // QJJJJ split to save in db
	VKNumber         string `field:"4104"` // VK-Nummer
	CostUnitSubGroup string `field:"4106"` // Kostenträger-Untergruppe
	ScheinStart      string `field:"4150"`
	ScheinEnd        string `field:"4151"`
	BillingDate      string `field:"4152"`
	IkNumber         string `field:"4111"`

	ReportType            string `field:"4490"` // Statutory deduction for inpatient treatment according to § 6a GOÄ
	AccidentDate          string `field:"4500"`
	AccidentTime          string `field:"4501"` // HHMM
	ArrivedDate           string `field:"4502"`
	ArrivedTime           string `field:"4503"` // HHMM
	StartOfWorkingTime    string `field:"4504"` // HHMM
	AccidentLocation      string `field:"4505"`
	EmployedAs            string `field:"4506"`
	EmployedSinceDate     string `field:"4507"` // DDMM
	Nationality           string `field:"4508"`
	AccidentCompany       string `field:"4509"`
	AccidentCircumstances string `field:"4510"`

	BehaviorInjuredPerson           []string `field:"4512"`
	InitialTreatmentOnDate          string   `field:"4513"`
	InitialTreatmentTreatmentBy     string   `field:"4514"`
	TypeOfThisFirstMedicalTreatment string   `field:"4515"`

	AlcoholInfluence                      string   `field:"4520"` // 1 = no, 2 = yes
	SignsAlcoholInfluence                 []string `field:"4521"` // ≤60
	BloodSample                           string   `field:"4522"` // 1 = no, 2 = yes
	MandatoryAppointmentWithADDoctor      string   `field:"4524"` // 1 = no, 2 = yes F1050 01/2004
	DateOfAppointmentWithADDoctor         string   `field:"4525"` // wenn 4524=2
	BecauseSickLeaveAfterAccident         string   `field:"4526"` // 1 = checked if 4524=2
	TreatmentMoreThan1Week                string   `field:"4527"` // 1 = checked if 4524=2
	PrescriptionOfMedicinalProducts       string   `field:"4528"` // 1 = checked if 4524=2
	RecurrenceOfIllness                   string   `field:"4529"` // 1 = checked if 4524=2
	Findings                              []string `field:"4530"`
	Diagnosis                             []string `field:"4535"`
	ICD                                   string   `field:"4536"`
	ICPM                                  string   `field:"4537"`
	AOClassification                      string   `field:"4538"`
	XRayResult                            []string `field:"4540"`
	FurtherInformation                    []string `field:"4545"`
	TypeOfInitialInsuranceByDDoctor       []string `field:"4550"`
	ChangesInIllnessIndependentOfAccident []string `field:"4551"`

	ConcernsAboutTheInsurersDetails                                    string   `field:"4552"` // 1 = no, 2 = yes
	ConditionAboutThePresenceOfWorkplaceAccident                       []string `field:"4554"` // falls 4552 = 2
	AdviceToTheInsuredByAnEmployeeOfTheAccidentInsuranceProvider       string   `field:"4556"` // 1 = no, 2 = yes
	ReasonForTheAssumption                                             []string `field:"4557"` // falls 4556 = 2
	AbleToWork                                                         string   `field:"4560"` // 1 = checked
	AbleToWorkAgainFromDate                                            string   `field:"4561"` // falls 4560 = 1
	InvalidityCertificateIssued                                        string   `field:"4562"` // 1 = checked
	IsThereAnInjuryAccordingToTheListOfInjuryTypes                     string   `field:"4563"` // 1 = no, 2 = yes
	Digit                                                              string   `field:"4564"` // falls 4563 = 2
	NoMedicalTreatmentIsProvidedAtTheExpenseOfTheAccidentInsuranceFund string   `field:"4565"` // 1 = checked
	Reason                                                             []string `field:"4566"` // falls 4565 = 1
	IsTheInvolvementOfConsultantPhysiciansNecessary                    string   `field:"4567"` // 1 = no, 2 = yes
	NameOfConsultantPhysician                                          string   `field:"4568"` // falls 4567 = 2
	InjuryNumberFromTheCatalog                                         string   `field:"4569"`
	SpecialMedicalTreatmentRequired                                    string   `field:"4570"` // 1 = outpatient, 2 = inpatient
	SpecialMedicalTreatmentBy                                          string   `field:"4571"` // 1 = self, 2 = other D-doctor, if 4570 <> blank
	AddressOfTheTreatingPhysician                                      []string `field:"4572"` // ≤ 60 if 4571 or 4581 = 2
	IncapacityForWorkDate                                              string   `field:"4573"` // 1 = checked if 4570 <> blank
	ExpectedDurationOfIncapacityForWork                                string   `field:"4574"` // 3 TTT if 4570 <> blank
	ExpectedDurationOfIncapacityForWorkLongerThan6Months               string   `field:"4575"` // 1 = checked if 4570 <> blank longer than 6 months
	GeneralMedicalTreatmentBy                                          string   `field:"4581"` // 1 = self, 2 = other doctor, if 4580 = 40
	IncapacityForWorkMoreThan3Days                                     string   `field:"4582"` // 1 = checked
	IncapacityForWorkCertifiedUntilDate                                string   `field:"4583"`
	FollowUpExaminationRequiredDate                                    string   `field:"4584"` //
	ExplanationForFollowUpExamination                                  string   `field:"4585"`
	LongTermCareInsurance                                              string   `field:"4590"`
	ConsecutiveNumber                                                  string   `field:"4591"` // ≤ 60

	EndWorkingTime                      string   `field:"4592"` // HHMM
	FileNumberStr                       string   `field:"4593"`
	VisualAcuityWithoutCorrectionRight  string   `field:"4701"` // K a ≤60 Ophthalmologist's report
	VisualAcuityWithoutCorrectionLeft   string   `field:"4702"` // K a ≤60 Ophthalmologist's report
	VisualAcuityWithCorrectionFarRight  string   `field:"4711"` // K a ≤60 Ophthalmologist's report
	VisualAcuityWithCorrectionFarLeft   string   `field:"4712"` // K a ≤60 Ophthalmologist's report
	RefractionSphereFarRight            string   `field:"4713"` // K a ≤60 Ophthalmologist's report
	RefractionSphereFarLeft             string   `field:"4714"` // K a ≤60 Ophthalmologist's report
	RefractionCylFarRight               string   `field:"4715"` // K a ≤60 Ophthalmologist report
	RefractionCylFarLeft                string   `field:"4716"` // K a ≤60 Ophthalmologist report
	RefractionAxisFarRight              string   `field:"4717"` // K a ≤60 Ophthalmologist report
	RefractionAxisFarLeft               string   `field:"4718"` // K a ≤60 Ophthalmologist report
	VisualAcuityWithCorrectionNearRight string   `field:"4721"` // K a ≤60 Ophthalmologist report
	VisualAcuityWithCorrectionNearLeft  string   `field:"4722"` // K a ≤60 Ophthalmologist report
	RefractionSphereNearRight           string   `field:"4723"` // K a ≤60 Ophthalmologist report
	RefractionSphereNearLeft            string   `field:"4724"` // K a ≤60 Ophthalmologist report
	RefractionCylNearRight              string   `field:"4725"` // K a ≤60 Ophthalmologist report
	RefractionCylNearLeft               string   `field:"4726"` // K a ≤60 Ophthalmologist report
	RefractionAxisNearRight             string   `field:"4727"` // K a ≤60 Ophthalmologist report
	RefractionAxisNearLeft              string   `field:"4728"` // K a ≤60 Ophthalmologist report
	OtherDeviationsFromNorm             []string `field:"4730"` // K a ≤60 Ophthalmologist's report
	SymptomsOfAnAccident                []string `field:"4735"` // K a ≤60 Ophthalmologist's report
	InvoiceType                         string   `field:"4580"` // 40 = General medical treatment, 41 = Professional association medical treatment
	InvoiceNumber                       string   `field:"4601"`
	BillingAddress                      string   `field:"4602"` // Name# Street# Postal code# City
	InvoiceDate                         string   `field:"4604"`
	FeeAmount                           string   `field:"4605"` // 123.90
	LastPaymentDate                     string   `field:"4620"`
	PaymentAmount                       string   `field:"4621"` // 123.60
	LastReminderDate                    string   `field:"4625"`
	ReminderLevel                       string   `field:"4626"` // 1, 2, 3
	ReminderFee                         string   `field:"4627"` // 5.00
	AmountsInEuros                      string   `field:"4630"` // 1=yes, 0 or not present=no
	InvoiceExtras                       []string `field:"4635"` //
	// 5xxx string // not available

	Di5000s []BG_Di5000 `field:"5000"`
	Di6200s []PS_Di6200 `field:"6200"`
}

type BG_Di5000 struct {
	SelectedDate string      `field:"5000"` // yyyymmdd
	Di5001       []BG_Di5001 `field:"5001"` // GNR
}

type BG_Di5001 struct {
	Di5001 string   `field:"5001"` // payload.code timeline
	Di5002 []string `field:"5002"` // payload.additionInfors join ""
	Di5003 []string `field:"5003"` // payload.additionInfors join ""

	Di5005 string      `field:"5005"` // pad 0 in front of this number to 3 chars follow our app
	Di5006 string      `field:"5006"`
	Un5007 string      `field:"5007"` // don't exist in sample file
	Di5008 string      `field:"5008"`
	Di5009 []string    `field:"5009"`
	Di5010 string      `field:"5010"`
	Di5012 []PS_Di5012 `field:"5012"`
	Di5013 string      `field:"5013"`
	Di5015 []string    `field:"5015"`
	Di5017 []string    `field:"5017"`

	Un5061 string   `field:"5061"`
	Un5062 string   `field:"5062"`
	Un5063 string   `field:"5063"`
	Un5065 []string `field:"5065"`
	Un5090 []string `field:"5090"`
	Un5091 []string `field:"5091"`
	Un9901 []string `field:"9901"`
}
