package xdt_test

import (
	"bufio"
	"os"
	"strings"
	"testing"

	"git.tutum.dev/medi/tutum/ares/pkg/xdt"
	"git.tutum.dev/medi/tutum/ares/pkg/xdt/bdt"
	bdt_model "git.tutum.dev/medi/tutum/ares/pkg/xdt/bdt/model"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/stretchr/testify/require"
	"golang.org/x/text/encoding/charmap"
)

var CommonConfig = bdt_model.Config{
	MaxLengthOfContent: 270,
	DateTimeFormat:     util.YYYYMMDD,
	Separator:          "^",
	Version:            "2.9",
	Decoder:            charmap.ISO8859_15.NewDecoder(),
}

func extractXdtModel(rawData string) (bdt.XdtModelSlice, error) {
	fields := bdt.XdtModelSlice{}
	previousField := &xdt.XdtModel{}
	var err error
	for _, line := range strings.Split(rawData, "\n") {
		if line == "" {
			continue
		}
		var xdtModel *xdt.XdtModel
		xdtModel, err = bdt.ToXdtModel(line, CommonConfig, previousField)
		if err != nil {
			break
		}
		if xdtModel == nil {
			continue
		}

		if previousField != nil && previousField.FieldId != "" {
			previousField = &xdt.XdtModel{}
		}

		fields = append(fields, *xdtModel)
	}
	return fields, nil
}

func extractModelByFilePath(filePath string) (bdt.XdtModelSlice, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()
	scanner := bufio.NewScanner(file)
	fields := bdt.XdtModelSlice{}
	previousField := &xdt.XdtModel{}
	for i := 0; scanner.Scan(); i++ {
		line := scanner.Text()
		if line == "" {
			continue
		}

		xdtModel, err := bdt.ToXdtModel(line, CommonConfig, previousField)
		if err != nil {
			return nil, err
		}

		if xdtModel == nil {
			continue
		}

		if previousField != nil && previousField.FieldId != "" {
			previousField = &xdt.XdtModel{}
		}

		fields = append(fields, *xdtModel)
	}
	return fields, nil
}

var privateScheinRaw = `
01380000190
017810000000884
0185098613231900
0185099837404101
0189901ArztNr.:3
0189901Kuerzel:#
0123000571
0153003238915
0203101Omcehzusrcu
0143102Ujbuy
017310319610724
0193105Q666777996
0193119Q666777996
024310670286 Tsyvrbawr
0243107Adhgavhosvrzmvf
011310977
014311270286
0183113Tsyvrbawr
01031083
0103110W
02336353#Joachim Ries
014410132024
0304135AOK Baden-W�rttemberg
017415020240710
017415120240710
017415220240821
01899018#bezahlt
011423990
011458001
0194601240710-571
017460420240821
014460514.91
014462114.91
01046301
017500020240710
0125001250
01250621.8
0185098613231900
0185099837404101
0189901ArztNr.:3
0189901Kuerzel:#
01050011
01250622.3
0185098613231900
0185099837404101
0189901ArztNr.:3
0189901Kuerzel:#
017620020240710
0185098000000000
0185099000000000
0189901ArztNr.:0
0189901Kuerzel:0
0276000Vitamin Bestimmung
`

func TestFillDataPrivateSchein(t *testing.T) {
	fields, err := extractXdtModel(privateScheinRaw)
	privateSchein := bdt_model.PrivateSchein{}

	xdt.FillData(&privateSchein, fields, 0)
	require.NoError(t, err)
	require.Equal(t, "571", privateSchein.PatientNumber)
	require.Equal(t, "Q666777996", privateSchein.InsuranceNumber)
	require.Equal(t, "3#Joachim Ries", privateSchein.TreatmentDoctor)
	require.Equal(t, "20240821", privateSchein.BillingDate)
	require.Equal(t, "240710-571", privateSchein.InvoiceNumber)
	require.Equal(t, "20240821", privateSchein.InvoiceDate)
	require.Equal(t, "14.91", privateSchein.FeeAmount)
}

var headerRaw = `
01380000020
017810000001284
0189100613231900
017910320240919
**********
01091064
01091071
019990126_07_2024
0239901xBDT-Version 2
0349901xBDT-Datensatzversion 2.9
0229901Demoversion 3
0309901StammdatenSatzarten=N
0259901Diagnosenstamm=J
0229901Adressstamm=J
0289901Medikamentenstamm=J
0249901Textbausteine=J
0249901Ziffernketten=J
0259901Terminkalender=J
0369901Kalenderdaten_ab=1941-06-19
0299901max_Zeilenlaenge=250
0299901Zeilentrennzeichen=^
0369901Format_externer_Dokumente=R
0379901Ausgabe_externer_Dokumente=S
0319901Verweis_auf_Original=N
0369901Scheindiagnosen_in_SA6200=J
0339901ICD_bei_Dauerdiagnosen=J
0349901ICD_bei_Karteidiagnosen=J
0349901Datum_vor_jede_Diagnose=J
0399901Medikamente_PZN_wie_BDT02/94=N
0249901Regelpruefung=N
0319901Ziffernkennzeichnung=V
0329901Trennung_der_BDTDatei=0
0389901Trennung_externer_Dokumente=N
0319901FormularUebertragung=T
0379901Standard-FK_fuer_Formulare=J
0459901Kennzeichnung_Kartei_und_Diagnosen=2
0319901Karteikuerzelausgabe=3
0259901Datum_mit_Zeit=N
0349901maximale_Dateigroesse=0MB
0349901BG-Bericht-Uebertragung=0
0309901DatumsFormat=JJJJMMTT
0279901BMP-Uebertragung=B
0499901keine_Patienten_mit_letzter_Beh_vor=----
0369901Patienten_neu_nummerieren=N
0339901PatientenNummernOffset=0
`

func TestFillDataHeader(t *testing.T) {
	fields, err := extractXdtModel(headerRaw)
	header := bdt_model.BDTHeader{}

	xdt.FillData(&header, fields, 0)
	require.NoError(t, err)
	require.Equal(t, header.CharacterCode, "4")
	require.Equal(t, header.DateFormat, "1")
}

var patientRaw = `
01380006100
017810000000355
0185098000000000
0185099000000000
0189901ArztNr.:0
0189901Kuerzel:0
01330003370
0203101Csxakoqgskm
0163102Egbzdzx
017310319530616
0183105005032228
02331067027 Tsxobeuyw
0223107Atalxvtywvlys
01031095
01331127027
0183113Tsxobeuyw
01031081
0103110M
020362601729980752
02336351#Murat Cavdar
01341121000
0104113M
`

func TestFillDataPatient(t *testing.T) {
	fields, err := extractXdtModel(patientRaw)
	patient := bdt_model.PatientData{}

	xdt.FillData(&patient, fields, 0)
	require.NoError(t, err)
	require.Equal(t, "3370", patient.PatientNumber)
	require.Equal(t, "Csxakoqgskm", patient.LastName)
	require.Equal(t, "Egbzdzx", patient.FirstName)
	require.Equal(t, "19530616", patient.DateOfBirth)
	require.Equal(t, "005032228", patient.InsuranceNumber)
	require.Equal(t, "7027 Tsxobeuyw", patient.Wohnort)
	require.Equal(t, "Atalxvtywvlys", patient.Street)
	require.Equal(t, "5", patient.Number)
	require.Equal(t, "7027", patient.PostCode)
	require.Equal(t, "Tsxobeuyw", patient.City)
}

var scheinRaw = `
01380000101
017810000004653
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
01330003371
014300377231
01030041
01430065.1.0
0163101Opnkyym
0153102Amfjso
017310319840310
0193105K999333118
0193119K999333118
024310670054 Tsvwtfcie
0243107Csqdmrubcmumusu
01031096
014311270054
0183113Tsvwtfcie
01031081
0103110M
03836352#Dr. med. Werner Baumg�rtner
014410112017
017410220170110
014410461125
011410600
017410920170110
017411020170630
0184111108018121
0324134AOK Stuttgart-B�blingen
01341121000
01041131
011413100
011413200
01041211
011412200
017415020170110
017415120170331
017415220170331
011423900
017500020170110
014500103003
0185098613231900
0185099837404101
0189901ArztNr.:3
0189901Kuerzel:#
014500103000
0185098613231900
0185099837404101
0189901ArztNr.:3
0189901Kuerzel:#
014500103230
0185098613231900
0185099837404101
0189901ArztNr.:3
0189901Kuerzel:#
014500140120
0185098613231900
0185099837404101
0189901ArztNr.:3
0189901Kuerzel:#
014500140144
0185098613231900
0185099837404101
0189901ArztNr.:3
0189901Kuerzel:#
015500103220H
0185098613231900
0185099837404101
0189901ArztNr.:3
0189901Kuerzel:#
017500020170118
014500101430
0185098613231900
0185099837404101
0189901ArztNr.:3
0189901Kuerzel:#
014500101435
0185098613231900
0185099837404101
0189901ArztNr.:3
0189901Kuerzel:#
017500020170209
014500103230
0185098613231900
0185099119308201
0189901ArztNr.:1
0189901Kuerzel:�
017500020170213
014500103230
0185098613231900
0185099837404101
0189901ArztNr.:3
0189901Kuerzel:#
017500020170320
014500101430
0185098613231900
0185099837404101
0189901ArztNr.:3
0189901Kuerzel:#
014500101435
0185098613231900
0185099837404101
0189901ArztNr.:3
0189901Kuerzel:#
017500020170331
014500101430
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
017620020170110
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0276000Akute Appendizitis
0146001K35.8
0106003V
017620020170110
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0206000Zystenniere
0146001Q61.9
0106003G
0106004B
017620020170110
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0386000Bauchschmerzen ohne Krankheit
0146001R10.4
0106003G
017620020170116
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0326000Obstruktive Schlafapnoe
0156001G47.31
0106003G
017620020170116
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0306000arterielle Hypertonie
0156001I10.90
0106003G
017620020170116
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0306000Nierenarterienstenose
0146001I70.1
0106003G
017620020170116
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0426000Sch�digung des Meniscus lateralis
0156001M23.36
0106003G
0106004R
017620020170120
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0306000arterielle Hypertonie
0156001I10.90
0106003G
017620020170120
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0306000Nierenarterienstenose
0146001I70.1
0106003G
017620020170120
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0326000Obstruktive Schlafapnoe
0156001G47.31
0106003G
017620020170209
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0376000V.a.kl.Fibrom am Nasenr�cken
0146001D21.9
0106003V
017620020170209
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0346000Akute Sinusitis frontalis
0146001J01.1
0106003G
017620020170209
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0266000Akute Pharyngitis
0146001J02.9
0106003G
017620020170209
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0256000Grippaler Infekt
0146001J06.9
0106003G
017620020170213
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0276000Akute Pansinusitis
0146001J01.4
0106003G
017620020170320
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0216000Nasenabszess
0146001J34.0
0106003G
017620020170120
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0303672arterielle Hypertonie
0153673I10.90
0103674G
01036782
017620020170120
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0303672Nierenarterienstenose
0143673I70.1
0103674G
01036782
017620020170120
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0323672Obstruktive Schlafapnoe
0153673G47.31
0103674G
01036782
`

func TestFillDataKVSchein(t *testing.T) {
	fields, err := extractXdtModel(scheinRaw)
	schein := bdt_model.Schein{}

	xdt.FillData(&schein, fields, 0)
	require.NoError(t, err)
	require.Equal(t, "3371", schein.PatientNumber)
	require.Equal(t, "Opnkyym", schein.LastName)
	require.Equal(t, "Amfjso", schein.FirstName)
	require.Equal(t, "19840310", schein.DateOfBirth)
	require.Equal(t, "K999333118", schein.InsuranceNumber)
	require.Equal(t, "70054 Tsvwtfcie", schein.Wohnort)
	require.Equal(t, "Csqdmrubcmumusu", schein.Street)
	require.Equal(t, "6", schein.Number)
	require.Equal(t, "70054", schein.PostCode)
	require.Equal(t, "Tsvwtfcie", schein.City)
	require.Equal(t, "20170110", schein.Ad4125From)
	require.Equal(t, "20170331", schein.Ad4125To)
	require.Equal(t, "108018121", schein.IkNumber)
	require.Len(t, schein.Di5000s, 6)
	require.Len(t, schein.Di6200s, 19)
	require.Equal(t, "20170110", schein.Di5000s[0].SelectedDate)
	require.Len(t, schein.Di5000s[0].Di5001, 6)
	require.Equal(t, "03003", schein.Di5000s[0].Di5001[0].Di5001)
	require.Equal(t, "20170110", schein.Di6200s[0].SelectedDate)
	require.Equal(t, "Akute Appendizitis", schein.Di6200s[0].Di6000)
	require.Equal(t, "K35.8", schein.Di6200s[0].Di6001.Di6001)
}

var treatmentRaw = `
01380006200
017810000145492
01330003371
**********
**********.00
017620019900302
0185098000000000
0185099000000000
0189901ArztNr.:0
0189901Kuerzel:0
0106330N
0166330Notizen
0306331Patientennotiz Garrio
017620020070703
0185098000000000
0185099000000000
0189901ArztNr.:0
0189901Kuerzel:0
0356205Nekrot.Angina tonsillaris+
017620020101104
0185098000000000
0185099000000000
0189901ArztNr.:0
0189901Kuerzel:0
0106330N
0166330Notizen
0306331Patientennotiz Garrio
017620020170110
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0276205Akute Appendizitis
0146001K35.8
0106003V
017620020170110
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0206205Zystenniere
0146001Q61.9
0106003G
0106004B
017620020170110
0185098613231900
0185099766892801
0189901ArztNr.:2
0189901Kuerzel:�
0386205Bauchschmerzen ohne Krankheit
0146001R10.4
0106003G
0185098000000000
0185099000000000
0189901ArztNr.:0
0189901Kuerzel:0
0256210 Garrio Medikation
017080011285201
014080117.93
01008020
01008030
0185098000000000
0185099000000000
0189901ArztNr.:0
0189901Kuerzel:0
`

func TestFillDataTreatment(t *testing.T) {
	fields, err := extractXdtModel(treatmentRaw)
	treatment := bdt_model.Treatment{}

	xdt.FillData(&treatment, fields, 0)
	require.NoError(t, err)
	require.Equal(t, "3371", treatment.PatientNumber)
	require.Equal(t, "180", treatment.Height)
	require.Equal(t, "120.00", treatment.Weight)
	require.Len(t, treatment.Un6200, 6)
	require.Equal(t, "19900302", treatment.Un6200[0].Un6200)
	require.Equal(t, "Notizen", treatment.Un6200[0].Un6330)
	require.Equal(t, "Patientennotiz Garrio", strings.Join(treatment.Un6200[0].Un6331, ""))
}

func TestExtractTreatmentData(t *testing.T) {
	fields, err := extractModelByFilePath("fixtures/patient79_6200.bdt")
	require.NoError(t, err)
	treatment := bdt_model.Treatment{}
	xdt.FillData(&treatment, fields, 0)
	require.NoError(t, err)
	require.Len(t, treatment.Un6200, 5)
	require.Len(t, treatment.Un6200[4].Medicines, 1)
	require.Equal(t, "Eintrag ersetzt wegen Demoversion", treatment.Un6200[4].Medicines[0].Name)
	require.Equal(t, "Resochin  Tabl. ,20 Filmtbl.                        13.91", treatment.Un6200[4].MedicinesKRez_6215[0].Name)
}

func TestExtractBGSchein(t *testing.T) {
	fields, err := extractModelByFilePath("fixtures/patient366_0191.bdt")
	require.NoError(t, err)
	bgSchein := bdt_model.BGSchein{}
	xdt.FillData(&bgSchein, fields, 0)
	require.NoError(t, err)
	require.Equal(t, "366", bgSchein.PatientNumber)
	require.Equal(t, "20130325", bgSchein.InvoiceDate)
	require.Equal(t, "17.78", bgSchein.FeeAmount)
	require.Equal(t, "20130325", bgSchein.BillingDate)
}

func TestExtractActionChain(t *testing.T) {
	fields, err := extractModelByFilePath("fixtures/actionChain.bdt")
	require.NoError(t, err)
	actionChain := bdt_model.ActionChain{}
	xdt.FillData(&actionChain, fields, 0)
	require.NoError(t, err)
	require.Len(t, actionChain.Abbreviation, 4)
	require.Equal(t, "ABD", actionChain.Abbreviation[0].Name)
	require.Equal(t, "Akutes Abdomen", actionChain.Abbreviation[0].Diagnoses[0])
	require.Equal(t, "R10.0", actionChain.Abbreviation[0].ICD.Code)
	require.Equal(t, "ABS", actionChain.Abbreviation[1].Name)
	require.Equal(t, "Hautabsze, Furunkel od. Karbunkel, n.n.bez.", actionChain.Abbreviation[1].Diagnoses[0])
	require.Equal(t, "L02.9", actionChain.Abbreviation[1].ICD.Code)
}

func TestExtractTextModule(t *testing.T) {
	fields, err := extractModelByFilePath("fixtures/textModule.bdt")
	require.NoError(t, err)
	textModule := bdt_model.TextModule{}
	xdt.FillData(&textModule, fields, 0)
	require.NoError(t, err)
	require.Len(t, textModule.ShortCuts, 4)
	require.Equal(t, "AA", textModule.ShortCuts[0].ShortCutText)
	require.Equal(t, "Arzt fur Allgemeinmedizin", textModule.ShortCuts[0].Contents[0])
	require.Equal(t, "ABD_WEICH,", textModule.ShortCuts[1].ShortCutText)
	require.Equal(t, "Abd.weich, NL frei, Bruchpforten geschlossen, DG lebhaft", textModule.ShortCuts[1].Contents[0])
	require.Equal(t, "ANLEITUNGZ", textModule.ShortCuts[2].ShortCutText)
	require.Equal(t, "Anleitung zu selbstandigen Ubungen in Eigenregie", textModule.ShortCuts[2].Contents[0])
}

func TestExtractSchein(t *testing.T) {
	fields, err := extractModelByFilePath("fixtures/schein.bdt")
	require.NoError(t, err)
	schein := bdt_model.Schein{}
	xdt.FillData(&schein, fields, 0)
	require.NoError(t, err)
	require.Equal(t, "4996", schein.PatientNumber)
	require.Equal(t, "PRO", schein.LastName)
	require.Equal(t, "garrio", schein.FirstName)
	require.Equal(t, "19950127", schein.DateOfBirth)
	require.Equal(t, "B000000000", schein.InsuranceNumber)
	require.Equal(t, "73337 Bad �berkingen", schein.Wohnort)
	require.Equal(t, "Ahzznpwx", schein.Street)
	require.Equal(t, "3", schein.Number)
	require.Equal(t, "73337", schein.PostCode)
	require.Equal(t, "Bad �berkingen", schein.City)
	require.Equal(t, "20010101", schein.Ad4125From)
	require.Equal(t, "20010331", schein.Ad4125To)
	require.Equal(t, "108018520", schein.IkNumber)
	require.Len(t, schein.Di5000s, 1)
	require.Len(t, schein.Di6200s, 2)
	require.Equal(t, "20010128", schein.Di5000s[0].SelectedDate)
	require.Len(t, schein.Di5000s[0].Di5001, 2)
	require.Equal(t, "1", schein.Di5000s[0].Di5001[0].Di5001)
	require.Equal(t, "20010128", schein.Di6200s[0].SelectedDate)
	require.Equal(t, "Akute Bronchitis", schein.Di6200s[0].Di6000)
	require.Equal(t, "J20.9", schein.Di6200s[0].Di6001.Di6001)
}

func TestCusomizedFieldsPatient(t *testing.T) {
	fields, err := extractModelByFilePath("fixtures/patient_customized_fields.bdt")
	require.NoError(t, err)
	patient := bdt_model.PatientData{}
	xdt.FillData(&patient, fields, 0)
	require.NoError(t, err)
	require.Equal(t, "DMP Diabetes", patient.Cave.Value)
	require.Equal(t, "verstorben", patient.Status.Value)
	require.Len(t, patient.OtherInfos, 2)
}
