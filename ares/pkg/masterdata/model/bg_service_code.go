package masterdata_model

import (
	"strconv"

	catalog_utils_common "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_utils_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_uv_goa_common"
	"git.tutum.dev/medi/tutum/pkg/util"
)

type BGServiceCode struct {
	BaseEntity                     `json:",inline"`
	Id                             string                          `json:"id"`
	AdditionalCode                 *string                         `json:"additionalCode"`
	Code                           string                          `json:"code"`
	Description                    string                          `json:"description"`
	GeneralCost                    string                          `json:"generalCost"`
	GeneralTreatmentEvaluation     string                          `json:"generalTreatmentEvaluation"`
	HospitalTreatmentEvaluation    *string                         `json:"hospitalTreatmentEvaluation"`
	LongDescription                string                          `json:"longDescription"`
	MaterialCost                   string                          `json:"materialCost"`
	ResidentialTreatmentEvaluation *string                         `json:"residentialTreatmentEvaluation"`
	SpecificTreatmentEvaluation    string                          `json:"specificTreatmentEvaluation"`
	SourceType                     catalog_utils_common.SourceType `json:"sourceType"`
	Validity                       *catalog_utils_common.Validity  `json:"validity"`
	CareProviderId                 string                          `json:"careProviderId"`
	BsnrId                         string                          `json:"bsnrId"`
	Billable                       bool                            `json:"billable"`
}

func (b *BGServiceCode) ToUvGoaCatalog() (*catalog_uv_goa_common.UvGoaCatalog, error) {
	generalTreatmentEvaluation, err := stringToFloat64(b.GeneralTreatmentEvaluation)
	if err != nil {
		return nil, err
	}
	generalCost, err := stringToFloat64(b.GeneralCost)
	if err != nil {
		return nil, err
	}

	materialCost, err := stringToFloat64(b.MaterialCost)
	if err != nil {
		return nil, err
	}

	specificTreatmentEvaluation, err := stringToFloat64(b.SpecificTreatmentEvaluation)
	if err != nil {
		return nil, err
	}

	description := b.Description
	if b.SourceType == catalog_utils_common.XmlFile {
		description = ModifyDescription(b.Description)
	}

	var validity catalog_utils_common.Validity
	if b.Validity != nil {
		validity = *b.Validity
	}

	return &catalog_uv_goa_common.UvGoaCatalog{
		UvGoaId:                        b.Id,
		Code:                           b.Code,
		Description:                    description,
		AdditionalCode:                 b.AdditionalCode,
		Validity:                       validity,
		GeneralTreatmentEvaluation:     generalTreatmentEvaluation,
		GeneralCost:                    generalCost,
		HospitalTreatmentEvaluation:    b.HospitalTreatmentEvaluation,
		LongDescription:                b.LongDescription,
		Source:                         b.SourceType,
		MaterialCost:                   materialCost,
		ResidentialTreatmentEvaluation: b.ResidentialTreatmentEvaluation,
		SpecificTreatmentEvaluation:    specificTreatmentEvaluation,
		IsNotBillable:                  util.NewBool(!b.Billable),
	}, nil
}

func (b *BGServiceCode) ToUvGoaItem(isGeneral bool) (*catalog_uv_goa_common.UvGoaItem, error) {
	evaluation := b.GeneralTreatmentEvaluation
	if !isGeneral {
		evaluation = b.SpecificTreatmentEvaluation
	}

	evaluationFloat, err := stringToFloat64(evaluation)
	if err != nil {
		return nil, err
	}

	return &catalog_uv_goa_common.UvGoaItem{
		UvGoaId:       b.Id,
		Code:          b.Code,
		Description:   b.Description,
		Evaluation:    util.GetPointerValue(evaluationFloat),
		IsSelfCreated: catalog_utils_common.SourceType(b.SourceType) == catalog_utils_common.SelfCreated,
	}, nil
}

func stringToFloat64(str string) (*float64, error) {
	if str == "" {
		return nil, nil
	}

	val, err := strconv.ParseFloat(str, 64)
	if err != nil {
		return nil, err
	}

	return &val, nil
}
