package masterdata_model

import (
	catalog_goa_common "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_goa_common"
	catalog_utils_common "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_utils_common"
)

type Goa struct {
	BaseEntity     `json:",inline"`
	Id             string                          `json:"id"`
	GoaNumber      string                          `json:"goaNumber" validate:"required"`
	Description    string                          `json:"description" validate:"required"`
	ExcludedCodes  []string                        `json:"excludedCodes"`
	Validity       catalog_utils_common.Validity   `json:"validity"`
	Factor         catalog_goa_common.Factor       `json:"factor"`
	Evaluation     float64                         `json:"evaluation"`
	Unit           *catalog_goa_common.Unit        `json:"unit"`
	SourceType     catalog_utils_common.SourceType `json:"sourceType"`
	Chapter        Chapter                         `json:"chapter"`
	Remark         *string                         `json:"remark"`
	CareProviderId string                          `json:"careProviderId"`
	BsnrId         string                          `json:"bsnrId"`
	Price          float64                         `json:"price"`
}

type Chapter = catalog_goa_common.Chapter

type GoaData struct {
	GoaNumber string `csv:"goa_number"`
	Factor    string `csv:"factor"`
	Chapter   string `csv:"chapter"`
}
