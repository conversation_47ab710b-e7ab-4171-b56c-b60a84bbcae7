package hpm_rest_hzv_client

import (
	"testing"
	"time"
)

// TestParseDateTime tests the ParseDateTime function with various inputs
// Generated by Copilot
func TestParseDateTime(t *testing.T) {
	tests := []struct {
		name        string
		timeValue   string
		expected    time.Time
		expectError bool
	}{
		{
			name:        "Valid DateTime",
			timeValue:   "20250101120000",
			expected:    time.Date(2025, 1, 1, 12, 0, 0, 0, time.UTC),
			expectError: false,
		},
		{
			name:        "Valid DateTime at End of Day",
			timeValue:   "20250101235959",
			expected:    time.Date(2025, 1, 1, 23, 59, 0, 0, time.UTC),
			expectError: false,
		},
		{
			name:        "Valid DateTime at End of Month",
			timeValue:   "20250331235959",
			expected:    time.Date(2025, 3, 31, 23, 59, 0, 0, time.UTC),
			expectError: false,
		},
		{
			name:        "Valid DateTime with Leap Year",
			timeValue:   "20240229235959",
			expected:    time.Date(2024, 2, 29, 23, 59, 0, 0, time.UTC),
			expectError: false,
		},
		{
			name:        "Invalid Format - Too Short",
			timeValue:   "2025010112000",
			expectError: true,
		},
		{
			name:        "Invalid Format - Too Long",
			timeValue:   "202501011200000",
			expectError: true,
		},
		{
			name:        "Invalid Format - Non-numeric",
			timeValue:   "2025010X120000",
			expectError: true,
		},
		{
			name:        "Invalid Month - Zero",
			timeValue:   "20250001120000",
			expected:    time.Date(2024, 12, 1, 12, 0, 0, 0, time.UTC), // Go normalizes month 0 to December of previous year
			expectError: false,                                         // ParseDateTime doesn't validate date values
		},
		{
			name:        "Invalid Month - Thirteen",
			timeValue:   "20251301120000",
			expected:    time.Date(2026, 1, 1, 12, 0, 0, 0, time.UTC), // Go normalizes month 13 to January of next year
			expectError: false,                                        // ParseDateTime doesn't validate date values
		},
		{
			name:        "Invalid Day - Zero",
			timeValue:   "20250100120000",
			expected:    time.Date(2024, 12, 31, 12, 0, 0, 0, time.UTC), // Go normalizes day 0 to last day of previous month
			expectError: false,                                          // ParseDateTime doesn't validate date values
		},
		{
			name:        "Invalid Day - Thirty-Two",
			timeValue:   "20250132120000",
			expected:    time.Date(2025, 2, 1, 12, 0, 0, 0, time.UTC), // Go normalizes day 32 of January to February 1
			expectError: false,                                        // ParseDateTime doesn't validate date values
		},
		{
			name:        "Empty String",
			timeValue:   "",
			expectError: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			result, err := ParseDateTime(tc.timeValue)

			// Check error expectation
			if tc.expectError && err == nil {
				t.Errorf("Expected error but got none")
				return
			}

			if !tc.expectError && err != nil {
				t.Errorf("Didn't expect error but got: %v", err)
				return
			}

			// If we don't expect an error, also check the result value
			if !tc.expectError {
				if result == nil {
					t.Errorf("Expected non-nil result but got nil")
					return
				}

				if !result.Equal(tc.expected) {
					t.Errorf("Expected %v but got %v", tc.expected, *result)
				}
			}
		})
	}
}

// TestParseDateTime_EdgeCases tests edge cases for the ParseDateTime function
// Generated by Copilot
func TestParseDateTime_EdgeCases(t *testing.T) {
	// Test the specific edge case with "20250331235959"
	t.Run("Specific Edge Case - March 31, 2025 at 23:59:59", func(t *testing.T) {
		timeValue := "20250331235959"
		expected := time.Date(2025, 3, 31, 23, 59, 0, 0, time.UTC)

		result, err := ParseDateTime(timeValue)
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
			return
		}

		if result == nil {
			t.Errorf("Expected non-nil result but got nil")
			return
		}

		if !result.Equal(expected) {
			t.Errorf("Expected %v but got %v", expected, *result)
		}

		// Additionally check the individual components
		if result.Year() != 2025 {
			t.Errorf("Expected year 2025 but got %d", result.Year())
		}

		if result.Month() != time.March {
			t.Errorf("Expected month March but got %s", result.Month())
		}

		if result.Day() != 31 {
			t.Errorf("Expected day 31 but got %d", result.Day())
		}

		if result.Hour() != 23 {
			t.Errorf("Expected hour 23 but got %d", result.Hour())
		}

		if result.Minute() != 59 {
			t.Errorf("Expected minute 59 but got %d", result.Minute())
		}
	})

	// Test date validation issues
	t.Run("February 30 - Invalid Date", func(t *testing.T) {
		timeValue := "20250230120000"

		// ParseDateTime doesn't validate actual date correctness
		// It just parses the components without checking if it's a valid date
		result, err := ParseDateTime(timeValue)

		// The function won't return an error for invalid dates
		if err != nil {
			t.Errorf("Unexpected error: %v", err)
			return
		}

		// Go's time.Date normalizes invalid dates, so February 30, 2025
		// would be normalized to March 2, 2025 (or March 1 in non-leap years)
		// This test checks if this normalization happens as expected
		expectedMonth := time.March
		if result.Month() != expectedMonth {
			t.Errorf("Expected normalized month %s but got %s", expectedMonth, result.Month())
		}
	})
}
