// Package hpm_rest_hzv_client provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package hpm_rest_hzv_client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/oapi-codegen/runtime"
)

// Defines values for DiagnoseCodeSystemName.
const (
	Icd10gm2020 DiagnoseCodeSystemName = "Icd10gm2020"
	Icd10gm2021 DiagnoseCodeSystemName = "Icd10gm2021"
	Icd10gm2022 DiagnoseCodeSystemName = "Icd10gm2022"
	Icd10gm2023 DiagnoseCodeSystemName = "Icd10gm2023"
	Icd10gm2024 DiagnoseCodeSystemName = "Icd10gm2024"
	Icd10gm2025 DiagnoseCodeSystemName = "Icd10gm2025"
	None        DiagnoseCodeSystemName = "None"
)

// Defines values for DiagnoseSeitenlokalisation.
const (
	DiagnoseSeitenlokalisationB DiagnoseSeitenlokalisation = "B"
	DiagnoseSeitenlokalisationL DiagnoseSeitenlokalisation = "L"
	DiagnoseSeitenlokalisationR DiagnoseSeitenlokalisation = "R"
	DiagnoseSeitenlokalisationU DiagnoseSeitenlokalisation = "U"
)

// Defines values for DiagnoseSicherheit.
const (
	DiagnoseSicherheitA           DiagnoseSicherheit = "A"
	DiagnoseSicherheitG           DiagnoseSicherheit = "G"
	DiagnoseSicherheitKeineAngabe DiagnoseSicherheit = "KeineAngabe"
	DiagnoseSicherheitV           DiagnoseSicherheit = "V"
	DiagnoseSicherheitZ           DiagnoseSicherheit = "Z"
)

// Defines values for Meldungsart.
const (
	MeldungsartFehler      Meldungsart = "Fehler"
	MeldungsartInformation Meldungsart = "Information"
	MeldungsartUnbekannt   Meldungsart = "Unbekannt"
	MeldungsartWarnung     Meldungsart = "Warnung"
)

// Defines values for Meldungskategorie.
const (
	MeldungskategorieKeineAngabe   Meldungskategorie = "KeineAngabe"
	MeldungskategorieLaufzeit      Meldungskategorie = "Laufzeit"
	MeldungskategorieRechenzentrum Meldungskategorie = "Rechenzentrum"
	MeldungskategorieValidierung   Meldungskategorie = "Validierung"
)

// Defines values for PatientGeschlecht.
const (
	PatientGeschlechtD PatientGeschlecht = "D"
	PatientGeschlechtM PatientGeschlecht = "M"
	PatientGeschlechtU PatientGeschlecht = "U"
	PatientGeschlechtW PatientGeschlecht = "W"
	PatientGeschlechtX PatientGeschlecht = "X"
)

// Defines values for ReferenzTyp.
const (
	ReferenzTypArzneimittel                ReferenzTyp = "Arzneimittel"
	ReferenzTypDiagnose                    ReferenzTyp = "Diagnose"
	ReferenzTypErkrankungsstufe            ReferenzTyp = "Erkrankungsstufe"
	ReferenzTypHinderungsfaktor            ReferenzTyp = "Hinderungsfaktor"
	ReferenzTypIstFremdeingeschrieben      ReferenzTyp = "IstFremdeingeschrieben"
	ReferenzTypIstNichtFremdeingeschrieben ReferenzTyp = "IstNichtFremdeingeschrieben"
	ReferenzTypIstwert                     ReferenzTyp = "Istwert"
	ReferenzTypKeineAngabe                 ReferenzTyp = "KeineAngabe"
	ReferenzTypLeistung                    ReferenzTyp = "Leistung"
	ReferenzTypOperation                   ReferenzTyp = "Operation"
	ReferenzTypPatient                     ReferenzTyp = "Patient"
	ReferenzTypPraxisgebuehr               ReferenzTyp = "Praxisgebuehr"
	ReferenzTypRezept                      ReferenzTyp = "Rezept"
	ReferenzTypUeberweisung                ReferenzTyp = "Ueberweisung"
	ReferenzTypVertragsidentifikator       ReferenzTyp = "Vertragsidentifikator"
	ReferenzTypZielwert                    ReferenzTyp = "Zielwert"
)

// Defines values for ResultatStatus.
const (
	ResultatStatusFehlgeschlagen       ResultatStatus = "Fehlgeschlagen"
	ResultatStatusOK                   ResultatStatus = "OK"
	ResultatStatusTeilweiseVerarbeitet ResultatStatus = "TeilweiseVerarbeitet"
	ResultatStatusUnbekannt            ResultatStatus = "Unbekannt"
)

// Defines values for Therapieverfahren.
const (
	A Therapieverfahren = "A"
	N Therapieverfahren = "N"
	P Therapieverfahren = "P"
	T Therapieverfahren = "T"
	V Therapieverfahren = "V"
)

// Defines values for UebermittlungsStatus.
const (
	UebermittlungsStatusFachlicheVerletzung         UebermittlungsStatus = "FachlicheVerletzung"
	UebermittlungsStatusInternerFehler              UebermittlungsStatus = "InternerFehler"
	UebermittlungsStatusKeineAngabe                 UebermittlungsStatus = "KeineAngabe"
	UebermittlungsStatusKeineBerechtigungWebService UebermittlungsStatus = "KeineBerechtigungWebService"
	UebermittlungsStatusOK                          UebermittlungsStatus = "OK"
	UebermittlungsStatusUngueltigeStammdaten        UebermittlungsStatus = "UngueltigeStammdaten"
	UebermittlungsStatusWartung                     UebermittlungsStatus = "Wartung"
)

// Defines values for Uebertragungsart.
const (
	Datentraeger Uebertragungsart = "Datentraeger"
	Online       Uebertragungsart = "Online"
)

// Defines values for UeberweisungAuftragsart.
const (
	UeberweisungAuftragsartAuftragsleistung      UeberweisungAuftragsart = "Auftragsleistung"
	UeberweisungAuftragsartKeineAngabe           UeberweisungAuftragsart = "KeineAngabe"
	UeberweisungAuftragsartKonsiliaruntersuchung UeberweisungAuftragsart = "Konsiliaruntersuchung"
	UeberweisungAuftragsartMitWeiterbehandlung   UeberweisungAuftragsart = "MitWeiterbehandlung"
)

// Defines values for Ueberweisungsart.
const (
	UeberweisungsartBelegaerztlich Ueberweisungsart = "Belegaerztlich"
	UeberweisungsartKeineAngabe    Ueberweisungsart = "KeineAngabe"
	UeberweisungsartKurativ        Ueberweisungsart = "Kurativ"
	UeberweisungsartParagraph116b  Ueberweisungsart = "Paragraph116b"
	UeberweisungsartPraeventiv     Ueberweisungsart = "Praeventiv"
)

// AbrechnungsAnzahlenTyp Die Anzahl der Abrechnungen.
type AbrechnungsAnzahlenTyp struct {
	// DiagnosenAnzahl Die Gesamtzahl der übermittelten Diagnosen.
	DiagnosenAnzahl *DiagnosenAnzahlTyp `json:"diagnosenAnzahl,omitempty"`

	// LeistungenAnzahl Die Gesamtzahl der übermittelten Leistungen.
	LeistungenAnzahl *LeistungenAnzahlTyp `json:"leistungenAnzahl,omitempty"`
}

// AbrechnungsContainer Der Container mit den Daten für die Abrechnung.
type AbrechnungsContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation        `json:"arztInformationsSystem"`
	Dokumentationen        []AbrechnungsDokumentation `json:"dokumentationen"`
	NurPrueflauf           bool                       `json:"nurPrueflauf"`
	Testuebermittlung      bool                       `json:"testuebermittlung"`
	Uebertragungsart       Uebertragungsart           `json:"uebertragungsart"`

	// Vertragskontext Angaben zum Vertrag.
	Vertragskontext              Vertragskontext `json:"vertragskontext"`
	Vertragspartneridentifikator string          `json:"vertragspartneridentifikator"`
}

// AbrechnungsDokumentation Die AbrechnungsDokumentation enthält unter anderem, für genau einen Vertrag, einen abrechnenden Vertragsarzt, ein Quartal und ein Jahr, die bezogenen Leistungen und die Diagnosen eines zugehörigen Patienten.
type AbrechnungsDokumentation struct {
	Diagnosen   *[]Diagnose  `json:"diagnosen"`
	Leistungen  *[]Leistung  `json:"leistungen"`
	Operationen *[]Operation `json:"operationen"`

	// Patient Alle Informationen zum Patienten.
	Patient                  Patient          `json:"patient"`
	Praxisgebuehren          *[]Praxisgebuehr `json:"praxisgebuehren"`
	Ueberweisungen           *[]Ueberweisung  `json:"ueberweisungen"`
	UnfallkennzeichenGesetzt bool             `json:"unfallkennzeichenGesetzt"`
}

// AbrechnungsResultat Das Ergebnis eines Abrechnungsversands.
type AbrechnungsResultat struct {
	// AbrechnungsAnzahlen Die Anzahl der Abrechnungen.
	AbrechnungsAnzahlen *AbrechnungsAnzahlenTyp `json:"abrechnungsAnzahlen,omitempty"`

	// Datentraeger Der nach einer erfolgreichen Offline-Abrechnung erstellte Datenträger, dessen ID und dessen Begleitschreiben.
	Datentraeger *DatentraegerTyp `json:"datentraeger,omitempty"`
	Meldungen    *[]Meldung       `json:"meldungen"`
	Status       *ResultatStatus  `json:"status,omitempty"`
	TransferId   *string          `json:"transferId"`

	// UebermittlungsBeleg Der Beleg der erfolgreichen Übermittlung.
	UebermittlungsBeleg     *UebermittlungsBelegTyp `json:"uebermittlungsBeleg,omitempty"`
	UebermittlungsStatus    *UebermittlungsStatus   `json:"uebermittlungsStatus,omitempty"`
	Uebermittlungsprotokoll *[]byte                 `json:"uebermittlungsprotokoll"`
}

// Anforderungszeitpunkt Der Anforderungszeitpunkt der Leistung.
type Anforderungszeitpunkt struct {
	Datum   time.Time `json:"datum"`
	Uhrzeit string    `json:"uhrzeit"`
}

// ArztIdentifikation Die Identifikation eines Arztes anhand von LANR und BSNR.
type ArztIdentifikation struct {
	Bsnr *string `json:"bsnr"`
	Lanr *string `json:"lanr"`
}

// BetreuteamArzt Ein Arzt eines Betreuteams.
type BetreuteamArzt struct {
	Lanr string `json:"lanr"`
}

// BetreuteamBetriebsstaette Eine Betriebsstaette eines Betreuteams.
type BetreuteamBetriebsstaette struct {
	Bsnr string `json:"bsnr"`
}

// DatentraegerTyp Der nach einer erfolgreichen Offline-Abrechnung erstellte Datenträger, dessen ID und dessen Begleitschreiben.
type DatentraegerTyp struct {
	Begleitschreiben *[]byte `json:"begleitschreiben"`
	DatentraegerID   *string `json:"datentraegerID"`
	IsoImage         *[]byte `json:"isoImage"`
}

// Diagnose Alle Informationen zu der erstellten Diagnose.
type Diagnose struct {
	// BetreuteamArzt Ein Arzt eines Betreuteams.
	BetreuteamArzt *BetreuteamArzt `json:"betreuteamArzt,omitempty"`

	// BetreuteamBetriebsstaette Eine Betriebsstaette eines Betreuteams.
	BetreuteamBetriebsstaette  *BetreuteamBetriebsstaette `json:"betreuteamBetriebsstaette,omitempty"`
	CodeSystemName             DiagnoseCodeSystemName     `json:"codeSystemName"`
	DiagnoseCode               string                     `json:"diagnoseCode"`
	DiagnoseId                 string                     `json:"diagnoseId"`
	Diagnoseausnahmetatbestand *string                    `json:"diagnoseausnahmetatbestand"`
	Diagnoseerlaeuterung       *string                    `json:"diagnoseerlaeuterung"`
	DokumentationsDatum        time.Time                  `json:"dokumentationsDatum"`
	IstDauerDiagnose           *bool                      `json:"istDauerDiagnose,omitempty"`
	Seitenlokalisation         DiagnoseSeitenlokalisation `json:"seitenlokalisation"`
	Sicherheit                 DiagnoseSicherheit         `json:"sicherheit"`

	// Stellvertreter Der Stellvertreter, der in Vertretung für den abrechnenden Vertragsarzt gehandelt hat.
	Stellvertreter *Stellvertreter `json:"stellvertreter,omitempty"`
}

// DiagnoseCodeSystemName defines model for DiagnoseCodeSystemName.
type DiagnoseCodeSystemName string

// DiagnoseElement Diagnoseinformationen für eine Teilnahmeerklärung im Rahmen eines Facharztvertrages.
type DiagnoseElement struct {
	CodeSystemName DiagnoseCodeSystemName `json:"codeSystemName"`
	DiagnoseCode   string                 `json:"diagnoseCode"`
	Sicherheit     DiagnoseSicherheit     `json:"sicherheit"`
}

// DiagnoseSeitenlokalisation defines model for DiagnoseSeitenlokalisation.
type DiagnoseSeitenlokalisation string

// DiagnoseSicherheit defines model for DiagnoseSicherheit.
type DiagnoseSicherheit string

// DiagnosenAnzahlTyp Die Gesamtzahl der übermittelten Diagnosen.
type DiagnosenAnzahlTyp struct {
	Anzahl                *int32                           `json:"anzahl,omitempty"`
	DiagnosenAnzahlDetail *[]DokumentationsAnzahlDetailTyp `json:"diagnosenAnzahlDetail"`
}

// DokumentationsAnzahlDetailTyp Die Details der übermittelten Diagnosen.
type DokumentationsAnzahlDetailTyp struct {
	Anzahl      *int32  `json:"anzahl,omitempty"`
	Auspraegung *string `json:"auspraegung"`
}

// EDMPContainer Der Container mit Angaben zum Versand von EDMP Daten.
type EDMPContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem       SoftwareInformation `json:"arztInformationsSystem"`
	DokumentationsDatum          *time.Time          `json:"dokumentationsDatum"`
	FileContent                  []byte              `json:"fileContent"`
	Filename                     string              `json:"filename"`
	Ik                           string              `json:"ik"`
	Testuebermittlung            bool                `json:"testuebermittlung"`
	VertragsIdentifikator        string              `json:"vertragsIdentifikator"`
	Vertragspartneridentifikator string              `json:"vertragspartneridentifikator"`
}

// KrankheitsbildAuswertungContainer defines model for KrankheitsbildAuswertungContainer.
type KrankheitsbildAuswertungContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem       SoftwareInformation               `json:"arztInformationsSystem"`
	AuswertungsJahr              int32                             `json:"auswertungsJahr"`
	AuswertungsKontext           string                            `json:"auswertungsKontext"`
	AuswertungsQuartal           int32                             `json:"auswertungsQuartal"`
	Patienten                    []KrankheitsbildAuswertungPatient `json:"patienten"`
	VertragsIdentifikator        string                            `json:"vertragsIdentifikator"`
	Vertragspartneridentifikator string                            `json:"vertragspartneridentifikator"`
}

// KrankheitsbildAuswertungPatient defines model for KrankheitsbildAuswertungPatient.
type KrankheitsbildAuswertungPatient struct {
	Diagnosen   *[]string `json:"diagnosen"`
	PatientenId *string   `json:"patientenId"`
}

// KrankheitsbildAuswertungResultat defines model for KrankheitsbildAuswertungResultat.
type KrankheitsbildAuswertungResultat struct {
	Meldungen            *[]Meldung                                 `json:"meldungen"`
	Patienten            *[]KrankheitsbildAuswertungResultatPatient `json:"patienten"`
	Status               *ResultatStatus                            `json:"status,omitempty"`
	UebermittlungsStatus *UebermittlungsStatus                      `json:"uebermittlungsStatus,omitempty"`
}

// KrankheitsbildAuswertungResultatPatient defines model for KrankheitsbildAuswertungResultatPatient.
type KrankheitsbildAuswertungResultatPatient struct {
	Krankheitsbilder *[]string `json:"krankheitsbilder"`
	PatientenId      *string   `json:"patientenId"`
}

// Leistung Alle Informationen zu einer spezifischen Leistung aus den abgerechneten Leistungen.
type Leistung struct {
	Abrechnungsbegruendung *string `json:"abrechnungsbegruendung"`

	// Anforderungszeitpunkt Der Anforderungszeitpunkt der Leistung.
	Anforderungszeitpunkt *Anforderungszeitpunkt `json:"anforderungszeitpunkt,omitempty"`

	// BetreuteamArzt Ein Arzt eines Betreuteams.
	BetreuteamArzt *BetreuteamArzt `json:"betreuteamArzt,omitempty"`

	// BetreuteamBetriebsstaette Eine Betriebsstaette eines Betreuteams.
	BetreuteamBetriebsstaette *BetreuteamBetriebsstaette `json:"betreuteamBetriebsstaette,omitempty"`

	// InVertretungFuer Die Identifikation eines Arztes anhand von LANR und BSNR.
	InVertretungFuer *ArztIdentifikation `json:"inVertretungFuer,omitempty"`
	LeistungsId      string              `json:"leistungsId"`
	Leistungsdatum   *time.Time          `json:"leistungsdatum"`
	Leistungsziffer  string              `json:"leistungsziffer"`
	Sachkosten       *[]Sachkosten       `json:"sachkosten"`

	// Stellvertreter Der Stellvertreter, der in Vertretung für den abrechnenden Vertragsarzt gehandelt hat.
	Stellvertreter *Stellvertreter `json:"stellvertreter,omitempty"`

	// UeberweisenderArzt Die Identifikation eines Arztes anhand von LANR und BSNR.
	UeberweisenderArzt *ArztIdentifikation `json:"ueberweisenderArzt,omitempty"`
	Zusatzinformation  *string             `json:"zusatzinformation"`
}

// LeistungenAnzahlTyp Die Gesamtzahl der übermittelten Leistungen.
type LeistungenAnzahlTyp struct {
	Anzahl                 *int32                           `json:"anzahl,omitempty"`
	LeistungenAnzahlDetail *[]DokumentationsAnzahlDetailTyp `json:"leistungenAnzahlDetail"`
}

// LiefereBereitgestellteTeilnehmerverzeichnisseAnfrageContainer Der Container mit Angaben zum Abruf von bereitgestellten Teilnehmerverzeichnissen.
type LiefereBereitgestellteTeilnehmerverzeichnisseAnfrageContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem       SoftwareInformation `json:"arztInformationsSystem"`
	Testuebermittlung            *bool               `json:"testuebermittlung,omitempty"`
	Vertragspartneridentifikator string              `json:"vertragspartneridentifikator"`
}

// LiefereBereitgestellteTeilnehmerverzeichnisseResultat Die von der Funktion LiefereBereitgestellteTeilnehmerverzeichnisse gelieferte Ergebnisstruktur.
type LiefereBereitgestellteTeilnehmerverzeichnisseResultat struct {
	Meldungen               *[]Meldung                   `json:"meldungen"`
	Status                  *ResultatStatus              `json:"status,omitempty"`
	Teilnehmerverzeichnisse *[]TeilnehmerverzeichnisInfo `json:"teilnehmerverzeichnisse"`
	UebermittlungsStatus    *UebermittlungsStatus        `json:"uebermittlungsStatus,omitempty"`
}

// LiefereTeilnehmerverzeichnisAnfrageContainer Der Container mit Angaben zum Abruf von Teilnehmerverzeichnissen.
type LiefereTeilnehmerverzeichnisAnfrageContainer struct {
	Abrufcode    string `json:"abrufcode"`
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem       SoftwareInformation `json:"arztInformationsSystem"`
	DokumentIdentifikator        string              `json:"dokumentIdentifikator"`
	Testuebermittlung            bool                `json:"testuebermittlung"`
	Vertragspartneridentifikator string              `json:"vertragspartneridentifikator"`
}

// LiefereTeilnehmerverzeichnisResultat Das Ergebnis des Abrufs von Teilnehmerverzeichnissen.
type LiefereTeilnehmerverzeichnisResultat struct {
	PatientParticipationList *string               `json:"PatientParticipationList"`
	Meldungen                *[]Meldung            `json:"meldungen"`
	Status                   *ResultatStatus       `json:"status,omitempty"`
	UebermittlungsStatus     *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// Meldung Die Meldung, die an den Aufrufer gesendet wird.
type Meldung struct {
	Art                  *Meldungsart          `json:"art,omitempty"`
	Code                 *string               `json:"code"`
	Kategorie            *Meldungskategorie    `json:"kategorie,omitempty"`
	Nachricht            *string               `json:"nachricht"`
	Referenzen           *[]Referenz           `json:"referenzen"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// Meldungsart defines model for Meldungsart.
type Meldungsart string

// Meldungskategorie defines model for Meldungskategorie.
type Meldungskategorie string

// Operation Alle Informationen zu einer spezifischen Operation aus den abgerechneten Operationen.
type Operation struct {
	// InVertretungFuer Die Identifikation eines Arztes anhand von LANR und BSNR.
	InVertretungFuer     *ArztIdentifikation        `json:"inVertretungFuer,omitempty"`
	OperationId          string                     `json:"operationId"`
	OperationsDatum      time.Time                  `json:"operationsDatum"`
	OperationsSchluessel string                     `json:"operationsSchluessel"`
	Seitenlokalisation   DiagnoseSeitenlokalisation `json:"seitenlokalisation"`

	// Stellvertreter Der Stellvertreter, der in Vertretung für den abrechnenden Vertragsarzt gehandelt hat.
	Stellvertreter *Stellvertreter `json:"stellvertreter,omitempty"`
}

// Patient Alle Informationen zum Patienten.
type Patient struct {
	AktuelleVertragsteilnahmen *[]Vertragsteilnahme `json:"aktuelleVertragsteilnahmen"`
	Geburtsdatum               string               `json:"geburtsdatum"`
	Geschlecht                 PatientGeschlecht    `json:"geschlecht"`
	Nachname                   string               `json:"nachname"`
	PatientenId                string               `json:"patientenId"`

	// Versicherungsnachweis Informationen über den Versicherungsnachweis des Patienten (i.d.R. Daten der Versichertenkarte).
	Versicherungsnachweis Versicherungsnachweis `json:"versicherungsnachweis"`
	Vorname               string                `json:"vorname"`
}

// PatientGeschlecht defines model for PatientGeschlecht.
type PatientGeschlecht string

// PracManContainer Der Container zum Versand der PracMan-Daten.
type PracManContainer struct {
	Content               []byte    `json:"content"`
	DokumentationsDatum   time.Time `json:"dokumentationsDatum"`
	Testuebermittlung     bool      `json:"testuebermittlung"`
	VertragsIdentifikator string    `json:"vertragsIdentifikator"`
}

// Praxisgebuehr Die zu einer entrichteten Praxisgebühr gehörenden Details.
type Praxisgebuehr struct {
	Datum           time.Time `json:"datum"`
	PraxisgebuehrId string    `json:"praxisgebuehrId"`

	// Stellvertreter Der Stellvertreter, der in Vertretung für den abrechnenden Vertragsarzt gehandelt hat.
	Stellvertreter *Stellvertreter `json:"stellvertreter,omitempty"`
	Ziffer         string          `json:"ziffer"`
}

// PruefeVersichertenteilnahmeResultat defines model for PruefeVersichertenteilnahmeResultat.
type PruefeVersichertenteilnahmeResultat struct {
	Meldungen            *[]Meldung                  `json:"meldungen"`
	Status               *ResultatStatus             `json:"status,omitempty"`
	TeilnahmeResultate   *[]TeilnahmePatientResultat `json:"teilnahmeResultate"`
	UebermittlungsStatus *UebermittlungsStatus       `json:"uebermittlungsStatus,omitempty"`
}

// Referenz Die Referenzen zu den vom Aufrufer übermittelten Objekten.
type Referenz struct {
	Id          *string      `json:"id"`
	ReferenzTyp *ReferenzTyp `json:"referenzTyp,omitempty"`
}

// ReferenzTyp defines model for ReferenzTyp.
type ReferenzTyp string

// ResultatStatus defines model for ResultatStatus.
type ResultatStatus string

// Sachkosten Ein konkreter Kostenpunkt der Sachkosten.
type Sachkosten struct {
	Betrag        string   `json:"betrag"`
	Bezeichnungen []string `json:"bezeichnungen"`
}

// SendeVorEinschreibeLeistungContainer Der Container mit Angaben zum Versand von Voreinschreibeleistungen.
type SendeVorEinschreibeLeistungContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation `json:"arztInformationsSystem"`
	NurPrueflauf           bool                `json:"nurPrueflauf"`

	// Patient Alle Informationen zum Patienten.
	Patient           Patient `json:"patient"`
	Testuebermittlung bool    `json:"testuebermittlung"`

	// Vertragskontext Angaben zum Vertrag.
	Vertragskontext              Vertragskontext `json:"vertragskontext"`
	Vertragspartneridentifikator string          `json:"vertragspartneridentifikator"`

	// VorEinschreibeLeistung Die VorEinschreibeLeistungen des betreffenden Quartals.
	VorEinschreibeLeistung VorEinschreibeLeistung `json:"vorEinschreibeLeistung"`
}

// SoftwareInformation Informationen über das Arztinformationssystem und dessen Hersteller.
type SoftwareInformation struct {
	Name         *string `json:"name"`
	Organisation *string `json:"organisation"`
	SystemOid    string  `json:"systemOid"`
	Version      string  `json:"version"`
}

// Stellvertreter Der Stellvertreter, der in Vertretung für den abrechnenden Vertragsarzt gehandelt hat.
type Stellvertreter struct {
	Lanr string `json:"lanr"`
}

// TeilnahmeListeContainer Der Container mit Angaben zum Abruf von Teilnahmelisten.
type TeilnahmeListeContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem       SoftwareInformation `json:"arztInformationsSystem"`
	Teilnahmeanfragen            []Teilnahmeanfrage  `json:"teilnahmeanfragen"`
	Testuebermittlung            bool                `json:"testuebermittlung"`
	Vertragspartneridentifikator string              `json:"vertragspartneridentifikator"`
}

// TeilnahmePatientResultat defines model for TeilnahmePatientResultat.
type TeilnahmePatientResultat struct {
	PatientenId        *string                            `json:"patientenId"`
	Vertragsteilnahmen *[]TeilnahmePatientVertragResultat `json:"vertragsteilnahmen"`
}

// TeilnahmePatientVertragResultat defines model for TeilnahmePatientVertragResultat.
type TeilnahmePatientVertragResultat struct {
	IstFremdeingeschrieben *bool   `json:"istFremdeingeschrieben"`
	Vertragsidentifikator  *string `json:"vertragsidentifikator"`
}

// Teilnahmeanfrage Die für eine Teilnahmeanfrage nötigen Daten.
type Teilnahmeanfrage struct {
	AbfrageDatum            time.Time `json:"abfrageDatum"`
	KrankenkassenIK         string    `json:"krankenkassenIK"`
	PatientenId             string    `json:"patientenId"`
	Versichertennummer      string    `json:"versichertennummer"`
	VertragsIdentifikatoren []string  `json:"vertragsIdentifikatoren"`
}

// TeilnahmeerklaerungContainer Der Container mit Angaben zum Versand von Teilnahmeerklärungen.
type TeilnahmeerklaerungContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem       SoftwareInformation          `json:"arztInformationsSystem"`
	Teilnahmeerklaerungen        []TeilnahmeerklaerungPatient `json:"teilnahmeerklaerungen"`
	Testuebermittlung            bool                         `json:"testuebermittlung"`
	VertragsIdentifikator        string                       `json:"vertragsIdentifikator"`
	Vertragspartneridentifikator string                       `json:"vertragspartneridentifikator"`
}

// TeilnahmeerklaerungPatient Der Patient, der an dem Vertrag teilnehmen will.
type TeilnahmeerklaerungPatient struct {
	Arztwechselgrund                       *string            `json:"arztwechselgrund"`
	Diagnosen                              *[]DiagnoseElement `json:"diagnosen"`
	Geburtsdatum                           string             `json:"geburtsdatum"`
	IstArztwechsel                         *bool              `json:"istArztwechsel,omitempty"`
	IstEinwilligungTelefonkontaktVorhanden *bool              `json:"istEinwilligungTelefonkontaktVorhanden,omitempty"`
	KassenIk                               string             `json:"kassenIk"`
	KopfDatum                              time.Time          `json:"kopfDatum"`
	Lanr                                   string             `json:"lanr"`
	Nachname                               string             `json:"nachname"`
	PatientenId                            string             `json:"patientenId"`
	TeilnahmeerklaerungIdentifikator       string             `json:"teilnahmeerklaerungIdentifikator"`
	Therapieverfahren                      *Therapieverfahren `json:"therapieverfahren,omitempty"`
	TherapieverfahrenSpecified             *bool              `json:"therapieverfahrenSpecified,omitempty"`
	Versichertennummer                     string             `json:"versichertennummer"`
	Vorname                                string             `json:"vorname"`
}

// TeilnahmeerklaerungResultat Die Resultatsmeldung der gesendeten Teilnahmeerklärungen.
type TeilnahmeerklaerungResultat struct {
	Meldungen            *[]Meldung            `json:"meldungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// TeilnehmerverzeichnisInfo Metainformation zu einem Teilnehmerverzeichnis.
type TeilnehmerverzeichnisInfo struct {
	DokumentIdentifikator *string `json:"dokumentIdentifikator"`
	Jahr                  *int32  `json:"jahr,omitempty"`
	Quartal               *int32  `json:"quartal,omitempty"`
	Version               *int32  `json:"version,omitempty"`
	VertragsIdentifikator *string `json:"vertragsIdentifikator"`
}

// Therapieverfahren defines model for Therapieverfahren.
type Therapieverfahren string

// TransferResultat defines model for TransferResultat.
type TransferResultat struct {
	Meldungen            *[]Meldung            `json:"meldungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	TransferId           *string               `json:"transferId"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// UebermittlungsBelegTyp Der Beleg der erfolgreichen Übermittlung.
type UebermittlungsBelegTyp struct {
	LeistungsIds *[]string `json:"leistungsIds"`
	PatientenIds *[]string `json:"patientenIds"`
}

// UebermittlungsStatus defines model for UebermittlungsStatus.
type UebermittlungsStatus string

// Uebertragungsart defines model for Uebertragungsart.
type Uebertragungsart string

// Ueberweisung Die vollständigen Angaben einer Überweisung durch den abrechnenden Vertragsarzt, oder aber eine Überweisung an den abrechnenden Vertragsarzt.
type Ueberweisung struct {
	AuftragsArt UeberweisungAuftragsart `json:"auftragsArt"`

	// BetreuteamArzt Ein Arzt eines Betreuteams.
	BetreuteamArzt *BetreuteamArzt `json:"betreuteamArzt,omitempty"`

	// BetreuteamBetriebsstaette Eine Betriebsstaette eines Betreuteams.
	BetreuteamBetriebsstaette *BetreuteamBetriebsstaette `json:"betreuteamBetriebsstaette,omitempty"`
	Datum                     time.Time                  `json:"datum"`
	Jahr                      int32                      `json:"jahr"`
	Quartal                   int32                      `json:"quartal"`

	// Stellvertreter Der Stellvertreter, der in Vertretung für den abrechnenden Vertragsarzt gehandelt hat.
	Stellvertreter   *Stellvertreter  `json:"stellvertreter,omitempty"`
	UeberweisungAn   *string          `json:"ueberweisungAn"`
	UeberweisungId   *string          `json:"ueberweisungId"`
	Ueberweisungsart Ueberweisungsart `json:"ueberweisungsart"`
}

// UeberweisungAuftragsart defines model for UeberweisungAuftragsart.
type UeberweisungAuftragsart string

// Ueberweisungsart defines model for Ueberweisungsart.
type Ueberweisungsart string

// Versicherungsnachweis Informationen über den Versicherungsnachweis des Patienten (i.d.R. Daten der Versichertenkarte).
type Versicherungsnachweis struct {
	BesonderePersonengruppe *string `json:"besonderePersonengruppe"`
	DmpKennzeichnung        *string `json:"dmpKennzeichnung"`
	KrankenkassenIk         string  `json:"krankenkassenIk"`
	VersichertenArt         string  `json:"versichertenArt"`
	VersichertenNummer      string  `json:"versichertenNummer"`
}

// Vertragskontext Angaben zum Vertrag.
type Vertragskontext struct {
	AbrechnungsJahr            int32  `json:"abrechnungsJahr"`
	AbrechnungsQuartal         int32  `json:"abrechnungsQuartal"`
	HonoraranlageIdentifikator string `json:"honoraranlageIdentifikator"`
	VertragsIdentifikator      string `json:"vertragsIdentifikator"`
}

// VertragspartnerAerzteResultat Alle Ärzte, die einem Vertragspartner zugeordnet sind.
type VertragspartnerAerzteResultat struct {
	Aerzte    *[]VertragspartnerArzt `json:"aerzte"`
	Meldungen *[]Meldung             `json:"meldungen"`
	Status    *ResultatStatus        `json:"status,omitempty"`
}

// VertragspartnerArzt Ein Arzt mit Gültigkeitszeitraum der einem Vertragspartner zugeordnet ist.
type VertragspartnerArzt struct {
	GueltigBis time.Time `json:"gueltigBis"`
	GueltigVon time.Time `json:"gueltigVon"`
	Lanr       string    `json:"lanr"`
	Nachname   string    `json:"nachname"`
	Titel      *string   `json:"titel"`
	Vorname    string    `json:"vorname"`
}

// VertragspartnerBetriebsstaette Eine Betriebsstaette mit Gültigkeitszeitraum die einem Vertragspartner zugeordnet ist.
type VertragspartnerBetriebsstaette struct {
	Bsnr                    string    `json:"bsnr"`
	GueltigBis              time.Time `json:"gueltigBis"`
	GueltigVon              time.Time `json:"gueltigVon"`
	IstHauptbetriebsstaette bool      `json:"istHauptbetriebsstaette"`
	Ort                     string    `json:"ort"`
	Postleitzahl            string    `json:"postleitzahl"`
	StrasseHausnummer       string    `json:"strasseHausnummer"`
}

// VertragspartnerBetriebsstaettenResultat Alle Betriebsstätten, die einem Vertragspartner zugeordnet sind.
type VertragspartnerBetriebsstaettenResultat struct {
	Betriebsstaetten *[]VertragspartnerBetriebsstaette `json:"betriebsstaetten"`
	Meldungen        *[]Meldung                        `json:"meldungen"`
	Status           *ResultatStatus                   `json:"status,omitempty"`
}

// VertragspartnerResultat Alle Informationen zu einem Vertragspartner.
type VertragspartnerResultat struct {
	Aerzte             *[]VertragspartnerArzt              `json:"aerzte"`
	Betriebsstaetten   *[]VertragspartnerBetriebsstaette   `json:"betriebsstaetten"`
	Identifikator      *string                             `json:"identifikator"`
	Meldungen          *[]Meldung                          `json:"meldungen"`
	Name               *string                             `json:"name"`
	Status             *ResultatStatus                     `json:"status,omitempty"`
	Vertragsteilnahmen *[]VertragspartnerVertragsteilnahme `json:"vertragsteilnahmen"`
}

// VertragspartnerVertragsteilnahme Ein Vertrag mit Gültigkeitszeitraum und Honoraranlage.
type VertragspartnerVertragsteilnahme struct {
	GueltigBis                 time.Time `json:"gueltigBis"`
	GueltigVon                 time.Time `json:"gueltigVon"`
	Honoraranlageidentifikator string    `json:"honoraranlageidentifikator"`
	Vertragsidentifikator      string    `json:"vertragsidentifikator"`
}

// VertragspartnerVertragsteilnahmenResultat Alle Verträge, an denen der Vertragspartner aktuell eingeschrieben ist.
type VertragspartnerVertragsteilnahmenResultat struct {
	Meldungen          *[]Meldung                          `json:"meldungen"`
	Status             *ResultatStatus                     `json:"status,omitempty"`
	Vertragsteilnahmen *[]VertragspartnerVertragsteilnahme `json:"vertragsteilnahmen"`
}

// VertragspartneridentifikatorResultat defines model for VertragspartneridentifikatorResultat.
type VertragspartneridentifikatorResultat struct {
	Meldungen                    *[]Meldung      `json:"meldungen"`
	Status                       *ResultatStatus `json:"status,omitempty"`
	Vertragspartneridentifikator *string         `json:"vertragspartneridentifikator"`
}

// Vertragsteilnahme Alle Informationen zu den Vertragsteilnahmen des abgerechneten Patienten.
type Vertragsteilnahme struct {
	IstVertreterteilnahme bool   `json:"istVertreterteilnahme"`
	VertragsIdentifikator string `json:"vertragsIdentifikator"`
}

// VorEinschreibeLeistung Die VorEinschreibeLeistungen des betreffenden Quartals.
type VorEinschreibeLeistung struct {
	// BetreuteamArzt Ein Arzt eines Betreuteams.
	BetreuteamArzt *BetreuteamArzt `json:"betreuteamArzt,omitempty"`

	// BetreuteamBetriebsstaette Eine Betriebsstaette eines Betreuteams.
	BetreuteamBetriebsstaette *BetreuteamBetriebsstaette `json:"betreuteamBetriebsstaette,omitempty"`
	LeistungsId               string                     `json:"leistungsId"`
	Leistungsdatum            *time.Time                 `json:"leistungsdatum"`
	Leistungsziffer           string                     `json:"leistungsziffer"`
}

// GetApiMigrationenVertragspartneridentifikatorFacharztParams defines parameters for GetApiMigrationenVertragspartneridentifikatorFacharzt.
type GetApiMigrationenVertragspartneridentifikatorFacharztParams struct {
	// MediId Die MediID, die in den Vertragspartneridentifikator migriert werden soll.
	MediId *int32 `form:"mediId,omitempty" json:"mediId,omitempty"`
}

// GetApiMigrationenVertragspartneridentifikatorHausarztParams defines parameters for GetApiMigrationenVertragspartneridentifikatorHausarzt.
type GetApiMigrationenVertragspartneridentifikatorHausarztParams struct {
	// Haevgid Die HaevgID, die in den Vertragspartneridentifikator migriert werden soll.
	Haevgid *int32 `form:"haevgid,omitempty" json:"haevgid,omitempty"`
}

// GetApiVertragspartnerVertragspartneridentifikatorParams defines parameters for GetApiVertragspartnerVertragspartneridentifikator.
type GetApiVertragspartnerVertragspartneridentifikatorParams struct {
	// XAbsenderBsnr Die BSNR des Absenders.
	XAbsenderBsnr *string `json:"X-AbsenderBsnr,omitempty"`

	// XSystemoid Die SystemOID des Absenders.
	XSystemoid *string `json:"X-Systemoid,omitempty"`
}

// GetApiVertragspartnerVertragspartneridentifikatorAerzteParams defines parameters for GetApiVertragspartnerVertragspartneridentifikatorAerzte.
type GetApiVertragspartnerVertragspartneridentifikatorAerzteParams struct {
	// XAbsenderBsnr Die BSNR des Absenders.
	XAbsenderBsnr *string `json:"X-AbsenderBsnr,omitempty"`

	// XSystemoid Die SystemOID des Absenders.
	XSystemoid *string `json:"X-Systemoid,omitempty"`
}

// GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenParams defines parameters for GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaetten.
type GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenParams struct {
	// XAbsenderBsnr Die BSNR des Absenders.
	XAbsenderBsnr *string `json:"X-AbsenderBsnr,omitempty"`

	// XSystemoid Die SystemOID des Absenders.
	XSystemoid *string `json:"X-Systemoid,omitempty"`
}

// GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenParams defines parameters for GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmen.
type GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenParams struct {
	// XAbsenderBsnr Die BSNR des Absenders.
	XAbsenderBsnr *string `json:"X-AbsenderBsnr,omitempty"`

	// XSystemoid Die SystemOID des Absenders.
	XSystemoid *string `json:"X-Systemoid,omitempty"`
}

// PostApiAbrechnungenApplicationWildcardPlusJSONRequestBody defines body for PostApiAbrechnungen for application/*+json ContentType.
type PostApiAbrechnungenApplicationWildcardPlusJSONRequestBody = AbrechnungsContainer

// PostApiAbrechnungenJSONRequestBody defines body for PostApiAbrechnungen for application/json ContentType.
type PostApiAbrechnungenJSONRequestBody = AbrechnungsContainer

// PostApiAbrechnungenVoreinschreibeleistungenApplicationWildcardPlusJSONRequestBody defines body for PostApiAbrechnungenVoreinschreibeleistungen for application/*+json ContentType.
type PostApiAbrechnungenVoreinschreibeleistungenApplicationWildcardPlusJSONRequestBody = SendeVorEinschreibeLeistungContainer

// PostApiAbrechnungenVoreinschreibeleistungenJSONRequestBody defines body for PostApiAbrechnungenVoreinschreibeleistungen for application/json ContentType.
type PostApiAbrechnungenVoreinschreibeleistungenJSONRequestBody = SendeVorEinschreibeLeistungContainer

// PostApiAuswertungenApplicationWildcardPlusJSONRequestBody defines body for PostApiAuswertungen for application/*+json ContentType.
type PostApiAuswertungenApplicationWildcardPlusJSONRequestBody = KrankheitsbildAuswertungContainer

// PostApiAuswertungenJSONRequestBody defines body for PostApiAuswertungen for application/json ContentType.
type PostApiAuswertungenJSONRequestBody = KrankheitsbildAuswertungContainer

// PostApiBereitgestellteteilnehmerverzeichnisseApplicationWildcardPlusJSONRequestBody defines body for PostApiBereitgestellteteilnehmerverzeichnisse for application/*+json ContentType.
type PostApiBereitgestellteteilnehmerverzeichnisseApplicationWildcardPlusJSONRequestBody = LiefereBereitgestellteTeilnehmerverzeichnisseAnfrageContainer

// PostApiBereitgestellteteilnehmerverzeichnisseJSONRequestBody defines body for PostApiBereitgestellteteilnehmerverzeichnisse for application/json ContentType.
type PostApiBereitgestellteteilnehmerverzeichnisseJSONRequestBody = LiefereBereitgestellteTeilnehmerverzeichnisseAnfrageContainer

// PostApiPartnerdatenEdmpApplicationWildcardPlusJSONRequestBody defines body for PostApiPartnerdatenEdmp for application/*+json ContentType.
type PostApiPartnerdatenEdmpApplicationWildcardPlusJSONRequestBody = EDMPContainer

// PostApiPartnerdatenEdmpJSONRequestBody defines body for PostApiPartnerdatenEdmp for application/json ContentType.
type PostApiPartnerdatenEdmpJSONRequestBody = EDMPContainer

// PostApiPartnerdatenPracmanApplicationWildcardPlusJSONRequestBody defines body for PostApiPartnerdatenPracman for application/*+json ContentType.
type PostApiPartnerdatenPracmanApplicationWildcardPlusJSONRequestBody = PracManContainer

// PostApiPartnerdatenPracmanJSONRequestBody defines body for PostApiPartnerdatenPracman for application/json ContentType.
type PostApiPartnerdatenPracmanJSONRequestBody = PracManContainer

// PostApiTeilnehmerverzeichnisApplicationWildcardPlusJSONRequestBody defines body for PostApiTeilnehmerverzeichnis for application/*+json ContentType.
type PostApiTeilnehmerverzeichnisApplicationWildcardPlusJSONRequestBody = LiefereTeilnehmerverzeichnisAnfrageContainer

// PostApiTeilnehmerverzeichnisJSONRequestBody defines body for PostApiTeilnehmerverzeichnis for application/json ContentType.
type PostApiTeilnehmerverzeichnisJSONRequestBody = LiefereTeilnehmerverzeichnisAnfrageContainer

// PostApiVersichertenteilnahmenErklaerungenApplicationWildcardPlusJSONRequestBody defines body for PostApiVersichertenteilnahmenErklaerungen for application/*+json ContentType.
type PostApiVersichertenteilnahmenErklaerungenApplicationWildcardPlusJSONRequestBody = TeilnahmeerklaerungContainer

// PostApiVersichertenteilnahmenErklaerungenJSONRequestBody defines body for PostApiVersichertenteilnahmenErklaerungen for application/json ContentType.
type PostApiVersichertenteilnahmenErklaerungenJSONRequestBody = TeilnahmeerklaerungContainer

// PostApiVersichertenteilnahmenPruefungenApplicationWildcardPlusJSONRequestBody defines body for PostApiVersichertenteilnahmenPruefungen for application/*+json ContentType.
type PostApiVersichertenteilnahmenPruefungenApplicationWildcardPlusJSONRequestBody = TeilnahmeListeContainer

// PostApiVersichertenteilnahmenPruefungenJSONRequestBody defines body for PostApiVersichertenteilnahmenPruefungen for application/json ContentType.
type PostApiVersichertenteilnahmenPruefungenJSONRequestBody = TeilnahmeListeContainer

// RequestEditorFn  is the function signature for the RequestEditor callback function
type RequestEditorFn func(ctx context.Context, req *http.Request) error

// Doer performs HTTP requests.
//
// The standard http.Client implements this interface.
type HttpRequestDoer interface {
	Do(req *http.Request) (*http.Response, error)
}

// Client which conforms to the OpenAPI3 specification for this service.
type Client struct {
	// The endpoint of the server conforming to this interface, with scheme,
	// https://api.deepmap.com for example. This can contain a path relative
	// to the server, such as https://api.deepmap.com/dev-test, and all the
	// paths in the swagger spec will be appended to the server.
	Server string

	// Doer for performing requests, typically a *http.Client with any
	// customized settings, such as certificate chains.
	Client HttpRequestDoer

	// A list of callbacks for modifying requests which are generated before sending over
	// the network.
	RequestEditors []RequestEditorFn
}

// ClientOption allows setting custom parameters during construction
type ClientOption func(*Client) error

// Creates a new Client, with reasonable defaults
func NewClient(server string, opts ...ClientOption) (*Client, error) {
	// create a client with sane default values
	client := Client{
		Server: server,
	}
	// mutate client and add all optional params
	for _, o := range opts {
		if err := o(&client); err != nil {
			return nil, err
		}
	}
	// ensure the server URL always has a trailing slash
	if !strings.HasSuffix(client.Server, "/") {
		client.Server += "/"
	}
	// create httpClient, if not already present
	if client.Client == nil {
		client.Client = &http.Client{}
	}
	return &client, nil
}

// WithHTTPClient allows overriding the default Doer, which is
// automatically created using http.Client. This is useful for tests.
func WithHTTPClient(doer HttpRequestDoer) ClientOption {
	return func(c *Client) error {
		c.Client = doer
		return nil
	}
}

// WithRequestEditorFn allows setting up a callback function, which will be
// called right before sending the request. This can be used to mutate the request.
func WithRequestEditorFn(fn RequestEditorFn) ClientOption {
	return func(c *Client) error {
		c.RequestEditors = append(c.RequestEditors, fn)
		return nil
	}
}

// The interface specification for the client above.
type ClientInterface interface {
	// PostApiAbrechnungenWithBody request with any body
	PostApiAbrechnungenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiAbrechnungenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiAbrechnungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiAbrechnungen(ctx context.Context, body PostApiAbrechnungenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiAbrechnungenVoreinschreibeleistungenWithBody request with any body
	PostApiAbrechnungenVoreinschreibeleistungenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiAbrechnungenVoreinschreibeleistungenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiAbrechnungenVoreinschreibeleistungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiAbrechnungenVoreinschreibeleistungen(ctx context.Context, body PostApiAbrechnungenVoreinschreibeleistungenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiAuswertungenWithBody request with any body
	PostApiAuswertungenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiAuswertungenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiAuswertungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiAuswertungen(ctx context.Context, body PostApiAuswertungenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiBereitgestellteteilnehmerverzeichnisseWithBody request with any body
	PostApiBereitgestellteteilnehmerverzeichnisseWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiBereitgestellteteilnehmerverzeichnisseWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiBereitgestellteteilnehmerverzeichnisseApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiBereitgestellteteilnehmerverzeichnisse(ctx context.Context, body PostApiBereitgestellteteilnehmerverzeichnisseJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApiMigrationenVertragspartneridentifikatorFacharzt request
	GetApiMigrationenVertragspartneridentifikatorFacharzt(ctx context.Context, params *GetApiMigrationenVertragspartneridentifikatorFacharztParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApiMigrationenVertragspartneridentifikatorHausarzt request
	GetApiMigrationenVertragspartneridentifikatorHausarzt(ctx context.Context, params *GetApiMigrationenVertragspartneridentifikatorHausarztParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiPartnerdatenEdmpWithBody request with any body
	PostApiPartnerdatenEdmpWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiPartnerdatenEdmpWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiPartnerdatenEdmpApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiPartnerdatenEdmp(ctx context.Context, body PostApiPartnerdatenEdmpJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiPartnerdatenPracmanWithBody request with any body
	PostApiPartnerdatenPracmanWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiPartnerdatenPracmanWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiPartnerdatenPracmanApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiPartnerdatenPracman(ctx context.Context, body PostApiPartnerdatenPracmanJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiTeilnehmerverzeichnisWithBody request with any body
	PostApiTeilnehmerverzeichnisWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiTeilnehmerverzeichnisWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiTeilnehmerverzeichnisApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiTeilnehmerverzeichnis(ctx context.Context, body PostApiTeilnehmerverzeichnisJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiVersichertenteilnahmenErklaerungenWithBody request with any body
	PostApiVersichertenteilnahmenErklaerungenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiVersichertenteilnahmenErklaerungenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiVersichertenteilnahmenErklaerungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiVersichertenteilnahmenErklaerungen(ctx context.Context, body PostApiVersichertenteilnahmenErklaerungenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiVersichertenteilnahmenPruefungenWithBody request with any body
	PostApiVersichertenteilnahmenPruefungenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiVersichertenteilnahmenPruefungenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiVersichertenteilnahmenPruefungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiVersichertenteilnahmenPruefungen(ctx context.Context, body PostApiVersichertenteilnahmenPruefungenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApiVertragspartnerVertragspartneridentifikator request
	GetApiVertragspartnerVertragspartneridentifikator(ctx context.Context, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApiVertragspartnerVertragspartneridentifikatorAerzte request
	GetApiVertragspartnerVertragspartneridentifikatorAerzte(ctx context.Context, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorAerzteParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaetten request
	GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaetten(ctx context.Context, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmen request
	GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmen(ctx context.Context, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenParams, reqEditors ...RequestEditorFn) (*http.Response, error)
}

func (c *Client) PostApiAbrechnungenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiAbrechnungenRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiAbrechnungenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiAbrechnungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiAbrechnungenRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiAbrechnungen(ctx context.Context, body PostApiAbrechnungenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiAbrechnungenRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiAbrechnungenVoreinschreibeleistungenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiAbrechnungenVoreinschreibeleistungenRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiAbrechnungenVoreinschreibeleistungenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiAbrechnungenVoreinschreibeleistungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiAbrechnungenVoreinschreibeleistungenRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiAbrechnungenVoreinschreibeleistungen(ctx context.Context, body PostApiAbrechnungenVoreinschreibeleistungenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiAbrechnungenVoreinschreibeleistungenRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiAuswertungenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiAuswertungenRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiAuswertungenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiAuswertungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiAuswertungenRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiAuswertungen(ctx context.Context, body PostApiAuswertungenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiAuswertungenRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiBereitgestellteteilnehmerverzeichnisseWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiBereitgestellteteilnehmerverzeichnisseRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiBereitgestellteteilnehmerverzeichnisseWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiBereitgestellteteilnehmerverzeichnisseApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiBereitgestellteteilnehmerverzeichnisseRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiBereitgestellteteilnehmerverzeichnisse(ctx context.Context, body PostApiBereitgestellteteilnehmerverzeichnisseJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiBereitgestellteteilnehmerverzeichnisseRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApiMigrationenVertragspartneridentifikatorFacharzt(ctx context.Context, params *GetApiMigrationenVertragspartneridentifikatorFacharztParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApiMigrationenVertragspartneridentifikatorFacharztRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApiMigrationenVertragspartneridentifikatorHausarzt(ctx context.Context, params *GetApiMigrationenVertragspartneridentifikatorHausarztParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApiMigrationenVertragspartneridentifikatorHausarztRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiPartnerdatenEdmpWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiPartnerdatenEdmpRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiPartnerdatenEdmpWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiPartnerdatenEdmpApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiPartnerdatenEdmpRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiPartnerdatenEdmp(ctx context.Context, body PostApiPartnerdatenEdmpJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiPartnerdatenEdmpRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiPartnerdatenPracmanWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiPartnerdatenPracmanRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiPartnerdatenPracmanWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiPartnerdatenPracmanApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiPartnerdatenPracmanRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiPartnerdatenPracman(ctx context.Context, body PostApiPartnerdatenPracmanJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiPartnerdatenPracmanRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiTeilnehmerverzeichnisWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiTeilnehmerverzeichnisRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiTeilnehmerverzeichnisWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiTeilnehmerverzeichnisApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiTeilnehmerverzeichnisRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiTeilnehmerverzeichnis(ctx context.Context, body PostApiTeilnehmerverzeichnisJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiTeilnehmerverzeichnisRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiVersichertenteilnahmenErklaerungenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiVersichertenteilnahmenErklaerungenRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiVersichertenteilnahmenErklaerungenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiVersichertenteilnahmenErklaerungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiVersichertenteilnahmenErklaerungenRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiVersichertenteilnahmenErklaerungen(ctx context.Context, body PostApiVersichertenteilnahmenErklaerungenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiVersichertenteilnahmenErklaerungenRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiVersichertenteilnahmenPruefungenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiVersichertenteilnahmenPruefungenRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiVersichertenteilnahmenPruefungenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiVersichertenteilnahmenPruefungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiVersichertenteilnahmenPruefungenRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiVersichertenteilnahmenPruefungen(ctx context.Context, body PostApiVersichertenteilnahmenPruefungenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiVersichertenteilnahmenPruefungenRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApiVertragspartnerVertragspartneridentifikator(ctx context.Context, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApiVertragspartnerVertragspartneridentifikatorRequest(c.Server, vertragspartneridentifikator, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApiVertragspartnerVertragspartneridentifikatorAerzte(ctx context.Context, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorAerzteParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApiVertragspartnerVertragspartneridentifikatorAerzteRequest(c.Server, vertragspartneridentifikator, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaetten(ctx context.Context, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenRequest(c.Server, vertragspartneridentifikator, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmen(ctx context.Context, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenRequest(c.Server, vertragspartneridentifikator, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

// NewPostApiAbrechnungenRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiAbrechnungen builder with application/*+json body
func NewPostApiAbrechnungenRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiAbrechnungenApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiAbrechnungenRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiAbrechnungenRequest calls the generic PostApiAbrechnungen builder with application/json body
func NewPostApiAbrechnungenRequest(server string, body PostApiAbrechnungenJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiAbrechnungenRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiAbrechnungenRequestWithBody generates requests for PostApiAbrechnungen with any type of body
func NewPostApiAbrechnungenRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/hzv/abrechnungen")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiAbrechnungenVoreinschreibeleistungenRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiAbrechnungenVoreinschreibeleistungen builder with application/*+json body
func NewPostApiAbrechnungenVoreinschreibeleistungenRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiAbrechnungenVoreinschreibeleistungenApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiAbrechnungenVoreinschreibeleistungenRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiAbrechnungenVoreinschreibeleistungenRequest calls the generic PostApiAbrechnungenVoreinschreibeleistungen builder with application/json body
func NewPostApiAbrechnungenVoreinschreibeleistungenRequest(server string, body PostApiAbrechnungenVoreinschreibeleistungenJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiAbrechnungenVoreinschreibeleistungenRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiAbrechnungenVoreinschreibeleistungenRequestWithBody generates requests for PostApiAbrechnungenVoreinschreibeleistungen with any type of body
func NewPostApiAbrechnungenVoreinschreibeleistungenRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/hzv/abrechnungen/voreinschreibeleistungen")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiAuswertungenRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiAuswertungen builder with application/*+json body
func NewPostApiAuswertungenRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiAuswertungenApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiAuswertungenRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiAuswertungenRequest calls the generic PostApiAuswertungen builder with application/json body
func NewPostApiAuswertungenRequest(server string, body PostApiAuswertungenJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiAuswertungenRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiAuswertungenRequestWithBody generates requests for PostApiAuswertungen with any type of body
func NewPostApiAuswertungenRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/hzv/auswertungen")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiBereitgestellteteilnehmerverzeichnisseRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiBereitgestellteteilnehmerverzeichnisse builder with application/*+json body
func NewPostApiBereitgestellteteilnehmerverzeichnisseRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiBereitgestellteteilnehmerverzeichnisseApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiBereitgestellteteilnehmerverzeichnisseRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiBereitgestellteteilnehmerverzeichnisseRequest calls the generic PostApiBereitgestellteteilnehmerverzeichnisse builder with application/json body
func NewPostApiBereitgestellteteilnehmerverzeichnisseRequest(server string, body PostApiBereitgestellteteilnehmerverzeichnisseJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiBereitgestellteteilnehmerverzeichnisseRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiBereitgestellteteilnehmerverzeichnisseRequestWithBody generates requests for PostApiBereitgestellteteilnehmerverzeichnisse with any type of body
func NewPostApiBereitgestellteteilnehmerverzeichnisseRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/hzv/bereitgestellteteilnehmerverzeichnisse")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetApiMigrationenVertragspartneridentifikatorFacharztRequest generates requests for GetApiMigrationenVertragspartneridentifikatorFacharzt
func NewGetApiMigrationenVertragspartneridentifikatorFacharztRequest(server string, params *GetApiMigrationenVertragspartneridentifikatorFacharztParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/hzv/migrationen/vertragspartneridentifikator/facharzt")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.MediId != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "mediId", runtime.ParamLocationQuery, *params.MediId); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetApiMigrationenVertragspartneridentifikatorHausarztRequest generates requests for GetApiMigrationenVertragspartneridentifikatorHausarzt
func NewGetApiMigrationenVertragspartneridentifikatorHausarztRequest(server string, params *GetApiMigrationenVertragspartneridentifikatorHausarztParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/hzv/migrationen/vertragspartneridentifikator/hausarzt")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Haevgid != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "haevgid", runtime.ParamLocationQuery, *params.Haevgid); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPostApiPartnerdatenEdmpRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiPartnerdatenEdmp builder with application/*+json body
func NewPostApiPartnerdatenEdmpRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiPartnerdatenEdmpApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiPartnerdatenEdmpRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiPartnerdatenEdmpRequest calls the generic PostApiPartnerdatenEdmp builder with application/json body
func NewPostApiPartnerdatenEdmpRequest(server string, body PostApiPartnerdatenEdmpJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiPartnerdatenEdmpRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiPartnerdatenEdmpRequestWithBody generates requests for PostApiPartnerdatenEdmp with any type of body
func NewPostApiPartnerdatenEdmpRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/hzv/partnerdaten/edmp")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiPartnerdatenPracmanRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiPartnerdatenPracman builder with application/*+json body
func NewPostApiPartnerdatenPracmanRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiPartnerdatenPracmanApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiPartnerdatenPracmanRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiPartnerdatenPracmanRequest calls the generic PostApiPartnerdatenPracman builder with application/json body
func NewPostApiPartnerdatenPracmanRequest(server string, body PostApiPartnerdatenPracmanJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiPartnerdatenPracmanRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiPartnerdatenPracmanRequestWithBody generates requests for PostApiPartnerdatenPracman with any type of body
func NewPostApiPartnerdatenPracmanRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/hzv/partnerdaten/pracman")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiTeilnehmerverzeichnisRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiTeilnehmerverzeichnis builder with application/*+json body
func NewPostApiTeilnehmerverzeichnisRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiTeilnehmerverzeichnisApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiTeilnehmerverzeichnisRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiTeilnehmerverzeichnisRequest calls the generic PostApiTeilnehmerverzeichnis builder with application/json body
func NewPostApiTeilnehmerverzeichnisRequest(server string, body PostApiTeilnehmerverzeichnisJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiTeilnehmerverzeichnisRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiTeilnehmerverzeichnisRequestWithBody generates requests for PostApiTeilnehmerverzeichnis with any type of body
func NewPostApiTeilnehmerverzeichnisRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/hzv/teilnehmerverzeichnis")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiVersichertenteilnahmenErklaerungenRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiVersichertenteilnahmenErklaerungen builder with application/*+json body
func NewPostApiVersichertenteilnahmenErklaerungenRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiVersichertenteilnahmenErklaerungenApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiVersichertenteilnahmenErklaerungenRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiVersichertenteilnahmenErklaerungenRequest calls the generic PostApiVersichertenteilnahmenErklaerungen builder with application/json body
func NewPostApiVersichertenteilnahmenErklaerungenRequest(server string, body PostApiVersichertenteilnahmenErklaerungenJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiVersichertenteilnahmenErklaerungenRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiVersichertenteilnahmenErklaerungenRequestWithBody generates requests for PostApiVersichertenteilnahmenErklaerungen with any type of body
func NewPostApiVersichertenteilnahmenErklaerungenRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/hzv/versichertenteilnahmen/erklaerungen")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiVersichertenteilnahmenPruefungenRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiVersichertenteilnahmenPruefungen builder with application/*+json body
func NewPostApiVersichertenteilnahmenPruefungenRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiVersichertenteilnahmenPruefungenApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiVersichertenteilnahmenPruefungenRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiVersichertenteilnahmenPruefungenRequest calls the generic PostApiVersichertenteilnahmenPruefungen builder with application/json body
func NewPostApiVersichertenteilnahmenPruefungenRequest(server string, body PostApiVersichertenteilnahmenPruefungenJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiVersichertenteilnahmenPruefungenRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiVersichertenteilnahmenPruefungenRequestWithBody generates requests for PostApiVersichertenteilnahmenPruefungen with any type of body
func NewPostApiVersichertenteilnahmenPruefungenRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/hzv/versichertenteilnahmen/pruefungen")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetApiVertragspartnerVertragspartneridentifikatorRequest generates requests for GetApiVertragspartnerVertragspartneridentifikator
func NewGetApiVertragspartnerVertragspartneridentifikatorRequest(server string, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "vertragspartneridentifikator", runtime.ParamLocationPath, vertragspartneridentifikator)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/hzv/vertragspartner/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	if params != nil {

		if params.XAbsenderBsnr != nil {
			var headerParam0 string

			headerParam0, err = runtime.StyleParamWithLocation("simple", false, "X-AbsenderBsnr", runtime.ParamLocationHeader, *params.XAbsenderBsnr)
			if err != nil {
				return nil, err
			}

			req.Header.Set("X-AbsenderBsnr", headerParam0)
		}

		if params.XSystemoid != nil {
			var headerParam1 string

			headerParam1, err = runtime.StyleParamWithLocation("simple", false, "X-Systemoid", runtime.ParamLocationHeader, *params.XSystemoid)
			if err != nil {
				return nil, err
			}

			req.Header.Set("X-Systemoid", headerParam1)
		}

	}

	return req, nil
}

// NewGetApiVertragspartnerVertragspartneridentifikatorAerzteRequest generates requests for GetApiVertragspartnerVertragspartneridentifikatorAerzte
func NewGetApiVertragspartnerVertragspartneridentifikatorAerzteRequest(server string, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorAerzteParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "vertragspartneridentifikator", runtime.ParamLocationPath, vertragspartneridentifikator)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/hzv/vertragspartner/%s/aerzte", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	if params != nil {

		if params.XAbsenderBsnr != nil {
			var headerParam0 string

			headerParam0, err = runtime.StyleParamWithLocation("simple", false, "X-AbsenderBsnr", runtime.ParamLocationHeader, *params.XAbsenderBsnr)
			if err != nil {
				return nil, err
			}

			req.Header.Set("X-AbsenderBsnr", headerParam0)
		}

		if params.XSystemoid != nil {
			var headerParam1 string

			headerParam1, err = runtime.StyleParamWithLocation("simple", false, "X-Systemoid", runtime.ParamLocationHeader, *params.XSystemoid)
			if err != nil {
				return nil, err
			}

			req.Header.Set("X-Systemoid", headerParam1)
		}

	}

	return req, nil
}

// NewGetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenRequest generates requests for GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaetten
func NewGetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenRequest(server string, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "vertragspartneridentifikator", runtime.ParamLocationPath, vertragspartneridentifikator)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/hzv/vertragspartner/%s/betriebsstaetten", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	if params != nil {

		if params.XAbsenderBsnr != nil {
			var headerParam0 string

			headerParam0, err = runtime.StyleParamWithLocation("simple", false, "X-AbsenderBsnr", runtime.ParamLocationHeader, *params.XAbsenderBsnr)
			if err != nil {
				return nil, err
			}

			req.Header.Set("X-AbsenderBsnr", headerParam0)
		}

		if params.XSystemoid != nil {
			var headerParam1 string

			headerParam1, err = runtime.StyleParamWithLocation("simple", false, "X-Systemoid", runtime.ParamLocationHeader, *params.XSystemoid)
			if err != nil {
				return nil, err
			}

			req.Header.Set("X-Systemoid", headerParam1)
		}

	}

	return req, nil
}

// NewGetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenRequest generates requests for GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmen
func NewGetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenRequest(server string, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "vertragspartneridentifikator", runtime.ParamLocationPath, vertragspartneridentifikator)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/hzv/vertragspartner/%s/vertragsteilnahmen", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	if params != nil {

		if params.XAbsenderBsnr != nil {
			var headerParam0 string

			headerParam0, err = runtime.StyleParamWithLocation("simple", false, "X-AbsenderBsnr", runtime.ParamLocationHeader, *params.XAbsenderBsnr)
			if err != nil {
				return nil, err
			}

			req.Header.Set("X-AbsenderBsnr", headerParam0)
		}

		if params.XSystemoid != nil {
			var headerParam1 string

			headerParam1, err = runtime.StyleParamWithLocation("simple", false, "X-Systemoid", runtime.ParamLocationHeader, *params.XSystemoid)
			if err != nil {
				return nil, err
			}

			req.Header.Set("X-Systemoid", headerParam1)
		}

	}

	return req, nil
}

func (c *Client) applyEditors(ctx context.Context, req *http.Request, additionalEditors []RequestEditorFn) error {
	for _, r := range c.RequestEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	for _, r := range additionalEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	return nil
}

// ClientWithResponses builds on ClientInterface to offer response payloads
type ClientWithResponses struct {
	ClientInterface
}

// NewClientWithResponses creates a new ClientWithResponses, which wraps
// Client with return type handling
func NewClientWithResponses(server string, opts ...ClientOption) (*ClientWithResponses, error) {
	client, err := NewClient(server, opts...)
	if err != nil {
		return nil, err
	}
	return &ClientWithResponses{client}, nil
}

// WithBaseURL overrides the baseURL.
func WithBaseURL(baseURL string) ClientOption {
	return func(c *Client) error {
		newBaseURL, err := url.Parse(baseURL)
		if err != nil {
			return err
		}
		c.Server = newBaseURL.String()
		return nil
	}
}

// ClientWithResponsesInterface is the interface specification for the client with responses above.
type ClientWithResponsesInterface interface {
	// PostApiAbrechnungenWithBodyWithResponse request with any body
	PostApiAbrechnungenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiAbrechnungenResponse, error)

	PostApiAbrechnungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiAbrechnungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiAbrechnungenResponse, error)

	PostApiAbrechnungenWithResponse(ctx context.Context, body PostApiAbrechnungenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiAbrechnungenResponse, error)

	// PostApiAbrechnungenVoreinschreibeleistungenWithBodyWithResponse request with any body
	PostApiAbrechnungenVoreinschreibeleistungenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiAbrechnungenVoreinschreibeleistungenResponse, error)

	PostApiAbrechnungenVoreinschreibeleistungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiAbrechnungenVoreinschreibeleistungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiAbrechnungenVoreinschreibeleistungenResponse, error)

	PostApiAbrechnungenVoreinschreibeleistungenWithResponse(ctx context.Context, body PostApiAbrechnungenVoreinschreibeleistungenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiAbrechnungenVoreinschreibeleistungenResponse, error)

	// PostApiAuswertungenWithBodyWithResponse request with any body
	PostApiAuswertungenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiAuswertungenResponse, error)

	PostApiAuswertungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiAuswertungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiAuswertungenResponse, error)

	PostApiAuswertungenWithResponse(ctx context.Context, body PostApiAuswertungenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiAuswertungenResponse, error)

	// PostApiBereitgestellteteilnehmerverzeichnisseWithBodyWithResponse request with any body
	PostApiBereitgestellteteilnehmerverzeichnisseWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiBereitgestellteteilnehmerverzeichnisseResponse, error)

	PostApiBereitgestellteteilnehmerverzeichnisseWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiBereitgestellteteilnehmerverzeichnisseApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiBereitgestellteteilnehmerverzeichnisseResponse, error)

	PostApiBereitgestellteteilnehmerverzeichnisseWithResponse(ctx context.Context, body PostApiBereitgestellteteilnehmerverzeichnisseJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiBereitgestellteteilnehmerverzeichnisseResponse, error)

	// GetApiMigrationenVertragspartneridentifikatorFacharztWithResponse request
	GetApiMigrationenVertragspartneridentifikatorFacharztWithResponse(ctx context.Context, params *GetApiMigrationenVertragspartneridentifikatorFacharztParams, reqEditors ...RequestEditorFn) (*GetApiMigrationenVertragspartneridentifikatorFacharztResponse, error)

	// GetApiMigrationenVertragspartneridentifikatorHausarztWithResponse request
	GetApiMigrationenVertragspartneridentifikatorHausarztWithResponse(ctx context.Context, params *GetApiMigrationenVertragspartneridentifikatorHausarztParams, reqEditors ...RequestEditorFn) (*GetApiMigrationenVertragspartneridentifikatorHausarztResponse, error)

	// PostApiPartnerdatenEdmpWithBodyWithResponse request with any body
	PostApiPartnerdatenEdmpWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiPartnerdatenEdmpResponse, error)

	PostApiPartnerdatenEdmpWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiPartnerdatenEdmpApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiPartnerdatenEdmpResponse, error)

	PostApiPartnerdatenEdmpWithResponse(ctx context.Context, body PostApiPartnerdatenEdmpJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiPartnerdatenEdmpResponse, error)

	// PostApiPartnerdatenPracmanWithBodyWithResponse request with any body
	PostApiPartnerdatenPracmanWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiPartnerdatenPracmanResponse, error)

	PostApiPartnerdatenPracmanWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiPartnerdatenPracmanApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiPartnerdatenPracmanResponse, error)

	PostApiPartnerdatenPracmanWithResponse(ctx context.Context, body PostApiPartnerdatenPracmanJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiPartnerdatenPracmanResponse, error)

	// PostApiTeilnehmerverzeichnisWithBodyWithResponse request with any body
	PostApiTeilnehmerverzeichnisWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiTeilnehmerverzeichnisResponse, error)

	PostApiTeilnehmerverzeichnisWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiTeilnehmerverzeichnisApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTeilnehmerverzeichnisResponse, error)

	PostApiTeilnehmerverzeichnisWithResponse(ctx context.Context, body PostApiTeilnehmerverzeichnisJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTeilnehmerverzeichnisResponse, error)

	// PostApiVersichertenteilnahmenErklaerungenWithBodyWithResponse request with any body
	PostApiVersichertenteilnahmenErklaerungenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiVersichertenteilnahmenErklaerungenResponse, error)

	PostApiVersichertenteilnahmenErklaerungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiVersichertenteilnahmenErklaerungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiVersichertenteilnahmenErklaerungenResponse, error)

	PostApiVersichertenteilnahmenErklaerungenWithResponse(ctx context.Context, body PostApiVersichertenteilnahmenErklaerungenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiVersichertenteilnahmenErklaerungenResponse, error)

	// PostApiVersichertenteilnahmenPruefungenWithBodyWithResponse request with any body
	PostApiVersichertenteilnahmenPruefungenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiVersichertenteilnahmenPruefungenResponse, error)

	PostApiVersichertenteilnahmenPruefungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiVersichertenteilnahmenPruefungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiVersichertenteilnahmenPruefungenResponse, error)

	PostApiVersichertenteilnahmenPruefungenWithResponse(ctx context.Context, body PostApiVersichertenteilnahmenPruefungenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiVersichertenteilnahmenPruefungenResponse, error)

	// GetApiVertragspartnerVertragspartneridentifikatorWithResponse request
	GetApiVertragspartnerVertragspartneridentifikatorWithResponse(ctx context.Context, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorParams, reqEditors ...RequestEditorFn) (*GetApiVertragspartnerVertragspartneridentifikatorResponse, error)

	// GetApiVertragspartnerVertragspartneridentifikatorAerzteWithResponse request
	GetApiVertragspartnerVertragspartneridentifikatorAerzteWithResponse(ctx context.Context, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorAerzteParams, reqEditors ...RequestEditorFn) (*GetApiVertragspartnerVertragspartneridentifikatorAerzteResponse, error)

	// GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenWithResponse request
	GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenWithResponse(ctx context.Context, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenParams, reqEditors ...RequestEditorFn) (*GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenResponse, error)

	// GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenWithResponse request
	GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenWithResponse(ctx context.Context, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenParams, reqEditors ...RequestEditorFn) (*GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenResponse, error)
}

type PostApiAbrechnungenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *AbrechnungsResultat
}

// Status returns HTTPResponse.Status
func (r PostApiAbrechnungenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiAbrechnungenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiAbrechnungenVoreinschreibeleistungenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *TransferResultat
}

// Status returns HTTPResponse.Status
func (r PostApiAbrechnungenVoreinschreibeleistungenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiAbrechnungenVoreinschreibeleistungenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiAuswertungenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *KrankheitsbildAuswertungResultat
}

// Status returns HTTPResponse.Status
func (r PostApiAuswertungenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiAuswertungenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiBereitgestellteteilnehmerverzeichnisseResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *LiefereBereitgestellteTeilnehmerverzeichnisseResultat
}

// Status returns HTTPResponse.Status
func (r PostApiBereitgestellteteilnehmerverzeichnisseResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiBereitgestellteteilnehmerverzeichnisseResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApiMigrationenVertragspartneridentifikatorFacharztResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *VertragspartneridentifikatorResultat
}

// Status returns HTTPResponse.Status
func (r GetApiMigrationenVertragspartneridentifikatorFacharztResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApiMigrationenVertragspartneridentifikatorFacharztResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApiMigrationenVertragspartneridentifikatorHausarztResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *VertragspartneridentifikatorResultat
}

// Status returns HTTPResponse.Status
func (r GetApiMigrationenVertragspartneridentifikatorHausarztResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApiMigrationenVertragspartneridentifikatorHausarztResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiPartnerdatenEdmpResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *TransferResultat
}

// Status returns HTTPResponse.Status
func (r PostApiPartnerdatenEdmpResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiPartnerdatenEdmpResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiPartnerdatenPracmanResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *TransferResultat
}

// Status returns HTTPResponse.Status
func (r PostApiPartnerdatenPracmanResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiPartnerdatenPracmanResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiTeilnehmerverzeichnisResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *LiefereTeilnehmerverzeichnisResultat
}

// Status returns HTTPResponse.Status
func (r PostApiTeilnehmerverzeichnisResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiTeilnehmerverzeichnisResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiVersichertenteilnahmenErklaerungenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *TeilnahmeerklaerungResultat
}

// Status returns HTTPResponse.Status
func (r PostApiVersichertenteilnahmenErklaerungenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiVersichertenteilnahmenErklaerungenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiVersichertenteilnahmenPruefungenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *PruefeVersichertenteilnahmeResultat
}

// Status returns HTTPResponse.Status
func (r PostApiVersichertenteilnahmenPruefungenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiVersichertenteilnahmenPruefungenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApiVertragspartnerVertragspartneridentifikatorResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *VertragspartnerResultat
}

// Status returns HTTPResponse.Status
func (r GetApiVertragspartnerVertragspartneridentifikatorResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApiVertragspartnerVertragspartneridentifikatorResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApiVertragspartnerVertragspartneridentifikatorAerzteResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *VertragspartnerAerzteResultat
}

// Status returns HTTPResponse.Status
func (r GetApiVertragspartnerVertragspartneridentifikatorAerzteResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApiVertragspartnerVertragspartneridentifikatorAerzteResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *VertragspartnerBetriebsstaettenResultat
}

// Status returns HTTPResponse.Status
func (r GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *VertragspartnerVertragsteilnahmenResultat
}

// Status returns HTTPResponse.Status
func (r GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

// PostApiAbrechnungenWithBodyWithResponse request with arbitrary body returning *PostApiAbrechnungenResponse
func (c *ClientWithResponses) PostApiAbrechnungenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiAbrechnungenResponse, error) {
	rsp, err := c.PostApiAbrechnungenWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiAbrechnungenResponse(rsp)
}

func (c *ClientWithResponses) PostApiAbrechnungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiAbrechnungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiAbrechnungenResponse, error) {
	rsp, err := c.PostApiAbrechnungenWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiAbrechnungenResponse(rsp)
}

func (c *ClientWithResponses) PostApiAbrechnungenWithResponse(ctx context.Context, body PostApiAbrechnungenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiAbrechnungenResponse, error) {
	rsp, err := c.PostApiAbrechnungen(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiAbrechnungenResponse(rsp)
}

// PostApiAbrechnungenVoreinschreibeleistungenWithBodyWithResponse request with arbitrary body returning *PostApiAbrechnungenVoreinschreibeleistungenResponse
func (c *ClientWithResponses) PostApiAbrechnungenVoreinschreibeleistungenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiAbrechnungenVoreinschreibeleistungenResponse, error) {
	rsp, err := c.PostApiAbrechnungenVoreinschreibeleistungenWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiAbrechnungenVoreinschreibeleistungenResponse(rsp)
}

func (c *ClientWithResponses) PostApiAbrechnungenVoreinschreibeleistungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiAbrechnungenVoreinschreibeleistungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiAbrechnungenVoreinschreibeleistungenResponse, error) {
	rsp, err := c.PostApiAbrechnungenVoreinschreibeleistungenWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiAbrechnungenVoreinschreibeleistungenResponse(rsp)
}

func (c *ClientWithResponses) PostApiAbrechnungenVoreinschreibeleistungenWithResponse(ctx context.Context, body PostApiAbrechnungenVoreinschreibeleistungenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiAbrechnungenVoreinschreibeleistungenResponse, error) {
	rsp, err := c.PostApiAbrechnungenVoreinschreibeleistungen(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiAbrechnungenVoreinschreibeleistungenResponse(rsp)
}

// PostApiAuswertungenWithBodyWithResponse request with arbitrary body returning *PostApiAuswertungenResponse
func (c *ClientWithResponses) PostApiAuswertungenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiAuswertungenResponse, error) {
	rsp, err := c.PostApiAuswertungenWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiAuswertungenResponse(rsp)
}

func (c *ClientWithResponses) PostApiAuswertungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiAuswertungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiAuswertungenResponse, error) {
	rsp, err := c.PostApiAuswertungenWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiAuswertungenResponse(rsp)
}

func (c *ClientWithResponses) PostApiAuswertungenWithResponse(ctx context.Context, body PostApiAuswertungenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiAuswertungenResponse, error) {
	rsp, err := c.PostApiAuswertungen(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiAuswertungenResponse(rsp)
}

// PostApiBereitgestellteteilnehmerverzeichnisseWithBodyWithResponse request with arbitrary body returning *PostApiBereitgestellteteilnehmerverzeichnisseResponse
func (c *ClientWithResponses) PostApiBereitgestellteteilnehmerverzeichnisseWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiBereitgestellteteilnehmerverzeichnisseResponse, error) {
	rsp, err := c.PostApiBereitgestellteteilnehmerverzeichnisseWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiBereitgestellteteilnehmerverzeichnisseResponse(rsp)
}

func (c *ClientWithResponses) PostApiBereitgestellteteilnehmerverzeichnisseWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiBereitgestellteteilnehmerverzeichnisseApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiBereitgestellteteilnehmerverzeichnisseResponse, error) {
	rsp, err := c.PostApiBereitgestellteteilnehmerverzeichnisseWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiBereitgestellteteilnehmerverzeichnisseResponse(rsp)
}

func (c *ClientWithResponses) PostApiBereitgestellteteilnehmerverzeichnisseWithResponse(ctx context.Context, body PostApiBereitgestellteteilnehmerverzeichnisseJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiBereitgestellteteilnehmerverzeichnisseResponse, error) {
	rsp, err := c.PostApiBereitgestellteteilnehmerverzeichnisse(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiBereitgestellteteilnehmerverzeichnisseResponse(rsp)
}

// GetApiMigrationenVertragspartneridentifikatorFacharztWithResponse request returning *GetApiMigrationenVertragspartneridentifikatorFacharztResponse
func (c *ClientWithResponses) GetApiMigrationenVertragspartneridentifikatorFacharztWithResponse(ctx context.Context, params *GetApiMigrationenVertragspartneridentifikatorFacharztParams, reqEditors ...RequestEditorFn) (*GetApiMigrationenVertragspartneridentifikatorFacharztResponse, error) {
	rsp, err := c.GetApiMigrationenVertragspartneridentifikatorFacharzt(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApiMigrationenVertragspartneridentifikatorFacharztResponse(rsp)
}

// GetApiMigrationenVertragspartneridentifikatorHausarztWithResponse request returning *GetApiMigrationenVertragspartneridentifikatorHausarztResponse
func (c *ClientWithResponses) GetApiMigrationenVertragspartneridentifikatorHausarztWithResponse(ctx context.Context, params *GetApiMigrationenVertragspartneridentifikatorHausarztParams, reqEditors ...RequestEditorFn) (*GetApiMigrationenVertragspartneridentifikatorHausarztResponse, error) {
	rsp, err := c.GetApiMigrationenVertragspartneridentifikatorHausarzt(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApiMigrationenVertragspartneridentifikatorHausarztResponse(rsp)
}

// PostApiPartnerdatenEdmpWithBodyWithResponse request with arbitrary body returning *PostApiPartnerdatenEdmpResponse
func (c *ClientWithResponses) PostApiPartnerdatenEdmpWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiPartnerdatenEdmpResponse, error) {
	rsp, err := c.PostApiPartnerdatenEdmpWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiPartnerdatenEdmpResponse(rsp)
}

func (c *ClientWithResponses) PostApiPartnerdatenEdmpWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiPartnerdatenEdmpApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiPartnerdatenEdmpResponse, error) {
	rsp, err := c.PostApiPartnerdatenEdmpWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiPartnerdatenEdmpResponse(rsp)
}

func (c *ClientWithResponses) PostApiPartnerdatenEdmpWithResponse(ctx context.Context, body PostApiPartnerdatenEdmpJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiPartnerdatenEdmpResponse, error) {
	rsp, err := c.PostApiPartnerdatenEdmp(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiPartnerdatenEdmpResponse(rsp)
}

// PostApiPartnerdatenPracmanWithBodyWithResponse request with arbitrary body returning *PostApiPartnerdatenPracmanResponse
func (c *ClientWithResponses) PostApiPartnerdatenPracmanWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiPartnerdatenPracmanResponse, error) {
	rsp, err := c.PostApiPartnerdatenPracmanWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiPartnerdatenPracmanResponse(rsp)
}

func (c *ClientWithResponses) PostApiPartnerdatenPracmanWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiPartnerdatenPracmanApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiPartnerdatenPracmanResponse, error) {
	rsp, err := c.PostApiPartnerdatenPracmanWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiPartnerdatenPracmanResponse(rsp)
}

func (c *ClientWithResponses) PostApiPartnerdatenPracmanWithResponse(ctx context.Context, body PostApiPartnerdatenPracmanJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiPartnerdatenPracmanResponse, error) {
	rsp, err := c.PostApiPartnerdatenPracman(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiPartnerdatenPracmanResponse(rsp)
}

// PostApiTeilnehmerverzeichnisWithBodyWithResponse request with arbitrary body returning *PostApiTeilnehmerverzeichnisResponse
func (c *ClientWithResponses) PostApiTeilnehmerverzeichnisWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiTeilnehmerverzeichnisResponse, error) {
	rsp, err := c.PostApiTeilnehmerverzeichnisWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiTeilnehmerverzeichnisResponse(rsp)
}

func (c *ClientWithResponses) PostApiTeilnehmerverzeichnisWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiTeilnehmerverzeichnisApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTeilnehmerverzeichnisResponse, error) {
	rsp, err := c.PostApiTeilnehmerverzeichnisWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiTeilnehmerverzeichnisResponse(rsp)
}

func (c *ClientWithResponses) PostApiTeilnehmerverzeichnisWithResponse(ctx context.Context, body PostApiTeilnehmerverzeichnisJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTeilnehmerverzeichnisResponse, error) {
	rsp, err := c.PostApiTeilnehmerverzeichnis(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiTeilnehmerverzeichnisResponse(rsp)
}

// PostApiVersichertenteilnahmenErklaerungenWithBodyWithResponse request with arbitrary body returning *PostApiVersichertenteilnahmenErklaerungenResponse
func (c *ClientWithResponses) PostApiVersichertenteilnahmenErklaerungenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiVersichertenteilnahmenErklaerungenResponse, error) {
	rsp, err := c.PostApiVersichertenteilnahmenErklaerungenWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiVersichertenteilnahmenErklaerungenResponse(rsp)
}

func (c *ClientWithResponses) PostApiVersichertenteilnahmenErklaerungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiVersichertenteilnahmenErklaerungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiVersichertenteilnahmenErklaerungenResponse, error) {
	rsp, err := c.PostApiVersichertenteilnahmenErklaerungenWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiVersichertenteilnahmenErklaerungenResponse(rsp)
}

func (c *ClientWithResponses) PostApiVersichertenteilnahmenErklaerungenWithResponse(ctx context.Context, body PostApiVersichertenteilnahmenErklaerungenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiVersichertenteilnahmenErklaerungenResponse, error) {
	rsp, err := c.PostApiVersichertenteilnahmenErklaerungen(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiVersichertenteilnahmenErklaerungenResponse(rsp)
}

// PostApiVersichertenteilnahmenPruefungenWithBodyWithResponse request with arbitrary body returning *PostApiVersichertenteilnahmenPruefungenResponse
func (c *ClientWithResponses) PostApiVersichertenteilnahmenPruefungenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiVersichertenteilnahmenPruefungenResponse, error) {
	rsp, err := c.PostApiVersichertenteilnahmenPruefungenWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiVersichertenteilnahmenPruefungenResponse(rsp)
}

func (c *ClientWithResponses) PostApiVersichertenteilnahmenPruefungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiVersichertenteilnahmenPruefungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiVersichertenteilnahmenPruefungenResponse, error) {
	rsp, err := c.PostApiVersichertenteilnahmenPruefungenWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiVersichertenteilnahmenPruefungenResponse(rsp)
}

func (c *ClientWithResponses) PostApiVersichertenteilnahmenPruefungenWithResponse(ctx context.Context, body PostApiVersichertenteilnahmenPruefungenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiVersichertenteilnahmenPruefungenResponse, error) {
	rsp, err := c.PostApiVersichertenteilnahmenPruefungen(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiVersichertenteilnahmenPruefungenResponse(rsp)
}

// GetApiVertragspartnerVertragspartneridentifikatorWithResponse request returning *GetApiVertragspartnerVertragspartneridentifikatorResponse
func (c *ClientWithResponses) GetApiVertragspartnerVertragspartneridentifikatorWithResponse(ctx context.Context, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorParams, reqEditors ...RequestEditorFn) (*GetApiVertragspartnerVertragspartneridentifikatorResponse, error) {
	rsp, err := c.GetApiVertragspartnerVertragspartneridentifikator(ctx, vertragspartneridentifikator, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApiVertragspartnerVertragspartneridentifikatorResponse(rsp)
}

// GetApiVertragspartnerVertragspartneridentifikatorAerzteWithResponse request returning *GetApiVertragspartnerVertragspartneridentifikatorAerzteResponse
func (c *ClientWithResponses) GetApiVertragspartnerVertragspartneridentifikatorAerzteWithResponse(ctx context.Context, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorAerzteParams, reqEditors ...RequestEditorFn) (*GetApiVertragspartnerVertragspartneridentifikatorAerzteResponse, error) {
	rsp, err := c.GetApiVertragspartnerVertragspartneridentifikatorAerzte(ctx, vertragspartneridentifikator, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApiVertragspartnerVertragspartneridentifikatorAerzteResponse(rsp)
}

// GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenWithResponse request returning *GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenResponse
func (c *ClientWithResponses) GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenWithResponse(ctx context.Context, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenParams, reqEditors ...RequestEditorFn) (*GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenResponse, error) {
	rsp, err := c.GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaetten(ctx, vertragspartneridentifikator, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenResponse(rsp)
}

// GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenWithResponse request returning *GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenResponse
func (c *ClientWithResponses) GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenWithResponse(ctx context.Context, vertragspartneridentifikator string, params *GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenParams, reqEditors ...RequestEditorFn) (*GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenResponse, error) {
	rsp, err := c.GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmen(ctx, vertragspartneridentifikator, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenResponse(rsp)
}

// ParsePostApiAbrechnungenResponse parses an HTTP response from a PostApiAbrechnungenWithResponse call
func ParsePostApiAbrechnungenResponse(rsp *http.Response) (*PostApiAbrechnungenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiAbrechnungenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest AbrechnungsResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiAbrechnungenVoreinschreibeleistungenResponse parses an HTTP response from a PostApiAbrechnungenVoreinschreibeleistungenWithResponse call
func ParsePostApiAbrechnungenVoreinschreibeleistungenResponse(rsp *http.Response) (*PostApiAbrechnungenVoreinschreibeleistungenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiAbrechnungenVoreinschreibeleistungenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest TransferResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiAuswertungenResponse parses an HTTP response from a PostApiAuswertungenWithResponse call
func ParsePostApiAuswertungenResponse(rsp *http.Response) (*PostApiAuswertungenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiAuswertungenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest KrankheitsbildAuswertungResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiBereitgestellteteilnehmerverzeichnisseResponse parses an HTTP response from a PostApiBereitgestellteteilnehmerverzeichnisseWithResponse call
func ParsePostApiBereitgestellteteilnehmerverzeichnisseResponse(rsp *http.Response) (*PostApiBereitgestellteteilnehmerverzeichnisseResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiBereitgestellteteilnehmerverzeichnisseResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest LiefereBereitgestellteTeilnehmerverzeichnisseResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParseGetApiMigrationenVertragspartneridentifikatorFacharztResponse parses an HTTP response from a GetApiMigrationenVertragspartneridentifikatorFacharztWithResponse call
func ParseGetApiMigrationenVertragspartneridentifikatorFacharztResponse(rsp *http.Response) (*GetApiMigrationenVertragspartneridentifikatorFacharztResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApiMigrationenVertragspartneridentifikatorFacharztResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest VertragspartneridentifikatorResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParseGetApiMigrationenVertragspartneridentifikatorHausarztResponse parses an HTTP response from a GetApiMigrationenVertragspartneridentifikatorHausarztWithResponse call
func ParseGetApiMigrationenVertragspartneridentifikatorHausarztResponse(rsp *http.Response) (*GetApiMigrationenVertragspartneridentifikatorHausarztResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApiMigrationenVertragspartneridentifikatorHausarztResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest VertragspartneridentifikatorResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiPartnerdatenEdmpResponse parses an HTTP response from a PostApiPartnerdatenEdmpWithResponse call
func ParsePostApiPartnerdatenEdmpResponse(rsp *http.Response) (*PostApiPartnerdatenEdmpResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiPartnerdatenEdmpResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest TransferResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiPartnerdatenPracmanResponse parses an HTTP response from a PostApiPartnerdatenPracmanWithResponse call
func ParsePostApiPartnerdatenPracmanResponse(rsp *http.Response) (*PostApiPartnerdatenPracmanResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiPartnerdatenPracmanResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest TransferResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiTeilnehmerverzeichnisResponse parses an HTTP response from a PostApiTeilnehmerverzeichnisWithResponse call
func ParsePostApiTeilnehmerverzeichnisResponse(rsp *http.Response) (*PostApiTeilnehmerverzeichnisResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiTeilnehmerverzeichnisResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest LiefereTeilnehmerverzeichnisResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiVersichertenteilnahmenErklaerungenResponse parses an HTTP response from a PostApiVersichertenteilnahmenErklaerungenWithResponse call
func ParsePostApiVersichertenteilnahmenErklaerungenResponse(rsp *http.Response) (*PostApiVersichertenteilnahmenErklaerungenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiVersichertenteilnahmenErklaerungenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest TeilnahmeerklaerungResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiVersichertenteilnahmenPruefungenResponse parses an HTTP response from a PostApiVersichertenteilnahmenPruefungenWithResponse call
func ParsePostApiVersichertenteilnahmenPruefungenResponse(rsp *http.Response) (*PostApiVersichertenteilnahmenPruefungenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiVersichertenteilnahmenPruefungenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest PruefeVersichertenteilnahmeResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParseGetApiVertragspartnerVertragspartneridentifikatorResponse parses an HTTP response from a GetApiVertragspartnerVertragspartneridentifikatorWithResponse call
func ParseGetApiVertragspartnerVertragspartneridentifikatorResponse(rsp *http.Response) (*GetApiVertragspartnerVertragspartneridentifikatorResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApiVertragspartnerVertragspartneridentifikatorResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest VertragspartnerResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParseGetApiVertragspartnerVertragspartneridentifikatorAerzteResponse parses an HTTP response from a GetApiVertragspartnerVertragspartneridentifikatorAerzteWithResponse call
func ParseGetApiVertragspartnerVertragspartneridentifikatorAerzteResponse(rsp *http.Response) (*GetApiVertragspartnerVertragspartneridentifikatorAerzteResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApiVertragspartnerVertragspartneridentifikatorAerzteResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest VertragspartnerAerzteResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParseGetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenResponse parses an HTTP response from a GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenWithResponse call
func ParseGetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenResponse(rsp *http.Response) (*GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApiVertragspartnerVertragspartneridentifikatorBetriebsstaettenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest VertragspartnerBetriebsstaettenResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParseGetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenResponse parses an HTTP response from a GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenWithResponse call
func ParseGetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenResponse(rsp *http.Response) (*GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest VertragspartnerVertragsteilnahmenResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}
