package hpm_rest_hzv_client

import (
	"encoding/xml"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"emperror.dev/errors"
	"git.tutum.dev/medi/tutum/ares/pkg/hpm_service/hpm"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	
	"github.com/jinzhu/copier"
)

type PatientParticipationListDocument struct {
	XMLName   xml.Name `xml:"PatientParticipationListDocument"`
	Text      string   `xml:",chardata"`
	Xsd       string   `xml:"xsd,attr"`
	Xsi       string   `xml:"xsi,attr"`
	ClassCode string   `xml:"classCode,attr"`
	MoodCode  string   `xml:"moodCode,attr"`
	Xmlns     string   `xml:"xmlns,attr"`
	RealmCode struct {
		Text string `xml:",chardata"`
		Code string `xml:"code,attr"`
	} `xml:"realmCode"`
	TypeId struct {
		Text      string `xml:",chardata"`
		Root      string `xml:"root,attr"`
		Extension string `xml:"extension,attr"`
	} `xml:"typeId"`
	TemplateId struct {
		Text string `xml:",chardata"`
		Root string `xml:"root,attr"`
	} `xml:"templateId"`
	ID struct {
		Text      string `xml:",chardata"`
		Root      string `xml:"root,attr"`
		Extension string `xml:"extension,attr"`
	} `xml:"id"`
	Code struct {
		Text        string `xml:",chardata"`
		Code        string `xml:"code,attr"`
		CodeSystem  string `xml:"codeSystem,attr"`
		DisplayName string `xml:"displayName,attr"`
	} `xml:"code"`
	Title         string `xml:"title"`
	EffectiveTime struct {
		Text string `xml:",chardata"`
		Low  struct {
			Text  string `xml:",chardata"`
			Value string `xml:"value,attr"`
		} `xml:"low"`
		High struct {
			Text  string `xml:",chardata"`
			Value string `xml:"value,attr"`
		} `xml:"high"`
	} `xml:"effectiveTime"`
	ConfidentialityCode struct {
		Text       string `xml:",chardata"`
		Code       string `xml:"code,attr"`
		CodeSystem string `xml:"codeSystem,attr"`
	} `xml:"confidentialityCode"`
	LanguageCode struct {
		Text string `xml:",chardata"`
		Code string `xml:"code,attr"`
	} `xml:"languageCode"`
	SetId struct {
		Text      string `xml:",chardata"`
		Root      string `xml:"root,attr"`
		Extension string `xml:"extension,attr"`
	} `xml:"setId"`
	VersionNumber struct {
		Text  string `xml:",chardata"`
		Value string `xml:"value,attr"`
	} `xml:"versionNumber"`
	Author struct {
		Text               string `xml:",chardata"`
		TypeCode           string `xml:"typeCode,attr"`
		ContextControlCode string `xml:"contextControlCode,attr"`
		Time               struct {
			Text  string `xml:",chardata"`
			Value string `xml:"value,attr"`
		} `xml:"time"`
		AssignedAuthor struct {
			Text      string `xml:",chardata"`
			ClassCode string `xml:"classCode,attr"`
			ID        struct {
				Text      string `xml:",chardata"`
				Root      string `xml:"root,attr"`
				Extension string `xml:"extension,attr"`
			} `xml:"id"`
			AssignedAuthoringDevice struct {
				Text                  string `xml:",chardata"`
				DeterminerCode        string `xml:"determinerCode,attr"`
				ManufacturerModelName string `xml:"manufacturerModelName"`
				SoftwareName          string `xml:"softwareName"`
			} `xml:"assignedAuthoringDevice"`
			RepresentedOrganization struct {
				Text           string `xml:",chardata"`
				ClassCode      string `xml:"classCode,attr"`
				DeterminerCode string `xml:"determinerCode,attr"`
				ID             struct {
					Text      string `xml:",chardata"`
					Root      string `xml:"root,attr"`
					Extension string `xml:"extension,attr"`
				} `xml:"id"`
				Name string `xml:"name"`
			} `xml:"representedOrganization"`
		} `xml:"assignedAuthor"`
	} `xml:"author"`
	Custodian struct {
		Text              string `xml:",chardata"`
		TypeCode          string `xml:"typeCode,attr"`
		AssignedCustodian struct {
			Text                             string `xml:",chardata"`
			ClassCode                        string `xml:"classCode,attr"`
			RepresentedCustodianOrganization struct {
				Text           string `xml:",chardata"`
				ClassCode      string `xml:"classCode,attr"`
				DeterminerCode string `xml:"determinerCode,attr"`
				ID             struct {
					Text string `xml:",chardata"`
					Root string `xml:"root,attr"`
				} `xml:"id"`
				Name string `xml:"name"`
			} `xml:"representedCustodianOrganization"`
		} `xml:"assignedCustodian"`
	} `xml:"custodian"`
	InformationRecipient struct {
		Text              string `xml:",chardata"`
		TypeCode          string `xml:"typeCode,attr"`
		IntendedRecipient struct {
			Text string `xml:",chardata"`
			ID   struct {
				Text      string `xml:",chardata"`
				Root      string `xml:"root,attr"`
				Extension string `xml:"extension,attr"`
			} `xml:"id"`
			InformationRecipient struct {
				Text           string `xml:",chardata"`
				ClassCode      string `xml:"classCode,attr"`
				DeterminerCode string `xml:"determinerCode,attr"`
				Name           struct {
					Text   string `xml:",chardata"`
					Given  string `xml:"given"`
					Family string `xml:"family"`
					Prefix string `xml:"prefix"`
				} `xml:"name"`
			} `xml:"informationRecipient"`
		} `xml:"intendedRecipient"`
	} `xml:"informationRecipient"`
	Component struct {
		Text string `xml:",chardata"`
		Act  struct {
			Text       string `xml:",chardata"`
			ClassCode  string `xml:"classCode,attr"`
			MoodCode   string `xml:"moodCode,attr"`
			TemplateId struct {
				Text string `xml:",chardata"`
				Root string `xml:"root,attr"`
			} `xml:"templateId"`
			ID struct {
				Text      string `xml:",chardata"`
				Root      string `xml:"root,attr"`
				Extension string `xml:"extension,attr"`
			} `xml:"id"`
			Code struct {
				Text       string `xml:",chardata"`
				Code       string `xml:"code,attr"`
				CodeSystem string `xml:"codeSystem,attr"`
			} `xml:"code"`
			Title       string `xml:"title"`
			Participant []struct {
				Text               string `xml:",chardata"`
				TypeCode           string `xml:"typeCode,attr"`
				ContextControlCode string `xml:"contextControlCode,attr"`
				TemplateId         struct {
					Text string `xml:",chardata"`
					Root string `xml:"root,attr"`
				} `xml:"templateId"`
				Time struct {
					Text string `xml:",chardata"`
					Low  struct {
						Text  string `xml:",chardata"`
						Value string `xml:"value,attr"`
					} `xml:"low"`
					High struct {
						Text  string `xml:",chardata"`
						Value string `xml:"value,attr"`
					} `xml:"high"`
				} `xml:"time"`
				StatusCode struct {
					Text string `xml:",chardata"`
					Code string `xml:"code,attr"`
				} `xml:"statusCode"`
				ReasonCode struct {
					Text         string `xml:",chardata"`
					NullFlavor   string `xml:"nullFlavor,attr"`
					OriginalText string `xml:"originalText"`
				} `xml:"reasonCode"`
				AssociatedEntity struct {
					Text      string `xml:",chardata"`
					ClassCode string `xml:"classCode,attr"`
					ID        struct {
						Text       string `xml:",chardata"`
						Root       string `xml:"root,attr"`
						Extension  string `xml:"extension,attr"`
						NullFlavor string `xml:"nullFlavor,attr"`
					} `xml:"id"`
					AssociatedPatient struct {
						Text           string `xml:",chardata"`
						ClassCode      string `xml:"classCode,attr"`
						DeterminerCode string `xml:"determinerCode,attr"`
						Name           struct {
							Text   string `xml:",chardata"`
							Family struct {
								Text       string `xml:",chardata"`
								NullFlavor string `xml:"nullFlavor,attr"`
							} `xml:"family"`
							Given struct {
								Text       string `xml:",chardata"`
								NullFlavor string `xml:"nullFlavor,attr"`
							} `xml:"given"`
						} `xml:"name"`
						AdministrativeGenderCode struct {
							Text       string `xml:",chardata"`
							Code       string `xml:"code,attr"`
							CodeSystem string `xml:"codeSystem,attr"`
						} `xml:"administrativeGenderCode"`
						BirthTime struct {
							Text       string `xml:",chardata"`
							Value      string `xml:"value,attr"`
							NullFlavor string `xml:"nullFlavor,attr"`
						} `xml:"birthTime"`
					} `xml:"associatedPatient"`
					ScopingOrganization struct {
						Text           string `xml:",chardata"`
						ClassCode      string `xml:"classCode,attr"`
						DeterminerCode string `xml:"determinerCode,attr"`
						ID             struct {
							Text      string `xml:",chardata"`
							Root      string `xml:"root,attr"`
							Extension string `xml:"extension,attr"`
						} `xml:"id"`
						Name struct {
							Text       string `xml:",chardata"`
							NullFlavor string `xml:"nullFlavor,attr"`
						} `xml:"name"`
					} `xml:"scopingOrganization"`
				} `xml:"associatedEntity"`
			} `xml:"participant"`
		} `xml:"act"`
	} `xml:"component"`
}

func ToDiagnoseCodeSystemName(year int32) DiagnoseCodeSystemName {
	switch year {
	case 2020:
		return Icd10gm2022
	case 2021:
		return Icd10gm2021
	case 2022:
		return Icd10gm2022
	case 2023:
		return Icd10gm2023
	case 2024:
		return Icd10gm2024
	case 2025:
		return Icd10gm2025
	default:
		return None
	}
}

func (a *AbrechnungsContainer) GetContractId() string {
	return a.Vertragskontext.VertragsIdentifikator
}

func (a *AbrechnungsContainer) GetDiagnosesServiceIds() ([]uuid.UUID, []uuid.UUID) {
	diagnosisId := []uuid.UUID{}
	serviceId := []uuid.UUID{}
	for _, document := range a.Dokumentationen {
		if document.Diagnosen != nil {
			for _, d := range *document.Diagnosen {
				diagnosisId = append(diagnosisId, uuid.MustParse(d.DiagnoseId))
			}
		}
		if document.Leistungen != nil {
			for _, l := range *document.Leistungen {
				serviceId = append(serviceId, uuid.MustParse(l.LeistungsId))
			}
		}
	}
	return diagnosisId, serviceId
}

func (a *AbrechnungsContainer) GetYearQuarter() *util.YearQuarter {
	return &util.YearQuarter{
		Year:    a.Vertragskontext.AbrechnungsJahr,
		Quarter: a.Vertragskontext.AbrechnungsQuartal,
	}
}

type hpmBaseResponse struct {
	Status               *ResultatStatus
	Meldungen            *[]Meldung
	UebermittlungsStatus *UebermittlungsStatus
}

func (h hpmBaseResponse) HasResponse() bool {
	return h.Status != nil && *h.Status == ResultatStatusOK
}

func GetHpmError[T any](statusCode int, a T) error {
	if statusCode < 200 || statusCode >= 300 {
		return fmt.Errorf("request hpm fail: %d, %s", statusCode, http.StatusText(statusCode))
	}

	var hpmBaseResponse hpmBaseResponse
	if err := copier.Copy(&hpmBaseResponse, a); err != nil {
		return err
	}

	if hpmBaseResponse.HasResponse() {
		return nil
	}
	status := *hpmBaseResponse.Status
	meldungen := hpmBaseResponse.Meldungen

	if status != ResultatStatusOK {
		serverError := string(status)
		if meldungen == nil {
			return &hpm.HpmError{ServerError: serverError, Messages: nil}
		}
		errorMessages := slice.Map(*meldungen, func(message Meldung) hpm.HpmErrorMessage {
			return hpm.HpmErrorMessage{
				Message: util.GetPointerValue(message.Nachricht),
				Code:    util.GetPointerValue(message.Code),
			}
		})
		return &hpm.HpmError{ServerError: serverError, Messages: errorMessages}
	}

	return nil
}

type ParticipateResultStatus string

const (
	ParticipateResultStatus_Ok                        ParticipateResultStatus = "OK"
	ParticipateResultStatus_NoInformation             ParticipateResultStatus = "NO_INFORMATION"
	ParticipateResultStatus_Failed                    ParticipateResultStatus = "FAILED"
	ParticipateResultStatus_TechnicalViolation        ParticipateResultStatus = "TECHNICAL_VIOLATION"
	ParticipateResultStatus_NoAuthorizationWebservice ParticipateResultStatus = "NO_AUTHORIZATION_WEB_SERVICE"
	ParticipateResultStatus_Maintenance               ParticipateResultStatus = "MAINTENANCE"
	ParticipateResultStatus_InternalError             ParticipateResultStatus = "INTERNAL_ERROR"
	ParticipateResultStatus_InValidMasterData         ParticipateResultStatus = "INVALID_MASTER_DATA"
	ParticipateResultStatus_Unknown                   ParticipateResultStatus = "UNKNOWN"
	ParticipateResultStatus_PartiallyProcessed        ParticipateResultStatus = "PARTIALLY_PROCESSED"
	DATE_TIME_FORMAT                                                          = "yyyyMMddHHmmss"
	DATE_FORMAT                                                               = "yyyyMMdd"
	OID_INSURANCE_NUMBER                                                      = "1.2.276.********"
	OID_OLD_STYLE_INSURANCE_NUMBER                                            = "1.2.276.********.*********.9999.9999.999955"
	OID_HEALTH_INSURANCE_IK                                                   = "1.2.276.********"
)

type PTVPatientParticipation struct {
	IkNumber          string
	InsuranceNumber   string
	Status            ContractStatus
	Names             PTVPatientName
	Reason            string
	Gender            Gender
	Dob               int64
	ContractBeginDate int64
	ContractEndDate   *int64 // endate is nullable
}

type PTVPatientName struct {
	FirstName string
	LastName  string
}

type PTVContractFolder struct {
	DocumentId string
	Year       int32
	Quarter    int32
	Version    int32
	ContractId string
}

type ContractStatus string

const (
	ContractStatus_Active     ContractStatus = "active"
	ContractStatus_Cancelled  ContractStatus = "cancelled"
	ContractStatus_Requested  ContractStatus = "requested"
	ContractStatus_Terminated ContractStatus = "terminated"
	ContractStatus_Rejected   ContractStatus = "rejected"
	ContractStatus_None       ContractStatus = "none"
)

type Gender string

const (
	Male         Gender = "M"
	Female       Gender = "F"
	Undetermined Gender = "UN"
)

func ParseDateTime(timeValue string, location *time.Location) (*time.Time, error) {
	if len(timeValue) != len(DATE_TIME_FORMAT) {
		return nil, fmt.Errorf("invalid format")
	}
	year, err := strconv.Atoi(timeValue[0:4])
	if err != nil {
		return nil, err
	}
	month, err := strconv.Atoi(timeValue[4:6])
	if err != nil {
		return nil, err
	}
	day, err := strconv.Atoi(timeValue[6:8])
	if err != nil {
		return nil, err
	}
	hour, err := strconv.Atoi(timeValue[8:10])
	if err != nil {
		return nil, err
	}
	minute, err := strconv.Atoi(timeValue[10:12])
	if err != nil {
		return nil, err
	}
	timeData := time.Date(year, time.Month(month), day, hour, minute, 0, 0, location)
	return &timeData, nil
}

func parseDate(timeValue string, location *time.Location) (*time.Time, error) {
	if len(timeValue) != len(DATE_FORMAT) {
		return nil, fmt.Errorf("invalid format")
	}
	year, err := strconv.Atoi(timeValue[0:4])
	if err != nil {
		return nil, err
	}
	month, err := strconv.Atoi(timeValue[4:6])
	if err != nil {
		return nil, err
	}
	day, err := strconv.Atoi(timeValue[6:8])
	if err != nil {
		return nil, err
	}
	timeData := time.Date(year, time.Month(month), day, 0, 0, 0, 0, location)
	return &timeData, nil
}

func (l *PatientParticipationListDocument) ToPTVPatientParticipation(location *time.Location) ([]PTVPatientParticipation, error) {
	result := []PTVPatientParticipation{}
	component := l.Component
	for _, participant := range component.Act.Participant {
		temp := PTVPatientParticipation{}
		if len(participant.StatusCode.Code) > 0 {
			switch participant.StatusCode.Code {
			case "active":
				temp.Status = ContractStatus_Active
			case "cancelled":
				temp.Status = ContractStatus_Cancelled
			case "requested":
				temp.Status = ContractStatus_Requested
			case "terminated":
				temp.Status = ContractStatus_Terminated
			case "rejected":
				temp.Status = ContractStatus_Rejected
			default:
				temp.Status = ContractStatus_None
			}
		}

		if len(participant.Time.High.Value) > 0 {
			endDateContract, err := ParseDateTime(participant.Time.High.Value, location)
			if err != nil {
				return result, errors.WithStack(err)
			}
			endDateContractMillis := util.ConvertTimeToMiliSecond(*endDateContract)
			temp.ContractEndDate = &endDateContractMillis
		}
		if len(participant.Time.Low.Value) > 0 {
			beginDateContract, err := ParseDateTime(participant.Time.Low.Value, location)
			if err != nil {
				return result, errors.WithStack(err)
			}
			temp.ContractBeginDate = util.ConvertTimeToMiliSecond(*beginDateContract)
		}

		if participant.AssociatedEntity.ID.Root == OID_INSURANCE_NUMBER ||
			participant.AssociatedEntity.ID.Root == OID_OLD_STYLE_INSURANCE_NUMBER {
			temp.InsuranceNumber = participant.AssociatedEntity.ID.Extension
		}

		if len(participant.AssociatedEntity.AssociatedPatient.Name.Given.Text) > 0 && len(participant.AssociatedEntity.AssociatedPatient.Name.Family.Text) > 0 {
			firstName := participant.AssociatedEntity.AssociatedPatient.Name.Given.Text
			LastName := participant.AssociatedEntity.AssociatedPatient.Name.Family.Text
			temp.Names = PTVPatientName{FirstName: firstName, LastName: LastName}
		}

		administrativeGenderCode := participant.AssociatedEntity.AssociatedPatient.AdministrativeGenderCode
		if len(administrativeGenderCode.Code) > 0 {
			switch administrativeGenderCode.Code {
			case "M":
				temp.Gender = Male
			case "F":
				temp.Gender = Female
			case "UN":
				temp.Gender = Undetermined
			default:
				temp.Gender = Undetermined
			}
		}

		birthTime := participant.AssociatedEntity.AssociatedPatient.BirthTime.Value
		if len(birthTime) > 0 {
			birthDate, err := parseDate(birthTime, location)
			if err != nil {
				return result, errors.WithStack(err)
			}
			temp.Dob = util.ConvertTimeToMiliSecond(*birthDate)
		}

		if participant.AssociatedEntity.ScopingOrganization.ID.Root == OID_HEALTH_INSURANCE_IK {
			temp.IkNumber = participant.AssociatedEntity.ScopingOrganization.ID.Extension
		}
		temp.Reason = participant.ReasonCode.OriginalText

		result = append(result, temp)
	}

	return result, nil
}
