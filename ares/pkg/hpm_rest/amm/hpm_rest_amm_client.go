// Package hpm_rest_amm_client provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package hpm_rest_amm_client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// Defines values for Meldungsart.
const (
	MeldungsartFehler      Meldungsart = "Fehler"
	MeldungsartInformation Meldungsart = "Information"
	MeldungsartUnbekannt   Meldungsart = "Unbekannt"
	MeldungsartWarnung     Meldungsart = "Warnung"
)

// Defines values for Meldungskategorie.
const (
	MeldungskategorieKeineAngabe   Meldungskategorie = "KeineAngabe"
	MeldungskategorieLaufzeit      Meldungskategorie = "Laufzeit"
	MeldungskategorieRechenzentrum Meldungskategorie = "Rechenzentrum"
	MeldungskategorieValidierung   Meldungskategorie = "Validierung"
)

// Defines values for PatientGeschlecht.
const (
	D PatientGeschlecht = "D"
	M PatientGeschlecht = "M"
	U PatientGeschlecht = "U"
	W PatientGeschlecht = "W"
	X PatientGeschlecht = "X"
)

// Defines values for ReferenzTyp.
const (
	ReferenzTypArzneimittel                ReferenzTyp = "Arzneimittel"
	ReferenzTypDiagnose                    ReferenzTyp = "Diagnose"
	ReferenzTypErkrankungsstufe            ReferenzTyp = "Erkrankungsstufe"
	ReferenzTypHinderungsfaktor            ReferenzTyp = "Hinderungsfaktor"
	ReferenzTypIstFremdeingeschrieben      ReferenzTyp = "IstFremdeingeschrieben"
	ReferenzTypIstNichtFremdeingeschrieben ReferenzTyp = "IstNichtFremdeingeschrieben"
	ReferenzTypIstwert                     ReferenzTyp = "Istwert"
	ReferenzTypKeineAngabe                 ReferenzTyp = "KeineAngabe"
	ReferenzTypLeistung                    ReferenzTyp = "Leistung"
	ReferenzTypOperation                   ReferenzTyp = "Operation"
	ReferenzTypPatient                     ReferenzTyp = "Patient"
	ReferenzTypPraxisgebuehr               ReferenzTyp = "Praxisgebuehr"
	ReferenzTypRezept                      ReferenzTyp = "Rezept"
	ReferenzTypUeberweisung                ReferenzTyp = "Ueberweisung"
	ReferenzTypVertragsidentifikator       ReferenzTyp = "Vertragsidentifikator"
	ReferenzTypZielwert                    ReferenzTyp = "Zielwert"
)

// Defines values for ResultatStatus.
const (
	ResultatStatusFehlgeschlagen       ResultatStatus = "Fehlgeschlagen"
	ResultatStatusOK                   ResultatStatus = "OK"
	ResultatStatusTeilweiseVerarbeitet ResultatStatus = "TeilweiseVerarbeitet"
	ResultatStatusUnbekannt            ResultatStatus = "Unbekannt"
)

// Defines values for UebermittlungsStatus.
const (
	FachlicheVerletzung         UebermittlungsStatus = "FachlicheVerletzung"
	InternerFehler              UebermittlungsStatus = "InternerFehler"
	KeineAngabe                 UebermittlungsStatus = "KeineAngabe"
	KeineBerechtigungWebService UebermittlungsStatus = "KeineBerechtigungWebService"
	OK                          UebermittlungsStatus = "OK"
	UngueltigeStammdaten        UebermittlungsStatus = "UngueltigeStammdaten"
	Wartung                     UebermittlungsStatus = "Wartung"
)

// Arzneimittel defines model for Arzneimittel.
type Arzneimittel struct {
	Anzahl         *int32 `json:"anzahl,omitempty"`
	ArzneimittelId string `json:"arzneimittelId"`
	AutIdem        bool   `json:"autIdem"`
}

// ArzneimittelInformationenContainer Der Container mit den PZNs für die detailierte Informationen abgerufen werden sollen.
type ArzneimittelInformationenContainer struct {
	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation `json:"arztInformationsSystem"`
	Ik                     string              `json:"ik"`
	PzNs                   []string            `json:"pzNs"`
	ReferenzDatum          time.Time           `json:"referenzDatum"`
	VertragsIdentifikator  string              `json:"vertragsIdentifikator"`
}

// ArzneimittelListe Die angeforderte ArzneimittelListe.
type ArzneimittelListe struct {
	Eintraege *[]ArzneimittelListeEintrag `json:"eintraege"`
}

// ArzneimittelListeContainer defines model for ArzneimittelListeContainer.
type ArzneimittelListeContainer struct {
	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation `json:"arztInformationsSystem"`
	Ik                     string              `json:"ik"`
	ReferenzDatum          time.Time           `json:"referenzDatum"`
	VertragsIdentifikator  string              `json:"vertragsIdentifikator"`
}

// ArzneimittelListeEintrag Ein Eintrag der angeforderten ArzneimittelListe.
type ArzneimittelListeEintrag struct {
	ArzneimittelKategorie *string    `json:"arzneimittelKategorie"`
	GueltigBis            *time.Time `json:"gueltigBis,omitempty"`
	GueltigVon            *time.Time `json:"gueltigVon,omitempty"`
	IstInPriscusListe     *bool      `json:"istInPriscusListe,omitempty"`
	Pzn                   *string    `json:"pzn"`
}

// BeschreibungsText Die textuellen Beschreibungen zu möglichen Substituten.
type BeschreibungsText struct {
	Index  *int32  `json:"index,omitempty"`
	Inhalt *string `json:"inhalt"`
	Quelle *string `json:"quelle"`
}

// IndikatorWirkstoffListeContainer Der Vertragsidentifikator, zu dem eine Indikatorwirkstoffliste generiert werden soll.
type IndikatorWirkstoffListeContainer struct {
	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation `json:"arztInformationsSystem"`
	VertragsIdentifikator  string              `json:"vertragsIdentifikator"`
}

// IndikatorWirkstoffListeEintrag Ein Eintrag der Indikatorwirkstoffliste, bestehend aus ATCcode und zugehörigem Diagnosecode.
type IndikatorWirkstoffListeEintrag struct {
	AtcCode      *string `json:"atcCode"`
	DiagnoseCode *string `json:"diagnoseCode"`
}

// LiefereArzneimittelInformationenResultat Das Ergebnis eines Arzneimittelinformationenabrufs.
type LiefereArzneimittelInformationenResultat struct {
	Meldungen            *[]Meldung            `json:"meldungen"`
	Packungen            *[]Packung            `json:"packungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// LiefereArzneimittelListeGZipResultat Die erstellte, GZip-komprimierte, ArzneimittelListe.
type LiefereArzneimittelListeGZipResultat struct {
	GZip                 *[]byte               `json:"gZip"`
	Meldungen            *[]Meldung            `json:"meldungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// LiefereArzneimittelListeResultat Die erstellte ArzneimittelListe.
type LiefereArzneimittelListeResultat struct {
	// ArzneimittelListe Die angeforderte ArzneimittelListe.
	ArzneimittelListe    *ArzneimittelListe    `json:"arzneimittelListe,omitempty"`
	Meldungen            *[]Meldung            `json:"meldungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// LiefereIndikatorWirkstoffListeResultat Das Ergebnis einer Abfrage nach Indikatorwirkstoffen.
type LiefereIndikatorWirkstoffListeResultat struct {
	IndikatorWirkstoffListe *[]IndikatorWirkstoffListeEintrag `json:"indikatorWirkstoffListe"`
	Meldungen               *[]Meldung                        `json:"meldungen"`
	Status                  *ResultatStatus                   `json:"status,omitempty"`
	UebermittlungsStatus    *UebermittlungsStatus             `json:"uebermittlungsStatus,omitempty"`
}

// LieferePznAtcFuerHochverordnungListeResultat Das Ergebnis einer Anfrage nach Hochverordnungen.
type LieferePznAtcFuerHochverordnungListeResultat struct {
	Meldungen                     *[]Meldung                              `json:"meldungen"`
	PznAtcFuerHochverordnungListe *[]PznAtcFuerHochverordnungListeEintrag `json:"pznAtcFuerHochverordnungListe"`
	Status                        *ResultatStatus                         `json:"status,omitempty"`
	UebermittlungsStatus          *UebermittlungsStatus                   `json:"uebermittlungsStatus,omitempty"`
}

// Meldung Die Meldung, die an den Aufrufer gesendet wird.
type Meldung struct {
	Art                  *Meldungsart          `json:"art,omitempty"`
	Code                 *string               `json:"code"`
	Kategorie            *Meldungskategorie    `json:"kategorie,omitempty"`
	Nachricht            *string               `json:"nachricht"`
	Referenzen           *[]Referenz           `json:"referenzen"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// MeldungsText Eine Meldungstext samt Metainformationen.
type MeldungsText struct {
	Inhalt *string `json:"inhalt"`
	Logik  *int32  `json:"logik,omitempty"`
	Typ    *int32  `json:"typ,omitempty"`
}

// Meldungsart defines model for Meldungsart.
type Meldungsart string

// Meldungskategorie defines model for Meldungskategorie.
type Meldungskategorie string

// Packung Die Rabattinformationen zu einer PZN.
type Packung struct {
	ArzneimittelKategorie *string         `json:"arzneimittelKategorie"`
	AusserVertrieb        *bool           `json:"ausserVertrieb,omitempty"`
	Festbetrag            *string         `json:"festbetrag"`
	HerstellerName        *string         `json:"herstellerName"`
	IstBTM                *bool           `json:"istBTM,omitempty"`
	IstInPriscusListe     *bool           `json:"istInPriscusListe,omitempty"`
	IstLifestyle          *bool           `json:"istLifestyle,omitempty"`
	IstNegativliste       *bool           `json:"istNegativliste,omitempty"`
	IstOTC                *bool           `json:"istOTC,omitempty"`
	Meldungen             *[]MeldungsText `json:"meldungen"`
	Name                  *string         `json:"name"`
	PackungsGroesse       *string         `json:"packungsGroesse"`
	Preis                 *string         `json:"preis"`
	Pzn                   *string         `json:"pzn"`
	Sortierung            *int32          `json:"sortierung,omitempty"`
	Wirkstaerke           *string         `json:"wirkstaerke"`
	Zuzahlung             *string         `json:"zuzahlung"`
}

// Patient Alle Informationen zum Patienten.
type Patient struct {
	AktuelleVertragsteilnahmen *[]Vertragsteilnahme `json:"aktuelleVertragsteilnahmen"`
	Geburtsdatum               string               `json:"geburtsdatum"`
	Geschlecht                 PatientGeschlecht    `json:"geschlecht"`
	Nachname                   string               `json:"nachname"`
	PatientenId                string               `json:"patientenId"`

	// Versicherungsnachweis Informationen über den Versicherungsnachweis des Patienten (i.d.R. Daten der Versichertenkarte).
	Versicherungsnachweis Versicherungsnachweis `json:"versicherungsnachweis"`
	Vorname               string                `json:"vorname"`
}

// PatientGeschlecht defines model for PatientGeschlecht.
type PatientGeschlecht string

// PznAtcFuerHochverordnungListeContainer defines model for PznAtcFuerHochverordnungListeContainer.
type PznAtcFuerHochverordnungListeContainer struct {
	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation `json:"arztInformationsSystem"`
	VertragsIdentifikator  string              `json:"vertragsIdentifikator"`
}

// PznAtcFuerHochverordnungListeEintrag Ein Element der Hochverordnungsliste.
type PznAtcFuerHochverordnungListeEintrag struct {
	AtcCode *string `json:"atcCode"`
	Pzn     *string `json:"pzn"`
}

// Referenz Die Referenzen zu den vom Aufrufer übermittelten Objekten.
type Referenz struct {
	Id          *string      `json:"id"`
	ReferenzTyp *ReferenzTyp `json:"referenzTyp,omitempty"`
}

// ReferenzTyp defines model for ReferenzTyp.
type ReferenzTyp string

// ResultatStatus defines model for ResultatStatus.
type ResultatStatus string

// Rezept Ein Rezept, bestehend aus Angaben über einen Arbeitsunfall, die Arzneimittel und weiterer Angaben.
type Rezept struct {
	ArbeitsunfallDatum  *time.Time       `json:"arbeitsunfallDatum"`
	ArbeitsunfallOrt    *string          `json:"arbeitsunfallOrt"`
	Arzneimittel        *[]Arzneimittel  `json:"arzneimittel"`
	Bvg                 *bool            `json:"bvg,omitempty"`
	Gebuehrenpflichtig  *bool            `json:"gebuehrenpflichtig,omitempty"`
	KvDetails           *RezeptKvDetails `json:"kvDetails,omitempty"`
	Notfall             *bool            `json:"notfall,omitempty"`
	RezeptId            string           `json:"rezeptId"`
	SonstigerKt         *bool            `json:"sonstigerKt,omitempty"`
	Sprechstundenbedarf *bool            `json:"sprechstundenbedarf,omitempty"`

	// Stellvertreter Der Stellvertreter, der in Vertretung für den abrechnenden Vertragsarzt gehandelt hat.
	Stellvertreter *Stellvertreter `json:"stellvertreter,omitempty"`
	Unfall         *bool           `json:"unfall,omitempty"`

	// VerordnungsZeitpunkte Die Zeitpunkte für Erstellung, Änderung und Löschung des Rezeptes.
	VerordnungsZeitpunkte VerordnungsZeitpunkte `json:"verordnungsZeitpunkte"`
}

// RezeptKvDetails defines model for RezeptKvDetails.
type RezeptKvDetails struct {
	Begruendungspflicht *bool `json:"begruendungspflicht,omitempty"`
	Hilfsmittel         *bool `json:"hilfsmittel,omitempty"`
	Impfstoff           *bool `json:"impfstoff,omitempty"`
}

// SoftwareInformation Informationen über das Arztinformationssystem und dessen Hersteller.
type SoftwareInformation struct {
	Name         *string `json:"name"`
	Organisation *string `json:"organisation"`
	SystemOid    string  `json:"systemOid"`
	Version      string  `json:"version"`
}

// Stellvertreter Der Stellvertreter, der in Vertretung für den abrechnenden Vertragsarzt gehandelt hat.
type Stellvertreter struct {
	Lanr string `json:"lanr"`
}

// Substitution Die einzelnen Ergebnisse der Substitutionen.
type Substitution struct {
	AtcCode        *string              `json:"atcCode"`
	AtcName        *string              `json:"atcName"`
	Beschreibungen *[]BeschreibungsText `json:"beschreibungen"`
	Packungen      *[]Packung           `json:"packungen"`
	Prioritaet     *int32               `json:"prioritaet,omitempty"`
}

// SubstitutionContainer Der Container mit der PZN für die Substitute abgerufen werden sollen.
type SubstitutionContainer struct {
	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation `json:"arztInformationsSystem"`
	Ik                     string              `json:"ik"`
	Pzn                    string              `json:"pzn"`
	ReferenzDatum          time.Time           `json:"referenzDatum"`
	VertragsIdentifikator  string              `json:"vertragsIdentifikator"`
}

// SubstitutionResultat Das Ergebnis einer Substitutionsanforderung.
type SubstitutionResultat struct {
	Meldungen            *[]Meldung            `json:"meldungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	Substitutionen       *[]Substitution       `json:"substitutionen"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// UebermittlungsStatus defines model for UebermittlungsStatus.
type UebermittlungsStatus string

// Verordnungen Die Verordnungen zum zugehörigen Patienten.
type Verordnungen struct {
	Rezepte []Rezept `json:"rezepte"`
}

// VerordnungsContainer defines model for VerordnungsContainer.
type VerordnungsContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation        `json:"arztInformationsSystem"`
	Dokumentationen        []VerordnungsDokumentation `json:"dokumentationen"`
	NurPrueflauf           bool                       `json:"nurPrueflauf"`
	Testuebermittlung      bool                       `json:"testuebermittlung"`

	// Vertragskontext Angaben zum Vertrag.
	Vertragskontext              Vertragskontext `json:"vertragskontext"`
	Vertragspartneridentifikator string          `json:"vertragspartneridentifikator"`
}

// VerordnungsDokumentation Eine Dokumentation, in der zu einem Patienten jeweils seine Verordnungen gelistet sind.
type VerordnungsDokumentation struct {
	// Patient Alle Informationen zum Patienten.
	Patient *Patient `json:"patient,omitempty"`

	// Verordnungen Die Verordnungen zum zugehörigen Patienten.
	Verordnungen *Verordnungen `json:"verordnungen,omitempty"`
}

// VerordnungsZeitpunkte Die Zeitpunkte für Erstellung, Änderung und Löschung des Rezeptes.
type VerordnungsZeitpunkte struct {
	Erstellt  *time.Time `json:"erstellt,omitempty"`
	Geaendert *time.Time `json:"geaendert,omitempty"`
	Gedruckt  *time.Time `json:"gedruckt"`
	Geloescht *time.Time `json:"geloescht"`
}

// VerordnungsdatenUebermittlungResultat Das Ergebnis eines Verordnungsdatenversands.
type VerordnungsdatenUebermittlungResultat struct {
	Meldungen               *[]Meldung            `json:"meldungen"`
	Status                  *ResultatStatus       `json:"status,omitempty"`
	TransferId              *string               `json:"transferId"`
	UebermittlungsStatus    *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
	Uebermittlungsprotokoll *[]byte               `json:"uebermittlungsprotokoll"`
}

// Versicherungsnachweis Informationen über den Versicherungsnachweis des Patienten (i.d.R. Daten der Versichertenkarte).
type Versicherungsnachweis struct {
	BesonderePersonengruppe *string `json:"besonderePersonengruppe"`
	DmpKennzeichnung        *string `json:"dmpKennzeichnung"`
	KrankenkassenIk         string  `json:"krankenkassenIk"`
	VersichertenArt         string  `json:"versichertenArt"`
	VersichertenNummer      string  `json:"versichertenNummer"`
}

// Vertragskontext Angaben zum Vertrag.
type Vertragskontext struct {
	AbrechnungsJahr            int32  `json:"abrechnungsJahr"`
	AbrechnungsQuartal         int32  `json:"abrechnungsQuartal"`
	HonoraranlageIdentifikator string `json:"honoraranlageIdentifikator"`
	VertragsIdentifikator      string `json:"vertragsIdentifikator"`
}

// Vertragsteilnahme Alle Informationen zu den Vertragsteilnahmen des abgerechneten Patienten.
type Vertragsteilnahme struct {
	IstVertreterteilnahme bool   `json:"istVertreterteilnahme"`
	VertragsIdentifikator string `json:"vertragsIdentifikator"`
}

// PostApiArzneimittelInformationenApplicationWildcardPlusJSONRequestBody defines body for PostApiArzneimittelInformationen for application/*+json ContentType.
type PostApiArzneimittelInformationenApplicationWildcardPlusJSONRequestBody = ArzneimittelInformationenContainer

// PostApiArzneimittelInformationenJSONRequestBody defines body for PostApiArzneimittelInformationen for application/json ContentType.
type PostApiArzneimittelInformationenJSONRequestBody = ArzneimittelInformationenContainer

// PostApiArzneimittelListeApplicationWildcardPlusJSONRequestBody defines body for PostApiArzneimittelListe for application/*+json ContentType.
type PostApiArzneimittelListeApplicationWildcardPlusJSONRequestBody = ArzneimittelListeContainer

// PostApiArzneimittelListeJSONRequestBody defines body for PostApiArzneimittelListe for application/json ContentType.
type PostApiArzneimittelListeJSONRequestBody = ArzneimittelListeContainer

// PostApiArzneimittelListeGzipApplicationWildcardPlusJSONRequestBody defines body for PostApiArzneimittelListeGzip for application/*+json ContentType.
type PostApiArzneimittelListeGzipApplicationWildcardPlusJSONRequestBody = ArzneimittelListeContainer

// PostApiArzneimittelListeGzipJSONRequestBody defines body for PostApiArzneimittelListeGzip for application/json ContentType.
type PostApiArzneimittelListeGzipJSONRequestBody = ArzneimittelListeContainer

// PostApiArzneimittelSubstitutionenApplicationWildcardPlusJSONRequestBody defines body for PostApiArzneimittelSubstitutionen for application/*+json ContentType.
type PostApiArzneimittelSubstitutionenApplicationWildcardPlusJSONRequestBody = SubstitutionContainer

// PostApiArzneimittelSubstitutionenJSONRequestBody defines body for PostApiArzneimittelSubstitutionen for application/json ContentType.
type PostApiArzneimittelSubstitutionenJSONRequestBody = SubstitutionContainer

// PostApiHochverordnungenApplicationWildcardPlusJSONRequestBody defines body for PostApiHochverordnungen for application/*+json ContentType.
type PostApiHochverordnungenApplicationWildcardPlusJSONRequestBody = PznAtcFuerHochverordnungListeContainer

// PostApiHochverordnungenJSONRequestBody defines body for PostApiHochverordnungen for application/json ContentType.
type PostApiHochverordnungenJSONRequestBody = PznAtcFuerHochverordnungListeContainer

// PostApiIndikatorwirkstoffeApplicationWildcardPlusJSONRequestBody defines body for PostApiIndikatorwirkstoffe for application/*+json ContentType.
type PostApiIndikatorwirkstoffeApplicationWildcardPlusJSONRequestBody = IndikatorWirkstoffListeContainer

// PostApiIndikatorwirkstoffeJSONRequestBody defines body for PostApiIndikatorwirkstoffe for application/json ContentType.
type PostApiIndikatorwirkstoffeJSONRequestBody = IndikatorWirkstoffListeContainer

// PostApiVerordnungenApplicationWildcardPlusJSONRequestBody defines body for PostApiVerordnungen for application/*+json ContentType.
type PostApiVerordnungenApplicationWildcardPlusJSONRequestBody = VerordnungsContainer

// PostApiVerordnungenJSONRequestBody defines body for PostApiVerordnungen for application/json ContentType.
type PostApiVerordnungenJSONRequestBody = VerordnungsContainer

// RequestEditorFn  is the function signature for the RequestEditor callback function
type RequestEditorFn func(ctx context.Context, req *http.Request) error

// Doer performs HTTP requests.
//
// The standard http.Client implements this interface.
type HttpRequestDoer interface {
	Do(req *http.Request) (*http.Response, error)
}

// Client which conforms to the OpenAPI3 specification for this service.
type Client struct {
	// The endpoint of the server conforming to this interface, with scheme,
	// https://api.deepmap.com for example. This can contain a path relative
	// to the server, such as https://api.deepmap.com/dev-test, and all the
	// paths in the swagger spec will be appended to the server.
	Server string

	// Doer for performing requests, typically a *http.Client with any
	// customized settings, such as certificate chains.
	Client HttpRequestDoer

	// A list of callbacks for modifying requests which are generated before sending over
	// the network.
	RequestEditors []RequestEditorFn
}

// ClientOption allows setting custom parameters during construction
type ClientOption func(*Client) error

// Creates a new Client, with reasonable defaults
func NewClient(server string, opts ...ClientOption) (*Client, error) {
	// create a client with sane default values
	client := Client{
		Server: server,
	}
	// mutate client and add all optional params
	for _, o := range opts {
		if err := o(&client); err != nil {
			return nil, err
		}
	}
	// ensure the server URL always has a trailing slash
	if !strings.HasSuffix(client.Server, "/") {
		client.Server += "/"
	}
	// create httpClient, if not already present
	if client.Client == nil {
		client.Client = &http.Client{}
	}
	return &client, nil
}

// WithHTTPClient allows overriding the default Doer, which is
// automatically created using http.Client. This is useful for tests.
func WithHTTPClient(doer HttpRequestDoer) ClientOption {
	return func(c *Client) error {
		c.Client = doer
		return nil
	}
}

// WithRequestEditorFn allows setting up a callback function, which will be
// called right before sending the request. This can be used to mutate the request.
func WithRequestEditorFn(fn RequestEditorFn) ClientOption {
	return func(c *Client) error {
		c.RequestEditors = append(c.RequestEditors, fn)
		return nil
	}
}

// The interface specification for the client above.
type ClientInterface interface {
	// PostApiArzneimittelInformationenWithBody request with any body
	PostApiArzneimittelInformationenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiArzneimittelInformationenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiArzneimittelInformationenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiArzneimittelInformationen(ctx context.Context, body PostApiArzneimittelInformationenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiArzneimittelListeWithBody request with any body
	PostApiArzneimittelListeWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiArzneimittelListeWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiArzneimittelListeApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiArzneimittelListe(ctx context.Context, body PostApiArzneimittelListeJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiArzneimittelListeGzipWithBody request with any body
	PostApiArzneimittelListeGzipWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiArzneimittelListeGzipWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiArzneimittelListeGzipApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiArzneimittelListeGzip(ctx context.Context, body PostApiArzneimittelListeGzipJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiArzneimittelSubstitutionenWithBody request with any body
	PostApiArzneimittelSubstitutionenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiArzneimittelSubstitutionenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiArzneimittelSubstitutionenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiArzneimittelSubstitutionen(ctx context.Context, body PostApiArzneimittelSubstitutionenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiHochverordnungenWithBody request with any body
	PostApiHochverordnungenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiHochverordnungenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiHochverordnungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiHochverordnungen(ctx context.Context, body PostApiHochverordnungenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiIndikatorwirkstoffeWithBody request with any body
	PostApiIndikatorwirkstoffeWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiIndikatorwirkstoffeWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiIndikatorwirkstoffeApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiIndikatorwirkstoffe(ctx context.Context, body PostApiIndikatorwirkstoffeJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiVerordnungenWithBody request with any body
	PostApiVerordnungenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiVerordnungenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiVerordnungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiVerordnungen(ctx context.Context, body PostApiVerordnungenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)
}

func (c *Client) PostApiArzneimittelInformationenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArzneimittelInformationenRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArzneimittelInformationenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiArzneimittelInformationenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArzneimittelInformationenRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArzneimittelInformationen(ctx context.Context, body PostApiArzneimittelInformationenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArzneimittelInformationenRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArzneimittelListeWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArzneimittelListeRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArzneimittelListeWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiArzneimittelListeApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArzneimittelListeRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArzneimittelListe(ctx context.Context, body PostApiArzneimittelListeJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArzneimittelListeRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArzneimittelListeGzipWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArzneimittelListeGzipRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArzneimittelListeGzipWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiArzneimittelListeGzipApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArzneimittelListeGzipRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArzneimittelListeGzip(ctx context.Context, body PostApiArzneimittelListeGzipJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArzneimittelListeGzipRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArzneimittelSubstitutionenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArzneimittelSubstitutionenRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArzneimittelSubstitutionenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiArzneimittelSubstitutionenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArzneimittelSubstitutionenRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArzneimittelSubstitutionen(ctx context.Context, body PostApiArzneimittelSubstitutionenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArzneimittelSubstitutionenRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiHochverordnungenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiHochverordnungenRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiHochverordnungenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiHochverordnungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiHochverordnungenRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiHochverordnungen(ctx context.Context, body PostApiHochverordnungenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiHochverordnungenRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiIndikatorwirkstoffeWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiIndikatorwirkstoffeRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiIndikatorwirkstoffeWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiIndikatorwirkstoffeApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiIndikatorwirkstoffeRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiIndikatorwirkstoffe(ctx context.Context, body PostApiIndikatorwirkstoffeJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiIndikatorwirkstoffeRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiVerordnungenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiVerordnungenRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiVerordnungenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiVerordnungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiVerordnungenRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiVerordnungen(ctx context.Context, body PostApiVerordnungenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiVerordnungenRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

// NewPostApiArzneimittelInformationenRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiArzneimittelInformationen builder with application/*+json body
func NewPostApiArzneimittelInformationenRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiArzneimittelInformationenApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiArzneimittelInformationenRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiArzneimittelInformationenRequest calls the generic PostApiArzneimittelInformationen builder with application/json body
func NewPostApiArzneimittelInformationenRequest(server string, body PostApiArzneimittelInformationenJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiArzneimittelInformationenRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiArzneimittelInformationenRequestWithBody generates requests for PostApiArzneimittelInformationen with any type of body
func NewPostApiArzneimittelInformationenRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/amm/arzneimittel/informationen")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiArzneimittelListeRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiArzneimittelListe builder with application/*+json body
func NewPostApiArzneimittelListeRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiArzneimittelListeApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiArzneimittelListeRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiArzneimittelListeRequest calls the generic PostApiArzneimittelListe builder with application/json body
func NewPostApiArzneimittelListeRequest(server string, body PostApiArzneimittelListeJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiArzneimittelListeRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiArzneimittelListeRequestWithBody generates requests for PostApiArzneimittelListe with any type of body
func NewPostApiArzneimittelListeRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/amm/arzneimittel/liste")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiArzneimittelListeGzipRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiArzneimittelListeGzip builder with application/*+json body
func NewPostApiArzneimittelListeGzipRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiArzneimittelListeGzipApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiArzneimittelListeGzipRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiArzneimittelListeGzipRequest calls the generic PostApiArzneimittelListeGzip builder with application/json body
func NewPostApiArzneimittelListeGzipRequest(server string, body PostApiArzneimittelListeGzipJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiArzneimittelListeGzipRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiArzneimittelListeGzipRequestWithBody generates requests for PostApiArzneimittelListeGzip with any type of body
func NewPostApiArzneimittelListeGzipRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/amm/arzneimittel/liste/gzip")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiArzneimittelSubstitutionenRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiArzneimittelSubstitutionen builder with application/*+json body
func NewPostApiArzneimittelSubstitutionenRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiArzneimittelSubstitutionenApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiArzneimittelSubstitutionenRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiArzneimittelSubstitutionenRequest calls the generic PostApiArzneimittelSubstitutionen builder with application/json body
func NewPostApiArzneimittelSubstitutionenRequest(server string, body PostApiArzneimittelSubstitutionenJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiArzneimittelSubstitutionenRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiArzneimittelSubstitutionenRequestWithBody generates requests for PostApiArzneimittelSubstitutionen with any type of body
func NewPostApiArzneimittelSubstitutionenRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/amm/arzneimittel/substitutionen")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiHochverordnungenRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiHochverordnungen builder with application/*+json body
func NewPostApiHochverordnungenRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiHochverordnungenApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiHochverordnungenRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiHochverordnungenRequest calls the generic PostApiHochverordnungen builder with application/json body
func NewPostApiHochverordnungenRequest(server string, body PostApiHochverordnungenJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiHochverordnungenRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiHochverordnungenRequestWithBody generates requests for PostApiHochverordnungen with any type of body
func NewPostApiHochverordnungenRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/amm/hochverordnungen")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiIndikatorwirkstoffeRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiIndikatorwirkstoffe builder with application/*+json body
func NewPostApiIndikatorwirkstoffeRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiIndikatorwirkstoffeApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiIndikatorwirkstoffeRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiIndikatorwirkstoffeRequest calls the generic PostApiIndikatorwirkstoffe builder with application/json body
func NewPostApiIndikatorwirkstoffeRequest(server string, body PostApiIndikatorwirkstoffeJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiIndikatorwirkstoffeRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiIndikatorwirkstoffeRequestWithBody generates requests for PostApiIndikatorwirkstoffe with any type of body
func NewPostApiIndikatorwirkstoffeRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/amm/indikatorwirkstoffe")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiVerordnungenRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiVerordnungen builder with application/*+json body
func NewPostApiVerordnungenRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiVerordnungenApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiVerordnungenRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiVerordnungenRequest calls the generic PostApiVerordnungen builder with application/json body
func NewPostApiVerordnungenRequest(server string, body PostApiVerordnungenJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiVerordnungenRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiVerordnungenRequestWithBody generates requests for PostApiVerordnungen with any type of body
func NewPostApiVerordnungenRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/amm/verordnungen")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

func (c *Client) applyEditors(ctx context.Context, req *http.Request, additionalEditors []RequestEditorFn) error {
	for _, r := range c.RequestEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	for _, r := range additionalEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	return nil
}

// ClientWithResponses builds on ClientInterface to offer response payloads
type ClientWithResponses struct {
	ClientInterface
}

// NewClientWithResponses creates a new ClientWithResponses, which wraps
// Client with return type handling
func NewClientWithResponses(server string, opts ...ClientOption) (*ClientWithResponses, error) {
	client, err := NewClient(server, opts...)
	if err != nil {
		return nil, err
	}
	return &ClientWithResponses{client}, nil
}

// WithBaseURL overrides the baseURL.
func WithBaseURL(baseURL string) ClientOption {
	return func(c *Client) error {
		newBaseURL, err := url.Parse(baseURL)
		if err != nil {
			return err
		}
		c.Server = newBaseURL.String()
		return nil
	}
}

// ClientWithResponsesInterface is the interface specification for the client with responses above.
type ClientWithResponsesInterface interface {
	// PostApiArzneimittelInformationenWithBodyWithResponse request with any body
	PostApiArzneimittelInformationenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiArzneimittelInformationenResponse, error)

	PostApiArzneimittelInformationenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiArzneimittelInformationenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArzneimittelInformationenResponse, error)

	PostApiArzneimittelInformationenWithResponse(ctx context.Context, body PostApiArzneimittelInformationenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArzneimittelInformationenResponse, error)

	// PostApiArzneimittelListeWithBodyWithResponse request with any body
	PostApiArzneimittelListeWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiArzneimittelListeResponse, error)

	PostApiArzneimittelListeWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiArzneimittelListeApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArzneimittelListeResponse, error)

	PostApiArzneimittelListeWithResponse(ctx context.Context, body PostApiArzneimittelListeJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArzneimittelListeResponse, error)

	// PostApiArzneimittelListeGzipWithBodyWithResponse request with any body
	PostApiArzneimittelListeGzipWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiArzneimittelListeGzipResponse, error)

	PostApiArzneimittelListeGzipWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiArzneimittelListeGzipApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArzneimittelListeGzipResponse, error)

	PostApiArzneimittelListeGzipWithResponse(ctx context.Context, body PostApiArzneimittelListeGzipJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArzneimittelListeGzipResponse, error)

	// PostApiArzneimittelSubstitutionenWithBodyWithResponse request with any body
	PostApiArzneimittelSubstitutionenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiArzneimittelSubstitutionenResponse, error)

	PostApiArzneimittelSubstitutionenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiArzneimittelSubstitutionenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArzneimittelSubstitutionenResponse, error)

	PostApiArzneimittelSubstitutionenWithResponse(ctx context.Context, body PostApiArzneimittelSubstitutionenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArzneimittelSubstitutionenResponse, error)

	// PostApiHochverordnungenWithBodyWithResponse request with any body
	PostApiHochverordnungenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiHochverordnungenResponse, error)

	PostApiHochverordnungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiHochverordnungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiHochverordnungenResponse, error)

	PostApiHochverordnungenWithResponse(ctx context.Context, body PostApiHochverordnungenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiHochverordnungenResponse, error)

	// PostApiIndikatorwirkstoffeWithBodyWithResponse request with any body
	PostApiIndikatorwirkstoffeWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiIndikatorwirkstoffeResponse, error)

	PostApiIndikatorwirkstoffeWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiIndikatorwirkstoffeApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiIndikatorwirkstoffeResponse, error)

	PostApiIndikatorwirkstoffeWithResponse(ctx context.Context, body PostApiIndikatorwirkstoffeJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiIndikatorwirkstoffeResponse, error)

	// PostApiVerordnungenWithBodyWithResponse request with any body
	PostApiVerordnungenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiVerordnungenResponse, error)

	PostApiVerordnungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiVerordnungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiVerordnungenResponse, error)

	PostApiVerordnungenWithResponse(ctx context.Context, body PostApiVerordnungenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiVerordnungenResponse, error)
}

type PostApiArzneimittelInformationenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *LiefereArzneimittelInformationenResultat
}

// Status returns HTTPResponse.Status
func (r PostApiArzneimittelInformationenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiArzneimittelInformationenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiArzneimittelListeResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *LiefereArzneimittelListeResultat
}

// Status returns HTTPResponse.Status
func (r PostApiArzneimittelListeResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiArzneimittelListeResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiArzneimittelListeGzipResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *LiefereArzneimittelListeGZipResultat
}

// Status returns HTTPResponse.Status
func (r PostApiArzneimittelListeGzipResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiArzneimittelListeGzipResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiArzneimittelSubstitutionenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *SubstitutionResultat
}

// Status returns HTTPResponse.Status
func (r PostApiArzneimittelSubstitutionenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiArzneimittelSubstitutionenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiHochverordnungenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *LieferePznAtcFuerHochverordnungListeResultat
}

// Status returns HTTPResponse.Status
func (r PostApiHochverordnungenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiHochverordnungenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiIndikatorwirkstoffeResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *LiefereIndikatorWirkstoffListeResultat
}

// Status returns HTTPResponse.Status
func (r PostApiIndikatorwirkstoffeResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiIndikatorwirkstoffeResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiVerordnungenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *VerordnungsdatenUebermittlungResultat
}

// Status returns HTTPResponse.Status
func (r PostApiVerordnungenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiVerordnungenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

// PostApiArzneimittelInformationenWithBodyWithResponse request with arbitrary body returning *PostApiArzneimittelInformationenResponse
func (c *ClientWithResponses) PostApiArzneimittelInformationenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiArzneimittelInformationenResponse, error) {
	rsp, err := c.PostApiArzneimittelInformationenWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArzneimittelInformationenResponse(rsp)
}

func (c *ClientWithResponses) PostApiArzneimittelInformationenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiArzneimittelInformationenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArzneimittelInformationenResponse, error) {
	rsp, err := c.PostApiArzneimittelInformationenWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArzneimittelInformationenResponse(rsp)
}

func (c *ClientWithResponses) PostApiArzneimittelInformationenWithResponse(ctx context.Context, body PostApiArzneimittelInformationenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArzneimittelInformationenResponse, error) {
	rsp, err := c.PostApiArzneimittelInformationen(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArzneimittelInformationenResponse(rsp)
}

// PostApiArzneimittelListeWithBodyWithResponse request with arbitrary body returning *PostApiArzneimittelListeResponse
func (c *ClientWithResponses) PostApiArzneimittelListeWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiArzneimittelListeResponse, error) {
	rsp, err := c.PostApiArzneimittelListeWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArzneimittelListeResponse(rsp)
}

func (c *ClientWithResponses) PostApiArzneimittelListeWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiArzneimittelListeApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArzneimittelListeResponse, error) {
	rsp, err := c.PostApiArzneimittelListeWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArzneimittelListeResponse(rsp)
}

func (c *ClientWithResponses) PostApiArzneimittelListeWithResponse(ctx context.Context, body PostApiArzneimittelListeJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArzneimittelListeResponse, error) {
	rsp, err := c.PostApiArzneimittelListe(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArzneimittelListeResponse(rsp)
}

// PostApiArzneimittelListeGzipWithBodyWithResponse request with arbitrary body returning *PostApiArzneimittelListeGzipResponse
func (c *ClientWithResponses) PostApiArzneimittelListeGzipWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiArzneimittelListeGzipResponse, error) {
	rsp, err := c.PostApiArzneimittelListeGzipWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArzneimittelListeGzipResponse(rsp)
}

func (c *ClientWithResponses) PostApiArzneimittelListeGzipWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiArzneimittelListeGzipApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArzneimittelListeGzipResponse, error) {
	rsp, err := c.PostApiArzneimittelListeGzipWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArzneimittelListeGzipResponse(rsp)
}

func (c *ClientWithResponses) PostApiArzneimittelListeGzipWithResponse(ctx context.Context, body PostApiArzneimittelListeGzipJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArzneimittelListeGzipResponse, error) {
	rsp, err := c.PostApiArzneimittelListeGzip(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArzneimittelListeGzipResponse(rsp)
}

// PostApiArzneimittelSubstitutionenWithBodyWithResponse request with arbitrary body returning *PostApiArzneimittelSubstitutionenResponse
func (c *ClientWithResponses) PostApiArzneimittelSubstitutionenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiArzneimittelSubstitutionenResponse, error) {
	rsp, err := c.PostApiArzneimittelSubstitutionenWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArzneimittelSubstitutionenResponse(rsp)
}

func (c *ClientWithResponses) PostApiArzneimittelSubstitutionenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiArzneimittelSubstitutionenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArzneimittelSubstitutionenResponse, error) {
	rsp, err := c.PostApiArzneimittelSubstitutionenWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArzneimittelSubstitutionenResponse(rsp)
}

func (c *ClientWithResponses) PostApiArzneimittelSubstitutionenWithResponse(ctx context.Context, body PostApiArzneimittelSubstitutionenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArzneimittelSubstitutionenResponse, error) {
	rsp, err := c.PostApiArzneimittelSubstitutionen(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArzneimittelSubstitutionenResponse(rsp)
}

// PostApiHochverordnungenWithBodyWithResponse request with arbitrary body returning *PostApiHochverordnungenResponse
func (c *ClientWithResponses) PostApiHochverordnungenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiHochverordnungenResponse, error) {
	rsp, err := c.PostApiHochverordnungenWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiHochverordnungenResponse(rsp)
}

func (c *ClientWithResponses) PostApiHochverordnungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiHochverordnungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiHochverordnungenResponse, error) {
	rsp, err := c.PostApiHochverordnungenWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiHochverordnungenResponse(rsp)
}

func (c *ClientWithResponses) PostApiHochverordnungenWithResponse(ctx context.Context, body PostApiHochverordnungenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiHochverordnungenResponse, error) {
	rsp, err := c.PostApiHochverordnungen(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiHochverordnungenResponse(rsp)
}

// PostApiIndikatorwirkstoffeWithBodyWithResponse request with arbitrary body returning *PostApiIndikatorwirkstoffeResponse
func (c *ClientWithResponses) PostApiIndikatorwirkstoffeWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiIndikatorwirkstoffeResponse, error) {
	rsp, err := c.PostApiIndikatorwirkstoffeWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiIndikatorwirkstoffeResponse(rsp)
}

func (c *ClientWithResponses) PostApiIndikatorwirkstoffeWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiIndikatorwirkstoffeApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiIndikatorwirkstoffeResponse, error) {
	rsp, err := c.PostApiIndikatorwirkstoffeWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiIndikatorwirkstoffeResponse(rsp)
}

func (c *ClientWithResponses) PostApiIndikatorwirkstoffeWithResponse(ctx context.Context, body PostApiIndikatorwirkstoffeJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiIndikatorwirkstoffeResponse, error) {
	rsp, err := c.PostApiIndikatorwirkstoffe(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiIndikatorwirkstoffeResponse(rsp)
}

// PostApiVerordnungenWithBodyWithResponse request with arbitrary body returning *PostApiVerordnungenResponse
func (c *ClientWithResponses) PostApiVerordnungenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiVerordnungenResponse, error) {
	rsp, err := c.PostApiVerordnungenWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiVerordnungenResponse(rsp)
}

func (c *ClientWithResponses) PostApiVerordnungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiVerordnungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiVerordnungenResponse, error) {
	rsp, err := c.PostApiVerordnungenWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiVerordnungenResponse(rsp)
}

func (c *ClientWithResponses) PostApiVerordnungenWithResponse(ctx context.Context, body PostApiVerordnungenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiVerordnungenResponse, error) {
	rsp, err := c.PostApiVerordnungen(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiVerordnungenResponse(rsp)
}

// ParsePostApiArzneimittelInformationenResponse parses an HTTP response from a PostApiArzneimittelInformationenWithResponse call
func ParsePostApiArzneimittelInformationenResponse(rsp *http.Response) (*PostApiArzneimittelInformationenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiArzneimittelInformationenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest LiefereArzneimittelInformationenResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiArzneimittelListeResponse parses an HTTP response from a PostApiArzneimittelListeWithResponse call
func ParsePostApiArzneimittelListeResponse(rsp *http.Response) (*PostApiArzneimittelListeResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiArzneimittelListeResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest LiefereArzneimittelListeResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiArzneimittelListeGzipResponse parses an HTTP response from a PostApiArzneimittelListeGzipWithResponse call
func ParsePostApiArzneimittelListeGzipResponse(rsp *http.Response) (*PostApiArzneimittelListeGzipResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiArzneimittelListeGzipResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest LiefereArzneimittelListeGZipResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiArzneimittelSubstitutionenResponse parses an HTTP response from a PostApiArzneimittelSubstitutionenWithResponse call
func ParsePostApiArzneimittelSubstitutionenResponse(rsp *http.Response) (*PostApiArzneimittelSubstitutionenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiArzneimittelSubstitutionenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest SubstitutionResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiHochverordnungenResponse parses an HTTP response from a PostApiHochverordnungenWithResponse call
func ParsePostApiHochverordnungenResponse(rsp *http.Response) (*PostApiHochverordnungenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiHochverordnungenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest LieferePznAtcFuerHochverordnungListeResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiIndikatorwirkstoffeResponse parses an HTTP response from a PostApiIndikatorwirkstoffeWithResponse call
func ParsePostApiIndikatorwirkstoffeResponse(rsp *http.Response) (*PostApiIndikatorwirkstoffeResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiIndikatorwirkstoffeResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest LiefereIndikatorWirkstoffListeResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiVerordnungenResponse parses an HTTP response from a PostApiVerordnungenWithResponse call
func ParsePostApiVerordnungenResponse(rsp *http.Response) (*PostApiVerordnungenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiVerordnungenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest VerordnungsdatenUebermittlungResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}
