// Package hpm_rest_eav_client provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package hpm_rest_eav_client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// Defines values for BearbeitungsstatusTelekonsil.
const (
	Abgebrochen   BearbeitungsstatusTelekonsil = "Abgebrochen"
	Abgeschlossen BearbeitungsstatusTelekonsil = "Abgeschlossen"
	Beantwortet   BearbeitungsstatusTelekonsil = "Beantwortet"
	Beauftragt    BearbeitungsstatusTelekonsil = "Beauftragt"
	Befundet      BearbeitungsstatusTelekonsil = "Befundet"
	Rueckfrage    BearbeitungsstatusTelekonsil = "Rueckfrage"
)

// Defines values for DokumentType.
const (
	DokumentTypeArztbrief  DokumentType = "Arztbrief"
	DokumentTypeTelekonsil DokumentType = "Telekonsil"
)

// Defines values for EmpfangsstatusTypeDokument.
const (
	EmpfangsstatusTypeDokumentInZustellung    EmpfangsstatusTypeDokument = "InZustellung"
	EmpfangsstatusTypeDokumentNichtZugestellt EmpfangsstatusTypeDokument = "NichtZugestellt"
	EmpfangsstatusTypeDokumentUnbekannt       EmpfangsstatusTypeDokument = "Unbekannt"
	EmpfangsstatusTypeDokumentZugestellt      EmpfangsstatusTypeDokument = "Zugestellt"
)

// Defines values for KontextType.
const (
	KontextTypeArztbrief            KontextType = "Arztbrief"
	KontextTypeTeleScanDermatologie KontextType = "TeleScanDermatologie"
)

// Defines values for Meldungsart.
const (
	MeldungsartFehler      Meldungsart = "Fehler"
	MeldungsartInformation Meldungsart = "Information"
	MeldungsartUnbekannt   Meldungsart = "Unbekannt"
	MeldungsartWarnung     Meldungsart = "Warnung"
)

// Defines values for Meldungskategorie.
const (
	MeldungskategorieKeineAngabe   Meldungskategorie = "KeineAngabe"
	MeldungskategorieLaufzeit      Meldungskategorie = "Laufzeit"
	MeldungskategorieRechenzentrum Meldungskategorie = "Rechenzentrum"
	MeldungskategorieValidierung   Meldungskategorie = "Validierung"
)

// Defines values for PatientGeschlecht.
const (
	D PatientGeschlecht = "D"
	M PatientGeschlecht = "M"
	U PatientGeschlecht = "U"
	W PatientGeschlecht = "W"
	X PatientGeschlecht = "X"
)

// Defines values for ReferenzTyp.
const (
	ReferenzTypArzneimittel                ReferenzTyp = "Arzneimittel"
	ReferenzTypDiagnose                    ReferenzTyp = "Diagnose"
	ReferenzTypErkrankungsstufe            ReferenzTyp = "Erkrankungsstufe"
	ReferenzTypHinderungsfaktor            ReferenzTyp = "Hinderungsfaktor"
	ReferenzTypIstFremdeingeschrieben      ReferenzTyp = "IstFremdeingeschrieben"
	ReferenzTypIstNichtFremdeingeschrieben ReferenzTyp = "IstNichtFremdeingeschrieben"
	ReferenzTypIstwert                     ReferenzTyp = "Istwert"
	ReferenzTypKeineAngabe                 ReferenzTyp = "KeineAngabe"
	ReferenzTypLeistung                    ReferenzTyp = "Leistung"
	ReferenzTypOperation                   ReferenzTyp = "Operation"
	ReferenzTypPatient                     ReferenzTyp = "Patient"
	ReferenzTypPraxisgebuehr               ReferenzTyp = "Praxisgebuehr"
	ReferenzTypRezept                      ReferenzTyp = "Rezept"
	ReferenzTypUeberweisung                ReferenzTyp = "Ueberweisung"
	ReferenzTypVertragsidentifikator       ReferenzTyp = "Vertragsidentifikator"
	ReferenzTypZielwert                    ReferenzTyp = "Zielwert"
)

// Defines values for ResultatStatus.
const (
	ResultatStatusFehlgeschlagen       ResultatStatus = "Fehlgeschlagen"
	ResultatStatusOK                   ResultatStatus = "OK"
	ResultatStatusTeilweiseVerarbeitet ResultatStatus = "TeilweiseVerarbeitet"
	ResultatStatusUnbekannt            ResultatStatus = "Unbekannt"
)

// Defines values for SperrstatusTyp.
const (
	Aktiv       SperrstatusTyp = "Aktiv"
	Gesperrt    SperrstatusTyp = "Gesperrt"
	KeineAngabe SperrstatusTyp = "KeineAngabe"
)

// Defines values for TelekonsilTyp.
const (
	Adressiert TelekonsilTyp = "Adressiert"
	Gerichtet  TelekonsilTyp = "Gerichtet"
)

// Defines values for UebermittlungsStatus.
const (
	UebermittlungsStatusFachlicheVerletzung         UebermittlungsStatus = "FachlicheVerletzung"
	UebermittlungsStatusInternerFehler              UebermittlungsStatus = "InternerFehler"
	UebermittlungsStatusKeineAngabe                 UebermittlungsStatus = "KeineAngabe"
	UebermittlungsStatusKeineBerechtigungWebService UebermittlungsStatus = "KeineBerechtigungWebService"
	UebermittlungsStatusOK                          UebermittlungsStatus = "OK"
	UebermittlungsStatusUngueltigeStammdaten        UebermittlungsStatus = "UngueltigeStammdaten"
	UebermittlungsStatusWartung                     UebermittlungsStatus = "Wartung"
)

// Defines values for ZertifikatsTyp.
const (
	Arztzertifikat             ZertifikatsTyp = "Arztzertifikat"
	Betriebsstaettenzertifikat ZertifikatsTyp = "Betriebsstaettenzertifikat"
)

// ArztIdentifikationType Eine eindeutige Identifikation dieses Arztes im Rahmen der Zertifikatserzeugung. Dies kann ein dem Arzt im Rahmen der Teilnahme an dem Verfahren mitgeteilter Identifikationscode sein,
// bei einer Erneuerung eines Zertifikats aber auch das noch gültige bisherige Arztzertifikat.
type ArztIdentifikationType struct {
	// ArztIdentifikationZertifikat Eine eindeutige Identifikation dieses Arztes im Rahmen der Zertifikatserzeugung, die auf dem noch aktuell gültigen Arztzertifikat basiert.
	ArztIdentifikationZertifikat *ArztIdentifikationZertifikatType `json:"arztIdentifikationZertifikat,omitempty"`
	ArztIdentifikationscode      *string                           `json:"arztIdentifikationscode"`
}

// ArztIdentifikationZertifikatType Eine eindeutige Identifikation dieses Arztes im Rahmen der Zertifikatserzeugung, die auf dem noch aktuell gültigen Arztzertifikat basiert.
type ArztIdentifikationZertifikatType struct {
	Kennwort       string `json:"kennwort"`
	ZertifikatArzt []byte `json:"zertifikatArzt"`
}

// ArztVernetzung Informationen zu einem Arzt als Empfänger von Informationen innerhalb der Vernetzung.
type ArztVernetzung struct {
	Bezeichnung *string `json:"bezeichnung"`
	Lanr        *string `json:"lanr"`
}

// Arztbrief Ein Arztbrief samt Metainformationen.
type Arztbrief struct {
	CdaDokument           *string `json:"CdaDokument"`
	AbsenderBsnr          *string `json:"absenderBsnr"`
	DokumentIdentifikator *string `json:"dokumentIdentifikator"`
	DokumentZugriffstoken *string `json:"dokumentZugriffstoken"`
	PdfDokument           *[]byte `json:"pdfDokument"`
	Versichertennummer    *string `json:"versichertennummer"`
}

// ArztbriefEmpfaenger Angabe eines zulässigen Empfängers des Arztbriefes (Betriebsstätte oder Empfängergruppe).
type ArztbriefEmpfaenger struct {
	Bsnr                                   *string `json:"bsnr"`
	EmpfaengergruppeIdentifikator          *int32  `json:"empfaengergruppeIdentifikator,omitempty"`
	EmpfaengergruppeIdentifikatorSpecified *bool   `json:"empfaengergruppeIdentifikatorSpecified,omitempty"`
}

// BearbeitungsstatusTelekonsil defines model for BearbeitungsstatusTelekonsil.
type BearbeitungsstatusTelekonsil string

// BearbeitungsstatusTelekonsilVorgang Der Bearbeitungsstatus eines einzelnen Vorgangs, bestehend aus ID und Status der Telekonsile.
type BearbeitungsstatusTelekonsilVorgang struct {
	Bearbeitungsstatus    *BearbeitungsstatusTelekonsil `json:"bearbeitungsstatus,omitempty"`
	VorgangsIdentifikator *string                       `json:"vorgangsIdentifikator"`
}

// BetriebsstaetteVernetzung Informationen zu einer Betriebsstätte als Empfänger von Informationen innerhalb der Vernetzung.
type BetriebsstaetteVernetzung struct {
	Adresse     *string           `json:"adresse"`
	Aerzte      *[]ArztVernetzung `json:"aerzte"`
	Bezeichnung *string           `json:"bezeichnung"`
	Bsnr        *string           `json:"bsnr"`
}

// DokumentIdentifikation Die Informationen zu einem einzelnen Dokument bestehend aus Identifier und Sicherheitstoken.
type DokumentIdentifikation struct {
	DokumentIdentifikator *string `json:"dokumentIdentifikator"`
	DokumentZugriffstoken *string `json:"dokumentZugriffstoken"`
}

// DokumentType defines model for DokumentType.
type DokumentType string

// Empfaengergruppe Eine der im Rahmen der Vernetzung verfügbaren Empfängergruppen zum gerichteten Versand in der eAV.
type Empfaengergruppe struct {
	EmpfaengergruppeBezeichnung   *string `json:"empfaengergruppeBezeichnung"`
	EmpfaengergruppeIdentifikator *int32  `json:"empfaengergruppeIdentifikator,omitempty"`
}

// EmpfangsstatusDokument Der Empfangsstatus eines einzelnen Dokuments.
type EmpfangsstatusDokument struct {
	DokumentIdentifikator     *string                     `json:"dokumentIdentifikator"`
	EmpfangsstatusTypDokument *EmpfangsstatusTypeDokument `json:"empfangsstatusTypDokument,omitempty"`
}

// EmpfangsstatusTypeDokument defines model for EmpfangsstatusTypeDokument.
type EmpfangsstatusTypeDokument string

// ErzeugePdfAusCdaDokumentContainer Der Container mit Angaben zur Erzeugung von PDF Dokumenten aus CdaDokumenten.
type ErzeugePdfAusCdaDokumentContainer struct {
	CdaDokument  *string `json:"CdaDokument"`
	AbsenderBsnr string  `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation `json:"arztInformationsSystem"`
}

// ErzeugePdfAusCdaDokumentResultat Die Ergebnisstruktur des Funktionsaufrufs von ErzeugePdfAusCdaDokument.
type ErzeugePdfAusCdaDokumentResultat struct {
	ErzeugtesPdf         *[]byte               `json:"erzeugtesPdf"`
	Meldungen            *[]Meldung            `json:"meldungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// ErzeugeZertifikatArztContainer Der Container mit Angaben zur Erzeugung eines Arztzertifikats.
type ErzeugeZertifikatArztContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztIdentifikation Eine eindeutige Identifikation dieses Arztes im Rahmen der Zertifikatserzeugung. Dies kann ein dem Arzt im Rahmen der Teilnahme an dem Verfahren mitgeteilter Identifikationscode sein,
	// bei einer Erneuerung eines Zertifikats aber auch das noch gültige bisherige Arztzertifikat.
	ArztIdentifikation ArztIdentifikationType `json:"arztIdentifikation"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation `json:"arztInformationsSystem"`
	Kennwort               string              `json:"kennwort"`
	Lanr                   string              `json:"lanr"`
	Testuebermittlung      bool                `json:"testuebermittlung"`
}

// ErzeugeZertifikatArztResultat Das Ergebnis des Erzeugens eines Arztzertifikats.
type ErzeugeZertifikatArztResultat struct {
	Meldungen            *[]Meldung            `json:"meldungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
	ZertifikatArzt       *[]byte               `json:"zertifikatArzt"`

	// ZertifikatArztInfo Informationen über das neu erzeugte Zertifikat für den identifizierten Arzt.
	ZertifikatArztInfo *ZertifikatArztInfoType `json:"zertifikatArztInfo,omitempty"`
}

// ErzeugeZertifikatBetriebsstaetteContainer Der Container mit Angaben zur Erzeugung eines Betriebsstättenzertifikats.
type ErzeugeZertifikatBetriebsstaetteContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztIdentifikationZertifikat Eine eindeutige Identifikation dieses Arztes im Rahmen der Zertifikatserzeugung, die auf dem noch aktuell gültigen Arztzertifikat basiert.
	ArztIdentifikationZertifikat ArztIdentifikationZertifikatType `json:"arztIdentifikationZertifikat"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation `json:"arztInformationsSystem"`
	Lanr                   string              `json:"lanr"`
	Testuebermittlung      bool                `json:"testuebermittlung"`
}

// ErzeugeZertifikatBetriebsstaetteResultat Das Ergebnis des Erzeugens eines Betriebsstättenzertifikats.
type ErzeugeZertifikatBetriebsstaetteResultat struct {
	Meldungen                 *[]Meldung            `json:"meldungen"`
	Status                    *ResultatStatus       `json:"status,omitempty"`
	UebermittlungsStatus      *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
	ZertifikatBetriebsstaette *[]byte               `json:"zertifikatBetriebsstaette"`

	// ZertifikatBetriebsstaetteInfo Informationen über das neu erzeugte Zertifikat für die angegebene Betriebsstätte.
	ZertifikatBetriebsstaetteInfo *ZertifikatBetriebsstaetteInfoType `json:"zertifikatBetriebsstaetteInfo,omitempty"`
}

// ExtrahiereZertifikatsinformationVernetzungContainer Der Container mit Angaben zum Auslesen eines Zertifikats der eAV.
type ExtrahiereZertifikatsinformationVernetzungContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation `json:"arztInformationsSystem"`
	Kennwort               *string             `json:"kennwort"`
	Zertifikat             []byte              `json:"zertifikat"`
}

// ExtrahiereZertifikatsinformationVernetzungResultat Das Ergebnis der Extraktion der Metainformationen des Zertifikats.
type ExtrahiereZertifikatsinformationVernetzungResultat struct {
	Meldungen            *[]Meldung            `json:"meldungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`

	// Zertifikationsinformation Die gesammelten Informationen über das vorliegende Zertifikat.
	Zertifikationsinformation *ZertifikatInfoType `json:"zertifikationsinformation,omitempty"`
}

// KontextType defines model for KontextType.
type KontextType string

// LiefereArztbriefeContainer Der Container mit Angaben zum Abruf von Arztbriefen.
type LiefereArztbriefeContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem    SoftwareInformation `json:"arztInformationsSystem"`
	Lanr                      string              `json:"lanr"`
	Testuebermittlung         bool                `json:"testuebermittlung"`
	Versichertennummer        *string             `json:"versichertennummer"`
	ZertifikatBetriebsstaette []byte              `json:"zertifikatBetriebsstaette"`
}

// LiefereArztbriefeResultat Das Ergebnis des Abrufs von Arztbriefen.
type LiefereArztbriefeResultat struct {
	Arztbriefe           *[]Arztbrief          `json:"arztbriefe"`
	Meldungen            *[]Meldung            `json:"meldungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// LiefereBearbeitungsstatusTelekonsileContainer Der Container mit Angaben zum Abruf der Bearbeitungsstatus.
type LiefereBearbeitungsstatusTelekonsileContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem  SoftwareInformation `json:"arztInformationsSystem"`
	Lanr                    string              `json:"lanr"`
	Testuebermittlung       bool                `json:"testuebermittlung"`
	VorgangsIdentifikatoren []string            `json:"vorgangsIdentifikatoren"`
}

// LiefereBearbeitungsstatusTelekonsileResultat Die von der Funktion LiefereBearbeitungsstatusTelekonsile gelieferte Ergebnisstruktur.
type LiefereBearbeitungsstatusTelekonsileResultat struct {
	BearbeitungsstatusTelekonsile *[]BearbeitungsstatusTelekonsilVorgang `json:"bearbeitungsstatusTelekonsile"`
	Meldungen                     *[]Meldung                             `json:"meldungen"`
	Status                        *ResultatStatus                        `json:"status,omitempty"`
	UebermittlungsStatus          *UebermittlungsStatus                  `json:"uebermittlungsStatus,omitempty"`
}

// LiefereDokumentenEmpfangsstatusContainer Der Container mit Angaben zum Abruf von Dokumentenstatus.
type LiefereDokumentenEmpfangsstatusContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem  SoftwareInformation `json:"arztInformationsSystem"`
	DokumentIdentifikatoren []string            `json:"dokumentIdentifikatoren"`
	DokumentTyp             DokumentType        `json:"dokumentTyp"`
	Lanr                    string              `json:"lanr"`
	Testuebermittlung       bool                `json:"testuebermittlung"`
}

// LiefereDokumentenEmpfangsstatusResultat Die von der Funktion LiefereDokumentenEmpfangsstatus gelieferte Ergebnisstruktur.
type LiefereDokumentenEmpfangsstatusResultat struct {
	EmpfangsstatusDokumente *[]EmpfangsstatusDokument `json:"empfangsstatusDokumente"`
	Meldungen               *[]Meldung                `json:"meldungen"`
	Status                  *ResultatStatus           `json:"status,omitempty"`
	UebermittlungsStatus    *UebermittlungsStatus     `json:"uebermittlungsStatus,omitempty"`
}

// LiefereEmpfaengergruppenVernetzungContainer Der Container mit Angaben zum Abruf von Empfängergruppen.
type LiefereEmpfaengergruppenVernetzungContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation `json:"arztInformationsSystem"`
	Kontext                KontextType         `json:"kontext"`
	Lanr                   string              `json:"lanr"`
	Testuebermittlung      bool                `json:"testuebermittlung"`
}

// LiefereEmpfaengergruppenVernetzungResultat Das Ergebnis des Abrufs der Empfängergruppen der Vernetzung.
type LiefereEmpfaengergruppenVernetzungResultat struct {
	Empfaengergruppen    *[]Empfaengergruppe   `json:"empfaengergruppen"`
	Meldungen            *[]Meldung            `json:"meldungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// LiefereMedikationsinformationContainer Der Container mit Angaben zum Abruf von Medikationsinformationen.
type LiefereMedikationsinformationContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem    SoftwareInformation `json:"arztInformationsSystem"`
	Lanr                      string              `json:"lanr"`
	Testuebermittlung         bool                `json:"testuebermittlung"`
	Versichertennummer        string              `json:"versichertennummer"`
	ZertifikatBetriebsstaette []byte              `json:"zertifikatBetriebsstaette"`
}

// LiefereMedikationsinformationErgaenzungenContainer Der Container mit Angaben zum Abruf von Medikationsergänzungen.
type LiefereMedikationsinformationErgaenzungenContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem    SoftwareInformation `json:"arztInformationsSystem"`
	Lanr                      string              `json:"lanr"`
	Testuebermittlung         bool                `json:"testuebermittlung"`
	ZertifikatBetriebsstaette []byte              `json:"zertifikatBetriebsstaette"`
}

// LiefereMedikationsinformationErgaenzungenResultat Das Ergebnis des Abrufs der Liste von Patienten mit zu kuratierenden Medikationsinformationen.
type LiefereMedikationsinformationErgaenzungenResultat struct {
	Ergaenzungen         *[]MedikationsinformationErgaenzung `json:"ergaenzungen"`
	Meldungen            *[]Meldung                          `json:"meldungen"`
	Status               *ResultatStatus                     `json:"status,omitempty"`
	UebermittlungsStatus *UebermittlungsStatus               `json:"uebermittlungsStatus,omitempty"`
}

// LiefereMedikationsinformationResultat Das Ergebnis des Abrufs der aktuellen Medikationsinformation eines Patienten.
type LiefereMedikationsinformationResultat struct {
	CdaDokument          *string               `json:"CdaDokument"`
	Meldungen            *[]Meldung            `json:"meldungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// LiefereTelekonsilMetainformationenContainer Der Container mit Angaben zum Abruf von Telekonsilmetainformationen.
type LiefereTelekonsilMetainformationenContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem    SoftwareInformation `json:"arztInformationsSystem"`
	Lanr                      string              `json:"lanr"`
	TelekonsilTyp             TelekonsilTyp       `json:"telekonsilTyp"`
	Testuebermittlung         bool                `json:"testuebermittlung"`
	ZertifikatBetriebsstaette []byte              `json:"zertifikatBetriebsstaette"`
}

// LiefereTelekonsilMetainformationenResultat Die von der Funktion LiefereTelekonsilMetainformationen gelieferte Ergebnisstruktur.
type LiefereTelekonsilMetainformationenResultat struct {
	Meldungen                    *[]Meldung                    `json:"meldungen"`
	Status                       *ResultatStatus               `json:"status,omitempty"`
	TelekonsileMetainformationen *[]TelekonsileMetainformation `json:"telekonsileMetainformationen"`
	UebermittlungsStatus         *UebermittlungsStatus         `json:"uebermittlungsStatus,omitempty"`
}

// LiefereTelekonsileContainer Der Container mit Angaben zum Abruf von Telekonsilen. Die CDA-Inhalte werden als html-enkodierter String übermittelt.
type LiefereTelekonsileContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem    SoftwareInformation `json:"arztInformationsSystem"`
	DokumentIdentifikatoren   []string            `json:"dokumentIdentifikatoren"`
	Lanr                      string              `json:"lanr"`
	Testuebermittlung         bool                `json:"testuebermittlung"`
	ZertifikatBetriebsstaette []byte              `json:"zertifikatBetriebsstaette"`
}

// LiefereTelekonsileResultat Die von der Funktion LiefereTelekonsile gelieferte Ergebnisstruktur.
type LiefereTelekonsileResultat struct {
	Meldungen            *[]Meldung            `json:"meldungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	Telekonsile          *[]Telekonsil         `json:"telekonsile"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// LiefereZertifikatinformationVernetzungContainer Der Container mit Angaben zum Abruf von Zertifikatsinformationen.
type LiefereZertifikatinformationVernetzungContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation `json:"arztInformationsSystem"`
	Lanr                   string              `json:"lanr"`
	Testuebermittlung      bool                `json:"testuebermittlung"`
}

// LiefereZertifikatinformationVernetzungResultat Die von der Funktion LiefereZertifikatinformationVernetzung gelieferte Ergebnisstruktur.
type LiefereZertifikatinformationVernetzungResultat struct {
	Meldungen                   *[]Meldung                `json:"meldungen"`
	Status                      *ResultatStatus           `json:"status,omitempty"`
	UebermittlungsStatus        *UebermittlungsStatus     `json:"uebermittlungsStatus,omitempty"`
	Zertifikationsinformationen *[]Zertifikatsinformation `json:"zertifikationsinformationen"`
}

// MedikationsinformationErgaenzung Die Versichertennummer des Patienten mit zu kuratierenden Medikationsinformationen zusammen mit dem letzten Änderungsdatum der Medikationsinformation.
type MedikationsinformationErgaenzung struct {
	LetzteAenderung    *time.Time `json:"letzteAenderung,omitempty"`
	Versichertennummer *string    `json:"versichertennummer"`
}

// Meldung Die Meldung, die an den Aufrufer gesendet wird.
type Meldung struct {
	Art                  *Meldungsart          `json:"art,omitempty"`
	Code                 *string               `json:"code"`
	Kategorie            *Meldungskategorie    `json:"kategorie,omitempty"`
	Nachricht            *string               `json:"nachricht"`
	Referenzen           *[]Referenz           `json:"referenzen"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// Meldungsart defines model for Meldungsart.
type Meldungsart string

// Meldungskategorie defines model for Meldungskategorie.
type Meldungskategorie string

// Patient Alle Informationen zum Patienten.
type Patient struct {
	AktuelleVertragsteilnahmen *[]Vertragsteilnahme `json:"aktuelleVertragsteilnahmen"`
	Geburtsdatum               string               `json:"geburtsdatum"`
	Geschlecht                 PatientGeschlecht    `json:"geschlecht"`
	Nachname                   string               `json:"nachname"`
	PatientenId                string               `json:"patientenId"`

	// Versicherungsnachweis Informationen über den Versicherungsnachweis des Patienten (i.d.R. Daten der Versichertenkarte).
	Versicherungsnachweis Versicherungsnachweis `json:"versicherungsnachweis"`
	Vorname               string                `json:"vorname"`
}

// PatientGeschlecht defines model for PatientGeschlecht.
type PatientGeschlecht string

// PruefeAufBereitgestellteArztbriefeContainer Der Container mit Angaben zum Prüfen auf bereitgestellte Arztbriefe.
type PruefeAufBereitgestellteArztbriefeContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem    SoftwareInformation `json:"arztInformationsSystem"`
	Lanr                      string              `json:"lanr"`
	Testuebermittlung         bool                `json:"testuebermittlung"`
	Versichertennummer        *string             `json:"versichertennummer"`
	ZertifikatBetriebsstaette []byte              `json:"zertifikatBetriebsstaette"`
}

// PruefeAufBereitgestellteArztbriefeResultat Das Ergebnis der Überprüfung auf bereitgestellte Arztbriefe.
type PruefeAufBereitgestellteArztbriefeResultat struct {
	IstArztbriefBereitgestellt *bool                 `json:"istArztbriefBereitgestellt,omitempty"`
	Meldungen                  *[]Meldung            `json:"meldungen"`
	Status                     *ResultatStatus       `json:"status,omitempty"`
	UebermittlungsStatus       *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// PruefeSignaturCdaDokumentContainer Der Container mit Angaben zur Prüfung einer Signatur.
type PruefeSignaturCdaDokumentContainer struct {
	CdaDokument  *string `json:"CdaDokument"`
	AbsenderBsnr string  `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation `json:"arztInformationsSystem"`
	Lanr                   string              `json:"lanr"`
	Testuebermittlung      bool                `json:"testuebermittlung"`
}

// PruefeSignaturCdaDokumentResultat Das Ergebnis der Signaturprüfung eines CDA Dokuments.
type PruefeSignaturCdaDokumentResultat struct {
	Meldungen *[]Meldung `json:"meldungen"`

	// SignaturInfo Informationen über das zur Signaturerzeugung verwendete Zertifikat.
	SignaturInfo         *SignaturInfoType     `json:"signaturInfo,omitempty"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// PruefeVersichertenteilnahmeVernetzungContainer Der Container mit Angaben zur Versichertenteilnahmeprüfung der eAV.
type PruefeVersichertenteilnahmeVernetzungContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation `json:"arztInformationsSystem"`
	Lanr                   string              `json:"lanr"`
	Testuebermittlung      bool                `json:"testuebermittlung"`
	Versichertennummer     string              `json:"versichertennummer"`
	VertragsIdentifikator  string              `json:"vertragsIdentifikator"`
}

// PruefeVersichertenteilnahmeVernetzungResultat Das Ergebnis einer Versichertenprüfung der eAV.
type PruefeVersichertenteilnahmeVernetzungResultat struct {
	IstVersichertenteilnahmeVernetzung *bool                 `json:"istVersichertenteilnahmeVernetzung,omitempty"`
	Meldungen                          *[]Meldung            `json:"meldungen"`
	Status                             *ResultatStatus       `json:"status,omitempty"`
	TransferId                         *string               `json:"transferId"`
	UebermittlungsStatus               *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// Referenz Die Referenzen zu den vom Aufrufer übermittelten Objekten.
type Referenz struct {
	Id          *string      `json:"id"`
	ReferenzTyp *ReferenzTyp `json:"referenzTyp,omitempty"`
}

// ReferenzTyp defines model for ReferenzTyp.
type ReferenzTyp string

// ResultatStatus defines model for ResultatStatus.
type ResultatStatus string

// SendeArztbriefContainer Der Container mit Angaben zum Versand von Arztbriefen. Das CDA wird als html-enkodierter String übermittelt.
type SendeArztbriefContainer struct {
	CdaDokument         *string `json:"CdaDokument"`
	SignaturCdaDokument *string `json:"SignaturCdaDokument"`
	AbsenderBsnr        string  `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation   `json:"arztInformationsSystem"`
	ArztbriefEmpfaenger    []ArztbriefEmpfaenger `json:"arztbriefEmpfaenger"`
	Lanr                   string                `json:"lanr"`
	NurPrueflauf           bool                  `json:"nurPrueflauf"`
	PdfDokument            []byte                `json:"pdfDokument"`
	Testuebermittlung      bool                  `json:"testuebermittlung"`
}

// SendeArztbriefResultat Das Ergebnis eines Arztbriefversands.
type SendeArztbriefResultat struct {
	DokumentIdentifikator *string               `json:"dokumentIdentifikator"`
	Meldungen             *[]Meldung            `json:"meldungen"`
	Status                *ResultatStatus       `json:"status,omitempty"`
	TransferId            *string               `json:"transferId"`
	UebermittlungsStatus  *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// SendeDokumentenAbrufbestaetigungContainer Der Container mit Angaben zum Versand von Abrufbestätigungen.
type SendeDokumentenAbrufbestaetigungContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem   SoftwareInformation      `json:"arztInformationsSystem"`
	DokumentIdentifikationen []DokumentIdentifikation `json:"dokumentIdentifikationen"`
	DokumentTyp              DokumentType             `json:"dokumentTyp"`
	Lanr                     string                   `json:"lanr"`
	Testuebermittlung        bool                     `json:"testuebermittlung"`
}

// SendeDokumentenAbrufbestaetigungResultat Das Ergebnis des Sendens einer Abrufbestätigung von Dokumenten.
type SendeDokumentenAbrufbestaetigungResultat struct {
	Meldungen            *[]Meldung            `json:"meldungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// SendeMedikationsinformationContainer Der Container mit Angaben zum Versand von Medikationsinformationen. Das CDA wird als html-enkodierter String übermittelt.
type SendeMedikationsinformationContainer struct {
	CdaDokument  *string `json:"CdaDokument"`
	AbsenderBsnr string  `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation `json:"arztInformationsSystem"`
	Lanr                   string              `json:"lanr"`
	NurPrueflauf           bool                `json:"nurPrueflauf"`
	Testuebermittlung      bool                `json:"testuebermittlung"`
}

// SendeMedikationsinformationPflegearztContainer Der Container mit Angaben zum Setzen eines Pflegearztes.
type SendeMedikationsinformationPflegearztContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem      SoftwareInformation `json:"arztInformationsSystem"`
	EntfernePflegearztZuordnung bool                `json:"entfernePflegearztZuordnung"`
	Lanr                        string              `json:"lanr"`

	// Patient Alle Informationen zum Patienten.
	Patient                   Patient `json:"patient"`
	Testuebermittlung         bool    `json:"testuebermittlung"`
	ZertifikatBetriebsstaette []byte  `json:"zertifikatBetriebsstaette"`
}

// SendeMedikationsinformationPflegearztResultat Das Ergebnis des Sendens eines Pflegearztes für die Medikationsformation für einen Patienten.
type SendeMedikationsinformationPflegearztResultat struct {
	Meldungen            *[]Meldung            `json:"meldungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// SendeMedikationsinformationResultat Das Ergebnis eines Medikationsversands.
type SendeMedikationsinformationResultat struct {
	Meldungen            *[]Meldung            `json:"meldungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	TransferId           *string               `json:"transferId"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// SendeTelekonsilContainer Der Container mit Angaben zum Versand von Telekonsilen. Das CDA wird als html-enkodierter String übermittelt.
type SendeTelekonsilContainer struct {
	CdaDokument         *string `json:"CdaDokument"`
	SignaturCdaDokument *string `json:"SignaturCdaDokument"`
	AbsenderBsnr        string  `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation    `json:"arztInformationsSystem"`
	Lanr                   string                 `json:"lanr"`
	NurPrueflauf           bool                   `json:"nurPrueflauf"`
	TelekonsilEmpfaenger   []TelekonsilEmpfaenger `json:"telekonsilEmpfaenger"`
	Testuebermittlung      bool                   `json:"testuebermittlung"`
}

// SendeTelekonsilResultat Das Ergebnis eines Telekonsilversands.
type SendeTelekonsilResultat struct {
	Meldungen            *[]Meldung            `json:"meldungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	TransferId           *string               `json:"transferId"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// SignaturInfoType Informationen über das zur Signaturerzeugung verwendete Zertifikat.
type SignaturInfoType struct {
	Bsnr                 *string    `json:"bsnr"`
	Lanr                 *string    `json:"lanr"`
	SignaturZeitpunkt    *time.Time `json:"signaturZeitpunkt,omitempty"`
	Unterzeichner        *string    `json:"unterzeichner"`
	ZertifikatAussteller *string    `json:"zertifikatAussteller"`
	ZertifikatGueltigBis *time.Time `json:"zertifikatGueltigBis,omitempty"`
	ZertifikatGueltigVon *time.Time `json:"zertifikatGueltigVon,omitempty"`
}

// SigniereCdaDokumentContainer Der Container mit Angaben zur Erzeugung einer Signatur.
type SigniereCdaDokumentContainer struct {
	CdaDokument  *string `json:"CdaDokument"`
	AbsenderBsnr string  `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation `json:"arztInformationsSystem"`
	Kennwort               string              `json:"kennwort"`
	Lanr                   string              `json:"lanr"`
	Testuebermittlung      bool                `json:"testuebermittlung"`
	ZertifikatArzt         []byte              `json:"zertifikatArzt"`
}

// SigniereCdaDokumentResultat Das Ergebnis des Signierens eines CDA Dokuments.
type SigniereCdaDokumentResultat struct {
	SignaturCdaDokument  *string               `json:"SignaturCdaDokument"`
	Meldungen            *[]Meldung            `json:"meldungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// SoftwareInformation Informationen über das Arztinformationssystem und dessen Hersteller.
type SoftwareInformation struct {
	Name         *string `json:"name"`
	Organisation *string `json:"organisation"`
	SystemOid    string  `json:"systemOid"`
	Version      string  `json:"version"`
}

// SperrstatusTyp defines model for SperrstatusTyp.
type SperrstatusTyp string

// SuchbegriffEmpfaengerVernetzung Enthält alle Suchbegriffe für die Empfängersuche.
type SuchbegriffEmpfaengerVernetzung struct {
	Bsnr                                   *string `json:"bsnr"`
	EmpfaengergruppeIdentifikator          *int32  `json:"empfaengergruppeIdentifikator,omitempty"`
	EmpfaengergruppeIdentifikatorSpecified *bool   `json:"empfaengergruppeIdentifikatorSpecified,omitempty"`
	Lanr                                   *string `json:"lanr"`
	NameArzt                               *string `json:"nameArzt"`
	Ort                                    *string `json:"ort"`
	Plz                                    *string `json:"plz"`
	Strasse                                *string `json:"strasse"`
}

// SucheEmpfaengerVernetzungContainer Der Container mit Angaben zur Empfängersuche.
type SucheEmpfaengerVernetzungContainer struct {
	AbsenderBsnr string `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation `json:"arztInformationsSystem"`
	Kontext                KontextType         `json:"kontext"`
	Lanr                   string              `json:"lanr"`

	// Suchbegriff Enthält alle Suchbegriffe für die Empfängersuche.
	Suchbegriff       SuchbegriffEmpfaengerVernetzung `json:"suchbegriff"`
	Testuebermittlung bool                            `json:"testuebermittlung"`
}

// SucheEmpfaengerVernetzungResultat Das Ergebnis der Empfängersuche in der Vernetzung.
type SucheEmpfaengerVernetzungResultat struct {
	Betriebsstaetten     *[]BetriebsstaetteVernetzung `json:"betriebsstaetten"`
	Meldungen            *[]Meldung                   `json:"meldungen"`
	Status               *ResultatStatus              `json:"status,omitempty"`
	UebermittlungsStatus *UebermittlungsStatus        `json:"uebermittlungsStatus,omitempty"`
}

// Telekonsil Ein Telekonsil samt Informationen dazu.
type Telekonsil struct {
	CdaDokument           *string `json:"CdaDokument"`
	DokumentIdentifikator *string `json:"dokumentIdentifikator"`
	DokumentZugriffstoken *string `json:"dokumentZugriffstoken"`
}

// TelekonsilEmpfaenger Angabe eines Empfängers des Telekonsils.
type TelekonsilEmpfaenger struct {
	EmpfaengergruppeIdentifikator          *int32  `json:"empfaengergruppeIdentifikator,omitempty"`
	EmpfaengergruppeIdentifikatorSpecified *bool   `json:"empfaengergruppeIdentifikatorSpecified,omitempty"`
	Lanr                                   *string `json:"lanr"`
}

// TelekonsilTyp defines model for TelekonsilTyp.
type TelekonsilTyp string

// TelekonsileMetainformation Die Metainformationen eines spezifischen Telekonsils, die von einem Arzt abrufbar sind.
type TelekonsileMetainformation struct {
	BearbeitungsstatusTelekonsil *BearbeitungsstatusTelekonsil `json:"bearbeitungsstatusTelekonsil,omitempty"`
	DokumentIdentifikator        *string                       `json:"dokumentIdentifikator"`
	LetzterBearbeitungszeitpunkt *time.Time                    `json:"letzterBearbeitungszeitpunkt,omitempty"`
	SenderArzt                   *string                       `json:"senderArzt"`
	SenderArztLanr               *string                       `json:"senderArztLanr"`
	VorgangsId                   *string                       `json:"vorgangsId"`
}

// UebermittlungsStatus defines model for UebermittlungsStatus.
type UebermittlungsStatus string

// ValidiereAuContainer Der Container für die Validierung der eAU auf FHIR-Basis. Der xml-Inhalt wird Html-encodiert als string übergeben.
type ValidiereAuContainer struct {
	FhirDokument *string `json:"FhirDokument"`
	AbsenderBsnr string  `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation `json:"arztInformationsSystem"`
	Lanr                   *string             `json:"lanr"`
}

// ValidiereAuResultat defines model for ValidiereAuResultat.
type ValidiereAuResultat struct {
	Meldungen            *[]Meldung            `json:"meldungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// ValidiereEinweisungsbriefContainer defines model for ValidiereEinweisungsbriefContainer.
type ValidiereEinweisungsbriefContainer struct {
	CdaDokument  *string `json:"CdaDokument"`
	AbsenderBsnr string  `json:"absenderBsnr"`

	// ArztInformationsSystem Informationen über das Arztinformationssystem und dessen Hersteller.
	ArztInformationsSystem SoftwareInformation `json:"arztInformationsSystem"`
}

// ValidiereEinweisungsbriefResultat defines model for ValidiereEinweisungsbriefResultat.
type ValidiereEinweisungsbriefResultat struct {
	Meldungen            *[]Meldung            `json:"meldungen"`
	Status               *ResultatStatus       `json:"status,omitempty"`
	UebermittlungsStatus *UebermittlungsStatus `json:"uebermittlungsStatus,omitempty"`
}

// Versicherungsnachweis Informationen über den Versicherungsnachweis des Patienten (i.d.R. Daten der Versichertenkarte).
type Versicherungsnachweis struct {
	BesonderePersonengruppe *string `json:"besonderePersonengruppe"`
	DmpKennzeichnung        *string `json:"dmpKennzeichnung"`
	KrankenkassenIk         string  `json:"krankenkassenIk"`
	VersichertenArt         string  `json:"versichertenArt"`
	VersichertenNummer      string  `json:"versichertenNummer"`
}

// Vertragsteilnahme Alle Informationen zu den Vertragsteilnahmen des abgerechneten Patienten.
type Vertragsteilnahme struct {
	IstVertreterteilnahme bool   `json:"istVertreterteilnahme"`
	VertragsIdentifikator string `json:"vertragsIdentifikator"`
}

// ZertifikatArztInfoType Informationen über das neu erzeugte Zertifikat für den identifizierten Arzt.
type ZertifikatArztInfoType struct {
	Bsnr                 *string    `json:"bsnr"`
	Lanr                 *string    `json:"lanr"`
	Seriennummer         *string    `json:"seriennummer"`
	ZertifikatAussteller *string    `json:"zertifikatAussteller"`
	ZertifikatGueltigBis *time.Time `json:"zertifikatGueltigBis,omitempty"`
	ZertifikatGueltigVon *time.Time `json:"zertifikatGueltigVon,omitempty"`
	Zertifikatsinhaber   *string    `json:"zertifikatsinhaber"`
}

// ZertifikatBetriebsstaetteInfoType Informationen über das neu erzeugte Zertifikat für die angegebene Betriebsstätte.
type ZertifikatBetriebsstaetteInfoType struct {
	Bsnr                 *string    `json:"bsnr"`
	Seriennummer         *string    `json:"seriennummer"`
	ZertifikatAussteller *string    `json:"zertifikatAussteller"`
	ZertifikatGueltigBis *time.Time `json:"zertifikatGueltigBis,omitempty"`
	ZertifikatGueltigVon *time.Time `json:"zertifikatGueltigVon,omitempty"`
	Zertifikatsinhaber   *string    `json:"zertifikatsinhaber"`
}

// ZertifikatInfoType Die gesammelten Informationen über das vorliegende Zertifikat.
type ZertifikatInfoType struct {
	Bsnr                 *string    `json:"bsnr"`
	Lanr                 *string    `json:"lanr"`
	Seriennummer         *string    `json:"seriennummer"`
	ZertifikatAussteller *string    `json:"zertifikatAussteller"`
	ZertifikatGueltigBis *time.Time `json:"zertifikatGueltigBis,omitempty"`
	ZertifikatGueltigVon *time.Time `json:"zertifikatGueltigVon,omitempty"`
	Zertifikatsinhaber   *string    `json:"zertifikatsinhaber"`
}

// ZertifikatsTyp defines model for ZertifikatsTyp.
type ZertifikatsTyp string

// Zertifikatsinformation Die Informationen eines betroffenen Zertifikats.
type Zertifikatsinformation struct {
	Seriennummer   *string         `json:"seriennummer"`
	Sperrstatus    *SperrstatusTyp `json:"sperrstatus,omitempty"`
	ZertifikatsTyp *ZertifikatsTyp `json:"zertifikatsTyp,omitempty"`
}

// PostApiArbeitsunfaehigkeitsbescheinigungenValidierungApplicationWildcardPlusJSONRequestBody defines body for PostApiArbeitsunfaehigkeitsbescheinigungenValidierung for application/*+json ContentType.
type PostApiArbeitsunfaehigkeitsbescheinigungenValidierungApplicationWildcardPlusJSONRequestBody = ValidiereAuContainer

// PostApiArbeitsunfaehigkeitsbescheinigungenValidierungJSONRequestBody defines body for PostApiArbeitsunfaehigkeitsbescheinigungenValidierung for application/json ContentType.
type PostApiArbeitsunfaehigkeitsbescheinigungenValidierungJSONRequestBody = ValidiereAuContainer

// PostApiArztbriefeAbrufApplicationWildcardPlusJSONRequestBody defines body for PostApiArztbriefeAbruf for application/*+json ContentType.
type PostApiArztbriefeAbrufApplicationWildcardPlusJSONRequestBody = LiefereArztbriefeContainer

// PostApiArztbriefeAbrufJSONRequestBody defines body for PostApiArztbriefeAbruf for application/json ContentType.
type PostApiArztbriefeAbrufJSONRequestBody = LiefereArztbriefeContainer

// PostApiArztbriefeAbrufVerfuegbarkeitApplicationWildcardPlusJSONRequestBody defines body for PostApiArztbriefeAbrufVerfuegbarkeit for application/*+json ContentType.
type PostApiArztbriefeAbrufVerfuegbarkeitApplicationWildcardPlusJSONRequestBody = PruefeAufBereitgestellteArztbriefeContainer

// PostApiArztbriefeAbrufVerfuegbarkeitJSONRequestBody defines body for PostApiArztbriefeAbrufVerfuegbarkeit for application/json ContentType.
type PostApiArztbriefeAbrufVerfuegbarkeitJSONRequestBody = PruefeAufBereitgestellteArztbriefeContainer

// PostApiArztbriefeVersandApplicationWildcardPlusJSONRequestBody defines body for PostApiArztbriefeVersand for application/*+json ContentType.
type PostApiArztbriefeVersandApplicationWildcardPlusJSONRequestBody = SendeArztbriefContainer

// PostApiArztbriefeVersandJSONRequestBody defines body for PostApiArztbriefeVersand for application/json ContentType.
type PostApiArztbriefeVersandJSONRequestBody = SendeArztbriefContainer

// PostApiDokumenteAbrufbestaetigungApplicationWildcardPlusJSONRequestBody defines body for PostApiDokumenteAbrufbestaetigung for application/*+json ContentType.
type PostApiDokumenteAbrufbestaetigungApplicationWildcardPlusJSONRequestBody = SendeDokumentenAbrufbestaetigungContainer

// PostApiDokumenteAbrufbestaetigungJSONRequestBody defines body for PostApiDokumenteAbrufbestaetigung for application/json ContentType.
type PostApiDokumenteAbrufbestaetigungJSONRequestBody = SendeDokumentenAbrufbestaetigungContainer

// PostApiDokumenteEmpfangsstatusApplicationWildcardPlusJSONRequestBody defines body for PostApiDokumenteEmpfangsstatus for application/*+json ContentType.
type PostApiDokumenteEmpfangsstatusApplicationWildcardPlusJSONRequestBody = LiefereDokumentenEmpfangsstatusContainer

// PostApiDokumenteEmpfangsstatusJSONRequestBody defines body for PostApiDokumenteEmpfangsstatus for application/json ContentType.
type PostApiDokumenteEmpfangsstatusJSONRequestBody = LiefereDokumentenEmpfangsstatusContainer

// PostApiEinweisungsbriefeValidierungApplicationWildcardPlusJSONRequestBody defines body for PostApiEinweisungsbriefeValidierung for application/*+json ContentType.
type PostApiEinweisungsbriefeValidierungApplicationWildcardPlusJSONRequestBody = ValidiereEinweisungsbriefContainer

// PostApiEinweisungsbriefeValidierungJSONRequestBody defines body for PostApiEinweisungsbriefeValidierung for application/json ContentType.
type PostApiEinweisungsbriefeValidierungJSONRequestBody = ValidiereEinweisungsbriefContainer

// PostApiEmpfaengerGruppenApplicationWildcardPlusJSONRequestBody defines body for PostApiEmpfaengerGruppen for application/*+json ContentType.
type PostApiEmpfaengerGruppenApplicationWildcardPlusJSONRequestBody = LiefereEmpfaengergruppenVernetzungContainer

// PostApiEmpfaengerGruppenJSONRequestBody defines body for PostApiEmpfaengerGruppen for application/json ContentType.
type PostApiEmpfaengerGruppenJSONRequestBody = LiefereEmpfaengergruppenVernetzungContainer

// PostApiEmpfaengerSucheApplicationWildcardPlusJSONRequestBody defines body for PostApiEmpfaengerSuche for application/*+json ContentType.
type PostApiEmpfaengerSucheApplicationWildcardPlusJSONRequestBody = SucheEmpfaengerVernetzungContainer

// PostApiEmpfaengerSucheJSONRequestBody defines body for PostApiEmpfaengerSuche for application/json ContentType.
type PostApiEmpfaengerSucheJSONRequestBody = SucheEmpfaengerVernetzungContainer

// PostApiMedikationsinformationenAbrufApplicationWildcardPlusJSONRequestBody defines body for PostApiMedikationsinformationenAbruf for application/*+json ContentType.
type PostApiMedikationsinformationenAbrufApplicationWildcardPlusJSONRequestBody = LiefereMedikationsinformationContainer

// PostApiMedikationsinformationenAbrufJSONRequestBody defines body for PostApiMedikationsinformationenAbruf for application/json ContentType.
type PostApiMedikationsinformationenAbrufJSONRequestBody = LiefereMedikationsinformationContainer

// PostApiMedikationsinformationenErgaenzungenApplicationWildcardPlusJSONRequestBody defines body for PostApiMedikationsinformationenErgaenzungen for application/*+json ContentType.
type PostApiMedikationsinformationenErgaenzungenApplicationWildcardPlusJSONRequestBody = LiefereMedikationsinformationErgaenzungenContainer

// PostApiMedikationsinformationenErgaenzungenJSONRequestBody defines body for PostApiMedikationsinformationenErgaenzungen for application/json ContentType.
type PostApiMedikationsinformationenErgaenzungenJSONRequestBody = LiefereMedikationsinformationErgaenzungenContainer

// PostApiMedikationsinformationenPflegearztApplicationWildcardPlusJSONRequestBody defines body for PostApiMedikationsinformationenPflegearzt for application/*+json ContentType.
type PostApiMedikationsinformationenPflegearztApplicationWildcardPlusJSONRequestBody = SendeMedikationsinformationPflegearztContainer

// PostApiMedikationsinformationenPflegearztJSONRequestBody defines body for PostApiMedikationsinformationenPflegearzt for application/json ContentType.
type PostApiMedikationsinformationenPflegearztJSONRequestBody = SendeMedikationsinformationPflegearztContainer

// PostApiMedikationsinformationenVersandApplicationWildcardPlusJSONRequestBody defines body for PostApiMedikationsinformationenVersand for application/*+json ContentType.
type PostApiMedikationsinformationenVersandApplicationWildcardPlusJSONRequestBody = SendeMedikationsinformationContainer

// PostApiMedikationsinformationenVersandJSONRequestBody defines body for PostApiMedikationsinformationenVersand for application/json ContentType.
type PostApiMedikationsinformationenVersandJSONRequestBody = SendeMedikationsinformationContainer

// PostApiPdferzeugungCdadokumentApplicationWildcardPlusJSONRequestBody defines body for PostApiPdferzeugungCdadokument for application/*+json ContentType.
type PostApiPdferzeugungCdadokumentApplicationWildcardPlusJSONRequestBody = ErzeugePdfAusCdaDokumentContainer

// PostApiPdferzeugungCdadokumentJSONRequestBody defines body for PostApiPdferzeugungCdadokument for application/json ContentType.
type PostApiPdferzeugungCdadokumentJSONRequestBody = ErzeugePdfAusCdaDokumentContainer

// PostApiSignaturenErzeugungApplicationWildcardPlusJSONRequestBody defines body for PostApiSignaturenErzeugung for application/*+json ContentType.
type PostApiSignaturenErzeugungApplicationWildcardPlusJSONRequestBody = SigniereCdaDokumentContainer

// PostApiSignaturenErzeugungJSONRequestBody defines body for PostApiSignaturenErzeugung for application/json ContentType.
type PostApiSignaturenErzeugungJSONRequestBody = SigniereCdaDokumentContainer

// PostApiSignaturenPruefungApplicationWildcardPlusJSONRequestBody defines body for PostApiSignaturenPruefung for application/*+json ContentType.
type PostApiSignaturenPruefungApplicationWildcardPlusJSONRequestBody = PruefeSignaturCdaDokumentContainer

// PostApiSignaturenPruefungJSONRequestBody defines body for PostApiSignaturenPruefung for application/json ContentType.
type PostApiSignaturenPruefungJSONRequestBody = PruefeSignaturCdaDokumentContainer

// PostApiTelekonsileAbrufApplicationWildcardPlusJSONRequestBody defines body for PostApiTelekonsileAbruf for application/*+json ContentType.
type PostApiTelekonsileAbrufApplicationWildcardPlusJSONRequestBody = LiefereTelekonsileContainer

// PostApiTelekonsileAbrufJSONRequestBody defines body for PostApiTelekonsileAbruf for application/json ContentType.
type PostApiTelekonsileAbrufJSONRequestBody = LiefereTelekonsileContainer

// PostApiTelekonsileBearbeitungsstatusApplicationWildcardPlusJSONRequestBody defines body for PostApiTelekonsileBearbeitungsstatus for application/*+json ContentType.
type PostApiTelekonsileBearbeitungsstatusApplicationWildcardPlusJSONRequestBody = LiefereBearbeitungsstatusTelekonsileContainer

// PostApiTelekonsileBearbeitungsstatusJSONRequestBody defines body for PostApiTelekonsileBearbeitungsstatus for application/json ContentType.
type PostApiTelekonsileBearbeitungsstatusJSONRequestBody = LiefereBearbeitungsstatusTelekonsileContainer

// PostApiTelekonsileMetainformationenApplicationWildcardPlusJSONRequestBody defines body for PostApiTelekonsileMetainformationen for application/*+json ContentType.
type PostApiTelekonsileMetainformationenApplicationWildcardPlusJSONRequestBody = LiefereTelekonsilMetainformationenContainer

// PostApiTelekonsileMetainformationenJSONRequestBody defines body for PostApiTelekonsileMetainformationen for application/json ContentType.
type PostApiTelekonsileMetainformationenJSONRequestBody = LiefereTelekonsilMetainformationenContainer

// PostApiTelekonsileVersandApplicationWildcardPlusJSONRequestBody defines body for PostApiTelekonsileVersand for application/*+json ContentType.
type PostApiTelekonsileVersandApplicationWildcardPlusJSONRequestBody = SendeTelekonsilContainer

// PostApiTelekonsileVersandJSONRequestBody defines body for PostApiTelekonsileVersand for application/json ContentType.
type PostApiTelekonsileVersandJSONRequestBody = SendeTelekonsilContainer

// PostApiVersichertenteilnahmeApplicationWildcardPlusJSONRequestBody defines body for PostApiVersichertenteilnahme for application/*+json ContentType.
type PostApiVersichertenteilnahmeApplicationWildcardPlusJSONRequestBody = PruefeVersichertenteilnahmeVernetzungContainer

// PostApiVersichertenteilnahmeJSONRequestBody defines body for PostApiVersichertenteilnahme for application/json ContentType.
type PostApiVersichertenteilnahmeJSONRequestBody = PruefeVersichertenteilnahmeVernetzungContainer

// PostApiZertifikateAnalyseApplicationWildcardPlusJSONRequestBody defines body for PostApiZertifikateAnalyse for application/*+json ContentType.
type PostApiZertifikateAnalyseApplicationWildcardPlusJSONRequestBody = ExtrahiereZertifikatsinformationVernetzungContainer

// PostApiZertifikateAnalyseJSONRequestBody defines body for PostApiZertifikateAnalyse for application/json ContentType.
type PostApiZertifikateAnalyseJSONRequestBody = ExtrahiereZertifikatsinformationVernetzungContainer

// PostApiZertifikateErzeugungArztApplicationWildcardPlusJSONRequestBody defines body for PostApiZertifikateErzeugungArzt for application/*+json ContentType.
type PostApiZertifikateErzeugungArztApplicationWildcardPlusJSONRequestBody = ErzeugeZertifikatArztContainer

// PostApiZertifikateErzeugungArztJSONRequestBody defines body for PostApiZertifikateErzeugungArzt for application/json ContentType.
type PostApiZertifikateErzeugungArztJSONRequestBody = ErzeugeZertifikatArztContainer

// PostApiZertifikateErzeugungBetriebsstaetteApplicationWildcardPlusJSONRequestBody defines body for PostApiZertifikateErzeugungBetriebsstaette for application/*+json ContentType.
type PostApiZertifikateErzeugungBetriebsstaetteApplicationWildcardPlusJSONRequestBody = ErzeugeZertifikatBetriebsstaetteContainer

// PostApiZertifikateErzeugungBetriebsstaetteJSONRequestBody defines body for PostApiZertifikateErzeugungBetriebsstaette for application/json ContentType.
type PostApiZertifikateErzeugungBetriebsstaetteJSONRequestBody = ErzeugeZertifikatBetriebsstaetteContainer

// PostApiZertifikateInformationenApplicationWildcardPlusJSONRequestBody defines body for PostApiZertifikateInformationen for application/*+json ContentType.
type PostApiZertifikateInformationenApplicationWildcardPlusJSONRequestBody = LiefereZertifikatinformationVernetzungContainer

// PostApiZertifikateInformationenJSONRequestBody defines body for PostApiZertifikateInformationen for application/json ContentType.
type PostApiZertifikateInformationenJSONRequestBody = LiefereZertifikatinformationVernetzungContainer

// RequestEditorFn  is the function signature for the RequestEditor callback function
type RequestEditorFn func(ctx context.Context, req *http.Request) error

// Doer performs HTTP requests.
//
// The standard http.Client implements this interface.
type HttpRequestDoer interface {
	Do(req *http.Request) (*http.Response, error)
}

// Client which conforms to the OpenAPI3 specification for this service.
type Client struct {
	// The endpoint of the server conforming to this interface, with scheme,
	// https://api.deepmap.com for example. This can contain a path relative
	// to the server, such as https://api.deepmap.com/dev-test, and all the
	// paths in the swagger spec will be appended to the server.
	Server string

	// Doer for performing requests, typically a *http.Client with any
	// customized settings, such as certificate chains.
	Client HttpRequestDoer

	// A list of callbacks for modifying requests which are generated before sending over
	// the network.
	RequestEditors []RequestEditorFn
}

// ClientOption allows setting custom parameters during construction
type ClientOption func(*Client) error

// Creates a new Client, with reasonable defaults
func NewClient(server string, opts ...ClientOption) (*Client, error) {
	// create a client with sane default values
	client := Client{
		Server: server,
	}
	// mutate client and add all optional params
	for _, o := range opts {
		if err := o(&client); err != nil {
			return nil, err
		}
	}
	// ensure the server URL always has a trailing slash
	if !strings.HasSuffix(client.Server, "/") {
		client.Server += "/"
	}
	// create httpClient, if not already present
	if client.Client == nil {
		client.Client = &http.Client{}
	}
	return &client, nil
}

// WithHTTPClient allows overriding the default Doer, which is
// automatically created using http.Client. This is useful for tests.
func WithHTTPClient(doer HttpRequestDoer) ClientOption {
	return func(c *Client) error {
		c.Client = doer
		return nil
	}
}

// WithRequestEditorFn allows setting up a callback function, which will be
// called right before sending the request. This can be used to mutate the request.
func WithRequestEditorFn(fn RequestEditorFn) ClientOption {
	return func(c *Client) error {
		c.RequestEditors = append(c.RequestEditors, fn)
		return nil
	}
}

// The interface specification for the client above.
type ClientInterface interface {
	// PostApiArbeitsunfaehigkeitsbescheinigungenValidierungWithBody request with any body
	PostApiArbeitsunfaehigkeitsbescheinigungenValidierungWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiArbeitsunfaehigkeitsbescheinigungenValidierungWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiArbeitsunfaehigkeitsbescheinigungenValidierungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiArbeitsunfaehigkeitsbescheinigungenValidierung(ctx context.Context, body PostApiArbeitsunfaehigkeitsbescheinigungenValidierungJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiArztbriefeAbrufWithBody request with any body
	PostApiArztbriefeAbrufWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiArztbriefeAbrufWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiArztbriefeAbrufApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiArztbriefeAbruf(ctx context.Context, body PostApiArztbriefeAbrufJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiArztbriefeAbrufVerfuegbarkeitWithBody request with any body
	PostApiArztbriefeAbrufVerfuegbarkeitWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiArztbriefeAbrufVerfuegbarkeitWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiArztbriefeAbrufVerfuegbarkeitApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiArztbriefeAbrufVerfuegbarkeit(ctx context.Context, body PostApiArztbriefeAbrufVerfuegbarkeitJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiArztbriefeVersandWithBody request with any body
	PostApiArztbriefeVersandWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiArztbriefeVersandWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiArztbriefeVersandApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiArztbriefeVersand(ctx context.Context, body PostApiArztbriefeVersandJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiDokumenteAbrufbestaetigungWithBody request with any body
	PostApiDokumenteAbrufbestaetigungWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiDokumenteAbrufbestaetigungWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiDokumenteAbrufbestaetigungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiDokumenteAbrufbestaetigung(ctx context.Context, body PostApiDokumenteAbrufbestaetigungJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiDokumenteEmpfangsstatusWithBody request with any body
	PostApiDokumenteEmpfangsstatusWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiDokumenteEmpfangsstatusWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiDokumenteEmpfangsstatusApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiDokumenteEmpfangsstatus(ctx context.Context, body PostApiDokumenteEmpfangsstatusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiEinweisungsbriefeValidierungWithBody request with any body
	PostApiEinweisungsbriefeValidierungWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiEinweisungsbriefeValidierungWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiEinweisungsbriefeValidierungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiEinweisungsbriefeValidierung(ctx context.Context, body PostApiEinweisungsbriefeValidierungJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiEmpfaengerGruppenWithBody request with any body
	PostApiEmpfaengerGruppenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiEmpfaengerGruppenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiEmpfaengerGruppenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiEmpfaengerGruppen(ctx context.Context, body PostApiEmpfaengerGruppenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiEmpfaengerSucheWithBody request with any body
	PostApiEmpfaengerSucheWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiEmpfaengerSucheWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiEmpfaengerSucheApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiEmpfaengerSuche(ctx context.Context, body PostApiEmpfaengerSucheJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiMedikationsinformationenAbrufWithBody request with any body
	PostApiMedikationsinformationenAbrufWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiMedikationsinformationenAbrufWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiMedikationsinformationenAbrufApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiMedikationsinformationenAbruf(ctx context.Context, body PostApiMedikationsinformationenAbrufJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiMedikationsinformationenErgaenzungenWithBody request with any body
	PostApiMedikationsinformationenErgaenzungenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiMedikationsinformationenErgaenzungenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiMedikationsinformationenErgaenzungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiMedikationsinformationenErgaenzungen(ctx context.Context, body PostApiMedikationsinformationenErgaenzungenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiMedikationsinformationenPflegearztWithBody request with any body
	PostApiMedikationsinformationenPflegearztWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiMedikationsinformationenPflegearztWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiMedikationsinformationenPflegearztApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiMedikationsinformationenPflegearzt(ctx context.Context, body PostApiMedikationsinformationenPflegearztJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiMedikationsinformationenVersandWithBody request with any body
	PostApiMedikationsinformationenVersandWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiMedikationsinformationenVersandWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiMedikationsinformationenVersandApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiMedikationsinformationenVersand(ctx context.Context, body PostApiMedikationsinformationenVersandJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiPdferzeugungCdadokumentWithBody request with any body
	PostApiPdferzeugungCdadokumentWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiPdferzeugungCdadokumentWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiPdferzeugungCdadokumentApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiPdferzeugungCdadokument(ctx context.Context, body PostApiPdferzeugungCdadokumentJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiSignaturenErzeugungWithBody request with any body
	PostApiSignaturenErzeugungWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiSignaturenErzeugungWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiSignaturenErzeugungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiSignaturenErzeugung(ctx context.Context, body PostApiSignaturenErzeugungJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiSignaturenPruefungWithBody request with any body
	PostApiSignaturenPruefungWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiSignaturenPruefungWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiSignaturenPruefungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiSignaturenPruefung(ctx context.Context, body PostApiSignaturenPruefungJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiTelekonsileAbrufWithBody request with any body
	PostApiTelekonsileAbrufWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiTelekonsileAbrufWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiTelekonsileAbrufApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiTelekonsileAbruf(ctx context.Context, body PostApiTelekonsileAbrufJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiTelekonsileBearbeitungsstatusWithBody request with any body
	PostApiTelekonsileBearbeitungsstatusWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiTelekonsileBearbeitungsstatusWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiTelekonsileBearbeitungsstatusApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiTelekonsileBearbeitungsstatus(ctx context.Context, body PostApiTelekonsileBearbeitungsstatusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiTelekonsileMetainformationenWithBody request with any body
	PostApiTelekonsileMetainformationenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiTelekonsileMetainformationenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiTelekonsileMetainformationenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiTelekonsileMetainformationen(ctx context.Context, body PostApiTelekonsileMetainformationenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiTelekonsileVersandWithBody request with any body
	PostApiTelekonsileVersandWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiTelekonsileVersandWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiTelekonsileVersandApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiTelekonsileVersand(ctx context.Context, body PostApiTelekonsileVersandJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiVersichertenteilnahmeWithBody request with any body
	PostApiVersichertenteilnahmeWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiVersichertenteilnahmeWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiVersichertenteilnahmeApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiVersichertenteilnahme(ctx context.Context, body PostApiVersichertenteilnahmeJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiZertifikateAnalyseWithBody request with any body
	PostApiZertifikateAnalyseWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiZertifikateAnalyseWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiZertifikateAnalyseApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiZertifikateAnalyse(ctx context.Context, body PostApiZertifikateAnalyseJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiZertifikateErzeugungArztWithBody request with any body
	PostApiZertifikateErzeugungArztWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiZertifikateErzeugungArztWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiZertifikateErzeugungArztApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiZertifikateErzeugungArzt(ctx context.Context, body PostApiZertifikateErzeugungArztJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiZertifikateErzeugungBetriebsstaetteWithBody request with any body
	PostApiZertifikateErzeugungBetriebsstaetteWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiZertifikateErzeugungBetriebsstaetteWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiZertifikateErzeugungBetriebsstaetteApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiZertifikateErzeugungBetriebsstaette(ctx context.Context, body PostApiZertifikateErzeugungBetriebsstaetteJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiZertifikateInformationenWithBody request with any body
	PostApiZertifikateInformationenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiZertifikateInformationenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiZertifikateInformationenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	PostApiZertifikateInformationen(ctx context.Context, body PostApiZertifikateInformationenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)
}

func (c *Client) PostApiArbeitsunfaehigkeitsbescheinigungenValidierungWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArbeitsunfaehigkeitsbescheinigungenValidierungRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArbeitsunfaehigkeitsbescheinigungenValidierungWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiArbeitsunfaehigkeitsbescheinigungenValidierungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArbeitsunfaehigkeitsbescheinigungenValidierungRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArbeitsunfaehigkeitsbescheinigungenValidierung(ctx context.Context, body PostApiArbeitsunfaehigkeitsbescheinigungenValidierungJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArbeitsunfaehigkeitsbescheinigungenValidierungRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArztbriefeAbrufWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArztbriefeAbrufRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArztbriefeAbrufWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiArztbriefeAbrufApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArztbriefeAbrufRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArztbriefeAbruf(ctx context.Context, body PostApiArztbriefeAbrufJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArztbriefeAbrufRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArztbriefeAbrufVerfuegbarkeitWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArztbriefeAbrufVerfuegbarkeitRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArztbriefeAbrufVerfuegbarkeitWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiArztbriefeAbrufVerfuegbarkeitApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArztbriefeAbrufVerfuegbarkeitRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArztbriefeAbrufVerfuegbarkeit(ctx context.Context, body PostApiArztbriefeAbrufVerfuegbarkeitJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArztbriefeAbrufVerfuegbarkeitRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArztbriefeVersandWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArztbriefeVersandRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArztbriefeVersandWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiArztbriefeVersandApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArztbriefeVersandRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiArztbriefeVersand(ctx context.Context, body PostApiArztbriefeVersandJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiArztbriefeVersandRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiDokumenteAbrufbestaetigungWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiDokumenteAbrufbestaetigungRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiDokumenteAbrufbestaetigungWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiDokumenteAbrufbestaetigungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiDokumenteAbrufbestaetigungRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiDokumenteAbrufbestaetigung(ctx context.Context, body PostApiDokumenteAbrufbestaetigungJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiDokumenteAbrufbestaetigungRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiDokumenteEmpfangsstatusWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiDokumenteEmpfangsstatusRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiDokumenteEmpfangsstatusWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiDokumenteEmpfangsstatusApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiDokumenteEmpfangsstatusRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiDokumenteEmpfangsstatus(ctx context.Context, body PostApiDokumenteEmpfangsstatusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiDokumenteEmpfangsstatusRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiEinweisungsbriefeValidierungWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiEinweisungsbriefeValidierungRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiEinweisungsbriefeValidierungWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiEinweisungsbriefeValidierungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiEinweisungsbriefeValidierungRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiEinweisungsbriefeValidierung(ctx context.Context, body PostApiEinweisungsbriefeValidierungJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiEinweisungsbriefeValidierungRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiEmpfaengerGruppenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiEmpfaengerGruppenRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiEmpfaengerGruppenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiEmpfaengerGruppenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiEmpfaengerGruppenRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiEmpfaengerGruppen(ctx context.Context, body PostApiEmpfaengerGruppenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiEmpfaengerGruppenRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiEmpfaengerSucheWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiEmpfaengerSucheRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiEmpfaengerSucheWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiEmpfaengerSucheApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiEmpfaengerSucheRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiEmpfaengerSuche(ctx context.Context, body PostApiEmpfaengerSucheJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiEmpfaengerSucheRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiMedikationsinformationenAbrufWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiMedikationsinformationenAbrufRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiMedikationsinformationenAbrufWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiMedikationsinformationenAbrufApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiMedikationsinformationenAbrufRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiMedikationsinformationenAbruf(ctx context.Context, body PostApiMedikationsinformationenAbrufJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiMedikationsinformationenAbrufRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiMedikationsinformationenErgaenzungenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiMedikationsinformationenErgaenzungenRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiMedikationsinformationenErgaenzungenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiMedikationsinformationenErgaenzungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiMedikationsinformationenErgaenzungenRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiMedikationsinformationenErgaenzungen(ctx context.Context, body PostApiMedikationsinformationenErgaenzungenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiMedikationsinformationenErgaenzungenRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiMedikationsinformationenPflegearztWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiMedikationsinformationenPflegearztRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiMedikationsinformationenPflegearztWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiMedikationsinformationenPflegearztApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiMedikationsinformationenPflegearztRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiMedikationsinformationenPflegearzt(ctx context.Context, body PostApiMedikationsinformationenPflegearztJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiMedikationsinformationenPflegearztRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiMedikationsinformationenVersandWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiMedikationsinformationenVersandRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiMedikationsinformationenVersandWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiMedikationsinformationenVersandApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiMedikationsinformationenVersandRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiMedikationsinformationenVersand(ctx context.Context, body PostApiMedikationsinformationenVersandJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiMedikationsinformationenVersandRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiPdferzeugungCdadokumentWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiPdferzeugungCdadokumentRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiPdferzeugungCdadokumentWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiPdferzeugungCdadokumentApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiPdferzeugungCdadokumentRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiPdferzeugungCdadokument(ctx context.Context, body PostApiPdferzeugungCdadokumentJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiPdferzeugungCdadokumentRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiSignaturenErzeugungWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiSignaturenErzeugungRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiSignaturenErzeugungWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiSignaturenErzeugungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiSignaturenErzeugungRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiSignaturenErzeugung(ctx context.Context, body PostApiSignaturenErzeugungJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiSignaturenErzeugungRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiSignaturenPruefungWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiSignaturenPruefungRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiSignaturenPruefungWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiSignaturenPruefungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiSignaturenPruefungRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiSignaturenPruefung(ctx context.Context, body PostApiSignaturenPruefungJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiSignaturenPruefungRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiTelekonsileAbrufWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiTelekonsileAbrufRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiTelekonsileAbrufWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiTelekonsileAbrufApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiTelekonsileAbrufRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiTelekonsileAbruf(ctx context.Context, body PostApiTelekonsileAbrufJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiTelekonsileAbrufRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiTelekonsileBearbeitungsstatusWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiTelekonsileBearbeitungsstatusRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiTelekonsileBearbeitungsstatusWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiTelekonsileBearbeitungsstatusApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiTelekonsileBearbeitungsstatusRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiTelekonsileBearbeitungsstatus(ctx context.Context, body PostApiTelekonsileBearbeitungsstatusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiTelekonsileBearbeitungsstatusRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiTelekonsileMetainformationenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiTelekonsileMetainformationenRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiTelekonsileMetainformationenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiTelekonsileMetainformationenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiTelekonsileMetainformationenRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiTelekonsileMetainformationen(ctx context.Context, body PostApiTelekonsileMetainformationenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiTelekonsileMetainformationenRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiTelekonsileVersandWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiTelekonsileVersandRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiTelekonsileVersandWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiTelekonsileVersandApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiTelekonsileVersandRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiTelekonsileVersand(ctx context.Context, body PostApiTelekonsileVersandJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiTelekonsileVersandRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiVersichertenteilnahmeWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiVersichertenteilnahmeRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiVersichertenteilnahmeWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiVersichertenteilnahmeApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiVersichertenteilnahmeRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiVersichertenteilnahme(ctx context.Context, body PostApiVersichertenteilnahmeJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiVersichertenteilnahmeRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiZertifikateAnalyseWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiZertifikateAnalyseRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiZertifikateAnalyseWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiZertifikateAnalyseApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiZertifikateAnalyseRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiZertifikateAnalyse(ctx context.Context, body PostApiZertifikateAnalyseJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiZertifikateAnalyseRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiZertifikateErzeugungArztWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiZertifikateErzeugungArztRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiZertifikateErzeugungArztWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiZertifikateErzeugungArztApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiZertifikateErzeugungArztRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiZertifikateErzeugungArzt(ctx context.Context, body PostApiZertifikateErzeugungArztJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiZertifikateErzeugungArztRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiZertifikateErzeugungBetriebsstaetteWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiZertifikateErzeugungBetriebsstaetteRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiZertifikateErzeugungBetriebsstaetteWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiZertifikateErzeugungBetriebsstaetteApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiZertifikateErzeugungBetriebsstaetteRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiZertifikateErzeugungBetriebsstaette(ctx context.Context, body PostApiZertifikateErzeugungBetriebsstaetteJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiZertifikateErzeugungBetriebsstaetteRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiZertifikateInformationenWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiZertifikateInformationenRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiZertifikateInformationenWithApplicationWildcardPlusJSONBody(ctx context.Context, body PostApiZertifikateInformationenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiZertifikateInformationenRequestWithApplicationWildcardPlusJSONBody(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiZertifikateInformationen(ctx context.Context, body PostApiZertifikateInformationenJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiZertifikateInformationenRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

// NewPostApiArbeitsunfaehigkeitsbescheinigungenValidierungRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiArbeitsunfaehigkeitsbescheinigungenValidierung builder with application/*+json body
func NewPostApiArbeitsunfaehigkeitsbescheinigungenValidierungRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiArbeitsunfaehigkeitsbescheinigungenValidierungApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiArbeitsunfaehigkeitsbescheinigungenValidierungRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiArbeitsunfaehigkeitsbescheinigungenValidierungRequest calls the generic PostApiArbeitsunfaehigkeitsbescheinigungenValidierung builder with application/json body
func NewPostApiArbeitsunfaehigkeitsbescheinigungenValidierungRequest(server string, body PostApiArbeitsunfaehigkeitsbescheinigungenValidierungJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiArbeitsunfaehigkeitsbescheinigungenValidierungRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiArbeitsunfaehigkeitsbescheinigungenValidierungRequestWithBody generates requests for PostApiArbeitsunfaehigkeitsbescheinigungenValidierung with any type of body
func NewPostApiArbeitsunfaehigkeitsbescheinigungenValidierungRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/arbeitsunfaehigkeitsbescheinigungen/validierung")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiArztbriefeAbrufRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiArztbriefeAbruf builder with application/*+json body
func NewPostApiArztbriefeAbrufRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiArztbriefeAbrufApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiArztbriefeAbrufRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiArztbriefeAbrufRequest calls the generic PostApiArztbriefeAbruf builder with application/json body
func NewPostApiArztbriefeAbrufRequest(server string, body PostApiArztbriefeAbrufJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiArztbriefeAbrufRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiArztbriefeAbrufRequestWithBody generates requests for PostApiArztbriefeAbruf with any type of body
func NewPostApiArztbriefeAbrufRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/arztbriefe/abruf")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiArztbriefeAbrufVerfuegbarkeitRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiArztbriefeAbrufVerfuegbarkeit builder with application/*+json body
func NewPostApiArztbriefeAbrufVerfuegbarkeitRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiArztbriefeAbrufVerfuegbarkeitApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiArztbriefeAbrufVerfuegbarkeitRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiArztbriefeAbrufVerfuegbarkeitRequest calls the generic PostApiArztbriefeAbrufVerfuegbarkeit builder with application/json body
func NewPostApiArztbriefeAbrufVerfuegbarkeitRequest(server string, body PostApiArztbriefeAbrufVerfuegbarkeitJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiArztbriefeAbrufVerfuegbarkeitRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiArztbriefeAbrufVerfuegbarkeitRequestWithBody generates requests for PostApiArztbriefeAbrufVerfuegbarkeit with any type of body
func NewPostApiArztbriefeAbrufVerfuegbarkeitRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/arztbriefe/abruf/verfuegbarkeit")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiArztbriefeVersandRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiArztbriefeVersand builder with application/*+json body
func NewPostApiArztbriefeVersandRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiArztbriefeVersandApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiArztbriefeVersandRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiArztbriefeVersandRequest calls the generic PostApiArztbriefeVersand builder with application/json body
func NewPostApiArztbriefeVersandRequest(server string, body PostApiArztbriefeVersandJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiArztbriefeVersandRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiArztbriefeVersandRequestWithBody generates requests for PostApiArztbriefeVersand with any type of body
func NewPostApiArztbriefeVersandRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/arztbriefe/versand")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiDokumenteAbrufbestaetigungRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiDokumenteAbrufbestaetigung builder with application/*+json body
func NewPostApiDokumenteAbrufbestaetigungRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiDokumenteAbrufbestaetigungApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiDokumenteAbrufbestaetigungRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiDokumenteAbrufbestaetigungRequest calls the generic PostApiDokumenteAbrufbestaetigung builder with application/json body
func NewPostApiDokumenteAbrufbestaetigungRequest(server string, body PostApiDokumenteAbrufbestaetigungJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiDokumenteAbrufbestaetigungRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiDokumenteAbrufbestaetigungRequestWithBody generates requests for PostApiDokumenteAbrufbestaetigung with any type of body
func NewPostApiDokumenteAbrufbestaetigungRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/dokumente/abrufbestaetigung")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiDokumenteEmpfangsstatusRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiDokumenteEmpfangsstatus builder with application/*+json body
func NewPostApiDokumenteEmpfangsstatusRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiDokumenteEmpfangsstatusApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiDokumenteEmpfangsstatusRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiDokumenteEmpfangsstatusRequest calls the generic PostApiDokumenteEmpfangsstatus builder with application/json body
func NewPostApiDokumenteEmpfangsstatusRequest(server string, body PostApiDokumenteEmpfangsstatusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiDokumenteEmpfangsstatusRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiDokumenteEmpfangsstatusRequestWithBody generates requests for PostApiDokumenteEmpfangsstatus with any type of body
func NewPostApiDokumenteEmpfangsstatusRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/dokumente/empfangsstatus")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiEinweisungsbriefeValidierungRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiEinweisungsbriefeValidierung builder with application/*+json body
func NewPostApiEinweisungsbriefeValidierungRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiEinweisungsbriefeValidierungApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiEinweisungsbriefeValidierungRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiEinweisungsbriefeValidierungRequest calls the generic PostApiEinweisungsbriefeValidierung builder with application/json body
func NewPostApiEinweisungsbriefeValidierungRequest(server string, body PostApiEinweisungsbriefeValidierungJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiEinweisungsbriefeValidierungRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiEinweisungsbriefeValidierungRequestWithBody generates requests for PostApiEinweisungsbriefeValidierung with any type of body
func NewPostApiEinweisungsbriefeValidierungRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/einweisungsbriefe/validierung")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiEmpfaengerGruppenRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiEmpfaengerGruppen builder with application/*+json body
func NewPostApiEmpfaengerGruppenRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiEmpfaengerGruppenApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiEmpfaengerGruppenRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiEmpfaengerGruppenRequest calls the generic PostApiEmpfaengerGruppen builder with application/json body
func NewPostApiEmpfaengerGruppenRequest(server string, body PostApiEmpfaengerGruppenJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiEmpfaengerGruppenRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiEmpfaengerGruppenRequestWithBody generates requests for PostApiEmpfaengerGruppen with any type of body
func NewPostApiEmpfaengerGruppenRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/empfaenger/gruppen")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiEmpfaengerSucheRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiEmpfaengerSuche builder with application/*+json body
func NewPostApiEmpfaengerSucheRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiEmpfaengerSucheApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiEmpfaengerSucheRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiEmpfaengerSucheRequest calls the generic PostApiEmpfaengerSuche builder with application/json body
func NewPostApiEmpfaengerSucheRequest(server string, body PostApiEmpfaengerSucheJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiEmpfaengerSucheRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiEmpfaengerSucheRequestWithBody generates requests for PostApiEmpfaengerSuche with any type of body
func NewPostApiEmpfaengerSucheRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/empfaenger/suche")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiMedikationsinformationenAbrufRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiMedikationsinformationenAbruf builder with application/*+json body
func NewPostApiMedikationsinformationenAbrufRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiMedikationsinformationenAbrufApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiMedikationsinformationenAbrufRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiMedikationsinformationenAbrufRequest calls the generic PostApiMedikationsinformationenAbruf builder with application/json body
func NewPostApiMedikationsinformationenAbrufRequest(server string, body PostApiMedikationsinformationenAbrufJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiMedikationsinformationenAbrufRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiMedikationsinformationenAbrufRequestWithBody generates requests for PostApiMedikationsinformationenAbruf with any type of body
func NewPostApiMedikationsinformationenAbrufRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/medikationsinformationen/abruf")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiMedikationsinformationenErgaenzungenRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiMedikationsinformationenErgaenzungen builder with application/*+json body
func NewPostApiMedikationsinformationenErgaenzungenRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiMedikationsinformationenErgaenzungenApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiMedikationsinformationenErgaenzungenRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiMedikationsinformationenErgaenzungenRequest calls the generic PostApiMedikationsinformationenErgaenzungen builder with application/json body
func NewPostApiMedikationsinformationenErgaenzungenRequest(server string, body PostApiMedikationsinformationenErgaenzungenJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiMedikationsinformationenErgaenzungenRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiMedikationsinformationenErgaenzungenRequestWithBody generates requests for PostApiMedikationsinformationenErgaenzungen with any type of body
func NewPostApiMedikationsinformationenErgaenzungenRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/medikationsinformationen/ergaenzungen")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiMedikationsinformationenPflegearztRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiMedikationsinformationenPflegearzt builder with application/*+json body
func NewPostApiMedikationsinformationenPflegearztRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiMedikationsinformationenPflegearztApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiMedikationsinformationenPflegearztRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiMedikationsinformationenPflegearztRequest calls the generic PostApiMedikationsinformationenPflegearzt builder with application/json body
func NewPostApiMedikationsinformationenPflegearztRequest(server string, body PostApiMedikationsinformationenPflegearztJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiMedikationsinformationenPflegearztRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiMedikationsinformationenPflegearztRequestWithBody generates requests for PostApiMedikationsinformationenPflegearzt with any type of body
func NewPostApiMedikationsinformationenPflegearztRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/medikationsinformationen/pflegearzt")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiMedikationsinformationenVersandRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiMedikationsinformationenVersand builder with application/*+json body
func NewPostApiMedikationsinformationenVersandRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiMedikationsinformationenVersandApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiMedikationsinformationenVersandRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiMedikationsinformationenVersandRequest calls the generic PostApiMedikationsinformationenVersand builder with application/json body
func NewPostApiMedikationsinformationenVersandRequest(server string, body PostApiMedikationsinformationenVersandJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiMedikationsinformationenVersandRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiMedikationsinformationenVersandRequestWithBody generates requests for PostApiMedikationsinformationenVersand with any type of body
func NewPostApiMedikationsinformationenVersandRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/medikationsinformationen/versand")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiPdferzeugungCdadokumentRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiPdferzeugungCdadokument builder with application/*+json body
func NewPostApiPdferzeugungCdadokumentRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiPdferzeugungCdadokumentApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiPdferzeugungCdadokumentRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiPdferzeugungCdadokumentRequest calls the generic PostApiPdferzeugungCdadokument builder with application/json body
func NewPostApiPdferzeugungCdadokumentRequest(server string, body PostApiPdferzeugungCdadokumentJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiPdferzeugungCdadokumentRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiPdferzeugungCdadokumentRequestWithBody generates requests for PostApiPdferzeugungCdadokument with any type of body
func NewPostApiPdferzeugungCdadokumentRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/pdferzeugung/cdadokument")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiSignaturenErzeugungRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiSignaturenErzeugung builder with application/*+json body
func NewPostApiSignaturenErzeugungRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiSignaturenErzeugungApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiSignaturenErzeugungRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiSignaturenErzeugungRequest calls the generic PostApiSignaturenErzeugung builder with application/json body
func NewPostApiSignaturenErzeugungRequest(server string, body PostApiSignaturenErzeugungJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiSignaturenErzeugungRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiSignaturenErzeugungRequestWithBody generates requests for PostApiSignaturenErzeugung with any type of body
func NewPostApiSignaturenErzeugungRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/signaturen/erzeugung")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiSignaturenPruefungRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiSignaturenPruefung builder with application/*+json body
func NewPostApiSignaturenPruefungRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiSignaturenPruefungApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiSignaturenPruefungRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiSignaturenPruefungRequest calls the generic PostApiSignaturenPruefung builder with application/json body
func NewPostApiSignaturenPruefungRequest(server string, body PostApiSignaturenPruefungJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiSignaturenPruefungRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiSignaturenPruefungRequestWithBody generates requests for PostApiSignaturenPruefung with any type of body
func NewPostApiSignaturenPruefungRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/signaturen/pruefung")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiTelekonsileAbrufRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiTelekonsileAbruf builder with application/*+json body
func NewPostApiTelekonsileAbrufRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiTelekonsileAbrufApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiTelekonsileAbrufRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiTelekonsileAbrufRequest calls the generic PostApiTelekonsileAbruf builder with application/json body
func NewPostApiTelekonsileAbrufRequest(server string, body PostApiTelekonsileAbrufJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiTelekonsileAbrufRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiTelekonsileAbrufRequestWithBody generates requests for PostApiTelekonsileAbruf with any type of body
func NewPostApiTelekonsileAbrufRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/telekonsile/abruf")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiTelekonsileBearbeitungsstatusRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiTelekonsileBearbeitungsstatus builder with application/*+json body
func NewPostApiTelekonsileBearbeitungsstatusRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiTelekonsileBearbeitungsstatusApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiTelekonsileBearbeitungsstatusRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiTelekonsileBearbeitungsstatusRequest calls the generic PostApiTelekonsileBearbeitungsstatus builder with application/json body
func NewPostApiTelekonsileBearbeitungsstatusRequest(server string, body PostApiTelekonsileBearbeitungsstatusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiTelekonsileBearbeitungsstatusRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiTelekonsileBearbeitungsstatusRequestWithBody generates requests for PostApiTelekonsileBearbeitungsstatus with any type of body
func NewPostApiTelekonsileBearbeitungsstatusRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/telekonsile/bearbeitungsstatus")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiTelekonsileMetainformationenRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiTelekonsileMetainformationen builder with application/*+json body
func NewPostApiTelekonsileMetainformationenRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiTelekonsileMetainformationenApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiTelekonsileMetainformationenRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiTelekonsileMetainformationenRequest calls the generic PostApiTelekonsileMetainformationen builder with application/json body
func NewPostApiTelekonsileMetainformationenRequest(server string, body PostApiTelekonsileMetainformationenJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiTelekonsileMetainformationenRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiTelekonsileMetainformationenRequestWithBody generates requests for PostApiTelekonsileMetainformationen with any type of body
func NewPostApiTelekonsileMetainformationenRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/telekonsile/metainformationen")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiTelekonsileVersandRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiTelekonsileVersand builder with application/*+json body
func NewPostApiTelekonsileVersandRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiTelekonsileVersandApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiTelekonsileVersandRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiTelekonsileVersandRequest calls the generic PostApiTelekonsileVersand builder with application/json body
func NewPostApiTelekonsileVersandRequest(server string, body PostApiTelekonsileVersandJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiTelekonsileVersandRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiTelekonsileVersandRequestWithBody generates requests for PostApiTelekonsileVersand with any type of body
func NewPostApiTelekonsileVersandRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/telekonsile/versand")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiVersichertenteilnahmeRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiVersichertenteilnahme builder with application/*+json body
func NewPostApiVersichertenteilnahmeRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiVersichertenteilnahmeApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiVersichertenteilnahmeRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiVersichertenteilnahmeRequest calls the generic PostApiVersichertenteilnahme builder with application/json body
func NewPostApiVersichertenteilnahmeRequest(server string, body PostApiVersichertenteilnahmeJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiVersichertenteilnahmeRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiVersichertenteilnahmeRequestWithBody generates requests for PostApiVersichertenteilnahme with any type of body
func NewPostApiVersichertenteilnahmeRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/versichertenteilnahme")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiZertifikateAnalyseRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiZertifikateAnalyse builder with application/*+json body
func NewPostApiZertifikateAnalyseRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiZertifikateAnalyseApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiZertifikateAnalyseRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiZertifikateAnalyseRequest calls the generic PostApiZertifikateAnalyse builder with application/json body
func NewPostApiZertifikateAnalyseRequest(server string, body PostApiZertifikateAnalyseJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiZertifikateAnalyseRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiZertifikateAnalyseRequestWithBody generates requests for PostApiZertifikateAnalyse with any type of body
func NewPostApiZertifikateAnalyseRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/zertifikate/analyse")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiZertifikateErzeugungArztRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiZertifikateErzeugungArzt builder with application/*+json body
func NewPostApiZertifikateErzeugungArztRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiZertifikateErzeugungArztApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiZertifikateErzeugungArztRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiZertifikateErzeugungArztRequest calls the generic PostApiZertifikateErzeugungArzt builder with application/json body
func NewPostApiZertifikateErzeugungArztRequest(server string, body PostApiZertifikateErzeugungArztJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiZertifikateErzeugungArztRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiZertifikateErzeugungArztRequestWithBody generates requests for PostApiZertifikateErzeugungArzt with any type of body
func NewPostApiZertifikateErzeugungArztRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/zertifikate/erzeugung/arzt")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiZertifikateErzeugungBetriebsstaetteRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiZertifikateErzeugungBetriebsstaette builder with application/*+json body
func NewPostApiZertifikateErzeugungBetriebsstaetteRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiZertifikateErzeugungBetriebsstaetteApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiZertifikateErzeugungBetriebsstaetteRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiZertifikateErzeugungBetriebsstaetteRequest calls the generic PostApiZertifikateErzeugungBetriebsstaette builder with application/json body
func NewPostApiZertifikateErzeugungBetriebsstaetteRequest(server string, body PostApiZertifikateErzeugungBetriebsstaetteJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiZertifikateErzeugungBetriebsstaetteRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiZertifikateErzeugungBetriebsstaetteRequestWithBody generates requests for PostApiZertifikateErzeugungBetriebsstaette with any type of body
func NewPostApiZertifikateErzeugungBetriebsstaetteRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/zertifikate/erzeugung/betriebsstaette")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiZertifikateInformationenRequestWithApplicationWildcardPlusJSONBody calls the generic PostApiZertifikateInformationen builder with application/*+json body
func NewPostApiZertifikateInformationenRequestWithApplicationWildcardPlusJSONBody(server string, body PostApiZertifikateInformationenApplicationWildcardPlusJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiZertifikateInformationenRequestWithBody(server, "application/*+json", bodyReader)
}

// NewPostApiZertifikateInformationenRequest calls the generic PostApiZertifikateInformationen builder with application/json body
func NewPostApiZertifikateInformationenRequest(server string, body PostApiZertifikateInformationenJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPostApiZertifikateInformationenRequestWithBody(server, "application/json", bodyReader)
}

// NewPostApiZertifikateInformationenRequestWithBody generates requests for PostApiZertifikateInformationen with any type of body
func NewPostApiZertifikateInformationenRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/latest/eav/zertifikate/informationen")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

func (c *Client) applyEditors(ctx context.Context, req *http.Request, additionalEditors []RequestEditorFn) error {
	for _, r := range c.RequestEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	for _, r := range additionalEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	return nil
}

// ClientWithResponses builds on ClientInterface to offer response payloads
type ClientWithResponses struct {
	ClientInterface
}

// NewClientWithResponses creates a new ClientWithResponses, which wraps
// Client with return type handling
func NewClientWithResponses(server string, opts ...ClientOption) (*ClientWithResponses, error) {
	client, err := NewClient(server, opts...)
	if err != nil {
		return nil, err
	}
	return &ClientWithResponses{client}, nil
}

// WithBaseURL overrides the baseURL.
func WithBaseURL(baseURL string) ClientOption {
	return func(c *Client) error {
		newBaseURL, err := url.Parse(baseURL)
		if err != nil {
			return err
		}
		c.Server = newBaseURL.String()
		return nil
	}
}

// ClientWithResponsesInterface is the interface specification for the client with responses above.
type ClientWithResponsesInterface interface {
	// PostApiArbeitsunfaehigkeitsbescheinigungenValidierungWithBodyWithResponse request with any body
	PostApiArbeitsunfaehigkeitsbescheinigungenValidierungWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiArbeitsunfaehigkeitsbescheinigungenValidierungResponse, error)

	PostApiArbeitsunfaehigkeitsbescheinigungenValidierungWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiArbeitsunfaehigkeitsbescheinigungenValidierungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArbeitsunfaehigkeitsbescheinigungenValidierungResponse, error)

	PostApiArbeitsunfaehigkeitsbescheinigungenValidierungWithResponse(ctx context.Context, body PostApiArbeitsunfaehigkeitsbescheinigungenValidierungJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArbeitsunfaehigkeitsbescheinigungenValidierungResponse, error)

	// PostApiArztbriefeAbrufWithBodyWithResponse request with any body
	PostApiArztbriefeAbrufWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiArztbriefeAbrufResponse, error)

	PostApiArztbriefeAbrufWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiArztbriefeAbrufApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArztbriefeAbrufResponse, error)

	PostApiArztbriefeAbrufWithResponse(ctx context.Context, body PostApiArztbriefeAbrufJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArztbriefeAbrufResponse, error)

	// PostApiArztbriefeAbrufVerfuegbarkeitWithBodyWithResponse request with any body
	PostApiArztbriefeAbrufVerfuegbarkeitWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiArztbriefeAbrufVerfuegbarkeitResponse, error)

	PostApiArztbriefeAbrufVerfuegbarkeitWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiArztbriefeAbrufVerfuegbarkeitApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArztbriefeAbrufVerfuegbarkeitResponse, error)

	PostApiArztbriefeAbrufVerfuegbarkeitWithResponse(ctx context.Context, body PostApiArztbriefeAbrufVerfuegbarkeitJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArztbriefeAbrufVerfuegbarkeitResponse, error)

	// PostApiArztbriefeVersandWithBodyWithResponse request with any body
	PostApiArztbriefeVersandWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiArztbriefeVersandResponse, error)

	PostApiArztbriefeVersandWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiArztbriefeVersandApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArztbriefeVersandResponse, error)

	PostApiArztbriefeVersandWithResponse(ctx context.Context, body PostApiArztbriefeVersandJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArztbriefeVersandResponse, error)

	// PostApiDokumenteAbrufbestaetigungWithBodyWithResponse request with any body
	PostApiDokumenteAbrufbestaetigungWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiDokumenteAbrufbestaetigungResponse, error)

	PostApiDokumenteAbrufbestaetigungWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiDokumenteAbrufbestaetigungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiDokumenteAbrufbestaetigungResponse, error)

	PostApiDokumenteAbrufbestaetigungWithResponse(ctx context.Context, body PostApiDokumenteAbrufbestaetigungJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiDokumenteAbrufbestaetigungResponse, error)

	// PostApiDokumenteEmpfangsstatusWithBodyWithResponse request with any body
	PostApiDokumenteEmpfangsstatusWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiDokumenteEmpfangsstatusResponse, error)

	PostApiDokumenteEmpfangsstatusWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiDokumenteEmpfangsstatusApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiDokumenteEmpfangsstatusResponse, error)

	PostApiDokumenteEmpfangsstatusWithResponse(ctx context.Context, body PostApiDokumenteEmpfangsstatusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiDokumenteEmpfangsstatusResponse, error)

	// PostApiEinweisungsbriefeValidierungWithBodyWithResponse request with any body
	PostApiEinweisungsbriefeValidierungWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiEinweisungsbriefeValidierungResponse, error)

	PostApiEinweisungsbriefeValidierungWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiEinweisungsbriefeValidierungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiEinweisungsbriefeValidierungResponse, error)

	PostApiEinweisungsbriefeValidierungWithResponse(ctx context.Context, body PostApiEinweisungsbriefeValidierungJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiEinweisungsbriefeValidierungResponse, error)

	// PostApiEmpfaengerGruppenWithBodyWithResponse request with any body
	PostApiEmpfaengerGruppenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiEmpfaengerGruppenResponse, error)

	PostApiEmpfaengerGruppenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiEmpfaengerGruppenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiEmpfaengerGruppenResponse, error)

	PostApiEmpfaengerGruppenWithResponse(ctx context.Context, body PostApiEmpfaengerGruppenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiEmpfaengerGruppenResponse, error)

	// PostApiEmpfaengerSucheWithBodyWithResponse request with any body
	PostApiEmpfaengerSucheWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiEmpfaengerSucheResponse, error)

	PostApiEmpfaengerSucheWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiEmpfaengerSucheApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiEmpfaengerSucheResponse, error)

	PostApiEmpfaengerSucheWithResponse(ctx context.Context, body PostApiEmpfaengerSucheJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiEmpfaengerSucheResponse, error)

	// PostApiMedikationsinformationenAbrufWithBodyWithResponse request with any body
	PostApiMedikationsinformationenAbrufWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenAbrufResponse, error)

	PostApiMedikationsinformationenAbrufWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiMedikationsinformationenAbrufApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenAbrufResponse, error)

	PostApiMedikationsinformationenAbrufWithResponse(ctx context.Context, body PostApiMedikationsinformationenAbrufJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenAbrufResponse, error)

	// PostApiMedikationsinformationenErgaenzungenWithBodyWithResponse request with any body
	PostApiMedikationsinformationenErgaenzungenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenErgaenzungenResponse, error)

	PostApiMedikationsinformationenErgaenzungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiMedikationsinformationenErgaenzungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenErgaenzungenResponse, error)

	PostApiMedikationsinformationenErgaenzungenWithResponse(ctx context.Context, body PostApiMedikationsinformationenErgaenzungenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenErgaenzungenResponse, error)

	// PostApiMedikationsinformationenPflegearztWithBodyWithResponse request with any body
	PostApiMedikationsinformationenPflegearztWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenPflegearztResponse, error)

	PostApiMedikationsinformationenPflegearztWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiMedikationsinformationenPflegearztApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenPflegearztResponse, error)

	PostApiMedikationsinformationenPflegearztWithResponse(ctx context.Context, body PostApiMedikationsinformationenPflegearztJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenPflegearztResponse, error)

	// PostApiMedikationsinformationenVersandWithBodyWithResponse request with any body
	PostApiMedikationsinformationenVersandWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenVersandResponse, error)

	PostApiMedikationsinformationenVersandWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiMedikationsinformationenVersandApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenVersandResponse, error)

	PostApiMedikationsinformationenVersandWithResponse(ctx context.Context, body PostApiMedikationsinformationenVersandJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenVersandResponse, error)

	// PostApiPdferzeugungCdadokumentWithBodyWithResponse request with any body
	PostApiPdferzeugungCdadokumentWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiPdferzeugungCdadokumentResponse, error)

	PostApiPdferzeugungCdadokumentWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiPdferzeugungCdadokumentApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiPdferzeugungCdadokumentResponse, error)

	PostApiPdferzeugungCdadokumentWithResponse(ctx context.Context, body PostApiPdferzeugungCdadokumentJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiPdferzeugungCdadokumentResponse, error)

	// PostApiSignaturenErzeugungWithBodyWithResponse request with any body
	PostApiSignaturenErzeugungWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiSignaturenErzeugungResponse, error)

	PostApiSignaturenErzeugungWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiSignaturenErzeugungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiSignaturenErzeugungResponse, error)

	PostApiSignaturenErzeugungWithResponse(ctx context.Context, body PostApiSignaturenErzeugungJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiSignaturenErzeugungResponse, error)

	// PostApiSignaturenPruefungWithBodyWithResponse request with any body
	PostApiSignaturenPruefungWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiSignaturenPruefungResponse, error)

	PostApiSignaturenPruefungWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiSignaturenPruefungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiSignaturenPruefungResponse, error)

	PostApiSignaturenPruefungWithResponse(ctx context.Context, body PostApiSignaturenPruefungJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiSignaturenPruefungResponse, error)

	// PostApiTelekonsileAbrufWithBodyWithResponse request with any body
	PostApiTelekonsileAbrufWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiTelekonsileAbrufResponse, error)

	PostApiTelekonsileAbrufWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiTelekonsileAbrufApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTelekonsileAbrufResponse, error)

	PostApiTelekonsileAbrufWithResponse(ctx context.Context, body PostApiTelekonsileAbrufJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTelekonsileAbrufResponse, error)

	// PostApiTelekonsileBearbeitungsstatusWithBodyWithResponse request with any body
	PostApiTelekonsileBearbeitungsstatusWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiTelekonsileBearbeitungsstatusResponse, error)

	PostApiTelekonsileBearbeitungsstatusWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiTelekonsileBearbeitungsstatusApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTelekonsileBearbeitungsstatusResponse, error)

	PostApiTelekonsileBearbeitungsstatusWithResponse(ctx context.Context, body PostApiTelekonsileBearbeitungsstatusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTelekonsileBearbeitungsstatusResponse, error)

	// PostApiTelekonsileMetainformationenWithBodyWithResponse request with any body
	PostApiTelekonsileMetainformationenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiTelekonsileMetainformationenResponse, error)

	PostApiTelekonsileMetainformationenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiTelekonsileMetainformationenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTelekonsileMetainformationenResponse, error)

	PostApiTelekonsileMetainformationenWithResponse(ctx context.Context, body PostApiTelekonsileMetainformationenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTelekonsileMetainformationenResponse, error)

	// PostApiTelekonsileVersandWithBodyWithResponse request with any body
	PostApiTelekonsileVersandWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiTelekonsileVersandResponse, error)

	PostApiTelekonsileVersandWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiTelekonsileVersandApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTelekonsileVersandResponse, error)

	PostApiTelekonsileVersandWithResponse(ctx context.Context, body PostApiTelekonsileVersandJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTelekonsileVersandResponse, error)

	// PostApiVersichertenteilnahmeWithBodyWithResponse request with any body
	PostApiVersichertenteilnahmeWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiVersichertenteilnahmeResponse, error)

	PostApiVersichertenteilnahmeWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiVersichertenteilnahmeApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiVersichertenteilnahmeResponse, error)

	PostApiVersichertenteilnahmeWithResponse(ctx context.Context, body PostApiVersichertenteilnahmeJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiVersichertenteilnahmeResponse, error)

	// PostApiZertifikateAnalyseWithBodyWithResponse request with any body
	PostApiZertifikateAnalyseWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiZertifikateAnalyseResponse, error)

	PostApiZertifikateAnalyseWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiZertifikateAnalyseApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiZertifikateAnalyseResponse, error)

	PostApiZertifikateAnalyseWithResponse(ctx context.Context, body PostApiZertifikateAnalyseJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiZertifikateAnalyseResponse, error)

	// PostApiZertifikateErzeugungArztWithBodyWithResponse request with any body
	PostApiZertifikateErzeugungArztWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiZertifikateErzeugungArztResponse, error)

	PostApiZertifikateErzeugungArztWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiZertifikateErzeugungArztApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiZertifikateErzeugungArztResponse, error)

	PostApiZertifikateErzeugungArztWithResponse(ctx context.Context, body PostApiZertifikateErzeugungArztJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiZertifikateErzeugungArztResponse, error)

	// PostApiZertifikateErzeugungBetriebsstaetteWithBodyWithResponse request with any body
	PostApiZertifikateErzeugungBetriebsstaetteWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiZertifikateErzeugungBetriebsstaetteResponse, error)

	PostApiZertifikateErzeugungBetriebsstaetteWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiZertifikateErzeugungBetriebsstaetteApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiZertifikateErzeugungBetriebsstaetteResponse, error)

	PostApiZertifikateErzeugungBetriebsstaetteWithResponse(ctx context.Context, body PostApiZertifikateErzeugungBetriebsstaetteJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiZertifikateErzeugungBetriebsstaetteResponse, error)

	// PostApiZertifikateInformationenWithBodyWithResponse request with any body
	PostApiZertifikateInformationenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiZertifikateInformationenResponse, error)

	PostApiZertifikateInformationenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiZertifikateInformationenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiZertifikateInformationenResponse, error)

	PostApiZertifikateInformationenWithResponse(ctx context.Context, body PostApiZertifikateInformationenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiZertifikateInformationenResponse, error)
}

type PostApiArbeitsunfaehigkeitsbescheinigungenValidierungResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ValidiereAuResultat
}

// Status returns HTTPResponse.Status
func (r PostApiArbeitsunfaehigkeitsbescheinigungenValidierungResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiArbeitsunfaehigkeitsbescheinigungenValidierungResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiArztbriefeAbrufResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *LiefereArztbriefeResultat
}

// Status returns HTTPResponse.Status
func (r PostApiArztbriefeAbrufResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiArztbriefeAbrufResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiArztbriefeAbrufVerfuegbarkeitResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *PruefeAufBereitgestellteArztbriefeResultat
}

// Status returns HTTPResponse.Status
func (r PostApiArztbriefeAbrufVerfuegbarkeitResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiArztbriefeAbrufVerfuegbarkeitResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiArztbriefeVersandResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *SendeArztbriefResultat
}

// Status returns HTTPResponse.Status
func (r PostApiArztbriefeVersandResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiArztbriefeVersandResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiDokumenteAbrufbestaetigungResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *SendeDokumentenAbrufbestaetigungResultat
}

// Status returns HTTPResponse.Status
func (r PostApiDokumenteAbrufbestaetigungResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiDokumenteAbrufbestaetigungResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiDokumenteEmpfangsstatusResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *LiefereDokumentenEmpfangsstatusResultat
}

// Status returns HTTPResponse.Status
func (r PostApiDokumenteEmpfangsstatusResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiDokumenteEmpfangsstatusResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiEinweisungsbriefeValidierungResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ValidiereEinweisungsbriefResultat
}

// Status returns HTTPResponse.Status
func (r PostApiEinweisungsbriefeValidierungResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiEinweisungsbriefeValidierungResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiEmpfaengerGruppenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *LiefereEmpfaengergruppenVernetzungResultat
}

// Status returns HTTPResponse.Status
func (r PostApiEmpfaengerGruppenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiEmpfaengerGruppenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiEmpfaengerSucheResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *SucheEmpfaengerVernetzungResultat
}

// Status returns HTTPResponse.Status
func (r PostApiEmpfaengerSucheResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiEmpfaengerSucheResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiMedikationsinformationenAbrufResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *LiefereMedikationsinformationResultat
}

// Status returns HTTPResponse.Status
func (r PostApiMedikationsinformationenAbrufResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiMedikationsinformationenAbrufResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiMedikationsinformationenErgaenzungenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *LiefereMedikationsinformationErgaenzungenResultat
}

// Status returns HTTPResponse.Status
func (r PostApiMedikationsinformationenErgaenzungenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiMedikationsinformationenErgaenzungenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiMedikationsinformationenPflegearztResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *SendeMedikationsinformationPflegearztResultat
}

// Status returns HTTPResponse.Status
func (r PostApiMedikationsinformationenPflegearztResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiMedikationsinformationenPflegearztResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiMedikationsinformationenVersandResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *SendeMedikationsinformationResultat
}

// Status returns HTTPResponse.Status
func (r PostApiMedikationsinformationenVersandResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiMedikationsinformationenVersandResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiPdferzeugungCdadokumentResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ErzeugePdfAusCdaDokumentResultat
}

// Status returns HTTPResponse.Status
func (r PostApiPdferzeugungCdadokumentResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiPdferzeugungCdadokumentResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiSignaturenErzeugungResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *SigniereCdaDokumentResultat
}

// Status returns HTTPResponse.Status
func (r PostApiSignaturenErzeugungResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiSignaturenErzeugungResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiSignaturenPruefungResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *PruefeSignaturCdaDokumentResultat
}

// Status returns HTTPResponse.Status
func (r PostApiSignaturenPruefungResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiSignaturenPruefungResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiTelekonsileAbrufResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *LiefereTelekonsileResultat
}

// Status returns HTTPResponse.Status
func (r PostApiTelekonsileAbrufResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiTelekonsileAbrufResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiTelekonsileBearbeitungsstatusResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *LiefereBearbeitungsstatusTelekonsileResultat
}

// Status returns HTTPResponse.Status
func (r PostApiTelekonsileBearbeitungsstatusResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiTelekonsileBearbeitungsstatusResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiTelekonsileMetainformationenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *LiefereTelekonsilMetainformationenResultat
}

// Status returns HTTPResponse.Status
func (r PostApiTelekonsileMetainformationenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiTelekonsileMetainformationenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiTelekonsileVersandResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *SendeTelekonsilResultat
}

// Status returns HTTPResponse.Status
func (r PostApiTelekonsileVersandResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiTelekonsileVersandResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiVersichertenteilnahmeResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *PruefeVersichertenteilnahmeVernetzungResultat
}

// Status returns HTTPResponse.Status
func (r PostApiVersichertenteilnahmeResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiVersichertenteilnahmeResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiZertifikateAnalyseResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ExtrahiereZertifikatsinformationVernetzungResultat
}

// Status returns HTTPResponse.Status
func (r PostApiZertifikateAnalyseResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiZertifikateAnalyseResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiZertifikateErzeugungArztResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ErzeugeZertifikatArztResultat
}

// Status returns HTTPResponse.Status
func (r PostApiZertifikateErzeugungArztResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiZertifikateErzeugungArztResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiZertifikateErzeugungBetriebsstaetteResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ErzeugeZertifikatBetriebsstaetteResultat
}

// Status returns HTTPResponse.Status
func (r PostApiZertifikateErzeugungBetriebsstaetteResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiZertifikateErzeugungBetriebsstaetteResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiZertifikateInformationenResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *LiefereZertifikatinformationVernetzungResultat
}

// Status returns HTTPResponse.Status
func (r PostApiZertifikateInformationenResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiZertifikateInformationenResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

// PostApiArbeitsunfaehigkeitsbescheinigungenValidierungWithBodyWithResponse request with arbitrary body returning *PostApiArbeitsunfaehigkeitsbescheinigungenValidierungResponse
func (c *ClientWithResponses) PostApiArbeitsunfaehigkeitsbescheinigungenValidierungWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiArbeitsunfaehigkeitsbescheinigungenValidierungResponse, error) {
	rsp, err := c.PostApiArbeitsunfaehigkeitsbescheinigungenValidierungWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArbeitsunfaehigkeitsbescheinigungenValidierungResponse(rsp)
}

func (c *ClientWithResponses) PostApiArbeitsunfaehigkeitsbescheinigungenValidierungWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiArbeitsunfaehigkeitsbescheinigungenValidierungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArbeitsunfaehigkeitsbescheinigungenValidierungResponse, error) {
	rsp, err := c.PostApiArbeitsunfaehigkeitsbescheinigungenValidierungWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArbeitsunfaehigkeitsbescheinigungenValidierungResponse(rsp)
}

func (c *ClientWithResponses) PostApiArbeitsunfaehigkeitsbescheinigungenValidierungWithResponse(ctx context.Context, body PostApiArbeitsunfaehigkeitsbescheinigungenValidierungJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArbeitsunfaehigkeitsbescheinigungenValidierungResponse, error) {
	rsp, err := c.PostApiArbeitsunfaehigkeitsbescheinigungenValidierung(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArbeitsunfaehigkeitsbescheinigungenValidierungResponse(rsp)
}

// PostApiArztbriefeAbrufWithBodyWithResponse request with arbitrary body returning *PostApiArztbriefeAbrufResponse
func (c *ClientWithResponses) PostApiArztbriefeAbrufWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiArztbriefeAbrufResponse, error) {
	rsp, err := c.PostApiArztbriefeAbrufWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArztbriefeAbrufResponse(rsp)
}

func (c *ClientWithResponses) PostApiArztbriefeAbrufWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiArztbriefeAbrufApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArztbriefeAbrufResponse, error) {
	rsp, err := c.PostApiArztbriefeAbrufWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArztbriefeAbrufResponse(rsp)
}

func (c *ClientWithResponses) PostApiArztbriefeAbrufWithResponse(ctx context.Context, body PostApiArztbriefeAbrufJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArztbriefeAbrufResponse, error) {
	rsp, err := c.PostApiArztbriefeAbruf(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArztbriefeAbrufResponse(rsp)
}

// PostApiArztbriefeAbrufVerfuegbarkeitWithBodyWithResponse request with arbitrary body returning *PostApiArztbriefeAbrufVerfuegbarkeitResponse
func (c *ClientWithResponses) PostApiArztbriefeAbrufVerfuegbarkeitWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiArztbriefeAbrufVerfuegbarkeitResponse, error) {
	rsp, err := c.PostApiArztbriefeAbrufVerfuegbarkeitWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArztbriefeAbrufVerfuegbarkeitResponse(rsp)
}

func (c *ClientWithResponses) PostApiArztbriefeAbrufVerfuegbarkeitWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiArztbriefeAbrufVerfuegbarkeitApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArztbriefeAbrufVerfuegbarkeitResponse, error) {
	rsp, err := c.PostApiArztbriefeAbrufVerfuegbarkeitWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArztbriefeAbrufVerfuegbarkeitResponse(rsp)
}

func (c *ClientWithResponses) PostApiArztbriefeAbrufVerfuegbarkeitWithResponse(ctx context.Context, body PostApiArztbriefeAbrufVerfuegbarkeitJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArztbriefeAbrufVerfuegbarkeitResponse, error) {
	rsp, err := c.PostApiArztbriefeAbrufVerfuegbarkeit(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArztbriefeAbrufVerfuegbarkeitResponse(rsp)
}

// PostApiArztbriefeVersandWithBodyWithResponse request with arbitrary body returning *PostApiArztbriefeVersandResponse
func (c *ClientWithResponses) PostApiArztbriefeVersandWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiArztbriefeVersandResponse, error) {
	rsp, err := c.PostApiArztbriefeVersandWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArztbriefeVersandResponse(rsp)
}

func (c *ClientWithResponses) PostApiArztbriefeVersandWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiArztbriefeVersandApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArztbriefeVersandResponse, error) {
	rsp, err := c.PostApiArztbriefeVersandWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArztbriefeVersandResponse(rsp)
}

func (c *ClientWithResponses) PostApiArztbriefeVersandWithResponse(ctx context.Context, body PostApiArztbriefeVersandJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiArztbriefeVersandResponse, error) {
	rsp, err := c.PostApiArztbriefeVersand(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiArztbriefeVersandResponse(rsp)
}

// PostApiDokumenteAbrufbestaetigungWithBodyWithResponse request with arbitrary body returning *PostApiDokumenteAbrufbestaetigungResponse
func (c *ClientWithResponses) PostApiDokumenteAbrufbestaetigungWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiDokumenteAbrufbestaetigungResponse, error) {
	rsp, err := c.PostApiDokumenteAbrufbestaetigungWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiDokumenteAbrufbestaetigungResponse(rsp)
}

func (c *ClientWithResponses) PostApiDokumenteAbrufbestaetigungWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiDokumenteAbrufbestaetigungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiDokumenteAbrufbestaetigungResponse, error) {
	rsp, err := c.PostApiDokumenteAbrufbestaetigungWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiDokumenteAbrufbestaetigungResponse(rsp)
}

func (c *ClientWithResponses) PostApiDokumenteAbrufbestaetigungWithResponse(ctx context.Context, body PostApiDokumenteAbrufbestaetigungJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiDokumenteAbrufbestaetigungResponse, error) {
	rsp, err := c.PostApiDokumenteAbrufbestaetigung(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiDokumenteAbrufbestaetigungResponse(rsp)
}

// PostApiDokumenteEmpfangsstatusWithBodyWithResponse request with arbitrary body returning *PostApiDokumenteEmpfangsstatusResponse
func (c *ClientWithResponses) PostApiDokumenteEmpfangsstatusWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiDokumenteEmpfangsstatusResponse, error) {
	rsp, err := c.PostApiDokumenteEmpfangsstatusWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiDokumenteEmpfangsstatusResponse(rsp)
}

func (c *ClientWithResponses) PostApiDokumenteEmpfangsstatusWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiDokumenteEmpfangsstatusApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiDokumenteEmpfangsstatusResponse, error) {
	rsp, err := c.PostApiDokumenteEmpfangsstatusWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiDokumenteEmpfangsstatusResponse(rsp)
}

func (c *ClientWithResponses) PostApiDokumenteEmpfangsstatusWithResponse(ctx context.Context, body PostApiDokumenteEmpfangsstatusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiDokumenteEmpfangsstatusResponse, error) {
	rsp, err := c.PostApiDokumenteEmpfangsstatus(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiDokumenteEmpfangsstatusResponse(rsp)
}

// PostApiEinweisungsbriefeValidierungWithBodyWithResponse request with arbitrary body returning *PostApiEinweisungsbriefeValidierungResponse
func (c *ClientWithResponses) PostApiEinweisungsbriefeValidierungWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiEinweisungsbriefeValidierungResponse, error) {
	rsp, err := c.PostApiEinweisungsbriefeValidierungWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiEinweisungsbriefeValidierungResponse(rsp)
}

func (c *ClientWithResponses) PostApiEinweisungsbriefeValidierungWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiEinweisungsbriefeValidierungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiEinweisungsbriefeValidierungResponse, error) {
	rsp, err := c.PostApiEinweisungsbriefeValidierungWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiEinweisungsbriefeValidierungResponse(rsp)
}

func (c *ClientWithResponses) PostApiEinweisungsbriefeValidierungWithResponse(ctx context.Context, body PostApiEinweisungsbriefeValidierungJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiEinweisungsbriefeValidierungResponse, error) {
	rsp, err := c.PostApiEinweisungsbriefeValidierung(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiEinweisungsbriefeValidierungResponse(rsp)
}

// PostApiEmpfaengerGruppenWithBodyWithResponse request with arbitrary body returning *PostApiEmpfaengerGruppenResponse
func (c *ClientWithResponses) PostApiEmpfaengerGruppenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiEmpfaengerGruppenResponse, error) {
	rsp, err := c.PostApiEmpfaengerGruppenWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiEmpfaengerGruppenResponse(rsp)
}

func (c *ClientWithResponses) PostApiEmpfaengerGruppenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiEmpfaengerGruppenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiEmpfaengerGruppenResponse, error) {
	rsp, err := c.PostApiEmpfaengerGruppenWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiEmpfaengerGruppenResponse(rsp)
}

func (c *ClientWithResponses) PostApiEmpfaengerGruppenWithResponse(ctx context.Context, body PostApiEmpfaengerGruppenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiEmpfaengerGruppenResponse, error) {
	rsp, err := c.PostApiEmpfaengerGruppen(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiEmpfaengerGruppenResponse(rsp)
}

// PostApiEmpfaengerSucheWithBodyWithResponse request with arbitrary body returning *PostApiEmpfaengerSucheResponse
func (c *ClientWithResponses) PostApiEmpfaengerSucheWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiEmpfaengerSucheResponse, error) {
	rsp, err := c.PostApiEmpfaengerSucheWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiEmpfaengerSucheResponse(rsp)
}

func (c *ClientWithResponses) PostApiEmpfaengerSucheWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiEmpfaengerSucheApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiEmpfaengerSucheResponse, error) {
	rsp, err := c.PostApiEmpfaengerSucheWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiEmpfaengerSucheResponse(rsp)
}

func (c *ClientWithResponses) PostApiEmpfaengerSucheWithResponse(ctx context.Context, body PostApiEmpfaengerSucheJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiEmpfaengerSucheResponse, error) {
	rsp, err := c.PostApiEmpfaengerSuche(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiEmpfaengerSucheResponse(rsp)
}

// PostApiMedikationsinformationenAbrufWithBodyWithResponse request with arbitrary body returning *PostApiMedikationsinformationenAbrufResponse
func (c *ClientWithResponses) PostApiMedikationsinformationenAbrufWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenAbrufResponse, error) {
	rsp, err := c.PostApiMedikationsinformationenAbrufWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiMedikationsinformationenAbrufResponse(rsp)
}

func (c *ClientWithResponses) PostApiMedikationsinformationenAbrufWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiMedikationsinformationenAbrufApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenAbrufResponse, error) {
	rsp, err := c.PostApiMedikationsinformationenAbrufWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiMedikationsinformationenAbrufResponse(rsp)
}

func (c *ClientWithResponses) PostApiMedikationsinformationenAbrufWithResponse(ctx context.Context, body PostApiMedikationsinformationenAbrufJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenAbrufResponse, error) {
	rsp, err := c.PostApiMedikationsinformationenAbruf(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiMedikationsinformationenAbrufResponse(rsp)
}

// PostApiMedikationsinformationenErgaenzungenWithBodyWithResponse request with arbitrary body returning *PostApiMedikationsinformationenErgaenzungenResponse
func (c *ClientWithResponses) PostApiMedikationsinformationenErgaenzungenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenErgaenzungenResponse, error) {
	rsp, err := c.PostApiMedikationsinformationenErgaenzungenWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiMedikationsinformationenErgaenzungenResponse(rsp)
}

func (c *ClientWithResponses) PostApiMedikationsinformationenErgaenzungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiMedikationsinformationenErgaenzungenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenErgaenzungenResponse, error) {
	rsp, err := c.PostApiMedikationsinformationenErgaenzungenWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiMedikationsinformationenErgaenzungenResponse(rsp)
}

func (c *ClientWithResponses) PostApiMedikationsinformationenErgaenzungenWithResponse(ctx context.Context, body PostApiMedikationsinformationenErgaenzungenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenErgaenzungenResponse, error) {
	rsp, err := c.PostApiMedikationsinformationenErgaenzungen(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiMedikationsinformationenErgaenzungenResponse(rsp)
}

// PostApiMedikationsinformationenPflegearztWithBodyWithResponse request with arbitrary body returning *PostApiMedikationsinformationenPflegearztResponse
func (c *ClientWithResponses) PostApiMedikationsinformationenPflegearztWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenPflegearztResponse, error) {
	rsp, err := c.PostApiMedikationsinformationenPflegearztWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiMedikationsinformationenPflegearztResponse(rsp)
}

func (c *ClientWithResponses) PostApiMedikationsinformationenPflegearztWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiMedikationsinformationenPflegearztApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenPflegearztResponse, error) {
	rsp, err := c.PostApiMedikationsinformationenPflegearztWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiMedikationsinformationenPflegearztResponse(rsp)
}

func (c *ClientWithResponses) PostApiMedikationsinformationenPflegearztWithResponse(ctx context.Context, body PostApiMedikationsinformationenPflegearztJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenPflegearztResponse, error) {
	rsp, err := c.PostApiMedikationsinformationenPflegearzt(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiMedikationsinformationenPflegearztResponse(rsp)
}

// PostApiMedikationsinformationenVersandWithBodyWithResponse request with arbitrary body returning *PostApiMedikationsinformationenVersandResponse
func (c *ClientWithResponses) PostApiMedikationsinformationenVersandWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenVersandResponse, error) {
	rsp, err := c.PostApiMedikationsinformationenVersandWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiMedikationsinformationenVersandResponse(rsp)
}

func (c *ClientWithResponses) PostApiMedikationsinformationenVersandWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiMedikationsinformationenVersandApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenVersandResponse, error) {
	rsp, err := c.PostApiMedikationsinformationenVersandWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiMedikationsinformationenVersandResponse(rsp)
}

func (c *ClientWithResponses) PostApiMedikationsinformationenVersandWithResponse(ctx context.Context, body PostApiMedikationsinformationenVersandJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiMedikationsinformationenVersandResponse, error) {
	rsp, err := c.PostApiMedikationsinformationenVersand(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiMedikationsinformationenVersandResponse(rsp)
}

// PostApiPdferzeugungCdadokumentWithBodyWithResponse request with arbitrary body returning *PostApiPdferzeugungCdadokumentResponse
func (c *ClientWithResponses) PostApiPdferzeugungCdadokumentWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiPdferzeugungCdadokumentResponse, error) {
	rsp, err := c.PostApiPdferzeugungCdadokumentWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiPdferzeugungCdadokumentResponse(rsp)
}

func (c *ClientWithResponses) PostApiPdferzeugungCdadokumentWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiPdferzeugungCdadokumentApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiPdferzeugungCdadokumentResponse, error) {
	rsp, err := c.PostApiPdferzeugungCdadokumentWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiPdferzeugungCdadokumentResponse(rsp)
}

func (c *ClientWithResponses) PostApiPdferzeugungCdadokumentWithResponse(ctx context.Context, body PostApiPdferzeugungCdadokumentJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiPdferzeugungCdadokumentResponse, error) {
	rsp, err := c.PostApiPdferzeugungCdadokument(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiPdferzeugungCdadokumentResponse(rsp)
}

// PostApiSignaturenErzeugungWithBodyWithResponse request with arbitrary body returning *PostApiSignaturenErzeugungResponse
func (c *ClientWithResponses) PostApiSignaturenErzeugungWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiSignaturenErzeugungResponse, error) {
	rsp, err := c.PostApiSignaturenErzeugungWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiSignaturenErzeugungResponse(rsp)
}

func (c *ClientWithResponses) PostApiSignaturenErzeugungWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiSignaturenErzeugungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiSignaturenErzeugungResponse, error) {
	rsp, err := c.PostApiSignaturenErzeugungWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiSignaturenErzeugungResponse(rsp)
}

func (c *ClientWithResponses) PostApiSignaturenErzeugungWithResponse(ctx context.Context, body PostApiSignaturenErzeugungJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiSignaturenErzeugungResponse, error) {
	rsp, err := c.PostApiSignaturenErzeugung(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiSignaturenErzeugungResponse(rsp)
}

// PostApiSignaturenPruefungWithBodyWithResponse request with arbitrary body returning *PostApiSignaturenPruefungResponse
func (c *ClientWithResponses) PostApiSignaturenPruefungWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiSignaturenPruefungResponse, error) {
	rsp, err := c.PostApiSignaturenPruefungWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiSignaturenPruefungResponse(rsp)
}

func (c *ClientWithResponses) PostApiSignaturenPruefungWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiSignaturenPruefungApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiSignaturenPruefungResponse, error) {
	rsp, err := c.PostApiSignaturenPruefungWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiSignaturenPruefungResponse(rsp)
}

func (c *ClientWithResponses) PostApiSignaturenPruefungWithResponse(ctx context.Context, body PostApiSignaturenPruefungJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiSignaturenPruefungResponse, error) {
	rsp, err := c.PostApiSignaturenPruefung(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiSignaturenPruefungResponse(rsp)
}

// PostApiTelekonsileAbrufWithBodyWithResponse request with arbitrary body returning *PostApiTelekonsileAbrufResponse
func (c *ClientWithResponses) PostApiTelekonsileAbrufWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiTelekonsileAbrufResponse, error) {
	rsp, err := c.PostApiTelekonsileAbrufWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiTelekonsileAbrufResponse(rsp)
}

func (c *ClientWithResponses) PostApiTelekonsileAbrufWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiTelekonsileAbrufApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTelekonsileAbrufResponse, error) {
	rsp, err := c.PostApiTelekonsileAbrufWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiTelekonsileAbrufResponse(rsp)
}

func (c *ClientWithResponses) PostApiTelekonsileAbrufWithResponse(ctx context.Context, body PostApiTelekonsileAbrufJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTelekonsileAbrufResponse, error) {
	rsp, err := c.PostApiTelekonsileAbruf(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiTelekonsileAbrufResponse(rsp)
}

// PostApiTelekonsileBearbeitungsstatusWithBodyWithResponse request with arbitrary body returning *PostApiTelekonsileBearbeitungsstatusResponse
func (c *ClientWithResponses) PostApiTelekonsileBearbeitungsstatusWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiTelekonsileBearbeitungsstatusResponse, error) {
	rsp, err := c.PostApiTelekonsileBearbeitungsstatusWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiTelekonsileBearbeitungsstatusResponse(rsp)
}

func (c *ClientWithResponses) PostApiTelekonsileBearbeitungsstatusWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiTelekonsileBearbeitungsstatusApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTelekonsileBearbeitungsstatusResponse, error) {
	rsp, err := c.PostApiTelekonsileBearbeitungsstatusWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiTelekonsileBearbeitungsstatusResponse(rsp)
}

func (c *ClientWithResponses) PostApiTelekonsileBearbeitungsstatusWithResponse(ctx context.Context, body PostApiTelekonsileBearbeitungsstatusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTelekonsileBearbeitungsstatusResponse, error) {
	rsp, err := c.PostApiTelekonsileBearbeitungsstatus(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiTelekonsileBearbeitungsstatusResponse(rsp)
}

// PostApiTelekonsileMetainformationenWithBodyWithResponse request with arbitrary body returning *PostApiTelekonsileMetainformationenResponse
func (c *ClientWithResponses) PostApiTelekonsileMetainformationenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiTelekonsileMetainformationenResponse, error) {
	rsp, err := c.PostApiTelekonsileMetainformationenWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiTelekonsileMetainformationenResponse(rsp)
}

func (c *ClientWithResponses) PostApiTelekonsileMetainformationenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiTelekonsileMetainformationenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTelekonsileMetainformationenResponse, error) {
	rsp, err := c.PostApiTelekonsileMetainformationenWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiTelekonsileMetainformationenResponse(rsp)
}

func (c *ClientWithResponses) PostApiTelekonsileMetainformationenWithResponse(ctx context.Context, body PostApiTelekonsileMetainformationenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTelekonsileMetainformationenResponse, error) {
	rsp, err := c.PostApiTelekonsileMetainformationen(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiTelekonsileMetainformationenResponse(rsp)
}

// PostApiTelekonsileVersandWithBodyWithResponse request with arbitrary body returning *PostApiTelekonsileVersandResponse
func (c *ClientWithResponses) PostApiTelekonsileVersandWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiTelekonsileVersandResponse, error) {
	rsp, err := c.PostApiTelekonsileVersandWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiTelekonsileVersandResponse(rsp)
}

func (c *ClientWithResponses) PostApiTelekonsileVersandWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiTelekonsileVersandApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTelekonsileVersandResponse, error) {
	rsp, err := c.PostApiTelekonsileVersandWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiTelekonsileVersandResponse(rsp)
}

func (c *ClientWithResponses) PostApiTelekonsileVersandWithResponse(ctx context.Context, body PostApiTelekonsileVersandJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiTelekonsileVersandResponse, error) {
	rsp, err := c.PostApiTelekonsileVersand(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiTelekonsileVersandResponse(rsp)
}

// PostApiVersichertenteilnahmeWithBodyWithResponse request with arbitrary body returning *PostApiVersichertenteilnahmeResponse
func (c *ClientWithResponses) PostApiVersichertenteilnahmeWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiVersichertenteilnahmeResponse, error) {
	rsp, err := c.PostApiVersichertenteilnahmeWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiVersichertenteilnahmeResponse(rsp)
}

func (c *ClientWithResponses) PostApiVersichertenteilnahmeWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiVersichertenteilnahmeApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiVersichertenteilnahmeResponse, error) {
	rsp, err := c.PostApiVersichertenteilnahmeWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiVersichertenteilnahmeResponse(rsp)
}

func (c *ClientWithResponses) PostApiVersichertenteilnahmeWithResponse(ctx context.Context, body PostApiVersichertenteilnahmeJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiVersichertenteilnahmeResponse, error) {
	rsp, err := c.PostApiVersichertenteilnahme(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiVersichertenteilnahmeResponse(rsp)
}

// PostApiZertifikateAnalyseWithBodyWithResponse request with arbitrary body returning *PostApiZertifikateAnalyseResponse
func (c *ClientWithResponses) PostApiZertifikateAnalyseWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiZertifikateAnalyseResponse, error) {
	rsp, err := c.PostApiZertifikateAnalyseWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiZertifikateAnalyseResponse(rsp)
}

func (c *ClientWithResponses) PostApiZertifikateAnalyseWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiZertifikateAnalyseApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiZertifikateAnalyseResponse, error) {
	rsp, err := c.PostApiZertifikateAnalyseWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiZertifikateAnalyseResponse(rsp)
}

func (c *ClientWithResponses) PostApiZertifikateAnalyseWithResponse(ctx context.Context, body PostApiZertifikateAnalyseJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiZertifikateAnalyseResponse, error) {
	rsp, err := c.PostApiZertifikateAnalyse(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiZertifikateAnalyseResponse(rsp)
}

// PostApiZertifikateErzeugungArztWithBodyWithResponse request with arbitrary body returning *PostApiZertifikateErzeugungArztResponse
func (c *ClientWithResponses) PostApiZertifikateErzeugungArztWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiZertifikateErzeugungArztResponse, error) {
	rsp, err := c.PostApiZertifikateErzeugungArztWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiZertifikateErzeugungArztResponse(rsp)
}

func (c *ClientWithResponses) PostApiZertifikateErzeugungArztWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiZertifikateErzeugungArztApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiZertifikateErzeugungArztResponse, error) {
	rsp, err := c.PostApiZertifikateErzeugungArztWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiZertifikateErzeugungArztResponse(rsp)
}

func (c *ClientWithResponses) PostApiZertifikateErzeugungArztWithResponse(ctx context.Context, body PostApiZertifikateErzeugungArztJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiZertifikateErzeugungArztResponse, error) {
	rsp, err := c.PostApiZertifikateErzeugungArzt(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiZertifikateErzeugungArztResponse(rsp)
}

// PostApiZertifikateErzeugungBetriebsstaetteWithBodyWithResponse request with arbitrary body returning *PostApiZertifikateErzeugungBetriebsstaetteResponse
func (c *ClientWithResponses) PostApiZertifikateErzeugungBetriebsstaetteWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiZertifikateErzeugungBetriebsstaetteResponse, error) {
	rsp, err := c.PostApiZertifikateErzeugungBetriebsstaetteWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiZertifikateErzeugungBetriebsstaetteResponse(rsp)
}

func (c *ClientWithResponses) PostApiZertifikateErzeugungBetriebsstaetteWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiZertifikateErzeugungBetriebsstaetteApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiZertifikateErzeugungBetriebsstaetteResponse, error) {
	rsp, err := c.PostApiZertifikateErzeugungBetriebsstaetteWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiZertifikateErzeugungBetriebsstaetteResponse(rsp)
}

func (c *ClientWithResponses) PostApiZertifikateErzeugungBetriebsstaetteWithResponse(ctx context.Context, body PostApiZertifikateErzeugungBetriebsstaetteJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiZertifikateErzeugungBetriebsstaetteResponse, error) {
	rsp, err := c.PostApiZertifikateErzeugungBetriebsstaette(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiZertifikateErzeugungBetriebsstaetteResponse(rsp)
}

// PostApiZertifikateInformationenWithBodyWithResponse request with arbitrary body returning *PostApiZertifikateInformationenResponse
func (c *ClientWithResponses) PostApiZertifikateInformationenWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PostApiZertifikateInformationenResponse, error) {
	rsp, err := c.PostApiZertifikateInformationenWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiZertifikateInformationenResponse(rsp)
}

func (c *ClientWithResponses) PostApiZertifikateInformationenWithApplicationWildcardPlusJSONBodyWithResponse(ctx context.Context, body PostApiZertifikateInformationenApplicationWildcardPlusJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiZertifikateInformationenResponse, error) {
	rsp, err := c.PostApiZertifikateInformationenWithApplicationWildcardPlusJSONBody(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiZertifikateInformationenResponse(rsp)
}

func (c *ClientWithResponses) PostApiZertifikateInformationenWithResponse(ctx context.Context, body PostApiZertifikateInformationenJSONRequestBody, reqEditors ...RequestEditorFn) (*PostApiZertifikateInformationenResponse, error) {
	rsp, err := c.PostApiZertifikateInformationen(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiZertifikateInformationenResponse(rsp)
}

// ParsePostApiArbeitsunfaehigkeitsbescheinigungenValidierungResponse parses an HTTP response from a PostApiArbeitsunfaehigkeitsbescheinigungenValidierungWithResponse call
func ParsePostApiArbeitsunfaehigkeitsbescheinigungenValidierungResponse(rsp *http.Response) (*PostApiArbeitsunfaehigkeitsbescheinigungenValidierungResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiArbeitsunfaehigkeitsbescheinigungenValidierungResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ValidiereAuResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiArztbriefeAbrufResponse parses an HTTP response from a PostApiArztbriefeAbrufWithResponse call
func ParsePostApiArztbriefeAbrufResponse(rsp *http.Response) (*PostApiArztbriefeAbrufResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiArztbriefeAbrufResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest LiefereArztbriefeResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiArztbriefeAbrufVerfuegbarkeitResponse parses an HTTP response from a PostApiArztbriefeAbrufVerfuegbarkeitWithResponse call
func ParsePostApiArztbriefeAbrufVerfuegbarkeitResponse(rsp *http.Response) (*PostApiArztbriefeAbrufVerfuegbarkeitResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiArztbriefeAbrufVerfuegbarkeitResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest PruefeAufBereitgestellteArztbriefeResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiArztbriefeVersandResponse parses an HTTP response from a PostApiArztbriefeVersandWithResponse call
func ParsePostApiArztbriefeVersandResponse(rsp *http.Response) (*PostApiArztbriefeVersandResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiArztbriefeVersandResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest SendeArztbriefResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiDokumenteAbrufbestaetigungResponse parses an HTTP response from a PostApiDokumenteAbrufbestaetigungWithResponse call
func ParsePostApiDokumenteAbrufbestaetigungResponse(rsp *http.Response) (*PostApiDokumenteAbrufbestaetigungResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiDokumenteAbrufbestaetigungResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest SendeDokumentenAbrufbestaetigungResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiDokumenteEmpfangsstatusResponse parses an HTTP response from a PostApiDokumenteEmpfangsstatusWithResponse call
func ParsePostApiDokumenteEmpfangsstatusResponse(rsp *http.Response) (*PostApiDokumenteEmpfangsstatusResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiDokumenteEmpfangsstatusResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest LiefereDokumentenEmpfangsstatusResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiEinweisungsbriefeValidierungResponse parses an HTTP response from a PostApiEinweisungsbriefeValidierungWithResponse call
func ParsePostApiEinweisungsbriefeValidierungResponse(rsp *http.Response) (*PostApiEinweisungsbriefeValidierungResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiEinweisungsbriefeValidierungResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ValidiereEinweisungsbriefResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiEmpfaengerGruppenResponse parses an HTTP response from a PostApiEmpfaengerGruppenWithResponse call
func ParsePostApiEmpfaengerGruppenResponse(rsp *http.Response) (*PostApiEmpfaengerGruppenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiEmpfaengerGruppenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest LiefereEmpfaengergruppenVernetzungResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiEmpfaengerSucheResponse parses an HTTP response from a PostApiEmpfaengerSucheWithResponse call
func ParsePostApiEmpfaengerSucheResponse(rsp *http.Response) (*PostApiEmpfaengerSucheResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiEmpfaengerSucheResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest SucheEmpfaengerVernetzungResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiMedikationsinformationenAbrufResponse parses an HTTP response from a PostApiMedikationsinformationenAbrufWithResponse call
func ParsePostApiMedikationsinformationenAbrufResponse(rsp *http.Response) (*PostApiMedikationsinformationenAbrufResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiMedikationsinformationenAbrufResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest LiefereMedikationsinformationResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiMedikationsinformationenErgaenzungenResponse parses an HTTP response from a PostApiMedikationsinformationenErgaenzungenWithResponse call
func ParsePostApiMedikationsinformationenErgaenzungenResponse(rsp *http.Response) (*PostApiMedikationsinformationenErgaenzungenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiMedikationsinformationenErgaenzungenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest LiefereMedikationsinformationErgaenzungenResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiMedikationsinformationenPflegearztResponse parses an HTTP response from a PostApiMedikationsinformationenPflegearztWithResponse call
func ParsePostApiMedikationsinformationenPflegearztResponse(rsp *http.Response) (*PostApiMedikationsinformationenPflegearztResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiMedikationsinformationenPflegearztResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest SendeMedikationsinformationPflegearztResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiMedikationsinformationenVersandResponse parses an HTTP response from a PostApiMedikationsinformationenVersandWithResponse call
func ParsePostApiMedikationsinformationenVersandResponse(rsp *http.Response) (*PostApiMedikationsinformationenVersandResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiMedikationsinformationenVersandResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest SendeMedikationsinformationResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiPdferzeugungCdadokumentResponse parses an HTTP response from a PostApiPdferzeugungCdadokumentWithResponse call
func ParsePostApiPdferzeugungCdadokumentResponse(rsp *http.Response) (*PostApiPdferzeugungCdadokumentResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiPdferzeugungCdadokumentResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ErzeugePdfAusCdaDokumentResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiSignaturenErzeugungResponse parses an HTTP response from a PostApiSignaturenErzeugungWithResponse call
func ParsePostApiSignaturenErzeugungResponse(rsp *http.Response) (*PostApiSignaturenErzeugungResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiSignaturenErzeugungResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest SigniereCdaDokumentResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiSignaturenPruefungResponse parses an HTTP response from a PostApiSignaturenPruefungWithResponse call
func ParsePostApiSignaturenPruefungResponse(rsp *http.Response) (*PostApiSignaturenPruefungResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiSignaturenPruefungResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest PruefeSignaturCdaDokumentResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiTelekonsileAbrufResponse parses an HTTP response from a PostApiTelekonsileAbrufWithResponse call
func ParsePostApiTelekonsileAbrufResponse(rsp *http.Response) (*PostApiTelekonsileAbrufResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiTelekonsileAbrufResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest LiefereTelekonsileResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiTelekonsileBearbeitungsstatusResponse parses an HTTP response from a PostApiTelekonsileBearbeitungsstatusWithResponse call
func ParsePostApiTelekonsileBearbeitungsstatusResponse(rsp *http.Response) (*PostApiTelekonsileBearbeitungsstatusResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiTelekonsileBearbeitungsstatusResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest LiefereBearbeitungsstatusTelekonsileResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiTelekonsileMetainformationenResponse parses an HTTP response from a PostApiTelekonsileMetainformationenWithResponse call
func ParsePostApiTelekonsileMetainformationenResponse(rsp *http.Response) (*PostApiTelekonsileMetainformationenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiTelekonsileMetainformationenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest LiefereTelekonsilMetainformationenResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiTelekonsileVersandResponse parses an HTTP response from a PostApiTelekonsileVersandWithResponse call
func ParsePostApiTelekonsileVersandResponse(rsp *http.Response) (*PostApiTelekonsileVersandResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiTelekonsileVersandResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest SendeTelekonsilResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiVersichertenteilnahmeResponse parses an HTTP response from a PostApiVersichertenteilnahmeWithResponse call
func ParsePostApiVersichertenteilnahmeResponse(rsp *http.Response) (*PostApiVersichertenteilnahmeResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiVersichertenteilnahmeResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest PruefeVersichertenteilnahmeVernetzungResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiZertifikateAnalyseResponse parses an HTTP response from a PostApiZertifikateAnalyseWithResponse call
func ParsePostApiZertifikateAnalyseResponse(rsp *http.Response) (*PostApiZertifikateAnalyseResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiZertifikateAnalyseResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ExtrahiereZertifikatsinformationVernetzungResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiZertifikateErzeugungArztResponse parses an HTTP response from a PostApiZertifikateErzeugungArztWithResponse call
func ParsePostApiZertifikateErzeugungArztResponse(rsp *http.Response) (*PostApiZertifikateErzeugungArztResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiZertifikateErzeugungArztResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ErzeugeZertifikatArztResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiZertifikateErzeugungBetriebsstaetteResponse parses an HTTP response from a PostApiZertifikateErzeugungBetriebsstaetteWithResponse call
func ParsePostApiZertifikateErzeugungBetriebsstaetteResponse(rsp *http.Response) (*PostApiZertifikateErzeugungBetriebsstaetteResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiZertifikateErzeugungBetriebsstaetteResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ErzeugeZertifikatBetriebsstaetteResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}

// ParsePostApiZertifikateInformationenResponse parses an HTTP response from a PostApiZertifikateInformationenWithResponse call
func ParsePostApiZertifikateInformationenResponse(rsp *http.Response) (*PostApiZertifikateInformationenResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiZertifikateInformationenResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest LiefereZertifikatinformationVernetzungResultat
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case rsp.StatusCode == 200:
		// Content-type (text/plain) unsupported

	}

	return response, nil
}
