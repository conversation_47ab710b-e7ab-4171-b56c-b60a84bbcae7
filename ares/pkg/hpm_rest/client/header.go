package hpm_rest_client

import (
	"crypto/sha512"
	"encoding/base64"
	"encoding/json"
	"io"
	"net/http"
	"time"

	"git.tutum.dev/medi/tutum/ares/pkg/config/hpm"
	"github.com/google/uuid"
	"github.com/spf13/viper"
)

type TimestampResponse struct {
	Timestamp string `json:"Timestamp"`
}

func getDateTimeHeaderFromHPM() string {
	localDateTime := time.Now().Format(time.RFC3339)
	url := viper.GetString(hpm.HPM_URL) + "/api/timestamp"

	resp, err := http.Get(url)
	if err != nil || resp.StatusCode != http.StatusOK {
		return localDateTime
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return localDateTime
	}

	var timestampResponse TimestampResponse
	if err := json.Unmarshal(body, &timestampResponse); err != nil {
		return localDateTime
	}

	location, err := time.LoadLocation("Europe/Berlin")
	if err != nil {
		return localDateTime
	}

	dateTime, err := time.ParseInLocation("2006-01-02T15:04:05", timestampResponse.Timestamp, location)
	if err != nil {
		return localDateTime
	}

	return dateTime.Format(time.RFC3339)
}

func genAuthHeaders(secret, systemOid string) map[string]string {
	randHeaderValue := uuid.New().String()
	datetimeHeaderValue := getDateTimeHeaderFromHPM()

	decodedBytes, err := base64.StdEncoding.DecodeString(secret)
	if err != nil {
		panic(err)
	}
	secret = string(decodedBytes)
	// Prepare data for SHA512 hash
	aggregated := secret + systemOid + randHeaderValue + datetimeHeaderValue
	dataBytes := []byte(aggregated)

	// Compute SHA512 hash
	authData := sha512.Sum512(dataBytes)

	// Encode the hash in Base64
	authDataB64 := base64.StdEncoding.EncodeToString(authData[:])

	// Prepare the Authorization header
	userPwB64 := base64.StdEncoding.EncodeToString([]byte(":" + authDataB64))

	return map[string]string{
		"X-Systemoid":                    systemOid,
		"X-Rand":                         randHeaderValue,
		"X-Datetime":                     datetimeHeaderValue,
		"Authorization":                  "Basic " + userPwB64,
		"Accept":                         "application/json",
		"Content-Type":                   "application/json",
		"X-Vertragspartneridentifikator": "H111111111",
	}
}

type CustomTransport struct {
	UnderlyingTransport http.RoundTripper
	Secret              string
	SystemOid           string
}

func (t *CustomTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	authHeaders := genAuthHeaders(t.Secret, t.SystemOid)
	for key, value := range authHeaders {
		req.Header.Add(key, value)
	}

	return t.UnderlyingTransport.RoundTrip(req)
}
