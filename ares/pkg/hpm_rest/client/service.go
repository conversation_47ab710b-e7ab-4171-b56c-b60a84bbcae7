package hpm_rest_client

import (
	"context"
	"encoding/xml"
	"fmt"
	"html"
	"strconv"
	"time"

	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/pkg/config/hpm"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_copy"
	"git.tutum.dev/medi/tutum/ares/pkg/xml_util"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"

	"git.tutum.dev/medi/tutum/ares/pkg/config/software"
	hpm_rest_amm_client "git.tutum.dev/medi/tutum/ares/pkg/hpm_rest/amm"
	hpm_rest_eav_client "git.tutum.dev/medi/tutum/ares/pkg/hpm_rest/eav"
	hpm_rest_hzv_client "git.tutum.dev/medi/tutum/ares/pkg/hpm_rest/hzv"
	hpm_rest_info_client "git.tutum.dev/medi/tutum/ares/pkg/hpm_rest/info"
	"git.tutum.dev/medi/tutum/ares/pkg/hpm_service/hpm_next"
	"git.tutum.dev/medi/tutum/ares/service/contract/contract"
	contract_model_resource "git.tutum.dev/medi/tutum/ares/service/contract/contract/model"
	profile_service "git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	enrollment_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/enrollment"
	employee_service "git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee"
	feature_flag_service "git.tutum.dev/medi/tutum/ares/service/feature_flag"
	"git.tutum.dev/medi/tutum/ares/service/feature_flag/common"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
)

const (
	HeaderVertragspartneridentifikator  = "X-Vertragspartneridentifikator"
	HeaderVertragspartneridentifikator2 = "vertragspartneridentifikator2"
)

type ServiceRest struct {
	hpmConfig              hpm.HpmConfig
	hpmSoftwareInformation *hpm_next.SoftwareInformation
	clientService          *ClientService
	contractService        *contract.Service
	featureFlagService     *feature_flag_service.FeatureFlagService
}

func (s *ServiceRest) HpmHealCheck(ctx *titan.Context) bool {
	timeoutCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()
	res, err := s.clientService.Info.GetApiHealthWithResponse(timeoutCtx)
	if err != nil || res == nil {
		return false
	}
	if res.StatusCode() != 200 {
		return false
	}
	return slice.EveryBy(*res.JSON200.EndpunktVerfügbarkeit, func(t hpm_rest_info_client.ConnectivityEndPoint) bool {
		return *t.Verfügbar
	})
}

// check hpm connectivity
func (s *ServiceRest) CheckHpmConnectivity(ctx *titan.Context) (*hpm_rest_info_client.CommonResponseStatus, error) {
	timeoutCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	hpmTestMode, err := s.featureFlagService.IsFeatureEnabled(ctx, common.FeatureFlagKey_HPM_TESTMODE)
	if err != nil {
		return nil, err
	}

	testuebermittlung := "false"
	if hpmTestMode {
		testuebermittlung = "true"
	}
	res, err := s.clientService.Info.GetApiKonnektivitaetWithResponse(timeoutCtx, &hpm_rest_info_client.GetApiKonnektivitaetParams{
		Testuebermittlung: testuebermittlung,
	})
	if err != nil {
		return nil, err
	}
	if res.StatusCode() != 200 {
		return nil, errors.New("HPM connectivity check failed")
	}
	if res.JSON200 == nil {
		return nil, errors.New("HPM connectivity check failed")
	}
	return res.JSON200, nil
}

func (s *ServiceRest) GetHavgVpId(ctx *titan.Context, request *GetHavgVpIdRequest) (*GetHavgVpIdResponse, error) {
	havgId, err := strconv.Atoi(request.HavgId)
	if err != nil {
		return nil, err
	}
	req := &hpm_rest_hzv_client.GetApiMigrationenVertragspartneridentifikatorHausarztParams{
		Haevgid: util.NewPointer(int32(havgId)),
	}
	resHavg, err := s.clientService.Hzv.GetApiMigrationenVertragspartneridentifikatorHausarztWithResponse(ctx, req)
	if err != nil {
		return nil, err
	}

	if hpmError := hpm_rest_hzv_client.GetHpmError(resHavg.StatusCode(), resHavg.JSON200); hpmError != nil {
		return nil, hpmError
	}
	return &GetHavgVpIdResponse{
		HavgVpId: util.GetPointerValue(resHavg.JSON200.Vertragspartneridentifikator),
	}, nil
}

func (s *ServiceRest) GetMediVpId(ctx *titan.Context, request *GetMediVpIdRequest) (*GetMediVpIdResponse, error) {
	mediId, err := strconv.Atoi(request.MediId)
	if err != nil {
		return nil, err
	}
	req := &hpm_rest_hzv_client.GetApiMigrationenVertragspartneridentifikatorFacharztParams{
		MediId: util.NewPointer(int32(mediId)),
	}
	resMedi, err := s.clientService.Hzv.GetApiMigrationenVertragspartneridentifikatorFacharztWithResponse(ctx, req)
	if err != nil {
		return nil, err
	}
	if hpmError := hpm_rest_hzv_client.GetHpmError(resMedi.StatusCode(), resMedi.JSON200); hpmError != nil {
		return nil, hpmError
	}
	return &GetMediVpIdResponse{
		MediVpId: util.GetPointerValue(resMedi.JSON200.Vertragspartneridentifikator),
	}, nil
}

func (s *ServiceRest) SendParticipation(ctx *titan.Context,
	enrollment enrollment_repo.PatientEnrollmentEntity,
	patientProfile *profile_service.PatientProfile,
	doctor *employee_service.EmployeeProfile,
	contract *contract_model_resource.Contract,
	ikNumber int32,
	insuranceNumber string,
) (*hpm_rest_hzv_client.TeilnahmeerklaerungResultat, error) {
	if !contract.CheckAvailableHPMFunction(contract_model_resource.HpmFunktionSimpleTyp_SENDE_TEILNAHMEERKLAERUNG, enrollment.AppliedDate) {
		return nil, errors.New(string(error_code.ErrorCode_HpmFunction_Not_Available))
	}
	vertragspartneridentifikator, err := getVertragspartneridentifikator(contract, doctor)
	if err != nil {
		return nil, err
	}

	hpmTestMode, err := s.featureFlagService.IsFeatureEnabled(ctx, common.FeatureFlagKey_HPM_TESTMODE)
	if err != nil {
		return nil, err
	}

	req := hpm_rest_hzv_client.TeilnahmeerklaerungContainer{
		AbsenderBsnr:                 doctor.Bsnr,
		ArztInformationsSystem:       toArztInformationsSystem[hpm_rest_hzv_client.SoftwareInformation](s.hpmSoftwareInformation),
		Testuebermittlung:            hpmTestMode,
		VertragsIdentifikator:        contract.Id,
		Vertragspartneridentifikator: *vertragspartneridentifikator,
		Teilnahmeerklaerungen: []hpm_rest_hzv_client.TeilnahmeerklaerungPatient{
			BuildTeilnahmeerklaerungen(ctx, enrollment, patientProfile, doctor, ikNumber, insuranceNumber),
		},
	}

	headerEditor := WithHeader(HeaderVertragspartneridentifikator, *vertragspartneridentifikator)
	resp, err := s.clientService.Hzv.PostApiVersichertenteilnahmenErklaerungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx, req, hpm_rest_hzv_client.RequestEditorFn(headerEditor))
	if err != nil {
		return nil, err
	}
	if hpmError := hpm_rest_hzv_client.GetHpmError(resp.StatusCode(), resp.JSON200); hpmError != nil {
		return resp.JSON200, hpmError
	}
	return resp.JSON200, nil
}

func getVertragspartneridentifikator(contract *contract_model_resource.Contract, doctor *employee_service.EmployeeProfile) (*string, error) {
	var vertragspartneridentifikator *string
	c := contract.GetContractType()
	if c == nil {
		return nil, errors.New("cannot get contract type")
	}

	if c.TYP == contract_model_resource.VertragtypSimpleTyp_FACHAERZTLICHE_VERSORGUNG {
		vertragspartneridentifikator = doctor.MediverbundVpId
	} else if c.TYP == contract_model_resource.VertragtypSimpleTyp_HAUSARZTZENTRIERTE_VERSORGUNG {
		vertragspartneridentifikator = doctor.HavgVpId
	}
	if util.GetPointerValue(vertragspartneridentifikator) == "" {
		return nil, errors.New("doctor does not have a valid vertragspartneridentifikator")
	}
	return vertragspartneridentifikator, nil
}

// func (s *ServiceRest) SendMultiplePatientParticipation(
// 	ctx *titan.Context,
// 	enrollments []enrollment_repo.PatientEnrollmentEntity,
// 	patientProfiles []*profile_service.PatientProfile,
// 	doctor *employee_service.EmployeeProfile,
// 	contract contract_model_resource.Contract,
// ) (*hpm_rest_hzv_client.TeilnahmeerklaerungResultat, error) {
// 	teilnahmeerklaerungen := []hpm_rest_hzv_client.TeilnahmeerklaerungPatient{}
// 	startDate := util.NowUnixMillis(ctx)
// 	for _, e := range enrollments {
// 		foundPatient := slice.FindOne(patientProfiles, func(t *profile_service.PatientProfile) bool {
// 			return t != nil && t.Id.String() == e.PatientId.String()
// 		})
// 		patient := *foundPatient
// 		payload := BuildTeilnahmeerklaerungen(ctx, e, patient, doctor, patient.PatientInfo.GetActiveInsurance().IkNumber, *patient.PatientInfo.GetActiveInsurance().InsuranceNumber)
// 		teilnahmeerklaerungen = append(teilnahmeerklaerungen, payload)
// 		if e.StartDate != 0 && e.StartDate < startDate {
// 			startDate = e.StartDate
// 		}
// 	}
// 	if !contract.CheckAvailableHPMFunction(contract_model_resource.HpmFunktionSimpleTyp_SENDE_TEILNAHMEERKLAERUNG, startDate) {
// 		return nil, errors.New(string(error_code.ErrorCode_HpmFunction_Not_Available))
// 	}
// 	vertragspartneridentifikator, err := getVertragspartneridentifikator(&contract, doctor)
// 	if err != nil {
// 		return nil, err
// 	}

// 	hpmTestMode, err := s.featureFlagService.IsFeatureEnabled(ctx, common.FeatureFlagKey_HPM_TESTMODE)
// 	if err != nil {
// 		return nil, err
// 	}

// 	req := hpm_rest_hzv_client.TeilnahmeerklaerungContainer{
// 		AbsenderBsnr:                 doctor.Bsnr,
// 		ArztInformationsSystem:       toArztInformationsSystem[hpm_rest_hzv_client.SoftwareInformation](s.hpmSoftwareInformation),
// 		Testuebermittlung:            hpmTestMode,
// 		VertragsIdentifikator:        contract.Id,
// 		Vertragspartneridentifikator: *vertragspartneridentifikator,
// 		Teilnahmeerklaerungen:        teilnahmeerklaerungen,
// 	}
// 	headerEditor := WithHeader(HeaderVertragspartneridentifikator, *vertragspartneridentifikator)
// 	resp, err := s.clientService.Hzv.PostApiVersichertenteilnahmenErklaerungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx, req, hpm_rest_hzv_client.RequestEditorFn(headerEditor))
// 	if err != nil {
// 		return nil, err
// 	}
// 	if hpmError := hpm_rest_hzv_client.GetHpmError(resp.StatusCode(), resp.JSON200); hpmError != nil {
// 		return resp.JSON200, hpmError
// 	}
// 	return resp.JSON200, nil
// }

func (s *ServiceRest) CheckParticipationList(ctx *titan.Context, request *hpm_next.CheckParticipationRequest) (*hpm_next.CheckParticipationResponse, error) {
	contracts := s.contractService.GetContractByIds(request.ContractIds)
	if len(contracts) == 0 {
		return nil, errors.New("cannot get contract")
	}
	availables := slice.Map(contracts, func(c contract_model_resource.Contract) bool {
		return c.CheckAvailableHPMFunction(contract_model_resource.HpmFunktionSimpleTyp_PRUEFE_TEILNAHMEN_LISTE, request.CheckTime)
	})
	uniqAvailable := slice.Uniq(availables)
	if !(len(uniqAvailable) == 1 && uniqAvailable[0]) {
		return nil, errors.New(string(error_code.ErrorCode_HpmFunction_Not_Available))
	}

	var (
		mediId  *int32
		haevgId *int32
	)
	if request.Doctor.HaevgId != "" {
		intHaevgId, err := strconv.Atoi(request.Doctor.HaevgId)
		if err != nil {
			return nil, err
		}
		haevgId = util.NewPointer(int32(intHaevgId))
	}
	if request.Doctor.MediId != "" {
		intMediId, err := strconv.Atoi(request.Doctor.MediId)
		if err != nil {
			return nil, err
		}
		mediId = util.NewPointer(int32(intMediId))
	}
	identifierId, err := s.clientService.GetPartnerIdentifier(ctx, GetPartnerIdentifierRequest{
		Haevgid: haevgId,
		MediId:  mediId,
	})
	if err != nil {
		return nil, errors.Wrap(err, "cannot get partner id")

	}

	// assume that all contracts have the same contract type
	contractType := util.GetPointerValue(contracts[0].GetContractType().Type())
	canGetIdentifierId := identifierId != nil
	if !canGetIdentifierId {
		return nil, errors.New("cannot get partner id")
	}

	hpmTestMode, err := s.featureFlagService.IsFeatureEnabled(ctx, common.FeatureFlagKey_HPM_TESTMODE)
	if err != nil {
		return nil, err
	}

	getVpId := func() string {
		strIdentifierId := ""
		if contractType == contract_model_resource.ContractType_HouseDoctorCare {
			strIdentifierId = util.GetPointerValue(identifierId.VpHaevgid)
			if strIdentifierId == "" {
				strIdentifierId = util.GetPointerValue(identifierId.VpMediId)
			}
		}
		if contractType == contract_model_resource.ContractType_SpecialistCare {
			strIdentifierId = util.GetPointerValue(identifierId.VpMediId)
			if strIdentifierId == "" {
				strIdentifierId = util.GetPointerValue(identifierId.VpHaevgid)
			}
		}
		return strIdentifierId
	}
	vpId := getVpId()

	req := hpm_rest_hzv_client.TeilnahmeListeContainer{
		AbsenderBsnr:           request.Doctor.Bsnr,
		ArztInformationsSystem: toArztInformationsSystem[hpm_rest_hzv_client.SoftwareInformation](s.hpmSoftwareInformation),
		Teilnahmeanfragen: []hpm_rest_hzv_client.Teilnahmeanfrage{{
			AbfrageDatum:            util.ConvertMillisecondsToTime(request.CheckTime),
			KrankenkassenIK:         request.IkNumber,
			PatientenId:             request.PatientId,
			Versichertennummer:      request.InsuranceId,
			VertragsIdentifikatoren: request.ContractIds,
		}},
		Testuebermittlung:            hpmTestMode,
		Vertragspartneridentifikator: vpId,
	}
	headerEditor := WithHeader(HeaderVertragspartneridentifikator, vpId)
	resp, err := s.clientService.Hzv.PostApiVersichertenteilnahmenPruefungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx, req, hpm_rest_hzv_client.RequestEditorFn(headerEditor))
	if err != nil {
		return nil, errors.Wrap(err, "cannot post teilnahmeListeContainer")
	}
	hpmError := hpm_rest_hzv_client.GetHpmError(resp.StatusCode(), resp.JSON200)
	if hpmError != nil {
		return &hpm_next.CheckParticipationResponse{}, hpmError
	}
	result := *resp.JSON200.TeilnahmeResultate
	response := hpm_next.CheckParticipationResponse{}
	for _, r := range result {
		if r.Vertragsteilnahmen == nil {
			continue
		}
		for _, c := range *r.Vertragsteilnahmen {
			if c.Vertragsidentifikator == nil {
				continue
			}
			response.ContractIds = append(response.ContractIds, *c.Vertragsidentifikator)
			isDeputyActive := util.GetPointerValue(c.IstFremdeingeschrieben)
			if isDeputyActive {
				response.DeputyContractIds = append(response.DeputyContractIds, *c.Vertragsidentifikator)
			}
		}
	}
	response.Status = hpm_next.ParticipateResultStatus_Ok
	return &response, nil
}

func (s *ServiceRest) SubmitBilling(ctx *titan.Context, req hpm_rest_hzv_client.AbrechnungsContainer, doctorId uuid.UUID) (*hpm_next.StarteAbrechnungV2Response, error) {
	contract := s.contractService.GetContractDetailById(req.GetContractId())
	if !contract.CheckAvailableHPMFunction(contract_model_resource.HpmFunktionSimpleTyp_STARTE_ABRECHNUNG, *req.GetYearQuarter().StartTime()) {
		return nil, errors.New(string(error_code.ErrorCode_HpmFunction_Not_Available))
	}
	headerEditor := WithHeader(HeaderVertragspartneridentifikator, req.Vertragspartneridentifikator)
	resp, err := s.clientService.Hzv.PostApiAbrechnungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx, req, hpm_rest_hzv_client.RequestEditorFn(headerEditor))
	if err != nil {
		return nil, err
	}
	// err = hpm_rest_hzv_client.GetHpmError(resp.StatusCode(), resp.JSON200)
	// if err != nil {
	// 	return nil, err
	// }
	result := resp.JSON200
	transferId := result.TransferId
	rawResponses := []hpm_next.RawResponse{}
	year := req.Vertragskontext.AbrechnungsJahr
	quarter := req.Vertragskontext.AbrechnungsQuartal
	errorEntryIds := []uuid.UUID{}
	if result.Meldungen != nil {
		patientError, entryIds := mappingPatientErrorHPM(*result.Meldungen, req, doctorId)
		errorEntryIds = append(errorEntryIds, entryIds...)
		rawResponses = append(rawResponses, patientError...)
	}
	diagnoseIds, serviceIds := req.GetDiagnosesServiceIds()
	return &hpm_next.StarteAbrechnungV2Response{
		HPMResponse: hpm_next.HPMResponse{
			ErrorEntryIds:     errorEntryIds,
			Year:              year,
			Quarter:           quarter,
			RequestPayload:    xml_util.MarshalToString(req),
			BillingContractId: req.Vertragskontext.VertragsIdentifikator,
			ProtocolFile: function.Do(func() []byte {
				if result.Uebermittlungsprotokoll != nil {
					return *result.Uebermittlungsprotokoll
				}
				return []byte{}
			}),
			OfflineBillingData: result.Datentraeger,
		},
		SubmitBillingError: rawResponses,
		TransferId: function.Do(func() string {
			if transferId == nil {
				return ""
			}
			return *transferId
		}),
		EntryIds: hpm_next.EntryIds{
			DiagnoseEntryIds: diagnoseIds,
			ServiceEntryIds:  serviceIds,
		},
	}, nil
}

func (s *ServiceRest) SubmmitMedicinPrescription(
	ctx *titan.Context,
	req hpm_rest_amm_client.VerordnungsContainer,
	doctorId uuid.UUID,
	medicineItemFinder func(medicineId *uuid.UUID, formId *uuid.UUID) *uuid.UUID,
) (*hpm_next.StarteVerordnungsdatenUebermittlungV2Response, error) {
	contract := s.contractService.GetContractDetailById(req.GetContractId())
	if !contract.CheckAvailableHPMFunction(contract_model_resource.HpmFunktionSimpleTyp_STARTE_VERORDNUNGSDATEN_UEBERMITTLUNG, *req.GetYearQuarter().StartTime()) {
		return nil, errors.New(string(error_code.ErrorCode_HpmFunction_Not_Available))
	}
	headerEditor := WithHeader(HeaderVertragspartneridentifikator, req.Vertragspartneridentifikator)
	resp, err := s.clientService.Amm.PostApiVerordnungenWithApplicationWildcardPlusJSONBodyWithResponse(ctx, req, hpm_rest_amm_client.RequestEditorFn(headerEditor))
	if err != nil {
		return nil, errors.WithMessage(err, "can not starte verordnungsdaten uebermittlung")
	}
	// err = hpm_rest_hzv_client.GetHpmError(resp.StatusCode(), resp.JSON200)
	// if err != nil {
	// 	return nil, err
	// }
	result := resp.JSON200
	rawResponses := []hpm_next.RawResponse{}
	year := req.Vertragskontext.AbrechnungsJahr
	quarter := req.Vertragskontext.AbrechnungsQuartal
	errorEntryIds := []uuid.UUID{}
	if result.Meldungen != nil {
		for _, m := range *result.Meldungen {
			if m.UebermittlungsStatus != nil && *m.UebermittlungsStatus == hpm_rest_amm_client.OK {
				continue
			}
			if m.Referenzen != nil {
				var (
					formId     *uuid.UUID
					medicineId *uuid.UUID
					patientId  *uuid.UUID
				)
				for _, r := range *m.Referenzen {
					if r.ReferenzTyp != nil {
						var id *uuid.UUID
						if r.Id != nil {
							id = util.NewPointer(uuid.MustParse(*r.Id))
						}
						if *r.ReferenzTyp == hpm_rest_amm_client.ReferenzTypPatient {
							patientId = id
						} else if *r.ReferenzTyp == hpm_rest_amm_client.ReferenzTypRezept {
							formId = id
						} else if *r.ReferenzTyp == hpm_rest_amm_client.ReferenzTypArzneimittel {
							medicineId = id
						}
					}
				}
				if patientId != nil && (formId != nil || medicineId != nil) {
					referenceId := medicineItemFinder(medicineId, formId)
					rawResponses = append(rawResponses, hpm_next.RawResponse{
						ErrorCode:    *m.Code,
						ErrorMessage: *m.Nachricht,
						TimelineId:   referenceId,
						ErrorType:    hpm_next.MessageTypeHpm(*m.Art),
						PatientId:    patientId,
						DoctorId:     doctorId,
						ContractId:   req.GetContractId(),
						YearQuarter:  req.Vertragskontext.GetYearQuarter(),
					})
					if referenceId != nil {
						errorEntryIds = append(errorEntryIds, *referenceId)
					}
				}
			} else {
				rawResponses = append(rawResponses, hpm_next.RawResponse{
					ErrorCode:    *m.Code,
					ErrorMessage: *m.Nachricht,
					ErrorType:    hpm_next.MessageTypeHpm(*m.Art),
					DoctorId:     doctorId,
					ContractId:   req.GetContractId(),
					YearQuarter:  req.Vertragskontext.GetYearQuarter(),
				})
			}
		}
	}
	return &hpm_next.StarteVerordnungsdatenUebermittlungV2Response{
		HPMResponse: hpm_next.HPMResponse{
			Year:              year,
			Quarter:           quarter,
			ErrorEntryIds:     errorEntryIds,
			RequestPayload:    xml_util.MarshalToString(req),
			BillingContractId: req.GetContractId(),
			ProtocolFile: function.Do(func() []byte {
				if result.Uebermittlungsprotokoll != nil {
					return *result.Uebermittlungsprotokoll
				}
				return []byte{}
			}),
		},
		SubmitBillingError: rawResponses,
		MedicationEntryIds: []uuid.UUID{},
		TransferId:         result.TransferId,
	}, nil
}

func (s *ServiceRest) GetMedicines(ctx *titan.Context, request *GetMedicinesRequest) (response *GetMedicinesResponse, err error) {
	referralDate, err := time.Parse(util.YYYY_MM_DD, request.ReferenceDate)
	if err != nil {
		return nil, err
	}
	headerEditor := WithHeader(HeaderVertragspartneridentifikator, request.VpId)
	res, err := s.clientService.Amm.PostApiArzneimittelInformationenWithResponse(ctx, hpm_rest_amm_client.PostApiArzneimittelInformationenJSONRequestBody{
		PzNs:                   request.PzNs,
		Ik:                     fmt.Sprintf("%d", request.IkNumber),
		ReferenzDatum:          referralDate,
		VertragsIdentifikator:  request.ContractId,
		ArztInformationsSystem: toArztInformationsSystem[hpm_rest_amm_client.SoftwareInformation](s.hpmSoftwareInformation),
	}, hpm_rest_amm_client.RequestEditorFn(headerEditor))
	if err != nil {
		return nil, err
	}
	hpmErrorCodeAsWarningCode := []string{}
	if res != nil && res.JSON200 != nil && res.JSON200.Meldungen != nil {
		errorCodeAsWarnings := []string{"E001077"} // No discount warning
		messages := slice.Filter(*res.JSON200.Meldungen, func(m hpm_rest_amm_client.Meldung) bool {
			if slice.Contains(errorCodeAsWarnings, *m.Code) {
				hpmErrorCodeAsWarningCode = append(hpmErrorCodeAsWarningCode, *m.Code)
				return false
			}
			return true
		})
		res.JSON200.Meldungen = &messages
		if len(messages) == 0 {
			res.JSON200.Status = util.NewPointer(hpm_rest_amm_client.ResultatStatusOK)
		}
	}
	hpmError := hpm_rest_hzv_client.GetHpmError(res.StatusCode(), res.JSON200)
	if hpmError != nil {
		return nil, hpmError
	}
	medicinesMap := make(map[string]Medicine)
	if res.JSON200.Packungen != nil {
		for _, p := range *res.JSON200.Packungen {
			if p.Pzn == nil {
				continue
			}
			medicine := Medicine{
				Pzn:             *p.Pzn,
				FixedPrice:      util.GetPointerValue(p.Festbetrag),
				Color:           util.GetPointerValue(p.ArzneimittelKategorie),
				Hints:           getHints(p.Meldungen),
				IsInPriscusList: util.GetPointerValue(p.IstInPriscusListe),
			}
			medicinesMap[*p.Pzn] = medicine
		}
	}

	return &GetMedicinesResponse{
		Medicines:                 medicinesMap,
		HpmErrorCodeAsWarningCode: hpmErrorCodeAsWarningCode,
	}, nil
}

func (s *ServiceRest) GetSubstitution(ctx *titan.Context, request *GetSubstitutionRequest) (response *GetSubstitutionResponse, err error) {
	referralDate, err := time.Parse(util.YYYY_MM_DD, request.ReferenceDate)
	if err != nil {
		return nil, err
	}
	headerEditor := WithHeader(HeaderVertragspartneridentifikator, request.VpId)
	res, err := s.clientService.Amm.PostApiArzneimittelSubstitutionenWithResponse(ctx, hpm_rest_amm_client.PostApiArzneimittelSubstitutionenJSONRequestBody{
		Pzn:                    request.Pzn,
		Ik:                     fmt.Sprintf("%d", request.IkNumber),
		ReferenzDatum:          referralDate,
		VertragsIdentifikator:  request.ContractId,
		ArztInformationsSystem: toArztInformationsSystem[hpm_rest_amm_client.SoftwareInformation](s.hpmSoftwareInformation),
	}, hpm_rest_amm_client.RequestEditorFn(headerEditor))

	if err != nil {
		return nil, err
	}
	hpmError := hpm_rest_hzv_client.GetHpmError(res.StatusCode(), res.JSON200)
	if hpmError != nil {
		return nil, hpmError
	}
	medicinesMap := make(map[string]Medicine)
	if res.JSON200.Substitutionen != nil {
		for _, subsitution := range *res.JSON200.Substitutionen {
			if subsitution.Packungen == nil {
				continue
			}
			for _, p := range *subsitution.Packungen {
				if util.GetPointerValue(p.Pzn) == "" {
					continue
				}
				medicine := Medicine{
					Pzn:             *p.Pzn,
					Color:           util.GetPointerValue(p.ArzneimittelKategorie),
					Hints:           getHints(p.Meldungen),
					IsInPriscusList: util.GetPointerValue(p.IstInPriscusListe),
				}
				medicinesMap[*p.Pzn] = medicine
			}
		}
	}

	return &GetSubstitutionResponse{
		Medicines: medicinesMap,
	}, nil
}

func (s *ServiceRest) SubmitPreParticipateService(
	ctx *titan.Context,
	req hpm_rest_hzv_client.PostApiAbrechnungenVoreinschreibeleistungenJSONRequestBody,
) (*SubmitPreParticipateServiceResponse, error) {
	headerEditor := WithHeader(HeaderVertragspartneridentifikator, req.Vertragspartneridentifikator)
	resp, err := s.clientService.Hzv.PostApiAbrechnungenVoreinschreibeleistungenWithResponse(ctx, req, hpm_rest_hzv_client.RequestEditorFn(headerEditor))
	if err != nil {
		return nil, err
	}
	err = hpm_rest_hzv_client.GetHpmError(resp.StatusCode(), resp.JSON200)
	if err != nil {
		return nil, err
	}
	return &SubmitPreParticipateServiceResponse{
		Status: resp.JSON200.Status,
		// TODO: fake transerId to test flow
		TransferId: function.Do(func() *string {
			if resp.JSON200.TransferId == nil {
				return util.NewString(uuid.NewString())
			}
			return resp.JSON200.TransferId
		}),
		Meldungen:            resp.JSON200.Meldungen,
		UebermittlungsStatus: resp.JSON200.UebermittlungsStatus,
	}, nil
}

func getHints(meldungen *[]hpm_rest_amm_client.MeldungsText) []string {
	if meldungen == nil {
		return []string{}
	}
	return slice.Map(*meldungen, func(m hpm_rest_amm_client.MeldungsText) string {
		if m.Inhalt != nil {
			return *m.Inhalt
		}
		return ""
	})
}

func (s *ServiceRest) ValidateEAU(ctx *titan.Context, req *PostValidierungRequest) (*[]hpm_rest_eav_client.Meldung, error) {
	headerEditor := WithHeader(HeaderVertragspartneridentifikator, req.VpId)
	res, err := s.clientService.Eav.PostApiArbeitsunfaehigkeitsbescheinigungenValidierungWithApplicationWildcardPlusJSONBodyWithResponse(ctx, hpm_rest_eav_client.ValidiereAuContainer{
		FhirDokument:           req.FhirDokument,
		AbsenderBsnr:           req.AbsenderBsnr,
		Lanr:                   req.Lanr,
		ArztInformationsSystem: toArztInformationsSystem[hpm_rest_eav_client.SoftwareInformation](s.hpmSoftwareInformation),
	}, hpm_rest_eav_client.RequestEditorFn(headerEditor))
	if err != nil {
		return nil, err
	}
	err = hpm_rest_hzv_client.GetHpmError(res.StatusCode(), res.JSON200)
	if err != nil {
		return nil, err
	}

	return res.JSON200.Meldungen, nil
}

func (s *ServiceRest) GetIndicatorActiveIngredients(ctx *titan.Context, contractId, vpId string) ([]GetIndicatorActiveIngredientsResponse, error) {
	result := []GetIndicatorActiveIngredientsResponse{}
	contract := s.contractService.GetContractDetailById(contractId)
	if slice.FindOne(contract.GetContractSoftwareFunction(), func(f contract_model_resource.AnforderungTyp) bool {
		return f.ID == "VSST785"
	}) == nil {
		return result, nil
	}
	headerEditor := WithHeader(HeaderVertragspartneridentifikator, vpId)
	resp, err := s.clientService.Amm.PostApiIndikatorwirkstoffeWithResponse(ctx, hpm_rest_amm_client.IndikatorWirkstoffListeContainer{
		ArztInformationsSystem: toArztInformationsSystem[hpm_rest_amm_client.SoftwareInformation](s.hpmSoftwareInformation),
		VertragsIdentifikator:  contractId,
	}, hpm_rest_amm_client.RequestEditorFn(headerEditor))
	if err != nil {
		return result, err
	}
	err = hpm_rest_hzv_client.GetHpmError(resp.StatusCode(), resp.JSON200)
	if err != nil {
		return result, err
	}
	for _, i := range *resp.JSON200.IndikatorWirkstoffListe {
		if i.AtcCode == nil && i.DiagnoseCode == nil {
			continue
		}
		result = append(result, GetIndicatorActiveIngredientsResponse{
			AtcCode:      i.AtcCode,
			DiagnoseCode: i.DiagnoseCode,
		})
	}
	return result, nil
}

func (s *ServiceRest) GetListPznAtcForHighPrescription(ctx *titan.Context, contractId, vpId string) ([]GetListPznAtcForHighPrescriptionResponse, error) {
	result := []GetListPznAtcForHighPrescriptionResponse{}
	headerEditor := WithHeader(HeaderVertragspartneridentifikator, vpId)
	resp, err := s.clientService.Amm.PostApiHochverordnungenWithResponse(ctx, hpm_rest_amm_client.PostApiHochverordnungenJSONRequestBody{
		ArztInformationsSystem: toArztInformationsSystem[hpm_rest_amm_client.SoftwareInformation](s.hpmSoftwareInformation),
		VertragsIdentifikator:  contractId,
	}, hpm_rest_amm_client.RequestEditorFn(headerEditor))
	if err != nil {
		return result, err
	}
	err = hpm_rest_hzv_client.GetHpmError(resp.StatusCode(), resp.JSON200)
	if err != nil {
		return result, err
	}
	for _, i := range *resp.JSON200.PznAtcFuerHochverordnungListe {
		if i.AtcCode == nil && i.Pzn == nil {
			continue
		}
		result = append(result, GetListPznAtcForHighPrescriptionResponse{
			AtcCode: i.AtcCode,
			Pzn:     i.Pzn,
		})
	}
	return result, nil
}

func (s *ServiceRest) GetPTVFoldersByDoctor(ctx *titan.Context, req GetPtvFoldersaByDoctorRequest) ([]hpm_rest_hzv_client.PTVContractFolder, error) {
	hpmTestMode, err := s.featureFlagService.IsFeatureEnabled(ctx, common.FeatureFlagKey_HPM_TESTMODE)
	if err != nil {
		return nil, err
	}

	result := []hpm_rest_hzv_client.PTVContractFolder{}
	headerEditor := WithHeader(HeaderVertragspartneridentifikator, req.ContractPartnerIdentifier)
	resp, err := s.clientService.Hzv.PostApiBereitgestellteteilnehmerverzeichnisseWithResponse(ctx, hpm_rest_hzv_client.PostApiBereitgestellteteilnehmerverzeichnisseJSONRequestBody{
		// Abrufcode:                    &req.Icode,
		AbsenderBsnr:                 req.Bsnr,
		Testuebermittlung:            util.NewPointer(hpmTestMode),
		Vertragspartneridentifikator: req.ContractPartnerIdentifier,
		ArztInformationsSystem:       toArztInformationsSystem[hpm_rest_hzv_client.SoftwareInformation](s.hpmSoftwareInformation),
	}, hpm_rest_hzv_client.RequestEditorFn(headerEditor))
	if err != nil {
		return result, err
	}
	err = hpm_rest_hzv_client.GetHpmError(resp.StatusCode(), resp.JSON200)
	if err != nil {
		return result, err
	}
	for _, f := range *resp.JSON200.Teilnehmerverzeichnisse {
		if f.DokumentIdentifikator == nil || f.Jahr == nil || f.Quartal == nil || f.Version == nil || f.VertragsIdentifikator == nil {
			continue
		}
		result = append(result, hpm_rest_hzv_client.PTVContractFolder{
			DocumentId: *f.DokumentIdentifikator,
			Year:       *f.Jahr,
			Quarter:    *f.Quartal,
			Version:    *f.Version,
			ContractId: *f.VertragsIdentifikator,
		})
	}
	return result, nil
}

func (s *ServiceRest) GetPTVContractsByDoctor(ctx *titan.Context, req GetPtvContractsByDoctorRequest) ([]hpm_rest_hzv_client.PTVPatientParticipation, error) {
	hpmTestMode, err := s.featureFlagService.IsFeatureEnabled(ctx, common.FeatureFlagKey_HPM_TESTMODE)
	if err != nil {
		return nil, err
	}

	result := []hpm_rest_hzv_client.PTVPatientParticipation{}
	headerEditor := WithHeader(HeaderVertragspartneridentifikator, req.ContractPartnerIdentifier)
	resp, err := s.clientService.Hzv.PostApiTeilnehmerverzeichnisWithResponse(ctx, hpm_rest_hzv_client.PostApiTeilnehmerverzeichnisJSONRequestBody{
		ArztInformationsSystem:       toArztInformationsSystem[hpm_rest_hzv_client.SoftwareInformation](s.hpmSoftwareInformation),
		Abrufcode:                    req.Icode,
		Testuebermittlung:            hpmTestMode,
		AbsenderBsnr:                 req.Bsnr,
		Vertragspartneridentifikator: req.ContractPartnerIdentifier,
		DokumentIdentifikator:        req.DocumentIdentifier,
	}, hpm_rest_hzv_client.RequestEditorFn(headerEditor))
	if err != nil {
		return result, err
	}
	hpmError := hpm_rest_hzv_client.GetHpmError(resp.StatusCode(), resp.JSON200)
	if hpmError != nil {
		return nil, hpmError
	}

	str := *resp.JSON200.PatientParticipationList
	str = html.UnescapeString(str)
	str = util.UmlautToNormalChar(str)

	patientPartipationList := hpm_rest_hzv_client.PatientParticipationListDocument{}
	if err := xml.Unmarshal([]byte(str), &patientPartipationList); err != nil {
		return result, err
	}
	result, err = patientPartipationList.ToPTVPatientParticipation(ctx.RequestTimeZone())
	if err != nil {
		return result, err
	}
	return result, nil
}

func (s *ServiceRest) LoadIndicatorByContractID(ctx *titan.Context, contractId, vpId string) (map[string][]string, error) {
	headerEditor := WithHeader(HeaderVertragspartneridentifikator, vpId)
	res, err := s.clientService.Amm.PostApiIndikatorwirkstoffeWithResponse(ctx, hpm_rest_amm_client.IndikatorWirkstoffListeContainer{
		ArztInformationsSystem: toArztInformationsSystem[hpm_rest_amm_client.SoftwareInformation](s.hpmSoftwareInformation),
		VertragsIdentifikator:  contractId,
	}, hpm_rest_amm_client.RequestEditorFn(headerEditor))
	if err != nil {
		return nil, err
	}
	if hpmError := hpm_rest_hzv_client.GetHpmError(res.StatusCode(), res.JSON200); hpmError != nil {
		return nil, hpmError
	}

	mapATCAndDiagnoreCode := make(map[string][]string)
	for _, indicator := range *res.JSON200.IndikatorWirkstoffListe {
		mapATCAndDiagnoreCode[*indicator.AtcCode] = append(mapATCAndDiagnoreCode[*indicator.AtcCode], *indicator.DiagnoseCode)
	}
	return mapATCAndDiagnoreCode, nil
}

func (s *ServiceRest) GetParticipationContracts(ctx *titan.Context, havgVpId, bsnrCode string) (*GetParticipationContractsResponse, error) {
	params := &hpm_rest_hzv_client.GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenParams{
		XAbsenderBsnr: &bsnrCode,
		XSystemoid:    &s.hpmConfig.SystemOid,
	}
	headerEditor := WithHeader(HeaderVertragspartneridentifikator, havgVpId)
	headerEditor2 := WithHeader(HeaderVertragspartneridentifikator2, havgVpId)
	res, err := s.clientService.Hzv.GetApiVertragspartnerVertragspartneridentifikatorVertragsteilnahmenWithResponse(ctx,
		havgVpId, params,
		hpm_rest_hzv_client.RequestEditorFn(headerEditor),
		hpm_rest_hzv_client.RequestEditorFn(headerEditor2),
	)
	if err != nil {
		return nil, err
	}
	if hpmError := hpm_rest_hzv_client.GetHpmError(res.StatusCode(), res.JSON200); hpmError != nil {
		return nil, hpmError
	}

	data := slice.Map(*res.JSON200.Vertragsteilnahmen, func(i hpm_rest_hzv_client.VertragspartnerVertragsteilnahme) ParticipationContract {
		endDate := i.GueltigBis.UnixMilli()
		// 253402214400000 == 9999-12-31T00:00:00Z
		// meaning no end date
		if endDate == 253402214400000 {
			endDate = 0
		}

		return ParticipationContract{
			ChargeSystemId: i.Honoraranlageidentifikator,
			ContractId:     i.Vertragsidentifikator,
			StartDate:      i.GueltigVon.UnixMilli(),
			EndDate:        endDate,
		}
	})

	return &GetParticipationContractsResponse{
		Data: data,
	}, nil
}

// GetVersionInformation retrieves the version information from the HPM REST API.
//
//	Example response:
//	{
//	    "Uebertragungsweg": "Online",
//	    "Status": "OK",
//	    "UebermittlungsStatus": "OK",
//	    "Version": "2025-3-0050",
//	    "IstTestsystem": true,
//	    "HpmDateTimeNow": "2025-06-27T04:46:29.912412229+02:00",
//	    "HzvOnlineKeyInformationen": [
//	        {
//	            "Bsnr": "9900362",
//	            "GueltigVon": "2023-12-19",
//	            "GueltigBis": "2026-12-18",
//	            "SerienNummer": "3197425"
//	        }
//	    ]
//	}
func (srv *ServiceRest) GetVersionInformation(ctx *titan.Context) (*hpm_rest_info_client.InfoServiceLiefereHPMInformationResult, error) {
	hpmResponse, err := srv.clientService.Info.GetApiHpminformationWithResponse(ctx)
	if err != nil {
		return nil, err
	}
	if hpmError := hpm_rest_hzv_client.GetHpmError(hpmResponse.StatusCode(), hpmResponse.JSON200); hpmError != nil {
		return nil, hpmError
	}
	return hpmResponse.JSON200, nil
}

func (srv *ServiceRest) AnalyzeForDiseases(ctx *titan.Context, request *hpm_next.DiseaseAnalysisRequest) (*hpm_rest_hzv_client.KrankheitsbildAuswertungResultat, error) {
	patients := slice.Map(request.Patients, func(p *hpm_next.DiseaseAnalysisPatientRequest) hpm_rest_hzv_client.KrankheitsbildAuswertungPatient {
		return hpm_rest_hzv_client.KrankheitsbildAuswertungPatient{
			Diagnosen:   &p.DiagnoseCodes,
			PatientenId: util.NewPointer(p.PatientId.String()),
		}
	})

	// auswertungen
	headerEditor := WithHeader(HeaderVertragspartneridentifikator, request.VpId)
	res, err := srv.clientService.Hzv.PostApiAuswertungenWithResponse(ctx, hpm_rest_hzv_client.PostApiAuswertungenJSONRequestBody{
		AbsenderBsnr:                 request.Bsnr,
		ArztInformationsSystem:       toArztInformationsSystem[hpm_rest_hzv_client.SoftwareInformation](srv.hpmSoftwareInformation),
		AuswertungsJahr:              request.Year,
		AuswertungsKontext:           request.EvaluationContext,
		AuswertungsQuartal:           request.Quarter,
		Patienten:                    patients,
		VertragsIdentifikator:        request.ContractId,
		Vertragspartneridentifikator: request.VpId,
	}, hpm_rest_hzv_client.RequestEditorFn(headerEditor))
	if err != nil {
		return nil, err
	}
	if hpmError := hpm_rest_hzv_client.GetHpmError(res.StatusCode(), res.JSON200); hpmError != nil {
		return nil, hpmError
	}

	return res.JSON200, nil
}

func toArztInformationsSystem[T SoftwareInformationConstant](s *hpm_next.SoftwareInformation) T {
	return T{
		Name:         &s.Name,
		Organisation: &s.Organisation,
		SystemOid:    s.SystemOid,
		Version:      s.Version,
	}
}

type HpmSoftware software.SoftwareConfig

func (h HpmSoftware) toHPMSortware() hpm_next.SoftwareInformation {
	return hpm_next.SoftwareInformation{
		KbvPruefnummer: h.KbvPruefnummer,
		Version:        h.Version,
		Vorname:        h.Vorname,
		Nachname:       h.Nachname,
		Name:           h.Name,
		Organisation:   h.Organisation,
		Strasse:        h.Strasse,
		Plz:            h.Plz,
		Stadt:          h.Stadt,
		Telefon:        h.Telefon,
		Telefax:        h.Telefax,
		TelefonMobil:   h.TelefonMobil,
		SystemOid:      h.SystemOid,
	}
}

var HpmRestServiceMod = submodule.Make[*ServiceRest](func(
	contractService *contract.Service,
	mvzConfig *config.MvzAppConfigs,
	featureFlagService *feature_flag_service.FeatureFlagService,
) *ServiceRest {
	hpmConfig := mvzConfig.Hpm
	softwareInformation := mvzConfig.SoftwareConfig
	return newServiceRest(hpmConfig, pkg_copy.CloneTo[HpmSoftware](softwareInformation), contractService, featureFlagService)
},
	contract.ContractServiceMod,
	config.MvzAppConfigMod,
	feature_flag_service.FeatureFlagServiceMod,
)

func newServiceRest(
	config hpm.HpmConfig,
	softwareInformation HpmSoftware,
	contractService *contract.Service,
	featureFlagService *feature_flag_service.FeatureFlagService,
) *ServiceRest {
	if config.Url == "" {
		panic("HPM NEXT URL is empty")
	}
	if config.Secret == "" {
		panic("HPM SECRET is empty")
	}
	if config.SystemOid == "" {
		panic("HPM SYSTEM OID is empty")
	}

	hpmSortwareInfo := softwareInformation.toHPMSortware()
	return &ServiceRest{
		hpmSoftwareInformation: &hpmSortwareInfo,
		hpmConfig:              config,
		clientService:          NewClientService(config.Url),
		contractService:        contractService,
		featureFlagService:     featureFlagService,
	}
}

func NewServiceRestWithEndpoint(
	url string,
) *ServiceRest {
	hpmConfig := config.MvzAppConfigMod.Resolve().Hpm

	if url != "" {
		hpmConfig.Url = url
	}

	mvzConfig := config.MvzAppConfigMod.Resolve()
	softwareInformation := mvzConfig.SoftwareConfig
	contractService := contract.ContractServiceMod.Resolve()
	featureFlagService := feature_flag_service.FeatureFlagServiceMod.Resolve()
	return newServiceRest(hpmConfig, pkg_copy.CloneTo[HpmSoftware](softwareInformation), contractService, featureFlagService)
}
