<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="http://haevg-rz.de/hpm" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" targetNamespace="http://haevg-rz.de/hpm" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="http://haevg-rz.de/hpm">
      <s:import namespace="http://microsoft.com/wsdl/types/"/>
      <s:element name="StarteAbrechnung">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:AbrechnungsContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StarteVerordnungsdatenUebermittlungResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StarteVerordnungsdatenUebermittlungResult" type="tns:VerordnungsdatenUebermittlungResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="StarteVerordnungsdatenUebermittlung">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:VerordnungsContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PruefeTeilnahmenListeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="PruefeTeilnahmenListeResult" type="tns:TeilnahmeListeResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PruefeTeilnahmenListe">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="teilnahmeContainer" type="tns:TeilnahmeListeContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SendeTeilnahmeerklaerungResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SendeTeilnahmeerklaerungResult" type="tns:TeilnahmeerklaerungResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SendeTeilnahmeerklaerung">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="teilnahmeerklaerungContainer" type="tns:TeilnahmeerklaerungContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SendeEDMPDatenResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SendeEDMPDatenResult" type="tns:TransferResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SendeEDMPDaten">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:EDMPContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SendePracManDatenResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SendePracManDatenResult" type="tns:TransferResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="SendePracManDaten">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:PracManContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
	  <s:element name="MigriereVertragspartnerResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="MigriereVertragspartnerResult" type="tns:MigriereVertragspartnerResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="MigriereVertragspartner">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:MigriereVertragspartnerContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LiefereArzneimittelListeGZipResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LiefereArzneimittelListeGZipResult" type="tns:LiefereArzneimittelListeGZipResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LiefereArzneimittelListeGZip">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:ArzneimittelListeContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LiefereArzneimittelListeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LiefereArzneimittelListeResult" type="tns:LiefereArzneimittelListeResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LiefereArzneimittelListe">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:ArzneimittelListeContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LiefereIndikatorWirkstoffListe">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:IndikatorWirkstoffListeContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LiefereIndikatorWirkstoffListeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LiefereIndikatorWirkstoffListeResult" type="tns:LiefereIndikatorWirkstoffListeResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LieferePznAtcFuerHochverordnungListe">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:PznAtcFuerHochverordnungListeContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="PznAtcFuerHochverordnungListeContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBasis">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="VertragsIdentifikator" type="s:string"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="LieferePznAtcFuerHochverordnungListeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LieferePznAtcFuerHochverordnungListeResult" type="tns:LieferePznAtcFuerHochverordnungListeResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LieferePznAtcFuerHochverordnungListeResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="PznAtcFuerHochverordnungListe" type="tns:ArrayOfPznAtcFuerHochverordnungListeEintrag"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArrayOfPznAtcFuerHochverordnungListeEintrag">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="PznAtcFuerHochverordnungListeEintrag" type="tns:PznAtcFuerHochverordnungListeEintrag"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="PznAtcFuerHochverordnungListeEintrag">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="PZN" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="AtcCode" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:element name="LiefereSubstitutionenResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LiefereSubstitutionenResult" type="tns:SubstitutionResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LiefereSubstitutionen">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:SubstitutionContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LiefereArzneimittelInformationenResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LiefereArzneimittelInformationenResult" type="tns:LiefereArzneimittelInformationenResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LiefereArzneimittelInformationen">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:ArzneimittelInformationenContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="LiefereHpmInformation">
        <s:complexType/>
      </s:element>
      <s:element name="LiefereHpmInformationResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LiefereHpmInformationResult" type="tns:HpmInformationResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="HpmInformationResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="Version" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="IstTestversion" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="IstTestsystem" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="Uebertragungsweg" type="tns:UebertragungswegType"/>
              <s:element minOccurs="1" maxOccurs="1" name="HzvOnlineKeyInformationen" type="tns:ArrayOfHzvOnlineKeyInformation"/>
              <s:element minOccurs="1" maxOccurs="1" name="HpmDateTimeNow" type="s:dateTime"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="Update">
        <s:complexType/>
      </s:element>
      <s:element name="UpdateResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="UpdateResult" type="tns:UpdateResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfHzvOnlineKeyInformation">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="HzvOnlineKeyInformation" type="tns:HzvOnlineKeyInformation"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="HzvOnlineKeyInformation">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Bsnr" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Vertragspartneridentifikator" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="GueltigVon" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="GueltigBis" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="SerienNummer" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="GeraeteId" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="UpdateResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat"/>
        </s:complexContent>
      </s:complexType>
      <s:element name="PruefeKonnektivitaet">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:PruefeKonnektivitaetContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="PruefeKonnektivitaetContainer">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="PruefeKonnektivitaetResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat"/>
        </s:complexContent>
      </s:complexType>
      <s:element name="PruefeKonnektivitaetResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="PruefeKonnektivitaetResult" type="tns:PruefeKonnektivitaetResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="PruefeKonnektivitaetItv">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:PruefeKonnektivitaetItvContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="PruefeKonnektivitaetItvContainer">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="PruefeKonnektivitaetItvResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat"/>
        </s:complexContent>
      </s:complexType>
      <s:element name="PruefeKonnektivitaetItvResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="PruefeKonnektivitaetItvResult" type="tns:PruefeKonnektivitaetItvResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="VerordnungsdatenUebermittlungResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:TransferResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="Uebermittlungsprotokoll" type="s:base64Binary"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="AbrechnungsContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:VertragsContainer">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="Dokumentationen" type="tns:ArrayOfAbrechnungsDokumentation"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="NurPrueflauf" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="Uebertragungsart" type="tns:Uebertragungsart"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="VerordnungsZeitpunkte">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Erstellt" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="Geaendert" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="Gedruckt" nillable="true" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="Geloescht" nillable="true" type="s:dateTime"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="RezeptKvDetails">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Begruendungspflicht" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="Hilfsmittel" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="Impfstoff" type="s:boolean"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArzneimittelFreitext">
        <s:complexContent mixed="false">
          <s:extension base="tns:Arzneimittel">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="Name" type="s:string"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArzneimittelPzn">
        <s:complexContent mixed="false">
          <s:extension base="tns:Arzneimittel">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="Preis" type="s:int"/>
              <s:element minOccurs="1" maxOccurs="1" name="Pzn" type="s:string"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="Arzneimittel" abstract="true">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ArzneimittelId" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="AutIdem" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="Anzahl" type="s:int"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfArzneimittel">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Arzneimittel" type="tns:Arzneimittel"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Rezept">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ArbeitsunfallDatum" nillable="true" type="s:date"/>
          <s:element minOccurs="0" maxOccurs="1" name="ArbeitsunfallOrt" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Arzneimittel" type="tns:ArrayOfArzneimittel"/>
          <s:element minOccurs="1" maxOccurs="1" name="Bvg" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="Gebuehrenpflichtig" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="KvDetails" type="tns:RezeptKvDetails"/>
          <s:element minOccurs="1" maxOccurs="1" name="Notfall" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="RezeptId" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="SonstigerKt" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="Sprechstundenbedarf" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="Stellvertreter" type="tns:Stellvertreter"/>
          <s:element minOccurs="1" maxOccurs="1" name="Unfall" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="VerordnungsZeitpunkte" type="tns:VerordnungsZeitpunkte"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfRezept">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Rezept" type="tns:Rezept"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Verordnungen">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Rezepte" type="tns:ArrayOfRezept"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="VerordnungsDokumentation">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Patient" type="tns:Patient"/>
          <s:element minOccurs="1" maxOccurs="1" name="Verordnungen" type="tns:Verordnungen"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfVerordnungsDokumentation">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="VerordnungsDokumentation" type="tns:VerordnungsDokumentation"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="VerordnungsContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:VertragsContainer">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="Dokumentationen" type="tns:ArrayOfVerordnungsDokumentation"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="NurPrueflauf" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="TeilnahmeListeResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat"/>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="Teilnahmeanfrage">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="AbfrageDatum" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="KrankenkassenIK" type="tns:IkKrankenkasseType"/>
          <s:element minOccurs="1" maxOccurs="1" name="PatientenId" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Versichertennummer" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="VertragsIdentifikatoren" type="tns:ArrayOfString"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfTeilnahmeanfrage">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Teilnahmeanfrage" type="tns:Teilnahmeanfrage"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="TeilnahmeListeContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerVertragspartnerBasis">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="Teilnahmeanfragen" type="tns:ArrayOfTeilnahmeanfrage"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="TeilnahmeerklaerungResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat"/>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="TeilnahmeerklaerungPatient">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Geburtsdatum" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Nachname" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="PatientenId" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Vorname" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Arztwechselgrund" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="IstArztwechsel" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="IstEinwilligungTelefonkontaktVorhanden" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="KassenIk" type="tns:IkKrankenkasseType"/>
          <s:element minOccurs="1" maxOccurs="1" name="KopfDatum" type="s:date"/>
		  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="TeilnahmeerklaerungIdentifikator" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Versichertennummer" type="s:string"/>
	  <s:element minOccurs="0" maxOccurs="1" name="Therapieverfahren" type="tns:Therapieverfahren"/>
	  <s:element minOccurs="0" maxOccurs="1" name="Diagnosen" type="tns:ArrayOfDiagnoseElement"/>
        </s:sequence>
      </s:complexType>
	  <s:simpleType name="Therapieverfahren">
        <s:restriction base="s:string">
          <s:enumeration value="V"/>
          <s:enumeration value="T"/>
          <s:enumeration value="N"/>
          <s:enumeration value="P"/>
          <s:enumeration value="A"/>
        </s:restriction>
      </s:simpleType>
	  <s:complexType name="ArrayOfDiagnoseElement">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Diagnose" type="tns:DiagnoseElement"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="DiagnoseElement">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="CodeSystemName" type="tns:DiagnoseCodeSystemName"/>
          <s:element minOccurs="1" maxOccurs="1" name="DiagnoseCode" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Sicherheit" type="tns:DiagnoseSicherheit"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfTeilnahmeerklaerungPatient">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="TeilnahmeerklaerungPatient" type="tns:TeilnahmeerklaerungPatient"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="TeilnahmeerklaerungContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerVertragspartnerBasis">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="Teilnahmeerklaerungen" type="tns:ArrayOfTeilnahmeerklaerungPatient"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="VertragsIdentifikator" type="s:string"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="EDMPContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerVertragspartnerBasis">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="Filename" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="FileContent" type="s:base64Binary"/>
              <s:element minOccurs="1" maxOccurs="1" name="IK" type="tns:IkType"/>
              <s:element minOccurs="1" maxOccurs="1" name="DokumentationsDatum" nillable="true" type="s:date"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="VertragsIdentifikator" type="s:string"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="PracManContainer">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Content" type="s:base64Binary"/>
          <s:element minOccurs="1" maxOccurs="1" name="DokumentationsDatum" nillable="true" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="VertragsIdentifikator" type="s:string"/>
        </s:sequence>
      </s:complexType>	  
	  <s:complexType name="MigriereVertragspartnerResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="Vertragspartneridentifikator" type="s:string"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
	  <s:complexType name="MigriereVertragspartnerContainer">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="HaevgId" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" name="MediId" type="s:int"/>          
        </s:sequence>
      </s:complexType>
      <s:complexType name="LiefereArzneimittelListeGZipResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="GZip" type="s:base64Binary"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArzneimittelListeEintrag">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="PZN" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="ArzneimittelKategorie" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="GueltigVon" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="GueltigBis" type="s:date"/>
          <s:element minOccurs="0" maxOccurs="1" name="IstInPriscusListe" type="s:boolean" default="false"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfArzneimittelListeEintrag">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="ArzneimittelListeEintrag" type="tns:ArzneimittelListeEintrag"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArzneimittelListe">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Eintraege" type="tns:ArrayOfArzneimittelListeEintrag"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="LiefereArzneimittelListeResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="ArzneimittelListe" type="tns:ArzneimittelListe"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArzneimittelListeContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:RabattContainer"/>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="IndikatorWirkstoffListeEintrag">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="AtcCode" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="DiagnoseCode" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfIndikatorWirkstoffListeEintrag">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="IndikatorWirkstoffListeEintrag" type="tns:IndikatorWirkstoffListeEintrag"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="LiefereIndikatorWirkstoffListeResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="IndikatorWirkstoffListe" type="tns:ArrayOfIndikatorWirkstoffListeEintrag"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="IndikatorWirkstoffListeContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBasis">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="VertragsIdentifikator" type="s:string"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="Substitution">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="AtcCode" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="AtcName" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Packungen" type="tns:ArrayOfPackung"/>
          <s:element minOccurs="1" maxOccurs="1" name="Prioritaet" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" name="Beschreibungen" type="tns:ArrayOfBeschreibungsText"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfSubstitution">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Substitution" type="tns:Substitution"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="SubstitutionResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="Substitutionen" type="tns:ArrayOfSubstitution"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="SubstitutionContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:RabattContainer">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="PZN" type="s:string"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArrayOfPackung">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Packung" type="tns:Packung"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Packung">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="HerstellerName" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="IstBTM" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="IstLifestyle" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="IstNegativliste" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="IstOTC" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="Name" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Preis" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="PZN" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="ArzneimittelKategorie" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Wirkstaerke" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Sortierung" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="AusserVertrieb" type="s:boolean"/>
          <s:element minOccurs="0" maxOccurs="1" name="PackungsGroesse" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Zuzahlung" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Festbetrag" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="IstInPriscusListe" type="s:boolean" default="false"/>
          <s:element minOccurs="0" maxOccurs="1" name="Meldungen" type="tns:ArrayOfMeldungsText"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="BeschreibungsText">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Index" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" name="Inhalt" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Quelle" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="MeldungsText">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Logik" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="Typ" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" name="Inhalt" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfBeschreibungsText">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Beschreibung" type="tns:BeschreibungsText"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfMeldungsText">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Meldung" type="tns:MeldungsText"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="LiefereArzneimittelInformationenResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="Packungen" type="tns:ArrayOfPackung"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="RabattContainer" abstract="true">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBasis">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="IK" type="tns:IkKrankenkasseType"/>
              <s:element minOccurs="1" maxOccurs="1" name="ReferenzDatum" type="s:date"/>
              <s:element minOccurs="1" maxOccurs="1" name="VertragsIdentifikator" type="s:string"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArzneimittelInformationenContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:RabattContainer">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="PZNs" type="tns:ArrayOfString"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="VertragsContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerVertragspartnerBasis">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="Vertragskontext" type="tns:Vertragskontext"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ContainerBasis">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ArztInformationsSystem" type="tns:SoftwareInformation"/>
        </s:sequence>
      </s:complexType>
	  <s:complexType name="ContainerBsnrBasis" abstract="true">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBasis">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="AbsenderBsnr" type="s:string"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
	  <s:complexType name="ContainerVertragspartnerBasis" abstract="true">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="Vertragspartneridentifikator" type="s:string"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="SoftwareInformation">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="KbvPruefnummer" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Version" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Vorname" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Nachname" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Name" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Organisation" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Strasse" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Plz" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Stadt" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Telefon" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Telefax" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="TelefonMobil" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="SystemOid" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Vertragskontext">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="VertragsIdentifikator" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="HonoraranlageIdentifikator" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="AbrechnungsJahr" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="AbrechnungsQuartal" type="s:int"/>
        </s:sequence>
      </s:complexType>      
      <s:complexType name="ArrayOfAbrechnungsDokumentation">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="AbrechnungsDokumentation" type="tns:AbrechnungsDokumentation"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="AbrechnungsDokumentation">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Patient" type="tns:Patient"/>
          <s:element minOccurs="0" maxOccurs="1" name="Diagnosen" type="tns:ArrayOfDiagnose"/>
          <s:element minOccurs="0" maxOccurs="1" name="Leistungen" type="tns:ArrayOfLeistung"/>
          <s:element minOccurs="0" maxOccurs="1" name="Praxisgebuehren" type="tns:ArrayOfPraxisgebuehr"/>
          <s:element minOccurs="0" maxOccurs="1" name="Ueberweisungen" type="tns:ArrayOfUeberweisung"/>
          <s:element minOccurs="0" maxOccurs="1" name="Operationen" type="tns:ArrayOfOperation"/>
          <s:element minOccurs="1" maxOccurs="1" name="UnfallkennzeichenGesetzt" type="s:boolean"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfDiagnose">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Diagnose" type="tns:Diagnose"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Diagnose">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="CodeSystemName" type="tns:DiagnoseCodeSystemName"/>
          <s:element minOccurs="1" maxOccurs="1" name="DokumentationsDatum" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="DiagnoseCode" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Sicherheit" type="tns:DiagnoseSicherheit"/>
          <s:element minOccurs="1" maxOccurs="1" name="Seitenlokalisation" type="tns:DiagnoseSeitenlokalisation"/>
          <s:element minOccurs="1" maxOccurs="1" name="IstDauerDiagnose" type="s:boolean"/>
          <s:element minOccurs="1" maxOccurs="1" name="DiagnoseId" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Stellvertreter" type="tns:Stellvertreter"/>
		  <s:element minOccurs="0" maxOccurs="1" name="Diagnoseausnahmetatbestand" type="s:string"/>
		  <s:element minOccurs="0" maxOccurs="1" name="Diagnoseerlaeuterung" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="DiagnoseCodeSystemName">
        <s:restriction base="s:string">
          <s:enumeration value="None"/>  
          <s:enumeration value="Icd10gm2020"/>
          <s:enumeration value="Icd10gm2021"/>
          <s:enumeration value="Icd10gm2022"/>
		  <s:enumeration value="Icd10gm2023"/>
		  <s:enumeration value="Icd10gm2024"/>
		  <s:enumeration value="Icd10gm2025"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="DiagnoseSicherheit">
        <s:restriction base="s:string">
          <s:enumeration value="KeineAngabe"/>
          <s:enumeration value="G"/>
          <s:enumeration value="V"/>
          <s:enumeration value="Z"/>
          <s:enumeration value="A"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="DiagnoseSeitenlokalisation">
        <s:restriction base="s:string">
          <s:enumeration value="U"/>
          <s:enumeration value="L"/>
          <s:enumeration value="R"/>
          <s:enumeration value="B"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="Stellvertreter">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Patient">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="AktuelleVertragsteilnahmen" type="tns:ArrayOfVertragsteilnahme"/>
          <s:element minOccurs="1" maxOccurs="1" name="PatientenId" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Nachname" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Vorname" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Geburtsdatum" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Geschlecht" type="tns:PatientGeschlecht"/>
          <s:element minOccurs="1" maxOccurs="1" name="Versicherungsnachweis" type="tns:Versicherungsnachweis"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfVertragsteilnahme">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Vertragsteilnahme" type="tns:Vertragsteilnahme"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Vertragsteilnahme">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="VertragsIdentifikator" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="IstVertreterteilnahme" type="s:boolean"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="PatientGeschlecht">
        <s:restriction base="s:string">
          <s:enumeration value="U"/>
          <s:enumeration value="M"/>
          <s:enumeration value="W"/>
          <s:enumeration value="X"/>
          <s:enumeration value="D"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="Versicherungsnachweis">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="KrankenkassenIk" type="tns:IkKrankenkasseType"/>
          <s:element minOccurs="1" maxOccurs="1" name="VersichertenNummer" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="VersichertenArt" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="BesonderePersonengruppe" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="DmpKennzeichnung" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfLeistung">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Leistung" type="tns:Leistung"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Leistung">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Leistungsdatum" nillable="true" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="Leistungsziffer" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Zusatzinformation" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="LeistungsId" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Stellvertreter" type="tns:Stellvertreter"/>
          <s:element minOccurs="0" maxOccurs="1" name="InVertretungFuer" type="tns:ArztIdentifikation"/>
          <s:element minOccurs="0" maxOccurs="1" name="UeberweisenderArzt" type="tns:ArztIdentifikation"/>
          <s:element minOccurs="0" maxOccurs="1" name="Anforderungszeitpunkt" type="tns:Anforderungszeitpunkt"/>
          <s:element minOccurs="0" maxOccurs="1" name="Abrechnungsbegruendung" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Sachkosten" type="tns:ArrayOfSachkosten"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Anforderungszeitpunkt">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Datum" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="Uhrzeit" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArztIdentifikation">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Bsnr" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfPraxisgebuehr">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Praxisgebuehr" type="tns:Praxisgebuehr"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Praxisgebuehr">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="PraxisgebuehrId" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Datum" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="Ziffer" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Stellvertreter" type="tns:Stellvertreter"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfUeberweisung">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Ueberweisung" type="tns:Ueberweisung"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Ueberweisung">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="UeberweisungId" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="AuftragsArt" type="tns:UeberweisungAuftragsart"/>
          <s:element minOccurs="1" maxOccurs="1" name="Datum" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="Jahr" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="Quartal" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="1" name="UeberweisungAn" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Ueberweisungsart" type="tns:Ueberweisungsart"/>
          <s:element minOccurs="0" maxOccurs="1" name="Stellvertreter" type="tns:Stellvertreter"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="UeberweisungAuftragsart">
        <s:restriction base="s:string">
          <s:enumeration value="KeineAngabe"/>
          <s:enumeration value="Auftragsleistung"/>
          <s:enumeration value="Konsiliaruntersuchung"/>
          <s:enumeration value="MitWeiterbehandlung"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="Ueberweisungsart">
        <s:restriction base="s:string">
          <s:enumeration value="KeineAngabe"/>
          <s:enumeration value="Kurativ"/>
          <s:enumeration value="Praeventiv"/>
          <s:enumeration value="Belegaerztlich"/>
          <s:enumeration value="Paragraph116b"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="Uebertragungsart">
        <s:restriction base="s:string">
          <s:enumeration value="Online"/>
          <s:enumeration value="Datentraeger"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="StarteAbrechnungResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="StarteAbrechnungResult" type="tns:AbrechnungsResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="AbrechnungsResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:TransferResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="AbrechnungsAnzahlen" type="tns:AbrechnungsAnzahlenTyp"/>
              <s:element minOccurs="0" maxOccurs="1" name="UebermittlungsBeleg" type="tns:UebermittlungsBelegTyp"/>
              <s:element minOccurs="0" maxOccurs="1" name="Datentraeger" type="tns:DatentraegerTyp"/>
              <s:element minOccurs="0" maxOccurs="1" name="Uebermittlungsprotokoll" type="s:base64Binary"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="SendeVorEinschreibeLeistung">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:SendeVorEinschreibeLeistungContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="SendeVorEinschreibeLeistungContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:VertragsContainer">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="Patient" type="tns:Patient"/>
              <s:element minOccurs="1" maxOccurs="1" name="VorEinschreibeLeistung" type="tns:VorEinschreibeLeistung"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="NurPrueflauf" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="VorEinschreibeLeistung">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Leistungsdatum" nillable="true" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="Leistungsziffer" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="LeistungsId" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:element name="SendeVorEinschreibeLeistungResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SendeVorEinschreibeLeistungResult" type="tns:TransferResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="TransferResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="TransferId" type="s:string"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="AbstractResultat" abstract="true">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Meldungen" type="tns:ArrayOfMeldung"/>
          <s:element minOccurs="1" maxOccurs="1" name="Status" type="tns:ResultatStatus"/>
          <s:element minOccurs="1" maxOccurs="1" name="UebermittlungsStatus" type="tns:UebermittlungsStatus"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfMeldung">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Meldung" type="tns:Meldung"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Meldung">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Nachricht" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Code" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Art" type="tns:Meldungsart"/>
          <s:element minOccurs="1" maxOccurs="1" name="Kategorie" type="tns:Meldungskategorie"/>
          <s:element minOccurs="0" maxOccurs="1" name="Referenzen" type="tns:ArrayOfReferenz"/>
          <s:element minOccurs="1" maxOccurs="1" name="UebermittlungsStatus" type="tns:UebermittlungsStatus"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="Meldungsart">
        <s:restriction base="s:string">
          <s:enumeration value="Unbekannt"/>
          <s:enumeration value="Information"/>
          <s:enumeration value="Warnung"/>
          <s:enumeration value="Fehler"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="Meldungskategorie">
        <s:restriction base="s:string">
          <s:enumeration value="KeineAngabe"/>
          <s:enumeration value="Validierung"/>
          <s:enumeration value="Laufzeit"/>
          <s:enumeration value="Rechenzentrum"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="ArrayOfReferenz">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Referenz" type="tns:Referenz"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Referenz">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Id" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="ReferenzTyp" type="tns:ReferenzTyp"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="ReferenzTyp">
        <s:restriction base="s:string">
          <s:enumeration value="KeineAngabe"/>
          <s:enumeration value="Leistung"/>
          <s:enumeration value="Patient"/>
          <s:enumeration value="Diagnose"/>
          <s:enumeration value="Ueberweisung"/>
          <s:enumeration value="Praxisgebuehr"/>
          <s:enumeration value="Vertragsidentifikator"/>
          <s:enumeration value="Arzneimittel"/>
          <s:enumeration value="Rezept"/>
          <s:enumeration value="Operation"/>
          <s:enumeration value="Zielwert"/>
          <s:enumeration value="Istwert"/>
          <s:enumeration value="Erkrankungsstufe"/>
          <s:enumeration value="Hinderungsfaktor"/>
          <s:enumeration value="IstFremdeingeschrieben"/>
          <s:enumeration value="IstNichtFremdeingeschrieben"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="UebermittlungsStatus">
        <s:restriction base="s:string">
          <s:enumeration value="KeineAngabe"/>
          <s:enumeration value="OK"/>
          <s:enumeration value="FachlicheVerletzung"/>
          <s:enumeration value="KeineBerechtigungWebService"/>
          <s:enumeration value="Wartung"/>
          <s:enumeration value="InternerFehler"/>
          <s:enumeration value="UngueltigeStammdaten"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="ResultatStatus">
        <s:restriction base="s:string">
          <s:enumeration value="Unbekannt"/>
          <s:enumeration value="OK"/>
          <s:enumeration value="Fehlgeschlagen"/>
          <s:enumeration value="TeilweiseVerarbeitet"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="DatentraegerTyp">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="DatentraegerID" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="IsoImage" type="s:base64Binary"/>
          <s:element minOccurs="1" maxOccurs="1" name="Begleitschreiben" type="s:base64Binary"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="AbrechnungsAnzahlenTyp">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="LeistungenAnzahl" type="tns:LeistungenAnzahlTyp"/>
          <s:element minOccurs="1" maxOccurs="1" name="DiagnosenAnzahl" type="tns:DiagnosenAnzahlTyp"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="LeistungenAnzahlTyp">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Anzahl" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="unbounded" name="LeistungenAnzahlDetail" type="tns:DokumentationsAnzahlDetailTyp"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="DiagnosenAnzahlTyp">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Anzahl" type="s:int"/>
          <s:element minOccurs="0" maxOccurs="unbounded" name="DiagnosenAnzahlDetail" type="tns:DokumentationsAnzahlDetailTyp"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="DokumentationsAnzahlDetailTyp">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Auspraegung" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Anzahl" type="s:int"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="UebermittlungsBelegTyp">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="PatientenIds" type="tns:IdListeType"/>
          <s:element minOccurs="1" maxOccurs="1" name="LeistungsIds" type="tns:IdListeType"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="IdListeType">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Id" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfString">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="string" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="UebertragungswegType">
        <s:restriction base="s:string">
          <s:enumeration value="Unbekannt"/>
          <s:enumeration value="Offline"/>
          <s:enumeration value="Online"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="SetzeUebertragungsweg">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:SetzeUebertragungswegContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="SetzeUebertragungswegContainer">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Uebertragungsweg" type="tns:UebertragungswegType"/>
        </s:sequence>
      </s:complexType>
      <s:element name="SetzeUebertragungswegResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SetzeUebertragungswegResult" type="tns:SetzeUebertragungswegResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="SetzeUebertragungswegResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat"/>
        </s:complexContent>
      </s:complexType>
      <s:element name="LiefereKrankheitsbildAuswertung">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:KrankheitsbildAuswertungContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="KrankheitsbildAuswertungContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerVertragspartnerBasis">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="VertragsIdentifikator" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="AuswertungsKontext" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="AuswertungsJahr" type="s:int"/>
              <s:element minOccurs="1" maxOccurs="1" name="AuswertungsQuartal" type="s:int"/>
              <s:element minOccurs="1" maxOccurs="1" name="Patienten" type="tns:ArrayOfKrankheitsbildAuswertungPatient"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArrayOfKrankheitsbildAuswertungPatient">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Patient" type="tns:KrankheitsbildAuswertungPatient"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="KrankheitsbildAuswertungPatient">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="PatientenId" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Diagnosen" type="tns:ArrayOfString"/>
        </s:sequence>
      </s:complexType>
      <s:element name="LiefereKrankheitsbildAuswertungResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LiefereKrankheitsbildAuswertungResult" type="tns:KrankheitsbildAuswertungResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="KrankheitsbildAuswertungResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="Patienten" type="tns:ArrayOfKrankheitsbildAuswertungResultatPatient"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArrayOfKrankheitsbildAuswertungResultatPatient">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Patient" type="tns:KrankheitsbildAuswertungResultatPatient"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="KrankheitsbildAuswertungResultatPatient">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="PatientenId" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Krankheitsbilder" type="tns:ArrayOfKrankheitsbild"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfKrankheitsbild">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Krankheitsbild" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfOperation">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Operation" type="tns:Operation"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Operation">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="OperationId" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Stellvertreter" type="tns:Stellvertreter"/>
          <s:element minOccurs="0" maxOccurs="1" name="InVertretungFuer" type="tns:ArztIdentifikation"/>
          <s:element minOccurs="1" maxOccurs="1" name="OperationsDatum" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="OperationsSchluessel" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Seitenlokalisation" type="tns:DiagnoseSeitenlokalisation"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfSachkosten">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Sachkosten" type="tns:Sachkosten"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Sachkosten">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Betrag" type="tns:SachkostenBetragType"/>
          <s:element minOccurs="1" maxOccurs="1" name="Bezeichnungen" type="tns:ArrayOfString"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="IkType">
        <s:restriction base="s:string">
          <s:pattern value="\d{9}"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="IkKrankenkasseType">
        <s:restriction base="s:string">
          <s:pattern value="10\d{7}"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="SachkostenBetragType">
        <s:restriction base="s:string">
          <s:pattern value="\d{1,10}\.\d{2}"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="ZielwertWertType">
        <s:restriction base="s:string">
          <s:pattern value="\d+|\d+\.\d+"/>
        </s:restriction>
      </s:simpleType>
      
      <s:simpleType name="KontextType">
        <s:restriction base="s:string">
          <s:enumeration value="Arztbrief"/>
          <s:enumeration value="TeleScanDermatologie"/>
        </s:restriction>
      </s:simpleType>
      
      <s:element name="LiefereBereitgestellteTeilnehmerverzeichnisse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="1" maxOccurs="1" name="container" type="tns:LiefereBereitgestellteTeilnehmerverzeichnisseAnfrageContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereBereitgestellteTeilnehmerverzeichnisseAnfrageContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerVertragspartnerBasis">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="LiefereBereitgestellteTeilnehmerverzeichnisseResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LiefereBereitgestellteTeilnehmerverzeichnisseResult" type="tns:LiefereBereitgestellteTeilnehmerverzeichnisseResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereBereitgestellteTeilnehmerverzeichnisseResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="Teilnehmerverzeichnisse" type="tns:ArrayOfTeilnehmerverzeichnisInfo"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArrayOfTeilnehmerverzeichnisInfo">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="TeilnehmerverzeichnisInfo" type="tns:TeilnehmerverzeichnisInfo"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="TeilnehmerverzeichnisInfo">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="DokumentIdentifikator" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="VertragsIdentifikator" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Jahr" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="Quartal" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="Version" type="s:int"/>
        </s:sequence>
      </s:complexType>

      <s:element name="LiefereTeilnehmerverzeichnis">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:LiefereTeilnehmerverzeichnisAnfrageContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereTeilnehmerverzeichnisAnfrageContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerVertragspartnerBasis">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="Abrufcode" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="DokumentIdentifikator" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="LiefereTeilnehmerverzeichnisResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LiefereTeilnehmerverzeichnisResult" type="tns:LiefereTeilnehmerverzeichnisResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereTeilnehmerverzeichnisResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="PatientParticipationList">
                <s:complexType mixed="true">
                  <s:sequence>
                    <s:any/>
                  </s:sequence>
                </s:complexType>
              </s:element>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>

      <s:element name="ValidiereAu">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:ValidiereAuContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ValidiereAuContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="FhirDokument">
                <s:complexType mixed="true">
                  <s:sequence>
                    <s:any/>
                  </s:sequence>
                </s:complexType>
              </s:element>
			  
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="ValidiereAuResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ValidiereAuResult" type="tns:ValidiereAuResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ValidiereAuResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
          </s:extension>
        </s:complexContent>
      </s:complexType>

      <s:element name="ValidiereEinweisungsbrief">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:ValidiereEinweisungsbriefContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ValidiereEinweisungsbriefContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CdaDokument">
                <s:complexType mixed="true">
                  <s:sequence>
                    <s:any/>
                  </s:sequence>
                </s:complexType>
              </s:element>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="ValidiereEinweisungsbriefResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ValidiereEinweisungsbriefResult" type="tns:ValidiereEinweisungsbriefResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ValidiereEinweisungsbriefResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
          </s:extension>
        </s:complexContent>
      </s:complexType>
      
      <s:element name="PruefeVersichertenteilnahmeVernetzung">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:PruefeVersichertenteilnahmeVernetzungContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="PruefeVersichertenteilnahmeVernetzungContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="VertragsIdentifikator" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="Versichertennummer" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="PruefeVersichertenteilnahmeVernetzungResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="PruefeVersichertenteilnahmeVernetzungResult" type="tns:PruefeVersichertenteilnahmeVernetzungResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="PruefeVersichertenteilnahmeVernetzungResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:TransferResultat">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="IstVersichertenteilnahmeVernetzung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>

      <s:element name="SigniereCdaDokument">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:SigniereCdaDokumentContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="SigniereCdaDokumentContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="CdaDokument">
                <s:complexType mixed="true">
                  <s:sequence>
                    <s:any/>
                  </s:sequence>
                </s:complexType>
              </s:element>
              <s:element minOccurs="1" maxOccurs="1" name="ZertifikatArzt" type="s:base64Binary"/>
              <s:element minOccurs="1" maxOccurs="1" name="Kennwort" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="SigniereCdaDokumentResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SigniereCdaDokumentResult" type="tns:SigniereCdaDokumentResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="SigniereCdaDokumentResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="SignaturCdaDokument">
                <s:complexType mixed="true">
                  <s:sequence>
                    <s:any/>
                  </s:sequence>
                </s:complexType>
              </s:element>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>

      <s:element name="PruefeSignaturCdaDokument">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:PruefeSignaturCdaDokumentContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="PruefeSignaturCdaDokumentContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/> 	
              <s:element minOccurs="1" maxOccurs="1" name="CdaDokument">
                <s:complexType mixed="true">
                  <s:sequence>
                    <s:any/>
                  </s:sequence>
                </s:complexType>
              </s:element>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="PruefeSignaturCdaDokumentResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="PruefeSignaturCdaDokumentResult" type="tns:PruefeSignaturCdaDokumentResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="PruefeSignaturCdaDokumentResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="SignaturInfo" type="tns:SignaturInfoType"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="SignaturInfoType">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="SignaturZeitpunkt" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="Unterzeichner" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Bsnr" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="ZertifikatGueltigVon" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="ZertifikatGueltigBis" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="ZertifikatAussteller" type="s:string"/>
        </s:sequence>
      </s:complexType>

      <s:element name="ErzeugeZertifikatArzt">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:ErzeugeZertifikatArztContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ErzeugeZertifikatArztContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="ArztIdentifikation" type="tns:ArztIdentifikationType"/>
              <s:element minOccurs="1" maxOccurs="1" name="Kennwort" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArztIdentifikationType">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="ArztIdentifikationscode" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="ArztIdentifikationZertifikat" type="tns:ArztIdentifikationZertifikatType"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArztIdentifikationZertifikatType">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="ZertifikatArzt" type="s:base64Binary"/>
          <s:element minOccurs="1" maxOccurs="1" name="Kennwort" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:element name="ErzeugeZertifikatArztResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ErzeugeZertifikatArztResult" type="tns:ErzeugeZertifikatArztResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ErzeugeZertifikatArztResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="ZertifikatArzt" type="s:base64Binary"/>
              <s:element minOccurs="0" maxOccurs="1" name="ZertifikatArztInfo" type="tns:ZertifikatArztInfoType"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ZertifikatArztInfoType">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Zertifikatsinhaber" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Bsnr" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="ZertifikatGueltigVon" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="ZertifikatGueltigBis" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="ZertifikatAussteller" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Seriennummer" type="s:string"/>
        </s:sequence>
      </s:complexType>

      <s:element name="ErzeugeZertifikatBetriebsstaette">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:ErzeugeZertifikatBetriebsstaetteContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ErzeugeZertifikatBetriebsstaetteContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="ArztIdentifikationZertifikat" type="tns:ArztIdentifikationZertifikatType"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="ErzeugeZertifikatBetriebsstaetteResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ErzeugeZertifikatBetriebsstaetteResult" type="tns:ErzeugeZertifikatBetriebsstaetteResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ErzeugeZertifikatBetriebsstaetteResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="ZertifikatBetriebsstaette" type="s:base64Binary"/>
              <s:element minOccurs="0" maxOccurs="1" name="ZertifikatBetriebsstaetteInfo" type="tns:ZertifikatBetriebsstaetteInfoType"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ZertifikatBetriebsstaetteInfoType">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Zertifikatsinhaber" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Bsnr" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="ZertifikatGueltigVon" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="ZertifikatGueltigBis" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="ZertifikatAussteller" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Seriennummer" type="s:string"/>
        </s:sequence>
      </s:complexType>

      <s:element name="LiefereEmpfaengergruppenVernetzung">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:LiefereEmpfaengergruppenVernetzungContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereEmpfaengergruppenVernetzungContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="Kontext" type="tns:KontextType"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="LiefereEmpfaengergruppenVernetzungResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LiefereEmpfaengergruppenVernetzungResult" type="tns:LiefereEmpfaengergruppenVernetzungResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereEmpfaengergruppenVernetzungResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="Empfaengergruppen" type="tns:ArrayOfEmpfaengergruppe"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArrayOfEmpfaengergruppe">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Empfaengergruppe" type="tns:Empfaengergruppe"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Empfaengergruppe">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="EmpfaengergruppeIdentifikator" type="s:int"/>
          <s:element minOccurs="1" maxOccurs="1" name="EmpfaengergruppeBezeichnung" type="s:string"/>
        </s:sequence>
      </s:complexType>

      <s:element name="SucheEmpfaengerVernetzung">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:SucheEmpfaengerVernetzungContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="SucheEmpfaengerVernetzungContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="Suchbegriff" type="tns:SuchbegriffEmpfaengerVernetzung"/>
              <s:element minOccurs="1" maxOccurs="1" name="Kontext" type="tns:KontextType"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="SuchbegriffEmpfaengerVernetzung">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="NameArzt" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Plz" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Ort" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Strasse" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Lanr" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Bsnr" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="EmpfaengergruppeIdentifikator" type="s:int"/>
        </s:sequence>
      </s:complexType>
      <s:element name="SucheEmpfaengerVernetzungResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SucheEmpfaengerVernetzungResult" type="tns:SucheEmpfaengerVernetzungResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="SucheEmpfaengerVernetzungResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="Betriebsstaetten" type="tns:ArrayOfBetriebsstaetteVernetzung"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArrayOfBetriebsstaetteVernetzung">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Betriebsstaette" type="tns:BetriebsstaetteVernetzung"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="BetriebsstaetteVernetzung">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Bsnr" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Bezeichnung" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Adresse" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Aerzte" type="tns:ArrayOfArztVernetzung"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArrayOfArztVernetzung">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Arzt" type="tns:ArztVernetzung"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArztVernetzung">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Bezeichnung" type="s:string"/>
        </s:sequence>
      </s:complexType>

      <s:element name="SendeArztbrief">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:SendeArztbriefContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="SendeArztbriefContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="ArztbriefEmpfaenger" type="tns:ArrayOfArztbriefEmpfaenger"/>
              <s:element minOccurs="1" maxOccurs="1" name="CdaDokument">
                <s:complexType mixed="true">
                  <s:sequence>
                    <s:any/>
                  </s:sequence>
                </s:complexType>
              </s:element>
              <s:element minOccurs="0" maxOccurs="1" name="SignaturCdaDokument">
                <s:complexType mixed="true">
                  <s:sequence>
                    <s:any/>
                  </s:sequence>
                </s:complexType>
              </s:element>
              <s:element minOccurs="0" maxOccurs="1" name="PdfDokument" type="s:base64Binary"/>
              <s:element minOccurs="0" maxOccurs="1" name="NurPrueflauf" type="s:boolean" default="false"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArrayOfArztbriefEmpfaenger">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="ArztbriefEmpfaenger" type="tns:ArztbriefEmpfaenger"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArztbriefEmpfaenger">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Bsnr" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="EmpfaengergruppeIdentifikator" type="s:int"/>
        </s:sequence>
      </s:complexType>
      <s:element name="SendeArztbriefResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SendeArztbriefResult" type="tns:SendeArztbriefResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="SendeArztbriefResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:TransferResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="DokumentIdentifikator" type="s:string"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>

      <s:element name="PruefeAufBereitgestellteArztbriefe">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:PruefeAufBereitgestellteArztbriefeContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="PruefeAufBereitgestellteArztbriefeContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="Versichertennummer" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="ZertifikatBetriebsstaette" type="s:base64Binary"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="PruefeAufBereitgestellteArztbriefeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="PruefeAufBereitgestellteArztbriefeResult" type="tns:PruefeAufBereitgestellteArztbriefeResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="PruefeAufBereitgestellteArztbriefeResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="IstArztbriefBereitgestellt" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>

      <s:element name="LiefereArztbriefe">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:LiefereArztbriefeContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereArztbriefeContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="0" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="Versichertennummer" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="ZertifikatBetriebsstaette" type="s:base64Binary"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="LiefereArztbriefeResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LiefereArztbriefeResult" type="tns:LiefereArztbriefeResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereArztbriefeResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="Arztbriefe" type="tns:ArrayOfArztbrief"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArrayOfArztbrief">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Arztbrief" type="tns:Arztbrief"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Arztbrief">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="DokumentIdentifikator" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="DokumentZugriffstoken" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Versichertennummer" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="AbsenderBsnr" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="CdaDokument">
            <s:complexType mixed="true">
              <s:sequence>
                <s:any/>
              </s:sequence>
            </s:complexType>
          </s:element>
          <s:element minOccurs="1" maxOccurs="1" name="PdfDokument" type="s:base64Binary"/>
        </s:sequence>
      </s:complexType>

      <s:complexType name="ArrayOfDokumentIdentifikator">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="DokumentIdentifikator" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="EmpfangsstatusArztbrief">
        <s:restriction base="s:string">
          <s:enumeration value="Unbekannt"/>
          <s:enumeration value="InZustellung"/>
          <s:enumeration value="Zugestellt"/>
          <s:enumeration value="NichtZugestellt"/>
        </s:restriction>
      </s:simpleType>

      <s:complexType name="ArztbriefIdentifikationen">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="ArztbriefIdentifikation" type="tns:ArztbriefIdentifikation"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="ArztbriefIdentifikation">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="DokumentIdentifikator" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="DokumentZugriffstoken" type="s:string"/>
        </s:sequence>
      </s:complexType>

      <s:element name="LiefereMedikationsinformation">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:LiefereMedikationsinformationContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereMedikationsinformationContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="ZertifikatBetriebsstaette" type="s:base64Binary"/>
              <s:element minOccurs="1" maxOccurs="1" name="Versichertennummer" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="LiefereMedikationsinformationResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LiefereMedikationsinformationResult" type="tns:LiefereMedikationsinformationResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereMedikationsinformationResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CdaDokument">
                <s:complexType mixed="true">
                  <s:sequence>
                    <s:any/>
                  </s:sequence>
                </s:complexType>
              </s:element>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>

      <s:element name="SendeMedikationsinformation">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:SendeMedikationsinformationContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="SendeMedikationsinformationContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="CdaDokument">
                <s:complexType mixed="true">
                  <s:sequence>
                    <s:any/>
                  </s:sequence>
                </s:complexType>
              </s:element>
              <s:element minOccurs="0" maxOccurs="1" name="NurPrueflauf" type="s:boolean" default="false"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="SendeMedikationsinformationResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SendeMedikationsinformationResult" type="tns:SendeMedikationsinformationResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="SendeMedikationsinformationResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:TransferResultat">
          </s:extension>
        </s:complexContent>
      </s:complexType>

      <s:element name="SendeMedikationsinformationPflegearzt">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:SendeMedikationsinformationPflegearztContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="SendeMedikationsinformationPflegearztContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="ZertifikatBetriebsstaette" type="s:base64Binary"/>
              <s:element minOccurs="1" maxOccurs="1" name="Patient" type="tns:Patient"/>
              <s:element minOccurs="0" maxOccurs="1" name="EntfernePflegearztZuordnung" type="s:boolean" default="false"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="SendeMedikationsinformationPflegearztResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SendeMedikationsinformationPflegearztResult" type="tns:SendeMedikationsinformationPflegearztResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="SendeMedikationsinformationPflegearztResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat"/>
        </s:complexContent>
      </s:complexType>

      <s:element name="LiefereMedikationsinformationErgaenzungen">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:LiefereMedikationsinformationErgaenzungenContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereMedikationsinformationErgaenzungenContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="ZertifikatBetriebsstaette" type="s:base64Binary"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="LiefereMedikationsinformationErgaenzungenResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LiefereMedikationsinformationErgaenzungenResult" type="tns:LiefereMedikationsinformationErgaenzungenResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereMedikationsinformationErgaenzungenResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="Ergaenzungen" type="tns:ArrayOfMedikationsinformationErgaenzung"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArrayOfMedikationsinformationErgaenzung">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Ergaenzung" type="tns:MedikationsinformationErgaenzung"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="MedikationsinformationErgaenzung">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Versichertennummer" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="LetzteAenderung" type="s:dateTime"/>
        </s:sequence>
      </s:complexType>

      <s:element name="ErzeugePdfAusCdaDokument">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:ErzeugePdfAusCdaDokumentContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ErzeugePdfAusCdaDokumentContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="CdaDokument">
                <s:complexType mixed="true">
                  <s:sequence>
                    <s:any/>
                  </s:sequence>
                </s:complexType>
              </s:element>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="ErzeugePdfAusCdaDokumentResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ErzeugePdfAusCdaDokumentResult" type="tns:ErzeugePdfAusCdaDokumentResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ErzeugePdfAusCdaDokumentResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="ErzeugtesPdf" type="s:base64Binary"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>

      <s:element name="SendeTelekonsil">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:SendeTelekonsilContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="SendeTelekonsilContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="0" maxOccurs="1" name="TelekonsilEmpfaenger" type="tns:ArrayOfTelekonsilEmpfaenger"/>
              <s:element minOccurs="1" maxOccurs="1" name="CdaDokument">
                <s:complexType mixed="true">
                  <s:sequence>
                    <s:any/>
                  </s:sequence>
                </s:complexType>
              </s:element>
              <s:element minOccurs="0" maxOccurs="1" name="SignaturCdaDokument">
                <s:complexType mixed="true">
                  <s:sequence>
                    <s:any/>
                  </s:sequence>
                </s:complexType>
              </s:element>
              <s:element minOccurs="0" maxOccurs="1" name="NurPrueflauf" type="s:boolean" default="false"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArrayOfTelekonsilEmpfaenger">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="TelekonsilEmpfaenger" type="tns:TelekonsilEmpfaenger"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="TelekonsilEmpfaenger">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="1" name="Lanr" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="EmpfaengergruppeIdentifikator" type="s:int"/>
        </s:sequence>
      </s:complexType>
      <s:element name="SendeTelekonsilResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SendeTelekonsilResult" type="tns:SendeTelekonsilResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="SendeTelekonsilResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:TransferResultat"/>
        </s:complexContent>
      </s:complexType>

      <s:element name="LiefereTelekonsilMetainformationen">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:LiefereTelekonsilMetainformationenContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereTelekonsilMetainformationenContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="ZertifikatBetriebsstaette" type="s:base64Binary"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="TelekonsilTyp" type="tns:TelekonsilTyp"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>

      <s:simpleType name="TelekonsilTyp">
        <s:restriction base="s:string">
          <s:enumeration value="Adressiert"/>
          <s:enumeration value="Gerichtet"/>
        </s:restriction>
      </s:simpleType>

      <s:element name="LiefereTelekonsilMetainformationenResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LiefereTelekonsilMetainformationenResult" type="tns:LiefereTelekonsilMetainformationenResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereTelekonsilMetainformationenResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="TelekonsileMetainformationen" type="tns:ArrayOfTelekonsileMetainformation"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArrayOfTelekonsileMetainformation">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="TelekonsileMetainformation" type="tns:TelekonsileMetainformation"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="TelekonsileMetainformation">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="DokumentIdentifikator" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="VorgangsId" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="SenderArzt" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="BearbeitungsstatusTelekonsil" type="tns:BearbeitungsstatusTelekonsil"/>
          <s:element minOccurs="1" maxOccurs="1" name="LetzterBearbeitungszeitpunkt" type="s:dateTime"/>
          <s:element minOccurs="1" maxOccurs="1" name="SenderArztLanr" type="s:string"/>
        </s:sequence>
      </s:complexType>

      <s:element name="LiefereTelekonsile">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:LiefereTelekonsileContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereTelekonsileContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="DokumentIdentifikatoren" type="tns:ArrayOfDokumentIdentifikator"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="ZertifikatBetriebsstaette" type="s:base64Binary"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="LiefereTelekonsileResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LiefereTelekonsileResult" type="tns:LiefereTelekonsileResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereTelekonsileResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="Telekonsile" type="tns:ArrayOfTelekonsil"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArrayOfTelekonsil">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="Telekonsil" type="tns:Telekonsil"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Telekonsil">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="DokumentIdentifikator" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="DokumentZugriffstoken" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="CdaDokument">
            <s:complexType mixed="true">
              <s:sequence>
                <s:any/>
              </s:sequence>
            </s:complexType>
          </s:element>
        </s:sequence>
      </s:complexType>

      <s:element name="LiefereZertifikatinformationVernetzung">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:LiefereZertifikatinformationVernetzungContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereZertifikatinformationVernetzungContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="LiefereZertifikatinformationVernetzungResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LiefereZertifikatinformationVernetzungResult" type="tns:LiefereZertifikatinformationVernetzungResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereZertifikatinformationVernetzungResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="Zertifikationsinformationen" type="tns:ArrayOfZertifikatsinformation"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArrayOfZertifikatsinformation">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="Zertifikatsinformation" type="tns:Zertifikatsinformation"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="Zertifikatsinformation">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Seriennummer" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Sperrstatus" type="tns:SperrstatusTyp"/>
          <s:element minOccurs="1" maxOccurs="1" name="ZertifikatsTyp" type="tns:ZertifikatsTyp"/>
        </s:sequence>
      </s:complexType>

      <s:simpleType name="SperrstatusTyp">
        <s:restriction base="s:string">
          <s:enumeration value="Aktiv"/>
          <s:enumeration value="Gesperrt"/>
          <s:enumeration value="KeineAngabe"/>
        </s:restriction>
      </s:simpleType>
      <s:simpleType name="ZertifikatsTyp">
        <s:restriction base="s:string">
          <s:enumeration value="Arztzertifikat"/>
          <s:enumeration value="Betriebsstaettenzertifikat"/>
        </s:restriction>
      </s:simpleType>
      <s:element name="SendeDokumentenAbrufbestaetigung">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:SendeDokumentenAbrufbestaetigungContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="SendeDokumentenAbrufbestaetigungContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="DokumentIdentifikationen" type="tns:DokumentIdentifikationen"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
              <s:element minOccurs="1" maxOccurs="1" name="DokumentTyp" type="tns:DokumentType"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:simpleType name="DokumentType">
        <s:restriction base="s:string">
          <s:enumeration value="Arztbrief"/>
	        <s:enumeration value="Telekonsil"/>
        </s:restriction>
      </s:simpleType>
      <s:complexType name="DokumentIdentifikationen">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="DokumentIdentifikation" type="tns:DokumentIdentifikation"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="DokumentIdentifikation">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="DokumentIdentifikator" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="DokumentZugriffstoken" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:element name="SendeDokumentenAbrufbestaetigungResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="SendeDokumentenAbrufbestaetigungResult" type="tns:SendeDokumentenAbrufbestaetigungResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="SendeDokumentenAbrufbestaetigungResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat"/>
        </s:complexContent>
      </s:complexType>
      
      <s:element name="LiefereBearbeitungsstatusTelekonsile">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:LiefereBearbeitungsstatusTelekonsileContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereBearbeitungsstatusTelekonsileContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="VorgangsIdentifikatoren" type="tns:ArrayOfVorgangsIdentifikator"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArrayOfVorgangsIdentifikator">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="VorgangsIdentifikator" type="s:string"/>
        </s:sequence>
      </s:complexType>      
      <s:element name="LiefereBearbeitungsstatusTelekonsileResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LiefereBearbeitungsstatusTelekonsileResult" type="tns:LiefereBearbeitungsstatusTelekonsileResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereBearbeitungsstatusTelekonsileResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="BearbeitungsstatusTelekonsile" type="tns:ArrayOfBearbeitungsstatusTelekonsil"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArrayOfBearbeitungsstatusTelekonsil">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="BearbeitungsstatusTelekonsil" type="tns:BearbeitungsstatusTelekonsilVorgang"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="BearbeitungsstatusTelekonsilVorgang">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Bearbeitungsstatus" type="tns:BearbeitungsstatusTelekonsil"/>
          <s:element minOccurs="1" maxOccurs="1" name="VorgangsIdentifikator" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="BearbeitungsstatusTelekonsil">
        <s:restriction base="s:string">
          <s:enumeration value="Beauftragt"/>
          <s:enumeration value="Befundet"/>
          <s:enumeration value="Rueckfrage"/>
          <s:enumeration value="Beantwortet"/>
          <s:enumeration value="Abgebrochen"/>
          <s:enumeration value="Abgeschlossen"/>
        </s:restriction>
      </s:simpleType>
   
      <s:element name="LiefereDokumentenEmpfangsstatus">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:LiefereDokumentenEmpfangsstatusContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereDokumentenEmpfangsstatusContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
			  <s:element minOccurs="1" maxOccurs="1" name="Lanr" type="s:string"/>
              <s:element minOccurs="1" maxOccurs="1" name="DokumentIdentifikatoren" type="tns:ArrayOfDokumentIdentifikator"/>
              <s:element minOccurs="1" maxOccurs="1" name="DokumentTyp" type="tns:DokumentType"/>
              <s:element minOccurs="1" maxOccurs="1" name="Testuebermittlung" type="s:boolean"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="LiefereDokumentenEmpfangsstatusResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="LiefereDokumentenEmpfangsstatusResult" type="tns:LiefereDokumentenEmpfangsstatusResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="LiefereDokumentenEmpfangsstatusResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="EmpfangsstatusDokumente" type="tns:ArrayOfEmpfangsstatusDokument"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:complexType name="ArrayOfEmpfangsstatusDokument">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="unbounded" name="EmpfangsstatusDokument" type="tns:EmpfangsstatusDokument"/>
        </s:sequence>
      </s:complexType>
      <s:complexType name="EmpfangsstatusDokument">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="EmpfangsstatusTypDokument" type="tns:EmpfangsstatusTypeDokument"/>
          <s:element minOccurs="1" maxOccurs="1" name="DokumentIdentifikator" type="s:string"/>
        </s:sequence>
      </s:complexType>
      <s:simpleType name="EmpfangsstatusTypeDokument">
        <s:restriction base="s:string">
          <s:enumeration value="Unbekannt"/>
          <s:enumeration value="InZustellung"/>
          <s:enumeration value="Zugestellt"/>
          <s:enumeration value="NichtZugestellt"/>
        </s:restriction>
      </s:simpleType>

      <s:element name="ExtrahiereZertifikatsinformationVernetzung">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="container" type="tns:ExtrahiereZertifikatsinformationVernetzungContainer"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ExtrahiereZertifikatsinformationVernetzungContainer">
        <s:complexContent mixed="false">
          <s:extension base="tns:ContainerBsnrBasis">
            <s:sequence>
              <s:element minOccurs="1" maxOccurs="1" name="Zertifikat" type="s:base64Binary"/>
              <s:element minOccurs="0" maxOccurs="1" name="Kennwort" type="s:string"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>
      <s:element name="ExtrahiereZertifikatsinformationVernetzungResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="ExtrahiereZertifikatsinformationVernetzungResult" type="tns:ExtrahiereZertifikatsinformationVernetzungResultat"/>
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ExtrahiereZertifikatsinformationVernetzungResultat">
        <s:complexContent mixed="false">
          <s:extension base="tns:AbstractResultat">
            <s:sequence>
              <s:element minOccurs="0" maxOccurs="1" name="Zertifikationsinformation" type="tns:ZertifikatInfoType"/>
            </s:sequence>
          </s:extension>
        </s:complexContent>
      </s:complexType>

      <s:complexType name="ZertifikatInfoType">
        <s:sequence>
          <s:element minOccurs="1" maxOccurs="1" name="Zertifikatsinhaber" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Bsnr" type="s:string"/>
          <s:element minOccurs="0" maxOccurs="1" name="Lanr" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="ZertifikatGueltigVon" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="ZertifikatGueltigBis" type="s:date"/>
          <s:element minOccurs="1" maxOccurs="1" name="ZertifikatAussteller" type="s:string"/>
          <s:element minOccurs="1" maxOccurs="1" name="Seriennummer" type="s:string"/>
        </s:sequence>
      </s:complexType>

    </s:schema>
  </wsdl:types>
  <wsdl:message name="StarteAbrechnungSoapIn">
    <wsdl:part name="parameters" element="tns:StarteAbrechnung"/>
  </wsdl:message>
  <wsdl:message name="StarteAbrechnungSoapOut">
    <wsdl:part name="parameters" element="tns:StarteAbrechnungResponse"/>
  </wsdl:message>
  <wsdl:message name="SendeVorEinschreibeLeistungSoapIn">
    <wsdl:part name="parameters" element="tns:SendeVorEinschreibeLeistung"/>
  </wsdl:message>
  <wsdl:message name="SendeVorEinschreibeLeistungSoapOut">
    <wsdl:part name="parameters" element="tns:SendeVorEinschreibeLeistungResponse"/>
  </wsdl:message>
  <wsdl:message name="LiefereKrankheitsbildAuswertungSoapIn">
    <wsdl:part name="parameters" element="tns:LiefereKrankheitsbildAuswertung"/>
  </wsdl:message>
  <wsdl:message name="LiefereKrankheitsbildAuswertungSoapOut">
    <wsdl:part name="parameters" element="tns:LiefereKrankheitsbildAuswertungResponse"/>
  </wsdl:message>
  <wsdl:message name="StarteVerordnungsdatenUebermittlungSoapOut">
    <wsdl:part name="parameters" element="tns:StarteVerordnungsdatenUebermittlungResponse"/>
  </wsdl:message>
  <wsdl:message name="StarteVerordnungsdatenUebermittlungSoapIn">
    <wsdl:part name="parameters" element="tns:StarteVerordnungsdatenUebermittlung"/>
  </wsdl:message>
  <wsdl:message name="PruefeTeilnahmenListeSoapOut">
    <wsdl:part name="parameters" element="tns:PruefeTeilnahmenListeResponse"/>
  </wsdl:message>
  <wsdl:message name="PruefeTeilnahmenListeSoapIn">
    <wsdl:part name="parameters" element="tns:PruefeTeilnahmenListe"/>
  </wsdl:message>
  <wsdl:message name="SendeTeilnahmeerklaerungSoapOut">
    <wsdl:part name="parameters" element="tns:SendeTeilnahmeerklaerungResponse"/>
  </wsdl:message>
  <wsdl:message name="SendeTeilnahmeerklaerungSoapIn">
    <wsdl:part name="parameters" element="tns:SendeTeilnahmeerklaerung"/>
  </wsdl:message>
  <wsdl:message name="SendeEDMPDatenSoapOut">
    <wsdl:part name="parameters" element="tns:SendeEDMPDatenResponse"/>
  </wsdl:message>
  <wsdl:message name="SendeEDMPDatenSoapIn">
    <wsdl:part name="parameters" element="tns:SendeEDMPDaten"/>
  </wsdl:message>
  <wsdl:message name="SendePracManDatenSoapOut">
    <wsdl:part name="parameters" element="tns:SendePracManDatenResponse"/>
  </wsdl:message>
  <wsdl:message name="SendePracManDatenSoapIn">
    <wsdl:part name="parameters" element="tns:SendePracManDaten"/>
  </wsdl:message>
  <wsdl:message name="MigriereVertragspartnerSoapOut">
    <wsdl:part name="parameters" element="tns:MigriereVertragspartnerResponse"/>
  </wsdl:message>
  <wsdl:message name="MigriereVertragspartnerSoapIn">
    <wsdl:part name="parameters" element="tns:MigriereVertragspartner"/>
  </wsdl:message>
  <wsdl:message name="LiefereArzneimittelListeGZipSoapOut">
    <wsdl:part name="parameters" element="tns:LiefereArzneimittelListeGZipResponse"/>
  </wsdl:message>
  <wsdl:message name="LiefereArzneimittelListeGZipSoapIn">
    <wsdl:part name="parameters" element="tns:LiefereArzneimittelListeGZip"/>
  </wsdl:message>
  <wsdl:message name="LiefereArzneimittelListeSoapOut">
    <wsdl:part name="parameters" element="tns:LiefereArzneimittelListeResponse"/>
  </wsdl:message>
  <wsdl:message name="LiefereArzneimittelListeSoapIn">
    <wsdl:part name="parameters" element="tns:LiefereArzneimittelListe"/>
  </wsdl:message>
  <wsdl:message name="LiefereSubstitutionenSoapOut">
    <wsdl:part name="parameters" element="tns:LiefereSubstitutionenResponse"/>
  </wsdl:message>
  <wsdl:message name="LiefereSubstitutionenSoapIn">
    <wsdl:part name="parameters" element="tns:LiefereSubstitutionen"/>
  </wsdl:message>
  <wsdl:message name="LiefereIndikatorWirkstoffListeSoapOut">
    <wsdl:part name="parameters" element="tns:LiefereIndikatorWirkstoffListeResponse"/>
  </wsdl:message>
  <wsdl:message name="LiefereIndikatorWirkstoffListeSoapIn">
    <wsdl:part name="parameters" element="tns:LiefereIndikatorWirkstoffListe"/>
  </wsdl:message>
  <wsdl:message name="LieferePznAtcFuerHochverordnungListeSoapIn">
    <wsdl:part name="parameters" element="tns:LieferePznAtcFuerHochverordnungListe"/>
  </wsdl:message>
  <wsdl:message name="LieferePznAtcFuerHochverordnungListeSoapOut">
    <wsdl:part name="parameters" element="tns:LieferePznAtcFuerHochverordnungListeResponse"/>
  </wsdl:message>
  <wsdl:message name="LiefereArzneimittelInformationenSoapOut">
    <wsdl:part name="parameters" element="tns:LiefereArzneimittelInformationenResponse"/>
  </wsdl:message>
  <wsdl:message name="LiefereArzneimittelInformationenSoapIn">
    <wsdl:part name="parameters" element="tns:LiefereArzneimittelInformationen"/>
  </wsdl:message>
  <wsdl:message name="LiefereHpmInformationSoapIn">
    <wsdl:part name="parameters" element="tns:LiefereHpmInformation"/>
  </wsdl:message>
  <wsdl:message name="LiefereHpmInformationSoapOut">
    <wsdl:part name="parameters" element="tns:LiefereHpmInformationResponse"/>
  </wsdl:message>
  <wsdl:message name="PruefeKonnektivitaetSoapIn">
    <wsdl:part name="parameters" element="tns:PruefeKonnektivitaet"/>
  </wsdl:message>
  <wsdl:message name="PruefeKonnektivitaetSoapOut">
    <wsdl:part name="parameters" element="tns:PruefeKonnektivitaetResponse"/>
  </wsdl:message>
  <wsdl:message name="PruefeKonnektivitaetItvSoapIn">
    <wsdl:part name="parameters" element="tns:PruefeKonnektivitaetItv"/>
  </wsdl:message>
  <wsdl:message name="PruefeKonnektivitaetItvSoapOut">
    <wsdl:part name="parameters" element="tns:PruefeKonnektivitaetItvResponse"/>
  </wsdl:message>
  <wsdl:message name="UpdateSoapIn">
    <wsdl:part name="parameters" element="tns:Update"/>
  </wsdl:message>
  <wsdl:message name="UpdateSoapOut">
    <wsdl:part name="parameters" element="tns:UpdateResponse"/>
  </wsdl:message>
  <wsdl:message name="SetzeUebertragungswegSoapIn">
    <wsdl:part name="parameters" element="tns:SetzeUebertragungsweg"/>
  </wsdl:message>
  <wsdl:message name="SetzeUebertragungswegSoapOut">
    <wsdl:part name="parameters" element="tns:SetzeUebertragungswegResponse"/>
  </wsdl:message>
  <wsdl:message name="LiefereBereitgestellteTeilnehmerverzeichnisseSoapIn">
    <wsdl:part name="parameters" element="tns:LiefereBereitgestellteTeilnehmerverzeichnisse"/>
  </wsdl:message>
  <wsdl:message name="LiefereBereitgestellteTeilnehmerverzeichnisseSoapOut">
    <wsdl:part name="parameters" element="tns:LiefereBereitgestellteTeilnehmerverzeichnisseResponse"/>
  </wsdl:message>
  <wsdl:message name="LiefereTeilnehmerverzeichnisSoapIn">
    <wsdl:part name="parameters" element="tns:LiefereTeilnehmerverzeichnis"/>
  </wsdl:message>
  <wsdl:message name="LiefereTeilnehmerverzeichnisSoapOut">
    <wsdl:part name="parameters" element="tns:LiefereTeilnehmerverzeichnisResponse"/>
  </wsdl:message>
 <wsdl:message name="ValidiereAuSoapIn">
    <wsdl:part name="parameters" element="tns:ValidiereAu"/>
  </wsdl:message>
  <wsdl:message name="ValidiereAuSoapOut">
    <wsdl:part name="parameters" element="tns:ValidiereAuResponse"/>
  </wsdl:message>
  <wsdl:message name="ValidiereEinweisungsbriefSoapIn">
    <wsdl:part name="parameters" element="tns:ValidiereEinweisungsbrief"/>
  </wsdl:message>
  <wsdl:message name="ValidiereEinweisungsbriefSoapOut">
    <wsdl:part name="parameters" element="tns:ValidiereEinweisungsbriefResponse"/>
  </wsdl:message>
  <wsdl:message name="PruefeVersichertenteilnahmeVernetzungSoapIn">
    <wsdl:part name="parameters" element="tns:PruefeVersichertenteilnahmeVernetzung"/>
  </wsdl:message>
  <wsdl:message name="PruefeVersichertenteilnahmeVernetzungSoapOut">
    <wsdl:part name="parameters" element="tns:PruefeVersichertenteilnahmeVernetzungResponse"/>
  </wsdl:message>
  <wsdl:message name="SigniereCdaDokumentSoapIn">
    <wsdl:part name="parameters" element="tns:SigniereCdaDokument"/>
  </wsdl:message>
  <wsdl:message name="SigniereCdaDokumentSoapOut">
    <wsdl:part name="parameters" element="tns:SigniereCdaDokumentResponse"/>
  </wsdl:message>
  <wsdl:message name="PruefeSignaturCdaDokumentSoapIn">
    <wsdl:part name="parameters" element="tns:PruefeSignaturCdaDokument"/>
  </wsdl:message>
  <wsdl:message name="PruefeSignaturCdaDokumentSoapOut">
    <wsdl:part name="parameters" element="tns:PruefeSignaturCdaDokumentResponse"/>
  </wsdl:message>
  <wsdl:message name="ErzeugeZertifikatArztSoapIn">
    <wsdl:part name="parameters" element="tns:ErzeugeZertifikatArzt"/>
  </wsdl:message>
  <wsdl:message name="ErzeugeZertifikatArztSoapOut">
    <wsdl:part name="parameters" element="tns:ErzeugeZertifikatArztResponse"/>
  </wsdl:message>
  <wsdl:message name="ErzeugeZertifikatBetriebsstaetteSoapIn">
    <wsdl:part name="parameters" element="tns:ErzeugeZertifikatBetriebsstaette"/>
  </wsdl:message>
  <wsdl:message name="ErzeugeZertifikatBetriebsstaetteSoapOut">
    <wsdl:part name="parameters" element="tns:ErzeugeZertifikatBetriebsstaetteResponse"/>
  </wsdl:message>
  <wsdl:message name="LiefereEmpfaengergruppenVernetzungSoapIn">
    <wsdl:part name="parameters" element="tns:LiefereEmpfaengergruppenVernetzung"/>
  </wsdl:message>
  <wsdl:message name="LiefereEmpfaengergruppenVernetzungSoapOut">
    <wsdl:part name="parameters" element="tns:LiefereEmpfaengergruppenVernetzungResponse"/>
  </wsdl:message>
  <wsdl:message name="SucheEmpfaengerVernetzungSoapIn">
    <wsdl:part name="parameters" element="tns:SucheEmpfaengerVernetzung"/>
  </wsdl:message>
  <wsdl:message name="SucheEmpfaengerVernetzungSoapOut">
    <wsdl:part name="parameters" element="tns:SucheEmpfaengerVernetzungResponse"/>
  </wsdl:message>
  <wsdl:message name="SendeArztbriefSoapIn">
    <wsdl:part name="parameters" element="tns:SendeArztbrief"/>
  </wsdl:message>
  <wsdl:message name="SendeArztbriefSoapOut">
    <wsdl:part name="parameters" element="tns:SendeArztbriefResponse"/>
  </wsdl:message>
  <wsdl:message name="PruefeAufBereitgestellteArztbriefeSoapIn">
    <wsdl:part name="parameters" element="tns:PruefeAufBereitgestellteArztbriefe"/>
  </wsdl:message>
  <wsdl:message name="PruefeAufBereitgestellteArztbriefeSoapOut">
    <wsdl:part name="parameters" element="tns:PruefeAufBereitgestellteArztbriefeResponse"/>
  </wsdl:message>
  <wsdl:message name="LiefereArztbriefeSoapIn">
    <wsdl:part name="parameters" element="tns:LiefereArztbriefe"/>
  </wsdl:message>
  <wsdl:message name="LiefereArztbriefeSoapOut">
    <wsdl:part name="parameters" element="tns:LiefereArztbriefeResponse"/>
  </wsdl:message>

  <wsdl:message name="LiefereMedikationsinformationSoapIn">
    <wsdl:part name="parameters" element="tns:LiefereMedikationsinformation"/>
  </wsdl:message>
  <wsdl:message name="LiefereMedikationsinformationSoapOut">
    <wsdl:part name="parameters" element="tns:LiefereMedikationsinformationResponse"/>
  </wsdl:message>
  <wsdl:message name="SendeMedikationsinformationSoapIn">
    <wsdl:part name="parameters" element="tns:SendeMedikationsinformation"/>
  </wsdl:message>
  <wsdl:message name="SendeMedikationsinformationSoapOut">
    <wsdl:part name="parameters" element="tns:SendeMedikationsinformationResponse"/>
  </wsdl:message>
  <wsdl:message name="SendeMedikationsinformationPflegearztSoapIn">
    <wsdl:part name="parameters" element="tns:SendeMedikationsinformationPflegearzt"/>
  </wsdl:message>
  <wsdl:message name="SendeMedikationsinformationPflegearztSoapOut">
    <wsdl:part name="parameters" element="tns:SendeMedikationsinformationPflegearztResponse"/>
  </wsdl:message>
  <wsdl:message name="LiefereMedikationsinformationErgaenzungenSoapIn">
    <wsdl:part name="parameters" element="tns:LiefereMedikationsinformationErgaenzungen"/>
  </wsdl:message>
  <wsdl:message name="LiefereMedikationsinformationErgaenzungenSoapOut">
    <wsdl:part name="parameters" element="tns:LiefereMedikationsinformationErgaenzungenResponse"/>
  </wsdl:message>

  <wsdl:message name="SendeTelekonsilSoapIn">
    <wsdl:part name="parameters" element="tns:SendeTelekonsil"/>
  </wsdl:message>
  <wsdl:message name="SendeTelekonsilSoapOut">
    <wsdl:part name="parameters" element="tns:SendeTelekonsilResponse"/>
  </wsdl:message>
  <wsdl:message name="LiefereTelekonsilMetainformationenSoapIn">
    <wsdl:part name="parameters" element="tns:LiefereTelekonsilMetainformationen"/>
  </wsdl:message>
  <wsdl:message name="LiefereTelekonsilMetainformationenSoapOut">
    <wsdl:part name="parameters" element="tns:LiefereTelekonsilMetainformationenResponse"/>
  </wsdl:message>
  <wsdl:message name="LiefereTelekonsileSoapIn">
    <wsdl:part name="parameters" element="tns:LiefereTelekonsile"/>
  </wsdl:message>
  <wsdl:message name="LiefereTelekonsileSoapOut">
    <wsdl:part name="parameters" element="tns:LiefereTelekonsileResponse"/>
  </wsdl:message>
  <wsdl:message name="SendeDokumentenAbrufbestaetigungSoapIn">
    <wsdl:part name="parameters" element="tns:SendeDokumentenAbrufbestaetigung"/>
  </wsdl:message>
  <wsdl:message name="SendeDokumentenAbrufbestaetigungSoapOut">
    <wsdl:part name="parameters" element="tns:SendeDokumentenAbrufbestaetigungResponse"/>
  </wsdl:message>

  <wsdl:message name="ErzeugePdfAusCdaDokumentSoapIn">
    <wsdl:part name="parameters" element="tns:ErzeugePdfAusCdaDokument"/>
  </wsdl:message>
  <wsdl:message name="ErzeugePdfAusCdaDokumentSoapOut">
    <wsdl:part name="parameters" element="tns:ErzeugePdfAusCdaDokumentResponse"/>
  </wsdl:message>
  <wsdl:message name="LiefereZertifikatinformationVernetzungSoapIn">
    <wsdl:part name="parameters" element="tns:LiefereZertifikatinformationVernetzung"/>
  </wsdl:message>
  <wsdl:message name="LiefereZertifikatinformationVernetzungSoapOut">
    <wsdl:part name="parameters" element="tns:LiefereZertifikatinformationVernetzungResponse"/>
  </wsdl:message>

  <wsdl:message name="LiefereDokumentenEmpfangsstatusSoapIn">
    <wsdl:part name="parameters" element="tns:LiefereDokumentenEmpfangsstatus"/>
  </wsdl:message>
  <wsdl:message name="LiefereDokumentenEmpfangsstatusSoapOut">
    <wsdl:part name="parameters" element="tns:LiefereDokumentenEmpfangsstatusResponse"/>
  </wsdl:message>
 <wsdl:message name="LiefereBearbeitungsstatusTelekonsileSoapIn">
    <wsdl:part name="parameters" element="tns:LiefereBearbeitungsstatusTelekonsile"/>
  </wsdl:message>
  <wsdl:message name="LiefereBearbeitungsstatusTelekonsileSoapOut">
    <wsdl:part name="parameters" element="tns:LiefereBearbeitungsstatusTelekonsileResponse"/>
  </wsdl:message>
  <wsdl:message name="ExtrahiereZertifikatsinformationVernetzungSoapIn">
    <wsdl:part name="parameters" element="tns:ExtrahiereZertifikatsinformationVernetzung"/>
  </wsdl:message>
  <wsdl:message name="ExtrahiereZertifikatsinformationVernetzungSoapOut">
    <wsdl:part name="parameters" element="tns:ExtrahiereZertifikatsinformationVernetzungResponse"/>
  </wsdl:message>
 

  <wsdl:portType name="ServiceBinding">
    <wsdl:operation name="StarteAbrechnung">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zur Übermittlung von Abrechnungsdaten</wsdl:documentation>
      <wsdl:input message="tns:StarteAbrechnungSoapIn"/>
      <wsdl:output message="tns:StarteAbrechnungSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="SendeVorEinschreibeLeistung">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zur Übermittlung einer Vor-Einschreibe-Leistung</wsdl:documentation>
      <wsdl:input message="tns:SendeVorEinschreibeLeistungSoapIn"/>
      <wsdl:output message="tns:SendeVorEinschreibeLeistungSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="LiefereKrankheitsbildAuswertung">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zur Auswertung von Krankheitbildern</wsdl:documentation>
      <wsdl:input message="tns:LiefereKrankheitsbildAuswertungSoapIn"/>
      <wsdl:output message="tns:LiefereKrankheitsbildAuswertungSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="StarteVerordnungsdatenUebermittlung">
      <wsdl:input message="tns:StarteVerordnungsdatenUebermittlungSoapIn"/>
      <wsdl:output message="tns:StarteVerordnungsdatenUebermittlungSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="PruefeTeilnahmenListe">
      <wsdl:input message="tns:PruefeTeilnahmenListeSoapIn"/>
      <wsdl:output message="tns:PruefeTeilnahmenListeSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="SendeTeilnahmeerklaerung">
      <wsdl:input message="tns:SendeTeilnahmeerklaerungSoapIn"/>
      <wsdl:output message="tns:SendeTeilnahmeerklaerungSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="SendeEDMPDaten">
      <wsdl:input message="tns:SendeEDMPDatenSoapIn"/>
      <wsdl:output message="tns:SendeEDMPDatenSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="SendePracManDaten">
      <wsdl:input message="tns:SendePracManDatenSoapIn"/>
      <wsdl:output message="tns:SendePracManDatenSoapOut"/>
    </wsdl:operation>
	<wsdl:operation name="MigriereVertragspartner">
      <wsdl:input message="tns:MigriereVertragspartnerSoapIn"/>
      <wsdl:output message="tns:MigriereVertragspartnerSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="LiefereArzneimittelInformationen">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zur Ermittlung von Arzneimittel-Informationen</wsdl:documentation>
      <wsdl:input message="tns:LiefereArzneimittelInformationenSoapIn"/>
      <wsdl:output message="tns:LiefereArzneimittelInformationenSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="LiefereSubstitutionen">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zur Ermittlung von Substitutionen</wsdl:documentation>
      <wsdl:input message="tns:LiefereSubstitutionenSoapIn"/>
      <wsdl:output message="tns:LiefereSubstitutionenSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="LiefereArzneimittelListe">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zur Ermittlung einer Arzneimittel-Liste</wsdl:documentation>
      <wsdl:input message="tns:LiefereArzneimittelListeSoapIn"/>
      <wsdl:output message="tns:LiefereArzneimittelListeSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="LiefereArzneimittelListeGZip">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zur Ermittlung einer Arzneimittel-Liste als GZip</wsdl:documentation>
      <wsdl:input message="tns:LiefereArzneimittelListeGZipSoapIn"/>
      <wsdl:output message="tns:LiefereArzneimittelListeGZipSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="LiefereIndikatorWirkstoffListe">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zur Ermittlung der vertragsspezifischen Indikator-Wirkstoff-Liste</wsdl:documentation>
      <wsdl:input message="tns:LiefereIndikatorWirkstoffListeSoapIn"/>
      <wsdl:output message="tns:LiefereIndikatorWirkstoffListeSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="LieferePznAtcFuerHochverordnungListe">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zur Ermittlung der vertragsspezifischen PZN-ATC-Liste fuer Hochverordnung</wsdl:documentation>
      <wsdl:input message="tns:LieferePznAtcFuerHochverordnungListeSoapIn"/>
      <wsdl:output message="tns:LieferePznAtcFuerHochverordnungListeSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="LiefereHpmInformation">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zur Ermittlung von Informationen zur eingesetzenden HPM-Instanz</wsdl:documentation>
      <wsdl:input message="tns:LiefereHpmInformationSoapIn"/>
      <wsdl:output message="tns:LiefereHpmInformationSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="PruefeKonnektivitaet">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Prüfen der Konnektivität zum Backend-Rechenzentrum</wsdl:documentation>
      <wsdl:input message="tns:PruefeKonnektivitaetSoapIn"/>
      <wsdl:output message="tns:PruefeKonnektivitaetSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="PruefeKonnektivitaetItv">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Prüfen der Konnektivität für die Elektronische Arzt-Vernetzung</wsdl:documentation>
      <wsdl:input message="tns:PruefeKonnektivitaetItvSoapIn"/>
      <wsdl:output message="tns:PruefeKonnektivitaetItvSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="Update">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Ausführen der Überprüfung auf Aktualisierungen</wsdl:documentation>
      <wsdl:input message="tns:UpdateSoapIn"/>
      <wsdl:output message="tns:UpdateSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="SetzeUebertragungsweg">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Setzen des aktuell eingestellten Übertragungswegs</wsdl:documentation>
      <wsdl:input message="tns:SetzeUebertragungswegSoapIn"/>
      <wsdl:output message="tns:SetzeUebertragungswegSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="LiefereBereitgestellteTeilnehmerverzeichnisse">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Abruf der Liste der für den Empfänger bereitgestellten Dokumente</wsdl:documentation>
      <wsdl:input message="tns:LiefereBereitgestellteTeilnehmerverzeichnisseSoapIn"/>
      <wsdl:output message="tns:LiefereBereitgestellteTeilnehmerverzeichnisseSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="LiefereTeilnehmerverzeichnis">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Abruf eines für den Empfänger bereitgestellten Dokuments</wsdl:documentation>
      <wsdl:input message="tns:LiefereTeilnehmerverzeichnisSoapIn"/>
      <wsdl:output message="tns:LiefereTeilnehmerverzeichnisSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="ValidiereAu">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Validieren einer AU im FHIR-Format</wsdl:documentation>
      <wsdl:input message="tns:ValidiereAuSoapIn"/>
      <wsdl:output message="tns:ValidiereAuSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="ValidiereEinweisungsbrief">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Validieren eines CDA-Einweisungsbriefs</wsdl:documentation>
      <wsdl:input message="tns:ValidiereEinweisungsbriefSoapIn"/>
      <wsdl:output message="tns:ValidiereEinweisungsbriefSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="PruefeVersichertenteilnahmeVernetzung">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Prüfen der Teilnahme eines Versicherten an der IT-Vernetzung</wsdl:documentation>
      <wsdl:input message="tns:PruefeVersichertenteilnahmeVernetzungSoapIn"/>
      <wsdl:output message="tns:PruefeVersichertenteilnahmeVernetzungSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="SigniereCdaDokument">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Signieren eines CDA-Dokuments</wsdl:documentation>
      <wsdl:input message="tns:SigniereCdaDokumentSoapIn"/>
      <wsdl:output message="tns:SigniereCdaDokumentSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="PruefeSignaturCdaDokument">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zur Überprüfung der Signatur eines CDA-Dokuments</wsdl:documentation>
      <wsdl:input message="tns:PruefeSignaturCdaDokumentSoapIn"/>
      <wsdl:output message="tns:PruefeSignaturCdaDokumentSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="ErzeugeZertifikatArzt">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Erzeugen eines Zertifikats für einen Arzt</wsdl:documentation>
      <wsdl:input message="tns:ErzeugeZertifikatArztSoapIn"/>
      <wsdl:output message="tns:ErzeugeZertifikatArztSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="ErzeugeZertifikatBetriebsstaette">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Erzeugen eines Zertifikats für eine Betriebsstätte</wsdl:documentation>
      <wsdl:input message="tns:ErzeugeZertifikatBetriebsstaetteSoapIn"/>
      <wsdl:output message="tns:ErzeugeZertifikatBetriebsstaetteSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="LiefereEmpfaengergruppenVernetzung">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Abfragen der aktuellen Empfängergruppen für die Vernetzung</wsdl:documentation>
      <wsdl:input message="tns:LiefereEmpfaengergruppenVernetzungSoapIn"/>
      <wsdl:output message="tns:LiefereEmpfaengergruppenVernetzungSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="SucheEmpfaengerVernetzung">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Suchen eines Empfängers für die Vernetzung</wsdl:documentation>
      <wsdl:input message="tns:SucheEmpfaengerVernetzungSoapIn"/>
      <wsdl:output message="tns:SucheEmpfaengerVernetzungSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="SendeArztbrief">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Versenden eines Arztbriefs</wsdl:documentation>
      <wsdl:input message="tns:SendeArztbriefSoapIn"/>
      <wsdl:output message="tns:SendeArztbriefSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="PruefeAufBereitgestellteArztbriefe">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Prüfen auf für einen Patienten bereitgestellte Arztbriefe</wsdl:documentation>
      <wsdl:input message="tns:PruefeAufBereitgestellteArztbriefeSoapIn"/>
      <wsdl:output message="tns:PruefeAufBereitgestellteArztbriefeSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="LiefereArztbriefe">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Abrufen von Arztbriefen anhand von DokumentIdentifikatoren</wsdl:documentation>
      <wsdl:input message="tns:LiefereArztbriefeSoapIn"/>
      <wsdl:output message="tns:LiefereArztbriefeSoapOut"/>
    </wsdl:operation>

    <wsdl:operation name="LiefereMedikationsinformation">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Abrufen der Medikationsinformation eines Patienten</wsdl:documentation>
      <wsdl:input message="tns:LiefereMedikationsinformationSoapIn"/>
      <wsdl:output message="tns:LiefereMedikationsinformationSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="SendeMedikationsinformation">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Übertragen der Medikationsinformation eines Patienten</wsdl:documentation>
      <wsdl:input message="tns:SendeMedikationsinformationSoapIn"/>
      <wsdl:output message="tns:SendeMedikationsinformationSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="SendeMedikationsinformationPflegearzt">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Übernehmen der Rolle des Kurators der Medikationsinformation eines Patienten</wsdl:documentation>
      <wsdl:input message="tns:SendeMedikationsinformationPflegearztSoapIn"/>
      <wsdl:output message="tns:SendeMedikationsinformationPflegearztSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="LiefereMedikationsinformationErgaenzungen">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Abrufen der Liste von Patienten, zu denen zu kuratierende Medikationsinformationen vorliegen</wsdl:documentation>
      <wsdl:input message="tns:LiefereMedikationsinformationErgaenzungenSoapIn"/>
      <wsdl:output message="tns:LiefereMedikationsinformationErgaenzungenSoapOut"/>
    </wsdl:operation>

    <wsdl:operation name="SendeTelekonsil">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Versenden eines Telekonsils</wsdl:documentation>
      <wsdl:input message="tns:SendeTelekonsilSoapIn"/>
      <wsdl:output message="tns:SendeTelekonsilSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="LiefereTelekonsilMetainformationen">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Abruf von TelekonsilMetainformationen</wsdl:documentation>
      <wsdl:input message="tns:LiefereTelekonsilMetainformationenSoapIn"/>
      <wsdl:output message="tns:LiefereTelekonsilMetainformationenSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="LiefereTelekonsile">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Abrufen von Telekonsilen</wsdl:documentation>
      <wsdl:input message="tns:LiefereTelekonsileSoapIn"/>
      <wsdl:output message="tns:LiefereTelekonsileSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="SendeDokumentenAbrufbestaetigung">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Bestätigen des erfolgreichen Abrufs eines oder mehrerer Telekonsile</wsdl:documentation>
      <wsdl:input message="tns:SendeDokumentenAbrufbestaetigungSoapIn"/>
      <wsdl:output message="tns:SendeDokumentenAbrufbestaetigungSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="LiefereBearbeitungsstatusTelekonsile">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Abruf der Bearbeitungsstatus von Telekonsil-Vorgängen</wsdl:documentation>
      <wsdl:input message="tns:LiefereBearbeitungsstatusTelekonsileSoapIn"/>
      <wsdl:output message="tns:LiefereBearbeitungsstatusTelekonsileSoapOut"/>
    </wsdl:operation>
    
    <wsdl:operation name="ErzeugePdfAusCdaDokument">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Erzeugen eines PDFs</wsdl:documentation>
      <wsdl:input message="tns:ErzeugePdfAusCdaDokumentSoapIn"/>
      <wsdl:output message="tns:ErzeugePdfAusCdaDokumentSoapOut"/>
    </wsdl:operation>
    <wsdl:operation name="LiefereZertifikatinformationVernetzung">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Abrufen von Zertifikatsinformationen der ITV</wsdl:documentation>
      <wsdl:input message="tns:LiefereZertifikatinformationVernetzungSoapIn"/>
      <wsdl:output message="tns:LiefereZertifikatinformationVernetzungSoapOut"/>
    </wsdl:operation>
    
    <wsdl:operation name="LiefereDokumentenEmpfangsstatus">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Abruf von Empfangsstatus von Dokumenten</wsdl:documentation>
      <wsdl:input message="tns:LiefereDokumentenEmpfangsstatusSoapIn"/>
      <wsdl:output message="tns:LiefereDokumentenEmpfangsstatusSoapOut"/>
    </wsdl:operation>
   <wsdl:operation name="ExtrahiereZertifikatsinformationVernetzung">
      <wsdl:documentation xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">HÄVG-Prüfmodul - Methode zum Abruf von Informationen über das vorliegene Zertifikat</wsdl:documentation>
      <wsdl:input message="tns:ExtrahiereZertifikatsinformationVernetzungSoapIn"/>
      <wsdl:output message="tns:ExtrahiereZertifikatsinformationVernetzungSoapOut"/>
    </wsdl:operation>

  </wsdl:portType>
  <wsdl:binding name="ServiceBinding" type="tns:ServiceBinding">
    <wsdl:documentation>
      <wsi:Claim conformsTo="http://ws-i.org/profiles/basic/1.1" xmlns:wsi="http://ws-i.org/schemas/conformanceClaim/"/>
    </wsdl:documentation>
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="StarteAbrechnung">
      <soap:operation soapAction="http://haevg-rz.de/hpm/StarteAbrechnung" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendeVorEinschreibeLeistung">
      <soap:operation soapAction="http://haevg-rz.de/hpm/SendeVorEinschreibeLeistung" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereKrankheitsbildAuswertung">
      <soap:operation soapAction="http://haevg-rz.de/hpm/LiefereKrankheitsbildAuswertung" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StarteVerordnungsdatenUebermittlung">
      <soap:operation soapAction="http://haevg-rz.de/hpm/StarteVerordnungsdatenUebermittlung" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PruefeTeilnahmenListe">
      <soap:operation soapAction="http://haevg-rz.de/hpm/PruefeTeilnahmenListe" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendeTeilnahmeerklaerung">
      <soap:operation soapAction="http://haevg-rz.de/hpm/SendeTeilnahmeerklaerung" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendeEDMPDaten">
      <soap:operation soapAction="http://haevg-rz.de/hpm/SendeEDMPDaten" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendePracManDaten">
      <soap:operation soapAction="http://haevg-rz.de/hpm/SendePracManDaten" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
	<wsdl:operation name="MigriereVertragspartner">
      <soap:operation soapAction="http://haevg-rz.de/hpm/MigriereVertragspartner" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereArzneimittelInformationen">
      <soap:operation soapAction="http://haevg-rz.de/hpm/LiefereArzneimittelInformationen" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereSubstitutionen">
      <soap:operation soapAction="http://haevg-rz.de/hpm/LiefereSubstitutionen" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereArzneimittelListe">
      <soap:operation soapAction="http://haevg-rz.de/hpm/LiefereArzneimittelListe" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereArzneimittelListeGZip">
      <soap:operation soapAction="http://haevg-rz.de/hpm/LiefereArzneimittelListeGZip" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereIndikatorWirkstoffListe">
      <soap:operation soapAction="http://haevg-rz.de/hpm/LiefereIndikatorWirkstoffListe" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LieferePznAtcFuerHochverordnungListe">
      <soap:operation soapAction="http://haevg-rz.de/hpm/LieferePznAtcFuerHochverordnungListe" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereHpmInformation">
      <soap:operation soapAction="http://haevg-rz.de/hpm/LiefereHpmInformation" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PruefeKonnektivitaet">
      <soap:operation soapAction="http://haevg-rz.de/hpm/PruefeKonnektivitaet" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PruefeKonnektivitaetItv">
      <soap:operation soapAction="http://haevg-rz.de/hpm/PruefeKonnektivitaetItv" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Update">
      <soap:operation soapAction="http://haevg-rz.de/hpm/Update" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetzeUebertragungsweg">
      <soap:operation soapAction="http://haevg-rz.de/hpm/SetzeUebertragungsweg" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereBereitgestellteTeilnehmerverzeichnisse">
      <soap:operation soapAction="http://haevg-rz.de/hpm/LiefereBereitgestellteTeilnehmerverzeichnisse" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereTeilnehmerverzeichnis">
      <soap:operation soapAction="http://haevg-rz.de/hpm/LiefereTeilnehmerverzeichnis" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ValidiereAu">
      <soap:operation soapAction="http://haevg-rz.de/hpm/ValidiereAu" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ValidiereEinweisungsbrief">
      <soap:operation soapAction="http://haevg-rz.de/hpm/ValidiereEinweisungsbrief" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PruefeVersichertenteilnahmeVernetzung">
      <soap:operation soapAction="http://haevg-rz.de/hpm/PruefeVersichertenteilnahmeVernetzung" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SigniereCdaDokument">
      <soap:operation soapAction="http://haevg-rz.de/hpm/SigniereCdaDokument" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PruefeSignaturCdaDokument">
      <soap:operation soapAction="http://haevg-rz.de/hpm/PruefeSignaturCdaDokument" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ErzeugeZertifikatArzt">
      <soap:operation soapAction="http://haevg-rz.de/hpm/ErzeugeZertifikatArzt" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ErzeugeZertifikatBetriebsstaette">
      <soap:operation soapAction="http://haevg-rz.de/hpm/ErzeugeZertifikatBetriebsstaette" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereEmpfaengergruppenVernetzung">
      <soap:operation soapAction="http://haevg-rz.de/hpm/LiefereEmpfaengergruppenVernetzung" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SucheEmpfaengerVernetzung">
      <soap:operation soapAction="http://haevg-rz.de/hpm/SucheEmpfaengerVernetzung" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendeArztbrief">
      <soap:operation soapAction="http://haevg-rz.de/hpm/SendeArztbrief" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PruefeAufBereitgestellteArztbriefe">
      <soap:operation soapAction="http://haevg-rz.de/hpm/PruefeAufBereitgestellteArztbriefe" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereArztbriefe">
      <soap:operation soapAction="http://haevg-rz.de/hpm/LiefereArztbriefe" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>

    <wsdl:operation name="LiefereMedikationsinformation">
      <soap:operation soapAction="http://haevg-rz.de/hpm/LiefereMedikationsinformation" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendeMedikationsinformation">
      <soap:operation soapAction="http://haevg-rz.de/hpm/SendeMedikationsinformation" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendeMedikationsinformationPflegearzt">
      <soap:operation soapAction="http://haevg-rz.de/hpm/SendeMedikationsinformationPflegearzt" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereMedikationsinformationErgaenzungen">
      <soap:operation soapAction="http://haevg-rz.de/hpm/LiefereMedikationsinformationErgaenzungen" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>

    <wsdl:operation name="SendeTelekonsil">
      <soap:operation soapAction="http://haevg-rz.de/hpm/SendeTelekonsil" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereTelekonsilMetainformationen">
      <soap:operation soapAction="http://haevg-rz.de/hpm/LiefereTelekonsilMetainformationen" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereTelekonsile">
      <soap:operation soapAction="http://haevg-rz.de/hpm/LiefereTelekonsile" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendeDokumentenAbrufbestaetigung">
      <soap:operation soapAction="http://haevg-rz.de/hpm/SendeDokumentenAbrufbestaetigung" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereBearbeitungsstatusTelekonsile">
      <soap:operation soapAction="http://haevg-rz.de/hpm/LiefereBearbeitungsstatusTelekonsile" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>

    <wsdl:operation name="ErzeugePdfAusCdaDokument">
      <soap:operation soapAction="http://haevg-rz.de/hpm/ErzeugePdfAusCdaDokument" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereZertifikatinformationVernetzung">
      <soap:operation soapAction="http://haevg-rz.de/hpm/LiefereZertifikatinformationVernetzung" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>

    <wsdl:operation name="LiefereDokumentenEmpfangsstatus">
      <soap:operation soapAction="http://haevg-rz.de/hpm/LiefereDokumentenEmpfangsstatus" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
   <wsdl:operation name="ExtrahiereZertifikatsinformationVernetzung">
      <soap:operation soapAction="http://haevg-rz.de/hpm/ExtrahiereZertifikatsinformationVernetzung" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>

  </wsdl:binding>
  <wsdl:binding name="ServiceBinding1" type="tns:ServiceBinding">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="StarteAbrechnung">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/StarteAbrechnung" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendeVorEinschreibeLeistung">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/SendeVorEinschreibeLeistung" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereKrankheitsbildAuswertung">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/LiefereKrankheitsbildAuswertung" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="StarteVerordnungsdatenUebermittlung">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/StarteVerordnungsdatenUebermittlung" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PruefeTeilnahmenListe">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/PruefeTeilnahmenListe" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendeTeilnahmeerklaerung">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/SendeTeilnahmeerklaerung" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendeEDMPDaten">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/SendeEDMPDaten" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendePracManDaten">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/SendePracManDaten" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
	<wsdl:operation name="MigriereVertragspartner">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/MigriereVertragspartner" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereArzneimittelInformationen">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/LiefereArzneimittelInformationen" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereSubstitutionen">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/LiefereSubstitutionen" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereArzneimittelListe">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/LiefereArzneimittelListe" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereArzneimittelListeGZip">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/LiefereArzneimittelListeGZip" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereIndikatorWirkstoffListe">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/LiefereIndikatorWirkstoffListe" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LieferePznAtcFuerHochverordnungListe">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/LieferePznAtcFuerHochverordnungListe" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereHpmInformation">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/LiefereHpmInformation" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PruefeKonnektivitaet">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/PruefeKonnektivitaet" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PruefeKonnektivitaetItv">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/PruefeKonnektivitaetItv" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="Update">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/Update" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SetzeUebertragungsweg">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/SetzeUebertragungsweg" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereBereitgestellteTeilnehmerverzeichnisse">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/LiefereBereitgestellteTeilnehmerverzeichnisse" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereTeilnehmerverzeichnis">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/LiefereTeilnehmerverzeichnis" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ValidiereAu">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/ValidiereAu" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ValidiereEinweisungsbrief">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/ValidiereEinweisungsbrief" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PruefeVersichertenteilnahmeVernetzung">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/PruefeVersichertenteilnahmeVernetzung" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SigniereCdaDokument">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/SigniereCdaDokument" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PruefeSignaturCdaDokument">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/PruefeSignaturCdaDokument" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ErzeugeZertifikatArzt">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/ErzeugeZertifikatArzt" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ErzeugeZertifikatBetriebsstaette">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/ErzeugeZertifikatBetriebsstaette" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereEmpfaengergruppenVernetzung">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/LiefereEmpfaengergruppenVernetzung" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SucheEmpfaengerVernetzung">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/SucheEmpfaengerVernetzung" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendeArztbrief">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/SendeArztbrief" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="PruefeAufBereitgestellteArztbriefe">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/PruefeAufBereitgestellteArztbriefe" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereArztbriefe">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/LiefereArztbriefe" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>

    <wsdl:operation name="LiefereMedikationsinformation">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/LiefereMedikationsinformation" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendeMedikationsinformation">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/SendeMedikationsinformation" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendeMedikationsinformationPflegearzt">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/SendeMedikationsinformationPflegearzt" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereMedikationsinformationErgaenzungen">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/LiefereMedikationsinformationErgaenzungen" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>

    <wsdl:operation name="SendeTelekonsil">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/SendeTelekonsil" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereTelekonsilMetainformationen">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/LiefereTelekonsilMetainformationen" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereTelekonsile">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/LiefereTelekonsile" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="SendeDokumentenAbrufbestaetigung">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/SendeDokumentenAbrufbestaetigung" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LiefereBearbeitungsstatusTelekonsile">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/LiefereBearbeitungsstatusTelekonsile" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>

    <wsdl:operation name="ErzeugePdfAusCdaDokument">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/ErzeugePdfAusCdaDokument" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
   <wsdl:operation name="LiefereZertifikatinformationVernetzung">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/LiefereZertifikatinformationVernetzung" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>

    <wsdl:operation name="LiefereDokumentenEmpfangsstatus">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/LiefereDokumentenEmpfangsstatus" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ExtrahiereZertifikatsinformationVernetzung">
      <soap12:operation soapAction="http://haevg-rz.de/hpm/ExtrahiereZertifikatsinformationVernetzung" style="document"/>
      <wsdl:input>
        <soap12:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>

  </wsdl:binding>
  <wsdl:service name="Service">
    <wsdl:port name="ServiceBinding" binding="tns:ServiceBinding">
      <soap:address location="http://localhost:22220/Service/"/>
    </wsdl:port>
    <wsdl:port name="ServiceBinding1" binding="tns:ServiceBinding1">
      <soap12:address location="http://localhost:22220/Service/"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
