{"openapi": "3.0.4", "info": {"title": "HPM 2025 Q3", "version": "2025 Q3"}, "paths": {"/api/arzneimittel/liste": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Liefert die Arzneimittelliste für einen Vertrag und IK zu einem Referenzdatum.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ArzneimittelListeContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ArzneimittelListeContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ArzneimittelListeContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LiefereArzneimittelListeResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LiefereArzneimittelListeResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereArzneimittelListeResultat"}}}}}}}, "/api/arzneimittel/liste/gzip": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Liefert die Arzneimittelliste für einen Vertrag und IK zu einem Referenzdatum als komprimiertes gzip Archiv.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ArzneimittelListeContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ArzneimittelListeContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ArzneimittelListeContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LiefereArzneimittelListeGZipResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LiefereArzneimittelListeGZipResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereArzneimittelListeGZipResultat"}}}}}}}, "/api/arzneimittel/informationen": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Liefert detaillierte Arzneimittelinformationen für einen Vertrag, IK und einer Liste von PZNs zu einem Referenzdatum.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ArzneimittelInformationenContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ArzneimittelInformationenContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ArzneimittelInformationenContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LiefereArzneimittelInformationenResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LiefereArzneimittelInformationenResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereArzneimittelInformationenResultat"}}}}}}}, "/api/arzneimittel/substitutionen": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Liefert Substitute für einen Vertrag, IK und eine PZN zu einem Referenzdatum.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubstitutionContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubstitutionContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SubstitutionContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SubstitutionResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SubstitutionResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SubstitutionResultat"}}}}}}}, "/api/hochverordnungen": {"post": {"tags": ["Hochverordnungen"], "summary": "Liefert die vertragsspezifische PZN-ATC-Liste für Hochverordnungen.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PznAtcFuerHochverordnungListeContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PznAtcFuerHochverordnungListeContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PznAtcFuerHochverordnungListeContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LieferePznAtcFuerHochverordnungListeResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LieferePznAtcFuerHochverordnungListeResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LieferePznAtcFuerHochverordnungListeResultat"}}}}}}}, "/api/indikatorwirkstoffe": {"post": {"tags": ["IndikatorWirkstoffe"], "summary": "Liefert die vertragsspezifische Indikator-Wirkstoff-Liste.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/IndikatorWirkstoffListeContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IndikatorWirkstoffListeContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/IndikatorWirkstoffListeContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LiefereIndikatorWirkstoffListeResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LiefereIndikatorWirkstoffListeResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereIndikatorWirkstoffListeResultat"}}}}}}}, "/api/verordnungen": {"post": {"tags": ["Verordnungen"], "summary": "Validierung und Versand der Verordnungsdaten für einen Vertrag.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerordnungsContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerordnungsContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VerordnungsContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/VerordnungsdatenUebermittlungResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VerordnungsdatenUebermittlungResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerordnungsdatenUebermittlungResultat"}}}}}}}}, "components": {"schemas": {"Arzneimittel": {"required": ["arzneimittelId", "autIdem"], "type": "object", "properties": {"arzneimittelId": {"minLength": 1, "type": "string"}, "autIdem": {"type": "boolean"}, "anzahl": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ArzneimittelFreitext": {"allOf": [{"$ref": "#/components/schemas/Arzneimittel"}, {"type": "object", "properties": {"name": {"type": "string", "nullable": true}}, "additionalProperties": false}], "description": "Der Name des Arzneimittels als Freitext."}, "ArzneimittelInformationenContainer": {"required": ["arztInformationsSystem", "ik", "pzNs", "referenzDatum", "vertragsIdentifikator"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "ik": {"minLength": 1, "type": "string"}, "referenzDatum": {"type": "string", "format": "date-time"}, "vertragsIdentifikator": {"minLength": 1, "type": "string"}, "pzNs": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false, "description": "Der Container mit den PZNs für die detailierte Informationen abgerufen werden sollen."}, "ArzneimittelListe": {"type": "object", "properties": {"eintraege": {"type": "array", "items": {"$ref": "#/components/schemas/ArzneimittelListeEintrag"}, "nullable": true}}, "additionalProperties": false, "description": "Die angeforderte ArzneimittelListe."}, "ArzneimittelListeContainer": {"required": ["arztInformationsSystem", "ik", "referenzDatum", "vertragsIdentifikator"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "ik": {"minLength": 1, "type": "string"}, "referenzDatum": {"type": "string", "format": "date-time"}, "vertragsIdentifikator": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "ArzneimittelListeEintrag": {"type": "object", "properties": {"pzn": {"type": "string", "nullable": true}, "arzneimittelKategorie": {"type": "string", "nullable": true}, "gueltigVon": {"type": "string", "format": "date-time"}, "gueltigBis": {"type": "string", "format": "date-time"}, "istInPriscusListe": {"type": "boolean"}}, "additionalProperties": false, "description": "Ein Eintrag der angeforderten ArzneimittelListe."}, "ArzneimittelPzn": {"allOf": [{"$ref": "#/components/schemas/Arzneimittel"}, {"type": "object", "properties": {"preis": {"type": "integer", "format": "int32"}, "pzn": {"type": "string", "nullable": true}}, "additionalProperties": false}], "description": "<PERSON> Rezept, bestehend aus Angaben über einen Arbeitsunfall, die Arzneimittel und weiterer Angaben."}, "BeschreibungsText": {"type": "object", "properties": {"index": {"type": "integer", "format": "int32"}, "inhalt": {"type": "string", "nullable": true}, "quelle": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Die textuellen Beschreibungen zu möglichen Substituten."}, "IndikatorWirkstoffListeContainer": {"required": ["arztInformationsSystem", "vertragsIdentifikator"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "vertragsIdentifikator": {"minLength": 1, "type": "string"}}, "additionalProperties": false, "description": "Der Vertragsidentifikator, zu dem eine Indikatorwirkstoffliste generiert werden soll."}, "IndikatorWirkstoffListeEintrag": {"type": "object", "properties": {"atcCode": {"type": "string", "nullable": true}, "diagnoseCode": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Ein Eintrag der Indikatorwirkstoffliste, bestehend aus ATCcode und zugehörigem Diagnosecode."}, "LiefereArzneimittelInformationenResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "packungen": {"type": "array", "items": {"$ref": "#/components/schemas/Packung"}, "nullable": true}}, "additionalProperties": false, "description": "Das Ergebnis eines Arzneimittelinformationenabrufs."}, "LiefereArzneimittelListeGZipResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "gZip": {"type": "string", "format": "byte", "nullable": true}}, "additionalProperties": false, "description": "Die erstellte, GZip-komprimierte, ArzneimittelListe."}, "LiefereArzneimittelListeResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "arzneimittelListe": {"$ref": "#/components/schemas/ArzneimittelListe"}}, "additionalProperties": false, "description": "Die erstellte ArzneimittelListe."}, "LiefereIndikatorWirkstoffListeResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "indikatorWirkstoffListe": {"type": "array", "items": {"$ref": "#/components/schemas/IndikatorWirkstoffListeEintrag"}, "nullable": true}}, "additionalProperties": false, "description": "Das Ergebnis einer Abfrage nach Indikatorwirkstoffen."}, "LieferePznAtcFuerHochverordnungListeResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "pznAtcFuerHochverordnungListe": {"type": "array", "items": {"$ref": "#/components/schemas/PznAtcFuerHochverordnungListeEintrag"}, "nullable": true}}, "additionalProperties": false, "description": "Das Ergebnis einer Anfrage nach Hochverordnungen."}, "Meldung": {"type": "object", "properties": {"nachricht": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "art": {"$ref": "#/components/schemas/Meldu<PERSON>t"}, "kategorie": {"$ref": "#/components/schemas/Meldungskategorie"}, "referenzen": {"type": "array", "items": {"$ref": "#/components/schemas/Referenz"}, "nullable": true}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}}, "additionalProperties": false, "description": "<PERSON> Meldung, die an den Aufrufer gesendet wird."}, "MeldungsText": {"type": "object", "properties": {"logik": {"type": "integer", "format": "int32"}, "typ": {"type": "integer", "format": "int32"}, "inhalt": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Eine Meldungstext samt Metainformationen."}, "Meldungsart": {"enum": ["Unbekannt", "Information", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "type": "string"}, "Meldungskategorie": {"enum": ["KeineAngabe", "Validierung", "Laufzeit", "Rechenzentrum"], "type": "string"}, "Packung": {"type": "object", "properties": {"herstellerName": {"type": "string", "nullable": true}, "istBTM": {"type": "boolean"}, "istLifestyle": {"type": "boolean"}, "istNegativliste": {"type": "boolean"}, "istOTC": {"type": "boolean"}, "name": {"type": "string", "nullable": true}, "preis": {"type": "string", "nullable": true}, "pzn": {"type": "string", "nullable": true}, "arzneimittelKategorie": {"type": "string", "nullable": true}, "wirkstaerke": {"type": "string", "nullable": true}, "sortierung": {"type": "integer", "format": "int32"}, "ausserVertrieb": {"type": "boolean"}, "packungsGroesse": {"type": "string", "nullable": true}, "zuzahlung": {"type": "string", "nullable": true}, "festbetrag": {"type": "string", "nullable": true}, "istInPriscusListe": {"type": "boolean"}, "meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/MeldungsText"}, "nullable": true}}, "additionalProperties": false, "description": "Die Rabattinformationen zu einer PZN."}, "Patient": {"required": ["geburtsdatum", "geschlecht", "nachname", "patientenId", "versicherungsnachweis", "vorname"], "type": "object", "properties": {"aktuelleVertragsteilnahmen": {"type": "array", "items": {"$ref": "#/components/schemas/Vertragsteilnahme"}, "nullable": true}, "patientenId": {"minLength": 1, "type": "string"}, "nachname": {"minLength": 1, "type": "string"}, "vorname": {"minLength": 1, "type": "string"}, "geburtsdatum": {"minLength": 1, "type": "string"}, "geschlecht": {"$ref": "#/components/schemas/PatientGeschlecht"}, "versicherungsnachweis": {"$ref": "#/components/schemas/Versicherungsnachweis"}}, "additionalProperties": false, "description": "Alle Informationen zum Patienten."}, "PatientGeschlecht": {"enum": ["U", "M", "W", "X", "D"], "type": "string"}, "PznAtcFuerHochverordnungListeContainer": {"required": ["arztInformationsSystem", "vertragsIdentifikator"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "vertragsIdentifikator": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "PznAtcFuerHochverordnungListeEintrag": {"type": "object", "properties": {"pzn": {"type": "string", "nullable": true}, "atcCode": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Ein Element der Hochverordnungsliste."}, "Referenz": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "referenzTyp": {"$ref": "#/components/schemas/ReferenzTyp"}}, "additionalProperties": false, "description": "Die Referenzen zu den vom Aufrufer übermittelten Objekten."}, "ReferenzTyp": {"enum": ["KeineAngabe", "Le<PERSON><PERSON>", "Patient", "Diagnose", "Ueberweisung", "Praxisgebuehr", "Vertragsidentifikator", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Rezept", "Operation", "Zielwert", "Istwert", "Erkrankungsstufe", "Hinderungsfaktor", "IstFremdeingeschrieben", "IstNichtFremdeingeschrieben"], "type": "string"}, "ResultatStatus": {"enum": ["Unbekannt", "OK", "Fehlgeschlagen", "TeilweiseVerarbeitet"], "type": "string"}, "Rezept": {"required": ["rezeptId", "verordnungsZeitpunkte"], "type": "object", "properties": {"arbeitsunfallDatum": {"type": "string", "format": "date-time", "nullable": true}, "arbeitsunfallOrt": {"type": "string", "nullable": true}, "arzneimittel": {"type": "array", "items": {"$ref": "#/components/schemas/Arzneimittel"}, "nullable": true}, "bvg": {"type": "boolean"}, "gebuehrenpflichtig": {"type": "boolean"}, "kvDetails": {"$ref": "#/components/schemas/RezeptKvDetails"}, "notfall": {"type": "boolean"}, "rezeptId": {"minLength": 1, "type": "string"}, "sonstigerKt": {"type": "boolean"}, "sprechstundenbedarf": {"type": "boolean"}, "stellvertreter": {"$ref": "#/components/schemas/Stellvertreter"}, "unfall": {"type": "boolean"}, "verordnungsZeitpunkte": {"$ref": "#/components/schemas/VerordnungsZeitpunkte"}}, "additionalProperties": false, "description": "<PERSON> Rezept, bestehend aus Angaben über einen Arbeitsunfall, die Arzneimittel und weiterer Angaben."}, "RezeptKvDetails": {"type": "object", "properties": {"begruendungspflicht": {"type": "boolean"}, "hilfsmittel": {"type": "boolean"}, "impfstoff": {"type": "boolean"}}, "additionalProperties": false}, "SoftwareInformation": {"required": ["systemOid", "version"], "type": "object", "properties": {"systemOid": {"minLength": 1, "type": "string"}, "version": {"minLength": 1, "type": "string"}, "organisation": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Informationen über das Arztinformationssystem und dessen Hersteller."}, "Stellvertreter": {"required": ["lanr"], "type": "object", "properties": {"lanr": {"minLength": 1, "type": "string"}}, "additionalProperties": false, "description": "Der Stellvertreter, der in Vertretung für den abrechnenden Vertragsarzt gehandelt hat."}, "Substitution": {"type": "object", "properties": {"atcCode": {"type": "string", "nullable": true}, "atcName": {"type": "string", "nullable": true}, "packungen": {"type": "array", "items": {"$ref": "#/components/schemas/Packung"}, "nullable": true}, "prioritaet": {"type": "integer", "format": "int32"}, "beschreibungen": {"type": "array", "items": {"$ref": "#/components/schemas/BeschreibungsText"}, "nullable": true}}, "additionalProperties": false, "description": "Die einzelnen Ergebnisse der Substitutionen."}, "SubstitutionContainer": {"required": ["arztInformationsSystem", "ik", "pzn", "referenzDatum", "vertragsIdentifikator"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "ik": {"minLength": 1, "type": "string"}, "referenzDatum": {"type": "string", "format": "date-time"}, "vertragsIdentifikator": {"minLength": 1, "type": "string"}, "pzn": {"minLength": 1, "type": "string"}}, "additionalProperties": false, "description": "Der Container mit der PZN für die Substitute abgerufen werden sollen."}, "SubstitutionResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "substitutionen": {"type": "array", "items": {"$ref": "#/components/schemas/Substitution"}, "nullable": true}}, "additionalProperties": false, "description": "Das Ergebnis einer Substitutionsanforderung."}, "UebermittlungsStatus": {"enum": ["KeineAngabe", "OK", "FachlicheVerletzung", "KeineBerechtigungWebService", "Wartung", "Interner<PERSON><PERSON><PERSON>", "UngueltigeStammdaten"], "type": "string"}, "Verordnungen": {"required": ["rezepte"], "type": "object", "properties": {"rezepte": {"type": "array", "items": {"$ref": "#/components/schemas/Rezept"}}}, "additionalProperties": false, "description": "Die Verordnungen zum zugehörigen Patienten."}, "VerordnungsContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "dokumentationen", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "testuebermittlung", "vertragskontext", "vertragspartneridentifikator"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "vertragspartneridentifikator": {"minLength": 1, "type": "string"}, "vertragskontext": {"$ref": "#/components/schemas/Vertragskontext"}, "dokumentationen": {"type": "array", "items": {"$ref": "#/components/schemas/VerordnungsDokumentation"}}, "testuebermittlung": {"type": "boolean"}, "nurPrueflauf": {"type": "boolean"}}, "additionalProperties": false}, "VerordnungsDokumentation": {"type": "object", "properties": {"patient": {"$ref": "#/components/schemas/Patient"}, "verordnungen": {"$ref": "#/components/schemas/Verordnungen"}}, "additionalProperties": false, "description": "Eine Dokumentation, in der zu einem Patienten jeweils seine Verordnungen gelistet sind."}, "VerordnungsZeitpunkte": {"type": "object", "properties": {"erstellt": {"type": "string", "format": "date-time"}, "geaendert": {"type": "string", "format": "date-time"}, "gedruckt": {"type": "string", "format": "date-time", "nullable": true}, "geloescht": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false, "description": "Die Zeitpunkte für Erstellung, Änderung und Löschung des Rezeptes."}, "VerordnungsdatenUebermittlungResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "transferId": {"type": "string", "nullable": true}, "uebermittlungsprotokoll": {"type": "string", "format": "byte", "nullable": true}}, "additionalProperties": false, "description": "Das Ergebnis eines Verordnungsdatenversands."}, "Versicherungsnachweis": {"required": ["krankenkassenIk", "versichertenArt", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>mer"], "type": "object", "properties": {"krankenkassenIk": {"minLength": 1, "type": "string"}, "versichertenNummer": {"minLength": 1, "type": "string"}, "versichertenArt": {"minLength": 1, "type": "string"}, "besonderePersonengruppe": {"type": "string", "nullable": true}, "dmpKennzeichnung": {"type": "string", "default": "^(0?[0-9]|1[0-2]|(3|4)[0-9]|5[0-8])$", "nullable": true}}, "additionalProperties": false, "description": "Informationen über den Versicherungsnachweis des Patienten (i.d.R. Daten der Versichertenkarte)."}, "Vertragskontext": {"required": ["abrechnungsJahr", "abrechnungsQuartal", "honoraranlageIdentif<PERSON><PERSON>", "vertragsIdentifikator"], "type": "object", "properties": {"vertragsIdentifikator": {"minLength": 1, "type": "string"}, "honoraranlageIdentifikator": {"minLength": 1, "type": "string"}, "abrechnungsJahr": {"type": "integer", "format": "int32"}, "abrechnungsQuartal": {"type": "integer", "format": "int32"}}, "additionalProperties": false, "description": "Angaben zum Vertrag."}, "Vertragsteilnahme": {"required": ["istVertreterteilnahme", "vertragsIdentifikator"], "type": "object", "properties": {"vertragsIdentifikator": {"minLength": 1, "type": "string"}, "istVertreterteilnahme": {"type": "boolean"}}, "additionalProperties": false, "description": "Alle Informationen zu den Vertragsteilnahmen des abgerechneten Patienten."}}}}