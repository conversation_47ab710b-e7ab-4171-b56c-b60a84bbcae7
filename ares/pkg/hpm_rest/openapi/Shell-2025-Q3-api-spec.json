{"components": {"schemas": {"common.Meldung": {"properties": {"Art": {"example": "<PERSON><PERSON>", "type": "string"}, "Code": {"example": "X000100", "type": "string"}, "Kategorie": {"example": "KeineAngabe", "type": "string"}, "Nachricht": {"example": "<PERSON>s ist ein interner Fehler aufgetreten.", "type": "string"}, "UebermittlungsStatus": {"example": "KeineAngabe", "type": "string"}}, "type": "object"}, "common.ResponseStatus": {"properties": {"Meldungen": {"items": {"$ref": "#/components/schemas/common.Meldung"}, "type": "array", "uniqueItems": false}, "Status": {"example": "Fehlgeschlagen", "type": "string"}, "UebermittlungsStatus": {"example": "KeineAngabe", "type": "string"}}, "type": "object"}, "connectivity.EndPoint": {"properties": {"Endpunkt": {"example": "Frontend-Reverseproxy", "type": "string"}, "IstTLS": {"example": false, "type": "boolean"}, "Nachricht": {"example": "", "type": "string"}, "Port": {"example": 22220, "type": "integer"}, "Verfügbar": {"example": true, "type": "boolean"}}, "type": "object"}, "connectivity.HealthCheckResult": {"properties": {"EndpunktVerfügbarkeit": {"items": {"$ref": "#/components/schemas/connectivity.EndPoint"}, "type": "array", "uniqueItems": false}, "Verfügbar": {"example": true, "type": "boolean"}}, "type": "object"}, "infoService.HzvOnlineKeyInformation": {"properties": {"Bsnr": {"example": "0000000", "type": "string"}, "GeraeteId": {"example": "HZV000000", "type": "string"}, "GueltigBis": {"example": "2222-01-01", "type": "string"}, "GueltigVon": {"example": "2000-01-01", "type": "string"}, "SerienNummer": {"example": "3000000", "type": "string"}, "Vertragspartneridentifikator": {"example": "HA1B2C3", "type": "string"}}, "type": "object"}, "infoService.LiefereHPMInformationResult": {"properties": {"HpmDateTimeNow": {"example": "2023-07-20T12:55:45.5761588+02:00", "type": "string"}, "HzvOnlineKeyInformationen": {"items": {"$ref": "#/components/schemas/infoService.HzvOnlineKeyInformation"}, "type": "array", "uniqueItems": false}, "IstTestsystem": {"example": true, "type": "boolean"}, "IstTestversion": {"example": true, "type": "boolean"}, "Status": {"example": "OK", "type": "string"}, "UebermittlungsStatus": {"example": "OK", "type": "string"}, "Uebertragungsweg": {"example": "Online", "type": "string"}, "Version": {"example": "9999-4-0000", "type": "string"}}, "type": "object"}, "infoService.UebertragungswegJSON": {"description": "Setzt den Uebertragungsweg.", "properties": {"Uebertragungsweg": {"example": "Online", "type": "string"}}, "type": "object"}, "reverseProxyFrontend.ConnectivityCheckResult": {"properties": {"ErreichbarkeitExtrnerEndpunkte": {"items": {"$ref": "#/components/schemas/reverseProxyFrontend.ExternalEndpointStatus"}, "type": "array", "uniqueItems": false}, "HPMKonfiguration": {"example": "Produktivversion", "type": "string"}, "HPMVersion": {"example": "2025-1-001", "type": "string"}}, "type": "object"}, "reverseProxyFrontend.ExternalEndpointStatus": {"properties": {"Name": {"example": "HZV", "type": "string"}, "Status": {"example": "<PERSON><PERSON><PERSON><PERSON>", "type": "string"}}, "type": "object"}, "reverseProxyFrontend.TimestampJSON": {"properties": {"Timestamp": {"example": "2023-07-20T09:52:59", "type": "string"}}, "type": "object"}}}, "info": {"title": "HPM Infoservice", "version": "1.0"}, "externalDocs": {"description": "", "url": ""}, "paths": {"/api/basicconnectivity": {"get": {"description": "Prüft die Konnektivität betriebsrelevanter, externer Endpunkte und liefert das Ergebnis in strukturierter JSON Form zurück.\nZusätzlich werden Versionsinformationen geliefert.", "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/reverseProxyFrontend.ConnectivityCheckResult"}}}, "description": "Success"}}, "summary": "Konnektivitätsprüfung betriebsrelevanter, externer Endpunkte", "tags": ["connectivity"]}}, "/api/health": {"get": {"description": "Gibt die Verfügbarkeit des HPMs und der einzelnen Endpunkte in strukturierter JSON Form zurück.\nDas allgemeine Feld \"Verfügbar\" ist nur dann \"true\", wenn alle Endpunkte verfügbar sind.", "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/connectivity.HealthCheckResult"}}}, "description": "Success"}}, "summary": "HPM und Fachservice Verfügbarkeitscheck", "tags": ["health"]}}, "/api/hpminformation": {"get": {"description": "Dieser Endpunkt gibt Informationen zu Status, UebermittlungsStatus, Uebertragungsweg, Version, HpmDateTimeNow, IstTestversion, IstTestsystem, HzvOnlineKeyInformationen im JSON-Format zurück.", "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/infoService.LiefereHPMInformationResult"}}}, "description": "Success"}}, "summary": "HPM-Informationen abrufen", "tags": ["hpminformation"]}}, "/api/konnektivitaet": {"get": {"description": "Bei dem Aufru<PERSON> dieser Schnittstelle werden Konnektivitätstest getriggert. Ist Testübermittlung: true, ist das Ziel der Konnektivitätstests die Test-Umgebung.", "parameters": [{"description": "legt die Umgebung fest", "in": "query", "name": "testuebermittlung", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/common.ResponseStatus"}}}, "description": "Success"}}, "summary": "Prüfe Konnektivität", "tags": ["konnektivitaet"]}}, "/api/konnektivitaeteav": {"get": {"description": "Bei dem Aufru<PERSON> dieser Schnittstelle werden Konnektivitätstest getriggert. Ist Testübermittlung: true, ist das Ziel der Konnektivitätstests die Test-Umgebung.", "parameters": [{"description": "legt die Umgebung fest", "in": "query", "name": "testuebermittlung", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/common.ResponseStatus"}}}, "description": "Success"}}, "summary": "Prüfe Konnektivität (EAV)", "tags": ["konnektivitaeteav"]}}, "/api/timestamp": {"get": {"description": "Gibt die Zeit des HPMs zurück.", "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/reverseProxyFrontend.TimestampJSON"}}}, "description": "Success"}}, "summary": "HPM-Zeit abrufen", "tags": ["timestamp"]}}, "/api/uebertragungsweg": {"put": {"description": "Über diese Schnittstelle kann das Flag \"IstOnline\" in der hpm_config gesetzt werden (Online/Offline)", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/infoService.UebertragungswegJSON"}}}, "description": "Setze Übertragungsweg", "required": true}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/common.ResponseStatus"}}}, "description": "Success"}}, "summary": "Übertragungsweg setzen", "tags": ["uebertragungsweg"]}}, "/api/update": {"post": {"description": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> dieser Schnittstelle wird versucht ein Update des HPM auszuführen.", "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/common.ResponseStatus"}}}, "description": "Success"}}, "summary": "Update anstoßen", "tags": ["update"]}}}, "openapi": "3.1.0"}