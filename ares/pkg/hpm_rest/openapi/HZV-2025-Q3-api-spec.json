{"openapi": "3.0.4", "info": {"title": "HPM 2025 Q3", "version": "2025 Q3"}, "paths": {"/api/abrechnungen": {"post": {"tags": ["Abrechnungen"], "summary": "Validierung und Versand einer Abrechnung für einen Vertrag.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AbrechnungsContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AbrechnungsContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AbrechnungsContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AbrechnungsResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AbrechnungsResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AbrechnungsResultat"}}}}}}}, "/api/abrechnungen/voreinschreibeleistungen": {"post": {"tags": ["Abrechnungen"], "summary": "Validierung und Versand einer Voreinschreibeleistung.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendeVorEinschreibeLeistungContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendeVorEinschreibeLeistungContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SendeVorEinschreibeLeistungContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransferResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransferResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransferResultat"}}}}}}}, "/api/auswertungen": {"post": {"tags": ["Auswertungen"], "summary": "Krankheitsbildauswertungen für Patienten", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/KrankheitsbildAuswertungContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/KrankheitsbildAuswertungContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/KrankheitsbildAuswertungContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/KrankheitsbildAuswertungResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/KrankheitsbildAuswertungResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/KrankheitsbildAuswertungResultat"}}}}}}}, "/api/bereitgestellteteilnehmerverzeichnisse": {"post": {"tags": ["BereitgestellteTeilnehmerVerzeichnisse"], "summary": "<PERSON><PERSON><PERSON><PERSON> <PERSON>hmerverzeichnissen für den Arzt.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LiefereBereitgestellteTeilnehmerverzeichnisseAnfrageContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereBereitgestellteTeilnehmerverzeichnisseAnfrageContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LiefereBereitgestellteTeilnehmerverzeichnisseAnfrageContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LiefereBereitgestellteTeilnehmerverzeichnisseResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LiefereBereitgestellteTeilnehmerverzeichnisseResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereBereitgestellteTeilnehmerverzeichnisseResultat"}}}}}}}, "/api/migrationen/vertragspartneridentifikator/hausarzt": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Gibt den Vertragspartneridentifikator für eine HaevgID zurück", "parameters": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "in": "query", "description": "Die HaevgID, die in den Vertragspartneridentifikator migriert werden soll.", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/VertragspartneridentifikatorResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VertragspartneridentifikatorResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VertragspartneridentifikatorResultat"}}}}}}}, "/api/migrationen/vertragspartneridentifikator/facharzt": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "summary": "Gibt den Vertragspartneridentifikator für eine MediID zurück", "parameters": [{"name": "mediId", "in": "query", "description": "Die MediID, die in den Vertragspartneridentifikator migriert werden soll.", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/VertragspartneridentifikatorResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VertragspartneridentifikatorResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VertragspartneridentifikatorResultat"}}}}}}}, "/api/partnerdaten/edmp": {"post": {"tags": ["Partnerdaten"], "summary": "<PERSON><PERSON><PERSON>Date<PERSON>.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EDMPContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EDMPContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EDMPContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransferResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransferResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransferResultat"}}}}}}}, "/api/partnerdaten/pracman": {"post": {"tags": ["Partnerdaten"], "summary": "<PERSON><PERSON><PERSON>.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PracManContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PracManContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PracManContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TransferResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TransferResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TransferResultat"}}}}}}}, "/api/teilnehmerverzeichnis": {"post": {"tags": ["TeilnehmerVerzeichnisse"], "summary": "<PERSON><PERSON><PERSON><PERSON> von strukturieren Teilnehmerverzeichnissen.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LiefereTeilnehmerverzeichnisAnfrageContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereTeilnehmerverzeichnisAnfrageContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LiefereTeilnehmerverzeichnisAnfrageContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LiefereTeilnehmerverzeichnisResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LiefereTeilnehmerverzeichnisResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereTeilnehmerverzeichnisResultat"}}}}}}}, "/api/versichertenteilnahmen/erklaerungen": {"post": {"tags": ["Versichertenteilnahmen"], "summary": "<PERSON><PERSON><PERSON> von Versichertenteilnahmeerklärungen (nur für unterstützte Verträge).", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeilnahmeerklaerungContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TeilnahmeerklaerungContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TeilnahmeerklaerungContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TeilnahmeerklaerungResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TeilnahmeerklaerungResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TeilnahmeerklaerungResultat"}}}}}}}, "/api/versichertenteilnahmen/pruefungen": {"post": {"tags": ["Versichertenteilnahmen"], "summary": "Prüfung auf Versichertenteilnahme an Verträgen.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeilnahmeListeContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TeilnahmeListeContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TeilnahmeListeContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PruefeVersichertenteilnahmeResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PruefeVersichertenteilnahmeResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PruefeVersichertenteilnahmeResultat"}}}}}}}, "/api/vertragspartner/{vertragspartneridentifikator}": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "Abruf aller Informationen des Vertragspartners", "parameters": [{"name": "X-AbsenderBsnr", "in": "header", "description": "Die BSNR des Absenders.", "schema": {"type": "string"}}, {"name": "X-Systemoid", "in": "header", "description": "Die SystemOID des Absenders.", "schema": {"type": "string"}}, {"name": "vertragspartneridentifikator", "in": "path", "description": "Der Vertragspartneridentifikator des Vertragspartners.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/VertragspartnerResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VertragspartnerResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VertragspartnerResultat"}}}}}}}, "/api/vertragspartner/{vertragspartneridentifikator}/vertragsteilnahmen": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON> <PERSON> Vertragsteilnahmen des Vertragspartners", "parameters": [{"name": "X-AbsenderBsnr", "in": "header", "description": "Die BSNR des Absenders.", "schema": {"type": "string"}}, {"name": "X-Systemoid", "in": "header", "description": "Die SystemOID des Absenders.", "schema": {"type": "string"}}, {"name": "vertragspartneridentifikator", "in": "path", "description": "Der Vertragspartneridentifikator des Vertragspartners.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/VertragspartnerVertragsteilnahmenResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VertragspartnerVertragsteilnahmenResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VertragspartnerVertragsteilnahmenResultat"}}}}}}}, "/api/vertragspartner/{vertragspartneridentifikator}/aerzte": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON> <PERSON> Ärzten des Vertragspartners", "parameters": [{"name": "X-AbsenderBsnr", "in": "header", "description": "Die BSNR des Absenders.", "schema": {"type": "string"}}, {"name": "X-Systemoid", "in": "header", "description": "Die SystemOID des Absenders.", "schema": {"type": "string"}}, {"name": "vertragspartneridentifikator", "in": "path", "description": "Der Vertragspartneridentifikator des Vertragspartners.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/VertragspartnerAerzteResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VertragspartnerAerzteResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VertragspartnerAerzteResultat"}}}}}}}, "/api/vertragspartner/{vertragspartneridentifikator}/betriebsstaetten": {"get": {"tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON><PERSON><PERSON> <PERSON> Betriebsstätten des Vertragspartners", "parameters": [{"name": "X-AbsenderBsnr", "in": "header", "description": "Die BSNR des Absenders.", "schema": {"type": "string"}}, {"name": "X-Systemoid", "in": "header", "description": "Die SystemOID des Absenders.", "schema": {"type": "string"}}, {"name": "vertragspartneridentifikator", "in": "path", "description": "Der Vertragspartneridentifikator des Vertragspartners.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/VertragspartnerBetriebsstaettenResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/VertragspartnerBetriebsstaettenResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VertragspartnerBetriebsstaettenResultat"}}}}}}}}, "components": {"schemas": {"AbrechnungsAnzahlenTyp": {"type": "object", "properties": {"leistungenAnzahl": {"$ref": "#/components/schemas/LeistungenAnzahlTyp"}, "diagnosenAnzahl": {"$ref": "#/components/schemas/DiagnosenAnzahlTyp"}}, "additionalProperties": false, "description": "Die Anzahl der Abrechnungen."}, "AbrechnungsContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "dokumentationen", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "testuebermittlung", "uebertragungsart", "vertragskontext", "vertragspartneridentifikator"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "vertragspartneridentifikator": {"minLength": 1, "type": "string"}, "vertragskontext": {"$ref": "#/components/schemas/Vertragskontext"}, "dokumentationen": {"type": "array", "items": {"$ref": "#/components/schemas/AbrechnungsDokumentation"}}, "testuebermittlung": {"type": "boolean"}, "nurPrueflauf": {"type": "boolean"}, "uebertragungsart": {"$ref": "#/components/schemas/Uebertragungsart"}}, "additionalProperties": false, "description": "Der Container mit den Daten für die Abrechnung."}, "AbrechnungsDokumentation": {"required": ["patient", "unfallkennzeichenGesetzt"], "type": "object", "properties": {"patient": {"$ref": "#/components/schemas/Patient"}, "diagnosen": {"type": "array", "items": {"$ref": "#/components/schemas/Diagnose"}, "nullable": true}, "leistungen": {"type": "array", "items": {"$ref": "#/components/schemas/Leistung"}, "nullable": true}, "praxisgebuehren": {"type": "array", "items": {"$ref": "#/components/schemas/Praxisgebuehr"}, "nullable": true}, "ueberweisungen": {"type": "array", "items": {"$ref": "#/components/schemas/Ueberweisung"}, "nullable": true}, "operationen": {"type": "array", "items": {"$ref": "#/components/schemas/Operation"}, "nullable": true}, "unfallkennzeichenGesetzt": {"type": "boolean"}}, "additionalProperties": false, "description": "Die AbrechnungsDokumentation enthält unter anderem, für genau einen Vertrag, einen abrechnenden Vertragsarzt, ein Quartal und ein Jahr, die bezogenen Leistungen und die Diagnosen eines zugehörigen Patienten."}, "AbrechnungsResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "transferId": {"type": "string", "nullable": true}, "abrechnungsAnzahlen": {"$ref": "#/components/schemas/AbrechnungsAnzahlenTyp"}, "uebermittlungsBeleg": {"$ref": "#/components/schemas/UebermittlungsBelegTyp"}, "datentraeger": {"$ref": "#/components/schemas/DatentraegerTyp"}, "uebermittlungsprotokoll": {"type": "string", "format": "byte", "nullable": true}}, "additionalProperties": false, "description": "Das Ergebnis eines Abrechnungsversands."}, "Anforderungszeitpunkt": {"required": ["datum", "<PERSON><PERSON><PERSON>"], "type": "object", "properties": {"datum": {"type": "string", "format": "date-time"}, "uhrzeit": {"minLength": 1, "type": "string"}}, "additionalProperties": false, "description": "Der Anforderungszeitpunkt der Leistung."}, "ArztIdentifikation": {"type": "object", "properties": {"bsnr": {"type": "string", "nullable": true}, "lanr": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Die Identifikation eines Arztes anhand von LANR und BSNR."}, "BetreuteamArzt": {"required": ["lanr"], "type": "object", "properties": {"lanr": {"minLength": 1, "type": "string"}}, "additionalProperties": false, "description": "Ein Arzt eines Betreuteams."}, "BetreuteamBetriebsstaette": {"required": ["bsnr"], "type": "object", "properties": {"bsnr": {"minLength": 1, "type": "string"}}, "additionalProperties": false, "description": "Eine Betriebsstaette eines Betreuteams."}, "DatentraegerTyp": {"type": "object", "properties": {"datentraegerID": {"type": "string", "nullable": true}, "isoImage": {"type": "string", "format": "byte", "nullable": true}, "begleitschreiben": {"type": "string", "format": "byte", "nullable": true}}, "additionalProperties": false, "description": "Der nach einer erfolgreichen Offline-Abrechnung erstellte Datenträger, dessen ID und dessen Begleitschreiben."}, "Diagnose": {"required": ["codeSystemName", "diagnoseCode", "diagnoseId", "dokumentationsDatum", "seitenlokalisation", "sicherheit"], "type": "object", "properties": {"codeSystemName": {"$ref": "#/components/schemas/DiagnoseCodeSystemName"}, "dokumentationsDatum": {"type": "string", "format": "date-time"}, "diagnoseCode": {"minLength": 1, "type": "string"}, "sicherheit": {"$ref": "#/components/schemas/DiagnoseSicherheit"}, "seitenlokalisation": {"$ref": "#/components/schemas/DiagnoseSeitenlokalisation"}, "istDauerDiagnose": {"type": "boolean"}, "diagnoseId": {"minLength": 1, "type": "string"}, "stellvertreter": {"$ref": "#/components/schemas/Stellvertreter"}, "diagnoseausnahmetatbestand": {"type": "string", "nullable": true}, "diagnoseerlaeuterung": {"type": "string", "nullable": true}, "betreuteamArzt": {"$ref": "#/components/schemas/BetreuteamArzt"}, "betreuteamBetriebsstaette": {"$ref": "#/components/schemas/BetreuteamBetriebsstaette"}}, "additionalProperties": false, "description": "Alle Informationen zu der erstellten Diagnose."}, "DiagnoseCodeSystemName": {"enum": ["None", "Icd10gm2020", "Icd10gm2021", "Icd10gm2022", "Icd10gm2023", "Icd10gm2024", "Icd10gm2025"], "type": "string"}, "DiagnoseElement": {"required": ["codeSystemName", "diagnoseCode", "sicherheit"], "type": "object", "properties": {"codeSystemName": {"$ref": "#/components/schemas/DiagnoseCodeSystemName"}, "diagnoseCode": {"minLength": 1, "type": "string"}, "sicherheit": {"$ref": "#/components/schemas/DiagnoseSicherheit"}}, "additionalProperties": false, "description": "Diagnoseinformationen für eine Teilnahmeerklärung im Rahmen eines Facharztvertrages."}, "DiagnoseSeitenlokalisation": {"enum": ["U", "L", "R", "B"], "type": "string"}, "DiagnoseSicherheit": {"enum": ["KeineAngabe", "G", "V", "Z", "A"], "type": "string"}, "DiagnosenAnzahlTyp": {"type": "object", "properties": {"anzahl": {"type": "integer", "format": "int32"}, "diagnosenAnzahlDetail": {"type": "array", "items": {"$ref": "#/components/schemas/DokumentationsAnzahlDetailTyp"}, "nullable": true}}, "additionalProperties": false, "description": "Die Gesamtzahl der übermittelten Diagnosen."}, "DokumentationsAnzahlDetailTyp": {"type": "object", "properties": {"auspraegung": {"type": "string", "nullable": true}, "anzahl": {"type": "integer", "format": "int32"}}, "additionalProperties": false, "description": "Die Details der übermittelten Diagnosen."}, "EDMPContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "fileContent", "filename", "ik", "testuebermittlung", "vertragsIdentifikator", "vertragspartneridentifikator"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "vertragspartneridentifikator": {"minLength": 1, "type": "string"}, "filename": {"minLength": 1, "type": "string"}, "fileContent": {"type": "string", "format": "byte"}, "ik": {"minLength": 1, "type": "string"}, "dokumentationsDatum": {"type": "string", "format": "date-time", "nullable": true}, "testuebermittlung": {"type": "boolean"}, "vertragsIdentifikator": {"minLength": 1, "type": "string"}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Versand von EDMP Daten."}, "KrankheitsbildAuswertungContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "auswertungsJahr", "auswertungsKontext", "auswertungsQuartal", "<PERSON>en", "vertragsIdentifikator", "vertragspartneridentifikator"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "vertragspartneridentifikator": {"minLength": 1, "type": "string"}, "vertragsIdentifikator": {"minLength": 1, "type": "string"}, "auswertungsKontext": {"minLength": 1, "type": "string"}, "auswertungsJahr": {"type": "integer", "format": "int32"}, "auswertungsQuartal": {"type": "integer", "format": "int32"}, "patienten": {"type": "array", "items": {"$ref": "#/components/schemas/KrankheitsbildAuswertungPatient"}}}, "additionalProperties": false}, "KrankheitsbildAuswertungPatient": {"type": "object", "properties": {"patientenId": {"type": "string", "nullable": true}, "diagnosen": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "KrankheitsbildAuswertungResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "patienten": {"type": "array", "items": {"$ref": "#/components/schemas/KrankheitsbildAuswertungResultatPatient"}, "nullable": true}}, "additionalProperties": false}, "KrankheitsbildAuswertungResultatPatient": {"type": "object", "properties": {"patientenId": {"type": "string", "nullable": true}, "krankheitsbilder": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Leistung": {"required": ["leistungsId", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "type": "object", "properties": {"leistungsdatum": {"type": "string", "format": "date-time", "nullable": true}, "leistungsziffer": {"minLength": 1, "type": "string"}, "zusatzinformation": {"type": "string", "nullable": true}, "leistungsId": {"minLength": 1, "type": "string"}, "stellvertreter": {"$ref": "#/components/schemas/Stellvertreter"}, "inVertretungFuer": {"$ref": "#/components/schemas/ArztIdentifikation"}, "ueberweisenderArzt": {"$ref": "#/components/schemas/ArztIdentifikation"}, "anforderungszeitpunkt": {"$ref": "#/components/schemas/Anforderungszeitpunkt"}, "abrechnungsbegruendung": {"type": "string", "nullable": true}, "sachkosten": {"type": "array", "items": {"$ref": "#/components/schemas/Sa<PERSON>ten"}, "nullable": true}, "betreuteamArzt": {"$ref": "#/components/schemas/BetreuteamArzt"}, "betreuteamBetriebsstaette": {"$ref": "#/components/schemas/BetreuteamBetriebsstaette"}}, "additionalProperties": false, "description": "Alle Informationen zu einer spezifischen Leistung aus den abgerechneten Leistungen."}, "LeistungenAnzahlTyp": {"type": "object", "properties": {"anzahl": {"type": "integer", "format": "int32"}, "leistungenAnzahlDetail": {"type": "array", "items": {"$ref": "#/components/schemas/DokumentationsAnzahlDetailTyp"}, "nullable": true}}, "additionalProperties": false, "description": "Die Gesamtzahl der übermittelten Leistungen."}, "LiefereBereitgestellteTeilnehmerverzeichnisseAnfrageContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "vertragspartneridentifikator"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "vertragspartneridentifikator": {"minLength": 1, "type": "string"}, "testuebermittlung": {"type": "boolean"}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Abru<PERSON> von bereitgestellten Teilnehmerverzeichnissen."}, "LiefereBereitgestellteTeilnehmerverzeichnisseResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "teilnehmerverzeichnisse": {"type": "array", "items": {"$ref": "#/components/schemas/TeilnehmerverzeichnisInfo"}, "nullable": true}}, "additionalProperties": false, "description": "Die von der Funktion LiefereBereitgestellteTeilnehmerverzeichnisse gelieferte Ergebnisstruktur."}, "LiefereTeilnehmerverzeichnisAnfrageContainer": {"required": ["abrufcode", "absenderBsnr", "arztInformationsSystem", "dokumentIdentifikator", "testuebermittlung", "vertragspartneridentifikator"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "vertragspartneridentifikator": {"minLength": 1, "type": "string"}, "abrufcode": {"minLength": 1, "type": "string", "default": "^[A-z0-9]{4}-?[A-z0-9]{4}$"}, "dokumentIdentifikator": {"minLength": 1, "type": "string"}, "testuebermittlung": {"type": "boolean"}}, "additionalProperties": false, "description": "Der Container mit Angaben zum <PERSON><PERSON><PERSON><PERSON> von Teilnehmerverzei<PERSON>sen."}, "LiefereTeilnehmerverzeichnisResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "PatientParticipationList": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Das Ergebnis des Abrufs von Teilnehmerverzeichnissen."}, "Meldung": {"type": "object", "properties": {"nachricht": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "art": {"$ref": "#/components/schemas/Meldu<PERSON>t"}, "kategorie": {"$ref": "#/components/schemas/Meldungskategorie"}, "referenzen": {"type": "array", "items": {"$ref": "#/components/schemas/Referenz"}, "nullable": true}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}}, "additionalProperties": false, "description": "<PERSON> Meldung, die an den Aufrufer gesendet wird."}, "Meldungsart": {"enum": ["Unbekannt", "Information", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "type": "string"}, "Meldungskategorie": {"enum": ["KeineAngabe", "Validierung", "Laufzeit", "Rechenzentrum"], "type": "string"}, "Operation": {"required": ["operationId", "operationsDatum", "operationsSchluessel", "seitenlokalisation"], "type": "object", "properties": {"operationId": {"minLength": 1, "type": "string"}, "stellvertreter": {"$ref": "#/components/schemas/Stellvertreter"}, "inVertretungFuer": {"$ref": "#/components/schemas/ArztIdentifikation"}, "operationsDatum": {"type": "string", "format": "date-time"}, "operationsSchluessel": {"minLength": 1, "type": "string"}, "seitenlokalisation": {"$ref": "#/components/schemas/DiagnoseSeitenlokalisation"}}, "additionalProperties": false, "description": "Alle Informationen zu einer spezifischen Operation aus den abgerechneten Operationen."}, "Patient": {"required": ["geburtsdatum", "geschlecht", "nachname", "patientenId", "versicherungsnachweis", "vorname"], "type": "object", "properties": {"aktuelleVertragsteilnahmen": {"type": "array", "items": {"$ref": "#/components/schemas/Vertragsteilnahme"}, "nullable": true}, "patientenId": {"minLength": 1, "type": "string"}, "nachname": {"minLength": 1, "type": "string"}, "vorname": {"minLength": 1, "type": "string"}, "geburtsdatum": {"minLength": 1, "type": "string"}, "geschlecht": {"$ref": "#/components/schemas/PatientGeschlecht"}, "versicherungsnachweis": {"$ref": "#/components/schemas/Versicherungsnachweis"}}, "additionalProperties": false, "description": "Alle Informationen zum Patienten."}, "PatientGeschlecht": {"enum": ["U", "M", "W", "X", "D"], "type": "string"}, "PracManContainer": {"required": ["content", "dokumentationsDatum", "testuebermittlung", "vertragsIdentifikator"], "type": "object", "properties": {"content": {"type": "string", "format": "byte"}, "dokumentationsDatum": {"type": "string", "format": "date-time"}, "testuebermittlung": {"type": "boolean"}, "vertragsIdentifikator": {"minLength": 1, "type": "string"}}, "additionalProperties": false, "description": "Der Container zum Versand der PracMan-Daten."}, "Praxisgebuehr": {"required": ["datum", "praxisgebuehrId", "ziffer"], "type": "object", "properties": {"praxisgebuehrId": {"minLength": 1, "type": "string"}, "datum": {"type": "string", "format": "date-time"}, "ziffer": {"minLength": 1, "type": "string"}, "stellvertreter": {"$ref": "#/components/schemas/Stellvertreter"}}, "additionalProperties": false, "description": "Die zu einer entrichteten Praxisgebühr gehörenden Details."}, "PruefeVersichertenteilnahmeResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "teilnahmeResultate": {"type": "array", "items": {"$ref": "#/components/schemas/TeilnahmePatientResultat"}, "nullable": true}}, "additionalProperties": false}, "Referenz": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "referenzTyp": {"$ref": "#/components/schemas/ReferenzTyp"}}, "additionalProperties": false, "description": "Die Referenzen zu den vom Aufrufer übermittelten Objekten."}, "ReferenzTyp": {"enum": ["KeineAngabe", "Le<PERSON><PERSON>", "Patient", "Diagnose", "Ueberweisung", "Praxisgebuehr", "Vertragsidentifikator", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Rezept", "Operation", "Zielwert", "Istwert", "Erkrankungsstufe", "Hinderungsfaktor", "IstFremdeingeschrieben", "IstNichtFremdeingeschrieben"], "type": "string"}, "ResultatStatus": {"enum": ["Unbekannt", "OK", "Fehlgeschlagen", "TeilweiseVerarbeitet"], "type": "string"}, "Sachkosten": {"required": ["betrag", "bezeichnungen"], "type": "object", "properties": {"betrag": {"minLength": 1, "type": "string"}, "bezeichnungen": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false, "description": "Ein konkreter Kostenpunkt der Sachkosten."}, "SendeVorEinschreibeLeistungContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "patient", "testuebermittlung", "vertragskontext", "vertragspartneridentifikator", "vorEinschreibeLeistung"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "vertragspartneridentifikator": {"minLength": 1, "type": "string"}, "vertragskontext": {"$ref": "#/components/schemas/Vertragskontext"}, "patient": {"$ref": "#/components/schemas/Patient"}, "vorEinschreibeLeistung": {"$ref": "#/components/schemas/VorEinschreibeLeistung"}, "testuebermittlung": {"type": "boolean"}, "nurPrueflauf": {"type": "boolean"}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Versand von Voreinschreibeleistungen."}, "SoftwareInformation": {"required": ["systemOid", "version"], "type": "object", "properties": {"systemOid": {"minLength": 1, "type": "string"}, "version": {"minLength": 1, "type": "string"}, "organisation": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Informationen über das Arztinformationssystem und dessen Hersteller."}, "Stellvertreter": {"required": ["lanr"], "type": "object", "properties": {"lanr": {"minLength": 1, "type": "string"}}, "additionalProperties": false, "description": "Der Stellvertreter, der in Vertretung für den abrechnenden Vertragsarzt gehandelt hat."}, "TeilnahmeListeContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "teilnahmeanfragen", "testuebermittlung", "vertragspartneridentifikator"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "vertragspartneridentifikator": {"minLength": 1, "type": "string"}, "teilnahmeanfragen": {"type": "array", "items": {"$ref": "#/components/schemas/Teilnahmeanfrage"}}, "testuebermittlung": {"type": "boolean"}}, "additionalProperties": false, "description": "Der Container mit Angaben zum <PERSON>b<PERSON><PERSON> von Teilnahmelisten."}, "TeilnahmePatientResultat": {"type": "object", "properties": {"patientenId": {"type": "string", "nullable": true}, "vertragsteilnahmen": {"type": "array", "items": {"$ref": "#/components/schemas/TeilnahmePatientVertragResultat"}, "nullable": true}}, "additionalProperties": false}, "TeilnahmePatientVertragResultat": {"type": "object", "properties": {"vertragsidentifikator": {"type": "string", "nullable": true}, "istFremdeingeschrieben": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "Teilnahmeanfrage": {"required": ["abfrageDatum", "krankenkassenIK", "patientenId", "versichertennummer", "vertragsIdentifikatoren"], "type": "object", "properties": {"abfrageDatum": {"type": "string", "format": "date-time"}, "krankenkassenIK": {"minLength": 1, "type": "string"}, "patientenId": {"minLength": 1, "type": "string"}, "versichertennummer": {"minLength": 1, "type": "string"}, "vertragsIdentifikatoren": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false, "description": "Die für eine Teilnahmeanfrage nötigen Daten."}, "TeilnahmeerklaerungContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "teilnahmeerklaerungen", "testuebermittlung", "vertragsIdentifikator", "vertragspartneridentifikator"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "vertragspartneridentifikator": {"minLength": 1, "type": "string"}, "teilnahmeerklaerungen": {"type": "array", "items": {"$ref": "#/components/schemas/TeilnahmeerklaerungPatient"}}, "testuebermittlung": {"type": "boolean"}, "vertragsIdentifikator": {"minLength": 1, "type": "string"}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Versand von Teilnahmeerklärungen."}, "TeilnahmeerklaerungPatient": {"required": ["geburtsdatum", "kassenIk", "kopfDatum", "lanr", "nachname", "patientenId", "teilnahmeerklaerungIdentifikator", "versichertennummer", "vorname"], "type": "object", "properties": {"geburtsdatum": {"minLength": 1, "type": "string"}, "nachname": {"minLength": 1, "type": "string"}, "patientenId": {"minLength": 1, "type": "string"}, "vorname": {"minLength": 1, "type": "string"}, "arztwechselgrund": {"type": "string", "nullable": true}, "istArztwechsel": {"type": "boolean"}, "istEinwilligungTelefonkontaktVorhanden": {"type": "boolean"}, "kassenIk": {"minLength": 1, "type": "string"}, "kopfDatum": {"type": "string", "format": "date-time"}, "teilnahmeerklaerungIdentifikator": {"minLength": 1, "type": "string"}, "versichertennummer": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "therapieverfahren": {"$ref": "#/components/schemas/Therapieverfahren"}, "therapieverfahrenSpecified": {"type": "boolean"}, "diagnosen": {"type": "array", "items": {"$ref": "#/components/schemas/DiagnoseElement"}, "nullable": true}}, "additionalProperties": false, "description": "<PERSON> Patient, der an dem Vertrag teilnehmen will."}, "TeilnahmeerklaerungResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}}, "additionalProperties": false, "description": "Die Resultatsmeldung der gesendeten Teilnahmeerklärungen."}, "TeilnehmerverzeichnisInfo": {"type": "object", "properties": {"dokumentIdentifikator": {"type": "string", "nullable": true}, "vertragsIdentifikator": {"type": "string", "nullable": true}, "jahr": {"type": "integer", "format": "int32"}, "quartal": {"type": "integer", "format": "int32"}, "version": {"type": "integer", "format": "int32"}}, "additionalProperties": false, "description": "Metainformation zu einem Teilnehmerverzeichnis."}, "Therapieverfahren": {"enum": ["V", "T", "N", "P", "A"], "type": "string"}, "TransferResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "transferId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UebermittlungsBelegTyp": {"type": "object", "properties": {"patientenIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "leistungsIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false, "description": "Der Beleg der erfolgreichen Übermittlung."}, "UebermittlungsStatus": {"enum": ["KeineAngabe", "OK", "FachlicheVerletzung", "KeineBerechtigungWebService", "Wartung", "Interner<PERSON><PERSON><PERSON>", "UngueltigeStammdaten"], "type": "string"}, "Uebertragungsart": {"enum": ["Online", "<PERSON><PERSON><PERSON><PERSON>"], "type": "string"}, "Ueberweisung": {"required": ["auftragsArt", "datum", "jahr", "quartal", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "type": "object", "properties": {"ueberweisungId": {"type": "string", "nullable": true}, "auftragsArt": {"$ref": "#/components/schemas/UeberweisungAuftragsart"}, "datum": {"type": "string", "format": "date-time"}, "jahr": {"type": "integer", "format": "int32"}, "quartal": {"type": "integer", "format": "int32"}, "ueberweisungAn": {"type": "string", "nullable": true}, "ueberweisungsart": {"$ref": "#/components/schemas/Ueberweisungsart"}, "stellvertreter": {"$ref": "#/components/schemas/Stellvertreter"}, "betreuteamArzt": {"$ref": "#/components/schemas/BetreuteamArzt"}, "betreuteamBetriebsstaette": {"$ref": "#/components/schemas/BetreuteamBetriebsstaette"}}, "additionalProperties": false, "description": "Die vollständigen Angaben einer Überweisung durch den abrechnenden Vertragsarzt, oder aber eine Überweisung an den abrechnenden Vertragsarzt."}, "UeberweisungAuftragsart": {"enum": ["KeineAngabe", "Auftragsleistung", "Konsiliaruntersuchung", "MitWeiterbehandlung"], "type": "string"}, "Ueberweisungsart": {"enum": ["KeineAngabe", "<PERSON><PERSON><PERSON>", "Praeventiv", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Paragraph116b"], "type": "string"}, "Versicherungsnachweis": {"required": ["krankenkassenIk", "versichertenArt", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>mer"], "type": "object", "properties": {"krankenkassenIk": {"minLength": 1, "type": "string"}, "versichertenNummer": {"minLength": 1, "type": "string"}, "versichertenArt": {"minLength": 1, "type": "string"}, "besonderePersonengruppe": {"type": "string", "nullable": true}, "dmpKennzeichnung": {"type": "string", "default": "^(0?[0-9]|1[0-2]|(3|4)[0-9]|5[0-8])$", "nullable": true}}, "additionalProperties": false, "description": "Informationen über den Versicherungsnachweis des Patienten (i.d.R. Daten der Versichertenkarte)."}, "Vertragskontext": {"required": ["abrechnungsJahr", "abrechnungsQuartal", "honoraranlageIdentif<PERSON><PERSON>", "vertragsIdentifikator"], "type": "object", "properties": {"vertragsIdentifikator": {"minLength": 1, "type": "string"}, "honoraranlageIdentifikator": {"minLength": 1, "type": "string"}, "abrechnungsJahr": {"type": "integer", "format": "int32"}, "abrechnungsQuartal": {"type": "integer", "format": "int32"}}, "additionalProperties": false, "description": "Angaben zum Vertrag."}, "VertragspartnerAerzteResultat": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ResultatStatus"}, "meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "aerzte": {"type": "array", "items": {"$ref": "#/components/schemas/VertragspartnerArzt"}, "nullable": true}}, "additionalProperties": false, "description": "Alle Ärzte, die einem Vertragspartner zugeordnet sind."}, "VertragspartnerArzt": {"required": ["gueltigBis", "gueltigVon", "lanr", "nachname", "vorname"], "type": "object", "properties": {"lanr": {"minLength": 1, "type": "string"}, "titel": {"type": "string", "nullable": true}, "vorname": {"minLength": 1, "type": "string"}, "nachname": {"minLength": 1, "type": "string"}, "gueltigVon": {"type": "string", "format": "date-time"}, "gueltigBis": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "description": "Ein Arzt mit Gültigkeitszeitraum der einem Vertragspartner zugeordnet ist."}, "VertragspartnerBetriebsstaette": {"required": ["bsnr", "gueltigBis", "gueltigVon", "istHauptbetriebsstaette", "ort", "<PERSON><PERSON><PERSON><PERSON>", "strasseHausnummer"], "type": "object", "properties": {"bsnr": {"minLength": 1, "type": "string"}, "istHauptbetriebsstaette": {"type": "boolean"}, "strasseHausnummer": {"minLength": 1, "type": "string"}, "postleitzahl": {"minLength": 1, "type": "string"}, "ort": {"minLength": 1, "type": "string"}, "gueltigVon": {"type": "string", "format": "date-time"}, "gueltigBis": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "description": "Eine Betriebsstaette mit Gültigkeitszeitraum die einem Vertragspartner zugeordnet ist."}, "VertragspartnerBetriebsstaettenResultat": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ResultatStatus"}, "meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "betriebsstaetten": {"type": "array", "items": {"$ref": "#/components/schemas/VertragspartnerBetriebsstaette"}, "nullable": true}}, "additionalProperties": false, "description": "Alle Betriebsstätten, die einem Vertragspartner zugeordnet sind."}, "VertragspartnerResultat": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ResultatStatus"}, "meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "identifikator": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "vertragsteilnahmen": {"type": "array", "items": {"$ref": "#/components/schemas/VertragspartnerVertragsteilnahme"}, "nullable": true}, "aerzte": {"type": "array", "items": {"$ref": "#/components/schemas/VertragspartnerArzt"}, "nullable": true}, "betriebsstaetten": {"type": "array", "items": {"$ref": "#/components/schemas/VertragspartnerBetriebsstaette"}, "nullable": true}}, "additionalProperties": false, "description": "Alle Informationen zu einem Vertragspartner."}, "VertragspartnerVertragsteilnahme": {"required": ["gueltigBis", "gueltigVon", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "vertragsidentifikator"], "type": "object", "properties": {"vertragsidentifikator": {"minLength": 1, "type": "string"}, "gueltigVon": {"type": "string", "format": "date-time"}, "gueltigBis": {"type": "string", "format": "date-time"}, "honoraranlageidentifikator": {"minLength": 1, "type": "string"}}, "additionalProperties": false, "description": "Ein Vertrag mit Gültigkeitszeitraum und Honoraranlage."}, "VertragspartnerVertragsteilnahmenResultat": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ResultatStatus"}, "meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "vertragsteilnahmen": {"type": "array", "items": {"$ref": "#/components/schemas/VertragspartnerVertragsteilnahme"}, "nullable": true}}, "additionalProperties": false, "description": "Alle Verträge, an denen der Vertragspartner aktuell eingeschrieben ist."}, "VertragspartneridentifikatorResultat": {"type": "object", "properties": {"status": {"$ref": "#/components/schemas/ResultatStatus"}, "meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "vertragspartneridentifikator": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Vertragsteilnahme": {"required": ["istVertreterteilnahme", "vertragsIdentifikator"], "type": "object", "properties": {"vertragsIdentifikator": {"minLength": 1, "type": "string"}, "istVertreterteilnahme": {"type": "boolean"}}, "additionalProperties": false, "description": "Alle Informationen zu den Vertragsteilnahmen des abgerechneten Patienten."}, "VorEinschreibeLeistung": {"required": ["leistungsId", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "type": "object", "properties": {"leistungsdatum": {"type": "string", "format": "date-time", "nullable": true}, "leistungsziffer": {"minLength": 1, "type": "string"}, "leistungsId": {"minLength": 1, "type": "string"}, "betreuteamArzt": {"$ref": "#/components/schemas/BetreuteamArzt"}, "betreuteamBetriebsstaette": {"$ref": "#/components/schemas/BetreuteamBetriebsstaette"}}, "additionalProperties": false, "description": "Die VorEinschreibeLeistungen des betreffenden Quartals."}}}}