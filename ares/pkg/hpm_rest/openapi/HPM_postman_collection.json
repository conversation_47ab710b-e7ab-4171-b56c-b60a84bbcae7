{"collection": {"info": {"_postman_id": "086db61f-0cdb-4c5d-ae03-52bd0b798316", "name": "HPM PVS Hersteller", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "createdAt": "2022-01-12T14:10:09Z", "lastUpdatedBy": "13375207", "uid": "14558926-086db61f-0cdb-4c5d-ae03-52bd0b798316"}, "item": [{"name": "HZV", "item": [{"name": "Teilnahmeprüfung", "event": [{"listen": "test", "script": {"id": "b60ec04d-1d68-4916-ba65-670f330ca64c", "exec": [""], "type": "text/javascript"}}], "id": "3970b37c-91d9-4c73-a163-881ebc6a2edd", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"AbsenderBsnr\": \"*********\",\r\n    \"Vertragspartneridentifikator\":\"H2JHACAAT7\",\r\n    \"Teilnahmeanfragen\": [\r\n        {\r\n            \"AbfrageDatum\": \"2017-10-26T00:00:00\",\r\n            \"KrankenkassenIK\": \"108018007\",\r\n            \"PatientenId\": \"P1\",\r\n            \"Versichertennummer\": \"222222222\",\r\n            \"VertragsIdentifikatoren\": [\r\n                \"AWH_01\"\r\n            ]\r\n        },\r\n        {\r\n            \"AbfrageDatum\": \"2017-10-26T00:00:00\",\r\n            \"KrankenkassenIK\": \"108018007\",\r\n            \"PatientenId\": \"P2\",\r\n            \"Versichertennummer\": \"333333333\",\r\n            \"VertragsIdentifikatoren\": [\r\n                \"AWH_01\"\r\n            ]\r\n        },\r\n        {\r\n            \"AbfrageDatum\": \"2017-10-26T00:00:00\",\r\n            \"KrankenkassenIK\": \"108018007\",\r\n            \"PatientenId\": \"P3\",\r\n            \"Versichertennummer\": \"444444444\",\r\n            \"VertragsIdentifikatoren\": [\r\n                \"AWH_01\"\r\n            ]\r\n        },\r\n        {\r\n            \"AbfrageDatum\": \"2017-10-26T00:00:00\",\r\n            \"KrankenkassenIK\": \"108018007\",\r\n            \"PatientenId\": \"P4\",\r\n            \"Versichertennummer\": \"555555555\",\r\n            \"VertragsIdentifikatoren\": [\r\n                \"AWH_01\"\r\n            ]\r\n        }\r\n    ],\r\n    \"Testuebermittlung\": true,\r\n    \"ArztInformationsSystem\": {\r\n        \"KbvPruefnummer\": \"8018110\",\r\n        \"Version\": \"Q3-2010.0\",\r\n        \"Vorname\": \"Max\",\r\n        \"Nachname\": \"Mustermann\",\r\n        \"Name\": \"TestAIS\",\r\n        \"Organisation\": \"TestAIS GmbH & Co KG\",\r\n        \"Strasse\": \"Hauptstraße 1\",\r\n        \"Plz\": \"12345\",\r\n        \"Stadt\": \"Haupthausen\",\r\n        \"Telefon\": \"1111/2222222\",\r\n        \"Telefax\": \"1111/3333333\",\r\n        \"TelefonMobil\": \"1111/4444444\",\r\n        \"SystemOid\": \"{{SYSTEM_OID}}\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{HPM_HOST}}:{{HPM_PORT}}/api/latest/hzv/versichertenteilnahmen/pruefungen", "protocol": "http", "host": ["{{HPM_HOST}}"], "port": "{{HPM_PORT}}", "path": ["api", "latest", "hzv", "versichertenteilnahmen", "pruefungen"]}}, "response": [], "uid": "14558926-3970b37c-91d9-4c73-a163-881ebc6a2edd"}, {"name": "StarteAbrechnung 014_AWH_01", "event": [{"listen": "test", "script": {"id": "418ad918-5b6b-457d-a268-48ee507b972c", "exec": [""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "58a9bf8a-cb5c-405d-8c4d-f71949d9ea07", "exec": [""], "type": "text/javascript"}}], "id": "fe5f1908-ab14-4eb4-b11b-86df86109828", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Dokumentationen\": [\r\n        {\r\n            \"Patient\": {\r\n                \"AktuelleVertragsteilnahmen\": [\r\n                    {\r\n                        \"VertragsIdentifikator\": \"AWH_01\",\r\n                        \"IstVertreterteilnahme\": false\r\n                    }\r\n                ],\r\n                \"PatientenId\": \"1\",\r\n                \"Nachname\": \"<PERSON><PERSON>\",\r\n                \"Vorname\": \"G\\u00FCnther\",\r\n                \"Geburtsdatum\": \"1970-12-31\",\r\n                \"Geschlecht\": 1,\r\n                \"Versicherungsnachweis\": {\r\n                    \"KrankenkassenIk\": \"107018425\",\r\n                    \"VersichertenNummer\": \"841104883\",\r\n                    \"VersichertenArt\": \"3\",\r\n                    \"BesonderePersonengruppe\": null,\r\n                    \"DmpKennzeichnung\": null\r\n                }\r\n            },\r\n            \"Diagnosen\": [\r\n                {\r\n                    \"CodeSystemName\": \"Icd10gm{{ABRECHNUNGSJAHR}}\",\r\n                    \"DokumentationsDatum\": \"{{ABRECHNUNGSJAHR}}-01-02T00:00:00\",\r\n                    \"DiagnoseCode\": \"B00.0\",\r\n                    \"Sicherheit\": 1,\r\n                    \"Seitenlokalisation\": 0,\r\n                    \"IstDauerDiagnose\": false,\r\n                    \"DiagnoseId\": \"1\",\r\n                    \"Stellvertreter\": null,\r\n                    \"IstAkutdiagnose\": false,\r\n                    \"IstGenerischeAngabe\": false\r\n                }\r\n            ],\r\n            \"Leistungen\": [\r\n                {\r\n                    \"Leistungsdatum\": \"{{ABRECHNUNGSJAHR}}-01-02T00:00:00\",\r\n                    \"Leistungsziffer\": \"0000\",\r\n                    \"LeistungsId\": \"1\",\r\n                    \"Stellvertreter\": null,\r\n                    \"InVertretungFuer\": null,\r\n                    \"UeberweisenderArzt\": null,\r\n                    \"Anforderungszeitpunkt\": null,\r\n                    \"Abrechnungsbegruendung\": null,\r\n                    \"Sachkosten\": null,\r\n                    \"Id\": \"1\",\r\n                    \"ReferenzTyp\": 1,\r\n                    \"Datum\": \"{{ABRECHNUNGSJAHR}}-01-02T00:00:00\"\r\n                }\r\n            ],\r\n            \"Praxisgebuehren\": [],\r\n            \"Ueberweisungen\": [],\r\n            \"Operationen\": null,\r\n            \"UnfallkennzeichenGesetzt\": false,\r\n            \"IstValide\": false\r\n        }\r\n    ],\r\n    \"Testuebermittlung\": true,\r\n    \"NurPrueflauf\": false,\r\n    \"Uebertragungsart\": 0,\r\n    \"Vertragskontext\": {\r\n        \"VertragsIdentifikator\": \"AWH_01\",\r\n        \"HonoraranlageIdentifikator\": \"AWH_01\",\r\n        \"AbrechnungsJahr\": {{ABRECHNUNGSJAHR}},\r\n        \"AbrechnungsQuartal\": 1\r\n    },\r\n    \"AbsenderBsnr\": \"*********\",\r\n    \"Vertragspartneridentifikator\":\"H2JHACAAT7\",\r\n    \"ArztInformationsSystem\": {\r\n        \"KbvPruefnummer\": \"1234567\",\r\n        \"Version\": \"1.0\",\r\n        \"Vorname\": \"Willi\",\r\n        \"Nachname\": \"Wichtig\",\r\n        \"Name\": \"Test-AIS\",\r\n        \"Organisation\": \"Test-Company\",\r\n        \"Strasse\": \"Eisenstr.\",\r\n        \"Plz\": \"50825\",\r\n        \"Stadt\": \"K\\u00F6ln\",\r\n        \"Telefon\": \"0221 / 12345\",\r\n        \"Telefax\": null,\r\n        \"TelefonMobil\": null,\r\n        \"SystemOid\": \"{{SYSTEM_OID}}\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{HPM_HOST}}:{{HPM_PORT}}/api/latest/hzv/abrechnungen", "protocol": "http", "host": ["{{HPM_HOST}}"], "port": "{{HPM_PORT}}", "path": ["api", "latest", "hzv", "abrechnungen"]}}, "response": [], "uid": "14558926-fe5f1908-ab14-4eb4-b11b-86df86109828"}, {"name": "SendeVersichertenteilnahme", "event": [{"listen": "test", "script": {"id": "566d47fa-da76-470c-81fb-14ecbd11b615", "exec": [""], "type": "text/javascript"}}], "id": "aaa33e85-10da-428c-8fd8-ac1362ce39af", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Teilnahmeerklaerungen\": [\r\n        {\r\n            \"Geburtsdatum\": \"1985-01-01\",\r\n            \"Nachname\": \"<PERSON>\",\r\n            \"PatientenId\": \"P100_000\",\r\n            \"Vorname\": \"<PERSON><PERSON>\",\r\n            \"Arztwechselgrund\": \"Patient ist umgezogen.\",\r\n            \"IstArztwechsel\": true,\r\n            \"IstEinwilligungTelefonkontaktVorhanden\": false,\r\n            \"KassenIk\": \"108891982\",\r\n            \"KopfDatum\": \"2017-10-26T00:00:00\",\r\n            \"TeilnahmeerklaerungIdentifikator\": \"17108891982000120192357*********20120701131022\",\r\n            \"Versichertennummer\": \"120192357\",\r\n            \"Lanr\": \"*********\"\r\n        },\r\n        {\r\n            \"Geburtsdatum\": \"1983-01-01\",\r\n            \"Nachname\": \"<PERSON>\",\r\n            \"PatientenId\": \"P200_000\",\r\n            \"Vorname\": \"<PERSON>\",\r\n            \"Arztwechselgrund\": null,\r\n            \"IstArztwechsel\": false,\r\n            \"IstEinwilligungTelefonkontaktVorhanden\": false,\r\n            \"KassenIk\": \"108891982\",\r\n            \"KopfDatum\": \"2017-10-26T00:00:00\",\r\n            \"TeilnahmeerklaerungIdentifikator\": \"17108891982000******************20120701131022\",\r\n            \"Versichertennummer\": \"*********\",\r\n            \"Lanr\": \"*********\"\r\n        }\r\n    ],\r\n    \"Testuebermittlung\": true,\r\n    \"AbsenderBsnr\": \"*********\",\r\n    \"Vertragspartneridentifikator\":\"H2JHACAAT7\",\r\n    \"VertragsIdentifikator\": \"BKK_VAG_BW\",\r\n    \"ArztInformationsSystem\": {\r\n        \"KbvPruefnummer\": \"8018110\",\r\n        \"Version\": \"Q3-2010.0\",\r\n        \"Vorname\": \"Max\",\r\n        \"Nachname\": \"Mustermann\",\r\n        \"Name\": \"TestAIS\",\r\n        \"Organisation\": \"TestAIS GmbH & Co KG\",\r\n        \"Strasse\": \"Hauptstraße 1\",\r\n        \"Plz\": \"12345\",\r\n        \"Stadt\": \"Haupthausen\",\r\n        \"Telefon\": \"1111/2222222\",\r\n        \"Telefax\": \"1111/3333333\",\r\n        \"TelefonMobil\": \"1111/4444444\",\r\n        \"SystemOid\": \"{{SYSTEM_OID}}\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{HPM_HOST}}:{{HPM_PORT}}/api/latest/hzv/versichertenteilnahmen/erklaerungen", "protocol": "http", "host": ["{{HPM_HOST}}"], "port": "{{HPM_PORT}}", "path": ["api", "latest", "hzv", "versichertenteilnahmen", "erklaerungen"]}}, "response": [], "uid": "14558926-aaa33e85-10da-428c-8fd8-ac1362ce39af"}, {"name": "SendeEdmpDaten", "event": [{"listen": "test", "script": {"id": "41dc9c04-7d78-4193-a919-f3f5f7f8b86d", "exec": [""], "type": "text/javascript"}}], "id": "31c36718-e5aa-4ff7-b004-7bda2fa41eb0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"Filename\": \"Datei.zip.XKM\",\r\n    \"IK\": \"661430035\",\r\n    \"FileContent\": \"VGVzdA==\",\r\n    \"DokumentationsDatum\": \"2017-10-26T00:00:00\",\r\n    \"Testuebermittlung\": true,\r\n    \"AbsenderBsnr\": \"*********\",\r\n    \"Vertragspartneridentifikator\":\"H2JHACAAT7\",\r\n    \"VertragsIdentifikator\": \"AWH_01\",\r\n    \"ArztInformationsSystem\": {\r\n        \"KbvPruefnummer\": \"1234567\",\r\n        \"Version\": \"1.0\",\r\n        \"Vorname\": \"Willi\",\r\n        \"Nachname\": \"Wichtig\",\r\n        \"Name\": \"Test-AIS\",\r\n        \"Organisation\": \"Test-Company\",\r\n        \"Strasse\": \"Eisenstr.\",\r\n        \"Plz\": \"50825\",\r\n        \"Stadt\": \"Köln\",\r\n        \"Telefon\": \"0221 / 12345\",\r\n        \"Telefax\": null,\r\n        \"TelefonMobil\": null,\r\n        \"SystemOid\": \"{{SYSTEM_OID}}\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{HPM_HOST}}:{{HPM_PORT}}/api/latest/hzv/partnerdaten/edmp", "protocol": "http", "host": ["{{HPM_HOST}}"], "port": "{{HPM_PORT}}", "path": ["api", "latest", "hzv", "partner<PERSON>n", "edmp"]}}, "response": [], "uid": "14558926-31c36718-e5aa-4ff7-b004-7bda2fa41eb0"}, {"name": "SendeVersichertenteilnahme_SOAP", "event": [{"listen": "test", "script": {"id": "8a1ae102-fe5a-4c28-9790-b7d6ad2914ec", "exec": ["var jsonObject = xml2Json(responseBody);\r", "var result = jsonObject[\"s:Envelope\"][\"s:Body\"][\"SendeTeilnahmeerklaerungResponse\"][\"SendeTeilnahmeerklaerungResult\"];\r", "eval(pm.environment.get(\"baseTest\"))(result, true, true, false);"], "type": "text/javascript"}}], "id": "94555b5c-8f6b-43c0-a0dc-7e6418a90a66", "protocolProfileBehavior": {"disableBodyPruning": true, "disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8", "type": "text"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\r\n  <soap:Body>\r\n    <SendeTeilnahmeerklaerung xmlns=\"http://haevg-rz.de/hpm\">\r\n      <teilnahmeerklaerungContainer>\r\n       <Teilnahmeerklaerungen>\r\n        <TeilnahmeerklaerungPatient>\r\n          <Geburtsdatum>1985-01-01</Geburtsdatum>\r\n          <Nachname>Müller</Nachname>\r\n          <PatientenId>P100_000</PatientenId>\r\n          <Vorname>Maik</Vorname>\r\n          <Arztwechselgrund>Patient ist umgezogen.</Arztwechselgrund>\r\n          <IstArztwechsel>true</IstArztwechsel>\r\n          <IstEinwilligungTelefonkontaktVorhanden>false</IstEinwilligungTelefonkontaktVorhanden>\r\n          <KassenIk>108891982</KassenIk>\r\n          <KopfDatum>2017-10-26</KopfDatum>\r\n          <TeilnahmeerklaerungIdentifikator>17108891982000120192357*********20120701131022</TeilnahmeerklaerungIdentifikator>\r\n          <Versichertennummer>120192357</Versichertennummer>\r\n          <Lanr>*********</Lanr>\r\n        </TeilnahmeerklaerungPatient>\r\n        <TeilnahmeerklaerungPatient>\r\n            <Geburtsdatum>1983-01-01</Geburtsdatum>\r\n            <Nachname>Schmidt</Nachname>\r\n            <PatientenId>P200_000</PatientenId>\r\n            <Vorname>Manfred</Vorname>\r\n            <IstArztwechsel>false</IstArztwechsel>\r\n            <IstEinwilligungTelefonkontaktVorhanden>false</IstEinwilligungTelefonkontaktVorhanden>\r\n            <KassenIk>108891982</KassenIk>\r\n            <KopfDatum>2017-10-26</KopfDatum>\r\n            <TeilnahmeerklaerungIdentifikator>17108891982000******************20120701131022</TeilnahmeerklaerungIdentifikator>\r\n            <Versichertennummer>*********</Versichertennummer>\r\n            <Lanr>*********</Lanr>\r\n        </TeilnahmeerklaerungPatient>\r\n      </Teilnahmeerklaerungen>\r\n        <Testuebermittlung>true</Testuebermittlung>\r\n        <ArztInformationsSystem>\r\n            <KbvPruefnummer>1234567</KbvPruefnummer>\r\n            <Version>1.0</Version>\r\n            <Vorname>Willi</Vorname>\r\n            <Nachname>Wichtig</Nachname>\r\n            <Name>Test-AIS</Name>\r\n            <Organisation>Test-Company</Organisation>\r\n            <Strasse>Eisenstr.</Strasse>\r\n            <Plz>50825</Plz>\r\n            <Stadt>Köln</Stadt>\r\n            <Telefon>0221 / 12345</Telefon>\r\n            <SystemOid>{{SYSTEM_OID}}</SystemOid>\r\n        </ArztInformationsSystem>\r\n        <Vertragspartneridentifikator>H2JHACAAT7</Vertragspartneridentifikator>\r\n        <AbsenderBsnr>*********</AbsenderBsnr>\r\n        <VertragsIdentifikator>BKK_VAG_BW</VertragsIdentifikator>\r\n      </teilnahmeerklaerungContainer>\r\n    </SendeTeilnahmeerklaerung>\r\n  </soap:Body>\r\n</soap:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "http://{{HPM_HOST}}:{{HPM_PORT}}/Service.asmx", "protocol": "http", "host": ["{{HPM_HOST}}"], "port": "{{HPM_PORT}}", "path": ["Service.asmx"]}}, "response": [], "uid": "14558926-94555b5c-8f6b-43c0-a0dc-7e6418a90a66"}, {"name": "Abrechnung_AWH01_SOAP", "event": [{"listen": "test", "script": {"id": "fbba6431-50f5-4c23-85dd-a0fb4e6455e8", "exec": ["var jsonObject = xml2Json(responseBody);\r", "var result = jsonObject[\"s:Envelope\"][\"s:Body\"][\"StarteAbrechnungResponse\"][\"StarteAbrechnungResult\"];\r", "eval(pm.environment.get(\"baseTest\"))(result, true, true, true);"], "type": "text/javascript"}}], "id": "2c71ae30-c9cb-457b-87ea-e3021a1979e2", "protocolProfileBehavior": {"disableBodyPruning": true, "disabledSystemHeaders": {"content-type": true}}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8", "type": "text"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\r\n    <soap:Body>\r\n        <StarteAbrechnung xmlns=\"http://haevg-rz.de/hpm\">\r\n            <container>\r\n                <Vertragskontext>\r\n                    <VertragsIdentifikator>AWH_01</VertragsIdentifikator>\r\n                    <HonoraranlageIdentifikator>AWH_01</HonoraranlageIdentifikator>\r\n                    <AbrechnungsJahr>{{ABRECHNUNGSJAHR}}</AbrechnungsJahr>\r\n                    <AbrechnungsQuartal>1</AbrechnungsQuartal>\r\n                </Vertragskontext>\r\n                <ArztInformationsSystem >\r\n                    <KbvPruefnummer>8018110</KbvPruefnummer>\r\n                    <Version>Q3-2010.0</Version>\r\n                    <Vorname>Max</Vorname>\r\n                    <Nachname>Mustermann</Nachname>\r\n                    <Name>TestAIS</Name>\r\n                    <Organisation>TestAIS GmbCo KG</Organisation>\r\n                    <Strasse>Hauptstraße 1</Strasse>\r\n                    <Plz>12345</Plz>\r\n                    <Stadt>Haupthausen</Stadt>\r\n                    <Telefon>1111/2222222</Telefon>\r\n                    <Telefax>1111/3333333</Telefax>\r\n                    <TelefonMobil>1111/4444444</TelefonMobil>\r\n                    <SystemOid>{{SYSTEM_OID}}</SystemOid>\r\n                </ArztInformationsSystem>\r\n                <Vertragspartneridentifikator>H2JHACAAT7</Vertragspartneridentifikator>\r\n                <AbsenderBsnr>*********</AbsenderBsnr>\r\n                <Dokumentationen>\r\n                    <AbrechnungsDokumentation>\r\n                        <Patient>\r\n                            <AktuelleVertragsteilnahmen>\r\n                                <Vertragsteilnahme>\r\n                                    <VertragsIdentifikator>AWH_01</VertragsIdentifikator>\r\n                                    <IstVertreterteilnahme>false</IstVertreterteilnahme>\r\n                                </Vertragsteilnahme>\r\n                            </AktuelleVertragsteilnahmen>\r\n                            <PatientenId>1</PatientenId>\r\n                            <Nachname>Meier</Nachname>\r\n                            <Vorname>Günther</Vorname>\r\n                            <Geburtsdatum>1970-12-31</Geburtsdatum>\r\n                            <Geschlecht>M</Geschlecht>\r\n                            <Versicherungsnachweis>\r\n                                <KrankenkassenIk>107018425</KrankenkassenIk>\r\n                                <VersichertenNummer>841104883</VersichertenNummer>\r\n                                <VersichertenArt>3</VersichertenArt>\r\n                            </Versicherungsnachweis>\r\n                        </Patient>\r\n                        <Diagnosen>\r\n                            <Diagnose>\r\n                                <CodeSystemName>Icd10gm{{ABRECHNUNGSJAHR}}</CodeSystemName>\r\n                                <DokumentationsDatum>{{ABRECHNUNGSJAHR}}-01-02</DokumentationsDatum>\r\n                                <DiagnoseCode>B00.0</DiagnoseCode>\r\n                                <Sicherheit>G</Sicherheit>\r\n                                <Seitenlokalisation>U</Seitenlokalisation>\r\n                                <IstDauerDiagnose>false</IstDauerDiagnose>\r\n                                <DiagnoseId>1</DiagnoseId>\r\n                            </Diagnose>\r\n                        </Diagnosen>\r\n                        <Leistungen>\r\n                            <Leistung>\r\n                                <Leistungsdatum>{{ABRECHNUNGSJAHR}}-01-02</Leistungsdatum>\r\n                                <Leistungsziffer>0000</Leistungsziffer>\r\n                                <LeistungsId>1</LeistungsId>\r\n                            </Leistung>\r\n                        </Leistungen>\r\n                        <Praxisgebuehren />\r\n                        <Ueberweisungen />\r\n                        <UnfallkennzeichenGesetzt>false</UnfallkennzeichenGesetzt>\r\n                    </AbrechnungsDokumentation>\r\n                </Dokumentationen>\r\n                <Testuebermittlung>true</Testuebermittlung>\r\n                <NurPrueflauf>false</NurPrueflauf>\r\n                <Uebertragungsart>Online</Uebertragungsart>\r\n            </container>\r\n        </StarteAbrechnung>\r\n    </soap:Body>\r\n</soap:Envelope>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "http://{{HPM_HOST}}:{{HPM_PORT}}/Service.asmx", "protocol": "http", "host": ["{{HPM_HOST}}"], "port": "{{HPM_PORT}}", "path": ["Service.asmx"]}}, "response": [], "uid": "14558926-2c71ae30-c9cb-457b-87ea-e3021a1979e2"}, {"name": "Vertragsteilnahmen", "event": [{"listen": "test", "script": {"id": "195279ce-497f-44ba-9286-37aa0602732e", "exec": ["var result = pm.response.json();\r", "eval(pm.environment.get(\"baseTest\"))(result, true, false, true);\r", "\r", "pm.test(\"Vertragsteilnahmen ist nicht leer\", function () {\r", "    pm.expect(result.vertragsteilnahmen.length).to.greaterThan(0);\r", "});"], "type": "text/javascript"}}], "id": "e7b26055-6b30-45dd-a5cd-4afd0e123188", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "X-AbsenderBsnr", "value": "123456789", "type": "text"}, {"key": "X-Systemoid", "value": "fe336fec-504f-4e47-a4f5-36f7827dff80", "type": "text"}], "url": {"raw": "http://{{HPM_HOST}}:{{HPM_PORT}}/api/latest/hzv/vertragspartner/H3AAAX7/vertragsteilnahmen", "protocol": "http", "host": ["{{HPM_HOST}}"], "port": "{{HPM_PORT}}", "path": ["api", "latest", "hzv", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "H3AAAX7", "vertragsteilnahmen"]}}, "response": [], "uid": "14558926-e7b26055-6b30-45dd-a5cd-4afd0e123188"}], "id": "1b087800-06cb-4e15-9406-12ce7316f876", "uid": "14558926-1b087800-06cb-4e15-9406-12ce7316f876"}, {"name": "AMM", "item": [{"name": "LiefereArzneimittelliste AOK_HE_HZV", "event": [{"listen": "test", "script": {"id": "35d5c891-477b-4c0c-8390-641a68a8b9ac", "exec": [""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "71ff26fc-62f0-4bdf-979c-e16906e8c03a", "exec": [""], "type": "text/javascript"}}], "id": "bad78377-a22f-40f5-b89b-c5cb9a14461e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"VertragsIdentifikator\": \"AOK_HE_HZV\",\r\n    \"IK\": \"105313145\",\r\n    \"ReferenzDatum\": \"{{currentdate}}\",\r\n    \"ArztInformationsSystem\": {\r\n        \"kbvPruefnummer\": \"8018110\",\r\n        \"version\": \"1.0.0\",\r\n        \"vorname\": \"<PERSON>\",\r\n        \"nachname\": \"<PERSON><PERSON><PERSON>\",\r\n        \"name\": \"TestAIS\",\r\n        \"organisation\": \"TestAIS GmbH\",\r\n        \"strasse\": \"Hauptstraße 1\",\r\n        \"plz\": \"12345\",\r\n        \"stadt\": \"Haupthausen\",\r\n        \"telefon\": \"1111/2222222\",\r\n        \"telefax\": \"1111/3333333\",\r\n        \"telefonMobil\": \"1111/4444444\",\r\n        \"systemOid\": \"{{SYSTEM_OID}}\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{HPM_HOST}}:{{HPM_PORT}}/api/latest/amm/arzneimittel/liste", "protocol": "http", "host": ["{{HPM_HOST}}"], "port": "{{HPM_PORT}}", "path": ["api", "latest", "amm", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "liste"]}}, "response": [], "uid": "14558926-bad78377-a22f-40f5-b89b-c5cb9a14461e"}, {"name": "LiefereArzneimittelinformationen AWH_01", "event": [{"listen": "test", "script": {"id": "c6d784d6-00fa-4b84-94f1-a7075b563b9c", "exec": [""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "376c1fb3-c688-4731-8614-df35f3a25be7", "exec": [""], "type": "text/javascript"}}], "id": "952a59b0-9c85-49ec-bee8-c58087d25db8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"VertragsIdentifikator\": \"AWH_01\",\r\n    \"IK\": \"108018007\",\r\n    \"ReferenzDatum\": \"{{currentdate}}\",\r\n    \"ArztInformationsSystem\": {\r\n        \"kbvPruefnummer\": \"8018110\",\r\n        \"version\": \"1.0.0\",\r\n        \"vorname\": \"<PERSON>\",\r\n        \"nachname\": \"<PERSON><PERSON><PERSON>\",\r\n        \"name\": \"TestAIS\",\r\n        \"organisation\": \"TestAIS GmbH\",\r\n        \"strasse\": \"Hauptstraße 1\",\r\n        \"plz\": \"12345\",\r\n        \"stadt\": \"Haupthausen\",\r\n        \"telefon\": \"1111/2222222\",\r\n        \"telefax\": \"1111/3333333\",\r\n        \"telefonMobil\": \"1111/4444444\",\r\n        \"systemOid\": \"{{SYSTEM_OID}}\"\r\n    },\r\n    \"Pzns\": [\r\n        \"00055981\",\r\n        \"05108216\",\r\n        \"01035615\"\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{HPM_HOST}}:{{HPM_PORT}}/api/latest/amm/arzneimittel/informationen", "protocol": "http", "host": ["{{HPM_HOST}}"], "port": "{{HPM_PORT}}", "path": ["api", "latest", "amm", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "informationen"]}}, "response": [], "uid": "14558926-952a59b0-9c85-49ec-bee8-c58087d25db8"}, {"name": "StarteVerordnungsdatenuebermittelung AOK_PLUS_HZV", "event": [{"listen": "test", "script": {"id": "*************-4462-a445-65bd4376576f", "exec": [""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"id": "617d75a4-1361-4713-8e18-151640e4e324", "exec": [""], "type": "text/javascript"}}], "id": "7b4201f0-fe17-421a-be9f-0748868d2f6c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"vertragskontext\": {\r\n    \"vertragsIdentifikator\": \"AOK_PLUS_HZV\",\r\n    \"honoraranlageIdentifikator\": \"AOK_PLUS_HZV\",\r\n    \"abrechnungsJahr\": 2022,\r\n    \"abrechnungsQuartal\": 1\r\n  },\r\n  \"dokumentationen\": [\r\n    {\r\n      \"patient\": {\r\n        \"aktuelleVertragsteilnahmen\": [\r\n          {\r\n            \"vertragsIdentifikator\": \"AOK_PLUS_HZV\",\r\n            \"istVertreterteilnahme\": false\r\n          }\r\n        ],\r\n        \"patientenId\": \"1\",\r\n        \"nachname\": \"<PERSON><PERSON>\",\r\n        \"vorname\": \"<PERSON><PERSON><PERSON><PERSON>\",\r\n        \"geburtsdatum\": \"1970-12-31\",\r\n        \"geschlecht\": \"M\",\r\n        \"versicherungsnachweis\": {\r\n          \"krankenkassenIk\": \"108018007\",\r\n          \"versichertenNummer\": \"A115484497\",\r\n          \"versichertenArt\": \"1\",\r\n          \"besonderePersonengruppe\": null,\r\n          \"dmpKennzeichnung\": null\r\n        }\r\n      },\r\n      \"verordnungen\": {\r\n        \"rezepte\": [\r\n          {\r\n            \"arbeitsunfallDatum\": null,\r\n            \"arbeitsunfallOrt\": null,\r\n            \"arzneimittel\": [\r\n              {\r\n                \"arzneimittelId\": \"1\",\r\n                \"anzahl\": \"1\",\r\n                \"autIdem\": false,\r\n                \"pzn\": \"10794231\",\r\n                \"atc\": \"M01AE14\",\r\n                \"metaData\": \"Rot\"\r\n              },\r\n              {\r\n                \"arzneimittelId\": \"2\",\r\n                \"anzahl\": \"2\",\r\n                \"autIdem\": false,\r\n                \"pzn\": \"03525370\",\r\n                \"atc\": \"M01AB05\",\r\n                \"metaData\": \"Gruen\"\r\n              }\r\n            ],\r\n            \"bvg\": false,\r\n            \"gebuehrenpflichtig\": false,\r\n            \"kvDetails\": null,\r\n            \"notfall\": false,\r\n            \"rezeptId\": \"1\",\r\n            \"sonstigerKt\": false,\r\n            \"sprechstundenbedarf\": false,\r\n            \"stellvertreter\": null,\r\n            \"unfall\": false,\r\n            \"verordnungsZeitpunkte\": {\r\n              \"erstellt\": \"2022-01-15T12:23:01\",\r\n              \"geaendert\": \"2022-01-15T12:23:01\",\r\n              \"gedruckt\": null,\r\n              \"geloescht\": null\r\n            }\r\n          },\r\n          {\r\n            \"arbeitsunfallDatum\": null,\r\n            \"arbeitsunfallOrt\": null,\r\n            \"arzneimittel\": [\r\n              {\r\n                \"arzneimittelId\": \"3\",\r\n                \"anzahl\": \"1\",\r\n                \"autIdem\": true,\r\n                \"name\": \"Salbe\"\r\n              }\r\n            ],\r\n            \"bvg\": false,\r\n            \"gebuehrenpflichtig\": false,\r\n            \"kvDetails\": null,\r\n            \"notfall\": false,\r\n            \"rezeptId\": \"2\",\r\n            \"sonstigerKt\": false,\r\n            \"sprechstundenbedarf\": false,\r\n            \"stellvertreter\": null,\r\n            \"unfall\": false,\r\n            \"verordnungsZeitpunkte\": {\r\n              \"erstellt\": \"2022-01-15T12:23:01\",\r\n              \"geaendert\": \"2022-01-15T12:23:01\",\r\n              \"gedruckt\": null,\r\n              \"geloescht\": null\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      \"istValide\": false\r\n    }\r\n  ],\r\n  \"testuebermittlung\": true,\r\n  \"nurPrueflauf\": false,\r\n  \"AbsenderBsnr\": \"*********\",\r\n  \"Vertragspartneridentifikator\":\"H2AAAXR\",\r\n  \"arztInformationsSystem\": {\r\n    \"kbvPruefnummer\": \"8018110\",\r\n    \"version\": \"1.0.0\",\r\n    \"vorname\": \"Max\",\r\n    \"nachname\": \"Mustermann\",\r\n    \"name\": \"TestAIS\",\r\n    \"organisation\": \"TestAIS GmbH\",\r\n    \"strasse\": \"Hauptstraße 1\",\r\n    \"plz\": \"12345\",\r\n    \"stadt\": \"Haupthausen\",\r\n    \"telefon\": \"1111/2222222\",\r\n    \"telefax\": \"1111/3333333\",\r\n    \"telefonMobil\": \"1111/4444444\",\r\n    \"systemOid\": \"{{SYSTEM_OID}}\"\r\n  }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{HPM_HOST}}:{{HPM_PORT}}/api/latest/amm/verordnungen", "protocol": "http", "host": ["{{HPM_HOST}}"], "port": "{{HPM_PORT}}", "path": ["api", "latest", "amm", "verord<PERSON>ngen"]}}, "response": [], "uid": "14558926-7b4201f0-fe17-421a-be9f-0748868d2f6c"}], "id": "3d6b11ef-3f1c-46dc-b558-d1e1b38aa642", "uid": "14558926-3d6b11ef-3f1c-46dc-b558-d1e1b38aa642"}, {"name": "EAV", "item": [{"name": "PruefeVersichertenteilnahmeVernetzung", "event": [{"listen": "test", "script": {"id": "515f9c06-9952-4ace-b773-22c1a7c63ba4", "exec": [""], "type": "text/javascript"}}], "id": "bca29051-47cc-4904-9530-fd25d054ff63", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"AbsenderBsnr\" : \"*********\",\r\n    \"Lanr\" : \"*********\",\r\n    \"VertragsIdentifikator\": \"AWH_01\",\r\n    \"Versichertennummer\": \"H768317540\",\r\n    \"Testuebermittlung\": true,\r\n    \"ArztInformationsSystem\": {\r\n        \"KbvPruefnummer\": \"1234567\",\r\n        \"Version\": \"1.0\",\r\n        \"Vorname\": \"Willi\",\r\n        \"Nachname\": \"Wichtig\",\r\n        \"Name\": \"Test-AIS\",\r\n        \"Organisation\": \"Test-Company\",\r\n        \"Strasse\": \"Eisenstr.\",\r\n        \"Plz\": \"50825\",\r\n        \"Stadt\": \"Köln\",\r\n        \"Telefon\": \"0221 / 12345\",\r\n        \"Telefax\": null,\r\n        \"TelefonMobil\": null,\r\n        \"SystemOid\": \"{{SYSTEM_OID}}\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{HPM_HOST}}:{{HPM_PORT}}/api/latest/eav/versichertenteilnahme", "protocol": "http", "host": ["{{HPM_HOST}}"], "port": "{{HPM_PORT}}", "path": ["api", "latest", "eav", "versichertenteilnahme"]}}, "response": [], "uid": "14558926-bca29051-47cc-4904-9530-fd25d054ff63"}, {"name": "ErzeugeZertifikatArzt", "id": "6add2afb-6940-48a0-bf75-70f1ca9fceb0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{    \r\n    \"AbsenderBsnr\" : \"*********\",\r\n    \"Lanr\" : \"*********\",\r\n    \"ArztIdentifikation\": {\r\n        \"ArztIdentifikationscode\": \"0000 0000 0000 0000\"\r\n    },\r\n    \"Kennwort\": \"test\",\r\n    \"Testuebermittlung\": true,\r\n    \"ArztInformationsSystem\": {\r\n        \"KbvPruefnummer\": \"1234567\",\r\n        \"Version\": \"1.0\",\r\n        \"Vorname\": \"Willi\",\r\n        \"Nachname\": \"Wichtig\",\r\n        \"Name\": \"Test-AIS\",\r\n        \"Organisation\": \"Test-Company\",\r\n        \"Strasse\": \"Eisenstr.\",\r\n        \"Plz\": \"50825\",\r\n        \"Stadt\": \"Köln\",\r\n        \"Telefon\": \"0221 / 12345\",\r\n        \"Telefax\": null,\r\n        \"TelefonMobil\": null,\r\n        \"SystemOid\": \"{{SYSTEM_OID}}\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://{{HPM_HOST}}:{{HPM_PORT}}/api/latest/eav/zertifikate/erzeugung/arzt", "protocol": "http", "host": ["{{HPM_HOST}}"], "port": "{{HPM_PORT}}", "path": ["api", "latest", "eav", "zertifikate", "erzeugung", "arzt"]}}, "response": [], "uid": "14558926-6add2afb-6940-48a0-bf75-70f1ca9fceb0"}], "id": "8db567fe-24fd-434f-aae0-5d846b6032cf", "uid": "14558926-8db567fe-24fd-434f-aae0-5d846b6032cf"}, {"name": "Shell", "item": [{"name": "LiefereHpmInformation", "event": [{"listen": "test", "script": {"id": "d7d5b85f-f9e6-4be5-9c14-934071860e8b", "exec": [""], "type": "text/javascript", "packages": {}}}], "id": "c05965cd-e75f-4c75-b011-51113856fe4c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "http://{{HPM_HOST}}:{{HPM_PORT}}/api/hpminformation", "protocol": "http", "host": ["{{HPM_HOST}}"], "port": "{{HPM_PORT}}", "path": ["api", "hpminformation"]}}, "response": [{"id": "d63914b0-c4c4-467c-851a-4ca29e824f34", "name": "LiefereHpmInformation response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap12:Envelope xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">\n  <soap12:Body>\n    <LiefereHpmInformation xmlns=\"http://haevg-rz.de/hpm\"></LiefereHpmInformation>\n  </soap12:Body>\n</soap12:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "http://{{HPM_HOST}}:{{HPM_PORT}}/api/hpminformation", "protocol": "http", "host": ["{{HPM_HOST}}"], "port": "{{HPM_PORT}}", "path": ["api", "hpminformation"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "xml", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}], "cookie": [], "responseTime": null, "body": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap12:Envelope xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">\n  <soap12:Body>\n    <LiefereHpmInformationResponse xmlns=\"http://haevg-rz.de/hpm\">\n      <LiefereHpmInformationResult>\n        <Status>Unbekannt</Status>\n        <UebermittlungsStatus>KeineAngabe</UebermittlungsStatus>\n        <Version>place your string value here</Version>\n        <IstTestversion>true</IstTestversion>\n        <IstTestsystem>true</IstTestsystem>\n        <HpmDateTimeNow>0222-12-31T04:21:34+02:01</HpmDateTimeNow>\n      </LiefereHpmInformationResult>\n    </LiefereHpmInformationResponse>\n  </soap12:Body>\n</soap12:Envelope>\n", "uid": "14558926-d63914b0-c4c4-467c-851a-4ca29e824f34"}, {"id": "b0a8d1ac-c82c-4655-9dad-88cd5bcdde22", "name": "LiefereHpmInformation example", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap12:Envelope xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">\n  <soap12:Body>\n    <LiefereHpmInformation xmlns=\"http://haevg-rz.de/hpm\"></LiefereHpmInformation>\n  </soap12:Body>\n</soap12:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "http://{{HPM_HOST}}:{{HPM_PORT}}/api/hpminformation", "protocol": "http", "host": ["{{HPM_HOST}}"], "port": "{{HPM_PORT}}", "path": ["api", "hpminformation"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "xml", "header": [{"key": "Date", "value": "Fri, 05 Nov 2021 13:00:34 GMT"}, {"key": "Server", "value": "HÄVG-Prüfmodul/2021.4.7954.18856"}, {"key": "X-AspNet-Version", "value": "4.0.30319"}, {"key": "Cache-Control", "value": "private, max-age=0"}, {"key": "Content-Type", "value": "application/soap+xml; charset=utf-8"}, {"key": "Content-Length", "value": "1791"}, {"key": "Keep-Alive", "value": "timeout=15, max=100"}, {"key": "Connection", "value": "Keep-Alive"}], "cookie": [], "responseTime": null, "body": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\n    <soap:Body>\n        <LiefereHpmInformationResponse xmlns=\"http://haevg-rz.de/hpm\">\n            <LiefereHpmInformationResult>\n                <Status>OK</Status>\n                <UebermittlungsStatus>KeineAngabe</UebermittlungsStatus>\n                <Version>Q4-2021.0134</Version>\n                <IstTestversion>false</IstTestversion>\n                <IstTestsystem>true</IstTestsystem>\n                <Uebertragungsweg>Online</Uebertragungsweg>\n                <VertragsIdentifikatoren>\n                    <string>AOK_FA_GASTRO_BW</string>\n                    <string>AOK_FA_KARDIO_BW</string>\n                    <string>AOK_FA_NEPHRO_BW</string>\n                    <string>AOK_FA_NPPP_BW</string>\n                    <string>AOK_FA_OC_BW</string>\n                    <string>AOK_FA_PNEUMO_BW</string>\n                    <string>AOK_FA_TEDE_BW</string>\n                    <string>AOK_FA_URO_BW</string>\n                    <string>AWH_01</string>\n                    <string>BKK_BOSCH_BW</string>\n                    <string>BKK_BOSCH_FA_BW</string>\n                    <string>BKK_BW_HZV</string>\n                    <string>BKK_FA_GASTRO_BW</string>\n                    <string>BKK_FA_KARDIO_BW</string>\n                    <string>BKK_FA_OC_BW</string>\n                    <string>BKK_FA_PNEUMO_BW</string>\n                    <string>BKK_FA_URO_BW</string>\n                    <string>BKK_VAG_BW</string>\n                    <string>EK_BW_HZV</string>\n                    <string>EK_FA_DIA_BW</string>\n                    <string>IKK_CL_BW_HZV</string>\n                    <string>LKK_BW_HZV</string>\n                    <string>MEDI_FA_PT_BW</string>\n                    <string>RV_KBS_BW_HZV</string>\n                    <string>VAG_FA_GASTRO_BW</string>\n                    <string>VAG_FA_KARDIO_BW</string>\n                </VertragsIdentifikatoren>\n                <HzvOnlineKeyInformationen>\n                    <HzvOnlineKeyInformation>\n                        <Bsnr>000000000xx</Bsnr>\n                        <GueltigVon>2020-11-27</GueltigVon>\n                        <GueltigBis>2023-11-27</GueltigBis>\n                        <SerienNummer>3173146</SerienNummer>\n                        <GeraeteId>HZV001337</GeraeteId>\n                    </HzvOnlineKeyInformation>\n                </HzvOnlineKeyInformationen>\n                <HpmDateTimeNow>2021-11-05T14:00:34.0761723+01:00</HpmDateTimeNow>\n            </LiefereHpmInformationResult>\n        </LiefereHpmInformationResponse>\n    </soap:Body>\n</soap:Envelope>", "uid": "14558926-b0a8d1ac-c82c-4655-9dad-88cd5bcdde22"}], "uid": "14558926-c05965cd-e75f-4c75-b011-51113856fe4c"}, {"name": "PruefeKonnektivitaet", "event": [{"listen": "test", "script": {"id": "f4421eaf-547c-40f0-a60b-8f82f6ec209b", "exec": [""], "type": "text/javascript"}}], "id": "248d4828-3fb1-46a3-ad7b-e8ae4b0fb266", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}], "body": {"mode": "raw", "raw": "{\n    \"Testuebermittlung\":true\n}", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "http://{{HPM_HOST}}:{{HPM_PORT}}/api/pruefekonnektivitaet", "protocol": "http", "host": ["{{HPM_HOST}}"], "port": "{{HPM_PORT}}", "path": ["api", "pruefekonnektivitaet"]}}, "response": [{"id": "108d1b9b-e867-4c61-baa2-a8045b1a6e3b", "name": "PruefeKonnektivitaet response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap12:Envelope xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">\n  <soap12:Body>\n    <PruefeKonnektivitaet xmlns=\"http://haevg-rz.de/hpm\">\n      <container>\n        <Testuebermittlung>true</Testuebermittlung>\n      </container>\n    </PruefeKonnektivitaet>\n  </soap12:Body>\n</soap12:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "http://localhost:22220/Service/", "protocol": "http", "host": ["localhost"], "port": "22220", "path": ["Service", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "xml", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}], "cookie": [], "responseTime": null, "body": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap12:Envelope xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">\n  <soap12:Body>\n    <PruefeKonnektivitaetResponse xmlns=\"http://haevg-rz.de/hpm\">\n      <PruefeKonnektivitaetResult>\n        <Status>Unbekannt</Status>\n        <UebermittlungsStatus>KeineAngabe</UebermittlungsStatus>\n      </PruefeKonnektivitaetResult>\n    </PruefeKonnektivitaetResponse>\n  </soap12:Body>\n</soap12:Envelope>\n", "uid": "14558926-108d1b9b-e867-4c61-baa2-a8045b1a6e3b"}, {"id": "78ece9a1-0ec2-4622-9969-5c4ba673ee87", "name": "PruefeKonnektivitaet example", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "text/xml; charset=utf-8"}], "body": {"mode": "raw", "raw": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap12:Envelope xmlns:soap12=\"http://www.w3.org/2003/05/soap-envelope\">\n  <soap12:Body>\n    <PruefeKonnektivitaet xmlns=\"http://haevg-rz.de/hpm\">\n      <container>\n        <Testuebermittlung>true</Testuebermittlung>\n      </container>\n    </PruefeKonnektivitaet>\n  </soap12:Body>\n</soap12:Envelope>\n", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "http://{{HPM_HOST}}:{{HPM_PORT}}/Service.asmx", "protocol": "http", "host": ["{{HPM_HOST}}"], "port": "{{HPM_PORT}}", "path": ["Service.asmx"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "xml", "header": [{"key": "Date", "value": "Fri, 05 Nov 2021 13:11:41 GMT"}, {"key": "Server", "value": "HÄVG-Prüfmodul/2021.4.7954.18856"}, {"key": "X-AspNet-Version", "value": "4.0.30319"}, {"key": "Cache-Control", "value": "private, max-age=0"}, {"key": "Content-Type", "value": "application/soap+xml; charset=utf-8"}, {"key": "Content-Length", "value": "459"}, {"key": "Keep-Alive", "value": "timeout=15, max=98"}, {"key": "Connection", "value": "Keep-Alive"}], "cookie": [], "responseTime": null, "body": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<soap:Envelope xmlns:soap=\"http://www.w3.org/2003/05/soap-envelope\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\">\n    <soap:Body>\n        <PruefeKonnektivitaetResponse xmlns=\"http://haevg-rz.de/hpm\">\n            <PruefeKonnektivitaetResult>\n                <Status>OK</Status>\n                <UebermittlungsStatus>OK</UebermittlungsStatus>\n            </PruefeKonnektivitaetResult>\n        </PruefeKonnektivitaetResponse>\n    </soap:Body>\n</soap:Envelope>", "uid": "14558926-78ece9a1-0ec2-4622-9969-5c4ba673ee87"}], "uid": "14558926-248d4828-3fb1-46a3-ad7b-e8ae4b0fb266"}, {"name": "hpmhealth", "event": [{"listen": "test", "script": {"id": "ee3d2ce8-1dd4-44bb-8222-c477295936d8", "exec": ["var result = pm.response.json();\r", "pm.test(\"hpm health verfügbar\", function(){\r", "    pm.expect(result.<PERSON><PERSON><PERSON>üg<PERSON>).to.be.true; \r", "})"], "type": "text/javascript"}}], "id": "8519a525-b482-4a7c-8251-ca3509d2aa11", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{HPM_PROTOCOL}}://{{HPM_HOST}}:{{HPM_PORT}}/api/health", "protocol": "{{HPM_PROTOCOL}}", "host": ["{{HPM_HOST}}"], "port": "{{HPM_PORT}}", "path": ["api", "health"]}}, "response": [], "uid": "14558926-8519a525-b482-4a7c-8251-ca3509d2aa11"}], "id": "36948cb3-f38e-405a-b180-065e6cd88c95", "uid": "14558926-36948cb3-f38e-405a-b180-065e6cd88c95"}], "event": [{"listen": "prerequest", "script": {"id": "3ffcc23e-7f6e-4e82-bb98-259a8fa74445", "type": "text/javascript", "packages": {}, "exec": ["var moment = require('moment');", "pm.environment.set('currentdate', moment().format((\"YYYY-MM-DD\")));", "", "/* PVS authentication */", "var cryptoJS = require(\"crypto-js\");", "var uuid = require('uuid');", "", "var oid = pm.variables.get(\"SYSTEM_OID\")", "", "pm.request.headers.add({", "    key: \"X-Systemoid\",", "    value: oid", "});", "", "var randHeaderValue = uuid.v4();", "pm.request.headers.add({", "    key: \"X-Rand\",", "    value: randHeaderValue", "});", "", "var datetimeHeaderValue = moment().format();", "pm.request.headers.add({", "    key: \"X-Datetime\",", "    value: datetimeHeaderValue", "});", "", "var secret  = CryptoJS.enc.Base64.parse(pm.variables.get(\"SECRET\")).toString(CryptoJS.enc.Utf8);", "var aggregated = secret + oid + randHeaderValue + datetimeHeaderValue;", "var dataBytes = CryptoJS.enc.Utf8.parse(aggregated);", "var authData = cryptoJS.SHA512(dataBytes);", "var authDataB64 = CryptoJS.enc.Base64.stringify(authData);", "var userPwB64 = btoa(\":\" + authDataB64);", "", "pm.request.headers.add({", "    key: \"Authorization\",", "    value: \"Basic \" + userPwB64", "});", "/* end PVS authentication */"]}}, {"listen": "test", "script": {"id": "aca1b532-9d53-43a3-b02e-e84f133a4067", "type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "HPM_PROTOCOL", "value": "", "type": "string"}, {"key": "HPM_HOST", "value": "localhost"}, {"key": "HPM_PORT", "value": "22220"}, {"key": "SYSTEM_OID", "value": "", "type": "string"}, {"key": "SECRET", "value": "", "type": "string"}, {"key": "ABRECHNUNGSJAHR", "value": "", "type": "string"}]}}