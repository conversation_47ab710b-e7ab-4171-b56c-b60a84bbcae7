{"openapi": "3.0.4", "info": {"title": "HPM 2025 Q3", "version": "2025 Q3"}, "paths": {"/api/arbeitsunfaehigkeitsbescheinigungen/validierung": {"post": {"tags": ["Arbeitsunfaehigkeiten"], "summary": "Validierung einer eAU auf Basis eines FHIR-Dokuments.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidiereAuContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ValidiereAuContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ValidiereAuContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ValidiereAuResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ValidiereAuResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ValidiereAuResultat"}}}}}}}, "/api/arztbriefe/abruf/verfuegbarkeit": {"post": {"tags": ["Arztbriefe"], "summary": "Prüfen auf Verfügbarkeit eines gerichteten Arztbriefes.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PruefeAufBereitgestellteArztbriefeContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PruefeAufBereitgestellteArztbriefeContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PruefeAufBereitgestellteArztbriefeContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PruefeAufBereitgestellteArztbriefeResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PruefeAufBereitgestellteArztbriefeResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PruefeAufBereitgestellteArztbriefeResultat"}}}}}}}, "/api/arztbriefe/versand": {"post": {"tags": ["Arztbriefe"], "summary": "Versand eines gerichteten oder adressierten Arztbriefes.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendeArztbriefContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendeArztbriefContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SendeArztbriefContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SendeArztbriefResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SendeArztbriefResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendeArztbriefResultat"}}}}}}}, "/api/arztbriefe/abruf": {"post": {"tags": ["Arztbriefe"], "summary": "<PERSON><PERSON><PERSON><PERSON> von adressierten oder bei Angabe einer Versichertennummer von gerichteten Arztbriefen.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LiefereArztbriefeContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereArztbriefeContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LiefereArztbriefeContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LiefereArztbriefeResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LiefereArztbriefeResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereArztbriefeResultat"}}}}}}}, "/api/dokumente/empfangsstatus": {"post": {"tags": ["Dokumente"], "summary": "<PERSON><PERSON><PERSON><PERSON> <PERSON> von Doku<PERSON>en.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LiefereDokumentenEmpfangsstatusContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereDokumentenEmpfangsstatusContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LiefereDokumentenEmpfangsstatusContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LiefereDokumentenEmpfangsstatusResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LiefereDokumentenEmpfangsstatusResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereDokumentenEmpfangsstatusResultat"}}}}}}}, "/api/dokumente/abrufbestaetigung": {"post": {"tags": ["Dokumente"], "summary": "<PERSON><PERSON> von Abrufbestätigungen für Dokumente.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendeDokumentenAbrufbestaetigungContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendeDokumentenAbrufbestaetigungContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SendeDokumentenAbrufbestaetigungContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SendeDokumentenAbrufbestaetigungResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SendeDokumentenAbrufbestaetigungResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendeDokumentenAbrufbestaetigungResultat"}}}}}}}, "/api/einweisungsbriefe/validierung": {"post": {"tags": ["Einweisungsbriefe"], "summary": "Validierung eines Einweisungsbriefes auf Basis eines CDA-Dokuments.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidiereEinweisungsbriefContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ValidiereEinweisungsbriefContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ValidiereEinweisungsbriefContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ValidiereEinweisungsbriefResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ValidiereEinweisungsbriefResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ValidiereEinweisungsbriefResultat"}}}}}}}, "/api/empfaenger/suche": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON> <PERSON> Empfänger für den adressierten Versand von Dokumenten.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SucheEmpfaengerVernetzungContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SucheEmpfaengerVernetzungContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SucheEmpfaengerVernetzungContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SucheEmpfaengerVernetzungResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SucheEmpfaengerVernetzungResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SucheEmpfaengerVernetzungResultat"}}}}}}}, "/api/empfaenger/gruppen": {"post": {"tags": ["<PERSON><PERSON><PERSON><PERSON>"], "summary": "<PERSON><PERSON> von Empfängergruppen für den gerichteten Versand von Dokumenten.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LiefereEmpfaengergruppenVernetzungContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereEmpfaengergruppenVernetzungContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LiefereEmpfaengergruppenVernetzungContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LiefereEmpfaengergruppenVernetzungResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LiefereEmpfaengergruppenVernetzungResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereEmpfaengergruppenVernetzungResultat"}}}}}}}, "/api/medikationsinformationen/versand": {"post": {"tags": ["Medikationsinformationen"], "summary": "<PERSON><PERSON><PERSON> Medikationsinformationen.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendeMedikationsinformationContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendeMedikationsinformationContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SendeMedikationsinformationContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SendeMedikationsinformationResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SendeMedikationsinformationResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendeMedikationsinformationResultat"}}}}}}}, "/api/medikationsinformationen/abruf": {"post": {"tags": ["Medikationsinformationen"], "summary": "<PERSON><PERSON><PERSON><PERSON> Medikationsinformationen.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LiefereMedikationsinformationContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereMedikationsinformationContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LiefereMedikationsinformationContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LiefereMedikationsinformationResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LiefereMedikationsinformationResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereMedikationsinformationResultat"}}}}}}}, "/api/medikationsinformationen/pflegearzt": {"post": {"tags": ["Medikationsinformationen"], "summary": "Setzen eines Pflegearztes für Medikationsinformationen.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendeMedikationsinformationPflegearztContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendeMedikationsinformationPflegearztContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SendeMedikationsinformationPflegearztContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SendeMedikationsinformationPflegearztResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SendeMedikationsinformationPflegearztResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendeMedikationsinformationPflegearztResultat"}}}}}}}, "/api/medikationsinformationen/ergaenzungen": {"post": {"tags": ["Medikationsinformationen"], "summary": "<PERSON><PERSON><PERSON><PERSON> von Änderungen an Medikationseinträgen von Patienten, für die der Arzt als Pflegearzt gesetzt ist.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LiefereMedikationsinformationErgaenzungenContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereMedikationsinformationErgaenzungenContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LiefereMedikationsinformationErgaenzungenContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LiefereMedikationsinformationErgaenzungenResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LiefereMedikationsinformationErgaenzungenResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereMedikationsinformationErgaenzungenResultat"}}}}}}}, "/api/pdferzeugung/cdadokument": {"post": {"tags": ["PdfErzeugungen"], "summary": "<PERSON>rzeu<PERSON><PERSON> von PDF Dokumenten aus unterstützten CdaDokumenten.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErzeugePdfAusCdaDokumentContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErzeugePdfAusCdaDokumentContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ErzeugePdfAusCdaDokumentContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErzeugePdfAusCdaDokumentResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErzeugePdfAusCdaDokumentResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErzeugePdfAusCdaDokumentResultat"}}}}}}}, "/api/versichertenteilnahme": {"post": {"tags": ["PruefeVersichertenteilnahmeVernetzung"], "summary": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ob ein Versicherter an der eAV teilnimmt.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PruefeVersichertenteilnahmeVernetzungContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PruefeVersichertenteilnahmeVernetzungContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PruefeVersichertenteilnahmeVernetzungContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PruefeVersichertenteilnahmeVernetzungResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PruefeVersichertenteilnahmeVernetzungResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PruefeVersichertenteilnahmeVernetzungResultat"}}}}}}}, "/api/signaturen/erzeugung": {"post": {"tags": ["Signaturen"], "summary": "Erzeugung einer Signatur für ein CdaDokument.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SigniereCdaDokumentContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SigniereCdaDokumentContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SigniereCdaDokumentContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SigniereCdaDokumentResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SigniereCdaDokumentResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SigniereCdaDokumentResultat"}}}}}}}, "/api/signaturen/pruefung": {"post": {"tags": ["Signaturen"], "summary": "Prüfung einer Signatur für ein CdaDokument.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PruefeSignaturCdaDokumentContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PruefeSignaturCdaDokumentContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PruefeSignaturCdaDokumentContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PruefeSignaturCdaDokumentResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PruefeSignaturCdaDokumentResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PruefeSignaturCdaDokumentResultat"}}}}}}}, "/api/telekonsile/versand": {"post": {"tags": ["Telekonsile"], "summary": "Validierung und Versand eines Telekonsils.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendeTelekonsilContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendeTelekonsilContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SendeTelekonsilContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SendeTelekonsilResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SendeTelekonsilResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendeTelekonsilResultat"}}}}}}}, "/api/telekonsile/abruf": {"post": {"tags": ["Telekonsile"], "summary": "<PERSON><PERSON><PERSON><PERSON> von <PERSON>n oder adressierten Telekonsilen.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LiefereTelekonsileContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereTelekonsileContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LiefereTelekonsileContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LiefereTelekonsileResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LiefereTelekonsileResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereTelekonsileResultat"}}}}}}}, "/api/telekonsile/bearbeitungsstatus": {"post": {"tags": ["Telekonsile"], "summary": "<PERSON><PERSON><PERSON><PERSON> <PERSON> von Telekonsil-Vorgängen.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LiefereBearbeitungsstatusTelekonsileContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereBearbeitungsstatusTelekonsileContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LiefereBearbeitungsstatusTelekonsileContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LiefereBearbeitungsstatusTelekonsileResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LiefereBearbeitungsstatusTelekonsileResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereBearbeitungsstatusTelekonsileResultat"}}}}}}}, "/api/telekonsile/metainformationen": {"post": {"tags": ["Telekonsile"], "summary": "<PERSON><PERSON><PERSON><PERSON> <PERSON> Metainformationen für den potentiellen Abruf von Telekonsilen.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LiefereTelekonsilMetainformationenContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereTelekonsilMetainformationenContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LiefereTelekonsilMetainformationenContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LiefereTelekonsilMetainformationenResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LiefereTelekonsilMetainformationenResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereTelekonsilMetainformationenResultat"}}}}}}}, "/api/zertifikate/erzeugung/arzt": {"post": {"tags": ["Zertifikate"], "summary": "Erzeugung eines Arztzertifikats.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErzeugeZertifikatArztContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErzeugeZertifikatArztContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ErzeugeZertifikatArztContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErzeugeZertifikatArztResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErzeugeZertifikatArztResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErzeugeZertifikatArztResultat"}}}}}}}, "/api/zertifikate/erzeugung/betriebsstaette": {"post": {"tags": ["Zertifikate"], "summary": "Erzeugung eines Betriebsstättenzertifikats.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErzeugeZertifikatBetriebsstaetteContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErzeugeZertifikatBetriebsstaetteContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ErzeugeZertifikatBetriebsstaetteContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErzeugeZertifikatBetriebsstaetteResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErzeugeZertifikatBetriebsstaetteResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErzeugeZertifikatBetriebsstaetteResultat"}}}}}}}, "/api/zertifikate/analyse": {"post": {"tags": ["Zertifikate"], "summary": "Auslsen von Metainformationen aus einem Zertifikat.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExtrahiereZertifikatsinformationVernetzungContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExtrahiereZertifikatsinformationVernetzungContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExtrahiereZertifikatsinformationVernetzungContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ExtrahiereZertifikatsinformationVernetzungResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ExtrahiereZertifikatsinformationVernetzungResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExtrahiereZertifikatsinformationVernetzungResultat"}}}}}}}, "/api/zertifikate/informationen": {"post": {"tags": ["Zertifikate"], "summary": "Prüfung auf Sperrstatus von Arzt- und Betriebsstättenzertifikaten eines Arztes.", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LiefereZertifikatinformationVernetzungContainer"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereZertifikatinformationVernetzungContainer"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LiefereZertifikatinformationVernetzungContainer"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LiefereZertifikatinformationVernetzungResultat"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LiefereZertifikatinformationVernetzungResultat"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LiefereZertifikatinformationVernetzungResultat"}}}}}}}}, "components": {"schemas": {"ArztIdentifikationType": {"type": "object", "properties": {"arztIdentifikationscode": {"type": "string", "nullable": true}, "arztIdentifikationZertifikat": {"$ref": "#/components/schemas/ArztIdentifikationZertifikatType"}}, "additionalProperties": false, "description": "Eine eindeutige Identifikation dieses Arztes im Rahmen der Zertifikatserzeugung. Dies kann ein dem Arzt im Rahmen der Teilnahme an dem Verfahren mitgeteilter Identifikationscode sein,\r\nbei einer Erneuerung eines Zertifikats aber auch das noch gültige bisherige Arztzertifikat."}, "ArztIdentifikationZertifikatType": {"required": ["kennwort", "zertifikatArzt"], "type": "object", "properties": {"zertifikatArzt": {"type": "string", "format": "byte"}, "kennwort": {"minLength": 1, "type": "string"}}, "additionalProperties": false, "description": "Eine eindeutige Identifikation dieses Arztes im Rahmen der Zertifikatserzeugung, die auf dem noch aktuell gültigen Arztzertifikat basiert."}, "ArztVernetzung": {"type": "object", "properties": {"lanr": {"type": "string", "nullable": true}, "bezeichnung": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Informationen zu einem Arzt als Empfänger von Informationen innerhalb der Vernetzung."}, "Arztbrief": {"type": "object", "properties": {"dokumentIdentifikator": {"type": "string", "nullable": true}, "dokumentZugriffstoken": {"type": "string", "nullable": true}, "versichertennummer": {"type": "string", "nullable": true}, "absenderBsnr": {"type": "string", "nullable": true}, "CdaDokument": {"type": "string", "nullable": true}, "pdfDokument": {"type": "string", "format": "byte", "nullable": true}}, "additionalProperties": false, "description": "Ein Arztbrief samt Metainformationen."}, "ArztbriefEmpfaenger": {"type": "object", "properties": {"bsnr": {"type": "string", "nullable": true}, "empfaengergruppeIdentifikator": {"type": "integer", "format": "int32"}, "empfaengergruppeIdentifikatorSpecified": {"type": "boolean"}}, "additionalProperties": false, "description": "<PERSON><PERSON> eines zulässigen Empfängers des Arztbriefes (Betriebsstätte oder Empfängergruppe)."}, "BearbeitungsstatusTelekonsil": {"enum": ["Beauftragt", "Befundet", "Rueckfrage", "Beantwortet", "Abgebrochen", "Abgeschlossen"], "type": "string"}, "BearbeitungsstatusTelekonsilVorgang": {"type": "object", "properties": {"bearbeitungsstatus": {"$ref": "#/components/schemas/BearbeitungsstatusTelekonsil"}, "vorgangsIdentifikator": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Der Bearbeitungsstatus eines einzelnen Vorgangs, bestehend aus ID und Status der Telekonsile."}, "BetriebsstaetteVernetzung": {"type": "object", "properties": {"bsnr": {"type": "string", "nullable": true}, "bezeichnung": {"type": "string", "nullable": true}, "adresse": {"type": "string", "nullable": true}, "aerzte": {"type": "array", "items": {"$ref": "#/components/schemas/ArztVernetzung"}, "nullable": true}}, "additionalProperties": false, "description": "Informationen zu einer Betriebsstätte als Empfänger von Informationen innerhalb der Vernetzung."}, "DokumentIdentifikation": {"type": "object", "properties": {"dokumentIdentifikator": {"type": "string", "nullable": true}, "dokumentZugriffstoken": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Die Informationen zu einem einzelnen Dokument bestehend aus Identifier und Sicherheitstoken."}, "DokumentType": {"enum": ["Arztbrief", "Telekonsil"], "type": "string"}, "Empfaengergruppe": {"type": "object", "properties": {"empfaengergruppeIdentifikator": {"type": "integer", "format": "int32"}, "empfaengergruppeBezeichnung": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Eine der im Rahmen der Vernetzung verfügbaren Empfängergruppen zum gerichteten Versand in der eAV."}, "EmpfangsstatusDokument": {"type": "object", "properties": {"empfangsstatusTypDokument": {"$ref": "#/components/schemas/EmpfangsstatusTypeDokument"}, "dokumentIdentifikator": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Der Empfangsstatus eines einzelnen Dokuments."}, "EmpfangsstatusTypeDokument": {"enum": ["Unbekannt", "InZustellung", "Zugestellt", "NichtZugestellt"], "type": "string"}, "ErzeugePdfAusCdaDokumentContainer": {"required": ["absenderBsnr", "arztInformationsSystem"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "CdaDokument": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Der Container mit Angaben zur Erzeugung von PDF Dokumenten aus CdaDokumenten."}, "ErzeugePdfAusCdaDokumentResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "erzeugtesPdf": {"type": "string", "format": "byte", "nullable": true}}, "additionalProperties": false, "description": "Die Ergebnisstruktur des Funktionsaufrufs von ErzeugePdfAusCdaDokument."}, "ErzeugeZertifikatArztContainer": {"required": ["absenderBsnr", "arztIdentifikation", "arztInformationsSystem", "kennwort", "lanr", "testuebermittlung"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "arztIdentifikation": {"$ref": "#/components/schemas/ArztIdentifikationType"}, "kennwort": {"minLength": 1, "type": "string"}, "testuebermittlung": {"type": "boolean"}}, "additionalProperties": false, "description": "Der Container mit Angaben zur Erzeugung eines Arztzertifikats."}, "ErzeugeZertifikatArztResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "zertifikatArzt": {"type": "string", "format": "byte", "nullable": true}, "zertifikatArztInfo": {"$ref": "#/components/schemas/ZertifikatArztInfoType"}}, "additionalProperties": false, "description": "Das Ergebnis des Erzeugens eines Arztzertifikats."}, "ErzeugeZertifikatBetriebsstaetteContainer": {"required": ["absenderBsnr", "arztIdentifikationZertifikat", "arztInformationsSystem", "lanr", "testuebermittlung"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "arztIdentifikationZertifikat": {"$ref": "#/components/schemas/ArztIdentifikationZertifikatType"}, "testuebermittlung": {"type": "boolean"}}, "additionalProperties": false, "description": "Der Container mit Angaben zur Erzeugung eines Betriebsstättenzertifikats."}, "ErzeugeZertifikatBetriebsstaetteResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "zertifikatBetriebsstaette": {"type": "string", "format": "byte", "nullable": true}, "zertifikatBetriebsstaetteInfo": {"$ref": "#/components/schemas/ZertifikatBetriebsstaetteInfoType"}}, "additionalProperties": false, "description": "Das Ergebnis des Erzeugens eines Betriebsstättenzertifikats."}, "ExtrahiereZertifikatsinformationVernetzungContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "zertifikat"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "zertifikat": {"type": "string", "format": "byte"}, "kennwort": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Auslesen eines Zertifikats der eAV."}, "ExtrahiereZertifikatsinformationVernetzungResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "zertifikationsinformation": {"$ref": "#/components/schemas/ZertifikatInfoType"}}, "additionalProperties": false, "description": "Das Ergebnis der Extraktion der Metainformationen des Zertifikats."}, "KontextType": {"enum": ["Arztbrief", "TeleScanDermatologie"], "type": "string"}, "LiefereArztbriefeContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "lanr", "testuebermittlung", "zertifikatBetriebsstaette"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "versichertennummer": {"type": "string", "nullable": true}, "zertifikatBetriebsstaette": {"type": "string", "format": "byte"}, "testuebermittlung": {"type": "boolean"}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Abru<PERSON> von Arztbriefen."}, "LiefereArztbriefeResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "arztbriefe": {"type": "array", "items": {"$ref": "#/components/schemas/Arztbrief"}, "nullable": true}}, "additionalProperties": false, "description": "Das Ergebnis des Abrufs von Arztbriefen."}, "LiefereBearbeitungsstatusTelekonsileContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "lanr", "testuebermittlung", "vorgangsIdentifikatoren"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "vorgangsIdentifikatoren": {"type": "array", "items": {"type": "string"}}, "testuebermittlung": {"type": "boolean"}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Abruf der Bearbeitungsstatus."}, "LiefereBearbeitungsstatusTelekonsileResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "bearbeitungsstatusTelekonsile": {"type": "array", "items": {"$ref": "#/components/schemas/BearbeitungsstatusTelekonsilVorgang"}, "nullable": true}}, "additionalProperties": false, "description": "Die von der Funktion LiefereBearbeitungsstatusTelekonsile gelieferte Ergebnisstruktur."}, "LiefereDokumentenEmpfangsstatusContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "dokumentIdentifikatoren", "dokumentTyp", "lanr", "testuebermittlung"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "dokumentIdentifikatoren": {"type": "array", "items": {"type": "string"}}, "dokumentTyp": {"$ref": "#/components/schemas/DokumentType"}, "testuebermittlung": {"type": "boolean"}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Abru<PERSON> von Dokumentenstatus."}, "LiefereDokumentenEmpfangsstatusResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "empfangsstatusDokumente": {"type": "array", "items": {"$ref": "#/components/schemas/EmpfangsstatusDokument"}, "nullable": true}}, "additionalProperties": false, "description": "Die von der Funktion LiefereDokumentenEmpfangsstatus gelieferte Ergebnisstruktur."}, "LiefereEmpfaengergruppenVernetzungContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "kontext", "lanr", "testuebermittlung"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "testuebermittlung": {"type": "boolean"}, "kontext": {"$ref": "#/components/schemas/KontextType"}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Abruf von Empfängergruppen."}, "LiefereEmpfaengergruppenVernetzungResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "empfaengergruppen": {"type": "array", "items": {"$ref": "#/components/schemas/Empfaengergruppe"}, "nullable": true}}, "additionalProperties": false, "description": "Das Ergebnis des Abrufs der Empfängergruppen der Vernetzung."}, "LiefereMedikationsinformationContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "lanr", "testuebermittlung", "versichertennummer", "zertifikatBetriebsstaette"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "zertifikatBetriebsstaette": {"type": "string", "format": "byte"}, "versichertennummer": {"minLength": 1, "type": "string"}, "testuebermittlung": {"type": "boolean"}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Abru<PERSON> von Medikationsinformationen."}, "LiefereMedikationsinformationErgaenzungenContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "lanr", "testuebermittlung", "zertifikatBetriebsstaette"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "zertifikatBetriebsstaette": {"type": "string", "format": "byte"}, "testuebermittlung": {"type": "boolean"}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Abruf von Medikationsergänzungen."}, "LiefereMedikationsinformationErgaenzungenResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "ergaenzungen": {"type": "array", "items": {"$ref": "#/components/schemas/MedikationsinformationErgaenzung"}, "nullable": true}}, "additionalProperties": false, "description": "Das Ergebnis des Abrufs der Liste von Patienten mit zu kuratierenden Medikationsinformationen."}, "LiefereMedikationsinformationResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "CdaDokument": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Das Ergebnis des Abrufs der aktuellen Medikationsinformation eines Patienten."}, "LiefereTelekonsilMetainformationenContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "lanr", "telekonsilTyp", "testuebermittlung", "zertifikatBetriebsstaette"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "zertifikatBetriebsstaette": {"type": "string", "format": "byte"}, "testuebermittlung": {"type": "boolean"}, "telekonsilTyp": {"$ref": "#/components/schemas/TelekonsilTyp"}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Abruf von Telekonsilmetainformationen."}, "LiefereTelekonsilMetainformationenResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "telekonsileMetainformationen": {"type": "array", "items": {"$ref": "#/components/schemas/TelekonsileMetainformation"}, "nullable": true}}, "additionalProperties": false, "description": "Die von der Funktion LiefereTelekonsilMetainformationen gelieferte Ergebnisstruktur."}, "LiefereTelekonsileContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "dokumentIdentifikatoren", "lanr", "testuebermittlung", "zertifikatBetriebsstaette"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "dokumentIdentifikatoren": {"type": "array", "items": {"type": "string"}}, "testuebermittlung": {"type": "boolean"}, "zertifikatBetriebsstaette": {"type": "string", "format": "byte"}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Abru<PERSON> von Telekonsilen. Die CDA-Inhalte werden als html-enkodierter String übermittelt."}, "LiefereTelekonsileResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "telekonsile": {"type": "array", "items": {"$ref": "#/components/schemas/Telekonsil"}, "nullable": true}}, "additionalProperties": false, "description": "Die von der Funktion LiefereTelekonsile gelieferte Ergebnisstruktur."}, "LiefereZertifikatinformationVernetzungContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "lanr", "testuebermittlung"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "testuebermittlung": {"type": "boolean"}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Abru<PERSON> von Zertifikatsinformationen."}, "LiefereZertifikatinformationVernetzungResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "zertifikationsinformationen": {"type": "array", "items": {"$ref": "#/components/schemas/Zertifikatsinformation"}, "nullable": true}}, "additionalProperties": false, "description": "Die von der Funktion LiefereZertifikatinformationVernetzung gelieferte Ergebnisstruktur."}, "MedikationsinformationErgaenzung": {"type": "object", "properties": {"versichertennummer": {"type": "string", "nullable": true}, "letzteAenderung": {"type": "string", "format": "date-time"}}, "additionalProperties": false, "description": "Die Versichertennummer des Patienten mit zu kuratierenden Medikationsinformationen zusammen mit dem letzten Änderungsdatum der Medikationsinformation."}, "Meldung": {"type": "object", "properties": {"nachricht": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "art": {"$ref": "#/components/schemas/Meldu<PERSON>t"}, "kategorie": {"$ref": "#/components/schemas/Meldungskategorie"}, "referenzen": {"type": "array", "items": {"$ref": "#/components/schemas/Referenz"}, "nullable": true}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}}, "additionalProperties": false, "description": "<PERSON> Meldung, die an den Aufrufer gesendet wird."}, "Meldungsart": {"enum": ["Unbekannt", "Information", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "type": "string"}, "Meldungskategorie": {"enum": ["KeineAngabe", "Validierung", "Laufzeit", "Rechenzentrum"], "type": "string"}, "Patient": {"required": ["geburtsdatum", "geschlecht", "nachname", "patientenId", "versicherungsnachweis", "vorname"], "type": "object", "properties": {"aktuelleVertragsteilnahmen": {"type": "array", "items": {"$ref": "#/components/schemas/Vertragsteilnahme"}, "nullable": true}, "patientenId": {"minLength": 1, "type": "string"}, "nachname": {"minLength": 1, "type": "string"}, "vorname": {"minLength": 1, "type": "string"}, "geburtsdatum": {"minLength": 1, "type": "string"}, "geschlecht": {"$ref": "#/components/schemas/PatientGeschlecht"}, "versicherungsnachweis": {"$ref": "#/components/schemas/Versicherungsnachweis"}}, "additionalProperties": false, "description": "Alle Informationen zum Patienten."}, "PatientGeschlecht": {"enum": ["U", "M", "W", "X", "D"], "type": "string"}, "PruefeAufBereitgestellteArztbriefeContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "lanr", "testuebermittlung", "zertifikatBetriebsstaette"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "versichertennummer": {"type": "string", "nullable": true}, "zertifikatBetriebsstaette": {"type": "string", "format": "byte"}, "testuebermittlung": {"type": "boolean"}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Prüfen auf bereitgestellte Arztbriefe."}, "PruefeAufBereitgestellteArztbriefeResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "istArztbriefBereitgestellt": {"type": "boolean"}}, "additionalProperties": false, "description": "Das Ergebnis der Überprüfung auf bereitgestellte Arztbriefe."}, "PruefeSignaturCdaDokumentContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "lanr", "testuebermittlung"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "testuebermittlung": {"type": "boolean"}, "CdaDokument": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Der Container mit Angaben zur Prüfung einer Signatur."}, "PruefeSignaturCdaDokumentResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "signaturInfo": {"$ref": "#/components/schemas/SignaturInfoType"}}, "additionalProperties": false, "description": "Das Ergebnis der Signaturprüfung eines CDA Dokuments."}, "PruefeVersichertenteilnahmeVernetzungContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "lanr", "testuebermittlung", "versichertennummer", "vertragsIdentifikator"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "vertragsIdentifikator": {"minLength": 1, "type": "string"}, "versichertennummer": {"minLength": 1, "type": "string"}, "testuebermittlung": {"type": "boolean"}}, "additionalProperties": false, "description": "Der Container mit Angaben zur Versichertenteilnahmeprüfung der eAV."}, "PruefeVersichertenteilnahmeVernetzungResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "transferId": {"type": "string", "nullable": true}, "istVersichertenteilnahmeVernetzung": {"type": "boolean"}}, "additionalProperties": false, "description": "Das Ergebnis einer Versichertenprüfung der eAV."}, "Referenz": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "referenzTyp": {"$ref": "#/components/schemas/ReferenzTyp"}}, "additionalProperties": false, "description": "Die Referenzen zu den vom Aufrufer übermittelten Objekten."}, "ReferenzTyp": {"enum": ["KeineAngabe", "Le<PERSON><PERSON>", "Patient", "Diagnose", "Ueberweisung", "Praxisgebuehr", "Vertragsidentifikator", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Rezept", "Operation", "Zielwert", "Istwert", "Erkrankungsstufe", "Hinderungsfaktor", "IstFremdeingeschrieben", "IstNichtFremdeingeschrieben"], "type": "string"}, "ResultatStatus": {"enum": ["Unbekannt", "OK", "Fehlgeschlagen", "TeilweiseVerarbeitet"], "type": "string"}, "SendeArztbriefContainer": {"required": ["absenderBsnr", "arztbriefEmpfaenger", "arztInformationsSystem", "lanr", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pdfDokument", "testuebermittlung"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "arztbriefEmpfaenger": {"type": "array", "items": {"$ref": "#/components/schemas/ArztbriefEmpfaenger"}}, "pdfDokument": {"type": "string", "format": "byte"}, "nurPrueflauf": {"type": "boolean"}, "testuebermittlung": {"type": "boolean"}, "CdaDokument": {"type": "string", "nullable": true}, "SignaturCdaDokument": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Versand von Arztbriefen. Das CDA wird als html-enkodierter String übermittelt."}, "SendeArztbriefResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "transferId": {"type": "string", "nullable": true}, "dokumentIdentifikator": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Das Ergebnis eines Arztbriefversands."}, "SendeDokumentenAbrufbestaetigungContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "dokumentIdentifikationen", "dokumentTyp", "lanr", "testuebermittlung"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "dokumentIdentifikationen": {"type": "array", "items": {"$ref": "#/components/schemas/DokumentIdentifikation"}}, "testuebermittlung": {"type": "boolean"}, "dokumentTyp": {"$ref": "#/components/schemas/DokumentType"}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Versand von Abrufbestätigungen."}, "SendeDokumentenAbrufbestaetigungResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}}, "additionalProperties": false, "description": "Das Ergebnis des Sendens einer Abrufbestätigung von Dokumenten."}, "SendeMedikationsinformationContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "lanr", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "testuebermittlung"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "CdaDokument": {"type": "string", "nullable": true}, "nurPrueflauf": {"type": "boolean"}, "testuebermittlung": {"type": "boolean"}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Versand von Medikationsinformationen. Das CDA wird als html-enkodierter String übermittelt."}, "SendeMedikationsinformationPflegearztContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "entfernePflegearztZuordnung", "lanr", "patient", "testuebermittlung", "zertifikatBetriebsstaette"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "zertifikatBetriebsstaette": {"type": "string", "format": "byte"}, "patient": {"$ref": "#/components/schemas/Patient"}, "entfernePflegearztZuordnung": {"type": "boolean"}, "testuebermittlung": {"type": "boolean"}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Setzen eines Pflegearztes."}, "SendeMedikationsinformationPflegearztResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}}, "additionalProperties": false, "description": "Das Ergebnis des Sendens eines Pflegearztes für die Medikationsformation für einen Patienten."}, "SendeMedikationsinformationResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "transferId": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Das Ergebnis eines Medikationsversands."}, "SendeTelekonsilContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "lanr", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "telekonsilEmpfaenger", "testuebermittlung"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "telekonsilEmpfaenger": {"type": "array", "items": {"$ref": "#/components/schemas/TelekonsilEmpfaenger"}}, "nurPrueflauf": {"type": "boolean"}, "testuebermittlung": {"type": "boolean"}, "CdaDokument": {"type": "string", "nullable": true}, "SignaturCdaDokument": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Der Container mit Angaben zum Versand von Telekonsilen. Das CDA wird als html-enkodierter String übermittelt."}, "SendeTelekonsilResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "transferId": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Das Ergebnis eines Telekonsilversands."}, "SignaturInfoType": {"type": "object", "properties": {"signaturZeitpunkt": {"type": "string", "format": "date-time"}, "unterzeichner": {"type": "string", "nullable": true}, "bsnr": {"type": "string", "nullable": true}, "lanr": {"type": "string", "nullable": true}, "zertifikatGueltigVon": {"type": "string", "format": "date-time"}, "zertifikatGueltigBis": {"type": "string", "format": "date-time"}, "zertifikatAussteller": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Informationen über das zur Signaturerzeugung verwendete Zertifikat."}, "SigniereCdaDokumentContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "kennwort", "lanr", "testuebermittlung", "zertifikatArzt"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "CdaDokument": {"type": "string", "nullable": true}, "zertifikatArzt": {"type": "string", "format": "byte"}, "kennwort": {"minLength": 1, "type": "string"}, "testuebermittlung": {"type": "boolean"}}, "additionalProperties": false, "description": "Der Container mit Angaben zur Erzeugung einer Signatur."}, "SigniereCdaDokumentResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "SignaturCdaDokument": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Das Ergebnis des Signierens eines CDA Dokuments."}, "SoftwareInformation": {"required": ["systemOid", "version"], "type": "object", "properties": {"systemOid": {"minLength": 1, "type": "string"}, "version": {"minLength": 1, "type": "string"}, "organisation": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Informationen über das Arztinformationssystem und dessen Hersteller."}, "SperrstatusTyp": {"enum": ["Aktiv", "<PERSON><PERSON><PERSON><PERSON>", "KeineAngabe"], "type": "string"}, "SuchbegriffEmpfaengerVernetzung": {"type": "object", "properties": {"nameArzt": {"type": "string", "nullable": true}, "plz": {"type": "string", "nullable": true}, "ort": {"type": "string", "nullable": true}, "strasse": {"type": "string", "nullable": true}, "lanr": {"type": "string", "nullable": true}, "bsnr": {"type": "string", "nullable": true}, "empfaengergruppeIdentifikator": {"type": "integer", "format": "int32"}, "empfaengergruppeIdentifikatorSpecified": {"type": "boolean"}}, "additionalProperties": false, "description": "Enthält alle Suchbegriffe für die Empfängersuche."}, "SucheEmpfaengerVernetzungContainer": {"required": ["absenderBsnr", "arztInformationsSystem", "kontext", "lanr", "suchbegriff", "testuebermittlung"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"minLength": 1, "type": "string"}, "suchbegriff": {"$ref": "#/components/schemas/SuchbegriffEmpfaengerVernetzung"}, "kontext": {"$ref": "#/components/schemas/KontextType"}, "testuebermittlung": {"type": "boolean"}}, "additionalProperties": false, "description": "Der Container mit Angaben zur Empfängersuche."}, "SucheEmpfaengerVernetzungResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}, "betriebsstaetten": {"type": "array", "items": {"$ref": "#/components/schemas/BetriebsstaetteVernetzung"}, "nullable": true}}, "additionalProperties": false, "description": "Das Ergebnis der Empfängersuche in der Vernetzung."}, "Telekonsil": {"type": "object", "properties": {"dokumentIdentifikator": {"type": "string", "nullable": true}, "dokumentZugriffstoken": {"type": "string", "nullable": true}, "CdaDokument": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Ein Telekonsil samt Informationen dazu."}, "TelekonsilEmpfaenger": {"type": "object", "properties": {"lanr": {"type": "string", "nullable": true}, "empfaengergruppeIdentifikator": {"type": "integer", "format": "int32"}, "empfaengergruppeIdentifikatorSpecified": {"type": "boolean"}}, "additionalProperties": false, "description": "<PERSON><PERSON> eines Empfängers des Telekonsils."}, "TelekonsilTyp": {"enum": ["<PERSON><PERSON><PERSON><PERSON>", "Gerichtet"], "type": "string"}, "TelekonsileMetainformation": {"type": "object", "properties": {"dokumentIdentifikator": {"type": "string", "nullable": true}, "vorgangsId": {"type": "string", "nullable": true}, "senderArzt": {"type": "string", "nullable": true}, "bearbeitungsstatusTelekonsil": {"$ref": "#/components/schemas/BearbeitungsstatusTelekonsil"}, "letzterBearbeitungszeitpunkt": {"type": "string", "format": "date-time"}, "senderArztLanr": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Die Metainformationen eines spezifischen Telekonsils, die von einem Arzt abrufbar sind."}, "UebermittlungsStatus": {"enum": ["KeineAngabe", "OK", "FachlicheVerletzung", "KeineBerechtigungWebService", "Wartung", "Interner<PERSON><PERSON><PERSON>", "UngueltigeStammdaten"], "type": "string"}, "ValidiereAuContainer": {"required": ["absenderBsnr", "arztInformationsSystem"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "lanr": {"type": "string", "nullable": true}, "FhirDokument": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Der Container für die Validierung der eAU auf FHIR-Basis. Der xml-Inhalt wird Html-encodiert als string übergeben."}, "ValidiereAuResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}}, "additionalProperties": false}, "ValidiereEinweisungsbriefContainer": {"required": ["absenderBsnr", "arztInformationsSystem"], "type": "object", "properties": {"arztInformationsSystem": {"$ref": "#/components/schemas/SoftwareInformation"}, "absenderBsnr": {"minLength": 1, "type": "string"}, "CdaDokument": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ValidiereEinweisungsbriefResultat": {"type": "object", "properties": {"meldungen": {"type": "array", "items": {"$ref": "#/components/schemas/Meldung"}, "nullable": true}, "status": {"$ref": "#/components/schemas/ResultatStatus"}, "uebermittlungsStatus": {"$ref": "#/components/schemas/UebermittlungsStatus"}}, "additionalProperties": false}, "Versicherungsnachweis": {"required": ["krankenkassenIk", "versichertenArt", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>mer"], "type": "object", "properties": {"krankenkassenIk": {"minLength": 1, "type": "string"}, "versichertenNummer": {"minLength": 1, "type": "string"}, "versichertenArt": {"minLength": 1, "type": "string"}, "besonderePersonengruppe": {"type": "string", "nullable": true}, "dmpKennzeichnung": {"type": "string", "default": "^(0?[0-9]|1[0-2]|(3|4)[0-9]|5[0-8])$", "nullable": true}}, "additionalProperties": false, "description": "Informationen über den Versicherungsnachweis des Patienten (i.d.R. Daten der Versichertenkarte)."}, "Vertragsteilnahme": {"required": ["istVertreterteilnahme", "vertragsIdentifikator"], "type": "object", "properties": {"vertragsIdentifikator": {"minLength": 1, "type": "string"}, "istVertreterteilnahme": {"type": "boolean"}}, "additionalProperties": false, "description": "Alle Informationen zu den Vertragsteilnahmen des abgerechneten Patienten."}, "ZertifikatArztInfoType": {"type": "object", "properties": {"zertifikatsinhaber": {"type": "string", "nullable": true}, "bsnr": {"type": "string", "nullable": true}, "lanr": {"type": "string", "nullable": true}, "zertifikatGueltigVon": {"type": "string", "format": "date-time"}, "zertifikatGueltigBis": {"type": "string", "format": "date-time"}, "zertifikatAussteller": {"type": "string", "nullable": true}, "seriennummer": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Informationen über das neu erzeugte Zertifikat für den identifizierten Arzt."}, "ZertifikatBetriebsstaetteInfoType": {"type": "object", "properties": {"zertifikatsinhaber": {"type": "string", "nullable": true}, "bsnr": {"type": "string", "nullable": true}, "zertifikatGueltigVon": {"type": "string", "format": "date-time"}, "zertifikatGueltigBis": {"type": "string", "format": "date-time"}, "zertifikatAussteller": {"type": "string", "nullable": true}, "seriennummer": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Informationen über das neu erzeugte Zertifikat für die angegebene Betriebsstätte."}, "ZertifikatInfoType": {"type": "object", "properties": {"zertifikatsinhaber": {"type": "string", "nullable": true}, "bsnr": {"type": "string", "nullable": true}, "lanr": {"type": "string", "nullable": true}, "zertifikatGueltigVon": {"type": "string", "format": "date-time"}, "zertifikatGueltigBis": {"type": "string", "format": "date-time"}, "zertifikatAussteller": {"type": "string", "nullable": true}, "seriennummer": {"type": "string", "nullable": true}}, "additionalProperties": false, "description": "Die gesammelten Informationen über das vorliegende Zertifikat."}, "ZertifikatsTyp": {"enum": ["Arztzertif<PERSON><PERSON>", "Betriebsstaettenzertifikat"], "type": "string"}, "Zertifikatsinformation": {"type": "object", "properties": {"seriennummer": {"type": "string", "nullable": true}, "sperrstatus": {"$ref": "#/components/schemas/SperrstatusTyp"}, "zertifikatsTyp": {"$ref": "#/components/schemas/ZertifikatsTyp"}}, "additionalProperties": false, "description": "Die Informationen eines betroffenen Zertifikats."}}}}