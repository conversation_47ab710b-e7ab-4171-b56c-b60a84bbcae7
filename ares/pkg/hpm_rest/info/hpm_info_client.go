// Package hpm_rest_info_client provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package hpm_rest_info_client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"github.com/oapi-codegen/runtime"
)

// CommonMeldung defines model for common.Meldung.
type CommonMeldung struct {
	Art                  *string `json:"Art,omitempty"`
	Code                 *string `json:"Code,omitempty"`
	Kategorie            *string `json:"Kategorie,omitempty"`
	Nachricht            *string `json:"Nachricht,omitempty"`
	UebermittlungsStatus *string `json:"UebermittlungsStatus,omitempty"`
}

// CommonResponseStatus defines model for common.ResponseStatus.
type CommonResponseStatus struct {
	Meldungen            *[]CommonMeldung `json:"Meldungen,omitempty"`
	Status               *string          `json:"Status,omitempty"`
	UebermittlungsStatus *string          `json:"UebermittlungsStatus,omitempty"`
}

// ConnectivityEndPoint defines model for connectivity.EndPoint.
type ConnectivityEndPoint struct {
	Endpunkt  *string `json:"Endpunkt,omitempty"`
	IstTLS    *bool   `json:"IstTLS,omitempty"`
	Nachricht *string `json:"Nachricht,omitempty"`
	Port      *int    `json:"Port,omitempty"`
	Verfügbar *bool   `json:"Verfügbar,omitempty"`
}

// ConnectivityHealthCheckResult defines model for connectivity.HealthCheckResult.
type ConnectivityHealthCheckResult struct {
	EndpunktVerfügbarkeit *[]ConnectivityEndPoint `json:"EndpunktVerfügbarkeit,omitempty"`
	Verfügbar             *bool                   `json:"Verfügbar,omitempty"`
}

// InfoServiceHzvOnlineKeyInformation defines model for infoService.HzvOnlineKeyInformation.
type InfoServiceHzvOnlineKeyInformation struct {
	Bsnr                         *string `json:"Bsnr,omitempty"`
	GeraeteId                    *string `json:"GeraeteId,omitempty"`
	GueltigBis                   *string `json:"GueltigBis,omitempty"`
	GueltigVon                   *string `json:"GueltigVon,omitempty"`
	SerienNummer                 *string `json:"SerienNummer,omitempty"`
	Vertragspartneridentifikator *string `json:"Vertragspartneridentifikator,omitempty"`
}

// InfoServiceLiefereHPMInformationResult defines model for infoService.LiefereHPMInformationResult.
type InfoServiceLiefereHPMInformationResult struct {
	HpmDateTimeNow            *string                               `json:"HpmDateTimeNow,omitempty"`
	HzvOnlineKeyInformationen *[]InfoServiceHzvOnlineKeyInformation `json:"HzvOnlineKeyInformationen,omitempty"`
	IstTestsystem             *bool                                 `json:"IstTestsystem,omitempty"`
	IstTestversion            *bool                                 `json:"IstTestversion,omitempty"`
	Status                    *string                               `json:"Status,omitempty"`
	UebermittlungsStatus      *string                               `json:"UebermittlungsStatus,omitempty"`
	Uebertragungsweg          *string                               `json:"Uebertragungsweg,omitempty"`
	Version                   *string                               `json:"Version,omitempty"`
}

// InfoServiceUebertragungswegJSON Setzt den Uebertragungsweg.
type InfoServiceUebertragungswegJSON struct {
	Uebertragungsweg *string `json:"Uebertragungsweg,omitempty"`
}

// ReverseProxyFrontendConnectivityCheckResult defines model for reverseProxyFrontend.ConnectivityCheckResult.
type ReverseProxyFrontendConnectivityCheckResult struct {
	ErreichbarkeitExtrnerEndpunkte *[]ReverseProxyFrontendExternalEndpointStatus `json:"ErreichbarkeitExtrnerEndpunkte,omitempty"`
	HPMKonfiguration               *string                                       `json:"HPMKonfiguration,omitempty"`
	HPMVersion                     *string                                       `json:"HPMVersion,omitempty"`
}

// ReverseProxyFrontendExternalEndpointStatus defines model for reverseProxyFrontend.ExternalEndpointStatus.
type ReverseProxyFrontendExternalEndpointStatus struct {
	Name   *string `json:"Name,omitempty"`
	Status *string `json:"Status,omitempty"`
}

// ReverseProxyFrontendTimestampJSON defines model for reverseProxyFrontend.TimestampJSON.
type ReverseProxyFrontendTimestampJSON struct {
	Timestamp *string `json:"Timestamp,omitempty"`
}

// GetApiKonnektivitaetParams defines parameters for GetApiKonnektivitaet.
type GetApiKonnektivitaetParams struct {
	// Testuebermittlung legt die Umgebung fest
	Testuebermittlung string `form:"testuebermittlung" json:"testuebermittlung"`
}

// GetApiKonnektivitaeteavParams defines parameters for GetApiKonnektivitaeteav.
type GetApiKonnektivitaeteavParams struct {
	// Testuebermittlung legt die Umgebung fest
	Testuebermittlung string `form:"testuebermittlung" json:"testuebermittlung"`
}

// PutApiUebertragungswegJSONRequestBody defines body for PutApiUebertragungsweg for application/json ContentType.
type PutApiUebertragungswegJSONRequestBody = InfoServiceUebertragungswegJSON

// RequestEditorFn  is the function signature for the RequestEditor callback function
type RequestEditorFn func(ctx context.Context, req *http.Request) error

// Doer performs HTTP requests.
//
// The standard http.Client implements this interface.
type HttpRequestDoer interface {
	Do(req *http.Request) (*http.Response, error)
}

// Client which conforms to the OpenAPI3 specification for this service.
type Client struct {
	// The endpoint of the server conforming to this interface, with scheme,
	// https://api.deepmap.com for example. This can contain a path relative
	// to the server, such as https://api.deepmap.com/dev-test, and all the
	// paths in the swagger spec will be appended to the server.
	Server string

	// Doer for performing requests, typically a *http.Client with any
	// customized settings, such as certificate chains.
	Client HttpRequestDoer

	// A list of callbacks for modifying requests which are generated before sending over
	// the network.
	RequestEditors []RequestEditorFn
}

// ClientOption allows setting custom parameters during construction
type ClientOption func(*Client) error

// Creates a new Client, with reasonable defaults
func NewClient(server string, opts ...ClientOption) (*Client, error) {
	// create a client with sane default values
	client := Client{
		Server: server,
	}
	// mutate client and add all optional params
	for _, o := range opts {
		if err := o(&client); err != nil {
			return nil, err
		}
	}
	// ensure the server URL always has a trailing slash
	if !strings.HasSuffix(client.Server, "/") {
		client.Server += "/"
	}
	// create httpClient, if not already present
	if client.Client == nil {
		client.Client = &http.Client{}
	}
	return &client, nil
}

// WithHTTPClient allows overriding the default Doer, which is
// automatically created using http.Client. This is useful for tests.
func WithHTTPClient(doer HttpRequestDoer) ClientOption {
	return func(c *Client) error {
		c.Client = doer
		return nil
	}
}

// WithRequestEditorFn allows setting up a callback function, which will be
// called right before sending the request. This can be used to mutate the request.
func WithRequestEditorFn(fn RequestEditorFn) ClientOption {
	return func(c *Client) error {
		c.RequestEditors = append(c.RequestEditors, fn)
		return nil
	}
}

// The interface specification for the client above.
type ClientInterface interface {
	// GetApiBasicconnectivity request
	GetApiBasicconnectivity(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApiHealth request
	GetApiHealth(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApiHpminformation request
	GetApiHpminformation(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApiKonnektivitaet request
	GetApiKonnektivitaet(ctx context.Context, params *GetApiKonnektivitaetParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApiKonnektivitaeteav request
	GetApiKonnektivitaeteav(ctx context.Context, params *GetApiKonnektivitaeteavParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetApiTimestamp request
	GetApiTimestamp(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PutApiUebertragungswegWithBody request with any body
	PutApiUebertragungswegWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	PutApiUebertragungsweg(ctx context.Context, body PutApiUebertragungswegJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// PostApiUpdate request
	PostApiUpdate(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error)
}

func (c *Client) GetApiBasicconnectivity(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApiBasicconnectivityRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApiHealth(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApiHealthRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApiHpminformation(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApiHpminformationRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApiKonnektivitaet(ctx context.Context, params *GetApiKonnektivitaetParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApiKonnektivitaetRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApiKonnektivitaeteav(ctx context.Context, params *GetApiKonnektivitaeteavParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApiKonnektivitaeteavRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetApiTimestamp(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetApiTimestampRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutApiUebertragungswegWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutApiUebertragungswegRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PutApiUebertragungsweg(ctx context.Context, body PutApiUebertragungswegJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPutApiUebertragungswegRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) PostApiUpdate(ctx context.Context, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewPostApiUpdateRequest(c.Server)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

// NewGetApiBasicconnectivityRequest generates requests for GetApiBasicconnectivity
func NewGetApiBasicconnectivityRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/basicconnectivity")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetApiHealthRequest generates requests for GetApiHealth
func NewGetApiHealthRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/health")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetApiHpminformationRequest generates requests for GetApiHpminformation
func NewGetApiHpminformationRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/hpminformation")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetApiKonnektivitaetRequest generates requests for GetApiKonnektivitaet
func NewGetApiKonnektivitaetRequest(server string, params *GetApiKonnektivitaetParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/konnektivitaet")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "testuebermittlung", runtime.ParamLocationQuery, params.Testuebermittlung); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetApiKonnektivitaeteavRequest generates requests for GetApiKonnektivitaeteav
func NewGetApiKonnektivitaeteavRequest(server string, params *GetApiKonnektivitaeteavParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/konnektivitaeteav")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "testuebermittlung", runtime.ParamLocationQuery, params.Testuebermittlung); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetApiTimestampRequest generates requests for GetApiTimestamp
func NewGetApiTimestampRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/timestamp")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewPutApiUebertragungswegRequest calls the generic PutApiUebertragungsweg builder with application/json body
func NewPutApiUebertragungswegRequest(server string, body PutApiUebertragungswegJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewPutApiUebertragungswegRequestWithBody(server, "application/json", bodyReader)
}

// NewPutApiUebertragungswegRequestWithBody generates requests for PutApiUebertragungsweg with any type of body
func NewPutApiUebertragungswegRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/uebertragungsweg")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PUT", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewPostApiUpdateRequest generates requests for PostApiUpdate
func NewPostApiUpdateRequest(server string) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/api/update")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

func (c *Client) applyEditors(ctx context.Context, req *http.Request, additionalEditors []RequestEditorFn) error {
	for _, r := range c.RequestEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	for _, r := range additionalEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	return nil
}

// ClientWithResponses builds on ClientInterface to offer response payloads
type ClientWithResponses struct {
	ClientInterface
}

// NewClientWithResponses creates a new ClientWithResponses, which wraps
// Client with return type handling
func NewClientWithResponses(server string, opts ...ClientOption) (*ClientWithResponses, error) {
	client, err := NewClient(server, opts...)
	if err != nil {
		return nil, err
	}
	return &ClientWithResponses{client}, nil
}

// WithBaseURL overrides the baseURL.
func WithBaseURL(baseURL string) ClientOption {
	return func(c *Client) error {
		newBaseURL, err := url.Parse(baseURL)
		if err != nil {
			return err
		}
		c.Server = newBaseURL.String()
		return nil
	}
}

// ClientWithResponsesInterface is the interface specification for the client with responses above.
type ClientWithResponsesInterface interface {
	// GetApiBasicconnectivityWithResponse request
	GetApiBasicconnectivityWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetApiBasicconnectivityResponse, error)

	// GetApiHealthWithResponse request
	GetApiHealthWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetApiHealthResponse, error)

	// GetApiHpminformationWithResponse request
	GetApiHpminformationWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetApiHpminformationResponse, error)

	// GetApiKonnektivitaetWithResponse request
	GetApiKonnektivitaetWithResponse(ctx context.Context, params *GetApiKonnektivitaetParams, reqEditors ...RequestEditorFn) (*GetApiKonnektivitaetResponse, error)

	// GetApiKonnektivitaeteavWithResponse request
	GetApiKonnektivitaeteavWithResponse(ctx context.Context, params *GetApiKonnektivitaeteavParams, reqEditors ...RequestEditorFn) (*GetApiKonnektivitaeteavResponse, error)

	// GetApiTimestampWithResponse request
	GetApiTimestampWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetApiTimestampResponse, error)

	// PutApiUebertragungswegWithBodyWithResponse request with any body
	PutApiUebertragungswegWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutApiUebertragungswegResponse, error)

	PutApiUebertragungswegWithResponse(ctx context.Context, body PutApiUebertragungswegJSONRequestBody, reqEditors ...RequestEditorFn) (*PutApiUebertragungswegResponse, error)

	// PostApiUpdateWithResponse request
	PostApiUpdateWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*PostApiUpdateResponse, error)
}

type GetApiBasicconnectivityResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ReverseProxyFrontendConnectivityCheckResult
}

// Status returns HTTPResponse.Status
func (r GetApiBasicconnectivityResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApiBasicconnectivityResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApiHealthResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ConnectivityHealthCheckResult
}

// Status returns HTTPResponse.Status
func (r GetApiHealthResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApiHealthResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApiHpminformationResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *InfoServiceLiefereHPMInformationResult
}

// Status returns HTTPResponse.Status
func (r GetApiHpminformationResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApiHpminformationResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApiKonnektivitaetResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *CommonResponseStatus
}

// Status returns HTTPResponse.Status
func (r GetApiKonnektivitaetResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApiKonnektivitaetResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApiKonnektivitaeteavResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *CommonResponseStatus
}

// Status returns HTTPResponse.Status
func (r GetApiKonnektivitaeteavResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApiKonnektivitaeteavResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetApiTimestampResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *ReverseProxyFrontendTimestampJSON
}

// Status returns HTTPResponse.Status
func (r GetApiTimestampResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetApiTimestampResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PutApiUebertragungswegResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *CommonResponseStatus
}

// Status returns HTTPResponse.Status
func (r PutApiUebertragungswegResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PutApiUebertragungswegResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type PostApiUpdateResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *CommonResponseStatus
}

// Status returns HTTPResponse.Status
func (r PostApiUpdateResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r PostApiUpdateResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

// GetApiBasicconnectivityWithResponse request returning *GetApiBasicconnectivityResponse
func (c *ClientWithResponses) GetApiBasicconnectivityWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetApiBasicconnectivityResponse, error) {
	rsp, err := c.GetApiBasicconnectivity(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApiBasicconnectivityResponse(rsp)
}

// GetApiHealthWithResponse request returning *GetApiHealthResponse
func (c *ClientWithResponses) GetApiHealthWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetApiHealthResponse, error) {
	rsp, err := c.GetApiHealth(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApiHealthResponse(rsp)
}

// GetApiHpminformationWithResponse request returning *GetApiHpminformationResponse
func (c *ClientWithResponses) GetApiHpminformationWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetApiHpminformationResponse, error) {
	rsp, err := c.GetApiHpminformation(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApiHpminformationResponse(rsp)
}

// GetApiKonnektivitaetWithResponse request returning *GetApiKonnektivitaetResponse
func (c *ClientWithResponses) GetApiKonnektivitaetWithResponse(ctx context.Context, params *GetApiKonnektivitaetParams, reqEditors ...RequestEditorFn) (*GetApiKonnektivitaetResponse, error) {
	rsp, err := c.GetApiKonnektivitaet(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApiKonnektivitaetResponse(rsp)
}

// GetApiKonnektivitaeteavWithResponse request returning *GetApiKonnektivitaeteavResponse
func (c *ClientWithResponses) GetApiKonnektivitaeteavWithResponse(ctx context.Context, params *GetApiKonnektivitaeteavParams, reqEditors ...RequestEditorFn) (*GetApiKonnektivitaeteavResponse, error) {
	rsp, err := c.GetApiKonnektivitaeteav(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApiKonnektivitaeteavResponse(rsp)
}

// GetApiTimestampWithResponse request returning *GetApiTimestampResponse
func (c *ClientWithResponses) GetApiTimestampWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*GetApiTimestampResponse, error) {
	rsp, err := c.GetApiTimestamp(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetApiTimestampResponse(rsp)
}

// PutApiUebertragungswegWithBodyWithResponse request with arbitrary body returning *PutApiUebertragungswegResponse
func (c *ClientWithResponses) PutApiUebertragungswegWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*PutApiUebertragungswegResponse, error) {
	rsp, err := c.PutApiUebertragungswegWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutApiUebertragungswegResponse(rsp)
}

func (c *ClientWithResponses) PutApiUebertragungswegWithResponse(ctx context.Context, body PutApiUebertragungswegJSONRequestBody, reqEditors ...RequestEditorFn) (*PutApiUebertragungswegResponse, error) {
	rsp, err := c.PutApiUebertragungsweg(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePutApiUebertragungswegResponse(rsp)
}

// PostApiUpdateWithResponse request returning *PostApiUpdateResponse
func (c *ClientWithResponses) PostApiUpdateWithResponse(ctx context.Context, reqEditors ...RequestEditorFn) (*PostApiUpdateResponse, error) {
	rsp, err := c.PostApiUpdate(ctx, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParsePostApiUpdateResponse(rsp)
}

// ParseGetApiBasicconnectivityResponse parses an HTTP response from a GetApiBasicconnectivityWithResponse call
func ParseGetApiBasicconnectivityResponse(rsp *http.Response) (*GetApiBasicconnectivityResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApiBasicconnectivityResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ReverseProxyFrontendConnectivityCheckResult
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetApiHealthResponse parses an HTTP response from a GetApiHealthWithResponse call
func ParseGetApiHealthResponse(rsp *http.Response) (*GetApiHealthResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApiHealthResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "text") && rsp.StatusCode == 200:
		var dest ConnectivityHealthCheckResult
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetApiHpminformationResponse parses an HTTP response from a GetApiHpminformationWithResponse call
func ParseGetApiHpminformationResponse(rsp *http.Response) (*GetApiHpminformationResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApiHpminformationResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest InfoServiceLiefereHPMInformationResult
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetApiKonnektivitaetResponse parses an HTTP response from a GetApiKonnektivitaetWithResponse call
func ParseGetApiKonnektivitaetResponse(rsp *http.Response) (*GetApiKonnektivitaetResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApiKonnektivitaetResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest CommonResponseStatus
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetApiKonnektivitaeteavResponse parses an HTTP response from a GetApiKonnektivitaeteavWithResponse call
func ParseGetApiKonnektivitaeteavResponse(rsp *http.Response) (*GetApiKonnektivitaeteavResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApiKonnektivitaeteavResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest CommonResponseStatus
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetApiTimestampResponse parses an HTTP response from a GetApiTimestampWithResponse call
func ParseGetApiTimestampResponse(rsp *http.Response) (*GetApiTimestampResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetApiTimestampResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest ReverseProxyFrontendTimestampJSON
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePutApiUebertragungswegResponse parses an HTTP response from a PutApiUebertragungswegWithResponse call
func ParsePutApiUebertragungswegResponse(rsp *http.Response) (*PutApiUebertragungswegResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PutApiUebertragungswegResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest CommonResponseStatus
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParsePostApiUpdateResponse parses an HTTP response from a PostApiUpdateWithResponse call
func ParsePostApiUpdateResponse(rsp *http.Response) (*PostApiUpdateResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &PostApiUpdateResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest CommonResponseStatus
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}
