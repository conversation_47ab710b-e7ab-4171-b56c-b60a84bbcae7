// This code was autogenerated do not edit
package formkey

// This code was autogenerated, do not edit
type FormKey_MUSTER_1 string

const (
	MUSTER_1_LABEL_INSURANCE_NAME_0     FormKey_MUSTER_1 = "label_insurance_name_0"
	MUSTER_1_LABEL_PATIENTINFO_LINE2_0  FormKey_MUSTER_1 = "label_patientInfo_line2_0"
	MUSTER_1_LABEL_PATIENTINFO_LINE3_0  FormKey_MUSTER_1 = "label_patientInfo_line3_0"
	MUSTER_1_LABEL_PATIENTINFO_LINE4_0  FormKey_MUSTER_1 = "label_patientInfo_line4_0"
	MUSTER_1_LABEL_DATE_OF_BIRTH_0      FormKey_MUSTER_1 = "label_date_of_birth_0"
	MUSTER_1_LABEL_IK_NUMBER_0          FormKey_MUSTER_1 = "label_ik_number_0"
	MUSTER_1_LABEL_BSNR_0               FormKey_MUSTER_1 = "label_bsnr_0"
	MUSTER_1_LABEL_INSURANCE_NUMBER_0   FormKey_MUSTER_1 = "label_insurance_number_0"
	MUSTER_1_LABEL_INSURANCE_STATUS_0   FormKey_MUSTER_1 = "label_insurance_status_0"
	MUSTER_1_DATE_PRESCRIBE_0           FormKey_MUSTER_1 = "date_prescribe_0"
	MUSTER_1_CHECKBOX_FOLGEBES_0        FormKey_MUSTER_1 = "checkbox_folgebes_0"
	MUSTER_1_LABEL_ICD10_CODE_1_0       FormKey_MUSTER_1 = "label_icd10_code_1_0"
	MUSTER_1_LABEL_ICD10_CODE_2_0       FormKey_MUSTER_1 = "label_icd10_code_2_0"
	MUSTER_1_LABEL_ICD10_CODE_3_0       FormKey_MUSTER_1 = "label_icd10_code_3_0"
	MUSTER_1_LABEL_ICD10_CODE_4_0       FormKey_MUSTER_1 = "label_icd10_code_4_0"
	MUSTER_1_LABEL_ICD10_CODE_5_0       FormKey_MUSTER_1 = "label_icd10_code_5_0"
	MUSTER_1_LABEL_ICD10_CODE_6_0       FormKey_MUSTER_1 = "label_icd10_code_6_0"
	MUSTER_1_TEXTBOX_ICD_CODE_0         FormKey_MUSTER_1 = "textbox_icd_code_0"
	MUSTER_1_CHECKBOX_SONSTIGER_0       FormKey_MUSTER_1 = "checkbox_sonstiger_0"
	MUSTER_1_CHECKBOX_VERSOR_0          FormKey_MUSTER_1 = "checkbox_versor_0"
	MUSTER_1_CHECKBOX_LEISTUNGEN_0      FormKey_MUSTER_1 = "checkbox_leistungen_0"
	MUSTER_1_CHECKBOX_SONSTIGE_0        FormKey_MUSTER_1 = "checkbox_sonstige_0"
	MUSTER_1_TEXTBOX_SONSTIGE_0         FormKey_MUSTER_1 = "textbox_sonstige_0"
	MUSTER_1_CHECKBOX_7AU_0             FormKey_MUSTER_1 = "checkbox_7au_0"
	MUSTER_1_CHECKBOX_ENDBESCH_0        FormKey_MUSTER_1 = "checkbox_endbesch_0"
	MUSTER_1_LABEL_PATIENTINFO_LINE1_0  FormKey_MUSTER_1 = "label_patientInfo_line1_0"
	MUSTER_1_CHECKBOX_STUFENWEISE_0     FormKey_MUSTER_1 = "checkbox_stufenweise_0"
	MUSTER_1_DATE_ARBEIT_0              FormKey_MUSTER_1 = "date_arbeit_0"
	MUSTER_1_DATE_VORAU_0               FormKey_MUSTER_1 = "date_vorau_0"
	MUSTER_1_DATE_FESTGESTELLT_0        FormKey_MUSTER_1 = "date_festgestellt_0"
	MUSTER_1_LABEL_DATE_FESTGESTELLT_0  FormKey_MUSTER_1 = "label_date_festgestellt_0"
	MUSTER_1_LABEL_DATE_VORAU_0         FormKey_MUSTER_1 = "label_date_vorau_0"
	MUSTER_1_LABEL_DATE_ARBEIT_0        FormKey_MUSTER_1 = "label_date_arbeit_0"
	MUSTER_1_CHECKBOX_ERSTBES_0         FormKey_MUSTER_1 = "checkbox_erstbes_0"
	MUSTER_1_CHECKBOX_ARBEITSUNFALL_0   FormKey_MUSTER_1 = "checkbox_arbeitsunfall_0"
	MUSTER_1_CHECKBOX_DEM_DURCHGANG_0   FormKey_MUSTER_1 = "checkbox_dem_durchgang_0"
	MUSTER_1_LABEL_LANR_0               FormKey_MUSTER_1 = "label_lanr_0"
	MUSTER_1_BARCODE                    FormKey_MUSTER_1 = "barcode"
	MUSTER_1_LABEL_WOP_0                FormKey_MUSTER_1 = "label_wop_0"
	MUSTER_1_LABEL_INSURANCE_END_DATE_0 FormKey_MUSTER_1 = "label_insurance_end_date_0"
	MUSTER_1_LABEL_DOCTOR_STAMP_0       FormKey_MUSTER_1 = "label_doctor_stamp_0"
)

type FormKey_MUSTER_2 string

const (
	MUSTER_2_LABEL_INSURANCE_NAME        FormKey_MUSTER_2 = "label_insurance_name"
	MUSTER_2_LABEL_PATIENTINFO_LINE1     FormKey_MUSTER_2 = "label_patientInfo_line1"
	MUSTER_2_LABEL_PATIENTINFO_LINE2     FormKey_MUSTER_2 = "label_patientInfo_line2"
	MUSTER_2_LABEL_DATE_OF_BIRTH         FormKey_MUSTER_2 = "label_date_of_birth"
	MUSTER_2_LABEL_PATIENTINFO_LINE3     FormKey_MUSTER_2 = "label_patientInfo_line3"
	MUSTER_2_LABEL_PATIENTINFO_LINE4     FormKey_MUSTER_2 = "label_patientInfo_line4"
	MUSTER_2_LABEL_INSURANCE_NUMBER      FormKey_MUSTER_2 = "label_insurance_number"
	MUSTER_2_LABEL_INSURANCE_STATUS      FormKey_MUSTER_2 = "label_insurance_status"
	MUSTER_2_DATE_PRESCRIBE              FormKey_MUSTER_2 = "date_prescribe"
	MUSTER_2_LABEL_LANR                  FormKey_MUSTER_2 = "label_lanr"
	MUSTER_2_LABEL_BSNR                  FormKey_MUSTER_2 = "label_bsnr"
	MUSTER_2_LABEL_IK_NUMBER             FormKey_MUSTER_2 = "label_ik_number"
	MUSTER_2_CHECKBOX_NOTFALL            FormKey_MUSTER_2 = "checkbox_notfall"
	MUSTER_2_CHECKBOX_BELEGARZT          FormKey_MUSTER_2 = "checkbox_belegarzt"
	MUSTER_2_CHECKBOX_VERSOGUNGS         FormKey_MUSTER_2 = "checkbox_versogungs"
	MUSTER_2_TEXTBOX_NACHSTERREICHBARE   FormKey_MUSTER_2 = "textbox_nachsterreichbare"
	MUSTER_2_TEXTBOX_DIAGNOSE_LINE1      FormKey_MUSTER_2 = "textbox_diagnose_line1"
	MUSTER_2_TEXTBOX_DIAGNOSE_LINE2      FormKey_MUSTER_2 = "textbox_diagnose_line2"
	MUSTER_2_TEXTBOX_DIAGNOSE_LINE3      FormKey_MUSTER_2 = "textbox_diagnose_line3"
	MUSTER_2_TEXTBOX_DIAGNOSE_LINE4      FormKey_MUSTER_2 = "textbox_diagnose_line4"
	MUSTER_2_TEXTBOX_UNTERSUCHUNG_LINE1  FormKey_MUSTER_2 = "textbox_untersuchung_line1"
	MUSTER_2_TEXTBOX_UNTERSUCHUNG_LINE2  FormKey_MUSTER_2 = "textbox_untersuchung_line2"
	MUSTER_2_TEXTBOX_UNTERSUCHUNG_LINE3  FormKey_MUSTER_2 = "textbox_untersuchung_line3"
	MUSTER_2_TEXTBOX_BISHERIGE_LINE2     FormKey_MUSTER_2 = "textbox_bisherige_line2"
	MUSTER_2_TEXTBOX_BISHERIGE_LINE3     FormKey_MUSTER_2 = "textbox_bisherige_line3"
	MUSTER_2_TEXTBOX_BISHERIGE_LINE1     FormKey_MUSTER_2 = "textbox_bisherige_line1"
	MUSTER_2_TEXTBOX_FRAGESTELLUNG_LINE1 FormKey_MUSTER_2 = "textbox_fragestellung_line1"
	MUSTER_2_TEXTBOX_FRAGESTELLUNG_LINE2 FormKey_MUSTER_2 = "textbox_fragestellung_line2"
	MUSTER_2_TEXTBOX_FMITGEGEBENE_LINE1  FormKey_MUSTER_2 = "textbox_fmitgegebene_line1"
	MUSTER_2_TEXTBOX_FMITGEGEBENE_LINE2  FormKey_MUSTER_2 = "textbox_fmitgegebene_line2"
	MUSTER_2_CHECKBOX_UNFALL             FormKey_MUSTER_2 = "checkbox_unfall"
	MUSTER_2_LABEL_DOCTOR_STAMP          FormKey_MUSTER_2 = "label_doctor_stamp"
	MUSTER_2_BARCODE                     FormKey_MUSTER_2 = "barcode"
	MUSTER_2_LABEL_TAKEOVER_DIAGNOSIS    FormKey_MUSTER_2 = "label_takeover_diagnosis"
)

type FormKey_MUSTER_4 string

const (
	MUSTER_4_CHECKBOX_UNFALL_UNFALLFOLGE                        FormKey_MUSTER_4 = "checkbox_unfall_unfallfolge"
	MUSTER_4_CHECKBOX_ARBEITSUNFALL_BERUFSKRANKHEIT             FormKey_MUSTER_4 = "checkbox_arbeitsunfall_berufskrankheit"
	MUSTER_4_CHECKBOX_VERSORGUNGSLEIDEN                         FormKey_MUSTER_4 = "checkbox_versorgungsleiden"
	MUSTER_4_CHECKBOX_HINFAHRT                                  FormKey_MUSTER_4 = "checkbox_hinfahrt"
	MUSTER_4_CHECKBOX_RUCKFAHRT                                 FormKey_MUSTER_4 = "checkbox_ruckfahrt"
	MUSTER_4_CHECKBOX_ZUZAH_LUNGS_PFLICHT                       FormKey_MUSTER_4 = "checkbox_zuzah_lungs_pflicht"
	MUSTER_4_CHECKBOX_ZUZAH_FREI                                FormKey_MUSTER_4 = "checkbox_zuzah_frei"
	MUSTER_4_CHECKBOX_VOLL_TEILSTATIONARE_KRANKENHAUSBEHANDLUNG FormKey_MUSTER_4 = "checkbox_voll_teilstationare_krankenhausbehandlung"
	MUSTER_4_CHECKBOX_AMBULANTE_BEHANDLUNG                      FormKey_MUSTER_4 = "checkbox_ambulante_behandlung"
	MUSTER_4_CHECKBOX_VOR_NACHSTATIONARE_BEHANDLUNG             FormKey_MUSTER_4 = "checkbox_vor_nachstationare_behandlung"
	MUSTER_4_TEXTBOX_FAHRTEN_ZU_HOSPIZEN                        FormKey_MUSTER_4 = "textbox_fahrten_zu_hospizen"
	MUSTER_4_CHECKBOX_HOCHFREQUENTE_BEHANDLUNG                  FormKey_MUSTER_4 = "checkbox_hochfrequente_behandlung"
	MUSTER_4_CHECKBOX_VERGLEICHBARER_AUSNAHMEFALL               FormKey_MUSTER_4 = "checkbox_vergleichbarer_ausnahmefall"
	MUSTER_4_CHECKBOX_DAUERHAFTE_MOBILITATSBEEINTRACHTIGUNG     FormKey_MUSTER_4 = "checkbox_dauerhafte_mobilitatsbeeintrachtigung"
	MUSTER_4_CHECKBOX_ANDERER_GRUND_FUR_FAHRT                   FormKey_MUSTER_4 = "checkbox_anderer_grund_fur_fahrt"
	MUSTER_4_TEXTBOX_PRO_WOCHE                                  FormKey_MUSTER_4 = "textbox_pro_woche"
	MUSTER_4_TEXTBOX_BEHANDLUNGSSTATTE                          FormKey_MUSTER_4 = "textbox_behandlungsstatte"
	MUSTER_4_CHECKBOX_TAXI_MIETWAGEN                            FormKey_MUSTER_4 = "checkbox_taxi_mietwagen"
	MUSTER_4_CHECKBOX_KTW_DA_MEDIZINISCH                        FormKey_MUSTER_4 = "checkbox_ktw_da_medizinisch"
	MUSTER_4_TEXTBOX_ART_UND_AUSSTATTUNG_1                      FormKey_MUSTER_4 = "textbox_art_und_ausstattung_1"
	MUSTER_4_TEXTBOX_ART_UND_AUSSTATTUNG_2                      FormKey_MUSTER_4 = "textbox_art_und_ausstattung_2"
	MUSTER_4_CHECKBOX_ROLLSTUHL                                 FormKey_MUSTER_4 = "checkbox_rollstuhl"
	MUSTER_4_CHECKBOX_TRAGESTUHL                                FormKey_MUSTER_4 = "checkbox_tragestuhl"
	MUSTER_4_CHECKBOX_LIEGEND                                   FormKey_MUSTER_4 = "checkbox_liegend"
	MUSTER_4_CHECKBOX_RTW                                       FormKey_MUSTER_4 = "checkbox_rtw"
	MUSTER_4_CHECKBOX_NAW_NEF                                   FormKey_MUSTER_4 = "checkbox_naw_nef"
	MUSTER_4_CHECKBOX_ANDERE                                    FormKey_MUSTER_4 = "checkbox_andere"
	MUSTER_4_TEXTBOX_BEGRUNDUNG_SONSTIGES                       FormKey_MUSTER_4 = "textbox_begrundung_sonstiges"
	MUSTER_4_TEXTBOX_ANDERE                                     FormKey_MUSTER_4 = "textbox_andere"
	MUSTER_4_CHECKBOX_ANDERER_GRUND                             FormKey_MUSTER_4 = "checkbox_anderer_grund"
	MUSTER_4_BARCODE                                            FormKey_MUSTER_4 = "barcode"
	MUSTER_4_LABEL_DOCTOR_STAMP                                 FormKey_MUSTER_4 = "label_doctor_stamp"
	MUSTER_4_LABEL_INSURANCE_NAME                               FormKey_MUSTER_4 = "label_insurance_name"
	MUSTER_4_LABEL_WOP                                          FormKey_MUSTER_4 = "label_wop"
	MUSTER_4_DATE_LABEL_CUSTOM_VOM                              FormKey_MUSTER_4 = "date_label_custom_vom"
	MUSTER_4_DATE_LABEL_CUSTOM_BIS                              FormKey_MUSTER_4 = "date_label_custom_bis"
	MUSTER_4_LABEL_IK_NUMBER                                    FormKey_MUSTER_4 = "label_ik_number"
	MUSTER_4_LABEL_BSNR                                         FormKey_MUSTER_4 = "label_bsnr"
	MUSTER_4_LABEL_INSURANCE_NUMBER                             FormKey_MUSTER_4 = "label_insurance_number"
	MUSTER_4_LABEL_LANR                                         FormKey_MUSTER_4 = "label_lanr"
	MUSTER_4_DATE_PRESCRIBE                                     FormKey_MUSTER_4 = "date_prescribe"
	MUSTER_4_LABEL_INSURANCE_STATUS                             FormKey_MUSTER_4 = "label_insurance_status"
	MUSTER_4_LABEL_PATIENTINFO_LINE2                            FormKey_MUSTER_4 = "label_patientInfo_line2"
	MUSTER_4_LABEL_PATIENTINFO_LINE1                            FormKey_MUSTER_4 = "label_patientInfo_line1"
	MUSTER_4_LABEL_DATE_OF_BIRTH                                FormKey_MUSTER_4 = "label_date_of_birth"
	MUSTER_4_LABEL_PATIENTINFO_LINE3                            FormKey_MUSTER_4 = "label_patientInfo_line3"
	MUSTER_4_LABEL_PATIENTINFO_LINE4                            FormKey_MUSTER_4 = "label_patientInfo_line4"
	MUSTER_4_LABEL_INSURANCE_END_DATE                           FormKey_MUSTER_4 = "label_insurance_end_date"
	MUSTER_4_LABEL_DATE_LABEL_CUSTOM_BIS_NO_SPACE               FormKey_MUSTER_4 = "label_date_label_custom_bis_no_space"
	MUSTER_4_LABEL_DATE_LABEL_CUSTOM_VOM_NO_SPACE               FormKey_MUSTER_4 = "label_date_label_custom_vom_no_space"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_6 string

const (
	MUSTER_6_BARCODE                                 FormKey_MUSTER_6 = "barcode"
	MUSTER_6_LABEL_TAKEOVER_DIAGNOSIS                FormKey_MUSTER_6 = "label_takeover_diagnosis"
	MUSTER_6_TEXTBOX_DIAGNOSE_LINE1                  FormKey_MUSTER_6 = "textbox_diagnose_line1"
	MUSTER_6_TEXTBOX_DIAGNOSE_LINE2                  FormKey_MUSTER_6 = "textbox_diagnose_line2"
	MUSTER_6_LABEL_PRF_NR                            FormKey_MUSTER_6 = "label_prf_nr"
	MUSTER_6_LABEL_PATIENTINFO_LINE2                 FormKey_MUSTER_6 = "label_patientInfo_line2"
	MUSTER_6_LABEL_PATIENTINFO_LINE1                 FormKey_MUSTER_6 = "label_patientInfo_line1"
	MUSTER_6_LABEL_DATE_OF_BIRTH                     FormKey_MUSTER_6 = "label_date_of_birth"
	MUSTER_6_LABEL_INSURANCE_NAME                    FormKey_MUSTER_6 = "label_insurance_name"
	MUSTER_6_LABEL_WOP                               FormKey_MUSTER_6 = "label_wop"
	MUSTER_6_LABEL_PATIENTINFO_LINE3                 FormKey_MUSTER_6 = "label_patientInfo_line3"
	MUSTER_6_LABEL_PATIENTINFO_LINE4                 FormKey_MUSTER_6 = "label_patientInfo_line4"
	MUSTER_6_LABEL_IK_NUMBER                         FormKey_MUSTER_6 = "label_ik_number"
	MUSTER_6_LABEL_BSNR                              FormKey_MUSTER_6 = "label_bsnr"
	MUSTER_6_LABEL_INSURANCE_NUMBER                  FormKey_MUSTER_6 = "label_insurance_number"
	MUSTER_6_LABEL_LANR                              FormKey_MUSTER_6 = "label_lanr"
	MUSTER_6_DATE_PRESCRIBE                          FormKey_MUSTER_6 = "date_prescribe"
	MUSTER_6_LABEL_INSURANCE_END_DATE                FormKey_MUSTER_6 = "label_insurance_end_date"
	MUSTER_6_LABEL_INSURANCE_STATUS                  FormKey_MUSTER_6 = "label_insurance_status"
	MUSTER_6_CHECKBOX_KURATIV                        FormKey_MUSTER_6 = "checkbox_kurativ"
	MUSTER_6_CHECKBOX_PRAVENTIV                      FormKey_MUSTER_6 = "checkbox_praventiv"
	MUSTER_6_CHECKBOX_BEHANDL                        FormKey_MUSTER_6 = "checkbox_behandl"
	MUSTER_6_CHECKBOX_BEI_BELEGARZTL                 FormKey_MUSTER_6 = "checkbox_bei_belegarztl"
	MUSTER_6_CHECKBOX_UNFALL                         FormKey_MUSTER_6 = "checkbox_unfall"
	MUSTER_6_CHECKBOX_AUSFUHRUNG                     FormKey_MUSTER_6 = "checkbox_ausfuhrung"
	MUSTER_6_CHECKBOX_KONSILIAR                      FormKey_MUSTER_6 = "checkbox_konsiliar"
	MUSTER_6_CHECKBOX_MIT_WEITER                     FormKey_MUSTER_6 = "checkbox_mit_weiter"
	MUSTER_6_CHECKBOX_EINGESCHRANKTER                FormKey_MUSTER_6 = "checkbox_eingeschrankter"
	MUSTER_6_TEXTBOX_BEFUND_LINE1                    FormKey_MUSTER_6 = "textbox_befund_line1"
	MUSTER_6_TEXTBOX_BEFUND_LINE2                    FormKey_MUSTER_6 = "textbox_befund_line2"
	MUSTER_6_TEXTBOX_AUFTRAG_LINE1                   FormKey_MUSTER_6 = "textbox_auftrag_line1"
	MUSTER_6_TEXTBOX_AUFTRAG_LINE2                   FormKey_MUSTER_6 = "textbox_auftrag_line2"
	MUSTER_6_TEXTBOX_AUFTRAG_LINE3                   FormKey_MUSTER_6 = "textbox_auftrag_line3"
	MUSTER_6_TEXTBOX_AUFTRAG_LINE4                   FormKey_MUSTER_6 = "textbox_auftrag_line4"
	MUSTER_6_DATE_LABEL_CUSTOM_DER_OP                FormKey_MUSTER_6 = "date_label_custom_der_op"
	MUSTER_6_LABEL_DATE_LABEL_CUSTOM_DER_OP_NO_SPACE FormKey_MUSTER_6 = "label_date_label_custom_der_op_no_space"
	MUSTER_6_DATE_LABEL_CUSTOM_AU_BIS                FormKey_MUSTER_6 = "date_label_custom_au_bis"
	MUSTER_6_LABEL_DATE_LABEL_CUSTOM_AU_BIS_NO_SPACE FormKey_MUSTER_6 = "label_date_label_custom_au_bis_no_space"
	MUSTER_6_TEXTBOX_UBERWEISUNG                     FormKey_MUSTER_6 = "textbox_uberweisung"
	MUSTER_6_LABEL_DOCTOR_STAMP                      FormKey_MUSTER_6 = "label_doctor_stamp"
	MUSTER_6_LABEL_REFERRAL_THROUGH_TSS              FormKey_MUSTER_6 = "label_referral_through_tss"
	MUSTER_6_LABEL_GESCHLECHT                        FormKey_MUSTER_6 = "label_geschlecht"
	MUSTER_6_LABEL_DATE_QUARTAL                      FormKey_MUSTER_6 = "label_date_quartal"
	MUSTER_6_DATE_QUARTAL                            FormKey_MUSTER_6 = "date_quartal"
	MUSTER_6_CHECKBOX_LABEL_DATE_QUARTAL_QUARTER     FormKey_MUSTER_6 = "checkbox_label_date_quartal_quarter"
	MUSTER_6_LABEL_DATE_QUARTAL_YEAR                 FormKey_MUSTER_6 = "label_date_quartal_year"
)

// This code was autogenerated, do not edit
type FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 string

const (
	AWH_01_CHECKLISTE_SOMATIK_V1_LABEL_INSURANCE_NAME                     FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "label_insurance_name"
	AWH_01_CHECKLISTE_SOMATIK_V1_LABEL_PATIENTINFO_LINE2                  FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "label_patientInfo_line2"
	AWH_01_CHECKLISTE_SOMATIK_V1_LABEL_PATIENTINFO_LINE3                  FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "label_patientInfo_line3"
	AWH_01_CHECKLISTE_SOMATIK_V1_LABEL_PATIENTINFO_LINE1                  FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "label_patientInfo_line1"
	AWH_01_CHECKLISTE_SOMATIK_V1_LABEL_PATIENTINFO_LINE4                  FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "label_patientInfo_line4"
	AWH_01_CHECKLISTE_SOMATIK_V1_LABEL_IK_NUMBER                          FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "label_ik_number"
	AWH_01_CHECKLISTE_SOMATIK_V1_LABEL_BSNR                               FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "label_bsnr"
	AWH_01_CHECKLISTE_SOMATIK_V1_LABEL_DATE_OF_BIRTH                      FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "label_date_of_birth"
	AWH_01_CHECKLISTE_SOMATIK_V1_LABEL_INSURANCE_NUMBER                   FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "label_insurance_number"
	AWH_01_CHECKLISTE_SOMATIK_V1_LABEL_INSURANCE_STATUS                   FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "label_insurance_status"
	AWH_01_CHECKLISTE_SOMATIK_V1_LABEL_LANR                               FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "label_lanr"
	AWH_01_CHECKLISTE_SOMATIK_V1_LABEL_WOP                                FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "label_wop"
	AWH_01_CHECKLISTE_SOMATIK_V1_LABEL_INSURANCE_END_DATE                 FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "label_insurance_end_date"
	AWH_01_CHECKLISTE_SOMATIK_V1_CHECKBOX_VORSORGEBEDARF_RIGHT            FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "checkbox_vorsorgebedarf_right"
	AWH_01_CHECKLISTE_SOMATIK_V1_CHECKBOX_VORSORGEBEDARF_LEFT             FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "checkbox_vorsorgebedarf_left"
	AWH_01_CHECKLISTE_SOMATIK_V1_DATE_PRESCRIBE                           FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "date_prescribe"
	AWH_01_CHECKLISTE_SOMATIK_V1_DATE_CREATEDDATE                         FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "date_createddate"
	AWH_01_CHECKLISTE_SOMATIK_V1_CHECKBOX_KONTAKTAUFNAHME                 FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "checkbox_kontaktaufnahme"
	AWH_01_CHECKLISTE_SOMATIK_V1_CHECKBOX_OHNESYSTEMISCHEREAKTION         FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "checkbox_ohnesystemischereaktion"
	AWH_01_CHECKLISTE_SOMATIK_V1_CHECKBOX_ZUNEHMENDE_SYSTEMISCHE_REAKTION FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "checkbox_zunehmende_systemische_reaktion"
	AWH_01_CHECKLISTE_SOMATIK_V1_CHECKBOX_INTERMITTIERENDE_SYMPTOMATIK    FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "checkbox_intermittierende_symptomatik"
	AWH_01_CHECKLISTE_SOMATIK_V1_CHECKBOX_PERSISTIERENDE_SYMPTOMATIK      FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "checkbox_persistierende_symptomatik"
	AWH_01_CHECKLISTE_SOMATIK_V1_CHECKBOX_LEICHTE_BIS_MODERATE_EKZEME     FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "checkbox_leichte_bis_moderate_ekzeme"
	AWH_01_CHECKLISTE_SOMATIK_V1_CHECKBOX_SCHWERE_EKZEME                  FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "checkbox_schwere_ekzeme"
	AWH_01_CHECKLISTE_SOMATIK_V1_CHECKBOX_WIEDERHOLTE_OBSTRUKTIVE         FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "checkbox_wiederholte_obstruktive"
	AWH_01_CHECKLISTE_SOMATIK_V1_CHECKBOX_CHRONISCH_REZIDIVIERENDE        FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "checkbox_chronisch_rezidivierende"
	AWH_01_CHECKLISTE_SOMATIK_V1_CHECKBOX_UBERGEWICHT                     FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "checkbox_ubergewicht"
	AWH_01_CHECKLISTE_SOMATIK_V1_CHECKBOX_ADIPOSITAS                      FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "checkbox_adipositas"
	AWH_01_CHECKLISTE_SOMATIK_V1_LABEL_DOCTOR_STAMP                       FormKey_AWH_01_CHECKLISTE_SOMATIK_V1 = "label_doctor_stamp"
)

// This code was autogenerated
type FormKey_MUSTER_13 string

const (
	MUSTER_13_LABEL_PATIENTINFO_LINE2_0        FormKey_MUSTER_13 = "label_patientInfo_line2_0"
	MUSTER_13_LABEL_PATIENTINFO_LINE3_0        FormKey_MUSTER_13 = "label_patientInfo_line3_0"
	MUSTER_13_LABEL_PATIENTINFO_LINE4_0        FormKey_MUSTER_13 = "label_patientInfo_line4_0"
	MUSTER_13_LABEL_DATE_OF_BIRTH_0            FormKey_MUSTER_13 = "label_date_of_birth_0"
	MUSTER_13_LABEL_BSNR_0                     FormKey_MUSTER_13 = "label_bsnr_0"
	MUSTER_13_LABEL_INSURANCE_STATUS_0         FormKey_MUSTER_13 = "label_insurance_status_0"
	MUSTER_13_LABEL_LANR_0                     FormKey_MUSTER_13 = "label_lanr_0"
	MUSTER_13_DATE_PRESCRIBE_0                 FormKey_MUSTER_13 = "date_prescribe_0"
	MUSTER_13_CHECKBOX_PHYSIOTHERAPIE_0        FormKey_MUSTER_13 = "checkbox_physiotherapie_0"
	MUSTER_13_CHECKBOX_PODOLOGISCHE_0          FormKey_MUSTER_13 = "checkbox_podologische_0"
	MUSTER_13_CHECKBOX_SCHLUCKTHERAPIE_0       FormKey_MUSTER_13 = "checkbox_schlucktherapie_0"
	MUSTER_13_CHECKBOX_ERGOTHERAPIE_0          FormKey_MUSTER_13 = "checkbox_ergotherapie_0"
	MUSTER_13_CHECKBOX_ERNAHRUNGSTHERAPIE_0    FormKey_MUSTER_13 = "checkbox_ernahrungstherapie_0"
	MUSTER_13_LABEL_ICD_CODE10_LINE1_0         FormKey_MUSTER_13 = "label_icd_code10_line1_0"
	MUSTER_13_LABEL_ICD_CODE10_LINE2_0         FormKey_MUSTER_13 = "label_icd_code10_line2_0"
	MUSTER_13_LABEL_DIAGNOSE_GRUPPE_0          FormKey_MUSTER_13 = "label_diagnose_gruppe_0"
	MUSTER_13_CHECKBOX_LEITSYMPTOMATIK_A_0     FormKey_MUSTER_13 = "checkbox_leitsymptomatik_a_0"
	MUSTER_13_CHECKBOX_LEITSYMPTOMATIK_B_0     FormKey_MUSTER_13 = "checkbox_leitsymptomatik_b_0"
	MUSTER_13_CHECKBOX_LEITSYMPTOMATIK_C_0     FormKey_MUSTER_13 = "checkbox_leitsymptomatik_c_0"
	MUSTER_13_LABEL_BVB_0                      FormKey_MUSTER_13 = "label_bvb_0"
	MUSTER_13_LABEL_EDIT_BUTTON_0              FormKey_MUSTER_13 = "label_edit_button_0"
	MUSTER_13_CHECKBOX_PATIENTENINDIVIDUELLE_0 FormKey_MUSTER_13 = "checkbox_patientenindividuelle_0"
	MUSTER_13_LABEL_WOP_0                      FormKey_MUSTER_13 = "label_wop_0"
	MUSTER_13_LABEL_INSURANCE_END_DATE_0       FormKey_MUSTER_13 = "label_insurance_end_date_0"
	MUSTER_13_CHECKBOX_ZUZAHLUNGSFREI_0        FormKey_MUSTER_13 = "checkbox_zuzahlungsfrei_0"
	MUSTER_13_CHECKBOX_ZUZAHLUNGPFLICHT_0      FormKey_MUSTER_13 = "checkbox_zuzahlungpflicht_0"
	MUSTER_13_CHECKBOX_UNFALLFOLGEN_0          FormKey_MUSTER_13 = "checkbox_unfallfolgen_0"
	MUSTER_13_CHECKBOX_BVG_0                   FormKey_MUSTER_13 = "checkbox_bvg_0"
	MUSTER_13_TEXTBOX_HEILMITTEL_LINE1_0       FormKey_MUSTER_13 = "textbox_heilmittel_line1_0"
	MUSTER_13_TEXTBOX_HEILMITTEL_LINE2_0       FormKey_MUSTER_13 = "textbox_heilmittel_line2_0"
	MUSTER_13_TEXTBOX_HEILMITTEL_LINE3_0       FormKey_MUSTER_13 = "textbox_heilmittel_line3_0"
	MUSTER_13_TEXTBOX_BEHANDLUNG_LINE1_0       FormKey_MUSTER_13 = "textbox_behandlung_line1_0"
	MUSTER_13_TEXTBOX_BEHANDLUNG_LINE2_0       FormKey_MUSTER_13 = "textbox_behandlung_line2_0"
	MUSTER_13_TEXTBOX_BEHANDLUNG_LINE3_0       FormKey_MUSTER_13 = "textbox_behandlung_line3_0"
	MUSTER_13_AREA_TEXTBOX_STANDARD_0          FormKey_MUSTER_13 = "area_textbox_standard_0"
	MUSTER_13_CHECKBOX_THERAPIEBERICHT_0       FormKey_MUSTER_13 = "checkbox_therapiebericht_0"
	MUSTER_13_CHECKBOX_HAUSBESUCH_JA_0         FormKey_MUSTER_13 = "checkbox_hausbesuch_ja_0"
	MUSTER_13_CHECKBOX_HAUSBESUCH_NEIN_0       FormKey_MUSTER_13 = "checkbox_hausbesuch_nein_0"
	MUSTER_13_AREA_TEXTBOX_THERAPIEZIELE_0     FormKey_MUSTER_13 = "area_textbox_therapieziele_0"
	MUSTER_13_LABEL_THERAPIE_FREQUENZ_0        FormKey_MUSTER_13 = "label_therapie_frequenz_0"
	MUSTER_13_CHECKBOX_DRINGLICHER_14_TAGEN_0  FormKey_MUSTER_13 = "checkbox_dringlicher_14_tagen_0"
	MUSTER_13_BARCODE                          FormKey_MUSTER_13 = "barcode"
	MUSTER_13_LABEL_DOCTOR_STAMP_0             FormKey_MUSTER_13 = "label_doctor_stamp_0"
	MUSTER_13_TEXTBOX_ERGANZENDES_0            FormKey_MUSTER_13 = "textbox_erganzendes_0"
	MUSTER_13_TEXTBOX_BEHANDLUNG_LINE4_0       FormKey_MUSTER_13 = "textbox_behandlung_line4_0"
	MUSTER_13_AREA_TEXTBOX_LEITSYMTOMATIK_0    FormKey_MUSTER_13 = "area_textbox_leitsymtomatik_0"
	MUSTER_13_LABEL_INSURANCE_NAME_0           FormKey_MUSTER_13 = "label_insurance_name_0"
	MUSTER_13_LABEL_IK_NUMBER_0                FormKey_MUSTER_13 = "label_ik_number_0"
	MUSTER_13_LABEL_IK_NUMBER_1                FormKey_MUSTER_13 = "label_ik_number_1"
	MUSTER_13_LABEL_INSURANCE_NUMBER_0         FormKey_MUSTER_13 = "label_insurance_number_0"
	MUSTER_13_LABEL_INSURANCE_NUMBER_1         FormKey_MUSTER_13 = "label_insurance_number_1"
	MUSTER_13_LABEL_PATIENTINFO_LINE1_0        FormKey_MUSTER_13 = "label_patientInfo_line1_0"
	MUSTER_13_LABEL_PATIENTINFO_LINE1_1        FormKey_MUSTER_13 = "label_patientInfo_line1_1"
	MUSTER_13_AREA_TEXTBOX_ICD_CODE10_0        FormKey_MUSTER_13 = "area_textbox_icd_code10_0"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_15 string

const (
	MUSTER_15_CHECKBOX_YES_0             FormKey_MUSTER_15 = "checkbox_yes_0"
	MUSTER_15_CHECKBOX_NO_0              FormKey_MUSTER_15 = "checkbox_no_0"
	MUSTER_15_CHECKBOX_NORMAL_0          FormKey_MUSTER_15 = "checkbox_normal_0"
	MUSTER_15_CHECKBOX_OPERATIV1_0       FormKey_MUSTER_15 = "checkbox_operativ1_0"
	MUSTER_15_CHECKBOX_ENG1_0            FormKey_MUSTER_15 = "checkbox_eng1_0"
	MUSTER_15_CHECKBOX_TROMMELFELL1_0    FormKey_MUSTER_15 = "checkbox_trommelfell1_0"
	MUSTER_15_CHECKBOX_DURCH1_0          FormKey_MUSTER_15 = "checkbox_durch1_0"
	MUSTER_15_CHECKBOX_FEUCHT1_0         FormKey_MUSTER_15 = "checkbox_feucht1_0"
	MUSTER_15_CHECKBOX_NORMAL2_0         FormKey_MUSTER_15 = "checkbox_normal2_0"
	MUSTER_15_CHECKBOX_OPERATIVE2_0      FormKey_MUSTER_15 = "checkbox_operative2_0"
	MUSTER_15_CHECKBOX_ENG2_0            FormKey_MUSTER_15 = "checkbox_eng2_0"
	MUSTER_15_CHECKBOX_TROMMELFELL2_0    FormKey_MUSTER_15 = "checkbox_trommelfell2_0"
	MUSTER_15_CHECKBOX_DURCH2_0          FormKey_MUSTER_15 = "checkbox_durch2_0"
	MUSTER_15_CHECKBOX_FEUCHT2_0         FormKey_MUSTER_15 = "checkbox_feucht2_0"
	MUSTER_15_CHECKBOX_RECHTS_0          FormKey_MUSTER_15 = "checkbox_rechts_0"
	MUSTER_15_CHECKBOX_LINKS_0           FormKey_MUSTER_15 = "checkbox_links_0"
	MUSTER_15_CHECKBOX_BEIDERSEITS_0     FormKey_MUSTER_15 = "checkbox_beiderseits_0"
	MUSTER_15_TEXTBOX_KHZ_0              FormKey_MUSTER_15 = "textbox_khz_0"
	MUSTER_15_TEXTBOX_DB1_0              FormKey_MUSTER_15 = "textbox_db1_0"
	MUSTER_15_TEXTBOX_DB2_0              FormKey_MUSTER_15 = "textbox_db2_0"
	MUSTER_15_CHECKBOX_UNFALL_0          FormKey_MUSTER_15 = "checkbox_unfall_0"
	MUSTER_15_CHECKBOX_VERSORGUNGS_0     FormKey_MUSTER_15 = "checkbox_versorgungs_0"
	MUSTER_15_LABEL_DOCTOR_STAMP_0       FormKey_MUSTER_15 = "label_doctor_stamp_0"
	MUSTER_15_TEXTBOX_YESLINE2_0         FormKey_MUSTER_15 = "textbox_yesline2_0"
	MUSTER_15_LABEL_TAKEOVER_DIAGNOSIS_0 FormKey_MUSTER_15 = "label_takeover_diagnosis_0"
	MUSTER_15_TEXTBOX_DIAGNOSE_LINE1_0   FormKey_MUSTER_15 = "textbox_diagnose_line1_0"
	MUSTER_15_TEXTBOX_DIAGNOSE_LINE2_0   FormKey_MUSTER_15 = "textbox_diagnose_line2_0"
	MUSTER_15_TEXTBOX_LINKS_0            FormKey_MUSTER_15 = "textbox_links_0"
	MUSTER_15_LABEL_PRF_NR_0             FormKey_MUSTER_15 = "label_prf_nr_0"
	MUSTER_15_LABEL_PRF_NR_1             FormKey_MUSTER_15 = "label_prf_nr_1"
	MUSTER_15_LABEL_IK_NUMBER_1          FormKey_MUSTER_15 = "label_ik_number_1"
	MUSTER_15_LABEL_INSURANCE_NUMBER_1   FormKey_MUSTER_15 = "label_insurance_number_1"
	MUSTER_15_LABEL_PATIENT_FULLNAME_1   FormKey_MUSTER_15 = "label_patient_fullname_1"
	MUSTER_15_TEXTBOX_BELE_0             FormKey_MUSTER_15 = "textbox_bele_0"
	MUSTER_15_TEXTBOX_IK_0               FormKey_MUSTER_15 = "textbox_ik_0"
	MUSTER_15_TEXTBOX_RECH_0             FormKey_MUSTER_15 = "textbox_rech_0"
	MUSTER_15_TEXTBOX_YES_LINE1_0        FormKey_MUSTER_15 = "textbox_yes_line1_0"
	MUSTER_15_TEXTBOX_YES_LINE2_0        FormKey_MUSTER_15 = "textbox_yes_line2_0"
	MUSTER_15_LABEL_PATIENTINFO_LINE2_0  FormKey_MUSTER_15 = "label_patientInfo_line2_0"
	MUSTER_15_LABEL_PATIENTINFO_LINE1_0  FormKey_MUSTER_15 = "label_patientInfo_line1_0"
	MUSTER_15_LABEL_DATE_OF_BIRTH_0      FormKey_MUSTER_15 = "label_date_of_birth_0"
	MUSTER_15_LABEL_INSURANCE_NAME_0     FormKey_MUSTER_15 = "label_insurance_name_0"
	MUSTER_15_LABEL_WOP_0                FormKey_MUSTER_15 = "label_wop_0"
	MUSTER_15_LABEL_PATIENTINFO_LINE3_0  FormKey_MUSTER_15 = "label_patientInfo_line3_0"
	MUSTER_15_LABEL_PATIENTINFO_LINE4_0  FormKey_MUSTER_15 = "label_patientInfo_line4_0"
	MUSTER_15_LABEL_IK_NUMBER_0          FormKey_MUSTER_15 = "label_ik_number_0"
	MUSTER_15_LABEL_BSNR_0               FormKey_MUSTER_15 = "label_bsnr_0"
	MUSTER_15_LABEL_INSURANCE_NUMBER_0   FormKey_MUSTER_15 = "label_insurance_number_0"
	MUSTER_15_LABEL_LANR_0               FormKey_MUSTER_15 = "label_lanr_0"
	MUSTER_15_DATE_PRESCRIBE_0           FormKey_MUSTER_15 = "date_prescribe_0"
	MUSTER_15_LABEL_INSURANCE_END_DATE_0 FormKey_MUSTER_15 = "label_insurance_end_date_0"
	MUSTER_15_LABEL_INSURANCE_STATUS_0   FormKey_MUSTER_15 = "label_insurance_status_0"
	MUSTER_15_BARCODE_0                  FormKey_MUSTER_15 = "barcode_0"
)

// This code was autogenerated, do not edit
type FormKey_TREZEPTMUSTER string

const (
	TREZEPTMUSTER_CHECKBOX_GEBUHRFREI            FormKey_TREZEPTMUSTER = "checkbox_gebuhrfrei"
	TREZEPTMUSTER_CHECKBOX_GEBPFL                FormKey_TREZEPTMUSTER = "checkbox_gebpfl"
	TREZEPTMUSTER_CHECKBOX_NOCTU                 FormKey_TREZEPTMUSTER = "checkbox_noctu"
	TREZEPTMUSTER_CHECKBOX_SONSTIGE              FormKey_TREZEPTMUSTER = "checkbox_sonstige"
	TREZEPTMUSTER_LABEL_MEDICATION               FormKey_TREZEPTMUSTER = "label_medication"
	TREZEPTMUSTER_CHECKBOX_BVG                   FormKey_TREZEPTMUSTER = "checkbox_bvg"
	TREZEPTMUSTER_CHECKBOX_AUTIDEM1              FormKey_TREZEPTMUSTER = "checkbox_autIdem1"
	TREZEPTMUSTER_CHECKBOX_ALLE                  FormKey_TREZEPTMUSTER = "checkbox_alle"
	TREZEPTMUSTER_CHECKBOX_DEM                   FormKey_TREZEPTMUSTER = "checkbox_dem"
	TREZEPTMUSTER_CHECKBOX_BEHANDLUNG_ERFOLGT_AU FormKey_TREZEPTMUSTER = "checkbox_behandlung_erfolgt_au"
	TREZEPTMUSTER_CHECKBOX_BEHANDLUNG_ERFOLGT_IN FormKey_TREZEPTMUSTER = "checkbox_behandlung_erfolgt_in"
	TREZEPTMUSTER_LABEL_DOCTOR_STAMP             FormKey_TREZEPTMUSTER = "label_doctor_stamp"
	TREZEPTMUSTER_LABEL_PATIENTINFO_LINE2        FormKey_TREZEPTMUSTER = "label_patientInfo_line2"
	TREZEPTMUSTER_LABEL_PATIENTINFO_LINE3        FormKey_TREZEPTMUSTER = "label_patientInfo_line3"
	TREZEPTMUSTER_LABEL_PATIENTINFO_LINE1        FormKey_TREZEPTMUSTER = "label_patientInfo_line1"
	TREZEPTMUSTER_LABEL_PATIENTINFO_LINE4        FormKey_TREZEPTMUSTER = "label_patientInfo_line4"
	TREZEPTMUSTER_LABEL_IK_NUMBER                FormKey_TREZEPTMUSTER = "label_ik_number"
	TREZEPTMUSTER_LABEL_BSNR                     FormKey_TREZEPTMUSTER = "label_bsnr"
	TREZEPTMUSTER_LABEL_INSURANCE_NUMBER         FormKey_TREZEPTMUSTER = "label_insurance_number"
	TREZEPTMUSTER_LABEL_INSURANCE_STATUS         FormKey_TREZEPTMUSTER = "label_insurance_status"
	TREZEPTMUSTER_LABEL_LANR                     FormKey_TREZEPTMUSTER = "label_lanr"
	TREZEPTMUSTER_DATE_PRESCRIBE                 FormKey_TREZEPTMUSTER = "date_prescribe"
	TREZEPTMUSTER_LABEL_DATE_OF_BIRTH            FormKey_TREZEPTMUSTER = "label_date_of_birth"
	TREZEPTMUSTER_LABEL_INSURANCE_NAME           FormKey_TREZEPTMUSTER = "label_insurance_name"
	TREZEPTMUSTER_LABEL_WOP                      FormKey_TREZEPTMUSTER = "label_wop"
	TREZEPTMUSTER_LABEL_INSURANCE_END_DATE       FormKey_TREZEPTMUSTER = "label_insurance_end_date"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_16 string

const (
	MUSTER_16_CHECKBOX_GEBUHRFREI        FormKey_MUSTER_16 = "checkbox_gebuhrfrei"
	MUSTER_16_LABEL_INSURANCE_NAME       FormKey_MUSTER_16 = "label_insurance_name"
	MUSTER_16_TOGGLE_6_BVG               FormKey_MUSTER_16 = "toggle_6_bvg"
	MUSTER_16_TOGGLE_7_HILFSMITTEL       FormKey_MUSTER_16 = "toggle_7_hilfsmittel"
	MUSTER_16_TOGGLE_9_BEDARF            FormKey_MUSTER_16 = "toggle_9_bedarf"
	MUSTER_16_CHECKBOX_BEGR              FormKey_MUSTER_16 = "checkbox_begr"
	MUSTER_16_TOGGLE_8_IMPFSTOFF         FormKey_MUSTER_16 = "toggle_8_impfstoff"
	MUSTER_16_CHECKBOX_GEBPFL            FormKey_MUSTER_16 = "checkbox_gebpfl"
	MUSTER_16_CHECKBOX_NOCTU             FormKey_MUSTER_16 = "checkbox_noctu"
	MUSTER_16_LABEL_PATIENTINFO_LINE2    FormKey_MUSTER_16 = "label_patientInfo_line2"
	MUSTER_16_LABEL_PATIENTINFO_LINE3    FormKey_MUSTER_16 = "label_patientInfo_line3"
	MUSTER_16_CHECKBOX_SONSTIGE          FormKey_MUSTER_16 = "checkbox_sonstige"
	MUSTER_16_LABEL_PATIENTINFO_LINE1    FormKey_MUSTER_16 = "label_patientInfo_line1"
	MUSTER_16_LABEL_PATIENTINFO_LINE4    FormKey_MUSTER_16 = "label_patientInfo_line4"
	MUSTER_16_LABEL_IK_NUMBER            FormKey_MUSTER_16 = "label_ik_number"
	MUSTER_16_CHECKBOX_UNFALL            FormKey_MUSTER_16 = "checkbox_unfall"
	MUSTER_16_LABEL_BSNR                 FormKey_MUSTER_16 = "label_bsnr"
	MUSTER_16_CHECKBOX_ARBEITSUNFALL     FormKey_MUSTER_16 = "checkbox_arbeitsunfall"
	MUSTER_16_LABEL_KBVCERTIFICATENUMBER FormKey_MUSTER_16 = "label_KBVCertificateNumber"
	MUSTER_16_CHECKBOX_AUTIDEM1          FormKey_MUSTER_16 = "checkbox_autIdem1"
	MUSTER_16_CHECKBOX_AUTIDEM2          FormKey_MUSTER_16 = "checkbox_autIdem2"
	MUSTER_16_CHECKBOX_AUTIDEM3          FormKey_MUSTER_16 = "checkbox_autIdem3"
	MUSTER_16_LABEL_MEDICATION           FormKey_MUSTER_16 = "label_medication"
	MUSTER_16_LABEL_UNFALLTAG            FormKey_MUSTER_16 = "label_unfalltag"
	MUSTER_16_LABEL_UNFALLBETRIEBNUMMER  FormKey_MUSTER_16 = "label_unfallbetriebNummer"
	MUSTER_16_LABEL_DATE_OF_BIRTH        FormKey_MUSTER_16 = "label_date_of_birth"
	MUSTER_16_LABEL_INSURANCE_NUMBER     FormKey_MUSTER_16 = "label_insurance_number"
	MUSTER_16_LABEL_INSURANCE_STATUS     FormKey_MUSTER_16 = "label_insurance_status"
	MUSTER_16_LABEL_LANR                 FormKey_MUSTER_16 = "label_lanr"
	MUSTER_16_DATE_PRESCRIBE             FormKey_MUSTER_16 = "date_prescribe"
	MUSTER_16_LABEL_DOCTOR_STAMP         FormKey_MUSTER_16 = "label_doctor_stamp"
	MUSTER_16_LABEL_WOP                  FormKey_MUSTER_16 = "label_wop"
	MUSTER_16_LABEL_INSURANCE_END_DATE   FormKey_MUSTER_16 = "label_insurance_end_date"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_3A string

const (
	MUSTER_3A_LABEL_DOCTOR_STAMP                               FormKey_MUSTER_3A = "label_doctor_stamp"
	MUSTER_3A_TEXTBOX_BESONDERE_LINE1                          FormKey_MUSTER_3A = "textbox_besondere_line1"
	MUSTER_3A_BARCODE                                          FormKey_MUSTER_3A = "barcode"
	MUSTER_3A_TEXTBOX_BESONDERE_LINE3                          FormKey_MUSTER_3A = "textbox_besondere_line3"
	MUSTER_3A_LABEL_INSURANCE_NAME                             FormKey_MUSTER_3A = "label_insurance_name"
	MUSTER_3A_LABEL_PATIENTINFO_LINE1                          FormKey_MUSTER_3A = "label_patientInfo_line1"
	MUSTER_3A_LABEL_PATIENTINFO_LINE2                          FormKey_MUSTER_3A = "label_patientInfo_line2"
	MUSTER_3A_LABEL_DATE_OF_BIRTH                              FormKey_MUSTER_3A = "label_date_of_birth"
	MUSTER_3A_LABEL_PATIENTINFO_LINE3                          FormKey_MUSTER_3A = "label_patientInfo_line3"
	MUSTER_3A_LABEL_PATIENTINFO_LINE4                          FormKey_MUSTER_3A = "label_patientInfo_line4"
	MUSTER_3A_LABEL_INSURANCE_NUMBER                           FormKey_MUSTER_3A = "label_insurance_number"
	MUSTER_3A_LABEL_INSURANCE_STATUS                           FormKey_MUSTER_3A = "label_insurance_status"
	MUSTER_3A_DATE_PRESCRIBE                                   FormKey_MUSTER_3A = "date_prescribe"
	MUSTER_3A_LABEL_LANR                                       FormKey_MUSTER_3A = "label_lanr"
	MUSTER_3A_LABEL_BSNR                                       FormKey_MUSTER_3A = "label_bsnr"
	MUSTER_3A_LABEL_IK_NUMBER                                  FormKey_MUSTER_3A = "label_ik_number"
	MUSTER_3A_LABEL_WOP                                        FormKey_MUSTER_3A = "label_wop"
	MUSTER_3A_LABEL_INSURANCE_END_DATE                         FormKey_MUSTER_3A = "label_insurance_end_date"
	MUSTER_3A_TEXTBOX_BESONDERE_LINE2                          FormKey_MUSTER_3A = "textbox_besondere_line2"
	MUSTER_3A_LABEL_PRF_NR                                     FormKey_MUSTER_3A = "label_prf_nr"
	MUSTER_3A_LABEL_DATE_LABEL_CUSTOM_CHILDBIRTH_NO_SPACE      FormKey_MUSTER_3A = "label_date_label_custom_childbirth_no_space"
	MUSTER_3A_LABEL_DATE_LABEL_CUSTOM_CERTIFICATEDATE_NO_SPACE FormKey_MUSTER_3A = "label_date_label_custom_certificateDate_no_space"
	MUSTER_3A_DATE_LABEL_CUSTOM_CHILDBIRTH                     FormKey_MUSTER_3A = "date_label_custom_childbirth"
	MUSTER_3A_DATE_LABEL_CUSTOM_CERTIFICATEDATE                FormKey_MUSTER_3A = "date_label_custom_certificateDate"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_9 string

const (
	MUSTER_9_TEXTBOX_NAME                                    FormKey_MUSTER_9 = "textbox_name"
	MUSTER_9_TEXTBOX_STRABEHAUSNR                            FormKey_MUSTER_9 = "textbox_strabeHausNr"
	MUSTER_9_TEXTBOX_PLZ                                     FormKey_MUSTER_9 = "textbox_plz"
	MUSTER_9_TEXTBOX_WOHNORT                                 FormKey_MUSTER_9 = "textbox_wohnort"
	MUSTER_9_LABEL_DOCTOR_STAMP                              FormKey_MUSTER_9 = "label_doctor_stamp"
	MUSTER_9_CHECKBOX_BIRTHWEIGHTUNTER                       FormKey_MUSTER_9 = "checkbox_birthWeightUnter"
	MUSTER_9_CHECKBOX_BIRTHWEIGHTAB                          FormKey_MUSTER_9 = "checkbox_birthWeightAb"
	MUSTER_9_CHECKBOX_BIRTHWEIGHTABTOTGEBURT                 FormKey_MUSTER_9 = "checkbox_birthWeightAbTotgeburt"
	MUSTER_9_CHECKBOX_HASDISABILITY                          FormKey_MUSTER_9 = "checkbox_hasDisability"
	MUSTER_9_BARCODE                                         FormKey_MUSTER_9 = "barcode"
	MUSTER_9_LABEL_PATIENTINFO_LINE2                         FormKey_MUSTER_9 = "label_patientInfo_line2"
	MUSTER_9_LABEL_PATIENTINFO_LINE1                         FormKey_MUSTER_9 = "label_patientInfo_line1"
	MUSTER_9_LABEL_DATE_OF_BIRTH                             FormKey_MUSTER_9 = "label_date_of_birth"
	MUSTER_9_LABEL_INSURANCE_NAME                            FormKey_MUSTER_9 = "label_insurance_name"
	MUSTER_9_LABEL_WOP                                       FormKey_MUSTER_9 = "label_wop"
	MUSTER_9_LABEL_PATIENTINFO_LINE3                         FormKey_MUSTER_9 = "label_patientInfo_line3"
	MUSTER_9_LABEL_PATIENTINFO_LINE4                         FormKey_MUSTER_9 = "label_patientInfo_line4"
	MUSTER_9_LABEL_IK_NUMBER                                 FormKey_MUSTER_9 = "label_ik_number"
	MUSTER_9_LABEL_BSNR                                      FormKey_MUSTER_9 = "label_bsnr"
	MUSTER_9_LABEL_INSURANCE_NUMBER                          FormKey_MUSTER_9 = "label_insurance_number"
	MUSTER_9_LABEL_LANR                                      FormKey_MUSTER_9 = "label_lanr"
	MUSTER_9_DATE_PRESCRIBE                                  FormKey_MUSTER_9 = "date_prescribe"
	MUSTER_9_LABEL_INSURANCE_END_DATE                        FormKey_MUSTER_9 = "label_insurance_end_date"
	MUSTER_9_LABEL_INSURANCE_STATUS                          FormKey_MUSTER_9 = "label_insurance_status"
	MUSTER_9_LABEL_DATE_LABEL_CUSTOM_PREMATUREBIRTH_NO_SPACE FormKey_MUSTER_9 = "label_date_label_custom_prematureBirth_no_space"
	MUSTER_9_DATE_LABEL_CUSTOM_PREMATUREBIRTH                FormKey_MUSTER_9 = "date_label_custom_prematureBirth"
	MUSTER_9_LABEL_DATE_LABEL_CUSTOM_GEBURTSDATUM_NO_SPACE   FormKey_MUSTER_9 = "label_date_label_custom_geburtsdatum_no_space"
	MUSTER_9_DATE_LABEL_CUSTOM_GEBURTSDATUM                  FormKey_MUSTER_9 = "date_label_custom_geburtsdatum"
	MUSTER_9_DATE_LABEL_CUSTOM_DATE                          FormKey_MUSTER_9 = "date_label_custom_date"
	MUSTER_9_LABEL_DATE_LABEL_CUSTOM_DATUM_NO_SPACE          FormKey_MUSTER_9 = "label_date_label_custom_datum_no_space"
	MUSTER_9_LABEL_PRF_NR                                    FormKey_MUSTER_9 = "label_prf_nr"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_10_N string

const (
	MUSTER_10_N_CHECKBOX_EMPFANGNISREGELUNG                   FormKey_MUSTER_10_N = "checkbox_empfangnisregelung"
	MUSTER_10_N_DATE_ABNAHMEZEIT                              FormKey_MUSTER_10_N = "date_abnahmezeit"
	MUSTER_10_N_LABEL_DATE_ABNAHMEZEIT                        FormKey_MUSTER_10_N = "label_date_abnahmezeit"
	MUSTER_10_N_TEXTBOX_SSW                                   FormKey_MUSTER_10_N = "textbox_ssw"
	MUSTER_10_N_TEXTBOX_DIAGNOSE                              FormKey_MUSTER_10_N = "textbox_diagnose"
	MUSTER_10_N_CHECKBOX_TELEFON                              FormKey_MUSTER_10_N = "checkbox_telefon"
	MUSTER_10_N_CHECKBOX_FAX                                  FormKey_MUSTER_10_N = "checkbox_fax"
	MUSTER_10_N_TEXTBOX_NR                                    FormKey_MUSTER_10_N = "textbox_nr"
	MUSTER_10_N_TEXTBOX_BETRI                                 FormKey_MUSTER_10_N = "textbox_betri"
	MUSTER_10_N_TEXTBOX_ARZT                                  FormKey_MUSTER_10_N = "textbox_arzt"
	MUSTER_10_N_LABEL_DOCTOR_STAMP                            FormKey_MUSTER_10_N = "label_doctor_stamp"
	MUSTER_10_N_TEXTBOX_BEFUND_LINE1                          FormKey_MUSTER_10_N = "textbox_befund_line1"
	MUSTER_10_N_TEXTBOX_AUFTRAG_LINE1                         FormKey_MUSTER_10_N = "textbox_auftrag_line1"
	MUSTER_10_N_LABEL_DATE_ABNAHMEZEIT_HOUR                   FormKey_MUSTER_10_N = "label_date_abnahmezeit_hour"
	MUSTER_10_N_LABEL_DATE_ABNAHMEZEIT_MINUTE                 FormKey_MUSTER_10_N = "label_date_abnahmezeit_minute"
	MUSTER_10_N_LABEL_TAKEOVER_DIAGNOSIS                      FormKey_MUSTER_10_N = "label_takeover_diagnosis"
	MUSTER_10_N_CHECKBOX_BEFUND_EILT                          FormKey_MUSTER_10_N = "checkbox_befund_eilt"
	MUSTER_10_N_CHECKBOX_KURATIV                              FormKey_MUSTER_10_N = "checkbox_kurativ"
	MUSTER_10_N_CHECKBOX_PRAVENTIV                            FormKey_MUSTER_10_N = "checkbox_praventiv"
	MUSTER_10_N_CHECKBOX_BEHANDL                              FormKey_MUSTER_10_N = "checkbox_behandl"
	MUSTER_10_N_CHECKBOX_UNFALL                               FormKey_MUSTER_10_N = "checkbox_unfall"
	MUSTER_10_N_CHECKBOX_KONTROLLUNTERSUCHUNG                 FormKey_MUSTER_10_N = "checkbox_kontrolluntersuchung"
	MUSTER_10_N_CHECKBOX_EINGESCHRANKTER                      FormKey_MUSTER_10_N = "checkbox_eingeschrankter"
	MUSTER_10_N_LABEL_GENDER                                  FormKey_MUSTER_10_N = "label_gender"
	MUSTER_10_N_TEXTBOX_KNAPPSCHAFTS_KENNZIFFER               FormKey_MUSTER_10_N = "textbox_knappschafts_kennziffer"
	MUSTER_10_N_LABEL_DATE_LABEL_CUSTOM_ABNAHMEDATUM_NO_SPACE FormKey_MUSTER_10_N = "label_date_label_custom_abnahmedatum_no_space"
	MUSTER_10_N_DATE_LABEL_CUSTOM_ABNAHMEDATUM                FormKey_MUSTER_10_N = "date_label_custom_abnahmedatum"
	MUSTER_10_N_LABEL_DATE_QUARTAL                            FormKey_MUSTER_10_N = "label_date_quartal"
	MUSTER_10_N_DATE_QUARTAL                                  FormKey_MUSTER_10_N = "date_quartal"
	MUSTER_10_N_CHECKBOX_LABEL_DATE_QUARTAL_QUARTER           FormKey_MUSTER_10_N = "checkbox_label_date_quartal_quarter"
	MUSTER_10_N_LABEL_DATE_QUARTAL_YEAR                       FormKey_MUSTER_10_N = "label_date_quartal_year"
	MUSTER_10_N_BARCODE                                       FormKey_MUSTER_10_N = "barcode"
	MUSTER_10_N_LABEL_PRF_NR                                  FormKey_MUSTER_10_N = "label_prf_nr"
	MUSTER_10_N_LABEL_PATIENTINFO_LINE2                       FormKey_MUSTER_10_N = "label_patientInfo_line2"
	MUSTER_10_N_LABEL_PATIENTINFO_LINE1                       FormKey_MUSTER_10_N = "label_patientInfo_line1"
	MUSTER_10_N_LABEL_DATE_OF_BIRTH                           FormKey_MUSTER_10_N = "label_date_of_birth"
	MUSTER_10_N_LABEL_INSURANCE_NAME                          FormKey_MUSTER_10_N = "label_insurance_name"
	MUSTER_10_N_LABEL_WOP                                     FormKey_MUSTER_10_N = "label_wop"
	MUSTER_10_N_LABEL_PATIENTINFO_LINE3                       FormKey_MUSTER_10_N = "label_patientInfo_line3"
	MUSTER_10_N_LABEL_PATIENTINFO_LINE4                       FormKey_MUSTER_10_N = "label_patientInfo_line4"
	MUSTER_10_N_LABEL_IK_NUMBER                               FormKey_MUSTER_10_N = "label_ik_number"
	MUSTER_10_N_LABEL_BSNR                                    FormKey_MUSTER_10_N = "label_bsnr"
	MUSTER_10_N_LABEL_INSURANCE_NUMBER                        FormKey_MUSTER_10_N = "label_insurance_number"
	MUSTER_10_N_LABEL_LANR                                    FormKey_MUSTER_10_N = "label_lanr"
	MUSTER_10_N_DATE_PRESCRIBE                                FormKey_MUSTER_10_N = "date_prescribe"
	MUSTER_10_N_LABEL_INSURANCE_END_DATE                      FormKey_MUSTER_10_N = "label_insurance_end_date"
	MUSTER_10_N_LABEL_INSURANCE_STATUS                        FormKey_MUSTER_10_N = "label_insurance_status"
	MUSTER_10_N_TEXTBOX_BEFUND_LINE2                          FormKey_MUSTER_10_N = "textbox_befund_line2"
	MUSTER_10_N_TEXTBOX_AUFTRAG_LINE2                         FormKey_MUSTER_10_N = "textbox_auftrag_line2"
	MUSTER_10_N_TEXTBOX_AUFTRAG_LINE3                         FormKey_MUSTER_10_N = "textbox_auftrag_line3"
	MUSTER_10_N_TEXTBOX_BETRI_COMP                            FormKey_MUSTER_10_N = "textbox_betri_comp"
	MUSTER_10_N_TEXTBOX_ARZT_COMP                             FormKey_MUSTER_10_N = "textbox_arzt_comp"
	MUSTER_10_N_TEXTBOX_SSW_COMP                              FormKey_MUSTER_10_N = "textbox_ssw_comp"
	MUSTER_10_N_TEXTBOX_KNAPPSCHAFTS_KENNZIFFER_COMP          FormKey_MUSTER_10_N = "textbox_knappschafts_kennziffer_comp"
	MUSTER_10_N_CHECKBOX_SER                                  FormKey_MUSTER_10_N = "checkbox_ser"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_10A_N string

const (
	MUSTER_10A_N_LABEL_INSURANCE_NAME                          FormKey_MUSTER_10A_N = "label_insurance_name"
	MUSTER_10A_N_LABEL_PATIENTINFO_LINE3                       FormKey_MUSTER_10A_N = "label_patientInfo_line3"
	MUSTER_10A_N_LABEL_PATIENTINFO_LINE2                       FormKey_MUSTER_10A_N = "label_patientInfo_line2"
	MUSTER_10A_N_LABEL_PATIENTINFO_LINE1                       FormKey_MUSTER_10A_N = "label_patientInfo_line1"
	MUSTER_10A_N_LABEL_PATIENTINFO_LINE4                       FormKey_MUSTER_10A_N = "label_patientInfo_line4"
	MUSTER_10A_N_LABEL_DATE_OF_BIRTH                           FormKey_MUSTER_10A_N = "label_date_of_birth"
	MUSTER_10A_N_LABEL_IK_NUMBER                               FormKey_MUSTER_10A_N = "label_ik_number"
	MUSTER_10A_N_LABEL_BSNR                                    FormKey_MUSTER_10A_N = "label_bsnr"
	MUSTER_10A_N_LABEL_INSURANCE_NUMBER                        FormKey_MUSTER_10A_N = "label_insurance_number"
	MUSTER_10A_N_LABEL_LANR                                    FormKey_MUSTER_10A_N = "label_lanr"
	MUSTER_10A_N_DATE_PRESCRIBE                                FormKey_MUSTER_10A_N = "date_prescribe"
	MUSTER_10A_N_CHECKBOX_KURATIV                              FormKey_MUSTER_10A_N = "checkbox_kurativ"
	MUSTER_10A_N_CHECKBOX_PRAVENTIV                            FormKey_MUSTER_10A_N = "checkbox_praventiv"
	MUSTER_10A_N_CHECKBOX_BEHANDL                              FormKey_MUSTER_10A_N = "checkbox_behandl"
	MUSTER_10A_N_CHECKBOX_UNFALL                               FormKey_MUSTER_10A_N = "checkbox_unfall"
	MUSTER_10A_N_DATE_ABNAHMEZEIT                              FormKey_MUSTER_10A_N = "date_abnahmezeit"
	MUSTER_10A_N_LABEL_DATE_ABNAHMEZEIT                        FormKey_MUSTER_10A_N = "label_date_abnahmezeit"
	MUSTER_10A_N_TEXTBOX_KNAPPSCHAFTS_KENNZIFFER               FormKey_MUSTER_10A_N = "textbox_knappschafts_kennziffer"
	MUSTER_10A_N_LABEL_TEXTBOX_KNAPPSCHAFTS_KENNZIFFER         FormKey_MUSTER_10A_N = "label_textbox_knappschafts_kennziffer"
	MUSTER_10A_N_TEXTBOX_SSW                                   FormKey_MUSTER_10A_N = "textbox_ssw"
	MUSTER_10A_N_TEXTBOX_ZUSAT                                 FormKey_MUSTER_10A_N = "textbox_zusat"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_1                         FormKey_MUSTER_10A_N = "label_checkbox_exam-1"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_2                         FormKey_MUSTER_10A_N = "label_checkbox_exam-2"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_3                         FormKey_MUSTER_10A_N = "label_checkbox_exam-3"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_4                         FormKey_MUSTER_10A_N = "label_checkbox_exam-4"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_5                         FormKey_MUSTER_10A_N = "label_checkbox_exam-5"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_6                         FormKey_MUSTER_10A_N = "label_checkbox_exam-6"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_13                        FormKey_MUSTER_10A_N = "label_checkbox_exam-13"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_62                        FormKey_MUSTER_10A_N = "label_checkbox_exam-62"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_63                        FormKey_MUSTER_10A_N = "label_checkbox_exam-63"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_64                        FormKey_MUSTER_10A_N = "label_checkbox_exam-64"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_14                        FormKey_MUSTER_10A_N = "label_checkbox_exam-14"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_15                        FormKey_MUSTER_10A_N = "label_checkbox_exam-15"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_16                        FormKey_MUSTER_10A_N = "label_checkbox_exam-16"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_17                        FormKey_MUSTER_10A_N = "label_checkbox_exam-17"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_18                        FormKey_MUSTER_10A_N = "label_checkbox_exam-18"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_19                        FormKey_MUSTER_10A_N = "label_checkbox_exam-19"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_20                        FormKey_MUSTER_10A_N = "label_checkbox_exam-20"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_21                        FormKey_MUSTER_10A_N = "label_checkbox_exam-21"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_23                        FormKey_MUSTER_10A_N = "label_checkbox_exam-23"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_24                        FormKey_MUSTER_10A_N = "label_checkbox_exam-24"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_25                        FormKey_MUSTER_10A_N = "label_checkbox_exam-25"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_26                        FormKey_MUSTER_10A_N = "label_checkbox_exam-26"
	MUSTER_10A_N_LABEL_TEXTBOX_SSW                             FormKey_MUSTER_10A_N = "label_textbox_ssw"
	MUSTER_10A_N_LABEL_GENDER                                  FormKey_MUSTER_10A_N = "label_gender"
	MUSTER_10A_N_LABEL_HOVER_EXAM_1                            FormKey_MUSTER_10A_N = "label_hover_exam-1"
	MUSTER_10A_N_LABEL_HOVER_EXAM_2                            FormKey_MUSTER_10A_N = "label_hover_exam-2"
	MUSTER_10A_N_LABEL_HOVER_EXAM_3                            FormKey_MUSTER_10A_N = "label_hover_exam-3"
	MUSTER_10A_N_LABEL_HOVER_EXAM_4                            FormKey_MUSTER_10A_N = "label_hover_exam-4"
	MUSTER_10A_N_LABEL_HOVER_EXAM_5                            FormKey_MUSTER_10A_N = "label_hover_exam-5"
	MUSTER_10A_N_LABEL_HOVER_EXAM_6                            FormKey_MUSTER_10A_N = "label_hover_exam-6"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_42                        FormKey_MUSTER_10A_N = "label_checkbox_exam-42"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_44                        FormKey_MUSTER_10A_N = "label_checkbox_exam-44"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_45                        FormKey_MUSTER_10A_N = "label_checkbox_exam-45"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_31                        FormKey_MUSTER_10A_N = "label_checkbox_exam-31"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_32                        FormKey_MUSTER_10A_N = "label_checkbox_exam-32"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_34                        FormKey_MUSTER_10A_N = "label_checkbox_exam-34"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_35                        FormKey_MUSTER_10A_N = "label_checkbox_exam-35"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_36                        FormKey_MUSTER_10A_N = "label_checkbox_exam-36"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_37                        FormKey_MUSTER_10A_N = "label_checkbox_exam-37"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_38                        FormKey_MUSTER_10A_N = "label_checkbox_exam-38"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_39                        FormKey_MUSTER_10A_N = "label_checkbox_exam-39"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_40                        FormKey_MUSTER_10A_N = "label_checkbox_exam-40"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_41                        FormKey_MUSTER_10A_N = "label_checkbox_exam-41"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_43                        FormKey_MUSTER_10A_N = "label_checkbox_exam-43"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_46                        FormKey_MUSTER_10A_N = "label_checkbox_exam-46"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_48                        FormKey_MUSTER_10A_N = "label_checkbox_exam-48"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_49                        FormKey_MUSTER_10A_N = "label_checkbox_exam-49"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_47                        FormKey_MUSTER_10A_N = "label_checkbox_exam-47"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_51                        FormKey_MUSTER_10A_N = "label_checkbox_exam-51"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_53                        FormKey_MUSTER_10A_N = "label_checkbox_exam-53"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_54                        FormKey_MUSTER_10A_N = "label_checkbox_exam-54"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_52                        FormKey_MUSTER_10A_N = "label_checkbox_exam-52"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_27                        FormKey_MUSTER_10A_N = "label_checkbox_exam-27"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_29                        FormKey_MUSTER_10A_N = "label_checkbox_exam-29"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_30                        FormKey_MUSTER_10A_N = "label_checkbox_exam-30"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_28                        FormKey_MUSTER_10A_N = "label_checkbox_exam-28"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_55                        FormKey_MUSTER_10A_N = "label_checkbox_exam-55"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_58                        FormKey_MUSTER_10A_N = "label_checkbox_exam-58"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_60                        FormKey_MUSTER_10A_N = "label_checkbox_exam-60"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_56                        FormKey_MUSTER_10A_N = "label_checkbox_exam-56"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_8                         FormKey_MUSTER_10A_N = "label_checkbox_exam-8"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_9                         FormKey_MUSTER_10A_N = "label_checkbox_exam-9"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_10                        FormKey_MUSTER_10A_N = "label_checkbox_exam-10"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_11                        FormKey_MUSTER_10A_N = "label_checkbox_exam-11"
	MUSTER_10A_N_LABEL_CHECKBOX_EXAM_61                        FormKey_MUSTER_10A_N = "label_checkbox_exam-61"
	MUSTER_10A_N_LABEL_HOVER_EXAM_62                           FormKey_MUSTER_10A_N = "label_hover_exam-62"
	MUSTER_10A_N_LABEL_HOVER_EXAM_63                           FormKey_MUSTER_10A_N = "label_hover_exam-63"
	MUSTER_10A_N_LABEL_HOVER_EXAM_64                           FormKey_MUSTER_10A_N = "label_hover_exam-64"
	MUSTER_10A_N_LABEL_HOVER_EXAM_13                           FormKey_MUSTER_10A_N = "label_hover_exam-13"
	MUSTER_10A_N_LABEL_HOVER_EXAM_14                           FormKey_MUSTER_10A_N = "label_hover_exam-14"
	MUSTER_10A_N_LABEL_HOVER_EXAM_15                           FormKey_MUSTER_10A_N = "label_hover_exam-15"
	MUSTER_10A_N_LABEL_HOVER_EXAM_16                           FormKey_MUSTER_10A_N = "label_hover_exam-16"
	MUSTER_10A_N_LABEL_HOVER_EXAM_17                           FormKey_MUSTER_10A_N = "label_hover_exam-17"
	MUSTER_10A_N_LABEL_HOVER_EXAM_18                           FormKey_MUSTER_10A_N = "label_hover_exam-18"
	MUSTER_10A_N_LABEL_HOVER_EXAM_19                           FormKey_MUSTER_10A_N = "label_hover_exam-19"
	MUSTER_10A_N_LABEL_HOVER_EXAM_20                           FormKey_MUSTER_10A_N = "label_hover_exam-20"
	MUSTER_10A_N_LABEL_HOVER_EXAM_21                           FormKey_MUSTER_10A_N = "label_hover_exam-21"
	MUSTER_10A_N_LABEL_HOVER_EXAM_23                           FormKey_MUSTER_10A_N = "label_hover_exam-23"
	MUSTER_10A_N_LABEL_HOVER_EXAM_24                           FormKey_MUSTER_10A_N = "label_hover_exam-24"
	MUSTER_10A_N_LABEL_HOVER_EXAM_27                           FormKey_MUSTER_10A_N = "label_hover_exam-27"
	MUSTER_10A_N_LABEL_HOVER_EXAM_25                           FormKey_MUSTER_10A_N = "label_hover_exam-25"
	MUSTER_10A_N_LABEL_HOVER_EXAM_26                           FormKey_MUSTER_10A_N = "label_hover_exam-26"
	MUSTER_10A_N_LABEL_HOVER_EXAM_28                           FormKey_MUSTER_10A_N = "label_hover_exam-28"
	MUSTER_10A_N_LABEL_HOVER_EXAM_29                           FormKey_MUSTER_10A_N = "label_hover_exam-29"
	MUSTER_10A_N_LABEL_HOVER_EXAM_30                           FormKey_MUSTER_10A_N = "label_hover_exam-30"
	MUSTER_10A_N_LABEL_HOVER_EXAM_31                           FormKey_MUSTER_10A_N = "label_hover_exam-31"
	MUSTER_10A_N_LABEL_HOVER_EXAM_32                           FormKey_MUSTER_10A_N = "label_hover_exam-32"
	MUSTER_10A_N_LABEL_HOVER_EXAM_34                           FormKey_MUSTER_10A_N = "label_hover_exam-34"
	MUSTER_10A_N_LABEL_HOVER_EXAM_35                           FormKey_MUSTER_10A_N = "label_hover_exam-35"
	MUSTER_10A_N_LABEL_HOVER_EXAM_36                           FormKey_MUSTER_10A_N = "label_hover_exam-36"
	MUSTER_10A_N_LABEL_HOVER_EXAM_37                           FormKey_MUSTER_10A_N = "label_hover_exam-37"
	MUSTER_10A_N_LABEL_HOVER_EXAM_38                           FormKey_MUSTER_10A_N = "label_hover_exam-38"
	MUSTER_10A_N_LABEL_HOVER_EXAM_39                           FormKey_MUSTER_10A_N = "label_hover_exam-39"
	MUSTER_10A_N_LABEL_HOVER_EXAM_41                           FormKey_MUSTER_10A_N = "label_hover_exam-41"
	MUSTER_10A_N_LABEL_HOVER_EXAM_40                           FormKey_MUSTER_10A_N = "label_hover_exam-40"
	MUSTER_10A_N_LABEL_HOVER_EXAM_42                           FormKey_MUSTER_10A_N = "label_hover_exam-42"
	MUSTER_10A_N_LABEL_HOVER_EXAM_43                           FormKey_MUSTER_10A_N = "label_hover_exam-43"
	MUSTER_10A_N_LABEL_HOVER_EXAM_44                           FormKey_MUSTER_10A_N = "label_hover_exam-44"
	MUSTER_10A_N_LABEL_HOVER_EXAM_46                           FormKey_MUSTER_10A_N = "label_hover_exam-46"
	MUSTER_10A_N_LABEL_HOVER_EXAM_47                           FormKey_MUSTER_10A_N = "label_hover_exam-47"
	MUSTER_10A_N_LABEL_HOVER_EXAM_48                           FormKey_MUSTER_10A_N = "label_hover_exam-48"
	MUSTER_10A_N_LABEL_HOVER_EXAM_51                           FormKey_MUSTER_10A_N = "label_hover_exam-51"
	MUSTER_10A_N_LABEL_HOVER_EXAM_52                           FormKey_MUSTER_10A_N = "label_hover_exam-52"
	MUSTER_10A_N_LABEL_HOVER_EXAM_53                           FormKey_MUSTER_10A_N = "label_hover_exam-53"
	MUSTER_10A_N_LABEL_HOVER_EXAM_49                           FormKey_MUSTER_10A_N = "label_hover_exam-49"
	MUSTER_10A_N_LABEL_HOVER_EXAM_54                           FormKey_MUSTER_10A_N = "label_hover_exam-54"
	MUSTER_10A_N_LABEL_HOVER_EXAM_45                           FormKey_MUSTER_10A_N = "label_hover_exam-45"
	MUSTER_10A_N_LABEL_HOVER_EXAM_55                           FormKey_MUSTER_10A_N = "label_hover_exam-55"
	MUSTER_10A_N_LABEL_HOVER_EXAM_56                           FormKey_MUSTER_10A_N = "label_hover_exam-56"
	MUSTER_10A_N_LABEL_HOVER_EXAM_58                           FormKey_MUSTER_10A_N = "label_hover_exam-58"
	MUSTER_10A_N_LABEL_HOVER_EXAM_60                           FormKey_MUSTER_10A_N = "label_hover_exam-60"
	MUSTER_10A_N_LABEL_HOVER_EXAM_8                            FormKey_MUSTER_10A_N = "label_hover_exam-8"
	MUSTER_10A_N_LABEL_HOVER_EXAM_9                            FormKey_MUSTER_10A_N = "label_hover_exam-9"
	MUSTER_10A_N_LABEL_HOVER_EXAM_10                           FormKey_MUSTER_10A_N = "label_hover_exam-10"
	MUSTER_10A_N_LABEL_HOVER_EXAM_11                           FormKey_MUSTER_10A_N = "label_hover_exam-11"
	MUSTER_10A_N_LABEL_HOVER_EXAM_61                           FormKey_MUSTER_10A_N = "label_hover_exam-61"
	MUSTER_10A_N_LABEL_WOP                                     FormKey_MUSTER_10A_N = "label_wop"
	MUSTER_10A_N_LABEL_INSURANCE_END_DATE                      FormKey_MUSTER_10A_N = "label_insurance_end_date"
	MUSTER_10A_N_BARCODE                                       FormKey_MUSTER_10A_N = "barcode"
	MUSTER_10A_N_LABEL_DATE_ABNAHMEZEIT_HOUR                   FormKey_MUSTER_10A_N = "label_date_abnahmezeit_hour"
	MUSTER_10A_N_LABEL_DATE_ABNAHMEZEIT_MINUTE                 FormKey_MUSTER_10A_N = "label_date_abnahmezeit_minute"
	MUSTER_10A_N_LABEL_INSURANCE_STATUS                        FormKey_MUSTER_10A_N = "label_insurance_status"
	MUSTER_10A_N_TEXTBOX_EXAM61_LINE1                          FormKey_MUSTER_10A_N = "textbox_exam61_line1"
	MUSTER_10A_N_TEXTBOX_EXAM61_LINE2                          FormKey_MUSTER_10A_N = "textbox_exam61_line2"
	MUSTER_10A_N_LABEL_PRF_NR                                  FormKey_MUSTER_10A_N = "label_prf_nr"
	MUSTER_10A_N_LABEL_DATE_LABEL_CUSTOM_ABNAHMEDATUM_NO_SPACE FormKey_MUSTER_10A_N = "label_date_label_custom_abnahmedatum_no_space"
	MUSTER_10A_N_DATE_LABEL_CUSTOM_ABNAHMEDATUM                FormKey_MUSTER_10A_N = "date_label_custom_abnahmedatum"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_12A string

const (
	MUSTER_12A_CHECKBOX_ERST_0                             FormKey_MUSTER_12A = "checkbox_erst_0"
	MUSTER_12A_CHECKBOX_FOLGE_0                            FormKey_MUSTER_12A = "checkbox_folge_0"
	MUSTER_12A_CHECKBOX_HERRICHTEN_0                       FormKey_MUSTER_12A = "checkbox_herrichten_0"
	MUSTER_12A_CHECKBOX_INTRAMUSKULAR_0                    FormKey_MUSTER_12A = "checkbox_intramuskular_0"
	MUSTER_12A_CHECKBOX_SUBKUTAN_0                         FormKey_MUSTER_12A = "checkbox_subkutan_0"
	MUSTER_12A_CHECKBOX_ERSTODERNEUEINSTELLUNG_0           FormKey_MUSTER_12A = "checkbox_erstOderNeueinstellung_0"
	MUSTER_12A_CHECKBOX_BEIINTENSIVIERTERINSULINTHERAPIE_0 FormKey_MUSTER_12A = "checkbox_beiIntensivierterInsulintherapie_0"
	MUSTER_12A_CHECKBOX_STUTZENDE_0                        FormKey_MUSTER_12A = "checkbox_stutzende_0"
	MUSTER_12A_CHECKBOX_STUTZENDE1_0                       FormKey_MUSTER_12A = "checkbox_stutzende1_0"
	MUSTER_12A_CHECKBOX_GRUNDPFLEGE_0                      FormKey_MUSTER_12A = "checkbox_grundpflege_0"
	MUSTER_12A_CHECKBOX_HAUSWIRTSCHAFTLICHE_0              FormKey_MUSTER_12A = "checkbox_hauswirtschaftliche_0"
	MUSTER_12A_CHECKBOX_GRUNDPFLEGE1_0                     FormKey_MUSTER_12A = "checkbox_grundpflege1_0"
	MUSTER_12A_CHECKBOX_HAUSWIRTSCHAFTLICHE1_0             FormKey_MUSTER_12A = "checkbox_hauswirtschaftliche1_0"
	MUSTER_12A_LABEL_DOCTOR_STAMP_0                        FormKey_MUSTER_12A = "label_doctor_stamp_0"
	MUSTER_12A_CHECKBOX_HERRICHTENDERMEDIKAMENTENBOX_0     FormKey_MUSTER_12A = "checkbox_herrichtenDerMedikamentenbox_0"
	MUSTER_12A_CHECKBOX_MEDIKAMENTENGABE_0                 FormKey_MUSTER_12A = "checkbox_medikamentengabe_0"
	MUSTER_12A_CHECKBOX_INJEKTIONEN_0                      FormKey_MUSTER_12A = "checkbox_injektionen_0"
	MUSTER_12A_CHECKBOX_RECHTS_0                           FormKey_MUSTER_12A = "checkbox_rechts_0"
	MUSTER_12A_CHECKBOX_AUSZIHEN2_0                        FormKey_MUSTER_12A = "checkbox_auszihen2_0"
	MUSTER_12A_CHECKBOX_ABNEHMEN2_0                        FormKey_MUSTER_12A = "checkbox_abnehmen2_0"
	MUSTER_12A_CHECKBOX_AUSZIHEN3_0                        FormKey_MUSTER_12A = "checkbox_auszihen3_0"
	MUSTER_12A_CHECKBOX_ABNEHMEN3_0                        FormKey_MUSTER_12A = "checkbox_abnehmen3_0"
	MUSTER_12A_CHECKBOX_LINKS_0                            FormKey_MUSTER_12A = "checkbox_links_0"
	MUSTER_12A_CHECKBOX_BEIDSEITS_0                        FormKey_MUSTER_12A = "checkbox_beidseits_0"
	MUSTER_12A_CHECKBOX_AUSZIEHEN1_0                       FormKey_MUSTER_12A = "checkbox_ausziehen1_0"
	MUSTER_12A_CHECKBOX_ABNEHMEN1_0                        FormKey_MUSTER_12A = "checkbox_abnehmen1_0"
	MUSTER_12A_CHECKBOX_WUNDVERSORGUNGAKUT_0               FormKey_MUSTER_12A = "checkbox_wundversorgungAkut_0"
	MUSTER_12A_CHECKBOX_WUNDVERSORGUNGAKUT1_0              FormKey_MUSTER_12A = "checkbox_wundversorgungAkut1_0"
	MUSTER_12A_CHECKBOX_WUNDVERSORGUNGCHRONISCH_0          FormKey_MUSTER_12A = "checkbox_wundversorgungChronisch_0"
	MUSTER_12A_CHECKBOX_UNTERSTUTZUNGSPFLEGE_0             FormKey_MUSTER_12A = "checkbox_unterstutzungspflege_0"
	MUSTER_12A_CHECKBOX_KRANKENHAUSVERMEIDUNGSPFLEGE_0     FormKey_MUSTER_12A = "checkbox_krankenhausvermeidungspflege_0"
	MUSTER_12A_TEXTBOX_TGL_LINE1_0                         FormKey_MUSTER_12A = "textbox_tgl_line1_0"
	MUSTER_12A_TEXTBOX_TGL_LINE2_0                         FormKey_MUSTER_12A = "textbox_tgl_line2_0"
	MUSTER_12A_TEXTBOX_TGL_LINE3_0                         FormKey_MUSTER_12A = "textbox_tgl_line3_0"
	MUSTER_12A_TEXTBOX_TGL_LINE4_0                         FormKey_MUSTER_12A = "textbox_tgl_line4_0"
	MUSTER_12A_TEXTBOX_TGL_LINE5_0                         FormKey_MUSTER_12A = "textbox_tgl_line5_0"
	MUSTER_12A_TEXTBOX_TGL_LINE6_0                         FormKey_MUSTER_12A = "textbox_tgl_line6_0"
	MUSTER_12A_TEXTBOX_TGL_LINE7_0                         FormKey_MUSTER_12A = "textbox_tgl_line7_0"
	MUSTER_12A_TEXTBOX_WTL_LINE1_0                         FormKey_MUSTER_12A = "textbox_wtl_line1_0"
	MUSTER_12A_TEXTBOX_MTL_LINE1_0                         FormKey_MUSTER_12A = "textbox_mtl_line1_0"
	MUSTER_12A_TEXTBOX_WTL_LINE2_0                         FormKey_MUSTER_12A = "textbox_wtl_line2_0"
	MUSTER_12A_TEXTBOX_MTL_LINE2_0                         FormKey_MUSTER_12A = "textbox_mtl_line2_0"
	MUSTER_12A_TEXTBOX_WTL_LINE3_0                         FormKey_MUSTER_12A = "textbox_wtl_line3_0"
	MUSTER_12A_TEXTBOX_MTL_LINE3_0                         FormKey_MUSTER_12A = "textbox_mtl_line3_0"
	MUSTER_12A_TEXTBOX_WTL_LINE4_0                         FormKey_MUSTER_12A = "textbox_wtl_line4_0"
	MUSTER_12A_TEXTBOX_MTL_LINE4_0                         FormKey_MUSTER_12A = "textbox_mtl_line4_0"
	MUSTER_12A_TEXTBOX_WTL_LINE5_0                         FormKey_MUSTER_12A = "textbox_wtl_line5_0"
	MUSTER_12A_TEXTBOX_MTL_LINE5_0                         FormKey_MUSTER_12A = "textbox_mtl_line5_0"
	MUSTER_12A_TEXTBOX_WTL_LINE6_0                         FormKey_MUSTER_12A = "textbox_wtl_line6_0"
	MUSTER_12A_TEXTBOX_MTL_LINE6_0                         FormKey_MUSTER_12A = "textbox_mtl_line6_0"
	MUSTER_12A_TEXTBOX_WTL_LINE7_0                         FormKey_MUSTER_12A = "textbox_wtl_line7_0"
	MUSTER_12A_TEXTBOX_MTL_LINE7_0                         FormKey_MUSTER_12A = "textbox_mtl_line7_0"
	MUSTER_12A_TEXTBOX_TGL_LINE9_0                         FormKey_MUSTER_12A = "textbox_tgl_line9_0"
	MUSTER_12A_TEXTBOX_TGL_LINE8_0                         FormKey_MUSTER_12A = "textbox_tgl_line8_0"
	MUSTER_12A_TEXTBOX_WTL_LINE8_0                         FormKey_MUSTER_12A = "textbox_wtl_line8_0"
	MUSTER_12A_TEXTBOX_WTL_LINE9_0                         FormKey_MUSTER_12A = "textbox_wtl_line9_0"
	MUSTER_12A_TEXTBOX_MTL_LINE9_0                         FormKey_MUSTER_12A = "textbox_mtl_line9_0"
	MUSTER_12A_TEXTBOX_MTL_LINE8_0                         FormKey_MUSTER_12A = "textbox_mtl_line8_0"
	MUSTER_12A_TEXTBOX_TGL_LINE11_0                        FormKey_MUSTER_12A = "textbox_tgl_line11_0"
	MUSTER_12A_TEXTBOX_TGL_LINE10_0                        FormKey_MUSTER_12A = "textbox_tgl_line10_0"
	MUSTER_12A_TEXTBOX_WTL_LINE10_0                        FormKey_MUSTER_12A = "textbox_wtl_line10_0"
	MUSTER_12A_TEXTBOX_WTL_LINE11_0                        FormKey_MUSTER_12A = "textbox_wtl_line11_0"
	MUSTER_12A_TEXTBOX_MTL_LINE11_0                        FormKey_MUSTER_12A = "textbox_mtl_line11_0"
	MUSTER_12A_TEXTBOX_MTL_LINE10_0                        FormKey_MUSTER_12A = "textbox_mtl_line10_0"
	MUSTER_12A_TEXTBOX_ICD10_CODE2_0                       FormKey_MUSTER_12A = "textbox_icd10_code2_0"
	MUSTER_12A_TEXTBOX_ICD10_CODE4_0                       FormKey_MUSTER_12A = "textbox_icd10_code4_0"
	MUSTER_12A_TEXTBOX_ICD10_CODE1_0                       FormKey_MUSTER_12A = "textbox_icd10_code1_0"
	MUSTER_12A_TEXTBOX_ICD10_CODE3_0                       FormKey_MUSTER_12A = "textbox_icd10_code3_0"
	MUSTER_12A_DATE_DAUERVOM_LINE1_0                       FormKey_MUSTER_12A = "date_dauerVom_line1_0"
	MUSTER_12A_DATE_DAUERBIS_LINE1_0                       FormKey_MUSTER_12A = "date_dauerBis_line1_0"
	MUSTER_12A_DATE_DAUERVOM_LINE2_0                       FormKey_MUSTER_12A = "date_dauerVom_line2_0"
	MUSTER_12A_DATE_DAUERBIS_LINE2_0                       FormKey_MUSTER_12A = "date_dauerBis_line2_0"
	MUSTER_12A_DATE_DAUERVOM_LINE3_0                       FormKey_MUSTER_12A = "date_dauerVom_line3_0"
	MUSTER_12A_DATE_DAUERBIS_LINE3_0                       FormKey_MUSTER_12A = "date_dauerBis_line3_0"
	MUSTER_12A_DATE_DAUERVOM_LINE4_0                       FormKey_MUSTER_12A = "date_dauerVom_line4_0"
	MUSTER_12A_DATE_DAUERBIS_LINE4_0                       FormKey_MUSTER_12A = "date_dauerBis_line4_0"
	MUSTER_12A_DATE_DAUERVOM_LINE5_0                       FormKey_MUSTER_12A = "date_dauerVom_line5_0"
	MUSTER_12A_DATE_DAUERBIS_LINE5_0                       FormKey_MUSTER_12A = "date_dauerBis_line5_0"
	MUSTER_12A_DATE_DAUERBIS_LINE6_0                       FormKey_MUSTER_12A = "date_dauerBis_line6_0"
	MUSTER_12A_DATE_DAUERVOM_LINE7_0                       FormKey_MUSTER_12A = "date_dauerVom_line7_0"
	MUSTER_12A_DATE_DAUERVOM_LINE6_0                       FormKey_MUSTER_12A = "date_dauerVom_line6_0"
	MUSTER_12A_DATE_DAUERBIS_LINE7_0                       FormKey_MUSTER_12A = "date_dauerBis_line7_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERVOM_LINE1_0      FormKey_MUSTER_12A = "label_date_month_date_dauerVom_line1_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERBIS_LINE1_0      FormKey_MUSTER_12A = "label_date_month_date_dauerBis_line1_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERVOM_LINE2_0      FormKey_MUSTER_12A = "label_date_month_date_dauerVom_line2_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERBIS_LINE2_0      FormKey_MUSTER_12A = "label_date_month_date_dauerBis_line2_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERVOM_LINE3_0      FormKey_MUSTER_12A = "label_date_month_date_dauerVom_line3_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERBIS_LINE3_0      FormKey_MUSTER_12A = "label_date_month_date_dauerBis_line3_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERVOM_LINE4_0      FormKey_MUSTER_12A = "label_date_month_date_dauerVom_line4_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERBIS_LINE4_0      FormKey_MUSTER_12A = "label_date_month_date_dauerBis_line4_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERVOM_LINE5_0      FormKey_MUSTER_12A = "label_date_month_date_dauerVom_line5_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERBIS_LINE5_0      FormKey_MUSTER_12A = "label_date_month_date_dauerBis_line5_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERVOM_LINE6_0      FormKey_MUSTER_12A = "label_date_month_date_dauerVom_line6_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERBIS_LINE6_0      FormKey_MUSTER_12A = "label_date_month_date_dauerBis_line6_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERVOM_LINE7_0      FormKey_MUSTER_12A = "label_date_month_date_dauerVom_line7_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERBIS_LINE7_0      FormKey_MUSTER_12A = "label_date_month_date_dauerBis_line7_0"
	MUSTER_12A_DATE_DAUERVOM_LINE9_0                       FormKey_MUSTER_12A = "date_dauerVom_line9_0"
	MUSTER_12A_DATE_DAUERVOM_LINE8_0                       FormKey_MUSTER_12A = "date_dauerVom_line8_0"
	MUSTER_12A_DATE_DAUERBIS_LINE8_0                       FormKey_MUSTER_12A = "date_dauerBis_line8_0"
	MUSTER_12A_DATE_DAUERBIS_LINE9_0                       FormKey_MUSTER_12A = "date_dauerBis_line9_0"
	MUSTER_12A_DATE_DAUERVOM_LINE11_0                      FormKey_MUSTER_12A = "date_dauerVom_line11_0"
	MUSTER_12A_DATE_DAUERVOM_LINE10_0                      FormKey_MUSTER_12A = "date_dauerVom_line10_0"
	MUSTER_12A_DATE_DAUERBIS_LINE10_0                      FormKey_MUSTER_12A = "date_dauerBis_line10_0"
	MUSTER_12A_DATE_DAUERBIS_LINE11_0                      FormKey_MUSTER_12A = "date_dauerBis_line11_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERVOM_LINE9_0      FormKey_MUSTER_12A = "label_date_month_date_dauerVom_line9_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERVOM_LINE8_0      FormKey_MUSTER_12A = "label_date_month_date_dauerVom_line8_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERBIS_LINE8_0      FormKey_MUSTER_12A = "label_date_month_date_dauerBis_line8_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERBIS_LINE9_0      FormKey_MUSTER_12A = "label_date_month_date_dauerBis_line9_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERVOM_LINE11_0     FormKey_MUSTER_12A = "label_date_month_date_dauerVom_line11_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERVOM_LINE10_0     FormKey_MUSTER_12A = "label_date_month_date_dauerVom_line10_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERBIS_LINE10_0     FormKey_MUSTER_12A = "label_date_month_date_dauerBis_line10_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERBIS_LINE11_0     FormKey_MUSTER_12A = "label_date_month_date_dauerBis_line11_0"
	MUSTER_12A_LABEL_TAKEOVER_DIAGNOSIS_0                  FormKey_MUSTER_12A = "label_takeover_diagnosis_0"
	MUSTER_12A_BARCODE                                     FormKey_MUSTER_12A = "barcode"
	MUSTER_12A_LABEL_DATE_LABEL_CUSTOM_VOM_0_NO_SPACE_0    FormKey_MUSTER_12A = "label_date_label_custom_vom_0_no_space_0"
	MUSTER_12A_DATE_LABEL_CUSTOM_VOM_0                     FormKey_MUSTER_12A = "date_label_custom_vom_0"
	MUSTER_12A_LABEL_DATE_LABEL_CUSTOM_BIS_0_NO_SPACE_0    FormKey_MUSTER_12A = "label_date_label_custom_bis_0_no_space_0"
	MUSTER_12A_DATE_LABEL_CUSTOM_BIS_0                     FormKey_MUSTER_12A = "date_label_custom_bis_0"
	MUSTER_12A_TEXTBOX_TGL_LINE12_0                        FormKey_MUSTER_12A = "textbox_tgl_line12_0"
	MUSTER_12A_TEXTBOX_WTL_LINE12_0                        FormKey_MUSTER_12A = "textbox_wtl_line12_0"
	MUSTER_12A_TEXTBOX_MTL_LINE12_0                        FormKey_MUSTER_12A = "textbox_mtl_line12_0"
	MUSTER_12A_DATE_DAUERVOM_LINE12_0                      FormKey_MUSTER_12A = "date_dauerVom_line12_0"
	MUSTER_12A_DATE_DAUERBIS_LINE12_0                      FormKey_MUSTER_12A = "date_dauerBis_line12_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERVOM_LINE12_0     FormKey_MUSTER_12A = "label_date_month_date_dauerVom_line12_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERBIS_LINE12_0     FormKey_MUSTER_12A = "label_date_month_date_dauerBis_line12_0"
	MUSTER_12A_TEXTBOX_TGL_LINE13_0                        FormKey_MUSTER_12A = "textbox_tgl_line13_0"
	MUSTER_12A_TEXTBOX_WTL_LINE13_0                        FormKey_MUSTER_12A = "textbox_wtl_line13_0"
	MUSTER_12A_TEXTBOX_MTL_LINE13_0                        FormKey_MUSTER_12A = "textbox_mtl_line13_0"
	MUSTER_12A_DATE_DAUERVOM_LINE13_0                      FormKey_MUSTER_12A = "date_dauerVom_line13_0"
	MUSTER_12A_DATE_DAUERBIS_LINE13_0                      FormKey_MUSTER_12A = "date_dauerBis_line13_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERVOM_LINE13_0     FormKey_MUSTER_12A = "label_date_month_date_dauerVom_line13_0"
	MUSTER_12A_LABEL_DATE_MONTH_DATE_DAUERBIS_LINE13_0     FormKey_MUSTER_12A = "label_date_month_date_dauerBis_line13_0"
	MUSTER_12A_TEXTBOX_EINSCHRANKUNGEN_LINE1_0             FormKey_MUSTER_12A = "textbox_einschrankungen_line1_0"
	MUSTER_12A_TEXTBOX_EINSCHRANKUNGEN_LINE2_0             FormKey_MUSTER_12A = "textbox_einschrankungen_line2_0"
	MUSTER_12A_TEXTBOX_MEDIKAMENTENGABE1_0                 FormKey_MUSTER_12A = "textbox_medikamentengabe1_0"
	MUSTER_12A_TEXTBOX_MEDIKAMENTENGABE2_0                 FormKey_MUSTER_12A = "textbox_medikamentengabe2_0"
	MUSTER_12A_TEXTBOX_WUNDVERSORGUNG_0                    FormKey_MUSTER_12A = "textbox_wundversorgung_0"
	MUSTER_12A_TEXTBOX_WUNDART_0                           FormKey_MUSTER_12A = "textbox_wundart_0"
	MUSTER_12A_TEXTBOX_LOKALISATION_0                      FormKey_MUSTER_12A = "textbox_lokalisation_0"
	MUSTER_12A_TEXTBOX_LANGEBREITETIEFE_0                  FormKey_MUSTER_12A = "textbox_langeBreiteTiefe_0"
	MUSTER_12A_TEXTBOX_GRAD_0                              FormKey_MUSTER_12A = "textbox_grad_0"
	MUSTER_12A_TEXTBOX_VERBANDMATERIALIEN_0                FormKey_MUSTER_12A = "textbox_verbandmaterialien_0"
	MUSTER_12A_TEXTBOX_SONSTIGE_LINE1_0                    FormKey_MUSTER_12A = "textbox_sonstige_line1_0"
	MUSTER_12A_TEXTBOX_SONSTIGE_LINE2_0                    FormKey_MUSTER_12A = "textbox_sonstige_line2_0"
	MUSTER_12A_CHECKBOX_SONSTIGE_LINE1_0                   FormKey_MUSTER_12A = "checkbox_sonstige_line1_0"
	MUSTER_12A_CHECKBOX_SONSTIGE_LINE2_0                   FormKey_MUSTER_12A = "checkbox_sonstige_line2_0"
	MUSTER_12A_TEXTBOX_GRUNDPFLEGE_0                       FormKey_MUSTER_12A = "textbox_grundpflege_0"
	MUSTER_12A_CHECKBOX_GRUNDPFLEGE2_0                     FormKey_MUSTER_12A = "checkbox_grundpflege2_0"
	MUSTER_12A_TEXTBOX_WEITERE_LINE1_0                     FormKey_MUSTER_12A = "textbox_weitere_line1_0"
	MUSTER_12A_TEXTBOX_WEITERE_LINE2_0                     FormKey_MUSTER_12A = "textbox_weitere_line2_0"
	MUSTER_12A_TEXTBOX_WEITERE_LINE3_0                     FormKey_MUSTER_12A = "textbox_weitere_line3_0"
	MUSTER_12A_TEXTBOX_WEITERE_LINE4_0                     FormKey_MUSTER_12A = "textbox_weitere_line4_0"
	MUSTER_12A_LABEL_PATIENTINFO_LINE1_0                   FormKey_MUSTER_12A = "label_patientInfo_line1_0"
	MUSTER_12A_LABEL_DATE_OF_BIRTH_0                       FormKey_MUSTER_12A = "label_date_of_birth_0"
	MUSTER_12A_LABEL_INSURANCE_NAME_0                      FormKey_MUSTER_12A = "label_insurance_name_0"
	MUSTER_12A_LABEL_WOP_0                                 FormKey_MUSTER_12A = "label_wop_0"
	MUSTER_12A_LABEL_PATIENTINFO_LINE3_0                   FormKey_MUSTER_12A = "label_patientInfo_line3_0"
	MUSTER_12A_LABEL_PATIENTINFO_LINE4_0                   FormKey_MUSTER_12A = "label_patientInfo_line4_0"
	MUSTER_12A_LABEL_IK_NUMBER_0                           FormKey_MUSTER_12A = "label_ik_number_0"
	MUSTER_12A_LABEL_BSNR_0                                FormKey_MUSTER_12A = "label_bsnr_0"
	MUSTER_12A_LABEL_INSURANCE_NUMBER_0                    FormKey_MUSTER_12A = "label_insurance_number_0"
	MUSTER_12A_LABEL_LANR_0                                FormKey_MUSTER_12A = "label_lanr_0"
	MUSTER_12A_DATE_PRESCRIBE_0                            FormKey_MUSTER_12A = "date_prescribe_0"
	MUSTER_12A_LABEL_INSURANCE_END_DATE_0                  FormKey_MUSTER_12A = "label_insurance_end_date_0"
	MUSTER_12A_LABEL_INSURANCE_STATUS_0                    FormKey_MUSTER_12A = "label_insurance_status_0"
	MUSTER_12A_LABEL_PATIENTINFO_LINE2_0                   FormKey_MUSTER_12A = "label_patientInfo_line2_0"
	MUSTER_12A_CHECKBOX_UNFALL_0                           FormKey_MUSTER_12A = "checkbox_unfall_0"
	MUSTER_12A_CHECKBOX_SER_0                              FormKey_MUSTER_12A = "checkbox_ser_0"
	MUSTER_12A_LABEL_PRF_NR_0                              FormKey_MUSTER_12A = "label_prf_nr_0"
	MUSTER_12A_LABEL_IK_NUMBER_1                           FormKey_MUSTER_12A = "label_ik_number_1"
	MUSTER_12A_LABEL_INSURANCE_NUMBER_1                    FormKey_MUSTER_12A = "label_insurance_number_1"
	MUSTER_12A_LABEL_PATIENT_FULLNAME_1                    FormKey_MUSTER_12A = "label_patient_fullname_1"
	MUSTER_12A_LABEL_PRF_NR_1                              FormKey_MUSTER_12A = "label_prf_nr_1"
	MUSTER_12A_CHECKBOX_AUSZIHEN
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_19A string

const (
	MUSTER_19A_LABEL_INSURANCE_NAME                    FormKey_MUSTER_19A = "label_insurance_name"
	MUSTER_19A_LABEL_WOP                               FormKey_MUSTER_19A = "label_wop"
	MUSTER_19A_LABEL_PATIENTINFO_LINE2                 FormKey_MUSTER_19A = "label_patientInfo_line2"
	MUSTER_19A_LABEL_PATIENTINFO_LINE3                 FormKey_MUSTER_19A = "label_patientInfo_line3"
	MUSTER_19A_LABEL_PATIENTINFO_LINE1                 FormKey_MUSTER_19A = "label_patientInfo_line1"
	MUSTER_19A_LABEL_PATIENTINFO_LINE4                 FormKey_MUSTER_19A = "label_patientInfo_line4"
	MUSTER_19A_LABEL_IK_NUMBER                         FormKey_MUSTER_19A = "label_ik_number"
	MUSTER_19A_LABEL_BSNR                              FormKey_MUSTER_19A = "label_bsnr"
	MUSTER_19A_LABEL_DATE_OF_BIRTH                     FormKey_MUSTER_19A = "label_date_of_birth"
	MUSTER_19A_LABEL_INSURANCE_NUMBER                  FormKey_MUSTER_19A = "label_insurance_number"
	MUSTER_19A_LABEL_INSURANCE_STATUS                  FormKey_MUSTER_19A = "label_insurance_status"
	MUSTER_19A_LABEL_LANR                              FormKey_MUSTER_19A = "label_lanr"
	MUSTER_19A_DATE_PRESCRIBE                          FormKey_MUSTER_19A = "date_prescribe"
	MUSTER_19A_CHECKBOX_BEHANDLUNG                     FormKey_MUSTER_19A = "checkbox_behandlung"
	MUSTER_19A_CHECKBOX_BELEGARZTL                     FormKey_MUSTER_19A = "checkbox_belegarztl"
	MUSTER_19A_CHECKBOX_UNFALL                         FormKey_MUSTER_19A = "checkbox_unfall"
	MUSTER_19A_CHECKBOX_ABKLARUNG_SOMATISCHER_URSACHEN FormKey_MUSTER_19A = "checkbox_abklarung-somatischer-ursachen"
	MUSTER_19B_LABEL_W                                 FormKey_MUSTER_19A = "label_w"
	MUSTER_19B_LABEL_M                                 FormKey_MUSTER_19A = "label_m"
	MUSTER_19A_LABEL_DATE_QUARTAL                      FormKey_MUSTER_19A = "label_date_quartal"
	MUSTER_19A_DATE_QUARTAL                            FormKey_MUSTER_19A = "date_quartal"
	MUSTER_19A_TEXTBOX_DIAGNOSE_LINE1                  FormKey_MUSTER_19A = "textbox_diagnose_line1"
	MUSTER_19A_TEXTBOX_DIAGNOSE_LINE2                  FormKey_MUSTER_19A = "textbox_diagnose_line2"
	MUSTER_19A_TEXTBOX_DIAGNOSE_LINE3                  FormKey_MUSTER_19A = "textbox_diagnose_line3"
	MUSTER_19A_TEXTBOX_TAG01                           FormKey_MUSTER_19A = "textbox_tag01"
	MUSTER_19A_TEXTBOX_MONATE01                        FormKey_MUSTER_19A = "textbox_monate01"
	MUSTER_19A_TEXTBOX_OTHER01A                        FormKey_MUSTER_19A = "textbox_other01a"
	MUSTER_19A_TEXTBOX_OTHER01B                        FormKey_MUSTER_19A = "textbox_other01b"
	MUSTER_19A_TEXTBOX_TAG02                           FormKey_MUSTER_19A = "textbox_tag02"
	MUSTER_19A_TEXTBOX_MONATE02                        FormKey_MUSTER_19A = "textbox_monate02"
	MUSTER_19A_TEXTBOX_OTHER02A                        FormKey_MUSTER_19A = "textbox_other02a"
	MUSTER_19A_TEXTBOX_OTHER02B                        FormKey_MUSTER_19A = "textbox_other02b"
	MUSTER_19A_TEXTBOX_TAG03                           FormKey_MUSTER_19A = "textbox_tag03"
	MUSTER_19A_TEXTBOX_MONATE03                        FormKey_MUSTER_19A = "textbox_monate03"
	MUSTER_19A_TEXTBOX_OTHER03A                        FormKey_MUSTER_19A = "textbox_other03a"
	MUSTER_19A_TEXTBOX_OTHER03B                        FormKey_MUSTER_19A = "textbox_other03b"
	MUSTER_19A_TEXTBOX_TAG04                           FormKey_MUSTER_19A = "textbox_tag04"
	MUSTER_19A_TEXTBOX_MONATE04                        FormKey_MUSTER_19A = "textbox_monate04"
	MUSTER_19A_TEXTBOX_OTHER04A                        FormKey_MUSTER_19A = "textbox_other04a"
	MUSTER_19A_TEXTBOX_OTHER04B                        FormKey_MUSTER_19A = "textbox_other04b"
	MUSTER_19A_TEXTBOX_TAG05                           FormKey_MUSTER_19A = "textbox_tag05"
	MUSTER_19A_TEXTBOX_MONATE05                        FormKey_MUSTER_19A = "textbox_monate05"
	MUSTER_19A_TEXTBOX_OTHER05A                        FormKey_MUSTER_19A = "textbox_other05a"
	MUSTER_19A_TEXTBOX_OTHER05B                        FormKey_MUSTER_19A = "textbox_other05b"
	MUSTER_19A_TEXTBOX_TAG06                           FormKey_MUSTER_19A = "textbox_tag06"
	MUSTER_19A_TEXTBOX_MONATE06                        FormKey_MUSTER_19A = "textbox_monate06"
	MUSTER_19A_TEXTBOX_OTHER06A                        FormKey_MUSTER_19A = "textbox_other06a"
	MUSTER_19A_TEXTBOX_OTHER06B                        FormKey_MUSTER_19A = "textbox_other06b"
	MUSTER_19A_TEXTBOX_TAG07                           FormKey_MUSTER_19A = "textbox_tag07"
	MUSTER_19A_TEXTBOX_MONATE07                        FormKey_MUSTER_19A = "textbox_monate07"
	MUSTER_19A_TEXTBOX_OTHER07A                        FormKey_MUSTER_19A = "textbox_other07a"
	MUSTER_19A_TEXTBOX_OTHER07B                        FormKey_MUSTER_19A = "textbox_other07b"
	MUSTER_19A_TEXTBOX_TAG08                           FormKey_MUSTER_19A = "textbox_tag08"
	MUSTER_19A_TEXTBOX_MONATE08                        FormKey_MUSTER_19A = "textbox_monate08"
	MUSTER_19A_TEXTBOX_OTHER08A                        FormKey_MUSTER_19A = "textbox_other08a"
	MUSTER_19A_TEXTBOX_OTHER08B                        FormKey_MUSTER_19A = "textbox_other08b"
	MUSTER_19A_TEXTBOX_TAG09                           FormKey_MUSTER_19A = "textbox_tag09"
	MUSTER_19A_TEXTBOX_MONATE09                        FormKey_MUSTER_19A = "textbox_monate09"
	MUSTER_19A_TEXTBOX_OTHER09A                        FormKey_MUSTER_19A = "textbox_other09a"
	MUSTER_19A_TEXTBOX_OTHER09B                        FormKey_MUSTER_19A = "textbox_other09b"
	MUSTER_19A_TEXTBOX_TAG10                           FormKey_MUSTER_19A = "textbox_tag10"
	MUSTER_19A_TEXTBOX_MONATE10                        FormKey_MUSTER_19A = "textbox_monate10"
	MUSTER_19A_TEXTBOX_OTHER10A                        FormKey_MUSTER_19A = "textbox_other10a"
	MUSTER_19A_TEXTBOX_OTHER10B                        FormKey_MUSTER_19A = "textbox_other10b"
	MUSTER_19A_LABEL_DOCTOR_STAMP                      FormKey_MUSTER_19A = "label_doctor_stamp"
	MUSTER_19A_TEXTBOX_ARBEITSUNFAHIGKEIT              FormKey_MUSTER_19A = "textbox_arbeitsunfahigkeit"
	MUSTER_19A_BARCODE                                 FormKey_MUSTER_19A = "barcode"
	MUSTER_19A_TEXTBOX_BEFUNDE_LINE_1                  FormKey_MUSTER_19A = "textbox_befunde_line_1"
	MUSTER_19A_TEXTBOX_BEFUNDE_LINE_2                  FormKey_MUSTER_19A = "textbox_befunde_line_2"
	MUSTER_19A_TEXTBOX_BEFUNDE_LINE_3                  FormKey_MUSTER_19A = "textbox_befunde_line_3"
	MUSTER_19A_DATE_LABEL_CUSTOM_ARBEITSUNFAHIGKEIT    FormKey_MUSTER_19A = "date_label_custom_arbeitsunfahigkeit"
	MUSTER_19A_TEXTBOX_WEITERARZT                      FormKey_MUSTER_19A = "textbox_teib_b"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_20B string

const (
	MUSTER_20B_TEXTBOX_ZULETZT_LINE1   FormKey_MUSTER_20B = "textbox_zuletzt_line1"
	MUSTER_20B_TEXTBOX_ZULETZT_LINE2   FormKey_MUSTER_20B = "textbox_zuletzt_line2"
	MUSTER_20B_TEXTBOX_ZULETZT_LINE3   FormKey_MUSTER_20B = "textbox_zuletzt_line3"
	MUSTER_20B_TEXTBOX_ZULETZT_STUNDEN FormKey_MUSTER_20B = "textbox_zuletzt_stunden"
	MUSTER_20B_DATE_VOM_LINE1          FormKey_MUSTER_20B = "date_vom_line1"
	MUSTER_20B_DATE_VOM_LINE2          FormKey_MUSTER_20B = "date_vom_line2"
	MUSTER_20B_DATE_VOM_LINE3          FormKey_MUSTER_20B = "date_vom_line3"
	MUSTER_20B_DATE_VOM_LINE4          FormKey_MUSTER_20B = "date_vom_line4"
	MUSTER_20B_DATE_BIS_LINE1          FormKey_MUSTER_20B = "date_bis_line1"
	MUSTER_20B_DATE_BIS_LINE2          FormKey_MUSTER_20B = "date_bis_line2"
	MUSTER_20B_DATE_BIS_LINE3          FormKey_MUSTER_20B = "date_bis_line3"
	MUSTER_20B_DATE_BIS_LINE4          FormKey_MUSTER_20B = "date_bis_line4"
	MUSTER_20B_TEXTBOX_TATIGKEIT_LINE1 FormKey_MUSTER_20B = "textbox_tatigkeit_line1"
	MUSTER_20B_TEXTBOX_TATIGKEIT_LINE2 FormKey_MUSTER_20B = "textbox_tatigkeit_line2"
	MUSTER_20B_TEXTBOX_TATIGKEIT_LINE3 FormKey_MUSTER_20B = "textbox_tatigkeit_line3"
	MUSTER_20B_TEXTBOX_TATIGKEIT_LINE4 FormKey_MUSTER_20B = "textbox_tatigkeit_line4"
	MUSTER_20B_TEXTBOX_TAGLICH_LINE1   FormKey_MUSTER_20B = "textbox_taglich_line1"
	MUSTER_20B_TEXTBOX_TAGLICH_LINE2   FormKey_MUSTER_20B = "textbox_taglich_line2"
	MUSTER_20B_TEXTBOX_TAGLICH_LINE3   FormKey_MUSTER_20B = "textbox_taglich_line3"
	MUSTER_20B_TEXTBOX_TAGLICH_LINE4   FormKey_MUSTER_20B = "textbox_taglich_line4"
	MUSTER_20B_LABEL_DOCTOR_STAMP      FormKey_MUSTER_20B = "label_doctor_stamp"
	MUSTER_20B_LABEL_INSURANCE_NAME    FormKey_MUSTER_20B = "label_insurance_name"
	MUSTER_20B_LABEL_WOP               FormKey_MUSTER_20B = "label_wop"
	MUSTER_20B_LABEL_PATIENTINFO_LINE2 FormKey_MUSTER_20B = "label_patientInfo_line2"
	MUSTER_20B_LABEL_PATIENTINFO_LINE3 FormKey_MUSTER_20B = "label_patientInfo_line3"
	MUSTER_20B_LABEL_PATIENTINFO_LINE1 FormKey_MUSTER_20B = "label_patientInfo_line1"
	MUSTER_20B_LABEL_PATIENTINFO_LINE4 FormKey_MUSTER_20B = "label_patientInfo_line4"
	MUSTER_20B_LABEL_DATE_OF_BIRTH     FormKey_MUSTER_20B = "label_date_of_birth"
	MUSTER_20B_LABEL_IK_NUMBER         FormKey_MUSTER_20B = "label_ik_number"
	MUSTER_20B_LABEL_INSURANCE_NUMBER  FormKey_MUSTER_20B = "label_insurance_number"
	MUSTER_20B_LABEL_BSNR              FormKey_MUSTER_20B = "label_bsnr"
	MUSTER_20B_LABEL_LANR              FormKey_MUSTER_20B = "label_lanr"
	MUSTER_20B_DATE_PRESCRIBE          FormKey_MUSTER_20B = "date_prescribe"
	MUSTER_20B_LABEL_INSURANCE_STATUS  FormKey_MUSTER_20B = "label_insurance_status"
	MUSTER_20B_BARCODE                 FormKey_MUSTER_20B = "barcode"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_20C string

const (
	MUSTER_20C_TEXTBOX_ZULETZT_LINE1   FormKey_MUSTER_20C = "textbox_zuletzt_line1"
	MUSTER_20C_TEXTBOX_ZULETZT_LINE2   FormKey_MUSTER_20C = "textbox_zuletzt_line2"
	MUSTER_20C_TEXTBOX_ZULETZT_LINE3   FormKey_MUSTER_20C = "textbox_zuletzt_line3"
	MUSTER_20C_TEXTBOX_ZULETZT_STUNDEN FormKey_MUSTER_20C = "textbox_zuletzt_stunden"
	MUSTER_20C_DATE_VOM_LINE1          FormKey_MUSTER_20C = "date_vom_line1"
	MUSTER_20C_DATE_VOM_LINE2          FormKey_MUSTER_20C = "date_vom_line2"
	MUSTER_20C_DATE_VOM_LINE3          FormKey_MUSTER_20C = "date_vom_line3"
	MUSTER_20C_DATE_VOM_LINE4          FormKey_MUSTER_20C = "date_vom_line4"
	MUSTER_20C_DATE_BIS_LINE1          FormKey_MUSTER_20C = "date_bis_line1"
	MUSTER_20C_DATE_BIS_LINE2          FormKey_MUSTER_20C = "date_bis_line2"
	MUSTER_20C_DATE_BIS_LINE3          FormKey_MUSTER_20C = "date_bis_line3"
	MUSTER_20C_DATE_BIS_LINE4          FormKey_MUSTER_20C = "date_bis_line4"
	MUSTER_20C_TEXTBOX_TATIGKEIT_LINE1 FormKey_MUSTER_20C = "textbox_tatigkeit_line1"
	MUSTER_20C_TEXTBOX_TATIGKEIT_LINE2 FormKey_MUSTER_20C = "textbox_tatigkeit_line2"
	MUSTER_20C_TEXTBOX_TATIGKEIT_LINE3 FormKey_MUSTER_20C = "textbox_tatigkeit_line3"
	MUSTER_20C_TEXTBOX_TATIGKEIT_LINE4 FormKey_MUSTER_20C = "textbox_tatigkeit_line4"
	MUSTER_20C_TEXTBOX_TAGLICH_LINE1   FormKey_MUSTER_20C = "textbox_taglich_line1"
	MUSTER_20C_TEXTBOX_TAGLICH_LINE2   FormKey_MUSTER_20C = "textbox_taglich_line2"
	MUSTER_20C_TEXTBOX_TAGLICH_LINE3   FormKey_MUSTER_20C = "textbox_taglich_line3"
	MUSTER_20C_TEXTBOX_TAGLICH_LINE4   FormKey_MUSTER_20C = "textbox_taglich_line4"
	MUSTER_20C_LABEL_DOCTOR_STAMP      FormKey_MUSTER_20C = "label_doctor_stamp"
	MUSTER_20C_LABEL_INSURANCE_NAME    FormKey_MUSTER_20C = "label_insurance_name"
	MUSTER_20C_LABEL_WOP               FormKey_MUSTER_20C = "label_wop"
	MUSTER_20C_LABEL_PATIENTINFO_LINE2 FormKey_MUSTER_20C = "label_patientInfo_line2"
	MUSTER_20C_LABEL_PATIENTINFO_LINE3 FormKey_MUSTER_20C = "label_patientInfo_line3"
	MUSTER_20C_LABEL_PATIENTINFO_LINE1 FormKey_MUSTER_20C = "label_patientInfo_line1"
	MUSTER_20C_LABEL_PATIENTINFO_LINE4 FormKey_MUSTER_20C = "label_patientInfo_line4"
	MUSTER_20C_LABEL_DATE_OF_BIRTH     FormKey_MUSTER_20C = "label_date_of_birth"
	MUSTER_20C_LABEL_IK_NUMBER         FormKey_MUSTER_20C = "label_ik_number"
	MUSTER_20C_LABEL_INSURANCE_NUMBER  FormKey_MUSTER_20C = "label_insurance_number"
	MUSTER_20C_LABEL_BSNR              FormKey_MUSTER_20C = "label_bsnr"
	MUSTER_20C_LABEL_LANR              FormKey_MUSTER_20C = "label_lanr"
	MUSTER_20C_DATE_PRESCRIBE          FormKey_MUSTER_20C = "date_prescribe"
	MUSTER_20C_LABEL_INSURANCE_STATUS  FormKey_MUSTER_20C = "label_insurance_status"
	MUSTER_20C_BARCODE                 FormKey_MUSTER_20C = "barcode"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_21 string

const (
	MUSTER_21_LABEL_DOCTOR_STAMP                   FormKey_MUSTER_21 = "label_doctor_stamp"
	MUSTER_21_BARCODE                              FormKey_MUSTER_21 = "barcode"
	MUSTER_21_LABEL_DATE_LABEL_CUSTOM_VOM_NO_SPACE FormKey_MUSTER_21 = "label_date_label_custom_vom_no_space"
	MUSTER_21_DATE_LABEL_CUSTOM_VOM                FormKey_MUSTER_21 = "date_label_custom_vom"
	MUSTER_21_LABEL_DATE_LABEL_CUSTOM_BIS_NO_SPACE FormKey_MUSTER_21 = "label_date_label_custom_bis_no_space"
	MUSTER_21_DATE_LABEL_CUSTOM_BIS                FormKey_MUSTER_21 = "date_label_custom_bis"
	MUSTER_21_LABEL_PRF_NR                         FormKey_MUSTER_21 = "label_prf_nr"
	MUSTER_21_LABEL_PATIENTINFO_LINE1              FormKey_MUSTER_21 = "label_patientInfo_line1"
	MUSTER_21_LABEL_DATE_OF_BIRTH                  FormKey_MUSTER_21 = "label_date_of_birth"
	MUSTER_21_LABEL_INSURANCE_NAME                 FormKey_MUSTER_21 = "label_insurance_name"
	MUSTER_21_LABEL_PATIENTINFO_LINE3              FormKey_MUSTER_21 = "label_patientInfo_line3"
	MUSTER_21_LABEL_PATIENTINFO_LINE4              FormKey_MUSTER_21 = "label_patientInfo_line4"
	MUSTER_21_LABEL_IK_NUMBER                      FormKey_MUSTER_21 = "label_ik_number"
	MUSTER_21_LABEL_BSNR                           FormKey_MUSTER_21 = "label_bsnr"
	MUSTER_21_LABEL_INSURANCE_NUMBER               FormKey_MUSTER_21 = "label_insurance_number"
	MUSTER_21_LABEL_LANR                           FormKey_MUSTER_21 = "label_lanr"
	MUSTER_21_DATE_PRESCRIBE                       FormKey_MUSTER_21 = "date_prescribe"
	MUSTER_21_LABEL_INSURANCE_END_DATE             FormKey_MUSTER_21 = "label_insurance_end_date"
	MUSTER_21_LABEL_INSURANCE_STATUS               FormKey_MUSTER_21 = "label_insurance_status"
	MUSTER_21_LABEL_PATIENTINFO_LINE2              FormKey_MUSTER_21 = "label_patientInfo_line2"
	MUSTER_21_CHECKBOX_KITA                        FormKey_MUSTER_21 = "checkbox_kita"
	MUSTER_21_CHECKBOX_SONSTIGER                   FormKey_MUSTER_21 = "checkbox_sonstiger"
	MUSTER_21_CHECKBOX_SER                         FormKey_MUSTER_21 = "checkbox_ser"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_11 string

const (
	MUSTER_11_TEXTBOX_DIAGNOSE_LINE1                        FormKey_MUSTER_11 = "textbox_diagnose_line1"
	MUSTER_11_TEXTBOX_DIAGNOSE_LINE2                        FormKey_MUSTER_11 = "textbox_diagnose_line2"
	MUSTER_11_TEXTBOX_BEFUNDE_LINE1                         FormKey_MUSTER_11 = "textbox_befunde_line1"
	MUSTER_11_TEXTBOX_BEFUNDE_LINE2                         FormKey_MUSTER_11 = "textbox_befunde_line2"
	MUSTER_11_TEXTBOX_BEFUNDE_LINE3                         FormKey_MUSTER_11 = "textbox_befunde_line3"
	MUSTER_11_TEXTBOX_BEFUNDE_LINE4                         FormKey_MUSTER_11 = "textbox_befunde_line4"
	MUSTER_11_CHECKBOX_MITBE_NEIN                           FormKey_MUSTER_11 = "checkbox_mitbe_nein"
	MUSTER_11_CHECKBOX_MITBE_JA                             FormKey_MUSTER_11 = "checkbox_mitbe_ja"
	MUSTER_11_TEXTBOX_FACHBEREICH                           FormKey_MUSTER_11 = "textbox_fachbereich"
	MUSTER_11_CHECKBOX_ES                                   FormKey_MUSTER_11 = "checkbox_es"
	MUSTER_11_CHECKBOX_BEHINDERUNG                          FormKey_MUSTER_11 = "checkbox_behinderung"
	MUSTER_11_TEXTBOX_BESONDERE_LINE1                       FormKey_MUSTER_11 = "textbox_besondere_line1"
	MUSTER_11_TEXTBOX_BESONDERE_LINE2                       FormKey_MUSTER_11 = "textbox_besondere_line2"
	MUSTER_11_TEXTBOX_PATIENTIN_LINE1                       FormKey_MUSTER_11 = "textbox_patientin_line1"
	MUSTER_11_TEXTBOX_PATIENTIN_LINE2                       FormKey_MUSTER_11 = "textbox_patientin_line2"
	MUSTER_11_TEXTBOX_PATIENTIN_LINE3                       FormKey_MUSTER_11 = "textbox_patientin_line3"
	MUSTER_11_TEXTBOX_NACH                                  FormKey_MUSTER_11 = "textbox_nach"
	MUSTER_11_CHECKBOX_RONTGEN                              FormKey_MUSTER_11 = "checkbox_rontgen"
	MUSTER_11_CHECKBOX_ULTRASCHALL                          FormKey_MUSTER_11 = "checkbox_ultraschall"
	MUSTER_11_CHECKBOX_EKG                                  FormKey_MUSTER_11 = "checkbox_ekg"
	MUSTER_11_CHECKBOX_LABOR                                FormKey_MUSTER_11 = "checkbox_labor"
	MUSTER_11_CHECKBOX_ARZT                                 FormKey_MUSTER_11 = "checkbox_arzt"
	MUSTER_11_TEXTBOX_SONSTIGES                             FormKey_MUSTER_11 = "textbox_sonstiges"
	MUSTER_11_LABEL_DOCTOR_STAMP                            FormKey_MUSTER_11 = "label_doctor_stamp"
	MUSTER_11_LABEL_DATE_LABEL_CUSTOM_DATUM_NO_SPACE        FormKey_MUSTER_11 = "label_date_label_custom_datum_no_space"
	MUSTER_11_LABEL_DATE_LABEL_CUSTOM_VORAU_NO_SPACE        FormKey_MUSTER_11 = "label_date_label_custom_vorau_no_space"
	MUSTER_11_LABEL_DATE_LABEL_CUSTOM_ARBEIT_NO_SPACE       FormKey_MUSTER_11 = "label_date_label_custom_arbeit_no_space"
	MUSTER_11_LABEL_DATE_LABEL_CUSTOM_UNTERSUCHUNG_NO_SPACE FormKey_MUSTER_11 = "label_date_label_custom_untersuchung_no_space"
	MUSTER_11_DATE_LABEL_CUSTOM_DATUM                       FormKey_MUSTER_11 = "date_label_custom_datum"
	MUSTER_11_DATE_LABEL_CUSTOM_VORAU                       FormKey_MUSTER_11 = "date_label_custom_vorau"
	MUSTER_11_DATE_LABEL_CUSTOM_ARBEIT                      FormKey_MUSTER_11 = "date_label_custom_arbeit"
	MUSTER_11_DATE_LABEL_CUSTOM_UNTERSUCHUNG                FormKey_MUSTER_11 = "date_label_custom_untersuchung"
	MUSTER_11_LABEL_TAKEOVER_DIAGNOSIS                      FormKey_MUSTER_11 = "label_takeover_diagnosis"
	MUSTER_11_LABEL_PRF_NR                                  FormKey_MUSTER_11 = "label_prf_nr"
	MUSTER_11_LABEL_INSURANCE_NUMBER                        FormKey_MUSTER_11 = "label_insurance_number"
	MUSTER_11_LABEL_IK_NUMBER                               FormKey_MUSTER_11 = "label_ik_number"
	MUSTER_11_LABEL_PATIENT_FULLNAME                        FormKey_MUSTER_11 = "label_patient_fullname"
)

type FormKey_MUSTER_39 string

const (
	MUSTER_39_CHECKBOX_OP_NEIN                 FormKey_MUSTER_39 = "checkbox_op_nein"
	MUSTER_39_CHECKBOX_OP_JA                   FormKey_MUSTER_39 = "checkbox_op_ja"
	MUSTER_39_TEXTBOX_WELCHE                   FormKey_MUSTER_39 = "textbox_welche"
	MUSTER_39_DATE_CUSTOM_WANN                 FormKey_MUSTER_39 = "date_custom_wann"
	MUSTER_39_CHECKBOX_UNAUFFALLIG             FormKey_MUSTER_39 = "checkbox_unauffallig"
	MUSTER_39_CHECKBOX_AUFFALLIG               FormKey_MUSTER_39 = "checkbox_auffallig"
	MUSTER_39_AREA_TEXT_BOX_ERLAUTERUNGEN      FormKey_MUSTER_39 = "area_text_box_erlauterungen"
	MUSTER_39_LABEL_PATIENTINFO_LINE2          FormKey_MUSTER_39 = "label_patientInfo_line2"
	MUSTER_39_LABEL_PATIENTINFO_LINE3          FormKey_MUSTER_39 = "label_patientInfo_line3"
	MUSTER_39_LABEL_PATIENTINFO_LINE1          FormKey_MUSTER_39 = "label_patientInfo_line1"
	MUSTER_39_LABEL_PATIENTINFO_LINE4          FormKey_MUSTER_39 = "label_patientInfo_line4"
	MUSTER_39_LABEL_IK_NUMBER                  FormKey_MUSTER_39 = "label_ik_number"
	MUSTER_39_LABEL_BSNR                       FormKey_MUSTER_39 = "label_bsnr"
	MUSTER_39_LABEL_INSURANCE_NUMBER           FormKey_MUSTER_39 = "label_insurance_number"
	MUSTER_39_LABEL_INSURANCE_STATUS           FormKey_MUSTER_39 = "label_insurance_status"
	MUSTER_39_LABEL_LANR                       FormKey_MUSTER_39 = "label_lanr"
	MUSTER_39_DATE_PRESCRIBE                   FormKey_MUSTER_39 = "date_prescribe"
	MUSTER_39_CHECKBOX_20_29_JAHRE             FormKey_MUSTER_39 = "checkbox_20_29_jahre"
	MUSTER_39_CHECKBOX_30_34_JAHRE             FormKey_MUSTER_39 = "checkbox_30_34_jahre"
	MUSTER_39_CHECKBOX_AB_35_JAHRE             FormKey_MUSTER_39 = "checkbox_ab_35_jahre"
	MUSTER_39_CHECKBOX_PRIMAR_SCREENING        FormKey_MUSTER_39 = "checkbox_primar_screening"
	MUSTER_39_CHECKBOX_ABKLARUNGS_DIAGNOSTIK   FormKey_MUSTER_39 = "checkbox_abklarungs_diagnostik"
	MUSTER_39_CHECKBOX_ZYTOLOGIE               FormKey_MUSTER_39 = "checkbox_zytologie"
	MUSTER_39_CHECKBOX_HPV_TEST                FormKey_MUSTER_39 = "checkbox_hpv_test"
	MUSTER_39_CHECKBOX_KO_TESTUNG              FormKey_MUSTER_39 = "checkbox_ko_testung"
	MUSTER_39_CHECKBOX_NEIN                    FormKey_MUSTER_39 = "checkbox_nein"
	MUSTER_39_CHECKBOX_JA                      FormKey_MUSTER_39 = "checkbox_ja"
	MUSTER_39_DATE_ANAMNESE                    FormKey_MUSTER_39 = "date_anamnese"
	MUSTER_39_LABEL_GRUPPE                     FormKey_MUSTER_39 = "label_gruppe"
	MUSTER_39_CHECKBOX_HPV_VOLLSTANDIG         FormKey_MUSTER_39 = "checkbox_hpv_vollstandig"
	MUSTER_39_CHECKBOX_HPV_UNVOLLSTANDIG       FormKey_MUSTER_39 = "checkbox_hpv_unvollstandig"
	MUSTER_39_CHECKBOX_HPV_KEINE               FormKey_MUSTER_39 = "checkbox_hpv_keine"
	MUSTER_39_CHECKBOX_HPV_UNKLAR              FormKey_MUSTER_39 = "checkbox_hpv_unklar"
	MUSTER_39_CHECKBOX_HPV_HR_LIEGT_NICHT      FormKey_MUSTER_39 = "checkbox_hpv_hr_liegt_nicht"
	MUSTER_39_CHECKBOX_HPV_HR_LIEGT_VOR        FormKey_MUSTER_39 = "checkbox_hpv_hr_liegt_vor"
	MUSTER_39_CHECKBOX_HPV_HR_POSITIV          FormKey_MUSTER_39 = "checkbox_hpv_hr_positiv"
	MUSTER_39_CHECKBOX_HPV_HR_NEGATIV          FormKey_MUSTER_39 = "checkbox_hpv_hr_negativ"
	MUSTER_39_CHECKBOX_HPV_HR_NICHT_VERWERTBAR FormKey_MUSTER_39 = "checkbox_hpv_hr_nicht_verwertbar"
	MUSTER_39_DATE_CUSTOM_JETZT                FormKey_MUSTER_39 = "date_custom_jetzt"
	MUSTER_39_CHECKBOX_GRAVIDITAT_NEIN         FormKey_MUSTER_39 = "checkbox_graviditat_nein"
	MUSTER_39_CHECKBOX_GRAVIDITAT_JA           FormKey_MUSTER_39 = "checkbox_graviditat_ja"
	MUSTER_39_CHECKBOX_AUSFLUSS_NEIN           FormKey_MUSTER_39 = "checkbox_ausfluss_nein"
	MUSTER_39_CHECKBOX_AUSFLUSS_JA             FormKey_MUSTER_39 = "checkbox_ausfluss_ja"
	MUSTER_39_CHECKBOX_IUP_NEIN                FormKey_MUSTER_39 = "checkbox_iup_nein"
	MUSTER_39_CHECKBOX_IUP_JA                  FormKey_MUSTER_39 = "checkbox_iup_ja"
	MUSTER_39_CHECKBOX_EINNAHME_NEIN           FormKey_MUSTER_39 = "checkbox_einnahme_nein"
	MUSTER_39_CHECKBOX_EINNAHME_JA             FormKey_MUSTER_39 = "checkbox_einnahme_ja"
	MUSTER_39_LABEL_DATE_OF_BIRTH              FormKey_MUSTER_39 = "label_date_of_birth"
	MUSTER_39_LABEL_INSURANCE_NAME             FormKey_MUSTER_39 = "label_insurance_name"
	MUSTER_39_LABEL_MONTH_YEAR_DATE_ANAMNESE   FormKey_MUSTER_39 = "label_month_year_date_anamnese"
	MUSTER_39_LABEL_WOP                        FormKey_MUSTER_39 = "label_wop"
	MUSTER_39_LABEL_INSURANCE_END_DATE         FormKey_MUSTER_39 = "label_insurance_end_date"
	MUSTER_39_BARCODE                          FormKey_MUSTER_39 = "barcode"
	MUSTER_39_LABEL_DOCTOR_STAMP               FormKey_MUSTER_39 = "label_doctor_stamp"
	MUSTER_39_LABEL_DATE_CUSTOM_WANN           FormKey_MUSTER_39 = "label_date_custom_wann"
	MUSTER_39_LABEL_DATE_CUSTOM_JETZT          FormKey_MUSTER_39 = "label_date_custom_jetzt"
	MUSTER_39_LABEL_PRF_NR                     FormKey_MUSTER_39 = "label_prf_nr"
)

type FormKey_MUSTER_N63A string

const (
	MUSTER_N63A_CHECKBOX_63_BERATUNG_0                   FormKey_MUSTER_N63A = "checkbox_63_beratung_0"
	MUSTER_N63A_CHECKBOX_63_DES_BEH_ARZTES_0             FormKey_MUSTER_N63A = "checkbox_63_des_beh_arztes_0"
	MUSTER_N63A_CHECKBOX_63_DES_BEH_PFLEGEFACHKRAFT_0    FormKey_MUSTER_N63A = "checkbox_63_des_beh_pflegefachkraft_0"
	MUSTER_N63A_CHECKBOX_63_DES_PAT_DER_ANGEHORIGEN_0    FormKey_MUSTER_N63A = "checkbox_63_des_pat_der_angehorigen_0"
	MUSTER_N63A_CHECKBOX_63_KOORDINATION_0               FormKey_MUSTER_N63A = "checkbox_63_koordination_0"
	MUSTER_N63A_CHECKBOX_63_AUS_URO_0                    FormKey_MUSTER_N63A = "checkbox_63_aus_uro_0"
	MUSTER_N63A_CHECKBOX_63_AUS_ULZERIE_0                FormKey_MUSTER_N63A = "checkbox_63_aus_ulzerie_0"
	MUSTER_N63A_CHECKBOX_63_AUS_SCHMERZ_0                FormKey_MUSTER_N63A = "checkbox_63_aus_schmerz_0"
	MUSTER_N63A_CHECKBOX_63_AUS_RESPIRATORIS_0           FormKey_MUSTER_N63A = "checkbox_63_aus_respiratoris_0"
	MUSTER_N63A_CHECKBOX_63_AUS_NEUROLOGISCHE_0          FormKey_MUSTER_N63A = "checkbox_63_aus_neurologische_0"
	MUSTER_N63A_CHECKBOX_63_AUS_GASTRO_0                 FormKey_MUSTER_N63A = "checkbox_63_aus_gastro_0"
	MUSTER_N63A_CHECKBOX_63_AUS_KOMPLEXES_0              FormKey_MUSTER_N63A = "checkbox_63_aus_komplexes_0"
	MUSTER_N63A_CHECKBOX_63_ERSTVERORDNUNG_0             FormKey_MUSTER_N63A = "checkbox_63_erstverordnung_0"
	MUSTER_N63A_CHECKBOX_63_UNFALL_UNFALLFOLGEN_0        FormKey_MUSTER_N63A = "checkbox_63_unfall_unfallfolgen_0"
	MUSTER_N63A_CHECKBOX_63_FOLGEVERORDNUNG_0            FormKey_MUSTER_N63A = "checkbox_63_folgeverordnung_0"
	MUSTER_N63A_TEXTBOX_63_NAHERE_BES_LINE1_0            FormKey_MUSTER_N63A = "textbox_63_nahere_bes_line1_0"
	MUSTER_N63A_TEXTBOX_63_AKTUELLE_LINE1_0              FormKey_MUSTER_N63A = "textbox_63_aktuelle_line1_0"
	MUSTER_N63A_TEXTBOX_63_MIT_FOLGENDER_LINE1_0         FormKey_MUSTER_N63A = "textbox_63_mit_folgender_line1_0"
	MUSTER_N63A_TEXTBOX_63_NAHERE_LINE1_0                FormKey_MUSTER_N63A = "textbox_63_nahere_line1_0"
	MUSTER_N63A_TEXTBOX_63_NAHERE_BES_LINE2_0            FormKey_MUSTER_N63A = "textbox_63_nahere_bes_line2_0"
	MUSTER_N63A_TEXTBOX_63_AKTUELLE_LINE2_0              FormKey_MUSTER_N63A = "textbox_63_aktuelle_line2_0"
	MUSTER_N63A_TEXTBOX_63_AKTUELLE_LINE3_0              FormKey_MUSTER_N63A = "textbox_63_aktuelle_line3_0"
	MUSTER_N63A_TEXTBOX_63_AKTUELLE_LINE4_0              FormKey_MUSTER_N63A = "textbox_63_aktuelle_line4_0"
	MUSTER_N63A_TEXTBOX_63_AKTUELLE_LINE5_0              FormKey_MUSTER_N63A = "textbox_63_aktuelle_line5_0"
	MUSTER_N63A_TEXTBOX_63_NAHERE_LINE2_0                FormKey_MUSTER_N63A = "textbox_63_nahere_line2_0"
	MUSTER_N63A_TEXTBOX_63_NAHERE_LINE3_0                FormKey_MUSTER_N63A = "textbox_63_nahere_line3_0"
	MUSTER_N63A_TEXTBOX_63_NAHERE_LINE4_0                FormKey_MUSTER_N63A = "textbox_63_nahere_line4_0"
	MUSTER_N63A_TEXTBOX_63_NAHERE_LINE5_0                FormKey_MUSTER_N63A = "textbox_63_nahere_line5_0"
	MUSTER_N63A_TEXTBOX_63_MIT_FOLGENDER_LINE2_0         FormKey_MUSTER_N63A = "textbox_63_mit_folgender_line2_0"
	MUSTER_N63A_TEXTBOX_63_MIT_FOLGENDER_LINE3_0         FormKey_MUSTER_N63A = "textbox_63_mit_folgender_line3_0"
	MUSTER_N63A_LABEL_DOCTOR_STAMP_0                     FormKey_MUSTER_N63A = "label_doctor_stamp_0"
	MUSTER_N63A_CHECKBOX_63_VOL_VERSORGUNG_0             FormKey_MUSTER_N63A = "checkbox_63_vol_versorgung_0"
	MUSTER_N63A_CHECKBOX_63_ADDITIV_UNT_TEILVERSORGUNG_0 FormKey_MUSTER_N63A = "checkbox_63_additiv_unt_teilversorgung_0"
	MUSTER_N63A_LABEL_TAKEOVER_DIAGNOSIS_0               FormKey_MUSTER_N63A = "label_takeover_diagnosis_0"
	MUSTER_N63A_TEXTBOX_DIAGNOSE_LINE1_0                 FormKey_MUSTER_N63A = "textbox_diagnose_line1_0"
	MUSTER_N63A_TEXTBOX_DIAGNOSE_LINE2_0                 FormKey_MUSTER_N63A = "textbox_diagnose_line2_0"
	MUSTER_N63A_TEXTBOX_DIAGNOSE_LINE3_0                 FormKey_MUSTER_N63A = "textbox_diagnose_line3_0"
	MUSTER_N63A_LABEL_PRF_NR_0                           FormKey_MUSTER_N63A = "label_prf_nr_0"
	MUSTER_N63A_LABEL_PRF_NR_1                           FormKey_MUSTER_N63A = "label_prf_nr_1"
	MUSTER_N63A_BARCODE_0                                FormKey_MUSTER_N63A = "barcode_0"
	MUSTER_N63A_LABEL_INSURANCE_NAME_0                   FormKey_MUSTER_N63A = "label_insurance_name_0"
	MUSTER_N63A_LABEL_WOP_0                              FormKey_MUSTER_N63A = "label_wop_0"
	MUSTER_N63A_LABEL_PATIENTINFO_LINE1_0                FormKey_MUSTER_N63A = "label_patientInfo_line1_0"
	MUSTER_N63A_LABEL_PATIENTINFO_LINE2_0                FormKey_MUSTER_N63A = "label_patientInfo_line2_0"
	MUSTER_N63A_LABEL_DATE_OF_BIRTH_0                    FormKey_MUSTER_N63A = "label_date_of_birth_0"
	MUSTER_N63A_LABEL_PATIENTINFO_LINE3_0                FormKey_MUSTER_N63A = "label_patientInfo_line3_0"
	MUSTER_N63A_LABEL_PATIENTINFO_LINE4_0                FormKey_MUSTER_N63A = "label_patientInfo_line4_0"
	MUSTER_N63A_LABEL_INSURANCE_END_DATE_0               FormKey_MUSTER_N63A = "label_insurance_end_date_0"
	MUSTER_N63A_LABEL_IK_NUMBER_0                        FormKey_MUSTER_N63A = "label_ik_number_0"
	MUSTER_N63A_LABEL_INSURANCE_NUMBER_0                 FormKey_MUSTER_N63A = "label_insurance_number_0"
	MUSTER_N63A_LABEL_INSURANCE_STATUS_0                 FormKey_MUSTER_N63A = "label_insurance_status_0"
	MUSTER_N63A_LABEL_BSNR_0                             FormKey_MUSTER_N63A = "label_bsnr_0"
	MUSTER_N63A_LABEL_LANR_0                             FormKey_MUSTER_N63A = "label_lanr_0"
	MUSTER_N63A_DATE_PRESCRIBE_0                         FormKey_MUSTER_N63A = "date_prescribe_0"
	MUSTER_N63A_DATE_LABEL_CUSTOM_DATUM_1                FormKey_MUSTER_N63A = "date_label_custom_datum_1"
	MUSTER_N63A_LABEL_DATE_LABEL_CUSTOM_DATUM_1_NO_SPACE FormKey_MUSTER_N63A = "label_date_label_custom_datum_1_no_space"
	MUSTER_N63A_LABEL_PATIENT_FULLNAME_1                 FormKey_MUSTER_N63A = "label_patient_fullname_1"
	MUSTER_N63A_LABEL_IK_NUMBER_1                        FormKey_MUSTER_N63A = "label_ik_number_1"
	MUSTER_N63A_LABEL_INSURANCE_NUMBER_1                 FormKey_MUSTER_N63A = "label_insurance_number_1"
	MUSTER_N63A_LABEL_DATE_LABEL_CUSTOM_VOM_0_NO_SPACE   FormKey_MUSTER_N63A = "label_date_label_custom_vom_0_no_space"
	MUSTER_N63A_DATE_LABEL_CUSTOM_VOM_0                  FormKey_MUSTER_N63A = "date_label_custom_vom_0"
	MUSTER_N63A_LABEL_DATE_LABEL_CUSTOM_BIS_0_NO_SPACE   FormKey_MUSTER_N63A = "label_date_label_custom_bis_0_no_space"
	MUSTER_N63A_DATE_LABEL_CUSTOM_BIS_0                  FormKey_MUSTER_N63A = "date_label_custom_bis_0"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_56 string

const (
	MUSTER_56_CHECKBOX_56_FUR_REHABILITATIONSSPORT_0                                         FormKey_MUSTER_56 = "checkbox_56_fur_rehabilitationssport_0"
	MUSTER_56_CHECKBOX_56_FUR_FUNKTIONSTRAINING_0                                            FormKey_MUSTER_56 = "checkbox_56_fur_funktionstraining_0"
	MUSTER_56_TEXTBOX_56_ZIELDES_0                                                           FormKey_MUSTER_56 = "textbox_56_zieldes_0"
	MUSTER_56_TEXTBOX_56_SCHADIGUNG_0                                                        FormKey_MUSTER_56 = "textbox_56_schadigung_0"
	MUSTER_56_CHECKBOX_56_ERHOHTER_TEILHABEBEDARF_0                                          FormKey_MUSTER_56 = "checkbox_56_erhohter_teilhabebedarf_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_GYMNASTIK_0                                            FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_gymnastik_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_BEWEGUNGSSPIELE_0                                      FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_bewegungsspiele_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_SONSTIGE_0                                             FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_sonstige_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_SCHWIMMEN_0                                            FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_schwimmen_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_UBUNGENZURSTARKUNG_0                                   FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_ubungenzurstarkung_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_50UBUNGSEINHEITEN_0                                    FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_50ubungseinheiten_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_AUSDAUER_0                                             FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_ausdauer_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_0                                   FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_0"
	MUSTER_56_TEXTBOX_56_EMPFO_REHAB_SONSTIGE_0                                              FormKey_MUSTER_56 = "textbox_56_empfo_rehab_sonstige_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_ASTHMA_0                            FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_asthma_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_BLINDHEIT_0                         FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_blindheit_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_CHRONISCHER_0                       FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_chronischer_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_DEMETIELLES_0                       FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_demetielles_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_DIABETES_0                          FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_diabetes_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_DOPPELAMPUTATION_0                  FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_doppelamputation_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_EPILEPSIE_0                         FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_epilepsie_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_INFANTILER_0                        FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_infantiler_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_INTELLIGENZMIND_0                   FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_intelligenzmind_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_MORBUSBECHTEREW_0                   FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_morbusbechterew_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_MORBUSPARKINSON_0                   FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_morbusparkinson_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_MUKOVISZIDOSE_0                     FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_mukoviszidose_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_MULTIPLERSKLEROSE_0                 FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_multiplersklerose_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_MUSKELDYS_0                         FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_muskeldys_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_NIERENINSUFFIZIENZ_0                FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_niereninsuffizienz_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_ORGANISCHE_0                        FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_organische_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_POLYNEUROPATHIE_0                   FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_polyneuropathie_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_QUERSCHNITTLAHMUNG_0                FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_querschnittlahmung_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_120UBUNGSEINHEITEN_ANDERE_VERGL_0                      FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_120ubungseinheiten_andere_vergl_0"
	MUSTER_56_CHECKBOX_56_EMPFO_REHAB_28UBUNGSEINHEITEN_0                                    FormKey_MUSTER_56 = "checkbox_56_empfo_rehab_28ubungseinheiten_0"
	MUSTER_56_CHECKBOX_56_EMPFO_FUNKTION_TROCKENGYM_0                                        FormKey_MUSTER_56 = "checkbox_56_empfo_funktion_trockengym_0"
	MUSTER_56_CHECKBOX_56_EMPFO_FUNKTION_WASSERGYMNASTIK_0                                   FormKey_MUSTER_56 = "checkbox_56_empfo_funktion_wassergymnastik_0"
	MUSTER_56_CHECKBOX_56_EMPFO_FUNKTION_12MONATE_0                                          FormKey_MUSTER_56 = "checkbox_56_empfo_funktion_12monate_0"
	MUSTER_56_CHECKBOX_56_EMPFO_FUNKTION_24MONATE_0                                          FormKey_MUSTER_56 = "checkbox_56_empfo_funktion_24monate_0"
	MUSTER_56_CHECKBOX_56_EMPFO_FUNKTION_24MONATE_FIBROMYALGIE_0                             FormKey_MUSTER_56 = "checkbox_56_empfo_funktion_24monate_fibromyalgie_0"
	MUSTER_56_CHECKBOX_56_EMPFO_FUNKTION_24MONATE_KOLLAGENOSEN_0                             FormKey_MUSTER_56 = "checkbox_56_empfo_funktion_24monate_kollagenosen_0"
	MUSTER_56_CHECKBOX_56_EMPFO_FUNKTION_24MONATE_MORBUS_0                                   FormKey_MUSTER_56 = "checkbox_56_empfo_funktion_24monate_morbus_0"
	MUSTER_56_CHECKBOX_56_EMPFO_FUNKTION_24MONATE_OSTEOPOROSE_0                              FormKey_MUSTER_56 = "checkbox_56_empfo_funktion_24monate_osteoporose_0"
	MUSTER_56_CHECKBOX_56_EMPFO_FUNKTION_24MONATE_POLYARTHROSEN_0                            FormKey_MUSTER_56 = "checkbox_56_empfo_funktion_24monate_polyarthrosen_0"
	MUSTER_56_CHECKBOX_56_EMPFO_FUNKTION_24MONATE_PSORIASIS_0                                FormKey_MUSTER_56 = "checkbox_56_empfo_funktion_24monate_psoriasis_0"
	MUSTER_56_CHECKBOX_56_EMPFO_FUNKTION_24MONATE_RHEUMATOIDE_0                              FormKey_MUSTER_56 = "checkbox_56_empfo_funktion_24monate_rheumatoide_0"
	MUSTER_56_CHECKBOX_56_120UBUNGSEINHEITEN_0                                               FormKey_MUSTER_56 = "checkbox_56_120ubungseinheiten_0"
	MUSTER_56_CHECKBOX_24_MONATE_RICHTWERT_0                                                 FormKey_MUSTER_56 = "checkbox_24_monate_richtwert_0"
	MUSTER_56_CHECKBOX_56_FOLGEVERORDNUNG_0                                                  FormKey_MUSTER_56 = "checkbox_56_folgeverordnung_0"
	MUSTER_56_TEXTBOX_56_DER_LAGE_IST_DIE_ERLERNTEN_UBUNGEN_SELBSTANDIG_UND_EI_0             FormKey_MUSTER_56 = "textbox_56_der_lage_ist_die_erlernten_ubungen_selbstandig_und_ei_0"
	MUSTER_56_CHECKBOX_45_UBUNGSEINHEITEN_IN_12_MONATEN_RICHTWERTE_BEI_WEITERER_VERORDNUNG_1 FormKey_MUSTER_56 = "checkbox_45_ubungseinheiten_in_12_monaten_richtwerte_bei_weiterer_verordnung_1"
	MUSTER_56_CHECKBOX_NUR_BEI_BELASTUNGSGRENZE_1                                            FormKey_MUSTER_56 = "checkbox_nur_bei_belastungsgrenze_1"
	MUSTER_56_CHECKBOX_WENN_BEI_KOGNITIVEN_1                                                 FormKey_MUSTER_56 = "checkbox_wenn_bei_kognitiven_1"
	MUSTER_56_CHECKBOX_KINDERHERZGRUPPEN_120_UBU_24_MONATEN_1                                FormKey_MUSTER_56 = "checkbox_kinderherzgruppen_120_ubu_24_monaten_1"
	MUSTER_56_CHECKBOX_90_UBUNGSEINHEITEN_IN_24_MONATEN_RICHTWERTE_ALS_ERSTVERSORGUNG_1      FormKey_MUSTER_56 = "checkbox_90_ubungseinheiten_in_24_monaten_richtwerte_als_erstversorgung_1"
	MUSTER_56_CHECKBOX_HERZINSUFFIZIENZGRUPPE_1                                              FormKey_MUSTER_56 = "checkbox_herzinsuffizienzgruppe_1"
	MUSTER_56_CHECKBOX_HERZGRUPPE_1                                                          FormKey_MUSTER_56 = "checkbox_herzgruppe_1"
	MUSTER_56_CHECKBOX_REHABILITATIONSSPORT_2X_1                                             FormKey_MUSTER_56 = "checkbox_rehabilitationssport_2x_1"
	MUSTER_56_CHECKBOX_REHABILITATIONSSPORT_3X_1                                             FormKey_MUSTER_56 = "checkbox_rehabilitationssport_3x_1"
	MUSTER_56_CHECKBOX_REHABILITATIONSSPORT_1X_1                                             FormKey_MUSTER_56 = "checkbox_rehabilitationssport_1x_1"
	MUSTER_56_CHECKBOX_FUNKTIONSTRAINING_TROCKENGYMNASTIK_1X_1                               FormKey_MUSTER_56 = "checkbox_funktionstraining_trockengymnastik_1x_1"
	MUSTER_56_CHECKBOX_FUNKTIONSTRAINING_TROCKENGYMNASTIK_2X_1                               FormKey_MUSTER_56 = "checkbox_funktionstraining_trockengymnastik_2x_1"
	MUSTER_56_CHECKBOX_FUNKTIONSTRAINING_TROCKENGYMNASTIK_3X_1                               FormKey_MUSTER_56 = "checkbox_funktionstraining_trockengymnastik_3x_1"
	MUSTER_56_CHECKBOX_FUNKTIONSTRAINING_WASSERGYMNASTIK_1X_1                                FormKey_MUSTER_56 = "checkbox_funktionstraining_wassergymnastik_1x_1"
	MUSTER_56_CHECKBOX_FUNKTIONSTRAINING_WASSERGYMNASTIK_2X_1                                FormKey_MUSTER_56 = "checkbox_funktionstraining_wassergymnastik_2x_1"
	MUSTER_56_CHECKBOX_FUNKTIONSTRAINING_WASSERGYMNASTIK_3X_1                                FormKey_MUSTER_56 = "checkbox_funktionstraining_wassergymnastik_3x_1"
	MUSTER_56_BARCODE_1                                                                      FormKey_MUSTER_56 = "barcode_1"
	MUSTER_56_LABEL_DOCTOR_STAMP_1                                                           FormKey_MUSTER_56 = "label_doctor_stamp_1"
	MUSTER_56_TEXTBOX_56_BEGRRUNDUNG_LINE1_1                                                 FormKey_MUSTER_56 = "textbox_56_begrrundung_line1_1"
	MUSTER_56_TEXTBOX_56_BEGRRUNDUNG_LINE2_1                                                 FormKey_MUSTER_56 = "textbox_56_begrrundung_line2_1"
	MUSTER_56_TEXTBOX_56_ABWEICHUNG_1                                                        FormKey_MUSTER_56 = "textbox_56_abweichung_1"
	MUSTER_56_LABEL_TAKEOVER_DIAGNOSIS_0                                                     FormKey_MUSTER_56 = "label_takeover_diagnosis_0"
	MUSTER_56_TEXTBOX_ICD2_0                                                                 FormKey_MUSTER_56 = "textbox_icd2_0"
	MUSTER_56_LABEL_PRF_NR_1                                                                 FormKey_MUSTER_56 = "label_prf_nr_1"
	MUSTER_56_LABEL_PRF_NR_0                                                                 FormKey_MUSTER_56 = "label_prf_nr_0"
	MUSTER_56_TEXTBOX_ICD1_0                                                                 FormKey_MUSTER_56 = "textbox_icd1_0"
	MUSTER_56_TEXTBOX_SINGLE_DIAGNOSE1_0                                                     FormKey_MUSTER_56 = "textbox_single_diagnose1_0"
	MUSTER_56_TEXTBOX_SINGLE_DIAGNOSE2_0                                                     FormKey_MUSTER_56 = "textbox_single_diagnose2_0"
	MUSTER_56_LABEL_DATE_LABEL_CUSTOM_DATUM_1_NO_SPACE_1                                     FormKey_MUSTER_56 = "label_date_label_custom_datum_1_no_space_1"
	MUSTER_56_DATE_LABEL_CUSTOM_DATUM_1                                                      FormKey_MUSTER_56 = "date_label_custom_datum_1"
	MUSTER_56_LABEL_PATIENTINFO_LINE2_0                                                      FormKey_MUSTER_56 = "label_patientInfo_line2_0"
	MUSTER_56_LABEL_PATIENTINFO_LINE1_0                                                      FormKey_MUSTER_56 = "label_patientInfo_line1_0"
	MUSTER_56_LABEL_DATE_OF_BIRTH_0                                                          FormKey_MUSTER_56 = "label_date_of_birth_0"
	MUSTER_56_LABEL_INSURANCE_NAME_0                                                         FormKey_MUSTER_56 = "label_insurance_name_0"
	MUSTER_56_LABEL_WOP_0                                                                    FormKey_MUSTER_56 = "label_wop_0"
	MUSTER_56_LABEL_PATIENTINFO_LINE3_0                                                      FormKey_MUSTER_56 = "label_patientInfo_line3_0"
	MUSTER_56_LABEL_PATIENTINFO_LINE4_0                                                      FormKey_MUSTER_56 = "label_patientInfo_line4_0"
	MUSTER_56_LABEL_IK_NUMBER_0                                                              FormKey_MUSTER_56 = "label_ik_number_0"
	MUSTER_56_LABEL_BSNR_0                                                                   FormKey_MUSTER_56 = "label_bsnr_0"
	MUSTER_56_LABEL_INSURANCE_NUMBER_0                                                       FormKey_MUSTER_56 = "label_insurance_number_0"
	MUSTER_56_LABEL_LANR_0                                                                   FormKey_MUSTER_56 = "label_lanr_0"
	MUSTER_56_DATE_PRESCRIBE_0                                                               FormKey_MUSTER_56 = "date_prescribe_0"
	MUSTER_56_LABEL_INSURANCE_END_DATE_0                                                     FormKey_MUSTER_56 = "label_insurance_end_date_0"
	MUSTER_56_LABEL_INSURANCE_STATUS_0                                                       FormKey_MUSTER_56 = "label_insurance_status_0"
	MUSTER_56_LABEL_IK_NUMBER_1                                                              FormKey_MUSTER_56 = "label_ik_number_1"
	MUSTER_56_LABEL_INSURANCE_NUMBER_1                                                       FormKey_MUSTER_56 = "label_insurance_number_1"
	MUSTER_56_LABEL_PATIENT_FULLNAME_1                                                       FormKey_MUSTER_56 = "label_patient_fullname_1"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_PTV_1 string

const (
	MUSTER_PTV_1_LABEL_DATE_LABEL_CUSTOM_DATUM_NO_SPACE FormKey_MUSTER_PTV_1 = "label_date_label_custom_datum_no_space"
	MUSTER_PTV_1_DATE_LABEL_CUSTOM_DATUM                FormKey_MUSTER_PTV_1 = "date_label_custom_datum"
	MUSTER_PTV_1_CHECKBOX_ANALYTISCHE                   FormKey_MUSTER_PTV_1 = "checkbox_analytische"
	MUSTER_PTV_1_CHECKBOX_SYSTEMISCHE                   FormKey_MUSTER_PTV_1 = "checkbox_systemische"
	MUSTER_PTV_1_CHECKBOX_TIEFENPSYCHOLOGISCH           FormKey_MUSTER_PTV_1 = "checkbox_tiefenpsychologisch"
	MUSTER_PTV_1_CHECKBOX_VERHALTENSTHERAPIE            FormKey_MUSTER_PTV_1 = "checkbox_verhaltenstherapie"
	MUSTER_PTV_1_CHECKBOX_EINZELTHERAPIE                FormKey_MUSTER_PTV_1 = "checkbox_einzeltherapie"
	MUSTER_PTV_1_CHECKBOX_GRUPPENTHERAPIE               FormKey_MUSTER_PTV_1 = "checkbox_gruppentherapie"
	MUSTER_PTV_1_CHECKBOX_KOMBINATIONSBEHANDLUNG        FormKey_MUSTER_PTV_1 = "checkbox_kombinationsbehandlung"
	MUSTER_PTV_1_CHECKBOX_FOLGEANTRAG                   FormKey_MUSTER_PTV_1 = "checkbox_folgeantrag"
	MUSTER_PTV_1_CHECKBOX_ERSTANTRAG                    FormKey_MUSTER_PTV_1 = "checkbox_erstantrag"
	MUSTER_PTV_1_CHECKBOX_JA                            FormKey_MUSTER_PTV_1 = "checkbox_ja"
	MUSTER_PTV_1_CHECKBOX_NEIN                          FormKey_MUSTER_PTV_1 = "checkbox_nein"
	MUSTER_PTV_1_CHECKBOX_WAREN_JA                      FormKey_MUSTER_PTV_1 = "checkbox_waren_ja"
	MUSTER_PTV_1_CHECKBOX_WAREN_NEIN                    FormKey_MUSTER_PTV_1 = "checkbox_waren_nein"
	MUSTER_PTV_1_CHECKBOX_WURDE_JA                      FormKey_MUSTER_PTV_1 = "checkbox_wurde_ja"
	MUSTER_PTV_1_CHECKBOX_WURDE_NEIN                    FormKey_MUSTER_PTV_1 = "checkbox_wurde_nein"
	MUSTER_PTV_1_LABEL_DATE_LABEL_CUSTOM_ZWAR_NO_SPACE  FormKey_MUSTER_PTV_1 = "label_date_label_custom_zwar_no_space"
	MUSTER_PTV_1_DATE_LABEL_CUSTOM_ZWAR                 FormKey_MUSTER_PTV_1 = "date_label_custom_zwar"
	MUSTER_PTV_1_LABEL_DATE_LABEL_CUSTOM_GGF_NO_SPACE   FormKey_MUSTER_PTV_1 = "label_date_label_custom_ggf_no_space"
	MUSTER_PTV_1_DATE_LABEL_CUSTOM_GGF                  FormKey_MUSTER_PTV_1 = "date_label_custom_ggf"
	MUSTER_PTV_1_LABEL_PRF_NR                           FormKey_MUSTER_PTV_1 = "label_prf_nr"
	MUSTER_PTV_1_CHECKBOX_BEI_MIR                       FormKey_MUSTER_PTV_1 = "checkbox_bei_mir"
	MUSTER_PTV_1_LABEL_PATIENTINFO_LINE2                FormKey_MUSTER_PTV_1 = "label_patientInfo_line2"
	MUSTER_PTV_1_LABEL_PATIENTINFO_LINE1                FormKey_MUSTER_PTV_1 = "label_patientInfo_line1"
	MUSTER_PTV_1_LABEL_DATE_OF_BIRTH                    FormKey_MUSTER_PTV_1 = "label_date_of_birth"
	MUSTER_PTV_1_LABEL_INSURANCE_NAME                   FormKey_MUSTER_PTV_1 = "label_insurance_name"
	MUSTER_PTV_1_LABEL_WOP                              FormKey_MUSTER_PTV_1 = "label_wop"
	MUSTER_PTV_1_LABEL_PATIENTINFO_LINE3                FormKey_MUSTER_PTV_1 = "label_patientInfo_line3"
	MUSTER_PTV_1_LABEL_PATIENTINFO_LINE4                FormKey_MUSTER_PTV_1 = "label_patientInfo_line4"
	MUSTER_PTV_1_LABEL_IK_NUMBER                        FormKey_MUSTER_PTV_1 = "label_ik_number"
	MUSTER_PTV_1_LABEL_BSNR                             FormKey_MUSTER_PTV_1 = "label_bsnr"
	MUSTER_PTV_1_LABEL_INSURANCE_NUMBER                 FormKey_MUSTER_PTV_1 = "label_insurance_number"
	MUSTER_PTV_1_LABEL_LANR                             FormKey_MUSTER_PTV_1 = "label_lanr"
	MUSTER_PTV_1_DATE_PRESCRIBE                         FormKey_MUSTER_PTV_1 = "date_prescribe"
	MUSTER_PTV_1_LABEL_INSURANCE_END_DATE               FormKey_MUSTER_PTV_1 = "label_insurance_end_date"
	MUSTER_PTV_1_LABEL_INSURANCE_STATUS                 FormKey_MUSTER_PTV_1 = "label_insurance_status"
	MUSTER_PTV_1_AREA_TEXTBOX_ADDRESS                   FormKey_MUSTER_PTV_1 = "area_textbox_address"
	MUSTER_PTV_1_LABEL_PATIENT_FULLNAME_1               FormKey_MUSTER_PTV_1 = "label_patient_fullname_1"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_36 string

const (
	MUSTER_36_CHECKBOX_BEWEGUNGSGEWOHNHEITEN FormKey_MUSTER_36 = "checkbox_bewegungsgewohnheiten"
	MUSTER_36_LABEL_DOCTOR_STAMP             FormKey_MUSTER_36 = "label_doctor_stamp"
	MUSTER_36_CHECKBOX_ERNAHRUNG             FormKey_MUSTER_36 = "checkbox_ernahrung"
	MUSTER_36_CHECKBOX_STRESSMANAGEMENT      FormKey_MUSTER_36 = "checkbox_stressmanagement"
	MUSTER_36_CHECKBOX_SUCHTMITTELKONSUM     FormKey_MUSTER_36 = "checkbox_suchtmittelkonsum"
	MUSTER_36_TEXTBOX_SONSTIGES              FormKey_MUSTER_36 = "textbox_sonstiges"
	MUSTER_36_TEXTBOX_36_HINWEISE_LINE1      FormKey_MUSTER_36 = "textbox_36_hinweise_line1"
	MUSTER_36_TEXTBOX_36_HINWEISE_LINE2      FormKey_MUSTER_36 = "textbox_36_hinweise_line2"
	MUSTER_36_TEXTBOX_36_HINWEISE_LINE3      FormKey_MUSTER_36 = "textbox_36_hinweise_line3"
	MUSTER_36_TEXTBOX_36_HINWEISE_LINE4      FormKey_MUSTER_36 = "textbox_36_hinweise_line4"
	MUSTER_36_BARCODE                        FormKey_MUSTER_36 = "barcode"
	MUSTER_36_LABEL_PRF_NR                   FormKey_MUSTER_36 = "label_prf_nr"
	MUSTER_36_LABEL_PATIENTINFO_LINE2        FormKey_MUSTER_36 = "label_patientInfo_line2"
	MUSTER_36_LABEL_PATIENTINFO_LINE1        FormKey_MUSTER_36 = "label_patientInfo_line1"
	MUSTER_36_LABEL_DATE_OF_BIRTH            FormKey_MUSTER_36 = "label_date_of_birth"
	MUSTER_36_LABEL_INSURANCE_NAME           FormKey_MUSTER_36 = "label_insurance_name"
	MUSTER_36_LABEL_WOP                      FormKey_MUSTER_36 = "label_wop"
	MUSTER_36_LABEL_PATIENTINFO_LINE3        FormKey_MUSTER_36 = "label_patientInfo_line3"
	MUSTER_36_LABEL_PATIENTINFO_LINE4        FormKey_MUSTER_36 = "label_patientInfo_line4"
	MUSTER_36_LABEL_IK_NUMBER                FormKey_MUSTER_36 = "label_ik_number"
	MUSTER_36_LABEL_BSNR                     FormKey_MUSTER_36 = "label_bsnr"
	MUSTER_36_LABEL_INSURANCE_NUMBER         FormKey_MUSTER_36 = "label_insurance_number"
	MUSTER_36_LABEL_LANR                     FormKey_MUSTER_36 = "label_lanr"
	MUSTER_36_DATE_PRESCRIBE                 FormKey_MUSTER_36 = "date_prescribe"
	MUSTER_36_LABEL_INSURANCE_END_DATE       FormKey_MUSTER_36 = "label_insurance_end_date"
	MUSTER_36_LABEL_INSURANCE_STATUS         FormKey_MUSTER_36 = "label_insurance_status"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_52 string

const (
	MUSTER_52_TEXTBOX_WEGEN_ICD0                               FormKey_MUSTER_52 = "textbox_wegen_icd0"
	MUSTER_52_TEXTBOX_WEGEN_ICD5                               FormKey_MUSTER_52 = "textbox_wegen_icd5"
	MUSTER_52_TEXTBOX_WEGEN_ICD1                               FormKey_MUSTER_52 = "textbox_wegen_icd1"
	MUSTER_52_TEXTBOX_WEGEN_ICD2                               FormKey_MUSTER_52 = "textbox_wegen_icd2"
	MUSTER_52_TEXTBOX_WEGEN_ICD3                               FormKey_MUSTER_52 = "textbox_wegen_icd3"
	MUSTER_52_TEXTBOX_WEGEN_ICD4                               FormKey_MUSTER_52 = "textbox_wegen_icd4"
	MUSTER_52_TEXTBOX_WEGEN_ICD6                               FormKey_MUSTER_52 = "textbox_wegen_icd6"
	MUSTER_52_TEXTBOX_WEGEN_ICD7                               FormKey_MUSTER_52 = "textbox_wegen_icd7"
	MUSTER_52_TEXTBOX_WEGEN_ICD8                               FormKey_MUSTER_52 = "textbox_wegen_icd8"
	MUSTER_52_TEXTBOX_WEGEN_ICD9                               FormKey_MUSTER_52 = "textbox_wegen_icd9"
	MUSTER_52_TEXTBOX_WEGEN_ICD10                              FormKey_MUSTER_52 = "textbox_wegen_icd10"
	MUSTER_52_TEXTBOX_WEGEN_ICD11                              FormKey_MUSTER_52 = "textbox_wegen_icd11"
	MUSTER_52_TEXTBOX_ERWERBSTATIG                             FormKey_MUSTER_52 = "textbox_erwerbstatig"
	MUSTER_52_CHECKBOX_ERWERBSTATIG                            FormKey_MUSTER_52 = "checkbox_erwerbstatig"
	MUSTER_52_TEXTBOX_DER_VERSICHERTE                          FormKey_MUSTER_52 = "textbox_der_versicherte"
	MUSTER_52_CHECKBOX_KANNDER_EMPFANGER_JA                    FormKey_MUSTER_52 = "checkbox_kannder_empfanger_ja"
	MUSTER_52_CHECKBOX_KANNDER_EMPFANGER_NEIN                  FormKey_MUSTER_52 = "checkbox_kannder_empfanger_nein"
	MUSTER_52_CHECKBOX_DER_VERSICHERTE                         FormKey_MUSTER_52 = "checkbox_der_versicherte"
	MUSTER_52_CHECKBOX_ZEITPUNKT_NEIN                          FormKey_MUSTER_52 = "checkbox_zeitpunkt_nein"
	MUSTER_52_CHECKBOX_ZEITPUNKT_JA                            FormKey_MUSTER_52 = "checkbox_zeitpunkt_ja"
	MUSTER_52_TEXTBOX_WELCHE_KONSERVATIV                       FormKey_MUSTER_52 = "textbox_welche_konservativ"
	MUSTER_52_TEXTBOX_WEITERE_BEHANDELNDE                      FormKey_MUSTER_52 = "textbox_weitere_behandelnde"
	MUSTER_52_CHECKBOX_WELCHE_KEINE                            FormKey_MUSTER_52 = "checkbox_welche_keine"
	MUSTER_52_CHECKBOX_WELCHE_INNERBETRIEBLICHER               FormKey_MUSTER_52 = "checkbox_welche_innerbetrieblicher"
	MUSTER_52_CHECKBOX_WELCHE_STUFENWEISE                      FormKey_MUSTER_52 = "checkbox_welche_stufenweise"
	MUSTER_52_CHECKBOX_WELCHE_MEDIZINISCHE                     FormKey_MUSTER_52 = "checkbox_welche_medizinische"
	MUSTER_52_CHECKBOX_WELCHE_PSYCHOTHERAPEUTISCHE             FormKey_MUSTER_52 = "checkbox_welche_psychotherapeutische"
	MUSTER_52_CHECKBOX_WELCHE_LEISTUNG                         FormKey_MUSTER_52 = "checkbox_welche_leistung"
	MUSTER_52_CHECKBOX_GIBT_JA_FOLGENDE                        FormKey_MUSTER_52 = "checkbox_gibt_ja_folgende"
	MUSTER_52_CHECKBOX_BESTEHT_JA                              FormKey_MUSTER_52 = "checkbox_besteht_ja"
	MUSTER_52_CHECKBOX_BESTEHT_NEIN                            FormKey_MUSTER_52 = "checkbox_besteht_nein"
	MUSTER_52_LABEL_DOCTOR_STAMP                               FormKey_MUSTER_52 = "label_doctor_stamp"
	MUSTER_52_CHECKBOX_WELCHE_SONSTIGE                         FormKey_MUSTER_52 = "checkbox_welche_sonstige"
	MUSTER_52_CHECKBOX_GIBT_NEIN                               FormKey_MUSTER_52 = "checkbox_gibt_nein"
	MUSTER_52_TEXTBOX_WELCHE_OPERATIV                          FormKey_MUSTER_52 = "textbox_welche_operativ"
	MUSTER_52_TEXTBOX_WELCHE_SONSTIGE                          FormKey_MUSTER_52 = "textbox_welche_sonstige"
	MUSTER_52_BARCODE                                          FormKey_MUSTER_52 = "barcode"
	MUSTER_52_LABEL_TAKEOVER_DIAGNOSIS                         FormKey_MUSTER_52 = "label_takeover_diagnosis"
	MUSTER_52_LABEL_PRF_NR                                     FormKey_MUSTER_52 = "label_prf_nr"
	MUSTER_52_LABEL_PATIENTINFO_LINE2                          FormKey_MUSTER_52 = "label_patientInfo_line2"
	MUSTER_52_LABEL_PATIENTINFO_LINE1                          FormKey_MUSTER_52 = "label_patientInfo_line1"
	MUSTER_52_LABEL_DATE_OF_BIRTH                              FormKey_MUSTER_52 = "label_date_of_birth"
	MUSTER_52_LABEL_INSURANCE_NAME                             FormKey_MUSTER_52 = "label_insurance_name"
	MUSTER_52_LABEL_WOP                                        FormKey_MUSTER_52 = "label_wop"
	MUSTER_52_LABEL_PATIENTINFO_LINE3                          FormKey_MUSTER_52 = "label_patientInfo_line3"
	MUSTER_52_LABEL_PATIENTINFO_LINE4                          FormKey_MUSTER_52 = "label_patientInfo_line4"
	MUSTER_52_LABEL_IK_NUMBER                                  FormKey_MUSTER_52 = "label_ik_number"
	MUSTER_52_LABEL_BSNR                                       FormKey_MUSTER_52 = "label_bsnr"
	MUSTER_52_LABEL_INSURANCE_NUMBER                           FormKey_MUSTER_52 = "label_insurance_number"
	MUSTER_52_LABEL_LANR                                       FormKey_MUSTER_52 = "label_lanr"
	MUSTER_52_DATE_PRESCRIBE                                   FormKey_MUSTER_52 = "date_prescribe"
	MUSTER_52_LABEL_INSURANCE_END_DATE                         FormKey_MUSTER_52 = "label_insurance_end_date"
	MUSTER_52_LABEL_INSURANCE_STATUS                           FormKey_MUSTER_52 = "label_insurance_status"
	MUSTER_52_DATE_LABEL_FULL_DATE_WEIDEREINTRITTS             FormKey_MUSTER_52 = "date_label_full_date_weidereintritts"
	MUSTER_52_LABEL_DATE_LABEL_CUSTOM_WEIDEREINTRITTS_NO_SPACE FormKey_MUSTER_52 = "label_date_label_custom_weidereintritts_no_space"
	MUSTER_52_LABEL_DATE_LABEL_CUSTOM_DATUM_NO_SPACE           FormKey_MUSTER_52 = "label_date_label_custom_datum_no_space"
	MUSTER_52_DATE_LABEL_CUSTOM_DATUM                          FormKey_MUSTER_52 = "date_label_custom_datum"
	MUSTER_52_TEXTBOX_GIBT_ES_BEI_LINE1                        FormKey_MUSTER_52 = "textbox_gibt_es_bei_line1"
	MUSTER_52_TEXTBOX_GIBT_ES_BEI_LINE2                        FormKey_MUSTER_52 = "textbox_gibt_es_bei_line2"
	MUSTER_52_TEXTBOX_SONSTIGES_BEMERKUNGEN_LINE1              FormKey_MUSTER_52 = "textbox_sonstiges_bemerkungen_line1"
	MUSTER_52_TEXTBOX_SONSTIGES_BEMERKUNGEN_LINE2              FormKey_MUSTER_52 = "textbox_sonstiges_bemerkungen_line2"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_55 string

const (
	MUSTER_55_TEXTBOX_ICD1                                   FormKey_MUSTER_55 = "textbox_icd1"
	MUSTER_55_TEXTBOX_ICD2                                   FormKey_MUSTER_55 = "textbox_icd2"
	MUSTER_55_TEXTBOX_ICD3                                   FormKey_MUSTER_55 = "textbox_icd3"
	MUSTER_55_CHECKBOX_KONTINUIERLICHE_JA_ENDE               FormKey_MUSTER_55 = "checkbox_kontinuierliche_ja_ende"
	MUSTER_55_CHECKBOX_KONTINUIERLICHE_JA_VORAUSSICHTLICH    FormKey_MUSTER_55 = "checkbox_kontinuierliche_ja_voraussichtlich"
	MUSTER_55_CHECKBOX_KONTINUIERLICHE_NEIN                  FormKey_MUSTER_55 = "checkbox_kontinuierliche_nein"
	MUSTER_55_DATE_KONTINUIERLICHE                           FormKey_MUSTER_55 = "date_kontinuierliche"
	MUSTER_55_TEXTBOX_KRANKENVERSICHERTEN_NUMBER             FormKey_MUSTER_55 = "textbox_krankenversicherten_number"
	MUSTER_55_TEXTBOX_KRANKENKASSE                           FormKey_MUSTER_55 = "textbox_krankenkasse"
	MUSTER_55_TEXTBOX_VORNAME                                FormKey_MUSTER_55 = "textbox_vorname"
	MUSTER_55_BARCODE                                        FormKey_MUSTER_55 = "barcode"
	MUSTER_55_LABEL_DOCTOR_STAMP                             FormKey_MUSTER_55 = "label_doctor_stamp"
	MUSTER_55_LABEL_PRF_NR                                   FormKey_MUSTER_55 = "label_prf_nr"
	MUSTER_55_LABEL_PATIENTINFO_LINE2                        FormKey_MUSTER_55 = "label_patientInfo_line2"
	MUSTER_55_LABEL_PATIENTINFO_LINE1                        FormKey_MUSTER_55 = "label_patientInfo_line1"
	MUSTER_55_LABEL_DATE_OF_BIRTH                            FormKey_MUSTER_55 = "label_date_of_birth"
	MUSTER_55_LABEL_INSURANCE_NAME                           FormKey_MUSTER_55 = "label_insurance_name"
	MUSTER_55_LABEL_WOP                                      FormKey_MUSTER_55 = "label_wop"
	MUSTER_55_LABEL_PATIENTINFO_LINE3                        FormKey_MUSTER_55 = "label_patientInfo_line3"
	MUSTER_55_LABEL_PATIENTINFO_LINE4                        FormKey_MUSTER_55 = "label_patientInfo_line4"
	MUSTER_55_LABEL_IK_NUMBER                                FormKey_MUSTER_55 = "label_ik_number"
	MUSTER_55_LABEL_BSNR                                     FormKey_MUSTER_55 = "label_bsnr"
	MUSTER_55_LABEL_INSURANCE_NUMBER                         FormKey_MUSTER_55 = "label_insurance_number"
	MUSTER_55_LABEL_LANR                                     FormKey_MUSTER_55 = "label_lanr"
	MUSTER_55_DATE_PRESCRIBE                                 FormKey_MUSTER_55 = "date_prescribe"
	MUSTER_55_LABEL_INSURANCE_END_DATE                       FormKey_MUSTER_55 = "label_insurance_end_date"
	MUSTER_55_LABEL_INSURANCE_STATUS                         FormKey_MUSTER_55 = "label_insurance_status"
	MUSTER_55_LABEL_MONTH_YEAR_HAVE_DOT_DATE_KONTINUIERLICHE FormKey_MUSTER_55 = "label_month_year_have_dot_date_kontinuierliche"
	MUSTER_55_LABEL_DATE_LABEL_CUSTOM_SEIT_NO_SPACE          FormKey_MUSTER_55 = "label_date_label_custom_seit_no_space"
	MUSTER_55_DATE_LABEL_CUSTOM_SEIT                         FormKey_MUSTER_55 = "date_label_custom_seit"
	MUSTER_55_LABEL_DATE_LABEL_CUSTOM_DATUM_NO_SPACE         FormKey_MUSTER_55 = "label_date_label_custom_datum_no_space"
	MUSTER_55_DATE_LABEL_CUSTOM_DATUM                        FormKey_MUSTER_55 = "date_label_custom_datum"
	MUSTER_55_LABEL_TAKEOVER_DIAGNOSIS                       FormKey_MUSTER_55 = "label_takeover_diagnosis"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_61 string

const (
	MUSTER_61_CHECKBOX_BERATUNG_0                      FormKey_MUSTER_61 = "checkbox_beratung_0"
	MUSTER_61_CHECKBOX_PRUFUNG_0                       FormKey_MUSTER_61 = "checkbox_prufung_0"
	MUSTER_61_TEXTBOX_WEITERE_LINE1_0                  FormKey_MUSTER_61 = "textbox_weitere_line1_0"
	MUSTER_61_TEXTBOX_WEITERE_LINE2_0                  FormKey_MUSTER_61 = "textbox_weitere_line2_0"
	MUSTER_61_LABEL_DOCTOR_STAMP_0                     FormKey_MUSTER_61 = "label_doctor_stamp_0"
	MUSTER_61_CHECKBOX_KRANKENKASSE_0                  FormKey_MUSTER_61 = "checkbox_krankenkasse_0"
	MUSTER_61_CHECKBOX_RENTENVERSICHERUNG_0            FormKey_MUSTER_61 = "checkbox_rentenversicherung_0"
	MUSTER_61_CHECKBOX_SONSTIGES_0                     FormKey_MUSTER_61 = "checkbox_sonstiges_0"
	MUSTER_61_TEXTBOX_SONSTIGE_LINE1_0                 FormKey_MUSTER_61 = "textbox_sonstige_line1_0"
	MUSTER_61_TEXTBOX_SONSTIGE_LINE2_0                 FormKey_MUSTER_61 = "textbox_sonstige_line2_0"
	MUSTER_61_TEXTBOX_SONSTIGE_LINE3_0                 FormKey_MUSTER_61 = "textbox_sonstige_line3_0"
	MUSTER_61_BARCODE_0                                FormKey_MUSTER_61 = "barcode_0"
	MUSTER_61_LABEL_TAKEOVER_DIAGNOSIS_LINE2_0         FormKey_MUSTER_61 = "label_takeover_diagnosis_line2_0"
	MUSTER_61_TEXTBOX_ICD10_CODE1_0                    FormKey_MUSTER_61 = "textbox_icd10_code1_0"
	MUSTER_61_TEXTBOX_ICD10_CODE2_0                    FormKey_MUSTER_61 = "textbox_icd10_code2_0"
	MUSTER_61_TEXTBOX_ICD10_CODE3_0                    FormKey_MUSTER_61 = "textbox_icd10_code3_0"
	MUSTER_61_TEXTBOX_ICD10_CODE4_0                    FormKey_MUSTER_61 = "textbox_icd10_code4_0"
	MUSTER_61_TEXTBOX_ICD10_CODE5_0                    FormKey_MUSTER_61 = "textbox_icd10_code5_0"
	MUSTER_61_TEXTBOX_ICD10_CODE6_0                    FormKey_MUSTER_61 = "textbox_icd10_code6_0"
	MUSTER_61_LABEL_TAKEOVER_DIAGNOSIS_LINE1_0         FormKey_MUSTER_61 = "label_takeover_diagnosis_line1_0"
	MUSTER_61_LABEL_PRF_NR_0                           FormKey_MUSTER_61 = "label_prf_nr_0"
	MUSTER_61_LABEL_INSURANCE_NAME_0                   FormKey_MUSTER_61 = "label_insurance_name_0"
	MUSTER_61_LABEL_WOP_0                              FormKey_MUSTER_61 = "label_wop_0"
	MUSTER_61_LABEL_PATIENTINFO_LINE1_0                FormKey_MUSTER_61 = "label_patientInfo_line1_0"
	MUSTER_61_LABEL_DATE_OF_BIRTH_0                    FormKey_MUSTER_61 = "label_date_of_birth_0"
	MUSTER_61_LABEL_PATIENTINFO_LINE2_0                FormKey_MUSTER_61 = "label_patientInfo_line2_0"
	MUSTER_61_LABEL_PATIENTINFO_LINE3_0                FormKey_MUSTER_61 = "label_patientInfo_line3_0"
	MUSTER_61_LABEL_PATIENTINFO_LINE4_0                FormKey_MUSTER_61 = "label_patientInfo_line4_0"
	MUSTER_61_LABEL_INSURANCE_END_DATE_0               FormKey_MUSTER_61 = "label_insurance_end_date_0"
	MUSTER_61_LABEL_IK_NUMBER_0                        FormKey_MUSTER_61 = "label_ik_number_0"
	MUSTER_61_LABEL_INSURANCE_NUMBER_0                 FormKey_MUSTER_61 = "label_insurance_number_0"
	MUSTER_61_LABEL_INSURANCE_STATUS_0                 FormKey_MUSTER_61 = "label_insurance_status_0"
	MUSTER_61_LABEL_BSNR_0                             FormKey_MUSTER_61 = "label_bsnr_0"
	MUSTER_61_LABEL_LANR_0                             FormKey_MUSTER_61 = "label_lanr_0"
	MUSTER_61_DATE_PRESCRIBE_0                         FormKey_MUSTER_61 = "date_prescribe_0"
	MUSTER_61_TEXTBOX_DIAGNOSE1_0                      FormKey_MUSTER_61 = "textbox_diagnose1_0"
	MUSTER_61_TEXTBOX_DIAGNOSE2_0                      FormKey_MUSTER_61 = "textbox_diagnose2_0"
	MUSTER_61_TEXTBOX_DIAGNOSE3_0                      FormKey_MUSTER_61 = "textbox_diagnose3_0"
	MUSTER_61_TEXTBOX_DIAGNOSE4_0                      FormKey_MUSTER_61 = "textbox_diagnose4_0"
	MUSTER_61_TEXTBOX_DIAGNOSE5_0                      FormKey_MUSTER_61 = "textbox_diagnose5_0"
	MUSTER_61_TEXTBOX_DIAGNOSE6_0                      FormKey_MUSTER_61 = "textbox_diagnose6_0"
	MUSTER_61_LABEL_DATE_LABEL_CUSTOM_DATUM_0_NO_SPACE FormKey_MUSTER_61 = "label_date_label_custom_datum_0_no_space"
	MUSTER_61_DATE_LABEL_CUSTOM_DATUM_0                FormKey_MUSTER_61 = "date_label_custom_datum_0"
	MUSTER_61_TEXTBOX_URSACHE1_0                       FormKey_MUSTER_61 = "textbox_ursache1_0"
	MUSTER_61_TEXTBOX_URSACHE2_0                       FormKey_MUSTER_61 = "textbox_ursache2_0"
	MUSTER_61_TEXTBOX_URSACHE3_0                       FormKey_MUSTER_61 = "textbox_ursache3_0"
	MUSTER_61_TEXTBOX_URSACHE4_0                       FormKey_MUSTER_61 = "textbox_ursache4_0"
	MUSTER_61_TEXTBOX_URSACHE5_0                       FormKey_MUSTER_61 = "textbox_ursache5_0"
	MUSTER_61_TEXTBOX_URSACHE6_0                       FormKey_MUSTER_61 = "textbox_ursache6_0"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_64 string

const (
	MUSTER_64_TEXTBOX_VORSORGERELEVANTE_A_LINE1_0                FormKey_MUSTER_64 = "textbox_vorsorgerelevante_a_line1_0"
	MUSTER_64_TEXTBOX_VORSORGERELEVANTE_A_LINE2_0                FormKey_MUSTER_64 = "textbox_vorsorgerelevante_a_line2_0"
	MUSTER_64_TEXTBOX_VORSORGEBEDURFTIGKEIT_A_LINE1_0            FormKey_MUSTER_64 = "textbox_vorsorgebedurftigkeit_a_line1_0"
	MUSTER_64_TEXTBOX_VORSORGEBEDURFTIGKEIT_A_LINE2_0            FormKey_MUSTER_64 = "textbox_vorsorgebedurftigkeit_a_line2_0"
	MUSTER_64_TEXTBOX_VORSORGEBEDURFTIGKEIT_A_LINE3_0            FormKey_MUSTER_64 = "textbox_vorsorgebedurftigkeit_a_line3_0"
	MUSTER_64_TEXTBOX_VORSORGEBEDURFTIGKEIT_B_LINE1_0            FormKey_MUSTER_64 = "textbox_vorsorgebedurftigkeit_b_line1_0"
	MUSTER_64_TEXTBOX_VORSORGEBEDURFTIGKEIT_B_LINE2_0            FormKey_MUSTER_64 = "textbox_vorsorgebedurftigkeit_b_line2_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_ERZIEHUNGS_0              FormKey_MUSTER_64 = "checkbox_kontextfaktoren_erziehungs_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_MANGELNDE_0               FormKey_MUSTER_64 = "checkbox_kontextfaktoren_mangelnde_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_MEHRFACHBELASTUNG_0       FormKey_MUSTER_64 = "checkbox_kontextfaktoren_mehrfachbelastung_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_SCHWIERIGKEITEN_0         FormKey_MUSTER_64 = "checkbox_kontextfaktoren_schwierigkeiten_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_MANGELNDE_GRUND_0         FormKey_MUSTER_64 = "checkbox_kontextfaktoren_mangelnde_grund_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_ERHOHTER_0                FormKey_MUSTER_64 = "checkbox_kontextfaktoren_erhohter_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_FEHLENDE_0                FormKey_MUSTER_64 = "checkbox_kontextfaktoren_fehlende_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_PARTNER_0                 FormKey_MUSTER_64 = "checkbox_kontextfaktoren_partner_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_PFLEGE_0                  FormKey_MUSTER_64 = "checkbox_kontextfaktoren_pflege_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_TEENAGER_0                FormKey_MUSTER_64 = "checkbox_kontextfaktoren_teenager_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_SOZIALE_0                 FormKey_MUSTER_64 = "checkbox_kontextfaktoren_soziale_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_FINANZIELLE_0             FormKey_MUSTER_64 = "checkbox_kontextfaktoren_finanzielle_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_STANDIGER_0               FormKey_MUSTER_64 = "checkbox_kontextfaktoren_standiger_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_ALLEIN_0                  FormKey_MUSTER_64 = "checkbox_kontextfaktoren_allein_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_TOD_0                     FormKey_MUSTER_64 = "checkbox_kontextfaktoren_tod_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_BEEINTRACHTIGTE_0         FormKey_MUSTER_64 = "checkbox_kontextfaktoren_beeintrachtigte_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_UBERFORDERUNGSSITUA_0     FormKey_MUSTER_64 = "checkbox_kontextfaktoren_uberforderungssitua_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_SPRACHLICHE_0             FormKey_MUSTER_64 = "checkbox_kontextfaktoren_sprachliche_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_BESONDERS_0               FormKey_MUSTER_64 = "checkbox_kontextfaktoren_besonders_0"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_NICHT_0                   FormKey_MUSTER_64 = "checkbox_kontextfaktoren_nicht_0"
	MUSTER_64_TEXTBOX_VORSORGEBEDURFTIGKEIT_B_LINE3_0            FormKey_MUSTER_64 = "textbox_vorsorgebedurftigkeit_b_line3_0"
	MUSTER_64_TEXTBOX_ANDERE_KONTEXT_LINE1_0                     FormKey_MUSTER_64 = "textbox_andere_kontext_line1_0"
	MUSTER_64_TEXTBOX_ANDERE_KONTEXT_LINE2_0                     FormKey_MUSTER_64 = "textbox_andere_kontext_line2_0"
	MUSTER_64_TEXTBOX_KRANKENBEHANDLUNG_A_LINE1_1                FormKey_MUSTER_64 = "textbox_krankenbehandlung_a_line1_1"
	MUSTER_64_CHECKBOX_KRANKENBEHANDLUNG_HEILMITTEL_1            FormKey_MUSTER_64 = "checkbox_krankenbehandlung_heilmittel_1"
	MUSTER_64_TEXTBOX_KRANKENBEHANDLUNG_B_LINE1_1                FormKey_MUSTER_64 = "textbox_krankenbehandlung_b_line1_1"
	MUSTER_64_TEXTBOX_KRANKENBEHANDLUNG_B_LINE2_1                FormKey_MUSTER_64 = "textbox_krankenbehandlung_b_line2_1"
	MUSTER_64_TEXTBOX_VORSORGEZIELE_LINE1_1                      FormKey_MUSTER_64 = "textbox_vorsorgeziele_line1_1"
	MUSTER_64_TEXTBOX_VORSORGEZIELE_LINE2_1                      FormKey_MUSTER_64 = "textbox_vorsorgeziele_line2_1"
	MUSTER_64_CHECKBOX_ZUWEISUNGSEM_EMPFOHLENCE_1                FormKey_MUSTER_64 = "checkbox_zuweisungsem_empfohlence_1"
	MUSTER_64_CHECKBOX_ZUWEISUNGSEM_VATER_1                      FormKey_MUSTER_64 = "checkbox_zuweisungsem_vater_1"
	MUSTER_64_CHECKBOX_ZUWEISUNGSEM_MUTTER_1                     FormKey_MUSTER_64 = "checkbox_zuweisungsem_mutter_1"
	MUSTER_64_CHECKBOX_ZUWEISUNGSEM_VATER_KIND_1                 FormKey_MUSTER_64 = "checkbox_zuweisungsem_vater-kind_1"
	MUSTER_64_CHECKBOX_ZUWEISUNGSEM_ATTEST_LINE1_1               FormKey_MUSTER_64 = "checkbox_zuweisungsem_attest_line1_1"
	MUSTER_64_CHECKBOX_ZUWEISUNGSEM_ATTEST_LINE2_1               FormKey_MUSTER_64 = "checkbox_zuweisungsem_attest_line2_1"
	MUSTER_64_CHECKBOX_ZUWEISUNGSEM_ATTEST_LINE3_1               FormKey_MUSTER_64 = "checkbox_zuweisungsem_attest_line3_1"
	MUSTER_64_CHECKBOX_ZUWEISUNGSEM_BELASTETE_LINE1_1            FormKey_MUSTER_64 = "checkbox_zuweisungsem_belastete_line1_1"
	MUSTER_64_CHECKBOX_ZUWEISUNGSEM_BELASTETE_LINE2_1            FormKey_MUSTER_64 = "checkbox_zuweisungsem_belastete_line2_1"
	MUSTER_64_CHECKBOX_ZUWEISUNGSEM_BELASTETE_LINE3_1            FormKey_MUSTER_64 = "checkbox_zuweisungsem_belastete_line3_1"
	MUSTER_64_CHECKBOX_ZUWEISUNGSEM_PSYCHOSOZIABLE_LINE1_1       FormKey_MUSTER_64 = "checkbox_zuweisungsem_psychosoziable_line1_1"
	MUSTER_64_CHECKBOX_ZUWEISUNGSEM_PSYCHOSOZIABLE_LINE2_1       FormKey_MUSTER_64 = "checkbox_zuweisungsem_psychosoziable_line2_1"
	MUSTER_64_CHECKBOX_ZUWEISUNGSEM_PSYCHOSOZIABLE_LINE3_1       FormKey_MUSTER_64 = "checkbox_zuweisungsem_psychosoziable_line3_1"
	MUSTER_64_CHECKBOX_SONSTIGE_A_JA_1                           FormKey_MUSTER_64 = "checkbox_sonstige_a_ja_1"
	MUSTER_64_CHECKBOX_SONSTIGE_B_JA_1                           FormKey_MUSTER_64 = "checkbox_sonstige_b_ja_1"
	MUSTER_64_TEXTBOX_SONSTIGE_A_JA_1                            FormKey_MUSTER_64 = "textbox_sonstige_a_ja_1"
	MUSTER_64_TEXTBOX_SONSTIGE_C_SONSTIGES_LINE1_1               FormKey_MUSTER_64 = "textbox_sonstige_c_sonstiges_line1_1"
	MUSTER_64_TEXTBOX_SONSTIGE_C_SONSTIGES_LINE2_1               FormKey_MUSTER_64 = "textbox_sonstige_c_sonstiges_line2_1"
	MUSTER_64_TEXTBOX_SONSTIGE_D_RUCKRUF_1                       FormKey_MUSTER_64 = "textbox_sonstige_d_ruckruf_1"
	MUSTER_64_LABEL_DOCTOR_STAMP_1                               FormKey_MUSTER_64 = "label_doctor_stamp_1"
	MUSTER_64_TEXTBOX_KRANKENBEHANDLUNG_A_LINE2_1                FormKey_MUSTER_64 = "textbox_krankenbehandlung_a_line2_1"
	MUSTER_64_TEXTBOX_KRANKENBEHANDLUNG_A_LINE3_1                FormKey_MUSTER_64 = "textbox_krankenbehandlung_a_line3_1"
	MUSTER_64_CHECKBOX_KONTEXTFAKTOREN_DROHENDER_0               FormKey_MUSTER_64 = "checkbox_kontextfaktoren_drohender_0"
	MUSTER_64_TEXTBOX_SONSTIGE_B_JA_1                            FormKey_MUSTER_64 = "textbox_sonstige_b_ja_1"
	MUSTER_64_LABEL_TAKEOVER_DIAGNOSIS_0                         FormKey_MUSTER_64 = "label_takeover_diagnosis_0"
	MUSTER_64_TEXTBOX_ICD1_0                                     FormKey_MUSTER_64 = "textbox_icd1_0"
	MUSTER_64_TEXTBOX_ICD2_0                                     FormKey_MUSTER_64 = "textbox_icd2_0"
	MUSTER_64_TEXTBOX_ICD3_0                                     FormKey_MUSTER_64 = "textbox_icd3_0"
	MUSTER_64_LABEL_PRF_NR_1                                     FormKey_MUSTER_64 = "label_prf_nr_1"
	MUSTER_64_LABEL_PRF_NR_0                                     FormKey_MUSTER_64 = "label_prf_nr_0"
	MUSTER_64_TEXTBOX_SINGLE_DIAGNOSE1_0                         FormKey_MUSTER_64 = "textbox_single_diagnose1_0"
	MUSTER_64_TEXTBOX_SINGLE_DIAGNOSE2_0                         FormKey_MUSTER_64 = "textbox_single_diagnose2_0"
	MUSTER_64_TEXTBOX_SINGLE_DIAGNOSE3_0                         FormKey_MUSTER_64 = "textbox_single_diagnose3_0"
	MUSTER_64_LABEL_PATIENT_FULLNAME_1                           FormKey_MUSTER_64 = "label_patient_fullname_1"
	MUSTER_64_LABEL_IK_NUMBER_1                                  FormKey_MUSTER_64 = "label_ik_number_1"
	MUSTER_64_LABEL_INSURANCE_NUMBER_1                           FormKey_MUSTER_64 = "label_insurance_number_1"
	MUSTER_64_TEXTBOX_ZUWEISUNGSEM_NAME_DES_KINDES1_1            FormKey_MUSTER_64 = "textbox_zuweisungsem_name_des_kindes1_1"
	MUSTER_64_TEXTBOX_ZUWEISUNGSEM_NAME_DES_KINDES2_1            FormKey_MUSTER_64 = "textbox_zuweisungsem_name_des_kindes2_1"
	MUSTER_64_TEXTBOX_ZUWEISUNGSEM_NAME_DES_KINDES3_1            FormKey_MUSTER_64 = "textbox_zuweisungsem_name_des_kindes3_1"
	MUSTER_64_DATE_LABEL_CUSTOM_DATUM_1                          FormKey_MUSTER_64 = "date_label_custom_datum_1"
	MUSTER_64_LABEL_DATE_LABEL_CUSTOM_DATUM_1_NO_SPACE_1         FormKey_MUSTER_64 = "label_date_label_custom_datum_1_no_space_1"
	MUSTER_64_LABEL_DATE_LABEL_CUSTOM_GEBURTSDATUM1_1_NO_SPACE_1 FormKey_MUSTER_64 = "label_date_label_custom_geburtsdatum1_1_no_space_1"
	MUSTER_64_DATE_LABEL_CUSTOM_GEBURTSDATUM1_1                  FormKey_MUSTER_64 = "date_label_custom_geburtsdatum1_1"
	MUSTER_64_LABEL_DATE_LABEL_CUSTOM_GEBURTSDATUM2_1_NO_SPACE_1 FormKey_MUSTER_64 = "label_date_label_custom_geburtsdatum2_1_no_space_1"
	MUSTER_64_DATE_LABEL_CUSTOM_GEBURTSDATUM2_1                  FormKey_MUSTER_64 = "date_label_custom_geburtsdatum2_1"
	MUSTER_64_LABEL_DATE_LABEL_CUSTOM_GEBURTSDATUM3_1_NO_SPACE_1 FormKey_MUSTER_64 = "label_date_label_custom_geburtsdatum3_1_no_space_1"
	MUSTER_64_DATE_LABEL_CUSTOM_GEBURTSDATUM3_1                  FormKey_MUSTER_64 = "date_label_custom_geburtsdatum3_1"
	MUSTER_64_LABEL_PATIENTINFO_LINE2_0                          FormKey_MUSTER_64 = "label_patientInfo_line2_0"
	MUSTER_64_LABEL_PATIENTINFO_LINE1_0                          FormKey_MUSTER_64 = "label_patientInfo_line1_0"
	MUSTER_64_LABEL_DATE_OF_BIRTH_0                              FormKey_MUSTER_64 = "label_date_of_birth_0"
	MUSTER_64_LABEL_INSURANCE_NAME_0                             FormKey_MUSTER_64 = "label_insurance_name_0"
	MUSTER_64_LABEL_WOP_0                                        FormKey_MUSTER_64 = "label_wop_0"
	MUSTER_64_LABEL_PATIENTINFO_LINE3_0                          FormKey_MUSTER_64 = "label_patientInfo_line3_0"
	MUSTER_64_LABEL_PATIENTINFO_LINE4_0                          FormKey_MUSTER_64 = "label_patientInfo_line4_0"
	MUSTER_64_LABEL_IK_NUMBER_0                                  FormKey_MUSTER_64 = "label_ik_number_0"
	MUSTER_64_LABEL_BSNR_0                                       FormKey_MUSTER_64 = "label_bsnr_0"
	MUSTER_64_LABEL_INSURANCE_NUMBER_0                           FormKey_MUSTER_64 = "label_insurance_number_0"
	MUSTER_64_LABEL_LANR_0                                       FormKey_MUSTER_64 = "label_lanr_0"
	MUSTER_64_DATE_PRESCRIBE_0                                   FormKey_MUSTER_64 = "date_prescribe_0"
	MUSTER_64_LABEL_INSURANCE_END_DATE_0                         FormKey_MUSTER_64 = "label_insurance_end_date_0"
	MUSTER_64_LABEL_INSURANCE_STATUS_0                           FormKey_MUSTER_64 = "label_insurance_status_0"
	MUSTER_64_BARCODE_1                                          FormKey_MUSTER_64 = "barcode_1"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_22A_N string

const (
	MUSTER_22A_N_LABEL_PATIENTINFO_LINE2                FormKey_MUSTER_22A_N = "label_patientInfo_line2"
	MUSTER_22A_N_LABEL_PATIENTINFO_LINE1                FormKey_MUSTER_22A_N = "label_patientInfo_line1"
	MUSTER_22A_N_LABEL_DATE_OF_BIRTH                    FormKey_MUSTER_22A_N = "label_date_of_birth"
	MUSTER_22A_N_LABEL_INSURANCE_NAME                   FormKey_MUSTER_22A_N = "label_insurance_name"
	MUSTER_22A_N_LABEL_WOP                              FormKey_MUSTER_22A_N = "label_wop"
	MUSTER_22A_N_LABEL_PATIENTINFO_LINE3                FormKey_MUSTER_22A_N = "label_patientInfo_line3"
	MUSTER_22A_N_LABEL_PATIENTINFO_LINE4                FormKey_MUSTER_22A_N = "label_patientInfo_line4"
	MUSTER_22A_N_LABEL_IK_NUMBER                        FormKey_MUSTER_22A_N = "label_ik_number"
	MUSTER_22A_N_LABEL_BSNR                             FormKey_MUSTER_22A_N = "label_bsnr"
	MUSTER_22A_N_LABEL_INSURANCE_NUMBER                 FormKey_MUSTER_22A_N = "label_insurance_number"
	MUSTER_22A_N_LABEL_LANR                             FormKey_MUSTER_22A_N = "label_lanr"
	MUSTER_22A_N_DATE_PRESCRIBE                         FormKey_MUSTER_22A_N = "date_prescribe"
	MUSTER_22A_N_LABEL_INSURANCE_END_DATE               FormKey_MUSTER_22A_N = "label_insurance_end_date"
	MUSTER_22A_N_LABEL_INSURANCE_STATUS                 FormKey_MUSTER_22A_N = "label_insurance_status"
	MUSTER_22A_N_LABEL_DOCTOR_STAMP                     FormKey_MUSTER_22A_N = "label_doctor_stamp"
	MUSTER_22A_N_LABEL_PRF_NR                           FormKey_MUSTER_22A_N = "label_prf_nr"
	MUSTER_22A_N_TEXTBOX_NAME_DES_THERAPEUTEN           FormKey_MUSTER_22A_N = "textbox_name_des_therapeuten"
	MUSTER_22A_N_AREA_TEXTBOX_ANGABEN                   FormKey_MUSTER_22A_N = "area_textbox_angaben"
	MUSTER_22A_N_LABEL_DATE_TEXTBOX_ARZTNUMMER_NO_SPACE FormKey_MUSTER_22A_N = "label_date_textbox_arztnummer_no_space"
	MUSTER_22A_N_TEXTBOX_CUSTOM_ARZTNUMMER              FormKey_MUSTER_22A_N = "textbox_custom_arztnummer"
	MUSTER_22A_N_LABEL_TEXTBOX_CUSTOM_BETRIEB_NO_SPACE  FormKey_MUSTER_22A_N = "label_textbox_custom_betrieb_no_space"
	MUSTER_22A_N_TEXTBOX_CUSTOM_BETRIEB                 FormKey_MUSTER_22A_N = "textbox_custom_betrieb"
	MUSTER_22A_N_CHECKBOX_NICHT_ERFORDERLICH            FormKey_MUSTER_22A_N = "checkbox_nicht_erforderlich"
	MUSTER_22A_N_CHECKBOX_ERFOLGT                       FormKey_MUSTER_22A_N = "checkbox_erfolgt"
	MUSTER_22A_N_CHECKBOX_VERANLASST                    FormKey_MUSTER_22A_N = "checkbox_veranlasst"
	MUSTER_22A_N_CHECKBOX_ERFORDERLICH                  FormKey_MUSTER_22A_N = "checkbox_erforderlich"
	MUSTER_22A_N_CHECKBOX_AUFGRUND                      FormKey_MUSTER_22A_N = "checkbox_aufgrund"
	MUSTER_22A_N_CHECKBOX_ARZTLICHE                     FormKey_MUSTER_22A_N = "checkbox_arztliche"
	MUSTER_22A_N_LABEL_DATE_LABEL_CUSTOM_DATUM_NO_SPACE FormKey_MUSTER_22A_N = "label_date_label_custom_datum_no_space"
	MUSTER_22A_N_DATE_LABEL_CUSTOM_DATUM                FormKey_MUSTER_22A_N = "date_label_custom_datum"
	MUSTER_22A_N_TEXTBOX_SIND_LINE1                     FormKey_MUSTER_22A_N = "textbox_sind_line1"
	MUSTER_22A_N_TEXTBOX_SIND_LINE2                     FormKey_MUSTER_22A_N = "textbox_sind_line2"
	MUSTER_22A_N_TEXTBOX_ARZTLICHE_LINE1                FormKey_MUSTER_22A_N = "textbox_arztliche_line1"
	MUSTER_22A_N_TEXTBOX_ARZTLICHE_LINE2                FormKey_MUSTER_22A_N = "textbox_arztliche_line2"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_65A string

const (
	MUSTER_65A_TEXTBOX_65_KURZE_ANGABEN_LINE1_0       FormKey_MUSTER_65A = "textbox_65_kurze_angaben_line1_0"
	MUSTER_65A_TEXTBOX_65_KURZE_ANGABEN_LINE2_0       FormKey_MUSTER_65A = "textbox_65_kurze_angaben_line2_0"
	MUSTER_65A_TEXTBOX_65_VORLIEGENDE_GES_LINE1_0     FormKey_MUSTER_65A = "textbox_65_vorliegende_ges_line1_0"
	MUSTER_65A_TEXTBOX_65_VORLIEGENDE_GES_LINE2_0     FormKey_MUSTER_65A = "textbox_65_vorliegende_ges_line2_0"
	MUSTER_65A_TEXTBOX_65_VORLIEGENDE_WEI_LINE1_0     FormKey_MUSTER_65A = "textbox_65_vorliegende_wei_line1_0"
	MUSTER_65A_TEXTBOX_65_VORLIEGENDE_WEI_LINE2_0     FormKey_MUSTER_65A = "textbox_65_vorliegende_wei_line2_0"
	MUSTER_65A_TEXTBOX_65_BISHERIGE_LINE1_0           FormKey_MUSTER_65A = "textbox_65_bisherige_line1_0"
	MUSTER_65A_TEXTBOX_65_BISHERIGE_LINE2_0           FormKey_MUSTER_65A = "textbox_65_bisherige_line2_0"
	MUSTER_65A_TEXTBOX_65_EMPFOHLENE_LINE1_0          FormKey_MUSTER_65A = "textbox_65_empfohlene_line1_0"
	MUSTER_65A_TEXTBOX_65_EMPFOHLENE_LINE2_0          FormKey_MUSTER_65A = "textbox_65_empfohlene_line2_0"
	MUSTER_65A_TEXTBOX_65_HINWEISE_LINE1_0            FormKey_MUSTER_65A = "textbox_65_hinweise_line1_0"
	MUSTER_65A_TEXTBOX_65_HINWEISE_LINE2_0            FormKey_MUSTER_65A = "textbox_65_hinweise_line2_0"
	MUSTER_65A_CHECKBOX_65_HINWEISE_0                 FormKey_MUSTER_65A = "checkbox_65_hinweise_0"
	MUSTER_65A_LABEL_DOCTOR_STAMP_0                   FormKey_MUSTER_65A = "label_doctor_stamp_0"
	MUSTER_65A_CHECKBOX_JA_FOLGENDE_0                 FormKey_MUSTER_65A = "checkbox_ja-folgende_0"
	MUSTER_65A_TEXTBOX_ICD1_0                         FormKey_MUSTER_65A = "textbox_icd1_0"
	MUSTER_65A_TEXTBOX_ICD2_0                         FormKey_MUSTER_65A = "textbox_icd2_0"
	MUSTER_65A_TEXTBOX_ICD3_0                         FormKey_MUSTER_65A = "textbox_icd3_0"
	MUSTER_65A_LABEL_TAKEOVER_DIAGNOSIS_0             FormKey_MUSTER_65A = "label_takeover_diagnosis_0"
	MUSTER_65A_TEXTBOX_SINGLE_DIAGNOSE_LINE1_0        FormKey_MUSTER_65A = "textbox_single_diagnose_line1_0"
	MUSTER_65A_TEXTBOX_SINGLE_DIAGNOSE_LINE2_0        FormKey_MUSTER_65A = "textbox_single_diagnose_line2_0"
	MUSTER_65A_TEXTBOX_SINGLE_DIAGNOSE_LINE3_0        FormKey_MUSTER_65A = "textbox_single_diagnose_line3_0"
	MUSTER_65A_LABEL_DATE_LABEL_CUSTOM_DATUM_NO_SPACE FormKey_MUSTER_65A = "label_date_label_custom_datum_no_space"
	MUSTER_65A_DATE_LABEL_CUSTOM_DATUM                FormKey_MUSTER_65A = "date_label_custom_datum"
	MUSTER_65A_LABEL_PRF_NR                           FormKey_MUSTER_65A = "label_prf_nr"
	MUSTER_65A_LABEL_PATIENTINFO_LINE2                FormKey_MUSTER_65A = "label_patientInfo_line2"
	MUSTER_65A_LABEL_PATIENTINFO_LINE1                FormKey_MUSTER_65A = "label_patientInfo_line1"
	MUSTER_65A_LABEL_DATE_OF_BIRTH                    FormKey_MUSTER_65A = "label_date_of_birth"
	MUSTER_65A_LABEL_INSURANCE_NAME                   FormKey_MUSTER_65A = "label_insurance_name"
	MUSTER_65A_LABEL_WOP                              FormKey_MUSTER_65A = "label_wop"
	MUSTER_65A_LABEL_PATIENTINFO_LINE3                FormKey_MUSTER_65A = "label_patientInfo_line3"
	MUSTER_65A_LABEL_PATIENTINFO_LINE4                FormKey_MUSTER_65A = "label_patientInfo_line4"
	MUSTER_65A_LABEL_IK_NUMBER                        FormKey_MUSTER_65A = "label_ik_number"
	MUSTER_65A_LABEL_BSNR                             FormKey_MUSTER_65A = "label_bsnr"
	MUSTER_65A_LABEL_INSURANCE_NUMBER                 FormKey_MUSTER_65A = "label_insurance_number"
	MUSTER_65A_LABEL_LANR                             FormKey_MUSTER_65A = "label_lanr"
	MUSTER_65A_DATE_PRESCRIBE                         FormKey_MUSTER_65A = "date_prescribe"
	MUSTER_65A_LABEL_INSURANCE_END_DATE               FormKey_MUSTER_65A = "label_insurance_end_date"
	MUSTER_65A_LABEL_INSURANCE_STATUS                 FormKey_MUSTER_65A = "label_insurance_status"
	MUSTER_65A_BARCODE                                FormKey_MUSTER_65A = "barcode"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_20 string

const (
	MUSTER_20_TEXTBOX_ZULETZT_LINE1                  FormKey_MUSTER_20 = "textbox_zuletzt_line1"
	MUSTER_20_TEXTBOX_ZULETZT_LINE2                  FormKey_MUSTER_20 = "textbox_zuletzt_line2"
	MUSTER_20_TEXTBOX_ZULETZT_LINE3                  FormKey_MUSTER_20 = "textbox_zuletzt_line3"
	MUSTER_20_TEXTBOX_ZULETZT_STUNDEN                FormKey_MUSTER_20 = "textbox_zuletzt_stunden"
	MUSTER_20_TEXTBOX_TATIGKEIT_LINE1                FormKey_MUSTER_20 = "textbox_tatigkeit_line1"
	MUSTER_20_TEXTBOX_TATIGKEIT_LINE2                FormKey_MUSTER_20 = "textbox_tatigkeit_line2"
	MUSTER_20_TEXTBOX_TATIGKEIT_LINE3                FormKey_MUSTER_20 = "textbox_tatigkeit_line3"
	MUSTER_20_TEXTBOX_TATIGKEIT_LINE4                FormKey_MUSTER_20 = "textbox_tatigkeit_line4"
	MUSTER_20_TEXTBOX_TAGLICH_LINE1                  FormKey_MUSTER_20 = "textbox_taglich_line1"
	MUSTER_20_TEXTBOX_TAGLICH_LINE2                  FormKey_MUSTER_20 = "textbox_taglich_line2"
	MUSTER_20_TEXTBOX_TAGLICH_LINE3                  FormKey_MUSTER_20 = "textbox_taglich_line3"
	MUSTER_20_TEXTBOX_TAGLICH_LINE4                  FormKey_MUSTER_20 = "textbox_taglich_line4"
	MUSTER_20_LABEL_PRF_NR                           FormKey_MUSTER_20 = "label_prf_nr"
	MUSTER_20_DATE_LABEL_CUSTOM_DATUM                FormKey_MUSTER_20 = "date_label_custom_datum"
	MUSTER_20_LABEL_DATE_LABEL_CUSTOM_DATUM_NO_SPACE FormKey_MUSTER_20 = "label_date_label_custom_datum_no_space"
	MUSTER_20_LABEL_DATE_LABEL_CUSTOM_VOM1_NO_SPACE  FormKey_MUSTER_20 = "label_date_label_custom_vom1_no_space"
	MUSTER_20_DATE_LABEL_CUSTOM_VOM1                 FormKey_MUSTER_20 = "date_label_custom_vom1"
	MUSTER_20_LABEL_DATE_LABEL_CUSTOM_BIS1_NO_SPACE  FormKey_MUSTER_20 = "label_date_label_custom_bis1_no_space"
	MUSTER_20_DATE_LABEL_CUSTOM_BIS1                 FormKey_MUSTER_20 = "date_label_custom_bis1"
	MUSTER_20_LABEL_DATE_LABEL_CUSTOM_VOM2_NO_SPACE  FormKey_MUSTER_20 = "label_date_label_custom_vom2_no_space"
	MUSTER_20_DATE_LABEL_CUSTOM_VOM2                 FormKey_MUSTER_20 = "date_label_custom_vom2"
	MUSTER_20_LABEL_DATE_LABEL_CUSTOM_BIS2_NO_SPACE  FormKey_MUSTER_20 = "label_date_label_custom_bis2_no_space"
	MUSTER_20_DATE_LABEL_CUSTOM_BIS2                 FormKey_MUSTER_20 = "date_label_custom_bis2"
	MUSTER_20_LABEL_DATE_LABEL_CUSTOM_VOM3_NO_SPACE  FormKey_MUSTER_20 = "label_date_label_custom_vom3_no_space"
	MUSTER_20_DATE_LABEL_CUSTOM_VOM3                 FormKey_MUSTER_20 = "date_label_custom_vom3"
	MUSTER_20_LABEL_DATE_LABEL_CUSTOM_BIS3_NO_SPACE  FormKey_MUSTER_20 = "label_date_label_custom_bis3_no_space"
	MUSTER_20_DATE_LABEL_CUSTOM_BIS3                 FormKey_MUSTER_20 = "date_label_custom_bis3"
	MUSTER_20_LABEL_DATE_LABEL_CUSTOM_VOM4_NO_SPACE  FormKey_MUSTER_20 = "label_date_label_custom_vom4_no_space"
	MUSTER_20_DATE_LABEL_CUSTOM_VOM4                 FormKey_MUSTER_20 = "date_label_custom_vom4"
	MUSTER_20_LABEL_DATE_LABEL_CUSTOM_BIS4_NO_SPACE  FormKey_MUSTER_20 = "label_date_label_custom_bis4_no_space"
	MUSTER_20_DATE_LABEL_CUSTOM_BIS4                 FormKey_MUSTER_20 = "date_label_custom_bis4"
	MUSTER_20_LABEL_PATIENTINFO_LINE2                FormKey_MUSTER_20 = "label_patientInfo_line2"
	MUSTER_20_LABEL_PATIENTINFO_LINE1                FormKey_MUSTER_20 = "label_patientInfo_line1"
	MUSTER_20_LABEL_DATE_OF_BIRTH                    FormKey_MUSTER_20 = "label_date_of_birth"
	MUSTER_20_LABEL_INSURANCE_NAME                   FormKey_MUSTER_20 = "label_insurance_name"
	MUSTER_20_LABEL_WOP                              FormKey_MUSTER_20 = "label_wop"
	MUSTER_20_LABEL_PATIENTINFO_LINE3                FormKey_MUSTER_20 = "label_patientInfo_line3"
	MUSTER_20_LABEL_PATIENTINFO_LINE4                FormKey_MUSTER_20 = "label_patientInfo_line4"
	MUSTER_20_LABEL_IK_NUMBER                        FormKey_MUSTER_20 = "label_ik_number"
	MUSTER_20_LABEL_BSNR                             FormKey_MUSTER_20 = "label_bsnr"
	MUSTER_20_LABEL_INSURANCE_NUMBER                 FormKey_MUSTER_20 = "label_insurance_number"
	MUSTER_20_LABEL_LANR                             FormKey_MUSTER_20 = "label_lanr"
	MUSTER_20_DATE_PRESCRIBE                         FormKey_MUSTER_20 = "date_prescribe"
	MUSTER_20_LABEL_INSURANCE_END_DATE               FormKey_MUSTER_20 = "label_insurance_end_date"
	MUSTER_20_LABEL_DOCTOR_STAMP                     FormKey_MUSTER_20 = "label_doctor_stamp"
	MUSTER_20_BARCODE                                FormKey_MUSTER_20 = "barcode"
)

// This code was autogenerated, do not edit
type FormKey_MUSTER_61B string

const (
	MUSTER_61B_CHECKBOX_BERATUNG_0                                 FormKey_MUSTER_61B = "checkbox_beratung_0"
	MUSTER_61B_CHECKBOX_PRUFUNG_0                                  FormKey_MUSTER_61B = "checkbox_prufung_0"
	MUSTER_61B_TEXTBOX_WEITERE_LINE1_0                             FormKey_MUSTER_61B = "textbox_weitere_line1_0"
	MUSTER_61B_TEXTBOX_WEITERE_LINE2_0                             FormKey_MUSTER_61B = "textbox_weitere_line2_0"
	MUSTER_61B_LABEL_DOCTOR_STAMP_0                                FormKey_MUSTER_61B = "label_doctor_stamp_0"
	MUSTER_61B_CHECKBOX_KRANKENKASSE_0                             FormKey_MUSTER_61B = "checkbox_krankenkasse_0"
	MUSTER_61B_CHECKBOX_RENTENVERSICHERUNG_0                       FormKey_MUSTER_61B = "checkbox_rentenversicherung_0"
	MUSTER_61B_CHECKBOX_SONSTIGES_0                                FormKey_MUSTER_61B = "checkbox_sonstiges_0"
	MUSTER_61B_TEXTBOX_SONSTIGE_LINE1_0                            FormKey_MUSTER_61B = "textbox_sonstige_line1_0"
	MUSTER_61B_TEXTBOX_SONSTIGE_LINE2_0                            FormKey_MUSTER_61B = "textbox_sonstige_line2_0"
	MUSTER_61B_TEXTBOX_SONSTIGE_LINE3_0                            FormKey_MUSTER_61B = "textbox_sonstige_line3_0"
	MUSTER_61B_CHECKBOX_ESHANDELT_1                                FormKey_MUSTER_61B = "checkbox_esHandelt_1"
	MUSTER_61B_CHECKBOX_VERSJCHERTE_1                              FormKey_MUSTER_61B = "checkbox_versjcherte_1"
	MUSTER_61B_TEXTBOX_KURZEANGABEN_LINE1_1                        FormKey_MUSTER_61B = "textbox_kurzeAngaben_line1_1"
	MUSTER_61B_TEXTBOX_KURZEANGABEN_LINE2_1                        FormKey_MUSTER_61B = "textbox_kurzeAngaben_line2_1"
	MUSTER_61B_TEXTBOX_KURZEANGABEN_LINE3_1                        FormKey_MUSTER_61B = "textbox_kurzeAngaben_line3_1"
	MUSTER_61B_TEXTBOX_REHABILITATIONSRELEVANTE_LINE1_1            FormKey_MUSTER_61B = "textbox_rehabilitationsrelevante_line1_1"
	MUSTER_61B_TEXTBOX_REHABILITATIONSRELEVANTE_LINE2_1            FormKey_MUSTER_61B = "textbox_rehabilitationsrelevante_line2_1"
	MUSTER_61B_TEXTBOX_MOBILITATTUG_1                              FormKey_MUSTER_61B = "textbox_mobilitatTug_1"
	MUSTER_61B_TEXTBOX_BISHERIGEARZTLICHE_LINE1_1                  FormKey_MUSTER_61B = "textbox_bisherigeArztliche_line1_1"
	MUSTER_61B_TEXTBOX_BISHERIGEARZTLICHE_LINE2_1                  FormKey_MUSTER_61B = "textbox_bisherigeArztliche_line2_1"
	MUSTER_61B_TEXTBOX_MOBILITATCHAIRRISE_1                        FormKey_MUSTER_61B = "textbox_mobilitatChairRise_1"
	MUSTER_61B_TEXTBOX_MOBILITATHAND_1                             FormKey_MUSTER_61B = "textbox_mobilitatHand_1"
	MUSTER_61B_TEXTBOX_MOBILITATODER_1                             FormKey_MUSTER_61B = "textbox_mobilitatOder_1"
	MUSTER_61B_TEXTBOX_KOGNITIONMMST_1                             FormKey_MUSTER_61B = "textbox_kognitionMmst_1"
	MUSTER_61B_TEXTBOX_KOGNITIONGDS15_1                            FormKey_MUSTER_61B = "textbox_kognitionGds15_1"
	MUSTER_61B_TEXTBOX_KOGNITIONUHREN_1                            FormKey_MUSTER_61B = "textbox_kognitionUhren_1"
	MUSTER_61B_TEXTBOX_MOBILITATDEMMI_1                            FormKey_MUSTER_61B = "textbox_mobilitatDemmi_1"
	MUSTER_61B_TEXTBOX_MOBILITATTINETTI_1                          FormKey_MUSTER_61B = "textbox_mobilitatTinetti_1"
	MUSTER_61B_TEXTBOX_SCHMERZ_1                                   FormKey_MUSTER_61B = "textbox_schmerz_1"
	MUSTER_61B_TEXTBOX_HERZERGOMETRIE_1                            FormKey_MUSTER_61B = "textbox_herzErgometrie_1"
	MUSTER_61B_TEXTBOX_HERZFEV1_1                                  FormKey_MUSTER_61B = "textbox_herzFev1_1"
	MUSTER_61B_TEXTBOX_HERZVK_1                                    FormKey_MUSTER_61B = "textbox_herzVk_1"
	MUSTER_61B_TEXTBOX_HERZNYHA_1                                  FormKey_MUSTER_61B = "textbox_herzNyha_1"
	MUSTER_61B_CHECKBOX_HEILMITTEL_1                               FormKey_MUSTER_61B = "checkbox_heilmittel_1"
	MUSTER_61B_CHECKBOX_REHABILITATIONSRELEVANTENEIN_1             FormKey_MUSTER_61B = "checkbox_rehabilitationsrelevanteNein_1"
	MUSTER_61B_CHECKBOX_REHABILITATIONSRELEVANTEJA_1               FormKey_MUSTER_61B = "checkbox_rehabilitationsrelevanteJa_1"
	MUSTER_61B_TEXTBOX_REHABILITATIONSRELEVANTE_1                  FormKey_MUSTER_61B = "textbox_rehabilitationsrelevante_1"
	MUSTER_61B_CHECKBOX_LERNEN2_2                                  FormKey_MUSTER_61B = "checkbox_lernen2_2"
	MUSTER_61B_CHECKBOX_LERNEN3_2                                  FormKey_MUSTER_61B = "checkbox_lernen3_2"
	MUSTER_61B_CHECKBOX_LERNEN4_2                                  FormKey_MUSTER_61B = "checkbox_lernen4_2"
	MUSTER_61B_CHECKBOX_ALLGEMEINE2_2                              FormKey_MUSTER_61B = "checkbox_allgemeine2_2"
	MUSTER_61B_CHECKBOX_ALLGEMEINE3_2                              FormKey_MUSTER_61B = "checkbox_allgemeine3_2"
	MUSTER_61B_CHECKBOX_ALLGEMEINE4_2                              FormKey_MUSTER_61B = "checkbox_allgemeine4_2"
	MUSTER_61B_CHECKBOX_KOMMUNIKATION1_2                           FormKey_MUSTER_61B = "checkbox_kommunikation1_2"
	MUSTER_61B_CHECKBOX_KOMMUNIKATION2_2                           FormKey_MUSTER_61B = "checkbox_kommunikation2_2"
	MUSTER_61B_CHECKBOX_KOMMUNIKATION3_2                           FormKey_MUSTER_61B = "checkbox_kommunikation3_2"
	MUSTER_61B_CHECKBOX_KOMMUNIKATION4_2                           FormKey_MUSTER_61B = "checkbox_kommunikation4_2"
	MUSTER_61B_CHECKBOX_MOBILITAT_2                                FormKey_MUSTER_61B = "checkbox_mobilitat_2"
	MUSTER_61B_CHECKBOX_MOBILITATTRANSFER1_2                       FormKey_MUSTER_61B = "checkbox_mobilitatTransfer1_2"
	MUSTER_61B_CHECKBOX_MOBILITATTRANSFER2_2                       FormKey_MUSTER_61B = "checkbox_mobilitatTransfer2_2"
	MUSTER_61B_CHECKBOX_MOBILITATTRANSFER3_2                       FormKey_MUSTER_61B = "checkbox_mobilitatTransfer3_2"
	MUSTER_61B_CHECKBOX_MOBILITATTRANSFER4_2                       FormKey_MUSTER_61B = "checkbox_mobilitatTransfer4_2"
	MUSTER_61B_CHECKBOX_MOBILITATSTEHEN1_2                         FormKey_MUSTER_61B = "checkbox_mobilitatStehen1_2"
	MUSTER_61B_CHECKBOX_MOBILITATSTEHEN2_2                         FormKey_MUSTER_61B = "checkbox_mobilitatStehen2_2"
	MUSTER_61B_CHECKBOX_MOBILITATSTEHEN3_2                         FormKey_MUSTER_61B = "checkbox_mobilitatStehen3_2"
	MUSTER_61B_CHECKBOX_MOBILITATSTEHEN4_2                         FormKey_MUSTER_61B = "checkbox_mobilitatStehen4_2"
	MUSTER_61B_CHECKBOX_MOBILITATTREPPENSTEIGEN1_2                 FormKey_MUSTER_61B = "checkbox_mobilitatTreppensteigen1_2"
	MUSTER_61B_CHECKBOX_MOBILITATTREPPENSTEIGEN2_2                 FormKey_MUSTER_61B = "checkbox_mobilitatTreppensteigen2_2"
	MUSTER_61B_CHECKBOX_MOBILITATTREPPENSTEIGEN3_2                 FormKey_MUSTER_61B = "checkbox_mobilitatTreppensteigen3_2"
	MUSTER_61B_CHECKBOX_MOBILITATTREPPENSTEIGEN4_2                 FormKey_MUSTER_61B = "checkbox_mobilitatTreppensteigen4_2"
	MUSTER_61B_CHECKBOX_MOBILITATSONSTIGE1_2                       FormKey_MUSTER_61B = "checkbox_mobilitatSonstige1_2"
	MUSTER_61B_CHECKBOX_MOBILITATSONSTIGE2_2                       FormKey_MUSTER_61B = "checkbox_mobilitatSonstige2_2"
	MUSTER_61B_CHECKBOX_MOBILITATSONSTIGE3_2                       FormKey_MUSTER_61B = "checkbox_mobilitatSonstige3_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNG_2                         FormKey_MUSTER_61B = "checkbox_selbstversorgung_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGESSEN1_2                   FormKey_MUSTER_61B = "checkbox_selbstversorgungEssen1_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGESSEN2_2                   FormKey_MUSTER_61B = "checkbox_selbstversorgungEssen2_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGESSEN3_2                   FormKey_MUSTER_61B = "checkbox_selbstversorgungEssen3_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGESSEN4_2                   FormKey_MUSTER_61B = "checkbox_selbstversorgungEssen4_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGAN1_2                      FormKey_MUSTER_61B = "checkbox_selbstversorgungAn1_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGAN2_2                      FormKey_MUSTER_61B = "checkbox_selbstversorgungAn2_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGAN3_2                      FormKey_MUSTER_61B = "checkbox_selbstversorgungAn3_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGAN4_2                      FormKey_MUSTER_61B = "checkbox_selbstversorgungAn4_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGWASCHEN1_2                 FormKey_MUSTER_61B = "checkbox_selbstversorgungWaschen1_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGWASCHEN2_2                 FormKey_MUSTER_61B = "checkbox_selbstversorgungWaschen2_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGWASCHEN3_2                 FormKey_MUSTER_61B = "checkbox_selbstversorgungWaschen3_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGWASCHEN4_2                 FormKey_MUSTER_61B = "checkbox_selbstversorgungWaschen4_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGBADEN2_2                   FormKey_MUSTER_61B = "checkbox_selbstversorgungBaden2_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGBADEN3_2                   FormKey_MUSTER_61B = "checkbox_selbstversorgungBaden3_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGBADEN4_2                   FormKey_MUSTER_61B = "checkbox_selbstversorgungBaden4_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGBADEN1_2                   FormKey_MUSTER_61B = "checkbox_selbstversorgungBaden1_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGTOILETTENBENUTZUNG1_2      FormKey_MUSTER_61B = "checkbox_selbstversorgungToilettenbenutzung1_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGTOILETTENBENUTZUNG2_2      FormKey_MUSTER_61B = "checkbox_selbstversorgungToilettenbenutzung2_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGTOILETTENBENUTZUNG3_2      FormKey_MUSTER_61B = "checkbox_selbstversorgungToilettenbenutzung3_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGTOILETTENBENUTZUNG4_2      FormKey_MUSTER_61B = "checkbox_selbstversorgungToilettenbenutzung4_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGSONSTIGE1_2                FormKey_MUSTER_61B = "checkbox_selbstversorgungSonstige1_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGSONSTIGE2_2                FormKey_MUSTER_61B = "checkbox_selbstversorgungSonstige2_2"
	MUSTER_61B_CHECKBOX_SELBSTVERSORGUNGSONSTIGE3_2                FormKey_MUSTER_61B = "checkbox_selbstversorgungSonstige3_2"
	MUSTER_61B_CHECKBOX_HAUSLICHES1_2                              FormKey_MUSTER_61B = "checkbox_hausliches1_2"
	MUSTER_61B_CHECKBOX_HAUSLICHES2_2                              FormKey_MUSTER_61B = "checkbox_hausliches2_2"
	MUSTER_61B_CHECKBOX_HAUSLICHES3_2                              FormKey_MUSTER_61B = "checkbox_hausliches3_2"
	MUSTER_61B_CHECKBOX_HAUSLICHES4_2                              FormKey_MUSTER_61B = "checkbox_hausliches4_2"
	MUSTER_61B_CHECKBOX_INTERPERSONELLE1_2                         FormKey_MUSTER_61B = "checkbox_interpersonelle1_2"
	MUSTER_61B_CHECKBOX_INTERPERSONELLE2_2                         FormKey_MUSTER_61B = "checkbox_interpersonelle2_2"
	MUSTER_61B_CHECKBOX_INTERPERSONELLE3_2                         FormKey_MUSTER_61B = "checkbox_interpersonelle3_2"
	MUSTER_61B_CHECKBOX_INTERPERSONELLE4_2                         FormKey_MUSTER_61B = "checkbox_interpersonelle4_2"
	MUSTER_61B_CHECKBOX_BEDEUTENDE1_2                              FormKey_MUSTER_61B = "checkbox_bedeutende1_2"
	MUSTER_61B_CHECKBOX_BEDEUTENDE2_2                              FormKey_MUSTER_61B = "checkbox_bedeutende2_2"
	MUSTER_61B_CHECKBOX_BEDEUTENDE3_2                              FormKey_MUSTER_61B = "checkbox_bedeutende3_2"
	MUSTER_61B_CHECKBOX_BEDEUTENDE4_2                              FormKey_MUSTER_61B = "checkbox_bedeutende4_2"
	MUSTER_61B_CHECKBOX_GEMEINSCHAFTSLEBEN1_2                      FormKey_MUSTER_61B = "checkbox_gemeinschaftsleben1_2"
	MUSTER_61B_CHECKBOX_GEMEINSCHAFTSLEBEN2_2                      FormKey_MUSTER_61B = "checkbox_gemeinschaftsleben2_2"
	MUSTER_61B_CHECKBOX_GEMEINSCHAFTSLEBEN3_2                      FormKey_MUSTER_61B = "checkbox_gemeinschaftsleben3_2"
	MUSTER_61B_CHECKBOX_GEMEINSCHAFTSLEBEN4_2                      FormKey_MUSTER_61B = "checkbox_gemeinschaftsleben4_2"
	MUSTER_61B_TEXTBOX_MOBILITATSONSTIGE_2                         FormKey_MUSTER_61B = "textbox_mobilitatSonstige_2"
	MUSTER_61B_TEXTBOX_SEKBSTVERSORGUNGSONSTIGE_2                  FormKey_MUSTER_61B = "textbox_sekbstversorgungSonstige_2"
	MUSTER_61B_TEXTBOX_REHABILITATIONSRELEVANTEUMWELT_LINE1_2      FormKey_MUSTER_61B = "textbox_rehabilitationsrelevanteUmwelt_line1_2"
	MUSTER_61B_TEXTBOX_REHABILITATIONSRELEVANTEUMWELT_LINE2_2      FormKey_MUSTER_61B = "textbox_rehabilitationsrelevanteUmwelt_line2_2"
	MUSTER_61B_TEXTBOX_REHABILITATIONSRELEVANTEUMWELT_LINE3_2      FormKey_MUSTER_61B = "textbox_rehabilitationsrelevanteUmwelt_line3_2"
	MUSTER_61B_CHECKBOX_RISIKOFAKTORENBEWEGUNGSMANGEL_2            FormKey_MUSTER_61B = "checkbox_risikofaktorenBewegungsmangel_2"
	MUSTER_61B_CHECKBOX_RISIKOFAKTORENSONSTIGES_2                  FormKey_MUSTER_61B = "checkbox_risikofaktorenSonstiges_2"
	MUSTER_61B_CHECKBOX_RISIKOFAKTORENFEHL_2                       FormKey_MUSTER_61B = "checkbox_risikofaktorenFehl_2"
	MUSTER_61B_CHECKBOX_RISIKOFAKTORENMEDIKAMENTE_2                FormKey_MUSTER_61B = "checkbox_risikofaktorenMedikamente_2"
	MUSTER_61B_CHECKBOX_RISIKOFAKTORENNIKOTIN_2                    FormKey_MUSTER_61B = "checkbox_risikofaktorenNikotin_2"
	MUSTER_61B_TEXTBOX_RISIKOFAKTORENSONSTIGES_LINE1_2             FormKey_MUSTER_61B = "textbox_risikofaktorenSonstiges_line1_2"
	MUSTER_61B_TEXTBOX_RISIKOFAKTORENSONSTIGES_LINE2_2             FormKey_MUSTER_61B = "textbox_risikofaktorenSonstiges_line2_2"
	MUSTER_61B_TEXTBOX_RISIKOFAKTORENBMI_2                         FormKey_MUSTER_61B = "textbox_risikofaktorenBmi_2"
	MUSTER_61B_CHECKBOX_ANGABENJA_2                                FormKey_MUSTER_61B = "checkbox_angabenJa_2"
	MUSTER_61B_CHECKBOX_ANGABENNEIN_2                              FormKey_MUSTER_61B = "checkbox_angabenNein_2"
	MUSTER_61B_TEXTBOX_REHABILITATIONSZIELE_LINE1_3                FormKey_MUSTER_61B = "textbox_rehabilitationsziele_line1_3"
	MUSTER_61B_TEXTBOX_REHABILITATIONSZIELE_LINE2_3                FormKey_MUSTER_61B = "textbox_rehabilitationsziele_line2_3"
	MUSTER_61B_TEXTBOX_REHABILITATIONSZIELEAUSSICHT_3              FormKey_MUSTER_61B = "textbox_rehabilitationszieleAusSicht_3"
	MUSTER_61B_CHECKBOX_UNTERBERUCKSICHTIGUNGJA_3                  FormKey_MUSTER_61B = "checkbox_unterBerucksichtigungJa_3"
	MUSTER_61B_CHECKBOX_UNTERBERUCKSICHTIGUNGEINGESCHRANKT_3       FormKey_MUSTER_61B = "checkbox_unterBerucksichtigungEingeschrankt_3"
	MUSTER_61B_TEXTBOX_UNTERBERUCKSICHTIGUNG_LINE2_3               FormKey_MUSTER_61B = "textbox_unterBerucksichtigung_line2_3"
	MUSTER_61B_TEXTBOX_UNTERBERUCKSICHTIGUNG_LINE1_3               FormKey_MUSTER_61B = "textbox_unterBerucksichtigung_line1_3"
	MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENAMBULANT_3           FormKey_MUSTER_61B = "checkbox_zuweisungsempfehlungenAmbulant_3"
	MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENMUTTERLEISTUNG_3     FormKey_MUSTER_61B = "checkbox_zuweisungsempfehlungenMutterLeistung_3"
	MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENAMBULANTMOBIL_3      FormKey_MUSTER_61B = "checkbox_zuweisungsempfehlungenAmbulantMobil_3"
	MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENVATERLEISTUNG_3      FormKey_MUSTER_61B = "checkbox_zuweisungsempfehlungenVaterLeistung_3"
	MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENSTATIONAR_3          FormKey_MUSTER_61B = "checkbox_zuweisungsempfehlungenStationar_3"
	MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENMUTTERKINDLEISTUNG_3 FormKey_MUSTER_61B = "checkbox_zuweisungsempfehlungenMutterKindLeistung_3"
	MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENVATERKINDLEISTUNG_3  FormKey_MUSTER_61B = "checkbox_zuweisungsempfehlungenVaterKindLeistung_3"
	MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENGERIATR_3            FormKey_MUSTER_61B = "checkbox_zuweisungsempfehlungenGeriatr_3"
	MUSTER_61B_TEXTBOX_ZUWEISUNGSEMPFEHLUNGENINHALTLICHE_3         FormKey_MUSTER_61B = "textbox_zuweisungsempfehlungenInhaltliche_3"
	MUSTER_61B_TEXTBOX_ZUWEISUNGSEMPFEHLUNGENWEITEREBEMERKUNGEN_3  FormKey_MUSTER_61B = "textbox_zuweisungsempfehlungenWeitereBemerkungen_3"
	MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENANDERE_3             FormKey_MUSTER_61B = "checkbox_zuweisungsempfehlungenAndere_3"
	MUSTER_61B_CHECKBOX_ZUWEISUNGSEMPFEHLUNGENPFLEGENDE_3          FormKey_MUSTER_61B = "checkbox_zuweisungsempfehlungenPflegende_3"
	MUSTER_61B_CHECKBOX_SONSTIGEANGABENNEUER_3                     FormKey_MUSTER_61B = "checkbox_sonstigeAngabenNeuer_3"
	MUSTER_61B_CHECKBOX_SONSTIGEANGABENDIEZEITWEISE_3              FormKey_MUSTER_61B = "checkbox_sonstigeAngabenDieZeitweise_3"
	MUSTER_61B_CHECKBOX_SONSTIGEANGABENVERSCHLIMMERUNG_3           FormKey_MUSTER_61B = "checkbox_sonstigeAngabenVerschlimmerung_3"
	MUSTER_61B_CHECKBOX_SONSTIGEANGABENIMFALLE_3                   FormKey_MUSTER_61B = "checkbox_sonstigeAngabenImFalle_3"
	MUSTER_61B_CHECKBOX_SONSTIGEANGABENMITAUFNAHME_3               FormKey_MUSTER_61B = "checkbox_sonstigeAngabenMitaufnahme_3"
	MUSTER_61B_CHECKBOX_SONSTIGEANGABENKOORDINATION_3              FormKey_MUSTER_61B = "checkbox_sonstigeAngabenKoordination_3"
	MUSTER_61B_CHECKBOX_SONSTIGEANGABENBEGLEITPERSON_3             FormKey_MUSTER_61B = "checkbox_sonstigeAngabenBegleitperson_3"
	MUSTER_61B_CHECKBOX_SONSTIGEANGABENOFFENTLICHE_3               FormKey_MUSTER_61B = "checkbox_sonstigeAngabenOffentliche_3"
	MUSTER_61B_CHECKBOX_SONSTIGEANGABENPKW_3                       FormKey_MUSTER_61B = "checkbox_sonstigeAngabenPkw_3"
	MUSTER_61B_TEXTBOX_SONSTIGESBESONDERE_LINE1_3                  FormKey_MUSTER_61B = "textbox_sonstigesBesondere_line1_3"
	MUSTER_61B_TEXTBOX_SONSTIGESBESONDERE_LINE2_3                  FormKey_MUSTER_61B = "textbox_sonstigesBesondere_line2_3"
	MUSTER_61B_CHECKBOX_ERTEILTEJA2_4                              FormKey_MUSTER_61B = "checkbox_erteilteJa2_4"
	MUSTER_61B_CHECKBOX_ERTEILTENEIN2_4                            FormKey_MUSTER_61B = "checkbox_erteilteNein2_4"
	MUSTER_61B_CHECKBOX_ERTEILTEJA1_4                              FormKey_MUSTER_61B = "checkbox_erteilteJa1_4"
	MUSTER_61B_CHECKBOX_ERTEILTENEIN1_4                            FormKey_MUSTER_61B = "checkbox_erteilteNein1_4"
	MUSTER_61B_LABEL_DOCTOR_STAMP_4                                FormKey_MUSTER_61B = "label_doctor_stamp_4"
	MUSTER_61B_TEXTBOX_NAMEVORNAME1_4                              FormKey_MUSTER_61B = "textbox_nameVorname1_4"
	MUSTER_61B_TEXTBOX_PLZ1_4                                      FormKey_MUSTER_61B = "textbox_plz1_4"
	MUSTER_61B_TEXTBOX_ORT1_4                                      FormKey_MUSTER_61B = "textbox_ort1_4"
	MUSTER_61B_TEXTBOX_STRABE1_4                                   FormKey_MUSTER_61B = "textbox_strabe1_4"
	MUSTER_61B_TEXTBOX_NAMEVORNAME2_4                              FormKey_MUSTER_61B = "textbox_nameVorname2_4"
	MUSTER_61B_TEXTBOX_PLZ2_4                                      FormKey_MUSTER_61B = "textbox_plz2_4"
	MUSTER_61B_TEXTBOX_ORT2_4                                      FormKey_MUSTER_61B = "textbox_ort2_4"
	MUSTER_61B_TEXTBOX_STRABE2_4                                   FormKey_MUSTER_61B = "textbox_strabe2_4"
	MUSTER_61B_TEXTBOX_NAMEVORNAME3_4                              FormKey_MUSTER_61B = "textbox_nameVorname3_4"
	MUSTER_61B_TEXTBOX_PLZ3_4                                      FormKey_MUSTER_61B = "textbox_plz3_4"
	MUSTER_61B_TEXTBOX_ORT3_4                                      FormKey_MUSTER_61B = "textbox_ort3_4"
	MUSTER_61B_TEXTBOX_STRABE3_4                                   FormKey_MUSTER_61B = "textbox_strabe3_4"
	MUSTER_61B_BARCODE_0                                           FormKey_MUSTER_61B = "barcode_0"
	MUSTER_61B_BARCODE_4                                           FormKey_MUSTER_61B = "barcode_4"
	MUSTER_61B_CHECKBOX_ALLGEMEINE1_2                              FormKey_MUSTER_61B = "checkbox_allgemeine1_2"
	MUSTER_61B_CHECKBOX_LERNEN1_2                                  FormKey_MUSTER_61B = "checkbox_lernen1_2"
	MUSTER_61B_LABEL_IK_NUMBER_2                                   FormKey_MUSTER_61B = "label_ik_number_2"
	MUSTER_61B_LABEL_INSURANCE_NUMBER_2                            FormKey_MUSTER_61B = "label_insurance_number_2"
	MUSTER_61B_LABEL_IK_NUMBER_3                                   FormKey_MUSTER_61B = "label_ik_number_3"
	MUSTER_61B_LABEL_IK_NUMBER_4                                   FormKey_MUSTER_61B = "label_ik_number_4"
	MUSTER_61B_LABEL_INSURANCE_NUMBER_4                            FormKey_MUSTER_61B = "label_insurance_number_4"
	MUSTER_61B_LABEL_TAKEOVER_DIAGNOSIS_LINE2_0                    FormKey_MUSTER_61B = "label_takeover_diagnosis_line2_0"
	MUSTER_61B_TEXTBOX_ICD10_CODE1_0                               FormKey_MUSTER_61B = "textbox_icd10_code1_0"
	MUSTER_61B_TEXTBOX_ICD10_CODE2_0                               FormKey_MUSTER_61B = "textbox_icd10_code2_0"
	MUSTER_61B_TEXTBOX_ICD10_CODE3_0                               FormKey_MUSTER_61B = "textbox_icd10_code3_0"
	MUSTER_61B_TEXTBOX_ICD10_CODE4_0                               FormKey_MUSTER_61B = "textbox_icd10_code4_0"
	MUSTER_61B_TEXTBOX_ICD10_CODE5_0                               FormKey_MUSTER_61B = "textbox_icd10_code5_0"
	MUSTER_61B_TEXTBOX_ICD10_CODE6_0                               FormKey_MUSTER_61B = "textbox_icd10_code6_0"
	MUSTER_61B_TEXTBOX_ICD10_CODE1_1                               FormKey_MUSTER_61B = "textbox_icd10_code1_1"
	MUSTER_61B_TEXTBOX_ICD10_CODE2_1                               FormKey_MUSTER_61B = "textbox_icd10_code2_1"
	MUSTER_61B_TEXTBOX_ICD10_CODE3_1                               FormKey_MUSTER_61B = "textbox_icd10_code3_1"
	MUSTER_61B_TEXTBOX_ICD10_CODE4_1                               FormKey_MUSTER_61B = "textbox_icd10_code4_1"
	MUSTER_61B_TEXTBOX_ICD10_CODE5_1                               FormKey_MUSTER_61B = "textbox_icd10_code5_1"
	MUSTER_61B_TEXTBOX_ICD10_CODE6_1                               FormKey_MUSTER_61B = "textbox_icd10_code6_1"
	MUSTER_61B_LABEL_TAKEOVER_DIAGNOSIS_LINE2_1                    FormKey_MUSTER_61B = "label_takeover_diagnosis_line2_1"
	MUSTER_61B_LABEL_TAKEOVER_DIAGNOSIS_LINE1_0                    FormKey_MUSTER_61B = "label_takeover_diagnosis_line1_0"
	MUSTER_61B_LABEL_TAKEOVER_DIAGNOSIS_LINE1_1                    FormKey_MUSTER_61B = "label_takeover_diagnosis_line1_1"
	MUSTER_61B_LABEL_INSURANCE_NUMBER_3                            FormKey_MUSTER_61B = "label_insurance_number_3"
	MUSTER_61B_LABEL_PRF_NR_1                                      FormKey_MUSTER_61B = "label_prf_nr_1"
	MUSTER_61B_LABEL_PRF_NR_3                                      FormKey_MUSTER_61B = "label_prf_nr_3"
	MUSTER_61B_LABEL_PRF_NR_4                                      FormKey_MUSTER_61B = "label_prf_nr_4"
	MUSTER_61B_LABEL_PRF_NR_0                                      FormKey_MUSTER_61B = "label_prf_nr_0"
	MUSTER_61B_LABEL_PRF_NR_2                                      FormKey_MUSTER_61B = "label_prf_nr_2"
	MUSTER_61B_LABEL_INSURANCE_NAME_0                              FormKey_MUSTER_61B = "label_insurance_name_0"
	MUSTER_61B_LABEL_WOP_0                                         FormKey_MUSTER_61B = "label_wop_0"
	MUSTER_61B_LABEL_PATIENTINFO_LINE1_0                           FormKey_MUSTER_61B = "label_patientInfo_line1_0"
	MUSTER_61B_LABEL_DATE_OF_BIRTH_0                               FormKey_MUSTER_61B = "label_date_of_birth_0"
	MUSTER_61B_LABEL_PATIENTINFO_LINE2_0                           FormKey_MUSTER_61B = "label_patientInfo_line2_0"
	MUSTER_61B_LABEL_PATIENTINFO_LINE3_0                           FormKey_MUSTER_61B = "label_patientInfo_line3_0"
	MUSTER_61B_LABEL_PATIENTINFO_LINE4_0                           FormKey_MUSTER_61B = "label_patientInfo_line4_0"
	MUSTER_61B_LABEL_INSURANCE_END_DATE_0                          FormKey_MUSTER_61B = "label_insurance_end_date_0"
	MUSTER_61B_LABEL_IK_NUMBER_0                                   FormKey_MUSTER_61B = "label_ik_number_0"
	MUSTER_61B_LABEL_INSURANCE_NUMBER_0                            FormKey_MUSTER_61B = "label_insurance_number_0"
	MUSTER_61B_LABEL_INSURANCE_STATUS_0                            FormKey_MUSTER_61B = "label_insurance_status_0"
	MUSTER_61B_LABEL_BSNR_0                                        FormKey_MUSTER_61B = "label_bsnr_0"
	MUSTER_61B_LABEL_LANR_0                                        FormKey_MUSTER_61B = "label_lanr_0"
	MUSTER_61B_DATE_PRESCRIBE_0                                    FormKey_MUSTER_61B = "date_prescribe_0"
	MUSTER_61B_TEXTBOX_DIAGNOSE1_0                                 FormKey_MUSTER_61B = "textbox_diagnose1_0"
	MUSTER_61B_TEXTBOX_DIAGNOSE2_0                                 FormKey_MUSTER_61B = "textbox_diagnose2_0"
	MUSTER_61B_TEXTBOX_DIAGNOSE3_0                                 FormKey_MUSTER_61B = "textbox_diagnose3_0"
	MUSTER_61B_TEXTBOX_DIAGNOSE4_0                                 FormKey_MUSTER_61B = "textbox_diagnose4_0"
	MUSTER_61B_TEXTBOX_DIAGNOSE5_0                                 FormKey_MUSTER_61B = "textbox_diagnose5_0"
	MUSTER_61B_TEXTBOX_DIAGNOSE6_0                                 FormKey_MUSTER_61B = "textbox_diagnose6_0"
	MUSTER_61B_LABEL_DATE_LABEL_CUSTOM_DATUM_0_NO_SPACE            FormKey_MUSTER_61B = "label_date_label_custom_datum_0_no_space"
	MUSTER_61B_DATE_LABEL_CUSTOM_DATUM_0                           FormKey_MUSTER_61B = "date_label_custom_datum_0"
	MUSTER_61B_LABEL_INSURANCE_NAME_1                              FormKey_MUSTER_61B = "label_insurance_name_1"
	MUSTER_61B_LABEL_WOP_1                                         FormKey_MUSTER_61B = "label_wop_1"
	MUSTER_61B_LABEL_PATIENTINFO_LINE1_1                           FormKey_MUSTER_61B = "label_patientInfo_line1_1"
	MUSTER_61B_LABEL_PATIENTINFO_LINE2_1                           FormKey_MUSTER_61B = "label_patientInfo_line2_1"
	MUSTER_61B_LABEL_DATE_OF_BIRTH_1                               FormKey_MUSTER_61B = "label_date_of_birth_1"
	MUSTER_61B_LABEL_PATIENTINFO_LINE3_1                           FormKey_MUSTER_61B = "label_patientInfo_line3_1"
	MUSTER_61B_LABEL_PATIENTINFO_LINE4_1                           FormKey_MUSTER_61B = "label_patientInfo_line4_1"
	MUSTER_61B_LABEL_INSURANCE_END_DATE_1                          FormKey_MUSTER_61B = "label_insurance_end_date_1"
	MUSTER_61B_LABEL_IK_NUMBER_1                                   FormKey_MUSTER_61B = "label_ik_number_1"
	MUSTER_61B_LABEL_INSURANCE_NUMBER_1                            FormKey_MUSTER_61B = "label_insurance_number_1"
	MUSTER_61B_LABEL_INSURANCE_STATUS_1                            FormKey_MUSTER_61B = "label_insurance_status_1"
	MUSTER_61B_LABEL_BSNR_1                                        FormKey_MUSTER_61B = "label_bsnr_1"
	MUSTER_61B_LABEL_LANR_1                                        FormKey_MUSTER_61B = "label_lanr_1"
	MUSTER_61B_DATE_PRESCRIBE_1                                    FormKey_MUSTER_61B = "date_prescribe_1"
	MUSTER_61B_TEXTBOX_DIAGNOSE1_1                                 FormKey_MUSTER_61B = "textbox_diagnose1_1"
	MUSTER_61B_TEXTBOX_DIAGNOSE2_1                                 FormKey_MUSTER_61B = "textbox_diagnose2_1"
	MUSTER_61B_TEXTBOX_DIAGNOSE3_1                                 FormKey_MUSTER_61B = "textbox_diagnose3_1"
	MUSTER_61B_TEXTBOX_DIAGNOSE4_1                                 FormKey_MUSTER_61B = "textbox_diagnose4_1"
	MUSTER_61B_TEXTBOX_DIAGNOSE5_1                                 FormKey_MUSTER_61B = "textbox_diagnose5_1"
	MUSTER_61B_TEXTBOX_DIAGNOSE6_1                                 FormKey_MUSTER_61B = "textbox_diagnose6_1"
	MUSTER_61B_TEXTBOX_URSACHE1_1                                  FormKey_MUSTER_61B = "textbox_ursache1_1"
	MUSTER_61B_TEXTBOX_URSACHE2_1                                  FormKey_MUSTER_61B = "textbox_ursache2_1"
	MUSTER_61B_TEXTBOX_URSACHE3_1                                  FormKey_MUSTER_61B = "textbox_ursache3_1"
	MUSTER_61B_TEXTBOX_URSACHE4_1                                  FormKey_MUSTER_61B = "textbox_ursache4_1"
	MUSTER_61B_TEXTBOX_URSACHE5_1                                  FormKey_MUSTER_61B = "textbox_ursache5_1"
	MUSTER_61B_TEXTBOX_URSACHE6_1                                  FormKey_MUSTER_61B = "textbox_ursache6_1"
	MUSTER_61B_TEXTBOX_URSACHE1_0                                  FormKey_MUSTER_61B = "textbox_ursache1_0"
	MUSTER_61B_TEXTBOX_URSACHE2_0                                  FormKey_MUSTER_61B = "textbox_ursache2_0"
	MUSTER_61B_TEXTBOX_URSACHE3_0                                  FormKey_MUSTER_61B = "textbox_ursache3_0"
	MUSTER_61B_TEXTBOX_URSACHE4_0                                  FormKey_MUSTER_61B = "textbox_ursache4_0"
	MUSTER_61B_TEXTBOX_URSACHE5_0                                  FormKey_MUSTER_61B = "textbox_ursache5_0"
	MUSTER_61B_TEXTBOX_URSACHE6_0                                  FormKey_MUSTER_61B = "textbox_ursache6_0"
	MUSTER_61B_LABEL_DATE_LABEL_CUSTOM_DATUM_4_NO_SPACE            FormKey_MUSTER_61B = "label_date_label_custom_datum_4_no_space"
	MUSTER_61B_DATE_LABEL_CUSTOM_DATUM_4                           FormKey_MUSTER_61B = "date_label_custom_datum_4"
	MUSTER_61B_LABEL_DATE_LABEL_CUSTOM_DATUM2_4_NO_SPACE           FormKey_MUSTER_61B = "label_date_label_custom_datum2_4_no_space"
	MUSTER_61B_DATE_LABEL_CUSTOM_DATUM2_4                          FormKey_MUSTER_61B = "date_label_custom_datum2_4"
	MUSTER_61B_LABEL_PATIENT_FULLNAME_2                            FormKey_MUSTER_61B = "label_patient_fullname_2"
	MUSTER_61B_LABEL_PATIENT_FULLNAME_3                            FormKey_MUSTER_61B = "label_patient_fullname_3"
	MUSTER_61B_LABEL_PATIENT_FULLNAME_4                            FormKey_MUSTER_61B = "label_patient_fullname_4"
)

// This code was autogenerated, do not edit
type FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 string

const (
	LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3_LABEL_DOCTOR_STAMP                                 FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 = "label_doctor_stamp"
	LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3_CHECKBOX_ANDERE                                    FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 = "checkbox_andere"
	LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3_LABEL_DATE_LABEL_CUSTOM_AUSSTELLUNGSDATUM_NO_SPACE FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 = "label_date_label_custom_ausstellungsdatum_no_space"
	LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3_DATE_LABEL_CUSTOM_AUSSTELLUNGSDATUM                FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 = "date_label_custom_ausstellungsdatum"
	LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3_LABEL_PATIENTINFO_LINE1                            FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 = "label_patientInfo_line1"
	LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3_LABEL_DATE_OF_BIRTH                                FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 = "label_date_of_birth"
	LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3_LABEL_INSURANCE_NAME                               FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 = "label_insurance_name"
	LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3_LABEL_WOP                                          FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 = "label_wop"
	LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3_LABEL_PATIENTINFO_LINE3                            FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 = "label_patientInfo_line3"
	LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3_LABEL_PATIENTINFO_LINE4                            FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 = "label_patientInfo_line4"
	LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3_LABEL_IK_NUMBER                                    FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 = "label_ik_number"
	LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3_LABEL_BSNR                                         FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 = "label_bsnr"
	LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3_LABEL_INSURANCE_NUMBER                             FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 = "label_insurance_number"
	LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3_LABEL_LANR                                         FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 = "label_lanr"
	LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3_DATE_PRESCRIBE                                     FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 = "date_prescribe"
	LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3_LABEL_INSURANCE_END_DATE                           FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 = "label_insurance_end_date"
	LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3_LABEL_INSURANCE_STATUS                             FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 = "label_insurance_status"
	LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3_LABEL_PATIENTINFO_LINE2                            FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 = "label_patientInfo_line2"
	LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3_BARCODE                                            FormKey_LKK_BY_HZV_MUSTER_VERSICHERTENEINSCHREIBEBELEG_V3 = "barcode"
)

type FormKey_F1050 string

const (
	F1050_TEXTBOX_AUSWAHL_0                     FormKey_F1050 = "textbox_auswahl_0"
	F1050_TEXTBOX_LFD_0                         FormKey_F1050 = "textbox_lfd_0"
	F1050_TEXTBOX_UNFALLVERSICHER_0             FormKey_F1050 = "textbox_unfallversicher_0"
	F1050_LABEL_LASTNAME_0                      FormKey_F1050 = "label_lastname_0"
	F1050_LABEL_FIRSTNAME_0                     FormKey_F1050 = "label_firstname_0"
	F1050_LABEL_DATE_OF_BIRTH_0                 FormKey_F1050 = "label_date_of_birth_0"
	F1050_LABEL_ADDRESS_0                       FormKey_F1050 = "label_address_0"
	F1050_LABEL_DATE_LABEL_CUSTOM_START_DATUM_0 FormKey_F1050 = "label_date_label_custom_start_datum_0"
	F1050_DATE_LABEL_CUSTOM_START_DATUM_0       FormKey_F1050 = "date_label_custom_start_datum_0"
	F1050_TEXTBOX_COMPANY_ADDRESS_0             FormKey_F1050 = "textbox_company_address_0"
	F1050_DATE_LABEL_CUSTOM_ACCIDENT_DAY_0      FormKey_F1050 = "date_label_custom_accident_day_0"
	F1050_LABEL_TIME_ACCIDENT_TIME_0            FormKey_F1050 = "label_time_accident_time_0"
	F1050_TEXTBOX_ACCIDENT_PLACE_0              FormKey_F1050 = "textbox_accident_place_0"
	F1050_LABEL_TIME_ARRIVE_TIME_0              FormKey_F1050 = "label_time_arrive_time_0"
	F1050_CHECKBOX_FAMILIEN_NEIN_0              FormKey_F1050 = "checkbox_familien_nein_0"
	F1050_CHECKBOX_FAMILIEN_JA_0                FormKey_F1050 = "checkbox_familien_ja_0"
	F1050_TEXTBOX_FAMILY_INSURED_PERSON_0       FormKey_F1050 = "textbox_family_insured_person_0"
	F1050_TEXTBOX_INSURANCE_OF_ANOTHER_PERSON_0 FormKey_F1050 = "textbox_insurance_of_another_person_0"
	F1050_TEXTBOX_TEL_0                         FormKey_F1050 = "textbox_tel_0"
	F1050_TEXTBOX_NATIONALITY_0                 FormKey_F1050 = "textbox_nationality_0"
	F1050_TEXTBOX_GENDER_0                      FormKey_F1050 = "textbox_gender_0"
	F1050_LABEL_TIME_START_TIME_0               FormKey_F1050 = "label_time_start_time_0"
	F1050_LABEL_TIME_END_TIME_0                 FormKey_F1050 = "label_time_end_time_0"
	F1050_CHECKBOX_DIE_VERSICHERTE_0            FormKey_F1050 = "checkbox_die_versicherte_0"
	F1050_DATE_DIE_VERSICHERTE_0                FormKey_F1050 = "date_die_versicherte_0"
	F1050_TEXTBOX_DIE_VERSICHERTE_0             FormKey_F1050 = "textbox_die_versicherte_0"
	F1050_CHECKBOX_DIE_UNFALL_0                 FormKey_F1050 = "checkbox_die_unfall_0"
	F1050_CHECKBOX_DIE_BEHAND_0                 FormKey_F1050 = "checkbox_die_behand_0"
	F1050_CHECKBOX_DIE_VERORDNUNG_0             FormKey_F1050 = "checkbox_die_verordnung_0"
	F1050_CHECKBOX_EINE_WIEDERER_0              FormKey_F1050 = "checkbox_eine_wiederer_0"
	F1050_CHECKBOX_EINE_VORSTELLUNG_0           FormKey_F1050 = "checkbox_eine_vorstellung_0"
	F1050_AREA_ANGABEN_0                        FormKey_F1050 = "area_angaben_0"
	F1050_AREA_BESCHWERDEN_0                    FormKey_F1050 = "area_beschwerden_0"
	F1050_AREA_KURZE_ANGABE_0                   FormKey_F1050 = "area_kurze_angabe_0"
	F1050_AREA_DIAGNOSE_0                       FormKey_F1050 = "area_diagnose_0"
	F1050_AREA_ART_DER_0                        FormKey_F1050 = "area_art_der_0"
	F1050_LABEL_TAKEOVER_DIAGNOSIS_0            FormKey_F1050 = "label_takeover_diagnosis_0"
	F1050_CHECKBOX_IST_WEITERE_NEIN_0           FormKey_F1050 = "checkbox_ist_weitere_nein_0"
	F1050_CHECKBOX_IST_WEITERE_JA_0             FormKey_F1050 = "checkbox_ist_weitere_ja_0"
	F1050_CHECKBOX_DURCH_MICH_0                 FormKey_F1050 = "checkbox_durch_mich_0"
	F1050_CHECKBOX_DURCH_ANDERE_0               FormKey_F1050 = "checkbox_durch_andere_0"
	F1050_LABEL_DOCTOR_STAMP_0                  FormKey_F1050 = "label_doctor_stamp_0"
	F1050_AREA_DURCH_ANDERE_0                   FormKey_F1050 = "area_durch_andere_0"
	F1050_LABEL_CITY_BSNR_0                     FormKey_F1050 = "label_city_bsnr_0"
	F1050_DATE_LABEL_CUSTOM_ARRIVE_DATE_0       FormKey_F1050 = "date_label_custom_arrive_date_0"
	F1050_LABEL_PATIENT_FULLNAME_1              FormKey_F1050 = "label_patient_fullname_1"
	F1050_LABEL_LFD_1                           FormKey_F1050 = "label_lfd_1"
	F1050_LABEL_DATE_OF_BIRTH_1                 FormKey_F1050 = "label_date_of_birth_1"
	F1050_TEXTBOX_RECHNUNGSNUMMER_1             FormKey_F1050 = "textbox_rechnungsnummer_1"
	F1050_LABEL_IK_NUMBER_COST_UNIT_1           FormKey_F1050 = "label_ik_number_cost_unit_1"
	F1050_TEXTBOX_UV_GOA_1                      FormKey_F1050 = "textbox_uv_goa_1"
	F1050_TEXTBOX_NACH_NR0_1                    FormKey_F1050 = "textbox_nach_nr0_1"
	F1050_TEXTBOX_NACH_NR1_1                    FormKey_F1050 = "textbox_nach_nr1_1"
	F1050_TEXTBOX_NACH_NR2_1                    FormKey_F1050 = "textbox_nach_nr2_1"
	F1050_TEXTBOX_NACH_NR3_1                    FormKey_F1050 = "textbox_nach_nr3_1"
	F1050_TEXTBOX_NACH_NR4_1                    FormKey_F1050 = "textbox_nach_nr4_1"
	F1050_TEXTBOX_NACH_NR5_1                    FormKey_F1050 = "textbox_nach_nr5_1"
	F1050_TEXTBOX_NACH_NR6_1                    FormKey_F1050 = "textbox_nach_nr6_1"
	F1050_TEXTBOX_NACH_NR7_1                    FormKey_F1050 = "textbox_nach_nr7_1"
	F1050_TEXTBOX_NACH_NR8_1                    FormKey_F1050 = "textbox_nach_nr8_1"
	F1050_TEXTBOX_NACH_NR9_1                    FormKey_F1050 = "textbox_nach_nr9_1"
	F1050_TEXTBOX_KOSTEN0_1                     FormKey_F1050 = "textbox_kosten0_1"
	F1050_TEXTBOX_KOSTEN1_1                     FormKey_F1050 = "textbox_kosten1_1"
	F1050_TEXTBOX_KOSTEN2_1                     FormKey_F1050 = "textbox_kosten2_1"
	F1050_TEXTBOX_KOSTEN3_1                     FormKey_F1050 = "textbox_kosten3_1"
	F1050_TEXTBOX_KOSTEN4_1                     FormKey_F1050 = "textbox_kosten4_1"
	F1050_TEXTBOX_KOSTEN5_1                     FormKey_F1050 = "textbox_kosten5_1"
	F1050_TEXTBOX_KOSTEN6_1                     FormKey_F1050 = "textbox_kosten6_1"
	F1050_TEXTBOX_KOSTEN7_1                     FormKey_F1050 = "textbox_kosten7_1"
	F1050_TEXTBOX_KOSTEN8_1                     FormKey_F1050 = "textbox_kosten8_1"
	F1050_TEXTBOX_KOSTEN9_1                     FormKey_F1050 = "textbox_kosten9_1"
	F1050_TEXTBOX_RECHNUNG0_1                   FormKey_F1050 = "textbox_rechnung0_1"
	F1050_TEXTBOX_RECHNUNG1_1                   FormKey_F1050 = "textbox_rechnung1_1"
	F1050_TEXTBOX_RECHNUNG2_1                   FormKey_F1050 = "textbox_rechnung2_1"
	F1050_TEXTBOX_RECHNUNG3_1                   FormKey_F1050 = "textbox_rechnung3_1"
	F1050_TEXTBOX_RECHNUNG4_1                   FormKey_F1050 = "textbox_rechnung4_1"
	F1050_TEXTBOX_RECHNUNG5_1                   FormKey_F1050 = "textbox_rechnung5_1"
	F1050_TEXTBOX_RECHNUNG6_1                   FormKey_F1050 = "textbox_rechnung6_1"
	F1050_TEXTBOX_RECHNUNG7_1                   FormKey_F1050 = "textbox_rechnung7_1"
	F1050_TEXTBOX_RECHNUNG8_1                   FormKey_F1050 = "textbox_rechnung8_1"
	F1050_TEXTBOX_RECHNUNG9_1                   FormKey_F1050 = "textbox_rechnung9_1"
	F1050_LABEL_RECHNUNG10_1                    FormKey_F1050 = "label_rechnung10_1"
	F1050_TEXTBOX_RECHNUNG11_1                  FormKey_F1050 = "textbox_rechnung11_1"
	F1050_LABEL_SUM_EUR_1                       FormKey_F1050 = "label_sum_eur_1"
	F1050_TEXTBOX_JOB_0                         FormKey_F1050 = "textbox_job_0"
	F1050_LABEL_CUSTOM_ACCIDENT_DAY_1           FormKey_F1050 = "label_custom_accident_day_1"
	F1050_LABEL_ACTIVE_INSURANCE_NAME_0         FormKey_F1050 = "label_active_insurance_name_0"
	F1050_LABEL_BSNR_DATE_0                     FormKey_F1050 = "label_bsnr_date_0"
)
