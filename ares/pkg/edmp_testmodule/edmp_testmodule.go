package edmp_testmodule

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"io/ioutil"
	"mime/multipart"
	"net/http"
	"regexp"
	"strconv"
	"strings"

	"git.tutum.dev/medi/tutum/ares/pkg/xml_util"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/common"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/pkg/errors"
	"gitlab.com/silenteer-oss/titan"
)

const (
	DocumentType_ED      DocumentType = "ED"
	DocumentType_FD      DocumentType = "FD"
	DocumentType_EHKS_D  DocumentType = "D"
	DocumentType_EHKS_ND DocumentType = "ND"
)

const (
	StatusFile_OK StatusFile = iota
	StatusFile_Warning
	StatusFile_Error
	StatusFile_Cancel
	StatusFile_SystemError
)

const (
	FieldType_XML FileType = "XML"
	FieldType_PDF FileType = "PDF"
)

var fieldNamesXPMShorted = map[string]string{
	"Hormonrezeptorstatus": "Hormonrezeptorstatus Östrogen und/oder Progesteron (gemäß Immunreaktiver Score [IRS])",
	"Bisphoponate":         "Therapie bei Knochenmetastasen (Bisphosphonate)",
	"Denosumab":            "Therapie bei Knochenmetastasen (Denosumab)",
}

var manualErrorMap = map[string]string{
	"BKA-087":              "Wenn in Feld 'M' eine '1' angegeben wurde, ist das Ausfüllen der Felder 19 bis 23 nicht zulässig.",
	"BKA-084":              "Es konnte keine gültige Einschreibung festgestellt werden.",
	"KHK-STATINGRUND":      "Wenn in Feld 'Aktuelle Statin-Dosis' entweder 'Hoch', 'Kein Statin' oder 'Kontraindikation gegen Statin' angekreuzt wurde, dann darf im Feld 'Grund für moderate oder niedrige Statin-Dosis' keine Option ausgewählt werden. ",
	"AB-001":               "Der Wertebereich des FEV1-Wert muss zwischen 001,0 – 299,9 liegen",
	"Sensibilitätsprüfung": "Die Angabe zum Feld 'Sensibilitätsstatus' fehlt.",
	"HIS-018":              "Die Angaben 'Titrationsphase' und 'Max. tolerierte Dosis erreicht' dürfen nicht gleichzeitig angekreuzt werden.",
}

var manualErrorFields = map[string]string{
	"KHK-STATINGRUND": "Grund für moderate oder niedrige Statin-Dosis",
	"AB-001":          "Aktueller FEV1-Wert (mindestens alle 12 Monate)",
	"AB-007":          "Aktueller FEV1-Wert (mindestens alle 12 Monate)",
	"BKA-076":         "Lokalisation von Fernmetastasen (Datum der diagnostischen Sicherung)",
}

var (
	regexFieldNameError   = regexp.MustCompile(`'([^']+)'`)
	regexLineNumberError  = regexp.MustCompile(`\d+`)
	regexTypeErrorSchema  = regexp.MustCompile("(F*/([0-9]+))")
	regexTypeErrorInput   = regexp.MustCompile("(F*)")
	regexTypeErrorWarning = regexp.MustCompile("(W)")
)

/*
TODO: support for nested fields

In FE we are showing error for nested field with format Field/Nested Field
But the response of XPM just contains header and error field only
*/
var manualErrorNestedFieldToExtendField = map[string]map[string]string{
	"Schulung": {
		"Diabetes-Schulung":   "Empfohlene Schulung(en) wahrgenommen_Diabetes-Schulung",
		"Hypertonie-Schulung": "Empfohlene Schulung(en) wahrgenommen_Hypertonie-Schulung",
	},
	"Medikamente": {
		"Betablocker/Evidenzbasierte Zieldosis": "Betablocker_Evidenzbasierte Zieldosis",
		"ACE-Hemmer/Evidenzbasierte Zieldosis":  "ACE-Hemmer_Evidenzbasierte Zieldosis",
	},
	"Relevante Ereignisse": {
		"keine der genannten Ereignisse": "Relevante Ereignisse",
	},
}

var mapDMPLabelingValueWithDMPName = map[common.DMPValueEnum]string{
	common.DMPValueEnum_MellitusType1:         "diabetes1",
	common.DMPValueEnum_MellitusType2:         "diabetes2",
	common.DMPValueEnum_Brustkrebs:            "brustkrebs",
	common.DMPValueEnum_CoronaryArteryDisease: "khk",
	common.DMPValueEnum_AsthmaBronchiale:      "asthma",
	common.DMPValueEnum_COPD:                  "copd",
	common.DMPValueEnum_ChronicHeartFailure:   "herzinsuffizienz",
	common.DMPValueEnum_Depression:            "depression",
	common.DMPValueEnum_BackPain:              "ruecken",
	common.DMPValueEnum_EDO_SkinCancer:        "ehks",
}

var mapDocumentType = map[common.DocumentType]DocumentType{
	common.DocumentType_ED:      DocumentType_ED,
	common.DocumentType_PD:      DocumentType_ED,
	common.DocumentType_FD:      DocumentType_FD,
	common.DocumentType_EHKS_D:  DocumentType_EHKS_D,
	common.DocumentType_EHKS_ND: DocumentType_EHKS_ND,
}

type DocumentType string

type (
	FileType   string
	StatusFile int
)

type Result struct {
	Status            StatusFile `json:"status"`
	ErrorMessage      string     `json:"errorMessage"`
	ProtocolsBase64   string     `json:"protocolsBase64"`
	StatisticBase64   string     `json:"statisticBase64"`
	StatisticBase64ND string     `json:"statisticBase64ND"`
}

type XPMErrorCodeType string

type ErrorMessage struct {
	ErrorNo         string           `json:"errorNo"`
	Message         string           `json:"message"`
	LineNumberError *int32           `json:"lineNumberError"`
	HeaderName      string           `json:"headerName"`
	FieldName       string           `json:"fieldName"`
	ErrorType       common.ErrorType `json:"errorType"`
	BillingFileName string           `json:"billingFileName"`
}

type ErrorResponse struct {
	Result        common.EdmpTestModuleResultEnum
	DMPCaseNumber string
	ErrorMessages []ErrorMessage
}

type Record struct {
	Text         string `xml:",chardata"`
	GRUPPE       string `xml:"GRUPPE"`
	FEHLERNR     string `xml:"FEHLER_NR"`
	FEHLER_TEXT  string `xml:"FEHLER_TEXT"`
	MELDUNG      string `xml:"MELDUNG"`
	DocumentDate int64
	ANZAHL       int32 `xml:"ANZAHL"`
}

type XMLResponse struct {
	XMLName   xml.Name `xml:"data"`
	Text      string   `xml:",chardata"`
	Parameter struct {
		Text          string `xml:",chardata"`
		ABBRUCH       string `xml:"ABBRUCH"`
		ARZTNR        string `xml:"ARZT_NR"`
		BSNR          string `xml:"BSNR"`
		DATEI         string `xml:"DATEI"`
		DATEIDATUM    string `xml:"DATEI_DATUM"`
		DATUM         string `xml:"DATUM"`
		DMPNR         string `xml:"DMP_NR"`
		ERGEBNISTEXT  string `xml:"ERGEBNIS_TEXT"`
		FEHLER        string `xml:"FEHLER"`
		GEBAM         string `xml:"GEB_AM"`
		INFOS         string `xml:"INFOS"`
		KASSENR       string `xml:"KASSE_NR"`
		KBVTABVERSION string `xml:"KBV_TAB_VERSION"`
		KTVERSION     string `xml:"KT_VERSION"`
		NAME          string `xml:"NAME"`
		PAKETVERSION  string `xml:"PAKET_VERSION"`
		RETURNCODE    string `xml:"RETURN_CODE"`
		TITEL         string `xml:"TITEL"`
		WARNUNGEN     string `xml:"WARNUNGEN"`
		XPMVERSION    string `xml:"XPM_VERSION"`
		ZEIT          string `xml:"ZEIT"`
	} `xml:"parameter"`
	Record []Record `xml:"record"`
}

func Verify(edmpTestmodulePath string,
	dmpValue string, version string,
	documentType common.DocumentType,
	file []byte,
	fileType FileType,
	attachmentFileName string,
	isZipFile bool,
) (*Result, error) {
	if version == "" {
		return nil, errors.New("version is required")
	}
	testmoduleDMPName := mapDMPLabelingValueWithDMPName[common.DMPValueEnum(dmpValue)]
	// http://localhost:8888/asthma-2023.3.0/?fileType=PDF&documentType=FD
	url := fmt.Sprintf("%s/%s-%s/?fileType=%s&documentType=%s", edmpTestmodulePath, testmoduleDMPName, version, fileType, mapDocumentType[documentType])
	if isZipFile {
		url += "&isZipFile=true"
	}
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	fileField, err := writer.CreateFormFile("file", attachmentFileName)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to create form file: %s, url: %s", attachmentFileName, url)
	}
	_, err = fileField.Write(file)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to write file: %s, url: %s", attachmentFileName, url)
	}

	err = writer.Close()
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to close writer: %s, url: %s", attachmentFileName, url)
	}

	client := &http.Client{}
	client = titan.NewTraceHTTPClient(client)
	req, err := http.NewRequest("POST", url, body)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to create request: %s, url: %s", attachmentFileName, url)
	}
	req.Header.Set("Content-Type", writer.FormDataContentType())

	res, err := client.Do(req)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to do request: %s, url: %s", attachmentFileName, url)
	}
	defer res.Body.Close()

	responseBody, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to read response body: %s, url: %s", string(responseBody), url)
	}

	var result Result
	err = json.Unmarshal(responseBody, &result)
	if err != nil {
		return nil, errors.WithMessagef(err, "failed to unmarshal response body: %s, url: %s", string(responseBody), url)
	}

	return &result, nil
}

func (r *Record) getErrorMessage() string {
	if r == nil {
		return ""
	}
	// specific case for zip xpm check
	if r.FEHLER_TEXT != "" {
		return r.FEHLER_TEXT
	}

	// specific case for AKTUELLE STATIN-DOSIS
	if strings.Contains(r.MELDUNG, "Aktuelle Statin-Dosis") && strings.Contains(r.FEHLERNR, "KHK-STATINGRUND") {
		return manualErrorMap["KHK-STATINGRUND"]
	}

	// specific case for Sensibilitätsprüfung
	if strings.Contains(r.MELDUNG, "Sensibilitätsprüfung") {
		return manualErrorMap["Sensibilitätsprüfung"]
	}

	if strings.Contains(r.MELDUNG, "'Titrationsphase' und 'Max. tolerierte Dosis erreicht'") {
		return manualErrorMap["HIS-018"]
	}

	// BKA-087 (F*) -> BKA-087
	errCode := strings.Split(r.FEHLERNR, " ")
	errorMessage, found := manualErrorMap[errCode[0]]
	if found {
		return errorMessage
	}

	return r.MELDUNG
}

func (r *Record) getBillingFileName() string {
	if r.GRUPPE == "" {
		return ""
	}
	re := regexp.MustCompile(`(?m)^Datei:\s+([\w\d_]+\.(HKSD|HKSND|HKSDEV|HKSNDEV))`)
	matches := re.FindStringSubmatch(r.GRUPPE)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

func (r *Record) getErrorHeader() string {
	// fix for specific field LDL-Cholesterin
	if strings.Contains(r.MELDUNG, "Cholesterin") && r.GRUPPE == "Verschiedenes" {
		return "Anamnese- und Befunddaten"
	}

	return r.GRUPPE
}

func (r *Record) getErrorType() common.ErrorType {
	if r == nil {
		return ""
	}

	schemaErrorMatches := regexTypeErrorSchema.FindStringSubmatch(r.FEHLERNR)
	if len(schemaErrorMatches) > 1 {
		return common.ErrorType_Schema
	}

	warningMatches := regexTypeErrorWarning.FindStringSubmatch(r.FEHLERNR)
	if len(warningMatches) > 1 {
		return common.ErrorType_Warning
	}

	errorMatches := regexTypeErrorInput.FindStringSubmatch(r.FEHLERNR)
	if len(errorMatches) > 1 {
		return common.ErrorType_Error
	}

	return ""
}

type fieldNameConverter func(r *Record) string

var CholesterinConverter = func(r *Record) string {
	if strings.Contains(r.MELDUNG, "Cholesterin") {
		return "LDL-Cholesterin"
	}
	return ""
}

var InjektionsstellenConverter = func(r *Record) string {
	if strings.Contains(r.MELDUNG, "Feld Injektionsstellen") {
		return "Injektionsstellen (bei Insulintherapie)"
	}
	return ""
}

var FEV1WERTConverter = func(r *Record) string {
	if strings.Contains(r.MELDUNG, "FEV1-Wert") {
		return "Aktueller FEV1-Wert (alle 6 bis 12 Monate)"
	}
	return ""
}

var DiabetesMellitusTypConverter = func(r *Record) string {
	if strings.Contains(r.MELDUNG, "Diabetes mellitus Typ") {
		return "Einschreibung wegen"
	}
	return ""
}

var SpecialCaseHIConverter = func(r *Record) string {
	if r.GRUPPE == "Medikamente" {
		if strings.Contains(r.MELDUNG, "'Betablocker'") {
			return "Betablocker"
		}
		if strings.Contains(r.MELDUNG, "'ACE-Hemmer'") {
			return "ACE-Hemmer"
		}
		if strings.Contains(r.MELDUNG, "'Mineralokortikoid-Rezeptor-Antagonist (MRA)'") {
			return "Mineralokortikoid-Rezeptor-Antagonist (MRA)"
		}
		if strings.Contains(r.MELDUNG, "'SGLT2-Inhibitor'") {
			return "SGLT2-Inhibitor"
		}
		if strings.Contains(r.MELDUNG, "'Mineralokortikoid-Rezeptor-Antagonist (MRA)/Evidenzbasierte Zieldosis'") {
			return "Evidenzbasierte Zieldosis MRA"
		}
		if strings.Contains(r.MELDUNG, "'Betablocker/Evidenzbasierte Zieldosis'") {
			yearQuarter := util.ToYearQuarter(r.DocumentDate)
			if yearQuarter.Year > 2024 {
				return "Evidenzbasierte Zieldosis Betablocker"
			}
		}
		if strings.Contains(r.MELDUNG, "'ACE-Hemmer/Evidenzbasierte Zieldosis'") {
			yearQuarter := util.ToYearQuarter(r.DocumentDate)
			if yearQuarter.Year > 2024 {
				return "Evidenzbasierte Zieldosis ACE-Hemmer oder ARB"
			}
		}
	}
	return ""
}

var ManualErrorFieldsConverter = func(r *Record) string {
	errorCode := strings.Fields(r.FEHLERNR)
	if len(errorCode) == 0 {
		return ""
	}
	if len(manualErrorFields[errorCode[0]]) > 0 {
		return manualErrorFields[errorCode[0]]
	}
	return ""
}

var ManualErrorNestedFieldToExtendFieldConverter = func(r *Record) string {
	errorFieldName := getErrorFieldName(r.MELDUNG)
	if errorFieldName == "Betablocker/Evidenzbasierte Zieldosis" && r.GRUPPE == "Medikamente" {
		yearQuarter := util.ToYearQuarter(r.DocumentDate)
		if yearQuarter.Year > 2024 {
			return "Evidenzbasierte Zieldosis Betablocker"
		}
	}
	if errorFieldName == "ACE-Hemmer/Evidenzbasierte Zieldosis" && r.GRUPPE == "Medikamente" {
		yearQuarter := util.ToYearQuarter(r.DocumentDate)
		if yearQuarter.Year > 2024 {
			return "Evidenzbasierte Zieldosis ACE-Hemmer oder ARB"
		}
	}
	if fieldName, found := manualErrorNestedFieldToExtendField[r.GRUPPE][errorFieldName]; found {
		return fieldName
	}
	return ""
}

var FieldNamesXPMShortedConverter = func(r *Record) string {
	errorFieldName := getErrorFieldName(r.MELDUNG)
	if fieldNamesXPMShorted[errorFieldName] != "" {
		return fieldNamesXPMShorted[errorFieldName]
	}
	return ""
}

var re = regexp.MustCompile(`(?:darf im Feld|zum Feld|vom Typ|im Element) '([^']+)'`)

var FieldNamesEHKSConverter = func(r *Record) string {
	if r.FEHLERNR == "HKS-ZUL2 (F*)" {
		parentField, childField := extractFieldNames(r.MELDUNG)
		return parentField + "/" + childField
	}

	if r.FEHLERNR == "HKS-WERT2 (F*/214)" ||
		r.FEHLERNR == "HKS-FRMT2 (F*/214)" ||
		r.FEHLERNR == "HKS-FRMT2 (F*/168)" ||
		r.FEHLERNR == "HKS-WERT2 (F*/168)" ||
		r.MELDUNG == "Die Angabe zum Feld 'horizontaler Tumordurchmesser (klinisch)' fehlt. " {
		return "Basalzellkarzinom/horizontaler Tumordurchmesser (klinisch)"
	}

	if r.FEHLERNR == "HKS-WERT2 (F*/218)" ||
		r.FEHLERNR == "HKS-FRMT2 (F*/218)" ||
		r.MELDUNG == "Die Angabe zum Feld 'vertikaler Tumordurchmesser (histologisch)' fehlt. " {
		return "Basalzellkarzinom/vertikaler Tumordurchmesser (histologisch)"
	}

	if r.FEHLERNR == "HKS-FRMT (F*/135)" {
		return "Anzahl der entnommenen Biopsien/Exzisionen"
	}

	matches := re.FindStringSubmatch(r.MELDUNG)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

func extractFieldNames(message string) (parentField string, childField string) {
	pattern := regexp.MustCompile(`im Feld '([^']+)'`)
	matches := pattern.FindAllStringSubmatch(message, -1)

	if len(matches) > 0 && len(matches[0]) > 1 {
		parentField = strings.TrimSpace(matches[0][1])
	}
	if len(matches) > 1 && len(matches[1]) > 1 {
		childField = strings.TrimSpace(matches[1][1])
	}

	return parentField, childField
}

// regexConverter get error field by regex
func getErrorFieldName(v string) string {
	matches := regexFieldNameError.FindAllStringSubmatch(v, -1)
	if len(matches) == 0 || len(matches[0][1]) == 0 {
		return ""
	}

	return matches[0][1]
}

// Feld 'Operative Therapie' ist ein Pflichtfeld. --> Operative Therapie
func (r *Record) GetFieldName(converters ...fieldNameConverter) string {
	// NOTE: with gruppe == header meaning this error has no field name -> should be return to UI to ez to avoid
	if r == nil || r.GRUPPE == "Header" {
		return ""
	}

	errorFieldName := getErrorFieldName(r.MELDUNG)

	for _, converter := range converters {
		fieldName := converter(r)
		if len(fieldName) > 0 {
			return fieldName
		}

	}
	return errorFieldName
}

func (r *Record) getLineNumberError() *int32 {
	// >DMP-001 (F*/8) --> 8
	matches := regexLineNumberError.FindAllString(r.FEHLERNR, -1)
	if len(matches) > 0 {
		numStr := matches[len(matches)-1]
		num, err := strconv.ParseInt(numStr, 10, 32)
		if err != nil {
			return nil
		}

		lineNumber := int32(num)
		return &lineNumber
	}

	return nil
}

var edmpTestModuleResultMapBool = map[common.EdmpTestModuleResultEnum]bool{
	common.EdmpTestModuleResultEnum_Correct:   true,
	common.EdmpTestModuleResultEnum_Incorrect: false,
}

func ConvertToErrorResponse(errBase64List []string, fields []common.Field, documentDate int64) (*ErrorResponse, error) {
	result := &ErrorResponse{
		Result:        common.EdmpTestModuleResultEnum_Correct,
		DMPCaseNumber: "",
	}
	for _, errBase64 := range errBase64List {
		decodedBytes, err := base64.StdEncoding.DecodeString(errBase64)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to decode base64")
		}

		res, err := xml_util.XMlDecode[XMLResponse](decodedBytes)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to decode xml")
		}

		if res.Parameter.RETURNCODE == "0" {
			continue
		}
		result.Result = function.Do(func() common.EdmpTestModuleResultEnum {
			if edmpTestModuleResultMapBool[result.Result] && res.Parameter.ERGEBNISTEXT != "Fehlerhaft" {
				return common.EdmpTestModuleResultEnum_Correct
			}
			return common.EdmpTestModuleResultEnum_Incorrect
		})
		result.DMPCaseNumber = res.Parameter.KASSENR

		// TODO: get all errors from xpm
		var errorMessages []ErrorMessage
		for _, r := range res.Record {
			r.DocumentDate = documentDate
			errorMessages = append(errorMessages, ErrorMessage{
				ErrorNo:         r.FEHLERNR,
				Message:         r.getErrorMessage(),
				LineNumberError: r.getLineNumberError(),
				HeaderName:      r.getErrorHeader(),
				ErrorType:       r.getErrorType(),
				FieldName: r.GetFieldName(
					CholesterinConverter,
					InjektionsstellenConverter,
					FEV1WERTConverter,
					DiabetesMellitusTypConverter,
					SpecialCaseHIConverter,
					ManualErrorFieldsConverter,
					ManualErrorNestedFieldToExtendFieldConverter,
					FieldNamesXPMShortedConverter,
					FieldNamesEHKSConverter,
				),
				// for xpm zip check
				BillingFileName: r.getBillingFileName(),
			})
		}
		result.ErrorMessages = append(result.ErrorMessages, errorMessages...)

		// temp solution to override error field name for quarter 2 2024
		result.ErrorMessages = res.overrideFieldErrorName(result.ErrorMessages, fields)
	}
	return result, nil
}

func (r *XMLResponse) overrideFieldErrorName(errorMessages []ErrorMessage, fields []common.Field) []ErrorMessage {
	date, err := util.ConvertDateStringToTime(strings.Replace(r.Parameter.DATUM, ".", "", -1))
	if err != nil {
		return nil
	}

	yearQuarter := util.ToYearQuarter(date.UnixMilli())
	if (yearQuarter.Year == 2024 && yearQuarter.Quarter >= 2) || yearQuarter.Year > 2024 {
		for i, e := range errorMessages {
			if e.FieldName == "Aktueller FEV1-Wert (alle 6 bis 12 Monate)" {
				hasAktuellerFEV1WertField := slice.FindOne(fields, func(field common.Field) bool {
					return field.Name == "Aktueller FEV1-Wert (alle sechs bis zwölf Monate)"
				})

				if hasAktuellerFEV1WertField != nil {
					errorMessages[i].FieldName = "Aktueller FEV1-Wert (alle sechs bis zwölf Monate)"
				}
			}

			if yearQuarter.Year > 2024 && e.FieldName == "Empfohlene Schulung wahrgenommen" {
				errorMessages[i].FieldName = "Empfohlene Schulung(en) wahrgenommen"
			}
		}
	}

	return errorMessages
}

func (s *Result) GetError(XPMFileType common.XPMFileType, fields []common.Field, documentDate int64, isZipFile bool) (*common.XPMErrorResponse, error) {
	var errorBase64 string
	var errorBase64List []string
	if isZipFile {
		if len(s.StatisticBase64) > 0 {
			errorBase64List = append(errorBase64List, s.StatisticBase64)
		}
		if len(s.StatisticBase64ND) > 0 {
			errorBase64List = append(errorBase64List, s.StatisticBase64ND)
		}
	} else {
		errorBase64 = s.ProtocolsBase64
		if XPMFileType == common.XPMFileType_Statistic {
			errorBase64 = s.StatisticBase64
		}
		if len(errorBase64) > 0 {
			errorBase64List = append(errorBase64List, errorBase64)
		}
	}

	if len(errorBase64List) == 0 {
		return nil, fmt.Errorf("errBase64 is empty")
	}

	errorResponse, err := ConvertToErrorResponse(errorBase64List, fields, documentDate)
	if err != nil {
		return nil, err
	}

	return &common.XPMErrorResponse{
		DPMCaseNumber: errorResponse.DMPCaseNumber,
		XPMErrorContents: slice.Map(errorResponse.ErrorMessages, func(e ErrorMessage) common.XPMErrorContent {
			return common.XPMErrorContent{
				ErrorNo:         e.ErrorNo,
				Message:         e.Message,
				LineNumberError: util.GetPointerValue(e.LineNumberError),
				HeaderName:      e.HeaderName,
				FieldName:       e.FieldName,
				ErrorType:       e.ErrorType,
				BillingFileName: e.BillingFileName,
			}
		}),
		XPMFileType: XPMFileType,
	}, nil
}

func HealthCheck(baseUrl, path string) (*string, error) {
	url := fmt.Sprintf("%s/%s", baseUrl, path)
	method := "GET"

	client := &http.Client{}
	client = titan.NewTraceHTTPClient(client)
	req, err := http.NewRequest(method, url, nil)
	if err != nil {
		return nil, err
	}
	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}

	result := string(body)

	return &result, nil
}
