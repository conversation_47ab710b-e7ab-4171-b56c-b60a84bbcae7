package test

import (
	_ "embed"
	"encoding/base64"
	"testing"

	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/pkg/edmp_testmodule"
	"git.tutum.dev/medi/tutum/ares/pkg/test"
	"git.tutum.dev/medi/tutum/ares/service/domains/edmp/common"
	"github.com/stretchr/testify/require"
)

//go:embed testdata/XPM_RESULT_521111100_Dq4txYZ_20230728.EEAB
var testBase64 []byte

func TestConvertToErrorResponse(t *testing.T) {
	fileDecoded := base64.StdEncoding.EncodeToString(testBase64)
	res, err := edmp_testmodule.ConvertToErrorResponse([]string{fileDecoded}, []common.Field{}, 0)
	require.NoError(t, err)
	require.NotEmpty(t, res.Result)
	require.NotEmpty(t, res.DMPCaseNumber)
	require.Equal(t, common.EdmpTestModuleResultEnum_Incorrect, res.Result)
}

func Test_getFieldName(t *testing.T) {
	type args struct {
		str     string
		errCode string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "Feld 'Operative Therapie' ist ein Pflichtfeld",
			args: args{
				str:     "Feld 'Operative Therapie' ist ein Pflichtfeld",
				errCode: "DMP-021 (F*)",
			},
			want: "Operative Therapie",
		},
		{
			name: "Feld 'TNM-Klassifizierung' ist ein Pflichtfeld",
			args: args{
				str:     "Feld 'TNM-Klassifizierung' ist ein Pflichtfeld",
				errCode: "DMP-021 (F*)",
			},
			want: "TNM-Klassifizierung",
		},
		{
			name: "Feld 'T' ist ein Pflichtfeld",
			args: args{
				str:     "Feld 'T' ist ein Pflichtfeld",
				errCode: "DMP-021 (F*)",
			},
			want: "T",
		},
		{
			name: "Feld 'N' ist ein Pflichtfeld",
			args: args{
				str:     "Feld 'N' ist ein Pflichtfeld",
				errCode: "DMP-021 (F*)",
			},
			want: "N",
		},
		{
			name: `Formatfehler. Die Angabe 'baf3b05f-2d6c-4ee7-a0d0-b45dbfe5818b' entspricht nicht dem erlaubten Format gemäss des regulären Ausdrucks '[\p{L}|\d]*'. Die Wertangabe 'baf3b05f-2d6c-4ee7-a0d0-b45dbfe5818b' beim Attribut 'EX' im Element 'Dokument-ID bzw. Dokument-ID (ID und Vertragsarztnummer) bzw. Krankenhaus-IK oder BSNR oder LANR bzw. Patientennummer (DMP-Nummer)' stimmt mit seiner Definition nicht überein. Element '/levelone/clinical_document_header/patient/person/id'.`,
			args: args{
				str:     `Formatfehler. Die Angabe 'baf3b05f-2d6c-4ee7-a0d0-b45dbfe5818b' entspricht nicht dem erlaubten Format gemäss des regulären Ausdrucks '[\p{L}|\d]*'. Die Wertangabe 'baf3b05f-2d6c-4ee7-a0d0-b45dbfe5818b' beim Attribut 'EX' im Element 'Dokument-ID bzw. Dokument-ID (ID und Vertragsarztnummer) bzw. Krankenhaus-IK oder BSNR oder LANR bzw. Patientennummer (DMP-Nummer)' stimmt mit seiner Definition nicht überein. Element '/levelone/clinical_document_header/patient/person/id'.`,
				errCode: "DMP-021 (F*)",
			},
			want: "baf3b05f-2d6c-4ee7-a0d0-b45dbfe5818b",
		},
		{
			name: "Cholesterin",
			args: args{
				str:     "Cholesterin",
				errCode: "DMP-021 (F*)",
			},
			want: "LDL-Cholesterin",
		},
		{
			name: "Feld Injektionsstellen",
			args: args{
				str:     "Feld Injektionsstellen",
				errCode: "DMP-021 (F*)",
			},
			want: "Injektionsstellen (bei Insulintherapie)",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := edmp_testmodule.Record{
				FEHLERNR: tt.args.errCode,
				MELDUNG:  tt.args.str,
			}
			if got := r.GetFieldName(
				edmp_testmodule.CholesterinConverter,
				edmp_testmodule.InjektionsstellenConverter,
				edmp_testmodule.ManualErrorFieldsConverter,
				edmp_testmodule.ManualErrorNestedFieldToExtendFieldConverter,
				edmp_testmodule.FieldNamesXPMShortedConverter,
			); got != tt.want {
				t.Errorf("getFieldName() = %v, want %v", got, tt.want)
			}
		})
	}
}

var xpmSupportedVersions = []string{
	"asthma-2024.3.0?fileType=XML&documentType=ED",
	"asthma-2024.3.0?fileType=XML&documentType=FD",
	"asthma-2024.2.0?fileType=XML&documentType=ED",
	"asthma-2024.2.0?fileType=XML&documentType=FD",
	"asthma-2024.1.0?fileType=XML&documentType=ED",
	"asthma-2024.1.0?fileType=XML&documentType=FD",
	"asthma-2023.4.0?fileType=XML&documentType=ED",
	"asthma-2023.4.0?fileType=XML&documentType=FD",
	"asthma-2023.3.0?fileType=XML&documentType=ED",
	"asthma-2023.3.0?fileType=XML&documentType=FD",
	"asthma-2023.2.0?fileType=XML&documentType=ED",
	"asthma-2023.2.0?fileType=XML&documentType=FD",
	"asthma-2023.1.0?fileType=XML&documentType=ED",
	"asthma-2023.1.0?fileType=XML&documentType=FD",

	"brustkrebs-2024.3.0?fileType=XML&documentType=ED",
	"brustkrebs-2024.3.0?fileType=XML&documentType=FD",
	"brustkrebs-2024.2.0?fileType=XML&documentType=ED",
	"brustkrebs-2024.2.0?fileType=XML&documentType=FD",
	"brustkrebs-2024.1.0?fileType=XML&documentType=ED",
	"brustkrebs-2024.1.0?fileType=XML&documentType=FD",
	"brustkrebs-2023.4.0?fileType=XML&documentType=ED",
	"brustkrebs-2023.4.0?fileType=XML&documentType=FD",
	"brustkrebs-2023.3.0?fileType=XML&documentType=ED",
	"brustkrebs-2023.3.0?fileType=XML&documentType=FD",
	"brustkrebs-2023.2.0?fileType=XML&documentType=ED",
	"brustkrebs-2023.2.0?fileType=XML&documentType=FD",
	"brustkrebs-2023.1.0?fileType=XML&documentType=ED",
	"brustkrebs-2023.1.0?fileType=XML&documentType=FD",

	"copd-2024.3.0?fileType=XML&documentType=ED",
	"copd-2024.3.0?fileType=XML&documentType=FD",
	"copd-2024.2.0?fileType=XML&documentType=ED",
	"copd-2024.2.0?fileType=XML&documentType=FD",
	"copd-2024.1.0?fileType=XML&documentType=ED",
	"copd-2024.1.0?fileType=XML&documentType=FD",
	"copd-2023.4.0?fileType=XML&documentType=ED",
	"copd-2023.4.0?fileType=XML&documentType=FD",
	"copd-2023.3.0?fileType=XML&documentType=ED",
	"copd-2023.3.0?fileType=XML&documentType=FD",
	"copd-2023.2.0?fileType=XML&documentType=ED",
	"copd-2023.2.0?fileType=XML&documentType=FD",
	"copd-2023.1.0?fileType=XML&documentType=ED",
	"copd-2023.1.0?fileType=XML&documentType=FD",

	"herzinsuffizienz-2024.3.0?fileType=XML&documentType=ED",
	"herzinsuffizienz-2024.3.0?fileType=XML&documentType=FD",
	"herzinsuffizienz-2024.2.0?fileType=XML&documentType=ED",
	"herzinsuffizienz-2024.2.0?fileType=XML&documentType=FD",
	"herzinsuffizienz-2024.1.0?fileType=XML&documentType=ED",
	"herzinsuffizienz-2024.1.0?fileType=XML&documentType=FD",
	"herzinsuffizienz-2023.4.0?fileType=XML&documentType=ED",
	"herzinsuffizienz-2023.4.0?fileType=XML&documentType=FD",
	"herzinsuffizienz-2023.3.0?fileType=XML&documentType=ED",
	"herzinsuffizienz-2023.3.0?fileType=XML&documentType=FD",
	"herzinsuffizienz-2023.2.0?fileType=XML&documentType=ED",
	"herzinsuffizienz-2023.2.0?fileType=XML&documentType=FD",
	"herzinsuffizienz-2023.1.0?fileType=XML&documentType=ED",
	"herzinsuffizienz-2023.1.0?fileType=XML&documentType=FD",

	"khk-2024.3.0?fileType=XML&documentType=ED",
	"khk-2024.3.0?fileType=XML&documentType=FD",
	"khk-2024.2.0?fileType=XML&documentType=ED",
	"khk-2024.2.0?fileType=XML&documentType=FD",
	"khk-2024.1.0?fileType=XML&documentType=ED",
	"khk-2024.1.0?fileType=XML&documentType=FD",
	"khk-2023.4.0?fileType=XML&documentType=ED",
	"khk-2023.4.0?fileType=XML&documentType=FD",
	"khk-2023.3.0?fileType=XML&documentType=ED",
	"khk-2023.3.0?fileType=XML&documentType=FD",
	"khk-2023.2.0?fileType=XML&documentType=ED",
	"khk-2023.2.0?fileType=XML&documentType=FD",
	"khk-2023.1.0?fileType=XML&documentType=ED",
	"khk-2023.1.0?fileType=XML&documentType=FD",

	"depression-2024.3.0?fileType=XML&documentType=ED",
	"depression-2024.3.0?fileType=XML&documentType=FD",
	"depression-2024.2.0?fileType=XML&documentType=ED",
	"depression-2024.2.0?fileType=XML&documentType=FD",
	"depression-2024.1.0?fileType=XML&documentType=ED",
	"depression-2024.1.0?fileType=XML&documentType=FD",
	"depression-2023.4.0?fileType=XML&documentType=ED",
	"depression-2023.4.0?fileType=XML&documentType=FD",

	"diabetes1-2024.3.0?fileType=XML&documentType=ED",
	"diabetes1-2024.3.0?fileType=XML&documentType=FD",
	"diabetes1-2022.3.0?fileType=XML&documentType=ED",
	"diabetes1-2022.3.0?fileType=XML&documentType=FD",
	"diabetes1-2022.2.0?fileType=XML&documentType=ED",
	"diabetes1-2022.2.0?fileType=XML&documentType=FD",
	"diabetes1-2024.2.0?fileType=XML&documentType=ED",
	"diabetes1-2024.2.0?fileType=XML&documentType=FD",
	"diabetes1-2024.1.0?fileType=XML&documentType=ED",
	"diabetes1-2024.1.0?fileType=XML&documentType=FD",
	"diabetes1-2023.4.0?fileType=XML&documentType=ED",
	"diabetes1-2023.4.0?fileType=XML&documentType=FD",
	"diabetes1-2023.3.0?fileType=XML&documentType=ED",
	"diabetes1-2023.3.0?fileType=XML&documentType=FD",
	"diabetes1-2023.2.0?fileType=XML&documentType=ED",
	"diabetes1-2023.2.0?fileType=XML&documentType=FD",
	"diabetes1-2023.1.0?fileType=XML&documentType=ED",
	"diabetes1-2023.1.0?fileType=XML&documentType=FD",

	"diabetes2-2024.3.0?fileType=XML&documentType=ED",
	"diabetes2-2024.3.0?fileType=XML&documentType=FD",
	"diabetes2-2022.3.0?fileType=XML&documentType=ED",
	"diabetes2-2022.3.0?fileType=XML&documentType=FD",
	"diabetes2-2022.2.0?fileType=XML&documentType=ED",
	"diabetes2-2022.2.0?fileType=XML&documentType=FD",
	"diabetes2-2024.2.0?fileType=XML&documentType=ED",
	"diabetes2-2024.2.0?fileType=XML&documentType=FD",
	"diabetes2-2024.1.0?fileType=XML&documentType=ED",
	"diabetes2-2024.1.0?fileType=XML&documentType=FD",
	"diabetes2-2023.4.0?fileType=XML&documentType=ED",
	"diabetes2-2023.4.0?fileType=XML&documentType=FD",
	"diabetes2-2023.3.0?fileType=XML&documentType=ED",
	"diabetes2-2023.3.0?fileType=XML&documentType=FD",
	"diabetes2-2023.2.0?fileType=XML&documentType=ED",
	"diabetes2-2023.2.0?fileType=XML&documentType=FD",
	"diabetes2-2023.1.0?fileType=XML&documentType=ED",
	"diabetes2-2023.1.0?fileType=XML&documentType=FD",
}

func Test_HealthCheck(t *testing.T) {
	test.SetDefaultConfigValues()
	config, _ := config.MvzAppConfigMod.SafeResolve()
	for _, v := range xpmSupportedVersions {
		res, err := edmp_testmodule.HealthCheck(config.EdmpTestmoduleUrl, v)
		require.NoError(t, err)
		require.NotNil(t, res)
	}
}
