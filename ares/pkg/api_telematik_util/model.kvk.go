package api_telematik_util

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"io/ioutil"
	"strings"

	pkg_validator "git.tutum.dev/medi/tutum/ares/pkg/validator"
	"github.com/duke-git/lancet/v2/validator"
	"github.com/jinzhu/copier"
	"gitlab.com/silenteer-oss/titan"

	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/FA_VSD"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/FA_VSD_PKV"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/FA_VSD_PKV_PN"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/FA_VSD_PN"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/VSD"
	"git.tutum.dev/medi/tutum/pkg/pkg_cardreader"
)

func GetInsuranceType(readVSDResponse *VSD.ReadVsdresponse) patient_profile_common.TypeOfInsurance {
	insuranceStr, err := unzipBase64Data(readVSDResponse.AllgemeineVersicherungsdaten)
	if err != nil {
		return patient_profile_common.Public
	}
	if strings.Contains(insuranceStr, "KTR_TYP") {
		return patient_profile_common.Private
	}
	return patient_profile_common.Public
}

func GetEgkData(readVSDResponse *VSD.ReadVsdresponse, isMobileCard bool) (*EgkData, error) {
	personalB, err := unzipBase64Data(readVSDResponse.PersoenlicheVersichertendaten)
	if err != nil {
		return nil, err
	}
	personalXML, err := pkg_cardreader.UnmarshalXML[FA_VSD.UcPersoenlicheVersichertendatenXml]([]byte(personalB), isMobileCard)
	if err != nil {
		return nil, err
	}

	insuranceB, err := unzipBase64Data(readVSDResponse.AllgemeineVersicherungsdaten)
	if err != nil {
		return nil, err
	}
	insuranceXML, err := pkg_cardreader.UnmarshalXML[FA_VSD.UcAllgemeineVersicherungsdatenXml]([]byte(insuranceB), isMobileCard)
	if err != nil {
		return nil, err
	}
	copaymentB, err := unzipBase64Data(readVSDResponse.GeschuetzteVersichertendaten)
	if err != nil {
		return nil, err
	}
	copaymentXML, err := pkg_cardreader.UnmarshalXML[FA_VSD.UcGeschuetzteVersichertendatenXml]([]byte(copaymentB), isMobileCard)
	if err != nil {
		return nil, err
	}

	pnXML := &FA_VSD_PN.Pn{}
	if len(readVSDResponse.Pruefungsnachweis) > 0 {
		auditEvidenceB, err := unzipBase64Data(readVSDResponse.Pruefungsnachweis)
		if err != nil {
			return nil, err
		}
		pnXML, err = pkg_cardreader.UnmarshalXML[FA_VSD_PN.Pn]([]byte(auditEvidenceB), isMobileCard)
		if err != nil {
			return nil, err
		}
	}
	var vsdStatus VSD_StatusType
	err = copier.Copy(&vsdStatus, readVSDResponse.VsdStatus)
	if err != nil {
		return nil, err
	}

	egkData := &EgkData{
		PersonalData:           personalXML,
		InsuranceData:          insuranceXML,
		ProtectedInsuranceData: copaymentXML,
		ProofOfInsurance:       pnXML,
		VsdStatus:              &vsdStatus,
	}
	return egkData, nil
}

func GetPrivateEgkData(readVSDResponse *VSD.ReadVsdresponse, isMobileCard bool) (*PrivateEGKData, error) {
	personalB, err := unzipBase64Data(readVSDResponse.PersoenlicheVersichertendaten)
	if err != nil {
		return nil, err
	}
	personalXML, err := pkg_cardreader.UnmarshalXML[FA_VSD_PKV.UcPersoenlicheVersichertendatenXml]([]byte(personalB), isMobileCard)
	if err != nil {
		return nil, err
	}

	insuranceB, err := unzipBase64Data(readVSDResponse.AllgemeineVersicherungsdaten)
	if err != nil {
		return nil, err
	}
	insuranceXML, err := pkg_cardreader.UnmarshalXML[FA_VSD_PKV.UcAllgemeineVersicherungsdatenXml]([]byte(insuranceB), isMobileCard)
	if err != nil {
		return nil, err
	}
	copaymentB, err := unzipBase64Data(readVSDResponse.GeschuetzteVersichertendaten)
	if err != nil {
		return nil, err
	}
	copaymentXML, err := pkg_cardreader.UnmarshalXML[FA_VSD_PKV.UcGeschuetzteVersichertendatenXml]([]byte(copaymentB), isMobileCard)
	if err != nil {
		return nil, err
	}

	pnXML := &FA_VSD_PKV_PN.Pn{}
	if len(readVSDResponse.Pruefungsnachweis) > 0 {
		auditEvidenceB, err := unzipBase64Data(readVSDResponse.Pruefungsnachweis)
		if err != nil {
			return nil, err
		}
		pnXML, err = pkg_cardreader.UnmarshalXML[FA_VSD_PKV_PN.Pn]([]byte(auditEvidenceB), isMobileCard)
		if err != nil {
			return nil, err
		}
	}

	privateEgkData := &PrivateEGKData{
		PersonalData:           personalXML,
		InsuranceData:          insuranceXML,
		ProtectedInsuranceData: copaymentXML,
		ProofOfInsurance:       pnXML,
	}
	return privateEgkData, nil
}

func unzipBase64Data(input string) (string, error) {
	if !validator.IsBase64(input) {
		return input, nil
	}

	z, err := base64.StdEncoding.DecodeString(string(input))
	if err != nil {
		return "", err
	}

	buf := bytes.NewBuffer(z)
	reader, err := gzip.NewReader(buf)
	if err != nil {
		return "", err
	}
	defer reader.Close()

	s, err := ioutil.ReadAll(reader)
	if err != nil {
		return "", err
	}
	return string(s), nil
}

func ZipBase64Data(input string) (*string, error) {
	data := []byte(input)

	var buf bytes.Buffer
	gz := gzip.NewWriter(&buf)

	if _, err := gz.Write(data); err != nil {
		return nil, err
	}

	if err := gz.Flush(); err != nil {
		return nil, err
	}

	if err := gz.Close(); err != nil {
		return nil, err
	}

	encoded := base64.StdEncoding.EncodeToString(buf.Bytes())

	return &encoded, nil
}

type VsdDataResponse struct {
	PatientInfo      *patient_profile_common.PatientInfo
	ProofOfInsurance *patient_profile_common.ProofOfInsurance
	WarningCode      error_code.ErrorCode
}

func GetVsdData(ctx *titan.Context, r *VSD.ReadVsdresponse) (*VsdDataResponse, error) {
	if r == nil {
		return nil, nil
	}

	insuranceType := GetInsuranceType(r)

	if insuranceType == patient_profile_common.Private {
		privateEgkData, err := GetPrivateEgkData(r, false)
		if err != nil {
			return nil, ParseTiError(ctx, err)
		}

		err = pkg_validator.Run(ctx, privateEgkData, &PrivateEgkValidator{})
		if err != nil {
			return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode(err.Error()))
		}

		patientInfo := privateEgkData.ToPatientProfile(ctx)
		proofOfInsurance := privateEgkData.GetProofOfInsurance()
		warningCode := GetErrorPkvPn(privateEgkData.ProofOfInsurance)

		return &VsdDataResponse{
			PatientInfo:      patientInfo,
			ProofOfInsurance: proofOfInsurance,
			WarningCode:      warningCode,
		}, nil
	}

	egkData, err := GetEgkData(r, false)
	if err != nil {
		return nil, ParseTiError(ctx, err)
	}

	err = pkg_validator.Run(ctx, egkData, &EgkValidator{})
	if err != nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode(err.Error()))
	}

	patientInfo := egkData.ToPatientProfile(ctx)
	proofOfInsurance := egkData.GetProofOfInsurance()
	warningCode := GetErrorPn(egkData.ProofOfInsurance)

	return &VsdDataResponse{
		PatientInfo:      patientInfo,
		ProofOfInsurance: proofOfInsurance,
		WarningCode:      warningCode,
	}, nil
}

func (r *VsdDataResponse) GetWarningCode() *error_code.ErrorCode {
	if r == nil {
		return nil
	}

	return &r.WarningCode
}

func (r *VsdDataResponse) GetPatientInfo() *patient_profile_common.PatientInfo {
	if r == nil {
		return nil
	}

	return r.PatientInfo
}
