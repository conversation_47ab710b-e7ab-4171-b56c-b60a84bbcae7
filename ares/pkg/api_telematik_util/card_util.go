package api_telematik_util

import (
	"fmt"
	"strings"

	"emperror.dev/errors"
	"github.com/duke-git/lancet/v2/structs"
	"gitlab.com/silenteer-oss/titan"

	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/card_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/CARD"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/CARDCMN"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/GERROR"
	"git.tutum.dev/medi/tutum/pkg/pkg_cardreader"
	"git.tutum.dev/medi/tutum/pkg/slice"
)

func GetCardHandleByType(cards []CARD.CardInfoType, cardType card_common.CardTypeType) (*string, error) {
	foundCard := slice.FindOne(cards, func(c CARD.CardInfoType) bool {
		fCardType := c.CardType
		return fCardType == CARDCMN.CardTypeType(cardType)
	})
	if foundCard == nil {
		return nil, fmt.Errorf("cannot find out card type %s", cardType)
	}
	return (*string)(&foundCard.CardHandle), nil
}

func ParseTiError(ctx *titan.Context, err error) error {
	clientResponseError, ok := err.(*titan.ClientResponseError)
	if !ok {
		return err
	}

	errorXml := string(clientResponseError.ErrorMsg)
	// remaining error timeout, unauthenticated
	if !strings.Contains(errorXml, "http://ws.gematik.de/tel/error/v2.0") {
		return err
	}

	beginIdx := strings.Index(errorXml, "<detail>")
	endIdx := strings.Index(errorXml, "</detail>")
	errorDetailXml := errorXml[beginIdx+8 : endIdx]
	tiError := &GERROR.Error{}
	errDecode := pkg_cardreader.DecodeXmlWithCharSet([]byte(errorDetailXml), tiError)
	if errDecode != nil {
		return err
	}
	if len(tiError.Trace) == 0 {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_CardReader_Error, errorDetailXml)
	}

	trace := tiError.Trace[0]
	traceMap, err := structs.New(trace, "xml").ToMap()
	if err != nil {
		return errors.WithMessage(err, "trace to map failed")
	}

	if slice.Contains([]int64{114, 106, 107, 113, 4192}, trace.Code) {
		return pkg_errors.NewTitanCommonExceptionWithParams(error_code.ErrorCode_CardReader_PN4WithErrorCode, traceMap)
	}
	if slice.Contains([]int64{3040, 3039}, trace.Code) {
		return pkg_errors.NewTitanCommonExceptionWithParams(error_code.ErrorCode_CardReader_ErrorCode30403039, traceMap)
	}
	if slice.Contains([]int64{3001, 12105}, trace.Code) {
		return pkg_errors.NewTitanCommonExceptionWithParams(error_code.ErrorCode_CardReader_ErrorCode300112105, traceMap)
	}
	if slice.Contains([]int64{4001}, trace.Code) {
		return pkg_errors.NewTitanCommonExceptionWithParams(error_code.ErrorCode_CardReader_UnknownType_Card, traceMap)
	}
	if slice.Contains([]int64{3041}, trace.Code) {
		return pkg_errors.NewTitanCommonExceptionWithParams(error_code.ErrorCode_CardReader_UnknownType_Card_Status, traceMap)
	}

	return pkg_errors.NewTitanCommonExceptionWithParams(error_code.ErrorCode_CardReader_Error, traceMap)
}
