package api_telematik_util

import (
	"encoding/xml"
	"fmt"

	"emperror.dev/errors"
	"git.tutum.dev/medi/tutum/ares/app/companion/ti_app"
	audit_log_service "git.tutum.dev/medi/tutum/ares/service/domains/audit_log/service"
	"gitlab.com/silenteer-oss/titan"
)

func SendRequest[REQ any, RES any](
	ctx *titan.Context,
	comApp ti_app.TiApp,
	request *REQ,
	requestType ti_app.Type,
	tiPtv string,
	tiInfo *ti_app.TiInfo,
) (*RES, error) {
	spanName := fmt.Sprintf("SendRequest_Telematik_%s", requestType)
	ctx, span := titan.SpanContext(ctx, ctx.GetHTTPHeader(), spanName)
	defer span.End()

	requestXml, err := xml.Marshal(request)
	if err != nil {
		return nil, errors.WithMessage(err, "marshal request failed")
	}
	logger, ok := ctx.Value(audit_log_service.CtxKey_AuditLogService).(audit_log_service.AuditLogger)
	if ok {
		eventType := fmt.Sprintf("%s_%s", audit_log_service.EventsSchemaJsonEventTypeTiRequest, requestType)
		logger.AddLog(ctx, eventType, request, ctx.TraceId())
	}

	res, err := comApp.CommandHandler(ctx, &ti_app.CommandHandlerRequest{
		Type:    requestType,
		Payload: string(requestXml),
		TiPtv:   ti_app.TiPtv(tiPtv),
		TiInfo:  tiInfo,
	})
	if err != nil {
		return nil, ParseTiError(ctx, err)
	}
	var result RES
	err = xml.Unmarshal([]byte(res.Payload), &result)
	if err != nil {
		return nil, errors.WithMessage(err, "Unmarshal response payload failed")
	}

	if ok {
		eventType := fmt.Sprintf("%s_%s", audit_log_service.EventsSchemaJsonEventTypeTiResponse, requestType)
		logger.AddLog(ctx, eventType, result, ctx.TraceId())
	}

	return &result, nil
}
