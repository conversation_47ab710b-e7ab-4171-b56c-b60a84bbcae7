package software

import "github.com/spf13/viper"

type SoftwareConfig struct {
	KbvPruefnummer string
	Version        string
	Vorname        string
	Nachname       string
	Name           string
	Organisation   string
	Strasse        string
	Hausnummer     string
	Plz            string
	Ort            string
	Stadt          string
	Telefon        string
	Telefax        string
	TelefonMobil   string
	SystemOid      string
}

func GetSoftwareConfig() SoftwareConfig {
	return SoftwareConfig{
		KbvPruefnummer: viper.GetString("SoftwareInformation.KbvPruefnummer"),
		Version:        viper.GetString("SoftwareInformation.Version"),
		Vorname:        viper.GetString("SoftwareInformation.Vorname"),
		Nachname:       viper.GetString("SoftwareInformation.Nachname"),
		Name:           viper.GetString("SoftwareInformation.Name"),
		Organisation:   viper.GetString("SoftwareInformation.Organisation"),
		Strasse:        viper.GetString("SoftwareInformation.Strasse"),
		Hausnummer:     viper.GetString("SoftwareInformation.Hausnummer"),
		Plz:            viper.GetString("SoftwareInformation.Plz"),
		Ort:            viper.GetString("SoftwareInformation.Ort"),
		Stadt:          viper.GetString("SoftwareInformation.Stadt"),
		Telefon:        viper.GetString("SoftwareInformation.Telefon"),
		Telefax:        viper.GetString("SoftwareInformation.Telefax"),
		TelefonMobil:   viper.GetString("SoftwareInformation.TelefonMobil"),
		SystemOid:      viper.GetString("SoftwareInformation.SystemOid"),
	}
}

func SetDefaultSoftwareInformation() {
	viper.SetDefault("SoftwareInformation.KbvPruefnummer", "X/101/0812/36/100")
	viper.SetDefault("SoftwareInformation.Version", "local")
	viper.SetDefault("SoftwareInformation.Vorname", "Thomas")
	viper.SetDefault("SoftwareInformation.Nachname", "")
	viper.SetDefault("SoftwareInformation.Name", "garrioPRO")
	viper.SetDefault("SoftwareInformation.Organisation", "garrio GmbH")
	viper.SetDefault("SoftwareInformation.Strasse", "Edmund-Rumpler-Str. 2")
	viper.SetDefault("SoftwareInformation.Hausnummer", "2")
	viper.SetDefault("SoftwareInformation.Plz", "51149")
	viper.SetDefault("SoftwareInformation.Ort", "Köln")
	viper.SetDefault("SoftwareInformation.Stadt", "Stuttgart")
	viper.SetDefault("SoftwareInformation.Telefon", "-")
	viper.SetDefault("SoftwareInformation.Telefax", "-")
	viper.SetDefault("SoftwareInformation.TelefonMobil", "")
	viper.SetDefault("SoftwareInformation.SystemOid", "14CEEFF4-E2E0-4990-82D0-7FFC42FC4BA6")

}
