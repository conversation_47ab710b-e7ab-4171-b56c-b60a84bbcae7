// This code was autogenerated from app/companion/companion_dm.proto, do not edit.

package dm_app

import (
	"encoding/json"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type GetFolderStateRequest struct {
	Folder     string         `json:"folder" validate:"required"`
	Host       *string        `json:"host"`
	Username   *string        `json:"username"`
	Password   *string        `json:"password"`
	SourceType SourceTypeEnum `json:"sourceType"`
	SettingId  uuid.UUID      `json:"settingId"`
	Recursive  bool           `json:"recursive"`
}

type GetFolderStateResponse struct {
	FolderState map[string]int64 `json:"folderState"`
}

type FileUploadInfo struct {
	FilePath     string `json:"filePath"`
	PresignedUrl string `json:"presignedUrl"`
}

type UploadFilesRequest struct {
	ListFileUploadInfo []FileUploadInfo `json:"listFileUploadInfo"`
	Folder             string           `json:"folder"`
	Host               *string          `json:"host"`
	Username           *string          `json:"username"`
	Password           *string          `json:"password"`
	SourceType         SourceTypeEnum   `json:"sourceType"`
	DeleteAfterUpload  bool             `json:"deleteAfterUpload"`
}

type WatchFolderRequest struct {
	Folder     string         `json:"folder" validate:"required"`
	Host       *string        `json:"host"`
	Username   *string        `json:"username"`
	Password   *string        `json:"password"`
	SourceType SourceTypeEnum `json:"sourceType"`
	SettingId  uuid.UUID      `json:"settingId"`
	Recursive  bool           `json:"recursive"`
}

type ExportGdtDocumentRequest struct {
	PresignUrl          string         `json:"presignUrl"`
	FileName            string         `json:"fileName"`
	Folder              string         `json:"folder"`
	Host                *string        `json:"host"`
	Username            *string        `json:"username"`
	Password            *string        `json:"password"`
	SourceType          SourceTypeEnum `json:"sourceType"`
	ExternalAppFilePath *string        `json:"externalAppFilePath"`
}

type CheckGdtImportDocumentRequest struct {
	Filename          string            `json:"filename" validate:"required"`
	Folder            string            `json:"folder" validate:"required"`
	Host              *string           `json:"host"`
	Username          *string           `json:"username"`
	Password          *string           `json:"password"`
	SourceType        SourceTypeEnum    `json:"sourceType"`
	LastModifyTime    int64             `json:"lastModifyTime"`
	SettingId         uuid.UUID         `json:"settingId"`
	CharacterEncoding CharacterEncoding `json:"characterEncoding" validate:"required"`
}

type RemoveGdtDocumentRequest struct {
	Filename    string         `json:"filename" validate:"required"`
	Folder      string         `json:"folder"`
	Host        *string        `json:"host"`
	Username    *string        `json:"username"`
	Password    *string        `json:"password"`
	SourceType  SourceTypeEnum `json:"sourceType"`
	PdfFileName *string        `json:"pdfFileName"`
}

type CheckGdtImportDocumentResponse struct {
	ModifyTime         int64  `json:"modifyTime"`
	IsChanged          bool   `json:"isChanged"`
	GdtDocumentContent string `json:"gdtDocumentContent"`
}

type StopWatchFolderRequest struct {
	SourceType SourceTypeEnum `json:"sourceType"`
	SettingId  uuid.UUID      `json:"settingId"`
}

type HandleDocumentRequest struct {
	Filename          string            `json:"filename"`
	Folder            string            `json:"folder" validate:"required"`
	Host              *string           `json:"host"`
	Username          *string           `json:"username"`
	Password          *string           `json:"password"`
	SourceType        SourceTypeEnum    `json:"sourceType"`
	LastModifyTime    int64             `json:"lastModifyTime"`
	SettingId         uuid.UUID         `json:"settingId"`
	CharacterEncoding CharacterEncoding `json:"characterEncoding" validate:"required"`
	FileExt           string            `json:"fileExt"`
}

type CheckExistedDocumentResponse struct {
	IsExisted bool `json:"isExisted"`
}

// enum definitions
type SourceTypeEnum string

const (
	SourceTypeEnum_LOCAL SourceTypeEnum = "local"
	SourceTypeEnum_FTP   SourceTypeEnum = "ftp"
	SourceTypeEnum_SMB   SourceTypeEnum = "smb"
)

type CharacterEncoding string

const (
	CharacterEncoding_IBM437  CharacterEncoding = "IBM437"
	CharacterEncoding_7Bit    CharacterEncoding = "7-bit"
	CharacterEncoding_ISO8859 CharacterEncoding = "ISO8859-1 ANSI CP 1252"
)

// Define constants
const NATS_SUBJECT = "api.app.companion" // nats subject this service will listen to

// service event constants
const EVENT_GetFolderState = "api.app.companion.DmApp.GetFolderState"
const LEGACY_TOPIC_GetFolderState = "/api/app/companion/dm/getFolderState"
const EVENT_UploadFiles = "api.app.companion.DmApp.UploadFiles"
const LEGACY_TOPIC_UploadFiles = "/api/app/companion/dm/uploadFiles"
const EVENT_WatchFolder = "api.app.companion.DmApp.WatchFolder"
const LEGACY_TOPIC_WatchFolder = "/api/app/companion/dm/watchFolder"
const EVENT_ExportGdtDocument = "api.app.companion.DmApp.ExportGdtDocument"
const LEGACY_TOPIC_ExportGdtDocument = "/api/app/companion/dm/exportGdtDocument"
const EVENT_CheckGdtImportDocument = "api.app.companion.DmApp.CheckGdtImportDocument"
const LEGACY_TOPIC_CheckGdtImportDocument = "/api/app/companion/dm/checkGdtImportDocument"
const EVENT_StopWatchFolder = "api.app.companion.DmApp.StopWatchFolder"
const LEGACY_TOPIC_StopWatchFolder = "/api/app/companion/dm/stopWatchFolder"
const EVENT_RemoveGdtDocument = "api.app.companion.DmApp.RemoveGdtDocument"
const LEGACY_TOPIC_RemoveGdtDocument = "/api/app/companion/dm/removeGdtDocument"
const EVENT_CheckExistedDocument = "api.app.companion.DmApp.CheckExistedDocument"
const LEGACY_TOPIC_CheckExistedDocument = "/api/app/companion/dm/checkExistedDocument"
const EVENT_HandleGdtImportDocument = "api.app.companion.DmApp.HandleGdtImportDocument"
const LEGACY_TOPIC_HandleGdtImportDocument = "/api/app/companion/dm/handleGdtImportDocument"

// message event constants

// Define service interface -------------------------------------------------------------
type DmApp interface {
	GetFolderState(ctx *titan.Context, request GetFolderStateRequest) (*GetFolderStateResponse, error)
	UploadFiles(ctx *titan.Context, request UploadFilesRequest) error
	WatchFolder(ctx *titan.Context, request WatchFolderRequest) error
	ExportGdtDocument(ctx *titan.Context, request ExportGdtDocumentRequest) error
	CheckGdtImportDocument(ctx *titan.Context, request CheckGdtImportDocumentRequest) (*CheckGdtImportDocumentResponse, error)
	StopWatchFolder(ctx *titan.Context, request StopWatchFolderRequest) error
	RemoveGdtDocument(ctx *titan.Context, request RemoveGdtDocumentRequest) error
	CheckExistedDocument(ctx *titan.Context, request HandleDocumentRequest) (*CheckExistedDocumentResponse, error)
	HandleGdtImportDocument(ctx *titan.Context, request HandleDocumentRequest) error
}

// Define service proxy -------------------------------------------------------------------
type DmAppProxy struct {
	service DmApp
}

func (srv *DmAppProxy) GetFolderState(ctx *titan.Context, request GetFolderStateRequest) (*GetFolderStateResponse, error) {
	return srv.service.GetFolderState(ctx, request)
}
func (srv *DmAppProxy) UploadFiles(ctx *titan.Context, request UploadFilesRequest) error {
	return srv.service.UploadFiles(ctx, request)
}
func (srv *DmAppProxy) WatchFolder(ctx *titan.Context, request WatchFolderRequest) error {
	return srv.service.WatchFolder(ctx, request)
}
func (srv *DmAppProxy) ExportGdtDocument(ctx *titan.Context, request ExportGdtDocumentRequest) error {
	return srv.service.ExportGdtDocument(ctx, request)
}
func (srv *DmAppProxy) CheckGdtImportDocument(ctx *titan.Context, request CheckGdtImportDocumentRequest) (*CheckGdtImportDocumentResponse, error) {
	return srv.service.CheckGdtImportDocument(ctx, request)
}
func (srv *DmAppProxy) StopWatchFolder(ctx *titan.Context, request StopWatchFolderRequest) error {
	return srv.service.StopWatchFolder(ctx, request)
}
func (srv *DmAppProxy) RemoveGdtDocument(ctx *titan.Context, request RemoveGdtDocumentRequest) error {
	return srv.service.RemoveGdtDocument(ctx, request)
}
func (srv *DmAppProxy) CheckExistedDocument(ctx *titan.Context, request HandleDocumentRequest) (*CheckExistedDocumentResponse, error) {
	return srv.service.CheckExistedDocument(ctx, request)
}
func (srv *DmAppProxy) HandleGdtImportDocument(ctx *titan.Context, request HandleDocumentRequest) error {
	return srv.service.HandleGdtImportDocument(ctx, request)
}

// Define service router -----------------------------------------------------------------
type DmAppRouter struct {
	proxy *DmAppProxy
}

func (router *DmAppRouter) Register(r titan.Router) {
	r.RegisterTopic(EVENT_GetFolderState, router.proxy.GetFolderState, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetFolderState, router.proxy.GetFolderState, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_UploadFiles, router.proxy.UploadFiles, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_UploadFiles, router.proxy.UploadFiles, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_WatchFolder, router.proxy.WatchFolder, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_WatchFolder, router.proxy.WatchFolder, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_ExportGdtDocument, router.proxy.ExportGdtDocument, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_ExportGdtDocument, router.proxy.ExportGdtDocument, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_CheckGdtImportDocument, router.proxy.CheckGdtImportDocument, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_CheckGdtImportDocument, router.proxy.CheckGdtImportDocument, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_StopWatchFolder, router.proxy.StopWatchFolder, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_StopWatchFolder, router.proxy.StopWatchFolder, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_RemoveGdtDocument, router.proxy.RemoveGdtDocument, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_RemoveGdtDocument, router.proxy.RemoveGdtDocument, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_CheckExistedDocument, router.proxy.CheckExistedDocument, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_CheckExistedDocument, router.proxy.CheckExistedDocument, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_HandleGdtImportDocument, router.proxy.HandleGdtImportDocument, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_HandleGdtImportDocument, router.proxy.HandleGdtImportDocument, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
}

// Subscriber
func (router *DmAppRouter) Subscribe(s *titan.MessageSubscriber) {
}

func NewDmAppRouter(s DmApp) *DmAppRouter {
	p := &DmAppProxy{s}
	return &DmAppRouter{p}
}

func NewServer(bff0 DmApp, options ...titan.Option) *titan.Server {
	opt := options

	router0 := NewDmAppRouter(bff0)
	opt = append(opt, titan.Routes(router0.Register), titan.Subscribe(router0.Subscribe))

	return titan.NewServer(NATS_SUBJECT, opt...)
}

// Define client ----------------------------------------------------------------------------------------------
type DmAppClient struct {
	client *titan.Client
}

func (srv *DmAppClient) GetFolderState(ctx *titan.Context, request GetFolderStateRequest) (*GetFolderStateResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetFolderState).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetFolderStateResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *DmAppClient) UploadFiles(ctx *titan.Context, request UploadFilesRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UploadFiles).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *DmAppClient) WatchFolder(ctx *titan.Context, request WatchFolderRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_WatchFolder).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *DmAppClient) ExportGdtDocument(ctx *titan.Context, request ExportGdtDocumentRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_ExportGdtDocument).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *DmAppClient) CheckGdtImportDocument(ctx *titan.Context, request CheckGdtImportDocumentRequest) (*CheckGdtImportDocumentResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CheckGdtImportDocument).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &CheckGdtImportDocumentResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *DmAppClient) StopWatchFolder(ctx *titan.Context, request StopWatchFolderRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_StopWatchFolder).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *DmAppClient) RemoveGdtDocument(ctx *titan.Context, request RemoveGdtDocumentRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_RemoveGdtDocument).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *DmAppClient) CheckExistedDocument(ctx *titan.Context, request HandleDocumentRequest) (*CheckExistedDocumentResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CheckExistedDocument).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &CheckExistedDocumentResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *DmAppClient) HandleGdtImportDocument(ctx *titan.Context, request HandleDocumentRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_HandleGdtImportDocument).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}

func NewDmAppClient(clients ...*titan.Client) *DmAppClient {
	var client *titan.Client
	if len(clients) <= 0 {
		client = titan.GetDefaultClient()
	} else {
		client = clients[0]
	}
	return &DmAppClient{client: client}
}

// Define event Notifier -----------------------------------------------------------------------------------------
type DmAppNotifier struct {
	client *titan.Client
}

func NewDmAppNotifier() *DmAppNotifier {
	client := titan.GetDefaultClient()
	return &DmAppNotifier{client}
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type DmAppSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewDmAppSocketNotifier(socket *socket_api.SocketServiceClient) *DmAppSocketNotifier {
	return &DmAppSocketNotifier{socket}
}

// -----------------------------------------------
// Test event listener
type DmAppEventListener struct {
	mux sync.Mutex
}

func (listener *DmAppEventListener) Reset() {
	listener.mux.Lock()
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *DmAppEventListener) Subscribe(s *titan.MessageSubscriber) {
}
