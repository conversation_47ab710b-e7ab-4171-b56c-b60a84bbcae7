// This code was autogenerated from app/profile/profile_app.proto, do not edit.

package profile_bff

import (
	"encoding/json"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	admin_bff "git.tutum.dev/medi/tutum/ares/app/admin/api/admin_bff"

	common "git.tutum.dev/medi/tutum/ares/service/domains/api/common"

	patient_profile_common "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"

	bsnr_common "git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_common"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type EmployeeProfileResponse struct {
	Id                     *uuid.UUID                             `json:"id"`
	FirstName              string                                 `json:"firstName"`
	LastName               string                                 `json:"lastName"`
	Title                  string                                 `json:"title"`
	Salutation             *patient_profile_common.Salutation     `json:"salutation"`
	Dob                    *int64                                 `json:"dob"`
	Phone                  *string                                `json:"phone"`
	Email                  *string                                `json:"email"`
	Address                *string                                `json:"address"`
	HasHzvContracts        bool                                   `json:"hasHzvContracts"`
	HasFavContracts        bool                                   `json:"hasFavContracts"`
	Bsnr                   string                                 `json:"bsnr"`
	HavgId                 string                                 `json:"havgId"`
	MediId                 string                                 `json:"mediId"`
	Lanr                   string                                 `json:"lanr"`
	AdditionalName         *patient_profile_common.AdditionalName `json:"additionalName"`
	IntendWord             *patient_profile_common.IntendWord     `json:"intendWord"`
	Initial                string                                 `json:"initial"`
	DmpPrograms            *[]string                              `json:"dmpPrograms"`
	MarkAsBillingDoctor    bool                                   `json:"markAsBillingDoctor"`
	PseudoLanr             *string                                `json:"pseudoLanr"`
	TeamNumbers            *[]string                              `json:"teamNumbers"`
	DoctorStamp            string                                 `json:"doctorStamp"`
	BsnrCity               string                                 `json:"bsnrCity"`
	BsnrPracticeStamp      string                                 `json:"bsnrPracticeStamp"`
	BankInformations       []*common.BankInformation              `json:"bankInformations"`
	MarkAsEmployedDoctor   bool                                   `json:"markAsEmployedDoctor"`
	ResponsibleDoctorId    *uuid.UUID                             `json:"responsibleDoctorId"`
	RepresentativeDoctorId *uuid.UUID                             `json:"representativeDoctorId"`
	BsnrName               *string                                `json:"bsnrName"`
	BsnrStreet             string                                 `json:"bsnrStreet"`
	BsnrNumber             string                                 `json:"bsnrNumber"`
	BsnrPostCode           string                                 `json:"bsnrPostCode"`
	BsnrPhoneNumber        string                                 `json:"bsnrPhoneNumber"`
	BsnrFaxNumber          string                                 `json:"bsnrFaxNumber"`
	AreaOfExpertise        *[]string                              `json:"areaOfExpertise"`
	OrgId                  string                                 `json:"orgId"`
	IsParticipationActive  bool                                   `json:"isParticipationActive"`
	Types                  []common.UserType                      `json:"types"`
	HavgVpId               string                                 `json:"havgVpId"`
	MediVpId               string                                 `json:"mediVpId"`
	BsnrFacilityType       string                                 `json:"bsnrFacilityType"`
	HpmEndpoint            string                                 `json:"hpmEndpoint"`
	HzvContracts           []*admin_bff.Contract                  `json:"hzvContracts"`
	FavContracts           []*admin_bff.Contract                  `json:"favContracts"`
	UserName               string                                 `json:"userName"`
	DeviceId               *uuid.UUID                             `json:"deviceId"`
	IsDoctor               bool                                   `json:"isDoctor"`
	JobDescription         string                                 `json:"jobDescription"`
	Okv                    string                                 `json:"okv"`
	BsnrIds                []uuid.UUID                            `json:"bsnrIds"`
	Bsnrs                  []string                               `json:"bsnrs"`
	BsnrId                 *uuid.UUID                             `json:"bsnrId"`
	EHKSType               *common.EHKSType                       `json:"eHKSType"`
}

type MyEmployeeProfileResponse struct {
	Id                     *uuid.UUID                             `json:"id"`
	FirstName              string                                 `json:"firstName"`
	LastName               string                                 `json:"lastName"`
	Title                  string                                 `json:"title"`
	Salutation             *patient_profile_common.Salutation     `json:"salutation"`
	Dob                    *int64                                 `json:"dob"`
	Phone                  *string                                `json:"phone"`
	Email                  *string                                `json:"email"`
	Address                *string                                `json:"address"`
	HasHzvContracts        bool                                   `json:"hasHzvContracts"`
	HasFavContracts        bool                                   `json:"hasFavContracts"`
	Bsnr                   string                                 `json:"bsnr"`
	HavgId                 string                                 `json:"havgId"`
	MediId                 string                                 `json:"mediId"`
	Lanr                   string                                 `json:"lanr"`
	HzvContracts           []*admin_bff.Contract                  `json:"hzvContracts"`
	FavContracts           []*admin_bff.Contract                  `json:"favContracts"`
	AdditionalName         *patient_profile_common.AdditionalName `json:"additionalName"`
	IntendWord             *patient_profile_common.IntendWord     `json:"intendWord"`
	Initial                string                                 `json:"initial"`
	DmpPrograms            *[]string                              `json:"dmpPrograms"`
	MarkAsBillingDoctor    bool                                   `json:"markAsBillingDoctor"`
	MarkAsEmployedDoctor   bool                                   `json:"markAsEmployedDoctor"`
	ResponsibleDoctorId    *uuid.UUID                             `json:"responsibleDoctorId"`
	RepresentativeDoctorId *uuid.UUID                             `json:"representativeDoctorId"`
	JobDescription         *string                                `json:"jobDescription"`
	BsnrName               *string                                `json:"bsnrName"`
	DoctorStamp            string                                 `json:"doctorStamp"`
	IsParticipationActive  bool                                   `json:"isParticipationActive"`
	Types                  []common.UserType                      `json:"types"`
	DeviceId               *uuid.UUID                             `json:"deviceId"`
	ExternalId             string                                 `json:"externalId"`
	UserName               string                                 `json:"userName"`
	OrgId                  string                                 `json:"orgId"`
	HpmEndpoint            string                                 `json:"hpmEndpoint"`
	BankInformations       []*common.BankInformation              `json:"bankInformations"`
	AreaOfExpertise        *[]string                              `json:"areaOfExpertise"`
	BsnrId                 *uuid.UUID                             `json:"bsnrId" bson:"omitempty"`
	Bsnrs                  []string                               `json:"bsnrs"`
	BsnrIds                []uuid.UUID                            `json:"bsnrIds"`
	EHKSType               *common.EHKSType                       `json:"eHKSType"`
	IsDoctor               bool                                   `json:"isDoctor"`
}

type Practice struct {
	Bsnr      string `json:"bsnr"`
	Name      string `json:"name"`
	EndDate   int64  `json:"endDate"`
	StartDate int64  `json:"startDate"`
}

type EmployeeProfilesResponse struct {
	Profiles []*EmployeeProfileResponse `json:"profiles"`
}

type GetByIdsRequest struct {
	OriginalIds []uuid.UUID `json:"originalIds" validate:"required"`
}

type GetByBsnrRequest struct {
	BsnrId *uuid.UUID `json:"bsnrId" validate:"required"`
}

type GetByLanrIDRequest struct {
	Lanr string `json:"lanr"`
}

type GetByHzvIDRequest struct {
	HavgId string `json:"havgId"`
}

type GetByMediIDRequest struct {
	MediId string `json:"mediId"`
}

type GetAllInitialResponse struct {
	Data []string `json:"data"`
}

type CareProvider struct {
	Id    *uuid.UUID          `json:"id"`
	Name  string              `json:"name"`
	Bsnrs []*bsnr_common.BSNR `json:"bsnrs"`
}

type GetListBsnrOfEmployeeResponse struct {
	CareProviders []*CareProvider `json:"careProviders"`
}

// enum definitions

// Define constants
const NATS_SUBJECT = "api.app.profile" // nats subject this service will listen to

// service event constants
const EVENT_GetAllInitial = "api.app.profile.ProfileBff.GetAllInitial"
const LEGACY_TOPIC_GetAllInitial = "/api/app/profile/getAllInitial"
const EVENT_GetMyEmployeeProfile = "api.app.profile.ProfileBff.GetMyEmployeeProfile"
const LEGACY_TOPIC_GetMyEmployeeProfile = "/api/app/profile/getMyEmployeeProfile"
const EVENT_GetEmployeeProfileByIds = "api.app.profile.ProfileBff.GetEmployeeProfileByIds"
const LEGACY_TOPIC_GetEmployeeProfileByIds = "/api/app/profile/getEmployeeProfileByIds"
const EVENT_GetEmployeeProfilesByBsnrId = "api.app.profile.ProfileBff.GetEmployeeProfilesByBsnrId"
const LEGACY_TOPIC_GetEmployeeProfilesByBsnrId = "/api/app/profile/getEmployeeProfilesByBsnrId"
const EVENT_GetByLanrID = "api.app.profile.ProfileBff.GetByLanrID"
const LEGACY_TOPIC_GetByLanrID = "/api/app/profile/getByLanrID"
const EVENT_GetByHzvID = "api.app.profile.ProfileBff.GetByHzvID"
const LEGACY_TOPIC_GetByHzvID = "/api/app/profile/getByHzvID"
const EVENT_GetByMediID = "api.app.profile.ProfileBff.GetByMediID"
const LEGACY_TOPIC_GetByMediID = "/api/app/profile/getByMediID"
const EVENT_GetEmployeeByIds = "api.app.profile.ProfileBff.GetEmployeeByIds"
const LEGACY_TOPIC_GetEmployeeByIds = "/api/app/profile/getEmployeeByIds"
const EVENT_GetListBsnrOfEmployee = "api.app.profile.ProfileBff.GetListBsnrOfEmployee"
const LEGACY_TOPIC_GetListBsnrOfEmployee = "/api/app/profile/getListBsnrOfEmployee"

// message event constants

// Define service interface -------------------------------------------------------------
type ProfileBff interface {
	GetAllInitial(ctx *titan.Context) (*GetAllInitialResponse, error)
	GetMyEmployeeProfile(ctx *titan.Context) (*MyEmployeeProfileResponse, error)
	GetEmployeeProfileByIds(ctx *titan.Context, request *GetByIdsRequest) (*EmployeeProfilesResponse, error)
	GetEmployeeProfilesByBsnrId(ctx *titan.Context, request *GetByBsnrRequest) (*EmployeeProfilesResponse, error)
	GetByLanrID(ctx *titan.Context, request *GetByLanrIDRequest) (*EmployeeProfileResponse, error)
	GetByHzvID(ctx *titan.Context, request *GetByHzvIDRequest) (*EmployeeProfileResponse, error)
	GetByMediID(ctx *titan.Context, request *GetByMediIDRequest) (*EmployeeProfileResponse, error)
	GetEmployeeByIds(ctx *titan.Context, request *GetByIdsRequest) (*EmployeeProfilesResponse, error)
	GetListBsnrOfEmployee(ctx *titan.Context) (*GetListBsnrOfEmployeeResponse, error)
}

// Define service proxy -------------------------------------------------------------------
type ProfileBffProxy struct {
	service ProfileBff
}

func (srv *ProfileBffProxy) GetAllInitial(ctx *titan.Context) (*GetAllInitialResponse, error) {
	return srv.service.GetAllInitial(ctx)
}
func (srv *ProfileBffProxy) GetMyEmployeeProfile(ctx *titan.Context) (*MyEmployeeProfileResponse, error) {
	return srv.service.GetMyEmployeeProfile(ctx)
}
func (srv *ProfileBffProxy) GetEmployeeProfileByIds(ctx *titan.Context, request *GetByIdsRequest) (*EmployeeProfilesResponse, error) {
	return srv.service.GetEmployeeProfileByIds(ctx, request)
}
func (srv *ProfileBffProxy) GetEmployeeProfilesByBsnrId(ctx *titan.Context, request *GetByBsnrRequest) (*EmployeeProfilesResponse, error) {
	return srv.service.GetEmployeeProfilesByBsnrId(ctx, request)
}
func (srv *ProfileBffProxy) GetByLanrID(ctx *titan.Context, request *GetByLanrIDRequest) (*EmployeeProfileResponse, error) {
	return srv.service.GetByLanrID(ctx, request)
}
func (srv *ProfileBffProxy) GetByHzvID(ctx *titan.Context, request *GetByHzvIDRequest) (*EmployeeProfileResponse, error) {
	return srv.service.GetByHzvID(ctx, request)
}
func (srv *ProfileBffProxy) GetByMediID(ctx *titan.Context, request *GetByMediIDRequest) (*EmployeeProfileResponse, error) {
	return srv.service.GetByMediID(ctx, request)
}
func (srv *ProfileBffProxy) GetEmployeeByIds(ctx *titan.Context, request *GetByIdsRequest) (*EmployeeProfilesResponse, error) {
	return srv.service.GetEmployeeByIds(ctx, request)
}
func (srv *ProfileBffProxy) GetListBsnrOfEmployee(ctx *titan.Context) (*GetListBsnrOfEmployeeResponse, error) {
	return srv.service.GetListBsnrOfEmployee(ctx)
}

// Define service router -----------------------------------------------------------------
type ProfileBffRouter struct {
	proxy *ProfileBffProxy
}

func (router *ProfileBffRouter) Register(r titan.Router) {
	r.RegisterTopic(EVENT_GetAllInitial, router.proxy.GetAllInitial, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(LEGACY_TOPIC_GetAllInitial, router.proxy.GetAllInitial, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(EVENT_GetMyEmployeeProfile, router.proxy.GetMyEmployeeProfile, titan.Secured(infra.CARE_PROVIDER_MEMBER_PRE_SWITCH, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(LEGACY_TOPIC_GetMyEmployeeProfile, router.proxy.GetMyEmployeeProfile, titan.Secured(infra.CARE_PROVIDER_MEMBER_PRE_SWITCH, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN))
	r.RegisterTopic(EVENT_GetEmployeeProfileByIds, router.proxy.GetEmployeeProfileByIds, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(LEGACY_TOPIC_GetEmployeeProfileByIds, router.proxy.GetEmployeeProfileByIds, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(EVENT_GetEmployeeProfilesByBsnrId, router.proxy.GetEmployeeProfilesByBsnrId, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(LEGACY_TOPIC_GetEmployeeProfilesByBsnrId, router.proxy.GetEmployeeProfilesByBsnrId, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(EVENT_GetByLanrID, router.proxy.GetByLanrID, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(LEGACY_TOPIC_GetByLanrID, router.proxy.GetByLanrID, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(EVENT_GetByHzvID, router.proxy.GetByHzvID, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(LEGACY_TOPIC_GetByHzvID, router.proxy.GetByHzvID, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(EVENT_GetByMediID, router.proxy.GetByMediID, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(LEGACY_TOPIC_GetByMediID, router.proxy.GetByMediID, titan.Secured(infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(EVENT_GetEmployeeByIds, router.proxy.GetEmployeeByIds, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(LEGACY_TOPIC_GetEmployeeByIds, router.proxy.GetEmployeeByIds, titan.Secured(infra.PATIENT, infra.CARE_PROVIDER_MEMBER, infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER_PRE_SWITCH))
	r.RegisterTopic(EVENT_GetListBsnrOfEmployee, router.proxy.GetListBsnrOfEmployee, titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetListBsnrOfEmployee, router.proxy.GetListBsnrOfEmployee, titan.Secured(infra.CARE_PROVIDER_MEMBER))
}

// Subscriber
func (router *ProfileBffRouter) Subscribe(s *titan.MessageSubscriber) {
}

func NewProfileBffRouter(s ProfileBff) *ProfileBffRouter {
	p := &ProfileBffProxy{s}
	return &ProfileBffRouter{p}
}

func NewServer(bff0 ProfileBff, options ...titan.Option) *titan.Server {
	opt := options

	router0 := NewProfileBffRouter(bff0)
	opt = append(opt, titan.Routes(router0.Register), titan.Subscribe(router0.Subscribe))

	return titan.NewServer(NATS_SUBJECT, opt...)
}

// Define client ----------------------------------------------------------------------------------------------
type ProfileBffClient struct {
	client *titan.Client
}

func (srv *ProfileBffClient) GetAllInitial(ctx *titan.Context) (*GetAllInitialResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetAllInitial).
		Subject(NATS_SUBJECT).
		Build()
	var resp = &GetAllInitialResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ProfileBffClient) GetMyEmployeeProfile(ctx *titan.Context) (*MyEmployeeProfileResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetMyEmployeeProfile).
		Subject(NATS_SUBJECT).
		Build()
	var resp = &MyEmployeeProfileResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ProfileBffClient) GetEmployeeProfileByIds(ctx *titan.Context, request *GetByIdsRequest) (*EmployeeProfilesResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetEmployeeProfileByIds).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfilesResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ProfileBffClient) GetEmployeeProfilesByBsnrId(ctx *titan.Context, request *GetByBsnrRequest) (*EmployeeProfilesResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetEmployeeProfilesByBsnrId).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfilesResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ProfileBffClient) GetByLanrID(ctx *titan.Context, request *GetByLanrIDRequest) (*EmployeeProfileResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetByLanrID).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfileResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ProfileBffClient) GetByHzvID(ctx *titan.Context, request *GetByHzvIDRequest) (*EmployeeProfileResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetByHzvID).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfileResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ProfileBffClient) GetByMediID(ctx *titan.Context, request *GetByMediIDRequest) (*EmployeeProfileResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetByMediID).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfileResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ProfileBffClient) GetEmployeeByIds(ctx *titan.Context, request *GetByIdsRequest) (*EmployeeProfilesResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetEmployeeByIds).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EmployeeProfilesResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ProfileBffClient) GetListBsnrOfEmployee(ctx *titan.Context) (*GetListBsnrOfEmployeeResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetListBsnrOfEmployee).
		Subject(NATS_SUBJECT).
		Build()
	var resp = &GetListBsnrOfEmployeeResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func NewProfileBffClient(clients ...*titan.Client) *ProfileBffClient {
	var client *titan.Client
	if len(clients) <= 0 {
		client = titan.GetDefaultClient()
	} else {
		client = clients[0]
	}
	return &ProfileBffClient{client: client}
}

// Define event Notifier -----------------------------------------------------------------------------------------
type ProfileBffNotifier struct {
	client *titan.Client
}

func NewProfileBffNotifier() *ProfileBffNotifier {
	client := titan.GetDefaultClient()
	return &ProfileBffNotifier{client}
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type ProfileBffSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewProfileBffSocketNotifier(socket *socket_api.SocketServiceClient) *ProfileBffSocketNotifier {
	return &ProfileBffSocketNotifier{socket}
}

// -----------------------------------------------
// Test event listener
type ProfileBffEventListener struct {
	mux sync.Mutex
}

func (listener *ProfileBffEventListener) Reset() {
	listener.mux.Lock()
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *ProfileBffEventListener) Subscribe(s *titan.MessageSubscriber) {
}
