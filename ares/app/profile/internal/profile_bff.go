package internal

import (
	"fmt"

	"emperror.dev/errors"
	profile_bff "git.tutum.dev/medi/tutum/ares/app/profile/api/profile_bff"
	bsnr_service "git.tutum.dev/medi/tutum/ares/service/bsnr"
	profile_service "git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	sysadmin_service "git.tutum.dev/medi/tutum/ares/service/domains/api/sysadmin"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_repo"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/jinzhu/copier"
	"gitlab.com/silenteer-oss/titan"
)

type ProfileBffImpl struct {
	employeeProfileService    profile_service.EmployeeProfileService
	bsnrService               bsnr_service.BSNRService
	doctorParticipateRepo     *DoctorParticipateRepository
	organizationTicketService sysadmin_service.OrganizationTicketService
}

func (p *ProfileBffImpl) GetAllInitial(ctx *titan.Context) (*profile_bff.GetAllInitialResponse, error) {
	initials, err := p.employeeProfileService.GetAllInitial(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return &profile_bff.GetAllInitialResponse{
		Data: initials.Data,
	}, nil
}

func (p ProfileBffImpl) GetMyEmployeeProfile(ctx *titan.Context) (*profile_bff.MyEmployeeProfileResponse, error) {
	profile, err := p.employeeProfileService.GetMyEmployeeProfile(ctx)
	if err != nil {
		return nil, errors.WithMessage(err, "GetMyEmployeeProfile.GetMyEmployeeProfile error")
	}
	if profile == nil || profile.Id == nil {
		return &profile_bff.MyEmployeeProfileResponse{}, nil
	}
	tmpProfile := &profile_bff.MyEmployeeProfileResponse{
		Id:                    profile.Id,
		LastName:              profile.LastName,
		FirstName:             profile.FirstName,
		Dob:                   profile.Dob,
		Salutation:            profile.Salutation,
		Phone:                 profile.Phone,
		Email:                 profile.Email,
		Address:               profile.Address,
		Title:                 util.GetPointerValue(profile.Title),
		HasHzvContracts:       profile.HasHzvContracts,
		HasFavContracts:       profile.HasFavContracts,
		AdditionalName:        profile.AdditionalName,
		AreaOfExpertise:       profile.AreaOfExpertise,
		IntendWord:            profile.IntendWord,
		DmpPrograms:           profile.DmpPrograms,
		Initial:               profile.Initial,
		JobDescription:        profile.JobDescription,
		DoctorStamp:           profile.DoctorStamp,
		MarkAsBillingDoctor:   profile.MarkAsBillingDoctor,
		MarkAsEmployedDoctor:  profile.MarkAsEmployedDoctor,
		IsParticipationActive: profile.IsParticipationActive,
		Types:                 profile.Types,
		DeviceId:              profile.DeviceId,
		ExternalId:            profile.ExternalId,
		UserName:              profile.UserName,
		OrgId:                 profile.OrgId,
		BankInformations:      profile.BankInformations,
		HpmEndpoint:           profile.HpmEndpoint,
		BsnrId:                profile.BsnrId,
		EHKSType:              profile.EHKSType,
		Bsnrs:                 profile.Bsnrs,
		BsnrIds:               profile.BsnrIds,
		IsDoctor:              profile.IsDoctor,
	}
	tmpProfile.Bsnr = profile.Bsnr
	tmpProfile.BsnrName = profile.BsnrName
	if profile.HavgId != nil {
		tmpProfile.HavgId = *profile.HavgId
	}
	if profile.MediverbundId != nil {
		tmpProfile.MediId = *profile.MediverbundId
	}
	if profile.Lanr != nil {
		tmpProfile.Lanr = *profile.Lanr
	}
	if profile.HasHzvContracts || profile.HasFavContracts {
		doctorContract, err := p.doctorParticipateRepo.GetByDoctorId(ctx, *profile.Id)
		if err != nil {
			return nil, errors.WithMessage(err, "GetMyEmployeeProfile.GetMyEmployeeProfile error when get doctor participation")
		}

		if doctorContract != nil {
			tmpProfile.HzvContracts = doctorContract.HzvContracts
			tmpProfile.FavContracts = doctorContract.FavContracts
		}
	}
	if tmpProfile.BsnrName == nil && tmpProfile.Bsnr != "" {
		res, errCode := p.bsnrService.FindByCode(ctx, profile.Bsnr)
		if errCode != nil {
			return nil, errors.WithMessage(err, "GetMyEmployeeProfile.GetMyEmployeeProfile error when get bsnr name")
		}
		if res != nil {
			tmpProfile.BsnrName = &res.Name
		} else {
			tmpProfile.BsnrName = &profile.Bsnr
		}
	}
	return tmpProfile, nil
}

func (p ProfileBffImpl) GetEmployeeProfileByIds(ctx *titan.Context, request *profile_bff.GetByIdsRequest) (*profile_bff.EmployeeProfilesResponse, error) {
	if len(request.OriginalIds) == 0 {
		return &profile_bff.EmployeeProfilesResponse{
			Profiles: []*profile_bff.EmployeeProfileResponse{},
		}, nil
	}
	r := &profile_service.GetByIdsRequest{OriginalIds: request.OriginalIds}
	response, err := p.employeeProfileService.GetEmployeeProfileByIds(ctx, r)
	if err != nil {
		return nil, errors.WithMessage(err, "GetMyEmployeeProfile.GetMyEmployeeProfile error")
	}

	if response == nil {
		return &profile_bff.EmployeeProfilesResponse{
			Profiles: []*profile_bff.EmployeeProfileResponse{},
		}, nil
	}

	profiles := make([]*profile_bff.EmployeeProfileResponse, 0)

	for _, p := range response.Profiles {
		profiles = append(profiles, mappingEmployee(p, ctx.UserInfo().GetOrgId()))
	}
	return &profile_bff.EmployeeProfilesResponse{Profiles: profiles}, nil
}

func (p ProfileBffImpl) GetByLanrID(ctx *titan.Context, request *profile_bff.GetByLanrIDRequest) (*profile_bff.EmployeeProfileResponse, error) {
	res, err := p.employeeProfileService.GetEmployeeProfileByLanrId(ctx, &profile_service.GetByLanrIDRequest{
		Lanr: request.Lanr,
	})
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("GetByLanrID.GetEmployeeProfileByLanrId error : %v", err.Error()))
	}
	if res == nil {
		return nil, nil
	}
	tmpProfile := &profile_bff.EmployeeProfileResponse{
		Id:              res.Id,
		LastName:        res.LastName,
		FirstName:       res.FirstName,
		Dob:             res.Dob,
		Salutation:      res.Salutation,
		Phone:           res.Phone,
		Email:           res.Email,
		Address:         res.Address,
		Title:           *res.Title,
		HasHzvContracts: res.HasHzvContracts,
		HasFavContracts: res.HasFavContracts,
		AdditionalName:  res.AdditionalName,
		IntendWord:      res.IntendWord,
		DmpPrograms:     res.DmpPrograms,
		Initial:         res.Initial,
	}
	tmpProfile.Bsnr = res.Bsnr
	return tmpProfile, nil
}

func (p ProfileBffImpl) GetByHzvID(ctx *titan.Context, request *profile_bff.GetByHzvIDRequest) (*profile_bff.EmployeeProfileResponse, error) {
	res, err := p.employeeProfileService.GetEmployeeProfileByHzvId(ctx, &profile_service.GetByHzvIDRequest{
		HavgId: request.HavgId,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "GetByHzvID.GetEmployeeProfileByHzvId error")
	}
	if res == nil {
		return nil, nil
	}
	return mappingEmployee(res, ctx.UserInfo().GetOrgId()), nil
}

func (p ProfileBffImpl) GetByMediID(ctx *titan.Context, request *profile_bff.GetByMediIDRequest) (*profile_bff.EmployeeProfileResponse, error) {
	res, err := p.employeeProfileService.GetEmployeeProfileByMediId(ctx, &profile_service.GetByMediIDRequest{
		MediId: request.MediId,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "GetByHzvID.GetEmployeeProfileByHzvId error")
	}
	if res == nil {
		return nil, nil
	}
	return mappingEmployee(res, ctx.UserInfo().GetOrgId()), nil
}

func (p ProfileBffImpl) GetEmployeeProfilesByBsnrId(ctx *titan.Context, request *profile_bff.GetByBsnrRequest) (*profile_bff.EmployeeProfilesResponse, error) {
	employees, err := p.employeeProfileService.GetEmployeeProfilesByBsnrId(ctx, &profile_service.GetByBsnrIdRequest{
		BsnrId: request.BsnrId,
	})
	if err != nil {
		return nil, err
	}
	if employees == nil {
		return &profile_bff.EmployeeProfilesResponse{}, nil
	}
	profiles := make([]*profile_bff.EmployeeProfileResponse, 0)
	for _, p := range employees.Profiles {
		profiles = append(profiles, mappingEmployee(p, ctx.UserInfo().GetOrgId()))
	}
	return &profile_bff.EmployeeProfilesResponse{Profiles: profiles}, nil
}

func (p ProfileBffImpl) GetEmployeeByIds(ctx *titan.Context, request *profile_bff.GetByIdsRequest) (*profile_bff.EmployeeProfilesResponse, error) {
	res, err := p.employeeProfileService.GetEmployeeByIds(ctx, &profile_service.GetByIdsRequest{
		OriginalIds: request.OriginalIds,
	})
	if err != nil {
		return nil, err
	}
	return &profile_bff.EmployeeProfilesResponse{Profiles: slice.Map(res.Profiles, func(p *profile_service.EmployeeProfileResponse) *profile_bff.EmployeeProfileResponse {
		return mappingEmployee(p, ctx.UserInfo().GetOrgId())
	})}, nil
}

func (p *ProfileBffImpl) GetListBsnrOfEmployee(ctx *titan.Context) (*profile_bff.GetListBsnrOfEmployeeResponse, error) {
	profile, err := p.employeeProfileService.GetMyEmployeeProfile(ctx)
	if err != nil {
		return nil, err
	}
	if profile == nil {
		return nil, fmt.Errorf("GetMyEmployeeProfile.GetMyEmployeeProfile error")
	}
	bsnrs, err := p.bsnrService.GetByIds(ctx, profile.BsnrIds)
	if err != nil {
		return nil, err
	}
	bsnrsMap := slice.Map(bsnrs, func(b bsnr_repo.BSNR) *bsnr_common.BSNR {
		result := &bsnr_common.BSNR{}
		err = copier.Copy(result, b)
		if err != nil {
			ctx.Logger().Error("Failed to copy bsnr", "err", err.Error())
			return nil
		}
		return result
	})
	cp, err := p.organizationTicketService.GetById(ctx, &sysadmin_service.GetByIdRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
	})
	if err != nil {
		return nil, err
	}
	if cp == nil {
		return nil, fmt.Errorf("GetMyEmployeeProfile.GetMyEmployeeProfile error")
	}

	return &profile_bff.GetListBsnrOfEmployeeResponse{
		CareProviders: []*profile_bff.CareProvider{
			{
				Id:    cp.CareProviderId,
				Name:  cp.CareProviderName,
				Bsnrs: bsnrsMap,
			},
		},
	}, nil
}

func InitProfileBff(
	employeeProfileService profile_service.EmployeeProfileService,
	bsnrClient bsnr_service.BSNRService,
	organizationTicketService sysadmin_service.OrganizationTicketService,

) profile_bff.ProfileBff {
	return &ProfileBffImpl{
		employeeProfileService:    employeeProfileService,
		bsnrService:               bsnrClient,
		doctorParticipateRepo:     InitDoctorParticipateRepository(),
		organizationTicketService: organizationTicketService,
	}
}
