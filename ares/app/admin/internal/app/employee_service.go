package app

import (
	"fmt"

	"emperror.dev/errors"
	employeebff "git.tutum.dev/medi/tutum/ares/app/admin/api/admin_bff"
	"git.tutum.dev/medi/tutum/ares/app/admin/api/bsnr"
	auth_app "git.tutum.dev/medi/tutum/ares/app/auth/api/auth_app"
	"git.tutum.dev/medi/tutum/ares/pkg/hook"
	hpm_rest_client "git.tutum.dev/medi/tutum/ares/pkg/hpm_rest/client"
	contract_service_resource "git.tutum.dev/medi/tutum/ares/service/contract/contract"
	account_service "git.tutum.dev/medi/tutum/ares/service/domains/api/account"
	admin_service_employee "git.tutum.dev/medi/tutum/ares/service/domains/api/admin_service"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	profile_service_employee "git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	okv_util "git.tutum.dev/medi/tutum/ares/service/domains/pkg/okv"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_common"
	employeeProfileRepo "git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"github.com/google/uuid"
	"github.com/samber/lo"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"

	cal_service "git.tutum.dev/medi/tutum/ares/service/cal"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdebm_common"
	mmi_service "git.tutum.dev/medi/tutum/ares/service/domains/mmi"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/sdebm/additional_field"
	pkg_util "git.tutum.dev/medi/tutum/pkg/util"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
)

type EmployeeServiceImpl struct {
	adminService           admin_service_employee.EmployeeAdminService
	accountService         account_service.AccountService
	employeeProfileService profile_service_employee.EmployeeProfileService
	authClient             auth_app.AuthService
	doctorParticipateRepo  DoctorParticipateRepository
	bsnrService            bsnr.BsnrServiceApp
	employeeRepo           employeeProfileRepo.EmployeeProfileDefaultRepository
	contractService        *contract_service_resource.Service
	adminBffNotifier       *employeebff.AdminBffNotifier
	adminBffSocketNotifier *employeebff.AdminBffSocketNotifier
	CALService             *cal_service.CALService
	hpmRestService         hpm_rest_client.ServiceRest
	mmiService             mmi_service.MMIService
}

func (e *EmployeeServiceImpl) UpdateDefaultBsnrOfEmployee(ctx *titan.Context, request *employeebff.UpdateDefaultBsnrOfEmployeeRequest) error {
	// update employee profile
	err := e.employeeProfileService.UpdateDefaultBsnrOfEmployee(ctx, request)
	if err != nil {
		return err
	}
	// update current context
	err = e.authClient.UpdateBsnrIdInContext(ctx, &auth_app.UpdateBsnrIdInContextRequest{
		BsnrId: request.BsnrId,
	})
	if err != nil {
		return err
	}

	return e.adminBffSocketNotifier.NotifyCareProviderHandleUpdateEmployee(ctx, &employeebff.EventHandleUpdateEmployee{Id: ctx.UserInfo().UserUUID()})
}

func (e *EmployeeServiceImpl) ResetEmployee2FA(ctx *titan.Context, request *employeebff.ResetEmployee2FARequest) error {
	err := e.employeeProfileService.ResetEmployee2FA(ctx, &profile_service_employee.ResetEmployee2FARequest{
		ExternalId: request.ExternalId,
	})
	if err != nil {
		return err
	}
	return e.authClient.ClearSessionByExternalId(ctx, &auth_app.ClearSessionByExternalIdRequest{
		ExternalUserId: request.ExternalId,
	})
}

func (e *EmployeeServiceImpl) ResetEmployeePassword(ctx *titan.Context, request *employeebff.ResetEmployeePasswordRequest) (*employeebff.ResetEmployeePasswordResponse, error) {
	res, err := e.employeeProfileService.ResetEmployeePassword(
		ctx,
		&profile_service_employee.ResetEmployeePasswordRequest{
			ExternalId: request.ExternalId,
		},
	)
	if err != nil {
		return nil, err
	}
	err = e.authClient.ClearSessionByExternalId(ctx, &auth_app.ClearSessionByExternalIdRequest{
		ExternalUserId: request.ExternalId,
	})
	if err != nil {
		return nil, err
	}
	return &employeebff.ResetEmployeePasswordResponse{
		Password: res.Password,
	}, nil
}

func (e *EmployeeServiceImpl) UpdateEmployeeStatus(ctx *titan.Context, request *employeebff.UpdateEmployeeStatusRequest) error {
	err := e.employeeProfileService.UpdateEmployeeStatus(ctx, &profile_service_employee.UpdateEmployeeStatusRequest{
		ExternalId: request.ExternalId,
		Status:     request.Status,
	})
	if err != nil {
		return err
	}

	go func() {
		cloneCtx := ctx.Clone()
		if err := e.CALService.UpdateStaffStatus(cloneCtx, cal_service.UpdateStaffStatusRequest{
			Id:             request.UserId.String(),
			CareProviderId: cloneCtx.UserInfo().CareProviderUUID().String(),
			Status:         function.If(request.Status == common.EmployeeStatus_ACTIVE, cal_service.STAFF_STATUS_ACTIVE, cal_service.STAFF_STATUS_DEACTIVATE),
		}); err != nil {
			cloneCtx.Logger().Error("Fail to update status of staff in CAL", "error", err)
		}
	}()
	return e.authClient.ClearSessionByExternalId(ctx, &auth_app.ClearSessionByExternalIdRequest{
		ExternalUserId: request.ExternalId,
	})
}

func (e *EmployeeServiceImpl) GetEmployeeByDeviceId(ctx *titan.Context, request *employeebff.DeviceIdRequest) (*employeebff.EmployeeDetailsResponse, error) {
	res, err := e.employeeProfileService.GetEmployeeByDeviceId(ctx, &profile_service_employee.GetEmployeeByDeviceIdRequest{
		DeviceId: request.DeviceId,
	})
	if err != nil {
		return nil, err
	}
	employees := make([]*employeebff.EmployeeDetailResponse, len(res.Profiles))

	for i, profile := range res.Profiles {
		tmp := employeebff.EmployeeDetailResponse{}
		fillEmployeeProfileToEmployeeDetailResponse(&tmp, profile)
		employees[i] = &tmp
	}
	return &employeebff.EmployeeDetailsResponse{
		Employees:  employees,
		TotalCount: int64(len(employees)),
	}, nil
}

func (e *EmployeeServiceImpl) UpdateDeviceOfEmployee(ctx *titan.Context, request *employeebff.DeviceIdRequest) error {
	err := e.employeeProfileService.UpdateDevice(ctx, &profile_service_employee.UpdateDeviceRequest{
		DeviceId: request.DeviceId,
	})
	if err != nil {
		return errors.WithMessagef(err, "failed to update device of employee")
	}
	// Update deviceId in redis so user can get latest device
	return e.authClient.UpdateDeviceIdInContext(ctx, &auth_app.UpdateDeviceIdInContextRequest{
		DeviceId: request.DeviceId,
	})
}

const EmployeeClientTag = "EmployeeClient"

func (e *EmployeeServiceImpl) GetMyEmployeeInfo(ctx *titan.Context) (*employeebff.EmployeeResponse, error) {
	userInfo := ctx.UserInfo()
	if userInfo == nil || userInfo.UserId == "" {
		return &employeebff.EmployeeResponse{}, nil
	}
	id, err := uuid.Parse(userInfo.UserId.String())
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.GetMyEmployeeInfo", EmployeeClientTag))
	}
	return e.getById(ctx, &id)
}

func (e *EmployeeServiceImpl) GetById(ctx *titan.Context, request *employeebff.EmployeeGetByIdRequest) (*employeebff.EmployeeResponse, error) {
	return e.getById(ctx, request.Id)
}

func (e *EmployeeServiceImpl) getById(ctx *titan.Context, id *uuid.UUID) (*employeebff.EmployeeResponse, error) {
	result, err := e.adminService.GetById(ctx, &admin_service_employee.GetByIdRequest{Id: id})
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.GetById", EmployeeClientTag))
	}
	return &employeebff.EmployeeResponse{
		Id:             result.Id,
		AccountId:      result.AccountId,
		CareProviderId: result.CareProviderId,
		Types:          result.Types,
	}, nil
}

func (e *EmployeeServiceImpl) GetByType(ctx *titan.Context,
	request *employeebff.EmployeeGetByTypeRequest,
) (*employeebff.EmployeesResponse, error) {
	careProviderID, err := uuid.Parse(ctx.UserInfo().CareProviderId.String())
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.GetByType", EmployeeClientTag))
	}

	getByTypeRe := &admin_service_employee.GetByTypeRequest{
		CareProviderId: &careProviderID,
		Type:           request.Type,
	}
	result, err := e.adminService.GetByType(ctx, getByTypeRe)
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.GetByType", EmployeeClientTag))
	}
	return toEmployeesResponse(result.Employees), nil
}

func toEmployeesResponse(employees []*admin_service_employee.EmployeeResponse) *employeebff.EmployeesResponse {
	emps := make([]*employeebff.EmployeeResponse, 0)
	for _, emp := range employees {
		emps = append(emps, toEmployeeResponse(emp))
	}
	return &employeebff.EmployeesResponse{Employees: emps}
}

func toEmployeeResponse(emp *admin_service_employee.EmployeeResponse) *employeebff.EmployeeResponse {
	return &employeebff.EmployeeResponse{
		Id:             emp.Id,
		AccountId:      emp.AccountId,
		CareProviderId: emp.CareProviderId,
		Types:          emp.Types,
	}
}

func fillEmployeeProfileToEmployeeDetailResponse(employeeDetail *employeebff.EmployeeDetailResponse, empProfile *profile_service_employee.EmployeeProfileResponse) {
	employeeDetail.LastName = empProfile.LastName
	employeeDetail.FirstName = empProfile.FirstName
	employeeDetail.Dob = empProfile.Dob
	employeeDetail.Salutation = empProfile.Salutation
	employeeDetail.Phone = empProfile.Phone
	employeeDetail.Email = empProfile.Email
	employeeDetail.Address = empProfile.Address
	employeeDetail.Title = empProfile.Title
	employeeDetail.HasHzvContracts = empProfile.HasHzvContracts
	employeeDetail.HasFavContracts = empProfile.HasFavContracts
	employeeDetail.Lanr = empProfile.Lanr
	employeeDetail.Bsnr = empProfile.Bsnr
	employeeDetail.MediverbundId = empProfile.MediverbundId
	employeeDetail.MediverbundVpId = empProfile.MediverbundVpId
	employeeDetail.HavgId = empProfile.HavgId
	employeeDetail.HavgVpId = empProfile.HavgVpId
	employeeDetail.Okv = empProfile.Okv
	employeeDetail.AreaOfExpertise = empProfile.AreaOfExpertise
	employeeDetail.Initial = empProfile.Initial
	employeeDetail.AdditionalName = empProfile.AdditionalName
	employeeDetail.IntendWord = empProfile.IntendWord
	employeeDetail.DmpPrograms = empProfile.DmpPrograms
	employeeDetail.EmployeeProfileId = empProfile.Id
	if empProfile.Lanr != nil {
		employeeDetail.IsDoctor = true
	} else {
		employeeDetail.IsDoctor = false
	}
	employeeDetail.JobDescription = empProfile.JobDescription
	employeeDetail.MarkAsBillingDoctor = empProfile.MarkAsBillingDoctor
	employeeDetail.BsnrId = empProfile.BsnrId
	employeeDetail.PseudoLanr = empProfile.PseudoLanr
	employeeDetail.TeamNumbers = empProfile.TeamNumbers
	employeeDetail.DoctorStamp = empProfile.DoctorStamp
	employeeDetail.BankInformations = empProfile.BankInformations
	employeeDetail.MarkAsEmployedDoctor = empProfile.MarkAsEmployedDoctor
	employeeDetail.ResponsibleDoctorId = empProfile.ResponsibleDoctorId
	employeeDetail.RepresentativeDoctorId = empProfile.RepresentativeDoctorId
	employeeDetail.BsnrName = empProfile.BsnrName
	employeeDetail.IsParticipationActive = empProfile.IsParticipationActive
	employeeDetail.Types = empProfile.Types
	employeeDetail.DeviceId = empProfile.DeviceId
	employeeDetail.UserName = empProfile.UserName
	employeeDetail.HpmEndpoint = empProfile.HpmEndpoint
	employeeDetail.CreatedDate = empProfile.CreatedDate
	employeeDetail.Status = empProfile.Status
	employeeDetail.ExternalId = empProfile.ExternalId
	employeeDetail.Bsnrs = empProfile.Bsnrs
	employeeDetail.BsnrIds = empProfile.BsnrIds
	employeeDetail.EHKSType = empProfile.EHKSType
}

func (e *EmployeeServiceImpl) GetAll(ctx *titan.Context,
	request *common.Pagination,
) (*employeebff.EmployeesResponse, error) {
	careProviderID, err := uuid.Parse(ctx.UserInfo().CareProviderId.String())
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.GetAll", EmployeeClientTag))
	}
	r := &admin_service_employee.GetByCareProviderRequest{
		CareProviderId: &careProviderID,
		Pagination:     request,
	}
	result, err := e.adminService.GetByCareProviderId(ctx, r)
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.GetAll.GetByCareProviderId", EmployeeClientTag))
	}

	return toEmployeesResponse(result.Employees), nil
}

func (e EmployeeServiceImpl) CreateEmployee(ctx *titan.Context,
	request *employeebff.UpsertEmployeeRequest,
) (*employeebff.EmployeeResponse, error) {
	if request.Lanr != nil && request.IsDoctor {
		isValid, err := additional_field.NewRule050F(catalog_sdebm_common.RuleInfo{
			CurrentField: catalog_sdebm_common.FieldValue{FK: "5099", Value: *request.Lanr},
		}).IsValid()
		if !isValid {
			return nil, errors.WithMessage(err, fmt.Sprintf("%s.CreateEmployee.accountService", EmployeeClientTag))
		}

		// NOTE: check duplicated lanr
		existedDoctorWithLanr, err := e.employeeProfileService.GetEmployeeProfileByLanrId(ctx, &profile_service_employee.GetByLanrIDRequest{Lanr: *request.Lanr})
		if err != nil {
			return nil, errors.WithMessage(err, fmt.Sprintf("%s.CreateEmployee.accountService", EmployeeClientTag))
		}
		if existedDoctorWithLanr != nil {
			return nil, errors.WithMessage(errors.New("creating a doctor with a duplicated lanr"), fmt.Sprintf("%s.CreateEmployee.accountService", EmployeeClientTag))
		}
	}

	mapContract := []*admin_service_employee.Contract{}
	if request.Contracts != nil {
		for _, v := range *request.Contracts {
			mapContract = append(mapContract, &admin_service_employee.Contract{
				ContractId:            v.ContractId,
				ChargeSystemId:        v.ChargeSystemId,
				StartDate:             v.StartDate,
				Type:                  v.Type,
				EndDate:               v.EndDate,
				EnrollmentType:        v.EnrollmentType,
				EnrollmentTypeOptions: v.EnrollmentTypeOptions,
			})
		}
	}

	hasFavContract := request.HasFavContracts
	for _, bsnr := range request.Bsnrs {
		okv, err := okv_util.GetOkvByBsnr(bsnr)
		// TODO: recheck this logic, will never pass this if because GetOkvByBsnr only return value with nil error
		if err != nil && okv == "52" {
			// Add related FAV contracts to doctor with HZV contracts in OKV52
			hzvContracts := slice.Filter(mapContract, func(contract *admin_service_employee.Contract) bool {
				return contract.Type == common.ContractType_HouseDoctorCare
			})
			hzvContractIds := slice.Map(hzvContracts, func(contract *admin_service_employee.Contract) string {
				return contract.ContractId
			})
			contractDetails := e.contractService.GetContractByIds(hzvContractIds)
			for _, contract := range contractDetails {
				requestContract := slice.FindOne(hzvContracts, func(hzvContract *admin_service_employee.Contract) bool {
					return hzvContract.ContractId == contract.Id
				})
				if requestContract == nil {
					continue
				}
				relatedFAVContracts := contract.ContractDefinition.VertragsuebergreifendePatiententeilnahmeanfrage.PatiententeilnahmeanfrageVertrag
				for _, favContract := range relatedFAVContracts {
					if existed := slice.FindOne(mapContract, func(contract *admin_service_employee.Contract) bool {
						return contract.ContractId == favContract.ID
					}); existed != nil {
						continue
					}
					mapContract = append(mapContract, &admin_service_employee.Contract{
						ContractId:            favContract.ID,
						ChargeSystemId:        (*requestContract).ChargeSystemId,
						StartDate:             (*requestContract).StartDate,
						Type:                  common.ContractType_SpecialistCare,
						EndDate:               (*requestContract).EndDate,
						EnrollmentType:        (*requestContract).EnrollmentType,
						EnrollmentTypeOptions: (*requestContract).EnrollmentTypeOptions,
					})
					hasFavContract = true
				}
			}
		}
	}
	employeeId := uuid.New()
	createEmployeeRes, err := e.employeeProfileService.CreateEmployeeProfile(ctx, &profile_service_employee.EmployeeProfileRequest{
		OriginalId:             &employeeId,
		LastName:               request.LastName,
		FirstName:              request.FirstName,
		Dob:                    request.Dob,
		Salutation:             request.Salutation,
		Email:                  request.Email,
		Phone:                  request.Phone,
		Address:                request.Address,
		MediverbundId:          request.MediverbundId,
		MediverbundVpId:        request.MediverbundVpId,
		HavgId:                 request.HavgId,
		HavgVpId:               request.HavgVpId,
		Title:                  request.Title,
		AreaOfExpertise:        request.AreaOfExpertise,
		Okv:                    request.Okv,
		Lanr:                   request.Lanr,
		Bsnr:                   request.Bsnr,
		HasFavContracts:        hasFavContract,
		HasHzvContracts:        request.HasHzvContracts,
		AdditionalName:         request.AdditionalName,
		IntendWord:             request.IntendWord,
		Initial:                request.Initial,
		DmpPrograms:            request.DmpPrograms,
		JobDescription:         request.JobDescription,
		MarkAsBillingDoctor:    request.MarkAsBillingDoctor,
		BsnrId:                 request.BsnrId,
		DoctorStamp:            request.DoctorStamp,
		PseudoLanr:             request.PseudoLanr,
		TeamNumbers:            request.TeamNumbers,
		BankInformations:       request.BankInformations,
		MarkAsEmployedDoctor:   request.MarkAsEmployedDoctor,
		ResponsibleDoctorId:    request.ResponsibleDoctorId,
		RepresentativeDoctorId: request.RepresentativeDoctorId,
		BsnrName:               request.BsnrName,
		Password:               request.Password,
		IsParticipationActive:  pkg_util.GetPointerValue(request.IsParticipationActive),
		Types:                  request.Types,
		DeviceId:               request.DeviceId,
		UserName:               request.UserName,
		HpmEndpoint:            request.HpmEndpoint,
		HzvContracts:           request.HzvContracts,
		FavContracts:           request.FavContracts,
		Bsnrs:                  request.Bsnrs,
		BsnrIds:                request.BsnrIds,
		EHKSType:               request.EHKSType,
		IsDoctor:               request.IsDoctor,
	})
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.CreateEmployee.CreateEmployeeProfile", EmployeeClientTag))
	}

	err = e.mmiService.CreateHouseDoctorList(ctx, *createEmployeeRes.Id)
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.CreateEmployee.CreateHouseDoctorList", EmployeeClientTag))
	}

	empResult, err := e.adminService.CreateEmployee(ctx, &admin_service_employee.CreateEmployeeRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		AccountId:      &employeeId,
		Types:          request.Types,
		IsDoctor:       request.IsDoctor,
		Contracts:      &mapContract,
	})
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.CreateEmployee.adminService", EmployeeClientTag))
	}

	// Only create MFA and Doctor in CAL
	allowedStaffTypes := []common.UserType{common.DOCTOR, common.MFA}
	var role common.UserType
	if len(request.Types) > 0 {
		role = request.Types[0]
	}
	if slice.Contains(allowedStaffTypes, role) {
		go func() {
			ctxClone := ctx.Clone()
			err = e.CALService.CreateStaffCAL(ctxClone, cal_service.CreateOrUpdateStaffRequest{
				Id:             createEmployeeRes.Id,
				CareProviderId: ctx.UserInfo().CareProviderId.String(),
				FirstName:      createEmployeeRes.FirstName,
				LastName:       createEmployeeRes.LastName,
				Role:           pkg_util.NewPointer(string(role)),
				PracticeIds: slice.Map(createEmployeeRes.BsnrIds, func(id uuid.UUID) string {
					return id.String()
				}),
				Initial:         createEmployeeRes.Initial,
				AreaOfExpertise: pkg_util.GetPointerValue(createEmployeeRes.AreaOfExpertise), // list code of specialties
				PortalOrgId:     ctx.UserInfo().GetOrgId(),
				PortalUserId:    createEmployeeRes.ExternalId,
				Salutation:      string(pkg_util.GetPointerValue(createEmployeeRes.Salutation)),
				AdditionalName:  string(pkg_util.GetPointerValue(createEmployeeRes.AdditionalName)),
				Title:           pkg_util.GetPointerValue(createEmployeeRes.Title),
				IntendWord:      string(pkg_util.GetPointerValue(createEmployeeRes.IntendWord)),
				Lanr:            pkg_util.GetPointerValue(createEmployeeRes.Lanr),
			})
			if err != nil {
				ctxClone.Logger().Error("Fail to create staff in CAL", "error", err)
			}
		}()
	}

	return toEmployeeResponse(empResult), nil
}

func (e *EmployeeServiceImpl) UpdateEmployee(ctx *titan.Context,
	request *employeebff.UpdateEmployeeRequest,
) (*employeebff.EmployeeResponse, error) {
	mapContract := []*admin_service_employee.UpdateDoctorParticipate{}
	hzvContracts := []*employeebff.Contract{}
	favContracts := []*employeebff.Contract{}
	if request.Contracts == nil || len(*request.Contracts) == 0 {
		mapContract = nil
	} else {
		for _, v := range *request.Contracts {
			contract := &admin_service_employee.UpdateDoctorParticipate{
				Id:                    v.Id,
				ContractId:            v.ContractId,
				ChargeSystemId:        v.ChargeSystemId,
				StartDate:             &v.StartDate,
				Type:                  v.Type,
				EndDate:               v.EndDate,
				EnrollmentType:        v.EnrollmentType,
				EnrollmentTypeOptions: v.EnrollmentTypeOptions,
			}
			isHzVParticipation := request.HasHzvContracts && v.Type == common.ContractType_HouseDoctorCare
			isFavParticipation := request.HasFavContracts && v.Type == common.ContractType_SpecialistCare
			if isHzVParticipation || isFavParticipation {
				mapContract = append(mapContract, contract)
			}
			if isHzVParticipation {
				hzvContracts = append(hzvContracts, v)
			}
			if isFavParticipation {
				favContracts = append(favContracts, v)
			}
		}
		request.HzvContracts = hzvContracts
		request.FavContracts = favContracts
	}
	if request.IsDoctor && mapContract != nil {
		_, err := e.adminService.UpdateContractEmployee(ctx, &admin_service_employee.UpdateDoctorParticipateRequest{
			DoctorId:  request.Id,
			Contracts: mapContract,
		})
		if err != nil {
			return nil, errors.WithMessage(err, fmt.Sprintf("%s.CreateEmployee.adminService", EmployeeClientTag))
		}
	}

	if !request.HasHzvContracts {
		request.HzvContracts = nil
		request.HavgId = nil
		request.HavgVpId = nil
	}

	if !request.HasFavContracts {
		request.FavContracts = nil
		request.MediverbundId = nil
		request.MediverbundVpId = nil
	}

	updateRes, err := e.employeeProfileService.UpdateEmployeeProfile(ctx, &profile_service_employee.EmployeeProfileRequest{
		OriginalId:             request.Id,
		LastName:               request.LastName,
		FirstName:              request.FirstName,
		Dob:                    request.Dob,
		Salutation:             request.Salutation,
		Email:                  request.Email,
		Phone:                  request.Phone,
		Address:                request.Address,
		MediverbundId:          request.MediverbundId,
		MediverbundVpId:        request.MediverbundVpId,
		HavgId:                 request.HavgId,
		HavgVpId:               request.HavgVpId,
		Title:                  request.Title,
		AreaOfExpertise:        request.AreaOfExpertise,
		Okv:                    request.Okv,
		Lanr:                   request.Lanr, // edit for case changing BSNR
		Bsnr:                   request.Bsnr,
		HasFavContracts:        request.HasFavContracts,
		HasHzvContracts:        request.HasHzvContracts,
		AdditionalName:         request.AdditionalName,
		IntendWord:             request.IntendWord,
		Initial:                request.Initial,
		DmpPrograms:            request.DmpPrograms,
		JobDescription:         request.JobDescription,
		MarkAsBillingDoctor:    request.MarkAsBillingDoctor,
		BsnrId:                 request.BsnrId,
		PseudoLanr:             request.PseudoLanr,
		TeamNumbers:            request.TeamNumbers,
		DoctorStamp:            request.DoctorStamp,
		BankInformations:       request.BankInformations,
		MarkAsEmployedDoctor:   request.MarkAsEmployedDoctor,
		ResponsibleDoctorId:    request.ResponsibleDoctorId,
		RepresentativeDoctorId: request.RepresentativeDoctorId,
		BsnrName:               request.BsnrName,
		IsParticipationActive:  request.IsParticipationActive,
		Types:                  request.Types,
		DeviceId:               request.DeviceId,
		UserName:               request.UserName,
		HpmEndpoint:            request.HpmEndpoint,
		HzvContracts:           request.HzvContracts,
		FavContracts:           request.FavContracts,
		Bsnrs:                  request.Bsnrs,
		BsnrIds:                request.BsnrIds,
		EHKSType:               request.EHKSType,
		IsDoctor:               request.IsDoctor,
	})
	ok, err := titan.IsError(err, string(error_code.ErrorCode_Zitadel_Resource_Not_Changed))
	if !ok && err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.UpdateEmployee.UpdateEmployeeProfile", EmployeeClientTag))
	}

	if updateRes == nil {
		return nil, nil
	}

	// TODO: wait to confirm flow with migration
	// err = e.mmiService.CreateHouseDoctorList(ctx, *updateRes.Id)
	// if err != nil {
	// 	return nil, errors.WithMessage(err, fmt.Sprintf("%s.CreateEmployee.CreateHouseDoctorList", EmployeeClientTag))
	// }

	updateEmployeeNotifyReq := &employeebff.EventHandleUpdateEmployee{
		Id:   request.Id,
		Lanr: pkg_util.GetPointerValue(updateRes.Lanr),
	}
	if err := e.adminBffNotifier.NotifyHandleUpdateEmployee(ctx, updateEmployeeNotifyReq); err != nil {
		ctx.Logger().Error("NotifyHandleUpdateEmployee", "error", err)
	}
	if err := e.adminBffSocketNotifier.NotifyCareProviderHandleUpdateEmployee(ctx, updateEmployeeNotifyReq); err != nil {
		ctx.Logger().Error("NotifyCareProviderHandleUpdateEmployee", "error", err)
	}

	go func() {
		ctxClone := ctx.Clone()
		var role string
		if len(request.Types) > 0 {
			role = string(request.Types[0])
		}
		err = e.CALService.UpdateStaffCAL(ctxClone, cal_service.CreateOrUpdateStaffRequest{
			Id:             request.Id,
			FirstName:      request.FirstName,
			LastName:       request.LastName,
			CareProviderId: ctx.UserInfo().CareProviderId.String(),
			Role:           &role,
			PracticeIds: slice.Map(request.BsnrIds, func(id uuid.UUID) string {
				return id.String()
			}),
			Initial:         request.Initial,
			AreaOfExpertise: pkg_util.GetPointerValue(request.AreaOfExpertise),
			PortalOrgId:     ctx.UserInfo().GetOrgId(),
			PortalUserId:    updateRes.ExternalId,
			Salutation:      string(pkg_util.GetPointerValue(request.Salutation)),
			AdditionalName:  string(pkg_util.GetPointerValue(request.AdditionalName)),
			Title:           pkg_util.GetPointerValue(request.Title),
			IntendWord:      string(pkg_util.GetPointerValue(request.IntendWord)),
			Lanr:            pkg_util.GetPointerValue(request.Lanr),
		})
		if err != nil {
			ctxClone.Logger().Error("Fail to update staff in CAL", "error", err)
		}
	}()

	return &employeebff.EmployeeResponse{
		Id:             request.Id,
		AccountId:      nil,
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		Types:          request.Types,
	}, nil
}

func (e *EmployeeServiceImpl) DeleteById(ctx *titan.Context,
	request *employeebff.EmployeeDeleteByIdRequest,
) (*employeebff.EmployeeDeleteByIdResponse, error) {
	employee, err := e.adminService.GetById(ctx, &admin_service_employee.GetByIdRequest{Id: request.Id})
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.adminService.DeleteById.GetById", EmployeeClientTag))
	}
	if _, err = e.adminService.DeleteById(ctx, &admin_service_employee.EmployeeDeleteByIdRequest{Id: employee.Id}); err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.adminService.DeleteById", EmployeeClientTag))
	}
	if _, err = e.accountService.DeleteById(ctx, &account_service.DeleteByIdRequest{Id: employee.AccountId}); err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.accountService.DeleteById", EmployeeClientTag))
	}

	delProfileRe := &profile_service_employee.EmployeeProfileDeleteRequest{OriginalId: employee.Id}

	if _, err = e.employeeProfileService.DeleteEmployeeProfileById(ctx, delProfileRe); err != nil {
		return nil, err
	}
	return &employeebff.EmployeeDeleteByIdResponse{Id: employee.Id}, nil
}

func (e *EmployeeServiceImpl) ChangePassword(ctx *titan.Context,
	request *employeebff.EmployeeChangePasswordRequest,
) (*employeebff.EmployeeChangePasswordResponse, error) {
	employee, err := e.adminService.GetById(ctx, &admin_service_employee.GetByIdRequest{Id: ctx.UserInfo().UserUUID()})
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.adminService.GetById", EmployeeClientTag))
	}

	chRes := &account_service.ChangePasswordRequest{
		Id:              employee.AccountId,
		CurrentPassword: request.CurrentPassword,
		NewPassword:     request.NewPassword,
	}

	if _, err = e.accountService.ChangePassword(ctx, chRes); err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.accountService.ChangePassword", EmployeeClientTag))
	}

	return &employeebff.EmployeeChangePasswordResponse{}, err
}

func (e *EmployeeServiceImpl) ResetPassword(ctx *titan.Context,
	request *employeebff.EmployeeResetPasswordRequest,
) (*employeebff.EmployeeResetPasswordResponse, error) {
	employee, err := e.adminService.GetById(ctx, &admin_service_employee.GetByIdRequest{Id: request.EmployeeId})
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.adminService.GetById", EmployeeClientTag))
	}
	reRequest := &account_service.ResetPasswordRequest{
		Id:          employee.AccountId,
		NewPassword: request.NewPassword,
	}
	result, err := e.accountService.ResetPassword(ctx, reRequest)
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.adminService.GetById", EmployeeClientTag))
	}
	if result == nil {
		return &employeebff.EmployeeResetPasswordResponse{Success: false}, nil
	}
	return &employeebff.EmployeeResetPasswordResponse{Success: true}, nil
}

func (e *EmployeeServiceImpl) TotalEmployee(ctx *titan.Context) (*employeebff.TotalEmployeeResponse, error) {
	result, err := e.adminService.TotalEmployee(ctx)
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.TotalEmployee.GetByCareProviderId", EmployeeClientTag))
	}

	return &employeebff.TotalEmployeeResponse{
		Total: result.Total,
	}, nil
}

func (e *EmployeeServiceImpl) GetEmployeeDetails(ctx *titan.Context, request *employeebff.GetEmployeeDetailsRequest) (*employeebff.EmployeeDetailsResponse, error) {
	careProviderID := ctx.UserInfo().CareProviderUUID()
	employeeProfileRes, err := e.employeeProfileService.SearchEmployeeByName(ctx, &profile_service_employee.SearchEmployeeByNameRequest{
		Name:              request.Name,
		CareProviderId:    careProviderID,
		PaginationRequest: request.Pagination,
	})
	if err != nil {
		return nil, fmt.Errorf("failed SearchEmployeeByName: %w", err)
	}
	if employeeProfileRes == nil {
		return &employeebff.EmployeeDetailsResponse{
			Employees: []*employeebff.EmployeeDetailResponse{},
		}, nil
	}
	totalEmployee := pkg_util.GetPointerValue(employeeProfileRes.PaginationResponse).Total
	// employeeIds := []uuid.UUID{}
	mapEmployeeDetails := map[uuid.UUID]*employeebff.EmployeeDetailResponse{}
	employeeDetails := []*employeebff.EmployeeDetailResponse{}
	for _, emp := range employeeProfileRes.Profiles {
		if emp == nil {
			continue
		}
		// employeeIds = append(employeeIds, *emp.Id)
		empDetail := &employeebff.EmployeeDetailResponse{
			Id:    emp.Id.String(),
			Types: emp.Types,
			// AccountId:      emp.AccountId,
			CareProviderId: careProviderID,
		}
		mapEmployeeDetails[*emp.Id] = empDetail
		employeeDetails = append(employeeDetails, empDetail)
	}
	// employeeProfileRes, err := e.employeeProfileService.GetEmployeeProfileByIds(ctx, &profile_service_employee.GetByIdsRequest{OriginalIds: employeeIds})
	// if err != nil {
	// 	return nil, errors.WithMessage(err, fmt.Sprintf("%s.GetEmployeeProfileByIds.GetByCareProviderId", EmployeeClientTag))
	// }
	// if employeeProfileRes == nil {
	// 	return &employeebff.EmployeeDetailsResponse{
	// 		Employees: []*employeebff.EmployeeDetailResponse{},
	// 	}, nil
	// }
	doctorHaveContractIds := []uuid.UUID{}
	bsnrData, err := e.bsnrService.GetListBSNRName(ctx)
	if err != nil {
		return nil, err
	}
	for _, profile := range employeeProfileRes.Profiles {
		if bsnrData != nil && len(bsnrData.Data) > 0 {
			_, isFound := lo.Find(bsnrData.Data, func(b bsnr_common.BSNRName) bool {
				_, f := lo.Find(b.BillingDoctors, func(id uuid.UUID) bool {
					return id.String() == mapEmployeeDetails[*profile.Id].Id
				})
				if f {
					return true
				}
				if b.BSNRNames != nil {
					_, fbn := lo.Find(b.BSNRNames, func(bn bsnr_common.BSNRName) bool {
						billingDoctorIDs := slice.Map(bn.BillingDoctors, func(id uuid.UUID) string {
							return id.String()
						})
						return lo.Contains(billingDoctorIDs, mapEmployeeDetails[*profile.Id].Id)
					})
					if fbn {
						return true
					}
				}
				return false
			})

			if isFound {
				profile.MarkAsBillingDoctor = true
			}
		}

		fillEmployeeProfileToEmployeeDetailResponse(mapEmployeeDetails[*profile.Id], profile)
		if profile.HasHzvContracts || profile.HasFavContracts {
			doctorHaveContractIds = append(doctorHaveContractIds, *profile.Id)
		}
	}

	if len(doctorHaveContractIds) == 0 {
		return &employeebff.EmployeeDetailsResponse{Employees: employeeDetails, TotalCount: totalEmployee}, nil
	}
	mapDoctorContract, err := e.doctorParticipateRepo.GetByDoctorIds(ctx, doctorHaveContractIds)
	if err != nil {
		return &employeebff.EmployeeDetailsResponse{Employees: employeeDetails, TotalCount: totalEmployee}, errors.WithStack(err)
	}
	for id, doctorContract := range mapDoctorContract {
		hzvContract := doctorContract.HzvContracts
		favContract := doctorContract.FavContracts
		mapEmployeeDetails[id].HzvContracts = pkg_util.NewPointer(hzvContract)
		mapEmployeeDetails[id].FavContracts = pkg_util.NewPointer(favContract)
	}
	return &employeebff.EmployeeDetailsResponse{Employees: employeeDetails, TotalCount: totalEmployee}, nil
}

func (e *EmployeeServiceImpl) GetEmployeeProfilesByBsnrId(ctx *titan.Context, request *employeebff.GetByBsnrIdRequest) (*employeebff.EmployeeDetailsResponse, error) {
	result, err := e.GetEmployeeDetails(ctx, &employeebff.GetEmployeeDetailsRequest{
		Name: "",
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 2000,
			SortBy:   "name",
			Order:    "ASC",
		},
	})
	if err != nil {
		return &employeebff.EmployeeDetailsResponse{Employees: []*employeebff.EmployeeDetailResponse{}}, err
	}
	if result == nil {
		return &employeebff.EmployeeDetailsResponse{Employees: []*employeebff.EmployeeDetailResponse{}}, nil
	}
	if result.Employees == nil {
		return &employeebff.EmployeeDetailsResponse{Employees: []*employeebff.EmployeeDetailResponse{}}, nil
	}
	employees := slice.Filter(result.Employees, func(t *employeebff.EmployeeDetailResponse) bool {
		return request.BsnrId != nil && t.BsnrIds != nil && slice.Contains(t.BsnrIds, *request.BsnrId)
	})
	return &employeebff.EmployeeDetailsResponse{Employees: employees, TotalCount: int64(len(employees))}, nil
}

func (*EmployeeServiceImpl) GetHavgMigrateContractors(ctx *titan.Context, request *employeebff.GetHavgMigrateContractorsRequest) (*employeebff.GetHavgMigrateContractorsResponse, error) {
	hpmRestService := hpm_rest_client.NewServiceRestWithEndpoint(request.Endpoint)
	resHavg, err := hpmRestService.GetHavgVpId(ctx, &hpm_rest_client.GetHavgVpIdRequest{
		HavgId: request.HavgId,
	})
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.GetMigrateContractors", EmployeeClientTag))
	}
	res := &employeebff.GetHavgMigrateContractorsResponse{
		HavgVpId: resHavg.HavgVpId,
	}
	return res, nil
}

func (*EmployeeServiceImpl) GetMediMigrateContractors(ctx *titan.Context, request *employeebff.GetMediMigrateContractorsRequest) (*employeebff.GetMediMigrateContractorsResponse, error) {
	hpmRestService := hpm_rest_client.NewServiceRestWithEndpoint(request.Endpoint)
	resMedi, err := hpmRestService.GetMediVpId(ctx, &hpm_rest_client.GetMediVpIdRequest{
		MediId: request.MediId,
	})
	if err != nil {
		return nil, errors.WithMessage(err, fmt.Sprintf("%s.GetMigrateContractors", EmployeeClientTag))
	}
	res := &employeebff.GetMediMigrateContractorsResponse{
		MediVpId: resMedi.MediVpId,
	}
	return res, nil
}

func (*EmployeeServiceImpl) HpmHealCheck(ctx *titan.Context, request *employeebff.HpmHealCheckRequest) (*employeebff.HpmHealCheckResponse, error) {
	hpmRestService := hpm_rest_client.NewServiceRestWithEndpoint(request.Endpoint)
	isHealthy := hpmRestService.HpmHealCheck(ctx)
	return &employeebff.HpmHealCheckResponse{IsHealthy: isHealthy}, nil
}

func (e *EmployeeServiceImpl) HandleEventBSNRChange(ctx *titan.Context, event *bsnr.EventBSNRChange) error {
	employeeProfiles, err := e.employeeRepo.Find(ctx, bson.M{employeeProfileRepo.Field_BsnrId: event.Bsnr.Id})
	if err != nil {
		ctx.Logger().Error("HandleEventEditBSNR", "error", err)
		return err
	}

	employeeProfiles = slice.Map(employeeProfiles, func(profile employeeProfileRepo.EmployeeProfile) employeeProfileRepo.EmployeeProfile {
		profile.Bsnr = event.Bsnr.Code
		profile.BsnrFacilityType = string(event.Bsnr.FacilityType)
		profile.BsnrFaxNumber = event.Bsnr.Fax
		profile.BsnrId = event.Bsnr.Id
		profile.BsnrName = pkg_util.NewPointer(event.Bsnr.Name)
		profile.BsnrNumber = event.Bsnr.Number
		profile.BsnrPhoneNumber = event.Bsnr.PhoneNumber
		profile.BsnrPostCode = event.Bsnr.PostCode
		profile.BsnrPracticeStamp = event.Bsnr.PracticeStamp
		profile.BsnrStreet = event.Bsnr.Street
		return profile
	})
	if _, err := e.employeeRepo.UpdateMany(ctx, employeeProfiles); err != nil {
		ctx.Logger().Error("HandleEventEditBSNR", "error", err)
		return err
	}

	for _, employee := range employeeProfiles {
		err = e.adminBffSocketNotifier.NotifyCareProviderHandleUpdateEmployee(ctx, &employeebff.EventHandleUpdateEmployee{
			Id: employee.Id,
		})
		if err != nil {
			ctx.Logger().Error("HandleEventEditBSNR", "error", err)
		}
	}
	return nil
}

func (e *EmployeeServiceImpl) HandleEventBSNRDeactivate(ctx *titan.Context, event *bsnr.EventBSNRDeactivate) error {
	if event == nil || event.Bsnr == nil || event.Bsnr.Id == nil {
		return nil
	}

	employeeProfiles, err := e.employeeRepo.GetByBsnrId(ctx, event.Bsnr.Id)
	if err != nil {
		return errors.WithStack(err)
	}

	if len(employeeProfiles) == 0 {
		return nil
	}

	employeeProfiles = slice.Map(employeeProfiles, func(profile employeeProfileRepo.EmployeeProfile) employeeProfileRepo.EmployeeProfile {
		if profile.BsnrId != nil && profile.BsnrId.String() == event.Bsnr.Id.String() {
			profile.BsnrId = nil
			profile.Bsnr = ""
			profile.BsnrFacilityType = ""
			profile.BsnrFaxNumber = ""
			profile.BsnrName = pkg_util.NewString("")
			profile.BsnrNumber = ""
			profile.BsnrPhoneNumber = ""
			profile.BsnrPostCode = ""
			profile.BsnrPracticeStamp = ""
			profile.BsnrStreet = ""
		}
		profile.BsnrIds = slice.Filter(profile.BsnrIds, func(id uuid.UUID) bool {
			return id.String() != event.Bsnr.Id.String()
		})
		profile.Bsnrs = slice.Filter(profile.Bsnrs, func(bsnr string) bool {
			return bsnr != event.Bsnr.Code
		})

		return profile
	})

	updatedRes, err := e.employeeRepo.UpdateMany(ctx, employeeProfiles)
	if err != nil {
		return errors.WithStack(err)
	}

	if len(updatedRes) == 0 {
		return errors.New("no employee updated")
	}

	for _, employee := range updatedRes {
		err = e.adminBffSocketNotifier.NotifyCareProviderHandleUpdateEmployee(ctx, &employeebff.EventHandleUpdateEmployee{
			Id:   employee.Id,
			Lanr: pkg_util.GetPointerValue(employee.Lanr),
		})
		if err != nil {
			ctx.Logger().Error("HandleEventEditBSNR", "error", err)
		}
	}

	return nil
}

func InitEmployeeClient(
	adminService admin_service_employee.EmployeeAdminService,
	authService auth_app.AuthService,
	accountService account_service.AccountService,
	profileService profile_service_employee.EmployeeProfileService,
	doctorParticipateRepository DoctorParticipateRepository,
	bsnrClient bsnr.BsnrServiceApp,
	calService *cal_service.CALService,
	socket *socket_api.SocketServiceClient,
	contractService *contract_service_resource.Service,
	client *titan.Client,
	mmiService mmi_service.MMIService,
	hpmRestService hpm_rest_client.ServiceRest,
) employeebff.EmployeeManagementService {
	employeeRepo := employeeProfileRepo.NewEmployeeProfileDefaultRepository()
	service := &EmployeeServiceImpl{
		adminService:           adminService,
		authClient:             authService,
		accountService:         accountService,
		employeeProfileService: profileService,
		doctorParticipateRepo:  doctorParticipateRepository,
		bsnrService:            bsnrClient,
		employeeRepo:           employeeRepo,
		contractService:        contractService,
		adminBffNotifier:       employeebff.NewAdminBffNotifier(),
		adminBffSocketNotifier: employeebff.NewAdminBffSocketNotifier(socket),
		CALService:             calService,
		hpmRestService:         hpmRestService,
		mmiService:             mmiService,
	}
	hook.NewNatsEventWithQueueName[*bsnr.EventBSNRChange](
		bsnr.EVENT_BSNRChange,
		bsnr.EVENT_BSNRChange+"_employee_queue",
		client,
	).RegisterFunc(service.HandleEventBSNRChange)
	hook.NewNatsEventWithQueueName[*bsnr.EventBSNRDeactivate](
		bsnr.EVENT_BSNRDeactivate,
		bsnr.EVENT_BSNRDeactivate+"_employee_queue",
		client,
	).RegisterFunc(service.HandleEventBSNRDeactivate)
	return service
}
