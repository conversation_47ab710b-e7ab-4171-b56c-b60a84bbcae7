package app

import (
	"fmt"
	"sync"
	"time"

	"emperror.dev/errors"
	"github.com/spf13/cast"
	"gitlab.com/silenteer-oss/titan"

	"git.tutum.dev/medi/tutum/ares/app/admin/api/ti_connector"
	"git.tutum.dev/medi/tutum/ares/pkg/minio"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	"git.tutum.dev/medi/tutum/ares/pkg/redis"
	mongodb "git.tutum.dev/medi/tutum/ares/pkg/repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/ti_connector_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	admin_device "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/admin/device"
	settingsRepos "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/settings"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/sysadmin/device"
	"git.tutum.dev/medi/tutum/ares/service/domains/ti_service"
	"git.tutum.dev/medi/tutum/ares/service/domains/ti_service/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/ti_service/companion_service_provider"
	ti_session_service "git.tutum.dev/medi/tutum/ares/service/domains/ti_session/service"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/CARD"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/CARDCMN"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/CCTX"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/CERT"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/CONN"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/EVT"
	"git.tutum.dev/medi/tutum/pkg/api_telematik_dto/SI"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
)

type TIConnectorApp struct {
	SettingsRepo          mongodb.Repo[*settingsRepos.Settings[ti_connector_common.TIConnector]]
	tiSettingService      *ti_service.TiSettingService
	minioClient           *minio.Minio
	redisClient           *redis.Redis
	sysAdminDeviceRepo    device.AdminDeviceDefaultRepository
	mvzAdminDeviceRepo    admin_device.EmployeeDeviceDefaultRepository
	tiSessionService      *ti_session_service.TiSessionService
	tiSubscriptionService *ti_service.TiSubscriptionService
}

func NewTIConnectorApp(minioClient *minio.Minio, tiSubscriptionService *ti_service.TiSubscriptionService) ti_connector.TIConnectorApp {
	redisClient, err := redis.RedisMod.SafeResolve()
	if err != nil {
		panic(fmt.Errorf("failed to get redis client: %w", err))
	}
	tiConnectorService, err := ti_service.TiSettingServiceMod.SafeResolve()
	if err != nil {
		panic(fmt.Errorf("failed to init ti connector service: %w", err))
	}
	tiSessionService, err := ti_session_service.TiSessionServiceMod.SafeResolve()
	if err != nil {
		panic(fmt.Errorf("failed to get ti card session service: %w", err))
	}
	return &TIConnectorApp{
		SettingsRepo:          settingsRepos.NewSettingsDefaultRepository[ti_connector_common.TIConnector](),
		tiSettingService:      tiConnectorService,
		minioClient:           minioClient,
		redisClient:           redisClient,
		sysAdminDeviceRepo:    device.NewAdminDeviceDefaultRepository(),
		mvzAdminDeviceRepo:    admin_device.NewEmployeeDeviceDefaultRepository(),
		tiSessionService:      tiSessionService,
		tiSubscriptionService: tiSubscriptionService,
	}
}

func (*TIConnectorApp) validateDeviceName(ctx *titan.Context, connector ti_connector_common.TIConnector) (*string, error) {
	if connector.AdminDevice == "" {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Device_Not_Found)
	}

	return &connector.AdminDevice, nil
}

func (srv *TIConnectorApp) SaveTIConnector(ctx *titan.Context, request ti_connector.SaveTIConnectorRequest) error {
	tiSetting, err := srv.tiSettingService.SaveTIConnector(ctx, request)
	if err != nil {
		return err
	}

	if tiSetting == nil {
		return errors.New("failed to save ti connector")
	}

	go func() {
		if srv.tiSubscriptionService.SubscribeEvents(ctx.Clone(), *tiSetting) != nil {
			ctx.Logger().Error("failed to subscribe device")
		}
	}()

	return nil
}

func (srv *TIConnectorApp) GetTIConnectors(ctx *titan.Context) (*ti_connector.GetTIConnectorsResponse, error) {
	results, err := srv.tiSettingService.GetTIConnectors(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if len(results) == 0 {
		return &ti_connector.GetTIConnectorsResponse{
			TIConnectors: []ti_connector_common.TIConnectorListItem{},
		}, nil
	}

	adminDeviceName, err := srv.validateDeviceName(ctx, results[0])
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if adminDeviceName == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Device_Not_Found)
	}

	deviceName := util.GetPointerValue(adminDeviceName)

	// Initialize connectors with basic information
	tiConnectors := make([]ti_connector_common.TIConnectorListItem, len(results))
	for i, r := range results {
		tiConnectors[i] = ti_connector_common.TIConnectorListItem{
			ID:       r.ID,
			Settings: &r,
		}
	}

	// Process each connector concurrently
	var wg sync.WaitGroup
	errChan := make(chan error, len(results))

	for i, tiSetting := range results {
		wg.Add(1)
		go func(index int, setting ti_connector_common.TIConnector) {
			defer wg.Done()
			if err := srv.processConnector(ctx, deviceName, &tiConnectors[index], setting); err != nil {
				errChan <- err
				tiConnectors[index].ErrorMsg = err.Error()
			}
		}(i, tiSetting)
	}

	wg.Wait()
	close(errChan)

	// Log any errors that occurred during processing
	for err := range errChan {
		if err != nil {
			return nil, err
		}
	}

	return &ti_connector.GetTIConnectorsResponse{
		TIConnectors: tiConnectors,
	}, nil
}

// processConnector handles the processing of a single TI connector
func (srv *TIConnectorApp) processConnector(ctx *titan.Context, deviceName string, connector *ti_connector_common.TIConnectorListItem, tiSetting ti_connector_common.TIConnector) error {
	tiServiceProvider, err := companion_service_provider.GetConnectorServiceProvider(ctx, tiSetting.ToTiInfo())
	if err != nil {
		return errors.WithStack(err)
	}
	eventService := tiServiceProvider.GetEventService()
	certificateService := tiServiceProvider.GetCertificateService()

	var wg sync.WaitGroup
	wg.Add(2)
	var resourceErr, certificateErr error

	// Get resource information
	go func() {
		defer wg.Done()
		if err := srv.getResourceInformation(ctx, eventService, connector, tiSetting, deviceName); err != nil {
			resourceErr = err
		}
	}()

	// Check certificate expiration
	go func() {
		defer wg.Done()
		if err := srv.checkCertificateExpiration(ctx, eventService, certificateService, connector, tiSetting, deviceName); err != nil {
			certificateErr = err
		}
	}()

	wg.Wait()

	if resourceErr != nil {
		return resourceErr
	}
	if certificateErr != nil {
		return certificateErr
	}

	return nil
}

// getResourceInformation retrieves and sets resource information for a connector
func (*TIConnectorApp) getResourceInformation(ctx *titan.Context, eventService *companion_service_provider.EventService, connector *ti_connector_common.TIConnectorListItem, tiSetting ti_connector_common.TIConnector, deviceName string) error {
	getResourceInforRequest := &EVT.GetResourceInformation{
		Context: CCTX.ContextType{
			MandantId:      CONN.MandantIdType(tiSetting.MandantId),
			ClientSystemId: CONN.ClientSystemIdType(tiSetting.ClientSystemId),
			WorkplaceId:    CONN.WorkplaceIdType(deviceName),
		},
	}

	resourceResp, err := eventService.GetResourceInformation(ctx, getResourceInforRequest)
	if err != nil {
		return errors.WithStack(err)
	}

	if resourceResp != nil && resourceResp.Connector != nil {
		connector.TIStatus = resourceResp.Connector.Vpntistatus.ConnectionStatus
	}
	return nil
}

// checkCertificateExpiration checks and sets certificate expiration information
func (srv *TIConnectorApp) checkCertificateExpiration(ctx *titan.Context, eventService *companion_service_provider.EventService, certificateService *companion_service_provider.CertificateService, connector *ti_connector_common.TIConnectorListItem, tiSetting ti_connector_common.TIConnector, deviceName string) error {
	tiContext := CCTX.ContextType{
		MandantId:      CONN.MandantIdType(tiSetting.MandantId),
		ClientSystemId: CONN.ClientSystemIdType(tiSetting.ClientSystemId),
		WorkplaceId:    CONN.WorkplaceIdType(deviceName),
	}

	cardsResponse, err := eventService.GetCards(ctx, &EVT.GetCards{
		MandantWide: false,
		Context:     tiContext,
	})
	if err != nil {
		return errors.WithStack(err)
	}

	if err := srv.processCards(ctx, cardsResponse, &tiContext); err != nil {
		return err
	}

	checkCerResp, err := certificateService.CheckCertificateExpiration(ctx, &CERT.CheckCertificateExpiration{
		Context: tiContext,
	})
	if err != nil {
		return errors.WithStack(err)
	}

	if checkCerResp != nil {
		return srv.processExpirationDates(checkCerResp, connector)
	}

	return nil
}

// processCards processes card information and updates the context
func (srv *TIConnectorApp) processCards(ctx *titan.Context, cardsResponse *EVT.GetCardsResponse, tiContext *CCTX.ContextType) error {
	if cardsResponse == nil || len(cardsResponse.Cards.Card) == 0 {
		return nil
	}

	hbaCard := slice.FindOne(cardsResponse.Cards.Card, func(card CARD.CardInfoType) bool {
		return card.CardType == CARDCMN.CardTypeTypeHba ||
			card.CardType == CARDCMN.CardTypeTypeHbax ||
			card.CardType == CARDCMN.CardTypeTypeHbaQsig
	})

	if hbaCard != nil && hbaCard.Iccsn != nil {
		tiSession, err := srv.tiSessionService.TryGetCardSession(ctx, ti_session_service.GetSessionRequest{
			CardHandle: string(hbaCard.CardHandle),
			Iccsn:      string(*hbaCard.Iccsn),
		})
		if err != nil {
			return errors.WithStack(err)
		}
		tiContext.UserId = tiSession
	}

	return nil
}

// processExpirationDates processes certificate expiration dates
func (srv *TIConnectorApp) processExpirationDates(checkCerResp *CERT.CheckCertificateExpirationResponse, connector *ti_connector_common.TIConnectorListItem) error {
	for _, cer := range checkCerResp.CertificateExpiration {
		if cer.CtId == "" { // SmcKt card
			validityDate, err := srv.parseValidityDate(cer.Validity)
			if err != nil {
				return err
			}
			connector.SMCKExpiredDate = util.NewPointer(util.ConvertTimeToMiliSecond(validityDate))
			break
		}
	}
	return nil
}

// parseValidityDate parses the validity date string
func (*TIConnectorApp) parseValidityDate(validity string) (time.Time, error) {
	validityDate, err := time.Parse(util.YYYY_MM_DD, validity)
	if err == nil {
		return validityDate, nil
	}

	validityDate, err = time.Parse(util.YYYY_MM_DD_Z, validity)
	if err != nil {
		return time.Time{}, errors.WithStack(err)
	}

	return validityDate, nil
}

func (srv *TIConnectorApp) RemoveTIConnector(ctx *titan.Context, request ti_connector.TIConnectorRequest) error {
	tiInfo, err := srv.tiSettingService.GetBySettingId(ctx, request.ID)
	if err != nil {
		return err
	}
	if tiInfo == nil {
		return pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_TIConnector_Not_Found)
	}

	err = srv.tiSettingService.RemoveById(ctx, request.ID)
	if err != nil {
		return err
	}

	go func() {
		if err := srv.tiSubscriptionService.UnsubscribeEvents(ctx.Clone(), *tiInfo); err != nil {
			ctx.Logger().Error("failed to unsubscribe all devices", "err", err.Error())
		}
	}()

	return nil
}

func (srv *TIConnectorApp) ViewConnectorStatus(ctx *titan.Context, request ti_connector.TIConnectorRequest) (*ti_connector.ViewConnectorStatusResponse, error) {
	connector, err := srv.tiSettingService.GetBySettingId(ctx, request.ID)
	if err != nil {
		return nil, fmt.Errorf("cannot get connector: %w", err)
	}

	if connector == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_TIConnector_Not_Found)
	}

	deviceName, err := srv.validateDeviceName(ctx, *connector)
	if err != nil {
		return nil, fmt.Errorf("cannot get device name: %w", err)
	}

	if deviceName == nil {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Device_Not_Found)
	}

	tiServiceProvider, err := companion_service_provider.GetConnectorServiceProvider(ctx, connector.ToTiInfo())
	if err != nil {
		return nil, err
	}

	// query data from sds
	connectorService := tiServiceProvider.GetSdsService()

	productServiceInformation, err := connectorService.GetProductServiceInformation(ctx, fmt.Sprintf("%s:%s", connector.Host, connector.Port))
	if err != nil {
		return nil, errors.WithStack(err)
	}

	eventService := tiServiceProvider.GetEventService()

	getResourceInforRequest := &EVT.GetResourceInformation{
		Context: CCTX.ContextType{
			MandantId:      CONN.MandantIdType(connector.MandantId),
			ClientSystemId: CONN.ClientSystemIdType(connector.ClientSystemId),
			WorkplaceId:    CONN.WorkplaceIdType(*deviceName),
		},
	}
	resourceResp, err := eventService.GetResourceInformation(ctx, getResourceInforRequest)
	if err != nil {
		ctx.Logger().Error(errors.WithStack(err).Error())
	}

	var connectionStatus, sisStatus string
	errorStates := []ti_connector.ConnectionStatus{}
	if resourceResp != nil && resourceResp.Connector != nil {
		connectionStatus = resourceResp.Connector.Vpntistatus.ConnectionStatus
		sisStatus = resourceResp.Connector.Vpntistatus.ConnectionStatus
		for _, errorState := range resourceResp.Connector.OperatingState.ErrorState {
			validFrom, _ := cast.StringToDate(errorState.ValidFrom)
			errorStates = append(errorStates, ti_connector.ConnectionStatus{
				Severity:    ti_connector.ConnectionStatusEnum(errorState.Severity),
				Type:        errorState.Type,
				EventStatus: errorState.ErrorCondition,
				ValidFrom:   util.ConvertTimeToMiliSecond(validFrom),
			})
		}
	}

	// check unsupported version services
	unsupportedServices := []string{}
	for _, versionConfig := range common.SupportedServiceVersionConfigs {
		discoveryService := slice.FindOne(productServiceInformation.ServiceInformation.Service, func(t SI.ServiceType) bool {
			return t.Name == string(versionConfig.Name)
		})
		if discoveryService == nil {
			return nil, errors.New(string(error_code.ErrorCode_TIConnector_Error_Service_Not_Found))
		}

		supported := false
		for _, v := range versionConfig.Versions {
			supportedVersionConfig := slice.FindOne(discoveryService.Versions.Version, func(t SI.VersionType) bool {
				return t.Version == v
			})
			if supportedVersionConfig != nil {
				supported = true
			}
		}
		if !supported {
			unsupportedServices = append(unsupportedServices, string(versionConfig.Name))
		}
	}
	return &ti_connector.ViewConnectorStatusResponse{
		TIConnector:                *connector,
		Manufacturer:               string(productServiceInformation.ProductInformation.ProductTypeInformation.ProductType),
		ConnectorTypeVersion:       string(productServiceInformation.ProductInformation.ProductTypeInformation.ProductTypeVersion),
		HardWareVersion:            string(productServiceInformation.ProductInformation.ProductIdentification.ProductVersion.Local.Hwversion),
		FirmWareVersion:            string(productServiceInformation.ProductInformation.ProductIdentification.ProductVersion.Local.Fwversion),
		TiConnection:               connectionStatus,
		SisConnection:              sisStatus,
		ConnectionStatuses:         errorStates,
		UnsupportedVersionServices: unsupportedServices,
		ProductName:                string(productServiceInformation.ProductInformation.ProductMiscellaneous.ProductName),
	}, nil
}
