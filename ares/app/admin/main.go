package main

import (
	"context"
	"os"
	"time"

	"git.tutum.dev/medi/tutum/ares/app/admin/api/admin_bff"
	app "git.tutum.dev/medi/tutum/ares/app/admin/internal"
	"git.tutum.dev/medi/tutum/ares/app/auth/pkg/auth"
	"git.tutum.dev/medi/tutum/ares/pkg/config/cal"
	"git.tutum.dev/medi/tutum/ares/pkg/config/hpm"
	"git.tutum.dev/medi/tutum/ares/pkg/config/masterdata"
	"git.tutum.dev/medi/tutum/ares/pkg/config/minio"
	"git.tutum.dev/medi/tutum/ares/pkg/config/redis"
	"git.tutum.dev/medi/tutum/ares/pkg/healthcheck"
	"git.tutum.dev/medi/tutum/ares/share/config"
	"github.com/spf13/viper"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
	_ "go.uber.org/automaxprocs"
)

func init() {
	viper.SetDefault(minio.MINIO_ENDPOINT, "local")
	viper.SetDefault(minio.MINIO_ACCESS_KEY_ID, "minioadmin")
	viper.SetDefault(minio.MINIO_SECRET_ACCESS_KEY, "minioadmin")
	viper.SetDefault(minio.MINIO_BUCKET_KV_CONNECT, "bucket-kvconnect")
	viper.SetDefault(minio.MINIO_BUCKET_DOCTERLETTER, "bucket-doctor-letter")
	viper.SetDefault(minio.MINIO_BUCKET_TMP, "bucket-tmp")
	viper.SetDefault(minio.MINIO_BUCKET_XBDT, "bucket-xbdt")
	viper.SetDefault(minio.MINIO_BUCKET_DM_COMPANION, "bucket-dm-companion")
	viper.SetDefault("mongodb.uri", "mongodb://localhost:27017")
	viper.SetDefault("deployment.profile", "local")
	viper.SetDefault(cal.CAL_BASE_URL, "https://dev.garrio.dev/cal")

	viper.SetDefault(redis.REDIS_MAXIDLE, 9)
	viper.SetDefault(redis.REDIS_ADDRESS, "localhost:6379")
	viper.SetDefault(redis.REDIS_PASSWORD, "")
	viper.SetDefault(hpm.HPM_URL, "http://*************:22220")
	viper.SetDefault(hpm.HPM_SECRET, "Njg4NzA2NkEtODUzOC00MEFDLUIwQUYtN0IyOUVFMEZDNTdF")
	viper.SetDefault(hpm.HPM_SYSTEMOID, "14CEEFF4-E2E0-4990-82D0-7FFC42FC4BA6")

	viper.SetDefault(auth.ZitadelAuthCallback, "http://local/pro/admin/api/app/admin/auth/callBack")
	viper.SetDefault(auth.ZitadelAuthPostUrl, "https://local")
	viper.SetDefault(auth.ZitadelManagementDomain, "https://garrio-dev.zitadel.web-prod-1.rancher-prod-1.medi-verbund.de")
	viper.SetDefault(auth.ZitadelProjectId, "275947136518982054")
	viper.SetDefault(auth.ZitadelCALProjectId, "275582108993325478")
	viper.SetDefault(auth.ZitadelPROOrgId, "248218340563159550")
	viper.SetDefault(masterdata.MASTERDATA_SERVER_URI, "https://dev.garrio.dev/pro/admin/masterdata/v3")

	viper.SetDefault("mmi.baseUrl", "http://mmi.medi-verbund.de:7777")
	viper.SetDefault("mmi.username", "garrio-Stuttgart")
	viper.SetDefault("mmi.licensekey", "3FCR-TCJG-ESJH-ETW7")
	viper.SetDefault("etl.url", "host=localhost user=postgres password=admin dbname=etl port=5432 sslmode=disable")
}

func main() {
	healthcheck.Cmd(admin_bff.NATS_SUBJECT, os.Args)
	redisConfig, err := redis.RedisConfigMod.SafeResolve()
	if err != nil {
		panic(err)
	}

	adminAppConfigs := &config.AdminAppConfigs{
		NatsConfig: titan.GetNatsConfig(),
		MinioClientConfig: minio.MinioClientConfig{
			EndPoint:           viper.GetString(minio.MINIO_ENDPOINT),
			AccessKeyId:        viper.GetString(minio.MINIO_ACCESS_KEY_ID),
			SecretAccessKey:    viper.GetString(minio.MINIO_SECRET_ACCESS_KEY),
			BucketKVConnect:    viper.GetString(minio.MINIO_BUCKET_KV_CONNECT),
			BucketDoctorLetter: viper.GetString(minio.MINIO_BUCKET_DOCTERLETTER),
		},
		RedisConfig:         redisConfig,
		CallbackRedirectUrl: viper.GetString(auth.ZitadelAuthCallback),
		PostLogoutUrl:       viper.GetString(auth.ZitadelAuthPostUrl),
	}
	server := app.NewServer(adminAppConfigs)
	defer func() {
		server.Stop()
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()
		if err := submodule.DisposeGlobalScopeWithContext(ctx); err != nil {
			panic(err)
		}
	}()
	okChan := make(chan struct{})
	go func() {
		<-okChan
		_ = os.WriteFile("ok.txt", []byte("ok"), 0644)
	}()
	server.Start(okChan)
}
