package patient_profile

import (
	"fmt"
	"time"

	"emperror.dev/errors"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_profile"
	"git.tutum.dev/medi/tutum/ares/pkg/operator"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/pkg/slice"
	pkgUtil "git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/hestia/infrastructure/util"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type (
	PatientProfileRepository struct {
		patient.PatientProfileDefaultRepository
	}
)

var PatientProfileRepositoryMod = submodule.Make[*PatientProfileRepository](
	func(patientProfileDefaultRepo patient.PatientProfileDefaultRepository) *PatientProfileRepository {
		return &PatientProfileRepository{patientProfileDefaultRepo}
	},
	patient.PatientProfileDefaultRepositoryMod,
)

func NewPatientProfileRepository() *PatientProfileRepository {
	return &PatientProfileRepository{patient.NewPatientProfileDefaultRepository()}
}

type PatientEncryptID struct {
	EncryptedId    string `bson:"encryptedId"`
	Title          string `bson:"title"`
	FirstName      string `bson:"firstName"`
	LastName       string `bson:"lastName"`
	AdditionalName string `bson:"additionalName"`
}

type LHMActiveApprovalSelector struct {
	FirstICDCode       []string
	SecondICDCode      []string
	DiagnosisGroupCode []string
}

type GetRemedyRequest struct {
	PatientId          uuid.UUID
	FirstICDCode       string
	SecondICDCode      *string
	DiagnosisGroupCode string
}

type GetRemedyResponse struct {
	LHMApprovalId                 uuid.UUID
	RemediesPosition              []string
	ComplementaryRemediesPosition []string
}

type CreateInsurance struct {
	PatientId      uuid.UUID
	Insurances     []*patient_profile_common.InsuranceInfo
	PatientProfile patient_profile_common.PatientInfo
}

func (r *PatientProfileRepository) GetPatientProfileByPatientId(ctx *titan.Context, patientId uuid.UUID) (*patient.PatientProfile, error) {
	return r.GetPatientProfileById(ctx, patientId)
}

func (r *PatientProfileRepository) DeleteInsurances(ctx *titan.Context, patientId uuid.UUID, deleteInsuranceIds []uuid.UUID) (*patient_profile_common.PatientInfo, error) {
	profile, err := r.GetPatientProfileByPatientId(ctx, patientId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patient profile")
	}
	if profile == nil {
		return nil, errors.New("patient profile not found")
	}

	profile.PatientInfo.InsuranceInfos = slice.Filter(profile.PatientInfo.InsuranceInfos, func(insurance patient_profile_common.InsuranceInfo) bool {
		return !slice.Contains(deleteInsuranceIds, insurance.Id) || insurance.GetCurrentReadCardModel(ctx) != nil
	})

	patientUpdated, err := r.UpdatePatientProfileV2(ctx, patientId, *profile.PatientInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to update patient profile: %w", err)
	}
	return patientUpdated.PatientInfo, nil
}

type UpdateInsurancesResponse struct {
	PatientId   uuid.UUID
	Insurances  []*patient_profile_common.InsuranceInfo
	PatientInfo patient_profile_common.PatientInfo
}

func (r *PatientProfileRepository) DeletePatient(ctx *titan.Context, patientId uuid.UUID) error {
	_, err := r.Repo.DeleteById(ctx, patientId)
	return err
}

func (r *PatientProfileRepository) UpdateInsurance(ctx *titan.Context, patientId uuid.UUID, insuranceInfoUpdate []*patient_profile_common.InsuranceInfo) (*UpdateInsurancesResponse, error) {
	profile, err := r.GetPatientProfileByPatientId(ctx, patientId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patient profile")
	}
	if profile == nil {
		return nil, errors.New("patient profile not found")
	}
	insuranceInfoUpdate = slice.Map(insuranceInfoUpdate, func(v *patient_profile_common.InsuranceInfo) *patient_profile_common.InsuranceInfo {
		if v != nil && v.Id == uuid.Nil {
			v.Id = uuid.New()
		}
		return v
	})

	mapInsuranceInfoUpdateWithId := slice.Reduce(profile.PatientInfo.InsuranceInfos, func(acc map[uuid.UUID]patient_profile_common.InsuranceInfo, cur patient_profile_common.InsuranceInfo) map[uuid.UUID]patient_profile_common.InsuranceInfo {
		acc[cur.Id] = cur
		return acc
	}, map[uuid.UUID]patient_profile_common.InsuranceInfo{})

	// insurance combined by payload and patient profile
	insurances := []*patient_profile_common.InsuranceInfo{}
	profile.PatientInfo.InsuranceInfos = slice.Map(insuranceInfoUpdate, func(insurance *patient_profile_common.InsuranceInfo) patient_profile_common.InsuranceInfo {
		if insurance == nil {
			return patient_profile_common.InsuranceInfo{}
		}

		if insuranceUpdated, ok := mapInsuranceInfoUpdateWithId[insurance.Id]; ok {
			mergedInsurance := *insurance
			if len(insurance.ReadCardDatas) == 0 {
				mergedInsurance.ReadCardDatas = insuranceUpdated.ReadCardDatas
			}
			insurances = append(insurances, pkgUtil.NewPointer(mergedInsurance))
			return mergedInsurance
		}

		insurances = append(insurances, insurance)
		return *insurance
	})

	insuranceToActive := slice.FindOne(insuranceInfoUpdate, func(ii *patient_profile_common.InsuranceInfo) bool {
		return ii.IsActive
	})
	if pkgUtil.GetPointerValue(insuranceToActive) == nil && profile.GetPatientType() == patient_profile_common.PatientType_Public {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_ValidationError_NotActive_InsuranceInfo)
	}

	err = profile.PatientInfo.ValidateInsurances(profile.PatientInfo.InsuranceInfos, profile.PatientInfo.GenericInfo.PatientType)
	if err != nil {
		return nil, fmt.Errorf("failed to validate insurances: %w", err)
	}

	result, err := r.UpdatePatientProfileV2(ctx, patientId, *profile.PatientInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to update patient profile: %w", err)
	}

	return &UpdateInsurancesResponse{
		PatientId:   patientId,
		Insurances:  insurances,
		PatientInfo: *result.PatientInfo,
	}, nil
}

func (*PatientProfileRepository) CheckCopaymentExemptionTillDate(ctx *titan.Context, patientProfile patient.PatientProfile) (bool, error) {
	activeInsurance := patientProfile.PatientInfo.GetActiveInsurance()
	if activeInsurance == nil {
		return false, nil
	}

	if activeInsurance.HaveCoPaymentExemptionTill &&
		activeInsurance.CopaymentExemptionTillDate != nil &&
		*activeInsurance.CopaymentExemptionTillDate >= util.NowUnixMillis(ctx) {
		return true, nil
	}

	return false, nil
}

func (r *PatientProfileRepository) GetRemedy(ctx *titan.Context, req GetRemedyRequest) (*GetRemedyResponse, error) {
	patientProfile, err := r.GetPatientProfileByPatientId(ctx, req.PatientId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patient profile")
	}
	if patientProfile == nil {
		return nil, nil
	}

	activeInsurance := patientProfile.PatientInfo.GetActiveInsurance()

	if activeInsurance == nil {
		return nil, nil
	}

	activeLHMs := activeInsurance.GetActiveLHMs(ctx)
	if len(activeLHMs) == 0 {
		return nil, nil
	}

	filterBy := slice.FindOne(activeLHMs, func(t patient_profile_common.LHM) bool {
		return (t.FirstICDCode == req.FirstICDCode &&
			t.DiagnosisGroupCode == req.DiagnosisGroupCode &&
			pkgUtil.GetPointerValue(t.SecondICDCode) == pkgUtil.GetPointerValue(req.SecondICDCode)) ||
			(t.FirstICDCode == pkgUtil.GetPointerValue(req.SecondICDCode) &&
				t.DiagnosisGroupCode == req.DiagnosisGroupCode &&
				pkgUtil.GetPointerValue(t.SecondICDCode) == req.FirstICDCode)
	})

	if filterBy == nil {
		return nil, nil
	}

	return &GetRemedyResponse{
		LHMApprovalId:                 pkgUtil.GetPointerValue(filterBy.Id),
		RemediesPosition:              filterBy.PrimaryRemediesPosition,
		ComplementaryRemediesPosition: filterBy.ComplementaryRemediesPosition,
	}, nil
}

func (r *PatientProfileRepository) GetActiveHeimiSelectorApproval(ctx *titan.Context, patientId uuid.UUID) (*LHMActiveApprovalSelector, error) {
	patientProfile, err := r.GetPatientProfileByPatientId(ctx, patientId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patient profile")
	}
	if patientProfile == nil {
		return nil, nil
	}

	activeInsurance := patientProfile.PatientInfo.GetActiveInsurance()

	if activeInsurance == nil {
		return nil, nil
	}

	activeLHMs := activeInsurance.GetActiveLHMs(ctx)
	if len(activeLHMs) == 0 {
		return nil, nil
	}
	return &LHMActiveApprovalSelector{
		FirstICDCode: slice.Map(activeLHMs, func(lhm patient_profile_common.LHM) string { return lhm.FirstICDCode }),
		SecondICDCode: slice.Reduce(activeLHMs, func(v []string, t patient_profile_common.LHM) []string {
			if t.SecondICDCode != nil {
				v = append(v, *t.SecondICDCode)
			}
			return v
		}, []string{}),
		DiagnosisGroupCode: slice.Map(activeLHMs, func(lhm patient_profile_common.LHM) string { return lhm.DiagnosisGroupCode }),
	}, nil
}

func (r *PatientProfileRepository) UpdatePatientMedicalData(ctx *titan.Context, request patient_profile.UpdatePatientMedicalHistoryDataRequest) (*patient.PatientProfile, error) {
	filter := bson.M{
		patient.Field_Id: request.PatientId,
		repos.Field_IsDeleted: bson.M{
			"$ne": true,
		},
	}
	timeNow := util.NowUnixMillis(ctx)
	return r.FindOneAndUpdate(ctx, filter, bson.M{
		"$set": bson.M{
			patient.Field_PatientMedicalData:   request.PatientMedicalData,
			patient.Field_UpdatedAt:            timeNow,
			patient.Field_UpdatedBy:            ctx.UserInfo().UserUUID(),
			patient.Field_MedicalDataUpdatedAt: timeNow,
		},
	}, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (r *PatientProfileRepository) GetPatientProfileById(ctx *titan.Context, patientId uuid.UUID) (*patient.PatientProfile, error) {
	filter := bson.D{
		{Key: patient.Field_Id, Value: patientId},
		{Key: repos.Field_IsDeleted, Value: bson.M{
			"$ne": true,
		}},
		{Key: repos.Field_IsDeleted, Value: bson.M{
			"$ne": true,
		}},
	}

	var result []patient.PatientProfile
	err := r.IDBClient.Find(ctx, filter, &result)

	if err != nil {
		return nil, err
	}

	if len(result) == 0 {
		return nil, nil
	}
	// add patient id to PatientInfo
	result[0].PatientInfo.PatientId = result[0].Id
	return &result[0], nil
}

func decryptPatientIds(patients []patient.PatientProfile) []patient.PatientProfile {
	for i := range patients {
		patientId := patients[i].Id
		patients[i].PatientInfo.PatientId = patientId
	}

	return patients
}

func (r *PatientProfileRepository) GetPatientsProfileByInsuranceNumber(ctx *titan.Context, insuranceNumber string) ([]patient.PatientProfile, error) {
	insuranceField := fmt.Sprintf("%s.%s.%s",
		patient.Field_PatientInfo,
		patient.Field_PatientInfo_InsuranceInfos,
		patient.Field_PatientInfo_InsuranceInfos_InsuranceNumber)
	filter := bson.M{
		insuranceField: bson.M{
			"$in": []string{insuranceNumber},
		},
		repos.Field_IsDeleted: bson.M{
			"$ne": true,
		},
	}

	patients, err := r.Repo.Find(ctx, filter)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patients profile")
	}
	if len(patients) == 0 {
		return nil, nil
	}

	patientsDecrypted := decryptPatientIds(slice.ToValueType(patients))

	return patientsDecrypted, nil
}

func (r *PatientProfileRepository) GetPatientByNameAndDOB(ctx *titan.Context, firstName, lastName string, dob patient_profile_common.DateOfBirth) ([]patient.PatientProfile, error) {
	filter := bson.M{
		"$and": []bson.M{
			{
				"$or": []bson.M{
					{
						patient.Field_FirstName: bson.M{
							"$eq": pkgUtil.SpecialToNormalChar(firstName),
						},
					},
					{
						patient.Field_FirstName: bson.M{
							"$eq": firstName,
						},
					},
				},
			},
			{
				"$or": []bson.M{
					{
						patient.Field_LastName: bson.M{
							"$eq": pkgUtil.SpecialToNormalChar(lastName),
						},
					},
					{
						patient.Field_LastName: bson.M{
							"$eq": lastName,
						},
					},
				},
			},
			{
				repos.Field_IsDeleted: bson.M{
					"$ne": true,
				},
			},
		},
	}

	if dob.IsValidDOB {
		dobTime := time.Date(int(*dob.Year), time.Month(*dob.Month), int(*dob.Date), 0, 0, 0, 0, time.UTC)
		birthdateStartDate, birthdateEndDate := pkgUtil.GetDayRange(dobTime)
		birthdateStartDateNumber := pkgUtil.UnixMillis(birthdateStartDate)
		birthdateEndDateNumber := pkgUtil.UnixMillis(birthdateEndDate)

		filter["$and"] = append(filter["$and"].([]bson.M), bson.M{
			patient.Field_DateOfBirth: bson.M{
				"$gte": birthdateStartDateNumber,
				"$lte": birthdateEndDateNumber,
			},
		})
	}

	patients, err := r.Repo.Find(ctx, filter)

	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patients profile")
	}
	if len(patients) == 0 {
		return nil, nil
	}

	patientsDecrypted := decryptPatientIds(slice.ToValueType(patients))
	return patientsDecrypted, nil
}

func (r *PatientProfileRepository) GetPatientsProfileByIkNumber(ctx *titan.Context, ikNumber int32) ([]patient.PatientProfile, error) {
	insuranceField := fmt.Sprintf("%s.%s.%s",
		patient.Field_PatientInfo,
		patient.Field_PatientInfo_InsuranceInfos,
		patient.Field_PatientInfo_InsuranceInfos_IkNumber)
	filter := bson.M{
		insuranceField: bson.M{
			"$in": []int32{ikNumber},
		},
		repos.Field_IsDeleted: bson.M{
			"$ne": true,
		},
	}

	patients, err := r.Repo.Find(ctx, filter)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patients profile")
	}
	if len(patients) == 0 {
		return nil, nil
	}

	patientsDecrypted := decryptPatientIds(slice.ToValueType(patients))
	return patientsDecrypted, nil
}

func (r *PatientProfileRepository) GetPatientProfileByInsuranceNumberAndIkNumber(ctx *titan.Context, insuranceNumber string, ikNumber int32) ([]patient.PatientProfile, error) {
	insuranceField := fmt.Sprintf("%s.%s.%s",
		patient.Field_PatientInfo,
		patient.Field_PatientInfo_InsuranceInfos,
		patient.Field_PatientInfo_InsuranceInfos_InsuranceNumber)

	ikField := fmt.Sprintf("%s.%s.%s",
		patient.Field_PatientInfo,
		patient.Field_PatientInfo_InsuranceInfos,
		patient.Field_PatientInfo_InsuranceInfos_IkNumber)

	filter := bson.M{
		insuranceField: bson.M{
			"$in": []string{insuranceNumber},
		},
		ikField: bson.M{
			"$in": []int32{ikNumber},
		},
		repos.Field_IsDeleted: bson.M{
			"$ne": true,
		},
	}

	patients, err := r.Repo.Find(ctx, filter)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patients profile")
	}
	if len(patients) == 0 {
		return nil, nil
	}

	patientsDecrypted := decryptPatientIds(slice.ToValueType(patients))
	return patientsDecrypted, nil
}

func (r *PatientProfileRepository) GetPatientEncryptIDByInsuranceNumber(ctx *titan.Context, insuranceNumber string) (*PatientEncryptID, error) {
	insuranceField := fmt.Sprintf("%s.%s.%s",
		patient.Field_PatientInfo,
		patient.Field_PatientInfo_InsuranceInfos,
		patient.Field_PatientInfo_InsuranceInfos_InsuranceNumber)
	filter := bson.M{
		insuranceField: bson.M{
			"$in": []string{insuranceNumber},
		},
		repos.Field_IsDeleted: bson.M{
			"$ne": true,
		},
	}

	var result PatientEncryptID
	err := r.IDBClient.FindOne(ctx, filter, &result)

	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (r *PatientProfileRepository) CreatePatientProfileV2(ctx *titan.Context, patientId uuid.UUID, req patient_profile_common.PatientInfo) (*patient_profile_common.PatientInfo, error) {
	patientNumber, err := r.IDBClient.GetConsequenceNumber(ctx)
	if err != nil {
		return nil, errors.New(err.Error())
	}

	req.PatientNumber = int32(patientNumber)
	for insuranceIndex, insurance := range req.InsuranceInfos {
		req.InsuranceInfos[insuranceIndex].Id = uuid.New()
		for lhmIndex := range insurance.LHMs {
			req.InsuranceInfos[insuranceIndex].LHMs[lhmIndex].Id = pkgUtil.NewPointer(uuid.New())
		}
	}
	for insuranceIndex := range req.InsuranceInfos {
		if req.InsuranceInfos[insuranceIndex].Id == uuid.Nil {
			req.InsuranceInfos[insuranceIndex].Id = uuid.New()
		}
		for lhmIndex := range req.InsuranceInfos[insuranceIndex].LHMs {
			if req.InsuranceInfos[insuranceIndex].LHMs[lhmIndex].Id == nil {
				req.InsuranceInfos[insuranceIndex].LHMs[lhmIndex].Id = pkgUtil.NewPointer(uuid.New())
			}
		}
	}
	req.PersonalInfo.SetValidDateOfBirth()
	result, err := r.Create(ctx, &patient.PatientProfile{
		Id:                 &patientId,
		FirstName:          req.PersonalInfo.FirstName,
		LastName:           req.PersonalInfo.LastName,
		DateOfBirth:        req.PersonalInfo.DOB,
		Email:              pkgUtil.GetStringValue(req.ContactInfo.EmailAddress),
		ListHpmInformation: []*patient.HpmInformation{},
		PatientInfo:        &req,
		CreatedAt:          pkgUtil.NowUnixMillis(ctx),
		CreatedBy:          ctx.UserInfo().UserUUID(),
	})

	if err != nil {
		return nil, err
	}
	return result.PatientInfo, nil
}

func (r *PatientProfileRepository) UpdatePatientProfileV2(ctx *titan.Context,
	patientId uuid.UUID,
	req patient_profile_common.PatientInfo,
) (*patient.PatientProfile, error) {
	var employmentInfoUpdatedAt *int64
	nowTime := pkgUtil.NowUnixMillis(ctx)

	existPatient, err := r.GetPatientProfileById(ctx, patientId)
	if err != nil {
		return nil, fmt.Errorf("failed to get patient profile: %w", err)
	}

	if isEmployeeInfoChange(&existPatient.PatientInfo.EmploymentInfo, &req.EmploymentInfo) {
		employmentInfoUpdatedAt = &nowTime
	}

	if req.AddressInfo.Address.Distance == nil {
		req.AddressInfo.Address.Distance = pkgUtil.NewPointer(float32(0))
	}
	if req.InsuranceInfos == nil {
		req.InsuranceInfos = []patient_profile_common.InsuranceInfo{}
	}
	req.PersonalInfo.SetValidDateOfBirth()
	req.PatientNumber = existPatient.PatientInfo.PatientNumber // can not update patient number

	return r.FindOneAndUpdate(ctx, bson.M{
		patient.Field_Id: existPatient.Id,
		repos.Field_IsDeleted: bson.M{
			"$ne": true,
		},
	}, bson.M{
		"$set": bson.M{
			patient.Field_FirstName:               req.PersonalInfo.FirstName,
			patient.Field_LastName:                req.PersonalInfo.LastName,
			patient.Field_DateOfBirth:             req.PersonalInfo.DOB,
			patient.Field_Email:                   pkgUtil.GetStringValue(req.ContactInfo.EmailAddress),
			patient.Field_PatientInfo:             req,
			patient.Field_UpdatedAt:               pkgUtil.NowUnixMillis(ctx),
			patient.Field_UpdatedBy:               ctx.UserInfo().UserUUID(),
			patient.Field_EmploymentInfoUpdatedAt: employmentInfoUpdatedAt,
		},
	}, options.FindOneAndUpdate().SetReturnDocument(options.After))
}

func (r *PatientProfileRepository) SearchMatchingPatientProfiles(ctx *titan.Context,
	insuranceNumber string,
	firstName string,
	lastName string,
	dob *int64,
	dateOfBirth *patient_profile_common.DateOfBirth,
) ([]patient.PatientProfile, error) {
	insuranceField := fmt.Sprintf("%s.%s.%s",
		patient.Field_PatientInfo,
		patient.Field_PatientInfo_InsuranceInfos,
		patient.Field_PatientInfo_InsuranceInfos_InsuranceNumber)
	orFilter := []bson.M{
		{
			insuranceField: insuranceNumber,
		},
		{
			"$and": bson.A{
				bson.M{
					patient.Field_FirstName: firstName,
				},
				bson.M{
					patient.Field_LastName: lastName,
				},
			},
		},
	}

	if dob != nil && *dob != -1 {
		birthdateStartDate, birthdateEndDate := pkgUtil.GetDayRange(util.FromUnixMillis(*dob))
		birthdateStartDateNumber := pkgUtil.ConvertTimeToMiliSecond(birthdateStartDate)
		birthdateEndDateNumber := pkgUtil.UnixMillis(birthdateEndDate)
		orFilter = append(orFilter, bson.M{
			"$and": bson.A{
				bson.M{
					patient.Field_DateOfBirth: bson.M{
						"$gte": birthdateStartDateNumber,
						"$lte": birthdateEndDateNumber,
					},
				},
				bson.M{
					patient.Field_FirstName: firstName,
				},
			},
		})
	}

	if dateOfBirth != nil {
		yearField := fmt.Sprintf("%s.%s.%s.%s",
			patient.Field_PatientInfo,
			patient.Field_PatientInfo_PersonalInfo,
			patient.Field_PatientInfo_PersonalInfo_DateOfBirth,
			patient.Field_PatientInfo_PersonalInfo_DateOfBirth_Year)

		monthField := fmt.Sprintf("%s.%s.%s.%s",
			patient.Field_PatientInfo,
			patient.Field_PatientInfo_PersonalInfo,
			patient.Field_PatientInfo_PersonalInfo_DateOfBirth,
			patient.Field_PatientInfo_PersonalInfo_DateOfBirth_Month)

		dateField := fmt.Sprintf("%s.%s.%s.%s",
			patient.Field_PatientInfo,
			patient.Field_PatientInfo_PersonalInfo,
			patient.Field_PatientInfo_PersonalInfo_DateOfBirth,
			patient.Field_PatientInfo_PersonalInfo_DateOfBirth_Date)

		orFilter = append(orFilter, bson.M{
			"$and": bson.A{
				bson.M{
					yearField:  dateOfBirth.Year,
					monthField: dateOfBirth.Month,
					dateField:  dateOfBirth.Date,
				},
				bson.M{
					patient.Field_FirstName: firstName,
				},
			},
		})
	}

	filter := bson.M{
		"$or": orFilter,
		repos.Field_IsDeleted: bson.M{
			"$ne": true,
		},
	}

	var results []patient.PatientProfile
	err := r.IDBClient.Find(ctx, filter, &results, options.Find().SetSort(bson.D{{Key: patient.Field_FirstName, Value: 1}}))

	if err != nil {
		return nil, err
	}
	return results, nil
}

type SearchMatchingProfilesRequest struct {
	PatientIds      []uuid.UUID
	InsuranceNumber string
	FirstName       string
	LastName        string
	Dob             *int64
}

func (r *PatientProfileRepository) SearchMatchingProfiles(ctx *titan.Context, request SearchMatchingProfilesRequest) ([]patient.PatientProfile, error) {
	if request.PatientIds == nil {
		return nil, nil
	}

	patientIds := slice.Map(request.PatientIds, func(patientId uuid.UUID) uuid.UUID {
		return patientId
	})

	insuranceField := fmt.Sprintf("%s.%s.%s",
		patient.Field_PatientInfo,
		patient.Field_PatientInfo_InsuranceInfos,
		patient.Field_PatientInfo_InsuranceInfos_InsuranceNumber)

	andFilter := []bson.M{
		{
			repos.Field_IsDeleted: bson.M{
				"$ne": true,
			},
		},
		{
			patient.Field_Id: bson.M{
				"$in": patientIds,
			},
		},
		{
			insuranceField: bson.M{
				"$eq": request.InsuranceNumber,
			},
		},
		{
			"$or": []bson.M{
				{
					patient.Field_FirstName: bson.M{
						"$eq": pkgUtil.SpecialToNormalChar(request.FirstName),
					},
				},
				{
					patient.Field_FirstName: bson.M{
						"$eq": request.FirstName,
					},
				},
			},
		},
		{
			"$or": []bson.M{
				{
					patient.Field_LastName: bson.M{
						"$eq": pkgUtil.SpecialToNormalChar(request.LastName),
					},
				},
				{
					patient.Field_LastName: bson.M{
						"$eq": request.LastName,
					},
				},
			},
		},
	}

	if request.Dob != nil && *request.Dob != -1 {
		birthdateStartDate, birthdateEndDate := pkgUtil.GetDayRange(util.FromUnixMillis(*request.Dob))
		birthdateStartDateNumber := pkgUtil.ConvertTimeToMiliSecond(birthdateStartDate)
		birthdateEndDateNumber := pkgUtil.UnixMillis(birthdateEndDate)
		andFilter = append(andFilter, bson.M{
			patient.Field_DateOfBirth: bson.M{
				"$gte": birthdateStartDateNumber,
				"$lte": birthdateEndDateNumber,
			},
		})
	}

	filter := bson.M{
		"$and": andFilter,
	}

	var results []patient.PatientProfile
	err := r.IDBClient.Find(ctx, filter, &results)

	if err != nil {
		return nil, err
	}
	if len(results) == 0 {
		return nil, nil
	}

	patientsDecrypted := decryptPatientIds(results)
	return patientsDecrypted, nil
}

func (r *PatientProfileRepository) FindByFirstNameLastNameDob(ctx *titan.Context, firstName, lastName string, dob patient_profile_common.DateOfBirth) ([]patient.PatientProfile, error) {
	yearField := fmt.Sprintf("%s.%s.%s.%s",
		patient.Field_PatientInfo,
		patient.Field_PatientInfo_PersonalInfo,
		patient.Field_PatientInfo_PersonalInfo_DateOfBirth,
		patient.Field_PatientInfo_PersonalInfo_DateOfBirth_Year)

	monthField := fmt.Sprintf("%s.%s.%s.%s",
		patient.Field_PatientInfo,
		patient.Field_PatientInfo_PersonalInfo,
		patient.Field_PatientInfo_PersonalInfo_DateOfBirth,
		patient.Field_PatientInfo_PersonalInfo_DateOfBirth_Month)

	dateField := fmt.Sprintf("%s.%s.%s.%s",
		patient.Field_PatientInfo,
		patient.Field_PatientInfo_PersonalInfo,
		patient.Field_PatientInfo_PersonalInfo_DateOfBirth,
		patient.Field_PatientInfo_PersonalInfo_DateOfBirth_Date)

	filter := bson.M{
		repos.Field_IsDeleted: bson.M{
			"$ne": true,
		},
		"$and": []bson.M{
			{
				"$or": []bson.M{
					{
						patient.Field_FirstName: bson.M{
							"$eq": pkgUtil.SpecialToNormalChar(firstName),
						},
					},
					{
						patient.Field_FirstName: bson.M{
							"$eq": firstName,
						},
					},
				},
			},
			{
				"$or": []bson.M{
					{
						patient.Field_LastName: bson.M{
							"$eq": pkgUtil.SpecialToNormalChar(lastName),
						},
					},
					{
						patient.Field_LastName: bson.M{
							"$eq": lastName,
						},
					},
				},
			},
			{
				yearField: bson.M{
					"$eq": dob.Year,
				},
			},
			{
				monthField: bson.M{
					"$eq": dob.Month,
				},
			},
			{
				dateField: bson.M{
					"$eq": dob.Date,
				},
			},
		},
	}

	var results []patient.PatientProfile
	err := r.IDBClient.Find(ctx, filter, &results)
	if err != nil {
		return nil, err
	}
	if len(results) == 0 {
		return nil, nil
	}

	patientsDecrypted := decryptPatientIds(results)
	return patientsDecrypted, nil
}

func (*PatientProfileRepository) createRegexFilter(field, pattern string) bson.M {
	return bson.M{
		field: primitive.Regex{
			Pattern: pattern,
			Options: "i",
		},
	}
}

func (r *PatientProfileRepository) FindByName(ctx *titan.Context, name string) ([]patient.PatientProfile, error) {
	filter := bson.M{
		operator.Or: []bson.M{
			r.createRegexFilter(patient.Field_FirstName, name),
			r.createRegexFilter(patient.Field_FirstName, pkgUtil.SpecialToNormalChar(name)),
			r.createRegexFilter(patient.Field_LastName, name),
			r.createRegexFilter(patient.Field_LastName, pkgUtil.SpecialToNormalChar(name)),
		},
		repos.Field_IsDeleted: bson.M{
			"$ne": true,
		},
	}

	var results []patient.PatientProfile
	err := r.IDBClient.Find(ctx, filter, &results, &options.FindOptions{Limit: pkgUtil.NewPointer(int64(30))})
	if err != nil {
		return nil, err
	}
	if len(results) == 0 {
		return nil, nil
	}
	patientsDecrypted := decryptPatientIds(results)
	return patientsDecrypted, nil
}

func isEmployeeInfoChange(old, new *patient_profile_common.EmploymentInfo) bool {
	if old.IsEmployed != new.IsEmployed {
		return true
	}
	if old.JobStatus != new.JobStatus {
		return true
	}
	if old.Occupation != new.Occupation {
		return true
	}
	if old.SpecialProblemAtWork != new.SpecialProblemAtWork {
		return true
	}
	if old.WorkActivity1 != new.WorkActivity1 {
		return true
	}
	if old.WorkActivity2 != new.WorkActivity2 {
		return true
	}
	if old.WorkingHourInWeek != new.WorkingHourInWeek {
		return true
	}

	if old.CompanyAddress.City != new.CompanyAddress.City {
		return true
	}
	if old.CompanyAddress.CountryCode != new.CompanyAddress.CountryCode {
		return true
	}
	if old.CompanyAddress.Employer != new.CompanyAddress.Employer {
		return true
	}
	if old.CompanyAddress.Number != new.CompanyAddress.Number {
		return true
	}
	if old.CompanyAddress.PostCode != new.CompanyAddress.PostCode {
		return true
	}
	if old.CompanyAddress.Street != new.CompanyAddress.Street {
		return true
	}
	return false
}

func (r *PatientProfileRepository) FindByInsuranceNumberAndDoB(ctx *titan.Context, insuranceNumber string, dob patient_profile_common.DateOfBirth) ([]patient.PatientProfile, error) {
	insuranceField := fmt.Sprintf("%s.%s.%s",
		patient.Field_PatientInfo,
		patient.Field_PatientInfo_InsuranceInfos,
		patient.Field_PatientInfo_InsuranceInfos_InsuranceNumber)
	yearField := fmt.Sprintf("%s.%s.%s.%s",
		patient.Field_PatientInfo,
		patient.Field_PatientInfo_PersonalInfo,
		patient.Field_PatientInfo_PersonalInfo_DateOfBirth,
		patient.Field_PatientInfo_PersonalInfo_DateOfBirth_Year)
	monthField := fmt.Sprintf("%s.%s.%s.%s",
		patient.Field_PatientInfo,
		patient.Field_PatientInfo_PersonalInfo,
		patient.Field_PatientInfo_PersonalInfo_DateOfBirth,
		patient.Field_PatientInfo_PersonalInfo_DateOfBirth_Month)
	dateField := fmt.Sprintf("%s.%s.%s.%s",
		patient.Field_PatientInfo,
		patient.Field_PatientInfo_PersonalInfo,
		patient.Field_PatientInfo_PersonalInfo_DateOfBirth,
		patient.Field_PatientInfo_PersonalInfo_DateOfBirth_Date)

	filter := bson.M{
		insuranceField: bson.M{
			"$in": []string{insuranceNumber},
		},
		dateField:  pkgUtil.GetPointerValue(dob.Date),
		monthField: pkgUtil.GetPointerValue(dob.Month),
		yearField:  pkgUtil.GetPointerValue(dob.Year),
		repos.Field_IsDeleted: bson.M{
			"$ne": true,
		},
	}

	results, err := r.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	if len(results) == 0 {
		return nil, nil
	}

	patientsDecrypted := decryptPatientIds(slice.ToValueType(results))
	return patientsDecrypted, nil
}

// GetPatientsProfileByInsuranceNumbers searches for patient profiles matching any of the provided insurance numbers
func (r *PatientProfileRepository) GetPatientsProfileByInsuranceNumbers(ctx *titan.Context, insuranceNumbers []string) ([]patient.PatientProfile, error) {
	if len(insuranceNumbers) == 0 {
		return nil, nil
	}

	insuranceField := fmt.Sprintf("%s.%s.%s",
		patient.Field_PatientInfo,
		patient.Field_PatientInfo_InsuranceInfos,
		patient.Field_PatientInfo_InsuranceInfos_InsuranceNumber)

	filter := bson.M{
		insuranceField: bson.M{
			"$in": insuranceNumbers,
		},
		repos.Field_IsDeleted: bson.M{
			"$ne": true,
		},
	}

	patients, err := r.Repo.Find(ctx, filter)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patients profile by insurance numbers")
	}
	if len(patients) == 0 {
		return nil, nil
	}

	return decryptPatientIds(slice.ToValueType(patients)), nil
}

func (r *PatientProfileRepository) FindByPatientNumber(ctx *titan.Context, patientNumber int) (*patient.PatientProfile, error) {
	filter := bson.D{
		{Key: fmt.Sprintf("%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_PatientNumber), Value: patientNumber},
		{Key: repos.Field_IsDeleted, Value: bson.M{
			"$ne": true,
		}},
	}
	patientRepo, err := r.FindOne(ctx, filter)
	if err != nil {
		return nil, err
	}

	if patientRepo == nil {
		return nil, nil
	}

	patientId := patientRepo.Id
	patientRepo.PatientInfo.PatientId = patientId

	return patientRepo, nil
}
