package patient_overview

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.tutum.dev/medi/tutum/pkg/slice"

	pkgUtils "git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"

	service "git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_overview"
	contract_service "git.tutum.dev/medi/tutum/ares/service/contract/contract"
	enrollment_service "git.tutum.dev/medi/tutum/ares/service/domains/api/enrollment"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	enrollment_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/enrollment"
	model "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_overview"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	userSettingsRepos "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/user_settings"
	insuranceModel "git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/insurance"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type repoType string

type LstInsuranceAdvancedFilter struct {
	name          string
	compareMethod service.CompareMethod
}

const (
	overview  repoType = "overview"
	insurance repoType = "insurance"
	DESC      string   = "desc"
	icdCodes  string   = "icdCodes"
	isStrTrue          = "true"
)

var mapFieldNameWithTable = map[string]repoType{
	"ikNumber":      overview,
	"insuranceName": overview,
	"icdCodes":      overview,
	"status":        overview,
	"contractId":    overview,
	"enrolledBy":    overview,
	"doctorId":      overview,
	"endDate":       overview,
	"startDate":     overview,
	"enrolledDate":  overview,
}

var (
	notEnrolledHZV = bson.D{{
		Key: model.Field_ContractType,
		Value: bson.M{
			"$eq": "",
		},
	}}

	NotSubmitEnrollment = bson.D{{
		Key: "$and",
		Value: bson.A{
			bson.D{{
				Key: model.Field_Status,
				Value: bson.M{
					"$in": []model.ContactStatus{model.ContactStatus_Created, model.ContactStatus_Printed, model.ContactStatus_Faulty},
				},
			}},
			bson.D{{
				Key: model.Field_Status,
				Value: bson.M{
					"$ne": nil,
				},
			}},
		},
	}}

	pendingContract = bson.D{{
		Key:   model.Field_Status,
		Value: model.ContactStatus_Requested,
	}}
	verahAllFilterFunc func(ctx *titan.Context) bson.D = func(ctx *titan.Context) bson.D {
		now := pkgUtils.Now(ctx)
		beginQuarter := pkgUtils.BeginningOfQuarter(now).UnixMilli()
		nowTime := now.UnixMilli()
		verahAll := bson.D{{
			Key: "$or",
			Value: bson.A{
				bson.D{
					{
						Key: "$or",
						Value: bson.A{
							bson.M{
								model.Field_VerahRelatedIcds: bson.M{
									"$elemMatch": bson.M{
										model.Field_VerahRelatedIcd_Chronic: false,
										model.Field_VerahRelatedIcd_CreatedDate: bson.M{
											"$gt": beginQuarter,
										},
									},
								},
							},
							bson.M{
								model.Field_VerahRelatedIcds: bson.M{
									"$elemMatch": bson.M{
										model.Field_VerahRelatedIcd_Chronic:    true,
										model.Field_VerahRelatedIcd_ValidUntil: nil,
									},
								},
							},
							bson.M{
								model.Field_VerahRelatedIcds: bson.M{
									"$elemMatch": bson.M{
										model.Field_VerahRelatedIcd_Chronic: true,
										model.Field_VerahRelatedIcd_ValidUntil: bson.M{
											"$gt": nowTime,
										},
									},
								},
							},
						},
					},
				},
				bson.D{
					{
						Key: "$and",
						Value: bson.A{
							bson.M{
								model.Field_HasVerahService: true,
							},
							bson.M{
								model.Field_VerahUpdatedDate: bson.M{
									"$gt": beginQuarter,
								},
							},
						},
					},
				},
			},
		}}
		return verahAll
	}
	verahPotentialFilterFunc func(ctx *titan.Context) bson.D = func(ctx *titan.Context) bson.D {
		now := pkgUtils.Now(ctx)
		beginQuarter := pkgUtils.BeginningOfQuarter(now).UnixMilli()
		nowTime := now.UnixMilli()
		verahPotential := bson.D{{
			Key: "$and",
			Value: bson.A{
				bson.D{
					{
						Key: "$or",
						Value: bson.A{
							bson.M{
								model.Field_HasVerahService: false,
							},
							bson.M{
								model.Field_VerahUpdatedDate: bson.M{
									"$lt": beginQuarter,
								},
							},
						},
					},
				},
				bson.D{{
					Key: "$or",
					Value: bson.A{
						bson.M{
							model.Field_VerahRelatedIcds: bson.M{
								"$elemMatch": bson.M{
									model.Field_VerahRelatedIcd_Chronic: false,
									model.Field_VerahRelatedIcd_CreatedDate: bson.M{
										"$gt": beginQuarter,
									},
								},
							},
						},
						bson.M{
							model.Field_VerahRelatedIcds: bson.M{
								"$elemMatch": bson.M{
									model.Field_VerahRelatedIcd_Chronic:    true,
									model.Field_VerahRelatedIcd_ValidUntil: nil,
								},
							},
						},
						bson.M{
							model.Field_VerahRelatedIcds: bson.M{
								"$elemMatch": bson.M{
									model.Field_VerahRelatedIcd_Chronic: true,
									model.Field_VerahRelatedIcd_ValidUntil: bson.M{
										"$gt": nowTime,
									},
								},
							},
						},
					},
				}},
			},
		}}
		return verahPotential
	}
	verahDocumentedFilterFunc func(ctx *titan.Context) bson.D = func(ctx *titan.Context) bson.D {
		now := pkgUtils.Now(ctx)
		beginQuarter := pkgUtils.BeginningOfQuarter(now).UnixMilli()
		verahDocumented := bson.D{{
			Key: "$and",
			Value: bson.A{
				bson.M{
					model.Field_HasVerahService: true,
				},
				bson.M{
					model.Field_VerahUpdatedDate: bson.M{
						"$gt": beginQuarter,
					},
				},
			},
		}}
		return verahDocumented
	}

	selectiveContractPatients = bson.D{{
		Key: "$and",
		Value: bson.A{
			bson.D{{
				Key: model.Field_Status,
				Value: bson.M{
					"$in": []model.ContactStatus{
						model.ContactStatus_Active,
						model.ContactStatus_Terminated,
						model.ContactStatus_Requested,
						model.ContactStatus_Created,
						model.ContactStatus_Faulty,
						model.ContactStatus_Printed,
					},
				},
			}},
		},
	}}

	preenrollmentUhu35 = bson.D{{
		Key: "$and",
		Value: bson.A{
			bson.M{
				model.Field_HasUhu35Service: true,
			},
		},
	}}
)

type Repository struct {
	patientOverviewRepo model.PatientOverviewDefaultRepository
	insuranceRepo       insuranceModel.HealthInsuranceDefaultRepository
	profileRepo         patient.PatientProfileDefaultRepository
	scheinRepo          schein.ScheinRepoDefaultRepository
	SettingsRepo        userSettingsRepos.UserSettingsDefaultRepository
	enrollmentRepo      *enrollment_repo.Repository
	contractService     *contract_service.Service
}

type PatientOverviewEntity struct {
	PatientId            uuid.UUID                              `json:"patientId" validate:"required"`
	PatientNumber        int64                                  `json:"patientNumber" validate:"required"`
	FirstName            string                                 `json:"firstName" validate:"required"`
	LastName             string                                 `json:"lastName" validate:"required"`
	Dob                  int64                                  `json:"dob" validate:"required"`
	CreatedDate          *int64                                 `json:"createdDate"`
	UpdatedDate          *int64                                 `json:"updatedDate"`
	VerahUpdatedDate     *int64                                 `json:"verahUpdatedDate"`
	InsuranceInfos       []patient_profile_common.InsuranceInfo `json:"insuranceInfos"`
	DateOfDeath          *int64                                 `json:"dateOfDeath"`
	DateOfBirth          patient_profile_common.DateOfBirth     `json:"dateOfBirth"`
	Title                *string                                `json:"title"`
	IntendWord           *string                                `json:"intendWord"`
	PatientType          *patient_profile_common.PatientType    `json:"patientType"`
	Gender               *patient_profile_common.Gender         `json:"gender"`
	PrimaryContactNumber string                                 `json:"primaryContactNumber"`
	FurtherContactNumber []*string                              `json:"furtherContactNumber"`
	Number               string                                 `json:"number"`
	Street               string                                 `json:"street"`
	PostalCode           string                                 `json:"postalCode"`
	City                 string                                 `json:"city"`
}

type TotalResult struct {
	Total int64 `bson:"total"`
}

type ContractID struct {
	Id string `bson:"contractId"`
}

type InsuranceName struct {
	Name string `bson:"insuranceCompanyName"`
}

func (repo *Repository) GetDefaultPatients(ctx *titan.Context, request service.GetPatientOverviewRequest) ([]service.PatientOverview, error) {
	order := 1
	if strings.EqualFold(request.Pagination.Order, DESC) {
		order = -1
	}
	sortQuery := bson.M{
		request.Pagination.SortBy: order,
	}

	patientOverviews, err := repo.BuildDefaultPatientProfile(ctx, sortQuery, request.Pagination.Max+request.Pagination.Offset, request.Pagination.Offset)
	if err != nil {
		return nil, err
	}
	if len(patientOverviews) == 0 {
		return nil, nil
	}
	patientIDs := []uuid.UUID{}
	mapPatientOverview := map[string]*service.PatientOverview{}
	for index, patientOverview := range patientOverviews {
		patientIDs = append(patientIDs, patientOverview.PatientId)
		mapPatientOverview[patientOverview.PatientId.String()] = &patientOverviews[index]
	}
	filterQuery := bson.D{{
		Key: model.Field_PatientId,
		Value: bson.M{
			"$in": patientIDs,
		},
	}}
	patientContracts, err := repo.BuildPatientOverview(ctx, filterQuery)
	if err != nil {
		return nil, err
	}
	patientIds := slice.Map(patientOverviews, func(t service.PatientOverview) uuid.UUID {
		return t.PatientId
	})
	scheins, err := repo.scheinRepo.FindByPatientIds(ctx, patientIds)
	if err != nil {
		return nil, err
	}
	setting, err := repo.SettingsRepo.FindById(ctx, pkgUtils.GetPointerValue(ctx.UserInfo().UserUUID()))
	if err != nil {
		return nil, err
	}

	isFailedEnrollments := setting != nil && setting.Settings["failedEnrollments"] == isStrTrue

	if !isFailedEnrollments {
		scheins = []schein.ScheinRepo{}
	}
	for _, contract := range patientContracts {
		if v, ok := mapPatientOverview[contract.PatientId.String()]; ok {
			for _, patientContract := range contract.PatientContracts {
				schein := slice.FindOne(scheins, func(t schein.ScheinRepo) bool {
					return patientContract.ContractId != nil && t.ContractId != nil && *t.ContractId == *patientContract.ContractId
				})
				data := patientContract
				data.IsBilledFaulty = schein != nil && schein.IsBilledFaulty
				if data.ContractId != nil {
					contract := repo.contractService.GetContractDetailById(*patientContract.ContractId)
					if contract != nil {
						data.ContractName = &contract.ContractDefinition.Vertrag.NAME
					}
				}
				v.PatientContracts = append(v.PatientContracts, data)
			}
			// v.PatientEnrollment = contract.PatientEnrollment
		}
	}
	return patientOverviews, nil
}

func (repo *Repository) GetDefaultPatientsWithAdvanceFilters(ctx *titan.Context, request service.GetPatientOverviewRequest) ([]service.PatientOverview, error) {
	order := 1
	var patientOverviews []service.PatientOverview
	patientIDs := []uuid.UUID{}
	mapPatientOverview := map[string]*service.PatientOverview{}
	var err error
	if strings.EqualFold(request.Pagination.Order, DESC) {
		order = -1
	}
	sortQuery := bson.M{
		request.Pagination.SortBy: order,
	}

	mapAdvanceFilter, err := repo.AnalysisAdvanceFilters(ctx, request.AdvancedFilters)
	if err != nil {
		return nil, err
	}
	newMapAdvanceFilter := map[repoType][]bson.E{
		overview: {},
	}
	for _, s := range mapAdvanceFilter[overview] {
		if s.Key != model.Field_InsuranceName {
			newMapAdvanceFilter[overview] = append(newMapAdvanceFilter[overview], s)
		}
	}
	patientIds, err := repo.BuildPatientOverviewIDs(ctx, append(bson.D{}, newMapAdvanceFilter[overview]...), bson.D{})
	if err != nil {
		return nil, err
	}

	lstInsurance := GetLstInsNameRequest(request)
	// Refilter with insurance Name
	if len(lstInsurance) > 0 {
		filterIDsQueryPatient := bson.D{
			{Key: patient.Field_Id, Value: bson.M{
				"$in": patientIds,
			}},
		}
		patientIds = []uuid.UUID{}
		patients, err := repo.profileRepo.Find(ctx, filterIDsQueryPatient)
		if err != nil {
			return nil, err
		}
		for _, patient := range patients {
			for _, ins := range lstInsurance {
				existIns := slice.FindOne(patient.PatientInfo.InsuranceInfos, func(t patient_profile_common.InsuranceInfo) bool {
					return t.InsuranceCompanyName == ins.name
				})
				if (ins.compareMethod == service.Equal && existIns != nil) || (ins.compareMethod == service.NotEqual && existIns == nil) {
					patientIds = append(patientIds, *patient.Id)
				}
			}
		}
	}
	filterIDsQuery := bson.D{
		{Key: patient.Field_Id, Value: bson.M{
			"$in": patientIds,
		}},
	}
	patientOverviews, err = repo.BuildPatientProfile(ctx, filterIDsQuery, bson.D{}, sortQuery, request.Pagination.Max+request.Pagination.Offset, request.Pagination.Offset)
	if err != nil {
		return nil, err
	}

	if len(patientOverviews) == 0 {
		return nil, nil
	}
	for index, patientOverview := range patientOverviews {
		patientIDs = append(patientIDs, patientOverview.PatientId)
		mapPatientOverview[patientOverview.PatientId.String()] = &patientOverviews[index]
	}
	filterQuery := bson.D{{
		Key: model.Field_PatientId,
		Value: bson.M{
			"$in": patientIDs,
		},
	}}
	patientContracts, err := repo.BuildPatientOverview(ctx, filterQuery)
	if err != nil {
		return nil, err
	}
	patientIds = slice.Map(patientOverviews, func(t service.PatientOverview) uuid.UUID {
		return t.PatientId
	})
	scheins, err := repo.scheinRepo.FindByPatientIds(ctx, patientIds)
	if err != nil {
		return nil, err
	}
	setting, err := repo.SettingsRepo.FindById(ctx, pkgUtils.GetPointerValue(ctx.UserInfo().UserUUID()))
	if err != nil {
		return nil, err
	}

	isFailedEnrollments := setting != nil && setting.Settings["failedEnrollments"] == isStrTrue

	if !isFailedEnrollments {
		scheins = []schein.ScheinRepo{}
	}
	for _, contract := range patientContracts {
		if v, ok := mapPatientOverview[contract.PatientId.String()]; ok {
			for _, patientContract := range contract.PatientContracts {
				schein := slice.FindOne(scheins, func(t schein.ScheinRepo) bool {
					return patientContract.ContractId != nil && t.ContractId != nil && *t.ContractId == *patientContract.ContractId
				})
				data := patientContract
				data.IsBilledFaulty = schein != nil && schein.IsBilledFaulty
				v.PatientContracts = append(v.PatientContracts, data)
			}
			// v.PatientEnrollment = contract.PatientEnrollment
		}
	}
	return patientOverviews, nil
}

func (repo *Repository) GetQuickFilterPatientOverviews(ctx *titan.Context, request service.GetPatientOverviewRequest) ([]service.PatientOverview, error) {
	var filterQuery bson.D
	switch *request.QuickFilter {
	case service.NotEnrolledHZV:
		filterQuery = notEnrolledHZV
	case service.NotSubmittedEnrollment:
		filterQuery = NotSubmitEnrollment
	case service.PendingContracts:
		filterQuery = pendingContract
	case service.Verah_All:
		filterQuery = verahAllFilterFunc(ctx)
	case service.Verah_Documented:
		filterQuery = verahDocumentedFilterFunc(ctx)
	case service.Verah_Potential:
		filterQuery = verahPotentialFilterFunc(ctx)
	case service.SelectiveContractPatients:
		filterQuery = selectiveContractPatients
	case service.PreEnrollmentUHU35:
		filterQuery = preenrollmentUhu35
	}
	return repo.getQuickFilterPatientOverviews(ctx, request, filterQuery)
}

func (repo *Repository) GetFilterQueryPatientOverviews(ctx *titan.Context, request service.GetPatientOverviewRequest, filterQuery bson.D) ([]service.PatientOverview, error) {
	return repo.getQuickFilterPatientOverviews(ctx, request, filterQuery)
}

func (repo *Repository) getQuickFilterPatientOverviews(ctx *titan.Context, request service.GetPatientOverviewRequest, filterQuery bson.D) ([]service.PatientOverview, error) {
	mapAdvanceFilter, err := repo.AnalysisAdvanceFilters(ctx, request.AdvancedFilters)
	if err != nil {
		return nil, err
	}
	newMapAdvanceFilter := map[repoType][]bson.E{
		overview: {},
	}
	for _, s := range mapAdvanceFilter[overview] {
		if s.Key != model.Field_InsuranceName {
			newMapAdvanceFilter[overview] = append(newMapAdvanceFilter[overview], s)
		}
	}
	patientIds, err := repo.BuildPatientOverviewIDs(ctx, append(filterQuery, newMapAdvanceFilter[overview]...), bson.D{})
	if err != nil {
		return nil, err
	}

	lstInsurance := GetLstInsNameRequest(request)
	// Refilter with insurance Name
	if len(lstInsurance) > 0 {
		filterIDsQueryPatient := bson.D{
			{Key: patient.Field_Id, Value: bson.M{
				"$in": patientIds,
			}},
		}
		patientIds = []uuid.UUID{}
		patients, err := repo.profileRepo.Find(ctx, filterIDsQueryPatient)
		if err != nil {
			return nil, err
		}
		for _, patient := range patients {
			for _, ins := range lstInsurance {
				existIns := slice.FindOne(patient.PatientInfo.InsuranceInfos, func(t patient_profile_common.InsuranceInfo) bool {
					return t.InsuranceCompanyName == ins.name
				})
				if (ins.compareMethod == service.Equal && existIns != nil) || (ins.compareMethod == service.NotEqual && existIns == nil) {
					patientIds = append(patientIds, *patient.Id)
				}
			}
		}
	}
	filterIDsQuery := bson.D{
		{Key: patient.Field_Id, Value: bson.M{
			"$in": patientIds,
		}},
	}
	order := 1
	if strings.EqualFold(request.Pagination.Order, DESC) {
		order = -1
	}
	sortQuery := bson.M{
		request.Pagination.SortBy: order,
	}
	patientOverviewProfiles, err := repo.BuildPatientProfile(ctx, filterIDsQuery, nil, sortQuery, request.Pagination.Max+request.Pagination.Offset, request.Pagination.Offset)
	if err != nil {
		return nil, err
	}

	patientIDs := []uuid.UUID{}
	mapPatientOverview := map[string]*service.PatientOverview{}
	for index, patientOverview := range patientOverviewProfiles {
		patientIDs = append(patientIDs, patientOverview.PatientId)
		mapPatientOverview[patientOverview.PatientId.String()] = &patientOverviewProfiles[index]
	}
	finalFilterPatient := bson.D{{
		Key: model.Field_PatientId,
		Value: bson.M{
			"$in": patientIDs,
		},
	}}
	if len(filterQuery) > 0 {
		finalFilterPatient = pkgUtils.And(filterQuery)
	}
	patientContracts, err := repo.BuildPatientOverview(ctx, finalFilterPatient)
	if err != nil {
		return nil, err
	}
	patientIds = slice.Map(patientOverviewProfiles, func(t service.PatientOverview) uuid.UUID {
		return t.PatientId
	})
	scheins, err := repo.scheinRepo.FindByPatientIds(ctx, patientIds)
	if err != nil {
		return nil, err
	}
	setting, err := repo.SettingsRepo.FindById(ctx, pkgUtils.GetPointerValue(ctx.UserInfo().UserUUID()))
	if err != nil {
		return nil, err
	}

	isFailedEnrollments := setting != nil && setting.Settings["failedEnrollments"] == isStrTrue

	if !isFailedEnrollments {
		scheins = []schein.ScheinRepo{}
	}
	for _, contract := range patientContracts {
		if v, ok := mapPatientOverview[contract.PatientId.String()]; ok {
			for _, patientContract := range contract.PatientContracts {
				schein := slice.FindOne(scheins, func(t schein.ScheinRepo) bool {
					return !t.IsBilled && patientContract.ContractId != nil && t.ContractId != nil && *t.ContractId == *patientContract.ContractId
				})
				data := patientContract
				data.IsBilledFaulty = schein != nil && schein.IsBilledFaulty
				v.PatientContracts = append(v.PatientContracts, data)
			}
			// v.PatientEnrollment = contract.PatientEnrollment
		}
	}
	return patientOverviewProfiles, nil
}

func (repo *Repository) GetTotalDefaultPatients(ctx *titan.Context) (int64, error) {
	return repo.profileRepo.Count(ctx, bson.M{
		"collectionSequenceNumber": bson.M{
			"$exists": false,
		},
	})
}

func (repo *Repository) GetTotalDefaultPatientsWithAdvanceFilter(ctx *titan.Context, request service.GetPatientOverviewRequest) (int64, error) {
	mapAdvanceFilter, err := repo.AnalysisAdvanceFilters(ctx, request.AdvancedFilters)
	if err != nil {
		return 0, err
	}
	newMapAdvanceFilter := map[repoType][]bson.E{
		overview:  {},
		insurance: {},
	}
	for _, s := range mapAdvanceFilter[overview] {
		if s.Key != model.Field_InsuranceName {
			newMapAdvanceFilter[overview] = append(newMapAdvanceFilter[overview], s)
		}
	}

	patientIds, err := repo.BuildPatientOverviewIDs(ctx, newMapAdvanceFilter[overview], bson.D{})
	if err != nil {
		return 0, err
	}
	lstInsurance := GetLstInsNameRequest(request)
	// Refilter with insurance Name
	if len(lstInsurance) > 0 {
		filterIDsQueryPatient := bson.D{
			{Key: patient.Field_Id, Value: bson.M{
				"$in": patientIds,
			}},
		}
		patientIds = []uuid.UUID{}
		patients, err := repo.profileRepo.Find(ctx, filterIDsQueryPatient)
		if err != nil {
			return 0, err
		}
		for _, patient := range patients {
			for _, ins := range lstInsurance {
				existIns := slice.FindOne(patient.PatientInfo.InsuranceInfos, func(t patient_profile_common.InsuranceInfo) bool {
					return t.InsuranceCompanyName == ins.name
				})
				if (ins.compareMethod == service.Equal && existIns != nil) || (ins.compareMethod == service.NotEqual && existIns == nil) {
					patientIds = append(patientIds, *patient.Id)
				}
			}
		}
	}

	if newMapAdvanceFilter[insurance] != nil && len(newMapAdvanceFilter[insurance]) == 0 {
		return int64(len(patientIds)), nil
	}

	filterIDsQuery := bson.A{
		bson.M{patient.Field_Id: bson.M{
			"$in": patientIds,
		}},
	}
	return repo.insuranceRepo.Count(ctx, filterIDsQuery)
}

func (repo *Repository) GetTotalPatientOverviewWithQuickFilter(ctx *titan.Context, request service.GetPatientOverviewRequest) (int64, error) {
	var filterQuery bson.D
	switch *request.QuickFilter {
	case service.NotEnrolledHZV:
		filterQuery = notEnrolledHZV
	case service.NotSubmittedEnrollment:
		filterQuery = NotSubmitEnrollment
	case service.PendingContracts:
		filterQuery = pendingContract
	case service.Verah_All:
		filterQuery = verahAllFilterFunc(ctx)
	case service.Verah_Documented:
		filterQuery = verahDocumentedFilterFunc(ctx)
	case service.Verah_Potential:
		filterQuery = verahPotentialFilterFunc(ctx)
	case service.SelectiveContractPatients:
		filterQuery = selectiveContractPatients
	case service.PreEnrollmentUHU35:
		filterQuery = preenrollmentUhu35
	}
	mapAdvanceFilter, err := repo.AnalysisAdvanceFilters(ctx, request.AdvancedFilters)
	if err != nil {
		return 0, err
	}
	newMapAdvanceFilter := map[repoType][]bson.E{
		overview: {},
	}
	for _, s := range mapAdvanceFilter[overview] {
		if s.Key != model.Field_InsuranceName {
			newMapAdvanceFilter[overview] = append(newMapAdvanceFilter[overview], s)
		}
	}
	patientIds, err := repo.BuildPatientOverviewIDs(ctx, append(filterQuery, newMapAdvanceFilter[overview]...), bson.D{})
	if err != nil {
		return 0, err
	}

	lstInsurance := GetLstInsNameRequest(request)
	// Refilter with insurance Name
	if len(lstInsurance) > 0 {
		filterIDsQueryPatient := bson.D{
			{Key: patient.Field_Id, Value: bson.M{
				"$in": patientIds,
			}},
		}
		patientIds = []uuid.UUID{}
		patients, err := repo.profileRepo.Find(ctx, filterIDsQueryPatient)
		if err != nil {
			return 0, err
		}
		for _, patient := range patients {
			for _, ins := range lstInsurance {
				existIns := slice.FindOne(patient.PatientInfo.InsuranceInfos, func(t patient_profile_common.InsuranceInfo) bool {
					return t.InsuranceCompanyName == ins.name
				})
				if (ins.compareMethod == service.Equal && existIns != nil) || (ins.compareMethod == service.NotEqual && existIns == nil) {
					patientIds = append(patientIds, *patient.Id)
				}
			}
		}
	}
	filterIDsQuery := bson.D{
		{Key: patient.Field_Id, Value: bson.M{
			"$in": patientIds,
		}},
	}
	totalResult, err := repo.CountPatientProfile(ctx, filterIDsQuery)
	if err != nil {
		return 0, err
	}
	return totalResult, nil
}

func (repo *Repository) BuildDefaultPatientProfile(ctx *titan.Context, sorting bson.M, limit, offset int64) ([]service.PatientOverview, error) {
	stage1 := bson.M{
		"$match": bson.M{
			patient.Field_CollectionSequenceNumber: bson.M{"$exists": false},
		},
	}
	stage2 := bson.M{
		"$replaceWith": bson.M{
			"patientId":      fmt.Sprintf("$%s", patient.Field_Id),
			"patientNumber":  fmt.Sprintf("$%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_PatientNumber),
			"title":          fmt.Sprintf("$%s.%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_PersonalInfo, patient.Field_PatientInfo_PersonalInfo_Title),
			"intendWord":     fmt.Sprintf("$%s.%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_PersonalInfo, patient.Field_PatientInfo_PersonalInfo_IntendWord),
			"gender":         fmt.Sprintf("$%s.%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_PersonalInfo, patient.Field_PatientInfo_PersonalInfo_Gender),
			"patientType":    fmt.Sprintf("$%s.%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_GenericInfo, patient.Field_PatientInfo_PersonalInfo_PatientType),
			"firstName":      fmt.Sprintf("$%s", patient.Field_FirstName),
			"lastName":       fmt.Sprintf("$%s", patient.Field_LastName),
			"dob":            fmt.Sprintf("$%s", patient.Field_DateOfBirth),
			"insuranceInfos": fmt.Sprintf("$%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_InsuranceInfos),
			"createdDate":    fmt.Sprintf("$%s", patient.Field_CreatedAt),
			"updatedDate":    fmt.Sprintf("$%s", patient.Field_UpdatedAt),
			"dateOfDeath": fmt.Sprintf("$%s.%s.%s",
				patient.Field_PatientInfo,
				patient.Field_PatientInfo_PersonalInfo,
				patient.Field_PatientInfo_PersonalInfo_DateOfDeath),
			"dateOfBirth": fmt.Sprintf("$%s.%s.%s",
				patient.Field_PatientInfo,
				patient.Field_PatientInfo_PersonalInfo,
				patient.Field_PatientInfo_PersonalInfo_DateOfBirth),
			"primaryContactNumber": fmt.Sprintf("$%s.%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_ContactInfo, patient.Field_PatientInfo_ContactInfo_PrimaryContactNumber),
			"furtherContactNumber": fmt.Sprintf("$%s.%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_ContactInfo, patient.Field_PatientInfo_ContactInfo_FurtherContactNumber),
			"number":               fmt.Sprintf("$%s.%s.%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_AddressInfo, patient.Field_PatientInfo_AddressInfo_Address, patient.Field_PatientInfo_AddressInfo_Number),
			"street":               fmt.Sprintf("$%s.%s.%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_AddressInfo, patient.Field_PatientInfo_AddressInfo_Address, patient.Field_PatientInfo_AddressInfo_Street),
			"postalCode":           fmt.Sprintf("$%s.%s.%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_AddressInfo, patient.Field_PatientInfo_AddressInfo_Address, patient.Field_PatientInfo_AddressInfo_PostalCode),
			"city":                 fmt.Sprintf("$%s.%s.%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_AddressInfo, patient.Field_PatientInfo_AddressInfo_Address, patient.Field_PatientInfo_AddressInfo_City),
		},
	}

	stages := []bson.M{stage1, stage2}
	stages = append(stages, bson.M{
		"$sort": sorting,
	})
	if limit > 0 {
		stages = append(stages, bson.M{
			"$limit": limit,
		}, bson.M{
			"$skip": offset,
		})
	}

	var patientOverviewEntities []*PatientOverviewEntity
	err := repo.profileRepo.IDBClient.Aggregate(ctx, stages, &patientOverviewEntities)
	if err != nil {
		return nil, err
	}
	patientOverviews := []service.PatientOverview{}
	for _, entity := range patientOverviewEntities {
		var (
			InsureNumber *string
			InsureName   string
			IkNumber     int32
		)
		activeInsurance := patient_profile_common.PatientInsuranceInfos(entity.InsuranceInfos).GetActiveInsurance()
		if activeInsurance != nil {
			InsureNumber = activeInsurance.InsuranceNumber
			InsureName = activeInsurance.InsuranceCompanyName
			IkNumber = activeInsurance.IkNumber
		}

		var phoneNumber string
		var address string

		if entity.PrimaryContactNumber != "" {
			phoneNumber = entity.PrimaryContactNumber
		} else if len(entity.FurtherContactNumber) > 0 && *entity.FurtherContactNumber[0] != "" {
			phoneNumber = *entity.FurtherContactNumber[0]
		}

		if entity.Street != "" {
			address += entity.Street
		}

		if entity.Number != "" {
			address += " " + entity.Number
		}

		if entity.Number != "" || entity.Street != "" {
			address += ", "
		}

		if entity.PostalCode != "" {
			address += entity.PostalCode
		}

		if entity.City != "" {
			address += " " + entity.City
		}

		patientOverviews = append(patientOverviews, service.PatientOverview{
			PatientId:     entity.PatientId,
			PatientNumber: entity.PatientNumber,
			InsureNumber:  InsureNumber,
			FirstName:     entity.FirstName,
			LastName:      entity.LastName,
			Dob:           entity.Dob,
			InsureName:    InsureName,
			IkNumber:      IkNumber,
			CreatedDate:   entity.CreatedDate,
			UpdatedDate:   entity.UpdatedDate,
			DateOfDeath:   entity.DateOfDeath,
			DateOfBirth:   entity.DateOfBirth,
			Title:         entity.Title,
			IntendWord:    entity.IntendWord,
			Gender:        entity.Gender,
			PatientType:   entity.PatientType,
			PhoneNumber:   &phoneNumber,
			Address:       &address,
			InsuranceInfo: activeInsurance,
		})
	}
	return patientOverviews, nil
}

func (repo *Repository) BuildPatientOverview(ctx *titan.Context, filterQuery bson.D) ([]*service.PatientOverview, error) {
	stage1 := bson.M{
		"$match": filterQuery,
	}
	stage2 := bson.M{
		"$group": bson.M{
			"_id": bson.M{model.Field_PatientId: fmt.Sprintf("$%s", model.Field_PatientId)},
			"patientContracts": bson.M{
				"$push": bson.M{
					model.Field_Id:                  fmt.Sprintf("$%s", model.Field_Id),
					model.Field_ContractId:          fmt.Sprintf("$%s", model.Field_ContractId),
					model.Field_Status:              fmt.Sprintf("$%s", model.Field_Status),
					model.Field_DoctorId:            fmt.Sprintf("$%s", model.Field_DoctorId),
					model.Field_HpmErrors:           fmt.Sprintf("$%s", model.Field_HpmErrors),
					model.Field_DoctorFunctionType:  fmt.Sprintf("$%s", model.Field_DoctorFunctionType),
					model.Field_EnrolledBy:          fmt.Sprintf("$%s", model.Field_EnrolledBy),
					model.Field_AssigneeId:          fmt.Sprintf("$%s", model.Field_AssigneeId),
					model.Field_EnrolledDate:        fmt.Sprintf("$%s", model.Field_EnrolledDate),
					model.Field_StartDate:           fmt.Sprintf("$%s", model.Field_StartDate),
					model.Field_EndDate:             fmt.Sprintf("$%s", model.Field_EndDate),
					model.Field_CreatedDate:         fmt.Sprintf("$%s", model.Field_CreatedDate),
					model.Field_UpdatedDate:         fmt.Sprintf("$%s", model.Field_UpdatedDate),
					model.Field_VerahRelatedIcds:    fmt.Sprintf("$%s", model.Field_VerahRelatedIcds),
					model.Field_HasVerahService:     fmt.Sprintf("$%s", model.Field_HasVerahService),
					model.Field_HasUhu35Service:     fmt.Sprintf("$%s", model.Field_HasUhu35Service),
					model.Field_VerahUpdatedDate:    fmt.Sprintf("$%s", model.Field_VerahUpdatedDate),
					model.Field_Uhu35SubmissionDate: fmt.Sprintf("$%s", model.Field_Uhu35SubmissionDate),
					model.Field_TeId:                fmt.Sprintf("$%s", model.Field_TeId),
					model.Field_IsSignaturesDone:    fmt.Sprintf("$%s", model.Field_IsSignaturesDone),
					model.Field_SubmitUhu35Status:   fmt.Sprintf("$%s", model.Field_SubmitUhu35Status),
					model.Field_EnrollmentId:        fmt.Sprintf("$%s", model.Field_EnrollmentId),
				},
			},
		},
	}
	stage3 := bson.M{
		"$replaceWith": bson.M{
			model.Field_PatientId: fmt.Sprintf("$%s.%s", model.Field_Id, model.Field_PatientId),
			"patientContracts":    "$patientContracts",
		},
	}

	stages := []bson.M{
		stage1,
		stage2,
		stage3,
	}
	var patientOverviews []*service.PatientOverview
	err := repo.patientOverviewRepo.Db.Aggregate(ctx, stages, &patientOverviews)
	if err != nil {
		return nil, err
	}
	enrollmentIds := slice.Reduce(patientOverviews, func(acc []uuid.UUID, overview *service.PatientOverview) []uuid.UUID {
		for _, p := range overview.PatientContracts {
			if p.EnrollmentId != nil {
				acc = append(acc, *p.EnrollmentId)
			}
		}
		return acc
	}, []uuid.UUID{})

	enrollments := []enrollment_repo.PatientEnrollmentEntity{}
	if len(enrollmentIds) > 0 {
		enrollments, err = repo.enrollmentRepo.GetByIds(ctx, enrollmentIds)
		if err != nil {
			return nil, err
		}
	}
	for _, p := range patientOverviews {
		if p == nil {
			continue
		}
		p.PatientContracts = slice.Map(p.PatientContracts, func(c service.PatientContract) service.PatientContract {
			if c.ContractId != nil {
				contract := repo.contractService.GetContractDetailById(*c.ContractId)
				c.ContractName = &contract.ContractDefinition.Vertrag.NAME

				filter := bson.M{
					schein.Field_PatientId:  p.PatientId,
					schein.Field_ContractId: c.ContractId,
					schein.Field_IsDeleted:  false,
				}
				if c.StartDate != nil {
					yq := pkgUtils.ToYearQuarter(*c.StartDate)
					filter[schein.Field_Year] = yq.Year
					filter[schein.Field_Quarter] = yq.Quarter
				}
				schein, err := repo.scheinRepo.FindOne(ctx, filter, options.FindOne().SetSort(bson.M{
					schein.Field_CreatedAt: -1,
				}))
				if err != nil {
					ctx.Logger().Error(err.Error())
				}
				if schein != nil && schein.SvScheinDetail != nil {
					c.StartDate = schein.SvScheinDetail.StartDate
					c.EndDate = schein.SvScheinDetail.EndDate
				}
			}

			if c.EnrollmentId != nil {
				enrollment := slice.FindOne(enrollments, func(e enrollment_repo.PatientEnrollmentEntity) bool {
					return e.Id != nil && e.Id.String() == c.EnrollmentId.String()
				})
				if enrollment != nil {
					c.PatientEnrollment = &service.PatientEnrollment{
						Id:          *enrollment.Id,
						Status:      enrollment_service.PatientEnrollmentStatus(enrollment.Status),
						CreatedDate: enrollment.CreatedDate,
						UpdatedDate: enrollment.UpdatedDate,
						Message:     enrollment.Messages,
					}
				}
				return c
			}

			return c
		})
	}
	return patientOverviews, nil
}

func (repo *Repository) BuildPatientOverviewIDs(ctx *titan.Context, filterQuery, additionFilter bson.D) ([]uuid.UUID, error) {
	var stages []bson.M
	stage1 := bson.M{
		"$match": filterQuery,
	}
	stage2 := bson.M{
		"$group": bson.M{
			"_id": bson.M{model.Field_PatientId: fmt.Sprintf("$%s", model.Field_PatientId)},
		},
	}
	stage3 := bson.M{
		"$replaceWith": bson.M{
			model.Field_PatientId: fmt.Sprintf("$%s.%s", model.Field_Id, model.Field_PatientId),
		},
	}
	if len(additionFilter) > 0 {
		stages = []bson.M{{"$match": additionFilter}, stage1, stage2, stage3}
	} else {
		stages = []bson.M{stage1, stage2, stage3}
	}

	var patientOverviews []*service.PatientOverview
	err := repo.patientOverviewRepo.Db.Aggregate(ctx, stages, &patientOverviews)
	if err != nil {
		return nil, err
	}
	patientIDs := []uuid.UUID{}
	for _, patientOverview := range patientOverviews {
		patientIDs = append(patientIDs, patientOverview.PatientId)
	}
	return patientIDs, nil
}

func (repo *Repository) BuildPatientProfile(ctx *titan.Context, filterQuery, additionFilter bson.D, sorting bson.M, limit, offset int64) ([]service.PatientOverview, error) {
	var err error
	var tableName string
	var stages []bson.M
	tableName, err = repo.insuranceRepo.GetCollectionName(ctx)

	if err != nil {
		return nil, err
	}
	stage1 := bson.M{
		"$match": filterQuery,
	}
	stage2 := bson.M{
		"$lookup": bson.M{
			"from":         tableName,
			"localField":   "hashedId",
			"foreignField": "hashedId",
			"as":           "merge",
		},
	}

	stage3 := bson.M{
		"$replaceRoot": bson.M{
			"newRoot": bson.M{
				"$mergeObjects": bson.A{bson.M{"$arrayElemAt": bson.A{"$merge", 0}}, "$$ROOT"},
			},
		},
	}

	stage4 := bson.M{
		"$replaceWith": bson.M{
			"patientId":      fmt.Sprintf("$%s", patient.Field_Id),
			"patientNumber":  fmt.Sprintf("$%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_PatientNumber),
			"title":          fmt.Sprintf("$%s.%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_PersonalInfo, patient.Field_PatientInfo_PersonalInfo_Title),
			"intendWord":     fmt.Sprintf("$%s.%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_PersonalInfo, patient.Field_PatientInfo_PersonalInfo_IntendWord),
			"gender":         fmt.Sprintf("$%s.%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_PersonalInfo, patient.Field_PatientInfo_PersonalInfo_Gender),
			"patientType":    fmt.Sprintf("$%s.%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_GenericInfo, patient.Field_PatientInfo_PersonalInfo_PatientType),
			"firstName":      fmt.Sprintf("$%s", patient.Field_FirstName),
			"lastName":       fmt.Sprintf("$%s", patient.Field_LastName),
			"dob":            fmt.Sprintf("$%s", patient.Field_DateOfBirth),
			"insuranceInfos": fmt.Sprintf("$%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_InsuranceInfos),
			"createdDate":    fmt.Sprintf("$%s", patient.Field_CreatedAt),
			"updatedDate":    fmt.Sprintf("$%s", patient.Field_UpdatedAt),
			"dateOfDeath": fmt.Sprintf("$%s.%s.%s",
				patient.Field_PatientInfo,
				patient.Field_PatientInfo_PersonalInfo,
				patient.Field_PatientInfo_PersonalInfo_DateOfDeath),
			"dateOfBirth": fmt.Sprintf("$%s.%s.%s",
				patient.Field_PatientInfo,
				patient.Field_PatientInfo_PersonalInfo,
				patient.Field_PatientInfo_PersonalInfo_DateOfBirth),
			"number":     fmt.Sprintf("$%s.%s.%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_AddressInfo, patient.Field_PatientInfo_AddressInfo_Address, patient.Field_PatientInfo_AddressInfo_Number),
			"street":     fmt.Sprintf("$%s.%s.%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_AddressInfo, patient.Field_PatientInfo_AddressInfo_Address, patient.Field_PatientInfo_AddressInfo_Street),
			"postalCode": fmt.Sprintf("$%s.%s.%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_AddressInfo, patient.Field_PatientInfo_AddressInfo_Address, patient.Field_PatientInfo_AddressInfo_PostalCode),
			"city":       fmt.Sprintf("$%s.%s.%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_AddressInfo, patient.Field_PatientInfo_AddressInfo_Address, patient.Field_PatientInfo_AddressInfo_City),
		},
	}
	if len(filterQuery) == 0 {
		stages = []bson.M{stage2, stage3, stage4}
	} else {
		stages = []bson.M{stage1, stage2, stage3, stage4}
	}

	if len(additionFilter) > 0 {
		stages = append(stages, bson.M{"$match": additionFilter})
	}
	stages = append(stages, bson.M{
		"$sort": sorting,
	})
	if limit > 0 {
		stages = append(stages, bson.M{
			"$limit": limit,
		}, bson.M{
			"$skip": offset,
		})
	}
	var patientOverviewEntities []*PatientOverviewEntity
	err = repo.profileRepo.IDBClient.Aggregate(ctx, stages, &patientOverviewEntities)
	if err != nil {
		return nil, err
	}
	patientOverviews := []service.PatientOverview{}
	for _, entity := range patientOverviewEntities {
		activeInsurance := patient_profile_common.PatientInsuranceInfos(entity.InsuranceInfos).GetActiveInsurance()
		// if activeInsurance == nil {
		// 	continue
		// }
		insuranceNumber := ""
		insuranceCompanyName := ""
		var ikNumber int32

		if activeInsurance != nil {
			insuranceNumber = pkgUtils.GetPointerValue(activeInsurance.InsuranceNumber)
			insuranceCompanyName = activeInsurance.InsuranceCompanyName
			ikNumber = activeInsurance.IkNumber
		}

		var phoneNumber string
		var address string

		if entity.PrimaryContactNumber != "" {
			phoneNumber = entity.PrimaryContactNumber
		} else if len(entity.FurtherContactNumber) > 0 && *entity.FurtherContactNumber[0] != "" {
			phoneNumber = *entity.FurtherContactNumber[0]
		}

		if entity.Street != "" {
			address += entity.Street
		}

		if entity.Number != "" {
			address += " " + entity.Number
		}

		if entity.Number != "" || entity.Street != "" {
			address += ", "
		}

		if entity.PostalCode != "" {
			address += entity.PostalCode
		}

		if entity.City != "" {
			address += " " + entity.City
		}

		patientOverviews = append(patientOverviews, service.PatientOverview{
			PatientId:     entity.PatientId,
			PatientNumber: entity.PatientNumber,
			InsureNumber:  &insuranceNumber,
			FirstName:     entity.FirstName,
			LastName:      entity.LastName,
			Dob:           entity.Dob,
			InsureName:    insuranceCompanyName,
			IkNumber:      ikNumber,
			CreatedDate:   entity.CreatedDate,
			UpdatedDate:   entity.UpdatedDate,
			DateOfDeath:   entity.DateOfDeath,
			DateOfBirth:   entity.DateOfBirth,
			Title:         entity.Title,
			IntendWord:    entity.IntendWord,
			Gender:        entity.Gender,
			PatientType:   entity.PatientType,
			PhoneNumber:   &phoneNumber,
			Address:       &address,
		})
	}

	return patientOverviews, nil
}
func (repo *Repository) CountPatientProfile(ctx *titan.Context, filterQuery bson.D) (int64, error) {
	return repo.profileRepo.Count(ctx, filterQuery)
}

func (repo *Repository) GetListContract(ctx *titan.Context) ([]string, error) {
	contracts := []string{}
	stage1 := bson.M{
		"$group": bson.M{
			"_id": bson.M{model.Field_ContractId: fmt.Sprintf("$%s", model.Field_ContractId)},
		},
	}
	stage2 := bson.M{
		"$replaceWith": bson.M{
			model.Field_ContractId: fmt.Sprintf("$%s.%s", model.Field_Id, model.Field_ContractId),
		},
	}
	stages := []bson.M{stage1, stage2}
	var contractIDs []ContractID
	err := repo.patientOverviewRepo.Db.Aggregate(ctx, stages, &contractIDs)
	if err != nil {
		return contracts, err
	}
	for index := range contractIDs {
		contracts = append(contracts, contractIDs[index].Id)
	}
	return contracts, nil
}

func (repo *Repository) GetListInsuranceName(ctx *titan.Context) ([]string, error) {
	insuranceNames := []string{}
	patientProfiles, err := repo.profileRepo.Find(ctx, bson.M{})
	if err != nil {
		return []string{}, err
	}
	for _, patientProfile := range patientProfiles {
		if patientProfile.PatientInfo != nil && patientProfile.PatientInfo.InsuranceInfos != nil {
			for _, insurance := range patientProfile.PatientInfo.InsuranceInfos {
				insuranceNames = append(insuranceNames, insurance.InsuranceCompanyName)
			}
		}
	}
	return slice.UniqBy(insuranceNames, func(ins string) string {
		return ins
	}), nil
}

func (repo *Repository) AnalysisAdvanceFilters(ctx *titan.Context, filters []service.FilterData) (map[repoType][]bson.E, error) {
	mapQuery := map[repoType][]bson.E{
		overview: {},
	}
	mapFilterData := map[repoType][]*service.FilterData{
		overview: {},
	}
	for _, filter := range filters {
		nameRepo := mapFieldNameWithTable[filter.FilterName]
		tmpFilters := mapFilterData[nameRepo]
		mapFilterData[nameRepo] = append(tmpFilters, &service.FilterData{
			FilterName:    filter.FilterName,
			CompareMethod: filter.CompareMethod,
			Value:         filter.Value,
		})
	}

	if len(mapFilterData[overview]) > 0 {
		query, err := repo.AnalysisQueryForOverview(ctx, mapFilterData[overview])
		if err != nil {
			return nil, err
		}
		mapQuery[overview] = query
	}
	return mapQuery, nil
}

func (*Repository) AnalysisQueryForOverview(ctx *titan.Context, filters []*service.FilterData) ([]bson.E, error) {
	queryOverview := []bson.E{}
	now := pkgUtils.Now(ctx)
	beginQuarter := pkgUtils.BeginningOfQuarter(now).UnixMilli()
	nowTime := now.UnixMilli()
	for _, filter := range filters {
		var queryElement bson.E
		if filter.FilterName != model.Field_EndDate &&
			filter.FilterName != model.Field_StartDate &&
			filter.FilterName != model.Field_EnrolledDate &&
			filter.FilterName != icdCodes {
			switch filter.CompareMethod {
			case service.Equal:
				if filter.FilterName == model.Field_Status && filter.Value == "" {
					queryOverview = append(queryOverview, bson.E{Key: filter.FilterName, Value: nil})
					continue
				}
				if filter.FilterName == model.Field_DoctorId ||
					filter.FilterName == model.Field_EnrolledBy ||
					filter.FilterName == model.Field_AssigneeId {
					uuidFilter, _ := uuid.Parse(filter.Value)
					queryOverview = append(queryOverview, bson.E{Key: filter.FilterName, Value: uuidFilter})
					continue
				}
				queryElement = bson.E{Key: filter.FilterName, Value: filter.Value}
			case service.NotEqual:
				if filter.FilterName == model.Field_Status && filter.Value == "" {
					queryOverview = append(queryOverview, bson.E{Key: filter.FilterName, Value: bson.D{{Key: "$ne", Value: nil}}})
					continue
				}
				if filter.FilterName == model.Field_DoctorId ||
					filter.FilterName == model.Field_EnrolledBy ||
					filter.FilterName == model.Field_AssigneeId {
					uuidFilter, _ := uuid.Parse(filter.Value)
					queryOverview = append(queryOverview, bson.E{Key: filter.FilterName, Value: bson.D{{Key: "$ne", Value: uuidFilter}}})
					continue
				}
				queryElement = bson.E{Key: filter.FilterName, Value: bson.D{{Key: "$ne", Value: filter.Value}}}
			case service.Includes:
				queryElement = bson.E{Key: filter.FilterName, Value: bson.D{{Key: "$regex", Value: fmt.Sprintf(".*%v.*", filter.Value)}}}
			}
		} else if filter.FilterName == icdCodes {
			switch filter.CompareMethod {
			case service.Equal:
				queryElement = bson.E{
					Key: "$or",
					Value: bson.A{
						bson.M{
							model.Field_VerahRelatedIcds: bson.M{
								"$elemMatch": bson.M{
									model.Field_VerahRelatedIcd_Code:    filter.Value,
									model.Field_VerahRelatedIcd_Chronic: false,
									model.Field_VerahRelatedIcd_CreatedDate: bson.M{
										"$gt": beginQuarter,
									},
								},
							},
						},
						bson.M{
							model.Field_VerahRelatedIcds: bson.M{
								"$elemMatch": bson.M{
									model.Field_VerahRelatedIcd_Code:       filter.Value,
									model.Field_VerahRelatedIcd_Chronic:    true,
									model.Field_VerahRelatedIcd_ValidUntil: nil,
								},
							},
						},
						bson.M{
							model.Field_VerahRelatedIcds: bson.M{
								"$elemMatch": bson.M{
									model.Field_VerahRelatedIcd_Code:    filter.Value,
									model.Field_VerahRelatedIcd_Chronic: true,
									model.Field_VerahRelatedIcd_ValidUntil: bson.M{
										"$gt": nowTime,
									},
								},
							},
						},
					},
				}
			case service.NotEqual:
				queryElement = bson.E{
					Key: "$or",
					Value: bson.A{
						bson.M{
							model.Field_VerahRelatedIcds: bson.M{
								"$elemMatch": bson.M{
									model.Field_VerahRelatedIcd_Code:    bson.D{{Key: "$ne", Value: filter.Value}},
									model.Field_VerahRelatedIcd_Chronic: false,
									model.Field_VerahRelatedIcd_CreatedDate: bson.M{
										"$gt": beginQuarter,
									},
								},
							},
						},
						bson.M{
							model.Field_VerahRelatedIcds: bson.M{
								"$elemMatch": bson.M{
									model.Field_VerahRelatedIcd_Code:       bson.D{{Key: "$ne", Value: filter.Value}},
									model.Field_VerahRelatedIcd_Chronic:    true,
									model.Field_VerahRelatedIcd_ValidUntil: nil,
								},
							},
						},
						bson.M{
							model.Field_VerahRelatedIcds: bson.M{
								"$elemMatch": bson.M{
									model.Field_VerahRelatedIcd_Code:    bson.D{{Key: "$ne", Value: filter.Value}},
									model.Field_VerahRelatedIcd_Chronic: true,
									model.Field_VerahRelatedIcd_ValidUntil: bson.M{
										"$gt": nowTime,
									},
								},
							},
						},
					},
				}
			case service.Includes:
				queryElement = bson.E{
					Key: "$or",
					Value: bson.A{
						bson.M{
							model.Field_VerahRelatedIcds: bson.M{
								"$elemMatch": bson.M{
									model.Field_VerahRelatedIcd_Code:    bson.D{{Key: "$regex", Value: fmt.Sprintf(".*%v.*", filter.Value)}},
									model.Field_VerahRelatedIcd_Chronic: false,
									model.Field_VerahRelatedIcd_CreatedDate: bson.M{
										"$gt": beginQuarter,
									},
								},
							},
						},
						bson.M{
							model.Field_VerahRelatedIcds: bson.M{
								"$elemMatch": bson.M{
									model.Field_VerahRelatedIcd_Code:       bson.D{{Key: "$regex", Value: fmt.Sprintf(".*%v.*", filter.Value)}},
									model.Field_VerahRelatedIcd_Chronic:    true,
									model.Field_VerahRelatedIcd_ValidUntil: nil,
								},
							},
						},
						bson.M{
							model.Field_VerahRelatedIcds: bson.M{
								"$elemMatch": bson.M{
									model.Field_VerahRelatedIcd_Code:    bson.D{{Key: "$regex", Value: fmt.Sprintf(".*%v.*", filter.Value)}},
									model.Field_VerahRelatedIcd_Chronic: true,
									model.Field_VerahRelatedIcd_ValidUntil: bson.M{
										"$gt": nowTime,
									},
								},
							},
						},
					},
				}
			}
		} else {
			var fromTime time.Time
			var toTime time.Time
			if filter.CompareMethod == service.Equal {
				i, err := strconv.ParseInt(filter.Value, 10, 64)
				if err != nil {
					return nil, err
				}
				fromTime = time.Unix(i, 0)
				toTime = fromTime.AddDate(0, 0, 1)
			} else {
				timeList := strings.Split(filter.Value, "-")
				if len(timeList) == 1 {
					from, err := strconv.ParseInt(timeList[0], 10, 64)
					if err != nil {
						return nil, err
					}
					fromTime = time.Unix(from, 0)
					toTime = pkgUtils.Now(ctx)
				} else {
					from, err := strconv.ParseInt(timeList[0], 10, 64)
					if err != nil {
						return nil, err
					}
					fromTime = time.Unix(from, 0)

					to, err := strconv.ParseInt(timeList[1], 10, 64)
					if err != nil {
						return nil, err
					}
					toTime = time.Unix(to, 0)
				}
			}
			queryElement = bson.E{Key: filter.FilterName, Value: bson.D{{Key: "$gte", Value: fromTime.Unix()}, {Key: "$lt", Value: toTime.Unix()}}}
		}
		queryOverview = append(queryOverview, queryElement)
	}
	return queryOverview, nil
}

func InitPatientOverviewRepository(enrollmentRepo *enrollment_repo.Repository, contractService *contract_service.Service) *Repository {
	return &Repository{
		patientOverviewRepo: model.NewPatientOverviewDefaultRepository(),
		insuranceRepo:       insuranceModel.NewHealthInsuranceDefaultRepository(),
		profileRepo:         patient.NewPatientProfileDefaultRepository(),
		scheinRepo:          schein.NewScheinRepoDefaultRepository(),
		SettingsRepo:        userSettingsRepos.NewUserSettingsDefaultRepository(),
		enrollmentRepo:      enrollmentRepo,
		contractService:     contractService,
	}
}

var PatientOverviewRepoMod = submodule.Make[*Repository](InitPatientOverviewRepository,
	enrollment_repo.PatientEnrollmentRepositoryMod,
	contract_service.ContractServiceMod,
)

func ContainIns(insurances []patient_profile_common.InsuranceInfo, name string) bool {
	for _, ins := range insurances {
		if ins.InsuranceCompanyName == name {
			return true
		}
	}
	return false
}

func GetLstInsNameRequest(request service.GetPatientOverviewRequest) []LstInsuranceAdvancedFilter {
	lstInsruance := []LstInsuranceAdvancedFilter{}
	for _, advancedFilter := range request.AdvancedFilters {
		if advancedFilter.FilterName == model.Field_InsuranceName {
			lstInsruance = append(lstInsruance, LstInsuranceAdvancedFilter{name: advancedFilter.Value, compareMethod: advancedFilter.CompareMethod})
		}
	}
	return lstInsruance
}
