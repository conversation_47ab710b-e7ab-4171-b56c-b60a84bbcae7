package schein

import (
	"github.com/google/uuid"
	"gitlab.com/silenteer-oss/titan"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/private_schein_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"
	scheinService "git.tutum.dev/medi/tutum/ares/service/schein"
	"github.com/submodule-org/submodule.go/v2"
)

type ScheinApp struct {
	scheinService *scheinService.ScheinService
}

var ScheinAppMod = submodule.Make[schein.ScheinApp](func(scheinService *scheinService.ScheinService) schein.ScheinApp {
	return &ScheinApp{
		scheinService: scheinService,
	}
}, scheinService.ScheinServiceMod)

func (srv *ScheinApp) SaveSetting(ctx *titan.Context, request schein_common.SaveSettingRequest) error {
	return srv.scheinService.SaveSetting(ctx, request)
}

// ToggleMarkBill implements schein.ScheinApp
func (srv *ScheinApp) MarkBill(ctx *titan.Context, request schein.MarkBillRequest) error {
	return srv.scheinService.MarkBill(ctx, scheinService.MarkBillRequest{
		ContractId:       request.ContractId,
		MainGroup:        request.MainGroup,
		ExcludeScheinIds: request.ExcludeScheinIds,
		ValidScheinsIds:  request.ValidScheinsIds,
	})
}

func (srv *ScheinApp) GetKTABs(ctx *titan.Context, request schein.GetKTABsRequest) (*schein.GetKTABsResponse, error) {
	return srv.scheinService.GetKTABs(ctx, request)
}

// using in reloadSchein.ts
func (srv *ScheinApp) CreateSvScheins(ctx *titan.Context, request schein_common.CreateSvScheinRequest) error {
	return srv.scheinService.CreateSvScheins(ctx, request)
}

func (srv *ScheinApp) IsValid(ctx *titan.Context, req schein.IsValidRequest) (*schein.IsValidResponse, error) {
	return srv.scheinService.IsValid(ctx, req)
}

func (srv *ScheinApp) CreateSchein(ctx *titan.Context, req schein.CreateScheinRequest) (*schein.CreateScheinResponse, error) {
	return srv.scheinService.CreateSchein(ctx, req)
}

func (srv *ScheinApp) TakeOverScheinDiagnosis(ctx *titan.Context, req schein.TakeOverScheinDiagnisToRequest) error {
	return srv.scheinService.TakeOverScheinDiagnosis(ctx, req)
}

func (srv *ScheinApp) GetScheinDetailById(ctx *titan.Context, request schein_common.GetScheinDetailByIdRequest) (*schein_common.GetScheinDetailByIdResponse, error) {
	return srv.scheinService.GetScheinDetailById(ctx, request)
}

func (srv *ScheinApp) GetScheinDetailByIds(ctx *titan.Context, request schein_common.GetScheinDetailByIdsRequest) (*schein_common.GetScheinDetailByIdsResponse, error) {
	return srv.scheinService.GetScheinDetailByIds(ctx, request)
}

func (srv *ScheinApp) UpdateSchein(ctx *titan.Context, request schein_common.UpdateScheinRequest) error {
	return srv.scheinService.UpdateSchein(ctx, request)
}

func (srv *ScheinApp) MarkNotBilled(ctx *titan.Context, request schein_common.MarkNotBilledRequest) error {
	return srv.scheinService.MarkNotBilled(ctx, request)
}

func (srv *ScheinApp) DeleteSchein(ctx *titan.Context, request schein_common.DeleteScheinRequest) error {
	return srv.scheinService.DeleteSchein(ctx, request)
}

func (srv *ScheinApp) GetScheinDetail(ctx *titan.Context, request schein_common.GetScheinDetailRequest) (*schein_common.GetScheinDetailResponse, error) {
	return srv.scheinService.GetScheinDetail(ctx, request)
}

func (srv *ScheinApp) GetFields(ctx *titan.Context, request schein_common.GetFieldsRequest) (*schein_common.GetFieldsResponse, error) {
	return srv.scheinService.GetFields(ctx, request)
}

func (srv *ScheinApp) GetScheinsOverview(ctx *titan.Context, request schein_common.GetScheinsOverviewRequest) (*schein_common.GetScheinsOverviewResponse, error) {
	return srv.scheinService.GetScheinsOverview(ctx, request)
}

func (srv *ScheinApp) CheckExistKVScheinCurrentQuarter(ctx *titan.Context, request schein_common.CheckExistKVScheinCurrentQuarterRequest) (*schein_common.CheckExistKVScheinCurrentQuarterResponse, error) {
	return srv.scheinService.CheckExistKVScheinCurrentQuarter(ctx, request)
}

func (srv *ScheinApp) HandleEventScheinChanged(ctx *titan.Context, request schein.EventScheinChanged) error {
	return srv.scheinService.HandleEventScheinChanged(ctx, request)
}

func (srv *ScheinApp) HandleEventCreateRemoveSchein(ctx *titan.Context, request schein.EventCreateRemoveSchein) error {
	return srv.scheinService.HandleEventCreateRemoveSchein(ctx, request)
}

func (srv *ScheinApp) GetSetting(ctx *titan.Context) (*schein_common.GetSettingResponse, error) {
	return srv.scheinService.GetSetting(ctx)
}

func (srv *ScheinApp) GetSelectedTreatmentCaseSubgroup(ctx *titan.Context, request schein_common.GetSelectedTreatmentCaseSubgroupRequest) (*schein_common.GetSelectedTreatmentCaseSubgroupResponse, error) {
	return srv.scheinService.GetSelectedTreatmentCaseSubgroup(ctx, request)
}

func (srv *ScheinApp) GetOrderList(ctx *titan.Context) (*schein_common.GetOrderListResponse, error) {
	return srv.scheinService.GetOrderList(ctx)
}

func (srv *ScheinApp) SaveOrderList(ctx *titan.Context, request schein_common.SaveOrderListRequest) error {
	return srv.scheinService.SaveOrderList(ctx, request)
}

// CreateServiceCode88194Automatic creates service for KV patient which having Hzv contract
func (srv *ScheinApp) CreateServiceCode88194Automatic(
	ctx *titan.Context,
	scheinId *uuid.UUID,
	contractId *string,
	patientId uuid.UUID,
	doctorId uuid.UUID,
) error {
	return srv.scheinService.CreateServiceCode88194Automatic(ctx, scheinId, contractId, patientId, doctorId)
}

func (srv *ScheinApp) GetSubGroupFromMasterData(ctx *titan.Context, request schein.GetSubGroupFromMasterDataRequest) (*schein.GetSubGroupFromMasterDataResponse, error) {
	return srv.scheinService.GetSubGroupFromMasterData(ctx, request)
}

func (srv *ScheinApp) GetBillingAreaFromMasterData(ctx *titan.Context, request schein.GetBillingAreaFromMasterDataRequest) (*schein.GetBillingAreaFromMasterDataResponse, error) {
	return srv.scheinService.GetBillingAreaFromMasterData(ctx, request)
}

func (srv *ScheinApp) GetRezidivList(ctx *titan.Context) (*schein.GetRezidivListResponse, error) {
	return srv.scheinService.GetRezidivList(ctx)
}

func (app *ScheinApp) GetPsychotherapyById(ctx *titan.Context, request schein.GetPsychotherapyByIdRequest) (*schein.GetPsychotherapyByIdResponse, error) {
	return app.scheinService.GetPsychotherapyById(ctx, request)
}

func (app *ScheinApp) GetScheinByInsuranceId(ctx *titan.Context, request schein.GetScheinByInsuranceIdRequest) (*schein.GetScheinByInsuranceIdResponse, error) {
	return app.scheinService.GetScheinByInsuranceId(ctx, request)
}
func (app *ScheinApp) GetScheinsByInsuranceId(ctx *titan.Context, request schein.GetScheinByInsuranceIdRequest) (*schein.GetScheinsByInsuranceIdResponse, error) {
	return app.scheinService.GetScheinsByInsuranceId(ctx, request)
}

func (app *ScheinApp) RevertTechnicalSchein(ctx *titan.Context, request schein.RevertTechnicalScheinRequest) error {
	return app.scheinService.RevertTechnicalSchein(ctx, request)
}

func (app *ScheinApp) CreatePrivateSchein(ctx *titan.Context, request schein.CreatePrivateScheinRequest) (*private_schein_common.PrivateScheinItem, error) {
	return app.scheinService.CreatePrivateSchein(ctx, request)
}

func (app *ScheinApp) UpdatePrivateSchein(ctx *titan.Context, request schein.UpdatePrivateScheinRequest) (*private_schein_common.PrivateScheinItem, error) {
	return app.scheinService.UpdatePrivateSchein(ctx, request)
}

func (app *ScheinApp) DeletePrivateSchein(ctx *titan.Context, request schein.DeletePrivateScheinRequest) error {
	return app.scheinService.DeletePrivateSchein(ctx, request)
}

func (app *ScheinApp) GetPrivateScheinById(ctx *titan.Context, request schein.GetPrivateScheinByIdRequest) (*private_schein_common.PrivateScheinItem, error) {
	return app.scheinService.GetPrivateScheinById(ctx, request.ScheinId)
}

func (app *ScheinApp) GetGoaFactorValue(ctx *titan.Context, request schein.GetGoaFactorValueRequest) (*schein.GetGoaFactorValueResponse, error) {
	return app.scheinService.GetGoaFactorValue(ctx, request)
}

func (app *ScheinApp) TakeOverDiagnosisByScheinId(ctx *titan.Context, request schein.TakeOverDiagnosisByScheinIdRequest) error {
	return app.scheinService.TakeOverDiagnosisByScheinId(ctx, request)
}

func (app *ScheinApp) IsValidPrivateSchein(ctx *titan.Context, request schein.IsValidPrivateScheinRequest) (*schein.IsValidResponse, error) {
	return app.scheinService.IsValidPrivateSchein(ctx, request)
}

func (app *ScheinApp) GetScheinItemById(ctx *titan.Context, request schein.GetScheinItemByIdRequest) (*schein.GetScheinItemByIdResponse, error) {
	res, err := app.scheinService.GetScheinItemById(ctx, request.ScheinId)
	if err != nil {
		return nil, err
	}
	if res == nil {
		return nil, nil
	}
	return &schein.GetScheinItemByIdResponse{
		ScheinItems: *res,
	}, nil
}

func (app *ScheinApp) GetScheinItemByIds(ctx *titan.Context, request schein.GetScheinItemByIdsRequest) (*schein.GetScheinItemByIdsResponse, error) {
	res, err := app.scheinService.GetScheinItemByIds(ctx, request.ScheinIds)
	if err != nil {
		return nil, err
	}
	if res == nil {
		return nil, nil
	}
	return &schein.GetScheinItemByIdsResponse{
		ScheinItems: res,
	}, nil
}

func (app *ScheinApp) CreateBgSchein(ctx *titan.Context, request schein.CreateBgScheinRequest) (*schein_common.BgScheinItem, error) {
	return app.scheinService.CreateBgSchein(ctx, request)
}
func (app *ScheinApp) UpdateBgSchein(ctx *titan.Context, request schein.UpdateBgScheinRequest) (*schein_common.BgScheinItem, error) {
	return app.scheinService.UpdateBgSchein(ctx, request)
}
func (app *ScheinApp) DeleteBgSchein(ctx *titan.Context, request schein.DeleteBgScheinRequest) error {
	return app.scheinService.DeleteBgSchein(ctx, request)
}
func (app *ScheinApp) GetBgScheinById(ctx *titan.Context, request schein.GetBgScheinByIdRequest) (*schein_common.BgScheinItem, error) {
	return app.scheinService.GetBgScheinById(ctx, request.ScheinId)
}
func (app *ScheinApp) IsValidBgSchein(ctx *titan.Context, request schein.IsValidBgScheinRequest) (*schein.IsValidResponse, error) {
	return app.scheinService.IsValidBgSchein(ctx, request)
}

// NOTE: Unuse!
func (app *ScheinApp) CreateSvScheinAutomaticly(ctx *titan.Context, request schein_common.CreateSvScheinAutomaticlyRequest) (*schein_common.CreateSvScheinAutomaticlyResponse, error) {
	return app.scheinService.CreateSvScheinAutomaticly(ctx, request)
}

func (app *ScheinApp) CreateSvScheinFromReference(ctx *titan.Context, request schein_common.CreateSvScheinFromReferenceRequest) (*schein_common.CreateSvScheinFromReferenceResponse, error) {
	return app.scheinService.CreateSvScheinFromReference(ctx, request)
}

func (app *ScheinApp) CreateSvScheinManually(ctx *titan.Context, request schein_common.CreateSvScheinManuallyRequest) (*schein_common.CreateSvScheinManuallyResponse, error) {
	return app.scheinService.CreateSvScheinManually(ctx, request)
}

func (app *ScheinApp) UpdateSvSchein(ctx *titan.Context, request schein_common.UpdateSvScheinRequest) (*schein_common.UpdateSvScheinResponse, error) {
	return app.scheinService.UpdateSvSchein(ctx, request)
}

func (app *ScheinApp) MarkAsReferral(ctx *titan.Context, request schein_common.MarkAsReferralRequest) error {
	return app.scheinService.MarkAsReferral(ctx, request)
}

func (app *ScheinApp) RemoveReferral(ctx *titan.Context, request schein_common.RemoveReferralRequest) error {
	return app.scheinService.RemoveReferral(ctx, request)
}

func (app *ScheinApp) CheckDummyVknr(ctx *titan.Context, request schein.CheckDummyVknrRequest) (*schein.CheckDummyVknrResponse, error) {
	return app.scheinService.CheckDummyVknr(ctx, request)
}

func (app *ScheinApp) GetTotalScheins(ctx *titan.Context, request schein.GetTotalScheinsRequest) (*schein.GetTotalScheinsResponse, error) {
	return app.scheinService.GetTotalScheins(ctx, request)
}
