package timeline

import (
	"fmt"
	"time"

	"emperror.dev/errors"
	"github.com/google/uuid"
	"github.com/submodule-org/submodule.go/v2"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/erezept"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/timeline"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/pkg/hook"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/master_data_common"
	audit_log_service "git.tutum.dev/medi/tutum/ares/service/domains/audit_log/service"
	eab_service "git.tutum.dev/medi/tutum/ares/service/domains/eab/service"
	eab_service_history_srv "git.tutum.dev/medi/tutum/ares/service/domains/eab_service_history/service"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/timeline_service"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/validation_timeline"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
)

type TimelineApp struct {
	timelineService           *timeline_service.TimelineService[any]
	timelineValidationService *validation_timeline.TimelineValidationService
	timelineNotifier          *timeline.TimelineNotifier
	notifier                  *timeline.TimelineSocketNotifier
	auditLogService           audit_log_service.AuditLogger
	eabService                *eab_service.EABService
	eabServiceHistorySrv      *eab_service_history_srv.EABServiceHistoryService
}

func (app *TimelineApp) DocumentSuggestion(ctx *titan.Context, request timeline.DocumentSuggestionRequest) error {
	return app.timelineService.DocumentSuggestion(ctx, timeline_service.DocumentSuggestion{
		TimelineId:     request.TimelineId,
		SuggestionCode: request.SuggestionCode,
		SuggestionData: request.SuggestionData,
	})
}

func (app *TimelineApp) MarkTreatmentRelevant(ctx *titan.Context, request timeline.MarkTreatmentRelevantRequest) error {
	return app.timelineService.MarkTreatmentRelevant(ctx, request.TimelineId)
}

func (app *TimelineApp) UpdateEncounterCaseForServiceEntries(ctx *titan.Context, request timeline.UpdateEncounterCaseForServiceEntriesRequest) error {
	return app.timelineService.UpdateEncounterCaseForServiceEntries(ctx, request.TimelineId)
}

func (app *TimelineApp) UpdateMany(ctx *titan.Context, request timeline.UpdateManyRequest) error {
	if len(request.TimelineModels) == 0 {
		return nil
	}

	isMissingId := slice.Any(request.TimelineModels, func(tm common.TimelineModel) bool {
		return tm.Id == nil
	})

	if isMissingId {
		return errors.New("missing entry id")
	}
	entities := ConvertModelsToEntities(request.TimelineModels)
	_, err := app.timelineService.UpdateMany(ctx, entities)
	if err != nil {
		return fmt.Errorf("failed to update many timeline: %w", err)
	}
	return nil
}

func (app *TimelineApp) GetTakeOverDiagnosis(ctx *titan.Context, request timeline.GetTakeOverDiagnosisRequest) (*timeline.GetTakeOverDiagnosisResponse, error) {
	req := &timeline_service.GetTakeOverDiagnosisRequest{
		PatientId: request.PatientId,
		Query:     util.GetPointerValue(request.Query),
		StartDate: function.Do(func() *time.Time {
			if request.FromDate == nil {
				return nil
			}
			return util.NewPointer(util.ConvertMillisecondsToTime(*request.FromDate, ctx.RequestTimeZone()))
		}),
		EndDate: function.Do(func() *time.Time {
			if request.ToDate == nil {
				return nil
			}
			return util.NewPointer(util.ConvertMillisecondsToTime(*request.ToDate, ctx.RequestTimeZone()))
		}),
		ScheinId: request.ScheinId,
	}

	res, err := app.timelineService.GetTakeOverDiagnosis(ctx, req)
	if err != nil {
		return nil, err
	}

	if res == nil {
		return &timeline.GetTakeOverDiagnosisResponse{
			TakeOverDiagnosisGroup: nil,
		}, nil
	}

	return &timeline.GetTakeOverDiagnosisResponse{
		TakeOverDiagnosisGroup: res.Data,
	}, nil
}

// GetPreParticipationServiceCodes implements timeline.TimelineApp.
func (app *TimelineApp) GetPreParticipationServiceCodes(ctx *titan.Context, request timeline.GetPreParticipationServiceCodesRequest) (*timeline.GetTimelineEntryIdsResponse, error) {
	rs, err := app.timelineService.GetPreParticipationServiceCodes(ctx, request.PatientId)
	if err != nil {
		return nil, err
	}

	timelineModels := []common.TimelineModel{}
	for _, v := range rs {
		timelineModel := timeline_service.ConvertEntityAnyToModel(v)
		timelineModels = append(timelineModels, timelineModel)
	}
	return &timeline.GetTimelineEntryIdsResponse{
		TimelineModels: timelineModels,
	}, nil
}

// DeleteErezept implements timeline.TimelineApp
func (app *TimelineApp) DeleteErezept(ctx *titan.Context, request timeline.DeleteErezeptRequest) error {
	_, err := app.timelineService.DeleteErezept(ctx, request.PatientId, request.MedicineId)
	return err
}

// IgnoreSdkrwRule implements timeline.TimelineApp
func (app *TimelineApp) IgnoreSdkrwRule(ctx *titan.Context, request timeline.IgnoreSdkrwRuleRequest) error {
	return app.timelineService.IgnoreSuggestionRule(ctx, request.PatientId, request.RuleId, request.EncounterDate)
}

// UpdateSuggestionRuleApplied implements timeline.TimelineApp
func (app *TimelineApp) UpdateSuggestionRuleApplied(ctx *titan.Context, request timeline.UpdateSuggestionRuleAppliedRequest) error {
	return app.timelineService.UpdateSuggestionRuleApplied(ctx, *request.TimelineId, request.RuleId)
}

// GetByIds implements timeline.TimelineApp
func (app *TimelineApp) GetByIds(ctx *titan.Context, request timeline.GetByIdsRequest) (*timeline.GetResponse, error) {
	rs, err := app.timelineService.FindByIds(ctx, request.TimelineIds)
	if err != nil {
		return nil, err
	}

	timelineModels := []common.TimelineModel{}
	for _, v := range rs {
		timelineModel := timeline_service.ConvertEntityAnyToModel(v)
		timelineModels = append(timelineModels, timelineModel)
	}
	return &timeline.GetResponse{
		TimelineModels: timelineModels,
	}, nil
}

// GetDiagnoseByPatientId implements timeline.TimelineApp
func (app *TimelineApp) GetDiagnose(ctx *titan.Context, request timeline.GetDiagnoseRequest) (*timeline.GetDiagnoseResponse, error) {
	results, err := app.timelineService.GetDiagnoseUnique(ctx, timeline_repo.GetDiagnoseUniqueRequest{
		PatientId: request.PatientId,
		Codes:     request.Codes,
	})
	if err != nil {
		return nil, err
	}
	if len(results) == 0 {
		return &timeline.GetDiagnoseResponse{}, nil
	}

	var encounterDiagnoseTimeline []patient_encounter.EncounterDiagnoseTimeline
	for _, v := range results {
		encounterDiagnoseTimeline = append(encounterDiagnoseTimeline, patient_encounter.EncounterDiagnoseTimeline{
			Code:                    v.Code,
			Description:             v.Description,
			Type:                    v.Type,
			ValidUntil:              v.ValidUntil,
			Certainty:               v.Certainty,
			Laterality:              v.Laterality,
			Hib:                     v.Hib,
			Mrsa:                    v.Mrsa,
			Imported:                v.Imported,
			FreeText:                v.FreeText,
			Errors:                  v.Errors,
			Command:                 v.Command,
			Group:                   v.Group,
			Sources:                 v.Sources,
			MorbiRsa:                v.MorbiRsa,
			AuditLog:                v.AuditLog,
			Scheins:                 v.Scheins,
			DiagnoseSuggestions:     v.DiagnoseSuggestions,
			RunSdkrw:                v.RunSdkrw,
			Exception:               v.Exception,
			Explanation:             v.Explanation,
			SdvaRefs:                v.SdvaRefs,
			MarkedTreatmentRelevant: v.MarkedTreatmentRelevant,
			StartDate:               v.StartDate,
		})
	}

	return &timeline.GetDiagnoseResponse{
		EncounterDiagnoseTimeline: encounterDiagnoseTimeline,
	}, nil
}

// GetById implements timeline.TimelineApp
func (app *TimelineApp) GetById(ctx *titan.Context, request timeline.GetByIdRequest) (*timeline.GetByIdResponse, error) {
	result, err := app.timelineService.FindById(ctx, request.TimelineId)
	if err != nil {
		return nil, err
	}

	if result == nil {
		return &timeline.GetByIdResponse{}, nil
	}

	timelineModel := timeline_service.ConvertEntityAnyToModel(*result)
	return &timeline.GetByIdResponse{
		TimelineModel: &timelineModel,
	}, nil
}

func (app *TimelineApp) GroupByQuarter(ctx *titan.Context, request timeline.GroupByQuarterRequest) (*timeline.GroupByQuarterResponse, error) {
	res, err := app.timelineService.GroupByQuarter(ctx, timeline_service.GroupByQuarterRequest{
		PatientId:           request.PatientId,
		TimelineEntityTypes: request.TimelineEntityTypes,
		IsSortByCategory:    request.IsSortByCategory,
		IsHistoryMode:       request.IsHistoryMode,
		ScheinId:            request.ScheinId,
		FromDate:            request.FromDate,
		ToDate:              request.ToDate,
		Keyword:             request.Keyword,
		Year:                request.Year,
		Quarter:             request.Quarter,
	})
	if err != nil {
		return nil, err
	}

	groupByQuarters := []timeline.GroupByQuarter{}
	if len(res.Timelines) == 0 {
		if request.Year != nil && request.Quarter != nil {
			groupByQuarters = append(groupByQuarters, timeline.GroupByQuarter{
				Quarter:        *request.Quarter,
				Year:           *request.Year,
				TimelineModels: []common.TimelineModel{},
			})
		}
		return &timeline.GroupByQuarterResponse{
			GroupByQuarters: groupByQuarters,
		}, nil
	}

	// curChain store entity with the same chainId
	curChain := []timeline_repo.TimelineEntity[any]{}
	for _, v := range res.Timelines {
		group := timeline.GroupByQuarter{
			Quarter: int32(v.Quarter),
			Year:    int32(v.Year),
		}

		for _, entity := range v.TimelineEntities {
			if entity.ChainId != nil {
				if len(curChain) == 0 || *curChain[0].ChainId == *entity.ChainId {
					curChain = append(curChain, entity)
				} else {
					group.TimelineModels = append(group.TimelineModels, timeline_service.ConvertEntityChainToModel(curChain))
					curChain = []timeline_repo.TimelineEntity[any]{entity}
				}
				continue
			} else if len(curChain) > 0 {
				group.TimelineModels = append(group.TimelineModels, timeline_service.ConvertEntityChainToModel(curChain))
				curChain = curChain[:0]
			}
			model := timeline_service.ConvertEntityAnyToModel(entity)
			if *model.Type == common.TimelineEntityType_Diagnose {
				model = timeline_service.MapDiagnoseSuggestionByCheckTime(&model, master_data_common.CheckTime_Coding)
			}
			if *model.Type == common.TimelineEntityType_MedicinePrescription && entity.BillingInfo != nil {
				model.EncounterMedicinePrescription.BillingInfo = entity.BillingInfo
			}
			group.TimelineModels = append(group.TimelineModels, model)
		}
		if len(curChain) > 0 {
			group.TimelineModels = append(group.TimelineModels, timeline_service.ConvertEntityChainToModel(curChain))
			curChain = curChain[:0]
		}
		groupByQuarters = append(groupByQuarters, group)
	}

	if len(groupByQuarters) == 0 {
		return &timeline.GroupByQuarterResponse{
			GroupByQuarters: groupByQuarters,
			TotalPage:       0,
		}, nil
	}

	totalPage := 0
	if request.Pagination != nil && request.Pagination.Page != 0 && request.Pagination.PageSize != 0 {
		slice.SortBy(groupByQuarters, func(i, j timeline.GroupByQuarter) bool {
			return i.Year > j.Year || (i.Year == j.Year && i.Quarter > j.Quarter) // Descending
		})
		groupByQuarters, totalPage = slice.Paging(groupByQuarters, int(request.Pagination.PageSize), int(request.Pagination.Page))
	}

	return &timeline.GroupByQuarterResponse{
		GroupByQuarters: groupByQuarters,
		TotalPage:       int32(totalPage),
		MatchedTokens:   res.MatchedTokens,
	}, nil
}

func ConvertModelsToEntities(models []common.TimelineModel) []timeline_repo.TimelineEntity[any] {
	var entities []timeline_repo.TimelineEntity[any]
	for _, model := range models {
		entities = append(entities, timeline_service.ConvertModelToEntity[any](model))
	}
	return entities
}

// Create implements timeline.TimelineApp
func (app *TimelineApp) Create(ctx *titan.Context, request timeline.CreateRequest) (*timeline.CreateResponse, error) {
	// handle case service chain
	if isServiceChain(request.TimelineModel) {
		entities := convertServiceChainModelToEntity(request.TimelineModel)
		entries, err := app.timelineService.CreateMany(ctx, entities)
		if err != nil {
			return nil, err
		}
		return &timeline.CreateResponse{
			TimelineModel: timeline_service.ConvertEntityChainToModel(entries),
		}, nil
	}
	createRequest := timeline_service.ConvertModelToEntity[any](request.TimelineModel)
	timelineEntity, err := app.timelineService.Create(ctx, createRequest)
	if err != nil {
		return nil, err
	}
	if timelineEntity == nil {
		return nil, nil
	}

	timelineModel := timeline_service.ConvertEntityAnyToModel(*timelineEntity)

	return &timeline.CreateResponse{
		TimelineModel: timelineModel,
	}, nil
}

// Delete implements timeline.TimelineApp
func (app *TimelineApp) Remove(ctx *titan.Context, request timeline.RemoveRequest) (*timeline.RemoveResponse, error) {
	if util.GetPointerValue(request.IsChain) {
		err := app.timelineService.RemoveChain(ctx, *request.TimelineId, util.GetPointerValue(request.HasHardDelete))
		return nil, err
	}
	err := app.timelineService.Remove(ctx, *request.TimelineId, util.GetPointerValue(request.HasHardDelete))
	if err != nil {
		return nil, err
	}

	return nil, nil
}

// Edit implements timeline.TimelineApp
func (app *TimelineApp) Edit(ctx *titan.Context, request timeline.EditRequest) (*timeline.EditResponse, error) {
	if request.TimelineModel.Id == nil {
		return nil, errors.WithStack(fmt.Errorf("missing entry id"))
	}
	// handle case service-chain
	if isServiceChain(request.TimelineModel) {
		err := app.timelineService.DeleteServiceChain(ctx, *request.TimelineModel.Id)
		if err != nil {
			return nil, fmt.Errorf("failed to delete timeline entry: %w", err)
		}
		request.TimelineModel.Id = nil
		entities := convertServiceChainModelToEntity(request.TimelineModel)
		entries, err := app.timelineService.CreateMany(ctx, entities)
		if err != nil {
			return nil, err
		}
		return &timeline.EditResponse{
			TimelineModel: timeline_service.ConvertEntityChainToModel(entries),
		}, nil
	}

	editRequest := timeline_service.ConvertModelToEntity[any](request.TimelineModel)
	timelineEntity, err := app.timelineService.Edit(ctx, editRequest)
	if err != nil {
		return nil, fmt.Errorf("failed to edit timeline: %w", err)
	}
	if timelineEntity == nil {
		return nil, nil
	}

	timelineModel := timeline_service.ConvertEntityAnyToModel(*timelineEntity)

	return &timeline.EditResponse{
		TimelineModel: timelineModel,
	}, nil
}

// updatePrintDate handles all kinds of updatting printDate field
// will support Form, Himi, Heimi....
func (app *TimelineApp) UpdatePrintDate(ctx *titan.Context, request timeline.UpdatePrintDateRequest) (res *timeline.UpdatePrintDateResponse, err error) {
	if request.Type == common.TimelineEntityType_HimiPrescription {
		_, err = app.timelineService.UpdateHimiPrintDate(ctx, *request.TimelineId, request.PrintDate) // Single item
		return
	}

	if request.Type == common.TimelineEntityType_HeimiPrescription {
		_, err = app.timelineService.UpdateHeimiPrintDate(ctx, *request.FormId, request.PrintDate) // Single item
		return
	}

	return
}

// Get implements timeline.TimelineApp
func (app *TimelineApp) Get(ctx *titan.Context, request timeline.GetRequest) (*timeline.GetResponse, error) {
	timelineEntities, err := app.timelineService.Get(ctx, timeline_repo.GetRequest{
		PatientId:  request.PatientId,
		ContractId: request.ContractId,
		CreatedDate: function.Do(func() *time.Time {
			if request.CreatedDate == nil {
				return nil
			}
			return util.NewPointer(util.ConvertMillisecondsToTime(*request.CreatedDate))
		}),
		EncounterCase:       request.EncounterCase,
		TreatmentDoctorId:   request.TreatmentDoctorId,
		TimelineEntityTypes: request.Types,
	})
	if err != nil {
		return nil, err
	}
	if len(timelineEntities) == 0 {
		return nil, nil
	}
	var timelineModels []common.TimelineModel
	for _, timelineEntity := range timelineEntities {
		timelineModels = append(timelineModels, timeline_service.ConvertEntityAnyToModel(timelineEntity))
	}
	return &timeline.GetResponse{
		TimelineModels: timelineModels,
	}, nil
}

func (app *TimelineApp) GetTherapies(ctx *titan.Context, request timeline.GetTherapiesRequest) (*timeline.GetTherapiesResponse, error) {
	return app.timelineService.GetTherapies(ctx, request)
}

func (app *TimelineApp) GetAmountServiceCode(ctx *titan.Context, request timeline.GetAmountServiceCodeRequest) (*timeline.GetAmountServiceCodeResponse, error) {
	return app.timelineService.GetAmountServiceCode(ctx, request)
}

func (app *TimelineApp) MarkNotApprovedPyschotherapy(ctx *titan.Context, request timeline.MarkNotApprovedPyschotherapyRequest) error {
	entry, err := app.timelineService.MarkNotApprovedPyschotherapyForTimelineEntry(ctx, request)
	if err != nil {
		return err
	}
	model := timeline_service.ConvertEntityAnyToModel(*entry)
	if err := app.timelineService.MarkNotApprovedPyschotherapy(ctx, &model); err != nil {
		return err
	}
	if err := app.timelineService.HandleUpdateAmountPyschotherapy(ctx, &model, false); err != nil {
		return err
	}
	return app.notifier.NotifyCareProviderTimelineUpdate(ctx, &timeline.EventTimelineUpdate{
		PatientId:     request.PatientId,
		TimelineModel: common.TimelineModel{},
	})
}

func (*TimelineApp) HandleEventTimelineHardRemove(*titan.Context, timeline.EventTimelineHardRemove) error {
	return nil
}

func (app *TimelineApp) HandleEventTimelineRemove(ctx *titan.Context, request timeline.EventTimelineRemove) error {
	return app.notifier.NotifyCareProviderTimelineRemove(ctx, &request)
}

func (app *TimelineApp) HandleEventTimelineCreate(ctx *titan.Context, request timeline.EventTimelineCreate) error {
	return app.notifier.NotifyCareProviderTimelineCreate(ctx, &request)
}

func (app *TimelineApp) HandleEventTimelineUpdate(ctx *titan.Context, request timeline.EventTimelineUpdate) error {
	return app.notifier.NotifyCareProviderTimelineUpdate(ctx, &request)
}

func (app *TimelineApp) HandleEventTimelineValidation(ctx *titan.Context, request timeline.EventTimelineValidation) error {
	return app.notifier.NotifyCareProviderTimelineValidation(ctx, &request)
}

func (app *TimelineApp) HandleEventAutoAction(ctx *titan.Context, request timeline.EventAutoAction) error {
	return app.notifier.NotifyCareProviderAutoAction(ctx, &request)
}

func (srv *TimelineApp) RestoreEntryHistory(ctx *titan.Context, request timeline.RestoreEntryHistoryRequest) (*timeline.RestoreEntryHistoryResponse, error) {
	_, err := srv.timelineService.RestoreFromLogId(ctx, request.AuditLogId)
	if err != nil {
		return nil, fmt.Errorf("failed to edit timeline: %w", err)
	}
	return nil, nil
}

func (srv *TimelineApp) ValidateDiagnose(ctx *titan.Context, request timeline.ValidateDiagnoseRequest) (*timeline.ValidateDiagnoseResponse, error) {
	res, err := srv.timelineService.ValidateDiagnose(ctx, &timeline_service.ValidateDiagnoseRequest{
		IcdCode:   request.IcdCode,
		TypeCheck: request.TypeCheck,
	})
	if err != nil {
		return nil, err
	}

	return &timeline.ValidateDiagnoseResponse{
		Results: res,
	}, nil
}

func (srv *TimelineApp) ReRunValidateService(ctx *titan.Context, request timeline.ReRunValidateServiceRequest) error {
	return srv.timelineValidationService.RunValidation(ctx, request.PatientId, request.ContractId, common.TimelineEntityType_Service, nil)
}

func (app *TimelineApp) GetTimelineEntryByIds(ctx *titan.Context, request timeline.GetTimelineEntryByIdsRequest) (*timeline.GetTimelineEntryIdsResponse, error) {
	entries, err := app.timelineService.FindByIds(ctx, request.EntryIds)
	if err != nil {
		return nil, err
	}
	return &timeline.GetTimelineEntryIdsResponse{
		TimelineModels: slice.Map(entries, func(t timeline_repo.TimelineEntity[any]) common.TimelineModel {
			return timeline_service.ConvertEntityAnyToModel(t)
		}),
	}, nil
}

func (app *TimelineApp) Document88130(ctx *titan.Context, request timeline.Document88130Request) (*timeline.Document88130Response, error) {
	return app.timelineService.Document88130(ctx, &request)
}

func (app *TimelineApp) GetPsychotherapyTakeOver(ctx *titan.Context, request timeline.GetPsychotherapyTakeOverRequest) (*timeline.GetPsychotherapyTakeOverResponse, error) {
	return app.timelineService.GetPsychotherapyTakeOver(ctx, request)
}

func (app *TimelineApp) TakeoverServiceTerminalApproval(ctx *titan.Context, request timeline.TakeoverServiceTerminalApprovalRequest) error {
	return app.timelineService.TakeoverServiceTerminalApproval(ctx, request)
}

func (app *TimelineApp) GetPsychotherapyBefore2020(ctx *titan.Context, request timeline.GetPsychotherapyBefore2020Request) (*timeline.GetPsychotherapyBefore2020Response, error) {
	return app.timelineService.GetPsychotherapyBefore2020(ctx, request)
}

func (app *TimelineApp) TakeOverDiagnosisWithScheinId(ctx *titan.Context, request timeline.TakeOverDiagnosisWithScheinIdRequest) error {
	_, err := app.timelineService.TakeOverDiagnosisWithScheinId(ctx, request)
	return err
}

func (app *TimelineApp) MarkAcceptedByKV(ctx *titan.Context, request timeline.MarkAcceptedByKVRequest) error {
	return app.timelineService.MarkAcceptedByKV(ctx, request)
}

func (app *TimelineApp) RollbackDocumentTerminateService(ctx *titan.Context, request timeline.RollbackDocumentTerminateServiceRequest) error {
	return app.timelineService.RollbackDocumentTerminateService(ctx, request)
}

func (app *TimelineApp) GetLastDocumentedQuarter(ctx *titan.Context, request timeline.GetLastDocumentedQuarterRequest) (*timeline.GetLastDocumentedQuarterResponse, error) {
	res, err := app.timelineService.GetLastDocumentedQuarter(ctx, &timeline_service.GetLastDocumentedQuarterRequest{
		PatientId:          request.PatientId,
		TimelineEntityType: request.TimelineEntityType,
		Year:               request.Year,
		Quarter:            request.Quarter,
	})
	if err != nil {
		return nil, err
	}

	if res == nil {
		return nil, nil
	}

	return &timeline.GetLastDocumentedQuarterResponse{
		Year:    res.Year,
		Quarter: res.Quarter,
	}, nil
}

func (app *TimelineApp) GetTimelineByEnrollmentId(ctx *titan.Context, request timeline.GetTimelineByEnrollmentIdRequest) (*timeline.GetTimelineByEnrollmentIdResponse, error) {
	return app.timelineService.GetTimelineByEnrollmentId(ctx, request)
}

func (app *TimelineApp) ReSendEABMail(ctx *titan.Context, request timeline.ReSendEABMailRequest) error {
	return app.eabService.ReSendMail(ctx, request.TimelineId)
}

func (app *TimelineApp) DocumentEABServiceCode(ctx *titan.Context, request timeline.DocumentEABServiceCodeRequest) (*timeline.DocumentEABServiceCodeResponse, error) {
	res, err := app.eabServiceHistorySrv.DocumentEABServiceCodes(ctx, &eab_service_history_srv.DocumentServiceRequest{
		ScheinId:  request.ScheinId,
		PatientId: request.PatientId,
		BsnrCode:  request.BsnrCode,
	})
	if err != nil {
		return nil, err
	}

	return &timeline.DocumentEABServiceCodeResponse{
		ServiceCodes: res,
	}, nil
}

func (app *TimelineApp) GetActionChainDiagnoseByCodes(ctx *titan.Context, request timeline.GetActionChainDiagnoseByCodesRequest) (*timeline.GetActionChainDiagnoseByCodesResponse, error) {
	res, err := app.timelineService.GetActionChainDiagnoseByCodes(ctx, timeline.GetActionChainDiagnoseByCodesRequest{
		Codes: request.Codes,
	})
	if err != nil {
		return nil, err
	}

	return res, nil
}

func (app *TimelineApp) FindLatestTimelineEntry(ctx *titan.Context, request timeline.FindLatesTimelineEntryRequest) (*timeline.FindLatesTimelineEntryResponse, error) {
	return app.timelineService.FindLatestTimelineEntry(ctx, request)
}

func (app *TimelineApp) CheckIsVSST785(ctx *titan.Context, request timeline.CheckIsVSST785Request) (*timeline.CheckIsVSST785Response, error) {
	return app.timelineService.CheckIsVSST785(ctx, request)
}

func (app *TimelineApp) GetDoctorLetterById(ctx *titan.Context, request timeline.GetDoctorLetterByIdRequest) (*timeline.GetDoctorLetterByIdResponse, error) {
	res, err := app.timelineService.GetDoctorLetterById(ctx, request.DoctorLetterId)
	if err != nil {
		return nil, err
	}
	if res == nil {
		return nil, nil
	}
	timelineModel := timeline_service.ConvertEntityAnyToModel(*res)
	return &timeline.GetDoctorLetterByIdResponse{
		TimelineModel: &timelineModel,
	}, nil
}

var TimelineAppMod = submodule.Make[timeline.TimelineApp](
	NewTimelineApp,
	config.SocketServiceClientMod,
	timeline_service.TimelineServiceAnyMod,
	validation_timeline.TimelineValidationServiceMod,
	audit_log_service.AuditLogServiceMod,
	eab_service.EABServiceMod,
	eab_service_history_srv.EABServiceHistoryServiceMod,
	config.GetDefaultTitanClientMod,
)

func NewTimelineApp(
	socket *socket_api.SocketServiceClient,
	timelineService *timeline_service.TimelineService[any],
	timelineValidationService *validation_timeline.TimelineValidationService,
	auditLogService audit_log_service.AuditLogger,
	eabService *eab_service.EABService,
	eabServiceHistorySrv *eab_service_history_srv.EABServiceHistoryService,
	client *titan.Client,
) timeline.TimelineApp {
	t := &TimelineApp{
		timelineService:           timelineService,
		timelineValidationService: timelineValidationService,
		timelineNotifier:          timeline.NewTimelineNotifier(),
		notifier:                  timeline.NewTimelineSocketNotifier(socket),
		auditLogService:           auditLogService,
		eabService:                eabService,
		eabServiceHistorySrv:      eabServiceHistorySrv,
	}
	hook.NewNatsEventWithQueueName[erezept.EventErezeptChanged](
		string(erezept.EVENT_ErezeptChanged),
		string(erezept.EVENT_ErezeptChanged)+"timeline_queue",
		client,
	).RegisterFunc(func(ctx *titan.Context, eec erezept.EventErezeptChanged) error {
		var err error
		for _, v := range eec.ErezeptItems {
			if v.MedicineInfo.Id == nil || v.Status == "" {
				continue
			}

			_, err = timelineService.UpdateMedicineERezeptStatusById(ctx, v.Patient.Id, *v.MedicineInfo.Id, v.Status)
			if err != nil {
				ctx.Logger().Error("update erezept status error", "err", err.Error())
				return err
			}
		}
		return nil
	})
	return t
}

func convertServiceChainModelToEntity(m common.TimelineModel) []timeline_repo.TimelineEntity[any] {
	entities := []timeline_repo.TimelineEntity[any]{}
	chainId := m.Id
	if chainId == nil {
		chainId = util.NewPointer(uuid.New())
	}
	if m.EncounterServiceChain != nil {
		for i, svc := range m.EncounterServiceChain.Services {
			m.EncounterServiceTimeline = &svc
			entity := timeline_service.ConvertModelToEntity[any](m)
			entity.ChainId = chainId
			// save editorState to the first service code entry
			if i == 0 {
				entity.EncounterServiceChainRaw = &m.EncounterServiceChain.EncounterServiceChainRaw
			}
			entities = append(entities, entity)
		}
	}

	if m.EncounterGoaServiceChain != nil {
		for i, svc := range m.EncounterGoaServiceChain.GoaServices {
			m.EncounterGoaService = &svc
			entity := timeline_service.ConvertModelToEntity[any](m)
			entity.ChainId = chainId
			// save editorState to the first goa service code entry
			if i == 0 {
				entity.EncounterGoaServiceChainRaw = &m.EncounterGoaServiceChain.EncounterGoaServiceChainRaw
			}
			entities = append(entities, entity)
		}
	}

	if m.EncounterUvGoaServiceChain != nil {
		for i, svc := range m.EncounterUvGoaServiceChain.UvGoaServices {
			m.EncounterUvGoaService = &svc
			entity := timeline_service.ConvertModelToEntity[any](m)
			entity.ChainId = chainId
			if i == 0 {
				entity.EncounterUvGoaServiceChainRaw = &m.EncounterUvGoaServiceChain.EncounterUvGoaServiceChainRaw
			}
			entities = append(entities, entity)
		}
	}

	return entities
}

func isServiceChain(timelineModel common.TimelineModel) bool {
	isLKChain := timelineModel.EncounterServiceChain != nil && len(timelineModel.EncounterServiceChain.Services) > 0
	isGOAChain := timelineModel.EncounterGoaServiceChain != nil && len(timelineModel.EncounterGoaServiceChain.GoaServices) > 0
	isUvGOAChain := timelineModel.EncounterUvGoaServiceChain != nil && len(timelineModel.EncounterUvGoaServiceChain.UvGoaServices) > 0
	return isLKChain || isGOAChain || isUvGOAChain
}
