package app

import (
	"fmt"
	"net/http"

	"github.com/go-chi/chi"
	"gitlab.com/silenteer-oss/titan"

	action_chain_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/action_chain"
	arriba_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/arriba"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/attendance_protocol"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/audit_log"
	auth_mvz "git.tutum.dev/medi/tutum/ares/app/mvz/api/auth"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/barcode"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/bg_billing"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/billing"
	billing_edoku_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/billing_edoku"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/billing_history"
	billing_kv_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/billing_kv"
	billing_patient_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/billing_patient"
	blank_service_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/blank_service"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/bmp"
	card_raw_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/card_raw"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/cardservice"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_bg_insurance"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_goa"
	catalog_hgnc_chain_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_hgnc_chain"
	catalog_material_cost_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_material_cost"
	omimg_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_omimg_chain"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_sdav"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_sdebm"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_sdik"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_sdkt"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_uv_goa"
	coding_rule_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/coding_rule"
	contract_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/contract"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/daily_list"
	diga_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/diga"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/doctor_letter"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/document_management"
	document_type_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/document_type"
	eab_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/eab"
	eau_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/eau"
	edmp_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/edmp"
	edoku_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/edoku"
	enrollment_bff2 "git.tutum.dev/medi/tutum/ares/app/mvz/api/enrollment"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/erezept"
	feature_flag_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/feature_flag"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/file"
	form_app "git.tutum.dev/medi/tutum/ares/app/mvz/api/form"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/heimi"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/himi"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/hpm_check_history"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/lab"
	mail_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/mail"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/medicine"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/medicine_kbv"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/medicine_statistic"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/mvz"
	patient_combination_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_combination"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_encounter"
	patient_log_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_log"
	patient_overview_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_overview"
	patient_participation_bff_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_participation"
	patient_profile_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_profile"
	patient_search_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_search"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_settings"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_sidebar"
	point_value_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/point_value"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/printer"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/private_billing"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/private_billing_setting"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/private_contract_group"
	ptv_import_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/ptv_import"
	qes_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/qes"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/report"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/schein"
	sdkv "git.tutum.dev/medi/tutum/ares/app/mvz/api/sdkv"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/sdva"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/settings"
	sidebar_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/sidebar"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/text_module"
	timeline_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/timeline"
	tss_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/tss"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/user_settings"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/versioninfor"
	vos_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/vos"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/action_chain"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/arriba"
	attendance_protocol_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/attendance_protocol"
	auditlog "git.tutum.dev/medi/tutum/ares/app/mvz/internal/audit_log"
	barcodeApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/barcode"
	bg_billing_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/bg_billing"
	billing_bff_impl "git.tutum.dev/medi/tutum/ares/app/mvz/internal/billing"
	billing_edoku_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/billing_edoku"
	billing_history_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/billing_history"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/billing_kv"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/billing_patient"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/blank_service"
	bmpApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/bmp"
	card_raw_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/card_raw"
	card_service_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/card_service"
	catalogBGInsuranceApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/catalog_bg_insurance"
	catalogGoaApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/catalog_goa"
	cataloghgncchain "git.tutum.dev/medi/tutum/ares/app/mvz/internal/catalog_hgnc_chain"
	catalog_material_cost_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/catalog_material_cost"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/catalog_omimg_chain"
	catalogSdavApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/catalog_sdav"
	catalogSdebmApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/catalog_sdebm"
	catalogSdikApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/catalog_sdik"
	catalogSdktApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/catalog_sdkt"
	catalogUvGoaApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/catalog_uv_goa"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/coding_rule"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/contract"
	daily_list_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/daily_list"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/diga"
	doctorLetterApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/doctor_letter"
	document_management_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/document_management"
	document_type_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/document_type"
	eab_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/eab"
	eau_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/eau"
	edmp_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/edmp"
	edoku_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/edoku"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/enrollment"
	erezept_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/erezept"
	feature_flag_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/feature_flag"
	fileApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/file"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/form"
	heimiApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/heimi"
	himiApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/himi"
	hpm_check_history_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/hpm_check_history"
	labApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/lab"
	mail_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/mail"
	medicine_service "git.tutum.dev/medi/tutum/ares/app/mvz/internal/medicine"
	medicine_kbv_service "git.tutum.dev/medi/tutum/ares/app/mvz/internal/medicine_kbv"
	medicine_statistic_service "git.tutum.dev/medi/tutum/ares/app/mvz/internal/medicine_statistic"
	patient_combination_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/patient_combination"
	patient_encounter_bff_impl "git.tutum.dev/medi/tutum/ares/app/mvz/internal/patient_encounter"
	patient_log "git.tutum.dev/medi/tutum/ares/app/mvz/internal/patient_log"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/patient_participation"
	patientSettingsApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/patient_settings"
	patient_sidebar_bff_impl "git.tutum.dev/medi/tutum/ares/app/mvz/internal/patient_sidebar"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/point_value"
	printer_service "git.tutum.dev/medi/tutum/ares/app/mvz/internal/printer"
	private_billing_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/private_billing"
	private_billing_setting_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/private_billing_setting"
	pcg_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/private_contract_group"
	ptv_import_bff_impl "git.tutum.dev/medi/tutum/ares/app/mvz/internal/ptv_import"
	qes_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/qes"
	report_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/report"
	scheinApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/schein"
	sdkv_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/sdkv"
	sdva_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/sdva"
	settingsApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/settings"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/sidebar"
	text_module_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/text_module"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/timeline"
	tss_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/tss"
	userSettingsApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/user_settings"
	versionInforApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/version_infor"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/vos"
	"git.tutum.dev/medi/tutum/ares/app/mvz/patient_overview"
	"git.tutum.dev/medi/tutum/ares/app/mvz/patient_profile"
	"git.tutum.dev/medi/tutum/ares/app/mvz/patient_search"
	"git.tutum.dev/medi/tutum/ares/pkg/healthcheck"
	"git.tutum.dev/medi/tutum/ares/pkg/middlewares/authorization"
	"git.tutum.dev/medi/tutum/ares/pkg/middlewares/request_middleware"
	"git.tutum.dev/medi/tutum/ares/service/bsnr_listener"
	settings_service "git.tutum.dev/medi/tutum/ares/service/domains/settings/settings_service"
	external_service "git.tutum.dev/medi/tutum/ares/service/external"
)

// export private contract group for testing
var PrivateContractGroupAppMod = pcg_app.PrivateContractGroupAppMod

func NewServer(mvzAppConfigs config.MvzAppConfigs) *titan.Server {
	if mvzAppConfigs.NatsConfig == nil {
		panic("missing nats config")
	}

	if mvzAppConfigs.XPMUrls == "" {
		panic("missing xpm url")
	}

	if mvzAppConfigs.XKMUrl == "" {
		panic("missing xkm url")
	}

	if mvzAppConfigs.MinioClientConfig.BucketKvBilling == "" {
		panic("missing minio kv billing bucket")
	}

	if mvzAppConfigs.XSLTUrl == "" {
		panic("missing xslt url")
	}

	if mvzAppConfigs.RedisConfig.Address == "" {
		panic("missing redis config")
	}

	if mvzAppConfigs.MMIConfig == nil || mvzAppConfigs.MMIConfig.BaseUrl == "" || mvzAppConfigs.MMIConfig.Licensekey == "" || mvzAppConfigs.MMIConfig.Username == "" {
		panic("missing mmi config")
	}

	if mvzAppConfigs.EdmpTestmoduleUrl == "" {
		panic("missing edmp testmodule url")
	}

	if mvzAppConfigs.MinioClientConfig.BucketDmpBilling == "" {
		panic("missing minio dmp billing bucket")
	}

	if mvzAppConfigs.CallbackRedirectUrl == "" {
		panic("missing callback redirect url")
	}

	if mvzAppConfigs.PostLogoutUrl == "" {
		panic("missing post logout url")
	}

	middlewares := chi.Middlewares{
		authorization.AuthorizationMiddlewareMod.Resolve(),
	}
	getSystemDateFlow := settings_service.GetSystemDateSettingFlow.Resolve()
	middlewares = append(middlewares, request_middleware.OnRequestMiddleware(func(r *http.Request) {
		ctx := titan.NewContext(r.Context())
		time, err := getSystemDateFlow(ctx)
		if err != nil {
			ctx.Logger().Error("can not get system date", "err", err.Error())
			return
		}
		if time == nil {
			return
		}
		r.Header.Set(titan.XRequestCurrentTime, fmt.Sprintf("%d", time.UnixMilli()))
	}))

	titanOptions := []titan.Option{
		titan.Config(mvzAppConfigs.NatsConfig),
		titan.Middlewares(middlewares),
	}

	// register bsnr listener
	bsnr_listener.BsnrListenerMod.Resolve()
	external_service.ExternalServiceMod.Resolve()
	// end

	registrars := []titan.Registrar{
		contract_api.NewContractAppRouter(contract.ContractAppMod.Resolve()),
		blank_service_api.NewBlankServicesAppRouter(blank_service.BlankServiceAppMod.Resolve()),
		sidebar_api.NewSidebarAppRouter(sidebar.SidebarAppMod.Resolve()),
		timeline_api.NewTimelineAppRouter(timeline.TimelineAppMod.Resolve()),
		coding_rule_api.NewCodingRuleAppRouter(coding_rule.CodingRuleAppMod.Resolve()),
		action_chain_api.NewActionChainsAppRouter(action_chain.ActionChainAppMod.Resolve()),
		mail_api.NewMailAppRouter(mail_app.MailAppMod.Resolve()),
		erezept.NewErezeptAppRouter(erezept_app.ErezeptAPPMod.Resolve()),
		attendance_protocol.NewAttendanceProtocolAppRouter(attendance_protocol_app.AttendanceProtocolAppMod.Resolve()),
		tss_api.NewTssAppRouter(tss_app.TssAppMod.Resolve()),
		qes_api.NewQesAppRouter(qes_app.QesAppMod.Resolve()),
		eau_api.NewEAUAppRouter(eau_app.EAUAppMod.Resolve()),
		billing_history.NewBillingHistoryAppRouter(billing_history_app.BillingHistoryAppMod.Resolve()),
		edmp_api.NewEDMPAppRouter(edmp_app.EdmpAppMod.Resolve()),
		edoku_api.NewEdokuAppRouter(edoku_app.EdokuAppMod.Resolve()),
		audit_log.NewAuditLogAppRouter(auditlog.AuditLogAppMod.Resolve()),
		card_raw_api.NewCardRawAppRouter(card_raw_app.CardRawAppMod.Resolve()),
		sdva.NewSdvaAppRouter(sdva_app.SdvaAppMod.Resolve()),
		versioninfor.NewVersionInforAppRouter(versionInforApp.VersionInforAPPMod.Resolve()),
		daily_list.NewDailyListAppRouter(daily_list_app.DailyListAppMod.Resolve()),
		text_module.NewTextModuleAppRouter(text_module_app.TextModuleAppMod.Resolve()),
		sdkv.NewSdkvAppRouter(sdkv_app.SdkvAppMod.Resolve()),
		hpm_check_history.NewHpmCheckHistoryAppRouter(hpm_check_history_app.HPMCheckHistoryAppMod.Resolve()),
		auth_mvz.NewAuthAppRouter(auth_mvz.MVZAuthMod.Resolve()),
		eab_api.NewEABAppRouter(eab_app.EABAppMod.Resolve()),
		document_type_api.NewDocumentTypeAppRouter(document_type_app.DocumentTypeAppMod.Resolve()),
		document_management.NewDocumentManagementAppRouter(document_management_app.DocumentManagementAppMod.Resolve()),
		diga_api.NewDIGAAppRouter(diga.DigaAPPMod.Resolve()),
		omimg_api.NewOmimGChainAppRouter(catalog_omimg_chain.OmimGChainAppMod.Resolve()),
		catalog_hgnc_chain_api.NewHgncChainAppRouter(cataloghgncchain.CatalogHgncChainAppMod.Resolve()),
		settings.NewSettingsAppRouter(settingsApp.SettingsAppMod.Resolve()),
		patient_settings.NewPatientSettingsAppRouter(patientSettingsApp.PatientSettingsAppMod.Resolve()),
		patient_encounter.NewPatientEncounterAppRouter(patient_encounter_bff_impl.PatientEncounterAppMod.Resolve()),
		patient_profile_api.NewPatientProfileAppRouter(patient_profile.PatientProfileServiceMod.Resolve()),
		patient_search_api.NewPatientSearchAppRouter(patient_search.PatientSearchBffMod.Resolve()),
		patient_overview_api.NewPatientOverviewAppRouter(patient_overview.PatientOverviewServiceMod.Resolve()),
		ptv_import_api.NewPtvImportAppRouter(ptv_import_bff_impl.PtvImportAppMod.Resolve()),
		billing.NewBillingAppRouter(billing_bff_impl.BillingAppMod.Resolve()),
		mvz.NewMvzAppRouter(internal.MvzApplicationMod.Resolve()),
		enrollment_bff2.NewEnrollmentAppRouter(enrollment.EnrollmentBffMod.Resolve()),
		patient_participation_bff_api.NewPatientParticipationAppRouter(patient_participation.GetPatientParticipationAppMod.Resolve()),
		patient_sidebar.NewPatientSidebarAppRouter(patient_sidebar_bff_impl.PatientSidebarBffMod.Resolve()),
		bmp.NewBmpAppRouter(bmpApp.BmpAppMod.Resolve()),
		medicine.NewMedicineAppRouter(medicine_service.MedicineAppMod.Resolve()),
		medicine_kbv.NewMedicineKbvAppRouter(medicine_kbv_service.MedicineKbvAppMod.Resolve()),
		medicine_statistic.NewMedicineStatisticAppRouter(medicine_statistic_service.MedicineStatisticAppMod.Resolve()),
		himi.NewHimiAppRouter(himiApp.HimiAppMod.Resolve()),
		schein.NewScheinAppRouter(scheinApp.ScheinAppMod.Resolve()),
		private_contract_group.NewPrivateContractGroupAppRouter(pcg_app.PrivateContractGroupAppMod.Resolve()),
		private_billing.NewPrivateBillingAppRouter(private_billing_app.PrivateBillingAppMod.Resolve()),
		private_billing_setting.NewPrivateBillingSettingAppRouter(private_billing_setting_app.PrivateBillingSettingAppMod.Resolve()),
		heimi.NewHeimiAppRouter(heimiApp.HiemiAppMod.Resolve()),
		barcode.NewBarcodeAppRouter(barcodeApp.BarcodeAppMod.Resolve()),
		lab.NewLabAppRouter(labApp.LabAppMod.Resolve()),
		file.NewFileAPPRouter(fileApp.FileAppMod.Resolve()),
		user_settings.NewUserSettingsAppRouter(userSettingsApp.UserSettingsAppMod.Resolve()),
		cardservice.NewCardServiceAppRouter(card_service_app.CardServiceAppMod.Resolve()),
		catalog_sdkt.NewSdktCatalogAppRouter(catalogSdktApp.SdktCatalogAppMod.Resolve()),
		doctor_letter.NewDoctorLetterAppRouter(doctorLetterApp.DoctorLetterAppMod.Resolve()),
		printer.NewPrinterAppRouter(printer_service.PrinterServiceAppMod.Resolve()),
		catalog_sdebm.NewSdebmCatalogAppRouter(catalogSdebmApp.SdebmCatalogAppMod.Resolve()),
		catalog_goa.NewGoaCatalogAppRouter(catalogGoaApp.GoaCatalogAppMod.Resolve()),
		catalog_uv_goa.NewUvGoaCatalogAppRouter(catalogUvGoaApp.UvGoaCatalogAppMod.Resolve()),
		catalog_sdik.NewSdikCatalogAppRouter(catalogSdikApp.SdikCatalogAppMod.Resolve()),
		catalog_sdav.NewSdavCatalogAppRouter(catalogSdavApp.SdavCatalogAppMod.Resolve()),
		catalog_bg_insurance.NewBGInsuranceCatalogAppRouter(catalogBGInsuranceApp.BGInsuranceCatalogAppMod.Resolve()),
		form_app.NewFormAPPRouter(form.FormAPPMod.Resolve()),
		billing_kv_api.NewBillingKVAppRouter(billing_kv.BillingKVAppMod.Resolve()),
		billing_patient_api.NewBillingPatientAppRouter(billing_patient.BillingPatientAppMod.Resolve()),
		point_value_api.NewPointValueAppRouter(point_value.PointValueAppMod.Resolve()),
		feature_flag_api.NewFeatureFlagAppRouter(feature_flag_app.FeatureFlagAppMod.Resolve()),
		arriba_api.NewArribaAppRouter(arriba.ArribaAppMod.Resolve()),
		vos_api.NewVOSAppRouter(vos.VOSAppMod.Resolve()),
		patient_log_api.NewPatientLogAppRouter(patient_log.PatientLogAppMod.Resolve()),
		report.NewReportAppRouter(report_app.ReportApplicationMod.Resolve()),
		bg_billing.NewBgBillingAppRouter(bg_billing_app.BgBillingAppMod.Resolve()),
		billing_edoku_api.NewBillingEDokuAppRouter(billing_edoku_app.BillingEDokuAppMod.Resolve()),
		catalog_material_cost_api.NewCatalogMaterialCostAppRouter(catalog_material_cost_app.CatalogMaterialCostAppMod.Resolve()),
		patient_combination_api.NewPatientCombinationAppRouter(patient_combination_app.PatientCombinationAppMod.Resolve()),
	}

	for _, v := range registrars {
		titanOptions = append(titanOptions, titan.Routes(v.Register), titan.Subscribe(v.Subscribe))
	}

	healthCheckApp := healthcheck.NewHealthCheck(mvz.NATS_SUBJECT, config.GetDefaultTitanClientMod.Resolve())
	titanOptions = append(titanOptions, titan.Routes(healthCheckApp.Register))

	server := titan.NewServer(
		mvz.NATS_SUBJECT,
		titanOptions...,
	)
	return server
}
