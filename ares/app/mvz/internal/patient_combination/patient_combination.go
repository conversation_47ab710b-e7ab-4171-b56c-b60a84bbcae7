package patient_combination

import (
	patient_combination_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_combination"
	patient_combination_service "git.tutum.dev/medi/tutum/ares/service/domains/patient_combination"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
)

type PatientCombinationApp struct {
	patientCombinationService *patient_combination_service.PatientCombinationService
}

var PatientCombinationAppMod = submodule.Make[patient_combination_api.PatientCombinationApp](func(patientCombinationService *patient_combination_service.PatientCombinationService) patient_combination_api.PatientCombinationApp {
	return &PatientCombinationApp{
		patientCombinationService: patientCombinationService,
	}
}, patient_combination_service.PatientCombinationServiceMod)

func (app *PatientCombinationApp) CombinePatients(ctx *titan.Context, request patient_combination_api.CombinePatientsRequest) error {
	return app.patientCombinationService.CombinePatients(ctx, &request)
}
