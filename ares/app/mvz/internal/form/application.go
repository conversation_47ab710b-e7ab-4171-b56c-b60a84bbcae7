package form

import (
	"encoding/json"
	"fmt"
	"html"
	"net/url"
	"strconv"
	"strings"
	"time"

	"emperror.dev/errors"
	barcode_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/barcode"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/form"
	api_patient_profile "git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_profile"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/barcode"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/form_print"
	"git.tutum.dev/medi/tutum/ares/app/mvz/patient_profile"
	"git.tutum.dev/medi/tutum/ares/pkg/formkey"
	hpm_rest_client "git.tutum.dev/medi/tutum/ares/pkg/hpm_rest/client"
	"git.tutum.dev/medi/tutum/ares/pkg/pdfform"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	barcode_common "git.tutum.dev/medi/tutum/ares/service/barcode/common"
	bsnr_service "git.tutum.dev/medi/tutum/ares/service/bsnr"
	"git.tutum.dev/medi/tutum/ares/service/contract/contract"
	contract_model "git.tutum.dev/medi/tutum/ares/service/contract/contract/model"
	catalog_overview_app "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_overview"
	common_1 "git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	enrollment_service "git.tutum.dev/medi/tutum/ares/service/domains/api/enrollment"
	profile_service "git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"
	bg_billing_service "git.tutum.dev/medi/tutum/ares/service/domains/bg_billing/service"
	doctor_letter_common "git.tutum.dev/medi/tutum/ares/service/domains/doctor_letter/common"
	eau_common "git.tutum.dev/medi/tutum/ares/service/domains/eau/common"
	eau_service "git.tutum.dev/medi/tutum/ares/service/domains/eau/service"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	form_common "git.tutum.dev/medi/tutum/ares/service/domains/form/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/form/form_service"
	qes_common "git.tutum.dev/medi/tutum/ares/service/domains/qes/common"
	patient_encounter_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	printer_form_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/printer_form"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/timeline_service"
	schein_service "git.tutum.dev/medi/tutum/ares/service/schein"
	"git.tutum.dev/medi/tutum/ares/share"
	"git.tutum.dev/medi/tutum/ares/share/form_share"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
)

var (
	formApp form.FormAPP

	// list related forms to print together
	relatedForms = map[form_common.FormName][]form_common.FormName{
		form_common.Muster_2B:      {form_common.Muster_2A, form_common.Muster_2C},
		form_common.Muster_3A:      {form_common.Muster_3B},
		form_common.Muster_12A:     {form_common.Muster_12B, form_common.Muster_12C},
		form_common.Muster_19B:     {form_common.Muster_19A, form_common.Muster_19C},
		form_common.Muster_20A:     {form_common.Muster_20B, form_common.Muster_20C, form_common.Muster_20D},
		form_common.Muster_22A:     {form_common.Muster_22B, form_common.Muster_22C, form_common.Muster_22D},
		form_common.Muster_26A:     {form_common.Muster_26B, form_common.Muster_26C},
		form_common.Muster_27A:     {form_common.Muster_27B, form_common.Muster_27C},
		form_common.Muster_28A:     {form_common.Muster_28B, form_common.Muster_28C},
		form_common.Muster_39A:     {form_common.Muster_39B},
		form_common.Muster_61:      {form_common.Muster_61B, form_common.Muster_61C, form_common.Muster_61D},
		form_common.Muster_N63A:    {form_common.Muster_N63B, form_common.Muster_N63C, form_common.Muster_N63D}, // NOTE: Muster 63 : A1, A2, B, C, D
		form_common.Muster_64:      {form_common.Muster_64B},
		form_common.Muster_65A:     {form_common.Muster_65B},
		form_common.Muster_70:      {form_common.Muster_70_B},
		form_common.Muster_70A:     {form_common.Muster_70A_B},
		form_common.Muster_PTV_1A:  {form_common.Muster_PTV_1B, form_common.Muster_PTV_1C},
		form_common.Muster_PTV_2A:  {form_common.Muster_PTV_2B, form_common.Muster_PTV_2C},
		form_common.Muster_PTV_11A: {form_common.Muster_PTV_11B},
		form_common.Muster_PTV_12A: {form_common.Muster_PTV_12B},
	}
)

const (
	dateFormatForFullLabelDate           = "02012006"
	dateFormatForLabelDateWithDot        = "02.01.06"
	dateFormatForFullLabelDateWithDot    = "02.01.2006"
	dateFormatForFullLabelDateWithoutDot = "02012006"
)

type (
	FormAPP struct {
		pdfform                 *pdfform.Client
		barcodeApp              barcode_api.BarcodeApp
		patientProfileApp       *patient_profile.PatientProfileBffImpl
		employeeProfileService  profile_service.EmployeeProfileService
		timelineService         *timeline_service.TimelineService[patient_encounter_repo.EncounterForm]
		timelineServiceEntry    *timeline_service.TimelineService[patient_encounter_repo.EncounterServiceTimeline]
		contractService         *contract.Service
		printerFormRepo         printer_form_repo.PrinterFormRepo
		eauService              *eau_service.EAUService
		minioClient             share.FileStorage
		bucketAkaData           string
		scheinService           *schein_service.ScheinService
		catalogOverviewService  catalog_overview_app.CatalogOverviewService
		enrollmentServiceClient *enrollment_service.EnrollmentServiceClient
		hpmRestService          *hpm_rest_client.ServiceRest
		formPrescribe           *form_share.FormPrescribe
		formPrint               *form_print.FormPrint
		bucketTmp               string
		bsnrService             *bsnr_service.BSNRService
		bgBillingService        *bg_billing_service.Service
	}
	PrescribeEAURequest struct {
		prescribe   form_common.Prescribe
		printOption *form_common.PrintOption
	}
	PrintEAURequest struct {
		Id          uuid.UUID
		Prescribe   form_common.Prescribe
		PrintOption *form_common.PrintOption
	}
)

var FormAPPMod = submodule.Make[form.FormAPP](
	NewFormAPP,
	config.MvzAppConfigMod,
	patient_profile.PatientProfileServiceMod,
	share.EmployeeProfileServiceMod,
	contract.ContractServiceMod,
	share.MinioMod,
	share.EnrollmentServiceMod,
	timeline_service.TimelineServiceFormMod,
	timeline_service.TimelineServiceEncounterMod,
	printer_form_repo.PrinterFormRepoMod,
	eau_service.EAUServiceMod,
	schein_service.ScheinServiceMod,
	share.CatalogOverviewServiceMod,
	share.EnrollmentServiceMod,
	form_share.FormPrescribeMod,
	barcode.BarcodeAppMod,
	form_print.FormPrintMod,
	hpm_rest_client.HpmRestServiceMod,
	bsnr_service.BSNRServiceMod,
	bg_billing_service.BgBillingServiceMod,
)

func NewFormAPP(
	config *config.MvzAppConfigs,
	patientProfileAppClient *patient_profile.PatientProfileBffImpl,
	employeeProfileServiceClient profile_service.EmployeeProfileService,
	contractService *contract.Service,
	minioClient share.FileStorage,
	timelineService *timeline_service.TimelineService[patient_encounter_repo.EncounterForm],
	timelineServiceEntry *timeline_service.TimelineService[patient_encounter_repo.EncounterServiceTimeline],
	printerFormRepo *printer_form_repo.PrinterFormRepo,
	eauService *eau_service.EAUService,
	scheinService *schein_service.ScheinService,
	catalogOverviewService catalog_overview_app.CatalogOverviewService,
	enrollmentServiceClient *enrollment_service.EnrollmentServiceClient,
	formPrescribe *form_share.FormPrescribe,
	barcodeApp barcode_api.BarcodeApp,
	formPrint *form_print.FormPrint,
	hpmRestService *hpm_rest_client.ServiceRest,
	bsnrService *bsnr_service.BSNRService,
	bgBillingService *bg_billing_service.Service,
) form.FormAPP {
	pdfServicePath := config.PdfServicePath
	formApp = &FormAPP{
		pdfform:                 pdfform.NewPDFFormClient(pdfServicePath),
		barcodeApp:              barcodeApp,
		patientProfileApp:       patientProfileAppClient,
		employeeProfileService:  employeeProfileServiceClient,
		timelineService:         timelineService,
		contractService:         contractService,
		printerFormRepo:         *printerFormRepo,
		eauService:              eauService,
		minioClient:             minioClient,
		bucketAkaData:           config.MinioClientConfig.BucketAkaData,
		catalogOverviewService:  catalogOverviewService,
		scheinService:           scheinService,
		timelineServiceEntry:    timelineServiceEntry,
		enrollmentServiceClient: enrollmentServiceClient,
		hpmRestService:          hpmRestService,
		formPrescribe:           formPrescribe,
		formPrint:               formPrint,
		bucketTmp:               config.MinioClientConfig.BucketTmp,
		bsnrService:             bsnrService,
		bgBillingService:        bgBillingService,
	}
	return formApp
}

// GetPrescribe implements form.FormAPP
func (app *FormAPP) GetPrescribe(ctx *titan.Context, request form.GetPrescribeRequest) (*form.GetPrescribeResponse, error) {
	return app.formPrescribe.GetPrescribe(ctx, request)
}

// use in view form and print form include multi form at time
func (app *FormAPP) Print(ctx *titan.Context, req form.PrintRequest) (*form.PrintResponse, error) {
	formPrescribe, err := app.GetPrescribe(ctx, form.GetPrescribeRequest{PrescribeId: req.PrescribeId})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get prescribe form when print")
	}
	if formPrescribe == nil {
		return nil, nil
	}
	result := &form.PrintResponse{
		PrintData: []form.PrintData{},
	}

	if formPrescribe.Prescribe.FormName == form_common.Muster_1 {
		if req.EAUSetting == nil {
			return nil, errors.Errorf("eau setting is required for form %s", formPrescribe.Prescribe.FormName)
		}

		pdfUrl := util.GetPointerValue(formPrescribe.Prescribe.PdfUrl)
		if len(pdfUrl) == 0 {
			return nil, errors.Errorf("pdf url is required for form %s", formPrescribe.Prescribe.FormName)
		}

		printOption := req.EAUSetting.PrintOption
		pdfUrl = slice.Filter(pdfUrl, func(u string) bool {
			if strings.Contains(u, eau_service.EAU_INSURANCE) && printOption.PrintForInsurance {
				return true
			}

			if strings.Contains(u, eau_service.EAU_PATIENT) && printOption.PrintForPatient {
				return true
			}

			if strings.Contains(u, eau_service.EAU_EMPLOYER) && printOption.PrintForEmployer {
				return true
			}

			return false
		})

		printResult, err := app.getPrintResult(ctx, formPrescribe.Prescribe.FormName, pdfUrl)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to get print result")
		}

		if len(printResult) == 0 {
			return result, nil
		}

		formUrls := slice.Map(printResult, func(item form_common.PrintResult) string {
			return item.FormUrl
		})

		result.PrintData = append(result.PrintData, form.PrintData{
			FormUrls: formUrls,
		})
		return result, nil
	}
	printFormName := formPrescribe.Prescribe.FormName

	if printFormName == form_common.Muster_4 && !req.PrintOption.PdfWithBackground {
		printFormName = form_common.Muster_4_A5
	}

	formNames := getRelatedForms(printFormName)
	urls, err := app.printToPdfFile(ctx, req.PrintOption, formPrescribe, formNames)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to print forms")
	}
	printData := slice.Map(urls, func(url string) form.PrintData {
		return form.PrintData{
			FormUrls: []string{url},
		}
	})

	return &form.PrintResponse{
		PrintData: printData,
	}, nil
}

// return url of form in prescribe response
func (app *FormAPP) printToPdfFile(ctx *titan.Context, printOption form_common.PrintOption, formPrescribe *form.GetPrescribeResponse, formNames []form_common.FormName) ([]string, error) {
	formInfos := []form_print.BaseFormInfo{}
	for _, formName := range formNames {
		formInfos = append(formInfos, form_print.BaseFormInfo{
			FormSetting:    formPrescribe.Prescribe.Payload,
			FormName:       formName,
			PrintDate:      *formPrescribe.Prescribe.PrintedDate,
			PrintOption:    &printOption,
			PrescribeDate:  formPrescribe.Prescribe.PrescribeDate,
			PrescriptionId: formPrescribe.Prescribe.Id,
			PrescribeData:  &formPrescribe.Prescribe,
		})
	}

	printResults, err := app.formPrint.PrintToPDF(ctx,
		formInfos,
		util.GetPointerValue(formPrescribe.Prescribe.TreatmentDoctorId),
		util.GetPointerValue(formPrescribe.Prescribe.PatientId),
		util.GetPointerValue(formPrescribe.Prescribe.ScheinId),
		formPrescribe.Prescribe.AssignedToBsnrId,
	)
	if err != nil {
		return nil, err
	}
	urls := slice.Map(printResults, func(res form_common.PrintResult) string {
		return res.FormUrl
	})

	return urls, nil
}

// GetForm implements form.PrinterApp
func (s *FormAPP) GetForm(ctx *titan.Context, request form.GetFormRequest) (*form.GetFormResponse, error) {
	if request.Id == nil || *request.Id == "" {
		return nil, nil
	}
	forms := s.printerFormRepo.GetAllForms()
	foundForm := slice.FindOne(forms, func(t form_common.Form) bool {
		return t.Id == *request.Id
	})

	var formContracts []form_common.Form
	if util.GetPointerValue(request.OKV) != "" && util.GetPointerValue(request.ContractId) != "" && util.GetPointerValue(request.IkNumber) != 0 {
		contractServiceMod, err := form_service.FormServiceMod.SafeResolve()
		if err != nil {
			return nil, err
		}
		formContracts, err = contractServiceMod.GetFormsContract(ctx, request.Id, form.GetFormsRequest{
			OKV:        *request.OKV,
			IkNumber:   *request.IkNumber,
			ContractId: *request.ContractId,
		})
		if err != nil {
			return nil, err
		}
	}

	if foundForm == nil && len(formContracts) == 0 {
		return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Form_Not_Found, "Form not found")
	}

	if foundForm != nil {
		return &form.GetFormResponse{
			Form: *foundForm,
		}, nil
	}

	if len(formContracts) > 0 {
		return &form.GetFormResponse{
			Form: formContracts[0],
		}, nil
	}
	return nil, nil
}

// return url of form on prescribe response
func (app *FormAPP) prescribeEAU(ctx *titan.Context, req PrescribeEAURequest) (*eau_common.EAUModel, error) {
	printOption := req.printOption
	var patientHeaderMap map[string]string
	var labelStamp string
	var barcode *barcode_api.GetBarcodeResponse
	var err error
	barcode, err = app.barcodeApp.GetBarcode(ctx, barcode_api.GetBarcodeRequest{
		PatientId:        *req.prescribe.PatientId,
		EmployeeId:       *req.prescribe.TreatmentDoctorId,
		FormName:         req.prescribe.FormName,
		PrescriptionId:   *req.prescribe.Id,
		TopicName:        barcode_common.TopicName_Form,
		PrescribeData:    &req.prescribe,
		HasTimelineEntry: false,
		BsnrId:           req.prescribe.AssignedToBsnrId,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get barcode")
	}

	patientProfile, err := app.patientProfileApp.GetPatientFormProfile(ctx, &api_patient_profile.GetPatientFormProfileRequest{
		DoctorID:  req.prescribe.TreatmentDoctorId,
		PatientID: req.prescribe.PatientId,
		ScheinId:  req.prescribe.ScheinId,
		FormName:  util.NewString(string(req.prescribe.FormName)),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patient form profile")
	}

	patientHeaderMap = formkey.FillPatientHeader(patientProfile.Profile, req.prescribe.FormName, req.prescribe.PrescribeDate, false)
	doctor, err := app.employeeProfileService.GetEmployeeProfileById(ctx, &profile_service.EmployeeProfileGetRequest{
		OriginalId: req.prescribe.TreatmentDoctorId,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get doctor profile")
	}
	labelStamp = function.If(doctor.DoctorStamp != "", doctor.DoctorStamp, doctor.BsnrPracticeStamp)
	formMap, err := formkey.FillM1FormMap(ctx, patientHeaderMap, barcode.ImgBase64, labelStamp, req.prescribe.Payload, !printOption.PdfWithBackground)
	if err != nil {
		return nil, err
	}
	if req.printOption.DateOfPrint == 0 {
		req.printOption.DateOfPrint = util.GetPointerValue(req.prescribe.PrintedDate)
	}

	if req.printOption != nil && req.printOption.FormAction == form_common.FormAction_PrintFull && req.prescribe.PrintedDate != nil {
		err := eau_service.ValidateEAUFormMap(ctx, formMap)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	res, err := app.eauService.FindByFormId(ctx, *req.prescribe.Id)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if res == nil {
		res, err = app.eauService.CreateEAU(ctx, eau_service.CreateEAURequest{
			PatientId:   *req.prescribe.PatientId,
			DoctorId:    *req.prescribe.TreatmentDoctorId,
			ScheinId:    *req.prescribe.ScheinId,
			FormId:      *req.prescribe.Id,
			PrintOption: req.printOption,
			FormMap:     formMap,
			EAUSetting:  *req.prescribe.EAUSetting,
		})
		if err != nil {
			return nil, errors.WithStack(err)
		}

		if res == nil {
			return nil, nil
		}
	}

	if res.Status == qes_common.Status_Sent || res.Status == qes_common.Status_Delivered {
		return res, nil
	}

	eauId := util.GetPointerValue(res.Id)
	res, err = app.eauService.BuildPdfFile(ctx, eau_service.BuildPdfFileRequest{
		Id:            eauId,
		PrintOption:   req.printOption,
		BarCodeBase64: &barcode.ImgBase64,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if req.prescribe.PrintedDate == nil {
		return res, nil
	}

	if req.prescribe.EAUSetting != nil {
		eauSetting := req.prescribe.EAUSetting
		printOnly := util.GetPointerValue(eauSetting.PrintOnly)
		if printOnly {
			res.Status = qes_common.Status_NotDispatched
			if err := app.eauService.UpdateEAUByIds(ctx, &eau_service.UpdateEAUByIdsRequest{
				Ids:    []uuid.UUID{eauId},
				Status: util.NewPointer(res.Status),
			}); err != nil {
				return nil, errors.WithStack(err)
			}
			return res, nil
		}

		res.Status = qes_common.Status_Sending
		if err = app.eauService.SignAndSendEAU(ctx, eau_service.SignAndSendEAURequest{
			Ids: []uuid.UUID{
				eauId,
			},
			CardType: eauSetting.CardType,
			BsnrCode: eauSetting.BsnrCode,
		}); err != nil {
			return nil, errors.WithStack(err)
		}
	}

	return res, nil
}

func (app *FormAPP) getVpId(ctx *titan.Context, contract *contract_model.Contract) (string, error) {
	doctor, err := app.employeeProfileService.GetEmployeeProfileById(ctx, &profile_service.EmployeeProfileGetRequest{
		OriginalId: ctx.UserInfo().UserUUID(),
	})
	if err != nil {
		return "", err
	}
	contractType := contract.GetContractType().Type()
	if contractType == nil {
		return "", errors.New("contract type not supported")
	}
	if *contractType == contract_model.ContractType_HouseDoctorCare {
		return *doctor.HavgVpId, nil
	} else if *contractType == contract_model.ContractType_SpecialistCare {
		return *doctor.MediverbundVpId, nil
	}
	return "", errors.New("vpId not found")
}

func (app *FormAPP) BuildBundleAndValidation(ctx *titan.Context, req form.BuildBundleAndValidationRequest) (*form.BuildBundleAndValidationResponse, error) {
	if req.Prescribe.FormName != form_common.Muster_1 {
		return nil, errors.New("Not allowed")
	}

	doctor, err := app.employeeProfileService.GetEmployeeProfileById(ctx, &profile_service.EmployeeProfileGetRequest{
		OriginalId: req.Prescribe.TreatmentDoctorId,
	})
	if doctor == nil || err != nil {
		return nil, errors.WithMessage(err, "failed to get doctor profile")
	}

	currentSchein, err := app.scheinService.GetScheinById(ctx, util.GetPointerValue(req.Prescribe.ScheinId))
	if currentSchein == nil || err != nil {
		return nil, errors.WithMessage(err, "falied to get schein")
	}
	isSvSchein := currentSchein.IsSvSchein()
	if isSvSchein {
		if req.Prescribe.ContractId == nil {
			return nil, errors.New("contract id is required for sv schein")
		}
		contr := app.contractService.GetContractDetailById(*req.Prescribe.ContractId)
		if contr == nil {
			return nil, errors.New("contract not found")
		}
		hasFunction, err := contr.HasFunction(contract_model.FORM1844, util.NowUnixMillis(ctx), []string{util.GetPointerValue((doctor.Okv))}, util.GetPointerValue(currentSchein.IkNumber))
		if err != nil {
			return nil, errors.WithMessage(err, "failed to function FORM1844")
		}
		if !hasFunction {
			return &form.BuildBundleAndValidationResponse{
				EAUValidation: &eau_common.EAUValidation{
					ConfirmRequired: true,
				},
			}, nil
		}
	}

	printOption := req.PrintOption
	var patientHeaderMap map[string]string
	var labelStamp string

	if req.Prescribe.Id == nil {
		req.Prescribe.Id = util.NewUUID()
		req.Prescribe.CreatedDate = util.UnixMillis(util.Now(ctx))
	}
	barcode, err := app.barcodeApp.GetBarcode(ctx, barcode_api.GetBarcodeRequest{
		PatientId:        *req.Prescribe.PatientId,
		EmployeeId:       *req.Prescribe.DoctorId,
		FormName:         req.Prescribe.FormName,
		PrescriptionId:   *req.Prescribe.Id,
		TopicName:        barcode_common.TopicName_Form,
		PrescribeData:    &req.Prescribe,
		HasTimelineEntry: false,
		BsnrId:           req.Prescribe.AssignedToBsnrId,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get barcode")
	}

	patientProfile, err := app.patientProfileApp.GetPatientFormProfile(ctx, &api_patient_profile.GetPatientFormProfileRequest{
		DoctorID:  req.Prescribe.DoctorId,
		PatientID: req.Prescribe.PatientId,
		ScheinId:  req.Prescribe.ScheinId,
		FormName:  util.NewString(string(req.Prescribe.FormName)),
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get patient form profile")
	}

	patientHeaderMap = formkey.FillPatientHeader(patientProfile.Profile, req.Prescribe.FormName, req.Prescribe.PrescribeDate, false)

	labelStamp = function.If(doctor.DoctorStamp != "", doctor.DoctorStamp, doctor.BsnrPracticeStamp)
	formMap, err := formkey.FillM1FormMap(ctx, patientHeaderMap, barcode.ImgBase64, labelStamp, req.Prescribe.Payload, !printOption.PdfWithBackground)
	if err != nil {
		return nil, err
	}

	if req.PrintOption != nil && req.PrintOption.FormAction == form_common.FormAction_PrintFull {
		err := eau_service.ValidateEAUFormMap(ctx, formMap)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	if req.PrintOption.DateOfPrint == 0 {
		req.PrintOption.DateOfPrint = util.GetPointerValue(req.Prescribe.PrintedDate)
	}

	strXml, err := app.eauService.BundleValidation(ctx, eau_service.BundleValidationRequest{
		PatientId:   *req.Prescribe.PatientId,
		ScheinId:    *req.Prescribe.ScheinId,
		DoctorId:    *req.Prescribe.DoctorId,
		PrintOption: req.PrintOption,
		FormMap:     formMap,
	})
	if err != nil {
		return nil, err
	}

	if isSvSchein {
		contr := app.contractService.GetContractDetailById(util.GetPointerValue(req.Prescribe.ContractId))
		if contr == nil {
			return nil, errors.New("contract not found")
		}
		escapeStr := html.EscapeString(*strXml)
		hpmConfig, err := app.bsnrService.GetHpmConfig(ctx, ctx.UserInfo().BsnrId)
		if err != nil {
			return nil, err
		}
		vpId, err := app.getVpId(ctx, contr)
		if err != nil {
			return nil, err
		}
		hpmRestService := hpm_rest_client.NewServiceRestWithEndpoint(hpmConfig.Endpoint)
		meldum, err := hpmRestService.ValidateEAU(ctx, &hpm_rest_client.PostValidierungRequest{FhirDokument: &escapeStr, AbsenderBsnr: doctor.Bsnr, Lanr: doctor.Lanr, VpId: vpId})
		if err != nil {
			return nil, err
		}
		if meldum != nil {
			payload := []eau_common.Meldung{}
			err := copier.Copy(&payload, *meldum)
			if err != nil {
				return nil, err
			}

			return &form.BuildBundleAndValidationResponse{
				EAUValidation: &eau_common.EAUValidation{
					ConfirmRequired: true,
					Payload:         payload,
				},
			}, nil
		}
	}

	return &form.BuildBundleAndValidationResponse{
		EAUValidation: &eau_common.EAUValidation{
			ConfirmRequired: false,
		},
	}, nil
}

func (app *FormAPP) CreateServicePtz5Automatic(
	ctx *titan.Context,
	req form.PrescribeRequest,
	scheinIds []uuid.UUID,
) error {
	schein, err := app.scheinService.GetScheinDetailById(ctx, schein_common.GetScheinDetailByIdRequest{
		ScheinId: util.GetPointerValue(req.Prescribe.ScheinId),
	})
	if err != nil {
		return err
	}
	if schein == nil {
		return errors.New("failed to get scheins detail")
	}
	contractId := *req.Prescribe.ContractId
	contractDetail := app.contractService.GetContractDetailById(contractId)
	if contractDetail != nil {
		if contractDetail.CheckExistAnforderung(contract_model.ICheckExistAnforderung{
			ComplianceIds: []string{"FORM1687"},
			CheckTime:     util.NowUnixMillis(ctx),
		}) {
			formKeyMap, _ := formkey.NewFormMap[string](req.Prescribe.Payload)
			endDateStr, ok := formKeyMap["date_label_custom_abmel"]
			if ok {
				endDate, err := strconv.ParseInt(endDateStr, 10, 64)
				if err != nil {
					return err
				}
				endDateTime := util.ConvertMillisecondsToTime(endDate)
				resp := contractDetail.GetServiceCode("PTZ5", endDateTime)
				if resp == nil {
					return errors.New("cannot find service code PTZ5")
				}
				group := function.Do(func() common_1.MainGroup {
					if schein.ScheinMainGroup == schein_common.HZV {
						return common_1.HZV
					}
					if schein.ScheinMainGroup == schein_common.FAV {
						return common_1.FAV
					}
					return common_1.KV
				})

				preventCreatePTZ5, ok := formKeyMap["prevent_create_ptz5"]
				parseBool, _ := strconv.ParseBool(preventCreatePTZ5)
				if !ok && !parseBool {
					servicePtz5 := timeline_repo.TimelineEntity[patient_encounter_repo.EncounterServiceTimeline]{
						Id:                util.NewUUID(),
						ContractId:        req.Prescribe.ContractId,
						TreatmentDoctorId: *req.Prescribe.TreatmentDoctorId,
						BillingDoctorId:   req.Prescribe.DoctorId,
						PatientId:         *req.Prescribe.PatientId,
						ScheinIds:         scheinIds,
						EncounterCase:     (*patient_encounter_repo.EncounterCase)(&req.Prescribe.EncounterCase),
						SelectedDate:      endDateTime,
						Payload: patient_encounter_repo.EncounterServiceTimeline{
							Code:        resp.Ziffer,
							Description: resp.GetDescription(),
							Scheins: util.NewPointer([]*common_1.ScheinWithMainGroup{{
								ScheinId: &schein.ScheinId,
								Group:    group,
							}}),
							FreeText:         fmt.Sprintf("(%s) - %s", resp.Ziffer, resp.GetDescription()),
							Command:          "Z",
							PatientId:        req.Prescribe.PatientId,
							ServiceMainGroup: &group,
							ChargeSystemId: function.Do(func() *string {
								chargeSystems := contractDetail.GetChargeSystems()
								if len(chargeSystems) > 0 {
									return util.NewString(chargeSystems[0].GetId())
								}
								return nil
							}),
						},
					}
					_, err = app.timelineServiceEntry.Create(ctx, servicePtz5)
					if err != nil {
						return err
					}
				}
				_, err = app.enrollmentServiceClient.ActionOnFavContractGroup(ctx, &enrollment_service.ActionOnFavContractGroupRequest{
					Action:     enrollment_service.PatientEnrollmentInformationAction_TerminateFavGroupContract,
					PatientId:  req.Prescribe.PatientId,
					ContractId: contractId,
					EndDate:    util.NewPointer(endDate),
				})
				if err != nil {
					return err
				}
			}
		}
	}
	return nil
}

// GetForms implements form.FormAPP
func (app *FormAPP) GetForms(ctx *titan.Context, req form.GetFormsRequest) (*form.GetFormsResponse, error) {
	contractServiceMod, err := form_service.FormServiceMod.SafeResolve()
	if err != nil {
		return nil, err
	}

	appForms := app.printerFormRepo.GetAllForms()
	isBGSchein := util.GetPointerValue(req.ScheinMainGroup) == schein_common.BG

	if isBGSchein {
		bgForms := []form_common.FormName{form_common.F1000, form_common.F1050, form_common.F2100, form_common.F9990}
		foundForms := slice.Filter(appForms, func(t form_common.Form) bool {
			return slice.Contains(bgForms, form_common.FormName(t.Id)) || t.FormTab
		})

		return &form.GetFormsResponse{
			Forms: slice.Map(foundForms, func(t form_common.Form) form_common.Form {
				t.FormTab = true

				return t
			}),
		}, nil
	}

	foundForms := slice.Filter(appForms, func(t form_common.Form) bool {
		return t.FormTab
	})

	formFromContracts, err := contractServiceMod.GetFormsContract(ctx, nil, req)
	if err != nil {
		return nil, err
	}

	foundForms = append(foundForms, formFromContracts...)

	return &form.GetFormsResponse{
		Forms: foundForms,
	}, nil
}

func (app *FormAPP) GetIcdForm(ctx *titan.Context, req form.GetIcdFormRequest) (*form.GetIcdFormResponse, error) {
	icds, err := app.contractService.GetIcds(ctx, req.ContractId)
	if err != nil {
		return nil, err
	}
	return &form.GetIcdFormResponse{
		IcdCodes: icds,
	}, nil
}

// var versionRegex = regexp.MustCompile(`^(.*?)_V\d+`)

// print plain pdf, all content depends on Frontend
// form g81
func (app *FormAPP) PrintPlainPdf(ctx *titan.Context, request form.PrintPlainPdfRequest) (*form.PrintPlainPdfResponse, error) {
	result := &form.PrintPlainPdfResponse{}
	if request.FormName == "" {
		return nil, errors.New("no form name")
	}
	if request.FormSetting == "" {
		return nil, errors.New("no form setting")
	}
	pdfFile, err := form_print.TemplateFs.Open("templates/" + request.FormName + ".pdf")
	if err != nil {
		return nil, errors.WithMessagef(err, "file %s did not exists", request.FormName+".pdf")
	}
	defer pdfFile.Close()

	formSetting := make(map[string]any)
	err = json.Unmarshal([]byte(request.FormSetting), &formSetting)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to unmarshal form setting")
	}

	customFormSetting, err := app.customFormKey(ctx, formSetting, request.FormName, util.GetPointerValue(request.ContractId))
	if err != nil {
		return nil, errors.WithMessage(err, "failed to handle custom form")
	}
	for key, value := range customFormSetting {
		formSetting[key] = value
	}

	reformat := make(map[string]string)
	for key, value := range formSetting {
		switch value := value.(type) {
		case string:
			if strings.HasPrefix(key, "date_datumBirth") {
				day, month, year := formkey.ExtractTimeFromString(value)
				reformat[key] = day + month + year
				continue
			} else if strings.HasPrefix(key, "label_doctor_stamp") {
				doctor, err := app.employeeProfileService.GetEmployeeProfileById(ctx, &profile_service.EmployeeProfileGetRequest{
					OriginalId: request.TreatmentDoctorId,
				})
				if err != nil {
					return nil, errors.WithMessage(err, "failed to get doctor profile")
				}

				labelStamp, ok := formSetting["label_doctor_stamp"].(string)

				if !ok {
					return nil, errors.WithMessage(err, "failed to get doctor stamp")
				}

				if labelStamp == "" {
					labelStamp = function.If(doctor.DoctorStamp != "", doctor.DoctorStamp, doctor.BsnrPracticeStamp)
				}

				reformat[key] = labelStamp
			} else if !strings.HasPrefix(key, "label_date_label_full_date_without_dot") {
				reformat[key] = value
			}
		case float64:
			if strings.HasPrefix(key, "date_") {
				timeValue := int64(value)

				if strings.HasPrefix(key, "date_prescribe") {
					reformat[key] = util.FormatMillisecondsToString(&timeValue, dateFormatForLabelDateWithDot)
				} else if strings.HasPrefix(key, "date_label_full_date_without_dot") {
					last3Char := util.SubString(key, len(key)-3, 1)
					_, err := strconv.Atoi(last3Char)
					index := 2

					if err == nil {
						index = 3
					}
					prefix := key[(len(key) - index):]

					_, haveLabelDayField := formSetting["label_date_label_full_date_without_dot_day"+prefix]
					_, haveLabelMonthField := formSetting["label_date_label_full_date_without_dot_month"+prefix]
					_, haveLabelYearField := formSetting["label_date_label_full_date_without_dot_year"+prefix]

					if haveLabelDayField && haveLabelMonthField && haveLabelYearField {
						stringTime := util.FormatMillisecondsToString(&timeValue, dateFormatForFullLabelDateWithDot)
						day, month, year := formkey.ExtractTimeFromString(stringTime)

						reformat["label_date_label_full_date_without_dot_day"+prefix] = day
						reformat["label_date_label_full_date_without_dot_month"+prefix] = month
						reformat["label_date_label_full_date_without_dot_year"+prefix] = year
					} else {
						reformat[key] = util.FormatMillisecondsToString(&timeValue, dateFormatForFullLabelDateWithoutDot)
					}
				} else if strings.HasPrefix(key, "date_label_custom") {
					reformat[key] = util.FormatMillisecondsToString(&timeValue, dateFormatForLabelDateWithDot)
				} else {
					reformat[key] = time.Unix(timeValue/1000, 0).Format(dateFormatForFullLabelDate)
				}
			} else {
				reformat[key] = strconv.FormatInt(int64(value), 10)
			}
		case bool:
			var content string
			if value {
				content = "X"
			}
			reformat[key] = content
		default:
		}
	}

	pdfFill, err := app.pdfform.FillPdfFsFile(ctx, pdfFile, reformat, util.GetPointerValue(request.IsRemoveBackground))
	if err != nil {
		return nil, errors.WithMessage(err, "failed to fill pdf")
	}

	// for debug only
	// err = ioutil.WriteFile("g81.pdf", []byte(pdfFill), 6044)
	// if err != nil {
	// 	return nil, errors.WithMessage(err, "failed to write pdf document")
	// }

	printResult, err := app.formPrint.GetPresignedUrl(ctx, form_common.FormName(request.FormName), pdfFill)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get presigned url of form")
	}
	result.FormUrl = printResult.FormUrl
	return result, nil
}

func (app *FormAPP) customFormKey(ctx *titan.Context, formKey map[string]any, formName, contractId string) (map[string]any, error) {
	if formName == "HZV_Beleg_Muster_V3" && contractId != "" {
		contract := app.contractService.GetContractDetailById(contractId)
		supportQR := contract != nil && contract.DoesContractHasAttachmentWithProperty("4", "552")
		qrData, hasQrData := formKey["qrdata"]
		if supportQR && hasQrData && qrData.(string) != "" {
			res, err := app.barcodeApp.GetQrCode(ctx, barcode_api.GetQrCodeRequest{
				Content: qrData.(string),
			})
			if err != nil {
				return nil, errors.WithMessage(err, "failed to get qr code")
			}
			return map[string]any{
				"qrcode": res.ImgBase64,
			}, nil
		}
	}
	return nil, nil
}

func (app *FormAPP) GetAllForms(_ *titan.Context) (*form.GetFormsResponse, error) {
	forms := app.printerFormRepo.GetAllForms()
	return &form.GetFormsResponse{
		Forms: forms,
	}, nil
}

func (app *FormAPP) getPrintResult(ctx *titan.Context, formName form_common.FormName, pdfUrls []string) ([]form_common.PrintResult, error) {
	// pdfUrls format is bucketName/fileName
	printSafeResult := slice.NewSyncSlice[form_common.PrintResult](nil)
	var errGroup function.ErrorGroupRecover
	for _, f := range pdfUrls {
		var bucketName, fileName string
		errGroup.Go(func() error {
			temp := strings.Split(f, "/")
			if len(temp) == 2 {
				bucketName, fileName = temp[0], temp[1]
			}
			url, err := app.minioClient.PresignedGetObject(ctx, bucketName, fileName, time.Hour, url.Values{})
			if err != nil {
				return errors.WithMessage(err, "get presigned url form pdf")
			}
			printSafeResult.Append(form_common.PrintResult{
				FormName: formName,
				FormUrl:  url.String(),
			})
			return nil
		})
	}
	if err := errGroup.Wait(); err != nil {
		return nil, errors.WithMessage(err, "failed to get presigned url eau form")
	}

	printResult := printSafeResult.Get()

	return printResult, nil
}

func (srv *FormAPP) GetFileUrl(ctx *titan.Context, request form.GetFileUrlRequest) (*form.GetFileUrlResponse, error) {
	if request.FileName == "" {
		return &form.GetFileUrlResponse{}, nil
	}
	urlParams := url.Values{
		"response-content-disposition": []string{fmt.Sprintf("inline; filename=\"%q\"", request.FileName)},
		"response-content-type":        []string{"application/pdf"},
	}
	url, err := srv.minioClient.PresignedGetObject(ctx, srv.bucketAkaData, request.FileName, 5*time.Minute, urlParams)
	if err != nil {
		return nil, err
	}

	return &form.GetFileUrlResponse{
		FileUrl: url.String(),
	}, nil
}

func (app *FormAPP) printEAU(ctx *titan.Context, req PrintEAURequest) (*form.PrescribeResponseV2, error) {
	if req.PrintOption == nil {
		return nil, errors.Errorf("print option is required")
	}

	eauRes, err := app.prescribeEAU(ctx, PrescribeEAURequest{
		prescribe:   req.Prescribe,
		printOption: req.PrintOption,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "failed to prescribe eau")
	}

	if eauRes == nil {
		return nil, errors.Errorf("failed to prescribe eau")
	}

	pdfUrls := []string{}

	eauSettings := eauRes.Settings
	if eauSettings.PrintOption.PrintForInsurance && eauRes.PdfForInsuranceUrl != nil {
		pdfUrls = append(pdfUrls, *eauRes.PdfForInsuranceUrl)
	}

	if eauSettings.PrintOption.PrintForPatient && eauRes.PdfForPatientUrl != nil {
		pdfUrls = append(pdfUrls, *eauRes.PdfForPatientUrl)
	}

	if eauSettings.PrintOption.PrintForEmployer && eauRes.PdfForEmployerUrl != nil {
		pdfUrls = append(pdfUrls, *eauRes.PdfForEmployerUrl)
	}

	req.Prescribe.EAUBundleUrl = eauRes.BundleUrl
	req.Prescribe.EAUStatus = &eauRes.Status
	req.Prescribe.PdfUrl = &pdfUrls

	if req.PrintOption.FormAction == form_common.FormAction_PrintFull {
		// update timeline by id
		eauTimeline, err := app.timelineService.UpdateEAUPrescribe(ctx, &req.Prescribe)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to update timeline by id")
		}

		if eauTimeline == nil {
			return nil, errors.Errorf("failed to update eau timeline by id")
		}
	}

	timelineId := util.GetPointerValue(req.Prescribe.Id)
	if util.GetPointerValue(req.Prescribe.PrintedDate) == 0 {
		return &form.PrescribeResponseV2{
			TimelineId: timelineId,
			PrintInfos: []form_common.PrintResult{},
		}, nil
	}

	printResult, err := app.getPrintResult(ctx, req.Prescribe.FormName, pdfUrls)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to get print result")
	}

	return &form.PrescribeResponseV2{
		TimelineId: timelineId,
		PrintInfos: printResult,
	}, nil
}

// New Prescribe create new timeline and return file url with printer profile
func (app *FormAPP) PrescribeV2(ctx *titan.Context, req form.PrescribeRequest) (*form.PrescribeResponseV2, error) {
	var err error
	printOption := req.PrintOption
	isEdit := true

	if req.Prescribe.Id == nil {
		req.Prescribe.Id = util.NewUUID()
		req.Prescribe.CreatedDate = util.UnixMillis(util.Now(ctx))
		isEdit = false
	}

	if printOption == nil || !util.GetPointerValue(printOption.PreventAddToTimeline) {
		if printOption != nil && !printOption.PdfWithBackground && printOption.FormAction == form_common.FormAction_PrintHeader {
			scheinIds := function.If(req.Prescribe.ScheinId != nil, []uuid.UUID{
				util.GetPointerValue(req.Prescribe.ScheinId),
			}, []uuid.UUID{})
			timeLineEntity := timeline_repo.TimelineEntity[patient_encounter_repo.EncounterForm]{
				Id:                req.Prescribe.Id,
				ContractId:        req.Prescribe.ContractId,
				TreatmentDoctorId: *req.Prescribe.TreatmentDoctorId,
				BillingDoctorId:   req.Prescribe.DoctorId,
				PatientId:         *req.Prescribe.PatientId,
				ScheinIds:         scheinIds,
				EncounterCase:     (*patient_encounter_repo.EncounterCase)(&req.Prescribe.EncounterCase),
				Payload: patient_encounter_repo.EncounterForm{
					Id:        req.Prescribe.Id,
					Prescribe: &req.Prescribe,
				},
				IsImported:       util.GetPointerValue(req.Prescribe.IsImported),
				AssignedToBsnrId: req.Prescribe.AssignedToBsnrId,
			}

			if isEdit {
				_, err = app.timelineService.Edit(ctx, timeLineEntity)
				if err != nil {
					return nil, errors.WithMessage(err, "failed to edit form timeline")
				}
			} else {
				_, err = app.timelineService.Create(ctx, timeLineEntity)
				if err != nil {
					return nil, errors.WithMessage(err, "failed to create form timeline")
				}
			}
		} else {
			scheinIds := function.If(req.Prescribe.ScheinId != nil, []uuid.UUID{
				util.GetPointerValue(req.Prescribe.ScheinId),
			}, []uuid.UUID{})
			isBGForms := slice.Contains([]form_common.FormName{form_common.F1000, form_common.F1050, form_common.F2100, form_common.F9990}, req.Prescribe.FormName)
			payload := patient_encounter_repo.EncounterForm{
				Id:        req.Prescribe.Id,
				Prescribe: &req.Prescribe,
			}

			if isBGForms {
				bgBillingItem, err := app.bgBillingService.GetBgBillingByScheinId(ctx, *req.Prescribe.ScheinId)

				if err != nil {
					return nil, errors.WithMessage(err, "Failed to get bg billing by schein id.")
				}
				if bgBillingItem == nil {
					return nil, nil
				}

				payload.BgInvoice = &doctor_letter_common.BgInvoice{
					BillingId:     bgBillingItem.Id,
					InvoiceNumber: bgBillingItem.InvoiceNumber,
					Amount:        bgBillingItem.PaidAmount,
					Status:        function.If(req.Prescribe.PrintedDate != nil, doctor_letter_common.InvoiceStatus_Billing_Printed, doctor_letter_common.InvoiceStatus_Billing_Saved),
				}
			}

			timeLineEntity := timeline_repo.TimelineEntity[patient_encounter_repo.EncounterForm]{
				Id:                req.Prescribe.Id,
				ContractId:        req.Prescribe.ContractId,
				TreatmentDoctorId: *req.Prescribe.TreatmentDoctorId,
				BillingDoctorId:   req.Prescribe.DoctorId,
				PatientId:         *req.Prescribe.PatientId,
				ScheinIds:         scheinIds,
				EncounterCase:     (*patient_encounter_repo.EncounterCase)(&req.Prescribe.EncounterCase),
				Payload:           payload,
				IsImported:        util.GetPointerValue(req.Prescribe.IsImported),
				AssignedToBsnrId:  req.Prescribe.AssignedToBsnrId,
			}

			if isEdit {
				_, err = app.timelineService.Edit(ctx, timeLineEntity)
				if err != nil {
					return nil, errors.WithMessage(err, "failed to edit form timeline")
				}
			} else {
				if req.Prescribe.ContractId != nil && req.Prescribe.FormName == form_common.BKK_VAG_FA_PT_BW_Ausschreibeformular_V5 && !util.GetPointerValue(req.Prescribe.IsTerminated) {
					if err := app.CreateServicePtz5Automatic(ctx, req, scheinIds); err != nil {
						return nil, err
					}
				}
				_, err = app.timelineService.Create(ctx, timeLineEntity)
				if err != nil {
					return nil, errors.WithMessage(err, "failed to create form timeline")
				}
			}

			if printOption == nil { // not print then return
				return &form.PrescribeResponseV2{
					PrintInfos: []form_common.PrintResult{},
					TimelineId: util.GetPointerValue(req.Prescribe.Id),
				}, nil
			}
		}
	}

	if req.Prescribe.FormName == form_common.Muster_1 {
		eauSetting := req.EAUSetting
		if req.PrintOption.FormAction != form_common.FormAction_PrintFull {
			eauSetting = &eau_common.EAUSetting{
				PrintOption: eau_common.PrintOption{
					PrintForInsurance: true,
					PrintForEmployer:  true,
					PrintForPatient:   true,
				},
				PrintOnly: util.NewBool(true),
			}
		}

		if eauSetting == nil {
			return nil, errors.Errorf("eau setting is required for form %s", req.Prescribe.FormName)
		}

		req.Prescribe.EAUSetting = eauSetting
		return app.printEAU(ctx, PrintEAURequest{
			Prescribe:   req.Prescribe,
			PrintOption: req.PrintOption,
		})
	}

	formNames := getRelatedForms(req.Prescribe.FormName)
	var formInfos = []form_print.BaseFormInfo{}

	for _, formName := range formNames {
		printFormName := formName

		if printFormName == form_common.Muster_4 && !req.PrintOption.PdfWithBackground {
			printFormName = form_common.Muster_4_A5
		}

		formInfos = append(formInfos, form_print.BaseFormInfo{
			FormSetting:    req.Prescribe.Payload,
			FormName:       printFormName,
			PrintDate:      util.GetPointerValue(req.Prescribe.PrintedDate),
			PrintOption:    req.PrintOption,
			PrescribeDate:  req.Prescribe.PrescribeDate,
			PrescriptionId: req.Prescribe.Id,
			PrescribeData:  &req.Prescribe,
		})
	}

	printResult, err := app.formPrint.PrintToPDF(ctx,
		formInfos,
		util.GetPointerValue(req.Prescribe.TreatmentDoctorId),
		util.GetPointerValue(req.Prescribe.PatientId),
		util.GetPointerValue(req.Prescribe.ScheinId),
		req.Prescribe.AssignedToBsnrId,
	)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to print pdf")
	}

	return &form.PrescribeResponseV2{
		TimelineId: *req.Prescribe.Id,
		PrintInfos: printResult,
	}, nil
}

// NOTE: SV flow only
func (app *FormAPP) GetIndicatorActiveIngredients(ctx *titan.Context, request form.GetIndicatorActiveIngredientsRequest) (*form.GetIndicatorActiveIngredientsResponse, error) {
	hpmConfig, err := app.bsnrService.GetHpmConfig(ctx, ctx.UserInfo().BsnrId)
	if err != nil {
		return nil, err
	}
	hpmRestService := hpm_rest_client.NewServiceRestWithEndpoint(hpmConfig.Endpoint)
	contr := app.contractService.GetContractDetailById(request.ContractId)
	if contr == nil {
		return nil, errors.New("contract not found")
	}
	vpId, err := app.getVpId(ctx, contr)
	if err != nil {
		return nil, err
	}
	result, err := hpmRestService.GetIndicatorActiveIngredients(ctx, request.ContractId, vpId)
	if err != nil {
		return nil, err
	}
	return &form.GetIndicatorActiveIngredientsResponse{
		Data: slice.Map(result, func(ingredient hpm_rest_client.GetIndicatorActiveIngredientsResponse) form.AtcDiagnoseCode {
			return form.AtcDiagnoseCode{
				AtcCode:      ingredient.AtcCode,
				DiagnoseCode: ingredient.DiagnoseCode,
			}
		}),
	}, nil
}

// get related forms include input formName
func getRelatedForms(formName form_common.FormName) []form_common.FormName {
	result := []form_common.FormName{formName}
	if forms, found := relatedForms[formName]; found {
		result = append(result, forms...)
	}
	return result
}
