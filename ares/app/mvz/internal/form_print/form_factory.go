package form_print

import (
	"encoding/json"
	"io/fs"

	"emperror.dev/errors"
	barcode_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/barcode"
	"git.tutum.dev/medi/tutum/ares/pkg/formkey"
	barcode_common "git.tutum.dev/medi/tutum/ares/service/barcode/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	form_common "git.tutum.dev/medi/tutum/ares/service/domains/form/common"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"gitlab.com/silenteer-oss/titan"
)

type FormInfo interface {
	Get(ctx *titan.Context) (map[string]string, error)
}

type FormParams struct {
	printData              PrintData
	patientHeaderMap       map[string]string
	doctorId               uuid.UUID
	bsnrId                 *uuid.UUID
	form                   BaseFormInfo
	patientId              uuid.UUID
	GetEmployeeProfileById func(ctx *titan.Context, request *profile.EmployeeProfileGetRequest) (*profile.EmployeeProfileResponse, error)
	barcodeApp             barcode_api.BarcodeApp
	pdfFile                fs.File
}

func NewFormInfo(ctx *titan.Context, f FormParams) (FormInfo, error) {
	doctor, err := f.GetEmployeeProfileById(ctx, &profile.EmployeeProfileGetRequest{
		OriginalId: &f.doctorId,
		BsnrId:     f.bsnrId,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "fail to get doctor by Id")
	}
	labelStamp := function.If(doctor.DoctorStamp != "", doctor.DoctorStamp, doctor.BsnrPracticeStamp)

	switch f.form.FormType {
	case form_common.FormType_diga:
		return &DigaFormInfo{
			patientHeaderMap: f.patientHeaderMap,
			BaseFormInfo:     f.form,
			labelStamp:       labelStamp,
		}, nil
	case form_common.FormType_heimi:
		return &HeimiFormInfo{
			BaseFormInfo:     f.form,
			PatientId:        &f.patientId,
			patientHeaderMap: f.patientHeaderMap,
			EmployeeId:       &f.doctorId,
			barcodeApp:       f.barcodeApp,
			PrescriptionId:   f.printData.PrescriptionId,
			labelStamp:       labelStamp,
			BsnrId:           f.bsnrId,
		}, nil
	case form_common.FormType_himi:
		return &HimiFormInfo{
			patientHeaderMap: f.patientHeaderMap,
			BaseFormInfo:     f.form,
			PatientId:        &f.patientId,
			EmployeeId:       &f.doctorId,
			barcodeApp:       f.barcodeApp,
			PrescriptionId:   f.printData.PrescriptionId,
			labelStamp:       labelStamp,
			BsnrId:           f.bsnrId,
		}, nil
	case form_common.FormType_medication:
		return &MedicineFormInfo{
			patientHeaderMap: f.patientHeaderMap,
			BaseFormInfo:     f.form,
			PatientId:        &f.patientId,
			EmployeeId:       &f.doctorId,
			labelStamp:       labelStamp,
		}, nil
	default:
		return &MusterFormInfo{
			patientHeaderMap: f.patientHeaderMap,
			BaseFormInfo:     f.form,
			PatientId:        &f.patientId,
			EmployeeId:       &f.doctorId,
			barcodeApp:       f.barcodeApp,
			labelStamp:       labelStamp,
			BsnrId:           f.bsnrId,
		}, nil
	}
}

type MusterFormInfo struct {
	patientHeaderMap map[string]string
	BaseFormInfo
	barcodeApp     barcode_api.BarcodeApp
	PatientId      *uuid.UUID
	EmployeeId     *uuid.UUID
	PrescriptionId *uuid.UUID
	labelStamp     string
	BsnrId         *uuid.UUID
}

var (
	FormMap = map[form_common.FormName]string{
		form_common.Muster_2A:   "Muster_2A_n",
		form_common.Muster_2B:   "Muster_2B_n",
		form_common.Muster_2C:   "Muster_2C_n",
		form_common.Muster_4:    "Muster_4_n",
		form_common.Muster_4_A5: "Muster_4_print_preview_n",
		form_common.Muster_6:    "Muster_6_n",
		form_common.Muster_13:   "Muster_13_n",
		form_common.Muster_15:   "Muster_15_n",
		form_common.Muster_1:    "Muster_1_n",
		form_common.Muster_8:    "Muster_8_n",
		form_common.Muster_8A:   "Muster_8A_n",
		form_common.Muster_10:   "Muster_10_v2",
		form_common.Muster_10A:  "Muster_10A_Fill_n",
		form_common.Muster_50:   "Muster_50_n",
		form_common.Muster_51:   "Muster_51_n",
		form_common.Muster_22A:  "Muster_22A_n",
		form_common.Muster_22B:  "Muster_22B_n",
		form_common.Muster_22C:  "Muster_22C_n",
		form_common.Muster_22D:  "Muster_22D_n",
	}
)

func (f *MusterFormInfo) Get(ctx *titan.Context) (map[string]string, error) {
	var (
		barcodeContent string
		formMap        map[string]string
		err            error
	)
	barcodeContent, err = f.GetBarcode(ctx)
	if err != nil {
		return nil, err
	}

	isRemoveBackground := !f.PrintOption.PdfWithBackground

	if !isRemoveBackground {
		f.patientHeaderMap = formkey.FillFormMapWithPRF_NR(f.patientHeaderMap)
		f.patientHeaderMap = formkey.FillFormMapWithEHIC_PRF_NR(f.patientHeaderMap)
	}
	switch f.PrintOption.FormAction {
	case form_common.FormAction_PrintWithoutContent:
		patientHeaderMapBlanco := formkey.FillPatientHeaderForm(nil, f.FormName, f.PrescribeDate, false)
		patientHeaderMapBlanco = formkey.FillFormMapWithPRF_NR(patientHeaderMapBlanco)
		patientHeaderMapBlanco = formkey.FillFormMapWithEHIC_PRF_NR(patientHeaderMapBlanco)
		patientHeaderMapBlanco["barcode"] = barcodeContent
		patientHeaderMapBlanco["barcode_0"] = barcodeContent
		patientHeaderMapBlanco["barcode_1"] = barcodeContent
		formMap = patientHeaderMapBlanco
	case form_common.FormAction_PrintHeader:
		f.patientHeaderMap["barcode"] = barcodeContent
		f.patientHeaderMap["label_doctor_stamp"] = f.labelStamp
		f.patientHeaderMap["barcode_0"] = barcodeContent
		f.patientHeaderMap["label_doctor_stamp_0"] = f.labelStamp
		f.patientHeaderMap["barcode_1"] = barcodeContent
		f.patientHeaderMap["label_doctor_stamp_1"] = f.labelStamp
		formMap = f.patientHeaderMap
	case form_common.FormAction_PrintFull:
		formMap, err = formkey.FillFormMap(ctx, f.patientHeaderMap, barcodeContent, f.labelStamp, f.FormSetting, isRemoveBackground)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to fill form map")
		}
		formMap = formkey.FilterFormMapToPrint(formMap, f.FormName, isRemoveBackground)
	}

	return formMap, nil
}

func (f *MusterFormInfo) GetBarcode(ctx *titan.Context) (string, error) {
	barcodeRes, err := f.barcodeApp.GetBarcode(ctx, barcode_api.GetBarcodeRequest{
		PatientId:        util.GetPointerValue(f.PatientId),
		EmployeeId:       util.GetPointerValue(f.EmployeeId),
		FormName:         f.FormName,
		PrescriptionId:   util.GetPointerValue(f.BaseFormInfo.PrescriptionId),
		TopicName:        barcode_common.TopicName_Form,
		HasTimelineEntry: false,
		PrescribeData:    f.BaseFormInfo.PrescribeData,
		BsnrId:           f.BsnrId,
	})
	if err != nil {
		return "", errors.WithMessage(err, "failed to get barcode")
	}

	return barcodeRes.ImgBase64, nil
}

type DigaFormInfo struct {
	patientHeaderMap map[string]string
	BaseFormInfo
	labelStamp string
}

func (f *DigaFormInfo) Get(ctx *titan.Context) (map[string]string, error) {
	var formMap map[string]string
	var err error

	switch f.PrintOption.FormAction {
	case form_common.FormAction_PrintWithoutContent:
		formMap = map[string]string{}
	case form_common.FormAction_PrintHeader:
		f.patientHeaderMap["label_doctor_stamp"] = f.labelStamp
		f.patientHeaderMap["label_doctor_stamp_0"] = f.labelStamp
		f.patientHeaderMap["label_doctor_stamp_1"] = f.labelStamp
		formMap = f.patientHeaderMap
	case form_common.FormAction_PrintFull:
		formMap, err = formkey.FillFormMap(ctx, f.patientHeaderMap, "", f.labelStamp, f.FormSetting, !f.PrintOption.PdfWithBackground)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to fill form map")
		}
		formMap["label_ik_number"] = pseudoIK_FORM907
	}

	return formMap, nil
}

type HimiFormInfo struct {
	patientHeaderMap map[string]string
	BaseFormInfo
	barcodeApp     barcode_api.BarcodeApp
	PatientId      *uuid.UUID
	EmployeeId     *uuid.UUID
	BsnrId         *uuid.UUID
	PrescriptionId *uuid.UUID
	labelStamp     string
}

func (f *HimiFormInfo) Get(ctx *titan.Context) (map[string]string, error) {
	var formMapData map[string]string
	var err error
	barcodeContent, err := f.GetBarcode(ctx)
	if err != nil {
		return nil, err
	}
	formMapData, err = formkey.NewFormMap[string](f.BaseFormInfo.FormSetting)
	if err != nil {
		return nil, errors.WithMessagef(err, "Cannot parse form %s", f.BaseFormInfo.FormSetting)
	}

	// for case custom print medication by lines
	delete(formMapData, "label_medication")

	formMapDataConvert, err := json.Marshal(formMapData)

	if err != nil {
		return nil, err
	}

	formMapDataStr := string(formMapDataConvert)

	formMap, err := formkey.FillFormMap(ctx, f.patientHeaderMap, barcodeContent, f.labelStamp, formMapDataStr, !f.PrintOption.PdfWithBackground)

	if f.BaseFormInfo.HasSupportForm907 {
		formMap["label_ik_number"] = pseudoIK_FORM907
	}

	if err != nil {
		return nil, errors.WithMessage(err, "failed to fill form map")
	}

	return formMap, nil
}

func (f *HimiFormInfo) GetBarcode(ctx *titan.Context) (string, error) {
	barcodeRes, err := f.barcodeApp.GetBarcode(ctx, barcode_api.GetBarcodeRequest{
		PatientId:        util.GetPointerValue(f.PatientId),
		EmployeeId:       util.GetPointerValue(f.EmployeeId),
		FormName:         f.FormName,
		PrescriptionId:   util.GetPointerValue(f.BaseFormInfo.PrescriptionId),
		TopicName:        barcode_common.TopicName_Himi,
		HasTimelineEntry: true,
		BsnrId:           f.BsnrId,
	})
	if err != nil {
		return "", errors.WithMessage(err, "failed to get barcode")
	}

	return barcodeRes.ImgBase64, nil
}

type HeimiFormInfo struct {
	BaseFormInfo
	patientHeaderMap map[string]string
	barcodeApp       barcode_api.BarcodeApp
	PatientId        *uuid.UUID
	EmployeeId       *uuid.UUID
	PrescriptionId   *uuid.UUID
	labelStamp       string
	BsnrId           *uuid.UUID
}

func (f *HeimiFormInfo) Get(ctx *titan.Context) (map[string]string, error) {
	barcodeContent, err := f.GetBarcode(ctx)
	if err != nil {
		return nil, err
	}
	formMap, err := formkey.FillFormMap(ctx, f.patientHeaderMap, barcodeContent, f.labelStamp, f.FormSetting, !f.PrintOption.PdfWithBackground)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to fill form map")
	}

	formMap["label_ik_number_1"] = formMap["label_ik_number_0"]
	formMap["label_insurance_number_1"] = formMap["label_insurance_number_0"]

	return formMap, nil
}

func (f *HeimiFormInfo) GetBarcode(ctx *titan.Context) (string, error) {
	barcodeRes, err := f.barcodeApp.GetBarcode(ctx, barcode_api.GetBarcodeRequest{
		PatientId:        util.GetPointerValue(f.PatientId),
		EmployeeId:       util.GetPointerValue(f.EmployeeId),
		FormName:         f.FormName,
		PrescriptionId:   util.GetPointerValue(f.BaseFormInfo.PrescriptionId),
		TopicName:        barcode_common.TopicName_Heimi,
		HasTimelineEntry: true,
		BsnrId:           f.BsnrId,
	})
	if err != nil {
		return "", errors.WithMessage(err, "failed to get barcode")
	}

	return barcodeRes.ImgBase64, nil
}

type MedicineFormInfo struct {
	BaseFormInfo
	patientHeaderMap map[string]string
	PatientId        *uuid.UUID
	EmployeeId       *uuid.UUID
	labelStamp       string
}

func (f *MedicineFormInfo) Get(ctx *titan.Context) (map[string]string, error) {
	var formMap map[string]string
	var err error
	switch f.PrintOption.FormAction {
	case form_common.FormAction_PrintWithoutContent:
		formMap = map[string]string{}
	case form_common.FormAction_PrintHeader:
		f.patientHeaderMap["label_doctor_stamp"] = f.labelStamp
		f.patientHeaderMap["label_doctor_stamp_0"] = f.labelStamp
		f.patientHeaderMap["label_doctor_stamp_1"] = f.labelStamp
		formMap = f.patientHeaderMap
	case form_common.FormAction_PrintFull:
		formMap, err = formkey.FillFormMap(ctx, f.patientHeaderMap, "", f.labelStamp, f.FormSetting, !f.PrintOption.PdfWithBackground)
		if err != nil {
			return nil, errors.WithMessage(err, "failed to fill form map")
		}

		if f.HasSupportForm907 {
			formMap["label_ik_number"] = pseudoIK_FORM907
		}
	}

	return formMap, nil
}
