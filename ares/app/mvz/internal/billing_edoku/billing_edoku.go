package billing_edoku

import (
	billing_edoku_api "git.tutum.dev/medi/tutum/ares/app/mvz/api/billing_edoku"
	billing_edoku_service "git.tutum.dev/medi/tutum/ares/service/domains/billing_edoku/service"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
)

type BillingEDokuApp struct {
	billingEDokuService *billing_edoku_service.BillingEDokuService
}

var BillingEDokuAppMod = submodule.Make[billing_edoku_api.BillingEDokuApp](func(billingEDokuService *billing_edoku_service.BillingEDokuService) billing_edoku_api.BillingEDokuApp {
	return &BillingEDokuApp{
		billingEDokuService: billingEDokuService,
	}
}, billing_edoku_service.BillingEDokuServiceMod)

func (*BillingEDokuApp) Troubleshoot(_ *titan.Context, _ *billing_edoku_api.TroubleshootRequest) (*billing_edoku_api.TroubleshootResponse, error) {
	// Implement Troubleshoot logic here
	return &billing_edoku_api.TroubleshootResponse{}, nil
}

func (app *BillingEDokuApp) GetValidationList(context *titan.Context, request *billing_edoku_api.GetValidationListRequest) (*billing_edoku_api.GetValidationListResponse, error) {
	// Implement GetValidationList logic here
	res, err := app.billingEDokuService.GetValidationList(context, &billing_edoku_service.GetValidationListRequest{
		Quarter:             request.Quarter,
		BsnrId:              request.BsnrId,
		DocumentType:        request.DocumentType,
		OpenPreviousQuarter: request.OpenPreviousQuarter,
	})

	if err != nil {
		return nil, err
	}

	if res == nil {
		return nil, nil
	}

	return &billing_edoku_api.GetValidationListResponse{
		BillingValidationList: res.BillingValidationList,
		TotalPatient:          int32(res.TotalPatient),
	}, nil
}

func (*BillingEDokuApp) GetBillingSummary(_ *titan.Context, _ *billing_edoku_api.GetBillingSummaryRequest) (*billing_edoku_api.GetBillingSummaryResponse, error) {
	// Implement GetBillingSummary logic here
	return &billing_edoku_api.GetBillingSummaryResponse{}, nil
}

func (app *BillingEDokuApp) PrepareForShipping(ctx *titan.Context, req *billing_edoku_api.PrepareForShippingRequest) error {
	return app.billingEDokuService.HandlePrepareForShipping(ctx, &billing_edoku_service.PrepareForShippingRequest{
		BillingHistoryId: req.BillingHistoryId,
	})
}

func (app *BillingEDokuApp) SendMail(ctx *titan.Context, req *billing_edoku_api.SendMailRequest) error {
	return app.billingEDokuService.HandleSendMail(ctx, &billing_edoku_service.SendMailRequest{
		BillingHistoryId:    req.BillingHistoryId,
		SenderMailSettingId: req.SenderMailSettingId,
		TestToMail:          req.TestMailTo,
	})
}

func (*BillingEDokuApp) DownloadBillingFile(_ *titan.Context, _ *billing_edoku_api.DownloadBillingFileRequest) (*billing_edoku_api.DownloadBillingFileResponse, error) {
	// Implement DownloadBillingFile logic here
	return &billing_edoku_api.DownloadBillingFileResponse{}, nil
}

func (app *BillingEDokuApp) UndoSubmission(ctx *titan.Context, req *billing_edoku_api.UndoSubmissionRequest) error {
	return app.billingEDokuService.UndoSubmission(ctx, &billing_edoku_service.UndoSubmissionRequest{
		BillingHistoryId: req.BillingHistoryId,
	})
}

func (app *BillingEDokuApp) GetDispatchList(ctx *titan.Context, req *billing_edoku_api.GetDispatchListRequest) (*billing_edoku_api.GetDispatchListResponse, error) {
	res, err := app.billingEDokuService.GetDispatchList(ctx, &billing_edoku_service.GetDispatchListRequest{
		Query: req.Query,
	})
	if err != nil {
		return nil, err
	}

	return &billing_edoku_api.GetDispatchListResponse{
		BillingHistories: res,
	}, nil
}

func (s *BillingEDokuApp) GetEdokuBillingSelection(context *titan.Context) (*billing_edoku_api.GetEdokuBillingSelectionResponse, error) {
	res, err := s.billingEDokuService.GetEdokuBillingSelection(context)
	if err != nil {
		return nil, err
	}

	return &billing_edoku_api.GetEdokuBillingSelectionResponse{
		Quarters:      res.Quarters,
		Bsnrs:         res.Bsnrs,
		DocumentTypes: res.DocumentTypes,
	}, nil
}

func (s *BillingEDokuApp) CreateBilling(ctx *titan.Context, req *billing_edoku_api.CreateBillingRequest) (*billing_edoku_api.CreateBillingResponse, error) {
	res, err := s.billingEDokuService.CreateBilling(ctx, &billing_edoku_service.CreateBillingRequest{
		DocumentIds:           req.DocumentIds,
		Quarter:               req.Quarter,
		Bsnr:                  req.Bsnr,
		DMPValue:              req.DMPValue,
		IsOpenPreviousQuarter: req.IsOpenPreviousQuarter,
	})

	if err != nil {
		return nil, err
	}

	if res == nil {
		return nil, nil
	}

	return &billing_edoku_api.CreateBillingResponse{
		Status:                            res.Status,
		DMPBillingHistoryId:               res.DMPBillingHistoryId,
		DMPBillingFieldsValidationResults: res.DMPBillingFieldsValidationResults,
	}, nil
}

func (s *BillingEDokuApp) CheckForValidation(ctx *titan.Context, req *billing_edoku_api.CheckForValidationRequest) (*billing_edoku_api.CheckForValidationResponse, error) {
	res, err := s.billingEDokuService.CheckForValidation(ctx, &billing_edoku_service.CheckForValidationRequest{
		DocumentIds: req.DocumentIds,
		Quarter:     req.Quarter,
		Bsnr:        req.Bsnr,
	})
	if err != nil {
		return nil, err
	}

	return &billing_edoku_api.CheckForValidationResponse{
		Status:                            res.Status,
		DMPBillingFieldsValidationResults: res.DMPBillingFieldsValidationResults,
	}, nil
}

func (s *BillingEDokuApp) GetBillingHistory(ctx *titan.Context, req *billing_edoku_api.GetBillingHistoryRequest) (*billing_edoku_api.GetBillingHistoryResponse, error) {
	res, err := s.billingEDokuService.GetBillingHistory(ctx, &billing_edoku_service.GetBillingHistoryRequest{
		BillingHistoryId: req.BillingHistoryId,
	})
	if err != nil {
		return nil, err
	}

	return &billing_edoku_api.GetBillingHistoryResponse{
		BillingHistory: res,
	}, nil
}

func (s *BillingEDokuApp) GetEdokuDocumentByIds(ctx *titan.Context, req *billing_edoku_api.GetEdokuDocumentByIdsRequest) (*billing_edoku_api.GetEdokuDocumentByIdsResponse, error) {
	documents, err := s.billingEDokuService.GetEdokuDocumentationByIds(ctx, req.DocumentIds)
	if err != nil {
		return nil, err
	}
	return &billing_edoku_api.GetEdokuDocumentByIdsResponse{
		Documents: documents,
	}, nil
}
