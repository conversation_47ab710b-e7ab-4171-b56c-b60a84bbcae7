package bg_billing

import (
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/bg_billing"
	bg_billing_service "git.tutum.dev/medi/tutum/ares/service/domains/bg_billing/service"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/titan"
)

var BgBillingAppMod = submodule.Make[bg_billing.BgBillingApp](NewBgBillingApp, bg_billing_service.BgBillingServiceMod)

type BgBillingApp struct {
	service *bg_billing_service.Service
}

func NewBgBillingApp(bgBillingService *bg_billing_service.Service) bg_billing.BgBillingApp {
	return &BgBillingApp{
		service: bgBillingService,
	}
}

func (app *BgBillingApp) GetBgBilling(ctx *titan.Context, request *bg_billing.GetBgBillingsRequest) (*bg_billing.GetBgBillingsResponse, error) {
	entities, total, err := app.service.GetBgBilling(ctx, request)

	if err != nil {
		return nil, err
	}

	if entities == nil {
		return nil, nil
	}

	return &bg_billing.GetBgBillingsResponse{
		Items: entities,
		Total: total,
	}, nil
}

func (app *BgBillingApp) GetUvGoaServiceCode(ctx *titan.Context, request *bg_billing.GetUvGoaServiceCodeRequest) (*bg_billing.GetUvGoaServiceCodeResponse, error) {
	return app.service.GetUvGoaServiceCode(ctx, request)
}

func (app *BgBillingApp) GetUvGoaServiceCodeByIds(ctx *titan.Context, request *bg_billing.GetUvGoaServiceCodeByIdsRequest) (*bg_billing.GetUvGoaServiceCodeByIdsResponse, error) {
	return app.service.GetUvGoaServiceCodeByIds(ctx, request)
}

func (app *BgBillingApp) GetBgBillingByScheinId(ctx *titan.Context, request *bg_billing.GetBgBillingByScheinIdRequest) (*bg_billing.GetBgBillingByScheinIdResponse, error) {
	entity, err := app.service.GetBgBillingByScheinId(ctx, *request.ScheinId)
	if err != nil {
		return nil, err
	}
	if entity == nil {
		return nil, nil
	}

	return &bg_billing.GetBgBillingByScheinIdResponse{
		Item: entity,
	}, nil
}

func (app *BgBillingApp) GetBgBillingById(ctx *titan.Context, request *bg_billing.GetBgBillingByIdRequest) (*bg_billing.GetBgBillingByIdResponse, error) {
	billingEntity, err := app.service.GetBgBillingById(ctx, *request.Id)
	if err != nil {
		return nil, err
	}

	if billingEntity == nil {
		return nil, nil
	}
	return &bg_billing.GetBgBillingByIdResponse{
		Item: billingEntity.ToBgBillingItem(),
	}, nil
}

func (app *BgBillingApp) MarkBgBillingPaid(ctx *titan.Context, request *bg_billing.MarkBgBillingPaidRequest) (*bg_billing.MarkBgBillingPaidResponse, error) {
	timelineModel, err := app.service.MarkBgBillingPaid(ctx, request)
	if err != nil {
		return nil, err
	}
	if timelineModel == nil {
		return nil, nil
	}
	return &bg_billing.MarkBgBillingPaidResponse{
		TimelineModel: timelineModel,
	}, nil
}

func (app *BgBillingApp) MarkBgBillingUnpaid(ctx *titan.Context, request *bg_billing.MarkBgBillingUnpaidRequest) (*bg_billing.MarkBgBillingUnpaidResponse, error) {
	billingItem, err := app.service.MarkBgBillingUnpaid(ctx, request)
	if err != nil {
		return nil, err
	}

	if billingItem == nil {
		return nil, nil
	}
	return &bg_billing.MarkBgBillingUnpaidResponse{
		Item: billingItem,
	}, nil
}

func (app *BgBillingApp) MarkBgBillingCancelled(ctx *titan.Context, request *bg_billing.MarkBgBillingCancelledRequest) (*bg_billing.MarkBgBillingCancelledResponse, error) {
	billingItem, err := app.service.MarkBgBillingCancelled(ctx, request)
	if err != nil {
		return nil, err
	}

	if billingItem == nil {
		return nil, nil
	}
	return &bg_billing.MarkBgBillingCancelledResponse{
		Item: billingItem,
	}, nil
}

func (app *BgBillingApp) GetPrintedInvoices(ctx *titan.Context, request *bg_billing.GetPrintedInvoicesRequest) (*bg_billing.GetPrintedInvoicesResponse, error) {
	timelineModels, err := app.service.GetPrintedInvoices(ctx, request)
	if err != nil {
		return nil, err
	}

	return &bg_billing.GetPrintedInvoicesResponse{
		TimelineModels: timelineModels,
	}, nil
}

func (app *BgBillingApp) GetListDoctor(ctx *titan.Context) (*bg_billing.GetListDoctorResponse, error) {
	doctorModels, err := app.service.GetListDoctor(ctx)
	if err != nil {
		return nil, err
	}
	return &bg_billing.GetListDoctorResponse{
		Doctors: doctorModels,
	}, nil
}

func (app *BgBillingApp) GetListInsurance(ctx *titan.Context) (*bg_billing.GetListInsuranceResponse, error) {
	insuranceModels, err := app.service.GetListInsurance(ctx)
	if err != nil {
		return nil, err
	}
	return &bg_billing.GetListInsuranceResponse{
		Insurances: insuranceModels,
	}, nil
}
