package bmp

import (
	"time"

	"github.com/spf13/cast"

	"emperror.dev/errors"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/bmp"
	medicine_service "git.tutum.dev/medi/tutum/ares/app/mvz/api/medicine"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/medicine_kbv"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	medicine "git.tutum.dev/medi/tutum/ares/app/mvz/internal/medicine_kbv"
	medicine_util "git.tutum.dev/medi/tutum/ares/pkg/medicine"
	bmp_service "git.tutum.dev/medi/tutum/ares/service/domains/api/bmp"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/bmp/keytab"
	medicine_common "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/common"
	"git.tutum.dev/medi/tutum/ares/share"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/jinzhu/copier"
	"github.com/submodule-org/submodule.go/v2"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
)

var (
	ErrNoSelectedValue = errors.New("NoSelectedValue")
)

type BmpApp struct {
	keytabRepo        keytab.BmpMedicationPlanKeytabDefaultRepository
	bmpService        bmp_service.BmpService
	medicineAppSocket *medicine_service.MedicineSocketNotifier
	medicineKBVApp    medicine_kbv.MedicineKbvApp
	notifier          bmp.BmpSocketNotifier
}

var BmpAppMod = submodule.Make[bmp.BmpApp](
	NewBmpApp,
	config.SocketServiceClientMod,
	share.BmpDomainServiceMod,
	medicine.MedicineKbvAppMod,
)

func NewBmpApp(
	socket *socket_api.SocketServiceClient,
	bmpServiceClient bmp_service.BmpService,
	medicineKBVAPP medicine_kbv.MedicineKbvApp,
) bmp.BmpApp {
	return &BmpApp{
		keytabRepo:        keytab.NewBmpMedicationPlanKeytabDefaultRepository(),
		bmpService:        bmpServiceClient,
		medicineAppSocket: medicine_service.NewMedicineSocketNotifier(socket),
		medicineKBVApp:    medicineKBVAPP,
		notifier:          *bmp.NewBmpSocketNotifier(socket),
	}
}

func (app *BmpApp) ParseBarcode(ctx *titan.Context, request bmp_service.ParseBarcodeRequest) (*bmp_service.ParseBarcodeResponse, error) {
	return app.bmpService.ParseBarcode(ctx, request)
}

func (app *BmpApp) ParseBarcodeForUnmatch(ctx *titan.Context, request bmp_service.ParseBarcodeRequest) (*bmp_service.ParseBarcodeResponse, error) {
	return app.bmpService.ParseBarcodeForUnmatch(ctx, request)
}

func (app *BmpApp) CompareWithBmp(ctx *titan.Context, request bmp_service.CompareWithBmpRequest) (*bmp_service.CompareWithBmpResponse, error) {
	return app.bmpService.CompareWithBmp(ctx, request)
}

func (app *BmpApp) CompareWithBmpEvent(ctx *titan.Context, request bmp_service.CompareWithBmpRequest) error {
	ctxClone := ctx.Clone()
	go func() {
		start := ctxClone.RequestCurrentTime()
		result, err := app.bmpService.CompareWithBmp(ctxClone, request)
		if err != nil {
			return
		}
		elapsed := time.Since(start)
		if err := app.notifier.NotifyCareProviderResultCompareBmp(ctxClone, &bmp.EventResultCompareBmp{
			UserId:                      ctx.UserInfo().UserUUID(),
			PatientId:                   &request.PatientId,
			MedicationPlanGroupCompared: result.MedicationPlanGroupCompared,
			SessionId:                   request.MergeSessionId,
			MeasureExcutionTime:         float32(elapsed.Seconds()),
			AdditionalData:              result.AdditionalData,
		}); err != nil {
			ctx.Logger().Error("Failed to notify care provider result compare bmp", "err", err.Error())
			return
		}
	}()
	return nil
}

func (app *BmpApp) PrintPdf(ctx *titan.Context, request bmp_service.PrintPdfRequest) (*bmp_service.PrintPdfResponse, error) {
	return app.bmpService.PrintPdf(ctx, request)
}

func (app *BmpApp) PrintPdfViaMMI(ctx *titan.Context, request bmp_service.PrintPdfRequest) (*bmp_service.PrintPdfResponse, error) {
	return app.bmpService.PrintPdfViaMMI(ctx, request)
}

func (app *BmpApp) GetBmpList(ctx *titan.Context, request bmp_service.GetBmpListRequest) (*bmp_service.GetBmpListResponse, error) {
	return app.bmpService.GetBmpList(ctx, request)
}

func (app *BmpApp) AddEntry(ctx *titan.Context, request bmp_service.AddEntryRequest) (*bmp_service.EntryResponse, error) {
	return app.bmpService.AddEntry(ctx, request)
}

func (app *BmpApp) UpdateEntry(ctx *titan.Context, request bmp_service.UpdateEntryRequest) (*bmp_service.EntryResponse, error) {
	return app.bmpService.UpdateEntry(ctx, request)
}

func (app *BmpApp) DeleteEntry(ctx *titan.Context, request bmp_service.DeleteEntryRequest) error {
	return app.bmpService.DeleteEntry(ctx, request)
}

func (app *BmpApp) SaveSortedIdList(ctx *titan.Context, request bmp_service.SaveSortedIdListRequest) error {
	return app.bmpService.SaveSortedIdList(ctx, request)
}

func (app *BmpApp) SaveMergeResult(ctx *titan.Context, request bmp_service.SaveMergeResultRequest) (*bmp_service.GetBmpListResponse, error) {
	return app.bmpService.SaveMergeResult(ctx, request)
}

func (app *BmpApp) GetSubheadingsList(ctx *titan.Context) (*bmp_service.GetKeytabsResponse, error) {
	return app.bmpService.GetSubheadingsList(ctx)
}

func (app *BmpApp) AddKeytab(ctx *titan.Context, request bmp_service.AddKeytabRequest) error {
	return app.bmpService.AddKeytab(ctx, request)
}

func (app *BmpApp) QuitMergeMedicationPlan(ctx *titan.Context, request bmp_service.QuitMergeMedicationPlanRequest) error {
	return app.bmpService.QuitMergeMedicationPlan(ctx, request)
}

func (app *BmpApp) UpdateAdditionalData(ctx *titan.Context, request bmp_service.UpdateAdditionalDataRequest) error {
	return app.bmpService.UpdateAdditionalData(ctx, request)
}

func (app *BmpApp) RefillMedicationBmp(ctx *titan.Context, request bmp.RefillMedicationBmpRequest) (*bmp.RefillMedicationBmpResponse, error) {
	bmpMedication, err := app.bmpService.GetMedicationEntry(ctx,
		bmp_service.GetMedicationEntryRequest{
			PatientId: request.PatientId,
			EntryId:   request.EntryId,
		},
	)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var commonMedication medicine_common.MedicineInfo

	response := &bmp.RefillMedicationBmpResponse{
		PatientId: request.PatientId,
	}

	medicineKBV, err := app.medicineKBVApp.FindByID(ctx, medicine_kbv.FindByIdRequest{
		Pzn:       bmpMedication.MedicationInformation.Pzn,
		PatientId: &request.PatientId,
		IkNumber:  request.IkNumber,
	})
	if err != nil {
		return nil, err
	}

	if err := copier.Copy(&commonMedication, medicineKBV); err != nil {
		return nil, err
	}
	if medicineKBV != nil &&
		medicineKBV.ProductInformation != nil &&
		medicineKBV.DrugInformation != nil &&
		medicine_util.CheckIfMedicineNameEditedForAtcN07bc(bmpMedication.MedicationInformation.TradeName,
			medicineKBV.ProductInformation.Name,
			medicineKBV.DrugInformation.ATC,
		) {
		commonMedication.Name = bmpMedication.MedicationInformation.TradeName
	}

	commonMedication.Type = medicine_common.KBV
	commonMedication.Hint = bmpMedication.MedicationInformation.Hint
	commonMedication.AsNeeded = bmpMedication.MedicationInformation.AsNeeded
	commonMedication.AutIdem = bmpMedication.MedicationInformation.AutIdem
	commonMedication.Vaccinate = &bmpMedication.MedicationInformation.Vaccinate
	commonMedication.CurrentFormType = bmpMedication.MedicationInformation.FormType
	commonMedication.FurtherInformation = &bmpMedication.MedicationInformation.FurtherInformation
	var (
		morning   *float64
		evening   *float64
		afternoon *float64
		night     *float64
	)

	freetext := ""
	dj := true

	if bmpMedication.MedicationInformation.IntakeInterval != nil {
		intakeInterval := bmpMedication.MedicationInformation.IntakeInterval
		if intakeInterval.Morning != "" {
			morning = util.NewPointer(cast.ToFloat64(intakeInterval.Morning))
			freetext += intakeInterval.Morning
		}
		if intakeInterval.Afternoon != "" {
			afternoon = util.NewPointer(cast.ToFloat64(intakeInterval.Afternoon))
			freetext = freetext + "-" + intakeInterval.Afternoon
		}
		if intakeInterval.Evening != "" {
			evening = util.NewPointer(cast.ToFloat64(intakeInterval.Evening))
			freetext = freetext + "-" + intakeInterval.Evening
		}
		if intakeInterval.Night != "" {
			night = util.NewPointer(cast.ToFloat64(intakeInterval.Night))
			freetext = freetext + "-" + intakeInterval.Night
		}

		if intakeInterval.Freetext != "" {
			freetext = intakeInterval.Freetext
		}

		if intakeInterval.DJ != nil {
			dj = *intakeInterval.DJ
		}
	}

	commonMedication.IntakeInterval = &medicine_common.IntakeInterval{
		Morning:   morning,
		Evening:   evening,
		Afternoon: afternoon,
		Night:     night,
		Freetext:  freetext,
		DJ:        &dj,
	}
	commonMedication.IsArtificialInsemination = &bmpMedication.MedicationInformation.IsArtificialInsemination
	commonMedication.IsEPrescription = &bmpMedication.MedicationInformation.IsEPrescription

	response.MedicineInfos = []medicine_common.MedicineInfo{commonMedication}
	return response, nil
}

// GetPredefinedDataOfMedication returns list of hints, reason, forms, units
func (app *BmpApp) GetPredefinedDataOfMedication(ctx *titan.Context) (*bmp.GetPredefinedDataOfMedicationResponse, error) {
	rsFormsUnits, err := app.bmpService.GetFormsAndUnitsOfDrug(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	filter := bson.M{
		keytab.Field_Type:   bson.M{"$in": []string{string(bmp_service.K_HINT), string(bmp_service.K_REASON)}},
		keytab.Field_Enable: true,
	}
	rsHintsReasons, err := app.keytabRepo.Find(ctx, filter)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	hints := []bmp_service.BmpMedicationPlanKeytab{}
	reasons := []bmp_service.BmpMedicationPlanKeytab{}
	for _, v := range rsHintsReasons {
		if v.Type == string(bmp_service.K_HINT) {
			hints = append(hints, bmp_service.BmpMedicationPlanKeytab{Code: v.Code, Name: v.Name})
		} else if v.Type == string(bmp_service.K_REASON) {
			reasons = append(reasons, bmp_service.BmpMedicationPlanKeytab{Code: v.Code, Name: v.Name})
		}
	}

	response := &bmp.GetPredefinedDataOfMedicationResponse{
		Forms:   rsFormsUnits.Forms,
		Units:   rsFormsUnits.Units,
		Hints:   hints,
		Reasons: reasons,
	}
	return response, nil
}

func (app *BmpApp) HandleEventResultCompareBmp(ctx *titan.Context, _ bmp.EventResultCompareBmp) error {
	return app.notifier.NotifyCareProviderResultCompareBmp(ctx, &bmp.EventResultCompareBmp{
		UserId: ctx.UserInfo().UserUUID(),
	})
}

func (app *BmpApp) UpdateBmpOutDateViaMMI(ctx *titan.Context, request bmp_service.GetBmpListRequest) error {
	return app.bmpService.UpdateBmpOutDateViaMMI(ctx, request)
}
