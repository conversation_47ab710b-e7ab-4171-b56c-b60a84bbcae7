package medicine

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"net/url"
	"path"
	"sort"
	"strconv"
	"strings"
	"time"

	"emperror.dev/errors"
	api "git.tutum.dev/medi/tutum/ares/app/mvz/api/medicine_kbv"
	settingApi "git.tutum.dev/medi/tutum/ares/app/mvz/api/settings"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	settingsApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/settings"
	"git.tutum.dev/medi/tutum/ares/app/mvz/patient_profile"
	minio_config "git.tutum.dev/medi/tutum/ares/pkg/config/minio"
	redis_config "git.tutum.dev/medi/tutum/ares/pkg/config/redis"
	"git.tutum.dev/medi/tutum/ares/pkg/gob_util"
	hpm_rest_client "git.tutum.dev/medi/tutum/ares/pkg/hpm_rest/client"
	medicine_util "git.tutum.dev/medi/tutum/ares/pkg/medicine"
	"git.tutum.dev/medi/tutum/ares/pkg/minio"
	"git.tutum.dev/medi/tutum/ares/pkg/mmi"
	"git.tutum.dev/medi/tutum/ares/pkg/redis"
	bsnr_service "git.tutum.dev/medi/tutum/ares/service/bsnr"
	"git.tutum.dev/medi/tutum/ares/service/contract/contract"
	"git.tutum.dev/medi/tutum/ares/service/contract/contract/model"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/medicine_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	mmi_service "git.tutum.dev/medi/tutum/ares/service/domains/mmi"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	settings_common "git.tutum.dev/medi/tutum/ares/service/domains/settings/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/timeline_service"
	"git.tutum.dev/medi/tutum/ares/share"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/golang-module/carbon"
	"github.com/jinzhu/copier"
	"github.com/submodule-org/submodule.go/v2"
	"github.com/thoas/go-funk"
	"gitlab.com/silenteer-oss/titan"
	"go.uber.org/zap"
)

const (
	NOP_N1 string = "N1"
	NOP_N2 string = "N2"
	NOP_N3 string = "N3"
	NOP_N4 string = "N4"
)

const (
	SORT_ASC  string = "_ASC"
	SORT_DESC string = "_DESC"
)

const (
	SORT_FIELD_KBV string = "KBV"
)
const RED_HAND_FILTER_TIME = -1 * 6 * 30 * 24 * time.Hour

// MedicineKbvApp medicine struct
type MedicineKbvApp struct {
	mmiClient                mmi.PharmindexProService
	hpmRestService           hpm_rest_client.ServiceRest
	config                   *config.MMIConfig
	minioClient              *minio.Minio
	bucketName               string
	minioConfig              minio_config.MinioClientConfig
	RedisConfig              redis_config.RedisConfig
	settingApp               settingApi.SettingsApp
	redisClient              *redis.Redis
	timelineServiceDiagnosis *timeline_service.TimelineService[patient_encounter.EncounterDiagnoseTimeline]
	patientProfileRepo       *patient_profile.PatientProfileRepository
	contractService          *contract.Service
	profileService           profile.EmployeeProfileService
	mmiService               mmi_service.MMIService
	bsnrService              *bsnr_service.BSNRService
}

var MedicineKbvAppMod = submodule.Make[api.MedicineKbvApp](
	NewMedicineKbvApp,
	mmi.PharmindexProServiceMod,
	config.MvzAppConfigMod,
	minio.MinioMod,
	settingsApp.SettingsAppMod,
	redis.RedisMod,
	timeline_service.TimelineServiceDiagnosisMod,
	contract.ContractServiceMod,
	share.EmployeeProfileServiceMod,
	mmi_service.MMIServiceMod,
	hpm_rest_client.HpmRestServiceMod,
	bsnr_service.BSNRServiceMod,
)

// InitMedicineApp init medicine app
func NewMedicineKbvApp(
	mmiClient mmi.PharmindexProService,
	config *config.MvzAppConfigs,
	minioClient *minio.Minio,
	settingApp settingApi.SettingsApp,
	redisClient *redis.Redis,
	timelineServiceDiagnosis *timeline_service.TimelineService[patient_encounter.EncounterDiagnoseTimeline],
	contractService *contract.Service,
	employeeProfileServiceClient profile.EmployeeProfileService,
	mmiService mmi_service.MMIService,
	hpmRestService hpm_rest_client.ServiceRest,
	bsnrService *bsnr_service.BSNRService,
) api.MedicineKbvApp {
	mmiConfig := config.MMIConfig
	if mmiConfig == nil || mmiConfig.BaseUrl == "" || mmiConfig.Username == "" || mmiConfig.Licensekey == "" {
		panic("MMI config is invalid")
	}

	return &MedicineKbvApp{
		mmiClient:                mmiClient,
		config:                   config.MMIConfig,
		minioClient:              minioClient,
		bucketName:               config.MinioClientConfig.BucketTmp,
		minioConfig:              config.MinioClientConfig,
		RedisConfig:              config.RedisConfig,
		settingApp:               settingApp,
		redisClient:              redisClient,
		timelineServiceDiagnosis: timelineServiceDiagnosis,
		patientProfileRepo:       patient_profile.NewPatientProfileRepository(),
		hpmRestService:           hpmRestService,
		contractService:          contractService,
		profileService:           employeeProfileServiceClient,
		mmiService:               mmiService,
		bsnrService:              bsnrService,
	}
}

var (
	ATC                  = "ATC"
	CATALOGNAME_MOLECULE = "MOLECULEUNIT"
	CATALOGNAME_NORMSIZE = "PACKAGENORMSIZE"
)

func (m *MedicineKbvApp) GetMMIDBStatus(ctx *titan.Context, request api.GetMMIDBStatusRequest) (*api.GetMMIDBStatusResponse, error) {
	result, err := m.mmiClient.GetMetadataContext(ctx, &mmi.GetMetadata{})
	if err != nil {
		return nil, fmt.Errorf("GetMMIDBStatus: %w", err)
	}

	if result == nil {
		return nil, errors.New("GetMMIDBStatus: result is nil")
	}

	if result.STATUS.Code != 0 {
		return nil, errors.New(result.STATUS.Message)
	}

	if result.COUNT == 0 {
		return nil, errors.New("NOT_FOUND")
	}

	haveDate := result.MetadataResponse != nil && result.MetadataResponse.METADATA != nil && len(result.MetadataResponse.METADATA.METADATAENTRY_LIST) == 2
	if !haveDate {
		return nil, fmt.Errorf("GetMMIDBStatus: invalid result")
	}

	isUpdated, err := result.IsMMIUpdated(ctx, util.ConvertMillisecondsToTime(request.Date))
	if err != nil {
		return nil, err
	}

	metaData := result.MetadataResponse.METADATA
	if metaData == nil {
		return nil, nil
	}
	metaEntries := metaData.METADATAENTRY_LIST
	version := metaData.VERSION
	priceDateString := metaEntries[0].VALUE
	drugDateString := metaEntries[1].VALUE
	return &api.GetMMIDBStatusResponse{
		IsUpdated: isUpdated,
		PriceDate: priceDateString,
		DrugDate:  drugDateString,
		Version:   version,
	}, nil
}

func (m *MedicineKbvApp) GetGBADocument(ctx *titan.Context, request api.GetGBADocumentRequest) (*api.GetGBADocumentResponse, error) {
	getDocumentGPAResult, err := m.mmiClient.GetDocumentsContext(ctx, &mmi.GetDocuments{
		Licensekey:       m.config.Licensekey,
		Username:         m.config.Username,
		Pzn:              request.PZN,
		Documenttypecode: string(medicine_common.DocumentTypeCode_BENIFIT_ASSESSMENT),
		// NOTE: hardcode 100 to improve mmi response time
		Maxresult: 100,
	})
	if err != nil {
		return nil, err
	}
	var documents []medicine_common.DOCUMENT
	if err := copier.Copy(&documents, getDocumentGPAResult.DOCUMENT); err != nil {
		return nil, err
	}

	return &api.GetGBADocumentResponse{
		Documents: documents,
	}, nil
}

func (m *MedicineKbvApp) FindByPZN(ctx *titan.Context, request api.FindByPZNRequest) (*medicine_common.Medicine, error) {
	packageRequest := &mmi.GetPackages{
		Licensekey:              m.config.Licensekey,
		Username:                m.config.Username,
		Pzn_orlist:              []string{request.PZN},
		Maxresult:               1,
		IncludeClinicalPackages: false,
	}

	isValidSampleOption, mmiIncludeSample, err := m.IsIncludeSampleDrug(ctx)
	if err != nil {
		return nil, err
	}
	if isValidSampleOption {
		packageRequest.Includesample = mmiIncludeSample
	}

	packagesResult, err := m.mmiClient.GetPackagesContext(ctx, packageRequest)
	if err != nil {
		return nil, err
	}
	if packagesResult.STATUS.Code != 0 {
		return nil, errors.New(packagesResult.STATUS.Message)
	}

	if packagesResult.COUNT == 0 {
		return nil, errors.New("NOT_FOUND")
	}

	var patientAge *int = nil
	isCopaymentExemptionTillDate := false
	if request.PatientId != nil {
		patientProfile, err := m.patientProfileRepo.GetPatientProfileByPatientId(ctx, *request.PatientId)
		if err != nil {
			return nil, err
		}
		patientAge = patientProfile.GetAge(ctx)

		isCopaymentExemptionTillDate, err = m.patientProfileRepo.CheckCopaymentExemptionTillDate(ctx, *patientProfile)
		if err != nil {
			return nil, err
		}
	}

	getThinProductsResult, err := m.mmiClient.GetThinProductsContext(ctx, &mmi.GetThinProducts{
		Licensekey: m.config.Licensekey,
		Username:   m.config.Username,
		Pzn_orlist: []string{packagesResult.PPACKAGE[0].PZN},
		Maxresult:  1,
		Patientage: int32(util.GetPointerValue(patientAge)),
	})
	if err != nil {
		return nil, err
	}
	if getThinProductsResult.STATUS.Code != 0 {
		return nil, errors.New(getThinProductsResult.STATUS.Message)
	}

	moleculeUnitEntries, err := m.getCatalogEntriesByName(ctx, CATALOGNAME_MOLECULE)
	if err != nil {
		return nil, err
	}
	normSizeEntries, err := m.getCatalogEntriesByName(ctx, CATALOGNAME_NORMSIZE)
	if err != nil {
		return nil, err
	}

	pac := packagesResult.PPACKAGE[0]
	prod := getThinProductsResult.Products.Product[0]

	isSV := false
	med := medicine_util.ToMedicineModel(util.GetPointerValue(ctx.UserInfo().UserUUID()),
		*pac,
		prod,
		moleculeUnitEntries,
		normSizeEntries,
		patientAge,
		isCopaymentExemptionTillDate,
		nil,
		util.GetPointerValue(request.IsPrivateSchein),
		isSV,
		false,
		false)

	docs, err := m.mmiClient.GetDocumentsContext(ctx, &mmi.GetDocuments{
		Licensekey:       m.config.Licensekey,
		Username:         m.config.Username,
		Documentid:       pac.PRODUCT.TEXT_BASEINFO_ID,
		Documenttypecode: string(medicine_common.DocumentTypeCode_BI),
		Maxresult:        1,
	})
	if err != nil {
		return nil, err
	}
	if docs.STATUS.Code != 0 {
		return nil, errors.New(docs.STATUS.Message)
	}

	if docs.COUNT > 0 {
		medicine_util.UpdateMedicineModelDetails(&med, docs.DOCUMENT[0])
	}

	return &med, nil
}

func (m *MedicineKbvApp) FindByID(ctx *titan.Context, request api.FindByIdRequest) (*medicine_common.Medicine, error) {
	packagesRequest := &mmi.GetPackages{
		Licensekey:              m.config.Licensekey,
		Username:                m.config.Username,
		Pzn_orlist:              []string{request.Pzn},
		Insuranceiknr:           *request.IkNumber,
		Maxresult:               1,
		IncludeClinicalPackages: false,
	}
	isSV := request.IsSvPatient && request.ContractId != nil
	if isSV {
		var ikNumberInt int64

		if request.IkNumber != nil {
			ikNumberInt64, err := strconv.ParseInt(util.GetPointerValue(request.IkNumber), 10, 64)
			if err != nil {
				return nil, err
			}

			ikNumberInt = ikNumberInt64
		}

		packagesRequest.Haevg = &mmi.HaevgRequestParameter{
			IK:                    int32(ikNumberInt),
			ReferenzDatum:         *request.ReferenceDate,
			VertragsIdentifikator: *request.ContractId,
		}
	}

	isValidSampleOption, mmiIncludeSample, err := m.IsIncludeSampleDrug(ctx)
	if err != nil {
		return nil, err
	}
	if isValidSampleOption {
		packagesRequest.Includesample = mmiIncludeSample
	}

	packagesResult, err := m.mmiClient.GetPackagesContext(ctx, packagesRequest)
	if err != nil {
		return nil, err
	}
	if packagesResult.STATUS.Code != 0 {
		return nil, errors.New(packagesResult.STATUS.Message)
	}

	if packagesResult.COUNT == 0 {
		return nil, errors.New("NOT_FOUND")
	}

	var patientAge *int = nil
	isCopaymentExemptionTillDate := false
	if request.PatientId != nil {
		patientProfile, err := m.patientProfileRepo.GetPatientProfileByPatientId(ctx, *request.PatientId)
		if err != nil {
			return nil, err
		}
		patientAge = patientProfile.GetAge(ctx)

		isCopaymentExemptionTillDate, err = m.patientProfileRepo.CheckCopaymentExemptionTillDate(ctx, *patientProfile)
		if err != nil {
			return nil, err
		}
	}

	getThinProductsResult, err := m.mmiClient.GetThinProductsContext(ctx, &mmi.GetThinProducts{
		Licensekey: m.config.Licensekey,
		Username:   m.config.Username,
		Pzn_orlist: []string{request.Pzn},
		Maxresult:  100,
		Bsnr:       util.GetPointerValue(request.Bsnr),
		Lanr:       util.GetPointerValue(request.Lanr),
		Patientage: int32(util.GetPointerValue(patientAge)),
	})
	if err != nil {
		return nil, err
	}
	if getThinProductsResult.STATUS.Code != 0 {
		return nil, errors.New(getThinProductsResult.STATUS.Message)
	}

	moleculeUnitEntries, err := m.getCatalogEntriesByName(ctx, CATALOGNAME_MOLECULE)
	if err != nil {
		return nil, err
	}
	normSizeEntries, err := m.getCatalogEntriesByName(ctx, CATALOGNAME_NORMSIZE)
	if err != nil {
		return nil, err
	}

	amr, err := m.GetAMR(ctx, api.GetAMRRequest{
		Pzn:       request.Pzn,
		PatientId: request.PatientId,
	})
	if err != nil {
		return nil, err
	}

	pac := packagesResult.PPACKAGE[0]
	prod := getThinProductsResult.Products.Product[0]

	productId := pac.PRODUCTID
	blueHandLettersMap, err := m.mmiClient.GetBlueHandLetters(ctx, m.config.Licensekey, m.config.Username, []int32{productId}, nil)
	if err != nil {
		return nil, err
	}

	filterTime := util.Now(ctx).Add(RED_HAND_FILTER_TIME).UnixMilli()
	redHandLettersMap, err := m.mmiClient.GetRedHandLetters(ctx, m.config.Licensekey, m.config.Username, []int32{productId}, &filterTime)
	if err != nil {
		return nil, err
	}

	isSupportVSST855 := false
	isSupportForm1042 := false

	if request.ContractId != nil {
		contract := m.contractService.GetContractDetailById(*request.ContractId)
		now := util.NowUnixMillis(ctx)
		isSupportVSST855 = contract.CheckExistAnforderung(model.ICheckExistAnforderung{
			ComplianceIds: []string{"VSST855"},
			CheckTime:     now,
		})
		isSupportForm1042 = contract.CheckExistAnforderung(model.ICheckExistAnforderung{
			ComplianceIds: []string{"FORM1042"},
			CheckTime:     now,
		})
	}

	med := medicine_util.ToMedicineModel(util.GetPointerValue(ctx.UserInfo().UserUUID()),
		*pac,
		prod,
		moleculeUnitEntries,
		normSizeEntries,
		patientAge,
		isCopaymentExemptionTillDate,
		function.Do(func() []medicine_common.AMR {
			if amr == nil {
				return nil
			}
			return amr.AMRs
		}),
		util.GetPointerValue(request.IsPrivateSchein),
		isSV, isSupportVSST855, isSupportForm1042)

	med.BlueHandLetters = blueHandLettersMap[productId]
	med.RedHandLetters = redHandLettersMap[productId]

	var ikNumber int32

	if request.IkNumber != nil {
		ikNumberParseValue, err := strconv.ParseInt(util.GetPointerValue(request.IkNumber), 10, 32)
		if err != nil {
			return nil, err
		}
		ikNumber = int32(ikNumberParseValue)
	}

	medicines := []medicine_common.Medicine{med}
	medicines, _, err = m.mapHpmMedicines(ctx, api.SearchHpmMedicineRequest{
		IkNumber:      ikNumber,
		ReferenceDate: request.ReferenceDate,
		ContractId:    request.ContractId,
		Medicines:     medicines,
	})
	if err != nil {
		return nil, err
	}
	med = medicines[0]

	if pac.PRODUCT.TEXT_BASEINFO_ID != 0 {
		docs, err := m.mmiClient.GetDocumentsContext(ctx, &mmi.GetDocuments{
			Licensekey:       m.config.Licensekey,
			Username:         m.config.Username,
			Documentid:       pac.PRODUCT.TEXT_BASEINFO_ID,
			Documenttypecode: string(medicine_common.DocumentTypeCode_BI),
			Maxresult:        1,
		})
		if err != nil {
			return nil, err
		}
		if docs.STATUS.Code != 0 {
			return nil, errors.New(docs.STATUS.Message)
		}

		if docs.COUNT > 0 {
			medicine_util.UpdateMedicineModelDetails(&med, docs.DOCUMENT[0])
		}
	}

	quarterAndYears := util.GetCurrentQuarters(util.Now(ctx), 4)
	quarter := slice.Map(quarterAndYears, func(i []int) int {
		return i[0]
	})
	years := slice.Map(quarterAndYears, func(i []int) int {
		return i[1]
	})
	if request.PatientId == nil {
		return &med, nil
	}
	patientIcdCodes, err := m.timelineServiceDiagnosis.GetActiveDiagnosesCodesUniqueByQuarter(ctx, *request.PatientId, quarter, years)
	if err != nil {
		errMsg := fmt.Sprintf("Error while getting patient diagnosis codes: %s", err.Error())
		ctx.Logger().Warn(errMsg)
	} else {
		med.PatientICDCodes = patientIcdCodes
	}
	return &med, nil
}

func (m *MedicineKbvApp) GetAMR(ctx *titan.Context, request api.GetAMRRequest) (*api.GetAMRResponse, error) {
	getAMRResult, err := m.mmiClient.GetAMRContext(ctx, &mmi.GetAMR{
		Licensekey: m.config.Licensekey,
		Username:   m.config.Username,
		Pzn:        request.Pzn,
	})
	if err != nil {
		return nil, err
	}
	if getAMRResult.COUNT == 0 {
		return nil, nil
	}
	var amrUnder12 []medicine_common.AMR
	var amrOlder12 []medicine_common.AMR

	for _, v := range getAMRResult.AMR {
		var currentAMR medicine_common.AMR
		if err := copier.Copy(&currentAMR, v); err != nil {
			return nil, err
		}

		if v.AGETO != 0 && v.AGETO <= 12 {
			amrUnder12 = append(amrUnder12, currentAMR)
		} else {
			amrOlder12 = append(amrOlder12, currentAMR)
		}
	}

	var patientAge *int = nil
	if request.PatientId != nil {
		patientProfile, err := m.patientProfileRepo.GetPatientProfileByPatientId(ctx, *request.PatientId)
		if err != nil {
			return nil, err
		}
		patientAge = patientProfile.GetAge(ctx)
	}

	if patientAge == nil || *patientAge > 12 {
		return &api.GetAMRResponse{
			AMRs: amrOlder12,
		}, nil
	}
	return &api.GetAMRResponse{
		AMRs: amrUnder12,
	}, nil
}

type amrResult struct {
	Pzn string
	Res *mmi.GetAMRResponse
	Err error
}

// NOTE: This func would be removed after MMI has implemented the AMRs endpoint
func (m *MedicineKbvApp) getAMRsFromMMI(ctx *titan.Context, pzns []string) (map[string]*mmi.GetAMRResponse, error) {
	result := make(map[string]*mmi.GetAMRResponse)
	err := function.NewPool[string, *amrResult](
		function.EarlyReturn(),
		function.WithContext(ctx),
		function.WithSize(5),
	).WithFunc(func(pzn string) (*amrResult, error) {
		res, err := m.mmiClient.GetAMRContext(ctx, &mmi.GetAMR{
			Licensekey: m.config.Licensekey,
			Username:   m.config.Username,
			Pzn:        pzn,
		})
		if res.COUNT == 0 {
			return nil, nil
		}
		if err != nil {
			return nil, errors.WithMessagef(err, "could not fetch AMRs with pzn %s", pzn)
		}
		return &amrResult{
			Pzn: pzn,
			Res: res,
		}, nil
	}).Process(pzns, func(amrRes *amrResult, err error) {
		if err != nil {
			ctx.Logger().Error("could not fetch AMRs", zap.Error(err))
			return
		}
		if amrRes == nil {
			return
		}
		result[amrRes.Pzn] = amrRes.Res
	})
	if err != nil {
		return nil, errors.WithMessage(err, "could not create pool when fetching AMRs")
	}
	return result, nil
}

func (m *MedicineKbvApp) getAMRs(ctx *titan.Context, pzns []string, patientAge *int) (map[string][]medicine_common.AMR, error) {
	getAMRsResult, err := m.getAMRsFromMMI(ctx, pzns)
	if err != nil {
		return nil, err
	}
	result := make(map[string][]medicine_common.AMR)
	for pzn, val := range getAMRsResult {
		var amrUnder12 []medicine_common.AMR
		var amrOlder12 []medicine_common.AMR
		for _, v := range val.AMR {
			currentAMR := medicine_common.AMR{}
			if err := copier.Copy(&currentAMR, v); err != nil {
				return nil, err
			}
			if currentAMR.AGETO <= 12 {
				amrUnder12 = append(amrUnder12, currentAMR)
			} else {
				amrOlder12 = append(amrOlder12, currentAMR)
			}
		}
		if patientAge != nil && *patientAge <= 12 {
			result[pzn] = amrUnder12
		} else {
			result[pzn] = amrOlder12
		}
	}
	return result, nil
}

func (m *MedicineKbvApp) GetANL(ctx *titan.Context, request api.GetANLRequest) (*api.GetANLResponse, error) {
	getAMRResult, err := m.mmiClient.GetAMRContext(ctx, &mmi.GetAMR{
		Licensekey: m.config.Licensekey,
		Username:   m.config.Username,
		Productid:  request.ProductId,
		Maxresult:  1,
	})
	if err != nil {
		return nil, err
	}
	if len(getAMRResult.AMR) == 0 {
		return &api.GetANLResponse{
			Name: "",
		}, nil
	}
	typeCode := "AMR" + getAMRResult.AmrResponse.AMR[0].REGULATIONTYPECODE
	getCatalogEntriesResponse, err := m.mmiClient.GetCatalogEntriesContext(ctx, &mmi.GetCatalogEntries{
		Licensekey:       m.config.Licensekey,
		Username:         m.config.Username,
		Code:             typeCode,
		Catalogshortname: "REGULATIONTYPE",
		Maxresult:        1,
	})
	if err != nil {
		return nil, err
	}
	if len(getCatalogEntriesResponse.CATALOGENTRY) == 0 {
		return &api.GetANLResponse{
			Name: "",
		}, nil
	}
	return &api.GetANLResponse{
		Name: getCatalogEntriesResponse.CATALOGENTRY[0].NAME,
	}, nil
}

func buildATCwithChildList(entry *mmi.CATALOGENTRY) []api.ATCItem {
	childList := []api.ATCItem{{
		Code: entry.CODE,
		Name: entry.NAME,
	}}

	if entry.CHILD_LIST == nil {
		return childList
	}
	for _, child := range entry.CHILD_LIST {
		subChildList := buildATCwithChildList(child)
		childList = append(childList, subChildList...)

		//	Optimized for perfomance, avoid too deep Recursion
		if len(childList) > 50 {
			return childList
		}
	}
	return childList
}

func buildATCResponse(entries *mmi.GetCatalogEntriesResponse) []api.ATCItem {
	if entries.COUNT == 0 {
		return []api.ATCItem{}
	}
	res := []api.ATCItem{}

	// Search by Code
	if entries.COUNT == 1 {
		res = buildATCwithChildList(entries.CATALOGENTRY[0])
	} else {
		// Search by Name
		for _, entry := range entries.CATALOGENTRY {
			res = append(res, api.ATCItem{
				Code: entry.CODE,
				Name: entry.NAME,
			})
		}
	}

	return res
}

func (m *MedicineKbvApp) SearchATC(ctx *titan.Context, request api.SearchATCRequest) (*api.SearchATCResponse, error) {
	payload := &mmi.GetCatalogEntries{
		Licensekey:       m.config.Licensekey,
		Username:         m.config.Username,
		Catalogshortname: ATC,
		Maxresult:        30,
	}

	if len(util.GetStringValue(request.Name)) >= 3 {
		payload.Name = util.GetPointerValue(request.Name)
		payload.Disabledobjects = []string{"PARENT", "CHILD_LIST"}
	} else if len(util.GetStringValue(request.Code)) >= 3 {
		payload.Code = util.GetPointerValue(request.Code)
		payload.Disabledobjects = []string{"PARENT"}
	} else {
		return nil, nil
	}
	ATCs, err := m.mmiClient.GetCatalogEntriesContext(ctx, payload)
	if err != nil {
		return nil, err
	}
	if ATCs == nil {
		return nil, nil
	}

	res := buildATCResponse(ATCs)

	return &api.SearchATCResponse{
		ATCItems: res,
	}, nil
}

func (m *MedicineKbvApp) GetMolecules(ctx *titan.Context, request api.GetMoleculesRequest) (*api.GetMoleculesResponse, error) {
	res, err := m.mmiClient.GetMoleculesContext(ctx, &mmi.GetMolecules{
		Licensekey: m.config.Licensekey,
		Username:   m.config.Username,
		Name:       request.Value,
	})
	if err != nil {
		return nil, err
	}
	if res == nil || len(res.MOLECULE) == 0 {
		return nil, nil
	}

	molecules := slice.Map(res.MOLECULE, func(item *mmi.MOLECULE) api.MoleculeItem {
		return api.MoleculeItem{
			Id:   util.NewPointer(int64(item.ID)),
			Name: &item.NAME,
		}
	})

	return &api.GetMoleculesResponse{
		Molecules: molecules,
	}, nil
}

func (*MedicineKbvApp) sortMedicinesByReqOrder(medicines []medicine_common.Medicine, hpmPznsOrder []string) []medicine_common.Medicine {
	medicinesMap := map[string]medicine_common.Medicine{}
	for _, medicine := range medicines {
		medicinesMap[medicine.Pzn] = medicine
	}
	sortedMedicines := []medicine_common.Medicine{}
	for _, pzn := range hpmPznsOrder {
		if medicine, ok := medicinesMap[pzn]; ok {
			sortedMedicines = append(sortedMedicines, medicine)
		}
	}
	return sortedMedicines
}

func (m *MedicineKbvApp) mapHpmMedicines(ctx *titan.Context, request api.SearchHpmMedicineRequest) ([]medicine_common.Medicine, []string, error) {
	if request.ContractId == nil {
		return request.Medicines, nil, nil
	}
	contract := m.contractService.GetContractDetailById(*request.ContractId)
	now := util.NowUnixMillis(ctx)
	vsst870 := contract.CheckExistAnforderung(model.ICheckExistAnforderung{
		ComplianceIds: []string{"VSST870"},
		CheckTime:     now,
		// TODO: IKGroup
		// IkGroup: ,
	})

	// check contract
	vsst539 := contract.CheckExistAnforderung(model.ICheckExistAnforderung{
		ComplianceIds: []string{"VSST539"},
		CheckTime:     now,
	})
	vsst540 := contract.CheckExistAnforderung(model.ICheckExistAnforderung{
		ComplianceIds: []string{"VSST540"},
		CheckTime:     now,
	})

	if vsst870 && request.PznsOrder != nil {
		request.Medicines = m.sortMedicinesByReqOrder(request.Medicines, request.PznsOrder)
	}

	vsst537 := contract.CheckExistAnforderung(model.ICheckExistAnforderung{
		ComplianceIds: []string{"VSST537"},
		CheckTime:     now,
	})
	vsst551 := contract.CheckExistAnforderung(model.ICheckExistAnforderung{
		ComplianceIds: []string{"VSST551"},
		CheckTime:     now,
	})
	supportGetDrugInformationFromHpm := contract.CheckAvailableHPMFunction(model.HpmFunktionSimpleTyp_LIEFERE_ARZNEIMITTEL_INFORMATIONEN, util.NowUnixMillis(ctx))
	if (!vsst537 && !vsst551) || !supportGetDrugInformationFromHpm {
		return request.Medicines, nil, nil
	}

	pzns := slice.Map(request.Medicines, func(medicine medicine_common.Medicine) string {
		return medicine.Pzn
	})

	hpmConfig, err := m.bsnrService.GetHpmConfig(ctx, ctx.UserInfo().BsnrId)
	if err != nil {
		return nil, nil, err
	}
	hpmRestService := hpm_rest_client.NewServiceRestWithEndpoint(hpmConfig.Endpoint)
	res, err := hpmRestService.GetMedicines(ctx, &hpm_rest_client.GetMedicinesRequest{
		IkNumber:      request.IkNumber,
		ReferenceDate: util.GetPointerValue(request.ReferenceDate),
		ContractId:    util.GetPointerValue(request.ContractId),
		PzNs:          pzns,
	})
	if err != nil {
		return nil, nil, err
	}
	medicines := slice.Map(request.Medicines, func(medicine medicine_common.Medicine) medicine_common.Medicine {
		medicine.HintsAndWarnings = slice.Map(res.Medicines[medicine.Pzn].Hints, func(hint string) medicine_common.HintsAndWarning {
			return medicine_common.HintsAndWarning{
				Text: hint,
			}
		})

		hpmMedicine, ok := res.Medicines[medicine.Pzn]

		if ok {
			medicine.PriceInformation.PriceFixedStr = hpmMedicine.FixedPrice

			if vsst537 {
				medicine.ColorCategory.DrugCategory = hpmMedicine.Color
			}
		}

		isDiscountVsst539 := vsst539 && (medicine.ColorCategory.DrugCategory == "GRUEN" || medicine.ColorCategory.DrugCategory == "BLAU")
		isDiscountVsst540 := vsst540 && medicine.ColorCategory.DrugCategory == "GRUEN"
		if isDiscountVsst539 || isDiscountVsst540 {
			if medicine.PriceInformation == nil {
				medicine.PriceInformation = &medicine_common.PriceInformation{}
			}
			if medicine.PriceInformation.SvPrices == nil {
				medicine.PriceInformation.SvPrices = &medicine_common.SvPriceInformation{}
			}
			medicine.PriceInformation.SvPrices.PharmacySalePrice = "rabattiert"
		}
		return medicine
	})
	return medicines, res.HpmErrorCodeAsWarningCode, nil
}

func (m *MedicineKbvApp) mapHpmSubstitution(ctx *titan.Context, request api.SearchHpmSubstitutionRequest) ([]medicine_common.Medicine, error) {
	if request.ContractId == nil {
		return request.Medicines, nil
	}
	contract := m.contractService.GetContractDetailById(*request.ContractId)

	canGetSubstitution := contract.CheckAvailableHPMFunction(model.HpmFunktionSimpleTyp_LIEFERE_SUBSTITUTIONEN, util.NowUnixMillis(ctx))
	if !canGetSubstitution {
		return request.Medicines, nil
	}
	hpmConfig, err := m.bsnrService.GetHpmConfig(ctx, ctx.UserInfo().BsnrId)
	if err != nil {
		return nil, err
	}
	hpmRestService := hpm_rest_client.NewServiceRestWithEndpoint(hpmConfig.Endpoint)
	res, err := hpmRestService.GetSubstitution(ctx, &hpm_rest_client.GetSubstitutionRequest{
		IkNumber:      request.IkNumber,
		ReferenceDate: util.GetPointerValue(request.ReferenceDate),
		ContractId:    util.GetPointerValue(request.ContractId),
		Pzn:           request.Pzn,
	})
	if err != nil {
		return nil, err
	}
	medicines := slice.Map(request.Medicines, func(medicine medicine_common.Medicine) medicine_common.Medicine {
		if hpmInfo, ok := res.Medicines[medicine.Pzn]; ok {
			medicine.ColorCategory.DrugCategory = hpmInfo.Color
		}
		return medicine
	})
	return medicines, nil
}

// Search search medicines
func (m *MedicineKbvApp) Search(ctx *titan.Context, request api.SearchMedicineRequest) (*api.SearchMedicineResponse, error) {
	if request.Page == nil || request.PageSize == nil {
		request.Page = util.NewNumberPointer[int32, int32](1)
		request.PageSize = util.NewNumberPointer[int32, int32](10)
	}

	// TODO: for issue sorting color so we get first 60 items
	maxResult := util.NewNumberPointer[int32, int32](60)
	duration := *request.Page % (*maxResult / *request.PageSize)
	isIncludeData := duration == 0
	startindex := (*request.Page - 1) * *request.PageSize

	if isIncludeData {
		startindex = (*request.Page - 2) * *maxResult
	}

	ikNumberStr := fmt.Sprintf("%v", request.IkNumber)
	packageSearch := &mmi.GetPackages{
		Licensekey:              m.config.Licensekey,
		Username:                m.config.Username,
		Startindex:              startindex,
		Maxresult:               *maxResult,
		Fulltextsearch:          1,
		Insuranceiknr:           ikNumberStr,
		IncludeClinicalPackages: false,
		Normsizecode_orlist:     request.PackageSizes,
	}
	isSV := request.IsSV && request.ContractId != nil
	if isSV {
		packageSearch.Haevg = &mmi.HaevgRequestParameter{
			IK:                    request.IkNumber,
			ReferenzDatum:         *request.ReferenceDate,
			VertragsIdentifikator: *request.ContractId,
		}
	}

	isValidSampleOption, mmiIncludeSample, err := m.IsIncludeSampleDrug(ctx)
	if err != nil {
		return nil, err
	}
	if isValidSampleOption {
		packageSearch.Includesample = mmiIncludeSample
	}

	if request.Sort != nil {
		packageSearch.Sortorder = ToSortOrder(*request.Sort)
	}

	if request.IsDiscount {
		packageSearch.Insurancediscountfilter = 1
	}

	if request.IsShowRegistered {
		packageSearch.Has_registrationnumber = true
	}

	switch request.Type {
	case api.ATCCode:
		packageSearch.Atccode_andlist = []string{
			request.Value,
		}
	case api.ICDCode:
		packageSearch.Icd_orlist = []string{
			request.Value,
		}
	case api.PZN:
		packageSearch.Pzn_orlist = []string{
			request.Value,
		}
	case api.Substances:
		if len(request.MoleculeIds) == 0 {
			return &api.SearchMedicineResponse{
				Medicines:             []medicine_common.Medicine{},
				IsHaveDiscountProduct: true,
			}, nil
		}
		packageSearch.Moleculeid_andlist = slice.Map(request.MoleculeIds, func(molecule int64) int32 {
			return int32(molecule)
		})
		if request.IsMonoSearch != nil && *request.IsMonoSearch {
			packageSearch.Exactactivemoleculematch = util.GetPointerValue(request.IsMonoSearch)
		}

	default:
		searchModes := []string{}
		if request.SearchModes.TradeName {
			searchModes = append(searchModes, "PRODUCT")
		}
		if request.SearchModes.Manufataturer {
			searchModes = append(searchModes, "COMPANY")
		}
		if request.SearchModes.ActiveSubstanceName {
			searchModes = append(searchModes, "ACTIVE_SUBSTANCES")
		}

		if len(searchModes) > 0 {
			searchProductRequest := mmi.ProductSearch{
				Licensekey:  m.config.Licensekey,
				Username:    m.config.Username,
				SearchText:  request.Value,
				SearchModes: &mmi.ProductSearchSearchModes{SearchMode: searchModes},
			}
			respSearchProduct, err := m.mmiClient.ProductSearchContext(ctx, &searchProductRequest)
			if err != nil {
				return nil, err
			}

			searchProductIds := slice.Map(respSearchProduct.Products.Product, func(p *mmi.ProductSearchProduct) int32 {
				return p.Id
			})

			if len(searchProductIds) > 0 {
				packageSearch.Productid_orlist = searchProductIds
			} else {
				return &api.SearchMedicineResponse{
					Medicines:             []medicine_common.Medicine{},
					IsHaveDiscountProduct: true,
				}, nil
			}
		} else {
			packageSearch.Name = request.Value
		}
	}

	if request.IsFavourite {
		packageSearch.Houselistname = string(ctx.UserInfo().UserId)
		packageSearch.Houselistfilter = true
		packageSearch.Fulltextsearch = 0
		if request.Value == "" {
			resp, err := m.handleGetAllFavourite(ctx, request)
			if err != nil {
				return nil, err
			}
			if len(resp) > 0 {
				return &api.SearchMedicineResponse{
					Medicines:             resp,
					IsHaveDiscountProduct: true,
					IsHaveFavourite:       true,
				}, nil
			}
			return &api.SearchMedicineResponse{
				Medicines:             []medicine_common.Medicine{},
				IsHaveDiscountProduct: true,
				IsHaveFavourite:       false,
			}, nil
		}
	}
	if request.Value == "" {
		return &api.SearchMedicineResponse{
			Medicines:             []medicine_common.Medicine{},
			IsHaveDiscountProduct: true,
			IsHaveFavourite:       request.IsFavourite,
		}, nil
	}
	packagesResult, err := m.mmiClient.GetPackagesContext(ctx, packageSearch)
	if err != nil {
		return nil, err
	}
	if packagesResult == nil || packagesResult.PackageResponse == nil {
		return &api.SearchMedicineResponse{
			Medicines: []medicine_common.Medicine{},
		}, nil
	}
	if packagesResult.STATUS != nil && packagesResult.STATUS.Code != 0 {
		return nil, errors.New(packagesResult.STATUS.Message)
	}

	isHaveDiscountProduct := true
	if request.IsDiscount && packagesResult.COUNT == 0 {
		packageSearch.Insurancediscountfilter = 0
		isHaveDiscountProduct = false
		packagesResult, err = m.mmiClient.GetPackagesContext(ctx, packageSearch)
		if err != nil {
			return nil, err
		}
		if packagesResult.STATUS.Code != 0 {
			return nil, errors.New(packagesResult.STATUS.Message)
		}
	}
	isHaveFavourite := false
	if request.IsFavourite {
		if packagesResult.COUNT == 0 {
			packageSearch.Houselistname = ""
			packageSearch.Houselistfilter = false
			packageSearch.Fulltextsearch = 1
			packagesResult, err = m.mmiClient.GetPackagesContext(ctx, packageSearch)
			if err != nil {
				return nil, err
			}
			if packagesResult.STATUS.Code != 0 {
				return nil, errors.New(packagesResult.STATUS.Message)
			}
		} else {
			isHaveFavourite = true
		}
	}

	pzns := slice.Map(packagesResult.PPACKAGE, func(p *mmi.PACKAGE) string {
		return p.PZN
	})
	if len(pzns) == 0 {
		return &api.SearchMedicineResponse{
			Medicines:             []medicine_common.Medicine{},
			IsHaveDiscountProduct: isHaveDiscountProduct,
		}, nil
	}

	var patientAge *int = nil
	isCopaymentExemptionTillDate := false
	if request.PatientId != nil {
		patientProfile, err := m.patientProfileRepo.GetPatientProfileByPatientId(ctx, *request.PatientId)
		if err != nil {
			return nil, err
		}
		patientAge = patientProfile.GetAge(ctx)
		isCopaymentExemptionTillDate, err = m.patientProfileRepo.CheckCopaymentExemptionTillDate(ctx, *patientProfile)
		if err != nil {
			return nil, err
		}
	}

	getThinProductsResult, err := m.mmiClient.GetThinProductsContext(ctx, &mmi.GetThinProducts{
		Licensekey: m.config.Licensekey,
		Username:   m.config.Username,
		Pzn_orlist: pzns,
		Bsnr:       request.Bsnr,
		Lanr:       request.Lanr,
		Patientage: int32(util.GetPointerValue(patientAge)),
	})
	if err != nil {
		return nil, err
	}
	if getThinProductsResult.STATUS.Code != 0 {
		return nil, errors.New(getThinProductsResult.STATUS.Message)
	}
	thinProductMap := make(map[int32]mmi.THINPRODUCT)
	for _, thinProduct := range getThinProductsResult.Products.Product {
		if thinProduct != nil {
			thinProductMap[thinProduct.Id] = *thinProduct
		}
	}
	moleculeUnitEntries, err := m.getCatalogEntriesByName(ctx, CATALOGNAME_MOLECULE)
	if err != nil {
		return nil, err
	}
	normSizeEntries, err := m.getCatalogEntriesByName(ctx, CATALOGNAME_NORMSIZE)
	if err != nil {
		return nil, err
	}
	amrsByPzn, err := m.getAMRs(ctx, pzns, patientAge)
	if err != nil {
		return nil, err
	}

	productIds := slice.Map(packagesResult.PPACKAGE, func(p *mmi.PACKAGE) int32 {
		return p.PRODUCTID
	})
	blueHandLettersMap, err := m.mmiClient.GetBlueHandLetters(ctx, m.config.Licensekey, m.config.Username, productIds, nil)
	if err != nil {
		return nil, err
	}

	filterTime := util.Now(ctx).Add(RED_HAND_FILTER_TIME).UnixMilli()
	redHandLettersMap, err := m.mmiClient.GetRedHandLetters(ctx, m.config.Licensekey, m.config.Username, productIds, &filterTime)
	if err != nil {
		return nil, err
	}

	medicines := []medicine_common.Medicine{}
	for _, productPackage := range packagesResult.PPACKAGE {
		if productPackage == nil || productPackage.PRODUCT == nil || productPackage.PRODUCT.ID == 0 {
			continue
		}
		if thinProduct, ok := thinProductMap[productPackage.PRODUCT.ID]; ok {
			var AMRs []medicine_common.AMR
			if int64(thinProduct.HasAmr3ExclusionFlag) == 0 {
				AMRs = amrsByPzn[productPackage.PZN]
			}
			medicine := medicine_util.ToMedicineModel(
				util.GetPointerValue(ctx.UserInfo().UserUUID()),
				*productPackage,
				&thinProduct,
				moleculeUnitEntries,
				normSizeEntries,
				patientAge,
				isCopaymentExemptionTillDate,
				AMRs,
				util.GetPointerValue(request.IsPrivateSchein),
				isSV,
				false,
				false,
			)
			medicine.BlueHandLetters = blueHandLettersMap[productPackage.PRODUCT.ID]
			medicine.RedHandLetters = redHandLettersMap[productPackage.PRODUCT.ID]

			medicines = append(medicines, medicine)
		}
	}

	sortingAsc, sortingField := request.IsSortAsc()

	if request.ContractId != nil {
		var hpmErrorCodeAsWarnings []string
		medicines, hpmErrorCodeAsWarnings, err = m.mapHpmMedicines(ctx, api.SearchHpmMedicineRequest{
			IkNumber:      request.IkNumber,
			ReferenceDate: request.ReferenceDate,
			ContractId:    request.ContractId,
			Medicines:     medicines,
		})
		if err != nil {
			return nil, err
		}
		if len(hpmErrorCodeAsWarnings) != 0 {
			isHaveDiscountProduct = false
		}

		contract := m.contractService.GetContractDetailById(*request.ContractId)
		now := util.NowUnixMillis(ctx)
		isSupportVSST548 := contract.CheckExistAnforderung(model.ICheckExistAnforderung{
			ComplianceIds: []string{"VSST548"},
			CheckTime:     now,
		})

		if isSupportVSST548 {
			sort.Slice(medicines, func(i, _ int) bool {
				colorCategoryM1 := medicines[i].ColorCategory

				if colorCategoryM1 != nil && sortingAsc != nil {
					hasGreenOrBlueColor := slice.Contains([]string{"GRUEN", "BLAU"}, strings.ToUpper(colorCategoryM1.DrugCategory))

					if *sortingAsc {
						return hasGreenOrBlueColor
					}

					return !hasGreenOrBlueColor
				}

				return false
			})
		}
	}

	notSupportSortingByMmi := []api.SortField{api.Size, api.Copayment}
	if sortingAsc != nil && slice.Contains(notSupportSortingByMmi, sortingField) {
		if sortingField == api.Size {
			sort.Slice(medicines, func(i, j int) bool {
				return SortByNormAndQuantity(medicines[i], medicines[j], *sortingAsc)
			})
		}
	}

	startIndex := int32(0)

	if isIncludeData {
		startIndex = *request.PageSize
	}

	endIndex := startIndex + *request.PageSize
	if endIndex > int32(len(medicines)) {
		endIndex = int32(len(medicines))
	}

	totalPage := math.Ceil(float64(packagesResult.COUNT) / float64(*request.PageSize))
	return &api.SearchMedicineResponse{
		Medicines:             medicines[startIndex:endIndex],
		Page:                  *request.Page,
		Total:                 int32(totalPage),
		TotalRecords:          packagesResult.COUNT,
		IsHaveDiscountProduct: isHaveDiscountProduct,
		IsHaveFavourite:       isHaveFavourite,
	}, nil
}

func (m *MedicineKbvApp) handleGetAllFavourite(ctx *titan.Context, request api.SearchMedicineRequest) ([]medicine_common.Medicine, error) {
	result := []medicine_common.Medicine{}
	pzns, err := m.mmiService.GetFavouritesPzns(ctx, api.GetFavouritesRequest{})
	if err != nil {
		return nil, err
	}

	if len(pzns) == 0 {
		return result, nil
	}
	result, err = m.GetByPzns(ctx, GetByPznsRequest{
		Pzns:                   pzns,
		PatientId:              request.PatientId,
		Bsnr:                   request.Bsnr,
		Lanr:                   request.Lanr,
		IkNumber:               fmt.Sprintf("%d", request.IkNumber),
		Has_registrationnumber: request.IsShowRegistered,
		Insurancediscountfilter: function.Do(func() int32 {
			if request.IsDiscount {
				return 1
			}
			return 0
		}),
		Sort: request.Sort,
	})
	if err != nil {
		return nil, err
	}
	return result, nil
}

func SortByNormAndQuantity(a, b medicine_common.Medicine, isAsc bool) bool {
	aNop := a.PackagingInformation.PackageSize.Nop
	bNop := b.PackagingInformation.PackageSize.Nop
	aQuantity := *a.PackagingInformation.QuantityNumber
	bQuantity := *b.PackagingInformation.QuantityNumber

	switch {
	case (aNop == NOP_N1 || aNop == NOP_N2 || aNop == NOP_N3) && (bNop == NOP_N1 || bNop == NOP_N2 || bNop == NOP_N3):
		if aNop == bNop {
			if isAsc {
				return aQuantity < bQuantity
			}
			return aQuantity > bQuantity
		}
		if isAsc {
			return aNop < bNop
		}
		return aNop > bNop
	case aNop == NOP_N1 || aNop == NOP_N2 || aNop == NOP_N3:
		return true
	case bNop == NOP_N1 || bNop == NOP_N2 || bNop == NOP_N3:
		return false
	default:
		if aNop == "" && bNop == "" {
			if isAsc {
				return aQuantity < bQuantity
			}
			return aQuantity > bQuantity
		} else if aNop == "" {
			return true
		} else if bNop == "" {
			return false
		}

		if aNop == bNop && aQuantity != bQuantity {
			if isAsc {
				return aQuantity < bQuantity
			}
			return aQuantity > bQuantity
		}
		if aQuantity != bQuantity {
			if isAsc {
				return aQuantity < bQuantity
			}
			return aQuantity > bQuantity
		}
		if isAsc {
			return aNop < bNop
		}
		return aNop > bNop
	}
}

func SortByDoctorName(a, b profile.EmployeeProfileResponse, isAsc bool) bool {
	doctoNameA := strings.ToLower(util.GetDoctorName(a))
	doctorNameB := strings.ToLower(util.GetDoctorName(b))

	if isAsc {
		return doctorNameB > doctoNameA
	}

	return doctoNameA > doctorNameB
}

// hzv -> getPakages (haevg) -> PACKAGES + haevgPackage -mapping + haevgPackage> results
// kbv -> getPakages -> PACKAGES -mapping-> results

// hzv GetHaevgSubstitutionPackages + pzn -> haevgPackage -> pzns'
// getPackages + pzns' + haevg ->
// PACKAGES (haevgPackage)

// kbv getPackages + pzn ->
// PACKAGES
// -> pzns -> thinProducts
// results = Packages + thinProducts

func (m *MedicineKbvApp) GetPriceComparison(ctx *titan.Context, request api.GetPriceComparisonRequest) (*api.GetPriceComparisonResponse, error) {
	var ikNumber string

	if request.IkNumber != nil {
		ikNumber = *request.IkNumber
	}

	getPackageRequest := mmi.GetPackages{
		Licensekey:              m.config.Licensekey,
		Username:                m.config.Username,
		Insuranceiknr:           ikNumber,
		IncludeClinicalPackages: false,
		// NOTE: This 2 field below are used for KV
		Pricecomparisongroupid: request.PriceComparisonGroupId,
		// Remove this field to get original pzn in response
		// Pricecomparisonpzn:     request.Pzn,
	}
	isSV := request.IsSvPatient && request.ContractId != nil
	substitutionHints := map[string][]*mmi.HAEVGPACKAGEBESCHREIBUNGSTEXT{}
	if isSV {
		var packagesResultHZV *mmi.GetHaevgSubstitutionPackagesResponse
		ikNumberInt, _ := strconv.ParseInt(*request.IkNumber, 10, 64)
		packagesResultHZV, err := m.mmiClient.GetHaevgSubstitutionPackagesContext(ctx, &mmi.GetHaevgSubstitutionPackages{
			Licensekey: m.config.Licensekey,
			Username:   m.config.Username,
			Pzn:        request.Pzn,
			// haevg
			Haevg: &mmi.HaevgRequestParameter{
				IK:                    int32(ikNumberInt),
				ReferenzDatum:         *request.ReferenceDate,
				VertragsIdentifikator: *request.ContractId,
			},
		})
		if err != nil {
			return nil, err
		}

		substitutionPzns := []string{}
		for _, vSubstitution := range packagesResultHZV.SUBSTITUTIONEN.SUBSTITUTION {
			for _, vPackage := range vSubstitution.PACKUNGEN.PACKUNG {
				substitutionPzns = append(substitutionPzns, vPackage.PZN)
				substitutionHints[vPackage.PZN] = vSubstitution.BESCHREIBUNGEN.BESCHREIBUNG
			}
		}
		if len(request.ExcludedPzns) > 0 {
			substitutionPzns = slice.Filter(substitutionPzns, func(pzn string) bool {
				return !slice.Contains(request.ExcludedPzns, pzn)
			})
		}
		getPackageRequest.Pzn_orlist = substitutionPzns
		getPackageRequest.Haevg = &mmi.HaevgRequestParameter{
			IK:                    int32(ikNumberInt),
			ReferenzDatum:         *request.ReferenceDate,
			VertragsIdentifikator: *request.ContractId,
		}
		getPackageRequest.Pricecomparisongroupid = 0
		getPackageRequest.Pricecomparisonpzn = ""
	}
	isValidSampleOption, mmiIncludeSample, err := m.IsIncludeSampleDrug(ctx)
	if err != nil {
		return nil, err
	}
	if isValidSampleOption {
		getPackageRequest.Includesample = mmiIncludeSample
	}
	if request.IsDiscount {
		getPackageRequest.Insurancediscountfilter = 1
	}
	if request.Page == nil || *request.Page <= 0 {
		request.Page = util.NewNumberPointer[int32, int32](1)
	}
	if request.PageSize == nil || *request.PageSize <= 0 {
		request.PageSize = util.NewNumberPointer[int32, int32](10)
	}
	getPackageRequest.Page = util.GetPointerValue(request.Page)
	getPackageRequest.Pagesize = util.GetPointerValue(request.PageSize)
	var pznsOrder []string
	if request.Sort != nil {
		sort := SORT_ASC
		if request.Sort.Order == api.Desc {
			sort = SORT_DESC
		}
		if request.Sort.Field == api.Size {
			getPackageRequest.Sortorder = "NORMSIZE" + sort
		}
		if request.Sort.Field == api.Price {
			getPackageRequest.Sortorder = "PRICE" + sort
		}
	} else {
		getPackageRequest.Sortorder = SORT_FIELD_KBV
		// After send request to MMI, the response will be sorted by KBV.
		// So we need to sort it again according to the order of pzns in `GetHaevgSubstitutionPackages` response
		pznsOrder = getPackageRequest.Pzn_orlist
	}

	packagesResult, err := m.mmiClient.GetPackagesContext(ctx, &getPackageRequest)
	if err != nil {
		return nil, err
	}
	if packagesResult.STATUS.Code != 0 {
		return nil, errors.New(packagesResult.STATUS.Message)
	}

	isHaveDiscountProduct := true
	if request.IsDiscount && packagesResult.COUNT == 0 {
		getPackageRequest.Insuranceiknr = ""
		getPackageRequest.Insurancediscountfilter = 0
		isHaveDiscountProduct = false
		packagesResult, err = m.mmiClient.GetPackagesContext(ctx, &getPackageRequest)
		if err != nil {
			return nil, err
		}
	}
	pzns := slice.Map(packagesResult.PPACKAGE, func(p *mmi.PACKAGE) string {
		return p.PZN
	})

	var patientAge *int = nil
	isCopaymentExemptionTillDate := false
	if request.PatientId != nil {
		patientProfile, err := m.patientProfileRepo.GetPatientProfileByPatientId(ctx, *request.PatientId)
		if err != nil {
			return nil, err
		}
		patientAge = patientProfile.GetAge(ctx)

		isCopaymentExemptionTillDate, err = m.patientProfileRepo.CheckCopaymentExemptionTillDate(ctx, *patientProfile)
		if err != nil {
			return nil, err
		}
	}

	getThinProductsResult, err := m.mmiClient.GetThinProductsContext(ctx, &mmi.GetThinProducts{
		Licensekey: m.config.Licensekey,
		Username:   m.config.Username,
		Pzn_orlist: pzns,
		Patientage: int32(util.GetPointerValue(patientAge)),
	})
	if err != nil {
		return nil, err
	}
	if getThinProductsResult.STATUS.Code != 0 {
		return nil, errors.New(getThinProductsResult.STATUS.Message)
	}
	thinProductMap := make(map[int32]mmi.THINPRODUCT)
	for _, thinProduct := range getThinProductsResult.Products.Product {
		if thinProduct != nil {
			thinProductMap[thinProduct.Id] = *thinProduct
		}
	}

	moleculeUnitEntries, err := m.getCatalogEntriesByName(ctx, CATALOGNAME_MOLECULE)
	if err != nil {
		return nil, err
	}
	normSizeEntries, err := m.getCatalogEntriesByName(ctx, CATALOGNAME_NORMSIZE)
	if err != nil {
		return nil, err
	}
	amrsByPzn, err := m.getAMRs(ctx, pzns, patientAge)
	if err != nil {
		return nil, err
	}
	medicines := []medicine_common.Medicine{}
	for _, productPackage := range packagesResult.PPACKAGE {
		if productPackage == nil || productPackage.PRODUCT == nil || productPackage.PRODUCT.ID == 0 {
			continue
		}
		if thinProduct, ok := thinProductMap[productPackage.PRODUCT.ID]; ok {
			var AMRs []medicine_common.AMR
			if int64(thinProduct.HasAmr3ExclusionFlag) == 0 {
				AMRs = amrsByPzn[productPackage.PZN]
			}
			medicines = append(medicines, medicine_util.ToMedicineModel(util.GetPointerValue(ctx.UserInfo().UserUUID()), *productPackage, &thinProduct, moleculeUnitEntries, normSizeEntries, patientAge, isCopaymentExemptionTillDate, AMRs, util.GetPointerValue(request.IsPrivateSchein), isSV, false, false))
		}
	}
	var ikNumberValue int32

	if request.IkNumber != nil {
		ikNumberInt, err := strconv.ParseInt(util.GetPointerValue(request.IkNumber), 10, 32)
		if err != nil {
			return nil, err
		}
		ikNumberValue = int32(ikNumberInt)
	}
	var hpmErrorCodeAsWarning []string
	medicines, hpmErrorCodeAsWarning, err = m.mapHpmMedicines(ctx, api.SearchHpmMedicineRequest{
		IkNumber:      ikNumberValue,
		ReferenceDate: request.ReferenceDate,
		ContractId:    request.ContractId,
		Medicines:     medicines,
		PznsOrder:     pznsOrder,
	})
	if err != nil {
		return nil, err
	}
	if len(hpmErrorCodeAsWarning) != 0 {
		isHaveDiscountProduct = false
	}

	medicines, err = m.mapHpmSubstitution(ctx, api.SearchHpmSubstitutionRequest{
		IkNumber:      ikNumberValue,
		ReferenceDate: request.ReferenceDate,
		ContractId:    request.ContractId,
		Medicines:     medicines,
		Pzn:           request.Pzn,
	})

	for index, medicine := range medicines {
		hintsAndWarnings := []medicine_common.HintsAndWarning{}
		hints := substitutionHints[medicine.Pzn]

		for _, vHint := range hints {
			hintsAndWarnings = append(hintsAndWarnings, medicine_common.HintsAndWarning{
				StandardIndex: fmt.Sprintf("%d", vHint.INDEX),
				Text:          vHint.INHALT,
			})
		}

		medicines[index].HintsAndWarnings = hintsAndWarnings
	}

	if err != nil {
		return nil, err
	}

	total := packagesResult.COUNT
	return &api.GetPriceComparisonResponse{
		Medicines:             medicines,
		Page:                  *request.Page,
		Total:                 total,
		IsHaveDiscountProduct: isHaveDiscountProduct,
	}, nil
}

func (m *MedicineKbvApp) HasSubstitution(ctx *titan.Context, request api.HasSubstitutionRequest) (*api.HasSubstitutionResponse, error) {
	if request.ContractId == nil {
		return &api.HasSubstitutionResponse{
			Pzn:             request.Pzn,
			IkNumber:        request.IkNumber,
			ContractId:      request.ContractId,
			HasSubstitution: false,
		}, nil
	}
	ikNumberInt, err := strconv.ParseInt(request.IkNumber, 10, 64)
	if err != nil {
		return nil, err
	}
	packagesResultHZV, err := m.mmiClient.GetHaevgSubstitutionPackagesContext(ctx, &mmi.GetHaevgSubstitutionPackages{
		Licensekey: m.config.Licensekey,
		Username:   m.config.Username,
		Pzn:        request.Pzn,
		// haevg
		Haevg: &mmi.HaevgRequestParameter{
			IK:                    int32(ikNumberInt),
			ReferenzDatum:         request.ReferenceDate,
			VertragsIdentifikator: *request.ContractId,
		},
	})
	if err != nil {
		return nil, err
	}

	substitutionPzns := []string{}
	for _, vSubstitution := range packagesResultHZV.SUBSTITUTIONEN.SUBSTITUTION {
		for _, vPackage := range vSubstitution.PACKUNGEN.PACKUNG {
			if vPackage.PZN != request.Pzn {
				substitutionPzns = append(substitutionPzns, vPackage.PZN)
			}
		}
	}
	return &api.HasSubstitutionResponse{
		Pzn:             request.Pzn,
		IkNumber:        request.IkNumber,
		ContractId:      request.ContractId,
		HasSubstitution: len(substitutionPzns) > 0,
	}, nil
}

func (m *MedicineKbvApp) GetTechInformation(ctx *titan.Context, request api.GetTechInformationRequest) (*api.GetTechInformationResponse, error) {
	getDocumentResult, err := m.mmiClient.GetDocumentsContext(ctx, &mmi.GetDocuments{
		Licensekey:       m.config.Licensekey,
		Username:         m.config.Username,
		Documenttypecode: string(medicine_common.DocumentTypeCode_SPC),
		Documentid:       int32(request.TechInformationId),
		Maxresult:        1,
	})
	if err != nil {
		return nil, err
	}
	if getDocumentResult.STATUS.Code != 0 {
		return nil, errors.New(getDocumentResult.STATUS.Message)
	}
	if len(getDocumentResult.DOCUMENT) == 0 {
		return &api.GetTechInformationResponse{
			Documents: []medicine_common.TextInfomationItem{},
		}, nil
	}

	var documents []medicine_common.TextInfomationItem
	for _, docItem := range getDocumentResult.DOCUMENT[0].CATEGORY_LIST {
		order, _ := strconv.Atoi(docItem.NAME_SORT)
		textItem := medicine_common.TextInfomationItem{
			Name:    docItem.NAME,
			Content: docItem.CONTENT,
			Order:   int64(order),
		}
		documents = append(documents, textItem)
	}

	return &api.GetTechInformationResponse{
		Documents: documents,
	}, nil
}

func (m *MedicineKbvApp) GetARV(ctx *titan.Context, request api.GetARVRequest) (*api.GetARVResponse, error) {
	arvRes, err := m.mmiClient.GetARVContext(ctx, &mmi.GetARV{
		Licensekey: m.config.Licensekey,
		Username:   m.config.Username,
		Bsnr:       request.Bsnr,
		Lanr:       request.Lanr,
		Pzn:        request.Pzn,
		Disabledobjects: []string{
			"PACKAGEGROUP",
			"ALTERNATIVEPACKAGEGROUP",
			"HINT",
		},
	})
	if err != nil {
		return nil, err
	}
	if arvRes.STATUS.Code != 0 {
		return nil, errors.New(arvRes.STATUS.Message)
	}

	if arvRes.ARV == nil {
		return &api.GetARVResponse{
			Items: []api.ARVItem{},
		}, nil
	}

	items := []api.ARVItem{}
	for _, arvResponseItem := range arvRes.ARV {
		var validFrom, validTo *int64
		if arvResponseItem.VALIDFROM != "" {
			validFrom = util.NewNumberPointer[int64, int64](carbon.Parse(string(arvResponseItem.VALIDFROM)).TimestampMilli())
		}
		if arvResponseItem.VALIDTO != "" {
			validTo = util.NewNumberPointer[int64, int64](carbon.Parse(string(arvResponseItem.VALIDTO)).TimestampMilli())
		}
		newItem := api.ARVItem{
			ARVId:     int64(arvResponseItem.ID),
			TypeName:  arvResponseItem.ARVTYPE.NAME,
			ValidFrom: validFrom,
			ValidTo:   validTo,
		}

		items = append(items, newItem)
	}

	return &api.GetARVResponse{
		Items: items,
	}, nil
}

func (m *MedicineKbvApp) GetAlternativeDetails(ctx *titan.Context, request api.GetAlternativeDetailsRequest) (*api.GetAlternativeDetailsResponse, error) {
	if request.Page == nil || *request.Page <= 0 {
		request.Page = util.NewNumberPointer[int32, int32](1)
	}
	if request.PageSize == nil || *request.PageSize <= 0 {
		request.PageSize = util.NewNumberPointer[int32, int32](10)
	}
	if request.Filter == nil {
		request.Filter = &api.AlternativeDetailsFilter{}
	}
	hasFilter := request.Filter.DosaForm != nil ||
		request.Filter.Size != nil ||
		request.Filter.ConcentrationMin != nil ||
		request.Filter.ConcentrationMax != nil

	arvItems, err := m.getAllARVDetails(ctx, request.Bsnr, request.Lanr, request.Pzn)
	if err != nil {
		return nil, err
	}

	var arvItem *mmi.ARV
	for _, item := range arvItems {
		if int64(item.ID) == request.ARVId {
			arvItem = item
		}
	}

	if arvItem == nil || arvItem.ALTERNATIVEPACKAGEGROUP == nil {
		return &api.GetAlternativeDetailsResponse{
			Medicines:       []medicine_common.Medicine{},
			Page:            0,
			Total:           0,
			DosaFormFilters: []string{},
			SizeFilters:     []string{},
		}, nil
	}

	pacIds := slice.Map(arvItem.ALTERNATIVEPACKAGEGROUP.PACKAGE_LIST, func(p *mmi.ARVPACKAGE) int32 {
		return p.PACKAGEID
	})

	getPackageRequest := mmi.GetPackages{
		Licensekey:              m.config.Licensekey,
		Username:                m.config.Username,
		Insuranceiknr:           *request.IkNumber,
		Packageid_orlist:        pacIds,
		IncludeClinicalPackages: false,
		Sortorder:               "KBV",
	}
	isSV := request.IsSvPatient && request.ContractId != nil
	if isSV {
		var ikNumber int32

		if request.IkNumber != nil {
			ikNumberInt, err := strconv.ParseInt(*request.IkNumber, 10, 64)
			if err != nil {
				return nil, err
			}
			ikNumber = int32(ikNumberInt)
		}
		getPackageRequest.Haevg = &mmi.HaevgRequestParameter{
			IK:                    ikNumber,
			ReferenzDatum:         *request.ReferenceDate,
			VertragsIdentifikator: *request.ContractId,
		}
	}

	isValidSampleOption, mmiIncludeSample, err := m.IsIncludeSampleDrug(ctx)
	if err != nil {
		return nil, err
	}
	if isValidSampleOption {
		getPackageRequest.Includesample = mmiIncludeSample
	}

	packagesResult, err := m.mmiClient.GetPackagesContext(ctx, &getPackageRequest)
	if err != nil {
		return nil, err
	}
	if packagesResult.STATUS.Code != 0 {
		return nil, errors.New(packagesResult.STATUS.Message)
	}

	var patientAge *int = nil
	isCopaymentExemptionTillDate := false
	if request.PatientId != nil {
		patientProfile, err := m.patientProfileRepo.GetPatientProfileByPatientId(ctx, *request.PatientId)
		if err != nil {
			return nil, err
		}
		patientAge = patientProfile.GetAge(ctx)

		isCopaymentExemptionTillDate, err = m.patientProfileRepo.CheckCopaymentExemptionTillDate(ctx, *patientProfile)
		if err != nil {
			return nil, err
		}
	}

	getThinProductsResult, err := m.mmiClient.GetThinProductsContext(ctx, &mmi.GetThinProducts{
		Licensekey:       m.config.Licensekey,
		Username:         m.config.Username,
		Packageid_orlist: pacIds,
		Patientage:       int32(util.GetPointerValue(patientAge)),
	})
	if err != nil {
		return nil, err
	}
	if getThinProductsResult.STATUS.Code != 0 {
		return nil, errors.New(getThinProductsResult.STATUS.Message)
	}
	thinProductMap := make(map[int32]mmi.THINPRODUCT)
	for _, thinProduct := range getThinProductsResult.Products.Product {
		if thinProduct != nil {
			thinProductMap[thinProduct.Id] = *thinProduct
		}
	}
	moleculeUnitEntries, err := m.getCatalogEntriesByName(ctx, CATALOGNAME_MOLECULE)
	if err != nil {
		return nil, err
	}
	normSizeEntries, err := m.getCatalogEntriesByName(ctx, CATALOGNAME_NORMSIZE)
	if err != nil {
		return nil, err
	}

	var medicines []medicine_common.Medicine

	for _, productPackage := range packagesResult.PPACKAGE {
		if productPackage == nil || productPackage.PRODUCT == nil || productPackage.PRODUCT.ID == 0 {
			continue
		}
		if thinProduct, ok := thinProductMap[productPackage.PRODUCT.ID]; ok {
			medicines = append(medicines, medicine_util.ToMedicineModel(
				util.GetPointerValue(ctx.UserInfo().UserUUID()),
				*productPackage, &thinProduct,
				moleculeUnitEntries,
				normSizeEntries,
				patientAge,
				isCopaymentExemptionTillDate,
				nil,
				util.GetPointerValue(request.IsPrivateSchein),
				isSV,
				false,
				false,
			))
		}
	}

	// filters
	dosageForms := []string{}
	for _, med := range medicines {
		dosageForms = append(dosageForms, med.ProductInformation.DosageForm)
	}
	dosageForms = funk.UniqString(dosageForms)

	sizes := []string{}
	for _, med := range medicines {
		sizes = append(sizes, med.PackagingInformation.PackageSize.Nop)
	}
	sizes = funk.UniqString(sizes)

	// apply filter
	var filteredMedicines []medicine_common.Medicine
	if hasFilter {
		funcFilters := []func(med medicine_common.Medicine) bool{}
		if request.Filter.DosaForm != nil {
			funcFilters = append(funcFilters, func(med medicine_common.Medicine) bool {
				return med.ProductInformation.DosageForm == *request.Filter.DosaForm
			})
		}

		if request.Filter.Size != nil {
			funcFilters = append(funcFilters, func(med medicine_common.Medicine) bool {
				return med.PackagingInformation.PackageSize.Nop == *request.Filter.Size
			})
		}

		if request.Filter.ConcentrationMax != nil {
			funcFilters = append(funcFilters, func(med medicine_common.Medicine) bool {
				subtances := med.GetMainSubstances()
				if len(subtances) == 0 {
					return false
				}
				match := slice.Filter(subtances, func(s medicine_common.Substance) bool {
					amountInt := float32(s.Amount)
					return amountInt <= *request.Filter.ConcentrationMax
				})
				return len(match) > 0
			})
		}

		if request.Filter.ConcentrationMin != nil {
			funcFilters = append(funcFilters, func(med medicine_common.Medicine) bool {
				subtances := med.GetMainSubstances()
				if len(subtances) == 0 {
					return false
				}
				match := slice.Filter(subtances, func(s medicine_common.Substance) bool {
					amountInt := float32(s.Amount)
					return amountInt >= *request.Filter.ConcentrationMin
				})
				return len(match) > 0
			})
		}

		for _, med := range medicines {
			isValid := true
			for _, filter := range funcFilters {
				isValid = filter(med)
				if !isValid {
					break
				}
			}
			if isValid {
				filteredMedicines = append(filteredMedicines, med)
			}
		}
	} else {
		filteredMedicines = medicines
	}

	paginationRes := util.Paginate(filteredMedicines, *request.Page, *request.PageSize)
	return &api.GetAlternativeDetailsResponse{
		Medicines:       paginationRes.Data,
		Page:            *request.Page,
		Total:           paginationRes.TotalRecords,
		DosaFormFilters: dosageForms,
		SizeFilters:     sizes,
	}, nil
}

func (m *MedicineKbvApp) GetIWWListeDetails(ctx *titan.Context, request api.GetIWWListeDetailsRequest) (*api.GetIWWListeDetailsResponse, error) {
	arvRes, err := m.getAllARVDetails(ctx, request.Bsnr, request.Lanr, request.Pzn)
	if err != nil {
		return nil, err
	}

	arvItems := []*mmi.ARV{}
	for _, item := range arvRes {
		if funk.ContainsInt64(request.ARVIds, int64(item.ID)) {
			arvItems = append(arvItems, item)
		}
	}

	if len(arvItems) != len(request.ARVIds) {
		return &api.GetIWWListeDetailsResponse{
			Items: []api.IWWListeItem{},
		}, nil
	}

	iwwListeItems := []api.IWWListeItem{}
	for _, arvItem := range arvItems {
		classification := ""
		if arvItem.DETAIL != nil {
			classification = arvItem.DETAIL.CLASSIFICATION
		}
		docs := []api.ARVDocument{}
		if arvItem.DOCUMENT_LIST != nil {
			for _, docItem := range arvItem.DOCUMENT_LIST {
				docs = append(docs, api.ARVDocument{
					DocumentId: int64(docItem.SEQUENCEID),
					Name:       docItem.NAME,
				})
			}
		}

		hint := ""
		links := []api.ARVLink{}

		if arvItem.HINT != nil {
			hint = arvItem.HINT.TEXT
			if arvItem.HINT.LINKS != nil {
				for _, linkItem := range arvItem.HINT.LINKS {
					links = append(links, api.ARVLink{
						ATCCode:     linkItem.ATCCODE,
						Description: linkItem.DESCRIPTION,
					})
				}
			}
		}

		var validFrom, validTo *int64
		if arvItem.VALIDFROM != "" {
			validFrom = util.NewNumberPointer[int64, int64](carbon.Parse(string(arvItem.VALIDFROM)).TimestampMilli())
		}
		if arvItem.VALIDTO != "" {
			validTo = util.NewNumberPointer[int64, int64](carbon.Parse(string(arvItem.VALIDTO)).TimestampMilli())
		}
		newItem := api.IWWListeItem{
			ARVId:          int64(arvItem.ID),
			TypeName:       arvItem.ARVTYPE.NAME,
			Classification: classification,
			ValidFrom:      validFrom,
			ValidTo:        validTo,
			CreatedDate:    carbon.Parse(string(arvItem.CREATE_DATE)).TimestampMilli(),
			Hint:           hint,
			Links:          links,
			Documents:      docs,
		}
		iwwListeItems = append(iwwListeItems, newItem)
	}

	return &api.GetIWWListeDetailsResponse{
		Items: iwwListeItems,
	}, nil
}

func (m *MedicineKbvApp) getAllARVDetails(ctx *titan.Context, bsnr, lanr, pzn string) ([]*mmi.ARV, error) {
	page := 1
	pageSize := 5
	totalRecords := -1

	arvItems := []*mmi.ARV{}
	for totalRecords == -1 || (page-1)*pageSize < totalRecords {
		arvRes, err := m.mmiClient.GetARVContext(ctx, &mmi.GetARV{
			Licensekey: m.config.Licensekey,
			Username:   m.config.Username,
			Bsnr:       bsnr,
			Lanr:       lanr,
			Pzn:        pzn,
			Page:       int32(page),
			Pagesize:   int32(pageSize),
			Disabledobjects: []string{
				"PACKAGEGROUP",
				"PPackage",
				"HINTLINKPACKAGE",
			},
		})
		if err != nil {
			return nil, err
		}
		if arvRes.STATUS.Code != 0 {
			return nil, errors.New(arvRes.STATUS.Message)
		}

		totalRecords = int(arvRes.COUNT)
		page += 1
		arvItems = append(arvItems, arvRes.ARV...)
	}

	return arvItems, nil
}

func (m *MedicineKbvApp) CheckARVIndicationTreeExist(ctx *titan.Context, request api.GetARVIndicationTreeRequest) (*api.CheckARVIndicationTreeExistResponse, error) {
	arvTreeRes, err := m.mmiClient.GetARVIndicationTreesContext(ctx, &mmi.GetARVIndicationTrees{
		Licensekey: m.config.Licensekey,
		Username:   m.config.Username,
		Bsnr:       request.Bsnr,
		Disabledobjects: []string{
			"PPACKAGE",
			"ARVIWWIndMolecule",
		},
	})
	if err != nil {
		return nil, err
	}

	if arvTreeRes == nil || arvTreeRes.STATUS == nil {
		return nil, nil
	}

	if arvTreeRes.STATUS.Code != 0 {
		return nil, errors.New(arvTreeRes.STATUS.Message)
	}

	isExist := false
	if arvTreeRes.COUNT > 0 {
		isExist = true
	}
	return &api.CheckARVIndicationTreeExistResponse{
		IsExist: isExist,
	}, nil
}

func (m *MedicineKbvApp) GetARVIndicationTree(ctx *titan.Context, request api.GetARVIndicationTreeRequest) (*api.GetARVIndicationTreeResponse, error) {
	arvTreeRes, err := m.mmiClient.GetARVIndicationTreesContext(ctx, &mmi.GetARVIndicationTrees{
		Licensekey: m.config.Licensekey,
		Username:   m.config.Username,
		Bsnr:       request.Bsnr,
		Disabledobjects: []string{
			"PPACKAGE",
			"ARVIWWIndMoleculePackage",
			"ARVIWWHintLinkPackage",
		},
	})
	if err != nil {
		return nil, err
	}
	if arvTreeRes.STATUS.Code != 0 {
		return nil, errors.New(arvTreeRes.STATUS.Message)
	}
	if arvTreeRes.COUNT == 0 {
		return nil, errors.New("NOT_FOUND")
	}

	fakeRootItem := mmi.ARVIWWINDICATION{
		CHILDREN: arvTreeRes.INDICATIONTREE.INDICATIONS,
	}
	tree, err := mapOrFetchIndicationItem(ctx, m, request.Bsnr, fakeRootItem, -1)
	if err != nil {
		return nil, err
	}
	return &api.GetARVIndicationTreeResponse{
		IndicationItems: tree.Children,
	}, nil
}

func mapOrFetchIndicationItem(ctx *titan.Context, m *MedicineKbvApp, bsnr string, indicationItem mmi.ARVIWWINDICATION, depth int32) (*api.ARVIndicationItem, error) {
	indicationItemFull := indicationItem

	// fetch data
	needFetchData := indicationItem.CHILDREN == nil && indicationItem.MOLECULES == nil
	if needFetchData {
		msgStart := fmt.Sprintf("\tdepth=%d; indication = %d START\n", depth, indicationItem.ID)
		ctx.Logger().Info(msgStart)
		arvTreeRes, err := m.mmiClient.GetARVIndicationTreesContext(ctx, &mmi.GetARVIndicationTrees{
			Licensekey:   m.config.Licensekey,
			Username:     m.config.Username,
			Bsnr:         bsnr,
			Indicationid: indicationItem.ID,
			Disabledobjects: []string{
				"PPACKAGE",
				"ARVIWWIndMoleculePackage",
				"ARVIWWHintLinkPackage",
			},
			Maxresult: 1,
		})
		if err != nil {
			return nil, err
		}
		if arvTreeRes.STATUS.Code != 0 {
			return nil, errors.New(arvTreeRes.STATUS.Message)
		}
		if arvTreeRes.COUNT == 0 {
			return nil, errors.New("NOT_FOUND")
		}

		indicationItemFull = *arvTreeRes.INDICATIONTREE.INDICATIONS[0]
		msg := fmt.Sprintf("\tdepth=%d; indication = %d\n", depth, indicationItem.ID)
		ctx.Logger().Info(msg)
	}

	// map data
	id := int64(indicationItemFull.ID)
	description := indicationItemFull.NAME

	arvItem := api.ARVIndicationItem{
		IndicationId: id,
		Description:  description,
		Level:        depth,
		Children:     []api.ARVIndicationItem{},
		Molecules:    []api.ARVMoleculeItem{},
		Documents:    []api.ARVDocument{},
	}

	if indicationItemFull.DOCUMENT_LIST != nil {
		for _, docItem := range indicationItemFull.DOCUMENT_LIST {
			arvItem.Documents = append(arvItem.Documents, api.ARVDocument{
				DocumentId: int64(docItem.SEQUENCEID),
				Name:       docItem.NAME,
			})
		}
	}
	if indicationItemFull.CHILDREN != nil {
		c := make(chan *api.ARVIndicationItem)
		for childIdx, childItem := range indicationItemFull.CHILDREN {
			go func(order int, rootItem *mmi.ARVIWWINDICATION) {
				arvChildItem, err := mapOrFetchIndicationItem(ctx, m, bsnr, *rootItem, depth+1)
				arvChildItem.Order = int32(order)
				if err == nil && arvChildItem != nil {
					c <- arvChildItem
				}
			}(childIdx, childItem)
		}

		for i := 0; i < len(indicationItemFull.CHILDREN); i++ {
			arvItem.Children = append(arvItem.Children, *<-c)
		}

		close(c)
		sort.Slice(arvItem.Children, func(i, j int) bool {
			return arvItem.Children[i].Order < arvItem.Children[j].Order
		})
	}

	if indicationItemFull.MOLECULES != nil {
		c := make(chan api.ARVMoleculeItem)
		for childIdx, childItem := range indicationItemFull.MOLECULES {
			go func(order int, rootItem *mmi.ARVIWWINDMOLECULE) {
				mole, err := mapOrFetchMolecule(ctx, m, bsnr, int(indicationItemFull.ID), *rootItem)
				mole.Order = int32(order)
				if err == nil && mole != nil {
					c <- *mole
				}
			}(childIdx, childItem)
		}

		for i := 0; i < len(indicationItemFull.MOLECULES); i++ {
			arvItem.Molecules = append(arvItem.Molecules, <-c)
		}

		close(c)
		sort.Slice(arvItem.Molecules, func(i, j int) bool {
			return arvItem.Molecules[i].Order < arvItem.Molecules[j].Order
		})
	}

	return &arvItem, nil
}

func mapOrFetchMolecule(ctx *titan.Context, m *MedicineKbvApp, bsnr string, indicationId int, moleculeItem mmi.ARVIWWINDMOLECULE) (*api.ARVMoleculeItem, error) {
	moleculeFull := moleculeItem
	// fetch data
	needFetchData := moleculeItem.ATCS == nil
	if needFetchData {
		arvTreeRes, err := m.mmiClient.GetARVIndicationTreesContext(ctx, &mmi.GetARVIndicationTrees{
			Licensekey:   m.config.Licensekey,
			Username:     m.config.Username,
			Bsnr:         bsnr,
			Indicationid: int32(indicationId),
			Moleculeid:   moleculeItem.ID,
			Disabledobjects: []string{
				"PPACKAGE",
				"ARVIWWIndMoleculePackage",
				"ARVIWWHintLinkPackage",
			},
			Maxresult: 1,
		})
		if err != nil {
			return nil, err
		}
		if arvTreeRes.STATUS.Code != 0 {
			return nil, errors.New(arvTreeRes.STATUS.Message)
		}
		if arvTreeRes.COUNT == 0 {
			return nil, errors.New("NOT_FOUND")
		}

		moleculeFull = *arvTreeRes.INDICATIONTREE.INDICATIONS[0].MOLECULES[0]
	}

	// map data
	description := ""
	links := []api.ARVLink{}
	if len(moleculeFull.HINTS) > 0 {
		hint := *moleculeFull.HINTS[0]
		description = hint.DESCRIPTION
		if hint.LINKS != nil {
			for _, link := range hint.LINKS {
				links = append(links, api.ARVLink{
					ATCCode:     link.ATCCODE,
					Description: link.DESCRIPTION,
				})
			}
		}
	}

	docs := []api.ARVDocument{}
	if moleculeFull.DOCUMENT_MOLECULE_LIST != nil {
		for _, docItem := range moleculeFull.DOCUMENT_MOLECULE_LIST {
			docs = append(docs, api.ARVDocument{
				DocumentId: int64(docItem.SEQUENCEID),
				Name:       docItem.NAME,
			})
		}
	}

	atc := ""
	if len(moleculeFull.ATCS) > 0 {
		atc = moleculeFull.ATCS[0].ATCCODE
	}

	colorId := int32(0)
	colorName := ""
	colorHexCode := ""
	categoryName := ""
	if moleculeFull.CATEGORY != nil && moleculeFull.CATEGORY.COLOR != nil {
		colorId = moleculeFull.CATEGORY.COLOR.COLORID
		colorName = moleculeFull.CATEGORY.COLOR.NAME
		colorHexCode = moleculeFull.CATEGORY.COLOR.RGB
		categoryName = moleculeFull.CATEGORY.NAME
	}

	molecule := api.ARVMoleculeItem{
		MoleculeId:   int64(moleculeFull.ID),
		Name:         moleculeFull.NAME,
		Description:  description,
		Links:        links,
		Documents:    docs,
		ATCCode:      atc,
		ColorId:      colorId,
		ColorName:    colorName,
		ColorHexCode: colorHexCode,
		CategoryName: categoryName,
	}

	return &molecule, nil
}

func (m *MedicineKbvApp) GetIndicationDocumentLink(ctx *titan.Context, request api.GetDocumentLinkRequest) (*api.GetDocumentLinkResponse, error) {
	fileName := path.Join("sdarv-indication-document", fmt.Sprintf("%d-%d.pdf", request.Id, request.DocumentId))
	isExist, err := m.minioClient.ObjectExists(ctx, m.bucketName, fileName)
	if err != nil {
		return nil, err
	}

	if !isExist {
		binaryRes, err := m.mmiClient.GetIndicationDocumentBinary(ctx, &mmi.GetBinaryFile{
			Licensekey: &m.config.Licensekey,
			Username:   &m.config.Username,
			Id:         int(request.Id),
			SequenceId: int(request.DocumentId),
		})
		if err != nil {
			return nil, errors.WithMessage(err, "can not get indication document")
		}

		if len(binaryRes) == 0 {
			return nil, errors.New("file is empty")
		}

		fileReader := bytes.NewReader(binaryRes)
		_, err = m.minioClient.PutObject(ctx, m.bucketName, fileName, fileReader, fileReader.Size(), minio.PutObjectOptions{})
		if err != nil {
			return nil, errors.WithMessage(err, "can not upload indication document")
		}
	}

	url, err := m.minioClient.PresignedGetObject(ctx, m.bucketName, fileName, 50*time.Minute, url.Values{})
	if err != nil {
		return nil, errors.WithMessage(err, "can not get presigned url")
	}

	return &api.GetDocumentLinkResponse{
		DocumentLink: url.String(),
	}, nil
}

func (m *MedicineKbvApp) GetAMRDocumentLink(ctx *titan.Context, request api.GetAMRDocumentLinkRequest) (*api.GetDocumentLinkResponse, error) {
	fileName := path.Join("sdarv-amr-document", fmt.Sprintf("%s-%s.pdf", request.Filename, request.AmrTypeCode))
	isExist, err := m.minioClient.ObjectExists(ctx, m.bucketName, fileName)
	if err != nil {
		return nil, errors.WithMessage(err, "can not get amr document")
	}
	if !isExist {
		binaryRes, err := m.mmiClient.GetAMRDocumentBinary(ctx, request.AmrTypeCode, &mmi.GetBinaryFile{
			Licensekey: &m.config.Licensekey,
			Username:   &m.config.Username,
			Filename:   &request.Filename,
		})
		if err != nil {
			return nil, errors.WithMessage(err, "can not get amr document binary")
		}
		if len(binaryRes) == 0 {
			return nil, nil
		}

		fileReader := bytes.NewReader(binaryRes)
		_, err = m.minioClient.PutObject(ctx, m.bucketName, fileName, fileReader, fileReader.Size(), minio.PutObjectOptions{})
		if err != nil {
			return nil, errors.WithMessage(err, "can not upload amr document")
		}
	}
	url, err := m.minioClient.PresignedGetObject(ctx, m.bucketName, fileName, 50*time.Minute, url.Values{
		"response-content-type":        []string{"application/pdf"},
		"response-content-disposition": []string{fmt.Sprintf("inline; filename=\"%q\"", fileName)},
	})
	if err != nil {
		return nil, errors.WithMessage(err, "can not get presigned url")
	}
	return &api.GetDocumentLinkResponse{
		DocumentLink: url.String(),
	}, nil
}

func (m *MedicineKbvApp) GetMoleculeDocumentLink(ctx *titan.Context, request api.GetDocumentLinkRequest) (*api.GetDocumentLinkResponse, error) {
	fileName := path.Join("sdarv-molecule-document", fmt.Sprintf("%d-%d.pdf", request.Id, request.DocumentId))
	isExist, err := m.minioClient.ObjectExists(ctx, m.bucketName, fileName)
	if err != nil {
		return nil, errors.WithMessage(err, "can not get molecule document")
	}

	if !isExist {
		binaryRes, err := m.mmiClient.GetMoleculeDocumentBinary(ctx, &mmi.GetBinaryFile{
			Licensekey: &m.config.Licensekey,
			Username:   &m.config.Username,
			Id:         int(request.Id),
			SequenceId: int(request.DocumentId),
		})
		if err != nil {
			return nil, errors.WithDetails(err, "can not get molecule document binary")
		}

		if len(binaryRes) == 0 {
			return nil, nil
		}

		fileReader := bytes.NewReader(binaryRes)
		_, err = m.minioClient.PutObject(ctx, m.bucketName, fileName, fileReader, fileReader.Size(), minio.PutObjectOptions{})
		if err != nil {
			return nil, errors.WithMessage(err, "can not upload molecule document")
		}
	}

	url, err := m.minioClient.PresignedGetObject(ctx, m.bucketName, fileName, 50*time.Minute, url.Values{})
	if err != nil {
		return nil, errors.WithMessage(err, "can not get presigned url")
	}

	return &api.GetDocumentLinkResponse{
		DocumentLink: url.String(),
	}, nil
}

func (m *MedicineKbvApp) GetARVDocumentLink(ctx *titan.Context, request api.GetDocumentLinkRequest) (*api.GetDocumentLinkResponse, error) {
	fileName := path.Join("sdarv-arv-document", fmt.Sprintf("%d-%d.pdf", request.Id, request.DocumentId))
	isExist, err := m.minioClient.ObjectExists(ctx, m.bucketName, fileName)
	if err != nil {
		return nil, errors.WithMessage(err, "can not get arv document")
	}

	if !isExist {
		binaryRes, err := m.mmiClient.GetARVDocumentBinary(ctx, &mmi.GetBinaryFile{
			Licensekey: &m.config.Licensekey,
			Username:   &m.config.Username,
			Id:         int(request.Id),
			SequenceId: int(request.DocumentId),
		})
		if err != nil {
			return nil, errors.WithMessage(err, "can not get arv document binary")
		}

		if len(binaryRes) == 0 {
			return nil, nil
		}
		fileReader := bytes.NewReader(binaryRes)

		_, err = m.minioClient.PutObject(ctx, m.bucketName, fileName, fileReader, fileReader.Size(), minio.PutObjectOptions{})
		if err != nil {
			return nil, err
		}
	}

	url, err := m.minioClient.PresignedGetObject(ctx, m.bucketName, fileName, 50*time.Minute, url.Values{})
	if err != nil {
		return nil, errors.WithMessage(err, "can not get presigned url")
	}

	return &api.GetDocumentLinkResponse{
		DocumentLink: url.String(),
	}, nil
}

func (m *MedicineKbvApp) GetCatalogEntriesByName(ctx *titan.Context, req api.GetCatalogEntriesByNameRequest) (*api.GetCatalogEntriesByNameResponse, error) {
	catalogEntries, err := m.getCatalogEntriesByName(ctx, req.Name)
	if err != nil {
		return nil, errors.WithMessage(err, "can not get catalog entries")
	}

	apiCatalogEntries := slice.Map(catalogEntries, func(entry *mmi.CATALOGENTRY) medicine_common.CatalogEntry { return entry.ToCatalogEntry() })
	return &api.GetCatalogEntriesByNameResponse{
		CatalogEntries: apiCatalogEntries,
	}, nil
}

func (m *MedicineKbvApp) getCatalogEntriesByName(ctx *titan.Context, catalogName string) ([]*mmi.CATALOGENTRY, error) {
	key := "catalogEntries-" + catalogName
	isExists, err := m.redisClient.Exists(ctx, key)
	if err != nil {
		errMsg := fmt.Sprintf("Error while checking if key exists: %s", err.Error())
		_ = ctx.Logger().WithMessage(err, errMsg)
	}

	if isExists {
		cacheData := m.redisClient.Get(ctx, key)
		q, err := gob_util.Decode[mmi.GetCatalogEntriesResponse](bytes.NewBufferString(cacheData))
		if err != nil {
			ctx.Logger().Error("Error while decoding data", "err", err.Error())
			_, _ = m.redisClient.Delete(ctx, key)
		} else if len(q.CATALOGENTRY) > 0 {
			return q.CATALOGENTRY, nil
		}
	}

	moleculeUnitEntries, err := m.mmiClient.GetCatalogEntriesContext(ctx, &mmi.GetCatalogEntries{
		Licensekey:       m.config.Licensekey,
		Username:         m.config.Username,
		Catalogshortname: catalogName,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "can not get catalog entries")
	}
	if moleculeUnitEntries.STATUS.Code != 0 {
		return nil, errors.New(moleculeUnitEntries.STATUS.Message)
	}
	// save to cache
	if len(moleculeUnitEntries.CATALOGENTRY) > 0 {
		b, err := gob_util.Encode(moleculeUnitEntries)
		if err != nil {
			ctx.Logger().Error("Error while encoding data", "err", err.Error())
			_, _ = m.redisClient.Delete(ctx, "catalogEntries-"+catalogName)
		} else {
			_ = m.redisClient.SetEx(ctx, "catalogEntries-"+catalogName, b.String(), 24*time.Hour)
		}
	}

	return moleculeUnitEntries.CATALOGENTRY, nil
}

func (m *MedicineKbvApp) GetPackageSizes(ctx *titan.Context, request api.GetPackageSizesRequest) (*api.GetPackageSizesResponse, error) {
	productid_orlist := request.MedicineProductIds

	if len(productid_orlist) == 0 {
		return &api.GetPackageSizesResponse{
			NormSizeCodeList: nil,
		}, nil
	}

	packageRequest := &mmi.GetPackages{
		Licensekey:              m.config.Licensekey,
		Username:                m.config.Username,
		Productid_orlist:        productid_orlist,
		IncludeClinicalPackages: false,
	}

	if request.IsSvPatient && request.ContractId != nil {
		packageRequest.Haevg = &mmi.HaevgRequestParameter{
			IK:                    *request.IkNumber,
			ReferenzDatum:         *request.ReferenceDate,
			VertragsIdentifikator: *request.ContractId,
		}
	}

	isValidSampleOption, mmiIncludeSample, err := m.IsIncludeSampleDrug(ctx)
	if err != nil {
		return nil, errors.WithMessage(err, "can not get include sample drug")
	}
	if isValidSampleOption {
		packageRequest.Includesample = mmiIncludeSample
	}

	getPackagesResult, err := m.mmiClient.GetPackagesContext(ctx, packageRequest)
	if err != nil {
		return nil, errors.WithMessage(err, "can not get packages")
	}

	if getPackagesResult.STATUS.Code != 0 {
		return nil, errors.New(getPackagesResult.STATUS.Message)
	}

	if getPackagesResult.COUNT == 0 {
		return &api.GetPackageSizesResponse{
			NormSizeCodeList: nil,
		}, nil
	}

	normSizeCatalogs, err := m.getCatalogEntriesByName(ctx, CATALOGNAME_NORMSIZE)
	if err != nil {
		return nil, errors.WithMessage(err, "can not get catalog entries")
	}

	if len(normSizeCatalogs) == 0 {
		return &api.GetPackageSizesResponse{
			NormSizeCodeList: nil,
		}, nil
	}

	normSizeCodeList := slice.Reduce(getPackagesResult.PPACKAGE, func(list []api.NormSizeCodeListItem, ppkg *mmi.PACKAGE) []api.NormSizeCodeListItem {
		normSizeCatalog := slice.FindOne(normSizeCatalogs, func(size *mmi.CATALOGENTRY) bool {
			return ppkg != nil && size.CODE == ppkg.SIZE_NORMSIZECODE
		})

		if normSizeCatalog == nil {
			return list
		}

		normSizeCodeListItem := api.NormSizeCodeListItem{
			Pzn:               ppkg.PZN,
			Label:             (*normSizeCatalog).NAME,
			ProductName:       ppkg.PRODUCT.NAME,
			MedicineProductId: ppkg.PRODUCTID,
		}

		normSizeCodeListItem.PriceComparisonGroupId = ppkg.PRICE_COMPARISONGROUP1

		list = append(list, normSizeCodeListItem)

		return list
	}, []api.NormSizeCodeListItem{})

	normSizeCodeList = slice.UniqBy(normSizeCodeList, func(code api.NormSizeCodeListItem) string {
		return code.Label
	})

	return &api.GetPackageSizesResponse{
		NormSizeCodeList: normSizeCodeList,
	}, nil
}

func (m *MedicineKbvApp) GetMedicationByPzn(ctx *titan.Context, request api.GetMedicationsByPznRequest) (*api.GetMedicationsByPznResponse, error) {
	packageRequest := &mmi.GetPackages{
		Licensekey:              m.config.Licensekey,
		Username:                m.config.Username,
		Pzn_orlist:              []string{request.Pzn},
		Maxresult:               1,
		IncludeClinicalPackages: false,
	}

	isSV := request.IsSvPatient && request.ContractId != nil
	if isSV {
		packageRequest.Haevg = &mmi.HaevgRequestParameter{
			IK:                    util.GetPointerValue(request.IkNumber),
			ReferenzDatum:         *request.ReferenceDate,
			VertragsIdentifikator: *request.ContractId,
		}
	}

	isValidSampleOption, mmiIncludeSample, err := m.IsIncludeSampleDrug(ctx)
	if err != nil {
		return nil, errors.WithMessage(err, "can not get include sample drug")
	}

	if isValidSampleOption {
		packageRequest.Includesample = mmiIncludeSample
	}

	getPackagesResult, err := m.mmiClient.GetPackagesContext(ctx, packageRequest)
	if err != nil {
		return nil, errors.WithMessage(err, "can not get packages")
	}

	if getPackagesResult.STATUS.Code != 0 {
		return nil, errors.New(getPackagesResult.STATUS.Message)
	}

	if getPackagesResult.COUNT == 0 {
		return &api.GetMedicationsByPznResponse{
			Medicine: medicine_common.Medicine{},
		}, nil
	}

	pacIds := slice.Map(getPackagesResult.PPACKAGE, func(pkg *mmi.PACKAGE) int32 {
		return pkg.ID
	})

	var patientAge *int = nil
	isCopaymentExemptionTillDate := false
	if request.PatientId != nil {
		patientProfile, err := m.patientProfileRepo.GetPatientProfileByPatientId(ctx, *request.PatientId)
		if err != nil {
			return nil, err
		}
		patientAge = patientProfile.GetAge(ctx)

		isCopaymentExemptionTillDate, err = m.patientProfileRepo.CheckCopaymentExemptionTillDate(ctx, *patientProfile)
		if err != nil {
			return nil, err
		}
	}

	getThinProductsResult, err := m.mmiClient.GetThinProductsContext(ctx, &mmi.GetThinProducts{
		Licensekey:       m.config.Licensekey,
		Username:         m.config.Username,
		Packageid_orlist: pacIds,
		Patientage:       int32(util.GetPointerValue(patientAge)),
	})
	if err != nil {
		return nil, err
	}

	if getThinProductsResult.STATUS.Code != 0 {
		return nil, errors.New(getThinProductsResult.STATUS.Message)
	}

	moleculeUnitEntries, err := m.getCatalogEntriesByName(ctx, CATALOGNAME_MOLECULE)
	if err != nil {
		return nil, errors.WithMessage(err, "can not get molecule unit entries")
	}

	normSizeEntries, err := m.getCatalogEntriesByName(ctx, CATALOGNAME_NORMSIZE)
	if err != nil {
		return nil, errors.WithMessage(err, "can not get molecule unit entries")
	}

	pkg := getPackagesResult.PPACKAGE[0]

	mappedThinProduct := slice.FindOne(getThinProductsResult.Products.Product, func(thinProduct *mmi.THINPRODUCT) bool {
		return thinProduct.Id == pkg.PRODUCT.ID
	})

	medicine := medicine_util.ToMedicineModel(
		util.GetPointerValue(ctx.UserInfo().UserUUID()),
		*pkg,
		*mappedThinProduct,
		moleculeUnitEntries,
		normSizeEntries,
		patientAge,
		isCopaymentExemptionTillDate,
		nil,
		util.GetPointerValue(request.IsPrivateSchein),
		isSV,
		false,
		false,
	)

	return &api.GetMedicationsByPznResponse{
		Medicine: medicine,
	}, nil
}

func (m *MedicineKbvApp) GetMedicationsByPackageSizeForPriceComparison(ctx *titan.Context, request api.GetMedicationsByPackageSizeForPriceComparisonRequest) (*api.GetMedicationsByPackageSizeForPriceComparisonResponse, error) {
	sortorder := util.NewString("PRICE_ASC") // only sort on price
	if request.Sort != nil {
		sort := SORT_ASC
		if request.Sort.Order == api.Desc {
			sort = SORT_DESC
		}
		sortorder = util.NewString("PRICE" + sort)
	}

	if request.Page == nil || *request.Page <= 0 {
		request.Page = util.NewNumberPointer[int32, int32](1)
	}

	if request.PageSize == nil || *request.PageSize <= 0 {
		request.PageSize = util.NewNumberPointer[int32, int32](10)
	}

	packageRequest := &mmi.GetPackages{
		Licensekey:              m.config.Licensekey,
		Username:                m.config.Username,
		Pricecomparisongroupid:  *request.PriceComparisonGroupId,
		Sortorder:               *sortorder,
		Page:                    *request.Page,
		Pagesize:                *request.PageSize,
		IncludeClinicalPackages: false,
	}

	isSV := request.IsSvPatient && request.ContractId != nil
	if isSV {
		packageRequest.Haevg = &mmi.HaevgRequestParameter{
			IK:                    util.GetPointerValue(request.IkNumber),
			ReferenzDatum:         *request.ReferenceDate,
			VertragsIdentifikator: *request.ContractId,
		}
	}

	isValidSampleOption, mmiIncludeSample, err := m.IsIncludeSampleDrug(ctx)
	if err != nil {
		return nil, err
	}
	if isValidSampleOption {
		packageRequest.Includesample = mmiIncludeSample
	}

	packagesResult, err := m.mmiClient.GetPackagesContext(ctx, packageRequest)
	if err != nil {
		return nil, err
	}

	if packagesResult.STATUS.Code != 0 {
		return nil, errors.New(packagesResult.STATUS.Message)
	}

	if packagesResult.COUNT == 0 {
		return &api.GetMedicationsByPackageSizeForPriceComparisonResponse{
			Medicines: nil,
			Page:      *request.Page,
			Total:     0,
		}, nil
	}

	pacIds := slice.Map(packagesResult.PPACKAGE, func(pkg *mmi.PACKAGE) int32 {
		return pkg.ID
	})

	var patientAge *int = nil
	isCopaymentExemptionTillDate := false
	if request.PatientId != nil {
		patientProfile, err := m.patientProfileRepo.GetPatientProfileByPatientId(ctx, *request.PatientId)
		if err != nil {
			return nil, err
		}
		patientAge = patientProfile.GetAge(ctx)

		isCopaymentExemptionTillDate, err = m.patientProfileRepo.CheckCopaymentExemptionTillDate(ctx, *patientProfile)
		if err != nil {
			return nil, err
		}
	}

	getThinProductsResult, err := m.mmiClient.GetThinProductsContext(ctx, &mmi.GetThinProducts{
		Licensekey:       m.config.Licensekey,
		Username:         m.config.Username,
		Packageid_orlist: pacIds,
		Patientage:       int32(util.GetPointerValue(patientAge)),
	})
	if err != nil {
		return nil, err
	}
	if getThinProductsResult.STATUS.Code != 0 {
		return nil, errors.New(getThinProductsResult.STATUS.Message)
	}
	thinProductMap := make(map[int32]mmi.THINPRODUCT)
	for _, thinProduct := range getThinProductsResult.Products.Product {
		if thinProduct != nil {
			thinProductMap[thinProduct.Id] = *thinProduct
		}
	}

	moleculeUnitEntries, err := m.getCatalogEntriesByName(ctx, CATALOGNAME_MOLECULE)
	if err != nil {
		return nil, err
	}

	normSizeEntries, err := m.getCatalogEntriesByName(ctx, CATALOGNAME_NORMSIZE)
	if err != nil {
		return nil, err
	}

	medicines := []medicine_common.Medicine{}
	for _, productPackage := range packagesResult.PPACKAGE {
		if productPackage == nil || productPackage.PRODUCT == nil || productPackage.PRODUCT.ID == 0 {
			continue
		}
		if thinProduct, ok := thinProductMap[productPackage.PRODUCT.ID]; ok {
			medicines = append(medicines, medicine_util.ToMedicineModel(util.GetPointerValue(ctx.UserInfo().UserUUID()),
				*productPackage,
				&thinProduct,
				moleculeUnitEntries,
				normSizeEntries,
				patientAge,
				isCopaymentExemptionTillDate,
				nil,
				util.GetPointerValue(request.IsPrivateSchein),
				isSV,
				false,
				false,
			))
		}
	}

	return &api.GetMedicationsByPackageSizeForPriceComparisonResponse{
		Medicines: medicines,
		Page:      *request.Page,
		Total:     packagesResult.COUNT,
	}, nil
}

func (m *MedicineKbvApp) ChangeConcentration(ctx *titan.Context, request api.ChangeConcentrationRequest) (*api.SearchMedicineResponse, error) {
	packageSearch := &mmi.GetPackages{
		Licensekey:              m.config.Licensekey,
		Username:                m.config.Username,
		Fulltextsearch:          1,
		IncludeClinicalPackages: false,
	}
	isValidSampleOption, mmiIncludeSample, err := m.IsIncludeSampleDrug(ctx)
	if err != nil {
		return nil, err
	}
	if isValidSampleOption {
		packageSearch.Includesample = mmiIncludeSample
	}
	for _, moleculeId := range request.MoleculeIds {
		id := int(moleculeId)
		packageSearch.Moleculeid_andlist = append(packageSearch.Moleculeid_andlist, int32(id))
	}

	providerId := int32(request.ProviderId)
	packageSearch.Companyid_orlist = append(packageSearch.Companyid_orlist, providerId)

	packageSearch.Pharmformcode_orlist = append(packageSearch.Pharmformcode_orlist, request.DosageFormCode)

	isSV := request.IsSvPatient && request.ContractId != nil
	if isSV {
		packageSearch.Haevg = &mmi.HaevgRequestParameter{
			IK:                    util.GetPointerValue(request.IkNumber),
			ReferenzDatum:         *request.ReferenceDate,
			VertragsIdentifikator: *request.ContractId,
		}
	}
	packagesResult, err := m.mmiClient.GetPackagesContext(ctx, packageSearch)
	if err != nil {
		return nil, err
	}
	if packagesResult.STATUS.Code != 0 {
		return nil, errors.New(packagesResult.STATUS.Message)
	}

	isHaveDiscountProduct := true
	moleculeUnitEntries, err := m.getCatalogEntriesByName(ctx, CATALOGNAME_MOLECULE)
	if err != nil {
		return nil, err
	}
	normSizeEntries, err := m.getCatalogEntriesByName(ctx, CATALOGNAME_NORMSIZE)
	if err != nil {
		return nil, err
	}
	var patientAge *int = nil
	isCopaymentExemptionTillDate := false
	if request.PatientId != nil {
		patientProfile, err := m.patientProfileRepo.GetPatientProfileByPatientId(ctx, *request.PatientId)
		if err != nil {
			return nil, err
		}
		patientAge = patientProfile.GetAge(ctx)

		isCopaymentExemptionTillDate, err = m.patientProfileRepo.CheckCopaymentExemptionTillDate(ctx, *patientProfile)
		if err != nil {
			return nil, err
		}
	}

	var medicines []medicine_common.Medicine
	for _, productPackage := range packagesResult.PPACKAGE {
		medicines = append(medicines, medicine_util.ToMedicineModelChangeConcentration(*productPackage,
			moleculeUnitEntries,
			normSizeEntries,
			patientAge,
			isCopaymentExemptionTillDate,
			util.GetPointerValue(request.IsPrivateSchein),
			isSV))
	}

	return &api.SearchMedicineResponse{
		Medicines:             medicines,
		Page:                  0,
		Total:                 int32(1),
		TotalRecords:          packagesResult.COUNT,
		IsHaveDiscountProduct: isHaveDiscountProduct,
	}, nil
}

func (m *MedicineKbvApp) IsIncludeSampleDrug(ctx *titan.Context) (bool, *mmi.SampleInclusion, error) {
	settingsRs, err := m.settingApp.GetSetting(ctx, settingApi.SettingsRequest{
		Feature: settings_common.SettingsFeatures_KbvMedication,
	})
	if err != nil {
		return false, nil, err
	}

	drugSampleSetting := settingsRs.Settings[string(settingApi.IsShowDrugSample)]

	for _, s := range []string{"YES", "NO", "ONLY"} {
		if s == drugSampleSetting {
			return true, util.NewPointer(mmi.SampleInclusion(drugSampleSetting)), nil
		}
	}

	return false, nil, nil
}

func (m *MedicineKbvApp) GetRedHandLetters(ctx *titan.Context) (*api.GetRedHandLettersResponse, error) {
	key := "getRedHandLetters"
	isExists, err := m.redisClient.Exists(ctx, key)
	if err != nil {
		errMsg := fmt.Sprintf("Error while checking if key exists: %s", err.Error())
		_ = ctx.Logger().WithMessage(err, errMsg)
	}

	if isExists {
		cacheData := m.redisClient.Get(ctx, key)
		data, err := gob_util.Decode[[]api.HandLetterItem](bytes.NewBufferString(cacheData))
		if err != nil {
			ctx.Logger().Error("Error while decoding data", "err", err.Error())
			_, _ = m.redisClient.Delete(ctx, key)
		} else if len(data) > 0 {
			return &api.GetRedHandLettersResponse{
				RedHandLetters: data,
			}, nil
		}
	}

	res, err := m.mmiClient.GetRoteHandBriefeContext(ctx, &mmi.GetRoteHandBriefe{
		Licensekey: m.config.Licensekey,
		Username:   m.config.Username,
	})
	if err != nil {
		return nil, err
	}
	result := []api.HandLetterItem{}
	for _, item := range res.Rote_hand_briefe.Rote_hand_brief {
		dateValidTo, err := util.ParseStringToTime(item.Date_valid_to, util.YYYY_MM_DD)
		if err == nil && util.Now(ctx).After(*dateValidTo) {
			continue
		}
		result = append(result, api.HandLetterItem{
			Id:           int64(item.Id),
			Name:         item.Name,
			SourceName:   item.Source_name,
			DateValidTo:  item.Date_valid_to,
			DocumentDate: item.Document_date,
			Uuid:         item.Uuid,
		})
	}

	resultBytes, err := json.Marshal(result)
	if err != nil {
		log.Fatalf("Error marshalling result: %v", err)
	}
	_ = m.redisClient.SetEx(ctx, key, string(resultBytes), 14*24*time.Hour)
	return &api.GetRedHandLettersResponse{
		RedHandLetters: result,
	}, nil
}

func (m *MedicineKbvApp) GetBlueHandLetters(ctx *titan.Context) (*api.GetBlueHandLettersResponse, error) {
	key := "getBlueHandLetters"
	isExists, err := m.redisClient.Exists(ctx, key)
	if err != nil {
		errMsg := fmt.Sprintf("Error while checking if key exists: %s", err.Error())
		_ = ctx.Logger().WithMessage(err, errMsg)
	}

	if isExists {
		cacheData := m.redisClient.Get(ctx, key)
		data, err := gob_util.Decode[[]api.HandLetterItem](bytes.NewBufferString(cacheData))
		if err != nil {
			ctx.Logger().Error("Error while decoding data", "err", err.Error())
			_, _ = m.redisClient.Delete(ctx, key)
		} else if len(data) > 0 {
			return &api.GetBlueHandLettersResponse{
				BlueHandLetters: data,
			}, nil
		}
	}

	res, err := m.mmiClient.GetRoteHandBriefeContext(ctx, &mmi.GetRoteHandBriefe{
		Licensekey: m.config.Licensekey,
		Username:   m.config.Username,
	})
	if err != nil {
		return nil, err
	}
	result := []api.HandLetterItem{}
	for _, item := range res.Rote_hand_briefe.Rote_hand_brief {
		dateValidTo, err := util.ParseStringToTime(item.Date_valid_to, util.YYYY_MM_DD)
		if err == nil && util.Now(ctx).After(*dateValidTo) {
			continue
		}
		result = append(result, api.HandLetterItem{
			Id:           int64(item.Id),
			Name:         item.Name,
			SourceName:   item.Source_name,
			DateValidTo:  item.Date_valid_to,
			DocumentDate: item.Document_date,
			Uuid:         item.Uuid,
		})
	}

	resultBytes, err := json.Marshal(result)
	if err != nil {
		log.Fatalf("Error marshalling result: %v", err)
	}
	_ = m.redisClient.SetEx(ctx, key, string(resultBytes), 14*24*time.Hour)
	return &api.GetBlueHandLettersResponse{
		BlueHandLetters: result,
	}, nil
}

func (m *MedicineKbvApp) GetFavourites(ctx *titan.Context, req api.GetFavouritesRequest) (*api.GetFavouritesResponse, error) {
	pzns, err := m.mmiService.GetFavouritesPzns(ctx, req)
	if err != nil {
		return nil, err
	}
	if len(pzns) == 0 {
		return &api.GetFavouritesResponse{
			Medicines: []medicine_common.Medicine{},
		}, nil
	}
	profile, err := m.profileService.GetMyEmployeeProfile(ctx)
	if err != nil {
		return nil, err
	}
	medicines, err := m.GetByPzns(ctx, GetByPznsRequest{
		Pzns: pzns,
		Bsnr: profile.Bsnr,
		Lanr: *profile.Lanr,
	})
	if err != nil {
		return nil, err
	}
	return &api.GetFavouritesResponse{
		Medicines: medicines,
	}, nil
}

func (m *MedicineKbvApp) MarkFavourite(ctx *titan.Context, req api.MarkFavouriteRequest) error {
	return m.mmiService.MarkFavourite(ctx, req)
}

func (m *MedicineKbvApp) GetListPznAtcForHighPrescription(ctx *titan.Context, req api.GetListPznAtcForHighPrescriptionRequest) (*api.GetListPznAtcForHighPrescriptionResponse, error) {
	contract := m.contractService.GetContractDetailById(req.ContractId)
	if contract == nil {
		return nil, errors.New("contract not found")
	}
	if !contract.CheckExistAnforderung(model.ICheckExistAnforderung{
		ComplianceIds: []string{"VSST923"},
		CheckTime:     util.NowUnixMillis(ctx),
	}) {
		return &api.GetListPznAtcForHighPrescriptionResponse{
			Data: []api.PznAtcForHighPrescription{},
		}, nil
	}
	hpmConfig, err := m.bsnrService.GetHpmConfig(ctx, ctx.UserInfo().BsnrId)
	if err != nil {
		return nil, err
	}
	hpmRestService := hpm_rest_client.NewServiceRestWithEndpoint(hpmConfig.Endpoint)
	resp, err := hpmRestService.GetListPznAtcForHighPrescription(ctx, req.ContractId)
	if err != nil {
		return nil, err
	}
	return &api.GetListPznAtcForHighPrescriptionResponse{
		Data: slice.Map(resp, func(item hpm_rest_client.GetListPznAtcForHighPrescriptionResponse) api.PznAtcForHighPrescription {
			return api.PznAtcForHighPrescription{
				AtcCode: item.AtcCode,
				Pzn:     item.Pzn,
			}
		}),
	}, nil
}
