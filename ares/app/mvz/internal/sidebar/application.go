package sidebar

import (
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/sidebar"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	patient_overview_api "git.tutum.dev/medi/tutum/ares/app/mvz/patient_overview"
	"git.tutum.dev/medi/tutum/ares/app/mvz/patient_profile"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_overview"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/ptv_import"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"github.com/pkg/errors"
	"github.com/submodule-org/submodule.go/v2"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"
)

var SidebarAppMod = submodule.Make[sidebar.SidebarApp](NewSidebarApp, config.SocketServiceClientMod)

type SidebarApp struct {
	notifier            *sidebar.SidebarSocketNotifier
	patientProfileRepo  *patient_profile.PatientProfileRepository
	scheinRepo          schein.ScheinRepoDefaultRepository
	patientOverviewRepo patient_overview.PatientOverviewDefaultRepository
	ptvImportRepo       ptv_import.PtvImportRepo
}

func NewSidebarApp(socket *socket_api.SocketServiceClient) sidebar.SidebarApp {
	return &SidebarApp{
		notifier:            sidebar.NewSidebarSocketNotifier(socket),
		patientProfileRepo:  patient_profile.NewPatientProfileRepository(),
		scheinRepo:          schein.NewScheinRepoDefaultRepository(),
		patientOverviewRepo: patient_overview.NewPatientOverviewDefaultRepository(),
		ptvImportRepo:       ptv_import.NewPtvImportDefaultRepository(),
	}
}

func (srv *SidebarApp) GetDefaultInformation(ctx *titan.Context) (*sidebar.GetDefaultInformationResponse, error) {
	res := &sidebar.GetDefaultInformationResponse{
		HasNotSubmittedEnrollment: false,
		HasPendingPtvImport:       false,
	}

	notSubmittedEnrollment, err := srv.patientOverviewRepo.FindOne(ctx, patient_overview_api.NotSubmitEnrollment)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to find faulty enrollment")
	}
	if notSubmittedEnrollment != nil {
		res.HasNotSubmittedEnrollment = true
	}

	hasPendingPtvImport, err := srv.ptvImportRepo.HasNotDoneRecord(ctx)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to find ptv import")
	}
	if hasPendingPtvImport {
		res.HasPendingPtvImport = true
	}
	return res, nil
}

func (srv *SidebarApp) HandleEventSidebar(ctx *titan.Context, request sidebar.EventSidebarRepsonse) error {
	return srv.notifier.NotifyCareProviderSidebarRepsonse(ctx, &request)
}

func (srv *SidebarApp) HandleEventForPatientOverview(ctx *titan.Context, request sidebar.EventSidebarRepsonse) error {
	return srv.notifier.NotifyCareProviderSidebarRepsonse(ctx, &request)
}
