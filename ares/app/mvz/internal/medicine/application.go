package medicine

import (
	"encoding/json"
	"fmt"
	sortPkg "sort"
	"strings"

	bmpApp "git.tutum.dev/medi/tutum/ares/app/mvz/internal/bmp"
	"git.tutum.dev/medi/tutum/ares/pkg/formkey"
	"git.tutum.dev/medi/tutum/ares/pkg/pdfform"
	"git.tutum.dev/medi/tutum/ares/pkg/pkg_errors"
	"git.tutum.dev/medi/tutum/ares/share"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	util_pkg "git.tutum.dev/medi/tutum/pkg/util"

	"emperror.dev/errors"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/bmp"
	api "git.tutum.dev/medi/tutum/ares/app/mvz/api/medicine"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/medicine_kbv"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_profile"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/timeline"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/form_print"
	appKbv "git.tutum.dev/medi/tutum/ares/app/mvz/internal/medicine_kbv"
	patient_profile_bff "git.tutum.dev/medi/tutum/ares/app/mvz/patient_profile"
	hpm_rest_client "git.tutum.dev/medi/tutum/ares/pkg/hpm_rest/client"
	"git.tutum.dev/medi/tutum/ares/service/contract/contract"
	"git.tutum.dev/medi/tutum/ares/service/contract/contract/model"
	bmpDomain "git.tutum.dev/medi/tutum/ares/service/domains/api/bmp"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common/patientfile"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/medicine_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	form_common "git.tutum.dev/medi/tutum/ares/service/domains/form/common"
	mmi_service "git.tutum.dev/medi/tutum/ares/service/domains/mmi"
	"git.tutum.dev/medi/tutum/ares/service/domains/pkg/constant"
	bmpCommon "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/bmp/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/prescribed"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/prescription"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/shoppingbag"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	timeline_common "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/timeline_service"
	medicineClient "git.tutum.dev/medi/tutum/ares/service/medicine/api"
	"git.tutum.dev/medi/tutum/ares/service/schein"
	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	"github.com/spf13/cast"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/hestia/infrastructure/util"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// MedicineApp medicine struct
type MedicineApp struct {
	contractService          *contract.Service
	employeeProfileService   profile.EmployeeProfileService
	shoppingBagRepo          shoppingbag.MedicineShoppingBagDefaultRepository
	notifierSocket           *api.MedicineSocketNotifier
	medicinePrescriptionRepo prescription.MedicinePrescriptionDefaultRepository
	medicineService          medicineClient.MedicineApp
	prescribedMedicineRepo   prescribed.PrescribedMedicationDefaultRepository
	medicineKBV              medicine_kbv.MedicineKbvApp
	bmpApp                   bmp.BmpApp
	patientProfileApp        patient_profile.PatientProfileApp
	pdfform                  *pdfform.Client
	timelineService          *timeline_service.TimelineService[patient_encounter.EncounterMedicinePrescription]
	notifier                 *api.MedicineNotifier
	formPrint                *form_print.FormPrint
	mmiService               mmi_service.MMIService
	hpmRestService           *hpm_rest_client.ServiceRest
	scheinService            *schein.ScheinService
}

var MedicineAppMod = submodule.Make[api.MedicineApp](
	NewMedicineApp,
	contract.ContractServiceMod,
	share.EmployeeProfileServiceMod,
	config.SocketServiceClientMod,
	bmpApp.BmpAppMod,
	config.MvzAppConfigMod,
	patient_profile_bff.PatientProfileServiceMod,
	share.MedicineDomainService,
	appKbv.MedicineKbvAppMod,
	form_print.FormPrintMod,
	timeline_service.TimelineServiceMedicinePrescriptionMod,
	mmi_service.MMIServiceMod,
	hpm_rest_client.HpmRestServiceMod,
	schein.ScheinServiceMod,
)

func NewMedicineApp(
	contractService *contract.Service,
	employeeProfileService profile.EmployeeProfileService,
	socket *socket_api.SocketServiceClient,
	bmpApp bmp.BmpApp,
	config *config.MvzAppConfigs,
	patientProfileAppClient patient_profile.PatientProfileApp,
	medicineService medicineClient.MedicineApp,
	medicineKbvAppClient medicine_kbv.MedicineKbvApp,
	formPrint *form_print.FormPrint,
	timelineService *timeline_service.TimelineService[patient_encounter.EncounterMedicinePrescription],
	mmiService mmi_service.MMIService,
	hpmRestService *hpm_rest_client.ServiceRest,
	scheinService *schein.ScheinService,
) api.MedicineApp {
	return &MedicineApp{
		contractService,
		employeeProfileService,
		shoppingbag.NewMedicineShoppingBagDefaultRepository(),
		api.NewMedicineSocketNotifier(socket),
		prescription.NewMedicinePrescriptionDefaultRepository(),
		medicineService,
		prescribed.NewPrescribedMedicationDefaultRepository(),
		medicineKbvAppClient,
		bmpApp,
		patientProfileAppClient,
		pdfform.NewPDFFormClient(config.PdfServicePath),
		timelineService,
		api.NewMedicineNotifier(),
		formPrint,
		mmiService,
		hpmRestService,
		scheinService,
	}
}

func (m *MedicineApp) PrintForm(ctx *titan.Context, request api.PrintFormRequest) (*api.PrintFormResponse, error) {
	form, err := m.medicinePrescriptionRepo.FindFormById(ctx, request.FormId)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to find form")
	}
	var formInfos []api.FormInfo
	if err := copier.Copy(&formInfos, form.FormInfos); err != nil {
		return nil, errors.WithMessage(err, "failed to copy form infos")
	}
	for i := range formInfos {
		formInfos[i].PrintOption = &request.PrintOption
	}

	var patientId *uuid.UUID

	if request.PreventGetPatientProfile == nil || !*request.PreventGetPatientProfile {
		patientId = &form.PatientId
	}

	result, err := m.printToPDF(ctx,
		formInfos,
		&form.TreatmentDoctorId,
		patientId,
		&form.ScheinId,
		util_pkg.GetPointerValue(request.HasSupportForm907),
		form.AssignedToBsnrId,
	)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to print form")
	}

	_, err = m.timelineService.UpdateFormPrintDateTimeline(ctx, request.PatientId, request.FormId, util.NowUnixMillis(ctx))
	if err != nil {
		return nil, errors.WithMessage(err, "failed to update print date encounter medicine")
	}
	return &api.PrintFormResponse{
		PrintResults: result,
	}, nil
}

func (m *MedicineApp) getContractTypeById(ctx *titan.Context, contractId *string) (*common.ContractType, error) {
	var contractType common.ContractType
	if contractId != nil {
		contract := m.contractService.GetContractDetailById(*contractId)
		if contract == nil {
			return nil, pkg_errors.NewTitanCommonException(ctx, error_code.ErrorCode_Contract_Not_Found, *contractId)
		}

		contractType = common.ContractType(*contract.GetContractType().Type())
	} else {
		contractType = common.ContractType_KvContract
	}

	return &contractType, nil
}

func (m *MedicineApp) UpdateTreatmentDoctorMedicationForm(ctx *titan.Context, request api.UpdateTreatmentDoctorMedicationFormRequest) error {
	medicationFormResponse, err := m.timelineService.GetMedicationFormById(ctx, request.PatientId, request.MedicationFormId)
	if err != nil {
		return errors.Wrap(err, "can not medicationFormResponse")
	}

	if medicationFormResponse.TreatmentDoctorId.String() == request.TreatmentDoctorId.String() {
		return nil
	}

	contractType, err := m.getContractTypeById(ctx, medicationFormResponse.ContractId)
	if err != nil {
		return errors.WithStack(err)
	}

	err = m.medicineService.UpdateTreatmentDoctorMedicationForm(ctx, medicineClient.UpdateTreatmentDoctorMedicationFormRequest{
		MedicationFormId:  request.MedicationFormId,
		TreatmentDoctorId: request.TreatmentDoctorId,
		ContractType:      *contractType,
		PatientId:         request.PatientId,
	})
	if err != nil {
		return errors.Wrap(err, "can not UpdateTreatmentDoctorMedicationForm")
	}
	var formInfoResponses []api.MedicineShoppingBagInfo
	if err := copier.Copy(&formInfoResponses, medicationFormResponse.Medicines); err != nil {
		return err
	}
	err = m.HandleEventViewMedicationForm(ctx, api.EventViewMedicationForm{
		PatientId: medicationFormResponse.PatientId,
		ViewMedicationForm: api.ViewMedicationForm{
			TreatmentDoctorId: request.TreatmentDoctorId,
			AssignedToBsnrId:  util_pkg.GetPointerValue(request.AssignedToBsnrId),
			FormInfo: api.FormInfoResponse{
				Id:               medicationFormResponse.Id,
				FormInfoResponse: formInfoResponses,
				FormSetting:      medicationFormResponse.FormSetting,
				CurrentFormType:  api.FormType(medicationFormResponse.CurrentFormType),
				PrescribeDate:    medicationFormResponse.PrescribeDate,
				IsNotPicked:      medicationFormResponse.IsNotPicked,
				IsShowFavHint:    medicationFormResponse.IsShowFavHint,
			},
		},
		EventType: api.ChangeTreatmentDoctor,
	})
	if err != nil {
		return errors.Wrap(err, "can not HandleEventViewMedicationForm")
	}
	return nil
}

// CheckMissingDiagnose check missing diagnose
func (m *MedicineApp) CheckMissingDiagnose(ctx *titan.Context, request api.AddToShoppingBagRequest) (*api.CheckMissingDiagnoseResponse, error) {
	showWarning, err := m.showWarning(ctx, request)
	if err != nil {
		ctx.Logger().Error(err.Error())
		return nil, err
	}
	return &api.CheckMissingDiagnoseResponse{
		ShowWarning: showWarning,
	}, nil
}

func (m *MedicineApp) CheckMissingDiagnoses(ctx *titan.Context, request api.CheckMissingDiagnosesRequest) (*api.CheckMissingDiagnosesResponse, error) {
	var pzns []string
	for _, pzn := range request.Pzns {
		showWarning, err := m.showWarning(ctx, api.AddToShoppingBagRequest{
			PatientId:  &request.PatientId,
			DoctorId:   &request.DoctorId,
			ContractId: request.ContractId,
			IkNumber:   request.IkNumber,
			Medicine: api.MedicineShoppingBagInfo{
				Pzn: &pzn,
			},
		})
		if err != nil {
			ctx.Logger().Error(err.Error())
			return nil, err
		}
		if showWarning {
			pzns = append(pzns, pzn)
		}
	}

	return &api.CheckMissingDiagnosesResponse{
		Pzns: pzns,
	}, nil
}

func (m *MedicineApp) CreateMedicationPlan(ctx *titan.Context, request api.CreateMedicationPlanRequest) error {
	prescribedMedication, err := m.prescribedMedicineRepo.FindById(ctx, request.PrescribedMedicationId)
	if err != nil {
		return errors.WithStack(err)
	}
	if prescribedMedication == nil {
		return errors.New("can not find prescribedMedication")
	}

	currentMedicine := prescribedMedication.Medicine

	pharmFormCodeIFA := ""
	dosageForm := ""
	unit := ""
	if currentMedicine.ProductInformation != nil {
		pharmFormCodeIFA = currentMedicine.ProductInformation.PharmFormCodeIFA
		dosageForm = currentMedicine.ProductInformation.DosageForm
	}
	if currentMedicine.MedicationPlanInformation != nil {
		unit = currentMedicine.MedicationPlanInformation.MedicationPlanUnitCode
	}

	isKbv := prescribedMedication.Medicine.Type == shoppingbag.KBV
	intake := currentMedicine.IntakeInterval
	mediCommon := &bmpCommon.MedicationInformation{
		Pzn:                      currentMedicine.Pzn,
		Substances:               []bmpCommon.Substance{},
		TradeName:                currentMedicine.Name,
		DrugForm:                 function.If(isKbv, pharmFormCodeIFA, dosageForm),
		DrugFormDescription:      util_pkg.GetPointerValue(currentMedicine.DrugFormInformation),
		IntakeInterval:           &bmpCommon.IntakeInterval{},
		Unit:                     unit,
		UnitDescription:          "",
		Hint:                     request.Hint,
		Reason:                   "",
		AdditionalLine:           "",
		ModifiedDate:             0,
		IsKBVMedication:          function.If(isKbv, util_pkg.NewBool(true), util_pkg.NewBool(false)),
		KBVMedicineID:            currentMedicine.KBVMedicineId,
		ProductInformation:       currentMedicine.ProductInformation,
		FormType:                 currentMedicine.CurrentFormType,
		Quantity:                 currentMedicine.Quantity,
		FurtherInformation:       util_pkg.GetPointerValue(currentMedicine.FurtherInformation),
		IsArtificialInsemination: util_pkg.GetPointerValue(currentMedicine.IsArtificialInsemination),
		IsEPrescription:          util_pkg.GetPointerValue(currentMedicine.IsEPrescription),
		AsNeeded:                 currentMedicine.AsNeeded,
		AutIdem:                  currentMedicine.AutIdem,
		Vaccinate:                util_pkg.GetPointerValue(currentMedicine.Vaccinate),
	}
	if intake != nil {
		if intake.Morning != nil {
			mediCommon.IntakeInterval.Morning = cast.ToString(intake.Morning)
		}
		if intake.Evening != nil {
			mediCommon.IntakeInterval.Evening = cast.ToString(intake.Evening)
		}
		if intake.Afternoon != nil {
			mediCommon.IntakeInterval.Afternoon = cast.ToString(intake.Afternoon)
		}
		if intake.Night != nil {
			mediCommon.IntakeInterval.Night = cast.ToString(intake.Night)
		}

		mediCommon.IntakeInterval.Freetext = intake.Freetext
		mediCommon.IntakeInterval.DJ = intake.DJ
	}

	mainSubstances := currentMedicine.GetMainSubstances()
	if len(mainSubstances) > 0 {
		bmpSubs := []bmpCommon.Substance{}
		for _, v := range mainSubstances {
			bmpSubs = append(bmpSubs, bmpCommon.Substance{
				Name:          v.Name,
				Concentration: strings.Replace(strings.Replace(fmt.Sprintf("%.2f", v.Amount), ".", ",", 1), ",00", "", 1) + " " + v.Unit,
			})
		}
		mediCommon.Substances = bmpSubs
	}

	addEntryRequest := bmpDomain.AddEntryRequest{
		Type:                  bmpDomain.MEDICATION,
		SubHeading:            nil,
		MedicationInformation: mediCommon,
		RecipeInformation:     nil,
		FreeTextInformation:   nil,
		PatientId:             *prescribedMedication.PatientId,
		DoctorId:              *prescribedMedication.DoctorId,
		ContractId:            prescribedMedication.ContractId,
		EncounterCase:         prescribedMedication.EncounterCase,
	}
	res, err := m.bmpApp.AddEntry(ctx, addEntryRequest)
	if err != nil {
		return errors.WithStack(err)
	}
	if res == nil {
		return nil
	}

	prescribedMedication.MedicationPlanId = &res.Id
	_, err = m.prescribedMedicineRepo.Update(ctx, *prescribedMedication)
	if err != nil {
		return errors.WithStack(err)
	}
	err = m.HandleEventMedicationPlanChanged(ctx, api.EventMedicationPlanChanged{
		EventType:     api.CreateMedicationPlan,
		Payload:       *res,
		EncounterCase: prescribedMedication.EncounterCase,
		PatientId:     *prescribedMedication.PatientId,
	})
	if err != nil {
		return errors.WithStack(err)
	}
	err = m.HandleEventMedicationPrescribeChanged(ctx, api.EventMedicationPrescribe{
		EventType: api.CreateMedicationPlanSuccess,
		Payload: api.MedicationPrescribeResponse{
			Id:               request.PrescribedMedicationId,
			MedicationPlanId: &res.Id,
		},
		PatientId: *prescribedMedication.PatientId,
	})
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func determineFormType(currentFormType api.FormType, hasSupportForm907 bool) (string, bool) {
	isSupportForm907 := false
	formType := string(currentFormType)

	switch formType {
	case string(api.KREZ):
		formType = string(form_common.Muster_16)
		isSupportForm907 = hasSupportForm907
	case string(api.GREZ):
		formType = "Gruenes_Rezept"
	case string(api.TPrescription):
		formType = "T-Rezept-Muster"
	case string(api.Private):
		formType = "Blaues_Rezept"
	case string(api.BTM):
		formType = "Btm_Rezept_Print"
		isSupportForm907 = hasSupportForm907
	case string(api.AOKNordwet):
		formType = "AOK_Nordwet"
	case string(api.AOKBremen):
		formType = "AOK_Bremen_impfstoff"
	case string(api.Muster16aBay):
		formType = "Muster_16a_bay"
	}

	return formType, isSupportForm907
}

func (m *MedicineApp) printToPDF(
	ctx *titan.Context,
	formInfos []api.FormInfo,
	doctorId, patientId, scheinId *uuid.UUID,
	hasSupportForm907 bool,
	bsnrId *uuid.UUID,
) ([]api.PrintResult, error) {
	newFormInfos := slice.Map(formInfos, func(f api.FormInfo) form_print.BaseFormInfo {
		formType, isSupportForm907 := determineFormType(f.CurrentFormType, hasSupportForm907)
		formMapData, err := formkey.NewFormMap[string](f.FormSetting)

		if err != nil {
			ctx.Logger().Error("Cannot parse form", err.Error())
			return form_print.BaseFormInfo{}
		}

		medicineForms := []api.FormType{
			api.KREZ, api.GREZ, api.Private, api.TPrescription, api.BTM,
		}
		isValidFormType := slice.Contains(medicineForms, f.CurrentFormType)

		// for case custom print medication by lines
		if isValidFormType && (f.EPrescription == nil || f.EPrescription.ErezeptId == nil) {
			delete(formMapData, "label_medication")
			delete(formMapData, "label_medication1")
			delete(formMapData, "label_medication2")
			delete(formMapData, "label_medication3")
			delete(formMapData, "label_medication4")
			delete(formMapData, "label_medication5")
		}

		medicinePrivateForms := []api.FormType{
			api.GREZ, api.Private,
		}
		isValidPrivateFormType := slice.Contains(medicinePrivateForms, f.CurrentFormType)

		var schein = &schein.FullScheinInfo{}

		if scheinId != nil {
			schein, err = m.scheinService.GetFullScheinById(ctx, *scheinId)
		}

		if err != nil {
			ctx.Logger().Error("Cannot parse map to json", err.Error())
			return form_print.BaseFormInfo{}
		}

		isKvScheinPrivateForm := isValidPrivateFormType && schein.Schein.IsKvSchein()

		if isKvScheinPrivateForm {
			formMapData["label_insurance_name"] = "PRIVAT"
			formMapData["label_ik_number"] = ""
			formMapData["label_insurance_number"] = ""
		}

		formMapDataConvert, err := json.Marshal(formMapData)

		if err != nil {
			ctx.Logger().Error("Cannot parse map to json", err.Error())
			return form_print.BaseFormInfo{}
		}

		formMapDataStr := string(formMapDataConvert)

		return form_print.BaseFormInfo{
			FormSetting:           formMapDataStr,
			FormName:              form_common.FormName(formType),
			PrintDate:             util_pkg.GetPointerValue(f.PrintDate),
			PrintOption:           f.PrintOption,
			PrescribeDate:         util_pkg.GetPointerValue(f.PrescribeDate),
			HasSupportForm907:     isSupportForm907,
			FormType:              form_common.FormType_medication,
			IsKvScheinPrivateForm: isKvScheinPrivateForm,
		}
	})
	printResult, err := m.formPrint.PrintToPDF(ctx,
		newFormInfos,
		util_pkg.GetPointerValue(doctorId),
		util_pkg.GetPointerValue(patientId),
		util_pkg.GetPointerValue(scheinId),
		bsnrId,
	)
	if err != nil {
		return nil, errors.WithMessage(err, "can not print form")
	}
	mappedPrintResult := slice.Map(printResult, func(f form_common.PrintResult) api.PrintResult {
		return api.PrintResult{
			CurrentFormType: api.FormType(f.FormName),
			FormUrl:         f.FormUrl,
		}
	})
	return mappedPrintResult, nil
}

func (m *MedicineApp) PrescribeBSNR(ctx *titan.Context, request api.PrescribeRequest) (*api.PrescribeResponse, error) {
	var req medicineClient.PrescribeBSNRRequest
	err := copier.Copy(&req, request)
	if err != nil {
		ctx.Logger().Error(err.Error())
		return nil, errors.WithMessage(err, "can not copy request")
	}

	contractType, err := m.getContractTypeById(ctx, request.ContractId)
	if err != nil {
		return nil, err
	}
	req.ContractType = *contractType

	// TODO: update Prescribe only handle for normal prescription not erezept
	res, err := m.medicineService.PrescribeBSNR(ctx, req)
	if err != nil {
		ctx.Logger().Error(err.Error())
		return nil, errors.WithMessage(err, "can not prescribe")
	}

	var medicineDeletedIds []uuid.UUID
	for i := range request.FormInfos {
		medicineDeletedIds = append(medicineDeletedIds, request.FormInfos[i].MedicineIDs...)
	}

	err = m.notifierSocket.NotifyCareProviderShoppingBagRequest(ctx, &api.EventShoppingBagRequest{
		Type: api.DeleteShoppingBag,
		Payload: api.MedicineShoppingBagInfo{
			Id: &res.ShoppingBagId,
		},
		MedicineDeletedIds: medicineDeletedIds,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "can not notify care provider")
	}
	err = m.notifierSocket.NotifyCareProviderMedicationPrescribe(ctx, &api.EventMedicationPrescribe{
		EventType:  api.PrescribeSuccess,
		Payload:    api.MedicationPrescribeResponse{},
		Bsnr:       &req.Bsnr,
		ContractId: req.ContractId,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "can not notify care provider")
	}

	var apiResponse api.PrescribeResponse
	if err := copier.Copy(&apiResponse, res); err != nil {
		return nil, errors.WithMessage(err, "can not copy response")
	}
	printResult, err := m.printToPDF(ctx, request.FormInfos, request.TreatmentDoctorId, request.PatientId, request.ScheinId, false, request.AssignedToBsnrId)
	if err != nil {
		return nil, errors.WithMessage(err, "can not print form")
	}
	apiResponse.PrintResults = printResult
	return &apiResponse, nil
}

func (m *MedicineApp) PrescribePatient(ctx *titan.Context, request api.PrescribeRequest) (*api.PrescribeResponse, error) {
	var req medicineClient.PrescribeRequest
	err := copier.Copy(&req, request)
	if err != nil {
		ctx.Logger().Error(err.Error())
		return nil, errors.WithMessage(err, "can not copy request")
	}

	contractType, err := m.getContractTypeById(ctx, request.ContractId)
	if err != nil {
		return nil, err
	}

	req.ContractType = *contractType

	// TODO: update Prescribe only handle for normal prescription not erezept
	res, err := m.medicineService.Prescribe(ctx, req)
	if err != nil {
		ctx.Logger().Error(err.Error())
		return nil, errors.WithMessage(err, "can not prescribe")
	}

	var medicineDeletedIds []uuid.UUID
	for i := range request.FormInfos {
		medicineDeletedIds = append(medicineDeletedIds, request.FormInfos[i].MedicineIDs...)
	}

	err = m.notifierSocket.NotifyCareProviderShoppingBagRequest(ctx, &api.EventShoppingBagRequest{
		Type: api.DeleteShoppingBag,
		Payload: api.MedicineShoppingBagInfo{
			Id: &res.ShoppingBagId,
		},
		MedicineDeletedIds: medicineDeletedIds,
	})

	if err != nil {
		return nil, errors.WithMessage(err, "can not notify care provider")
	}
	err = m.notifierSocket.NotifyCareProviderMedicationPrescribe(ctx, &api.EventMedicationPrescribe{
		EventType:  api.PrescribeSuccess,
		Payload:    api.MedicationPrescribeResponse{},
		PatientId:  req.PatientId,
		DoctorId:   req.DoctorId,
		ContractId: req.ContractId,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "can not notify care provider")
	}

	var apiResponse api.PrescribeResponse
	if err := copier.Copy(&apiResponse, res); err != nil {
		return nil, errors.WithMessage(err, "can not copy response")
	}

	var patientId *uuid.UUID

	if request.PreventGetPatientProfile == nil || !*request.PreventGetPatientProfile {
		patientId = request.PatientId
	}

	// TODO: handle print eRezept from pdf in bundle xml
	printResult, err := m.printToPDF(ctx, request.FormInfos, request.TreatmentDoctorId, patientId, request.ScheinId, util_pkg.GetPointerValue(request.HasSupportForm907), request.AssignedToBsnrId)
	if err != nil {
		return nil, errors.WithMessage(err, "can not print form")
	}
	apiResponse.PrintResults = printResult
	return &apiResponse, nil
}

// Prescribe Prescribe medicine
// after prescribe medicine it will remove the current shopping bag
func (m *MedicineApp) Prescribe(ctx *titan.Context, request api.PrescribeRequest) (*api.PrescribeResponse, error) {
	if request.PatientId != nil && request.DoctorId != nil {
		return m.PrescribePatient(ctx, request)
	} else if IsQueryBSNR(request.Bsnr) {
		return m.PrescribeBSNR(ctx, request)
	}

	return nil, nil
}

func (m *MedicineApp) DeleteMedicationPrescribe(ctx *titan.Context, request api.DeleteMedicationPrescribeRequest) (*api.DeleteMedicationPrescribeResponse, error) {
	var deleteRequest medicineClient.DeleteMedicationPrescribeRequest
	if err := copier.Copy(&deleteRequest, request); err != nil {
		return nil, errors.WithMessage(err, "can not copy request")
	}

	deleteResponse, err := m.medicineService.DeleteMedicationPrescribe(ctx, deleteRequest)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	var apiResponse api.DeleteMedicationPrescribeResponse
	if err := copier.Copy(&apiResponse, deleteResponse); err != nil {
		return nil, errors.WithMessage(err, "can not copy response")
	}
	err = m.notifierSocket.NotifyCareProviderMedicationPrescribe(ctx, &api.EventMedicationPrescribe{
		EventType: api.Remove,
		Payload: api.MedicationPrescribeResponse{
			Id: request.Id,
		},
		PatientId: request.PatientId,
	})
	if err != nil {
		return nil, errors.WithMessage(err, "can not notify care provider")
	}
	return &apiResponse, nil
}

func (m *MedicineApp) DeleteMedicationPlan(ctx *titan.Context, request api.DeleteMedicationPlanRequest) (*api.DeleteMedicationPlanResponse, error) {
	deleteEntryRequest := bmpDomain.DeleteEntryRequest{
		EntryId:       request.MedicationPlanId,
		Type:          bmpDomain.MEDICATION,
		PatientId:     request.PatientId,
		DoctorId:      request.DoctorId,
		ContractId:    request.ContractId,
		EncounterCase: request.EncounterCase,
	}
	err := m.bmpApp.DeleteEntry(ctx, deleteEntryRequest)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	filter := bson.M{
		prescribed.Field_MedicationPlanId: request.MedicationPlanId,
		prescribed.Field_PatientId:        request.PatientId,
	}
	_, err = m.prescribedMedicineRepo.FindOneAndUpdate(
		ctx,
		filter,
		bson.M{
			"$set": bson.M{
				prescribed.Field_MedicationPlanId: nil,
			},
		})
	if err != nil {
		return nil, err
	}
	err = m.HandleEventMedicationPlanChanged(ctx, api.EventMedicationPlanChanged{
		EventType: api.RemoveMedicationPlan,
	})
	return &api.DeleteMedicationPlanResponse{}, err
}

func IsQueryBSNR(bsnr *string) bool {
	return len(util_pkg.GetStringValue(bsnr)) > 1
}

func (m *MedicineApp) GetMedicationPrescribe(ctx *titan.Context, request api.GetMedicationPrescribeRequest) (*api.GetMedicationPrescribeResponse, error) {
	pzns, err := m.mmiService.GetFavouritesPzns(ctx, medicine_kbv.GetFavouritesRequest{})
	if err != nil {
		return nil, err
	}
	filter := bson.M{
		prescription.Field_PatientId: request.PatientId,
	}
	if IsQueryBSNR(request.Bsnr) {
		filter = bson.M{
			prescription.Field_Bsnr: *request.Bsnr,
		}
	}

	order := -1
	sort := bson.M{
		prescribed.Field_PrescribeDate: order,
	}
	if request.Order == common.ASC {
		order = 1
	}
	if request.SortField == api.PrescribeDate {
		sort = bson.M{
			prescribed.Field_PrescribeDate: order,
		}
	} else if request.SortField == api.Tradename {
		MedicineInfo_Name := fmt.Sprintf("%s.%s", prescribed.Field_Medicine, shoppingbag.Field_MedicineInfo_Name)
		sort = bson.M{
			MedicineInfo_Name: order,
		}
	} else if request.SortField == api.Status {
		sort = bson.M{
			prescribed.Field_PrintDate: order,
		}
	}
	medicinePrescriptions, err := m.prescribedMedicineRepo.Find(ctx, filter, options.Find().SetSort(sort).SetCollation(&options.Collation{Locale: "en"}))
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if request.SortField == api.SizeMed {
		isAsc := true
		if order == -1 {
			isAsc = false
		}
		sortPkg.Slice(medicinePrescriptions, func(i, j int) bool {
			var medicineI, medicineJ medicine_common.Medicine
			err = copier.Copy(&medicineI, medicinePrescriptions[i].Medicine)
			err = copier.Copy(&medicineJ, medicinePrescriptions[j].Medicine)
			if err != nil {
				ctx.Logger().Error(err.Error())
				return false
			}
			return appKbv.SortByNormAndQuantity(medicineI, medicineJ, isAsc)
		})
	}

	if request.SortField == api.PrescribedBy {
		isAsc := true
		if order == -1 {
			isAsc = false
		}

		var doctorIds []uuid.UUID

		for _, medicine := range medicinePrescriptions {
			doctorIds = append(doctorIds, medicine.TreatmentDoctorId)
		}

		employeeProfileRes, err := m.employeeProfileService.GetEmployeeProfileByIds(ctx, &profile.GetByIdsRequest{
			OriginalIds: doctorIds,
		})

		if err != nil || len(employeeProfileRes.Profiles) == 0 {
			return nil, errors.WithStack(err)
		}

		sortPkg.Slice(medicinePrescriptions, func(i, j int) bool {
			profiles := slice.Map(employeeProfileRes.Profiles, func(p *profile.EmployeeProfileResponse) profile.EmployeeProfileResponse {
				return *p
			})

			employeeProfileResI := slice.FindOne(profiles, func(profile profile.EmployeeProfileResponse) bool {
				return *profile.Id == medicinePrescriptions[i].TreatmentDoctorId
			})

			if employeeProfileResI == nil {
				return false
			}

			employeeProfileResJ := slice.FindOne(profiles, func(profile profile.EmployeeProfileResponse) bool {
				return *profile.Id == medicinePrescriptions[j].TreatmentDoctorId
			})

			if employeeProfileResJ == nil {
				return false
			}

			return appKbv.SortByDoctorName(*employeeProfileResI, *employeeProfileResJ, isAsc)
		})
	}

	medicationPrescribeResponses := []api.MedicationPrescribeResponse{}
	for _, medicinePrescription := range medicinePrescriptions {
		var medicationPrescribeResponse api.MedicationPrescribeResponse
		if err := copier.Copy(&medicationPrescribeResponse, medicinePrescription.Medicine); err != nil {
			return nil, err
		}
		medicationPrescribeResponse.PrescribeDate = *medicinePrescription.PrescribeDate
		medicationPrescribeResponse.TreatmentDoctorId = medicinePrescription.TreatmentDoctorId
		medicationPrescribeResponse.PrintDate = medicinePrescription.PrintDate
		medicationPrescribeResponse.MedicationPlanId = medicinePrescription.MedicationPlanId
		medicationPrescribeResponse.ContractId = medicinePrescription.ContractId
		medicationPrescribeResponse.FormInfoId = medicinePrescription.FormInfoId
		medicationPrescribeResponse.AssignedToBsnrId = medicinePrescription.AssignedToBsnrId

		formInfoResponse := []api.MedicineShoppingBagInfo{}

		for _, medicine := range medicinePrescription.FormInfo.Medicines {
			formInfoResponse = append(formInfoResponse, api.MedicineShoppingBagInfo{
				Id:                       medicine.Id,
				Type:                     api.MedicineType(medicine.Type),
				Pzn:                      &medicine.Pzn,
				Name:                     medicine.Name,
				Quantity:                 medicine.Quantity,
				CurrentFormType:          api.FormType(medicine.CurrentFormType),
				FurtherInformation:       medicine.FurtherInformation,
				SubstitutionPrescription: medicine.SubstitutionPrescription,
				KBVMedicineId:            medicine.KBVMedicineId,
				IsEPrescription:          medicine.IsEPrescription,
				IsArtificialInsemination: medicine.IsArtificialInsemination,
				Vaccinate:                medicine.Vaccinate,
				DrugFormInformation:      medicine.DrugFormInformation,
			})
		}

		medicationPrescribeResponse.FormInfo = api.FormInfoResponse{
			Id:               medicinePrescription.FormInfo.Id,
			FormInfoResponse: formInfoResponse,
			FormSetting:      medicinePrescription.FormInfo.FormSetting,
			CurrentFormType:  api.FormType(medicinePrescription.FormInfo.CurrentFormType),
			PrescribeDate:    medicinePrescription.FormInfo.PrescribeDate,
			IsNotPicked:      false,
			IsShowFavHint:    medicinePrescription.FormInfo.IsShowFavHint,
		}
		medicationPrescribeResponse.IsFavourite = slice.Contains(pzns, medicinePrescription.Medicine.Pzn)
		medicationPrescribeResponses = append(medicationPrescribeResponses, medicationPrescribeResponse)
	}

	apiResponse := &api.GetMedicationPrescribeResponse{
		MedicationPrescribeResponses: medicationPrescribeResponses,
	}
	if request.PatientId != nil {
		apiResponse.PatientId = *request.PatientId
	}
	if request.Bsnr != nil {
		apiResponse.Bsnr = *request.Bsnr
	}
	return apiResponse, nil
}

// HandleEventShoppingBagChanged notification events create, delete, update shopping bag for client side
func (m *MedicineApp) HandleEventShoppingBagChanged(ctx *titan.Context, request api.EventShoppingBagRequest) error {
	return m.notifierSocket.NotifyCareProviderShoppingBagRequest(ctx, &request)
}

func (m *MedicineApp) HandleEventRefillMedicine(ctx *titan.Context, request api.EventRefillMedicine) error {
	return m.notifierSocket.NotifyCareProviderRefillMedicine(ctx, &request)
}

func (m *MedicineApp) HandleEventViewMedicationForm(ctx *titan.Context, request api.EventViewMedicationForm) error {
	return m.notifierSocket.NotifyCareProviderViewMedicationForm(ctx, &request)
}

func (m *MedicineApp) HandleEventMedicationPrescribeChanged(ctx *titan.Context, request api.EventMedicationPrescribe) error {
	return m.notifierSocket.NotifyCareProviderMedicationPrescribe(ctx, &request)
}

func (m *MedicineApp) HandleEventMedicationPlanChanged(ctx *titan.Context, request api.EventMedicationPlanChanged) error {
	return m.notifierSocket.NotifyCareProviderMedicationPlanChanged(ctx, &request)
}

// GetShoppingBag get shopping bag belong to patientid and doctor id
func (m *MedicineApp) GetShoppingBag(ctx *titan.Context, request api.GetShoppingBagRequest) (*api.GetShoppingBagRequestResponse, error) {
	filter := bson.M{
		shoppingbag.Field_PatientId: request.PatientId,
		shoppingbag.Field_DoctorId:  request.DoctorId,
		shoppingbag.Field_CreatedBy: ctx.UserInfo().UserUUID(),
	}
	if IsQueryBSNR(request.Bsnr) {
		filter = bson.M{
			shoppingbag.Field_Bsnr:      request.Bsnr,
			shoppingbag.Field_CreatedBy: ctx.UserInfo().UserUUID(),
		}
	}
	if request.ContractId != nil {
		filter[shoppingbag.Field_ContractId] = *request.ContractId
	}
	errMsgTemplate := ""
	if request.DoctorId != nil && request.PatientId != nil {
		errMsgTemplate = fmt.Sprintf("can not find shopping bag by DoctorId:%s and PatientId:%s", request.DoctorId.String(), request.PatientId.String())
	} else if request.Bsnr != nil {
		errMsgTemplate = fmt.Sprintf("can not find shopping bag by BSNR:%s", *request.Bsnr)
	}

	res, err := m.shoppingBagRepo.FindOne(ctx, filter)
	if err != nil {
		ctx.Logger().Error(errMsgTemplate)
		return nil, errors.WithMessage(err, errMsgTemplate)
	}

	if res == nil {
		if IsQueryBSNR(request.Bsnr) {
			return &api.GetShoppingBagRequestResponse{
				Bsnr:      *request.Bsnr,
				Medicines: []api.MedicineShoppingBagInfo{},
			}, nil
		}
		return &api.GetShoppingBagRequestResponse{
			DoctorId:          *request.DoctorId,
			PatientId:         *request.PatientId,
			TreatmentDoctorId: *request.DoctorId,
			Medicines:         []api.MedicineShoppingBagInfo{},
		}, nil
	}

	sortPkg.SliceStable(res.Medicines, func(i, j int) bool {
		mi, mj := res.Medicines[i], res.Medicines[j]
		return mi.CreatedDate > mj.CreatedDate
	})

	var apiRes api.GetShoppingBagRequestResponse
	err = copier.CopyWithOption(&apiRes, res, copier.Option{
		IgnoreEmpty: true,
	})
	if err != nil {
		ctx.Logger().Error(errMsgTemplate)
		return nil, errors.WithMessage(err, errMsgTemplate)
	}

	if IsQueryBSNR(request.Bsnr) {
		apiRes.Bsnr = *request.Bsnr
	} else {
		apiRes.DoctorId = *request.DoctorId
		apiRes.PatientId = *request.PatientId
		apiRes.TreatmentDoctorId = *res.TreatmentDoctorId
	}
	apiRes.ShoppingBagId = *res.Id

	if len(apiRes.Medicines) == 0 {
		apiRes.Medicines = []api.MedicineShoppingBagInfo{}
		return &apiRes, nil
	}
	for i, medicine := range apiRes.Medicines {
		apiRes.Medicines[i].PatientId = request.PatientId
		apiRes.Medicines[i].DoctorId = request.DoctorId

		for _, v := range res.Medicines {
			if v.Id.String() == medicine.Id.String() && v.PriceInformation != nil {
				apiRes.Medicines[i].PriceInformation.Discount = make(map[string]api.DiscountDetail)
				for k, v := range v.PriceInformation.Discount {
					apiRes.Medicines[i].PriceInformation.Discount[k] = api.DiscountDetail{
						AdditionalPaymentIndicator: v.AdditionalPaymentIndicator,
						DiscountFactor:             v.DiscountFactor,
						PreferAutIdem:              v.PreferAutIdem,
					}
				}
			}
		}
	}
	return &apiRes, nil
}

func getVpId(d *profile.EmployeeProfileResponse, contract *model.Contract) (string, error) {
	contractType := contract.GetContractType().Type()
	vpId := ""
	if contractType == nil {
		return "", errors.New("contract type is nil")
	}

	if *contractType == model.ContractType_HouseDoctorCare {
		if d.HavgVpId != nil {
			vpId = *d.HavgVpId
		}
	}

	if *contractType == model.ContractType_SpecialistCare {
		if d.MediverbundVpId != nil {
			vpId = *d.MediverbundVpId
		}
	}

	if vpId == "" {
		return "", errors.New("vp id is empty")
	}
	return vpId, nil
}

// showWarning checks missing diagnose in current quarter
func (m *MedicineApp) showWarning(ctx *titan.Context, request api.AddToShoppingBagRequest) (bool, error) {
	if ctx.UserInfo() == nil {
		return false, errors.New("current user info is nil")
	}
	currentDoctorID := ctx.UserInfo().UserId
	currentDoctorIDuuid, _ := uuid.Parse(currentDoctorID.String())
	userProfile, err := m.employeeProfileService.GetEmployeeProfileById(ctx, &profile.EmployeeProfileGetRequest{
		OriginalId: &currentDoctorIDuuid,
	})
	if err != nil {
		return false, errors.Wrapf(err, "can not get profile by id %s", currentDoctorIDuuid)
	}
	if request.ContractId == nil {
		return false, nil
	}
	contract := m.contractService.GetContractDetailById(*request.ContractId)
	if contract == nil {
		return false, nil
	}

	hasFunction, err := contract.HasFunction(model.ABRD785, util.NowUnixMillis(ctx), []string{*userProfile.Okv}, *request.IkNumber)
	if err != nil {
		errMsg := fmt.Sprintf("contract.HasFunction fail with okv: %s, ik: %d, func id: %s", model.VSST784, request.IkNumber, *userProfile.Okv)
		return false, errors.New(errMsg)
	}
	if !hasFunction {
		return false, nil
	}

	vpId, err := getVpId(userProfile, contract)
	if err != nil {
		return false, errors.Wrapf(err, "can not get vp id")
	}

	indicators, err := m.hpmRestService.LoadIndicatorByContractID(ctx, *request.ContractId, vpId)
	if err != nil {
		errMsg := fmt.Sprintf("can not load indicator for contract id %s,err: %v", *request.ContractId, err)
		return false, errors.New(errMsg)
	}

	if len(indicators) == 0 {
		return false, nil
	}

	var ikNumber string

	if request.IkNumber != nil {
		ikNumber = string(*request.IkNumber)
	}
	dateStr := util_pkg.Now(ctx).Format("2006-01-02")
	medicine, err := m.medicineKBV.FindByID(ctx, medicine_kbv.FindByIdRequest{
		Pzn:           *request.Medicine.Pzn,
		IkNumber:      &ikNumber,
		ReferenceDate: &dateStr,
		ContractId:    request.ContractId,
		IsSvPatient:   userProfile.HasHzvContracts,
	})

	if err != nil {
		errMsg := fmt.Sprintf("can not load medicine detail for pzn: %s, err: %v", *request.Medicine.Pzn, err)
		return false, errors.New(errMsg)
	}
	var medicineDiagnoreCodes []string
	for atc, diagnoreCodes := range indicators {
		if strings.HasPrefix(medicine.DrugInformation.ATC, atc) {
			medicineDiagnoreCodes = diagnoreCodes
			break
		}
	}

	if len(medicineDiagnoreCodes) == 0 {
		return false, nil
	}

	diagnoseTimelines, err := m.timelineService.GetDiagnosesByQuarter(ctx, *request.PatientId, util_pkg.NowQuarter(ctx), int32(util_pkg.Now(ctx).Year()))
	if err != nil {
		errMsg := fmt.Sprintf("GetDiagnosesByQuarter err: %s", err)
		return true, errors.New(errMsg)
	}
	if len(diagnoseTimelines) == 0 {
		return true, nil
	}
	foundDiagnose := slice.FindOne(diagnoseTimelines, func(d timeline_common.TimelineModel) bool {
		diagnose := d.EncounterDiagnoseTimeline
		for _, medicineDiagnose := range medicineDiagnoreCodes {
			if diagnose.Code == medicineDiagnose && diagnose.Certainty != nil && patientfile.Certainty(*diagnose.Certainty) == patientfile.G {
				return true
			}
		}
		return false
	})
	if foundDiagnose == nil {
		return true, nil
	}
	return true, nil
}

func (m *MedicineApp) addOrUpdateShoppingBag(ctx *titan.Context, request api.AddToShoppingBagRequest) (api.ShoppingBagEventType, error) {
	eventType := api.Add
	if request.Medicine.Pzn == nil {
		return eventType, nil
	}

	//	Case1: PatientId + DoctorId
	//	Case2: BsnrId
	filter := bson.M{
		shoppingbag.Field_CreatedBy: ctx.UserInfo().UserUUID(),
	}
	if request.PatientId != nil {
		filter[shoppingbag.Field_DoctorId] = request.DoctorId
		filter[shoppingbag.Field_PatientId] = *request.PatientId
	} else if request.Bsnr != nil {
		filter[shoppingbag.Field_Bsnr] = *request.Bsnr
	}
	if request.ContractId != nil {
		filter[shoppingbag.Field_ContractId] = *request.ContractId
	}

	shoppingBagItems, err := m.shoppingBagRepo.FindOne(ctx, filter)

	if err != nil {
		return "", errors.WithStack(err)
	}
	if shoppingBagItems != nil {
		for _, item := range shoppingBagItems.Medicines {
			if item != nil && item.Pzn == *request.Medicine.Pzn {
				eventType = api.Update
				break
			}
		}
	}

	return eventType, nil
}

// AddToShoppingBag add medicine item in to shopping bag
func (m *MedicineApp) AddToShoppingBag(ctx *titan.Context, request api.AddToShoppingBagRequest) (*api.ShoppingBagResponse, error) {
	var req medicineClient.AddToShoppingBagRequest
	err := copier.Copy(&req, request)
	if err != nil {
		ctx.Logger().Error(err.Error())
		return nil, err
	}

	if req.Medicine.PriceInformation != nil {
		req.Medicine.PriceInformation.Discount = make(map[string]medicine_common.DiscountDetail)
		for k, v := range request.Medicine.PriceInformation.Discount {
			req.Medicine.PriceInformation.Discount[k] = medicine_common.DiscountDetail{
				AdditionalPaymentIndicator: v.AdditionalPaymentIndicator,
				DiscountFactor:             v.DiscountFactor,
				PreferAutIdem:              v.PreferAutIdem,
			}
		}
	}

	eventType, err := m.addOrUpdateShoppingBag(ctx, request)
	if err != nil {
		return nil, err
	}

	res, err := m.medicineService.AddToShoppingBag(ctx, req)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	var resAPI api.ShoppingBagResponse
	err = copier.Copy(&resAPI, res)
	if err != nil {
		ctx.Logger().Error(err.Error())
		return nil, errors.WithStack(err)
	}

	// if res.Medicine.Type == medicine_common.KBV && util_pkg.IsStringPointerNotEmptyOrNil(request.Medicine.Pzn) {
	// 	if request.Medicine.ProductInformation != nil {
	// 		resAPI.Medicine.CurrentFormType = request.Medicine.ProductInformation.FormType
	// 	}
	// }
	err = m.notifierSocket.NotifyCareProviderShoppingBagRequest(ctx, &api.EventShoppingBagRequest{
		Type:              eventType,
		Payload:           resAPI.Medicine,
		ContractId:        req.ContractId,
		TreatmentDoctorId: req.DoctorId,
	})

	if err != nil {
		return nil, errors.WithStack(err)
	}

	return &resAPI, nil
}

// UpdateForm update from by ids
func (m *MedicineApp) UpdateForm(ctx *titan.Context, request api.UpdateFormRequest) error {
	var req medicineClient.UpdateFormRequest
	err := copier.Copy(&req, request)
	if err != nil {
		ctx.Logger().Error(err.Error())
		return err
	}

	err = m.medicineService.UpdateForm(ctx, req)
	if err != nil {
		errMsg := fmt.Sprintf("can not update medicine form %s", err.Error())
		ctx.Logger().Error(errMsg)
		return errors.WithStack(err)
	}

	for _, medicineID := range request.MedicineIDs {
		err = m.notifierSocket.NotifyCareProviderShoppingBagRequest(ctx, &api.EventShoppingBagRequest{
			Type: api.UpdateForm,
			Payload: api.MedicineShoppingBagInfo{
				Id:              &medicineID,
				CurrentFormType: request.CurrentFormType,
				DoctorId:        &request.DoctorId,
				PatientId:       &request.PatientId,
			},
			ContractId: request.ContractId,
		})
		if err != nil {
			errMsg := fmt.Sprintf("can not send event update form %s", err.Error())
			ctx.Logger().Error(errMsg)
			return errors.WithStack(err)
		}
	}

	return nil
}

func (m *MedicineApp) UpdateBSNRShoppingBagInformation(ctx *titan.Context, request api.UpdateShoppingBagInformationRequest) (*api.ShoppingBagResponse, error) {
	var updateRequest medicineClient.UpdateBSNRShoppingBagInformationRequest
	if err := copier.Copy(&updateRequest, request); err != nil {
		return nil, errors.Wrap(err, constant.MessageMappingError)
	}

	res, err := m.medicineService.UpdateBSNRShoppingBagInformation(ctx, updateRequest)
	if err != nil {
		return nil, errors.WithMessage(err, "can not update shopping bag")
	}
	var shoppingBagInfo api.MedicineShoppingBagInfo
	if err := copier.Copy(&shoppingBagInfo, res.Medicine); err != nil {
		return nil, errors.WithMessage(err, constant.MessageMappingError)
	}

	eventPayload := &api.EventShoppingBagRequest{
		Type:       api.Update,
		Payload:    shoppingBagInfo,
		ContractId: request.ContractId,
	}
	eventPayload.Payload.Bsnr = request.Bsnr
	if request.TreatmentDoctorId != nil {
		eventPayload.Type = api.UpdateDoctorId
		eventPayload.TreatmentDoctorId = request.TreatmentDoctorId
		eventPayload.AssignedToBsnrId = request.AssignedToBsnrId
	}
	if request.Vaccinate != nil {
		eventPayload.Payload.Vaccinate = res.Medicine.Vaccinate
	}
	err = m.notifierSocket.NotifyCareProviderShoppingBagRequest(ctx, eventPayload)
	if err != nil {
		return nil, errors.Wrap(err, "can not submit event update shopping bag")
	}
	return &api.ShoppingBagResponse{
		Medicine: shoppingBagInfo,
	}, nil
}

func (m *MedicineApp) UpdatePatientShoppingBagInformation(ctx *titan.Context, request api.UpdateShoppingBagInformationRequest) (*api.ShoppingBagResponse, error) {
	var updateRequest medicineClient.UpdateShoppingBagInformationRequest
	if err := copier.Copy(&updateRequest, request); err != nil {
		return nil, errors.Wrap(err, constant.MessageMappingError)
	}
	haveUpdateInfo := request.FurtherInformation != nil ||
		request.IntakeInterval != nil ||
		request.PackageSizeRequest != nil ||
		request.TreatmentDoctorId != nil ||
		request.AsNeeded != nil ||
		request.FreeText != nil ||
		request.SubstitutionPrescription != nil ||
		len(request.SpecialExceedings) > 0 ||
		request.IsEPrescription != nil ||
		request.IsArtificialInsemination != nil ||
		request.DrugFormInformation != nil

	if !haveUpdateInfo {
		return nil, errors.New("update info should have value")
	}

	res, err := m.medicineService.UpdateShoppingBagInformation(ctx, updateRequest)
	if err != nil {
		return nil, errors.WithMessage(err, "can not update shopping bag")
	}
	var shoppingBagInfo api.MedicineShoppingBagInfo
	if err := copier.Copy(&shoppingBagInfo, res.Medicine); err != nil {
		return nil, errors.WithMessage(err, constant.MessageMappingError)
	}

	eventPayload := &api.EventShoppingBagRequest{
		Type:       api.Update,
		Payload:    shoppingBagInfo,
		ContractId: request.ContractId,
	}
	if request.TreatmentDoctorId != nil {
		eventPayload.Type = api.UpdateDoctorId
		eventPayload.TreatmentDoctorId = request.TreatmentDoctorId
		eventPayload.Payload.DoctorId = request.DoctorId
		eventPayload.Payload.PatientId = request.PatientId
		eventPayload.AssignedToBsnrId = request.AssignedToBsnrId
	}

	err = m.notifierSocket.NotifyCareProviderShoppingBagRequest(ctx, eventPayload)
	if err != nil {
		return nil, errors.Wrap(err, "can not submit event update shopping bag")
	}
	return &api.ShoppingBagResponse{
		Medicine: shoppingBagInfo,
	}, nil
}

func (m *MedicineApp) UpdateShoppingBagInformation(ctx *titan.Context, request api.UpdateShoppingBagInformationRequest) (*api.ShoppingBagResponse, error) {
	if IsQueryBSNR(request.Bsnr) {
		return m.UpdateBSNRShoppingBagInformation(ctx, request)
	} else if request.PatientId != nil && request.DoctorId != nil {
		return m.UpdatePatientShoppingBagInformation(ctx, request)
	}
	return nil, nil
}

// UpdateShoppingBagQuantity update quantity for shopping bag item
func (m *MedicineApp) UpdateShoppingBagQuantity(ctx *titan.Context, request api.UpdateShoppingBagQuantityRequest) (*api.ShoppingBagResponse, error) {
	var req medicineClient.UpdateShoppingBagQuantityRequest
	err := copier.Copy(&req, request)
	if err != nil {
		ctx.Logger().Error(err.Error())
		return nil, errors.WithMessage(err, "can not Update Shopping Bag Quantity")
	}

	res, err := m.medicineService.UpdateShoppingBagQuantity(ctx, req)
	if err != nil {
		ctx.Logger().Error(err.Error())
		return nil, errors.WithMessage(err, "can not update shopping bag")
	}

	var apiResponse api.ShoppingBagResponse
	err = copier.Copy(&apiResponse, res)
	if err != nil {
		ctx.Logger().Error(err.Error())
		return nil, errors.WithStack(err)
	}
	eventPayload := &api.EventShoppingBagRequest{
		Type:       api.Update,
		Payload:    apiResponse.Medicine,
		ContractId: req.ContractId,
	}
	eventPayload.Payload.Bsnr = request.Bsnr
	// eventPayload.Payload.Vaccinate = request.Vaccinate
	err = m.notifierSocket.NotifyCareProviderShoppingBagRequest(ctx, eventPayload)

	if err != nil {
		return nil, errors.Wrap(err, "can not submit event update shopping bag")
	}

	return &apiResponse, nil
}

func (m *MedicineApp) RemoveFromBSNRShoppingBag(ctx *titan.Context, request api.RemoveFromShoppingBagRequest) (*api.ShoppingBagResponse, error) {
	var req medicineClient.RemoveFromBSNRShoppingBagRequest
	err := copier.Copy(&req, request)
	if err != nil {
		ctx.Logger().Error(err.Error())
		return nil, errors.WithStack(err)
	}

	_, err = m.medicineService.RemoveFromBSNRShoppingBag(ctx, req)
	if err != nil {
		ctx.Logger().Error(err.Error())
		return nil, errors.Append(err, errors.New("can not remove shopping bag"))
	}
	eventDeleteRequest := &api.EventShoppingBagRequest{
		Type: api.Delete,
		Payload: api.MedicineShoppingBagInfo{
			Id:   request.MedicineId,
			Bsnr: request.Bsnr,
		},
		ContractId: request.ContractId,
	}
	if request.ShoppingBagId != nil {
		eventDeleteRequest.Type = api.DeleteShoppingBag
		eventDeleteRequest.Payload.Id = request.ShoppingBagId
	}
	err = m.notifierSocket.NotifyCareProviderShoppingBagRequest(ctx, eventDeleteRequest)

	if err != nil {
		return nil, err
	}

	return &api.ShoppingBagResponse{
		Medicine: api.MedicineShoppingBagInfo{
			Id:   request.MedicineId,
			Bsnr: request.Bsnr,
		},
	}, nil
}

func (m *MedicineApp) RemoveFromPatientShoppingBag(ctx *titan.Context, request api.RemoveFromShoppingBagRequest) (*api.ShoppingBagResponse, error) {
	var req medicineClient.RemoveFromShoppingBagRequest
	err := copier.Copy(&req, request)
	if err != nil {
		ctx.Logger().Error(err.Error())
		return nil, errors.WithStack(err)
	}

	_, err = m.medicineService.RemoveFromShoppingBag(ctx, req)
	if err != nil {
		ctx.Logger().Error(err.Error())
		return nil, errors.Append(err, errors.New("can not remove shopping bag"))
	}
	eventDeleteRequest := &api.EventShoppingBagRequest{
		Type: api.Delete,
		Payload: api.MedicineShoppingBagInfo{
			Id:        request.MedicineId,
			PatientId: request.PatientId,
			DoctorId:  request.DoctorId,
		},
		ContractId: request.ContractId,
	}
	if request.ShoppingBagId != nil {
		eventDeleteRequest.Type = api.DeleteShoppingBag
		eventDeleteRequest.Payload.Id = request.ShoppingBagId
	}
	err = m.notifierSocket.NotifyCareProviderShoppingBagRequest(ctx, eventDeleteRequest)

	if err != nil {
		return nil, err
	}

	return &api.ShoppingBagResponse{
		Medicine: api.MedicineShoppingBagInfo{
			Id: request.MedicineId,
		},
	}, nil
}

// RemoveFromShoppingBag remove shopping bag by medicine id
func (m *MedicineApp) RemoveFromShoppingBag(ctx *titan.Context, request api.RemoveFromShoppingBagRequest) (*api.ShoppingBagResponse, error) {
	if IsQueryBSNR(request.Bsnr) {
		return m.RemoveFromBSNRShoppingBag(ctx, request)
	} else if request.DoctorId != nil && request.PatientId != nil {
		return m.RemoveFromPatientShoppingBag(ctx, request)
	}
	return nil, nil
}

func (m *MedicineApp) OnTimelineHardDelete(ctx *titan.Context, request timeline.EventTimelineHardRemove) error {
	return m.medicinePrescriptionRepo.OnTimelineHardDelete(ctx, request.TimelineId)
}
