package catalog_uv_goa_test

import (
	"testing"
	"time"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_uv_goa"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/app"
	catalog_uv_goa_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/catalog_uv_goa"
	ares_test "git.tutum.dev/medi/tutum/ares/pkg/test"
	"git.tutum.dev/medi/tutum/ares/pkg/test/data"
	"git.tutum.dev/medi/tutum/ares/pkg/test/fakeit"
	"git.tutum.dev/medi/tutum/ares/pkg/test/fixtures"
	"git.tutum.dev/medi/tutum/ares/pkg/test/repository"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_utils_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_uv_goa_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	fixtures2 "gitlab.com/silenteer-oss/hestia/test/fixtures"
	"gitlab.com/silenteer-oss/titan"
)

type CatalogUvGoaTestSuite struct {
	ares_test.WithDefaultInfraTestSuite
	db         *repository.Repos
	mvzFixture *fixtures2.CareProviderFixture
	doctor     *data.Doctor
	catalogGoa catalog_uv_goa.UvGoaCatalogApp
}

func TestCatalogUvGoaTestSuite(t *testing.T) {
	natsConfig, natsServer, _ := ares_test.NewInfraForTesting()
	patient := *fixtures.Patient_MariaMühlenberg
	patientFixture := &fixtures.PatientFixture{
		Patients: []*data.Patient{&patient},
	}
	mvzFixture := fixtures.MvzFixture_521111100.With(patientFixture).ToCareProviderFixture()

	config := config.MvzAppConfigMod.Resolve()
	if config == nil {
		panic("config is nil")
	}

	testServers := ares_test.NewTestServersWithDb(
		false,
		natsConfig,
		app.NewServer(
			*config,
		),
	)
	app := catalog_uv_goa_app.UvGoaCatalogAppMod.Resolve()
	s := &CatalogUvGoaTestSuite{
		db:         repository.DefaultRepos,
		mvzFixture: mvzFixture,
		doctor:     fixtures.Doctor_CatharineRobel,
		catalogGoa: app,
	}
	s.SetNatsServer(natsServer).SetTestServers(testServers).
		SetEnableSocketServer(true, natsConfig).
		SetFixtures(mvzFixture)
	suite.Run(t, s)
}

func (s *CatalogUvGoaTestSuite) Test_UVGOA_CreateUvGoaCatalog_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	t.Run("CreateUvGoaCatalog_Success", func(t *testing.T) {
		fakeUvGoa := generateCreateUvGoaCatalogRequest(ctx).UvGoa
		fakeUvGoa.Code = "12345678"
		uvGoaCatalog, err := s.catalogGoa.CreateUvGoaCatalog(ctx, &catalog_uv_goa.CreateUvGoaCatalogRequest{
			UvGoa: fakeUvGoa,
		})
		require.Nil(t, err)
		require.NotNil(t, uvGoaCatalog)
		require.NotNil(t, uvGoaCatalog.UvGoa)

		uvGoa := uvGoaCatalog.UvGoa
		require.Equal(t, fakeUvGoa.Code, uvGoa.Code)
		require.Equal(t, fakeUvGoa.Description, uvGoa.Description)
		require.Equal(t, fakeUvGoa.LongDescription, uvGoa.LongDescription)
		require.Equal(t, catalog_utils_common.SelfCreated, uvGoa.Source)

		fromDate := util.RoundTimeToDateOnly(util.GetPointerValue(fakeUvGoa.Validity.FromDate), ctx.RequestTimeZone())
		require.Equal(t, fromDate, *uvGoa.Validity.FromDate)
		require.Equal(t, *fakeUvGoa.Validity.ToDate, *uvGoa.Validity.ToDate)
	})

	t.Run("CreateUvGoaCatalog_Success_With_Billable", func(t *testing.T) {
		fakeUvGoa := generateCreateUvGoaCatalogRequest(ctx).UvGoa
		IsNotBillable := true
		fakeUvGoa.IsNotBillable = util.NewPointer(IsNotBillable)
		uvGoaCatalog, err := s.catalogGoa.CreateUvGoaCatalog(ctx, &catalog_uv_goa.CreateUvGoaCatalogRequest{
			UvGoa: fakeUvGoa,
		})
		require.Nil(t, err)
		require.NotNil(t, uvGoaCatalog)
		require.NotNil(t, uvGoaCatalog.UvGoa)

		uvGoa := uvGoaCatalog.UvGoa
		require.Equal(t, fakeUvGoa.Code, uvGoa.Code)
		require.Equal(t, IsNotBillable, util.GetPointerValue(uvGoa.IsNotBillable))
	})
}

func (s *CatalogUvGoaTestSuite) Test_UVGOA_GetUvGoaCatalogs_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	uvGoaCatalog, err := s.catalogGoa.GetUvGoaCatalogs(ctx, &catalog_uv_goa.GetUvGoaCatalogsRequest{
		IsOnlySelfCreated: false,
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 10,
		},
	})
	require.Nil(t, err)
	require.NotEmpty(t, uvGoaCatalog.Items)
}

func (s *CatalogUvGoaTestSuite) Test_UVGOA_GetUvGoaCatalogs_Sort_ASC_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	uvGoaCatalog, err := s.catalogGoa.GetUvGoaCatalogs(ctx, &catalog_uv_goa.GetUvGoaCatalogsRequest{
		IsOnlySelfCreated: false,
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 10,
			SortBy:   "code",
			Order:    "DESC",
		},
	})

	require.Nil(t, err)
	require.NotEmpty(t, uvGoaCatalog.Items)
	require.Len(t, uvGoaCatalog.Items, 10)
}

func (s *CatalogUvGoaTestSuite) Test_UVGOA_GetUvGoaCatalogs_Sort_DESC_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	uvGoaCatalog, err := s.catalogGoa.GetUvGoaCatalogs(ctx, &catalog_uv_goa.GetUvGoaCatalogsRequest{
		IsOnlySelfCreated: false,
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 10,
			SortBy:   "code",
			Order:    "ASC",
		},
	})

	require.Nil(t, err)
	require.NotEmpty(t, uvGoaCatalog.Items)

	firstItem := uvGoaCatalog.Items[0]
	secondItem := uvGoaCatalog.Items[1]
	require.Less(t, firstItem.Code, secondItem.Code)
}

func (s *CatalogUvGoaTestSuite) Test_UVGOA_GetUvGoaCatalogs_Search_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	// Create a catalog with specific description
	payload := generateCreateUvGoaCatalogRequest(ctx)
	payload.UvGoa.Description = "unique_description_for_search"
	createdCatalog, err := s.catalogGoa.CreateUvGoaCatalog(ctx, payload)
	require.Nil(t, err)
	require.NotNil(t, createdCatalog)

	// Search for it
	response, err := s.catalogGoa.GetUvGoaCatalogs(ctx, &catalog_uv_goa.GetUvGoaCatalogsRequest{
		Value: "unique_description_for_search",
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 10,
		},
	})
	require.Nil(t, err)
	require.NotEmpty(t, response.Items)

	// Verify the created item is in the search results
	found := false
	for _, item := range response.Items {
		if item.Description == "unique_description_for_search" {
			found = true
			break
		}
	}
	require.True(t, found, "Created catalog should be found in search results")
}

func (s *CatalogUvGoaTestSuite) Test_UVGOA_IsValidUpdateUvGoa_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	// Create a catalog first
	created, err := s.catalogGoa.CreateUvGoaCatalog(ctx, generateCreateUvGoaCatalogRequest(ctx))
	require.Nil(t, err)
	require.NotNil(t, created)

	// Validate update with valid changes
	validResponse, err := s.catalogGoa.IsValidUpdateUvGoa(ctx, &catalog_uv_goa.UpdateUvGoaCatalogRequest{
		UvGoa: created.UvGoa,
	})
	require.Nil(t, err)
	require.Len(t, validResponse.Errors, 0)
}

func (s *CatalogUvGoaTestSuite) Test_UVGOA_IsValidUpdateUvGoa_Fail_DuplicateCode() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	// Create first catalog
	first, err := s.catalogGoa.CreateUvGoaCatalog(ctx, generateCreateUvGoaCatalogRequest(ctx))
	require.Nil(t, err)
	require.NotNil(t, first)

	// Create second catalog
	second, err := s.catalogGoa.CreateUvGoaCatalog(ctx, generateCreateUvGoaCatalogRequest(ctx))
	require.Nil(t, err)
	require.NotNil(t, second)

	// Try to update second catalog with first catalog's code
	second.UvGoa.Code = first.UvGoa.Code
	validResponse, err := s.catalogGoa.IsValidUpdateUvGoa(ctx, &catalog_uv_goa.UpdateUvGoaCatalogRequest{
		UvGoa: second.UvGoa,
	})

	require.Nil(t, err)
	require.NotNil(t, validResponse.Errors)
}

func (s *CatalogUvGoaTestSuite) Test_UVGOA_UpdateUvGoaCatalog_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	// Create catalog first
	created, err := s.catalogGoa.CreateUvGoaCatalog(ctx, generateCreateUvGoaCatalogRequest(ctx))
	require.Nil(t, err)
	require.NotNil(t, created)

	// Update the catalog
	created.UvGoa.Description = "Updated Description"
	updated, err := s.catalogGoa.UpdateUvGoaCatalog(ctx, &catalog_uv_goa.UpdateUvGoaCatalogRequest{
		UvGoa: created.UvGoa,
	})
	require.Nil(t, err)
	require.NotNil(t, updated)
	require.Equal(t, "Updated Description", updated.UvGoa.Description)
	require.Equal(t, created.UvGoa.UvGoaId, updated.UvGoa.UvGoaId)
	require.Equal(t, created.UvGoa.Code, updated.UvGoa.Code)
	require.Equal(t, created.UvGoa.Source, updated.UvGoa.Source)
	require.Equal(t, created.UvGoa.Validity, updated.UvGoa.Validity)
}

func (s *CatalogUvGoaTestSuite) Test_UVGOA_UpdateUvGoaMasterdata_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	goaMasterdata, err := s.catalogGoa.GetUvGoaCatalogByCode(ctx, &catalog_uv_goa.GetUvGoaCatalogByCodeRequest{
		Number: "1", // masterdata code
	})
	require.Nil(t, err)
	require.NotNil(t, goaMasterdata)
	require.NotEmpty(t, goaMasterdata.UvGoa)
	require.Equal(t, catalog_utils_common.XmlFile, goaMasterdata.UvGoa.Source)

	goaMasterdata.UvGoa.Description = "Updated Description"
	updated, err := s.catalogGoa.UpdateUvGoaCatalog(ctx, &catalog_uv_goa.UpdateUvGoaCatalogRequest{
		UvGoa: goaMasterdata.UvGoa,
	})
	require.Nil(t, err)
	require.NotNil(t, updated)
	require.Equal(t, "Updated Description", updated.UvGoa.Description)
	require.NotEqual(t, goaMasterdata.UvGoa.UvGoaId, updated.UvGoa.UvGoaId)
	require.Equal(t, goaMasterdata.UvGoa.Code, updated.UvGoa.Code)
	require.Equal(t, catalog_utils_common.XmlFileWithOverrideData, updated.UvGoa.Source)
	fromDate := util.RoundTimeToDateOnly(util.GetPointerValue(goaMasterdata.UvGoa.Validity.FromDate), ctx.RequestTimeZone())
	require.Equal(t, fromDate, *updated.UvGoa.Validity.FromDate)
	require.Nil(t, updated.UvGoa.Validity.ToDate)
}

func (s *CatalogUvGoaTestSuite) Test_UVGOA_DeleteUvGoaCatalog_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	// Create catalog first
	created, err := s.catalogGoa.CreateUvGoaCatalog(ctx, generateCreateUvGoaCatalogRequest(ctx))
	require.Nil(t, err)
	require.NotNil(t, created)

	// Delete the catalog
	err = s.catalogGoa.DeleteUvGoaCatalog(ctx, &catalog_uv_goa.DeleteUvGoaCatalogRequest{
		Id: created.UvGoa.UvGoaId,
	})
	require.Nil(t, err)

	// Verify it's deleted by trying to get it
	catalogs, err := s.catalogGoa.GetUvGoaCatalogs(ctx, &catalog_uv_goa.GetUvGoaCatalogsRequest{
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 100,
		},
	})
	require.Nil(t, err)

	found := false
	for _, item := range catalogs.Items {
		if item.UvGoaId == created.UvGoa.UvGoaId {
			found = true
			break
		}
	}
	require.False(t, found, "Deleted catalog should not be found")
}

func (s *CatalogUvGoaTestSuite) Test_UVGOA_SearchUvGoaItems_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	// Create a catalog with specific search term
	payload := generateCreateUvGoaCatalogRequest(ctx)
	payload.UvGoa.Description = "specific_search_term"
	created, err := s.catalogGoa.CreateUvGoaCatalog(ctx, payload)
	require.Nil(t, err)
	require.NotNil(t, created)

	// Search for it
	searchResponse, err := s.catalogGoa.SearchUvGoaItems(ctx, &catalog_uv_goa.SearchUvGoaRequest{
		Value: "specific_search_term",
	})
	require.Nil(t, err)
	require.NotNil(t, searchResponse)
	require.NotEmpty(t, searchResponse.Items)

	found := false
	for _, item := range searchResponse.Items {
		if item.Description == "specific_search_term" {
			found = true
			break
		}
	}
	require.True(t, found, "Created catalog should be found in search results")
}

func (s *CatalogUvGoaTestSuite) Test_UVGOA_GetUvGoaCatalogByCode_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	// Create a catalog with specific code
	payload := generateCreateUvGoaCatalogRequest(ctx)
	created, err := s.catalogGoa.CreateUvGoaCatalog(ctx, payload)
	require.Nil(t, err)
	require.NotNil(t, created)

	// Get by code
	foundCatalog, err := s.catalogGoa.GetUvGoaCatalogByCode(ctx, &catalog_uv_goa.GetUvGoaCatalogByCodeRequest{
		Number: created.UvGoa.Code,
	})
	require.Nil(t, err)
	require.NotNil(t, foundCatalog)
	require.Equal(t, created.UvGoa.Code, foundCatalog.UvGoa.Code)
}

func (s *CatalogUvGoaTestSuite) Test_UVGOA_GetUvGoaCatalogByCode_NotFound() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	foundCatalog, err := s.catalogGoa.GetUvGoaCatalogByCode(ctx, &catalog_uv_goa.GetUvGoaCatalogByCodeRequest{
		Number: "empty",
	})
	require.Nil(t, err)
	require.Nil(t, foundCatalog)
}

func (s *CatalogUvGoaTestSuite) Test_UVGOA_GetUvGoaCatalogs_Pagination() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	// Get first page with page size 2
	firstPageResponse, err := s.catalogGoa.GetUvGoaCatalogs(ctx, &catalog_uv_goa.GetUvGoaCatalogsRequest{
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 30,
		},
	})
	require.Nil(t, err)
	require.NotNil(t, firstPageResponse)
	require.Len(t, firstPageResponse.Items, 30)

	// Get second page
	secondPageResponse, err := s.catalogGoa.GetUvGoaCatalogs(ctx, &catalog_uv_goa.GetUvGoaCatalogsRequest{
		Pagination: &common.PaginationRequest{
			Page:     2,
			PageSize: 30,
		},
	})
	require.Nil(t, err)
	require.NotNil(t, secondPageResponse)
	require.Len(t, secondPageResponse.Items, 30)
	require.Equal(t, firstPageResponse.Total, secondPageResponse.Total)
}

func (s *CatalogUvGoaTestSuite) Test_UVGOA_GetUvGoaCatalogs_OnlySelfCreated() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	// Create a catalog
	payload := generateCreateUvGoaCatalogRequest(ctx)
	payload.UvGoa.Source = catalog_utils_common.SelfCreated
	created, err := s.catalogGoa.CreateUvGoaCatalog(ctx, payload)
	require.Nil(t, err)
	require.NotNil(t, created)

	// Get only self-created catalogs
	response, err := s.catalogGoa.GetUvGoaCatalogs(ctx, &catalog_uv_goa.GetUvGoaCatalogsRequest{
		IsOnlySelfCreated: true,
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 100,
		},
	})
	require.Nil(t, err)
	require.NotEmpty(t, response.Items)

	// Verify all items are self-created
	for _, item := range response.Items {
		require.Equal(t, catalog_utils_common.SelfCreated, item.Source)
	}

	// Verify the created catalog is in the results
	found := false
	for _, item := range response.Items {
		if item.UvGoaId == created.UvGoa.UvGoaId {
			found = true
			break
		}
	}
	require.True(t, found, "Self-created catalog should be in the results")
}

// Helper function to generate a create UV GOA catalog request
func generateCreateUvGoaCatalogRequest(ctx *titan.Context) *catalog_uv_goa.CreateUvGoaCatalogRequest {
	return &catalog_uv_goa.CreateUvGoaCatalogRequest{
		UvGoa: &catalog_uv_goa_common.UvGoaCatalog{
			Code:            util.RandomNumericString(8),
			Description:     fakeit.RandomString(10),
			LongDescription: fakeit.RandomString(10),
			Validity: catalog_utils_common.Validity{
				FromDate: util.NewPointer(util.Now(ctx).UnixMilli()),
				ToDate:   util.NewPointer(util.Now(ctx).Add(time.Hour * 24 * 365).UnixMilli()),
			},
		},
	}
}
