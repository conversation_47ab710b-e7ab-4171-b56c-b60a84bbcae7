package patient_combination_test

import (
	"testing"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_combination"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/app"
	"git.tutum.dev/medi/tutum/ares/app/mvz/patient_profile"
	ares_test "git.tutum.dev/medi/tutum/ares/pkg/test"
	"git.tutum.dev/medi/tutum/ares/pkg/test/data"
	"git.tutum.dev/medi/tutum/ares/pkg/test/fixtures"
	"git.tutum.dev/medi/tutum/ares/pkg/test/repository"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	fixtures2 "gitlab.com/silenteer-oss/hestia/test/fixtures"
)

type PatientCombinationTestSuite struct {
	ares_test.WithDefaultInfraTestSuite
	db                    *repository.Repos
	patientCombinationApp patient_combination.PatientCombinationAppClient
	mvzFixture            *fixtures2.CareProviderFixture
	doctor                *data.Doctor
	patient1              *data.Patient
	patient2              *data.Patient
	patientProfileRepo    *patient_profile.PatientProfileRepository
}

func TestPatientCombinationTestSuite(t *testing.T) {
	natsConfig, natsServer, defaultClient := ares_test.NewInfraForTesting()

	patient1Id := uuid.New()
	patient1 := *fixtures.Patient_MariaMühlenberg
	patient1.PatientId = patient1Id
	patient1.Profile.Id = &patient1Id

	patient2Id := uuid.New()
	patient2 := *fixtures.Patient_MariaMühlenberg
	patient2.PatientId = patient2Id
	patient2.Profile.Id = &patient2Id

	patientFixture := &fixtures.PatientFixture{
		Patients: []*data.Patient{&patient1, &patient2},
	}
	mvzFixture := fixtures.MvzFixture_521111100.With(patientFixture).ToCareProviderFixture()

	testServers := ares_test.NewTestServersWithDb(
		false,
		natsConfig,
		app.NewServer(
			*config.MvzAppConfigMod.Resolve(),
		),
	)

	app := patient_combination.NewPatientCombinationAppClient(defaultClient)
	s := &PatientCombinationTestSuite{
		db:                    repository.DefaultRepos,
		patientCombinationApp: *app,
		mvzFixture:            mvzFixture,
		doctor:                fixtures.Doctor_CatharineRobel,
		patientProfileRepo:    patient_profile.PatientProfileRepositoryMod.Resolve(),
		patient1:              &patient1,
		patient2:              &patient2,
	}
	s.SetNatsServer(natsServer).SetTestServers(testServers).
		SetEnableSocketServer(true, natsConfig).SetFixtures(mvzFixture)
	suite.Run(t, s)
}

func (s *PatientCombinationTestSuite) Test_CombinePatients() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	firstName := s.patient1.Profile.FirstName
	lastName := s.patient1.Profile.LastName
	dateOfBirth := s.patient1.Profile.PatientInfo.PersonalInfo.DateOfBirth
	patients, err := s.patientProfileRepo.GetPatientByNameAndDOB(ctx, firstName, lastName, dateOfBirth)
	require.Nil(t, err)
	require.NotNil(t, patients)
	require.Len(t, patients, 2)

	err = s.patientCombinationApp.CombinePatients(ctx, patient_combination.CombinePatientsRequest{
		TargetPatientId:      s.patient1.PatientId,
		DuplicatedPatientIds: []uuid.UUID{s.patient2.PatientId},
	})
	require.Nil(t, err)

	patient, err := s.patientProfileRepo.GetPatientByNameAndDOB(ctx, firstName, lastName, dateOfBirth)
	require.Nil(t, err)
	require.NotNil(t, patient)
	require.Len(t, patient, 1)
}
