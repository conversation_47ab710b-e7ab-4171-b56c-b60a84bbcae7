package document_management_test

import (
	"testing"
	"time"

	_ "embed"

	dm_app "git.tutum.dev/medi/tutum/ares/app/companion/dm_app"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/document_management"
	document_management_app "git.tutum.dev/medi/tutum/ares/app/mvz/internal/document_management"
	"git.tutum.dev/medi/tutum/ares/app/mvz/patient_profile"
	ares_test "git.tutum.dev/medi/tutum/ares/pkg/test"
	"git.tutum.dev/medi/tutum/ares/pkg/test/data"
	pkg_fixtures "git.tutum.dev/medi/tutum/ares/pkg/test/fixtures"
	test_fixtures "git.tutum.dev/medi/tutum/ares/pkg/test/fixtures"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	document_management_common "git.tutum.dev/medi/tutum/ares/service/domains/api/document_management/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/document_setting_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	profile_service "git.tutum.dev/medi/tutum/ares/service/domains/api/profile"
	"git.tutum.dev/medi/tutum/ares/service/domains/companion_modules"
	document_setting_state_repo "git.tutum.dev/medi/tutum/ares/service/domains/document_management/repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/document_management/service"
	"git.tutum.dev/medi/tutum/ares/service/domains/document_setting/repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	document_management_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/document_management"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	"git.tutum.dev/medi/tutum/ares/share"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"github.com/submodule-org/submodule.go/v2"
	"gitlab.com/silenteer-oss/hestia/test/fixtures"
	"gitlab.com/silenteer-oss/titan"
)

var (
	//go:embed data/S01TESTJP_V1.LDT
	ldtOrder1Content string
	//go:embed data/S01TESTJP_V2.LDT
	ldtOrder2Content string
	//go:embed data/S01TESTJP_E12.LDT
	ldtResultContent string
	//go:embed data/order.LDT
	ldtOrderContent string
)

type DocumentManagementApp interface {
	document_management.DocumentManagementApp
}

type DocumentManagementTestSuite struct {
	ares_test.WithDefaultInfraTestSuite
	mvzFixture                     *fixtures.CareProviderFixture
	documentManagementApp          document_management.DocumentManagementApp
	doctor                         *data.Doctor
	documentSetting                *repo.DocumentSettingEntity
	documentManagementRepo         *document_management_repo.DocumentManagementRepo
	patientId                      uuid.UUID
	patients                       []*data.Patient
	timelineDocumentManagementRepo timeline_repo.TimelineEntityRepo[patient_encounter.EncounterDocumentManagement]
	timelineLdtRepo                timeline_repo.TimelineEntityRepo[patient_encounter.EncounterLDT]
	mockApps                       *ares_test.MockApp
	labResultRepo                  *document_setting_state_repo.LabResultRepo
	patientProfileRepo             *patient_profile.PatientProfileRepository
	labParamResultRepo             *document_setting_state_repo.LabParamsRepo
}

func (d *DocumentManagementTestSuite) createMockBuilderWithLdtContent(ldtContent, filePath string, documentSetting *repo.DocumentSettingEntity, patient1, patient2 data.Patient, handleDmEventFunc *func(ctx *titan.Context, request dm_app.EventMessage) error) *ares_test.MockApp {
	patient1.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = util.NewPointer("A123456788")
	patient2.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = util.NewPointer("A123456789")

	return ares_test.
		NewMockBuilder().
		WithDocumentManagementMocks(ares_test.DocumentManagementMockConfig{
			LdtContent:        ldtContent,
			DocumentSetting:   documentSetting,
			FilePath:          filePath,
			HandleDmEventFunc: handleDmEventFunc,
		}).
		WithPatientProfileMocks(
			ares_test.PatientProfileMockConfig{
				PatientProfile: &profile.PatientProfile{
					Id:          &patient1.PatientId,
					FirstName:   patient1.Profile.FirstName,
					LastName:    patient1.Profile.LastName,
					PatientInfo: patient1.Profile.PatientInfo,
				},
			},
		).
		WithPatientProfileMocks(
			ares_test.PatientProfileMockConfig{
				PatientProfile: &profile.PatientProfile{
					Id:          &patient2.PatientId,
					FirstName:   patient2.Profile.FirstName,
					LastName:    patient2.Profile.LastName,
					PatientInfo: patient2.Profile.PatientInfo,
				},
			},
		).Build()
}

func (d *DocumentManagementTestSuite) setupMockForLdtTest(ldtContent, filePath string) {
	var handleDmEventFunc func(ctx *titan.Context, request dm_app.EventMessage) error
	patient1 := *pkg_fixtures.Patient_MariaMühlenberg
	patient2 := *pkg_fixtures.Patient_FranzMonk

	mockBuilder := d.createMockBuilderWithLdtContent(ldtContent, filePath, d.documentSetting, patient1, patient2, &handleDmEventFunc)

	scope := submodule.CreateScope()
	scope.InitValue(companion_modules.CompanionServiceMod, mockBuilder.CompanionMock)
	scope.InitValue(share.CatalogOverviewServiceMod, mockBuilder.CatalogOverviewMock)
	scope.InitValue(profile_service.PatientProfileServiceMod, mockBuilder.PatientProfileMock)

	documentService := service.DocumentManagementServiceMod.ResolveWith(scope)
	documentApp := document_management_app.DocumentManagementAppMod.ResolveWith(scope)
	handleDmEventFunc = documentService.HandleDmEvent

	d.documentManagementApp = documentApp
	d.mockApps = mockBuilder
}

func TestDocumentManagementSuite(t *testing.T) {
	natsConfig, natsServer, _ := ares_test.NewInfraForTesting()
	documentSetting := &repo.DocumentSettingEntity{
		BaseEntity: repos.BaseEntity{
			Id: util.NewUUID(),
		},
		DocumentSetting: document_setting_common.DocumentSetting{
			DocumentSettingType: document_setting_common.DocumentSettingType_LdtImport,
			LdtImport: &document_setting_common.LdtImport{
				Name:         "fixture",
				TimelineType: "LDT",
				DeviceId:     uuid.New(),
			},
		},
	}
	patient1 := *pkg_fixtures.Patient_MariaMühlenberg
	patient2 := *pkg_fixtures.Patient_FranzMonk

	patientFixture := &test_fixtures.PatientFixture{
		Patients: []*data.Patient{&patient1, &patient2},
	}

	settingFixture := &pkg_fixtures.DocumentSettingFixture{
		Settings: []*repo.DocumentSettingEntity{documentSetting},
	}
	var handleDmEventFunc func(ctx *titan.Context, request dm_app.EventMessage) error

	// Create temporary suite instance to access helper method
	tempSuite := &DocumentManagementTestSuite{}
	mockBuilder := tempSuite.createMockBuilderWithLdtContent(ldtOrder1Content, "S01TESTJP_V1.LDT", documentSetting, patient1, patient2, &handleDmEventFunc)

	scope := submodule.CreateScope()
	scope.InitValue(companion_modules.CompanionServiceMod, mockBuilder.CompanionMock)
	scope.InitValue(share.CatalogOverviewServiceMod, mockBuilder.CatalogOverviewMock)
	scope.InitValue(profile_service.PatientProfileServiceMod, mockBuilder.PatientProfileMock)

	documentService := service.DocumentManagementServiceMod.ResolveWith(scope)
	documentApp := document_management_app.DocumentManagementAppMod.ResolveWith(scope)
	handleDmEventFunc = documentService.HandleDmEvent

	mvzFixture := pkg_fixtures.MvzFixture_521111100.With(patientFixture, settingFixture).ToCareProviderFixture()

	s := &DocumentManagementTestSuite{
		documentManagementApp:          documentApp,
		doctor:                         pkg_fixtures.Doctor_CatharineRobel,
		mvzFixture:                     mvzFixture,
		documentSetting:                documentSetting,
		documentManagementRepo:         document_management_repo.DocumentManagementRepoMod.Resolve(),
		patientId:                      patient1.PatientId,
		patients:                       []*data.Patient{&patient1, &patient2},
		timelineDocumentManagementRepo: timeline_repo.NewTimelineRepoDefaultRepository[patient_encounter.EncounterDocumentManagement](),
		timelineLdtRepo:                timeline_repo.NewTimelineRepoDefaultRepository[patient_encounter.EncounterLDT](),
		mockApps:                       mockBuilder,
		labResultRepo:                  document_setting_state_repo.LabResultRepoMod.Resolve(),
		patientProfileRepo:             patient_profile.PatientProfileRepositoryMod.Resolve(),
		labParamResultRepo:             document_setting_state_repo.LabParamsRepoMod.Resolve(),
	}

	s.SetNatsServer(natsServer).
		SetEnableSocketServer(true, natsConfig).
		SetFixtures(mvzFixture)

	suite.Run(t, s)
}

func (d *DocumentManagementTestSuite) SetupTest() {
	_ = d.ResetData()
}

func (d *DocumentManagementTestSuite) getTitanContext() *titan.Context {
	t := d.T()

	return d.mvzFixture.GetAllCareProviders(d.GetFixtureContext())[0].LoginAsDoctor(t, d.doctor)
}

func (d *DocumentManagementTestSuite) TestCreateDocumentManagement() {
	t := d.T()
	ctx := d.getTitanContext()

	res, err := d.documentManagementApp.CreateDocumentManagement(ctx, document_management.CreateDocumentManagementRequest{
		DocumentName:      "documentName",
		CompanionFileId:   1,
		CompanionFilePath: "./testdata/documentName.pdf",
		Status:            document_management_common.DocumentManagementStatus_New,
	})
	require.NoError(t, err)
	require.NotNil(t, res)
	require.Equal(t, "documentName", res.DocumentManagementModel.DocumentName)
	require.Equal(t, int64(1), util.GetPointerValue(res.DocumentManagementModel.CompanionFileId))
}

func (d *DocumentManagementTestSuite) Test_Should_Update_Document_Management_Status() {
	t := d.T()
	ctx := d.getTitanContext()

	c, err := d.documentManagementApp.CreateDocumentManagement(ctx, document_management.CreateDocumentManagementRequest{
		DocumentName:      "documentName",
		CompanionFileId:   2,
		CompanionFilePath: "./testdata/documentName.pdf",
		Status:            document_management_common.DocumentManagementStatus_New,
	})
	require.NoError(t, err)
	require.NotNil(t, c)

	res, err := d.documentManagementApp.UpdateDocumentManagementStatus(ctx, document_management.UpdateDocumentManagementStatusRequest{
		Id:           c.Id,
		Status:       document_management_common.DocumentManagementStatus_Completed,
		ImportedDate: time.Now().UnixMilli(),
	})
	require.NoError(t, err)
	require.NotNil(t, res)
	require.Equal(t, document_management_common.DocumentManagementStatus_Completed, res.DocumentManagementModel.Status)
}

func (d *DocumentManagementTestSuite) Test_Should_List_Document_Mangagement() {
	t := d.T()
	ctx := d.getTitanContext()

	res, err := d.documentManagementApp.CreateDocumentManagement(ctx, document_management.CreateDocumentManagementRequest{
		DocumentName:      "documentName",
		CompanionFileId:   1,
		CompanionFilePath: "./testdata/documentName.pdf",
		Status:            document_management_common.DocumentManagementStatus_New,
	})
	require.NoError(t, err)
	require.NotNil(t, res)

	listResp, err := d.documentManagementApp.ListDocumentManagement(ctx, document_management.ListDocumentManagementRequest{
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 20,
		},
	})
	require.NoError(t, err)
	require.NotNil(t, listResp)
	require.Greater(t, len(listResp.Data), 0)
}

func (d *DocumentManagementTestSuite) Test_Should_Search_Like_DocumentName_Document_Management() {
	t := d.T()
	ctx := d.getTitanContext()

	res, err := d.documentManagementApp.CreateDocumentManagement(ctx, document_management.CreateDocumentManagementRequest{
		DocumentName:      "search document Name",
		CompanionFileId:   1,
		CompanionFilePath: "./testdata/documentName.pdf",
		Status:            document_management_common.DocumentManagementStatus_New,
	})
	require.NoError(t, err)
	require.NotNil(t, res)

	listResp, err := d.documentManagementApp.ListDocumentManagement(ctx, document_management.ListDocumentManagementRequest{
		Value: util.NewPointer("search"),
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 20,
		},
	})
	require.NoError(t, err)
	require.NotNil(t, listResp)
	require.Greater(t, len(listResp.Data), 0)
}
