package document_management_test

import (
	"testing"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/document_management"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	document_management_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/document_management"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
)

func (d *DocumentManagementTestSuite) Test_Process_Ldt_Import_Result_Success() {
	t := d.T()
	ctx := d.getTitanContext()
	requestId := "**********"

	t.Run("should import ldt document successfully", func(t *testing.T) {
		err := d.documentManagementApp.ProcessImportLdtDocuments(ctx)
		require.NoError(t, err)
	})

	t.Run("should get document after import", func(t *testing.T) {
		documents, err := d.documentManagementRepo.FindOne(ctx, bson.M{
			document_management_repo.Field_Patient_Lab_Result_Request_Id: requestId,
		})
		require.NoError(t, err)
		require.NotNil(t, documents)
		metaData := documents.MetaData
		require.NotEmpty(t, (*metaData)["patient_lab_result"])
		require.Equal(t, "V", (*metaData)["gdt_file_status"])
	})

	t.Run("should assign patient", func(t *testing.T) {
		documents, err := d.documentManagementRepo.FindOne(ctx, bson.M{
			document_management_repo.Field_Patient_Lab_Result_Request_Id: requestId,
		})
		require.NoError(t, err)
		require.NotNil(t, documents)
		err = d.documentManagementApp.AssignPatientDocument(ctx, document_management.AssignPatientDocumentRequest{
			Id:           *documents.Id,
			PatientId:    &d.patientId,
			DocumentType: documents.DocumentType,
		})
		require.NoError(t, err)
	})

	t.Run("should remove partial lab when have full lab", func(t *testing.T) {
		d.setupMockForLdtTest(ldtResultContent, "S01TESTJP_E12.LDT")
		err := d.documentManagementApp.ProcessImportLdtDocuments(ctx)
		require.NoError(t, err)

		result, err := d.documentManagementRepo.GetPartialLabResult(ctx, document_management_repo.GetPartialLabResultRequest{
			OrderId:   requestId,
			IsDeleted: util.NewPointer(true),
		})
		require.NoError(t, err)
		require.Equal(t, true, result.IsDeleted)

		timelineDoc, err := d.timelineDocumentManagementRepo.GetByDocumentManagement(ctx, *result.Id, util.NewPointer(d.patientId))
		require.NoError(t, err)
		require.Nil(t, timelineDoc)

		labResult, err := d.labResultRepo.GetByDocumentManagementId(ctx, *result.Id)
		require.NoError(t, err)
		require.Nil(t, labResult)
	})
}

func (d *DocumentManagementTestSuite) Test_Should_Import_LDT_Order_Success() {
	t := d.T()
	ctx := d.getTitanContext()
	d.setupMockForLdtTest(ldtOrderContent, "order.LDT")
	requestId := "**********"
	t.Run("should import ldt order successfully", func(t *testing.T) {
		err := d.documentManagementApp.ProcessImportLdtDocuments(ctx)
		require.NoError(t, err)
	})

	t.Run("should get document after import", func(t *testing.T) {
		documents, err := d.documentManagementRepo.FindOne(ctx, bson.M{
			document_management_repo.Field_Patient_Lab_Order_Request_Id: requestId,
		})
		require.NoError(t, err)
		require.NotNil(t, documents)
	})
}

func (d *DocumentManagementTestSuite) Test_Should_Get_Overwrite_Lab_Params_Success() {
	t := d.T()
	ctx := d.getTitanContext()
	d.setupMockForLdtTest(ldtOrder2Content, "S01TESTJP_V2.LDT")
	var patientId uuid.UUID
	labParamsToCheck := map[string]struct {
		Min      string
		Max      string
		MinFinal string
		MaxFinal string
	}{
		"Erythrozyten": {
			Min:      "4.00",
			Max:      "5.64",
			MinFinal: "3.85",
			MaxFinal: "5.19",
		},
		"Homoglobin": {
			Min:      "12.50",
			Max:      "17.20",
			MinFinal: "11.80",
			MaxFinal: "15.80",
		},
	}

	t.Run("should import ldt result successfully with right lab params", func(t *testing.T) {
		err := d.documentManagementApp.ProcessImportLdtDocuments(ctx)
		require.NoError(t, err)

		patientProfile, err := d.patientProfileRepo.GetPatientByNameAndDOB(ctx, "Franz", "Monk", patient_profile_common.DateOfBirth{
			Year:       util.NewPointer(int32(1973)),
			Month:      util.NewPointer(int32(7)),
			Date:       util.NewPointer(int32(1)),
			IsValidDOB: true,
		})
		require.NoError(t, err)
		patientId = *patientProfile[0].Id
		labParams, err := d.labParamResultRepo.GetLabParamsByPatientId(ctx, patientId)
		require.NoError(t, err)

		for _, labParam := range labParams {
			if labParam.Name == "Erythrozyten" {
				require.Equal(t, labParamsToCheck["Erythrozyten"].Min, labParam.Min)
				require.Equal(t, labParamsToCheck["Erythrozyten"].Max, labParam.Max)
			}
			if labParam.Name == "Homoglobin" {
				require.Equal(t, labParamsToCheck["Homoglobin"].Min, labParam.Min)
				require.Equal(t, labParamsToCheck["Homoglobin"].Max, labParam.Max)
			}
		}
	})

	t.Run("should import ldt result final and override lab params", func(t *testing.T) {
		d.setupMockForLdtTest(ldtResultContent, "S01TESTJP_E12.LDT")
		err := d.documentManagementApp.ProcessImportLdtDocuments(ctx)
		require.NoError(t, err)

		labParamsOverride, err := d.labParamResultRepo.GetLabParamsByPatientId(ctx, patientId)
		require.NoError(t, err)

		for _, labParam := range labParamsOverride {
			if labParam.Name == "Erythrozyten" {
				require.Equal(t, labParamsToCheck["Erythrozyten"].MinFinal, labParam.Min)
				require.Equal(t, labParamsToCheck["Erythrozyten"].MaxFinal, labParam.Max)
			}
			if labParam.Name == "Homoglobin" {
				require.Equal(t, labParamsToCheck["Homoglobin"].MinFinal, labParam.Min)
				require.Equal(t, labParamsToCheck["Homoglobin"].MaxFinal, labParam.Max)
			}
		}

	})

}

func (d *DocumentManagementTestSuite) Test_Should_Auto_Assign_Muti_Patient_By_Name_And_DOB() {
	t := d.T()
	ctx := d.getTitanContext()
	d.setupMockForLdtTest(ldtOrder1Content, "S01TESTJP_V1.LDT")
	err := d.documentManagementApp.ProcessImportLdtDocuments(ctx)
	require.NoError(t, err)
	requestIdPatient1 := "**********"
	requestIdPatient2 := "**********"
	patient1ID := uuid.Nil
	patient2ID := uuid.Nil

	t.Run("should get patient profile", func(t *testing.T) {
		patientProfile1, err := d.patientProfileRepo.GetPatientByNameAndDOB(ctx, "Maria", "Mühlenberg", patient_profile_common.DateOfBirth{
			Year:       util.NewPointer(int32(1999)),
			Month:      util.NewPointer(int32(6)),
			Date:       util.NewPointer(int32(10)),
			IsValidDOB: true,
		})

		require.NoError(t, err)
		require.NotNil(t, patientProfile1)
		patient1ID = *patientProfile1[0].Id

		patientProfile2, _ := d.patientProfileRepo.GetPatientByNameAndDOB(ctx, "Franz", "Monk", patient_profile_common.DateOfBirth{
			Year:       util.NewPointer(int32(1973)),
			Month:      util.NewPointer(int32(7)),
			Date:       util.NewPointer(int32(1)),
			IsValidDOB: true,
		})

		require.NoError(t, err)
		require.NotNil(t, patientProfile2)
		patient2ID = *patientProfile2[0].Id
	})

	t.Run("should assign patient in document management with request id patient 1", func(t *testing.T) {
		documents, err := d.documentManagementRepo.Find(ctx, bson.M{
			document_management_repo.Field_Patient_Lab_Result_Request_Id: requestIdPatient1,
		})
		require.NoError(t, err)
		require.NotNil(t, documents)
		require.Equal(t, patient1ID, *documents[0].PatientId)
		labParams, err := d.labParamResultRepo.GetAllPartialLDTParams(ctx, []uuid.UUID{*documents[0].Id})
		require.NoError(t, err)
		for _, labParam := range labParams {
			require.Equal(t, patient1ID, labParam.PatientId)
		}

		labResult, err := d.labResultRepo.GetByDocumentManagementId(ctx, *documents[0].Id)
		require.NoError(t, err)
		require.Equal(t, 1, len(labResult))
		require.Equal(t, patient1ID, labResult[0].PatientId)
	})

	t.Run("should assign patient in document management with request id patient 2", func(t *testing.T) {
		documents, err := d.documentManagementRepo.Find(ctx, bson.M{
			document_management_repo.Field_Patient_Lab_Result_Request_Id: requestIdPatient2,
		})
		require.NoError(t, err)
		require.NotNil(t, documents)
		require.Equal(t, patient2ID, *documents[0].PatientId)
		labParams, err := d.labParamResultRepo.GetAllPartialLDTParams(ctx, []uuid.UUID{*documents[0].Id})
		require.NoError(t, err)
		for _, labParam := range labParams {
			require.Equal(t, patient2ID, labParam.PatientId)
		}

		labResult, err := d.labResultRepo.GetByDocumentManagementId(ctx, *documents[0].Id)
		require.NoError(t, err)
		require.Equal(t, 1, len(labResult))
		require.Equal(t, patient2ID, labResult[0].PatientId)
	})

}
