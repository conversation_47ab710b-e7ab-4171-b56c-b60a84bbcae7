package catalog_goa_test

import (
	"testing"
	"time"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_goa"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/app"
	ares_test "git.tutum.dev/medi/tutum/ares/pkg/test"
	"git.tutum.dev/medi/tutum/ares/pkg/test/data"
	"git.tutum.dev/medi/tutum/ares/pkg/test/fixtures"
	"git.tutum.dev/medi/tutum/ares/pkg/test/repository"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_goa_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_utils_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	goa_master_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/masterdata_repo/goa"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/brianvoe/gofakeit/v5"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	fixtures2 "gitlab.com/silenteer-oss/hestia/test/fixtures"
	"gitlab.com/silenteer-oss/titan"
)

type CatalogGoaTestSuite struct {
	ares_test.WithDefaultInfraTestSuite
	db         *repository.Repos
	mvzFixture *fixtures2.CareProviderFixture
	doctor     *data.Doctor
	catalogGoa *catalog_goa.GoaCatalogAppClient
}

func TestCatalogGoaTestSuite(t *testing.T) {
	natsConfig, natsServer, _ := ares_test.NewInfraForTesting()
	patient := *fixtures.Patient_MariaMühlenberg
	patientFixture := &fixtures.PatientFixture{
		Patients: []*data.Patient{&patient},
	}
	mvzFixture := fixtures.MvzFixture_521111100.With(patientFixture).ToCareProviderFixture()

	config := config.MvzAppConfigMod.Resolve()
	if config == nil {
		panic("config is nil")
	}

	testServers := ares_test.NewTestServersWithDb(
		false,
		natsConfig,
		app.NewServer(
			*config,
		),
	)
	app := catalog_goa.NewGoaCatalogAppClient()
	s := &CatalogGoaTestSuite{
		db:         repository.DefaultRepos,
		mvzFixture: mvzFixture,
		doctor:     fixtures.Doctor_CatharineRobel,
		catalogGoa: app,
	}
	s.SetNatsServer(natsServer).SetTestServers(testServers).
		SetEnableSocketServer(true, natsConfig).
		SetFixtures(mvzFixture)
	suite.Run(t, s)
}

func (s *CatalogGoaTestSuite) Test_GOA_CreateGoaCatalog_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	fakeGoa := generateCreateGoaCatalogRequest(ctx).Goa
	fakeGoa.GoaNumber = "number1"
	goaCatalog, err := s.catalogGoa.CreateGoaCatalog(ctx, &catalog_goa.CreateGoaCatalogRequest{
		Goa: fakeGoa,
	})
	require.Nil(t, err)
	require.NotNil(t, goaCatalog)
	require.NotNil(t, goaCatalog.Goa)

	goa := goaCatalog.Goa
	require.Equal(t, fakeGoa.GoaNumber, goa.GoaNumber)
	require.Equal(t, fakeGoa.Description, goa.Description)
	require.Equal(t, fakeGoa.Evaluation, goa.Evaluation)
	require.Equal(t, fakeGoa.Factor, goa.Factor)
	require.Equal(t, catalog_utils_common.SelfCreated, goa.Source)
	require.Equal(t, fakeGoa.Validity, goa.Validity)
	require.Equal(t, fakeGoa.Chapter, goa.Chapter)
	require.Equal(t, fakeGoa.Unit, goa.Unit)
	require.Equal(t, fakeGoa.ExcludedCode, goa.ExcludedCode)
	require.Equal(t, fakeGoa.Remark, goa.Remark)
}

func (s *CatalogGoaTestSuite) Test_GOA_GetGoaCatalogs_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	goaCatalog, err := s.catalogGoa.GetGoaCatalogs(ctx, &catalog_goa.GetGoaCatalogsRequest{
		IsOnlySelfCreated: false,
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 10,
		},
	})
	require.Nil(t, err)
	require.NotEmpty(t, goaCatalog.Items)
}

func (s *CatalogGoaTestSuite) Test_GOA_GetGoaCatalogs_Sort_ASC_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	goaCatalog, err := s.catalogGoa.GetGoaCatalogs(ctx, &catalog_goa.GetGoaCatalogsRequest{
		IsOnlySelfCreated: false,
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 10,
			SortBy:   "goaNumber",
			Order:    "ASC",
		},
	})

	goaCatalogItems := make([]*catalog_goa_common.GoaCatalog, len(goaCatalog.Items))
	copy(goaCatalogItems, goaCatalog.Items)

	require.Nil(t, err)
	require.NotEmpty(t, goaCatalog.Items)
	for i, val := range goaCatalog.Items {
		require.Equal(t, goaCatalogItems[i].GoaNumber, val.GoaNumber)
	}
}

func (s *CatalogGoaTestSuite) Test_GOA_GetGoaCatalogs_Sort_DESC_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	goaCatalog, err := s.catalogGoa.GetGoaCatalogs(ctx, &catalog_goa.GetGoaCatalogsRequest{
		IsOnlySelfCreated: false,
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 10,
			SortBy:   "goaNumber",
			Order:    "DESC",
		},
	})

	goaCatalogItems := make([]*catalog_goa_common.GoaCatalog, len(goaCatalog.Items))
	copy(goaCatalogItems, goaCatalog.Items)

	require.Nil(t, err)
	require.NotEmpty(t, goaCatalog.Items)
	for i, val := range goaCatalog.Items {
		require.Equal(t, goaCatalogItems[i].GoaNumber, val.GoaNumber)
	}
}

func (s *CatalogGoaTestSuite) Test_GOA_GetGoaCatalogs_Search_And_Sort_ASC_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	goaCatalog, err := s.catalogGoa.GetGoaCatalogs(ctx, &catalog_goa.GetGoaCatalogsRequest{
		Value:             "A1",
		IsOnlySelfCreated: false,
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 30,
			SortBy:   "goaNumber",
			Order:    "ASC",
		},
	})

	require.Nil(t, err)
	require.NotEmpty(t, goaCatalog.Items)
	goaCatalogItems := goaCatalog.Items
	require.Equal(t, "A1", goaCatalogItems[0].GoaNumber)
	for i, val := range goaCatalog.Items {
		require.Equal(t, goaCatalogItems[i].GoaNumber, val.GoaNumber)
	}
}
func (s *CatalogGoaTestSuite) Test_GOA_GetChapters_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	response, err := s.catalogGoa.GetGoaChapters(ctx)
	require.Nil(t, err)
	require.NotNil(t, response)
	require.NotNil(t, response.Items)
}

func (s *CatalogGoaTestSuite) Test_GOA_ValidateBeforeUpdateGoaCatalog() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	goaMasterdata, err := s.catalogGoa.GetGoaCatalogByGoaNumber(ctx, &catalog_goa.GetGoaCatalogByNumberRequest{
		GoaNumber: "1",
	})
	require.Nil(t, err)
	require.NotNil(t, goaMasterdata)

	t.Run("Should validate failed when goa number is existed", func(t *testing.T) {
		createdGoa := goaMasterdata
		createdGoa.Goa.GoaId = ""
		validationRes, err := s.catalogGoa.IsValidUpdateGoa(ctx, &catalog_goa.UpdateGoaCatalogRequest{
			Goa: createdGoa.Goa,
		})
		require.Nil(t, err)
		require.NotNil(t, validationRes)
		require.Len(t, validationRes.Errors, 1)
	})

	t.Run("Should validate pass when goa number is not existed", func(t *testing.T) {
		updatedGoa := goaMasterdata
		updatedGoa.Goa.GoaNumber = "**********"
		validationRes, err := s.catalogGoa.IsValidUpdateGoa(ctx, &catalog_goa.UpdateGoaCatalogRequest{
			Goa: updatedGoa.Goa,
		})
		require.Nil(t, err)
		require.Len(t, validationRes.Errors, 0)
	})
}

func (s *CatalogGoaTestSuite) Test_GOA_Update_SelfCreated_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	// create catalog
	payload := generateCreateGoaCatalogRequest(ctx)
	payload.Goa.GoaNumber = "number2"
	goaCatalog, err := s.catalogGoa.CreateGoaCatalog(ctx, payload)
	require.Nil(t, err)
	require.NotNil(t, goaCatalog)

	// check is valid update Goa catalog
	goaCatalog.Goa.Description = "Goa_Description_2"
	// update Goa catalog
	updatedGoaCatalog, err := s.catalogGoa.UpdateGoaCatalog(ctx, &catalog_goa.UpdateGoaCatalogRequest{
		Goa: goaCatalog.Goa,
	})
	require.Nil(t, err)
	require.NotNil(t, updatedGoaCatalog)
	require.Equal(t, goaCatalog.Goa.GoaId, updatedGoaCatalog.Goa.GoaId)
	require.Equal(t, "Goa_Description_2", updatedGoaCatalog.Goa.Description)

	// check is valid update Goa catalog with Goa number is existed
	goaCatalog.Goa.GoaNumber = "1"
	updatedGoaCatalog, err = s.catalogGoa.UpdateGoaCatalog(ctx, &catalog_goa.UpdateGoaCatalogRequest{
		Goa: goaCatalog.Goa,
	})
	require.NotNil(t, err)
	require.Nil(t, updatedGoaCatalog)
}

func (s *CatalogGoaTestSuite) Test_GOA_Update_Masterdata_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	goaMasterdata, err := s.catalogGoa.GetGoaCatalogByGoaNumber(ctx, &catalog_goa.GetGoaCatalogByNumberRequest{
		GoaNumber: "100",
	})
	require.Nil(t, err)
	require.NotNil(t, goaMasterdata)
	require.NotNil(t, goaMasterdata.Goa)

	goaCatalog := goaMasterdata.Goa
	goaCatalog.Description = "Goa_Description_2"
	// update Goa catalog
	updatedGoaCatalog, err := s.catalogGoa.UpdateGoaCatalog(ctx, &catalog_goa.UpdateGoaCatalogRequest{
		Goa: goaCatalog,
	})
	require.Nil(t, err)
	require.NotNil(t, updatedGoaCatalog)
	require.NotEqual(t, goaMasterdata.Goa.GoaId, updatedGoaCatalog.Goa.GoaId)
	require.Equal(t, catalog_utils_common.XmlFileWithOverrideData, updatedGoaCatalog.Goa.Source)
	require.Equal(t, "Goa_Description_2", updatedGoaCatalog.Goa.Description)
}

func (s *CatalogGoaTestSuite) Test_GOA_Delete_Catalog_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	// create catalog
	payload := generateCreateGoaCatalogRequest(ctx)
	payload.Goa.GoaNumber = "number3"
	goaCatalog, err := s.catalogGoa.CreateGoaCatalog(ctx, payload)
	require.Nil(t, err)
	err = s.catalogGoa.DeleteGoaCatalog(ctx, &catalog_goa.DeleteGoaCatalogRequest{
		Id: goaCatalog.Goa.GoaId,
	})
	require.Nil(t, err)
}

func (s *CatalogGoaTestSuite) Test_GOA_SearchCatalog_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	// create catalog
	payload := generateCreateGoaCatalogRequest(ctx)
	payload.Goa.GoaNumber = "number4"
	payload.Goa.Description = "description"
	_, err := s.catalogGoa.CreateGoaCatalog(ctx, payload)
	require.Nil(t, err)
	response, err := s.catalogGoa.GetGoaCatalogs(ctx, &catalog_goa.GetGoaCatalogsRequest{
		Value: "description",
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 10,
		},
	})
	require.Nil(t, err)
	require.NotEmpty(t, response.Items)
}

func (s *CatalogGoaTestSuite) Test_GOA_SearchCatalog_Sucess_With_Special_Character() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	// create catalog
	payload := generateCreateGoaCatalogRequest(ctx)
	payload.Goa.GoaNumber = "number5"
	payload.Goa.Description = "description*"
	_, err := s.catalogGoa.CreateGoaCatalog(ctx, payload)
	require.Nil(t, err)
	res, err := s.catalogGoa.GetGoaCatalogs(ctx, &catalog_goa.GetGoaCatalogsRequest{
		Value: "*",
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 10,
		},
	})
	require.Nil(t, err)
	require.NotEmpty(t, res.Items)

	// another case without error
	searchValue := []string{
		"*",
		"\\",
		"?",
		"[",
		"]",
		"(",
		")",
	}
	for _, text := range searchValue {
		_, err := s.catalogGoa.GetGoaCatalogs(ctx, &catalog_goa.GetGoaCatalogsRequest{
			Value: text,
			Pagination: &common.PaginationRequest{
				Page:     1,
				PageSize: 10,
			},
		})
		require.Nil(t, err)
	}
}

func (s *CatalogGoaTestSuite) Test_GetGoaCatalogsByGoaNumber_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	t.Run("Should get goa catalog by goa number", func(t *testing.T) {
		goaItem, err := s.catalogGoa.GetGoaCatalogByGoaNumber(ctx, &catalog_goa.GetGoaCatalogByNumberRequest{
			GoaNumber: "A7028",
		})
		require.Nil(t, err)
		require.NotEmpty(t, goaItem)
		require.Equal(t, catalog_goa_common.Factor_Medical, goaItem.Goa.Factor)
		require.Equal(t, "Analogziffern", goaItem.Goa.Chapter.ChapterName)
		require.Equal(t, "", goaItem.Goa.MaxEvaluation)
		require.Equal(t, goa_master_repo.CalculateGoaPrice(catalog_goa_common.Unit_Points, goaItem.Goa.Evaluation), goaItem.Goa.Price)
	})

	t.Run("Should get goa catalog by goa number with price", func(t *testing.T) {
		goaItem, err := s.catalogGoa.GetGoaCatalogByGoaNumber(ctx, &catalog_goa.GetGoaCatalogByNumberRequest{
			GoaNumber: "W1T",
		})
		require.Nil(t, err)
		require.NotEmpty(t, goaItem)
		require.Equal(t, float64(3.58), goaItem.Goa.Price)
	})
}

func (s *CatalogGoaTestSuite) Test_GetGoaCatalogsByGoaNumber_WithMaxEvaluation() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	goaItem, err := s.catalogGoa.GetGoaCatalogByGoaNumber(ctx, &catalog_goa.GetGoaCatalogByNumberRequest{
		GoaNumber: "3597.H1",
	})
	require.Nil(t, err)
	require.NotEmpty(t, goaItem)
	require.Equal(t, catalog_goa_common.Factor_Laboratory, goaItem.Goa.Factor)
	require.Equal(t, "Laboratoriumsuntersuchungen", goaItem.Goa.Chapter.ChapterName)
	require.Equal(t, "3541.H-Höchstwert für die mit H1 gekennzeichneten Untersuchungen des Abschnitts M II", goaItem.Goa.MaxEvaluation)
	require.Equal(t, goa_master_repo.CalculateGoaPrice(catalog_goa_common.Unit_Points, goaItem.Goa.Evaluation), goaItem.Goa.Price)
}

func (s *CatalogGoaTestSuite) Test_GOA_Should_Unique_Goa_Number() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	res, err := s.catalogGoa.GetGoaCatalogs(ctx, &catalog_goa.GetGoaCatalogsRequest{
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 10,
		},
		Value: "A1",
	})
	require.Nil(t, err)
	require.NotEmpty(t, res.Items)

	firstGoa := res.Items[0]
	firstGoa.Description = "updated"
	updated, err := s.catalogGoa.UpdateGoaCatalog(ctx, &catalog_goa.UpdateGoaCatalogRequest{
		Goa: firstGoa,
	})
	require.Nil(t, err)
	require.NotNil(t, updated)
	require.Equal(t, firstGoa.Description, updated.Goa.Description)

	updatedRes, err := s.catalogGoa.GetGoaCatalogs(ctx, &catalog_goa.GetGoaCatalogsRequest{
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 10,
		},
		Value: "A1",
	})
	require.Nil(t, err)
	// Total should not change
	require.Equal(t, res.Total, updatedRes.Total)
	firstItemUpdated := updatedRes.Items[0]
	require.Equal(t, firstItemUpdated.Description, "updated")
	require.Equal(t, firstItemUpdated.Source, catalog_utils_common.XmlFileWithOverrideData)
}

func (s *CatalogGoaTestSuite) Test_GOA_SearchGoaItems_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	key := "number6"
	payload := generateCreateGoaCatalogRequest(ctx)
	payload.Goa.GoaNumber = key
	createdRes, err := s.catalogGoa.CreateGoaCatalog(ctx, payload)
	require.Nil(t, err)
	require.NotNil(t, createdRes)

	response, err := s.catalogGoa.SearchGoaItems(ctx, &catalog_goa.SearchGoaRequest{
		Value: "m",
	})
	require.Nil(t, err)
	require.NotNil(t, response)
	require.NotEmpty(t, response.Items)

	res := slice.FindOne(response.Items, func(g *catalog_goa_common.GoaItem) bool {
		return g.GoaNumber == key
	})
	require.NotNil(t, res)
	goaItem := *res
	require.Equal(t, key, goaItem.GoaNumber)
	require.Equal(t, true, goaItem.IsSelfCreated)
}

func (s *CatalogGoaTestSuite) Test_GOA_CreateGoaCatalog_InvalidData() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	invalidRequest := &catalog_goa.CreateGoaCatalogRequest{
		Goa: &catalog_goa_common.GoaCatalog{
			// Missing required fields
		},
	}

	_, err := s.catalogGoa.CreateGoaCatalog(ctx, invalidRequest)
	require.NotNil(t, err)
}

func (s *CatalogGoaTestSuite) Test_GOA_UpdateGoaCatalog_NonExistent() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	nonExistentGoa := &catalog_goa_common.GoaCatalog{
		GoaId:       "non-existent-id",
		GoaNumber:   "test123",
		Description: "Test Description",
		Factor:      catalog_goa_common.Factor_Medical,
	}

	updatedGoaCatalog, err := s.catalogGoa.UpdateGoaCatalog(ctx, &catalog_goa.UpdateGoaCatalogRequest{
		Goa: nonExistentGoa,
	})
	require.Nil(t, err)
	require.NotNil(t, updatedGoaCatalog)
}

func (s *CatalogGoaTestSuite) Test_GOA_DeleteGoaCatalog_NonExistent() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	err := s.catalogGoa.DeleteGoaCatalog(ctx, &catalog_goa.DeleteGoaCatalogRequest{
		Id: "non-existent-id",
	})
	require.NotNil(t, err)
}

func (s *CatalogGoaTestSuite) Test_GOA_GetGoaCatalogByGoaNumber_NonExistent() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	goaItem, err := s.catalogGoa.GetGoaCatalogByGoaNumber(ctx, &catalog_goa.GetGoaCatalogByNumberRequest{
		GoaNumber: "non-existent-number",
	})
	require.Nil(t, err)
	require.Nil(t, goaItem)
}

func (s *CatalogGoaTestSuite) Test_GOA_GetGoaCatalogs_Pagination() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	// Get all catalogs with page size 5
	firstPageResponse, err := s.catalogGoa.GetGoaCatalogs(ctx, &catalog_goa.GetGoaCatalogsRequest{
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 5,
		},
	})
	require.Nil(t, err)
	require.NotNil(t, firstPageResponse)

	// Get second page
	secondPageResponse, err := s.catalogGoa.GetGoaCatalogs(ctx, &catalog_goa.GetGoaCatalogsRequest{
		Pagination: &common.PaginationRequest{
			Page:     2,
			PageSize: 5,
		},
	})
	require.Nil(t, err)
	require.NotNil(t, secondPageResponse)

	// Make sure we get different items on different pages
	if len(firstPageResponse.Items) > 0 && len(secondPageResponse.Items) > 0 {
		require.NotEqual(t, firstPageResponse.Items[0].GoaId, secondPageResponse.Items[0].GoaId)
	}

	// Verify total count is consistent
	require.Equal(t, firstPageResponse.Total, secondPageResponse.Total)
}

func (s *CatalogGoaTestSuite) Test_GOA_GetGoaCatalogs_OnlySelfCreated() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	// Create a catalog
	payload := generateCreateGoaCatalogRequest(ctx)
	payload.Goa.GoaNumber = "number7"
	createdCatalog, err := s.catalogGoa.CreateGoaCatalog(ctx, payload)
	require.Nil(t, err)
	require.NotNil(t, createdCatalog)

	// Get only self-created catalogs
	response, err := s.catalogGoa.GetGoaCatalogs(ctx, &catalog_goa.GetGoaCatalogsRequest{
		IsOnlySelfCreated: true,
		Pagination: &common.PaginationRequest{
			Page:     1,
			PageSize: 10,
		},
	})
	require.Nil(t, err)
	require.NotEmpty(t, response.Items)

	// Verify the created catalog is in the results
	found := false
	for _, item := range response.Items {
		if item.GoaId == createdCatalog.Goa.GoaId {
			found = true
			break
		}
	}
	require.True(t, found, "Self-created catalog should be in the results")

	// Verify all items are self-created
	for _, item := range response.Items {
		require.Equal(t, catalog_utils_common.SelfCreated, item.Source)
	}
}

func (s *CatalogGoaTestSuite) Test_GOA_SearchGoaItems_NewGoaItems() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	response, err := s.catalogGoa.SearchGoaItems(ctx, &catalog_goa.SearchGoaRequest{
		Value: "w1",
	})
	require.Nil(t, err)
	require.NotNil(t, response)
	require.NotEmpty(t, response.Items)
}

func generateCreateGoaCatalogRequest(ctx *titan.Context) *catalog_goa.CreateGoaCatalogRequest {
	return &catalog_goa.CreateGoaCatalogRequest{
		Goa: &catalog_goa_common.GoaCatalog{
			GoaNumber:   "",
			Description: gofakeit.JobDescriptor(),
			Evaluation:  gofakeit.Float64(),
			Factor:      catalog_goa_common.Factor_Medical,
			Unit:        catalog_goa_common.Unit_Points,
			Validity: catalog_utils_common.Validity{
				FromDate: util.NewPointer(util.Now(ctx).UnixMilli()),
				ToDate:   util.NewPointer(util.Now(ctx).Add(time.Hour * 24 * 365).UnixMilli()),
			},
			Chapter: catalog_goa_common.Chapter{
				ChapterNumber: catalog_goa_common.GoaChapter[1].ChapterNumber,
				ChapterName:   catalog_goa_common.GoaChapter[1].ChapterName,
				ChapterRange:  catalog_goa_common.GoaChapter[1].ChapterRange,
			},
			ExcludedCode: []string{"12345678"},
			Remark:       util.NewPointer("Remark"),
		},
	}
}
