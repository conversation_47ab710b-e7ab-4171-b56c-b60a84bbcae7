package timeline

import (
	"strings"
	"testing"
	"time"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/catalog_goa"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/timeline"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/app"
	"git.tutum.dev/medi/tutum/ares/pkg/hook"
	ares_test "git.tutum.dev/medi/tutum/ares/pkg/test"
	"git.tutum.dev/medi/tutum/ares/pkg/test/data"
	"git.tutum.dev/medi/tutum/ares/pkg/test/fixtures"
	"git.tutum.dev/medi/tutum/ares/pkg/test/repository"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_goa_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_utils_common"
	api_common "git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/validation_timeline"
	catalog_goa_service "git.tutum.dev/medi/tutum/ares/service/domains/catalog_goa"
	"git.tutum.dev/medi/tutum/ares/service/domains/error_code"
	form_common "git.tutum.dev/medi/tutum/ares/service/domains/form/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/pkg/constant"
	qes_common "git.tutum.dev/medi/tutum/ares/service/domains/qes/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	schein_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/settings"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/employee"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/timeline_service"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/validation_timeline/validations/validation_context"
	"git.tutum.dev/medi/tutum/ares/share"
	"git.tutum.dev/medi/tutum/pkg/function"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"github.com/submodule-org/submodule.go/v2"
	fixtures2 "gitlab.com/silenteer-oss/hestia/test/fixtures"
	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type TimelineTestSuite struct {
	ares_test.WithDefaultInfraTestSuite
	db                *repository.Repos
	mvzFixture        *fixtures2.CareProviderFixture
	doctor            *data.Doctor
	timelineApp       *timeline.TimelineAppClient
	patient           data.Patient
	patient2          data.Patient
	scheinFake        schein_repo.ScheinRepo
	scheinFake2       schein_repo.ScheinRepo
	scheinHZV         schein_repo.ScheinRepo
	catalogGoaService *catalog_goa_service.CatalogGoaService
}

func TestTimelineTestSuite(t *testing.T) {
	natsConfig, natsServer, defaultClient := ares_test.NewInfraForTesting()

	patient := *fixtures.Patient_MariaMühlenberg
	patient.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = util.NewPointer("A123456789")

	patient2 := *fixtures.Patient_MaritaFangl
	patient2.Profile.PatientInfo.InsuranceInfos[0].InsuranceNumber = util.NewPointer("A123456719")

	patientFixture := &fixtures.PatientFixture{
		Patients: []*data.Patient{
			&patient,
			&patient2,
		},
	}

	config := config.MvzAppConfigMod.Resolve()
	if config == nil {
		panic("config is nil")
	}

	testServers := ares_test.NewTestServersWithDb(
		false,
		natsConfig,
		app.NewServer(
			*config,
		),
	)

	scheinFake := data.CreateFakeSchein(fixtures.Doctor_EudoraBarton.EmployeeId, patient.PatientId)
	scheinFake.Schein.ScheinMainGroup = string(schein_common.KV)
	scheinFake.Schein.InsuranceId = patient.Profile.PatientInfo.InsuranceInfos[0].Id

	scheinFake2 := data.CreateFakeSchein(fixtures.Doctor_EudoraBarton.EmployeeId, patient2.PatientId)
	scheinFake2.Schein.ScheinMainGroup = string(schein_common.KV)
	scheinFake2.Schein.InsuranceId = patient2.Profile.PatientInfo.InsuranceInfos[0].Id

	scheinFakeHZV := data.CreateFakeSchein(fixtures.Doctor_EudoraBarton.EmployeeId, patient.PatientId)
	scheinFakeHZV.Schein.ScheinMainGroup = string(schein_common.HZV)
	scheinFakeHZV.Schein.InsuranceId = patient.Profile.PatientInfo.InsuranceInfos[0].Id

	scheinFixture := &fixtures.ScheinFixture{
		Scheins: []schein_repo.ScheinRepo{scheinFake, scheinFake2, scheinFakeHZV},
	}
	settingsFixture := &fixtures.SettingFixture{
		Settings: []settings.Settings[map[string]string]{
			data.CreateFakeSettingGnrCondition(),
		},
	}
	doctorFixture := fixtures.Doctor_EudoraBarton
	mvzFixture := fixtures.MvzFixture_931111100.With(patientFixture, scheinFixture, settingsFixture).ToCareProviderFixture()

	app := timeline.NewTimelineAppClient(defaultClient)
	catalogGoaService := share.CatalogGoaServiceMod.Resolve()
	s := &TimelineTestSuite{
		db:                repository.DefaultRepos,
		mvzFixture:        mvzFixture,
		doctor:            doctorFixture,
		timelineApp:       app,
		patient:           patient,
		patient2:          patient2,
		scheinFake:        scheinFake,
		scheinFake2:       scheinFake2,
		scheinHZV:         scheinFakeHZV,
		catalogGoaService: catalogGoaService,
	}
	s.SetNatsServer(natsServer).SetTestServers(testServers).
		SetEnableSocketServer(true, natsConfig).
		SetFixtures(mvzFixture, scheinFixture)
	suite.Run(t, s)
}

func (s *TimelineTestSuite) Test_Should_Auto_Document_When_Have_ServiceCode_HZV() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	quarter, year := util.GetCurrentQuarter(time.Now())
	_, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
		TimelineModel: common.TimelineModel{
			TreatmentDoctorId: s.doctor.EmployeeId,
			PatientId:         s.patient.PatientId,
			ScheinIds:         []uuid.UUID{*s.scheinHZV.Id},
			ContractId:        util.NewString("AWH_01"),
			Quarter:           int32(quarter),
			Year:              int32(year),
			Type:              util.NewPointer(common.TimelineEntityType_Service),
			EncounterCase:     util.NewPointer(patient_encounter.PB),
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			EncounterServiceTimeline: &patient_encounter.EncounterServiceTimeline{
				ServiceMainGroup: util.NewPointer(api_common.HZV),
				Code:             "89111",
				Description:      "89111",
				Scheins: &[]*api_common.ScheinWithMainGroup{
					{
						ScheinId: s.scheinHZV.Id,
						Group:    api_common.HZV,
					},
				},
			},
		},
	})
	require.NoError(t, err)
	timelines, err := s.timelineApp.Get(ctx, timeline.GetRequest{
		PatientId:         s.patient.PatientId,
		ContractId:        util.NewString("AWH_01"),
		EncounterCase:     util.NewPointer(string(patient_encounter.PB)),
		TreatmentDoctorId: util.NewPointer(s.doctor.EmployeeId),
	})
	require.NoError(t, err)
	serviceCode800922 := slice.Any(timelines.TimelineModels, func(v common.TimelineModel) bool {
		return v.EncounterServiceTimeline.Code == "80092.2"
	})

	require.True(t, serviceCode800922)
}

func (s *TimelineTestSuite) Test_Invalid_PseudoGnr() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	quarter := util.NowQuarter(ctx)
	year := util.NowYear(ctx)
	createdTimeline, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
		TimelineModel: common.TimelineModel{
			TreatmentDoctorId: s.doctor.EmployeeId,
			PatientId:         s.patient.PatientId,
			ScheinIds:         []uuid.UUID{*s.scheinFake.Id},
			Quarter:           quarter,
			Year:              year,
			EncounterServiceTimeline: &patient_encounter.EncounterServiceTimeline{
				Code: "30402A",
				AdditionalInfos: &[]*patient_encounter.AdditionalInfoParent{
					{
						FK:    "5012",
						Value: "quang test material cost field",
						Children: []*patient_encounter.AdditionalInfoChild{
							{FK: "5011", Value: "1"},
							{FK: "5074", Value: ""},
							{FK: "5075", Value: ""},
						},
					},
					{
						FK:    "5200",
						Value: "quang test pseudo-gnr field",
					},
				},
			},
		},
	})
	require.Nil(t, err)

	timelineService, _ := timeline_service.TimelineServiceEncounterMod.SafeResolve()
	validatedTimeline, err := timelineService.FindById(ctx, *createdTimeline.TimelineModel.Id)
	require.Nil(t, err)
	require.NotNil(t, validatedTimeline.Payload.Errors)

	s.ResetData()
}

func (s *TimelineTestSuite) Test_GetTimelineEntries() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	_, err := s.timelineApp.Get(ctx, timeline.GetRequest{})
	require.Nil(t, err)
}

func (s *TimelineTestSuite) Test_Register_Event_Hooks() {
	t := s.T()
	timelineService := submodule.Make[*timeline_service.TimelineService[any]](func() *timeline_service.TimelineService[any] {
		return &timeline_service.TimelineService[any]{
			HookBeforeAction: hook.NewCUDInMemoryHook[*timeline.EventTimelineCreate, *timeline.EventTimelineUpdate, *timeline.EventTimelineRemove](),
		}
	})

	timeLineService, _ := timelineService.SafeResolve()
	timeLineService.HookBeforeAction.RegisterOnCreateFunc(func(ctx *titan.Context, cm *timeline.EventTimelineCreate) error {
		return nil
	})
	timeLineService2, _ := timelineService.SafeResolve()
	timeLineService2.HookBeforeAction.RegisterOnCreateFunc(func(ctx *titan.Context, cm *timeline.EventTimelineCreate) error {
		return nil
	})

	require.Equal(t, 2, timeLineService.HookBeforeAction.TotalCreateFuncs())
}

func (s *TimelineTestSuite) Test_Get_GetDiagnose_Unique() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	_, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
		TimelineModel: common.TimelineModel{
			TreatmentDoctorId: s.doctor.EmployeeId,
			PatientId:         s.patient.PatientId,
			EncounterDiagnoseTimeline: &patient_encounter.EncounterDiagnoseTimeline{
				Code:       "A00",
				Certainty:  util.NewPointer(patient_encounter.A),
				Laterality: util.NewPointer(patient_encounter.U),
			},
		},
	})
	require.Nil(t, err)
	getDiagnoseResult, err := s.timelineApp.GetDiagnose(ctx, timeline.GetDiagnoseRequest{
		PatientId: s.patient.PatientId,
	})
	require.Nil(t, err)
	require.NotEmpty(t, getDiagnoseResult.EncounterDiagnoseTimeline)
}

func (s *TimelineTestSuite) Test_Create_Timeline_Entries() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	_, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
		TimelineModel: common.TimelineModel{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			TreatmentDoctorId: s.doctor.EmployeeId,
			PatientId:         s.patient.PatientId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			EncounterDiagnoseTimeline: &patient_encounter.EncounterDiagnoseTimeline{
				Code:        "C21.1",
				Description: "C21.1",
				Type:        patient_encounter.DIAGNOSETYPE_PERMANENT,
				Certainty:   util.NewPointer(patient_encounter.A),
				Laterality:  util.NewPointer(patient_encounter.U),
			},
		},
	})
	require.Nil(t, err)
	groupByQuarterResponse, err := s.timelineApp.GroupByQuarter(ctx, timeline.GroupByQuarterRequest{
		PatientId: s.patient.PatientId,
		FromDate:  util.NewPointer(util.NowUnixMillis(ctx)),
		ToDate:    util.NewPointer(util.ConvertTimeToMiliSecond(time.Now().AddDate(0, 0, 1))),
	})
	require.Nil(t, err)
	require.Equal(t, 1, len(groupByQuarterResponse.GroupByQuarters))
	require.NotNil(t, groupByQuarterResponse.GroupByQuarters[0].TimelineModels[0].EncounterDiagnoseTimeline)

	// timelineLog, err := s.timelineApp.GetEntryHistory(ctx, timeline.GetEntryHistoryRequest{
	// 	TimelineId: *createdItem.TimelineModel.Id,
	// })
	// require.Nil(t, err)
	// require.Equal(t, len(timelineLog.HistoryItems), 1)

	// checkingItem := timelineLog.HistoryItems[0].TimelineModel
	// require.Equal(t, checkingItem.EncounterDiagnoseTimeline.Code, createdItem.TimelineModel.EncounterDiagnoseTimeline.Code)
}

func (s *TimelineTestSuite) Test_Edit_Timeline_Entries() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	createResponse, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
		TimelineModel: common.TimelineModel{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			EncounterDiagnoseTimeline: &patient_encounter.EncounterDiagnoseTimeline{
				Code:        "C21.1",
				Description: "C21.1",
				Type:        patient_encounter.DIAGNOSETYPE_PERMANENT,
				Certainty:   util.NewPointer(patient_encounter.A),
				Laterality:  util.NewPointer(patient_encounter.U),
			},
		},
	})
	require.Nil(t, err)

	editResponse, err := s.timelineApp.Edit(ctx, timeline.EditRequest{TimelineModel: common.TimelineModel{
		Id:                createResponse.TimelineModel.Id,
		PatientId:         s.patient.PatientId,
		TreatmentDoctorId: s.doctor.EmployeeId,
		EncounterDiagnoseTimeline: &patient_encounter.EncounterDiagnoseTimeline{
			Code:        "C21.1_update",
			Description: "C21.1_update",
			Type:        patient_encounter.DIAGNOSETYPE_PERMANENT,
			Certainty:   util.NewPointer(patient_encounter.A),
			Laterality:  util.NewPointer(patient_encounter.U),
		},
	}})
	require.Nil(t, err)
	require.Equal(t, "C21.1_update", editResponse.TimelineModel.EncounterDiagnoseTimeline.Code)
}

func (s *TimelineTestSuite) Test_Exam() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	_, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
		TimelineModel: common.TimelineModel{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			EncounterDiagnoseTimeline: &patient_encounter.EncounterDiagnoseTimeline{
				Code:        "11297",
				Description: "Dung TRInh Minh",
				Type:        patient_encounter.DiagnoseType(patient_encounter.L),
				Certainty:   util.NewPointer(patient_encounter.A),
				Laterality:  util.NewPointer(patient_encounter.U),
			},
		},
	})
	require.Nil(t, err)
}

func (s *TimelineTestSuite) Test_Should_Get_Diagnosis() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	_, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
		TimelineModel: common.TimelineModel{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			EncounterDiagnoseTimeline: &patient_encounter.EncounterDiagnoseTimeline{
				Code:        "11297",
				Description: "Dung TRInh Minh",
				Type:        patient_encounter.DiagnoseType(patient_encounter.L),
				Certainty:   util.NewPointer(patient_encounter.A),
				Laterality:  util.NewPointer(patient_encounter.U),
				Command:     "D",
			},
		},
	})
	require.Nil(t, err)
	now := util.NowUnixMillis(ctx)
	res, err := s.timelineApp.GetTakeOverDiagnosis(ctx, timeline.GetTakeOverDiagnosisRequest{
		PatientId: s.patient.PatientId,
		FromDate:  util.NewPointer(now),
		ToDate:    util.NewPointer(now),
		Query:     util.NewPointer("11297"),
	})

	require.Nil(t, err)
	require.Equal(t, 1, len(res.TakeOverDiagnosisGroup))
}

// Case 1:
// Given there is another DD (same diagnose, same certainty) that has an end date
// When a user adds or edits the same diagnose and same certainty without an end date
// Then the end date of the existing DD is applied to the new record
func (s *TimelineTestSuite) Test_Should_Set_EndDate_Permanent_Diagnosis_When_Without_End_date() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	now := time.Now()
	t.Run("when create", func(t *testing.T) {
		_, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
			TimelineModel: common.TimelineModel{
				BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
				PatientId:         s.patient.PatientId,
				TreatmentDoctorId: s.doctor.EmployeeId,
				ScheinIds: []uuid.UUID{
					*s.scheinFake.Id,
				},
				EncounterDiagnoseTimeline: &patient_encounter.EncounterDiagnoseTimeline{
					Code:       "C21.8",
					Type:       patient_encounter.DIAGNOSETYPE_PERMANENT,
					Certainty:  util.NewPointer(patient_encounter.G),
					ValidUntil: util.NewPointer(now.AddDate(0, 0, 1).UnixMilli()),
					Command:    "DD",
				},
			},
		})
		require.Nil(t, err)
		_, err = s.timelineApp.Create(ctx, timeline.CreateRequest{
			TimelineModel: common.TimelineModel{
				BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
				PatientId:         s.patient.PatientId,
				TreatmentDoctorId: s.doctor.EmployeeId,
				ScheinIds: []uuid.UUID{
					*s.scheinFake.Id,
				},
				EncounterDiagnoseTimeline: &patient_encounter.EncounterDiagnoseTimeline{
					Code:       "C21.8",
					Type:       patient_encounter.DIAGNOSETYPE_PERMANENT,
					Certainty:  util.NewPointer(patient_encounter.A),
					ValidUntil: util.NewPointer(now.AddDate(0, 0, 1).UnixMilli()),
					Command:    "D",
				},
			},
		})
		require.Nil(t, err)
		res, err := s.timelineApp.GetTakeOverDiagnosis(ctx, timeline.GetTakeOverDiagnosisRequest{
			PatientId: s.patient.PatientId,
			Query:     util.NewPointer("C21.8"),
		})

		require.Nil(t, err)
		require.Equal(t, 2, len(res.TakeOverDiagnosisGroup))
		require.Equal(t, res.TakeOverDiagnosisGroup[0].TimelineModels[0].EncounterDiagnoseTimeline.ValidUntil, res.TakeOverDiagnosisGroup[1].TimelineModels[0].EncounterDiagnoseTimeline.ValidUntil)
	})
	t.Run("when edit", func(t *testing.T) {
		_, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
			TimelineModel: common.TimelineModel{
				BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
				PatientId:         s.patient.PatientId,
				TreatmentDoctorId: s.doctor.EmployeeId,
				ScheinIds: []uuid.UUID{
					*s.scheinFake.Id,
				},
				EncounterDiagnoseTimeline: &patient_encounter.EncounterDiagnoseTimeline{
					Code:       "C22.8",
					Type:       patient_encounter.DIAGNOSETYPE_PERMANENT,
					Certainty:  util.NewPointer(patient_encounter.A),
					ValidUntil: util.NewPointer(now.AddDate(0, 0, 1).UnixMilli()),
				},
			},
		})
		require.Nil(t, err)
		timeline2, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
			TimelineModel: common.TimelineModel{
				BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
				PatientId:         s.patient.PatientId,
				TreatmentDoctorId: s.doctor.EmployeeId,
				ScheinIds: []uuid.UUID{
					*s.scheinFake.Id,
				},
				EncounterDiagnoseTimeline: &patient_encounter.EncounterDiagnoseTimeline{
					Code:      "C22.8",
					Type:      patient_encounter.DIAGNOSETYPE_PERMANENT,
					Certainty: util.NewPointer(patient_encounter.A),
					Command:   "DD",
				},
			},
		})
		require.Nil(t, err)

		_, err = s.timelineApp.Edit(ctx, timeline.EditRequest{
			TimelineModel: common.TimelineModel{
				Id:                timeline2.TimelineModel.Id,
				BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
				PatientId:         s.patient.PatientId,
				TreatmentDoctorId: s.doctor.EmployeeId,
				ScheinIds: []uuid.UUID{
					*s.scheinFake.Id,
				},
				EncounterDiagnoseTimeline: &patient_encounter.EncounterDiagnoseTimeline{
					Code:       "C22.8",
					Type:       patient_encounter.DIAGNOSETYPE_PERMANENT,
					Certainty:  util.NewPointer(patient_encounter.A),
					Laterality: util.NewPointer(patient_encounter.U),
					Command:    "D",
				},
			},
		})
		require.Nil(t, err)

		res, err := s.timelineApp.GetTakeOverDiagnosis(ctx, timeline.GetTakeOverDiagnosisRequest{
			PatientId: s.patient.PatientId,
			FromDate:  util.NewPointer(now.UnixMilli()),
			ToDate:    util.NewPointer(now.UnixMilli()),
			Query:     util.NewPointer("C22.8"),
		})

		require.Nil(t, err)
		require.Equal(t, 2, len(res.TakeOverDiagnosisGroup))
		require.Equal(t, res.TakeOverDiagnosisGroup[0].TimelineModels[0].EncounterDiagnoseTimeline.ValidUntil, res.TakeOverDiagnosisGroup[1].TimelineModels[0].EncounterDiagnoseTimeline.ValidUntil)
	})
}

func (s *TimelineTestSuite) Test_Should_Set_EndDate_Permanent_Diagnosis_When_With_End_date() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	now := time.Now()
	tomorrow := now.AddDate(0, 0, 1)
	t.Run("when create", func(t *testing.T) {
		_, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
			TimelineModel: common.TimelineModel{
				BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
				PatientId:         s.patient.PatientId,
				TreatmentDoctorId: s.doctor.EmployeeId,
				ScheinIds: []uuid.UUID{
					*s.scheinFake.Id,
				},
				EncounterDiagnoseTimeline: &patient_encounter.EncounterDiagnoseTimeline{
					Code:       "C23.8",
					Type:       patient_encounter.DIAGNOSETYPE_PERMANENT,
					Certainty:  util.NewPointer(patient_encounter.G),
					ValidUntil: util.NewPointer(now.UnixMilli()),
					Command:    "DA",
				},
			},
		})
		require.Nil(t, err)
		_, err = s.timelineApp.Create(ctx, timeline.CreateRequest{
			TimelineModel: common.TimelineModel{
				BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
				PatientId:         s.patient.PatientId,
				TreatmentDoctorId: s.doctor.EmployeeId,
				ScheinIds: []uuid.UUID{
					*s.scheinFake.Id,
				},
				EncounterDiagnoseTimeline: &patient_encounter.EncounterDiagnoseTimeline{
					Code:       "C23.8",
					Type:       patient_encounter.DIAGNOSETYPE_PERMANENT,
					Certainty:  util.NewPointer(patient_encounter.A),
					ValidUntil: util.NewPointer(tomorrow.UnixMilli()),
					Command:    "DD",
				},
			},
		})
		require.Nil(t, err)

		res, err := s.timelineApp.GetTakeOverDiagnosis(ctx, timeline.GetTakeOverDiagnosisRequest{
			PatientId: s.patient.PatientId,
			Query:     util.NewPointer("C23.8"),
		})

		require.Nil(t, err)
		require.Equal(t, 1, len(res.TakeOverDiagnosisGroup))
		require.Equal(t, 2, len(res.TakeOverDiagnosisGroup[0].TimelineModels))

		timelineModel := res.TakeOverDiagnosisGroup[0].TimelineModels
		shouldHasTomorrow := slice.FindOne(timelineModel, func(tm common.TimelineModel) bool {
			return *tm.EncounterDiagnoseTimeline.ValidUntil == tomorrow.UnixMilli()
		})
		require.NotNil(t, shouldHasTomorrow)

		shouldHasNow := slice.FindOne(timelineModel, func(tm common.TimelineModel) bool {
			return *tm.EncounterDiagnoseTimeline.ValidUntil == now.UnixMilli()
		})
		require.NotNil(t, shouldHasNow)
	})
	t.Run("when edit", func(t *testing.T) {
		_, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
			TimelineModel: common.TimelineModel{
				BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
				PatientId:         s.patient.PatientId,
				TreatmentDoctorId: s.doctor.EmployeeId,
				ScheinIds: []uuid.UUID{
					*s.scheinFake.Id,
				},
				EncounterDiagnoseTimeline: &patient_encounter.EncounterDiagnoseTimeline{
					Code:       "C24.8",
					Type:       patient_encounter.DIAGNOSETYPE_PERMANENT,
					Certainty:  util.NewPointer(patient_encounter.A),
					ValidUntil: util.NewPointer(now.UnixMilli()),
					Command:    "DD",
				},
			},
		})
		require.Nil(t, err)
		timeline2, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
			TimelineModel: common.TimelineModel{
				BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
				PatientId:         s.patient.PatientId,
				TreatmentDoctorId: s.doctor.EmployeeId,
				ScheinIds: []uuid.UUID{
					*s.scheinFake.Id,
				},
				EncounterDiagnoseTimeline: &patient_encounter.EncounterDiagnoseTimeline{
					Code:       "C24.8",
					Type:       patient_encounter.DIAGNOSETYPE_PERMANENT,
					ValidUntil: util.NewPointer(now.UnixMilli()),
					Command:    "DD",
				},
			},
		})
		require.Nil(t, err)

		_, err = s.timelineApp.Edit(ctx, timeline.EditRequest{
			TimelineModel: common.TimelineModel{
				Id:                timeline2.TimelineModel.Id,
				BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
				PatientId:         s.patient.PatientId,
				TreatmentDoctorId: s.doctor.EmployeeId,
				ScheinIds: []uuid.UUID{
					*s.scheinFake.Id,
				},
				EncounterDiagnoseTimeline: &patient_encounter.EncounterDiagnoseTimeline{
					Code:       "C24.8",
					Type:       patient_encounter.DIAGNOSETYPE_PERMANENT,
					Certainty:  util.NewPointer(patient_encounter.A),
					Laterality: util.NewPointer(patient_encounter.U),
					ValidUntil: util.NewPointer(tomorrow.UnixMilli()),
					Command:    "DA",
				},
			},
		})
		require.Nil(t, err)

		res, err := s.timelineApp.GetTakeOverDiagnosis(ctx, timeline.GetTakeOverDiagnosisRequest{
			PatientId: s.patient.PatientId,
			FromDate:  util.NewPointer(now.UnixMilli()),
			ToDate:    util.NewPointer(now.UnixMilli()),
			Query:     util.NewPointer("C24.8"),
		})

		require.Nil(t, err)
		require.Equal(t, 1, len(res.TakeOverDiagnosisGroup))
		require.Equal(t, 2, len(res.TakeOverDiagnosisGroup[0].TimelineModels))
		timelineModel := res.TakeOverDiagnosisGroup[0].TimelineModels

		require.Equal(t, *timelineModel[0].EncounterDiagnoseTimeline.ValidUntil, tomorrow.UnixMilli())
		require.Equal(t, *timelineModel[1].EncounterDiagnoseTimeline.ValidUntil, tomorrow.UnixMilli())
	})
}

func (s *TimelineTestSuite) Test_Should_Invalid_01734_Service_Code() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	fakeInsuranceId := uuid.New()
	bsnr := "190000"

	store := submodule.CreateScope()
	var patientGetByHashIdFunc validation_context.PatientGetByIdFunc = func(ctx *titan.Context, uuid uuid.UUID) (*patient.PatientProfile, error) {
		return &patient.PatientProfile{
			Id:          &s.patient.PatientId,
			DateOfBirth: 1286496000000,
			PatientInfo: &patient_profile_common.PatientInfo{
				InsuranceInfos: []patient_profile_common.InsuranceInfo{
					{
						Id:                   fakeInsuranceId,
						InsuranceCompanyId:   "61125",
						InsuranceCompanyName: "AOK Baden-Württemberg Hauptverwaltung",
						IkNumber:             *********,
						FeeCatalogue:         "1",
					},
				},
			},
		}, nil

	}
	var employeeProfileFinderFunc validation_context.EmployeeProfileFinderFunc = func(ctx *titan.Context, filter any, opts ...*options.FindOptions) ([]employee.EmployeeProfile, error) {
		return []employee.EmployeeProfile{
			{
				Id:      &s.doctor.EmployeeId,
				Bsnr:    bsnr,
				BsnrId:  s.doctor.BsnrId,
				BsnrIds: s.doctor.BsnrIds,
				Bsnrs:   s.doctor.Bsnrs,
			},
		}, nil
	}

	var scheinFindByIdsFunc validation_context.ScheinFindByIdsFunc = func(ctx *titan.Context, ids []uuid.UUID) ([]schein_repo.ScheinRepo, error) {
		return []schein_repo.ScheinRepo{
			{
				Id: s.scheinFake.Id,
				Schein: schein_common.Schein{
					InsuranceId:     fakeInsuranceId,
					ScheinMainGroup: "KV",
				},
				DoctorId:         s.doctor.EmployeeId,
				AssignedToBsnrId: s.doctor.BsnrId,
			},
		}, nil
	}
	var bsnrGetByIdsFunc validation_context.BsnrGetByIdsFunc = func(ctx *titan.Context, ids []uuid.UUID) ([]bsnr_repo.BSNR, error) {
		return []bsnr_repo.BSNR{
			{
				Id:   s.doctor.BsnrId,
				Code: bsnr,
			},
		}, nil
	}

	store.InitValue(validation_context.PatientGetByHashIdMod, patientGetByHashIdFunc)
	store.InitValue(validation_context.EmployeeProfileFinderMod, employeeProfileFinderFunc)
	store.InitValue(validation_context.ScheinFindByIdsMod, scheinFindByIdsFunc)
	store.InitValue(validation_context.BsnrGetByIdsMod, bsnrGetByIdsFunc)

	timelineService, err := timeline_service.TimelineServiceEncounterMod.SafeResolveWith(store)
	require.Nil(s.T(), err)
	result, err := timelineService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
		BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
		PatientId:         s.patient.PatientId,
		TreatmentDoctorId: s.doctor.EmployeeId,
		ScheinIds: []uuid.UUID{
			*s.scheinFake.Id,
		},
		Payload: patient_encounter.EncounterServiceTimeline{
			Code: "01734",
		},
	})
	require.Nil(s.T(), err)

	timelineWithError, _ := timelineService.FindById(ctx, *result.Id)

	require.NotNil(s.T(), timelineWithError.Payload)
	require.NotNil(s.T(), timelineWithError.Payload.Errors)
	hasWarningAge := slice.Any(*timelineWithError.Payload.Errors, func(eie *patient_encounter.EncounterItemError) bool {
		return eie.ErrorCode == string(error_code.ErrorCode_Validation_RangeAge)
	})
	require.True(s.T(), hasWarningAge)
}

func (s *TimelineTestSuite) Test_Should_Replace_03000_Service_Code() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	fakeInsuranceId := uuid.New()
	age75 := time.Now().AddDate(-75, 0, 0).UnixMilli()

	store := submodule.CreateScope()
	var patientGetByHashIdFunc validation_context.PatientGetByIdFunc = func(ctx *titan.Context, uuid uuid.UUID) (*patient.PatientProfile, error) {
		return &patient.PatientProfile{
			Id:          &s.patient.PatientId,
			DateOfBirth: age75,
			PatientInfo: &patient_profile_common.PatientInfo{
				InsuranceInfos: []patient_profile_common.InsuranceInfo{
					{
						Id:                   fakeInsuranceId,
						InsuranceCompanyId:   "61125",
						InsuranceCompanyName: "AOK Baden-Württemberg Hauptverwaltung",
						IkNumber:             *********,
						FeeCatalogue:         "1",
					},
				},
			},
		}, nil
	}
	var employeeProfileFinderFunc validation_context.EmployeeProfileFinderFunc = func(ctx *titan.Context, filter any, opts ...*options.FindOptions) ([]employee.EmployeeProfile, error) {
		return []employee.EmployeeProfile{
			{
				Id:      &s.doctor.EmployeeId,
				Bsnr:    s.doctor.Bsnr,
				BsnrId:  s.doctor.BsnrId,
				Bsnrs:   s.doctor.Bsnrs,
				BsnrIds: s.doctor.BsnrIds,
			},
		}, nil
	}

	var scheinFindByIdsFunc validation_context.ScheinFindByIdsFunc = func(ctx *titan.Context, ids []uuid.UUID) ([]schein_repo.ScheinRepo, error) {
		return []schein_repo.ScheinRepo{
			{
				Id: s.scheinFake.Id,
				Schein: schein_common.Schein{
					InsuranceId:     fakeInsuranceId,
					ScheinMainGroup: "KV",
				},
				DoctorId: s.doctor.EmployeeId,
				ScheinDetail: schein_common.ScheinDetail{
					TsvgContactType: util.NewString("2"),
					TsvgTranferCode: util.NewString("code-fort-ssTS"),
					TsvgContactDate: util.NewPointer(time.Now().UnixMilli()),
				},
				AssignedToBsnrId: s.doctor.BsnrId,
			},
		}, nil
	}

	store.InitValue(validation_context.PatientGetByHashIdMod, patientGetByHashIdFunc)
	store.InitValue(validation_context.EmployeeProfileFinderMod, employeeProfileFinderFunc)
	store.InitValue(validation_context.ScheinFindByIdsMod, scheinFindByIdsFunc)

	timelineService, err := timeline_service.TimelineServiceEncounterMod.SafeResolveWith(store)
	require.Nil(s.T(), err)
	result, err := timelineService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
		BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
		PatientId:         s.patient.PatientId,
		TreatmentDoctorId: s.doctor.EmployeeId,
		ScheinIds: []uuid.UUID{
			*s.scheinFake.Id,
		},
		Payload: patient_encounter.EncounterServiceTimeline{
			Code: "03000",
		},
	})
	require.Nil(s.T(), err)

	timelineWithError, _ := timelineService.FindById(ctx, *result.Id)

	require.NotNil(s.T(), timelineWithError.Payload)
	require.NotNil(s.T(), timelineWithError.Payload.Errors)
	hasWarningReplaceServiceCode := slice.Any(*timelineWithError.Payload.Errors, func(eie *patient_encounter.EncounterItemError) bool {
		return eie.ErrorCode == string(error_code.ErrorCode_Validation_ReplacedWithServiceCodeWhenBilling)
	})
	hasWarningSuggestionCode := slice.Any(*timelineWithError.Payload.Errors, func(eie *patient_encounter.EncounterItemError) bool {
		return eie.ErrorCode == "tss_surcharge_suggestion"
	})
	require.True(s.T(), hasWarningSuggestionCode)
	require.True(s.T(), hasWarningReplaceServiceCode)
}

func (s *TimelineTestSuite) Test_Should_Missing_Pseudo_GNR() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	fakeInsuranceId := uuid.New()
	age75 := time.Now().AddDate(-75, 0, 0).UnixMilli()
	bsnr := "190000"
	var patientGetByHashIdFunc validation_context.PatientGetByIdFunc = func(ctx *titan.Context, uuid uuid.UUID) (*patient.PatientProfile, error) {
		return &patient.PatientProfile{
			Id:          &s.patient.PatientId,
			DateOfBirth: age75,
			PatientInfo: &patient_profile_common.PatientInfo{
				InsuranceInfos: []patient_profile_common.InsuranceInfo{
					{
						Id:                   fakeInsuranceId,
						InsuranceCompanyId:   "61125",
						InsuranceCompanyName: "AOK Baden-Württemberg Hauptverwaltung",
						IkNumber:             *********,
						FeeCatalogue:         "1",
					},
				},
			},
		}, nil
	}

	var scheinFindByIdsFunc validation_context.ScheinFindByIdsFunc = func(ctx *titan.Context, ids []uuid.UUID) ([]schein_repo.ScheinRepo, error) {
		return []schein_repo.ScheinRepo{
			{
				Id: s.scheinFake.Id,
				Schein: schein_common.Schein{
					InsuranceId:     fakeInsuranceId,
					ScheinMainGroup: "KV",
				},
				DoctorId:         s.doctor.EmployeeId,
				AssignedToBsnrId: s.doctor.BsnrId,
			},
		}, nil
	}

	var employeeProfileFinderFunc validation_context.EmployeeProfileFinderFunc = func(ctx *titan.Context, filter any, opts ...*options.FindOptions) ([]employee.EmployeeProfile, error) {
		return []employee.EmployeeProfile{
			{
				Id:      &s.doctor.EmployeeId,
				Bsnr:    bsnr,
				BsnrId:  s.doctor.BsnrId,
				BsnrIds: s.doctor.BsnrIds,
				Bsnrs:   s.doctor.Bsnrs,
			},
		}, nil
	}
	var bsnrGetByIdsFunc validation_context.BsnrGetByIdsFunc = func(ctx *titan.Context, ids []uuid.UUID) ([]bsnr_repo.BSNR, error) {
		return []bsnr_repo.BSNR{
			{
				Id:   s.doctor.BsnrId,
				Code: bsnr,
			},
		}, nil
	}

	store := submodule.CreateScope()
	store.InitValue(validation_context.PatientGetByHashIdMod, patientGetByHashIdFunc)
	store.InitValue(validation_context.ScheinFindByIdsMod, scheinFindByIdsFunc)
	store.InitValue(validation_context.EmployeeProfileFinderMod, employeeProfileFinderFunc)
	store.InitValue(validation_context.BsnrGetByIdsMod, bsnrGetByIdsFunc)

	timelineService, err := timeline_service.TimelineServiceEncounterMod.SafeResolveWith(store)
	require.Nil(s.T(), err)
	result, err := timelineService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
		BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
		PatientId:         s.patient.PatientId,
		TreatmentDoctorId: s.doctor.EmployeeId,
		ScheinIds: []uuid.UUID{
			*s.scheinFake.Id,
		},
		Payload: patient_encounter.EncounterServiceTimeline{
			Code: "03000",
			AdditionalInfos: &[]*patient_encounter.AdditionalInfoParent{
				{
					FK:    "5012",
					Value: "5012_value",
				},
			},
		},
		AssignedToBsnrId: s.doctor.BsnrId,
	})
	require.Nil(s.T(), err)

	timelineWithError, _ := timelineService.FindById(ctx, *result.Id)

	require.NotNil(s.T(), timelineWithError.Payload)
	require.NotNil(s.T(), timelineWithError.Payload.Errors)
	hasInvalidPseudoGNR := slice.Any(*timelineWithError.Payload.Errors, func(eie *patient_encounter.EncounterItemError) bool {
		return eie.ErrorCode == string(error_code.ErrorCode_Validation_Missing_Pseudo_GNR)
	})
	require.True(s.T(), hasInvalidPseudoGNR)
}

func (s *TimelineTestSuite) Test_Should_Invalid_Pseudo_GNR() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	fakeInsuranceId := uuid.New()
	age75 := time.Now().AddDate(-75, 0, 0).UnixMilli()
	bsnr := "190000"
	var patientGetByHashIdFunc validation_context.PatientGetByIdFunc = func(ctx *titan.Context, uuid uuid.UUID) (*patient.PatientProfile, error) {
		return &patient.PatientProfile{
			Id:          &s.patient.PatientId,
			DateOfBirth: age75,
			PatientInfo: &patient_profile_common.PatientInfo{
				InsuranceInfos: []patient_profile_common.InsuranceInfo{
					{
						Id:                   fakeInsuranceId,
						InsuranceCompanyId:   "61125",
						InsuranceCompanyName: "AOK Baden-Württemberg Hauptverwaltung",
						IkNumber:             *********,
						FeeCatalogue:         "1",
					},
				},
			},
		}, nil
	}

	var scheinFindByIdsFunc validation_context.ScheinFindByIdsFunc = func(ctx *titan.Context, ids []uuid.UUID) ([]schein_repo.ScheinRepo, error) {
		return []schein_repo.ScheinRepo{
			{
				Id: s.scheinFake.Id,
				Schein: schein_common.Schein{
					InsuranceId:     fakeInsuranceId,
					ScheinMainGroup: "KV",
				},
				DoctorId:         s.doctor.EmployeeId,
				AssignedToBsnrId: s.doctor.BsnrId,
			},
		}, nil
	}

	var employeeProfileFinderFunc validation_context.EmployeeProfileFinderFunc = func(ctx *titan.Context, filter any, opts ...*options.FindOptions) ([]employee.EmployeeProfile, error) {
		return []employee.EmployeeProfile{
			{
				Id:      &s.doctor.EmployeeId,
				Bsnr:    bsnr,
				BsnrId:  s.doctor.BsnrId,
				BsnrIds: s.doctor.BsnrIds,
				Bsnrs:   s.doctor.Bsnrs,
			},
		}, nil
	}

	var bsnrGetByIdsFunc validation_context.BsnrGetByIdsFunc = func(ctx *titan.Context, ids []uuid.UUID) ([]bsnr_repo.BSNR, error) {
		return []bsnr_repo.BSNR{
			{
				Id:   s.doctor.BsnrId,
				Code: bsnr,
			},
		}, nil
	}

	store := submodule.CreateScope()
	store.InitValue(validation_context.PatientGetByHashIdMod, patientGetByHashIdFunc)
	store.InitValue(validation_context.ScheinFindByIdsMod, scheinFindByIdsFunc)
	store.InitValue(validation_context.EmployeeProfileFinderMod, employeeProfileFinderFunc)
	store.InitValue(validation_context.BsnrGetByIdsMod, bsnrGetByIdsFunc)

	timelineService, err := timeline_service.TimelineServiceEncounterMod.SafeResolveWith(store)
	require.Nil(s.T(), err)
	result, err := timelineService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
		BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
		PatientId:         s.patient.PatientId,
		TreatmentDoctorId: s.doctor.EmployeeId,
		ScheinIds: []uuid.UUID{
			*s.scheinFake.Id,
		},
		Payload: patient_encounter.EncounterServiceTimeline{
			Code: "03000",
			AdditionalInfos: &[]*patient_encounter.AdditionalInfoParent{
				{
					FK:    "5012",
					Value: "5012_value",
				},
				{
					FK:    "5200",
					Value: "5200_value",
				},
			},
		},
	})
	require.Nil(s.T(), err)

	timelineWithError, _ := timelineService.FindById(ctx, *result.Id)

	require.NotNil(s.T(), timelineWithError.Payload)
	require.NotNil(s.T(), timelineWithError.Payload.Errors)
	hasInvalidPseudoGNR := slice.Any(*timelineWithError.Payload.Errors, func(eie *patient_encounter.EncounterItemError) bool {
		return eie.ErrorCode == string(error_code.ErrorCode_Validation_Invalid_Pseudo_GNR)
	})
	require.True(s.T(), hasInvalidPseudoGNR)
}

func (s *TimelineTestSuite) Test_Should_Valid_Pseudo_GNR() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	fakeInsuranceId := uuid.New()
	age75 := time.Now().AddDate(-75, 0, 0).UnixMilli()

	var patientGetByHashIdFunc validation_context.PatientGetByIdFunc = func(ctx *titan.Context, uuid uuid.UUID) (*patient.PatientProfile, error) {
		return &patient.PatientProfile{
			Id:          &s.patient.PatientId,
			DateOfBirth: age75,
			PatientInfo: &patient_profile_common.PatientInfo{
				InsuranceInfos: []patient_profile_common.InsuranceInfo{
					{
						Id:                   fakeInsuranceId,
						InsuranceCompanyId:   "61125",
						InsuranceCompanyName: "AOK Baden-Württemberg Hauptverwaltung",
						IkNumber:             *********,
						FeeCatalogue:         "1",
					},
				},
			},
		}, nil
	}

	var scheinFindByIdsFunc validation_context.ScheinFindByIdsFunc = func(ctx *titan.Context, ids []uuid.UUID) ([]schein_repo.ScheinRepo, error) {
		return []schein_repo.ScheinRepo{
			{
				Id: s.scheinFake.Id,
				Schein: schein_common.Schein{
					InsuranceId:     fakeInsuranceId,
					ScheinMainGroup: "KV",
				},
				DoctorId:         s.doctor.EmployeeId,
				AssignedToBsnrId: s.doctor.BsnrId,
			},
		}, nil
	}

	var employeeProfileFinderFunc validation_context.EmployeeProfileFinderFunc = func(ctx *titan.Context, filter any, opts ...*options.FindOptions) ([]employee.EmployeeProfile, error) {
		return []employee.EmployeeProfile{
			{
				Id:      &s.doctor.EmployeeId,
				Bsnr:    "190000",
				BsnrId:  s.doctor.BsnrId,
				Bsnrs:   s.doctor.Bsnrs,
				BsnrIds: s.doctor.BsnrIds,
			},
		}, nil
	}

	store := submodule.CreateScope()
	store.InitValue(validation_context.PatientGetByHashIdMod, patientGetByHashIdFunc)
	store.InitValue(validation_context.ScheinFindByIdsMod, scheinFindByIdsFunc)
	store.InitValue(validation_context.EmployeeProfileFinderMod, employeeProfileFinderFunc)

	timelineService, err := timeline_service.TimelineServiceEncounterMod.SafeResolveWith(store)

	require.Nil(s.T(), err)
	result, err := timelineService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
		BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
		PatientId:         s.patient.PatientId,
		TreatmentDoctorId: s.doctor.EmployeeId,
		ScheinIds: []uuid.UUID{
			*s.scheinFake.Id,
		},
		Payload: patient_encounter.EncounterServiceTimeline{
			Code: "03000",
			AdditionalInfos: &[]*patient_encounter.AdditionalInfoParent{
				{
					FK:    "5012",
					Value: "5012_value",
				},
				{
					FK:    "5200",
					Value: "90613",
				},
			},
		},
	})
	require.Nil(s.T(), err)

	timelineWithError, _ := timelineService.FindById(ctx, *result.Id)

	require.NotNil(s.T(), timelineWithError.Payload)
	require.NotNil(s.T(), timelineWithError.Payload.Errors)
	hasInvalidPseudoGNR := slice.Any(*timelineWithError.Payload.Errors, func(eie *patient_encounter.EncounterItemError) bool {
		return eie.ErrorCode == string(error_code.ErrorCode_Validation_Invalid_Pseudo_GNR) || eie.ErrorCode == string(error_code.ErrorCode_Validation_Missing_Pseudo_GNR)
	})
	require.True(s.T(), !hasInvalidPseudoGNR)
}

func (s *TimelineTestSuite) Test_Should_InValid_Time_Of_Treatment_Only_First_Item() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	fakeInsuranceId := uuid.New()
	age75 := time.Now().AddDate(-75, 0, 0).UnixMilli()

	var patientGetByHashIdFunc validation_context.PatientGetByIdFunc = func(ctx *titan.Context, uuid uuid.UUID) (*patient.PatientProfile, error) {
		return &patient.PatientProfile{
			Id:          &s.patient.PatientId,
			DateOfBirth: age75,
			PatientInfo: &patient_profile_common.PatientInfo{
				InsuranceInfos: []patient_profile_common.InsuranceInfo{
					{
						Id:                   fakeInsuranceId,
						InsuranceCompanyId:   "61125",
						InsuranceCompanyName: "AOK Baden-Württemberg Hauptverwaltung",
						IkNumber:             *********,
						FeeCatalogue:         "1",
					},
				},
			},
		}, nil
	}

	var scheinFindByIdsFunc validation_context.ScheinFindByIdsFunc = func(ctx *titan.Context, ids []uuid.UUID) ([]schein_repo.ScheinRepo, error) {
		return []schein_repo.ScheinRepo{
			{
				Id: s.scheinFake.Id,
				Schein: schein_common.Schein{
					InsuranceId:     fakeInsuranceId,
					ScheinMainGroup: "KV",
				},
				DoctorId: s.doctor.EmployeeId,
			},
		}, nil
	}

	var employeeProfileFinderFunc validation_context.EmployeeProfileFinderFunc = func(ctx *titan.Context, filter any, opts ...*options.FindOptions) ([]employee.EmployeeProfile, error) {
		return []employee.EmployeeProfile{
			{
				Id:   &s.doctor.EmployeeId,
				Bsnr: "710000",
			},
		}, nil
	}

	store := submodule.CreateScope()
	store.InitValue(validation_context.PatientGetByHashIdMod, patientGetByHashIdFunc)
	store.InitValue(validation_context.ScheinFindByIdsMod, scheinFindByIdsFunc)
	store.InitValue(validation_context.EmployeeProfileFinderMod, employeeProfileFinderFunc)

	timelineService, err := timeline_service.TimelineServiceEncounterMod.SafeResolve()
	require.Nil(s.T(), err)
	result1, err := timelineService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
		BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
		PatientId:         s.patient.PatientId,
		TreatmentDoctorId: s.doctor.EmployeeId,
		ScheinIds: []uuid.UUID{
			*s.scheinFake.Id,
		},
		Payload: patient_encounter.EncounterServiceTimeline{
			Code: "03000",
			AdditionalInfos: &[]*patient_encounter.AdditionalInfoParent{
				{
					FK:    "5012",
					Value: "5012_value",
				},
				{
					FK:    "5200",
					Value: "90613",
				},
			},
		},
	})
	require.NoError(t, err)

	result2, err := timelineService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
		BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
		PatientId:         s.patient.PatientId,
		TreatmentDoctorId: s.doctor.EmployeeId,
		ScheinIds: []uuid.UUID{
			*s.scheinFake.Id,
		},
		Payload: patient_encounter.EncounterServiceTimeline{
			Code: "03000",
			AdditionalInfos: &[]*patient_encounter.AdditionalInfoParent{
				{
					FK:    "5012",
					Value: "5012_value",
				},
				{
					FK:    "5200",
					Value: "90613",
				},
				{
					FK:    "5900",
					Value: "1212",
				},
				// {
				// 	FK:    "5006",
				// 	Value: "1111",
				// },
			},
		},
	})
	require.Nil(s.T(), err)

	timelineWithError1, _ := timelineService.FindById(ctx, *result1.Id)
	timelineWithError2, _ := timelineService.FindById(ctx, *result2.Id)

	require.NotNil(s.T(), timelineWithError1.Payload)
	require.NotNil(s.T(), timelineWithError1.Payload.Errors)

	hasInvalidTimelineOfTreatment := slice.Any(*timelineWithError1.Payload.Errors, func(eie *patient_encounter.EncounterItemError) bool {
		return eie.ErrorCode == string(error_code.ErrorCode_Validation_Missing_Treatment_Time)
	})
	require.True(s.T(), hasInvalidTimelineOfTreatment)

	hasInvalidTimelineOfTreatment = slice.Any(*timelineWithError2.Payload.Errors, func(eie *patient_encounter.EncounterItemError) bool {
		return eie.ErrorCode == string(error_code.ErrorCode_Validation_Missing_Treatment_Time)
	})
	require.False(s.T(), hasInvalidTimelineOfTreatment)
}

func (s *TimelineTestSuite) Test_Should_InValid_Time_Of_Treatment_With_Must_Not_Present_Type() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	fakeInsuranceId := uuid.New()
	age75 := time.Now().AddDate(-75, 0, 0).UnixMilli()

	var patientGetByHashIdFunc validation_context.PatientGetByIdFunc = func(ctx *titan.Context, uuid uuid.UUID) (*patient.PatientProfile, error) {
		return &patient.PatientProfile{
			Id:          &s.patient2.PatientId,
			DateOfBirth: age75,
			PatientInfo: &patient_profile_common.PatientInfo{
				InsuranceInfos: []patient_profile_common.InsuranceInfo{
					{
						Id:                   fakeInsuranceId,
						InsuranceCompanyId:   "61125",
						InsuranceCompanyName: "AOK Baden-Württemberg Hauptverwaltung",
						IkNumber:             *********,
						FeeCatalogue:         "1",
					},
				},
			},
		}, nil
	}

	var scheinFindByIdsFunc validation_context.ScheinFindByIdsFunc = func(ctx *titan.Context, ids []uuid.UUID) ([]schein_repo.ScheinRepo, error) {
		return []schein_repo.ScheinRepo{
			{
				Id: s.scheinFake.Id,
				Schein: schein_common.Schein{
					InsuranceId:     fakeInsuranceId,
					ScheinMainGroup: "KV",
				},
				DoctorId: s.doctor.EmployeeId,
			},
		}, nil
	}

	var employeeProfileFinderFunc validation_context.EmployeeProfileFinderFunc = func(ctx *titan.Context, filter any, opts ...*options.FindOptions) ([]employee.EmployeeProfile, error) {
		return []employee.EmployeeProfile{
			{
				Id:   &s.doctor.EmployeeId,
				Bsnr: "720000",
			},
		}, nil
	}

	store := submodule.CreateScope()
	store.InitValue(validation_context.PatientGetByHashIdMod, patientGetByHashIdFunc)
	store.InitValue(validation_context.ScheinFindByIdsMod, scheinFindByIdsFunc)
	store.InitValue(validation_context.EmployeeProfileFinderMod, employeeProfileFinderFunc)

	timelineService, err := timeline_service.TimelineServiceEncounterMod.SafeResolve()
	require.Nil(s.T(), err)

	timelineCreateRes, err := timelineService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
		BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
		PatientId:         s.patient2.PatientId,
		TreatmentDoctorId: s.doctor.EmployeeId,
		ScheinIds: []uuid.UUID{
			*s.scheinFake.Id,
		},
		Payload: patient_encounter.EncounterServiceTimeline{
			Code: "03000",
			AdditionalInfos: &[]*patient_encounter.AdditionalInfoParent{
				{
					FK:    "5012",
					Value: "5012_value",
				},
				{
					FK:    "5200",
					Value: "90613",
				},
				{
					FK:    "5006",
					Value: "1111",
				},
			},
		},
	})

	require.Nil(s.T(), err)

	timelineError, _ := timelineService.FindById(ctx, *timelineCreateRes.Id)
	hasInvalidTimelineOfTreatmentMustNotPresent := slice.Any(*timelineError.Payload.Errors, func(eie *patient_encounter.EncounterItemError) bool {
		return eie.ErrorCode == string(error_code.ErrorCode_Validation_Must_Not_Present_Treatment_Time)
	})
	require.False(s.T(), hasInvalidTimelineOfTreatmentMustNotPresent)
}

func (s *TimelineTestSuite) Test_Should_Find_Terminal_Psychotherapy() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	psychotherapyService, err := timeline_service.TimelineServicePsychotherapyMod.SafeResolve()
	require.Nil(s.T(), err)
	terminalId := uuid.New()
	psychotherapyService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterPsychotherapy]{
		BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
		PatientId:         s.patient.PatientId,
		TreatmentDoctorId: s.doctor.EmployeeId,
		ScheinIds: []uuid.UUID{
			*s.scheinFake.Id,
		},
		Payload: patient_encounter.EncounterPsychotherapy{
			Entries: map[string]*patient_encounter.ServiceCodeApproval{
				"03000": {
					TerminalId: &terminalId,
				},
				"03001": {
					TerminalId: util.NewUUID(),
				},
			},
		},
	})
	result, err := psychotherapyService.FindTerminalPsychotherapyApprovalById(ctx, s.patient.PatientId, terminalId)
	require.Nil(s.T(), err)
	require.NotNil(s.T(), result)
	require.Equal(s.T(), *result.Payload.Entries["03000"].TerminalId, terminalId)
}

func (s *TimelineTestSuite) Test_Should_Mark_Treatment_Relevant() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	timelineDiagnosisService, err := timeline_service.TimelineServiceDiagnosisMod.SafeResolve()
	require.Nil(t, err)
	t.Run("Should_Not_Mark_Treatment_Relevant_When_Command_Not_AD", func(t *testing.T) {
		timelineCreateResponse, _ := timelineDiagnosisService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Code:    "ABCD",
				Command: "CD",
			},
		})

		timelineDiagnosisService.MarkTreatmentRelevant(ctx, *timelineCreateResponse.Id)
		timeline, _ := timelineDiagnosisService.FindById(ctx, *timelineCreateResponse.Id)
		require.False(t, timeline.Payload.MarkedTreatmentRelevant)
	})

	t.Run("Should_Mark_Treatment_Relevant_When_Command_AD", func(t *testing.T) {
		timelineCreateResponse1, _ := timelineDiagnosisService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Code:    "ABCD",
				Command: "AD",
			},
		})

		timelineCreateResponse2, _ := timelineDiagnosisService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Code:    "ABCD",
				Command: "AD",
			},
		})

		timelineDiagnosisService.MarkTreatmentRelevant(ctx, *timelineCreateResponse1.Id)
		timelines, _ := timelineDiagnosisService.FindByIds(ctx, []uuid.UUID{*timelineCreateResponse1.Id, *timelineCreateResponse2.Id})
		require.True(t, timelines[0].Payload.MarkedTreatmentRelevant)
		require.True(t, timelines[1].Payload.MarkedTreatmentRelevant)
	})
}

func (s *TimelineTestSuite) Test_Should_Update_Encounter_Case_Service_Code() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	timelineService, err := timeline_service.TimelineServiceEncounterMod.SafeResolve()
	require.Nil(t, err)
	t.Run("Should_Update_Encounter_Case", func(t *testing.T) {
		timelineCreateResponse, _ := timelineService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			Payload: patient_encounter.EncounterServiceTimeline{
				Code:    "100",
				Command: "Z",
			},
			Quarter:       2,
			Year:          2024,
			EncounterCase: util.NewPointer(patient_encounter.NOT),
		})
		timelineCreateResponse2, _ := timelineService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			Payload: patient_encounter.EncounterServiceTimeline{
				Code:    "101",
				Command: "Z",
			},
			Quarter:       2,
			Year:          2024,
			EncounterCase: util.NewPointer(patient_encounter.AB),
		})

		timelineService.UpdateEncounterCaseForServiceEntries(ctx, *timelineCreateResponse.Id)
		timeline, _ := timelineService.FindById(ctx, *timelineCreateResponse2.Id)
		require.Equal(t, timeline.EncounterCase, timelineCreateResponse.EncounterCase)
	})
}

// TODO: add timeline restore tests
func (s *TimelineTestSuite) Test_Should_Restore_Timeline_Correctly() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	timelineDiagnosisSvc, err := timeline_service.TimelineServiceDiagnosisMod.SafeResolve()
	require.Nil(t, err)

	t.Run("Restore_When_Remove", func(t *testing.T) {
		creatingTimeline, err := timelineDiagnosisSvc.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Code:      "E11.30",
				Certainty: util.NewPointer(patient_encounter.G),
				Command:   "AD",
			},
		})
		require.Nil(t, err)
		err = timelineDiagnosisSvc.Remove(ctx, *creatingTimeline.Id, false)
		require.Nil(t, err)
		removedTimeline, err := timelineDiagnosisSvc.FindById(ctx, *creatingTimeline.Id)
		require.Nil(t, err)
		require.Nil(t, removedTimeline, "Should be deleted")

		// NOTE: perform restore on creating event
		_, err = timelineDiagnosisSvc.RestoreFromLogId(ctx, *creatingTimeline.RecentAuditLogs[0].AuditLogId)
		require.Nil(t, err)
		restoredTimeline, err := timelineDiagnosisSvc.FindById(ctx, *creatingTimeline.Id)
		require.Nil(t, err)
		require.Equal(t, restoredTimeline.Id.String(), creatingTimeline.Id.String(), "Should restore timeline entry correctly")
	})

	t.Run("Restore_When_Update", func(t *testing.T) {
		expectedCode := "E11.30"
		updatedCode := "G46.0"
		creatingTimeline, err := timelineDiagnosisSvc.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Code:      expectedCode,
				Certainty: util.NewPointer(patient_encounter.G),
				Command:   "AD",
			},
		})
		require.Nil(t, err)

		creatingTimeline.Payload.Code = updatedCode
		_, err = timelineDiagnosisSvc.Edit(ctx, *creatingTimeline)
		require.Nil(t, err)
		updatedTimeline, err := timelineDiagnosisSvc.FindById(ctx, *creatingTimeline.Id)
		require.Nil(t, err)
		require.Equal(t, updatedCode, updatedTimeline.Payload.Code, "Should be updated")

		// NOTE: perform restore on creating event
		_, err = timelineDiagnosisSvc.RestoreFromLogId(ctx, *creatingTimeline.RecentAuditLogs[0].AuditLogId)
		require.Nil(t, err)
		restoredTimeline, err := timelineDiagnosisSvc.FindById(ctx, *creatingTimeline.Id)
		require.Nil(t, err)
		require.Equal(t, expectedCode, restoredTimeline.Payload.Code, "Should restore timeline entry correctly")
	})
}

func (s *TimelineTestSuite) Test_Should_Trigger_Validation_After_Restore() {
	t := s.T()

	t.Run("Trigger_Timeline_Validation", func(t *testing.T) {
		ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
		timelineDiagnosisSvc, err := timeline_service.TimelineServiceDiagnosisMod.SafeResolve()
		require.Nil(t, err)

		invalidCode := "Invalid_Code"
		G46_0, err := timelineDiagnosisSvc.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Code:      "G46.0",
				Certainty: util.NewPointer(patient_encounter.G),
				Command:   "AD",
				Scheins: util.NewPointer([]*api_common.ScheinWithMainGroup{
					{
						ScheinId: s.scheinFake.Id,
						Group:    api_common.KV,
					},
				}),
			},
		})
		require.Nil(t, err)

		G46_0, err = timelineDiagnosisSvc.FindById(ctx, *G46_0.Id)
		require.Nil(t, err)
		hasError := slice.Any(util.GetPointerValue(G46_0.Payload.Errors), func(e *patient_encounter.EncounterItemError) bool {
			return error_code.ErrorCode(e.ErrorCode) == error_code.ErrorCode_Validation_ICD_Code_Not_In_Master_Data
		})
		require.False(t, hasError)

		G46_0.Payload.Code = invalidCode
		_, err = timelineDiagnosisSvc.Edit(ctx, *G46_0)
		require.Nil(t, err)
		G46_0, err = timelineDiagnosisSvc.FindById(ctx, *G46_0.Id)
		require.Nil(t, err)
		hasError = slice.Any(util.GetPointerValue(G46_0.Payload.Errors), func(e *patient_encounter.EncounterItemError) bool {
			return error_code.ErrorCode(e.ErrorCode) == error_code.ErrorCode_Validation_ICD_Code_Not_In_Master_Data
		})
		require.True(t, hasError)

		// NOTE: perform restore on creating event
		_, err = timelineDiagnosisSvc.RestoreFromLogId(ctx, *G46_0.RecentAuditLogs[0].AuditLogId)
		require.Nil(t, err)
		G46_0, err = timelineDiagnosisSvc.FindById(ctx, *G46_0.Id)
		require.Nil(t, err)
		hasError = slice.Any(util.GetPointerValue(G46_0.Payload.Errors), func(e *patient_encounter.EncounterItemError) bool {
			return error_code.ErrorCode(e.ErrorCode) == error_code.ErrorCode_Validation_ICD_Code_Not_In_Master_Data
		})
		require.False(t, hasError)
	})
	t.Run("Trigger_Coding_Rule", func(t *testing.T) {
		// NOTE: make icd suggestion with E11.30 (require H28.0)
		ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[1].LoginAsDoctor(t, s.doctor)
		timelineDiagnosisSvc, err := timeline_service.TimelineServiceDiagnosisMod.SafeResolve()
		require.Nil(t, err)

		E11_30, err := timelineDiagnosisSvc.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Code:      "E11.30",
				Certainty: util.NewPointer(patient_encounter.G),
				Command:   "AD",
				Scheins: util.NewPointer([]*api_common.ScheinWithMainGroup{
					{
						ScheinId: s.scheinFake.Id,
						Group:    api_common.KV,
					},
				}),
			},
		})
		require.Nil(t, err)

		// NOTE: fix coding rule with H28.0G
		H28_0, err := timelineDiagnosisSvc.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Code:      "H28.0",
				Certainty: util.NewPointer(patient_encounter.G),
				Command:   "AD",
				Scheins: util.NewPointer([]*api_common.ScheinWithMainGroup{
					{
						ScheinId: s.scheinFake.Id,
						Group:    api_common.KV,
					},
				}),
			},
		})
		require.Nil(t, err)

		fixed_E11_30, err := timelineDiagnosisSvc.FindById(ctx, *E11_30.Id)
		require.Nil(t, err)
		require.Zero(t, fixed_E11_30.Payload.DiagnoseSuggestions, "Should not have any suggestion")

		err = timelineDiagnosisSvc.Remove(ctx, *H28_0.Id, false)
		require.Nil(t, err)

		E11_30, err = timelineDiagnosisSvc.FindById(ctx, *E11_30.Id)
		require.Nil(t, err)
		require.Equal(t, 1, len(util.GetPointerValue(E11_30.Payload.DiagnoseSuggestions)), "Should have 1")

		// NOTE: perform restore on creating event
		_, err = timelineDiagnosisSvc.RestoreFromLogId(ctx, *H28_0.RecentAuditLogs[0].AuditLogId)
		require.Nil(t, err)
		E11_30, err = timelineDiagnosisSvc.FindById(ctx, *E11_30.Id)
		require.Nil(t, err)
		require.Zero(t, E11_30.Payload.DiagnoseSuggestions, "Should fix coding rule")
	})
}

func (s *TimelineTestSuite) Test_Should_Enforce_Selected_Date() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	timelineDiagnosisService, err := timeline_service.TimelineServiceDiagnosisMod.SafeResolve()
	require.Nil(s.T(), err)

	scheinYearQuarter := util.YearQuarter{
		Year:    2023,
		Quarter: 3,
	}
	res := timelineDiagnosisService.EnforceSelectedDateDiagnose(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
		BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
		PatientId:         s.patient.PatientId,
		TreatmentDoctorId: s.doctor.EmployeeId,
		ScheinIds: []uuid.UUID{
			*s.scheinFake.Id,
		},
		Payload: patient_encounter.EncounterDiagnoseTimeline{
			Code:    "E20.1",
			Command: "D",
		},
		Quarter:      4,
		Year:         2023,
		SelectedDate: util.ConvertMillisecondsToTime(util.NowUnixMillis(ctx)),
	}, scheinYearQuarter)
	require.NotNil(s.T(), res)

	firstDateOfQuarter := time.Date(int(scheinYearQuarter.Year), time.Month(scheinYearQuarter.Quarter)*3-2, 1, 0, 0, 0, 0, ctx.RequestTimeZone())
	expectedEndTime := util.EndOfQuarter(firstDateOfQuarter).AddDate(0, 0, -1).Add(time.Minute)
	require.Equal(s.T(), res.SelectedDate, expectedEndTime)
}

func (s *TimelineTestSuite) Test_GetLastDocumentedQuarter() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	timelineDiagnosisService, err := timeline_service.TimelineServiceDiagnosisMod.SafeResolve()
	require.Nil(s.T(), err)

	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	t.Run("Should_HaveLastDocumentedQuarter_InCurrentYear", func(t *testing.T) {
		icdC12, _ := timelineDiagnosisService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Code:    "C12.0",
				Command: "D",
			},
			SelectedDate:     time.Date(2023, 1, 1, 0, 0, 0, 0, ctx.RequestTimeZone()),
			AssignedToBsnrId: s.doctor.BsnrId,
		})

		res, err := timelineDiagnosisService.GetLastDocumentedQuarter(ctx, &timeline_service.GetLastDocumentedQuarterRequest{
			PatientId:          s.patient.PatientId,
			TimelineEntityType: util.NewPointer(common.TimelineEntityType_Diagnose),
			Year:               yearQuarter.Year,
			Quarter:            yearQuarter.Quarter,
		})
		require.Nil(s.T(), err)
		require.NotNil(s.T(), res)
		require.LessOrEqual(s.T(), icdC12.Quarter, int(res.Quarter))
		require.LessOrEqual(s.T(), icdC12.Year, int(res.Year))
	})

	t.Run("Should_HaveLastDocumentedQuarter_InPreviousYear", func(t *testing.T) {
		icdJ04, _ := timelineDiagnosisService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Code:    "J04.0",
				Command: "D",
			},
			SelectedDate: time.Date(2022, 11, 1, 0, 0, 0, 0, ctx.RequestTimeZone()),
		})

		res, err := timelineDiagnosisService.GetLastDocumentedQuarter(ctx, &timeline_service.GetLastDocumentedQuarterRequest{
			PatientId:          s.patient.PatientId,
			TimelineEntityType: util.NewPointer(common.TimelineEntityType_Diagnose),
			Year:               yearQuarter.Year,
			Quarter:            yearQuarter.Quarter,
		})
		require.Nil(s.T(), err)
		require.NotNil(s.T(), res)
		require.GreaterOrEqual(s.T(), icdJ04.Quarter, int(res.Quarter))
		require.LessOrEqual(s.T(), icdJ04.Year, int(res.Year))
	})
}

func (s *TimelineTestSuite) Test_DocumentGoaService_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	firstGoaServiceCode, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
		TimelineModel: common.TimelineModel{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			EncounterGoaService: &patient_encounter.EncounterGoaService{
				Command:     "Z",
				Code:        "1",
				Description: "fake description",
				FreeText:    "fake free text",
				Factor:      3,
				Quantity:    1,
			},
		},
	})
	require.NoError(t, err)
	require.NotNil(t, firstGoaServiceCode)
	require.Equal(t, "1", firstGoaServiceCode.TimelineModel.EncounterGoaService.Code)
	require.Equal(t, common.TimelineEntityType_Service_GOA, util.GetPointerValue(firstGoaServiceCode.TimelineModel.Type))

	// document another GOA service with service code is in the exclusion list of previous GOA service
	secondGoaServiceCode, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
		TimelineModel: common.TimelineModel{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			EncounterGoaService: &patient_encounter.EncounterGoaService{
				Code:        "2",
				Description: "fake description",
				FreeText:    "fake free text",
				Factor:      3,
				Quantity:    1,
				Command:     "Z",
			},
		},
	})
	require.NoError(t, err)
	require.NotNil(t, secondGoaServiceCode)
	require.Equal(t, "2", secondGoaServiceCode.TimelineModel.EncounterGoaService.Code)
	require.Equal(t, common.TimelineEntityType_Service_GOA, util.GetPointerValue(secondGoaServiceCode.TimelineModel.Type))

	// beacause second GOA service code is in the exclusion list of first GOA service
	// check validation timeline of first GOA service , it should have error after document second GOA service
	res, err := s.timelineApp.GetById(ctx, timeline.GetByIdRequest{
		TimelineId: *firstGoaServiceCode.TimelineModel.Id,
	})
	require.NoError(t, err)
	require.NotNil(t, res)

	errorCodes := slice.Map(*res.TimelineModel.EncounterGoaService.Errors, func(e *patient_encounter.EncounterItemError) string {
		return e.ErrorCode
	})
	require.Contains(t, errorCodes, string(error_code.ErrorCode_GoaService_ExcludedCode))
}

func (s *TimelineTestSuite) Test_Should_Have_Warning_Z017_With_Certainty_Not_G() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	timelineDiagnosisSvc, err := timeline_service.TimelineServiceDiagnosisMod.SafeResolve()
	require.Nil(t, err)

	res, err := timelineDiagnosisSvc.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
		BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
		PatientId:         s.patient.PatientId,
		TreatmentDoctorId: s.doctor.EmployeeId,
		ScheinIds: []uuid.UUID{
			*s.scheinFake.Id,
		},
		Payload: patient_encounter.EncounterDiagnoseTimeline{
			Code:      "Z01.7",
			Certainty: util.NewPointer(patient_encounter.A),
			Command:   "D",
			Scheins: util.NewPointer([]*api_common.ScheinWithMainGroup{
				{
					ScheinId: s.scheinFake.Id,
					Group:    api_common.KV,
				},
			}),
		},
	})
	require.Nil(t, err)

	diagnoseTimeline, err := timelineDiagnosisSvc.FindById(ctx, *res.Id)
	require.Nil(t, err)
	hasWarning := slice.Any(util.GetPointerValue(diagnoseTimeline.Payload.Errors), func(e *patient_encounter.EncounterItemError) bool {
		return strings.ToLower(string(e.Type)) == string(patient_encounter.EncounterItemErrorType_warning) && e.Message == constant.Z017_WARNING_MESSAGE
	})
	require.True(t, hasWarning)
}

func (s *TimelineTestSuite) Test_Should_Have_Warning_Primary_Diagnose() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	timelineDiagnosisSvc, err := timeline_service.TimelineServiceDiagnosisMod.SafeResolve()
	require.Nil(t, err)
	hasDismiss := func(e map[string]string) bool {
		_, ok := e[string(validation_timeline.IsShowDismiss)]
		return ok
	}
	t.Run("Test_Should_Have_Warning_Primary_Diagnose", func(t *testing.T) {
		resG8260, err := timelineDiagnosisSvc.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Code:      "G82.60",
				Certainty: util.NewPointer(patient_encounter.A),
				Command:   "D",
				Scheins: util.NewPointer([]*api_common.ScheinWithMainGroup{
					{
						ScheinId: s.scheinFake.Id,
						Group:    api_common.KV,
					},
				}),
			},
		})
		require.Nil(t, err)

		diagnoseTimeline, err := timelineDiagnosisSvc.FindById(ctx, *resG8260.Id)
		require.Nil(t, err)
		hasWarning := slice.Any(util.GetPointerValue(diagnoseTimeline.Payload.Errors), func(e *patient_encounter.EncounterItemError) bool {
			return strings.ToLower(string(e.Type)) == string(patient_encounter.EncounterItemErrorType_warning) &&
				e.ErrorCode == string(error_code.ErrorCode_Validation_MessageNeedPrimaryCode) &&
				hasDismiss(e.MetaData)
		})
		require.True(t, hasWarning)
	})

	t.Run("Test_Should_Have_No_Warning_Primary_Diagnose", func(t *testing.T) {
		_, err := timelineDiagnosisSvc.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Code:      "H17.8",
				Certainty: util.NewPointer(patient_encounter.A),
				Command:   "D",
				Scheins: util.NewPointer([]*api_common.ScheinWithMainGroup{
					{
						ScheinId: s.scheinFake.Id,
						Group:    api_common.KV,
					},
				}),
			},
		})
		require.Nil(t, err)

		resG8260, err := timelineDiagnosisSvc.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
			BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
			PatientId:         s.patient.PatientId,
			TreatmentDoctorId: s.doctor.EmployeeId,
			ScheinIds: []uuid.UUID{
				*s.scheinFake.Id,
			},
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Code:      "G82.60",
				Certainty: util.NewPointer(patient_encounter.A),
				Command:   "D",
				Scheins: util.NewPointer([]*api_common.ScheinWithMainGroup{
					{
						ScheinId: s.scheinFake.Id,
						Group:    api_common.KV,
					},
				}),
			},
		})
		require.Nil(t, err)

		diagnoseTimeline, err := timelineDiagnosisSvc.FindById(ctx, *resG8260.Id)
		require.Nil(t, err)
		hasWarning := slice.Any(util.GetPointerValue(diagnoseTimeline.Payload.Errors), func(e *patient_encounter.EncounterItemError) bool {
			return strings.ToLower(string(e.Type)) ==
				string(patient_encounter.EncounterItemErrorType_warning) &&
				e.ErrorCode == string(error_code.ErrorCode_Validation_MessageNeedPrimaryCode) &&
				hasDismiss(e.MetaData)
		})
		require.False(t, hasWarning)
	})
}

func (s *TimelineTestSuite) Test_Should_Auto_Create_HZV_Service_ABRG921() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	timelineService, err := timeline_service.TimelineServiceEncounterMod.SafeResolve()
	require.NoError(t, err)

	quarter, year := util.GetCurrentQuarter(time.Now())

	created, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
		TimelineModel: common.TimelineModel{
			ContractId:        util.NewPointer("BKK_BOSCH_BW"),
			TreatmentDoctorId: s.doctor.EmployeeId,
			PatientId:         s.patient.PatientId,
			ScheinIds:         []uuid.UUID{*s.scheinFake.Id},
			Quarter:           int32(quarter),
			Year:              int32(year),
			Type:              util.NewPointer(common.TimelineEntityType_Service),
			EncounterCase:     util.NewPointer(patient_encounter.PB),
			EncounterServiceTimeline: &patient_encounter.EncounterServiceTimeline{
				Code:             "NP1",
				AdditionalInfos:  nil,
				ChargeSystemId:   util.NewPointer("BKK_BOSCH_BW"),
				ServiceMainGroup: util.NewPointer(api_common.HZV),
				Scheins: util.NewPointer([]*api_common.ScheinWithMainGroup{{
					ScheinId: s.scheinFake.Id,
					Group:    api_common.HZV,
				}}),
			},
		},
	})
	require.NoError(t, err)

	_, services, _, err := timelineService.GetTimelinesDiagnoseAndServiceByScheinIds(ctx, timeline_service.GetTimelinesDiagnoseAndServiceByScheinIdsRequest{
		PatientWithScheinIds: []timeline_repo.PatientWithScheinIds{
			{
				PatientId: s.patient.PatientId,
				ScheinIds: []uuid.UUID{*s.scheinFake.Id},
			},
		},
	})
	require.NoError(t, err)

	var autoCreated timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]
	hasCorrectServiceCode := function.Do(func() bool {
		for i, s := range services {
			if s.Payload.Code == "80092.2" {
				autoCreated = s
				if services[i-1].Id.String() == created.TimelineModel.Id.String() {
					return true
				}
			}
		}

		return false
	})
	require.True(t, hasCorrectServiceCode)

	// s.timelineApp.Remove(ctx, request timeline.RemoveRequest)
	s.timelineApp.Remove(ctx, timeline.RemoveRequest{
		TimelineId: created.TimelineModel.Id,
	})
	s.timelineApp.Remove(ctx, timeline.RemoveRequest{
		TimelineId: autoCreated.Id,
	})
}

func (s *TimelineTestSuite) Test_Should_Auto_Create_FAV_Service_ABRG921() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	timelineService, err := timeline_service.TimelineServiceEncounterMod.SafeResolve()
	require.NoError(t, err)

	quarter, year := util.GetCurrentQuarter(time.Now())

	created, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
		TimelineModel: common.TimelineModel{
			ContractId:        util.NewPointer("BKK_BOSCH_FA_BW"),
			TreatmentDoctorId: s.doctor.EmployeeId,
			PatientId:         s.patient.PatientId,
			ScheinIds:         []uuid.UUID{*s.scheinFake.Id},
			Quarter:           int32(quarter),
			Year:              int32(year),
			Type:              util.NewPointer(common.TimelineEntityType_Service),
			EncounterCase:     util.NewPointer(patient_encounter.PB),
			EncounterServiceTimeline: &patient_encounter.EncounterServiceTimeline{
				Code:             "NP1",
				AdditionalInfos:  nil,
				ChargeSystemId:   util.NewPointer("BKK_BOSCH_FA_BW"),
				ServiceMainGroup: util.NewPointer(api_common.HZV),
				Scheins: util.NewPointer([]*api_common.ScheinWithMainGroup{{
					ScheinId: s.scheinFake.Id,
					Group:    api_common.FAV,
				}}),
			},
		},
	})
	require.NoError(t, err)

	_, services, _, err := timelineService.GetTimelinesDiagnoseAndServiceByScheinIds(ctx, timeline_service.GetTimelinesDiagnoseAndServiceByScheinIdsRequest{
		PatientWithScheinIds: []timeline_repo.PatientWithScheinIds{
			{
				PatientId: s.patient.PatientId,
				ScheinIds: []uuid.UUID{*s.scheinFake.Id},
			},
		},
	})
	require.NoError(t, err)

	var autoCreated timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]
	hasCorrectServiceCode := function.Do(func() bool {
		for i, s := range services {
			if s.Payload.Code == "80092.2" {
				autoCreated = s
				if services[i-1].Id.String() == created.TimelineModel.Id.String() {
					return true
				}
			}
		}

		return false
	})
	require.True(t, hasCorrectServiceCode)

	s.timelineApp.Remove(ctx, timeline.RemoveRequest{
		TimelineId: created.TimelineModel.Id,
	})
	s.timelineApp.Remove(ctx, timeline.RemoveRequest{
		TimelineId: autoCreated.Id,
	})
}

func (s *TimelineTestSuite) Test_EAB_GetTimelinesDiagnoseAndServiceBySchein() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	timelineService, err := timeline_service.TimelineServiceEncounterMod.SafeResolve()
	require.NoError(t, err)

	quarter, year := util.GetCurrentQuarter(time.Now())

	_, err = s.timelineApp.Create(ctx, timeline.CreateRequest{
		TimelineModel: common.TimelineModel{
			TreatmentDoctorId: s.doctor.EmployeeId,
			PatientId:         s.patient.PatientId,
			ScheinIds:         []uuid.UUID{*s.scheinFake.Id},
			Quarter:           int32(quarter),
			Year:              int32(year),
			Type:              util.NewPointer(common.TimelineEntityType_Service),
			EncounterServiceTimeline: &patient_encounter.EncounterServiceTimeline{
				Code:    "86900",
				Sources: util.NewPointer(patient_encounter.EAB),
			},
		},
	})
	require.NoError(t, err)

	t.Run("Case exclude", func(t *testing.T) {
		_, services, _, err := timelineService.GetTimelinesDiagnoseAndServiceByScheinIds(ctx, timeline_service.GetTimelinesDiagnoseAndServiceByScheinIdsRequest{
			PatientWithScheinIds: []timeline_repo.PatientWithScheinIds{
				{
					PatientId: s.patient.PatientId,
					ScheinIds: []uuid.UUID{*s.scheinFake.Id},
				},
			},
			ExcludeEABServiceCode: util.NewBool(true),
		})
		require.NoError(t, err)
		require.Empty(t, services)
	})

	t.Run("Case include", func(t *testing.T) {
		_, services, _, err := timelineService.GetTimelinesDiagnoseAndServiceByScheinIds(ctx, timeline_service.GetTimelinesDiagnoseAndServiceByScheinIdsRequest{
			PatientWithScheinIds: []timeline_repo.PatientWithScheinIds{
				{
					PatientId: s.patient.PatientId,
					ScheinIds: []uuid.UUID{*s.scheinFake.Id},
				},
			},
		})
		require.NoError(t, err)
		require.Len(t, services, 1)
	})
}

func (s *TimelineTestSuite) Test_Should_Run_Validation_GOA() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	findGOAError := func(res *timeline.GetByIdResponse) bool {
		return slice.Any(*res.TimelineModel.EncounterGoaService.Errors, func(t *patient_encounter.EncounterItemError) bool {
			return t.ErrorCode == string(error_code.ErrorCode_GoaService_GoaNumber_Not_Found)
		})
	}
	t.Run("Should run validation GOA when remove GOA", func(t *testing.T) {
		v, error := s.catalogGoaService.CreateGoaCatalog(ctx, &catalog_goa.CreateGoaCatalogRequest{
			Goa: &catalog_goa_common.GoaCatalog{
				GoaNumber:   "GoaNumber",
				Description: "Description",
				Validity: catalog_utils_common.Validity{
					FromDate: util.NewPointer(util.ConvertTimeToMiliSecond(time.Now())),
				},
			},
		})
		require.NoError(t, error)
		require.NotNil(t, v)

		quarter, year := util.GetCurrentQuarter(time.Now())
		ti, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
			TimelineModel: common.TimelineModel{
				TreatmentDoctorId: s.doctor.EmployeeId,
				PatientId:         s.patient.PatientId,
				ScheinIds:         []uuid.UUID{*s.scheinFake.Id},
				Quarter:           int32(quarter),
				Year:              int32(year),
				Type:              util.NewPointer(common.TimelineEntityType_Service_GOA),
				EncounterGoaService: &patient_encounter.EncounterGoaService{
					Code:        "GoaNumber",
					Description: "Description",
					Factor:      1,
					Quantity:    1,
				},
			},
		})
		require.NoError(t, err)
		err = s.catalogGoaService.DeleteGoaCatalog(ctx, &catalog_goa.DeleteGoaCatalogRequest{
			Id: v.Goa.GoaId,
		})
		require.NoError(t, err)
		time.Sleep(5 * time.Second) // wait for hook running
		res, err := s.timelineApp.GetById(ctx, timeline.GetByIdRequest{
			TimelineId: *ti.TimelineModel.Id,
		})
		require.NoError(t, err)
		require.True(t, findGOAError(res))
	})

	t.Run("Should remove timeline error when create GOA", func(t *testing.T) {
		quarter, year := util.GetCurrentQuarter(time.Now())
		ti, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
			TimelineModel: common.TimelineModel{
				TreatmentDoctorId: s.doctor.EmployeeId,
				PatientId:         s.patient.PatientId,
				ScheinIds:         []uuid.UUID{*s.scheinFake.Id},
				Quarter:           int32(quarter),
				Year:              int32(year),
				Type:              util.NewPointer(common.TimelineEntityType_Service_GOA),
				EncounterGoaService: &patient_encounter.EncounterGoaService{
					Code:        "**********",
					Description: "Description",
					Factor:      1,
					Quantity:    1,
				},
			},
		})
		require.NoError(t, err)
		res, err := s.timelineApp.GetById(ctx, timeline.GetByIdRequest{
			TimelineId: *ti.TimelineModel.Id,
		})
		require.NoError(t, err)
		require.True(t, findGOAError(res))

		v, err := s.catalogGoaService.CreateGoaCatalog(ctx, &catalog_goa.CreateGoaCatalogRequest{
			Goa: &catalog_goa_common.GoaCatalog{
				GoaNumber:   "**********",
				Description: "Description",
				Validity: catalog_utils_common.Validity{
					FromDate: util.NewPointer(util.ConvertTimeToMiliSecond(time.Now())),
				},
			},
		})
		require.NoError(t, err)
		require.NotNil(t, v)
		time.Sleep(5 * time.Second) // wait for hook running
		res, err = s.timelineApp.GetById(ctx, timeline.GetByIdRequest{
			TimelineId: *ti.TimelineModel.Id,
		})
		require.NoError(t, err)
		require.False(t, findGOAError(res))
	})
}

func (s *TimelineTestSuite) Test_GetTimelinesDiagnoseAndServiceByScheinIds() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	timelineService, err := timeline_service.TimelineServiceEncounterMod.SafeResolve()
	require.NoError(t, err)

	quarter, year := util.GetCurrentQuarter(time.Now())

	patientId1 := s.patient.PatientId
	scheinId1 := *s.scheinFake.Id
	patientId2 := s.patient2.PatientId
	scheinId2 := *s.scheinFake2.Id
	_, err = timelineService.CreateMany(ctx, []timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
		{
			TreatmentDoctorId: s.doctor.EmployeeId,
			PatientId:         patientId1,
			ScheinIds:         []uuid.UUID{scheinId1},
			Quarter:           quarter,
			Year:              year,
			Type:              common.TimelineEntityType_Service,
			Payload: patient_encounter.EncounterServiceTimeline{
				Code: "05372",
			},
		},
		{
			TreatmentDoctorId: s.doctor.EmployeeId,
			PatientId:         patientId1,
			ScheinIds:         []uuid.UUID{scheinId1},
			Quarter:           quarter,
			Year:              year,
			Type:              common.TimelineEntityType_Service,
			Payload: patient_encounter.EncounterServiceTimeline{
				Code: "03003",
			},
		},
	})
	require.NoError(t, err)

	timelineDiagnoseService, err := timeline_service.TimelineServiceDiagnosisMod.SafeResolve()
	require.NoError(t, err)

	_, err = timelineDiagnoseService.CreateMany(ctx, []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
		{
			TreatmentDoctorId: s.doctor.EmployeeId,
			PatientId:         patientId2,
			ScheinIds:         []uuid.UUID{scheinId2},
			Quarter:           quarter,
			Year:              year,
			Type:              common.TimelineEntityType_Diagnose,
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Code: "J06.1",
			},
		},
		{
			TreatmentDoctorId: s.doctor.EmployeeId,
			PatientId:         patientId2,
			ScheinIds:         []uuid.UUID{scheinId2},
			Quarter:           quarter,
			Year:              year,
			Type:              common.TimelineEntityType_Diagnose,
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Code: "E10.11",
			},
		},
	})
	require.NoError(t, err)

	t.Run("Should return timeline for billing of patient 1", func(t *testing.T) {
		_, services, _, err := timelineService.GetTimelinesDiagnoseAndServiceByScheinIds(ctx, timeline_service.GetTimelinesDiagnoseAndServiceByScheinIdsRequest{
			PatientWithScheinIds: []timeline_repo.PatientWithScheinIds{
				{
					PatientId: patientId1,
					ScheinIds: []uuid.UUID{scheinId1},
				},
			},
		})
		require.NoError(t, err)
		servicesForBilling := slice.Filter(services, func(s timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]) bool {
			return s.Payload.Code == "05372" || s.Payload.Code == "03003"
		})
		require.Len(t, servicesForBilling, 2)
	})

	t.Run("Should return timeline for billing of patient 2", func(t *testing.T) {
		diagnosis, _, _, err := timelineService.GetTimelinesDiagnoseAndServiceByScheinIds(ctx, timeline_service.GetTimelinesDiagnoseAndServiceByScheinIdsRequest{
			PatientWithScheinIds: []timeline_repo.PatientWithScheinIds{
				{
					PatientId: patientId2,
					ScheinIds: []uuid.UUID{scheinId2},
				},
			},
		})
		require.NoError(t, err)
		diagnosisForBilling := slice.Filter(diagnosis, func(d timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]) bool {
			return d.Payload.Code == "J06.1" || d.Payload.Code == "E10.11"
		})
		require.Len(t, diagnosisForBilling, 2)
	})

	t.Run("Should return timeline for billing of all patients", func(t *testing.T) {
		diagnosis, services, _, err := timelineService.GetTimelinesDiagnoseAndServiceByScheinIds(ctx, timeline_service.GetTimelinesDiagnoseAndServiceByScheinIdsRequest{
			PatientWithScheinIds: []timeline_repo.PatientWithScheinIds{
				{
					PatientId: patientId2,
					ScheinIds: []uuid.UUID{scheinId2},
				},
				{
					PatientId: patientId1,
					ScheinIds: []uuid.UUID{scheinId1},
				},
			},
		})
		require.NoError(t, err)
		require.Greater(t, len(diagnosis), 0)
		require.Greater(t, len(services), 0)
	})
}

func (s *TimelineTestSuite) Test_ValidateDiagnose() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	timelineService, err := timeline_service.TimelineServiceDiagnosisMod.SafeResolve()
	require.NoError(t, err)
	primary := []string{"E10.20", "E13.41"}
	secondary := []string{"D63.0"}
	reserved := []string{"U07.6", "U13.0"}
	notBillable := []string{"J06.-", "D89.-"}
	icdCodeNonExists := []string{"A10.1", "A10.2"}

	t.Run("Should validate diagnose non exist", func(t *testing.T) {
		res, err := timelineService.ValidateDiagnose(ctx, &timeline_service.ValidateDiagnoseRequest{
			IcdCode: icdCodeNonExists,
			TypeCheck: []common.IcdErrorTypeCheck{
				common.NonExist,
			},
		})
		require.NoError(t, err)
		require.Len(t, res, 1)
		require.Len(t, res[0].Codes, 2)
	})

	t.Run("Should validate diagnose non billable", func(t *testing.T) {
		t.Skip("Skip this test because of the non-billable ICD code was removed from masterdata")
		res, err := timelineService.ValidateDiagnose(ctx, &timeline_service.ValidateDiagnoseRequest{
			IcdCode: notBillable,
			TypeCheck: []common.IcdErrorTypeCheck{
				common.NonBilling,
			},
		})
		require.NoError(t, err)
		require.Len(t, res, 1)
		require.Len(t, res[0].Codes, 2)
	})

	t.Run("Should validate diagnose reserved", func(t *testing.T) {
		t.Skip("Skip this test because of the reserved ICD code was removed from masterdata")
		res, err := timelineService.ValidateDiagnose(ctx, &timeline_service.ValidateDiagnoseRequest{
			IcdCode: reserved,
			TypeCheck: []common.IcdErrorTypeCheck{
				common.Reserved,
			},
		})
		require.NoError(t, err)
		require.Len(t, res, 1)
		require.Len(t, res[0].Codes, 2)
	})

	t.Run("Should validate diagnose secondary", func(t *testing.T) {
		res, err := timelineService.ValidateDiagnose(ctx, &timeline_service.ValidateDiagnoseRequest{
			IcdCode: secondary,
			TypeCheck: []common.IcdErrorTypeCheck{
				common.Secondary,
			},
		})
		require.NoError(t, err)
		require.Len(t, res, 1)
		require.Len(t, res[0].Codes, 1)
	})

	t.Run("Should valid with primary and secondary", func(t *testing.T) {
		res, err := timelineService.ValidateDiagnose(ctx, &timeline_service.ValidateDiagnoseRequest{
			IcdCode: append(primary, secondary...),
			TypeCheck: []common.IcdErrorTypeCheck{
				common.Secondary,
			},
		})
		require.NoError(t, err)
		require.Len(t, res, 0)
	})

	t.Run("Should validate with combination error check", func(t *testing.T) {
		t.Skip("Skip this test because of the reserved ICD code and non-billable ICD code were removed from masterdata")
		codes := append(notBillable, append(icdCodeNonExists, reserved...)...)
		res, err := timelineService.ValidateDiagnose(ctx, &timeline_service.ValidateDiagnoseRequest{
			IcdCode: codes,
			TypeCheck: []common.IcdErrorTypeCheck{
				common.NonAble,
				common.NonExist,
				common.Reserved,
				common.NonBilling,
			},
		})
		require.NoError(t, err)
		require.NotNil(t, res)
		require.Len(t, res, 4)
	})
}

func (s *TimelineTestSuite) Test_GetDiagnosisInCurrentDate() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	timelineDiagnoseService, err := timeline_service.TimelineServiceDiagnosisMod.SafeResolve()
	require.NoError(t, err)

	date := time.Date(2022, 1, 1, 0, 0, 0, 0, ctx.RequestTimeZone())
	timelines, err := timelineDiagnoseService.CreateMany(ctx, []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
		{
			TreatmentDoctorId: s.doctor.EmployeeId,
			PatientId:         s.patient.PatientId,
			Type:              common.TimelineEntityType_Diagnose,
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Code: "J06.1",
			},
		},
		{
			TreatmentDoctorId: s.doctor.EmployeeId,
			PatientId:         s.patient.PatientId,
			Type:              common.TimelineEntityType_Diagnose,
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Code: "E10.11",
			},
			CreatedAt:    date,
			SelectedDate: date,
		},
	})
	require.NoError(t, err)
	require.Len(t, timelines, 2)

	res, err := timelineDiagnoseService.GetDiagnosisInCurrentDate(ctx, s.patient.PatientId)
	require.NoError(t, err)
	require.NotNil(t, res)
	icdFound := slice.FindOne(res, func(item common.TimelineModel) bool {
		return item.EncounterDiagnoseTimeline.Code == "J06.1"
	})
	require.NotNil(t, icdFound)
	icdShouldNotFound := slice.FindOne(res, func(item common.TimelineModel) bool {
		return item.EncounterDiagnoseTimeline.Code == "E10.11"
	})
	require.Nil(t, icdShouldNotFound)
}

func (s *TimelineTestSuite) Test_UpdateEAUStatus() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	timelineFormService, err := timeline_service.TimelineServiceFormMod.SafeResolve()
	require.NoError(t, err)

	formId := uuid.New()
	timelineRes, err := timelineFormService.Create(ctx, timeline_repo.TimelineEntity[patient_encounter.EncounterForm]{
		TreatmentDoctorId: s.doctor.EmployeeId,
		PatientId:         s.patient.PatientId,
		Type:              common.TimelineEntityType_Diagnose,
		Payload: patient_encounter.EncounterForm{
			Id: &formId,
			Prescribe: &form_common.Prescribe{
				Id:        &formId,
				EAUStatus: util.NewPointer(qes_common.Status_Created),
			},
		},
	})
	require.NoError(t, err)
	require.NotNil(t, timelineRes)

	res, err := timelineFormService.UpdateEAUStatusById(ctx, formId, qes_common.Status_Sent)
	require.NoError(t, err)
	require.NotNil(t, res)
	require.NotNil(t, res.EncounterForm.Prescribe)
	require.NotNil(t, res.EncounterForm.Prescribe.EAUStatus)
	require.Equal(t, qes_common.Status_Sent, util.GetPointerValue(res.EncounterForm.Prescribe.EAUStatus))
}
