package timeline

import (
	"testing"
	"time"

	ares_test "git.tutum.dev/medi/tutum/ares/pkg/test"
	"git.tutum.dev/medi/tutum/ares/pkg/test/data"
	"git.tutum.dev/medi/tutum/ares/pkg/test/fixtures"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	fixtures2 "gitlab.com/silenteer-oss/hestia/test/fixtures"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type TimelineRepoTestSuite struct {
	ares_test.WithDefaultInfraTestSuite
	mvzFixture          *fixtures2.CareProviderFixture
	doctor              *data.Doctor
	timelineRepo        timeline_repo.TimelineEntityRepo[patient_encounter.EncounterDiagnoseTimeline]
	timelineServiceRepo timeline_repo.TimelineEntityRepo[patient_encounter.EncounterServiceTimeline]
}

func TestTimelineRepoTestSuite(t *testing.T) {
	fakeDoctor := fixtures.Doctor_CatharineRobel
	mvzFixture := fixtures.MvzFixture_521111100.ToCareProviderFixture()
	s := &TimelineRepoTestSuite{
		mvzFixture:          mvzFixture,
		doctor:              fakeDoctor,
		timelineRepo:        timeline_repo.NewTimelineRepoDefaultRepository[patient_encounter.EncounterDiagnoseTimeline](),
		timelineServiceRepo: timeline_repo.NewTimelineRepoDefaultRepository[patient_encounter.EncounterServiceTimeline](),
	}
	s.SetFixtures(mvzFixture)
	suite.Run(t, s)
}

func (ts *TimelineRepoTestSuite) Test_BulkWrite() {
	t := ts.T()
	patientId := uuid.New()
	ctx := ts.mvzFixture.GetAllCareProviders(ts.GetFixtureContext())[0].LoginAsDoctor(t, ts.doctor)
	entity := timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
		PatientId: patientId,
		Payload: patient_encounter.EncounterDiagnoseTimeline{
			Command:   "D",
			Code:      "I63.9",
			Certainty: util.NewPointer(patient_encounter.G),
		},
		CreatedAt:         time.Now(),
		TreatmentDoctorId: ts.doctor.EmployeeId,
	}
	_, err := ts.timelineRepo.Create(ctx, entity)
	require.Nil(t, err)
	timeline, err := ts.timelineRepo.FindOne(ctx, bson.M{timeline_repo.Field_PatientId: patientId})
	require.Nil(t, err)
	require.NotNil(t, timeline)
	require.Equal(t, "I63.9", timeline.Payload.Code)

	// NOTE: run bulk write (force all code be into Q100 for testing)
	opts := options.BulkWrite().SetOrdered(false)
	models := []mongo.WriteModel{
		mongo.NewUpdateOneModel().
			SetFilter(bson.M{timeline_repo.Field_Id: timeline.Id}).
			SetUpdate(bson.M{"$set": bson.M{timeline_repo.Field_PatientId: patientId, timeline_repo.Field_ICD_Code: "Q100"}}).
			SetUpsert(true),
		mongo.NewUpdateOneModel().
			SetFilter(bson.M{timeline_repo.Field_Id: util.NewPointer(uuid.New())}).
			SetUpdate(bson.M{"$set": bson.M{timeline_repo.Field_PatientId: patientId, timeline_repo.Field_ICD_Code: "Q100"}}).
			SetUpsert(true),
		mongo.NewUpdateOneModel().
			SetFilter(bson.M{timeline_repo.Field_Id: util.NewPointer(uuid.New())}).
			SetUpdate(bson.M{"$set": bson.M{timeline_repo.Field_PatientId: patientId, timeline_repo.Field_ICD_Code: "Q100"}}).
			SetUpsert(true),
		mongo.NewUpdateOneModel().
			SetFilter(bson.M{timeline_repo.Field_Id: util.NewPointer(uuid.New())}).
			SetUpdate(bson.M{"$set": bson.M{timeline_repo.Field_PatientId: patientId, timeline_repo.Field_ICD_Code: "Q100"}}).
			SetUpsert(true),
	}
	err = ts.timelineRepo.BulkWrite(ctx, models, opts)
	require.Nil(t, err)
	timelines, err := ts.timelineRepo.Find(ctx, bson.M{timeline_repo.Field_PatientId: patientId})
	require.Nil(t, err)
	require.Equal(t, 4, len(timelines))
	for _, entity := range timelines {
		require.Equal(t, "Q100", entity.Payload.Code)
	}
}

func (ts *TimelineRepoTestSuite) Test_GetTimelineForValidation() {
	t := ts.T()
	patientId := uuid.New()
	ctx := ts.mvzFixture.GetAllCareProviders(ts.GetFixtureContext())[0].LoginAsDoctor(t, ts.doctor)
	now := time.Now()
	contractId := uuid.NewString()
	entities := []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
		{
			PatientId: patientId,
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Command:   "D",
				Code:      "I63.9",
				Certainty: util.NewPointer(patient_encounter.G),
			},
			CreatedAt:         now,
			TreatmentDoctorId: ts.doctor.EmployeeId,
			IsImported:        true,
			SelectedDate:      now,
			ContractId:        &contractId,
		},
		{
			PatientId: patientId,
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Command:   "D",
				Code:      "I63.1",
				Certainty: util.NewPointer(patient_encounter.G),
			},
			CreatedAt:         now,
			TreatmentDoctorId: ts.doctor.EmployeeId,
			IsImported:        false,
			SelectedDate:      now,
			ContractId:        &contractId,
		},
		{
			PatientId: patientId,
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Command:   "D",
				Code:      "I63.0",
				Certainty: util.NewPointer(patient_encounter.G),
			},
			CreatedAt:         now,
			TreatmentDoctorId: ts.doctor.EmployeeId,
			IsImported:        false,
			SelectedDate:      time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC),
			ContractId:        &contractId,
		},
	}
	_, err := ts.timelineRepo.CreateMany(ctx, entities)
	require.Nil(t, err)

	timelines, err := ts.timelineRepo.GetTimelinesForValidation(ctx, patientId, &contractId)
	require.Nil(t, err)
	require.Equal(t, 1, len(timelines))
	require.Equal(t, "I63.1", timelines[0].Payload.Code)
}

func (ts *TimelineRepoTestSuite) Test_Timeline_Remap_5011_5012() {
	t := ts.T()
	patientId := uuid.New()
	ctx := ts.mvzFixture.GetAllCareProviders(ts.GetFixtureContext())[0].LoginAsDoctor(t, ts.doctor)
	now := time.Now()
	contractId := uuid.NewString()
	entities := timeline_repo.TimelineEntity[patient_encounter.EncounterServiceTimeline]{
		PatientId: patientId,
		Payload: patient_encounter.EncounterServiceTimeline{
			AdditionalInfos: &[]*patient_encounter.AdditionalInfoParent{
				{
					FK:    "5011",
					Value: "Q100",
					Children: []*patient_encounter.AdditionalInfoChild{
						{
							FK:    "5012",
							Value: "Q100",
						},
						{
							FK:    "5013",
							Value: "Q101",
						},
					},
				},
			},
		},
		CreatedAt:         now,
		TreatmentDoctorId: ts.doctor.EmployeeId,
		IsImported:        true,
		SelectedDate:      now,
		ContractId:        &contractId,
	}
	res, err := ts.timelineServiceRepo.Create(ctx, entities)
	require.Nil(t, err)
	require.NotNil(t, res)
}

func (ts *TimelineRepoTestSuite) Test_GetLastDocumentedEntity() {
	t := ts.T()
	patientId := uuid.New()
	ctx := ts.mvzFixture.GetAllCareProviders(ts.GetFixtureContext())[0].LoginAsDoctor(t, ts.doctor)
	now := time.Now()
	selectedDate := time.Date(2025, 6, 1, 0, 0, 0, 0, time.UTC) // quarter 2
	entities := []timeline_repo.TimelineEntity[patient_encounter.EncounterDiagnoseTimeline]{
		{
			PatientId: patientId,
			Payload: patient_encounter.EncounterDiagnoseTimeline{
				Command:   "D",
				Code:      "a",
				Certainty: util.NewPointer(patient_encounter.G),
			},
			CreatedAt:         now,
			TreatmentDoctorId: ts.doctor.EmployeeId,
			SelectedDate:      selectedDate,
			ScheinIds:         []uuid.UUID{}, // has schein id
		},
	}
	_, err := ts.timelineRepo.CreateMany(ctx, entities)
	require.Nil(t, err)

	yearQuarter := util.ToYearQuarter(time.Date(2025, 7, 1, 0, 0, 0, 0, time.UTC).UnixMilli()) // quarter 3
	lastEntity, err := ts.timelineRepo.GetLastDocumentedEntity(ctx, &timeline_repo.GetLastDocumentedEntityRequest{
		PatientId:          patientId,
		Year:               &yearQuarter.Year,
		Quarter:            &yearQuarter.Quarter,
		TimelineEntityType: util.NewPointer(common.TimelineEntityType_Diagnose),
	})
	require.Nil(t, err)
	require.NotNil(t, lastEntity)
	require.Equal(t, "a", lastEntity.Payload.Code)
}
