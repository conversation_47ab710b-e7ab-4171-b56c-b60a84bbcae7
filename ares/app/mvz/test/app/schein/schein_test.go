package schein_test

import (
	"encoding/json"
	"errors"
	"os"
	"testing"
	"time"

	"github.com/brianvoe/gofakeit/v5"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	fixtures2 "gitlab.com/silenteer-oss/hestia/test/fixtures"
	"gitlab.com/silenteer-oss/titan"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_encounter"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_profile"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/private_contract_group"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/schein"
	schein_bff "git.tutum.dev/medi/tutum/ares/app/mvz/api/schein"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/timeline"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/app"
	patient_profile_service "git.tutum.dev/medi/tutum/ares/app/mvz/patient_profile"
	ares_test "git.tutum.dev/medi/tutum/ares/pkg/test"
	"git.tutum.dev/medi/tutum/ares/pkg/test/data"
	"git.tutum.dev/medi/tutum/ares/pkg/test/fixtures"
	"git.tutum.dev/medi/tutum/ares/pkg/test/repository"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/private_schein_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/validation_timeline"
	pcg_common "git.tutum.dev/medi/tutum/ares/service/domains/private_contract_group/common"
	patient_encounter_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"
	schein_repo "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/timeline_service"
	schein_service "git.tutum.dev/medi/tutum/ares/service/schein"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
)

type ScheinTestSuite struct {
	ares_test.WithDefaultInfraTestSuite
	db                      *repository.Repos
	scheinApp               *schein_bff.ScheinAppClient
	mvzFixture              *fixtures2.CareProviderFixture
	doctor                  *data.Doctor
	patient                 *data.Patient
	patient2                *data.Patient
	patientProfileApp       *patient_profile.PatientProfileAppClient
	PatientEncounterBff     patient_encounter.PatientEncounterAppClient
	timelineApp             *timeline.TimelineAppClient
	privateContractGroupApp *private_contract_group.PrivateContractGroupAppClient
}

var (
	year    int32 = util.NowYear(nil)
	quarter int32 = util.NowQuarter(nil)
)

func TestScheinAppSuite(t *testing.T) {
	natsConfig, natsServer, defaultClient := ares_test.NewInfraForTesting()

	patient := fixtures.Patient_HasValidKvInsurance
	patient.Profile.PatientInfo.PatientNumber = 1
	patient.Profile.PatientInfo.InsuranceInfos[0].IsActive = true

	patient2 := fixtures.Patient_HasValidKvInsurance
	patient2.Profile.PatientInfo.PatientNumber = 2
	patient2.Profile.PatientInfo.InsuranceInfos[0].IsActive = true

	doctor := fixtures.Doctor_CatharineRobel

	patientFixture := &fixtures.PatientFixture{
		Patients: []*data.Patient{
			patient,
			patient2,
		},
	}

	theFixtures := fixtures.MvzFixture_521111100.With(patientFixture).ToCareProviderFixture()

	scheinBff := schein_bff.NewScheinAppClient(defaultClient)
	s := &ScheinTestSuite{
		db:                      repository.DefaultRepos,
		scheinApp:               scheinBff,
		patientProfileApp:       patient_profile.NewPatientProfileAppClient(defaultClient),
		PatientEncounterBff:     *patient_encounter.NewPatientEncounterAppClient(defaultClient),
		mvzFixture:              theFixtures,
		doctor:                  doctor,
		timelineApp:             timeline.NewTimelineAppClient(defaultClient),
		patient:                 patient,
		patient2:                patient2,
		privateContractGroupApp: private_contract_group.NewPrivateContractGroupAppClient(defaultClient),
	}
	config := config.MvzAppConfigMod.Resolve()
	if config == nil {
		panic("config is nil")
	}

	testServers := ares_test.NewTestServersWithDbHpmBindingMock(
		true,
		natsConfig,
		app.NewServer(
			*config,
		),
	)
	s.SetNatsServer(natsServer).SetTestServers(testServers).
		SetEnableSocketServer(true, natsConfig).
		SetFixtures(theFixtures)
	suite.Run(t, s)
}

// This will run before each test in the suite
func (s *ScheinTestSuite) SetupTest() {
	_ = s.ResetData()
}

func (s *ScheinTestSuite) Test_GetScheinTakeoverDiagnoses() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	_, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
		TimelineModel: common.TimelineModel{
			TreatmentDoctorId: s.doctor.EmployeeId,
			PatientId:         s.patient.PatientId,
			EncounterDiagnoseTimeline: &patient_encounter_repo.EncounterDiagnoseTimeline{
				Code:       "A00",
				Certainty:  util.NewPointer(patient_encounter_repo.A),
				Laterality: util.NewPointer(patient_encounter_repo.U),
				Command:    "D",
			},
		},
	})
	require.Nil(t, err)

	_, err = s.timelineApp.Create(ctx, timeline.CreateRequest{
		TimelineModel: common.TimelineModel{
			TreatmentDoctorId: s.doctor.EmployeeId,
			PatientId:         s.patient.PatientId,
			EncounterDiagnoseTimeline: &patient_encounter_repo.EncounterDiagnoseTimeline{
				Code:       "A02",
				Certainty:  util.NewPointer(patient_encounter_repo.A),
				Laterality: util.NewPointer(patient_encounter_repo.U),
				FreeText:   "last_item",
				Command:    "DD",
			},
		},
	})
	require.Nil(t, err)

	_, err = s.timelineApp.Create(ctx, timeline.CreateRequest{
		TimelineModel: common.TimelineModel{
			TreatmentDoctorId: s.doctor.EmployeeId,
			PatientId:         s.patient.PatientId,
			EncounterDiagnoseTimeline: &patient_encounter_repo.EncounterDiagnoseTimeline{
				Code:       "A01",
				Certainty:  util.NewPointer(patient_encounter_repo.A),
				Laterality: util.NewPointer(patient_encounter_repo.U),
				Command:    "AD",
			},
		},
	})
	require.Nil(t, err)

	takeoverDiagnose, err := s.timelineApp.GetTakeOverDiagnosis(ctx, timeline.GetTakeOverDiagnosisRequest{
		PatientId: s.patient.PatientId,
	})
	require.Nil(t, err)
	require.NotEmpty(t, takeoverDiagnose)
	require.Equal(t, len(takeoverDiagnose.TakeOverDiagnosisGroup), 3)

	entry := slice.FindOne(takeoverDiagnose.TakeOverDiagnosisGroup, func(diagnoseGroup common.TakeOverDiagnosisGroup) bool {
		return slice.Any(diagnoseGroup.TimelineModels, func(t common.TimelineModel) bool {
			return t.EncounterDiagnoseTimeline.Code == "A02"
		})
	})
	require.NotNil(t, entry)
	require.Equal(t, "last_item", entry.TimelineModels[0].EncounterDiagnoseTimeline.FreeText)
}

func (s *ScheinTestSuite) Test_CreateScheinWithTakeoverDiagnoses() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	var costUnitAccountingArea string = "1"
	testStr := "test"
	var settlementArea string = "1"
	currentDate := util.NowUnixMillis(ctx)

	timeLineRes, err := s.timelineApp.Create(ctx, timeline.CreateRequest{
		TimelineModel: common.TimelineModel{
			TreatmentDoctorId: s.doctor.EmployeeId,
			PatientId:         s.patient.PatientId,
			EncounterDiagnoseTimeline: &patient_encounter_repo.EncounterDiagnoseTimeline{
				Code:       "A01",
				Certainty:  util.NewPointer(patient_encounter_repo.A),
				Laterality: util.NewPointer(patient_encounter_repo.U),
			},
		},
	})
	require.Nil(t, err)

	request := schein_bff.CreateScheinRequest{
		PatientId:        s.patient.PatientId,
		DoctorId:         &s.doctor.EmployeeId,
		ScheinMainGroup:  "KV",
		KvScheinSubGroup: util.NewPointer("27"),
		KvTreatmentCase:  schein_common.TCKvReferral,
		InsuranceId:      s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
		G4122:            &settlementArea,
		G4101Year:        year,
		G4101Quarter:     quarter,
		HzvContractId:    nil,
		ScheinDetails: &schein_common.ScheinDetail{
			G4122:           &settlementArea,
			G4106:           &costUnitAccountingArea,
			G4102:           util.NewPointer(int64(1)),
			G4104:           util.NewPointer(int32(1)),
			Re4205:          util.NewPointer("123"),
			Re4226:          &testStr,
			Re4242:          &testStr,
			Re4217:          util.NewPointer("int32(1)"),
			Re4221:          util.NewPointer("int32(1)"),
			TsvgContactType: &testStr,
			TsvgContactDate: &currentDate,
		},
		TakeOverDiagnoseInfos: []schein_bff.TakeOverDiagnoseInfo{
			{
				Id:                  *timeLineRes.TimelineModel.Id,
				IsTreatmentRelevant: false,
			},
		},
		NewTakeOverDiagnosis: []common.TimelineModel{
			{
				TreatmentDoctorId: s.doctor.EmployeeId,
				PatientId:         s.patient.PatientId,
				EncounterDiagnoseTimeline: &patient_encounter_repo.EncounterDiagnoseTimeline{
					Code:       "A01",
					Certainty:  util.NewPointer(patient_encounter_repo.A),
					Laterality: util.NewPointer(patient_encounter_repo.U),
				},
			},
		},
	}

	_, err = s.scheinApp.CreateSchein(ctx, request)
	currentQuarter := request.GetYearQuarter()
	activeinsurance := s.patient.Profile.PatientInfo.InsuranceInfos[0]
	insuranceValidStartDate := activeinsurance.IsValidWithQuarter(*currentQuarter)
	if !insuranceValidStartDate {
		require.NotNil(t, err)
	} else {
		require.Nil(t, err)
	}

	groupByQuarterResponse, err := s.timelineApp.GroupByQuarter(ctx, timeline.GroupByQuarterRequest{
		PatientId: s.patient.PatientId,
		FromDate:  util.NewPointer(util.NowUnixMillis(ctx)),
		ToDate:    util.NewPointer(util.ConvertTimeToMiliSecond(time.Now().AddDate(0, 0, 1))),
	})
	require.Nil(t, err)
	require.NotNil(t, groupByQuarterResponse)

	newEntry := groupByQuarterResponse.GroupByQuarters[0].TimelineModels[0]
	require.False(t, newEntry.EncounterDiagnoseTimeline.MarkedTreatmentRelevant)
}

func (s *ScheinTestSuite) Test_Should_Create_Pyschotherapy_When_Create_Schein() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	currentQuarter := util.ToYearQuarter(time.Now().UnixMilli())
	request := schein_bff.CreateScheinRequest{
		PatientId:        s.patient.PatientId,
		DoctorId:         &s.doctor.EmployeeId,
		ScheinMainGroup:  "KV",
		KvScheinSubGroup: util.NewPointer("00"),
		KvTreatmentCase:  schein_common.TCKvOutpatient,
		InsuranceId:      s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
		G4101Year:        currentQuarter.Year,
		G4101Quarter:     currentQuarter.Quarter,
		G4122:            util.NewPointer("00"),
		ScheinDetails: &schein_common.ScheinDetail{
			G4104: util.NewPointer(int32(61125)),
			G4122: util.NewPointer("00"),
			G4106: util.NewPointer("00"),
			Psychotherapy: []schein_common.Psychotherapy{
				{
					Ps4235: util.NewPointer(int64(1696550400000)),
					Ps4247: util.NewPointer(int64(1696377600000)),
					Ps4245: util.NewPointer(int64(21)),
					GroupServicesCode: []schein_common.GroupServicesCode{
						{
							ServiceCode:  "03000",
							AmountBilled: 1,
						},
						{
							ServiceCode:  "02200",
							AmountBilled: 1,
						},
					},
				},
			},
		},
	}
	res, err := s.scheinApp.CreateSchein(ctx, request)
	require.Nil(t, err)
	require.NotNil(t, res)
	require.Nil(t, res.FieldErrors)
	require.NotEmpty(t, res.ScheinItem.ScheinDetail.Psychotherapy)

	timelinePyschotherapy, err := timeline_service.TimelineServicePsychotherapyMod.SafeResolve()
	require.Nil(t, err)
	require.NotNil(t, timelinePyschotherapy)

	pyschotherapy, err := timelinePyschotherapy.FindById(ctx, *res.ScheinItem.ScheinDetail.Psychotherapy[0].Id)
	require.Nil(t, err)
	require.NotNil(t, pyschotherapy)
	require.Equal(t, pyschotherapy.Payload.AmountBilled, int32(2))
	require.Equal(t, pyschotherapy.Payload.ServiceCodes, []string{request.ScheinDetails.Psychotherapy[0].GroupServicesCode[0].ServiceCode, request.ScheinDetails.Psychotherapy[0].GroupServicesCode[1].ServiceCode})
	require.Equal(t, pyschotherapy.Payload.Status, patient_encounter_repo.INPROGRESS)
	require.Equal(t, *pyschotherapy.Payload.RequestDate, *request.ScheinDetails.Psychotherapy[0].Ps4247)
	require.Equal(t, pyschotherapy.Payload.ApprovalDate, *request.ScheinDetails.Psychotherapy[0].Ps4235)
	require.NotEmpty(t, pyschotherapy.Payload.Entries)
}

func (s *ScheinTestSuite) Test_Should_Create_Pyschotherapy_When_Create_Schein_Before_2011_7() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	currentQuarter := util.ToYearQuarter(time.Now().UnixMilli())
	before2011_7 := time.Now().AddDate(-10, 0, 0).UnixMilli()
	request := schein_bff.CreateScheinRequest{
		PatientId:        s.patient.PatientId,
		DoctorId:         &s.doctor.EmployeeId,
		ScheinMainGroup:  "KV",
		KvScheinSubGroup: util.NewPointer("00"),
		KvTreatmentCase:  schein_common.TCKvOutpatient,
		InsuranceId:      s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
		G4101Year:        currentQuarter.Year,
		G4101Quarter:     currentQuarter.Quarter,
		G4122:            util.NewPointer("00"),
		ScheinDetails: &schein_common.ScheinDetail{
			G4104: util.NewPointer(int32(61125)),
			G4122: util.NewPointer("00"),
			G4106: util.NewPointer("00"),
			Psychotherapy: []schein_common.Psychotherapy{
				{
					Ps4235: util.NewPointer(int64(1696550400000)),
					Ps4247: util.NewPointer(before2011_7),
					Ps4245: util.NewPointer(int64(21)),
					GroupServicesCodeBefore2017: []schein_common.GroupServicesCodeBefore2017{
						{
							ServiceCode:  "03000",
							AmountBilled: 1,
						},
						{
							ServiceCode:  "02200",
							AmountBilled: 1,
						},
					},
				},
			},
		},
	}
	res, err := s.scheinApp.CreateSchein(ctx, request)
	require.Nil(t, err)
	require.NotNil(t, res)
	require.Nil(t, res.FieldErrors)
	require.NotEmpty(t, res.ScheinItem.ScheinDetail.Psychotherapy)

	timelinePyschotherapy, err := timeline_service.TimelineServicePsychotherapyMod.SafeResolve()
	require.Nil(t, err)
	require.NotNil(t, timelinePyschotherapy)

	pyschotherapy, err := timelinePyschotherapy.FindById(ctx, *res.ScheinItem.ScheinDetail.Psychotherapy[0].Id)
	require.Nil(t, err)
	require.NotNil(t, pyschotherapy)
	require.Equal(t, pyschotherapy.Payload.AmountBilled, int32(0))
	require.Equal(t, pyschotherapy.Payload.ServiceCodes, []string{
		request.ScheinDetails.Psychotherapy[0].GroupServicesCodeBefore2017[0].ServiceCode,
		request.ScheinDetails.Psychotherapy[0].GroupServicesCodeBefore2017[1].ServiceCode,
	})
	require.Equal(t, pyschotherapy.Payload.Status, patient_encounter_repo.INPROGRESS)
	require.Equal(t, *pyschotherapy.Payload.RequestDate, *request.ScheinDetails.Psychotherapy[0].Ps4247)
	require.Equal(t, pyschotherapy.Payload.ApprovalDate, *request.ScheinDetails.Psychotherapy[0].Ps4235)
	require.NotEmpty(t, pyschotherapy.Payload.Entries)
}

func (s *ScheinTestSuite) Test_Pyschotherapy_Should_Not_Count_When_Full_Slot() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	serviceCodeDocument := "30935"
	currentQuarter := util.ToYearQuarter(time.Now().UnixMilli())
	request := schein_bff.CreateScheinRequest{
		PatientId:        s.patient.PatientId,
		DoctorId:         &s.doctor.EmployeeId,
		ScheinMainGroup:  "KV",
		KvScheinSubGroup: util.NewPointer("00"),
		KvTreatmentCase:  schein_common.TCKvOutpatient,
		InsuranceId:      s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
		G4101Year:        currentQuarter.Year,
		G4101Quarter:     currentQuarter.Quarter,
		G4122:            util.NewPointer("00"),
		ScheinDetails: &schein_common.ScheinDetail{
			G4104: util.NewPointer(int32(61125)),
			G4122: util.NewPointer("00"),
			G4106: util.NewPointer("00"),
			Psychotherapy: []schein_common.Psychotherapy{
				{
					Ps4235: util.NewPointer(int64(1696550400000)),
					Ps4247: util.NewPointer(int64(1696377600000)),
					Ps4245: util.NewPointer(int64(1)),
					GroupServicesCode: []schein_common.GroupServicesCode{
						{
							ServiceCode:  serviceCodeDocument,
							AmountBilled: 1,
						},
					},
				},
			},
		},
	}
	res, err := s.scheinApp.CreateSchein(ctx, request)
	require.Nil(t, err)
	require.NotNil(t, res)
	require.Nil(t, res.FieldErrors)
	require.NotEmpty(t, res.ScheinItem.ScheinDetail.Psychotherapy)

	timelinePyschotherapy, err := timeline_service.TimelineServicePsychotherapyMod.SafeResolve()
	require.Nil(t, err)
	require.NotNil(t, timelinePyschotherapy)

	pyschotherapy, err := timelinePyschotherapy.FindById(ctx, *res.ScheinItem.ScheinDetail.Psychotherapy[0].Id)
	require.Nil(t, err)
	require.NotNil(t, pyschotherapy)
	require.Empty(t, pyschotherapy.Payload.Entries[serviceCodeDocument].EntryIds)
}

func (s *ScheinTestSuite) Test_Should_Correct_AmountBill_When_Document_Same_Service_Code() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	serviceCodeDocument := "30934"
	currentQuarter := util.ToYearQuarter(time.Now().UnixMilli())
	request := schein_bff.CreateScheinRequest{
		PatientId:        s.patient.PatientId,
		DoctorId:         &s.doctor.EmployeeId,
		ScheinMainGroup:  "KV",
		KvScheinSubGroup: util.NewPointer("00"),
		KvTreatmentCase:  schein_common.TCKvOutpatient,
		InsuranceId:      s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
		G4101Year:        currentQuarter.Year,
		G4101Quarter:     currentQuarter.Quarter,
		G4122:            util.NewPointer("00"),
		ScheinDetails: &schein_common.ScheinDetail{
			G4104: util.NewPointer(int32(61125)),
			G4122: util.NewPointer("00"),
			G4106: util.NewPointer("00"),
			Psychotherapy: []schein_common.Psychotherapy{
				{
					Ps4235: util.NewPointer(int64(1696550400000)),
					Ps4247: util.NewPointer(int64(1696377600000)),
					Ps4245: util.NewPointer(int64(2)),
					GroupServicesCode: []schein_common.GroupServicesCode{
						{
							ServiceCode:  serviceCodeDocument,
							AmountBilled: 1,
						},
					},
				},
			},
		},
	}
	res, _ := s.scheinApp.CreateSchein(ctx, request)

	timelineServiceEncounter, _ := timeline_service.TimelineServiceEncounterMod.SafeResolve()
	timelineItem, err := timelineServiceEncounter.Create(ctx, timeline_repo.TimelineEntity[patient_encounter_repo.EncounterServiceTimeline]{
		TreatmentDoctorId: s.doctor.EmployeeId,
		PatientId:         s.patient.PatientId,
		ScheinIds: []uuid.UUID{
			res.ScheinItem.ScheinId,
		},
		Payload: patient_encounter_repo.EncounterServiceTimeline{
			Code: serviceCodeDocument,
		},
	})
	require.Nil(t, err)
	timelinePyschotherapy, _ := timeline_service.TimelineServicePsychotherapyMod.SafeResolve()
	pyschotherapy, err := timelinePyschotherapy.FindById(ctx, *res.ScheinItem.ScheinDetail.Psychotherapy[0].Id)
	require.Nil(t, err)
	require.NotNil(t, pyschotherapy)
	require.NotEmpty(t, pyschotherapy.Payload.Entries[serviceCodeDocument].EntryIds)
	require.Equal(t, pyschotherapy.Payload.Entries[serviceCodeDocument].EntryIds[0], *timelineItem.Id)
	require.Equal(t, pyschotherapy.Payload.Entries[serviceCodeDocument].AmountBilled, int32(2))
}

// tạo 2 schein , mỗi schein có 1 approval
// quý 1 (amountApproval = 2), quý 2 (amountApproval = 2)
// doc service cho quy 1 --> có suggestion hint trong error validation , status 1/2
// doc service cho quy 2 -->KHONG có suggestion hint trong error validation, status 1/2

func (s *ScheinTestSuite) Test_Should_GetScheinByInsuranceId() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	// create new insurance -> get schein -> should be nil
	patientProfile, _ := s.patientProfileApp.GetPatientProfileById(ctx, &patient_profile.GetById{
		Id: s.patient.PatientId.String(),
	})
	defaultInsurances := patientProfile.PatientInfo.InsuranceInfos
	var newInsurances []*patient_profile_common.InsuranceInfo
	insuranceDeleted := []*patient_profile_common.InsuranceInfo{}

	for i := range defaultInsurances {
		newInsurances = append(newInsurances, &defaultInsurances[i])
	}
	payloadCreated := append(newInsurances, &patient_profile_common.InsuranceInfo{
		InsuranceCompanyId: "3333",
		InsuranceNumber:    util.NewPointer("3"),
		IkNumber:           *********,
		InsuranceType:      patient_profile_common.Public,
	})
	insuranceCreated, err := s.patientProfileApp.UpdateInsurances(ctx, &patient_profile.UpdateInsurancesRequest{
		PatientId:             &s.patient.PatientId,
		InsuranceInfos:        payloadCreated,
		InsuranceInfosDeleted: insuranceDeleted,
	})
	require.NoError(t, err)
	schein, err := s.scheinApp.GetScheinByInsuranceId(ctx, schein_bff.GetScheinByInsuranceIdRequest{
		InsuranceId: insuranceCreated.InsuranceInfos[0].Id,
	})
	require.NoError(t, err)
	require.Nil(t, schein)

	// assign insurance to schein -> get schein -> should be valid
	serviceCodeDocument := "31935"
	currentQuarter := util.ToYearQuarter(time.Now().UnixMilli())
	request := schein_bff.CreateScheinRequest{
		PatientId:        s.patient.PatientId,
		DoctorId:         &s.doctor.EmployeeId,
		ScheinMainGroup:  "KV",
		KvScheinSubGroup: util.NewPointer("00"),
		KvTreatmentCase:  schein_common.TCKvOutpatient,
		InsuranceId:      s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
		G4101Year:        currentQuarter.Year,
		G4101Quarter:     currentQuarter.Quarter,
		G4122:            util.NewPointer("00"),
		ScheinDetails: &schein_common.ScheinDetail{
			G4104: util.NewPointer(int32(61125)),
			G4122: util.NewPointer("00"),
			G4106: util.NewPointer("00"),
			Psychotherapy: []schein_common.Psychotherapy{
				{
					Ps4235: util.NewPointer(int64(1696550400000)),
					Ps4247: util.NewPointer(int64(1696377600000)),
					Ps4245: util.NewPointer(int64(1)),
					GroupServicesCode: []schein_common.GroupServicesCode{
						{
							ServiceCode:  serviceCodeDocument,
							AmountBilled: 1,
						},
					},
				},
			},
		},
	}
	scheinCreated, err := s.scheinApp.CreateSchein(ctx, request)
	require.NoError(t, err)
	require.NotNil(t, scheinCreated)
	require.NotNil(t, scheinCreated.ScheinItem.ScheinId)

	schein, err = s.scheinApp.GetScheinByInsuranceId(ctx, schein_bff.GetScheinByInsuranceIdRequest{
		InsuranceId: s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
	})
	require.NoError(t, err)
	require.NotNil(t, schein.ScheinItem.ScheinId)
}

func (s *ScheinTestSuite) Test_Psychotherapy_Should_Remove_Terminate_Service_Code_88130() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	serviceCodeDocument := "03111"
	currentQuarter := util.ToYearQuarter(time.Now().UnixMilli())
	request := schein_bff.CreateScheinRequest{
		PatientId:        s.patient.PatientId,
		DoctorId:         &s.doctor.EmployeeId,
		ScheinMainGroup:  "KV",
		KvScheinSubGroup: util.NewPointer("00"),
		KvTreatmentCase:  schein_common.TCKvOutpatient,
		InsuranceId:      s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
		G4101Year:        currentQuarter.Year,
		G4101Quarter:     currentQuarter.Quarter,
		G4122:            util.NewPointer("00"),
		ScheinDetails: &schein_common.ScheinDetail{
			G4104: util.NewPointer(int32(61125)),
			G4122: util.NewPointer("00"),
			G4106: util.NewPointer("00"),
			Psychotherapy: []schein_common.Psychotherapy{
				{
					Ps4235: util.NewPointer(int64(1696550400000)),
					Ps4247: util.NewPointer(int64(1696377600000)),
					Ps4245: util.NewPointer(int64(2)),
					GroupServicesCode: []schein_common.GroupServicesCode{
						{
							ServiceCode:  serviceCodeDocument,
							AmountBilled: 1,
						},
					},
				},
			},
		},
	}
	res, _ := s.scheinApp.CreateSchein(ctx, request)

	timelineServiceEncounter, _ := timeline_service.TimelineServiceEncounterMod.SafeResolve()
	_, err := timelineServiceEncounter.Create(ctx, timeline_repo.TimelineEntity[patient_encounter_repo.EncounterServiceTimeline]{
		TreatmentDoctorId: s.doctor.EmployeeId,
		PatientId:         s.patient.PatientId,
		ScheinIds: []uuid.UUID{
			res.ScheinItem.ScheinId,
		},
		Payload: patient_encounter_repo.EncounterServiceTimeline{
			Code: serviceCodeDocument,
		},
	})
	require.Nil(t, err)
	timelineItem88130, err := timelineServiceEncounter.Create(ctx, timeline_repo.TimelineEntity[patient_encounter_repo.EncounterServiceTimeline]{
		TreatmentDoctorId: s.doctor.EmployeeId,
		PatientId:         s.patient.PatientId,
		ScheinIds: []uuid.UUID{
			res.ScheinItem.ScheinId,
		},
		Payload: patient_encounter_repo.EncounterServiceTimeline{
			Code: "88130",
		},
	})
	require.Nil(t, err)
	timelinePyschotherapy, _ := timeline_service.TimelineServicePsychotherapyMod.SafeResolve()
	psychotherapyEntry, err := timelinePyschotherapy.FindById(ctx, *res.ScheinItem.ScheinDetail.Psychotherapy[0].Id)
	require.Nil(t, err)
	require.True(t, *psychotherapyEntry.Payload.TerminateServiceId == *timelineItem88130.Id)
	require.True(t, psychotherapyEntry.Payload.Status == patient_encounter_repo.READY_TO_BILL)

	_ = timelineServiceEncounter.Remove(ctx, *timelineItem88130.Id, false)
	psychotherapyEntry, err = timelinePyschotherapy.FindById(ctx, *res.ScheinItem.ScheinDetail.Psychotherapy[0].Id)
	require.Nil(t, err)
	require.True(t, psychotherapyEntry.Payload.TerminateServiceId == nil)
	require.True(t, psychotherapyEntry.Payload.Status == patient_encounter_repo.INPROGRESS)
}

func (s *ScheinTestSuite) Test_Psychotherapy_Should_Remove_Terminate_Service_Code_88131() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	serviceCodeDocument := "35435U"
	currentQuarter := util.ToYearQuarter(time.Now().UnixMilli())
	request := schein_bff.CreateScheinRequest{
		PatientId:        s.patient.PatientId,
		DoctorId:         &s.doctor.EmployeeId,
		ScheinMainGroup:  "KV",
		KvScheinSubGroup: util.NewPointer("00"),
		KvTreatmentCase:  schein_common.TCKvOutpatient,
		InsuranceId:      s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
		G4101Year:        currentQuarter.Year,
		G4101Quarter:     currentQuarter.Quarter,
		G4122:            util.NewPointer("00"),
		ScheinDetails: &schein_common.ScheinDetail{
			G4104: util.NewPointer(int32(61125)),
			G4122: util.NewPointer("00"),
			G4106: util.NewPointer("00"),
			Psychotherapy: []schein_common.Psychotherapy{
				{
					Ps4235: util.NewPointer(int64(1696550400000)),
					Ps4247: util.NewPointer(int64(1696377600000)),
					Ps4245: util.NewPointer(int64(2)),
					GroupServicesCode: []schein_common.GroupServicesCode{
						{
							ServiceCode:  serviceCodeDocument,
							AmountBilled: 1,
						},
					},
				},
			},
		},
	}
	res, _ := s.scheinApp.CreateSchein(ctx, request)

	timelineServiceEncounter, _ := timeline_service.TimelineServiceEncounterMod.SafeResolve()
	_, err := timelineServiceEncounter.Create(ctx, timeline_repo.TimelineEntity[patient_encounter_repo.EncounterServiceTimeline]{
		TreatmentDoctorId: s.doctor.EmployeeId,
		PatientId:         s.patient.PatientId,
		ScheinIds: []uuid.UUID{
			res.ScheinItem.ScheinId,
		},
		Payload: patient_encounter_repo.EncounterServiceTimeline{
			Code: serviceCodeDocument,
		},
	})
	require.Nil(t, err)
	timelineItem88130, err := timelineServiceEncounter.Create(ctx, timeline_repo.TimelineEntity[patient_encounter_repo.EncounterServiceTimeline]{
		TreatmentDoctorId: s.doctor.EmployeeId,
		PatientId:         s.patient.PatientId,
		ScheinIds: []uuid.UUID{
			res.ScheinItem.ScheinId,
		},
		Payload: patient_encounter_repo.EncounterServiceTimeline{
			Code: "88131",
		},
	})
	require.Nil(t, err)
	timelinePyschotherapy, _ := timeline_service.TimelineServicePsychotherapyMod.SafeResolve()
	psychotherapyEntry, err := timelinePyschotherapy.FindById(ctx, *res.ScheinItem.ScheinDetail.Psychotherapy[0].Id)
	require.Nil(t, err)
	require.True(t, *psychotherapyEntry.Payload.TerminateServiceId == *timelineItem88130.Id)
	require.True(t, psychotherapyEntry.Payload.Status == patient_encounter_repo.READY_TO_BILL)

	_ = timelineServiceEncounter.Remove(ctx, *timelineItem88130.Id, false)
	psychotherapyEntry, err = timelinePyschotherapy.FindById(ctx, *res.ScheinItem.ScheinDetail.Psychotherapy[0].Id)
	require.Nil(t, err)
	require.True(t, psychotherapyEntry.Payload.TerminateServiceId == nil)
	require.True(t, psychotherapyEntry.Payload.Status == patient_encounter_repo.INPROGRESS)
}

func (s *ScheinTestSuite) Test_Psychotherapy_Should_Document_88130() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	serviceCodeDocument := "311122"
	currentQuarter := util.ToYearQuarter(time.Now().UnixMilli())
	request := schein_bff.CreateScheinRequest{
		PatientId:        s.patient.PatientId,
		DoctorId:         &s.doctor.EmployeeId,
		ScheinMainGroup:  "KV",
		KvScheinSubGroup: util.NewPointer("00"),
		KvTreatmentCase:  schein_common.TCKvOutpatient,
		InsuranceId:      s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
		G4101Year:        currentQuarter.Year,
		G4101Quarter:     currentQuarter.Quarter,
		G4122:            util.NewPointer("00"),
		ScheinDetails: &schein_common.ScheinDetail{
			G4104: util.NewPointer(int32(61125)),
			G4122: util.NewPointer("00"),
			G4106: util.NewPointer("00"),
			Psychotherapy: []schein_common.Psychotherapy{
				{
					Ps4235: util.NewPointer(int64(1696550400000)),
					Ps4247: util.NewPointer(int64(1696377600000)),
					Ps4245: util.NewPointer(int64(2)),
					GroupServicesCode: []schein_common.GroupServicesCode{
						{
							ServiceCode:  serviceCodeDocument,
							AmountBilled: 1,
						},
					},
				},
			},
		},
	}
	res, _ := s.scheinApp.CreateSchein(ctx, request)

	timelineServiceEncounter, _ := timeline_service.TimelineServiceEncounterMod.SafeResolve()
	timeline1, _ := timelineServiceEncounter.Create(ctx, timeline_repo.TimelineEntity[patient_encounter_repo.EncounterServiceTimeline]{
		TreatmentDoctorId: s.doctor.EmployeeId,
		PatientId:         s.patient.PatientId,
		ScheinIds: []uuid.UUID{
			res.ScheinItem.ScheinId,
		},
		Payload: patient_encounter_repo.EncounterServiceTimeline{
			Code: serviceCodeDocument,
		},
	})

	timelineServiceDiagnose, _ := timeline_service.TimelineServiceDiagnosisMod.SafeResolve()
	diagnosis, err := timelineServiceDiagnose.Create(ctx, timeline_repo.TimelineEntity[patient_encounter_repo.EncounterDiagnoseTimeline]{
		TreatmentDoctorId: s.doctor.EmployeeId,
		PatientId:         s.patient.PatientId,
		ScheinIds: []uuid.UUID{
			res.ScheinItem.ScheinId,
		},
		Payload: patient_encounter_repo.EncounterDiagnoseTimeline{
			Code: "F43.0",
		},
	})
	require.Nil(t, err)
	require.NotNil(t, diagnosis)

	document88130Res, err := timelineServiceEncounter.Document88130(ctx, &timeline.Document88130Request{
		TimelineModel: common.TimelineModel{
			TreatmentDoctorId: s.doctor.EmployeeId,
			PatientId:         s.patient.PatientId,
			EncounterServiceTimeline: &patient_encounter_repo.EncounterServiceTimeline{
				Code: "88130",
			},
		},
		ServiceEntryId: *timeline1.Id,
	})
	require.Nil(t, err)
	require.NotNil(t, document88130Res)
	require.Len(t, document88130Res.TakeoverDiagnosis, 1)
	timelines, err := timelineServiceEncounter.TakeOverDiagnosisWithScheinId(ctx, timeline.TakeOverDiagnosisWithScheinIdRequest{
		ScheinId: res.ScheinItem.ScheinId,
		TimelineModelIds: []uuid.UUID{
			*diagnosis.Id,
		},
	})
	require.Nil(t, err)
	require.True(t, timelines[0].Payload.Code == diagnosis.Payload.Code)
	timelinePyschotherapy, _ := timeline_service.TimelineServicePsychotherapyMod.SafeResolve()
	psychotherapyEntry, err := timelinePyschotherapy.FindById(ctx, *res.ScheinItem.ScheinDetail.Psychotherapy[0].Id)
	require.Nil(t, err)
	require.True(t, psychotherapyEntry.Payload.Status == patient_encounter_repo.READY_TO_BILL)
}

func (s *ScheinTestSuite) Test_Should_Correct_AmountBill_When_Before_2017() {
	t := s.T()
	before01042017, _ := util.ConvertDateStringToTime("25032017") // NOTE: 25-03-2017
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	serviceCodeDocument := "30934"
	currentQuarter := util.ToYearQuarter(time.Now().UnixMilli())
	request := schein_bff.CreateScheinRequest{
		PatientId:        s.patient.PatientId,
		DoctorId:         &s.doctor.EmployeeId,
		ScheinMainGroup:  "KV",
		KvScheinSubGroup: util.NewPointer("00"),
		KvTreatmentCase:  schein_common.TCKvOutpatient,
		InsuranceId:      s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
		G4101Year:        currentQuarter.Year,
		G4101Quarter:     currentQuarter.Quarter,
		G4122:            util.NewPointer("00"),
		ScheinDetails: &schein_common.ScheinDetail{
			G4104: util.NewPointer(int32(61125)),
			G4122: util.NewPointer("00"),
			G4106: util.NewPointer("00"),
			Psychotherapy: []schein_common.Psychotherapy{
				{
					Ps4235: util.NewPointer(int64(1696550400000)),
					Ps4247: util.NewPointer(before01042017.UnixMilli()), // NOTE: request date
					Ps4245: util.NewPointer(int64(2)),
					GroupServicesCodeBefore2017: []schein_common.GroupServicesCodeBefore2017{
						{
							ServiceCode:    serviceCodeDocument,
							AmountBilled:   1,
							AmountApproval: 3,
						},
					},
				},
			},
		},
	}
	res, _ := s.scheinApp.CreateSchein(ctx, request)
	timelinePyschotherapy, _ := timeline_service.TimelineServicePsychotherapyMod.SafeResolve()
	psyId := res.ScheinItem.ScheinDetail.Psychotherapy[0].Id
	psychotherapy, err := timelinePyschotherapy.FindById(ctx, *psyId)
	require.Nil(t, err)
	require.NotNil(t, psychotherapy)
	require.Equal(t, int32(1), psychotherapy.Payload.Entries[serviceCodeDocument].AmountBilled)
	require.Equal(t, int32(3), *psychotherapy.Payload.Entries[serviceCodeDocument].AmountApproval)

	timelineServiceEncounter, _ := timeline_service.TimelineServiceEncounterMod.SafeResolve()
	timelineServiceEncounter.Create(ctx, timeline_repo.TimelineEntity[patient_encounter_repo.EncounterServiceTimeline]{
		TreatmentDoctorId: s.doctor.EmployeeId,
		PatientId:         s.patient.PatientId,
		ScheinIds: []uuid.UUID{
			res.ScheinItem.ScheinId,
		},
		Payload: patient_encounter_repo.EncounterServiceTimeline{
			Code: serviceCodeDocument,
		},
	})

	psychotherapy, err = timelinePyschotherapy.FindById(ctx, *psyId)
	require.Nil(t, err)
	require.NotNil(t, psychotherapy)
	require.Equal(t, int32(2), psychotherapy.Payload.Entries[serviceCodeDocument].AmountBilled)
	require.Equal(t, int32(3), *psychotherapy.Payload.Entries[serviceCodeDocument].AmountApproval)
}

func (s *ScheinTestSuite) Test_Psychotherapy_Cover_2_Quarters_With_Leftovers() {
	t := s.T()
	t.Skip()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	serviceCodeDocument := "30934"
	currentQuarter := util.ToYearQuarter(time.Now().UnixMilli())
	totalQuarter := currentQuarter.Year*4 + currentQuarter.Quarter
	targetTotalQuarter := totalQuarter - 2
	quater := util.YearQuarter{
		Year:    targetTotalQuarter / 4,
		Quarter: targetTotalQuarter % 4,
	}
	request := schein_bff.CreateScheinRequest{
		PatientId:        s.patient.PatientId,
		DoctorId:         &s.doctor.EmployeeId,
		ScheinMainGroup:  "KV",
		KvScheinSubGroup: util.NewPointer("00"),
		KvTreatmentCase:  schein_common.TCKvOutpatient,
		InsuranceId:      s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
		G4101Year:        quater.Year,
		G4101Quarter:     quater.Quarter,
		G4122:            util.NewPointer("00"),
		ScheinDetails: &schein_common.ScheinDetail{
			G4104: util.NewPointer(int32(61125)),
			G4122: util.NewPointer("00"),
			G4106: util.NewPointer("00"),
			Psychotherapy: []schein_common.Psychotherapy{
				{
					Ps4235: util.NewPointer(int64(1672506000000)),
					Ps4247: util.NewPointer(int64(1672506000000)),
					Ps4245: util.NewPointer(int64(3)), // amount aproval
					GroupServicesCode: []schein_common.GroupServicesCode{
						{
							ServiceCode:  serviceCodeDocument,
							AmountBilled: 1,
						},
					},
				},
			},
		},
	}
	res, err := s.scheinApp.CreateSchein(ctx, request)
	require.NoError(t, err)
	require.NotNil(t, res)
	require.NotNil(t, res.ScheinItem.ScheinId)

	// document service code
	timelineServiceEncounter, _ := timeline_service.TimelineServiceEncounterMod.SafeResolve()
	serviceEntry, err := timelineServiceEncounter.Create(ctx, timeline_repo.TimelineEntity[patient_encounter_repo.EncounterServiceTimeline]{
		TreatmentDoctorId: s.doctor.EmployeeId,
		PatientId:         s.patient.PatientId,
		ScheinIds: []uuid.UUID{
			res.ScheinItem.ScheinId,
		},
		Payload: patient_encounter_repo.EncounterServiceTimeline{
			Code: serviceCodeDocument,
		},
		SelectedDate: util.ConvertMillisecondsToTime(*quater.StartTime()),
	})
	require.Nil(t, err)
	timelinePyschotherapy, _ := timeline_service.TimelineServicePsychotherapyMod.SafeResolve()
	pyschotherapy, err := timelinePyschotherapy.FindById(ctx, *res.ScheinItem.ScheinDetail.Psychotherapy[0].Id)

	// verify amount approval must increase 1
	require.Nil(t, err)
	require.NotNil(t, pyschotherapy)
	require.Equal(t, int32(2), pyschotherapy.Payload.GetTotalApproval())

	// verify hint error
	entry, err := timelineServiceEncounter.FindById(ctx, *serviceEntry.Id)
	require.Nil(t, err)
	errors := *entry.Payload.Errors
	errorCode := slice.Map(errors, func(err *patient_encounter_repo.EncounterItemError) string {
		return err.ErrorCode
	})
	require.True(t, slice.Contains(errorCode, string(validation_timeline.ServiceErrorCode_Psychotherapy)))
}

func (s *ScheinTestSuite) Test_Psychotherapy_Approval_With_Referene_Person() {
	t := s.T()
	quater := util.ToYearQuarter(time.Now().UnixMilli())
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	serviceCodeA := "03222"
	serviceCodeB := "03360"

	// create schein
	request := schein_bff.CreateScheinRequest{
		PatientId:        s.patient.PatientId,
		DoctorId:         &s.doctor.EmployeeId,
		ScheinMainGroup:  "KV",
		KvScheinSubGroup: util.NewPointer("00"),
		KvTreatmentCase:  schein_common.TCKvOutpatient,
		InsuranceId:      s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
		G4101Year:        quater.Year,
		G4101Quarter:     quater.Quarter,
		G4122:            util.NewPointer("00"),
		ScheinDetails: &schein_common.ScheinDetail{
			G4104: util.NewPointer(int32(61125)),
			G4122: util.NewPointer("00"),
			G4106: util.NewPointer("00"),
			Psychotherapy: []schein_common.Psychotherapy{
				{
					Ps4235: util.NewPointer(int64(1696550400000)),
					Ps4247: util.NewPointer(int64(1696377600000)),
					Ps4245: util.NewPointer(int64(2)), // amount aproval
					Ps4255: util.NewPointer(int32(4)), // amount aproval referene person
					GroupServicesCode: []schein_common.GroupServicesCode{
						{
							ServiceCode:  serviceCodeA,
							AmountBilled: 1,
						},
					},
					GroupCareGiver: []schein_common.GroupServicesCode{
						{
							ServiceCode:  serviceCodeB,
							AmountBilled: 2,
						},
					},
				},
			},
		},
	}
	res, err := s.scheinApp.CreateSchein(ctx, request)
	require.NotNil(t, res)
	require.NotNil(t, res.ScheinItem.ScheinId)
	require.NoError(t, err)

	// document service code A
	timelineServiceEncounter, _ := timeline_service.TimelineServiceEncounterMod.SafeResolve()
	_, err = timelineServiceEncounter.Create(ctx, timeline_repo.TimelineEntity[patient_encounter_repo.EncounterServiceTimeline]{
		TreatmentDoctorId: s.doctor.EmployeeId,
		PatientId:         s.patient.PatientId,
		SelectedDate:      util.ConvertMillisecondsToTime(time.Now().UnixMilli()),
		ScheinIds: []uuid.UUID{
			res.ScheinItem.ScheinId,
		},
		Payload: patient_encounter_repo.EncounterServiceTimeline{
			Code: serviceCodeA,
		},
	})
	require.NoError(t, err)
	// get psychotherapy timeline
	timelinePyschotherapy, _ := timeline_service.TimelineServicePsychotherapyMod.SafeResolve()
	pyschotherapy, err := timelinePyschotherapy.FindById(ctx, *res.ScheinItem.ScheinDetail.Psychotherapy[0].Id)
	require.NoError(t, err)
	// validate amount approval should be 4
	require.Equal(t, int32(4), pyschotherapy.Payload.GetTotalApproval())

	// document service code B
	_, err = timelineServiceEncounter.Create(ctx, timeline_repo.TimelineEntity[patient_encounter_repo.EncounterServiceTimeline]{
		TreatmentDoctorId: s.doctor.EmployeeId,
		PatientId:         s.patient.PatientId,
		SelectedDate:      util.ConvertMillisecondsToTime(time.Now().UnixMilli()),
		ScheinIds: []uuid.UUID{
			res.ScheinItem.ScheinId,
		},
		Payload: patient_encounter_repo.EncounterServiceTimeline{
			Code: serviceCodeB,
		},
	})
	require.NoError(t, err)
	pyschotherapy, err = timelinePyschotherapy.FindById(ctx, *res.ScheinItem.ScheinDetail.Psychotherapy[0].Id)
	require.NoError(t, err)
	// validate amount approval should be 5
	require.Equal(t, int32(5), pyschotherapy.Payload.GetTotalApproval())
}

// NOTE: create schein -> document diagnose + service -> document 88130 -> takeover timeline entry -> check isTechnicalSchein.
func (s *ScheinTestSuite) Test_Should_Has_TechnicalSchein_When_Document_88130() {
	t := s.T()
	prevQuarterDate := getQuarterDate(time.Now(), 1) // NOTE: current_quarter - 1
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	serviceCodeDocument := "30934"
	prevYearQuarter := util.ToYearQuarter(prevQuarterDate.UnixMilli())
	request := schein_bff.CreateScheinRequest{
		PatientId:        s.patient.PatientId,
		DoctorId:         &s.doctor.EmployeeId,
		ScheinMainGroup:  "KV",
		KvScheinSubGroup: util.NewPointer("00"),
		KvTreatmentCase:  schein_common.TCKvOutpatient,
		InsuranceId:      s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
		G4101Year:        prevYearQuarter.Year,
		G4101Quarter:     prevYearQuarter.Quarter,
		G4122:            util.NewPointer("00"),
		ScheinDetails: &schein_common.ScheinDetail{
			G4104: util.NewPointer(int32(61125)),
			G4122: util.NewPointer("00"),
			G4106: util.NewPointer("00"),
			Psychotherapy: []schein_common.Psychotherapy{
				{
					Ps4235: util.NewPointer(int64(prevQuarterDate.UnixMilli())), // NOTE: approval date
					Ps4247: util.NewPointer(int64(prevQuarterDate.UnixMilli())), // NOTE: request date
					Ps4245: util.NewPointer(int64(2)),                           // NOTE: amount approval
					GroupServicesCode: []schein_common.GroupServicesCode{
						{
							ServiceCode:  serviceCodeDocument,
							AmountBilled: 1,
						},
					},
				},
			},
		},
		AssignedToBsnrId: s.doctor.BsnrId,
	}
	createdSchein, _ := s.scheinApp.CreateSchein(ctx, request)

	// NOTE: create service entry
	timelineServiceEncounter, _ := timeline_service.TimelineServiceEncounterMod.SafeResolve()
	createdServiceEntry, _ := timelineServiceEncounter.Create(ctx, timeline_repo.TimelineEntity[patient_encounter_repo.EncounterServiceTimeline]{
		TreatmentDoctorId: s.doctor.EmployeeId,
		PatientId:         s.patient.PatientId,
		ScheinIds: []uuid.UUID{
			createdSchein.ScheinItem.ScheinId,
		},
		Payload: patient_encounter_repo.EncounterServiceTimeline{
			Code: serviceCodeDocument,
		},
		AssignedToBsnrId: s.doctor.BsnrId,
	})

	// NOTE: create diagnose entry
	timelineDiagnoseEncounter, _ := timeline_service.TimelineServiceDiagnosisMod.SafeResolve()
	timelineDiagnoseEncounter.Create(ctx, timeline_repo.TimelineEntity[patient_encounter_repo.EncounterDiagnoseTimeline]{
		TreatmentDoctorId: s.doctor.EmployeeId,
		PatientId:         s.patient.PatientId,
		ScheinIds: []uuid.UUID{
			createdSchein.ScheinItem.ScheinId,
		},
		Payload: patient_encounter_repo.EncounterDiagnoseTimeline{
			Code:      "E10.01",
			Certainty: util.NewPointer(patient_encounter_repo.G),
		},
		AssignedToBsnrId: s.doctor.BsnrId,
	})
	serviceEntry, _ := timelineServiceEncounter.FindById(ctx, *createdServiceEntry.Id)
	hasPsychotherapyHint := slice.Any(*serviceEntry.Payload.Errors, func(err *patient_encounter_repo.EncounterItemError) bool {
		return string(err.ErrorCode) == string(validation_timeline.ServiceErrorCode_Psychotherapy_88130)
	})
	require.True(t, hasPsychotherapyHint, "Should have 88130 hint")

	// NOTE: check amountBilled/amountApproval should be 2/2
	psyId := createdSchein.ScheinItem.ScheinDetail.Psychotherapy[0].Id
	timelinePyschotherapy, _ := timeline_service.TimelineServicePsychotherapyMod.SafeResolve()
	psychotherapy, err := timelinePyschotherapy.FindById(ctx, *psyId)
	require.Nil(t, err)
	require.Equal(t, int32(2), psychotherapy.Payload.Entries[serviceCodeDocument].AmountBilled)
	require.Equal(t, int32(2), psychotherapy.Payload.AmountApproval)

	respDocument88130, _ := s.timelineApp.Document88130(ctx, timeline.Document88130Request{
		TimelineModel: common.TimelineModel{
			TreatmentDoctorId: s.doctor.EmployeeId,
			PatientId:         s.patient.PatientId,
			ScheinIds:         []uuid.UUID{},
			EncounterServiceTimeline: &patient_encounter_repo.EncounterServiceTimeline{
				Code: "88130",
			},
			Type:             util.NewPointer(common.TimelineEntityType_Service),
			CreatedAt:        new(int64),
			CreatedAtString:  new(string),
			CreatedBy:        util.NewPointer(s.doctor.EmployeeId),
			SelectedDate:     util.NowUnixMillis(ctx),
			AssignedToBsnrId: s.doctor.BsnrId,
		},
		ServiceEntryId: *serviceEntry.Id,
	})

	takeOverIds := slice.Map(respDocument88130.TakeoverDiagnosis, func(d common.TimelineModel) uuid.UUID {
		return *d.Id
	})
	timelinePyschotherapy.TakeOverDiagnosisWithScheinId(ctx, timeline.TakeOverDiagnosisWithScheinIdRequest{
		ScheinId:         *respDocument88130.ScheinId,
		TimelineModelIds: takeOverIds,
	})

	scheinRepo := schein_repo.NewScheinRepoDefaultRepository()
	scheinEntity, _ := scheinRepo.FindById(ctx, *respDocument88130.ScheinId)
	require.True(t, util.GetPointerValue(scheinEntity.IsTechnicalSchein), "Should be technical schein")
}

func (s *ScheinTestSuite) Test_Psycotherapy_Remove_Service_Code_In_Approval() {
	t := s.T()
	quater := util.ToYearQuarter(time.Now().UnixMilli())
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	serviceCode := "03222"
	// create schein
	request := schein_bff.CreateScheinRequest{
		PatientId:        s.patient.PatientId,
		DoctorId:         &s.doctor.EmployeeId,
		ScheinMainGroup:  "KV",
		KvScheinSubGroup: util.NewPointer("00"),
		KvTreatmentCase:  schein_common.TCKvOutpatient,
		InsuranceId:      s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
		G4101Year:        quater.Year,
		G4101Quarter:     quater.Quarter,
		G4122:            util.NewPointer("00"),
		ScheinDetails: &schein_common.ScheinDetail{
			G4104: util.NewPointer(int32(61125)),
			G4122: util.NewPointer("00"),
			G4106: util.NewPointer("00"),
			Psychotherapy: []schein_common.Psychotherapy{
				{
					Ps4235: util.NewPointer(int64(1696550400000)),
					Ps4247: util.NewPointer(int64(1696377600000)),
					Ps4245: util.NewPointer(int64(3)), // amount aproval
					GroupServicesCode: []schein_common.GroupServicesCode{
						{
							ServiceCode:  serviceCode,
							AmountBilled: 1,
						},
					},
				},
			},
		},
	}
	res, err := s.scheinApp.CreateSchein(ctx, request)
	require.NotNil(t, res)
	require.NotNil(t, res.ScheinItem.ScheinId)
	require.NoError(t, err)

	// document service code
	timelineServiceEncounter, _ := timeline_service.TimelineServiceEncounterMod.SafeResolve()
	timeline, err := timelineServiceEncounter.Create(ctx, timeline_repo.TimelineEntity[patient_encounter_repo.EncounterServiceTimeline]{
		TreatmentDoctorId: s.doctor.EmployeeId,
		SelectedDate:      util.ConvertMillisecondsToTime(time.Now().UnixMilli()),
		PatientId:         s.patient.PatientId,
		ScheinIds: []uuid.UUID{
			res.ScheinItem.ScheinId,
		},
		Payload: patient_encounter_repo.EncounterServiceTimeline{
			Code: serviceCode,
		},
	})
	require.NoError(t, err)
	// get psychotherapy timeline
	timelinePyschotherapy, _ := timeline_service.TimelineServicePsychotherapyMod.SafeResolve()
	pyschotherapy, err := timelinePyschotherapy.FindById(ctx, *res.ScheinItem.ScheinDetail.Psychotherapy[0].Id)
	require.NoError(t, err)
	// validate amount approval should be 2
	require.Equal(t, int32(2), pyschotherapy.Payload.GetTotalApproval())

	// remove service code
	err = timelineServiceEncounter.Remove(ctx, *timeline.Id, false)
	require.NoError(t, err)
	pyschotherapy, err = timelinePyschotherapy.FindById(ctx, *res.ScheinItem.ScheinDetail.Psychotherapy[0].Id)
	require.NoError(t, err)
	// validate amount approval should be 1
	require.Equal(t, int32(1), pyschotherapy.Payload.GetTotalApproval())
}

func (s *ScheinTestSuite) Test_Update_Patient_Snapshot() {
	t := s.T()
	var scheinItem *schein_common.ScheinItem
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	scheinService, err := schein_service.ScheinServiceMod.SafeResolve()
	require.NoError(t, err)

	t.Run("update patient snapshot", func(t *testing.T) {
		quater := util.ToYearQuarter(time.Now().UnixMilli())
		request := schein_bff.CreateScheinRequest{
			PatientId:        s.patient.PatientId,
			DoctorId:         &s.doctor.EmployeeId,
			ScheinMainGroup:  "KV",
			KvScheinSubGroup: util.NewPointer("00"),
			KvTreatmentCase:  schein_common.TCKvOutpatient,
			InsuranceId:      s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
			G4101Year:        quater.Year,
			G4101Quarter:     quater.Quarter,
			G4122:            util.NewPointer("00"),
			ScheinDetails: &schein_common.ScheinDetail{
				G4106: util.NewPointer("00"),
				G4104: util.NewPointer(int32(61125)),
			},
		}
		res, err := scheinService.CreateSchein(ctx, request)
		require.NoError(t, err)
		scheinItem = res.ScheinItem

		require.Nil(t, res.FieldErrors)
		require.NotNil(t, res)
		patientService, err := patient_profile_service.PatientProfileServiceMod.SafeResolve()
		require.NoError(t, err)

		s.patient.Profile.PatientInfo.InsuranceInfos[0].ReadCardDatas = []patient_profile_common.ReadCardModel{
			{
				FromCardType: patient_profile_common.FromCardType_EGK,
				ProofOfInsurance: &patient_profile_common.ProofOfInsurance{
					OnlineCheckDate: time.Now().UnixMilli(),
					ResultCode:      2,
				},
			},
		}

		updatePatientRes, err := patientService.UpdatePatientProfileV2(ctx, &patient_profile.UpdatePatientProfileV2Request{
			Id:          &s.patient.PatientId,
			PatientInfo: s.patient.Profile.PatientInfo,
		})
		require.NoError(t, err)
		require.NotNil(t, updatePatientRes)
		require.Nil(t, updatePatientRes.UpdateErrorStatus)

		scheinById, err := scheinService.GetScheinById(ctx, res.ScheinItem.ScheinId)
		require.NoError(t, err)
		require.NotNil(t, scheinById)
		require.NotNil(t, scheinById.PatientSnapshot)
		require.Equal(t, scheinById.PatientSnapshot.PatientId, s.patient.PatientId)
	})

	t.Run("should fail when update insurance id which not in snapshot", func(t *testing.T) {
		err = scheinService.UpdateSchein(ctx, schein_common.UpdateScheinRequest{
			ScheinId:      scheinItem.ScheinId,
			ScheinDetails: &scheinItem.ScheinDetail,
			InsuranceId:   uuid.New(),
		})
		titanError := &titan.CommonException{}
		isTitanCommonError := errors.As(err, &titanError)
		require.True(t, isTitanCommonError)
		require.Equal(t, "Insurance not in snapshot", titanError.Message)
	})

	t.Run("should success when update insurance id", func(t *testing.T) {
		err = scheinService.UpdateSchein(ctx, schein_common.UpdateScheinRequest{
			ScheinId:      scheinItem.ScheinId,
			ScheinDetails: &scheinItem.ScheinDetail,
			InsuranceId:   s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
			PatientId:     s.patient.PatientId,
			DoctorId:      s.doctor.EmployeeId,
		})
		require.NoError(t, err)
	})

}

func getQuarterDate(date time.Time, subtract int) time.Time {
	date = date.AddDate(0, -subtract*3, 0)
	date = time.Date(date.Year(), date.Month(), 1, 0, 0, 0, 0, date.Location())
	return date
}

/*
if the patient read card in the selected quarter return 2
if the patient not read card by the selected quarter return > 2
*/
func (s *ScheinTestSuite) Test_GetKTabs() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	scheinService, err := schein_service.ScheinServiceMod.SafeResolve()
	require.NoError(t, err)

	patientProfileContent, _ := os.ReadFile("./testdata/mock-patient-profile.json")
	var patientProfileResponse patient_profile.PatientProfileResponse
	_ = json.Unmarshal(patientProfileContent, &patientProfileResponse)
	insuranceInfo := patientProfileResponse.PatientInfo.InsuranceInfos[0]

	patientCreated, err := s.patientProfileApp.CreatePatientProfileV2(ctx, &patient_profile.CreatePatientProfileV2Request{
		PatientInfo: patientProfileResponse.PatientInfo,
	})
	require.NoError(t, err)
	require.NotNil(t, patientCreated.Id)

	res, err := scheinService.GetKTABs(ctx, schein_bff.GetKTABsRequest{
		VKNR:         insuranceInfo.InsuranceCompanyId,
		SpecialGroup: insuranceInfo.SpecialGroup,
		PatientId:    *patientCreated.Id,
		Quarter:      1,
		Year:         2023,
		Bsnr:         s.doctor.Bsnr,
	})
	require.NoError(t, err)
	require.NotNil(t, res)
	require.Equal(t, len(res.KTABValue), 1)

	res, err = scheinService.GetKTABs(ctx, schein_bff.GetKTABsRequest{
		VKNR:         insuranceInfo.InsuranceCompanyId,
		SpecialGroup: insuranceInfo.SpecialGroup,
		PatientId:    *patientCreated.Id,
		Quarter:      2,
		Year:         2023,
		Bsnr:         s.doctor.Bsnr,
	})
	require.NoError(t, err)
	require.NotNil(t, res)
	require.Equal(t, len(res.KTABValue), 1)

	res, err = scheinService.GetKTABs(ctx, schein_bff.GetKTABsRequest{
		VKNR:         insuranceInfo.InsuranceCompanyId,
		SpecialGroup: insuranceInfo.SpecialGroup,
		PatientId:    *patientCreated.Id,
		Quarter:      3,
		Year:         2023,
		Bsnr:         s.doctor.Bsnr,
	})
	require.NoError(t, err)
	require.NotNil(t, res)
	require.Equal(t, len(res.KTABValue), 1)

	res, err = scheinService.GetKTABs(ctx, schein_bff.GetKTABsRequest{
		VKNR:         insuranceInfo.InsuranceCompanyId,
		SpecialGroup: insuranceInfo.SpecialGroup,
		PatientId:    *patientCreated.Id,
		Quarter:      4,
		Year:         2023,
		Bsnr:         s.doctor.Bsnr,
	})
	require.NoError(t, err)
	require.NotNil(t, res)
	require.Equal(t, len(res.KTABValue), 1)
}

func (s *ScheinTestSuite) Test_CreatePrivateSchein_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	privateContractGroup, err := s.privateContractGroupApp.CreatePrivateContractGroup(ctx, genCreatePCGRequest())
	require.NoError(t, err)

	request := schein_bff.CreatePrivateScheinRequest{
		Schein: private_schein_common.PrivateScheinItem{
			PatientId:              (s.patient.PatientId),
			DoctorId:               (s.doctor.EmployeeId),
			InsuranceId:            util.NewPointer(s.patient.Profile.PatientInfo.InsuranceInfos[0].Id),
			IssueDate:              util.NowUnixMillis(ctx),
			PrivateContractGroupId: privateContractGroup.Id,
			Discount:               10,
			DiscountUnit:           private_schein_common.DiscountUnit_Percent,
			InvoiceSendingType:     private_schein_common.InvoiceSendingType_Address,
			ExcludeFromBilling:     false,
			ScheinMainGroup:        schein_common.PRIVATE,
			IsVat:                  false,
			IkNumber:               util.NewPointer(s.patient.Profile.PatientInfo.InsuranceInfos[0].IkNumber),
		},
	}
	res, err := s.scheinApp.CreatePrivateSchein(ctx, request)
	require.NoError(t, err)
	require.NotNil(t, res)
}

func (s *ScheinTestSuite) Test_IsValidPrivateSchein() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	privateContractGroup, err := s.privateContractGroupApp.CreatePrivateContractGroup(ctx, genCreatePCGRequest())
	require.NoError(t, err)

	// Valid request
	request := schein_bff.IsValidPrivateScheinRequest{
		Schein: private_schein_common.PrivateScheinItem{
			PatientId:              (s.patient.PatientId),
			DoctorId:               (s.doctor.EmployeeId),
			InsuranceId:            util.NewPointer(s.patient.Profile.PatientInfo.InsuranceInfos[0].Id),
			IssueDate:              util.NowUnixMillis(ctx),
			PrivateContractGroupId: privateContractGroup.Id,
			Discount:               10,
			DiscountUnit:           private_schein_common.DiscountUnit_Percent,
			InvoiceSendingType:     private_schein_common.InvoiceSendingType_Address,
			ExcludeFromBilling:     false,
			ScheinMainGroup:        schein_common.PRIVATE,
			IsVat:                  false,
			IkNumber:               util.NewPointer(s.patient.Profile.PatientInfo.InsuranceInfos[0].IkNumber),
		},
	}

	res, err := s.scheinApp.IsValidPrivateSchein(ctx, request)
	require.NoError(t, err)
	require.Nil(t, res)

	// Invalid request
	inValidRequest := schein_bff.IsValidPrivateScheinRequest{
		Schein: private_schein_common.PrivateScheinItem{
			PatientId:              (s.patient2.PatientId),
			DoctorId:               (s.doctor.EmployeeId),
			InsuranceId:            util.NewPointer(s.patient2.Profile.PatientInfo.InsuranceInfos[0].Id),
			IssueDate:              652403779,
			PrivateContractGroupId: privateContractGroup.Id,
			Discount:               10,
			DiscountUnit:           private_schein_common.DiscountUnit_Percent,
			InvoiceSendingType:     private_schein_common.InvoiceSendingType_Address,
			ExcludeFromBilling:     false,
			ScheinMainGroup:        schein_common.PRIVATE,
			IsVat:                  false,
			IkNumber:               util.NewPointer(s.patient2.Profile.PatientInfo.InsuranceInfos[0].IkNumber),
		},
	}

	res, err = s.scheinApp.IsValidPrivateSchein(ctx, inValidRequest)
	require.NoError(t, err)
	require.NotNil(t, res)
}

func (s *ScheinTestSuite) Test_GetGoaFactorValue_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	privateContractGroup, err := s.privateContractGroupApp.CreatePrivateContractGroup(ctx, genCreatePCGRequest())
	require.NoError(t, err)

	request := schein_bff.CreatePrivateScheinRequest{
		Schein: private_schein_common.PrivateScheinItem{
			PatientId:              (s.patient.PatientId),
			DoctorId:               (s.doctor.EmployeeId),
			InsuranceId:            util.NewPointer(s.patient.Profile.PatientInfo.InsuranceInfos[0].Id),
			IssueDate:              util.NowUnixMillis(ctx),
			PrivateContractGroupId: privateContractGroup.Id,
			Discount:               10,
			DiscountUnit:           private_schein_common.DiscountUnit_Percent,
			InvoiceSendingType:     private_schein_common.InvoiceSendingType_Address,
			ExcludeFromBilling:     false,
			ScheinMainGroup:        schein_common.PRIVATE,
			IsVat:                  false,
			IkNumber:               util.NewPointer(s.patient.Profile.PatientInfo.InsuranceInfos[0].IkNumber),
		},
	}
	scheinRes, err := s.scheinApp.CreatePrivateSchein(ctx, request)
	require.NoError(t, err)
	res, err := s.scheinApp.GetGoaFactorValue(ctx, schein_bff.GetGoaFactorValueRequest{
		ScheinId:  scheinRes.ScheinId,
		GoaNumber: "1234",
	})
	require.NoError(t, err)
	require.Equal(t, float64(1.22), res.Value)
}

func (s *ScheinTestSuite) Test_UpdateApprovalDateForPsychotherapy_Success() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	timelinesService, _ := timeline_service.TimelineServicePsychotherapyMod.SafeResolve()
	serviceCodeDocument := "311122"

	insurance := s.patient.Profile.PatientInfo.GetActiveInsurance()
	require.NotNil(t, insurance)
	require.NotNil(t, insurance.StartDate)
	insuranceStartDate := util.ConvertMillisecondsToTime(*insurance.StartDate)
	currentQuarter := util.ToYearQuarter(*insurance.StartDate)
	request := schein_bff.CreateScheinRequest{
		PatientId:        s.patient.PatientId,
		DoctorId:         &s.doctor.EmployeeId,
		ScheinMainGroup:  "KV",
		KvScheinSubGroup: util.NewPointer("00"),
		KvTreatmentCase:  schein_common.TCKvOutpatient,
		InsuranceId:      insurance.Id,
		G4101Year:        currentQuarter.Year,
		G4101Quarter:     currentQuarter.Quarter,
		G4122:            util.NewPointer("00"),
		ScheinDetails: &schein_common.ScheinDetail{
			G4104:  util.NewPointer(int32(61125)),
			G4122:  util.NewPointer("00"),
			G4106:  util.NewPointer("00"),
			Ps4234: util.NewBool(true),
			Psychotherapy: []schein_common.Psychotherapy{
				{
					Ps4235: util.NewPointer(int64(1704042000000)),
					Ps4245: util.NewPointer(int64(2)),
					GroupServicesCode: []schein_common.GroupServicesCode{
						{
							ServiceCode:  serviceCodeDocument,
							AmountBilled: 0,
						},
					},
				},
			},
		},
	}
	res, err := s.scheinApp.CreateSchein(ctx, request)
	require.NoError(t, err)
	require.NotNil(t, res)

	// get next day
	firstSelectedDate := insuranceStartDate.Add(24 * time.Hour)
	// get next 10days
	secondSelectedDate := insuranceStartDate.Add(10 * 24 * time.Hour)

	timelineServiceEncounter, _ := timeline_service.TimelineServiceEncounterMod.SafeResolve()
	timeline1, _ := timelineServiceEncounter.Create(ctx, timeline_repo.TimelineEntity[patient_encounter_repo.EncounterServiceTimeline]{
		TreatmentDoctorId: s.doctor.EmployeeId,
		PatientId:         s.patient.PatientId,
		ScheinIds: []uuid.UUID{
			res.ScheinItem.ScheinId,
		},
		Payload: patient_encounter_repo.EncounterServiceTimeline{
			Code: serviceCodeDocument,
		},
		SelectedDate: firstSelectedDate,
	})

	timeline2, _ := timelineServiceEncounter.Create(ctx, timeline_repo.TimelineEntity[patient_encounter_repo.EncounterServiceTimeline]{
		TreatmentDoctorId: s.doctor.EmployeeId,
		PatientId:         s.patient.PatientId,
		ScheinIds: []uuid.UUID{
			res.ScheinItem.ScheinId,
		},
		Payload: patient_encounter_repo.EncounterServiceTimeline{
			Code: serviceCodeDocument,
		},
		SelectedDate: secondSelectedDate,
	})
	fetchRes, _ := s.scheinApp.GetScheinDetailById(ctx, schein_common.GetScheinDetailByIdRequest{
		ScheinId: res.ScheinItem.ScheinId,
	})

	update := schein_common.UpdateScheinRequest{
		ScheinId:         fetchRes.ScheinId,
		PatientId:        s.patient.PatientId,
		ScheinMainGroup:  "KV",
		KvScheinSubGroup: util.NewPointer("00"),
		KvTreatmentCase:  schein_common.TCKvOutpatient,
		InsuranceId:      s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
		G4101Year:        &currentQuarter.Year,
		G4101Quarter:     &currentQuarter.Quarter,
		ScheinDetails: &schein_common.ScheinDetail{
			G4104:  util.NewPointer(int32(61125)),
			G4122:  util.NewPointer("00"),
			G4106:  util.NewPointer("00"),
			Ps4234: util.NewBool(true),
			Psychotherapy: slice.Map(fetchRes.ScheinDetails.Psychotherapy, func(p schein_common.Psychotherapy) schein_common.Psychotherapy {
				newApprovalDate := util.ConvertMillisecondsToTime(*insurance.StartDate).Add(2 * 24 * time.Hour) // One day after insurance start date
				p.Ps4235 = util.NewPointer(newApprovalDate.UnixMilli())
				return p
			}),
		},
	}
	s.scheinApp.UpdateSchein(ctx, update)

	resp, err := s.scheinApp.GetScheinDetailById(ctx, schein_common.GetScheinDetailByIdRequest{
		ScheinId: fetchRes.ScheinId,
	})
	require.NoError(t, err)
	require.NotNil(t, resp)
	timeline1, _ = timelineServiceEncounter.FindById(ctx, *timeline1.Id)
	timeline2, _ = timelineServiceEncounter.FindById(ctx, *timeline2.Id)
	require.Nil(t, timeline1.Payload.ApprovalStatus)
	require.NotNil(t, timeline2.Payload.ApprovalStatus)
	psychotherapyEntries, _ := timelinesService.GetTherapies(ctx, timeline.GetTherapiesRequest{
		PatientId: resp.PatientId,
		ScheinId:  resp.ScheinId,
	})
	require.Len(t, psychotherapyEntries.Pyschotherapies, 1)
	require.Equal(t, psychotherapyEntries.Pyschotherapies[0].TimelineModel.EncounterPsychotherapy.GetTotalApproval(), int32(1))
}

func genCreatePCGRequest() *private_contract_group.CreatePrivateContractGroupRequest {
	return &private_contract_group.CreatePrivateContractGroupRequest{
		ContractGroup: &pcg_common.PrivateContractGroup{
			ContractNumber:         int32(9999),
			Description:            gofakeit.JobDescriptor(),
			MedicalServiceFactor:   1.22,
			TechnicalServiceFactor: 1.33,
			LabServiceFactor:       1.44,
			FeeServiceFactor:       1.55,
			AdditionalFactor:       1.66,
			Group:                  "KVB",
			ValueAddTax:            pcg_common.VAT_WithMwstsatz,
		},
	}
}

func (s *ScheinTestSuite) Test_CoverBackwardDocument() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	tresps, err := s.db.Mvz.ScheinRepository.CreateDefaultSchein(ctx, schein_repo.CreateDefaultScheinRequest{
		MainGroup:          string(schein_common.HZV),
		PatientId:          s.patient.PatientId,
		DoctorId:           s.doctor.EmployeeId,
		ContractId:         util.NewString("BKK_FA_GASTRO_BW"),
		ChargeSystemId:     util.NewString("BKK_FA_GASTRO_BW"),
		InsuranceId:        s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
		InsuranceCompanyId: s.patient.Profile.PatientInfo.InsuranceInfos[0].InsuranceCompanyId,
	})
	require.Nil(t, err)
	require.NotNil(t, tresps)
	resps := *tresps
	require.Len(t, resps, 1)
	schein := resps[0]
	selectedDate, err := time.Parse(util.DateFormat, "01012019")
	require.Nil(t, err)
	quarterYear := util.ToYearQuarter(selectedDate.UnixMilli())
	respSchein, err := s.scheinApp.CreateSvScheinAutomaticly(ctx, schein_common.CreateSvScheinAutomaticlyRequest{
		ReferenceScheinIds: []uuid.UUID{*schein.Id},
		SelectedDate:       selectedDate.UnixMilli(),
	})
	require.Nil(t, err)
	require.NotNil(t, respSchein)
	newScheins, err := s.scheinApp.GetScheinDetailByIds(ctx, schein_common.GetScheinDetailByIdsRequest{
		ScheinIds: respSchein.ScheinIds,
	})
	require.Nil(t, err)
	require.NotNil(t, newScheins)
	for _, newSchein := range newScheins.Data {
		require.Equal(t, *newSchein.G4101Year*4+*newSchein.G4101Quarter, quarterYear.Year*4+quarterYear.Quarter)
	}
}

func (s *ScheinTestSuite) Test_MarkAsReferralThenRemove() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	repoScheins, err := s.db.Mvz.ScheinRepository.CreateDefaultSchein(ctx, schein_repo.CreateDefaultScheinRequest{
		MainGroup:          string(schein_common.FAV),
		PatientId:          s.patient.PatientId,
		DoctorId:           s.doctor.EmployeeId,
		ContractId:         util.NewString("MEDI_FA_PT_BW"),
		ChargeSystemId:     util.NewString("MEDI_FA_PT_BW"),
		InsuranceId:        s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
		InsuranceCompanyId: s.patient.Profile.PatientInfo.InsuranceInfos[0].InsuranceCompanyId,
	})

	require.Nil(t, err)
	require.NotNil(t, repoScheins)
	scheinId := util.GetPointerValue((*repoScheins)[0].Id)

	err = s.scheinApp.MarkAsReferral(ctx, schein_common.MarkAsReferralRequest{
		ScheinId: scheinId,
		ReferralDoctor: schein_common.ReferralDoctor{
			Lanr: "*********",
			Bsnr: "*********",
		},
	})
	require.Nil(t, err)

	updatedSchein, err := s.db.Mvz.ScheinRepository.FindById(ctx, scheinId)
	require.Nil(t, err)
	require.NotNil(t, updatedSchein.ReferralDoctor)

	err = s.scheinApp.RemoveReferral(ctx, schein_common.RemoveReferralRequest{
		ScheinId: scheinId,
	})
	require.Nil(t, err)
	updatedSchein, err = s.db.Mvz.ScheinRepository.FindById(ctx, scheinId)
	require.Nil(t, err)
	require.Nil(t, updatedSchein.ReferralDoctor)

}

func (s *ScheinTestSuite) Test_GetTotalScheins() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	currentQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	kvSchein, err := s.scheinApp.CreateSchein(ctx, schein_bff.CreateScheinRequest{
		PatientId:        s.patient.PatientId,
		DoctorId:         &s.doctor.EmployeeId,
		ScheinMainGroup:  "KV",
		KvScheinSubGroup: util.NewPointer("00"),
		KvTreatmentCase:  schein_common.TCKvOutpatient,
		InsuranceId:      s.patient.Profile.PatientInfo.InsuranceInfos[0].Id,
		G4101Year:        currentQuarter.Year,
		G4101Quarter:     currentQuarter.Quarter,
		G4122:            util.NewPointer("00"),
		ScheinDetails: &schein_common.ScheinDetail{
			G4104:  util.NewPointer(int32(61125)),
			G4122:  util.NewPointer("00"),
			G4106:  util.NewPointer("00"),
			Ps4234: util.NewBool(true),
		},
	})
	require.NoError(t, err)
	require.NotNil(t, kvSchein)

	privateContractGroup, err := s.privateContractGroupApp.CreatePrivateContractGroup(ctx, genCreatePCGRequest())
	require.NoError(t, err)

	privateSchein, err := s.scheinApp.CreatePrivateSchein(ctx, schein_bff.CreatePrivateScheinRequest{
		Schein: private_schein_common.PrivateScheinItem{
			PatientId:              (s.patient.PatientId),
			DoctorId:               (s.doctor.EmployeeId),
			PrivateContractGroupId: privateContractGroup.Id,
			InsuranceId:            util.NewPointer(s.patient.Profile.PatientInfo.InsuranceInfos[0].Id),
			IssueDate:              util.NowUnixMillis(ctx),
			Discount:               10,
			DiscountUnit:           private_schein_common.DiscountUnit_Percent,
			InvoiceSendingType:     private_schein_common.InvoiceSendingType_Address,
			ExcludeFromBilling:     false,
			ScheinMainGroup:        schein_common.PRIVATE,
			IsVat:                  false,
			IkNumber:               util.NewPointer(s.patient.Profile.PatientInfo.InsuranceInfos[0].IkNumber),
		},
	})
	require.NoError(t, err)
	require.NotNil(t, privateSchein)

	t.Run("Get total scheins", func(t *testing.T) {
		res, err := s.scheinApp.GetTotalScheins(ctx, schein_bff.GetTotalScheinsRequest{
			PatientId: s.patient.PatientId,
		})
		require.NoError(t, err)
		require.NotNil(t, res)
		require.Equal(t, int32(2), res.TotalScheins)
	})

	t.Run("Get total scheins should return 0", func(t *testing.T) {
		err = s.scheinApp.MarkBill(ctx, schein.MarkBillRequest{
			ValidScheinsIds: []uuid.UUID{
				kvSchein.ScheinItem.ScheinId,
			},
			MainGroup: schein_common.KV,
		})
		require.NoError(t, err)

		scheinRepo, err := schein_repo.ScheinRepoMod.SafeResolve()
		require.NoError(t, err)

		updatedPrivSchein, err := scheinRepo.UpdatePrivateScheinStatus(ctx, privateSchein.ScheinId, schein_common.ScheinStatus_Billed)
		require.NoError(t, err)
		require.NotNil(t, updatedPrivSchein)
		require.Equal(t, schein_common.ScheinStatus_Billed, updatedPrivSchein.ScheinStatus)

		res, err := s.scheinApp.GetTotalScheins(ctx, schein_bff.GetTotalScheinsRequest{
			PatientId: s.patient.PatientId,
		})
		require.NoError(t, err)
		require.NotNil(t, res)
		require.Equal(t, int32(0), res.TotalScheins)
	})
}

func (s *ScheinTestSuite) Test_FilterInvalidInsurance() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	scheinService, err := schein_service.ScheinServiceMod.SafeResolve()
	require.NoError(t, err)

	scheinRepo, err := schein_repo.ScheinRepoMod.SafeResolve()
	require.NoError(t, err)

	scheins, err := scheinRepo.CreateMany(ctx, []schein_repo.ScheinRepo{
		{
			Id: util.NewUUID(),
			ScheinDetail: schein_common.ScheinDetail{
				G4104: util.NewPointer(int32(12345)),
			},
		},
		{
			Id: util.NewUUID(),
			ScheinDetail: schein_common.ScheinDetail{
				G4104: util.NewPointer(int32(61125)),
			},
		},
	})
	require.NoError(t, err)
	require.NotNil(t, scheins)
	require.Len(t, scheins, 2)

	scheins, err = scheinService.FilterInvalidInsurance(ctx, scheins)
	require.NoError(t, err)
	require.NotNil(t, scheins)
	require.Len(t, scheins, 1)
	require.Equal(t, int32(61125), *scheins[0].ScheinDetail.G4104)
}
