package service

import (
	"fmt"
	"os"
	"testing"

	ares_test "git.tutum.dev/medi/tutum/ares/pkg/test"
	"git.tutum.dev/medi/tutum/ares/pkg/test/data"
	"git.tutum.dev/medi/tutum/ares/pkg/test/fixtures"
	bsnr_service "git.tutum.dev/medi/tutum/ares/service/bsnr"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/card_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/ti_connector_common"
	doctor_letter_common "git.tutum.dev/medi/tutum/ares/service/domains/doctor_letter/common"
	eab_common "git.tutum.dev/medi/tutum/ares/service/domains/eab/common"
	eab_service "git.tutum.dev/medi/tutum/ares/service/domains/eab/service"
	eab_service_history_common "git.tutum.dev/medi/tutum/ares/service/domains/eab_service_history/common"
	eab_service_history_srv "git.tutum.dev/medi/tutum/ares/service/domains/eab_service_history/service"
	qes_common "git.tutum.dev/medi/tutum/ares/service/domains/qes/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/bsnr_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/admin/ti_card_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mail/mail_item_inbox_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mail/mail_setting_repo"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/eab"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/timeline_repo"
	settings_common "git.tutum.dev/medi/tutum/ares/service/domains/settings/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/timeline/timeline_service"
	mail_service "git.tutum.dev/medi/tutum/ares/service/mail"
	mail_common "git.tutum.dev/medi/tutum/ares/service/mail/common"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	fixtures2 "gitlab.com/silenteer-oss/hestia/test/fixtures"
	"gitlab.com/silenteer-oss/titan"
)

type EABTestSuite struct {
	ares_test.WithDefaultInfraTestSuite
	mvzFixture  *fixtures2.CareProviderFixture
	doctor      *data.Doctor
	patientId   uuid.UUID
	scheinId    uuid.UUID
	patient     *data.Patient
	bsnr        *bsnr_repo.BSNR
	eabId       uuid.UUID
	mailInboxId uuid.UUID
}

var Acceptable_Extensions = []string{"pdf", "xml", "ldt"}

const XML_HEADER = `<?xml version="1.0" encoding="UTF-8"?>
<?xml-stylesheet type="text/xsl" href="../stylesheets/cda.xsl"?>
<?xml-model href="../schematron/abde-ab2015.sch" type="application/xml" schematypens="http://purl.oclc.org/dsdl/schematron"?>`

const (
	Default_Subject = "Arztbrief"
	EAB             = "Arztbrief"
	EAB_MDN_SUBJECT = "Arztbrief-Eingangsbestaetigung"
)

func TestEABServiceTestSuite(t *testing.T) {
	infraForTesting, _ := ares_test.NewInfraForTestingV2.SafeResolve()
	natsConfig := infraForTesting.NatConfig
	natsServer := infraForTesting.Server
	testServers := ares_test.NewTestServersWithDb(
		false,
		natsConfig,
	)
	bsnr := fixtures.Bsnr_198212400
	patient := *fixtures.Patient_MariaMühlenberg
	patient.Profile.PatientInfo.InsuranceInfos[0].IkNumber = *********
	patientFixture := &fixtures.PatientFixture{
		Patients: []*data.Patient{
			&patient,
		},
	}
	addressBookFixture := &fixtures.AddressBookFixture{
		MailAddressBookEntity: data.CreateFakeMailAddressBook(),
	}
	tiConnectorId := uuid.New()
	testMailAccount := "<EMAIL>-test"
	mailSettingFixture := &fixtures.MailSettingFixture{
		MailSettings: []mail_setting_repo.MailSettingEntity{{
			General: mail_common.General{
				Bsnr: bsnr.Code,
			},
			ConnectorId: tiConnectorId,
			Provider: mail_common.Provider{
				HostName: "************",
				Pop3Port: 995,
				SmtpPort: 465,
			},
			AccountInfor: mail_common.AccountInfor{
				Email:        testMailAccount,
				PasswordHash: "garrioPro2023!",
			},
			CardInformation: mail_common.CardInformation{
				CardNumber: "80276883110000141615", CardType: string(card_common.CardTypeTypeSMCB),
			},
			ClientModule: mail_common.ClientModule{
				HostName:   "*",
				Pop3Port:   995,
				SmtpPort:   465,
				SystemName: "garrioPRO",
				Version:    "1",
			},
		}},
	}
	deviceId := "Workplace1"
	tiConnectorFixture := &fixtures.TiFixture{
		TiConnector: ti_connector_common.TIConnector{
			ID:             &tiConnectorId,
			Host:           "*************",
			Port:           "443",
			TLS:            settings_common.TLS_TLSAuthenByUserNameAndPassword,
			DeviceIds:      []string{deviceId},
			MandantId:      "Mandant1",
			ClientSystemId: "ClientID1",
			OnlineCheck:    ti_connector_common.OnlineCheck_Always,
			TLSUsername:    "garrioPro",
			TLSPassword:    "garrioPro2024",
			AdminDevice:    "Workplace1",
		},
		TiCards: []ti_card_repo.TICard{
			{
				Id:       util.NewUUID(),
				Iccsn:    "80276883110000141615",
				BsnrCode: &bsnr.Code,
			},
		},
	}

	eabId := uuid.New()
	eabFixture := &fixtures.EABFixture{
		EABEntities: []eab.EABEntity{
			{
				BaseEntity: repos.BaseEntity{
					Id: &eabId,
				},
				EABModel: eab_common.EABModel{
					Id:     &eabId,
					Status: qes_common.Status_Created,
					XmlFile: &eab_common.File{
						Name: fmt.Sprintf("%s.xml", EAB),
						Url:  fmt.Sprintf("%s/%s", "bucket-eab", fmt.Sprintf("%s.xml", EAB)),
					},
					SignedFile: &eab_common.File{
						Name: fmt.Sprintf("%s.pdf", EAB),
						Url:  "bucket-eab/signed_data",
					},
				},
			},
		},
	}

	mailId := uuid.New()
	eabFileName := fmt.Sprintf("%s.xml", EAB)
	bucketMailAttachment := "bucket-mailattachment"
	mailFixture := &fixtures.MailFixture{InboxMails: []mail_item_inbox_repo.MailItemEntity{
		{
			BaseEntity: repos.BaseEntity{
				Id: &mailId,
			},
			EmailItem: mail_common.EmailItem{
				MailHeader: &map[string]string{
					string(mail_common.MailHeaderKey_ServiceIdentifer): string(mail_common.MailCategory_EAB),
				},
				From: mail_common.Address{
					Address: testMailAccount,
				},
				To: []mail_common.Address{
					{
						Address: testMailAccount,
					},
				},
				Attachments: []mail_common.Attachment{
					{
						Name: eabFileName,
						Url:  fmt.Sprintf("%s/%s", bucketMailAttachment, eabFileName),
						Headers: map[string]string{
							mail_service.MailHeader_ContentDescription: string(eab_common.EAB_XML),
						},
					},
				},
				Patient: &mail_common.Patient{
					Id:       patient.PatientId,
					FistName: patient.Profile.FirstName,
					LastName: patient.Profile.LastName,
				},
			},
		},
	}}

	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	xmlData, err := os.ReadFile("./test_resources/eab.xml")
	require.Nil(t, err)

	err = eabService.UploadToMinio(titan.NewBackgroundContext(), &eab_service.UploadToMinioRequest{
		BucketName: bucketMailAttachment,
		FileName:   eabFileName,
		Data:       string(xmlData),
	})
	require.Nil(t, err)

	scheinFake := data.CreateFakeSchein(fixtures.Doctor_CatharineRobel.EmployeeId, patient.PatientId)
	scheinFake.Schein.InsuranceId = patient.Profile.PatientInfo.InsuranceInfos[0].Id
	scheinFixture := &fixtures.ScheinFixture{
		Scheins: []schein.ScheinRepo{scheinFake},
	}

	mvzFixture := fixtures.MvzFixture_521111100.WithDoctors(fixtures.Doctor_RemingtonGislason).With(patientFixture, scheinFixture, addressBookFixture, mailSettingFixture, tiConnectorFixture, eabFixture, mailFixture).ToCareProviderFixture()

	s := &EABTestSuite{
		mvzFixture:  mvzFixture,
		patientId:   patient.PatientId,
		doctor:      fixtures.Doctor_CatharineRobel,
		scheinId:    *scheinFake.Id,
		patient:     &patient,
		bsnr:        bsnr,
		eabId:       eabId,
		mailInboxId: mailId,
	}

	s.SetNatsServer(natsServer).
		SetTestServers(testServers).
		SetEnableSocketServer(true, natsConfig).
		SetFixtures(mvzFixture)
	suite.Run(t, s)
}

func (s *EABTestSuite) Test_CreateEAB() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	timelineService, err := timeline_service.TimelineServiceDoctorLetterMod.SafeResolve()
	require.Nil(t, err)

	insurance := s.patient.Profile.PatientInfo.InsuranceInfos[0]
	result, err := timelineService.Create(ctx, timeline_repo.TimelineEntity[doctor_letter_common.DoctorLetter]{
		BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
		PatientId:         s.patient.PatientId,
		TreatmentDoctorId: s.doctor.EmployeeId,
		ScheinIds: []uuid.UUID{
			s.scheinId,
		},
		Payload: doctor_letter_common.DoctorLetter{
			Receiver: &doctor_letter_common.Receiver{
				PatientPayload: &doctor_letter_common.PatientPayload{
					PersonalInfo: s.patient.Profile.PatientInfo.PersonalInfo,
					Address:      s.patient.Profile.PatientInfo.AddressInfo.Address,
				},
				InsurancePayload: &doctor_letter_common.InsurancePayload{
					Id:       &insurance.InsuranceCompanyId,
					IkNumber: []int32(insurance.GetIkNumberString()),
					Name:     insurance.InsuranceCompanyName,
				},
			},
		},
	})
	require.Nil(s.T(), err)
	require.NotNil(s.T(), result)

	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	res, err := eabService.CreateEAB(ctx, eab_service.CreateEABRequest{
		DoctorLetterId: *result.Id,
	})
	require.Nil(t, err)
	require.NotNil(t, res)
}

func (s *EABTestSuite) Test_GetKIMAccounts() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	res, err := eabService.GetKIMAccounts(ctx, []string{s.bsnr.Code})
	require.Nil(t, err)
	require.NotNil(t, res)
	require.Greater(t, len(res), 0)
}

func (s *EABTestSuite) Test_Sign() {
	t := s.T()
	t.Skip()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	userInfo := ctx.UserInfo()
	userInfo.DeviceId = "Workplace1"
	ctx.WithValue(titan.XUserInfo, userInfo)

	timelineService, err := timeline_service.TimelineServiceDoctorLetterMod.SafeResolve()
	require.Nil(t, err)

	insurance := s.patient.Profile.PatientInfo.InsuranceInfos[0]
	result, err := timelineService.Create(ctx, timeline_repo.TimelineEntity[doctor_letter_common.DoctorLetter]{
		BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
		PatientId:         s.patient.PatientId,
		TreatmentDoctorId: s.doctor.EmployeeId,
		ScheinIds: []uuid.UUID{
			s.scheinId,
		},
		Payload: doctor_letter_common.DoctorLetter{
			Receiver: &doctor_letter_common.Receiver{
				PatientPayload: &doctor_letter_common.PatientPayload{
					PersonalInfo: s.patient.Profile.PatientInfo.PersonalInfo,
					Address:      s.patient.Profile.PatientInfo.AddressInfo.Address,
				},
				InsurancePayload: &doctor_letter_common.InsurancePayload{
					Id:       &insurance.InsuranceCompanyId,
					IkNumber: []int32(insurance.GetIkNumberString()),
					Name:     insurance.InsuranceCompanyName,
				},
			},
		},
	})
	require.Nil(s.T(), err)
	require.NotNil(s.T(), result)

	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	res, err := eabService.CreateEAB(ctx, eab_service.CreateEABRequest{
		DoctorLetterId: *result.Id,
	})
	require.Nil(t, err)
	require.NotNil(t, res)

	resSign, err := eabService.Sign(ctx, []uuid.UUID{*res.Id})
	require.Nil(t, resSign)
	require.NotNil(t, resSign)
	require.Len(t, len(resSign), 1)
}

func (s *EABTestSuite) Test_GetById() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	res, err := eabService.GetById(ctx, s.eabId)
	require.Nil(t, err)
	require.NotNil(t, res)
}

func (s *EABTestSuite) Test_ValidateEmailAddresses() {
	t := s.T()
	t.Skip()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	res, err := eabService.ValidateEmailAddresses(ctx, []string{
		"<EMAIL>-test",
		"<EMAIL>-test",
		"<EMAIL>-test",
	})
	require.Nil(t, err)
	require.NotNil(t, res)
	require.Len(t, len(res), 2)
}

func (s *EABTestSuite) Test_UploadPDF() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	timelineService, err := timeline_service.TimelineServiceDoctorLetterMod.SafeResolve()
	require.Nil(t, err)

	insurance := s.patient.Profile.PatientInfo.InsuranceInfos[0]
	result, err := timelineService.Create(ctx, timeline_repo.TimelineEntity[doctor_letter_common.DoctorLetter]{
		BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
		PatientId:         s.patient.PatientId,
		TreatmentDoctorId: s.doctor.EmployeeId,
		ScheinIds: []uuid.UUID{
			s.scheinId,
		},
		Payload: doctor_letter_common.DoctorLetter{
			Id: util.NewUUID(),
			Receiver: &doctor_letter_common.Receiver{
				PatientPayload: &doctor_letter_common.PatientPayload{
					PersonalInfo: s.patient.Profile.PatientInfo.PersonalInfo,
					Address:      s.patient.Profile.PatientInfo.AddressInfo.Address,
				},
				InsurancePayload: &doctor_letter_common.InsurancePayload{
					Id:       &insurance.InsuranceCompanyId,
					IkNumber: []int32(insurance.GetIkNumberString()),
					Name:     insurance.InsuranceCompanyName,
				},
			},
		},
	})
	require.Nil(s.T(), err)
	require.NotNil(s.T(), result)

	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	eabRes, err := eabService.CreateEAB(ctx, eab_service.CreateEABRequest{
		DoctorLetterId: util.GetPointerValue(result.Id),
	})
	require.Nil(t, err)
	require.NotNil(t, eabRes)

	dataBase64, err := os.ReadFile("./test_resources/doctor_letter_base64")
	require.Nil(s.T(), err)

	res, err := eabService.UploadPDF(ctx, &eab_service.UploadPDFRequest{
		DoctorLetterId: util.GetPointerValue(result.Payload.Id),
		Base64:         string(dataBase64),
	})
	require.Nil(t, err)
	require.NotNil(t, res)
}

func (s *EABTestSuite) Test_GetSetting() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)
	setting, err := eabService.GetSetting(ctx)
	require.Nil(t, err)
	require.NotNil(t, setting)
}

func (s *EABTestSuite) Test_SaveSetting() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	req := &eab_service.SaveSettingRequest{
		IsAutoSending:   true,
		IsAutoReceiving: true,
	}
	res, err := eabService.SaveSetting(ctx, req)
	require.Nil(t, err)
	require.NotNil(t, res)

	setting, err := eabService.GetSetting(ctx)
	require.Nil(t, err)

	require.Equal(t, req.IsAutoSending, setting.Settings.IsAutoSending)
	require.Equal(t, req.IsAutoReceiving, setting.Settings.IsAutoReceiving)
	require.Equal(t, false, setting.Settings.IsQuarterReset)
}

func (s *EABTestSuite) Test_GetPatientInfoFromAttachments() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	mailService, err := mail_service.MailKimServiceMod.SafeResolve()
	require.Nil(s.T(), err)

	mailItem, err := mailService.GetInboxById(ctx, s.mailInboxId)
	require.Nil(s.T(), err)

	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	patientInfo, err := eabService.GetPatientInfoFromAttachments(ctx, mailItem.Attachments)
	require.Nil(s.T(), err)
	require.NotEmpty(s.T(), patientInfo)

	require.Equal(s.T(), patientInfo.AddressInfo.Address.PostCode, "80331")
	require.Equal(s.T(), patientInfo.PersonalInfo.FirstName, "Patient")
	require.Equal(s.T(), patientInfo.PersonalInfo.LastName, "Public")
	require.Equal(s.T(), patientInfo.PersonalInfo.Gender, patient_profile_common.M)
	require.Equal(s.T(), patientInfo.PersonalInfo.DOB, int64(1559692800000))
}

func (s *EABTestSuite) Test_CheckAutomaticallyAssignPatient() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	_, err = eabService.CheckAutomaticallyAssignPatient(ctx, s.mailInboxId)
	require.Nil(t, err)

	mailService, err := mail_service.MailKimServiceMod.SafeResolve()
	require.Nil(s.T(), err)

	mailRes, err := mailService.GetInboxById(ctx, s.mailInboxId)
	require.Nil(t, err)
	require.NotNil(t, mailRes)
	assignedPatient := mailRes.Patient
	require.NotNil(t, assignedPatient)
	require.Equal(t, assignedPatient.Id, s.patientId)

	timelineMailService, err := timeline_service.TimelineEmailItemMod.SafeResolve()
	require.Nil(t, err)

	timelineMailItem, err := timelineMailService.GetMailItem(ctx, mailRes.Patient.Id, mailRes.Id.String())
	require.Nil(t, err)
	require.NotNil(t, timelineMailItem)
}

func (s *EABTestSuite) Test_GetProposalPatients() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)
	_, err = eabService.GetProposalPatients(ctx, s.mailInboxId)
	require.Nil(t, err)
}

func (s *EABTestSuite) Test_AssignPatient() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	res, err := eabService.AssignPatient(ctx, eab_service.AssignPatientRequest{
		MailId:    s.mailInboxId,
		PatientId: s.patientId,
	})
	require.Nil(t, err)
	require.NotNil(t, res)

	mailService, err := mail_service.MailKimServiceMod.SafeResolve()
	require.Nil(s.T(), err)

	mailRes, err := mailService.GetInboxById(ctx, s.mailInboxId)
	require.Nil(t, err)
	require.NotNil(t, mailRes)
	assignedPatient := mailRes.Patient
	require.NotNil(t, assignedPatient)
	require.Equal(t, assignedPatient.Id, s.patientId)

	timelineMailService, err := timeline_service.TimelineEmailItemMod.SafeResolve()
	require.Nil(t, err)

	timelineMailItem, err := timelineMailService.GetMailItem(ctx, mailRes.Patient.Id, mailRes.Id.String())
	require.Nil(t, err)
	require.NotNil(t, timelineMailItem)
}

func (s *EABTestSuite) Test_UnAssignPatient() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	res, err := eabService.UnAssignPatientPatient(ctx, eab_service.UnAssignPatientRequest{
		MailId:    s.mailInboxId,
		PatientId: s.patientId,
	})
	require.Nil(t, err)
	require.NotNil(t, res)

	mailService, err := mail_service.MailKimServiceMod.SafeResolve()
	require.Nil(s.T(), err)

	mailRes, err := mailService.GetInboxById(ctx, s.mailInboxId)
	require.Nil(t, err)
	require.NotNil(t, mailRes)
	assignedPatient := mailRes.Patient
	require.Nil(t, assignedPatient)

	timelineMailService, err := timeline_service.TimelineEmailItemMod.SafeResolve()
	require.Nil(t, err)

	timelineMailItem, err := timelineMailService.GetMailItem(ctx, s.mailInboxId, mailRes.Id.String())

	require.Nil(t, err)
	require.Nil(t, timelineMailItem)
}
func (s *EABTestSuite) Test_PreparePatient() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	res, err := eabService.PreparePatientCompare(ctx, eab_service.AssignPatientRequest{
		MailId:    s.mailInboxId,
		PatientId: s.patientId,
	})
	require.Nil(t, err)
	require.NotNil(t, res)
	require.NotNil(t, res.SelectedPatient)
	require.NotNil(t, res.XmlPatient)
	require.Equal(t, res.IsExistSchein, true)
	require.Equal(t, res.SelectedPatientId, s.patientId)
}

func (s *EABTestSuite) Test_GetByTimelineId() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	timelineService, err := timeline_service.TimelineServiceDoctorLetterMod.SafeResolve()
	require.Nil(t, err)

	insurance := s.patient.Profile.PatientInfo.InsuranceInfos[0]
	result, err := timelineService.Create(ctx, timeline_repo.TimelineEntity[doctor_letter_common.DoctorLetter]{
		BillingDoctorId:   util.NewPointer(s.doctor.EmployeeId),
		PatientId:         s.patient.PatientId,
		TreatmentDoctorId: s.doctor.EmployeeId,
		ScheinIds: []uuid.UUID{
			s.scheinId,
		},
		Payload: doctor_letter_common.DoctorLetter{
			Receiver: &doctor_letter_common.Receiver{
				PatientPayload: &doctor_letter_common.PatientPayload{
					PersonalInfo: s.patient.Profile.PatientInfo.PersonalInfo,
					Address:      s.patient.Profile.PatientInfo.AddressInfo.Address,
				},
				InsurancePayload: &doctor_letter_common.InsurancePayload{
					Id:       &insurance.InsuranceCompanyId,
					IkNumber: []int32(insurance.GetIkNumberString()),
					Name:     insurance.InsuranceCompanyName,
				},
			},
		},
	})
	require.Nil(s.T(), err)
	require.NotNil(s.T(), result)

	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	res, err := eabService.CreateEAB(ctx, eab_service.CreateEABRequest{
		DoctorLetterId: *result.Id,
	})
	require.Nil(t, err)
	require.NotNil(t, res)

	eab, err := eabService.GetByDoctorLetterId(ctx, res.DoctorLetterId)
	require.Nil(t, err)
	require.NotNil(t, eab)
}

func (s *EABTestSuite) Test_CheckExistInsuranceByIKNumber() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	t.Run("Should exist", func(t *testing.T) {
		res, err := eabService.CheckExistInsuranceByIKNumber(ctx, "*********")
		require.Nil(t, err)
		require.Equal(t, res.IsExist, true)
	})

	t.Run("Should not exist", func(t *testing.T) {
		res, err := eabService.CheckExistInsuranceByIKNumber(ctx, "*********")
		require.Nil(t, err)
		require.Equal(t, res.IsExist, false)
	})
}

func (s *EABTestSuite) Test_CheckExistScheinByPatientId() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)
	res, err := eabService.CheckExistScheinByPatientId(ctx, s.patientId)
	require.Nil(t, err)
	require.Equal(t, res, true)
}

func (s *EABTestSuite) Test_ConsentDocumentingServiceCode() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	setting1, err := eabService.GetConsentDocumenting(ctx, eab_common.Receiving)
	require.Nil(t, err)
	require.NotNil(t, setting1)

	setting2, err := eabService.GetConsentDocumenting(ctx, eab_common.Sending)
	require.Nil(t, err)
	require.NotNil(t, setting2)

	res, err := eabService.GetSetting(ctx)
	require.Nil(t, err)
	require.NotNil(t, res)
	require.Equal(t, res.Settings.IsConsentTriggerForReceiving, true)
	require.Equal(t, res.Settings.IsConsentTriggerForSending, true)
}

func (s *EABTestSuite) Test_ShouldCreateEABServiceHistory() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)

	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	res, err := eabService.SaveSetting(ctx, &eab_service.SaveSettingRequest{
		IsAutoSending:   true,
		IsAutoReceiving: true,
	})
	require.Nil(t, err)
	require.NotNil(t, res)

	err = eabService.CreateEABServiceHistory(ctx, eab_service.CreateEABServiceHistoryRequest{
		PatientId:   s.patientId,
		ServiceCode: eab_service_history_common.EABServiceCode_Sending,
	})
	require.Nil(t, err)

	eabServiceHistorySrv, err := eab_service_history_srv.EABServiceHistoryServiceMod.SafeResolve()
	require.Nil(t, err)

	resServiceCode, err := eabServiceHistorySrv.GetNonDocumentedServiceCode(ctx, &eab_service_history_srv.GetNonDocumentedServiceCodeRequest{
		PatientId:   s.patientId,
		YearQuarter: util.ToYearQuarter(util.NowUnixMillis(nil)),
	})
	require.Nil(t, err)
	require.Len(t, resServiceCode, 1)
}

func (s *EABTestSuite) Test_ShoulTrueWhenHasEABMailSent() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	err = eabService.UpdateEabStateForBsnr(ctx, eab_service.UpdateEabStateForBsnrRequest{
		BsnrCode: s.bsnr.Code,
	})
	require.Nil(t, err)

	yearQuarter := util.ToYearQuarter(util.NowUnixMillis(ctx))
	quarterRange, err := yearQuarter.GetQuarterDateRange()
	require.Nil(t, err)

	startQuarter := util.GetPointerValue(quarterRange.Start)
	endQuarter := util.GetPointerValue(quarterRange.End)

	require.Nil(t, err)

	mailService, err := mail_service.MailKimServiceMod.SafeResolve()
	require.Nil(t, err)

	eABMailCount, err := mailService.CountMailOutboxInRange(ctx, startQuarter, endQuarter, util.NewString(string(mail_common.MailCategory_EAB)))
	require.Nil(t, err)

	bsnrService, err := bsnr_service.BSNRServiceMod.SafeResolve()
	require.Nil(t, err)

	bsnr, err := bsnrService.FindByCode(ctx, s.bsnr.Code)
	require.Nil(t, err)

	if bsnr != nil {
		if eABMailCount == 0 {
			require.False(t, bsnr.EArztbrief.Value)
		} else {
			require.True(t, bsnr.EArztbrief.Value)
		}
	}
}

// This test to cover the validation from KV Digital. Please do not change
func (s *EABTestSuite) Test_GetMailAttachment() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	eabRes, err := eabService.GetById(ctx, s.eabId)
	require.Nil(t, err)
	require.NotNil(t, eabRes)

	xmlData, err := os.ReadFile("./test_resources/eab.xml")
	require.Nil(t, err)
	err = eabService.UploadToMinio(ctx, &eab_service.UploadToMinioRequest{
		BucketName: "bucket-eab",
		FileName:   fmt.Sprintf("%s.xml", EAB),
		Data:       string(xmlData),
	})
	require.Nil(t, err)

	signedData, err := os.ReadFile("./test_resources/signed_pdf")
	require.Nil(t, err)
	err = eabService.UploadToMinio(ctx, &eab_service.UploadToMinioRequest{
		BucketName: "bucket-eab",
		FileName:   "signed_data",
		Data:       string(signedData),
	})
	require.Nil(t, err)

	res, err := eabService.GetMailAttachments(ctx, eab_service.GetMailAttachmentsRequest{
		EABModel: eabRes.EABModel,
		AdditionalAttachments: []mail_common.Attachment{
			{
				Name: "additional_01.pdf",
			},
			{
				Name: "additional_02.xml",
			},
		},
	})
	require.Nil(t, err)
	require.NotNil(t, res)
	require.Len(t, res, 4)
	require.Nil(s.T(), err)
	// XML attachment validation
	xmlAttachment := slice.FindOne(res, func(item mail_common.Attachment) bool {
		return item.Headers[mail_service.MailHeader_ContentDescription] == string(eab_common.EAB_XML)
	})
	require.NotNil(t, xmlAttachment)
	xmlAttachment.Headers[mail_service.MailHeader_ContentType] = fmt.Sprintf(`%s; name=%q`, mail_common.AttachmentContentType_Xml, xmlAttachment.Name)
	xmlAttachment.Headers[mail_service.MailHeader_ContentDisposition] = fmt.Sprintf(`attachment; filename=%q`, xmlAttachment.Name)
	xmlAttachment.Headers[mail_service.MailHeader_ContentTransferEncoding] = string(mail_common.ContentTransferEncoding_Base64)
	// Signed PDF attachment validation
	signedAttachment := slice.FindOne(res, func(item mail_common.Attachment) bool {
		return item.Headers[mail_service.MailHeader_ContentDescription] == string(eab_common.EAB_PDF_SIGNED)
	})
	require.NotNil(t, signedAttachment)
	signedAttachment.Headers[mail_service.MailHeader_ContentType] = fmt.Sprintf(`%s; name=%q`, mail_common.AttachmentContentType_Pdf, signedAttachment.Name)
	signedAttachment.Headers[mail_service.MailHeader_ContentDisposition] = fmt.Sprintf(`attachment; filename=%q`, signedAttachment.Name)
	signedAttachment.Headers[mail_service.MailHeader_ContentTransferEncoding] = string(mail_common.ContentTransferEncoding_Base64)
	// Additional attachment 01 validation
	additionalAttachment01 := slice.FindOne(res, func(item mail_common.Attachment) bool {
		return item.Headers[mail_service.MailHeader_ContentDescription] == fmt.Sprintf("%s-01", eab_common.EAB_ADDITIONAL_FILE)
	})
	require.NotNil(t, additionalAttachment01)
	additionalAttachment01.Headers[mail_service.MailHeader_ContentType] = fmt.Sprintf(`%s; name=%q`, mail_common.AttachmentContentType_Pdf, additionalAttachment01.Name)
	additionalAttachment01.Headers[mail_service.MailHeader_ContentDisposition] = fmt.Sprintf(`attachment; filename=%q`, additionalAttachment01.Name)
	additionalAttachment01.Headers[mail_service.MailHeader_ContentTransferEncoding] = string(mail_common.ContentTransferEncoding_Base64)
	// Additional attachment 02 mail_common
	additionalAttachment02 := slice.FindOne(res, func(item mail_common.Attachment) bool {
		return item.Headers[mail_service.MailHeader_ContentDescription] == fmt.Sprintf("%s-02", eab_common.EAB_ADDITIONAL_FILE)
	})
	require.NotNil(t, additionalAttachment01)
	additionalAttachment01.Headers[mail_service.MailHeader_ContentType] = fmt.Sprintf(`%s; name=%q`, mail_common.AttachmentContentType_Xml, additionalAttachment02.Name)
	additionalAttachment01.Headers[mail_service.MailHeader_ContentDisposition] = fmt.Sprintf(`attachment; filename=%q`, additionalAttachment01.Name)
	additionalAttachment01.Headers[mail_service.MailHeader_ContentTransferEncoding] = string(mail_common.ContentTransferEncoding_Base64)
}

func (s *EABTestSuite) Test_GetPatientXMLData() {
	t := s.T()
	ctx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctor)
	eabService, err := eab_service.EABServiceMod.SafeResolve()
	require.Nil(t, err)

	res, err := eabService.GetPatientXMLData(ctx, s.mailInboxId)
	require.Nil(t, err)
	require.NotNil(t, res)
	require.NotNil(t, res.InsuranceInfos[0].InsuranceCompanyName)
}
