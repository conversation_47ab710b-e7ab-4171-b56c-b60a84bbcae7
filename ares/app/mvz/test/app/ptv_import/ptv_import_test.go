package ptv_import_test

import (
	"testing"

	"git.tutum.dev/medi/tutum/ares/app/mvz/api/ptv_import"
	"git.tutum.dev/medi/tutum/ares/app/mvz/config"
	"git.tutum.dev/medi/tutum/ares/app/mvz/internal/app"
	ptv_import2 "git.tutum.dev/medi/tutum/ares/app/mvz/test/app/ptv_import"
	ares_test "git.tutum.dev/medi/tutum/ares/pkg/test"
	"git.tutum.dev/medi/tutum/ares/pkg/test/data"
	"git.tutum.dev/medi/tutum/ares/pkg/test/fixtures"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/feature_flag"
	feature_flag_domain_service "git.tutum.dev/medi/tutum/ares/service/domains/feature_flag"
	ptv_import_common "git.tutum.dev/medi/tutum/ares/service/domains/ptv_import/common"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_participation"
	feature_flag_common "git.tutum.dev/medi/tutum/ares/service/feature_flag/common"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	fixtures2 "gitlab.com/silenteer-oss/hestia/test/fixtures"
	"gitlab.com/silenteer-oss/titan"
)

type PTVImportTestSuite struct {
	ares_test.WithDefaultInfraTestSuite
	ptvImportApp        ptv_import.PtvImportApp
	mvzFixture          *fixtures2.CareProviderFixture
	doctorParticipation data.DoctorParticipation
}

func TestPTVImportAppSuite(t *testing.T) {
	natsConfig, natsServer, defaultClient := ares_test.NewInfraForTesting()

	config := config.MvzAppConfigMod.Resolve()
	if config == nil {
		panic("config is nil")
	}

	testServers := ares_test.NewTestServersWithDbHpmBindingMock(
		true,
		natsConfig,
		app.NewServer(
			*config,
		),
	)
	// Alexander Arzt A
	doctorAParticipation := fixtures.DoctorParticipation_BulahHaley_TK_HZV  // 711111100, 999990001
	doctorBParticipation := fixtures.DoctorParticipation_TyreseKoepp_TK_HZV // 711111100, 999966801

	// Britta Schneider, 101575519
	patientParticipationTestCase := fixtures.PatientParticipation_EduardKabel_TK_HZV.WithHzvDoctor(
		doctorAParticipation.Doctor,
		patient_participation.DoctorFunctionType_Custodian,
	)
	patientParticipationTestCase.Status = patient_participation.PatientParticipationStatus_Active

	patientOverviewFixture := &fixtures.PatientOverviewFixture{
		Patients: []*data.Patient{
			fixtures.Patient_EduardKabel,
		},
	}
	mvzFixture := fixtures.MvzFixture_711111100.With(patientOverviewFixture).WithDoctorParticipation(doctorAParticipation, doctorBParticipation).ToCareProviderFixture()

	s := &PTVImportTestSuite{
		ptvImportApp:        ptv_import.NewPtvImportAppClient(defaultClient),
		mvzFixture:          mvzFixture,
		doctorParticipation: *doctorAParticipation,
	}

	s.SetNatsServer(natsServer).SetTestServers(testServers).
		SetEnableSocketServer(true, natsConfig).
		SetFixtures(mvzFixture)
	suite.Run(t, s)
}

// This will run before each test in the suite
func (s *PTVImportTestSuite) SetupTest() {
	_ = s.ResetData()
}

// Test the full happy flow of the PTV import process from start to finish
func (s *PTVImportTestSuite) Test_full_happly_flow_Successfully() {
	t := s.T()
	doctorCtx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctorParticipation.Doctor)

	ffMod, err := feature_flag_domain_service.FeatureFlagDomainServiceMod.SafeResolve()
	assert.Nil(t, err, "Failed to resolve feature flag module")
	err = ffMod.UpdateFeatureFlag(doctorCtx, &feature_flag.UpdateFeatureFlagRequest{
		FFConfig: map[string]bool{
			string(feature_flag_common.FeatureFlagKey_HZV_PTV_TESTMODE): true,
		},
	})
	assert.Nil(t, err, "Failed to update feature flag")

	// STEP 1: Get available test files and load XML data
	testFiles, err := ptv_import2.ListTestDataFiles()
	assert.Nil(t, err, "Failed to list test data files")
	t.Logf("Available test files: %v", testFiles)

	// First read and summarize the file data before testing
	firstFileName := "PTV-haevgid00216-TK-HZV-Q4-24.xml"
	firstFileSummary, err := ptv_import2.ReadAndSummarizeXMLFile(firstFileName)
	assert.Nil(t, err, "Failed to summarize XML data")
	t.Logf("--- File Summary: %s ---", firstFileName)
	t.Logf("Year: %d, Quarter: %d, Provider: %s",
		firstFileSummary.Year, firstFileSummary.Quarter, firstFileSummary.Code)
	t.Logf("Patient counts: Total: %d, Status: %v",
		firstFileSummary.TotalCount, firstFileSummary.StatusCount)
	t.Log("Patient details:")
	for i, patient := range firstFileSummary.Patients {
		t.Logf("  %d. %s, DOB: %s, Status: %s, Insurance ID: %s",
			i+1, patient.Name, patient.DateOfBirth, patient.Status, patient.InsuranceID)
	}
	t.Log("--- End of File Summary ---")

	s.processTestFile(t, doctorCtx, firstFileName, false)

	// Read and summarize the second file
	secondFileName := "PTV-haevgid00216-TK-HZV-Q1-25.xml"
	secondFileSummary, err := ptv_import2.ReadAndSummarizeXMLFile(secondFileName)
	assert.Nil(t, err, "Failed to summarize second XML data")
	t.Logf("--- File Summary: %s ---", secondFileName)
	t.Logf("Year: %d, Quarter: %d, Provider: %s",
		secondFileSummary.Year, secondFileSummary.Quarter, secondFileSummary.Code)
	t.Logf("Patient counts: Total: %d, Active: %v",
		secondFileSummary.TotalCount, secondFileSummary.StatusCount)
	t.Log("Patient details:")
	for i, patient := range secondFileSummary.Patients {
		t.Logf("  %d. %s, DOB: %s, Status: %s, Insurance ID: %s",
			i+1, patient.Name, patient.DateOfBirth, patient.Status, patient.InsuranceID)
	}
	t.Log("--- End of File Summary ---")

	s.processTestFile(t, doctorCtx, secondFileName, false)

	// Read and summarize the third file
	thirdFileName := "PTV-haevgid00202-TK-HZV-Q1-25.xml"
	thirdFileSummary, err := ptv_import2.ReadAndSummarizeXMLFile(thirdFileName)
	assert.Nil(t, err, "Failed to summarize third XML data")
	t.Logf("--- File Summary: %s ---", thirdFileName)
	t.Logf("Year: %d, Quarter: %d, Provider: %s",
		thirdFileSummary.Year, thirdFileSummary.Quarter, thirdFileSummary.Code)
	t.Logf("Patient counts: Total: %d, Active: %v",
		thirdFileSummary.TotalCount, thirdFileSummary.StatusCount)
	t.Log("Patient details:")
	for i, patient := range thirdFileSummary.Patients {
		t.Logf("  %d. %s, DOB: %s, Status: %s, Insurance ID: %s",
			i+1, patient.Name, patient.DateOfBirth, patient.Status, patient.InsuranceID)
	}
	t.Log("--- End of File Summary ---")

	s.processTestFile(t, doctorCtx, thirdFileName, true)

	// patient participation status now is active

	// STEP 8: Verify import history was recorded
	paging := common.Pagination{
		Offset: 0,
		Max:    50, // Enough to get all records for test
		SortBy: "createTime",
		Order:  "DESC", // Get most recent first
	}

	historyResult, err := s.ptvImportApp.GetListPtvImportHistory(doctorCtx, ptv_import_common.GetListPtvImportHistoryRequest{
		Pagination: paging,
	})
	assert.Nil(t, err, "Getting import history should succeed")
	assert.Equal(t, 3, len(historyResult.Data), "Import history should not be empty")

	// Verify the most recent history record (should be from our test)
	mostRecentHistory := historyResult.Data[0]
	assert.Equal(t, s.doctorParticipation.Doctor.EmployeeId, mostRecentHistory.DoctorId,
		"History record should have correct doctor ID")

	t.Logf("PTV import test DONE")
}

// Helper function to process a test file and handle the import flow
func (s *PTVImportTestSuite) processTestFile(t *testing.T, doctorCtx *titan.Context, fileName string, expectConflicts bool) {
	t.Logf("Processing test file: %s", fileName)

	// STEP 1: Load XML data
	xmlData, err := ptv_import2.GetTestDataXML(fileName)
	if err != nil {
		t.Fatalf("Failed to load test XML file %s: %v", fileName, err)
	}
	assert.NotEmpty(t, xmlData, "XML test data should not be empty")

	// STEP 2: Import test data from embedded XML file
	err = s.ptvImportApp.ImportTestData(doctorCtx, ptv_import_common.ImportTestDataRequest{
		DoctorId: s.doctorParticipation.Doctor.EmployeeId,
		XmlData:  xmlData, // Pass the loaded XML data to the import function
	})
	assert.Nil(t, err, "Import test data should succeed")

	// STEP 3: Verify the retrieval code was properly imported
	codeResult, err := s.ptvImportApp.GetCodeByDoctor(doctorCtx, ptv_import_common.GetCodePtvImportByDoctorRequest{
		DoctorId: s.doctorParticipation.Doctor.EmployeeId,
		Year:     2022,
		Quarter:  3,
	})
	assert.Nil(t, err, "Getting code by doctor should succeed")
	assert.NotEmpty(t, codeResult.Code, "Retrieval code should not be empty")
	t.Logf("Retrieved code: %s", codeResult.Code)

	// STEP 4: Get contract information using the code
	contractResult, err := s.ptvImportApp.GetContractByDoctor(doctorCtx, ptv_import_common.GetPtvContractByDoctorRequest{
		DoctorId: s.doctorParticipation.Doctor.EmployeeId,
		Code:     codeResult.Code,
		Year:     2022,
		Quarter:  3,
	})
	assert.Nil(t, err, "Getting contracts by doctor should succeed")
	assert.NotEmpty(t, contractResult.Contracts, "Contracts list should not be empty")

	// STEP 5: For each contract, get participants and process them
	for _, contract := range contractResult.Contracts {
		t.Logf("Processing contract: %s, document: %s", contract.ContractId, contract.DocumentId)

		// Get participants for this contract document
		participantsResult, err := s.ptvImportApp.GetParticipantsByDoctor(doctorCtx, ptv_import_common.GetParticipantsByDoctorRequest{
			DoctorId:   s.doctorParticipation.Doctor.EmployeeId,
			Code:       codeResult.Code,
			DocumentId: contract.DocumentId,
			Year:       2022,
			Quarter:    3,
			Version:    contract.Version,
		})
		assert.Nil(t, err, "Getting participants should succeed")
		assert.NotNil(t, participantsResult, "Participants result should not be nil")

		// Log counts of different participant categories
		t.Logf("Auto-import participants: %d", len(participantsResult.AutoImportParticipants))
		t.Logf("Conflict participants: %d", len(participantsResult.ConflictParticipants))
		t.Logf("Missing participants: %d", len(participantsResult.MissingParticipants))

		// Summary of patient data in this test file
		t.Logf("\n--- Patient Summary for %s ---", fileName)

		// Summarize Auto-import participants
		if len(participantsResult.AutoImportParticipants) > 0 {
			t.Logf("\nAuto-import Participants:")
			for i, p := range participantsResult.AutoImportParticipants {
				t.Logf("%d. Name: %s %s, Birthday: %d, Status: %s",
					i+1,
					p.FirstName.HpmFirstName.Value,
					p.LastName.HpmLastName.Value,
					p.Dob.HpmDOB.Value,
					p.Status.HpmStatus.Value)
			}
		}

		// Summarize Conflict participants
		if len(participantsResult.ConflictParticipants) > 0 {
			t.Logf("\nConflict Participants:")
			for i, p := range participantsResult.ConflictParticipants {
				t.Logf("%d. HPM Data: Name: %s %s, Birthday: %d, Status: %s",
					i+1,
					p.FirstName.HpmFirstName.Value,
					p.LastName.HpmLastName.Value,
					p.Dob.HpmDOB.Value,
					p.Status.HpmStatus.Value)
				t.Logf("   Local Data: Name: %s %s, Birthday: %d, Status: %s",
					p.FirstName.LocalFirstName.Value,
					p.LastName.LocalLastName.Value,
					p.Dob.LocalDOB.Value,
					p.Status.LocalStatus.Value)
			}
		}

		// Summarize Missing participants
		if len(participantsResult.MissingParticipants) > 0 {
			t.Logf("\nMissing Participants:")
			for i, p := range participantsResult.MissingParticipants {
				if p.TypeGroupDecision == ptv_import_common.MissingGroupPTV {
					t.Logf("%d. Name: %s %s, Birthday: %d, Status: %s",
						i+1,
						p.FirstName.HpmFirstName.Value,
						p.LastName.HpmLastName.Value,
						p.Dob.HpmDOB.Value,
						p.Status.HpmStatus.Value)
				}
				if p.TypeGroupDecision == ptv_import_common.MissingGroupIV {
					t.Logf("%d. Name: %s %s, Birthday: %d, Status: %s",
						i+1,
						p.FirstName.LocalFirstName.Value,
						p.LastName.LocalLastName.Value,
						p.Dob.LocalDOB.Value,
						p.Status.LocalStatus.Value)
				}
			}
		}
		t.Logf("--- End of Patient Summary ---\n")

		// Check for expected conflicts based on the file we're testing
		if expectConflicts {
			// TODO: turn it on later
			// assert.True(t, len(participantsResult.ConflictParticipants) > 0,
			// 	"Expected conflict participants with conflict test data")
			t.Log("Successfully detected conflict participants as expected")
		}

		// STEP 6: Import participants based on their categories
		var selectedConflictParticipants []ptv_import_common.ParticipantDecision

		// Process conflict participants if any exist
		if len(participantsResult.ConflictParticipants) > 0 {
			// For the first conflict, select HPM data
			if len(participantsResult.ConflictParticipants) > 0 {
				firstConflict := participantsResult.ConflictParticipants[0]
				firstConflict.SetHpmSelected(true)
				firstConflict.SetLocalSelected(false)
				selectedConflictParticipants = append(selectedConflictParticipants, firstConflict)
				t.Logf("Selected HPM data for conflict participant: %s %s",
					firstConflict.FirstName.HpmFirstName.Value,
					firstConflict.LastName.HpmLastName.Value)
			}

			// For the second conflict (if exists), select local data
			if len(participantsResult.ConflictParticipants) > 1 {
				secondConflict := participantsResult.ConflictParticipants[1]
				secondConflict.SetHpmSelected(false)
				secondConflict.SetLocalSelected(true)
				selectedConflictParticipants = append(selectedConflictParticipants, secondConflict)
				t.Logf("Selected local data for conflict participant: %s %s",
					secondConflict.FirstName.LocalFirstName.Value,
					secondConflict.LastName.LocalLastName.Value)
			}

			// For remaining conflicts, alternate between HPM and local
			for i := 2; i < len(participantsResult.ConflictParticipants); i++ {
				conflict := participantsResult.ConflictParticipants[i]

				if i%2 == 0 {
					conflict.SetHpmSelected(true)
					conflict.SetLocalSelected(false)
					t.Logf("Selected HPM data for additional conflict participant")
				} else {
					conflict.SetHpmSelected(false)
					conflict.SetLocalSelected(true)
					t.Logf("Selected local data for additional conflict participant")
				}

				selectedConflictParticipants = append(selectedConflictParticipants, conflict)
			}
		}

		// Select missing participants if any
		var selectedMissingParticipants []ptv_import_common.ParticipantDecision
		for _, missing := range participantsResult.MissingParticipants {
			if missing.TypeGroupDecision == ptv_import_common.MissingGroupPTV {
				missing.SetHpmSelected(true)
				selectedMissingParticipants = append(selectedMissingParticipants, missing)
				t.Logf("Selected missing participant for import: %s %s",
					missing.FirstName.HpmFirstName.Value,
					missing.LastName.HpmLastName.Value)
			}
		}

		// Import the selected participants
		err = s.ptvImportApp.ImportParticipants(doctorCtx, ptv_import_common.ImportParticipantsRequest{
			Id:                     participantsResult.Id,
			DoctorId:               s.doctorParticipation.Doctor.EmployeeId,
			ContractId:             contract.ContractId,
			DocumentId:             contract.DocumentId,
			AutoImportParticipants: participantsResult.AutoImportParticipants,
			ConflictParticipants:   selectedConflictParticipants,
			MissingParticipants:    selectedMissingParticipants,
			ImportType:             ptv_import_common.ImportType_Full,
		})
		assert.Nil(t, err, "Importing participants should succeed")

		// STEP 7: Verify contract status has been updated to "Done"
		updatedContractResult, err := s.ptvImportApp.GetContractByDoctor(doctorCtx, ptv_import_common.GetPtvContractByDoctorRequest{
			DoctorId: s.doctorParticipation.Doctor.EmployeeId,
			Code:     codeResult.Code,
			Year:     2022,
			Quarter:  3,
		})
		assert.Nil(t, err, "Getting updated contracts should succeed")

		// Find the contract we just processed
		var updatedContract *ptv_import_common.ImportContract
		for _, c := range updatedContractResult.Contracts {
			if c.DocumentId == contract.DocumentId {
				updatedContract = &c
				break
			}
		}

		// Verify it exists and has been marked as done
		assert.NotNil(t, updatedContract, "Updated contract should exist")
		if updatedContract != nil {
			if expectConflicts {
				assert.Equal(t, ptv_import_common.ImportContractStatus_Done, updatedContract.Status,
					"Contract should be marked as 'Pending' after import")
			} else {
				assert.Equal(t, ptv_import_common.ImportContractStatus_Done, updatedContract.Status,
					"Contract should be marked as 'Done' after import")
			}
		}
	}

}
