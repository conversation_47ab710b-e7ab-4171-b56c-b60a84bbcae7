package ptv_import

import (
	"encoding/xml"
	"fmt"
	"time"
	hpm_rest_hzv_client "git.tutum.dev/medi/tutum/ares/pkg/hpm_rest/hzv"
	"git.tutum.dev/medi/tutum/pkg/util"
)

// Generated by Copilot

// PTVImportSummary provides a concise summary of PTV import data
type PTVImportSummary struct {
	Filename    string
	Year        int
	Quarter     int
	Code        string
	TotalCount  int
	StatusCount map[string]int
	Patients    []PatientSummary
}

// PatientSummary provides a concise summary of patient data
type PatientSummary struct {
	Name        string
	DateOfBirth string
	Status      string
	InsuranceID string
	KVID        string
}

// ReadAndSummarizeXMLFile reads a PTV import XML file and returns a summary
func ReadAndSummarizeXMLFile(filename string) (*PTVImportSummary, error) {
	// Read XML file
	xmlData, err := GetTestDataXML(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read XML file: %w", err)
	}

	// Parse XML data using the official HPM structure
	var participationDoc hpm_rest_hzv_client.PatientParticipationListDocument
	if err := xml.Unmarshal([]byte(xmlData), &participationDoc); err != nil {
		return nil, fmt.Errorf("failed to parse XML data: %w", err)
	}

	// Extract metadata from filename if not available in XML
	// effectiveTime is used to determine the year and quarter
	effectDate, err := hpm_rest_hzv_client.ParseDateTime(participationDoc.EffectiveTime.Low.Value, time.UTC)
	if err != nil {
		return nil, fmt.Errorf("failed to parse effectiveTime: %w", err)
	}
	quarter, year := util.GetCurrentQuarter(*effectDate)

	// Create summary and count statistics
	summary := &PTVImportSummary{
		Filename:    filename,
		Year:        year,
		Quarter:     quarter,
		Code:        participationDoc.Code.Code,
		StatusCount: make(map[string]int),
	}
	data, _ := participationDoc.ToPTVPatientParticipation(time.UTC)

	// Process patients and count by status
	for _, patient := range data {
		// Count statistics based on status
		summary.TotalCount++
		summary.StatusCount[string(patient.Status)]++

		// Create patient summary
		dob := util.ConvertMillisecondsToTime(patient.Dob).String()

		patientSummary := PatientSummary{
			Name:        fmt.Sprintf("%s %s", patient.Names.FirstName, patient.Names.LastName),
			DateOfBirth: dob,
			Status:      string(patient.Status),
			InsuranceID: patient.InsuranceNumber,
			KVID:        patient.IkNumber,
		}
		summary.Patients = append(summary.Patients, patientSummary)
	}

	return summary, nil
}

// extractMetadataFromFilename extracts year, quarter, and provider ID from the filename
// Format: PTV-{providerId}-TK-HZV-Q{quarter}-{year}.xml
func extractMetadataFromFilename(filename string) (year, quarter int, providerID string) {
	// Default values
	year = 0
	quarter = 0
	providerID = ""

	// Try to parse filename according to expected format
	var y, q int

	n, err := fmt.Sscanf(filename, "PTV-%s-TK-HZV-Q%d-%d.xml", &providerID, &q, &y)
	if err == nil && n >= 3 {
		year = y
		quarter = q
	}

	return
}
