package ptv_import_test

import (
	"testing"
	"time"

	ptv_import2 "git.tutum.dev/medi/tutum/ares/app/mvz/test/app/ptv_import"
	"git.tutum.dev/medi/tutum/ares/service/domains/api/feature_flag"
	feature_flag_domain_service "git.tutum.dev/medi/tutum/ares/service/domains/feature_flag"
	ptv_import_common "git.tutum.dev/medi/tutum/ares/service/domains/ptv_import/common"
	feature_flag_common "git.tutum.dev/medi/tutum/ares/service/feature_flag/common"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/stretchr/testify/assert"
)

// Test_VerifyParticipantDatesAfterImport verifies that start and end dates
// are correctly updated after PTV import
func (s *PTVImportTestSuite) Test_VerifyParticipantDatesAfterImport() {
	t := s.T()
	doctorCtx := s.mvzFixture.GetAllCareProviders(s.GetFixtureContext())[0].LoginAsDoctor(t, s.doctorParticipation.Doctor)
	ffMod, err := feature_flag_domain_service.FeatureFlagDomainServiceMod.SafeResolve()
	assert.Nil(t, err, "Failed to resolve feature flag module")
	err = ffMod.UpdateFeatureFlag(doctorCtx, &feature_flag.UpdateFeatureFlagRequest{
		FFConfig: map[string]bool{
			string(feature_flag_common.FeatureFlagKey_HZV_PTV_TESTMODE): true,
		},
	})
	assert.Nil(t, err, "Failed to update feature flag")

	// Process test file with known date scenarios
	fileName := "PTV-haevgid00216-TK-HZV-Q4-24_date.xml"
	xmlData, err := ptv_import2.GetTestDataXML(fileName)
	assert.Nil(t, err, "Loading test XML should succeed")

	// Import test data
	err = s.ptvImportApp.ImportTestData(doctorCtx, ptv_import_common.ImportTestDataRequest{
		DoctorId: s.doctorParticipation.Doctor.EmployeeId,
		XmlData:  xmlData,
	})
	assert.Nil(t, err, "Import test data should succeed")

	// Get contract info
	codeResult, err := s.ptvImportApp.GetCodeByDoctor(doctorCtx, ptv_import_common.GetCodePtvImportByDoctorRequest{
		DoctorId: s.doctorParticipation.Doctor.EmployeeId,
		Year:     2025,
		Quarter:  1,
	})
	assert.Nil(t, err, "Getting code should succeed")

	// Get participants to verify their dates
	contractResult, err := s.ptvImportApp.GetContractByDoctor(doctorCtx, ptv_import_common.GetPtvContractByDoctorRequest{
		DoctorId: s.doctorParticipation.Doctor.EmployeeId,
		Code:     codeResult.Code,
		Year:     2025,
		Quarter:  1,
	})
	assert.Nil(t, err, "Getting contract should succeed")

	// Verify participant dates in each contract
	for _, contract := range contractResult.Contracts {
		participantsResult, err := s.ptvImportApp.GetParticipantsByDoctor(doctorCtx, ptv_import_common.GetParticipantsByDoctorRequest{
			DoctorId:   s.doctorParticipation.Doctor.EmployeeId,
			Code:       codeResult.Code,
			DocumentId: contract.DocumentId,
			Year:       2025,
			Quarter:    1,
			Version:    contract.Version,
		})
		assert.Nil(t, err, "Getting participants should succeed")

		// Verify auto-import participants
		verifyParticipantList(t, participantsResult.AutoImportParticipants)

		// Verify conflict participants
		verifyParticipantList(t, participantsResult.ConflictParticipants)

		// Verify missing participants
		// verifyParticipantList(t, participantsResult.MissingParticipants)
		patient_fischer_theo := participantsResult.MissingParticipants[0]
		// patient FISCHER THEO has start date 20230401000000 and end date 20250331235959
		startDate := *patient_fischer_theo.ContractBeginDate.HpmContractBeginDate.Value
		// convert startDate to date
		datetimeValue := util.ConvertMillisecondsToTime(startDate, time.UTC)
		assert.Equal(t, 2023, datetimeValue.Year(), "Year for patient FISCHER THEO should be 2023")
		assert.Equal(t, "April", datetimeValue.Month().String(), "Month for patient FISCHER THEO should be April")
		assert.Equal(t, 1, datetimeValue.Day(), "Day for patient FISCHER THEO should be 1")
		// verify end date
		endDate := *patient_fischer_theo.ContractEndDate.HpmContractEndDate.Value
		datetimeEndValue := util.ConvertMillisecondsToTime(endDate, time.UTC)
		assert.Equal(t, 2025, datetimeEndValue.Year(), "Year for patient FISCHER THEO should be 2025")
		assert.Equal(t, "March", datetimeEndValue.Month().String(), "Month for patient FISCHER THEO should be March")
		assert.Equal(t, 31, datetimeEndValue.Day(), "Day for patient FISCHER THEO should be 31")

		// assert.Equal(t, "20230401000000", *patient_fischer_theo.ContractBeginDate.HpmContractBeginDate.Value,
		// 	"Start date for patient FISCHER THEO should be 20230401000000")
		// assert.Equal(t, "20250331235959", *patient_fischer_theo.ContractEndDate.HpmContractEndDate.Value,
		// 	"End date for patient FISCHER THEO should be 20250331235959")

	}
}

// verifyParticipantList verifies dates for a list of participants
func verifyParticipantList(t *testing.T, participants []ptv_import_common.ParticipantDecision) {
	for _, p := range participants {
		// All participants should have a start date
		assert.NotNil(t, p.ContractBeginDate.HpmContractBeginDate.Value,
			"Contract begin date should not be nil for participant %s %s",
			p.FirstName.HpmFirstName.Value,
			p.LastName.HpmLastName.Value)

		// For terminated participants, verify end date exists
		if p.Status.HpmStatus.Value == ptv_import_common.PatientParticipation_Terminated {
			assert.NotNil(t, p.ContractEndDate.HpmContractEndDate.Value,
				"Contract end date should not be nil for terminated participant %s %s",
				p.FirstName.HpmFirstName.Value,
				p.LastName.HpmLastName.Value)
		} else {
			// For active participants, end date should be nil if present
			if p.ContractEndDate.HpmContractEndDate.Value != nil {
				assert.Nil(t, p.ContractEndDate.HpmContractEndDate.Value,
					"Contract end date should be nil for active participant %s %s",
					p.FirstName.HpmFirstName.Value,
					p.LastName.HpmLastName.Value)
			}
		}

		// Log the verification for debugging
		t.Logf("Verified dates for participant %s %s: Start=%v, End=%v, Status=%v",
			p.FirstName.HpmFirstName.Value,
			p.LastName.HpmLastName.Value,
			p.ContractBeginDate.HpmContractBeginDate.Value,
			p.ContractEndDate.HpmContractEndDate.Value,
			p.Status.HpmStatus.Value)
	}
}
