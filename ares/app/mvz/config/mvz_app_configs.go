package config

import (
	"github.com/spf13/viper"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	"git.tutum.dev/medi/tutum/ares/app/auth/pkg/auth"
	"git.tutum.dev/medi/tutum/ares/pkg/config/cal"
	"git.tutum.dev/medi/tutum/ares/pkg/config/hpm"
	"git.tutum.dev/medi/tutum/ares/pkg/config/masterdata"
	"git.tutum.dev/medi/tutum/ares/pkg/config/minio"
	"git.tutum.dev/medi/tutum/ares/pkg/config/redis"
	"git.tutum.dev/medi/tutum/ares/pkg/config/software"
	"github.com/submodule-org/submodule.go/v2"
)

const (
	KEY_GARRIOPRO_VERSION = "garriopro.version"
)

var SetDefaultConfigSub = submodule.Make[bool](func() bool {
	// minio
	viper.SetDefault(minio.MINIO_ENDPOINT, "local")
	viper.SetDefault(minio.MINIO_ACCESS_KEY_ID, "minioadmin")
	viper.SetDefault(minio.MINIO_SECRET_ACCESS_KEY, "minioadmin")
	viper.SetDefault(minio.MINIO_BUCKET_BILLING, "bucket-billing")
	viper.SetDefault(minio.MINIO_BUCKET_PTVIMPORT, "bucket-ptvimport")
	viper.SetDefault(minio.MINIO_BUCKET_LDTCANCEL, "bucket-ldtcancel")
	viper.SetDefault(minio.MINIO_BUCKET_LDT, "bucket-ldt")
	viper.SetDefault(minio.MINIO_BUCKET_TMP, "bucket-tmp")
	viper.SetDefault(minio.MINIO_BUCKET_KVBilling, "bucket-kvbilling")
	viper.SetDefault(minio.MINIO_BUCKET_MAILATTACHMENT, "bucket-mailattachment")
	viper.SetDefault(minio.MINIO_BUCKET_EREZEPT_BUNDLE, "bucket-erezept-bundle")
	viper.SetDefault(minio.MINIO_BUCKET_KV_CONNECT, "bucket-kvconnect")
	viper.SetDefault(minio.MINIO_BUCKET_EAU, "bucket-eau")
	viper.SetDefault(minio.MINIO_BUCKET_DMP_BILLING, "bucket-dmpbilling")
	viper.SetDefault(minio.MINIO_BUCKET_AKA_DATA, "bucket-akadata")
	viper.SetDefault(minio.MINIO_BUCKET_EAB, "bucket-eab")
	viper.SetDefault(minio.MINIO_BUCKET_DOCTERLETTER, "bucket-doctor-letter")
	viper.SetDefault(minio.MINIO_BUCKET_MAILCOMPANION, "bucket-mail-companion")
	viper.SetDefault(minio.MINIO_BUCKET_DM_COMPANION, "bucket-dm-companion")
	viper.SetDefault(minio.MINIO_BUCKET_ARRIBA, "bucket-arriba")
	viper.SetDefault(minio.MINIO_BUCKET_VOS, "bucket-vos")

	hpm.SetHpmConfig()

	software.SetDefaultSoftwareInformation()

	viper.SetDefault("deployment.profile", "local")

	viper.SetDefault("mmi.baseUrl", "http://mmi.medi-verbund.de:7777")
	viper.SetDefault("mmi.username", "garrio-Stuttgart")
	viper.SetDefault("mmi.licensekey", "3FCR-TCJG-ESJH-ETW7")

	viper.SetDefault(redis.REDIS_MAXIDLE, 9)
	viper.SetDefault(redis.REDIS_ADDRESS, "localhost:6379")
	viper.SetDefault(redis.REDIS_PASSWORD, "")

	viper.SetDefault("mongodb.uri", "mongodb://localhost:27017")
	viper.SetDefault("html2pdf.url", "http://localhost:6579/forms/chromium")
	viper.SetDefault("pdf.url", "http://localhost:8081/pdf/fill")
	viper.SetDefault("ldk.url", "http://localhost:8080")
	viper.SetDefault("xpm.urls", "http://localhost:8082/xpm-kvdt-2024.4.1/")
	viper.SetDefault("xkm.url", "http://localhost:8083")
	viper.SetDefault("xslt.url", "http://localhost:8084")
	viper.SetDefault("xsl.url", "http://localhost:8087")
	viper.SetDefault("testmodule.edmp.url", "http://localhost:8085")
	viper.SetDefault("pdf.converter.url", "http://localhost:8089")
	viper.SetDefault("barcode", "http://localhost:8086/barcode/create")
	viper.SetDefault(cal.CAL_BASE_URL, "https://dev.garrio.dev/cal")
	viper.SetDefault(auth.ZitadelAuthCallback, "http://local/pro/praxis/api/app/mvz/auth/callBack")
	viper.SetDefault(auth.ZitadelAuthPostUrl, "https://local")
	viper.SetDefault(KEY_GARRIOPRO_VERSION, "Q4.24")

	viper.SetDefault(auth.ZitadelProjectId, "275947136518982054")
	viper.SetDefault(auth.ZitadelManagementDomain, "https://garrio-dev.zitadel.web-prod-1.rancher-prod-1.medi-verbund.de")
	viper.SetDefault(auth.ZitadelCALProjectId, "275582108993325478")
	viper.SetDefault(auth.ZitadelPROOrgId, "248218340563159550")
	viper.SetDefault("postgres.url", "host=localhost user=postgres password=123 dbname=himi port=5433 sslmode=disable")
	viper.SetDefault("etl.url", "host=localhost user=postgres password=admin dbname=etl port=5432 sslmode=disable")
	viper.SetDefault(masterdata.MASTERDATA_SERVER_URI, "https://dev.garrio.dev/pro/admin/masterdata/v3")

	return true
})

var GetNatsConfigMod = submodule.Make[*titan.NatsConfig](titan.GetNatsConfig)

var GetDefaultTitanClientMod = submodule.Make[*titan.Client](func(cfg *titan.NatsConfig, c titan.CreateNatClientFunc) *titan.Client {
	return c(cfg)
}, GetNatsConfigMod, titan.CreateClientMod)

var SocketServiceClientMod = submodule.Make[*socket_api.SocketServiceClient](func(titanClient *titan.Client) *socket_api.SocketServiceClient {
	return socket_api.NewSocketServiceClient(titanClient)
}, GetDefaultTitanClientMod)

var MvzAppConfigMod = submodule.Make[*MvzAppConfigs](func(natsConfig *titan.NatsConfig, redisConfig redis.RedisConfig) *MvzAppConfigs {
	config := newMvzAppDefaultConfig(natsConfig, redisConfig)
	return &config
}, GetNatsConfigMod, redis.RedisConfigMod)

type MMIConfig struct {
	BaseUrl    string
	Licensekey string
	Username   string
}
type MvzAppConfigs struct {
	NatsConfig          *titan.NatsConfig
	PdfServicePath      string
	LDKServicePath      string
	MinioClientConfig   minio.MinioClientConfig
	Hpm                 hpm.HpmConfig
	MMIConfig           *MMIConfig
	RedisConfig         redis.RedisConfig
	MongoDBUri          string
	XPMUrls             string
	XKMUrl              string
	XSLTUrl             string
	SoftwareConfig      software.SoftwareConfig
	EdmpTestmoduleUrl   string
	Html2PdfPath        string
	BarcodeUrl          string
	CallbackRedirectUrl string
	PostLogoutUrl       string
	GarrioproVersion    string
	DeploymentProfile   string
}

func GetMinioConfig() minio.MinioClientConfig {
	return minio.MinioClientConfig{
		EndPoint:             viper.GetString(minio.MINIO_ENDPOINT),
		AccessKeyId:          viper.GetString(minio.MINIO_ACCESS_KEY_ID),
		SecretAccessKey:      viper.GetString(minio.MINIO_SECRET_ACCESS_KEY),
		BucketBilling:        viper.GetString(minio.MINIO_BUCKET_BILLING),
		BucketPtvImport:      viper.GetString(minio.MINIO_BUCKET_PTVIMPORT),
		BucketLdtCancel:      viper.GetString(minio.MINIO_BUCKET_LDTCANCEL),
		BucketLdt:            viper.GetString(minio.MINIO_BUCKET_LDT),
		BucketTmp:            viper.GetString(minio.MINIO_BUCKET_TMP),
		BucketKvBilling:      viper.GetString(minio.MINIO_BUCKET_KVBilling),
		BucketMailAttachment: viper.GetString(minio.MINIO_BUCKET_MAILATTACHMENT),
		BucketErezeptBundle:  viper.GetString(minio.MINIO_BUCKET_EREZEPT_BUNDLE),
		BucketDmpBilling:     viper.GetString(minio.MINIO_BUCKET_DMP_BILLING),
		BucketEau:            viper.GetString(minio.MINIO_BUCKET_EAU),
		BucketAkaData:        viper.GetString(minio.MINIO_BUCKET_AKA_DATA),
		BucketEAB:            viper.GetString(minio.MINIO_BUCKET_EAB),
		BucketDoctorLetter:   viper.GetString(minio.MINIO_BUCKET_DOCTERLETTER),
		BucketMailCompanion:  viper.GetString(minio.MINIO_BUCKET_MAILCOMPANION),
		BucketDmCompanion:    viper.GetString(minio.MINIO_BUCKET_DM_COMPANION),
		BucketXBdt:           viper.GetString(minio.MINIO_BUCKET_XBDT),
		BucketArriba:         viper.GetString(minio.MINIO_BUCKET_ARRIBA),
		BucketVos:            viper.GetString(minio.MINIO_BUCKET_VOS),
	}
}

func newMvzAppDefaultConfig(natsConfig *titan.NatsConfig, redisConfig redis.RedisConfig) MvzAppConfigs {
	return MvzAppConfigs{
		NatsConfig:        natsConfig,
		PdfServicePath:    viper.GetString("pdf.url"),
		LDKServicePath:    viper.GetString("ldk.url"),
		MinioClientConfig: GetMinioConfig(),
		Html2PdfPath:      viper.GetString("html2pdf.url"),
		BarcodeUrl:        viper.GetString("barcode"),
		Hpm: hpm.HpmConfig{
			Url:         viper.GetString(hpm.HPM_URL),
			Mock:        viper.GetBool(hpm.HPM_MOCK),
			MockBinding: viper.GetBool(hpm.HPM_MOCKBINDING),
			Secret:      viper.GetString(hpm.HPM_SECRET),
			SystemOid:   viper.GetString(hpm.HPM_SYSTEMOID),
		},
		MMIConfig: &MMIConfig{
			BaseUrl:    viper.GetString("mmi.baseurl"),
			Licensekey: viper.GetString("mmi.licensekey"),
			Username:   viper.GetString("mmi.username"),
		},
		RedisConfig:         redisConfig,
		MongoDBUri:          viper.GetString("mongodb.url"),
		XPMUrls:             viper.GetString("xpm.urls"),
		XKMUrl:              viper.GetString("xkm.url"),
		XSLTUrl:             viper.GetString("xslt.url"),
		SoftwareConfig:      software.GetSoftwareConfig(),
		EdmpTestmoduleUrl:   viper.GetString("testmodule.edmp.url"),
		CallbackRedirectUrl: viper.GetString(auth.ZitadelAuthCallback),
		PostLogoutUrl:       viper.GetString(auth.ZitadelAuthPostUrl),
		GarrioproVersion:    viper.GetString(KEY_GARRIOPRO_VERSION),
		DeploymentProfile:   viper.GetString("deployment.profile"),
	}
}
