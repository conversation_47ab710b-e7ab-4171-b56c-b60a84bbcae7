// This code was autogenerated from app/mvz/form.proto, do not edit.

package form

import (
	"encoding/json"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	schein_common "git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"

	common1 "git.tutum.dev/medi/tutum/ares/service/domains/eau/common"

	common "git.tutum.dev/medi/tutum/ares/service/domains/form/common"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type GetFormRequest struct {
	Id         *string `json:"id"`
	OKV        *string `json:"oKV"`
	IkNumber   *int32  `json:"ikNumber"`
	ContractId *string `json:"contractId"`
}

type GetFormResponse struct {
	Form common.Form `json:"form"`
}

type PrescribeRequest struct {
	Prescribe   common.Prescribe    `json:"prescribe"`
	PrintOption *common.PrintOption `json:"printOption"`
	EAUSetting  *common1.EAUSetting `json:"eAUSetting"`
}

type PrescribeResponse struct {
	Prescribe     common.Prescribe `json:"prescribe"`
	PdfBase64     string           `json:"pdfBase64"`
	PdfBase64List []string         `json:"pdfBase64List"`
}

type BuildBundleAndValidationRequest struct {
	Prescribe   common.Prescribe    `json:"prescribe"`
	PrintOption *common.PrintOption `json:"printOption"`
}

type BuildBundleAndValidationResponse struct {
	EAUValidation *common1.EAUValidation `json:"eAUValidation"`
}

type GetPrescribeRequest struct {
	PrescribeId uuid.UUID `json:"prescribeId"`
}

type GetPrescribeResponse struct {
	Prescribe common.Prescribe `json:"prescribe"`
}

type GetFormsResponse struct {
	Forms []common.Form `json:"forms"`
}

type PrintRequest struct {
	PrescribeId uuid.UUID           `json:"prescribeId"`
	PrintOption common.PrintOption  `json:"printOption"`
	EAUSetting  *common1.EAUSetting `json:"eAUSetting"`
}

type PrintData struct {
	FormUrls []string `json:"formUrls"`
}

type PrintResponse struct {
	PrintData []PrintData `json:"printData"`
}

type GetFormsRequest struct {
	OKV                  string                   `json:"oKV"`
	IkNumber             int32                    `json:"ikNumber"`
	ContractId           string                   `json:"contractId"`
	ChargeSystemId       string                   `json:"chargeSystemId"`
	ModuleChargeSystemId *string                  `json:"moduleChargeSystemId"`
	ScheinMainGroup      *schein_common.MainGroup `json:"scheinMainGroup"`
}

type PrintPlainPdfRequest struct {
	FormSetting        string     `json:"formSetting" validate:"trimjson"`
	FormName           string     `json:"formName"`
	TreatmentDoctorId  *uuid.UUID `json:"treatmentDoctorId"`
	IsRemoveBackground *bool      `json:"isRemoveBackground"`
	ContractId         *string    `json:"contractId"`
}

type PrintPlainPdfResponse struct {
	FormUrl string `json:"formUrl"`
}

type GetFileUrlRequest struct {
	FileName string `json:"fileName"`
}

type GetFileUrlResponse struct {
	FileUrl string `json:"fileUrl"`
}

type PrescribeResponseV2 struct {
	PrintInfos []common.PrintResult `json:"printInfos"`
	TimelineId uuid.UUID            `json:"timelineId"`
}

type GetIndicatorActiveIngredientsRequest struct {
	ContractId string `json:"contractId" validate:"required"`
}

type AtcDiagnoseCode struct {
	AtcCode      *string `json:"atcCode"`
	DiagnoseCode *string `json:"diagnoseCode"`
}

type GetIndicatorActiveIngredientsResponse struct {
	Data []AtcDiagnoseCode `json:"data"`
}

type GetIcdFormRequest struct {
	ContractId string `json:"contractId"`
}

type GetIcdFormResponse struct {
	IcdCodes []string `json:"icdCodes"`
}

// enum definitions
type KBV_PRF_NR string

const (
	KBV_PRF_LABEL KBV_PRF_NR = "label_prf_nr"
	KBV_PRF_VALUE KBV_PRF_NR = "Y/9/2407/36/001"
)

type EHIC_PRF_NR string

const (
	EHIC_PRF_LABEL EHIC_PRF_NR = "label_ehic_prf_nr"
	EHIC_PRF_VALUE EHIC_PRF_NR = "Y/1/2404/36/701"
)

// Define constants
const NATS_SUBJECT = "api.app.mvz" // nats subject this service will listen to

// service event constants
const EVENT_GetForm = "api.app.mvz.FormAPP.GetForm"
const LEGACY_TOPIC_GetForm = "/api/app/mvz/form/app/getForm"
const EVENT_GetForms = "api.app.mvz.FormAPP.GetForms"
const LEGACY_TOPIC_GetForms = "/api/app/mvz/form/app/getForms"
const EVENT_GetIcdForm = "api.app.mvz.FormAPP.GetIcdForm"
const LEGACY_TOPIC_GetIcdForm = "/api/app/mvz/form/app/getIcdForm"
const EVENT_GetPrescribe = "api.app.mvz.FormAPP.GetPrescribe"
const LEGACY_TOPIC_GetPrescribe = "/api/app/mvz/form/app/getPrescribe"
const EVENT_Print = "api.app.mvz.FormAPP.Print"
const LEGACY_TOPIC_Print = "/api/app/mvz/form/app/print"
const EVENT_PrintPlainPdf = "api.app.mvz.FormAPP.PrintPlainPdf"
const LEGACY_TOPIC_PrintPlainPdf = "/api/app/mvz/form/app/printPlainPdf"
const EVENT_GetAllForms = "api.app.mvz.FormAPP.GetAllForms"
const LEGACY_TOPIC_GetAllForms = "/api/app/mvz/form/app/getAllForms"
const EVENT_GetFileUrl = "api.app.mvz.FormAPP.GetFileUrl"
const LEGACY_TOPIC_GetFileUrl = "/api/app/mvz/form/app/getFileUrl"
const EVENT_BuildBundleAndValidation = "api.app.mvz.FormAPP.BuildBundleAndValidation"
const LEGACY_TOPIC_BuildBundleAndValidation = "/api/app/mvz/form/app/buildBundleAndValidation"
const EVENT_PrescribeV2 = "api.app.mvz.FormAPP.PrescribeV2"
const LEGACY_TOPIC_PrescribeV2 = "/api/app/mvz/form/app/prescribeV2"
const EVENT_GetIndicatorActiveIngredients = "api.app.mvz.FormAPP.GetIndicatorActiveIngredients"
const LEGACY_TOPIC_GetIndicatorActiveIngredients = "/api/app/mvz/form/app/getIndicatorActiveIngredients"

// message event constants

// Define service interface -------------------------------------------------------------
type FormAPP interface {
	GetForm(ctx *titan.Context, request GetFormRequest) (*GetFormResponse, error)
	GetForms(ctx *titan.Context, request GetFormsRequest) (*GetFormsResponse, error)
	GetIcdForm(ctx *titan.Context, request GetIcdFormRequest) (*GetIcdFormResponse, error)
	GetPrescribe(ctx *titan.Context, request GetPrescribeRequest) (*GetPrescribeResponse, error)
	Print(ctx *titan.Context, request PrintRequest) (*PrintResponse, error)
	PrintPlainPdf(ctx *titan.Context, request PrintPlainPdfRequest) (*PrintPlainPdfResponse, error)
	GetAllForms(ctx *titan.Context) (*GetFormsResponse, error)
	GetFileUrl(ctx *titan.Context, request GetFileUrlRequest) (*GetFileUrlResponse, error)
	BuildBundleAndValidation(ctx *titan.Context, request BuildBundleAndValidationRequest) (*BuildBundleAndValidationResponse, error)
	PrescribeV2(ctx *titan.Context, request PrescribeRequest) (*PrescribeResponseV2, error)
	GetIndicatorActiveIngredients(ctx *titan.Context, request GetIndicatorActiveIngredientsRequest) (*GetIndicatorActiveIngredientsResponse, error)
}

// Define service proxy -------------------------------------------------------------------
type FormAPPProxy struct {
	service FormAPP
}

func (srv *FormAPPProxy) GetForm(ctx *titan.Context, request GetFormRequest) (*GetFormResponse, error) {
	return srv.service.GetForm(ctx, request)
}
func (srv *FormAPPProxy) GetForms(ctx *titan.Context, request GetFormsRequest) (*GetFormsResponse, error) {
	return srv.service.GetForms(ctx, request)
}
func (srv *FormAPPProxy) GetIcdForm(ctx *titan.Context, request GetIcdFormRequest) (*GetIcdFormResponse, error) {
	return srv.service.GetIcdForm(ctx, request)
}
func (srv *FormAPPProxy) GetPrescribe(ctx *titan.Context, request GetPrescribeRequest) (*GetPrescribeResponse, error) {
	return srv.service.GetPrescribe(ctx, request)
}
func (srv *FormAPPProxy) Print(ctx *titan.Context, request PrintRequest) (*PrintResponse, error) {
	return srv.service.Print(ctx, request)
}
func (srv *FormAPPProxy) PrintPlainPdf(ctx *titan.Context, request PrintPlainPdfRequest) (*PrintPlainPdfResponse, error) {
	return srv.service.PrintPlainPdf(ctx, request)
}
func (srv *FormAPPProxy) GetAllForms(ctx *titan.Context) (*GetFormsResponse, error) {
	return srv.service.GetAllForms(ctx)
}
func (srv *FormAPPProxy) GetFileUrl(ctx *titan.Context, request GetFileUrlRequest) (*GetFileUrlResponse, error) {
	return srv.service.GetFileUrl(ctx, request)
}
func (srv *FormAPPProxy) BuildBundleAndValidation(ctx *titan.Context, request BuildBundleAndValidationRequest) (*BuildBundleAndValidationResponse, error) {
	return srv.service.BuildBundleAndValidation(ctx, request)
}
func (srv *FormAPPProxy) PrescribeV2(ctx *titan.Context, request PrescribeRequest) (*PrescribeResponseV2, error) {
	return srv.service.PrescribeV2(ctx, request)
}
func (srv *FormAPPProxy) GetIndicatorActiveIngredients(ctx *titan.Context, request GetIndicatorActiveIngredientsRequest) (*GetIndicatorActiveIngredientsResponse, error) {
	return srv.service.GetIndicatorActiveIngredients(ctx, request)
}

// Define service router -----------------------------------------------------------------
type FormAPPRouter struct {
	proxy *FormAPPProxy
}

func (router *FormAPPRouter) Register(r titan.Router) {
	r.RegisterTopic(EVENT_GetForm, router.proxy.GetForm, titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_GetForm, router.proxy.GetForm, titan.IsAuthenticated())
	r.RegisterTopic(EVENT_GetForms, router.proxy.GetForms, titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_GetForms, router.proxy.GetForms, titan.IsAuthenticated())
	r.RegisterTopic(EVENT_GetIcdForm, router.proxy.GetIcdForm, titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_GetIcdForm, router.proxy.GetIcdForm, titan.IsAuthenticated())
	r.RegisterTopic(EVENT_GetPrescribe, router.proxy.GetPrescribe, titan.Secured(infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetPrescribe, router.proxy.GetPrescribe, titan.Secured(infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_Print, router.proxy.Print, titan.Secured(infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_Print, router.proxy.Print, titan.Secured(infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_PrintPlainPdf, router.proxy.PrintPlainPdf, titan.Secured(infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_PrintPlainPdf, router.proxy.PrintPlainPdf, titan.Secured(infra.CARE_PROVIDER_ADMIN, infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetAllForms, router.proxy.GetAllForms, titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_GetAllForms, router.proxy.GetAllForms, titan.IsAuthenticated())
	r.RegisterTopic(EVENT_GetFileUrl, router.proxy.GetFileUrl, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetFileUrl, router.proxy.GetFileUrl, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_BuildBundleAndValidation, router.proxy.BuildBundleAndValidation, titan.IsUser())
	r.RegisterTopic(LEGACY_TOPIC_BuildBundleAndValidation, router.proxy.BuildBundleAndValidation, titan.IsUser())
	r.RegisterTopic(EVENT_PrescribeV2, router.proxy.PrescribeV2, titan.IsUser(), titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_PrescribeV2, router.proxy.PrescribeV2, titan.IsUser(), titan.IsAuthenticated())
	r.RegisterTopic(EVENT_GetIndicatorActiveIngredients, router.proxy.GetIndicatorActiveIngredients, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetIndicatorActiveIngredients, router.proxy.GetIndicatorActiveIngredients, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
}

// Subscriber
func (router *FormAPPRouter) Subscribe(s *titan.MessageSubscriber) {
}

func NewFormAPPRouter(s FormAPP) *FormAPPRouter {
	p := &FormAPPProxy{s}
	return &FormAPPRouter{p}
}

func NewServer(bff0 FormAPP, options ...titan.Option) *titan.Server {
	opt := options

	router0 := NewFormAPPRouter(bff0)
	opt = append(opt, titan.Routes(router0.Register), titan.Subscribe(router0.Subscribe))

	return titan.NewServer(NATS_SUBJECT, opt...)
}

// Define client ----------------------------------------------------------------------------------------------
type FormAPPClient struct {
	client *titan.Client
}

func (srv *FormAPPClient) GetForm(ctx *titan.Context, request GetFormRequest) (*GetFormResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetForm).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetFormResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *FormAPPClient) GetForms(ctx *titan.Context, request GetFormsRequest) (*GetFormsResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetForms).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetFormsResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *FormAPPClient) GetIcdForm(ctx *titan.Context, request GetIcdFormRequest) (*GetIcdFormResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetIcdForm).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetIcdFormResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *FormAPPClient) GetPrescribe(ctx *titan.Context, request GetPrescribeRequest) (*GetPrescribeResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetPrescribe).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetPrescribeResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *FormAPPClient) Print(ctx *titan.Context, request PrintRequest) (*PrintResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_Print).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &PrintResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *FormAPPClient) PrintPlainPdf(ctx *titan.Context, request PrintPlainPdfRequest) (*PrintPlainPdfResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_PrintPlainPdf).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &PrintPlainPdfResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *FormAPPClient) GetAllForms(ctx *titan.Context) (*GetFormsResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetAllForms).
		Subject(NATS_SUBJECT).
		Build()
	var resp = &GetFormsResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *FormAPPClient) GetFileUrl(ctx *titan.Context, request GetFileUrlRequest) (*GetFileUrlResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetFileUrl).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetFileUrlResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *FormAPPClient) BuildBundleAndValidation(ctx *titan.Context, request BuildBundleAndValidationRequest) (*BuildBundleAndValidationResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_BuildBundleAndValidation).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &BuildBundleAndValidationResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *FormAPPClient) PrescribeV2(ctx *titan.Context, request PrescribeRequest) (*PrescribeResponseV2, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_PrescribeV2).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &PrescribeResponseV2{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *FormAPPClient) GetIndicatorActiveIngredients(ctx *titan.Context, request GetIndicatorActiveIngredientsRequest) (*GetIndicatorActiveIngredientsResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetIndicatorActiveIngredients).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetIndicatorActiveIngredientsResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func NewFormAPPClient(clients ...*titan.Client) *FormAPPClient {
	var client *titan.Client
	if len(clients) <= 0 {
		client = titan.GetDefaultClient()
	} else {
		client = clients[0]
	}
	return &FormAPPClient{client: client}
}

// Define event Notifier -----------------------------------------------------------------------------------------
type FormNotifier struct {
	client *titan.Client
}

func NewFormNotifier() *FormNotifier {
	client := titan.GetDefaultClient()
	return &FormNotifier{client}
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type FormSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewFormSocketNotifier(socket *socket_api.SocketServiceClient) *FormSocketNotifier {
	return &FormSocketNotifier{socket}
}

// -----------------------------------------------
// Test event listener
type FormEventListener struct {
	mux sync.Mutex
}

func (listener *FormEventListener) Reset() {
	listener.mux.Lock()
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *FormEventListener) Subscribe(s *titan.MessageSubscriber) {
}
