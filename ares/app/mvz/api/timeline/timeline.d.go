// This code was autogenerated from app/mvz/timeline.proto, do not edit.

package timeline

import (
	"encoding/json"
	"errors"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	common "git.tutum.dev/medi/tutum/ares/service/domains/api/common"

	schein_common "git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"

	validation_timeline "git.tutum.dev/medi/tutum/ares/service/domains/api/validation_timeline"

	common1 "git.tutum.dev/medi/tutum/ares/service/domains/eab_service_history/common"

	common2 "git.tutum.dev/medi/tutum/ares/service/domains/form/common"

	patient_encounter "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/patient_encounter"

	common3 "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type GetRequest struct {
	PatientId         uuid.UUID                    `json:"patientId"`
	ContractId        *string                      `json:"contractId"`
	CreatedDate       *int64                       `json:"createdDate"`
	EncounterCase     *string                      `json:"encounterCase"`
	TreatmentDoctorId *uuid.UUID                   `json:"treatmentDoctorId"`
	Types             []common3.TimelineEntityType `json:"types"`
}

type GetResponse struct {
	TimelineModels []common3.TimelineModel `json:"timelineModels"`
}

type CreateRequest struct {
	TimelineModel common3.TimelineModel `json:"timelineModel"`
}

type CreateResponse struct {
	TimelineModel common3.TimelineModel `json:"timelineModel"`
}

type RemoveRequest struct {
	HasHardDelete *bool      `json:"hasHardDelete"`
	TimelineId    *uuid.UUID `json:"timelineId"`
	IsChain       *bool      `json:"isChain"`
}

type RemoveResponse struct {
}

type EditRequest struct {
	TimelineModel common3.TimelineModel `json:"timelineModel"`
}

type EditResponse struct {
	TimelineModel common3.TimelineModel `json:"timelineModel"`
}

type UpdatePrintDateRequest struct {
	Type       common3.TimelineEntityType `json:"type"`
	TimelineId *uuid.UUID                 `json:"timelineId"`
	FormId     *uuid.UUID                 `json:"formId"`
	PrintDate  *int64                     `json:"printDate"`
}

type UpdateSuggestionRuleAppliedRequest struct {
	TimelineId *uuid.UUID `json:"timelineId"`
	RuleId     string     `json:"ruleId"`
}

type UpdatePrintDateResponse struct {
}

type EventTimelineHardRemove struct {
	PatientId     uuid.UUID             `json:"patientId"`
	TimelineModel common3.TimelineModel `json:"timelineModel"`
	TimelineId    uuid.UUID             `json:"timelineId"`
}

type EventTimelineRemove struct {
	PatientId     uuid.UUID             `json:"patientId"`
	TimelineModel common3.TimelineModel `json:"timelineModel"`
}

type EventTimelineRestore struct {
	TimelineModel common3.TimelineModel `json:"timelineModel"`
}

type EventTimelineCreate struct {
	PatientId     uuid.UUID             `json:"patientId"`
	TimelineModel common3.TimelineModel `json:"timelineModel"`
}

type EventTimelineUpdate struct {
	PatientId                           uuid.UUID              `json:"patientId"`
	TimelineModel                       common3.TimelineModel  `json:"timelineModel"`
	OldTimelineModel                    *common3.TimelineModel `json:"oldTimelineModel"`
	SkipUpdateEndDatePermanentDiagnoses bool                   `json:"skipUpdateEndDatePermanentDiagnoses"`
	AuditLogId                          *uuid.UUID             `json:"auditLogId"`
	PrescribeFormName                   *common2.FormName      `json:"prescribeFormName"`
}

type EventAutoAction struct {
	PatientId        uuid.UUID `json:"patientId"`
	NotificationCode string    `json:"notificationCode"`
}

type ITimelineEntityType struct {
	TimelineEntityType common3.TimelineEntityType `json:"timelineEntityType"`
	Command            *string                    `json:"command"`
}

type GroupByQuarterRequest struct {
	PatientId           uuid.UUID                 `json:"patientId"`
	FromDate            *int64                    `json:"fromDate"`
	ToDate              *int64                    `json:"toDate"`
	TimelineEntityTypes []ITimelineEntityType     `json:"timelineEntityTypes"`
	IsSortByCategory    bool                      `json:"isSortByCategory"`
	IsHistoryMode       bool                      `json:"isHistoryMode"`
	ScheinId            *uuid.UUID                `json:"scheinId"`
	Keyword             *string                   `json:"keyword"`
	Pagination          *common.PaginationRequest `json:"pagination"`
	Year                *int32                    `json:"year"`
	Quarter             *int32                    `json:"quarter"`
}

type GroupByQuarter struct {
	Year           int32                   `json:"year"`
	Quarter        int32                   `json:"quarter"`
	TimelineModels []common3.TimelineModel `json:"timelineModels"`
}

type GroupByQuarterResponse struct {
	GroupByQuarters []GroupByQuarter `json:"groupByQuarters"`
	TotalPage       int32            `json:"totalPage"`
	MatchedTokens   []string         `json:"matchedTokens"`
}

type EventTimelineValidation struct {
	PatientId      uuid.UUID               `json:"patientId"`
	TimelineModels []common3.TimelineModel `json:"timelineModels"`
}

type GetByIdRequest struct {
	TimelineId uuid.UUID `json:"timelineId"`
}

type GetByIdsRequest struct {
	TimelineIds []uuid.UUID `json:"timelineIds"`
}

type GetByIdResponse struct {
	TimelineModel *common3.TimelineModel `json:"timelineModel"`
}

type GetDiagnoseRequest struct {
	PatientId uuid.UUID `json:"patientId"`
	Codes     []string  `json:"codes"`
}

type GetDiagnoseResponse struct {
	EncounterDiagnoseTimeline []patient_encounter.EncounterDiagnoseTimeline `json:"encounterDiagnoseTimeline"`
}

type IgnoreSdkrwRuleRequest struct {
	PatientId     uuid.UUID `json:"patientId"`
	RuleId        string    `json:"ruleId"`
	EncounterDate int64     `json:"encounterDate"`
}

type UpdateErezeptStatusRequest struct {
	MedicineId uuid.UUID `json:"medicineId"`
	Status     string    `json:"status"`
}

type DeleteErezeptRequest struct {
	MedicineId uuid.UUID `json:"medicineId"`
	PatientId  uuid.UUID `json:"patientId" validate:"required"`
}

type GetTherapiesRequest struct {
	PatientId uuid.UUID `json:"patientId"`
	ScheinId  uuid.UUID `json:"scheinId"`
}

type TherapiesResponse struct {
	Pyschotherapy schein_common.Psychotherapy `json:"pyschotherapy"`
	TimelineModel common3.TimelineModel       `json:"timelineModel"`
}

type GetTherapiesResponse struct {
	Pyschotherapies []TherapiesResponse `json:"pyschotherapies"`
}

type GetAmountServiceCodeRequest struct {
	PatientId    uuid.UUID `json:"patientId"`
	ServiceCodes []string  `json:"serviceCodes"`
	Year         int32     `json:"year"`
	Quarter      int32     `json:"quarter"`
}

type GetAmountServiceCodeResponse struct {
	AmountServiceCode map[string]int64 `json:"amountServiceCode"`
}

type MarkNotApprovedPyschotherapyRequest struct {
	TimelineId []uuid.UUID `json:"timelineId"`
	PatientId  uuid.UUID   `json:"patientId"`
}

type RestoreEntryHistoryRequest struct {
	AuditLogId uuid.UUID `json:"auditLogId"`
}

type RestoreEntryHistoryResponse struct {
}

type ValidateDiagnoseRequest struct {
	IcdCode   []string                    `json:"icdCode" validate:"required"`
	TypeCheck []common3.IcdErrorTypeCheck `json:"typeCheck" validate:"required"`
}

type ValidateDiagnoseResponse struct {
	Results []common3.ValidationDiagnoseResult `json:"results"`
}

type ReRunValidateServiceRequest struct {
	PatientId  uuid.UUID `json:"patientId"`
	ContractId *string   `json:"contractId"`
}

type GetTimelineEntryByIdsRequest struct {
	EntryIds []uuid.UUID `json:"entryIds"`
}

type GetTimelineEntryIdsResponse struct {
	TimelineModels []common3.TimelineModel `json:"timelineModels"`
}

type GetPreParticipationServiceCodesRequest struct {
	PatientId uuid.UUID `json:"patientId"`
}

type Document88130Request struct {
	TimelineModel  common3.TimelineModel `json:"timelineModel"`
	ServiceEntryId uuid.UUID             `json:"serviceEntryId"`
}

type Document88130Response struct {
	TakeoverDiagnosis []common3.TimelineModel `json:"takeoverDiagnosis"`
	ScheinId          *uuid.UUID              `json:"scheinId"`
	ServiceId         uuid.UUID               `json:"serviceId"`
}

type GetPsychotherapyTakeOverRequest struct {
	PatientId uuid.UUID `json:"patientId"`
}

type GetPsychotherapyTakeOverResponse struct {
	PsychotherapyEntry []common3.TimelineModel `json:"psychotherapyEntry"`
}

type TakeoverServiceTerminalApprovalRequest struct {
	TimelineId uuid.UUID `json:"timelineId"`
	ScheinId   uuid.UUID `json:"scheinId"`
}

type GetTakeOverDiagnosisRequest struct {
	PatientId uuid.UUID  `json:"patientId" validate:"required"`
	FromDate  *int64     `json:"fromDate"`
	ToDate    *int64     `json:"toDate"`
	Query     *string    `json:"query"`
	ScheinId  *uuid.UUID `json:"scheinId"`
}

type GetTakeOverDiagnosisResponse struct {
	TakeOverDiagnosisGroup []common3.TakeOverDiagnosisGroup `json:"takeOverDiagnosisGroup"`
}

type GetPsychotherapyBefore2020 struct {
	TimelineModel common3.TimelineModel                `json:"timelineModel"`
	ErrorCode     validation_timeline.ServiceErrorCode `json:"errorCode"`
	ServiceEntry  common3.TimelineModel                `json:"serviceEntry"`
}

type GetPsychotherapyBefore2020Request struct {
	PatientId uuid.UUID `json:"patientId"`
}

type GetPsychotherapyBefore2020Response struct {
	Data []GetPsychotherapyBefore2020 `json:"data"`
}

type UpdateManyRequest struct {
	TimelineModels []common3.TimelineModel `json:"timelineModels"`
}

type TakeOverDiagnosisWithScheinIdRequest struct {
	ScheinId                 uuid.UUID               `json:"scheinId"`
	TimelineModelIds         []uuid.UUID             `json:"timelineModelIds"`
	NewDiagnosis             []common3.TimelineModel `json:"newDiagnosis"`
	MappingTreatmentRelevent map[string]bool         `json:"mappingTreatmentRelevent"`
}

type MarkTreatmentRelevantRequest struct {
	TimelineId uuid.UUID `json:"timelineId" validate:"required"`
}

type UpdateEncounterCaseForServiceEntriesRequest struct {
	TimelineId uuid.UUID `json:"timelineId" validate:"required"`
}

type MarkAcceptedByKVRequest struct {
	TimelineId uuid.UUID `json:"timelineId"`
}

type DocumentSuggestionRequest struct {
	TimelineId     uuid.UUID          `json:"timelineId" validate:"required"`
	SuggestionCode string             `json:"suggestionCode" validate:"required"`
	SuggestionData *map[string]string `json:"suggestionData"`
}

type RollbackDocumentTerminateServiceRequest struct {
	TerminateServiceId uuid.UUID `json:"terminateServiceId"`
	TehcnicalScheinId  uuid.UUID `json:"tehcnicalScheinId"`
	PatientId          uuid.UUID `json:"patientId" validate:"required"`
}

type GetLastDocumentedQuarterRequest struct {
	PatientId          uuid.UUID                   `json:"patientId" validate:"required"`
	Year               int32                       `json:"year" validate:"required"`
	Quarter            int32                       `json:"quarter" validate:"required"`
	TimelineEntityType *common3.TimelineEntityType `json:"timelineEntityType"`
}

type GetLastDocumentedQuarterResponse struct {
	Year    int32 `json:"year"`
	Quarter int32 `json:"quarter"`
}

type GetTimelineByEnrollmentIdRequest struct {
	EnrollmentId uuid.UUID `json:"enrollmentId"`
	PatientId    uuid.UUID `json:"patientId" validate:"required"`
}

type GetTimelineByEnrollmentIdResponse struct {
	TimelineModel common3.TimelineModel `json:"timelineModel"`
}

type ReSendEABMailRequest struct {
	TimelineId uuid.UUID `json:"timelineId" validate:"required"`
}

type DocumentEABServiceCodeRequest struct {
	ScheinId  uuid.UUID `json:"scheinId" validate:"required"`
	PatientId uuid.UUID `json:"patientId" validate:"required"`
	BsnrCode  string    `json:"bsnrCode" validate:"required"`
}

type DocumentEABServiceCodeResponse struct {
	ServiceCodes []common1.EABServiceCode `json:"serviceCodes"`
}

type FindLatesTimelineEntryRequest struct {
	PatientId  uuid.UUID                  `json:"patientId"`
	Type       common3.TimelineEntityType `json:"type"`
	ContractId string                     `json:"contractId"`
}

type FindLatesTimelineEntryResponse struct {
	TimelineModel common3.TimelineModel `json:"timelineModel"`
}

type CheckIsVSST785Request struct {
	ContractId string    `json:"contractId"`
	PatientId  uuid.UUID `json:"patientId"`
	AtcCodes   []string  `json:"atcCodes"`
	IkNumber   *int32    `json:"ikNumber,omitempty"`
}

type CheckIsVSST785Response struct {
	IsVSST785 bool `json:"isVSST785"`
}

type GetDoctorLetterByIdRequest struct {
	DoctorLetterId uuid.UUID `json:"doctorLetterId"`
}

type GetDoctorLetterByIdResponse struct {
	TimelineModel *common3.TimelineModel `json:"timelineModel"`
}

type GetActionChainDiagnoseByCodesRequest struct {
	Codes []string `json:"codes"`
	Year  int32    `json:"year"`
}

type GetActionChainDiagnoseByCodesResponse struct {
	ValidItems   []string `json:"validItems"`
	InValidItems []string `json:"inValidItems"`
}

// enum definitions

// Define constants
const NATS_SUBJECT = "api.app.mvz" // nats subject this service will listen to

// service event constants
const EVENT_DocumentSuggestion = "api.app.mvz.TimelineApp.DocumentSuggestion"
const LEGACY_TOPIC_DocumentSuggestion = "/api/app/mvz/timeline/documentSuggestion"
const EVENT_MarkTreatmentRelevant = "api.app.mvz.TimelineApp.MarkTreatmentRelevant"
const LEGACY_TOPIC_MarkTreatmentRelevant = "/api/app/mvz/timeline/markTreatmentRelevant"
const EVENT_UpdateEncounterCaseForServiceEntries = "api.app.mvz.TimelineApp.UpdateEncounterCaseForServiceEntries"
const LEGACY_TOPIC_UpdateEncounterCaseForServiceEntries = "/api/app/mvz/timeline/updateEncounterCaseForServiceEntries"
const EVENT_GetDiagnose = "api.app.mvz.TimelineApp.GetDiagnose"
const LEGACY_TOPIC_GetDiagnose = "/api/app/mvz/timeline/getDiagnose"
const EVENT_HandleEventTimelineValidation = "api.app.mvz.TimelineApp.HandleEventTimelineValidation"
const LEGACY_TOPIC_HandleEventTimelineValidation = "/api/app/mvz/timeline/handleEventTimelineValidation"
const EVENT_HandleEventTimelineHardRemove = "api.app.mvz.TimelineApp.HandleEventTimelineHardRemove"
const LEGACY_TOPIC_HandleEventTimelineHardRemove = "/api/app/mvz/timeline/handleEventTimelineHardRemove"
const EVENT_HandleEventTimelineRemove = "api.app.mvz.TimelineApp.HandleEventTimelineRemove"
const LEGACY_TOPIC_HandleEventTimelineRemove = "/api/app/mvz/timeline/handleEventTimelineRemove"
const EVENT_HandleEventTimelineCreate = "api.app.mvz.TimelineApp.HandleEventTimelineCreate"
const LEGACY_TOPIC_HandleEventTimelineCreate = "/api/app/mvz/timeline/handleEventTimelineCreate"
const EVENT_HandleEventTimelineUpdate = "api.app.mvz.TimelineApp.HandleEventTimelineUpdate"
const LEGACY_TOPIC_HandleEventTimelineUpdate = "/api/app/mvz/timeline/handleEventTimelineUpdate"
const EVENT_HandleEventAutoAction = "api.app.mvz.TimelineApp.HandleEventAutoAction"
const LEGACY_TOPIC_HandleEventAutoAction = "/api/app/mvz/timeline/handleEventAutoAction"
const EVENT_GetById = "api.app.mvz.TimelineApp.GetById"
const LEGACY_TOPIC_GetById = "/api/app/mvz/timeline/getById"
const EVENT_GetByIds = "api.app.mvz.TimelineApp.GetByIds"
const LEGACY_TOPIC_GetByIds = "/api/app/mvz/timeline/getByIds"
const EVENT_Get = "api.app.mvz.TimelineApp.Get"
const LEGACY_TOPIC_Get = "/api/app/mvz/timeline/get"
const EVENT_Create = "api.app.mvz.TimelineApp.Create"
const LEGACY_TOPIC_Create = "/api/app/mvz/timeline/create"
const EVENT_Edit = "api.app.mvz.TimelineApp.Edit"
const LEGACY_TOPIC_Edit = "/api/app/mvz/timeline/edit"
const EVENT_UpdateMany = "api.app.mvz.TimelineApp.UpdateMany"
const LEGACY_TOPIC_UpdateMany = "/api/app/mvz/timeline/updateMany"
const EVENT_Remove = "api.app.mvz.TimelineApp.Remove"
const LEGACY_TOPIC_Remove = "/api/app/mvz/timeline/remove"
const EVENT_GroupByQuarter = "api.app.mvz.TimelineApp.GroupByQuarter"
const LEGACY_TOPIC_GroupByQuarter = "/api/app/mvz/timeline/groupByQuarter"
const EVENT_UpdatePrintDate = "api.app.mvz.TimelineApp.UpdatePrintDate"
const LEGACY_TOPIC_UpdatePrintDate = "/api/app/mvz/timeline/updatePrintDate"
const EVENT_UpdateSuggestionRuleApplied = "api.app.mvz.TimelineApp.UpdateSuggestionRuleApplied"
const LEGACY_TOPIC_UpdateSuggestionRuleApplied = "/api/app/mvz/timeline/updateSuggestionRuleApplied"
const EVENT_IgnoreSdkrwRule = "api.app.mvz.TimelineApp.IgnoreSdkrwRule"
const LEGACY_TOPIC_IgnoreSdkrwRule = "/api/app/mvz/timeline/ignoreSdkrwRule"
const EVENT_DeleteErezept = "api.app.mvz.TimelineApp.DeleteErezept"
const LEGACY_TOPIC_DeleteErezept = "/api/app/mvz/timeline/deleteErezept"
const EVENT_GetTherapies = "api.app.mvz.TimelineApp.GetTherapies"
const LEGACY_TOPIC_GetTherapies = "/api/app/mvz/timeline/getTherapies"
const EVENT_GetAmountServiceCode = "api.app.mvz.TimelineApp.GetAmountServiceCode"
const LEGACY_TOPIC_GetAmountServiceCode = "/api/app/mvz/timeline/getAmountServiceCode"
const EVENT_MarkNotApprovedPyschotherapy = "api.app.mvz.TimelineApp.MarkNotApprovedPyschotherapy"
const LEGACY_TOPIC_MarkNotApprovedPyschotherapy = "/api/app/mvz/timeline/markNotApprovedPyschotherapy"
const EVENT_RestoreEntryHistory = "api.app.mvz.TimelineApp.RestoreEntryHistory"
const LEGACY_TOPIC_RestoreEntryHistory = "/api/app/mvz/timeline/restoreEntryHistory"
const EVENT_ValidateDiagnose = "api.app.mvz.TimelineApp.ValidateDiagnose"
const LEGACY_TOPIC_ValidateDiagnose = "/api/app/mvz/timeline/validateDiagnose"
const EVENT_ReRunValidateService = "api.app.mvz.TimelineApp.ReRunValidateService"
const LEGACY_TOPIC_ReRunValidateService = "/api/app/mvz/timeline/reRunValidateService"
const EVENT_GetTimelineEntryByIds = "api.app.mvz.TimelineApp.GetTimelineEntryByIds"
const LEGACY_TOPIC_GetTimelineEntryByIds = "/api/app/mvz/timeline/getTimelineEntryByIds"
const EVENT_GetPreParticipationServiceCodes = "api.app.mvz.TimelineApp.GetPreParticipationServiceCodes"
const LEGACY_TOPIC_GetPreParticipationServiceCodes = "/api/app/mvz/timeline/getPreParticipationServiceCodes"
const EVENT_Document88130 = "api.app.mvz.TimelineApp.Document88130"
const LEGACY_TOPIC_Document88130 = "/api/app/mvz/timeline/document88130"
const EVENT_GetPsychotherapyTakeOver = "api.app.mvz.TimelineApp.GetPsychotherapyTakeOver"
const LEGACY_TOPIC_GetPsychotherapyTakeOver = "/api/app/mvz/timeline/getPsychotherapyTakeOver"
const EVENT_TakeoverServiceTerminalApproval = "api.app.mvz.TimelineApp.TakeoverServiceTerminalApproval"
const LEGACY_TOPIC_TakeoverServiceTerminalApproval = "/api/app/mvz/timeline/takeoverServiceTerminalApproval"
const EVENT_GetTakeOverDiagnosis = "api.app.mvz.TimelineApp.GetTakeOverDiagnosis"
const LEGACY_TOPIC_GetTakeOverDiagnosis = "/api/app/mvz/timeline/getTakeOverDiagnosis"
const EVENT_GetPsychotherapyBefore2020 = "api.app.mvz.TimelineApp.GetPsychotherapyBefore2020"
const LEGACY_TOPIC_GetPsychotherapyBefore2020 = "/api/app/mvz/timeline/getPsychotherapyBefore2020"
const EVENT_TakeOverDiagnosisWithScheinId = "api.app.mvz.TimelineApp.TakeOverDiagnosisWithScheinId"
const LEGACY_TOPIC_TakeOverDiagnosisWithScheinId = "/api/app/mvz/timeline/takeOverDiagnosisWithScheinId"
const EVENT_MarkAcceptedByKV = "api.app.mvz.TimelineApp.MarkAcceptedByKV"
const LEGACY_TOPIC_MarkAcceptedByKV = "/api/app/mvz/timeline/markAcceptedByKV"
const EVENT_RollbackDocumentTerminateService = "api.app.mvz.TimelineApp.RollbackDocumentTerminateService"
const LEGACY_TOPIC_RollbackDocumentTerminateService = "/api/app/mvz/timeline/rollbackDocumentTerminateService"
const EVENT_GetLastDocumentedQuarter = "api.app.mvz.TimelineApp.GetLastDocumentedQuarter"
const LEGACY_TOPIC_GetLastDocumentedQuarter = "/api/app/mvz/timeline/getLastDocumentedQuarter"
const EVENT_GetTimelineByEnrollmentId = "api.app.mvz.TimelineApp.GetTimelineByEnrollmentId"
const LEGACY_TOPIC_GetTimelineByEnrollmentId = "/api/app/mvz/timeline/getTimelineByEnrollmentId"
const EVENT_ReSendEABMail = "api.app.mvz.TimelineApp.ReSendEABMail"
const LEGACY_TOPIC_ReSendEABMail = "/api/app/mvz/timeline/reSendEABMail"
const EVENT_DocumentEABServiceCode = "api.app.mvz.TimelineApp.DocumentEABServiceCode"
const LEGACY_TOPIC_DocumentEABServiceCode = "/api/app/mvz/timeline/documentEABServiceCode"
const EVENT_FindLatestTimelineEntry = "api.app.mvz.TimelineApp.FindLatestTimelineEntry"
const LEGACY_TOPIC_FindLatestTimelineEntry = "/api/app/mvz/timeline/findLatestTimelineEntry"
const EVENT_CheckIsVSST785 = "api.app.mvz.TimelineApp.CheckIsVSST785"
const LEGACY_TOPIC_CheckIsVSST785 = "/api/app/mvz/timeline/checkIsVSST785"
const EVENT_GetDoctorLetterById = "api.app.mvz.TimelineApp.GetDoctorLetterById"
const LEGACY_TOPIC_GetDoctorLetterById = "/api/app/mvz/timeline/getDoctorLetterById"
const EVENT_GetActionChainDiagnoseByCodes = "api.app.mvz.TimelineApp.GetActionChainDiagnoseByCodes"
const LEGACY_TOPIC_GetActionChainDiagnoseByCodes = "/api/app/mvz/timeline/getActionChainDiagnoseByCodes"

// message event constants
const EVENT_TimelineHardRemove = "api.app.mvz.AppMvzTimeline.TimelineHardRemove"
const EVENT_TimelineRemove = "api.app.mvz.AppMvzTimeline.TimelineRemove"
const EVENT_TimelineRestore = "api.app.mvz.AppMvzTimeline.TimelineRestore"
const EVENT_TimelineCreate = "api.app.mvz.AppMvzTimeline.TimelineCreate"
const EVENT_TimelineUpdate = "api.app.mvz.AppMvzTimeline.TimelineUpdate"
const EVENT_AutoAction = "api.app.mvz.AppMvzTimeline.AutoAction"
const EVENT_TimelineValidation = "api.app.mvz.AppMvzTimeline.TimelineValidation"

// Define service interface -------------------------------------------------------------
type TimelineApp interface {
	DocumentSuggestion(ctx *titan.Context, request DocumentSuggestionRequest) error
	MarkTreatmentRelevant(ctx *titan.Context, request MarkTreatmentRelevantRequest) error
	UpdateEncounterCaseForServiceEntries(ctx *titan.Context, request UpdateEncounterCaseForServiceEntriesRequest) error
	GetDiagnose(ctx *titan.Context, request GetDiagnoseRequest) (*GetDiagnoseResponse, error)
	HandleEventTimelineValidation(ctx *titan.Context, request EventTimelineValidation) error
	HandleEventTimelineHardRemove(ctx *titan.Context, request EventTimelineHardRemove) error
	HandleEventTimelineRemove(ctx *titan.Context, request EventTimelineRemove) error
	HandleEventTimelineCreate(ctx *titan.Context, request EventTimelineCreate) error
	HandleEventTimelineUpdate(ctx *titan.Context, request EventTimelineUpdate) error
	HandleEventAutoAction(ctx *titan.Context, request EventAutoAction) error
	GetById(ctx *titan.Context, request GetByIdRequest) (*GetByIdResponse, error)
	GetByIds(ctx *titan.Context, request GetByIdsRequest) (*GetResponse, error)
	Get(ctx *titan.Context, request GetRequest) (*GetResponse, error)
	Create(ctx *titan.Context, request CreateRequest) (*CreateResponse, error)
	Edit(ctx *titan.Context, request EditRequest) (*EditResponse, error)
	UpdateMany(ctx *titan.Context, request UpdateManyRequest) error
	Remove(ctx *titan.Context, request RemoveRequest) (*RemoveResponse, error)
	GroupByQuarter(ctx *titan.Context, request GroupByQuarterRequest) (*GroupByQuarterResponse, error)
	UpdatePrintDate(ctx *titan.Context, request UpdatePrintDateRequest) (*UpdatePrintDateResponse, error)
	UpdateSuggestionRuleApplied(ctx *titan.Context, request UpdateSuggestionRuleAppliedRequest) error
	IgnoreSdkrwRule(ctx *titan.Context, request IgnoreSdkrwRuleRequest) error
	DeleteErezept(ctx *titan.Context, request DeleteErezeptRequest) error
	GetTherapies(ctx *titan.Context, request GetTherapiesRequest) (*GetTherapiesResponse, error)
	GetAmountServiceCode(ctx *titan.Context, request GetAmountServiceCodeRequest) (*GetAmountServiceCodeResponse, error)
	MarkNotApprovedPyschotherapy(ctx *titan.Context, request MarkNotApprovedPyschotherapyRequest) error
	RestoreEntryHistory(ctx *titan.Context, request RestoreEntryHistoryRequest) (*RestoreEntryHistoryResponse, error)
	ValidateDiagnose(ctx *titan.Context, request ValidateDiagnoseRequest) (*ValidateDiagnoseResponse, error)
	ReRunValidateService(ctx *titan.Context, request ReRunValidateServiceRequest) error
	GetTimelineEntryByIds(ctx *titan.Context, request GetTimelineEntryByIdsRequest) (*GetTimelineEntryIdsResponse, error)
	GetPreParticipationServiceCodes(ctx *titan.Context, request GetPreParticipationServiceCodesRequest) (*GetTimelineEntryIdsResponse, error)
	Document88130(ctx *titan.Context, request Document88130Request) (*Document88130Response, error)
	GetPsychotherapyTakeOver(ctx *titan.Context, request GetPsychotherapyTakeOverRequest) (*GetPsychotherapyTakeOverResponse, error)
	TakeoverServiceTerminalApproval(ctx *titan.Context, request TakeoverServiceTerminalApprovalRequest) error
	GetTakeOverDiagnosis(ctx *titan.Context, request GetTakeOverDiagnosisRequest) (*GetTakeOverDiagnosisResponse, error)
	GetPsychotherapyBefore2020(ctx *titan.Context, request GetPsychotherapyBefore2020Request) (*GetPsychotherapyBefore2020Response, error)
	TakeOverDiagnosisWithScheinId(ctx *titan.Context, request TakeOverDiagnosisWithScheinIdRequest) error
	MarkAcceptedByKV(ctx *titan.Context, request MarkAcceptedByKVRequest) error
	RollbackDocumentTerminateService(ctx *titan.Context, request RollbackDocumentTerminateServiceRequest) error
	GetLastDocumentedQuarter(ctx *titan.Context, request GetLastDocumentedQuarterRequest) (*GetLastDocumentedQuarterResponse, error)
	GetTimelineByEnrollmentId(ctx *titan.Context, request GetTimelineByEnrollmentIdRequest) (*GetTimelineByEnrollmentIdResponse, error)
	ReSendEABMail(ctx *titan.Context, request ReSendEABMailRequest) error
	DocumentEABServiceCode(ctx *titan.Context, request DocumentEABServiceCodeRequest) (*DocumentEABServiceCodeResponse, error)
	FindLatestTimelineEntry(ctx *titan.Context, request FindLatesTimelineEntryRequest) (*FindLatesTimelineEntryResponse, error)
	CheckIsVSST785(ctx *titan.Context, request CheckIsVSST785Request) (*CheckIsVSST785Response, error)
	GetDoctorLetterById(ctx *titan.Context, request GetDoctorLetterByIdRequest) (*GetDoctorLetterByIdResponse, error)
	GetActionChainDiagnoseByCodes(ctx *titan.Context, request GetActionChainDiagnoseByCodesRequest) (*GetActionChainDiagnoseByCodesResponse, error)
}

// Define service proxy -------------------------------------------------------------------
type TimelineAppProxy struct {
	service TimelineApp
}

func (srv *TimelineAppProxy) DocumentSuggestion(ctx *titan.Context, request DocumentSuggestionRequest) error {
	return srv.service.DocumentSuggestion(ctx, request)
}
func (srv *TimelineAppProxy) MarkTreatmentRelevant(ctx *titan.Context, request MarkTreatmentRelevantRequest) error {
	return srv.service.MarkTreatmentRelevant(ctx, request)
}
func (srv *TimelineAppProxy) UpdateEncounterCaseForServiceEntries(ctx *titan.Context, request UpdateEncounterCaseForServiceEntriesRequest) error {
	return srv.service.UpdateEncounterCaseForServiceEntries(ctx, request)
}
func (srv *TimelineAppProxy) GetDiagnose(ctx *titan.Context, request GetDiagnoseRequest) (*GetDiagnoseResponse, error) {
	return srv.service.GetDiagnose(ctx, request)
}
func (srv *TimelineAppProxy) HandleEventTimelineValidation(ctx *titan.Context, request EventTimelineValidation) error {
	return srv.service.HandleEventTimelineValidation(ctx, request)
}
func (srv *TimelineAppProxy) HandleEventTimelineHardRemove(ctx *titan.Context, request EventTimelineHardRemove) error {
	return srv.service.HandleEventTimelineHardRemove(ctx, request)
}
func (srv *TimelineAppProxy) HandleEventTimelineRemove(ctx *titan.Context, request EventTimelineRemove) error {
	return srv.service.HandleEventTimelineRemove(ctx, request)
}
func (srv *TimelineAppProxy) HandleEventTimelineCreate(ctx *titan.Context, request EventTimelineCreate) error {
	return srv.service.HandleEventTimelineCreate(ctx, request)
}
func (srv *TimelineAppProxy) HandleEventTimelineUpdate(ctx *titan.Context, request EventTimelineUpdate) error {
	return srv.service.HandleEventTimelineUpdate(ctx, request)
}
func (srv *TimelineAppProxy) HandleEventAutoAction(ctx *titan.Context, request EventAutoAction) error {
	return srv.service.HandleEventAutoAction(ctx, request)
}
func (srv *TimelineAppProxy) GetById(ctx *titan.Context, request GetByIdRequest) (*GetByIdResponse, error) {
	return srv.service.GetById(ctx, request)
}
func (srv *TimelineAppProxy) GetByIds(ctx *titan.Context, request GetByIdsRequest) (*GetResponse, error) {
	return srv.service.GetByIds(ctx, request)
}
func (srv *TimelineAppProxy) Get(ctx *titan.Context, request GetRequest) (*GetResponse, error) {
	return srv.service.Get(ctx, request)
}
func (srv *TimelineAppProxy) Create(ctx *titan.Context, request CreateRequest) (*CreateResponse, error) {
	return srv.service.Create(ctx, request)
}
func (srv *TimelineAppProxy) Edit(ctx *titan.Context, request EditRequest) (*EditResponse, error) {
	return srv.service.Edit(ctx, request)
}
func (srv *TimelineAppProxy) UpdateMany(ctx *titan.Context, request UpdateManyRequest) error {
	return srv.service.UpdateMany(ctx, request)
}
func (srv *TimelineAppProxy) Remove(ctx *titan.Context, request RemoveRequest) (*RemoveResponse, error) {
	return srv.service.Remove(ctx, request)
}
func (srv *TimelineAppProxy) GroupByQuarter(ctx *titan.Context, request GroupByQuarterRequest) (*GroupByQuarterResponse, error) {
	return srv.service.GroupByQuarter(ctx, request)
}
func (srv *TimelineAppProxy) UpdatePrintDate(ctx *titan.Context, request UpdatePrintDateRequest) (*UpdatePrintDateResponse, error) {
	return srv.service.UpdatePrintDate(ctx, request)
}
func (srv *TimelineAppProxy) UpdateSuggestionRuleApplied(ctx *titan.Context, request UpdateSuggestionRuleAppliedRequest) error {
	return srv.service.UpdateSuggestionRuleApplied(ctx, request)
}
func (srv *TimelineAppProxy) IgnoreSdkrwRule(ctx *titan.Context, request IgnoreSdkrwRuleRequest) error {
	return srv.service.IgnoreSdkrwRule(ctx, request)
}
func (srv *TimelineAppProxy) DeleteErezept(ctx *titan.Context, request DeleteErezeptRequest) error {
	return srv.service.DeleteErezept(ctx, request)
}
func (srv *TimelineAppProxy) GetTherapies(ctx *titan.Context, request GetTherapiesRequest) (*GetTherapiesResponse, error) {
	return srv.service.GetTherapies(ctx, request)
}
func (srv *TimelineAppProxy) GetAmountServiceCode(ctx *titan.Context, request GetAmountServiceCodeRequest) (*GetAmountServiceCodeResponse, error) {
	return srv.service.GetAmountServiceCode(ctx, request)
}
func (srv *TimelineAppProxy) MarkNotApprovedPyschotherapy(ctx *titan.Context, request MarkNotApprovedPyschotherapyRequest) error {
	return srv.service.MarkNotApprovedPyschotherapy(ctx, request)
}
func (srv *TimelineAppProxy) RestoreEntryHistory(ctx *titan.Context, request RestoreEntryHistoryRequest) (*RestoreEntryHistoryResponse, error) {
	return srv.service.RestoreEntryHistory(ctx, request)
}
func (srv *TimelineAppProxy) ValidateDiagnose(ctx *titan.Context, request ValidateDiagnoseRequest) (*ValidateDiagnoseResponse, error) {
	return srv.service.ValidateDiagnose(ctx, request)
}
func (srv *TimelineAppProxy) ReRunValidateService(ctx *titan.Context, request ReRunValidateServiceRequest) error {
	return srv.service.ReRunValidateService(ctx, request)
}
func (srv *TimelineAppProxy) GetTimelineEntryByIds(ctx *titan.Context, request GetTimelineEntryByIdsRequest) (*GetTimelineEntryIdsResponse, error) {
	return srv.service.GetTimelineEntryByIds(ctx, request)
}
func (srv *TimelineAppProxy) GetPreParticipationServiceCodes(ctx *titan.Context, request GetPreParticipationServiceCodesRequest) (*GetTimelineEntryIdsResponse, error) {
	return srv.service.GetPreParticipationServiceCodes(ctx, request)
}
func (srv *TimelineAppProxy) Document88130(ctx *titan.Context, request Document88130Request) (*Document88130Response, error) {
	return srv.service.Document88130(ctx, request)
}
func (srv *TimelineAppProxy) GetPsychotherapyTakeOver(ctx *titan.Context, request GetPsychotherapyTakeOverRequest) (*GetPsychotherapyTakeOverResponse, error) {
	return srv.service.GetPsychotherapyTakeOver(ctx, request)
}
func (srv *TimelineAppProxy) TakeoverServiceTerminalApproval(ctx *titan.Context, request TakeoverServiceTerminalApprovalRequest) error {
	return srv.service.TakeoverServiceTerminalApproval(ctx, request)
}
func (srv *TimelineAppProxy) GetTakeOverDiagnosis(ctx *titan.Context, request GetTakeOverDiagnosisRequest) (*GetTakeOverDiagnosisResponse, error) {
	return srv.service.GetTakeOverDiagnosis(ctx, request)
}
func (srv *TimelineAppProxy) GetPsychotherapyBefore2020(ctx *titan.Context, request GetPsychotherapyBefore2020Request) (*GetPsychotherapyBefore2020Response, error) {
	return srv.service.GetPsychotherapyBefore2020(ctx, request)
}
func (srv *TimelineAppProxy) TakeOverDiagnosisWithScheinId(ctx *titan.Context, request TakeOverDiagnosisWithScheinIdRequest) error {
	return srv.service.TakeOverDiagnosisWithScheinId(ctx, request)
}
func (srv *TimelineAppProxy) MarkAcceptedByKV(ctx *titan.Context, request MarkAcceptedByKVRequest) error {
	return srv.service.MarkAcceptedByKV(ctx, request)
}
func (srv *TimelineAppProxy) RollbackDocumentTerminateService(ctx *titan.Context, request RollbackDocumentTerminateServiceRequest) error {
	return srv.service.RollbackDocumentTerminateService(ctx, request)
}
func (srv *TimelineAppProxy) GetLastDocumentedQuarter(ctx *titan.Context, request GetLastDocumentedQuarterRequest) (*GetLastDocumentedQuarterResponse, error) {
	return srv.service.GetLastDocumentedQuarter(ctx, request)
}
func (srv *TimelineAppProxy) GetTimelineByEnrollmentId(ctx *titan.Context, request GetTimelineByEnrollmentIdRequest) (*GetTimelineByEnrollmentIdResponse, error) {
	return srv.service.GetTimelineByEnrollmentId(ctx, request)
}
func (srv *TimelineAppProxy) ReSendEABMail(ctx *titan.Context, request ReSendEABMailRequest) error {
	return srv.service.ReSendEABMail(ctx, request)
}
func (srv *TimelineAppProxy) DocumentEABServiceCode(ctx *titan.Context, request DocumentEABServiceCodeRequest) (*DocumentEABServiceCodeResponse, error) {
	return srv.service.DocumentEABServiceCode(ctx, request)
}
func (srv *TimelineAppProxy) FindLatestTimelineEntry(ctx *titan.Context, request FindLatesTimelineEntryRequest) (*FindLatesTimelineEntryResponse, error) {
	return srv.service.FindLatestTimelineEntry(ctx, request)
}
func (srv *TimelineAppProxy) CheckIsVSST785(ctx *titan.Context, request CheckIsVSST785Request) (*CheckIsVSST785Response, error) {
	return srv.service.CheckIsVSST785(ctx, request)
}
func (srv *TimelineAppProxy) GetDoctorLetterById(ctx *titan.Context, request GetDoctorLetterByIdRequest) (*GetDoctorLetterByIdResponse, error) {
	return srv.service.GetDoctorLetterById(ctx, request)
}
func (srv *TimelineAppProxy) GetActionChainDiagnoseByCodes(ctx *titan.Context, request GetActionChainDiagnoseByCodesRequest) (*GetActionChainDiagnoseByCodesResponse, error) {
	return srv.service.GetActionChainDiagnoseByCodes(ctx, request)
}

// Define service router -----------------------------------------------------------------
type TimelineAppRouter struct {
	proxy *TimelineAppProxy
}

func (router *TimelineAppRouter) Register(r titan.Router) {
	r.RegisterTopic(EVENT_DocumentSuggestion, router.proxy.DocumentSuggestion, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_DocumentSuggestion, router.proxy.DocumentSuggestion, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_MarkTreatmentRelevant, router.proxy.MarkTreatmentRelevant, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_MarkTreatmentRelevant, router.proxy.MarkTreatmentRelevant, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_UpdateEncounterCaseForServiceEntries, router.proxy.UpdateEncounterCaseForServiceEntries, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_UpdateEncounterCaseForServiceEntries, router.proxy.UpdateEncounterCaseForServiceEntries, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetDiagnose, router.proxy.GetDiagnose, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetDiagnose, router.proxy.GetDiagnose, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetById, router.proxy.GetById, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetById, router.proxy.GetById, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetByIds, router.proxy.GetByIds, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetByIds, router.proxy.GetByIds, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_Get, router.proxy.Get, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_Get, router.proxy.Get, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_Create, router.proxy.Create, titan.IsUser())
	r.RegisterTopic(LEGACY_TOPIC_Create, router.proxy.Create, titan.IsUser())
	r.RegisterTopic(EVENT_Edit, router.proxy.Edit, titan.IsUser())
	r.RegisterTopic(LEGACY_TOPIC_Edit, router.proxy.Edit, titan.IsUser())
	r.RegisterTopic(EVENT_UpdateMany, router.proxy.UpdateMany, titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_UpdateMany, router.proxy.UpdateMany, titan.IsAuthenticated())
	r.RegisterTopic(EVENT_Remove, router.proxy.Remove, titan.IsUser())
	r.RegisterTopic(LEGACY_TOPIC_Remove, router.proxy.Remove, titan.IsUser())
	r.RegisterTopic(EVENT_GroupByQuarter, router.proxy.GroupByQuarter, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GroupByQuarter, router.proxy.GroupByQuarter, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_UpdatePrintDate, router.proxy.UpdatePrintDate, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_UpdatePrintDate, router.proxy.UpdatePrintDate, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_UpdateSuggestionRuleApplied, router.proxy.UpdateSuggestionRuleApplied, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_UpdateSuggestionRuleApplied, router.proxy.UpdateSuggestionRuleApplied, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_IgnoreSdkrwRule, router.proxy.IgnoreSdkrwRule, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_IgnoreSdkrwRule, router.proxy.IgnoreSdkrwRule, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_DeleteErezept, router.proxy.DeleteErezept, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_DeleteErezept, router.proxy.DeleteErezept, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetTherapies, router.proxy.GetTherapies, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetTherapies, router.proxy.GetTherapies, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetAmountServiceCode, router.proxy.GetAmountServiceCode, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetAmountServiceCode, router.proxy.GetAmountServiceCode, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_MarkNotApprovedPyschotherapy, router.proxy.MarkNotApprovedPyschotherapy, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_MarkNotApprovedPyschotherapy, router.proxy.MarkNotApprovedPyschotherapy, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_RestoreEntryHistory, router.proxy.RestoreEntryHistory, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_RestoreEntryHistory, router.proxy.RestoreEntryHistory, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_ValidateDiagnose, router.proxy.ValidateDiagnose, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_ValidateDiagnose, router.proxy.ValidateDiagnose, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_ReRunValidateService, router.proxy.ReRunValidateService, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_ReRunValidateService, router.proxy.ReRunValidateService, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetTimelineEntryByIds, router.proxy.GetTimelineEntryByIds, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetTimelineEntryByIds, router.proxy.GetTimelineEntryByIds, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetPreParticipationServiceCodes, router.proxy.GetPreParticipationServiceCodes, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetPreParticipationServiceCodes, router.proxy.GetPreParticipationServiceCodes, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_Document88130, router.proxy.Document88130, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_Document88130, router.proxy.Document88130, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetPsychotherapyTakeOver, router.proxy.GetPsychotherapyTakeOver, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetPsychotherapyTakeOver, router.proxy.GetPsychotherapyTakeOver, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_TakeoverServiceTerminalApproval, router.proxy.TakeoverServiceTerminalApproval, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_TakeoverServiceTerminalApproval, router.proxy.TakeoverServiceTerminalApproval, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetTakeOverDiagnosis, router.proxy.GetTakeOverDiagnosis, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetTakeOverDiagnosis, router.proxy.GetTakeOverDiagnosis, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetPsychotherapyBefore2020, router.proxy.GetPsychotherapyBefore2020, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetPsychotherapyBefore2020, router.proxy.GetPsychotherapyBefore2020, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_TakeOverDiagnosisWithScheinId, router.proxy.TakeOverDiagnosisWithScheinId, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_TakeOverDiagnosisWithScheinId, router.proxy.TakeOverDiagnosisWithScheinId, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_MarkAcceptedByKV, router.proxy.MarkAcceptedByKV, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_MarkAcceptedByKV, router.proxy.MarkAcceptedByKV, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_RollbackDocumentTerminateService, router.proxy.RollbackDocumentTerminateService, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_RollbackDocumentTerminateService, router.proxy.RollbackDocumentTerminateService, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetLastDocumentedQuarter, router.proxy.GetLastDocumentedQuarter, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetLastDocumentedQuarter, router.proxy.GetLastDocumentedQuarter, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetTimelineByEnrollmentId, router.proxy.GetTimelineByEnrollmentId, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetTimelineByEnrollmentId, router.proxy.GetTimelineByEnrollmentId, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_ReSendEABMail, router.proxy.ReSendEABMail, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_ReSendEABMail, router.proxy.ReSendEABMail, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_DocumentEABServiceCode, router.proxy.DocumentEABServiceCode, titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_DocumentEABServiceCode, router.proxy.DocumentEABServiceCode, titan.IsAuthenticated())
	r.RegisterTopic(EVENT_FindLatestTimelineEntry, router.proxy.FindLatestTimelineEntry, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_FindLatestTimelineEntry, router.proxy.FindLatestTimelineEntry, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_CheckIsVSST785, router.proxy.CheckIsVSST785, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_CheckIsVSST785, router.proxy.CheckIsVSST785, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetDoctorLetterById, router.proxy.GetDoctorLetterById, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetDoctorLetterById, router.proxy.GetDoctorLetterById, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetActionChainDiagnoseByCodes, router.proxy.GetActionChainDiagnoseByCodes, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetActionChainDiagnoseByCodes, router.proxy.GetActionChainDiagnoseByCodes, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
}

// Subscriber
func (router *TimelineAppRouter) Subscribe(s *titan.MessageSubscriber) {
	s.Register(EVENT_TimelineValidation, "api.app.mvz_TimelineApp_HandleEventTimelineValidation_Queue", func(p *titan.Message) error {
		var resp EventTimelineValidation
		ctx, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		return router.proxy.HandleEventTimelineValidation(ctx, resp)
	})
	s.Register(EVENT_TimelineHardRemove, "api.app.mvz_TimelineApp_HandleEventTimelineHardRemove_Queue", func(p *titan.Message) error {
		var resp EventTimelineHardRemove
		ctx, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		return router.proxy.HandleEventTimelineHardRemove(ctx, resp)
	})
	s.Register(EVENT_TimelineRemove, "api.app.mvz_TimelineApp_HandleEventTimelineRemove_Queue", func(p *titan.Message) error {
		var resp EventTimelineRemove
		ctx, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		return router.proxy.HandleEventTimelineRemove(ctx, resp)
	})
	s.Register(EVENT_TimelineCreate, "api.app.mvz_TimelineApp_HandleEventTimelineCreate_Queue", func(p *titan.Message) error {
		var resp EventTimelineCreate
		ctx, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		return router.proxy.HandleEventTimelineCreate(ctx, resp)
	})
	s.Register(EVENT_TimelineUpdate, "api.app.mvz_TimelineApp_HandleEventTimelineUpdate_Queue", func(p *titan.Message) error {
		var resp EventTimelineUpdate
		ctx, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		return router.proxy.HandleEventTimelineUpdate(ctx, resp)
	})
	s.Register(EVENT_AutoAction, "api.app.mvz_TimelineApp_HandleEventAutoAction_Queue", func(p *titan.Message) error {
		var resp EventAutoAction
		ctx, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		return router.proxy.HandleEventAutoAction(ctx, resp)
	})
}

func NewTimelineAppRouter(s TimelineApp) *TimelineAppRouter {
	p := &TimelineAppProxy{s}
	return &TimelineAppRouter{p}
}

func NewServer(bff0 TimelineApp, options ...titan.Option) *titan.Server {
	opt := options

	router0 := NewTimelineAppRouter(bff0)
	opt = append(opt, titan.Routes(router0.Register), titan.Subscribe(router0.Subscribe))

	return titan.NewServer(NATS_SUBJECT, opt...)
}

// Define client ----------------------------------------------------------------------------------------------
type TimelineAppClient struct {
	client *titan.Client
}

func (srv *TimelineAppClient) DocumentSuggestion(ctx *titan.Context, request DocumentSuggestionRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_DocumentSuggestion).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *TimelineAppClient) MarkTreatmentRelevant(ctx *titan.Context, request MarkTreatmentRelevantRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_MarkTreatmentRelevant).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *TimelineAppClient) UpdateEncounterCaseForServiceEntries(ctx *titan.Context, request UpdateEncounterCaseForServiceEntriesRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UpdateEncounterCaseForServiceEntries).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *TimelineAppClient) GetDiagnose(ctx *titan.Context, request GetDiagnoseRequest) (*GetDiagnoseResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetDiagnose).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetDiagnoseResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) GetById(ctx *titan.Context, request GetByIdRequest) (*GetByIdResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetById).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetByIdResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) GetByIds(ctx *titan.Context, request GetByIdsRequest) (*GetResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetByIds).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) Get(ctx *titan.Context, request GetRequest) (*GetResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_Get).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) Create(ctx *titan.Context, request CreateRequest) (*CreateResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_Create).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &CreateResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) Edit(ctx *titan.Context, request EditRequest) (*EditResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_Edit).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &EditResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) UpdateMany(ctx *titan.Context, request UpdateManyRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UpdateMany).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *TimelineAppClient) Remove(ctx *titan.Context, request RemoveRequest) (*RemoveResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_Remove).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &RemoveResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) GroupByQuarter(ctx *titan.Context, request GroupByQuarterRequest) (*GroupByQuarterResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GroupByQuarter).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GroupByQuarterResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) UpdatePrintDate(ctx *titan.Context, request UpdatePrintDateRequest) (*UpdatePrintDateResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UpdatePrintDate).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &UpdatePrintDateResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) UpdateSuggestionRuleApplied(ctx *titan.Context, request UpdateSuggestionRuleAppliedRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UpdateSuggestionRuleApplied).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *TimelineAppClient) IgnoreSdkrwRule(ctx *titan.Context, request IgnoreSdkrwRuleRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_IgnoreSdkrwRule).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *TimelineAppClient) DeleteErezept(ctx *titan.Context, request DeleteErezeptRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_DeleteErezept).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *TimelineAppClient) GetTherapies(ctx *titan.Context, request GetTherapiesRequest) (*GetTherapiesResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetTherapies).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetTherapiesResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) GetAmountServiceCode(ctx *titan.Context, request GetAmountServiceCodeRequest) (*GetAmountServiceCodeResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetAmountServiceCode).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetAmountServiceCodeResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) MarkNotApprovedPyschotherapy(ctx *titan.Context, request MarkNotApprovedPyschotherapyRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_MarkNotApprovedPyschotherapy).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *TimelineAppClient) RestoreEntryHistory(ctx *titan.Context, request RestoreEntryHistoryRequest) (*RestoreEntryHistoryResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_RestoreEntryHistory).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &RestoreEntryHistoryResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) ValidateDiagnose(ctx *titan.Context, request ValidateDiagnoseRequest) (*ValidateDiagnoseResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_ValidateDiagnose).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &ValidateDiagnoseResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) ReRunValidateService(ctx *titan.Context, request ReRunValidateServiceRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_ReRunValidateService).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *TimelineAppClient) GetTimelineEntryByIds(ctx *titan.Context, request GetTimelineEntryByIdsRequest) (*GetTimelineEntryIdsResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetTimelineEntryByIds).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetTimelineEntryIdsResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) GetPreParticipationServiceCodes(ctx *titan.Context, request GetPreParticipationServiceCodesRequest) (*GetTimelineEntryIdsResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetPreParticipationServiceCodes).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetTimelineEntryIdsResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) Document88130(ctx *titan.Context, request Document88130Request) (*Document88130Response, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_Document88130).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &Document88130Response{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) GetPsychotherapyTakeOver(ctx *titan.Context, request GetPsychotherapyTakeOverRequest) (*GetPsychotherapyTakeOverResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetPsychotherapyTakeOver).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetPsychotherapyTakeOverResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) TakeoverServiceTerminalApproval(ctx *titan.Context, request TakeoverServiceTerminalApprovalRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_TakeoverServiceTerminalApproval).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *TimelineAppClient) GetTakeOverDiagnosis(ctx *titan.Context, request GetTakeOverDiagnosisRequest) (*GetTakeOverDiagnosisResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetTakeOverDiagnosis).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetTakeOverDiagnosisResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) GetPsychotherapyBefore2020(ctx *titan.Context, request GetPsychotherapyBefore2020Request) (*GetPsychotherapyBefore2020Response, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetPsychotherapyBefore2020).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetPsychotherapyBefore2020Response{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) TakeOverDiagnosisWithScheinId(ctx *titan.Context, request TakeOverDiagnosisWithScheinIdRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_TakeOverDiagnosisWithScheinId).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *TimelineAppClient) MarkAcceptedByKV(ctx *titan.Context, request MarkAcceptedByKVRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_MarkAcceptedByKV).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *TimelineAppClient) RollbackDocumentTerminateService(ctx *titan.Context, request RollbackDocumentTerminateServiceRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_RollbackDocumentTerminateService).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *TimelineAppClient) GetLastDocumentedQuarter(ctx *titan.Context, request GetLastDocumentedQuarterRequest) (*GetLastDocumentedQuarterResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetLastDocumentedQuarter).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetLastDocumentedQuarterResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) GetTimelineByEnrollmentId(ctx *titan.Context, request GetTimelineByEnrollmentIdRequest) (*GetTimelineByEnrollmentIdResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetTimelineByEnrollmentId).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetTimelineByEnrollmentIdResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) ReSendEABMail(ctx *titan.Context, request ReSendEABMailRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_ReSendEABMail).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *TimelineAppClient) DocumentEABServiceCode(ctx *titan.Context, request DocumentEABServiceCodeRequest) (*DocumentEABServiceCodeResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_DocumentEABServiceCode).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &DocumentEABServiceCodeResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) FindLatestTimelineEntry(ctx *titan.Context, request FindLatesTimelineEntryRequest) (*FindLatesTimelineEntryResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_FindLatestTimelineEntry).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &FindLatesTimelineEntryResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) CheckIsVSST785(ctx *titan.Context, request CheckIsVSST785Request) (*CheckIsVSST785Response, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CheckIsVSST785).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &CheckIsVSST785Response{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) GetDoctorLetterById(ctx *titan.Context, request GetDoctorLetterByIdRequest) (*GetDoctorLetterByIdResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetDoctorLetterById).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetDoctorLetterByIdResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *TimelineAppClient) GetActionChainDiagnoseByCodes(ctx *titan.Context, request GetActionChainDiagnoseByCodesRequest) (*GetActionChainDiagnoseByCodesResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetActionChainDiagnoseByCodes).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetActionChainDiagnoseByCodesResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func NewTimelineAppClient(clients ...*titan.Client) *TimelineAppClient {
	var client *titan.Client
	if len(clients) <= 0 {
		client = titan.GetDefaultClient()
	} else {
		client = clients[0]
	}
	return &TimelineAppClient{client: client}
}

// Define event Notifier -----------------------------------------------------------------------------------------
type TimelineNotifier struct {
	client *titan.Client
}

func NewTimelineNotifier() *TimelineNotifier {
	client := titan.GetDefaultClient()
	return &TimelineNotifier{client}
}
func (p *TimelineNotifier) NotifyTimelineHardRemove(ctx *titan.Context, event *EventTimelineHardRemove) error {
	return p.client.Publish(ctx, EVENT_TimelineHardRemove, event)
}
func (p *TimelineNotifier) NotifyTimelineRemove(ctx *titan.Context, event *EventTimelineRemove) error {
	return p.client.Publish(ctx, EVENT_TimelineRemove, event)
}
func (p *TimelineNotifier) NotifyTimelineRestore(ctx *titan.Context, event *EventTimelineRestore) error {
	return p.client.Publish(ctx, EVENT_TimelineRestore, event)
}
func (p *TimelineNotifier) NotifyTimelineCreate(ctx *titan.Context, event *EventTimelineCreate) error {
	return p.client.Publish(ctx, EVENT_TimelineCreate, event)
}
func (p *TimelineNotifier) NotifyTimelineUpdate(ctx *titan.Context, event *EventTimelineUpdate) error {
	return p.client.Publish(ctx, EVENT_TimelineUpdate, event)
}
func (p *TimelineNotifier) NotifyAutoAction(ctx *titan.Context, event *EventAutoAction) error {
	return p.client.Publish(ctx, EVENT_AutoAction, event)
}
func (p *TimelineNotifier) NotifyTimelineValidation(ctx *titan.Context, event *EventTimelineValidation) error {
	return p.client.Publish(ctx, EVENT_TimelineValidation, event)
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type TimelineSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewTimelineSocketNotifier(socket *socket_api.SocketServiceClient) *TimelineSocketNotifier {
	return &TimelineSocketNotifier{socket}
}
func (n *TimelineSocketNotifier) NotifyCareProviderTimelineHardRemove(ctx *titan.Context, event *EventTimelineHardRemove) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineHardRemove,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyUserTimelineHardRemove(ctx *titan.Context, event *EventTimelineHardRemove) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineHardRemove,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *TimelineSocketNotifier) NotifyDeviceTimelineHardRemove(ctx *titan.Context, event *EventTimelineHardRemove) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineHardRemove,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyClientTimelineHardRemove(ctx *titan.Context, event *EventTimelineHardRemove) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineHardRemove,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyCareProviderTimelineRemove(ctx *titan.Context, event *EventTimelineRemove) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineRemove,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyUserTimelineRemove(ctx *titan.Context, event *EventTimelineRemove) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineRemove,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *TimelineSocketNotifier) NotifyDeviceTimelineRemove(ctx *titan.Context, event *EventTimelineRemove) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineRemove,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyClientTimelineRemove(ctx *titan.Context, event *EventTimelineRemove) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineRemove,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyCareProviderTimelineRestore(ctx *titan.Context, event *EventTimelineRestore) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineRestore,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyUserTimelineRestore(ctx *titan.Context, event *EventTimelineRestore) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineRestore,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *TimelineSocketNotifier) NotifyDeviceTimelineRestore(ctx *titan.Context, event *EventTimelineRestore) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineRestore,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyClientTimelineRestore(ctx *titan.Context, event *EventTimelineRestore) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineRestore,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyCareProviderTimelineCreate(ctx *titan.Context, event *EventTimelineCreate) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineCreate,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyUserTimelineCreate(ctx *titan.Context, event *EventTimelineCreate) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineCreate,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *TimelineSocketNotifier) NotifyDeviceTimelineCreate(ctx *titan.Context, event *EventTimelineCreate) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineCreate,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyClientTimelineCreate(ctx *titan.Context, event *EventTimelineCreate) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineCreate,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyCareProviderTimelineUpdate(ctx *titan.Context, event *EventTimelineUpdate) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineUpdate,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyUserTimelineUpdate(ctx *titan.Context, event *EventTimelineUpdate) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineUpdate,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *TimelineSocketNotifier) NotifyDeviceTimelineUpdate(ctx *titan.Context, event *EventTimelineUpdate) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineUpdate,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyClientTimelineUpdate(ctx *titan.Context, event *EventTimelineUpdate) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineUpdate,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyCareProviderAutoAction(ctx *titan.Context, event *EventAutoAction) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_AutoAction,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyUserAutoAction(ctx *titan.Context, event *EventAutoAction) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_AutoAction,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *TimelineSocketNotifier) NotifyDeviceAutoAction(ctx *titan.Context, event *EventAutoAction) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_AutoAction,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyClientAutoAction(ctx *titan.Context, event *EventAutoAction) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_AutoAction,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyCareProviderTimelineValidation(ctx *titan.Context, event *EventTimelineValidation) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineValidation,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyUserTimelineValidation(ctx *titan.Context, event *EventTimelineValidation) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineValidation,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *TimelineSocketNotifier) NotifyDeviceTimelineValidation(ctx *titan.Context, event *EventTimelineValidation) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineValidation,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *TimelineSocketNotifier) NotifyClientTimelineValidation(ctx *titan.Context, event *EventTimelineValidation) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_TimelineValidation,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}

// -----------------------------------------------
// Test event listener
type TimelineEventListener struct {
	mux                             sync.Mutex
	LastEventTimelineHardRemoveList []EventTimelineHardRemove
	LastEventTimelineRemoveList     []EventTimelineRemove
	LastEventTimelineRestoreList    []EventTimelineRestore
	LastEventTimelineCreateList     []EventTimelineCreate
	LastEventTimelineUpdateList     []EventTimelineUpdate
	LastEventAutoActionList         []EventAutoAction
	LastEventTimelineValidationList []EventTimelineValidation
}

func (listener *TimelineEventListener) Reset() {
	listener.mux.Lock()
	listener.LastEventTimelineHardRemoveList = []EventTimelineHardRemove{}
	listener.LastEventTimelineRemoveList = []EventTimelineRemove{}
	listener.LastEventTimelineRestoreList = []EventTimelineRestore{}
	listener.LastEventTimelineCreateList = []EventTimelineCreate{}
	listener.LastEventTimelineUpdateList = []EventTimelineUpdate{}
	listener.LastEventAutoActionList = []EventAutoAction{}
	listener.LastEventTimelineValidationList = []EventTimelineValidation{}
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *TimelineEventListener) Subscribe(s *titan.MessageSubscriber) {
	s.Register(EVENT_TimelineHardRemove, "api.app.mvz_AppMvzTimeline_TimelineHardRemove_Queue_test", func(p *titan.Message) error {
		var resp EventTimelineHardRemove
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventTimelineHardRemoveList) >= 100 {
			listener.LastEventTimelineHardRemoveList = listener.LastEventTimelineHardRemoveList[1:]
		}
		listener.LastEventTimelineHardRemoveList = append(listener.LastEventTimelineHardRemoveList, resp)
		listener.mux.Unlock()
		return nil
	})
	s.Register(EVENT_TimelineRemove, "api.app.mvz_AppMvzTimeline_TimelineRemove_Queue_test", func(p *titan.Message) error {
		var resp EventTimelineRemove
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventTimelineRemoveList) >= 100 {
			listener.LastEventTimelineRemoveList = listener.LastEventTimelineRemoveList[1:]
		}
		listener.LastEventTimelineRemoveList = append(listener.LastEventTimelineRemoveList, resp)
		listener.mux.Unlock()
		return nil
	})
	s.Register(EVENT_TimelineRestore, "api.app.mvz_AppMvzTimeline_TimelineRestore_Queue_test", func(p *titan.Message) error {
		var resp EventTimelineRestore
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventTimelineRestoreList) >= 100 {
			listener.LastEventTimelineRestoreList = listener.LastEventTimelineRestoreList[1:]
		}
		listener.LastEventTimelineRestoreList = append(listener.LastEventTimelineRestoreList, resp)
		listener.mux.Unlock()
		return nil
	})
	s.Register(EVENT_TimelineCreate, "api.app.mvz_AppMvzTimeline_TimelineCreate_Queue_test", func(p *titan.Message) error {
		var resp EventTimelineCreate
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventTimelineCreateList) >= 100 {
			listener.LastEventTimelineCreateList = listener.LastEventTimelineCreateList[1:]
		}
		listener.LastEventTimelineCreateList = append(listener.LastEventTimelineCreateList, resp)
		listener.mux.Unlock()
		return nil
	})
	s.Register(EVENT_TimelineUpdate, "api.app.mvz_AppMvzTimeline_TimelineUpdate_Queue_test", func(p *titan.Message) error {
		var resp EventTimelineUpdate
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventTimelineUpdateList) >= 100 {
			listener.LastEventTimelineUpdateList = listener.LastEventTimelineUpdateList[1:]
		}
		listener.LastEventTimelineUpdateList = append(listener.LastEventTimelineUpdateList, resp)
		listener.mux.Unlock()
		return nil
	})
	s.Register(EVENT_AutoAction, "api.app.mvz_AppMvzTimeline_AutoAction_Queue_test", func(p *titan.Message) error {
		var resp EventAutoAction
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventAutoActionList) >= 100 {
			listener.LastEventAutoActionList = listener.LastEventAutoActionList[1:]
		}
		listener.LastEventAutoActionList = append(listener.LastEventAutoActionList, resp)
		listener.mux.Unlock()
		return nil
	})
	s.Register(EVENT_TimelineValidation, "api.app.mvz_AppMvzTimeline_TimelineValidation_Queue_test", func(p *titan.Message) error {
		var resp EventTimelineValidation
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventTimelineValidationList) >= 100 {
			listener.LastEventTimelineValidationList = listener.LastEventTimelineValidationList[1:]
		}
		listener.LastEventTimelineValidationList = append(listener.LastEventTimelineValidationList, resp)
		listener.mux.Unlock()
		return nil
	})
}
func (listener *TimelineEventListener) GetLastEventTimelineHardRemoveList(timeOutInMilliSeconds time.Duration) []EventTimelineHardRemove {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventTimelineHardRemoveList
		default:
			// if any value
			if len(listener.LastEventTimelineHardRemoveList) > 0 {
				return listener.LastEventTimelineHardRemoveList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
func (listener *TimelineEventListener) GetLastEventTimelineRemoveList(timeOutInMilliSeconds time.Duration) []EventTimelineRemove {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventTimelineRemoveList
		default:
			// if any value
			if len(listener.LastEventTimelineRemoveList) > 0 {
				return listener.LastEventTimelineRemoveList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
func (listener *TimelineEventListener) GetLastEventTimelineRestoreList(timeOutInMilliSeconds time.Duration) []EventTimelineRestore {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventTimelineRestoreList
		default:
			// if any value
			if len(listener.LastEventTimelineRestoreList) > 0 {
				return listener.LastEventTimelineRestoreList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
func (listener *TimelineEventListener) GetLastEventTimelineCreateList(timeOutInMilliSeconds time.Duration) []EventTimelineCreate {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventTimelineCreateList
		default:
			// if any value
			if len(listener.LastEventTimelineCreateList) > 0 {
				return listener.LastEventTimelineCreateList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
func (listener *TimelineEventListener) GetLastEventTimelineUpdateList(timeOutInMilliSeconds time.Duration) []EventTimelineUpdate {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventTimelineUpdateList
		default:
			// if any value
			if len(listener.LastEventTimelineUpdateList) > 0 {
				return listener.LastEventTimelineUpdateList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
func (listener *TimelineEventListener) GetLastEventAutoActionList(timeOutInMilliSeconds time.Duration) []EventAutoAction {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventAutoActionList
		default:
			// if any value
			if len(listener.LastEventAutoActionList) > 0 {
				return listener.LastEventAutoActionList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
func (listener *TimelineEventListener) GetLastEventTimelineValidationList(timeOutInMilliSeconds time.Duration) []EventTimelineValidation {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventTimelineValidationList
		default:
			// if any value
			if len(listener.LastEventTimelineValidationList) > 0 {
				return listener.LastEventTimelineValidationList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
