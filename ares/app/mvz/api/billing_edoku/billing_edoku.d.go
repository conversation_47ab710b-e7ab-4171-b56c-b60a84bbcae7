// This code was autogenerated from app/mvz/billing_edoku.proto, do not edit.

package billing_edoku

import (
	"encoding/json"
	"errors"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	billing_history_common "git.tutum.dev/medi/tutum/ares/service/billing_history/billing_history_common"

	common1 "git.tutum.dev/medi/tutum/ares/service/domains/api/common"

	common "git.tutum.dev/medi/tutum/ares/service/domains/billing_edoku/common"

	common2 "git.tutum.dev/medi/tutum/ares/service/domains/edmp/common"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type GetValidationListRequest struct {
	Quarter             *common1.YearQuarter `json:"quarter" validate:"required"`
	BsnrId              *uuid.UUID           `json:"bsnrId" validate:"required"`
	DocumentType        string               `json:"documentType" validate:"required"`
	OpenPreviousQuarter bool                 `json:"openPreviousQuarter"`
}

type GetValidationListResponse struct {
	BillingValidationList []*common.BillingValidationListModel `json:"billingValidationList"`
	TotalPatient          int32                                `json:"totalPatient"`
}

type TroubleshootRequest struct {
}

type TroubleshootResponse struct {
}

type CreateBillingRequest struct {
	DocumentIds           []uuid.UUID          `json:"documentIds" validate:"required"`
	Quarter               *common1.YearQuarter `json:"quarter" validate:"required"`
	Bsnr                  string               `json:"bsnr" validate:"required"`
	DMPValue              common2.DMPValueEnum `json:"dMPValue" validate:"required"`
	IsOpenPreviousQuarter bool                 `json:"isOpenPreviousQuarter"`
}

type CreateBillingResponse struct {
	Status                            bool                                        `json:"status"`
	DMPBillingHistoryId               *uuid.UUID                                  `json:"dMPBillingHistoryId"`
	DMPBillingFieldsValidationResults []*common2.DMPBillingFieldsValidationResult `json:"dMPBillingFieldsValidationResults"`
}

type GetBillingSummaryRequest struct {
}

type GetBillingSummaryResponse struct {
}

type PrepareForShippingRequest struct {
	BillingHistoryId *uuid.UUID `json:"billingHistoryId" validate:"required"`
}

type SendMailRequest struct {
	BillingHistoryId    *uuid.UUID `json:"billingHistoryId" validate:"required"`
	SenderMailSettingId *uuid.UUID `json:"senderMailSettingId" validate:"required"`
	TestMailTo          *string    `json:"testMailTo"`
}

type DownloadBillingFileRequest struct {
}

type DownloadBillingFileResponse struct {
}

type UndoSubmissionRequest struct {
	BillingHistoryId *uuid.UUID `json:"billingHistoryId" validate:"required"`
}

type GetDispatchListRequest struct {
	Query *string `json:"query"`
}

type GetDispatchListResponse struct {
	BillingHistories []*common.BillingHistoryModel `json:"billingHistories"`
}

type GetEdokuBillingSelectionResponse struct {
	Quarters      []*common1.YearQuarter         `json:"quarters"`
	Bsnrs         []*billing_history_common.BSNR `json:"bsnrs"`
	DocumentTypes []string                       `json:"documentTypes"`
}

type CheckForValidationRequest struct {
	DocumentIds []uuid.UUID          `json:"documentIds" validate:"required"`
	Quarter     *common1.YearQuarter `json:"quarter" validate:"required"`
	Bsnr        string               `json:"bsnr" validate:"required"`
}

type CheckForValidationResponse struct {
	Status                            bool                                        `json:"status"`
	DMPBillingFieldsValidationResults []*common2.DMPBillingFieldsValidationResult `json:"dMPBillingFieldsValidationResults"`
}

type GetBillingHistoryRequest struct {
	BillingHistoryId *uuid.UUID `json:"billingHistoryId" validate:"required"`
}

type GetBillingHistoryResponse struct {
	BillingHistory *common.BillingHistoryModel `json:"billingHistory"`
}

type EventBillingEDokuStatusChanged struct {
	BillingHistoryId *uuid.UUID           `json:"billingHistoryId"`
	Status           common.BillingStatus `json:"status"`
}

type GetEdokuDocumentByIdsRequest struct {
	DocumentIds []uuid.UUID `json:"documentIds" validate:"required"`
}

type GetEdokuDocumentByIdsResponse struct {
	Documents []*common2.DocumentationOverview `json:"documents"`
}

// enum definitions

// Define constants
const NATS_SUBJECT = "api.app.mvz" // nats subject this service will listen to

// service event constants
const EVENT_Troubleshoot = "api.app.mvz.BillingEDokuApp.Troubleshoot"
const LEGACY_TOPIC_Troubleshoot = "/api/app/mvz/billing/e/doku/troubleshoot"
const EVENT_GetValidationList = "api.app.mvz.BillingEDokuApp.GetValidationList"
const LEGACY_TOPIC_GetValidationList = "/api/app/mvz/billing/e/doku/getValidationList"
const EVENT_CreateBilling = "api.app.mvz.BillingEDokuApp.CreateBilling"
const LEGACY_TOPIC_CreateBilling = "/api/app/mvz/billing/e/doku/createBilling"
const EVENT_GetBillingSummary = "api.app.mvz.BillingEDokuApp.GetBillingSummary"
const LEGACY_TOPIC_GetBillingSummary = "/api/app/mvz/billing/e/doku/getBillingSummary"
const EVENT_PrepareForShipping = "api.app.mvz.BillingEDokuApp.PrepareForShipping"
const LEGACY_TOPIC_PrepareForShipping = "/api/app/mvz/billing/e/doku/prepareForShipping"
const EVENT_SendMail = "api.app.mvz.BillingEDokuApp.SendMail"
const LEGACY_TOPIC_SendMail = "/api/app/mvz/billing/e/doku/sendMail"
const EVENT_DownloadBillingFile = "api.app.mvz.BillingEDokuApp.DownloadBillingFile"
const LEGACY_TOPIC_DownloadBillingFile = "/api/app/mvz/billing/e/doku/downloadBillingFile"
const EVENT_UndoSubmission = "api.app.mvz.BillingEDokuApp.UndoSubmission"
const LEGACY_TOPIC_UndoSubmission = "/api/app/mvz/billing/e/doku/undoSubmission"
const EVENT_GetDispatchList = "api.app.mvz.BillingEDokuApp.GetDispatchList"
const LEGACY_TOPIC_GetDispatchList = "/api/app/mvz/billing/e/doku/getDispatchList"
const EVENT_GetEdokuBillingSelection = "api.app.mvz.BillingEDokuApp.GetEdokuBillingSelection"
const LEGACY_TOPIC_GetEdokuBillingSelection = "/api/app/mvz/billing/e/doku/getEdokuBillingSelection"
const EVENT_CheckForValidation = "api.app.mvz.BillingEDokuApp.CheckForValidation"
const LEGACY_TOPIC_CheckForValidation = "/api/app/mvz/billing/e/doku/checkForValidation"
const EVENT_GetBillingHistory = "api.app.mvz.BillingEDokuApp.GetBillingHistory"
const LEGACY_TOPIC_GetBillingHistory = "/api/app/mvz/billing/e/doku/getBillingHistory"
const EVENT_GetEdokuDocumentByIds = "api.app.mvz.BillingEDokuApp.GetEdokuDocumentByIds"
const LEGACY_TOPIC_GetEdokuDocumentByIds = "/api/app/mvz/billing/e/doku/getEdokuDocumentByIds"

// message event constants
const EVENT_BillingEDokuStatusChanged = "api.app.mvz.AppMvzBillingEdoku.BillingEDokuStatusChanged"

// Define service interface -------------------------------------------------------------
type BillingEDokuApp interface {
	Troubleshoot(ctx *titan.Context, request *TroubleshootRequest) (*TroubleshootResponse, error)
	GetValidationList(ctx *titan.Context, request *GetValidationListRequest) (*GetValidationListResponse, error)
	CreateBilling(ctx *titan.Context, request *CreateBillingRequest) (*CreateBillingResponse, error)
	GetBillingSummary(ctx *titan.Context, request *GetBillingSummaryRequest) (*GetBillingSummaryResponse, error)
	PrepareForShipping(ctx *titan.Context, request *PrepareForShippingRequest) error
	SendMail(ctx *titan.Context, request *SendMailRequest) error
	DownloadBillingFile(ctx *titan.Context, request *DownloadBillingFileRequest) (*DownloadBillingFileResponse, error)
	UndoSubmission(ctx *titan.Context, request *UndoSubmissionRequest) error
	GetDispatchList(ctx *titan.Context, request *GetDispatchListRequest) (*GetDispatchListResponse, error)
	GetEdokuBillingSelection(ctx *titan.Context) (*GetEdokuBillingSelectionResponse, error)
	CheckForValidation(ctx *titan.Context, request *CheckForValidationRequest) (*CheckForValidationResponse, error)
	GetBillingHistory(ctx *titan.Context, request *GetBillingHistoryRequest) (*GetBillingHistoryResponse, error)
	GetEdokuDocumentByIds(ctx *titan.Context, request *GetEdokuDocumentByIdsRequest) (*GetEdokuDocumentByIdsResponse, error)
}

// Define service proxy -------------------------------------------------------------------
type BillingEDokuAppProxy struct {
	service BillingEDokuApp
}

func (srv *BillingEDokuAppProxy) Troubleshoot(ctx *titan.Context, request *TroubleshootRequest) (*TroubleshootResponse, error) {
	return srv.service.Troubleshoot(ctx, request)
}
func (srv *BillingEDokuAppProxy) GetValidationList(ctx *titan.Context, request *GetValidationListRequest) (*GetValidationListResponse, error) {
	return srv.service.GetValidationList(ctx, request)
}
func (srv *BillingEDokuAppProxy) CreateBilling(ctx *titan.Context, request *CreateBillingRequest) (*CreateBillingResponse, error) {
	return srv.service.CreateBilling(ctx, request)
}
func (srv *BillingEDokuAppProxy) GetBillingSummary(ctx *titan.Context, request *GetBillingSummaryRequest) (*GetBillingSummaryResponse, error) {
	return srv.service.GetBillingSummary(ctx, request)
}
func (srv *BillingEDokuAppProxy) PrepareForShipping(ctx *titan.Context, request *PrepareForShippingRequest) error {
	return srv.service.PrepareForShipping(ctx, request)
}
func (srv *BillingEDokuAppProxy) SendMail(ctx *titan.Context, request *SendMailRequest) error {
	return srv.service.SendMail(ctx, request)
}
func (srv *BillingEDokuAppProxy) DownloadBillingFile(ctx *titan.Context, request *DownloadBillingFileRequest) (*DownloadBillingFileResponse, error) {
	return srv.service.DownloadBillingFile(ctx, request)
}
func (srv *BillingEDokuAppProxy) UndoSubmission(ctx *titan.Context, request *UndoSubmissionRequest) error {
	return srv.service.UndoSubmission(ctx, request)
}
func (srv *BillingEDokuAppProxy) GetDispatchList(ctx *titan.Context, request *GetDispatchListRequest) (*GetDispatchListResponse, error) {
	return srv.service.GetDispatchList(ctx, request)
}
func (srv *BillingEDokuAppProxy) GetEdokuBillingSelection(ctx *titan.Context) (*GetEdokuBillingSelectionResponse, error) {
	return srv.service.GetEdokuBillingSelection(ctx)
}
func (srv *BillingEDokuAppProxy) CheckForValidation(ctx *titan.Context, request *CheckForValidationRequest) (*CheckForValidationResponse, error) {
	return srv.service.CheckForValidation(ctx, request)
}
func (srv *BillingEDokuAppProxy) GetBillingHistory(ctx *titan.Context, request *GetBillingHistoryRequest) (*GetBillingHistoryResponse, error) {
	return srv.service.GetBillingHistory(ctx, request)
}
func (srv *BillingEDokuAppProxy) GetEdokuDocumentByIds(ctx *titan.Context, request *GetEdokuDocumentByIdsRequest) (*GetEdokuDocumentByIdsResponse, error) {
	return srv.service.GetEdokuDocumentByIds(ctx, request)
}

// Define service router -----------------------------------------------------------------
type BillingEDokuAppRouter struct {
	proxy *BillingEDokuAppProxy
}

func (router *BillingEDokuAppRouter) Register(r titan.Router) {
	r.RegisterTopic(EVENT_Troubleshoot, router.proxy.Troubleshoot, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_Troubleshoot, router.proxy.Troubleshoot, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetValidationList, router.proxy.GetValidationList, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetValidationList, router.proxy.GetValidationList, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_CreateBilling, router.proxy.CreateBilling, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_CreateBilling, router.proxy.CreateBilling, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetBillingSummary, router.proxy.GetBillingSummary, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetBillingSummary, router.proxy.GetBillingSummary, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_PrepareForShipping, router.proxy.PrepareForShipping, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_PrepareForShipping, router.proxy.PrepareForShipping, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_SendMail, router.proxy.SendMail, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_SendMail, router.proxy.SendMail, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_DownloadBillingFile, router.proxy.DownloadBillingFile, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_DownloadBillingFile, router.proxy.DownloadBillingFile, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_UndoSubmission, router.proxy.UndoSubmission, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_UndoSubmission, router.proxy.UndoSubmission, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetDispatchList, router.proxy.GetDispatchList, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetDispatchList, router.proxy.GetDispatchList, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetEdokuBillingSelection, router.proxy.GetEdokuBillingSelection, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetEdokuBillingSelection, router.proxy.GetEdokuBillingSelection, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_CheckForValidation, router.proxy.CheckForValidation, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_CheckForValidation, router.proxy.CheckForValidation, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetBillingHistory, router.proxy.GetBillingHistory, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetBillingHistory, router.proxy.GetBillingHistory, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetEdokuDocumentByIds, router.proxy.GetEdokuDocumentByIds, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetEdokuDocumentByIds, router.proxy.GetEdokuDocumentByIds, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
}

// Subscriber
func (router *BillingEDokuAppRouter) Subscribe(s *titan.MessageSubscriber) {
}

func NewBillingEDokuAppRouter(s BillingEDokuApp) *BillingEDokuAppRouter {
	p := &BillingEDokuAppProxy{s}
	return &BillingEDokuAppRouter{p}
}

func NewServer(bff0 BillingEDokuApp, options ...titan.Option) *titan.Server {
	opt := options

	router0 := NewBillingEDokuAppRouter(bff0)
	opt = append(opt, titan.Routes(router0.Register), titan.Subscribe(router0.Subscribe))

	return titan.NewServer(NATS_SUBJECT, opt...)
}

// Define client ----------------------------------------------------------------------------------------------
type BillingEDokuAppClient struct {
	client *titan.Client
}

func (srv *BillingEDokuAppClient) Troubleshoot(ctx *titan.Context, request *TroubleshootRequest) (*TroubleshootResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_Troubleshoot).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &TroubleshootResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *BillingEDokuAppClient) GetValidationList(ctx *titan.Context, request *GetValidationListRequest) (*GetValidationListResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetValidationList).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetValidationListResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *BillingEDokuAppClient) CreateBilling(ctx *titan.Context, request *CreateBillingRequest) (*CreateBillingResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CreateBilling).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &CreateBillingResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *BillingEDokuAppClient) GetBillingSummary(ctx *titan.Context, request *GetBillingSummaryRequest) (*GetBillingSummaryResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetBillingSummary).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetBillingSummaryResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *BillingEDokuAppClient) PrepareForShipping(ctx *titan.Context, request *PrepareForShippingRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_PrepareForShipping).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *BillingEDokuAppClient) SendMail(ctx *titan.Context, request *SendMailRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_SendMail).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *BillingEDokuAppClient) DownloadBillingFile(ctx *titan.Context, request *DownloadBillingFileRequest) (*DownloadBillingFileResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_DownloadBillingFile).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &DownloadBillingFileResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *BillingEDokuAppClient) UndoSubmission(ctx *titan.Context, request *UndoSubmissionRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UndoSubmission).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *BillingEDokuAppClient) GetDispatchList(ctx *titan.Context, request *GetDispatchListRequest) (*GetDispatchListResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetDispatchList).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetDispatchListResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *BillingEDokuAppClient) GetEdokuBillingSelection(ctx *titan.Context) (*GetEdokuBillingSelectionResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetEdokuBillingSelection).
		Subject(NATS_SUBJECT).
		Build()
	var resp = &GetEdokuBillingSelectionResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *BillingEDokuAppClient) CheckForValidation(ctx *titan.Context, request *CheckForValidationRequest) (*CheckForValidationResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CheckForValidation).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &CheckForValidationResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *BillingEDokuAppClient) GetBillingHistory(ctx *titan.Context, request *GetBillingHistoryRequest) (*GetBillingHistoryResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetBillingHistory).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetBillingHistoryResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *BillingEDokuAppClient) GetEdokuDocumentByIds(ctx *titan.Context, request *GetEdokuDocumentByIdsRequest) (*GetEdokuDocumentByIdsResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetEdokuDocumentByIds).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetEdokuDocumentByIdsResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func NewBillingEDokuAppClient(clients ...*titan.Client) *BillingEDokuAppClient {
	var client *titan.Client
	if len(clients) <= 0 {
		client = titan.GetDefaultClient()
	} else {
		client = clients[0]
	}
	return &BillingEDokuAppClient{client: client}
}

// Define event Notifier -----------------------------------------------------------------------------------------
type BillingEdokuNotifier struct {
	client *titan.Client
}

func NewBillingEdokuNotifier() *BillingEdokuNotifier {
	client := titan.GetDefaultClient()
	return &BillingEdokuNotifier{client}
}
func (p *BillingEdokuNotifier) NotifyBillingEDokuStatusChanged(ctx *titan.Context, event *EventBillingEDokuStatusChanged) error {
	return p.client.Publish(ctx, EVENT_BillingEDokuStatusChanged, event)
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type BillingEdokuSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewBillingEdokuSocketNotifier(socket *socket_api.SocketServiceClient) *BillingEdokuSocketNotifier {
	return &BillingEdokuSocketNotifier{socket}
}
func (n *BillingEdokuSocketNotifier) NotifyCareProviderBillingEDokuStatusChanged(ctx *titan.Context, event *EventBillingEDokuStatusChanged) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_BillingEDokuStatusChanged,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *BillingEdokuSocketNotifier) NotifyUserBillingEDokuStatusChanged(ctx *titan.Context, event *EventBillingEDokuStatusChanged) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_BillingEDokuStatusChanged,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *BillingEdokuSocketNotifier) NotifyDeviceBillingEDokuStatusChanged(ctx *titan.Context, event *EventBillingEDokuStatusChanged) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_BillingEDokuStatusChanged,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *BillingEdokuSocketNotifier) NotifyClientBillingEDokuStatusChanged(ctx *titan.Context, event *EventBillingEDokuStatusChanged) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_BillingEDokuStatusChanged,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}

// -----------------------------------------------
// Test event listener
type BillingEdokuEventListener struct {
	mux                                    sync.Mutex
	LastEventBillingEDokuStatusChangedList []EventBillingEDokuStatusChanged
}

func (listener *BillingEdokuEventListener) Reset() {
	listener.mux.Lock()
	listener.LastEventBillingEDokuStatusChangedList = []EventBillingEDokuStatusChanged{}
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *BillingEdokuEventListener) Subscribe(s *titan.MessageSubscriber) {
	s.Register(EVENT_BillingEDokuStatusChanged, "api.app.mvz_AppMvzBillingEdoku_BillingEDokuStatusChanged_Queue_test", func(p *titan.Message) error {
		var resp EventBillingEDokuStatusChanged
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventBillingEDokuStatusChangedList) >= 100 {
			listener.LastEventBillingEDokuStatusChangedList = listener.LastEventBillingEDokuStatusChangedList[1:]
		}
		listener.LastEventBillingEDokuStatusChangedList = append(listener.LastEventBillingEDokuStatusChangedList, resp)
		listener.mux.Unlock()
		return nil
	})
}
func (listener *BillingEdokuEventListener) GetLastEventBillingEDokuStatusChangedList(timeOutInMilliSeconds time.Duration) []EventBillingEDokuStatusChanged {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventBillingEDokuStatusChangedList
		default:
			// if any value
			if len(listener.LastEventBillingEDokuStatusChangedList) > 0 {
				return listener.LastEventBillingEDokuStatusChangedList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
