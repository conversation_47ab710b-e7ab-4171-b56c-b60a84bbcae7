// This code was autogenerated from app/mvz/schein.proto, do not edit.

package schein

import (
	"encoding/json"
	"errors"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	catalog_sdkt_common "git.tutum.dev/medi/tutum/ares/service/domains/api/catalog_sdkt_common"

	common "git.tutum.dev/medi/tutum/ares/service/domains/api/common"

	patient_profile_common "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"

	private_schein_common "git.tutum.dev/medi/tutum/ares/service/domains/api/private_schein_common"

	schein_common "git.tutum.dev/medi/tutum/ares/service/domains/api/schein_common"

	common1 "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type ScheinChangedResponse struct {
	PatientId uuid.UUID `json:"patientId"`
	ScheinId  uuid.UUID `json:"scheinId"`
}

type EventScheinChanged struct {
	Data      ScheinChangedResponse `json:"data"`
	EventName EventName             `json:"eventName"`
}

type EventScheinChangedResponse struct {
	Data EventScheinChanged `json:"data"`
}

type EventCreateRemoveSchein struct {
	Data ScheinChangedResponse `json:"data"`
}

type IsValidRequest struct {
	CreateScheinRequest CreateScheinRequest                    `json:"createScheinRequest"`
	Insurances          []patient_profile_common.InsuranceInfo `json:"insurances"`
}

type IsValidResponse struct {
	Error *map[string]common.FieldError `json:"error"`
}

type GetKTABsRequest struct {
	VKNR         string                                         `json:"vKNR" validate:"required"`
	SpecialGroup patient_profile_common.SpecialGroupDescription `json:"specialGroup"`
	PatientId    uuid.UUID                                      `json:"patientId" validate:"required"`
	Quarter      int32                                          `json:"quarter" validate:"required"`
	Year         int32                                          `json:"year" validate:"required"`
	Bsnr         string                                         `json:"bsnr" validate:"required"`
}

type GetKTABsResponse struct {
	KTABValue []catalog_sdkt_common.KTABValue `json:"kTABValue"`
}

type MarkBillRequest struct {
	ContractId       *string                 `json:"contractId"`
	MainGroup        schein_common.MainGroup `json:"mainGroup"`
	ExcludeScheinIds []uuid.UUID             `json:"excludeScheinIds"`
	ValidScheinsIds  []uuid.UUID             `json:"validScheinsIds"`
}

type TakeOverDiagnoseInfo struct {
	Id                  uuid.UUID `json:"id"`
	IsTreatmentRelevant bool      `json:"isTreatmentRelevant"`
}

type CreateScheinRequest struct {
	ScheinId              *uuid.UUID                       `json:"scheinId"`
	PatientId             uuid.UUID                        `json:"patientId"`
	DoctorId              *uuid.UUID                       `json:"doctorId"`
	ScheinMainGroup       schein_common.MainGroup          `json:"scheinMainGroup"`
	KvTreatmentCase       schein_common.TreatmentCaseNames `json:"kvTreatmentCase"`
	KvScheinSubGroup      *string                          `json:"kvScheinSubGroup"`
	G4101Year             int32                            `json:"g4101Year" validate:"required"`
	G4101Quarter          int32                            `json:"g4101Quarter" validate:"required"`
	TariffType            *string                          `json:"tariffType"`
	BgType                *string                          `json:"bgType"`
	BgAccidentDate        *int64                           `json:"bgAccidentDate"`
	BgAccidentTime        *string                          `json:"bgAccidentTime"`
	BgWorkingTimeFrom     *string                          `json:"bgWorkingTimeFrom"`
	BgWorkingTimeTo       *string                          `json:"bgWorkingTimeTo"`
	BgEmployerName        *string                          `json:"bgEmployerName"`
	BgEmployerStreet      *string                          `json:"bgEmployerStreet"`
	BgEmployerHousenumber *string                          `json:"bgEmployerHousenumber"`
	BgEmployerPostcode    *string                          `json:"bgEmployerPostcode"`
	BgEmployerCity        *string                          `json:"bgEmployerCity"`
	BgEmployerCountry     *string                          `json:"bgEmployerCountry"`
	HzvContractId         *string                          `json:"hzvContractId"`
	ScheinDetails         *schein_common.ScheinDetail      `json:"scheinDetails"`
	InsuranceId           uuid.UUID                        `json:"insuranceId"`
	ExcludeFromBilling    bool                             `json:"excludeFromBilling"`
	TakeOverDiagnoseInfos []TakeOverDiagnoseInfo           `json:"takeOverDiagnoseInfos"`
	SkipFields            *bool                            `json:"skipFields"`
	G4122                 *string                          `json:"g4122"`
	G4101                 string                           `json:"g4101"`
	NewTakeOverDiagnosis  []common1.TimelineModel          `json:"newTakeOverDiagnosis"`
	AssignedToBsnrId      *uuid.UUID                       `json:"assignedToBsnrId"`
}

type CreateScheinResponse struct {
	ScheinItem  *schein_common.ScheinItem    `json:"scheinItem"`
	FieldErrors map[string]common.FieldError `json:"fieldErrors"`
}

type GetSubGroupFromMasterDataRequest struct {
	Bsnr string `json:"bsnr"`
}

type GetSubGroupFromMasterDataResponse struct {
	Keys []string `json:"keys"`
}

type GetBillingAreaFromMasterDataRequest struct {
	Bsnr     string `json:"bsnr"`
	Subgroup string `json:"subgroup"`
}

type GetBillingAreaFromMasterDataResponse struct {
	Keys []string `json:"keys"`
}

type Rezidiv struct {
	Code    string `json:"code"`
	Content string `json:"content"`
}

type GetRezidivListResponse struct {
	Data []Rezidiv `json:"data"`
}

type GetPsychotherapyByIdRequest struct {
	PsychotherapyId uuid.UUID `json:"psychotherapyId"`
}

type GetPsychotherapyByIdResponse struct {
	Data schein_common.Psychotherapy `json:"data"`
}

type GetScheinByInsuranceIdRequest struct {
	InsuranceId uuid.UUID `json:"insuranceId" validate:"required"`
}

type GetScheinByInsuranceIdResponse struct {
	ScheinItem schein_common.ScheinItem `json:"scheinItem"`
}

type GetScheinsByInsuranceIdResponse struct {
	ScheinItems []schein_common.ScheinItem `json:"scheinItems"`
}

type RevertTechnicalScheinRequest struct {
	ScheinId uuid.UUID `json:"scheinId"`
}

type CreatePrivateScheinRequest struct {
	Schein                private_schein_common.PrivateScheinItem `json:"schein"`
	TakeOverDiagnoseInfos []TakeOverDiagnoseInfo                  `json:"takeOverDiagnoseInfos"`
	NewTakeOverDiagnosis  []common1.TimelineModel                 `json:"newTakeOverDiagnosis"`
}

type UpdatePrivateScheinRequest struct {
	Schein private_schein_common.PrivateScheinItem `json:"schein"`
}

type DeletePrivateScheinRequest struct {
	ScheinId  uuid.UUID `json:"scheinId"`
	PatientId uuid.UUID `json:"patientId"`
}

type IsValidPrivateScheinRequest struct {
	Schein private_schein_common.PrivateScheinItem `json:"schein"`
}

type GetPrivateScheinByIdRequest struct {
	ScheinId uuid.UUID `json:"scheinId"`
}

type GetGoaFactorValueRequest struct {
	ScheinId  uuid.UUID `json:"scheinId" validate:"required"`
	GoaNumber string    `json:"goaNumber" validate:"required"`
}

type GetGoaFactorValueResponse struct {
	Value float64 `json:"value"`
}

type TakeOverScheinDiagnisToRequest struct {
	ScheinId              uuid.UUID               `json:"scheinId"`
	TakeOverDiagnoseInfos []TakeOverDiagnoseInfo  `json:"takeOverDiagnoseInfos"`
	NewTakeOverDiagnosis  []common1.TimelineModel `json:"newTakeOverDiagnosis"`
}

type TakeOverDiagnosisByScheinIdRequest struct {
	ScheinId              uuid.UUID               `json:"scheinId" validate:"required"`
	TakeOverDiagnoseInfos []TakeOverDiagnoseInfo  `json:"takeOverDiagnoseInfos" validate:"required"`
	NewTakeOverDiagnosis  []common1.TimelineModel `json:"newTakeOverDiagnosis"`
}

type GetScheinItemByIdRequest struct {
	ScheinId uuid.UUID `json:"scheinId" validate:"required"`
}

type GetScheinItemByIdResponse struct {
	ScheinItems schein_common.ScheinItem `json:"scheinItems"`
}

type GetScheinItemByIdsRequest struct {
	ScheinIds []uuid.UUID `json:"scheinIds" validate:"required"`
}

type GetScheinItemByIdsResponse struct {
	ScheinItems []schein_common.ScheinItem `json:"scheinItems"`
}

type CreateBgScheinRequest struct {
	Schein                schein_common.BgScheinItem `json:"schein"`
	TakeOverDiagnoseInfos []TakeOverDiagnoseInfo     `json:"takeOverDiagnoseInfos"`
	NewTakeOverDiagnosis  []common1.TimelineModel    `json:"newTakeOverDiagnosis"`
}

type UpdateBgScheinRequest struct {
	Schein schein_common.BgScheinItem `json:"schein"`
}

type DeleteBgScheinRequest struct {
	ScheinId  uuid.UUID `json:"scheinId"`
	PatientId uuid.UUID `json:"patientId"`
}

type IsValidBgScheinRequest struct {
	Schein     schein_common.BgScheinItem           `json:"schein"`
	Insurances patient_profile_common.InsuranceInfo `json:"insurances"`
}

type GetBgScheinByIdRequest struct {
	ScheinId uuid.UUID `json:"scheinId"`
}

type CheckDummyVknrRequest struct {
	ScheinId uuid.UUID `json:"scheinId" validate:"required"`
}

type CheckDummyVknrResponse struct {
	IsDummy bool `json:"isDummy"`
}

type GetTotalScheinsRequest struct {
	PatientId uuid.UUID `json:"patientId" validate:"required"`
}

type GetTotalScheinsResponse struct {
	TotalScheins int32 `json:"totalScheins"`
}

// enum definitions
type EventName string

const (
	EventName_UpdateSchein EventName = "EventName_UpdateSchein"
)

// Define constants
const NATS_SUBJECT = "api.app.mvz" // nats subject this service will listen to

// service event constants
const EVENT_CreateSvScheins = "api.app.mvz.ScheinApp.CreateSvScheins"
const LEGACY_TOPIC_CreateSvScheins = "/api/app/mvz/schein/createSvScheins"
const EVENT_IsValid = "api.app.mvz.ScheinApp.IsValid"
const LEGACY_TOPIC_IsValid = "/api/app/mvz/schein/isValid"
const EVENT_CreateSchein = "api.app.mvz.ScheinApp.CreateSchein"
const LEGACY_TOPIC_CreateSchein = "/api/app/mvz/schein/createSchein"
const EVENT_TakeOverScheinDiagnosis = "api.app.mvz.ScheinApp.TakeOverScheinDiagnosis"
const LEGACY_TOPIC_TakeOverScheinDiagnosis = "/api/app/mvz/schein/takeOverScheinDiagnosis"
const EVENT_GetScheinDetailById = "api.app.mvz.ScheinApp.GetScheinDetailById"
const LEGACY_TOPIC_GetScheinDetailById = "/api/app/mvz/schein/getScheinDetailById"
const EVENT_GetScheinDetailByIds = "api.app.mvz.ScheinApp.GetScheinDetailByIds"
const LEGACY_TOPIC_GetScheinDetailByIds = "/api/app/mvz/schein/getScheinDetailByIds"
const EVENT_UpdateSchein = "api.app.mvz.ScheinApp.UpdateSchein"
const LEGACY_TOPIC_UpdateSchein = "/api/app/mvz/schein/updateSchein"
const EVENT_MarkNotBilled = "api.app.mvz.ScheinApp.MarkNotBilled"
const LEGACY_TOPIC_MarkNotBilled = "/api/app/mvz/schein/markNotBilled"
const EVENT_CheckExistKVScheinCurrentQuarter = "api.app.mvz.ScheinApp.CheckExistKVScheinCurrentQuarter"
const LEGACY_TOPIC_CheckExistKVScheinCurrentQuarter = "/api/app/mvz/schein/checkExistKVScheinCurrentQuarter"
const EVENT_DeleteSchein = "api.app.mvz.ScheinApp.DeleteSchein"
const LEGACY_TOPIC_DeleteSchein = "/api/app/mvz/schein/deleteSchein"
const EVENT_GetScheinDetail = "api.app.mvz.ScheinApp.GetScheinDetail"
const LEGACY_TOPIC_GetScheinDetail = "/api/app/mvz/schein/getScheinDetail"
const EVENT_GetFields = "api.app.mvz.ScheinApp.GetFields"
const LEGACY_TOPIC_GetFields = "/api/app/mvz/schein/getFields"
const EVENT_GetScheinsOverview = "api.app.mvz.ScheinApp.GetScheinsOverview"
const LEGACY_TOPIC_GetScheinsOverview = "/api/app/mvz/schein/getScheinsOverview"
const EVENT_HandleEventScheinChanged = "api.app.mvz.ScheinApp.HandleEventScheinChanged"
const LEGACY_TOPIC_HandleEventScheinChanged = "/api/app/mvz/schein/handleEventScheinChanged"
const EVENT_HandleEventCreateRemoveSchein = "api.app.mvz.ScheinApp.HandleEventCreateRemoveSchein"
const LEGACY_TOPIC_HandleEventCreateRemoveSchein = "/api/app/mvz/schein/handleEventCreateRemoveSchein"
const EVENT_GetSetting = "api.app.mvz.ScheinApp.GetSetting"
const LEGACY_TOPIC_GetSetting = "/api/app/mvz/schein/getSetting"
const EVENT_SaveSetting = "api.app.mvz.ScheinApp.SaveSetting"
const LEGACY_TOPIC_SaveSetting = "/api/app/mvz/schein/saveSetting"
const EVENT_GetSelectedTreatmentCaseSubgroup = "api.app.mvz.ScheinApp.GetSelectedTreatmentCaseSubgroup"
const LEGACY_TOPIC_GetSelectedTreatmentCaseSubgroup = "/api/app/mvz/schein/getSelectedTreatmentCaseSubgroup"
const EVENT_GetOrderList = "api.app.mvz.ScheinApp.GetOrderList"
const LEGACY_TOPIC_GetOrderList = "/api/app/mvz/schein/getOrderList"
const EVENT_SaveOrderList = "api.app.mvz.ScheinApp.SaveOrderList"
const LEGACY_TOPIC_SaveOrderList = "/api/app/mvz/schein/saveOrderList"
const EVENT_MarkBill = "api.app.mvz.ScheinApp.MarkBill"
const LEGACY_TOPIC_MarkBill = "/api/app/mvz/schein/markBill"
const EVENT_GetKTABs = "api.app.mvz.ScheinApp.GetKTABs"
const LEGACY_TOPIC_GetKTABs = "/api/app/mvz/schein/getKTABs"
const EVENT_GetSubGroupFromMasterData = "api.app.mvz.ScheinApp.GetSubGroupFromMasterData"
const LEGACY_TOPIC_GetSubGroupFromMasterData = "/api/app/mvz/schein/getSubGroupFromMasterData"
const EVENT_GetBillingAreaFromMasterData = "api.app.mvz.ScheinApp.GetBillingAreaFromMasterData"
const LEGACY_TOPIC_GetBillingAreaFromMasterData = "/api/app/mvz/schein/getBillingAreaFromMasterData"
const EVENT_GetRezidivList = "api.app.mvz.ScheinApp.GetRezidivList"
const LEGACY_TOPIC_GetRezidivList = "/api/app/mvz/schein/getRezidivList"
const EVENT_GetPsychotherapyById = "api.app.mvz.ScheinApp.GetPsychotherapyById"
const LEGACY_TOPIC_GetPsychotherapyById = "/api/app/mvz/schein/getPsychotherapyById"
const EVENT_GetScheinByInsuranceId = "api.app.mvz.ScheinApp.GetScheinByInsuranceId"
const LEGACY_TOPIC_GetScheinByInsuranceId = "/api/app/mvz/schein/getScheinByInsuranceId"
const EVENT_GetScheinsByInsuranceId = "api.app.mvz.ScheinApp.GetScheinsByInsuranceId"
const LEGACY_TOPIC_GetScheinsByInsuranceId = "/api/app/mvz/schein/getScheinsByInsuranceId"
const EVENT_RevertTechnicalSchein = "api.app.mvz.ScheinApp.RevertTechnicalSchein"
const LEGACY_TOPIC_RevertTechnicalSchein = "/api/app/mvz/schein/revertTechnicalSchein"
const EVENT_CreatePrivateSchein = "api.app.mvz.ScheinApp.CreatePrivateSchein"
const LEGACY_TOPIC_CreatePrivateSchein = "/api/app/mvz/schein/createPrivateSchein"
const EVENT_UpdatePrivateSchein = "api.app.mvz.ScheinApp.UpdatePrivateSchein"
const LEGACY_TOPIC_UpdatePrivateSchein = "/api/app/mvz/schein/updatePrivateSchein"
const EVENT_DeletePrivateSchein = "api.app.mvz.ScheinApp.DeletePrivateSchein"
const LEGACY_TOPIC_DeletePrivateSchein = "/api/app/mvz/schein/deletePrivateSchein"
const EVENT_IsValidPrivateSchein = "api.app.mvz.ScheinApp.IsValidPrivateSchein"
const LEGACY_TOPIC_IsValidPrivateSchein = "/api/app/mvz/schein/isValidPrivateSchein"
const EVENT_GetPrivateScheinById = "api.app.mvz.ScheinApp.GetPrivateScheinById"
const LEGACY_TOPIC_GetPrivateScheinById = "/api/app/mvz/schein/getPrivateScheinById"
const EVENT_GetGoaFactorValue = "api.app.mvz.ScheinApp.GetGoaFactorValue"
const LEGACY_TOPIC_GetGoaFactorValue = "/api/app/mvz/schein/getGoaFactorValue"
const EVENT_TakeOverDiagnosisByScheinId = "api.app.mvz.ScheinApp.TakeOverDiagnosisByScheinId"
const LEGACY_TOPIC_TakeOverDiagnosisByScheinId = "/api/app/mvz/schein/takeOverDiagnosisByScheinId"
const EVENT_CreateSvScheinAutomaticly = "api.app.mvz.ScheinApp.CreateSvScheinAutomaticly"
const LEGACY_TOPIC_CreateSvScheinAutomaticly = "/api/app/mvz/schein/createSvScheinAutomaticly"
const EVENT_CreateSvScheinFromReference = "api.app.mvz.ScheinApp.CreateSvScheinFromReference"
const LEGACY_TOPIC_CreateSvScheinFromReference = "/api/app/mvz/schein/createSvScheinFromReference"
const EVENT_CreateSvScheinManually = "api.app.mvz.ScheinApp.CreateSvScheinManually"
const LEGACY_TOPIC_CreateSvScheinManually = "/api/app/mvz/schein/createSvScheinManually"
const EVENT_UpdateSvSchein = "api.app.mvz.ScheinApp.UpdateSvSchein"
const LEGACY_TOPIC_UpdateSvSchein = "/api/app/mvz/schein/updateSvSchein"
const EVENT_MarkAsReferral = "api.app.mvz.ScheinApp.MarkAsReferral"
const LEGACY_TOPIC_MarkAsReferral = "/api/app/mvz/schein/markAsReferral"
const EVENT_RemoveReferral = "api.app.mvz.ScheinApp.RemoveReferral"
const LEGACY_TOPIC_RemoveReferral = "/api/app/mvz/schein/removeReferral"
const EVENT_GetScheinItemById = "api.app.mvz.ScheinApp.GetScheinItemById"
const LEGACY_TOPIC_GetScheinItemById = "/api/app/mvz/schein/getScheinItemById"
const EVENT_GetScheinItemByIds = "api.app.mvz.ScheinApp.GetScheinItemByIds"
const LEGACY_TOPIC_GetScheinItemByIds = "/api/app/mvz/schein/getScheinItemByIds"
const EVENT_CreateBgSchein = "api.app.mvz.ScheinApp.CreateBgSchein"
const LEGACY_TOPIC_CreateBgSchein = "/api/app/mvz/schein/createBgSchein"
const EVENT_UpdateBgSchein = "api.app.mvz.ScheinApp.UpdateBgSchein"
const LEGACY_TOPIC_UpdateBgSchein = "/api/app/mvz/schein/updateBgSchein"
const EVENT_DeleteBgSchein = "api.app.mvz.ScheinApp.DeleteBgSchein"
const LEGACY_TOPIC_DeleteBgSchein = "/api/app/mvz/schein/deleteBgSchein"
const EVENT_GetBgScheinById = "api.app.mvz.ScheinApp.GetBgScheinById"
const LEGACY_TOPIC_GetBgScheinById = "/api/app/mvz/schein/getBgScheinById"
const EVENT_IsValidBgSchein = "api.app.mvz.ScheinApp.IsValidBgSchein"
const LEGACY_TOPIC_IsValidBgSchein = "/api/app/mvz/schein/isValidBgSchein"
const EVENT_CheckDummyVknr = "api.app.mvz.ScheinApp.CheckDummyVknr"
const LEGACY_TOPIC_CheckDummyVknr = "/api/app/mvz/schein/checkDummyVknr"
const EVENT_GetTotalScheins = "api.app.mvz.ScheinApp.GetTotalScheins"
const LEGACY_TOPIC_GetTotalScheins = "/api/app/mvz/schein/getTotalScheins"

// message event constants
const EVENT_ScheinChanged = "api.app.mvz.AppMvzSchein.ScheinChanged"
const EVENT_ScheinChangedResponse = "api.app.mvz.AppMvzSchein.ScheinChangedResponse"
const EVENT_CreateRemoveSchein = "api.app.mvz.AppMvzSchein.CreateRemoveSchein"

// Define service interface -------------------------------------------------------------
type ScheinApp interface {
	CreateSvScheins(ctx *titan.Context, request schein_common.CreateSvScheinRequest) error
	IsValid(ctx *titan.Context, request IsValidRequest) (*IsValidResponse, error)
	CreateSchein(ctx *titan.Context, request CreateScheinRequest) (*CreateScheinResponse, error)
	TakeOverScheinDiagnosis(ctx *titan.Context, request TakeOverScheinDiagnisToRequest) error
	GetScheinDetailById(ctx *titan.Context, request schein_common.GetScheinDetailByIdRequest) (*schein_common.GetScheinDetailByIdResponse, error)
	GetScheinDetailByIds(ctx *titan.Context, request schein_common.GetScheinDetailByIdsRequest) (*schein_common.GetScheinDetailByIdsResponse, error)
	UpdateSchein(ctx *titan.Context, request schein_common.UpdateScheinRequest) error
	MarkNotBilled(ctx *titan.Context, request schein_common.MarkNotBilledRequest) error
	CheckExistKVScheinCurrentQuarter(ctx *titan.Context, request schein_common.CheckExistKVScheinCurrentQuarterRequest) (*schein_common.CheckExistKVScheinCurrentQuarterResponse, error)
	DeleteSchein(ctx *titan.Context, request schein_common.DeleteScheinRequest) error
	GetScheinDetail(ctx *titan.Context, request schein_common.GetScheinDetailRequest) (*schein_common.GetScheinDetailResponse, error)
	GetFields(ctx *titan.Context, request schein_common.GetFieldsRequest) (*schein_common.GetFieldsResponse, error)
	GetScheinsOverview(ctx *titan.Context, request schein_common.GetScheinsOverviewRequest) (*schein_common.GetScheinsOverviewResponse, error)
	HandleEventScheinChanged(ctx *titan.Context, request EventScheinChanged) error
	HandleEventCreateRemoveSchein(ctx *titan.Context, request EventCreateRemoveSchein) error
	GetSetting(ctx *titan.Context) (*schein_common.GetSettingResponse, error)
	SaveSetting(ctx *titan.Context, request schein_common.SaveSettingRequest) error
	GetSelectedTreatmentCaseSubgroup(ctx *titan.Context, request schein_common.GetSelectedTreatmentCaseSubgroupRequest) (*schein_common.GetSelectedTreatmentCaseSubgroupResponse, error)
	GetOrderList(ctx *titan.Context) (*schein_common.GetOrderListResponse, error)
	SaveOrderList(ctx *titan.Context, request schein_common.SaveOrderListRequest) error
	MarkBill(ctx *titan.Context, request MarkBillRequest) error
	GetKTABs(ctx *titan.Context, request GetKTABsRequest) (*GetKTABsResponse, error)
	GetSubGroupFromMasterData(ctx *titan.Context, request GetSubGroupFromMasterDataRequest) (*GetSubGroupFromMasterDataResponse, error)
	GetBillingAreaFromMasterData(ctx *titan.Context, request GetBillingAreaFromMasterDataRequest) (*GetBillingAreaFromMasterDataResponse, error)
	GetRezidivList(ctx *titan.Context) (*GetRezidivListResponse, error)
	GetPsychotherapyById(ctx *titan.Context, request GetPsychotherapyByIdRequest) (*GetPsychotherapyByIdResponse, error)
	GetScheinByInsuranceId(ctx *titan.Context, request GetScheinByInsuranceIdRequest) (*GetScheinByInsuranceIdResponse, error)
	GetScheinsByInsuranceId(ctx *titan.Context, request GetScheinByInsuranceIdRequest) (*GetScheinsByInsuranceIdResponse, error)
	RevertTechnicalSchein(ctx *titan.Context, request RevertTechnicalScheinRequest) error
	CreatePrivateSchein(ctx *titan.Context, request CreatePrivateScheinRequest) (*private_schein_common.PrivateScheinItem, error)
	UpdatePrivateSchein(ctx *titan.Context, request UpdatePrivateScheinRequest) (*private_schein_common.PrivateScheinItem, error)
	DeletePrivateSchein(ctx *titan.Context, request DeletePrivateScheinRequest) error
	IsValidPrivateSchein(ctx *titan.Context, request IsValidPrivateScheinRequest) (*IsValidResponse, error)
	GetPrivateScheinById(ctx *titan.Context, request GetPrivateScheinByIdRequest) (*private_schein_common.PrivateScheinItem, error)
	GetGoaFactorValue(ctx *titan.Context, request GetGoaFactorValueRequest) (*GetGoaFactorValueResponse, error)
	TakeOverDiagnosisByScheinId(ctx *titan.Context, request TakeOverDiagnosisByScheinIdRequest) error
	CreateSvScheinAutomaticly(ctx *titan.Context, request schein_common.CreateSvScheinAutomaticlyRequest) (*schein_common.CreateSvScheinAutomaticlyResponse, error)
	CreateSvScheinFromReference(ctx *titan.Context, request schein_common.CreateSvScheinFromReferenceRequest) (*schein_common.CreateSvScheinFromReferenceResponse, error)
	CreateSvScheinManually(ctx *titan.Context, request schein_common.CreateSvScheinManuallyRequest) (*schein_common.CreateSvScheinManuallyResponse, error)
	UpdateSvSchein(ctx *titan.Context, request schein_common.UpdateSvScheinRequest) (*schein_common.UpdateSvScheinResponse, error)
	MarkAsReferral(ctx *titan.Context, request schein_common.MarkAsReferralRequest) error
	RemoveReferral(ctx *titan.Context, request schein_common.RemoveReferralRequest) error
	GetScheinItemById(ctx *titan.Context, request GetScheinItemByIdRequest) (*GetScheinItemByIdResponse, error)
	GetScheinItemByIds(ctx *titan.Context, request GetScheinItemByIdsRequest) (*GetScheinItemByIdsResponse, error)
	CreateBgSchein(ctx *titan.Context, request CreateBgScheinRequest) (*schein_common.BgScheinItem, error)
	UpdateBgSchein(ctx *titan.Context, request UpdateBgScheinRequest) (*schein_common.BgScheinItem, error)
	DeleteBgSchein(ctx *titan.Context, request DeleteBgScheinRequest) error
	GetBgScheinById(ctx *titan.Context, request GetBgScheinByIdRequest) (*schein_common.BgScheinItem, error)
	IsValidBgSchein(ctx *titan.Context, request IsValidBgScheinRequest) (*IsValidResponse, error)
	CheckDummyVknr(ctx *titan.Context, request CheckDummyVknrRequest) (*CheckDummyVknrResponse, error)
	GetTotalScheins(ctx *titan.Context, request GetTotalScheinsRequest) (*GetTotalScheinsResponse, error)
}

// Define service proxy -------------------------------------------------------------------
type ScheinAppProxy struct {
	service ScheinApp
}

func (srv *ScheinAppProxy) CreateSvScheins(ctx *titan.Context, request schein_common.CreateSvScheinRequest) error {
	return srv.service.CreateSvScheins(ctx, request)
}
func (srv *ScheinAppProxy) IsValid(ctx *titan.Context, request IsValidRequest) (*IsValidResponse, error) {
	return srv.service.IsValid(ctx, request)
}
func (srv *ScheinAppProxy) CreateSchein(ctx *titan.Context, request CreateScheinRequest) (*CreateScheinResponse, error) {
	return srv.service.CreateSchein(ctx, request)
}
func (srv *ScheinAppProxy) TakeOverScheinDiagnosis(ctx *titan.Context, request TakeOverScheinDiagnisToRequest) error {
	return srv.service.TakeOverScheinDiagnosis(ctx, request)
}
func (srv *ScheinAppProxy) GetScheinDetailById(ctx *titan.Context, request schein_common.GetScheinDetailByIdRequest) (*schein_common.GetScheinDetailByIdResponse, error) {
	return srv.service.GetScheinDetailById(ctx, request)
}
func (srv *ScheinAppProxy) GetScheinDetailByIds(ctx *titan.Context, request schein_common.GetScheinDetailByIdsRequest) (*schein_common.GetScheinDetailByIdsResponse, error) {
	return srv.service.GetScheinDetailByIds(ctx, request)
}
func (srv *ScheinAppProxy) UpdateSchein(ctx *titan.Context, request schein_common.UpdateScheinRequest) error {
	return srv.service.UpdateSchein(ctx, request)
}
func (srv *ScheinAppProxy) MarkNotBilled(ctx *titan.Context, request schein_common.MarkNotBilledRequest) error {
	return srv.service.MarkNotBilled(ctx, request)
}
func (srv *ScheinAppProxy) CheckExistKVScheinCurrentQuarter(ctx *titan.Context, request schein_common.CheckExistKVScheinCurrentQuarterRequest) (*schein_common.CheckExistKVScheinCurrentQuarterResponse, error) {
	return srv.service.CheckExistKVScheinCurrentQuarter(ctx, request)
}
func (srv *ScheinAppProxy) DeleteSchein(ctx *titan.Context, request schein_common.DeleteScheinRequest) error {
	return srv.service.DeleteSchein(ctx, request)
}
func (srv *ScheinAppProxy) GetScheinDetail(ctx *titan.Context, request schein_common.GetScheinDetailRequest) (*schein_common.GetScheinDetailResponse, error) {
	return srv.service.GetScheinDetail(ctx, request)
}
func (srv *ScheinAppProxy) GetFields(ctx *titan.Context, request schein_common.GetFieldsRequest) (*schein_common.GetFieldsResponse, error) {
	return srv.service.GetFields(ctx, request)
}
func (srv *ScheinAppProxy) GetScheinsOverview(ctx *titan.Context, request schein_common.GetScheinsOverviewRequest) (*schein_common.GetScheinsOverviewResponse, error) {
	return srv.service.GetScheinsOverview(ctx, request)
}
func (srv *ScheinAppProxy) HandleEventScheinChanged(ctx *titan.Context, request EventScheinChanged) error {
	return srv.service.HandleEventScheinChanged(ctx, request)
}
func (srv *ScheinAppProxy) HandleEventCreateRemoveSchein(ctx *titan.Context, request EventCreateRemoveSchein) error {
	return srv.service.HandleEventCreateRemoveSchein(ctx, request)
}
func (srv *ScheinAppProxy) GetSetting(ctx *titan.Context) (*schein_common.GetSettingResponse, error) {
	return srv.service.GetSetting(ctx)
}
func (srv *ScheinAppProxy) SaveSetting(ctx *titan.Context, request schein_common.SaveSettingRequest) error {
	return srv.service.SaveSetting(ctx, request)
}
func (srv *ScheinAppProxy) GetSelectedTreatmentCaseSubgroup(ctx *titan.Context, request schein_common.GetSelectedTreatmentCaseSubgroupRequest) (*schein_common.GetSelectedTreatmentCaseSubgroupResponse, error) {
	return srv.service.GetSelectedTreatmentCaseSubgroup(ctx, request)
}
func (srv *ScheinAppProxy) GetOrderList(ctx *titan.Context) (*schein_common.GetOrderListResponse, error) {
	return srv.service.GetOrderList(ctx)
}
func (srv *ScheinAppProxy) SaveOrderList(ctx *titan.Context, request schein_common.SaveOrderListRequest) error {
	return srv.service.SaveOrderList(ctx, request)
}
func (srv *ScheinAppProxy) MarkBill(ctx *titan.Context, request MarkBillRequest) error {
	return srv.service.MarkBill(ctx, request)
}
func (srv *ScheinAppProxy) GetKTABs(ctx *titan.Context, request GetKTABsRequest) (*GetKTABsResponse, error) {
	return srv.service.GetKTABs(ctx, request)
}
func (srv *ScheinAppProxy) GetSubGroupFromMasterData(ctx *titan.Context, request GetSubGroupFromMasterDataRequest) (*GetSubGroupFromMasterDataResponse, error) {
	return srv.service.GetSubGroupFromMasterData(ctx, request)
}
func (srv *ScheinAppProxy) GetBillingAreaFromMasterData(ctx *titan.Context, request GetBillingAreaFromMasterDataRequest) (*GetBillingAreaFromMasterDataResponse, error) {
	return srv.service.GetBillingAreaFromMasterData(ctx, request)
}
func (srv *ScheinAppProxy) GetRezidivList(ctx *titan.Context) (*GetRezidivListResponse, error) {
	return srv.service.GetRezidivList(ctx)
}
func (srv *ScheinAppProxy) GetPsychotherapyById(ctx *titan.Context, request GetPsychotherapyByIdRequest) (*GetPsychotherapyByIdResponse, error) {
	return srv.service.GetPsychotherapyById(ctx, request)
}
func (srv *ScheinAppProxy) GetScheinByInsuranceId(ctx *titan.Context, request GetScheinByInsuranceIdRequest) (*GetScheinByInsuranceIdResponse, error) {
	return srv.service.GetScheinByInsuranceId(ctx, request)
}
func (srv *ScheinAppProxy) GetScheinsByInsuranceId(ctx *titan.Context, request GetScheinByInsuranceIdRequest) (*GetScheinsByInsuranceIdResponse, error) {
	return srv.service.GetScheinsByInsuranceId(ctx, request)
}
func (srv *ScheinAppProxy) RevertTechnicalSchein(ctx *titan.Context, request RevertTechnicalScheinRequest) error {
	return srv.service.RevertTechnicalSchein(ctx, request)
}
func (srv *ScheinAppProxy) CreatePrivateSchein(ctx *titan.Context, request CreatePrivateScheinRequest) (*private_schein_common.PrivateScheinItem, error) {
	return srv.service.CreatePrivateSchein(ctx, request)
}
func (srv *ScheinAppProxy) UpdatePrivateSchein(ctx *titan.Context, request UpdatePrivateScheinRequest) (*private_schein_common.PrivateScheinItem, error) {
	return srv.service.UpdatePrivateSchein(ctx, request)
}
func (srv *ScheinAppProxy) DeletePrivateSchein(ctx *titan.Context, request DeletePrivateScheinRequest) error {
	return srv.service.DeletePrivateSchein(ctx, request)
}
func (srv *ScheinAppProxy) IsValidPrivateSchein(ctx *titan.Context, request IsValidPrivateScheinRequest) (*IsValidResponse, error) {
	return srv.service.IsValidPrivateSchein(ctx, request)
}
func (srv *ScheinAppProxy) GetPrivateScheinById(ctx *titan.Context, request GetPrivateScheinByIdRequest) (*private_schein_common.PrivateScheinItem, error) {
	return srv.service.GetPrivateScheinById(ctx, request)
}
func (srv *ScheinAppProxy) GetGoaFactorValue(ctx *titan.Context, request GetGoaFactorValueRequest) (*GetGoaFactorValueResponse, error) {
	return srv.service.GetGoaFactorValue(ctx, request)
}
func (srv *ScheinAppProxy) TakeOverDiagnosisByScheinId(ctx *titan.Context, request TakeOverDiagnosisByScheinIdRequest) error {
	return srv.service.TakeOverDiagnosisByScheinId(ctx, request)
}
func (srv *ScheinAppProxy) CreateSvScheinAutomaticly(ctx *titan.Context, request schein_common.CreateSvScheinAutomaticlyRequest) (*schein_common.CreateSvScheinAutomaticlyResponse, error) {
	return srv.service.CreateSvScheinAutomaticly(ctx, request)
}
func (srv *ScheinAppProxy) CreateSvScheinFromReference(ctx *titan.Context, request schein_common.CreateSvScheinFromReferenceRequest) (*schein_common.CreateSvScheinFromReferenceResponse, error) {
	return srv.service.CreateSvScheinFromReference(ctx, request)
}
func (srv *ScheinAppProxy) CreateSvScheinManually(ctx *titan.Context, request schein_common.CreateSvScheinManuallyRequest) (*schein_common.CreateSvScheinManuallyResponse, error) {
	return srv.service.CreateSvScheinManually(ctx, request)
}
func (srv *ScheinAppProxy) UpdateSvSchein(ctx *titan.Context, request schein_common.UpdateSvScheinRequest) (*schein_common.UpdateSvScheinResponse, error) {
	return srv.service.UpdateSvSchein(ctx, request)
}
func (srv *ScheinAppProxy) MarkAsReferral(ctx *titan.Context, request schein_common.MarkAsReferralRequest) error {
	return srv.service.MarkAsReferral(ctx, request)
}
func (srv *ScheinAppProxy) RemoveReferral(ctx *titan.Context, request schein_common.RemoveReferralRequest) error {
	return srv.service.RemoveReferral(ctx, request)
}
func (srv *ScheinAppProxy) GetScheinItemById(ctx *titan.Context, request GetScheinItemByIdRequest) (*GetScheinItemByIdResponse, error) {
	return srv.service.GetScheinItemById(ctx, request)
}
func (srv *ScheinAppProxy) GetScheinItemByIds(ctx *titan.Context, request GetScheinItemByIdsRequest) (*GetScheinItemByIdsResponse, error) {
	return srv.service.GetScheinItemByIds(ctx, request)
}
func (srv *ScheinAppProxy) CreateBgSchein(ctx *titan.Context, request CreateBgScheinRequest) (*schein_common.BgScheinItem, error) {
	return srv.service.CreateBgSchein(ctx, request)
}
func (srv *ScheinAppProxy) UpdateBgSchein(ctx *titan.Context, request UpdateBgScheinRequest) (*schein_common.BgScheinItem, error) {
	return srv.service.UpdateBgSchein(ctx, request)
}
func (srv *ScheinAppProxy) DeleteBgSchein(ctx *titan.Context, request DeleteBgScheinRequest) error {
	return srv.service.DeleteBgSchein(ctx, request)
}
func (srv *ScheinAppProxy) GetBgScheinById(ctx *titan.Context, request GetBgScheinByIdRequest) (*schein_common.BgScheinItem, error) {
	return srv.service.GetBgScheinById(ctx, request)
}
func (srv *ScheinAppProxy) IsValidBgSchein(ctx *titan.Context, request IsValidBgScheinRequest) (*IsValidResponse, error) {
	return srv.service.IsValidBgSchein(ctx, request)
}
func (srv *ScheinAppProxy) CheckDummyVknr(ctx *titan.Context, request CheckDummyVknrRequest) (*CheckDummyVknrResponse, error) {
	return srv.service.CheckDummyVknr(ctx, request)
}
func (srv *ScheinAppProxy) GetTotalScheins(ctx *titan.Context, request GetTotalScheinsRequest) (*GetTotalScheinsResponse, error) {
	return srv.service.GetTotalScheins(ctx, request)
}

// Define service router -----------------------------------------------------------------
type ScheinAppRouter struct {
	proxy *ScheinAppProxy
}

func (router *ScheinAppRouter) Register(r titan.Router) {
	r.RegisterTopic(EVENT_CreateSvScheins, router.proxy.CreateSvScheins, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_CreateSvScheins, router.proxy.CreateSvScheins, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_IsValid, router.proxy.IsValid, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_IsValid, router.proxy.IsValid, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_CreateSchein, router.proxy.CreateSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_CreateSchein, router.proxy.CreateSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_TakeOverScheinDiagnosis, router.proxy.TakeOverScheinDiagnosis, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_TakeOverScheinDiagnosis, router.proxy.TakeOverScheinDiagnosis, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetScheinDetailById, router.proxy.GetScheinDetailById, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetScheinDetailById, router.proxy.GetScheinDetailById, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetScheinDetailByIds, router.proxy.GetScheinDetailByIds, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetScheinDetailByIds, router.proxy.GetScheinDetailByIds, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_UpdateSchein, router.proxy.UpdateSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_UpdateSchein, router.proxy.UpdateSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_MarkNotBilled, router.proxy.MarkNotBilled, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_MarkNotBilled, router.proxy.MarkNotBilled, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_CheckExistKVScheinCurrentQuarter, router.proxy.CheckExistKVScheinCurrentQuarter, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_CheckExistKVScheinCurrentQuarter, router.proxy.CheckExistKVScheinCurrentQuarter, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_DeleteSchein, router.proxy.DeleteSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_DeleteSchein, router.proxy.DeleteSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetScheinDetail, router.proxy.GetScheinDetail, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetScheinDetail, router.proxy.GetScheinDetail, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetFields, router.proxy.GetFields, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetFields, router.proxy.GetFields, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetScheinsOverview, router.proxy.GetScheinsOverview, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetScheinsOverview, router.proxy.GetScheinsOverview, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetSetting, router.proxy.GetSetting, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetSetting, router.proxy.GetSetting, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_SaveSetting, router.proxy.SaveSetting, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_SaveSetting, router.proxy.SaveSetting, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetSelectedTreatmentCaseSubgroup, router.proxy.GetSelectedTreatmentCaseSubgroup, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetSelectedTreatmentCaseSubgroup, router.proxy.GetSelectedTreatmentCaseSubgroup, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetOrderList, router.proxy.GetOrderList, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetOrderList, router.proxy.GetOrderList, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_SaveOrderList, router.proxy.SaveOrderList, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_SaveOrderList, router.proxy.SaveOrderList, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_MarkBill, router.proxy.MarkBill, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_MarkBill, router.proxy.MarkBill, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetKTABs, router.proxy.GetKTABs, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetKTABs, router.proxy.GetKTABs, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetSubGroupFromMasterData, router.proxy.GetSubGroupFromMasterData, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetSubGroupFromMasterData, router.proxy.GetSubGroupFromMasterData, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetBillingAreaFromMasterData, router.proxy.GetBillingAreaFromMasterData, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetBillingAreaFromMasterData, router.proxy.GetBillingAreaFromMasterData, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetRezidivList, router.proxy.GetRezidivList, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetRezidivList, router.proxy.GetRezidivList, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetPsychotherapyById, router.proxy.GetPsychotherapyById, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetPsychotherapyById, router.proxy.GetPsychotherapyById, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetScheinByInsuranceId, router.proxy.GetScheinByInsuranceId, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetScheinByInsuranceId, router.proxy.GetScheinByInsuranceId, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetScheinsByInsuranceId, router.proxy.GetScheinsByInsuranceId, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetScheinsByInsuranceId, router.proxy.GetScheinsByInsuranceId, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_RevertTechnicalSchein, router.proxy.RevertTechnicalSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_RevertTechnicalSchein, router.proxy.RevertTechnicalSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_CreatePrivateSchein, router.proxy.CreatePrivateSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_CreatePrivateSchein, router.proxy.CreatePrivateSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_UpdatePrivateSchein, router.proxy.UpdatePrivateSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_UpdatePrivateSchein, router.proxy.UpdatePrivateSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_DeletePrivateSchein, router.proxy.DeletePrivateSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_DeletePrivateSchein, router.proxy.DeletePrivateSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_IsValidPrivateSchein, router.proxy.IsValidPrivateSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_IsValidPrivateSchein, router.proxy.IsValidPrivateSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetPrivateScheinById, router.proxy.GetPrivateScheinById, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetPrivateScheinById, router.proxy.GetPrivateScheinById, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetGoaFactorValue, router.proxy.GetGoaFactorValue, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetGoaFactorValue, router.proxy.GetGoaFactorValue, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_TakeOverDiagnosisByScheinId, router.proxy.TakeOverDiagnosisByScheinId, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_TakeOverDiagnosisByScheinId, router.proxy.TakeOverDiagnosisByScheinId, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_CreateSvScheinAutomaticly, router.proxy.CreateSvScheinAutomaticly, titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_CreateSvScheinAutomaticly, router.proxy.CreateSvScheinAutomaticly, titan.IsAuthenticated())
	r.RegisterTopic(EVENT_CreateSvScheinFromReference, router.proxy.CreateSvScheinFromReference, titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_CreateSvScheinFromReference, router.proxy.CreateSvScheinFromReference, titan.IsAuthenticated())
	r.RegisterTopic(EVENT_CreateSvScheinManually, router.proxy.CreateSvScheinManually, titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_CreateSvScheinManually, router.proxy.CreateSvScheinManually, titan.IsAuthenticated())
	r.RegisterTopic(EVENT_UpdateSvSchein, router.proxy.UpdateSvSchein, titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_UpdateSvSchein, router.proxy.UpdateSvSchein, titan.IsAuthenticated())
	r.RegisterTopic(EVENT_MarkAsReferral, router.proxy.MarkAsReferral, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_MarkAsReferral, router.proxy.MarkAsReferral, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_RemoveReferral, router.proxy.RemoveReferral, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_RemoveReferral, router.proxy.RemoveReferral, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetScheinItemById, router.proxy.GetScheinItemById, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetScheinItemById, router.proxy.GetScheinItemById, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetScheinItemByIds, router.proxy.GetScheinItemByIds, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetScheinItemByIds, router.proxy.GetScheinItemByIds, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_CreateBgSchein, router.proxy.CreateBgSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_CreateBgSchein, router.proxy.CreateBgSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_UpdateBgSchein, router.proxy.UpdateBgSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_UpdateBgSchein, router.proxy.UpdateBgSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_DeleteBgSchein, router.proxy.DeleteBgSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_DeleteBgSchein, router.proxy.DeleteBgSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetBgScheinById, router.proxy.GetBgScheinById, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetBgScheinById, router.proxy.GetBgScheinById, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_IsValidBgSchein, router.proxy.IsValidBgSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_IsValidBgSchein, router.proxy.IsValidBgSchein, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_CheckDummyVknr, router.proxy.CheckDummyVknr, titan.IsAuthenticated())
	r.RegisterTopic(LEGACY_TOPIC_CheckDummyVknr, router.proxy.CheckDummyVknr, titan.IsAuthenticated())
	r.RegisterTopic(EVENT_GetTotalScheins, router.proxy.GetTotalScheins, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetTotalScheins, router.proxy.GetTotalScheins, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
}

// Subscriber
func (router *ScheinAppRouter) Subscribe(s *titan.MessageSubscriber) {
	s.Register(EVENT_ScheinChanged, "api.app.mvz_ScheinApp_handleEventScheinChanged_Queue", func(p *titan.Message) error {
		var resp EventScheinChanged
		ctx, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		return router.proxy.HandleEventScheinChanged(ctx, resp)
	})
	s.Register(EVENT_CreateRemoveSchein, "api.app.mvz_ScheinApp_handleEventCreateRemoveSchein_Queue", func(p *titan.Message) error {
		var resp EventCreateRemoveSchein
		ctx, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		return router.proxy.HandleEventCreateRemoveSchein(ctx, resp)
	})
}

func NewScheinAppRouter(s ScheinApp) *ScheinAppRouter {
	p := &ScheinAppProxy{s}
	return &ScheinAppRouter{p}
}

func NewServer(bff0 ScheinApp, options ...titan.Option) *titan.Server {
	opt := options

	router0 := NewScheinAppRouter(bff0)
	opt = append(opt, titan.Routes(router0.Register), titan.Subscribe(router0.Subscribe))

	return titan.NewServer(NATS_SUBJECT, opt...)
}

// Define client ----------------------------------------------------------------------------------------------
type ScheinAppClient struct {
	client *titan.Client
}

func (srv *ScheinAppClient) CreateSvScheins(ctx *titan.Context, request schein_common.CreateSvScheinRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CreateSvScheins).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *ScheinAppClient) IsValid(ctx *titan.Context, request IsValidRequest) (*IsValidResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_IsValid).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &IsValidResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) CreateSchein(ctx *titan.Context, request CreateScheinRequest) (*CreateScheinResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CreateSchein).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &CreateScheinResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) TakeOverScheinDiagnosis(ctx *titan.Context, request TakeOverScheinDiagnisToRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_TakeOverScheinDiagnosis).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *ScheinAppClient) GetScheinDetailById(ctx *titan.Context, request schein_common.GetScheinDetailByIdRequest) (*schein_common.GetScheinDetailByIdResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetScheinDetailById).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &schein_common.GetScheinDetailByIdResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) GetScheinDetailByIds(ctx *titan.Context, request schein_common.GetScheinDetailByIdsRequest) (*schein_common.GetScheinDetailByIdsResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetScheinDetailByIds).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &schein_common.GetScheinDetailByIdsResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) UpdateSchein(ctx *titan.Context, request schein_common.UpdateScheinRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UpdateSchein).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *ScheinAppClient) MarkNotBilled(ctx *titan.Context, request schein_common.MarkNotBilledRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_MarkNotBilled).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *ScheinAppClient) CheckExistKVScheinCurrentQuarter(ctx *titan.Context, request schein_common.CheckExistKVScheinCurrentQuarterRequest) (*schein_common.CheckExistKVScheinCurrentQuarterResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CheckExistKVScheinCurrentQuarter).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &schein_common.CheckExistKVScheinCurrentQuarterResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) DeleteSchein(ctx *titan.Context, request schein_common.DeleteScheinRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_DeleteSchein).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *ScheinAppClient) GetScheinDetail(ctx *titan.Context, request schein_common.GetScheinDetailRequest) (*schein_common.GetScheinDetailResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetScheinDetail).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &schein_common.GetScheinDetailResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) GetFields(ctx *titan.Context, request schein_common.GetFieldsRequest) (*schein_common.GetFieldsResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetFields).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &schein_common.GetFieldsResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) GetScheinsOverview(ctx *titan.Context, request schein_common.GetScheinsOverviewRequest) (*schein_common.GetScheinsOverviewResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetScheinsOverview).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &schein_common.GetScheinsOverviewResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) GetSetting(ctx *titan.Context) (*schein_common.GetSettingResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetSetting).
		Subject(NATS_SUBJECT).
		Build()
	var resp = &schein_common.GetSettingResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) SaveSetting(ctx *titan.Context, request schein_common.SaveSettingRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_SaveSetting).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *ScheinAppClient) GetSelectedTreatmentCaseSubgroup(ctx *titan.Context, request schein_common.GetSelectedTreatmentCaseSubgroupRequest) (*schein_common.GetSelectedTreatmentCaseSubgroupResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetSelectedTreatmentCaseSubgroup).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &schein_common.GetSelectedTreatmentCaseSubgroupResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) GetOrderList(ctx *titan.Context) (*schein_common.GetOrderListResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetOrderList).
		Subject(NATS_SUBJECT).
		Build()
	var resp = &schein_common.GetOrderListResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) SaveOrderList(ctx *titan.Context, request schein_common.SaveOrderListRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_SaveOrderList).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *ScheinAppClient) MarkBill(ctx *titan.Context, request MarkBillRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_MarkBill).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *ScheinAppClient) GetKTABs(ctx *titan.Context, request GetKTABsRequest) (*GetKTABsResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetKTABs).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetKTABsResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) GetSubGroupFromMasterData(ctx *titan.Context, request GetSubGroupFromMasterDataRequest) (*GetSubGroupFromMasterDataResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetSubGroupFromMasterData).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetSubGroupFromMasterDataResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) GetBillingAreaFromMasterData(ctx *titan.Context, request GetBillingAreaFromMasterDataRequest) (*GetBillingAreaFromMasterDataResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetBillingAreaFromMasterData).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetBillingAreaFromMasterDataResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) GetRezidivList(ctx *titan.Context) (*GetRezidivListResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetRezidivList).
		Subject(NATS_SUBJECT).
		Build()
	var resp = &GetRezidivListResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) GetPsychotherapyById(ctx *titan.Context, request GetPsychotherapyByIdRequest) (*GetPsychotherapyByIdResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetPsychotherapyById).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetPsychotherapyByIdResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) GetScheinByInsuranceId(ctx *titan.Context, request GetScheinByInsuranceIdRequest) (*GetScheinByInsuranceIdResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetScheinByInsuranceId).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetScheinByInsuranceIdResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) GetScheinsByInsuranceId(ctx *titan.Context, request GetScheinByInsuranceIdRequest) (*GetScheinsByInsuranceIdResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetScheinsByInsuranceId).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetScheinsByInsuranceIdResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) RevertTechnicalSchein(ctx *titan.Context, request RevertTechnicalScheinRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_RevertTechnicalSchein).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *ScheinAppClient) CreatePrivateSchein(ctx *titan.Context, request CreatePrivateScheinRequest) (*private_schein_common.PrivateScheinItem, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CreatePrivateSchein).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &private_schein_common.PrivateScheinItem{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) UpdatePrivateSchein(ctx *titan.Context, request UpdatePrivateScheinRequest) (*private_schein_common.PrivateScheinItem, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UpdatePrivateSchein).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &private_schein_common.PrivateScheinItem{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) DeletePrivateSchein(ctx *titan.Context, request DeletePrivateScheinRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_DeletePrivateSchein).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *ScheinAppClient) IsValidPrivateSchein(ctx *titan.Context, request IsValidPrivateScheinRequest) (*IsValidResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_IsValidPrivateSchein).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &IsValidResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) GetPrivateScheinById(ctx *titan.Context, request GetPrivateScheinByIdRequest) (*private_schein_common.PrivateScheinItem, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetPrivateScheinById).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &private_schein_common.PrivateScheinItem{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) GetGoaFactorValue(ctx *titan.Context, request GetGoaFactorValueRequest) (*GetGoaFactorValueResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetGoaFactorValue).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetGoaFactorValueResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) TakeOverDiagnosisByScheinId(ctx *titan.Context, request TakeOverDiagnosisByScheinIdRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_TakeOverDiagnosisByScheinId).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *ScheinAppClient) CreateSvScheinAutomaticly(ctx *titan.Context, request schein_common.CreateSvScheinAutomaticlyRequest) (*schein_common.CreateSvScheinAutomaticlyResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CreateSvScheinAutomaticly).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &schein_common.CreateSvScheinAutomaticlyResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) CreateSvScheinFromReference(ctx *titan.Context, request schein_common.CreateSvScheinFromReferenceRequest) (*schein_common.CreateSvScheinFromReferenceResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CreateSvScheinFromReference).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &schein_common.CreateSvScheinFromReferenceResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) CreateSvScheinManually(ctx *titan.Context, request schein_common.CreateSvScheinManuallyRequest) (*schein_common.CreateSvScheinManuallyResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CreateSvScheinManually).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &schein_common.CreateSvScheinManuallyResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) UpdateSvSchein(ctx *titan.Context, request schein_common.UpdateSvScheinRequest) (*schein_common.UpdateSvScheinResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UpdateSvSchein).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &schein_common.UpdateSvScheinResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) MarkAsReferral(ctx *titan.Context, request schein_common.MarkAsReferralRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_MarkAsReferral).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *ScheinAppClient) RemoveReferral(ctx *titan.Context, request schein_common.RemoveReferralRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_RemoveReferral).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *ScheinAppClient) GetScheinItemById(ctx *titan.Context, request GetScheinItemByIdRequest) (*GetScheinItemByIdResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetScheinItemById).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetScheinItemByIdResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) GetScheinItemByIds(ctx *titan.Context, request GetScheinItemByIdsRequest) (*GetScheinItemByIdsResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetScheinItemByIds).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetScheinItemByIdsResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) CreateBgSchein(ctx *titan.Context, request CreateBgScheinRequest) (*schein_common.BgScheinItem, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CreateBgSchein).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &schein_common.BgScheinItem{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) UpdateBgSchein(ctx *titan.Context, request UpdateBgScheinRequest) (*schein_common.BgScheinItem, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UpdateBgSchein).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &schein_common.BgScheinItem{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) DeleteBgSchein(ctx *titan.Context, request DeleteBgScheinRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_DeleteBgSchein).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *ScheinAppClient) GetBgScheinById(ctx *titan.Context, request GetBgScheinByIdRequest) (*schein_common.BgScheinItem, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetBgScheinById).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &schein_common.BgScheinItem{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) IsValidBgSchein(ctx *titan.Context, request IsValidBgScheinRequest) (*IsValidResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_IsValidBgSchein).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &IsValidResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) CheckDummyVknr(ctx *titan.Context, request CheckDummyVknrRequest) (*CheckDummyVknrResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CheckDummyVknr).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &CheckDummyVknrResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *ScheinAppClient) GetTotalScheins(ctx *titan.Context, request GetTotalScheinsRequest) (*GetTotalScheinsResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetTotalScheins).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetTotalScheinsResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func NewScheinAppClient(clients ...*titan.Client) *ScheinAppClient {
	var client *titan.Client
	if len(clients) <= 0 {
		client = titan.GetDefaultClient()
	} else {
		client = clients[0]
	}
	return &ScheinAppClient{client: client}
}

// Define event Notifier -----------------------------------------------------------------------------------------
type ScheinNotifier struct {
	client *titan.Client
}

func NewScheinNotifier() *ScheinNotifier {
	client := titan.GetDefaultClient()
	return &ScheinNotifier{client}
}
func (p *ScheinNotifier) NotifyScheinChanged(ctx *titan.Context, event *EventScheinChanged) error {
	return p.client.Publish(ctx, EVENT_ScheinChanged, event)
}
func (p *ScheinNotifier) NotifyScheinChangedResponse(ctx *titan.Context, event *EventScheinChangedResponse) error {
	return p.client.Publish(ctx, EVENT_ScheinChangedResponse, event)
}
func (p *ScheinNotifier) NotifyCreateRemoveSchein(ctx *titan.Context, event *EventCreateRemoveSchein) error {
	return p.client.Publish(ctx, EVENT_CreateRemoveSchein, event)
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type ScheinSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewScheinSocketNotifier(socket *socket_api.SocketServiceClient) *ScheinSocketNotifier {
	return &ScheinSocketNotifier{socket}
}
func (n *ScheinSocketNotifier) NotifyCareProviderScheinChanged(ctx *titan.Context, event *EventScheinChanged) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_ScheinChanged,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *ScheinSocketNotifier) NotifyUserScheinChanged(ctx *titan.Context, event *EventScheinChanged) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_ScheinChanged,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *ScheinSocketNotifier) NotifyDeviceScheinChanged(ctx *titan.Context, event *EventScheinChanged) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_ScheinChanged,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *ScheinSocketNotifier) NotifyClientScheinChanged(ctx *titan.Context, event *EventScheinChanged) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_ScheinChanged,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}
func (n *ScheinSocketNotifier) NotifyCareProviderScheinChangedResponse(ctx *titan.Context, event *EventScheinChangedResponse) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_ScheinChangedResponse,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *ScheinSocketNotifier) NotifyUserScheinChangedResponse(ctx *titan.Context, event *EventScheinChangedResponse) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_ScheinChangedResponse,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *ScheinSocketNotifier) NotifyDeviceScheinChangedResponse(ctx *titan.Context, event *EventScheinChangedResponse) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_ScheinChangedResponse,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *ScheinSocketNotifier) NotifyClientScheinChangedResponse(ctx *titan.Context, event *EventScheinChangedResponse) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_ScheinChangedResponse,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}
func (n *ScheinSocketNotifier) NotifyCareProviderCreateRemoveSchein(ctx *titan.Context, event *EventCreateRemoveSchein) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_CreateRemoveSchein,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *ScheinSocketNotifier) NotifyUserCreateRemoveSchein(ctx *titan.Context, event *EventCreateRemoveSchein) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_CreateRemoveSchein,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *ScheinSocketNotifier) NotifyDeviceCreateRemoveSchein(ctx *titan.Context, event *EventCreateRemoveSchein) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_CreateRemoveSchein,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *ScheinSocketNotifier) NotifyClientCreateRemoveSchein(ctx *titan.Context, event *EventCreateRemoveSchein) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_CreateRemoveSchein,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}

// -----------------------------------------------
// Test event listener
type ScheinEventListener struct {
	mux                                sync.Mutex
	LastEventScheinChangedList         []EventScheinChanged
	LastEventScheinChangedResponseList []EventScheinChangedResponse
	LastEventCreateRemoveScheinList    []EventCreateRemoveSchein
}

func (listener *ScheinEventListener) Reset() {
	listener.mux.Lock()
	listener.LastEventScheinChangedList = []EventScheinChanged{}
	listener.LastEventScheinChangedResponseList = []EventScheinChangedResponse{}
	listener.LastEventCreateRemoveScheinList = []EventCreateRemoveSchein{}
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *ScheinEventListener) Subscribe(s *titan.MessageSubscriber) {
	s.Register(EVENT_ScheinChanged, "api.app.mvz_AppMvzSchein_ScheinChanged_Queue_test", func(p *titan.Message) error {
		var resp EventScheinChanged
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventScheinChangedList) >= 100 {
			listener.LastEventScheinChangedList = listener.LastEventScheinChangedList[1:]
		}
		listener.LastEventScheinChangedList = append(listener.LastEventScheinChangedList, resp)
		listener.mux.Unlock()
		return nil
	})
	s.Register(EVENT_ScheinChangedResponse, "api.app.mvz_AppMvzSchein_ScheinChangedResponse_Queue_test", func(p *titan.Message) error {
		var resp EventScheinChangedResponse
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventScheinChangedResponseList) >= 100 {
			listener.LastEventScheinChangedResponseList = listener.LastEventScheinChangedResponseList[1:]
		}
		listener.LastEventScheinChangedResponseList = append(listener.LastEventScheinChangedResponseList, resp)
		listener.mux.Unlock()
		return nil
	})
	s.Register(EVENT_CreateRemoveSchein, "api.app.mvz_AppMvzSchein_CreateRemoveSchein_Queue_test", func(p *titan.Message) error {
		var resp EventCreateRemoveSchein
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventCreateRemoveScheinList) >= 100 {
			listener.LastEventCreateRemoveScheinList = listener.LastEventCreateRemoveScheinList[1:]
		}
		listener.LastEventCreateRemoveScheinList = append(listener.LastEventCreateRemoveScheinList, resp)
		listener.mux.Unlock()
		return nil
	})
}
func (listener *ScheinEventListener) GetLastEventScheinChangedList(timeOutInMilliSeconds time.Duration) []EventScheinChanged {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventScheinChangedList
		default:
			// if any value
			if len(listener.LastEventScheinChangedList) > 0 {
				return listener.LastEventScheinChangedList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
func (listener *ScheinEventListener) GetLastEventScheinChangedResponseList(timeOutInMilliSeconds time.Duration) []EventScheinChangedResponse {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventScheinChangedResponseList
		default:
			// if any value
			if len(listener.LastEventScheinChangedResponseList) > 0 {
				return listener.LastEventScheinChangedResponseList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
func (listener *ScheinEventListener) GetLastEventCreateRemoveScheinList(timeOutInMilliSeconds time.Duration) []EventCreateRemoveSchein {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventCreateRemoveScheinList
		default:
			// if any value
			if len(listener.LastEventCreateRemoveScheinList) > 0 {
				return listener.LastEventCreateRemoveScheinList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
