// This code was autogenerated from app/mvz/patient_combination.proto, do not edit.

package patient_combination

import (
	"encoding/json"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type CombinePatientsRequest struct {
	TargetPatientId      uuid.UUID   `json:"targetPatientId" validate:"required"`
	DuplicatedPatientIds []uuid.UUID `json:"duplicatedPatientIds" validate:"required"`
}

// enum definitions

// Define constants
const NATS_SUBJECT = "api.app.mvz" // nats subject this service will listen to

// service event constants
const EVENT_CombinePatients = "api.app.mvz.PatientCombinationApp.CombinePatients"
const LEGACY_TOPIC_CombinePatients = "/api/app/mvz/patient/combination/combinePatients"

// message event constants

// Define service interface -------------------------------------------------------------
type PatientCombinationApp interface {
	CombinePatients(ctx *titan.Context, request CombinePatientsRequest) error
}

// Define service proxy -------------------------------------------------------------------
type PatientCombinationAppProxy struct {
	service PatientCombinationApp
}

func (srv *PatientCombinationAppProxy) CombinePatients(ctx *titan.Context, request CombinePatientsRequest) error {
	return srv.service.CombinePatients(ctx, request)
}

// Define service router -----------------------------------------------------------------
type PatientCombinationAppRouter struct {
	proxy *PatientCombinationAppProxy
}

func (router *PatientCombinationAppRouter) Register(r titan.Router) {
	r.RegisterTopic(EVENT_CombinePatients, router.proxy.CombinePatients, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_CombinePatients, router.proxy.CombinePatients, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
}

// Subscriber
func (router *PatientCombinationAppRouter) Subscribe(s *titan.MessageSubscriber) {
}

func NewPatientCombinationAppRouter(s PatientCombinationApp) *PatientCombinationAppRouter {
	p := &PatientCombinationAppProxy{s}
	return &PatientCombinationAppRouter{p}
}

func NewServer(bff0 PatientCombinationApp, options ...titan.Option) *titan.Server {
	opt := options

	router0 := NewPatientCombinationAppRouter(bff0)
	opt = append(opt, titan.Routes(router0.Register), titan.Subscribe(router0.Subscribe))

	return titan.NewServer(NATS_SUBJECT, opt...)
}

// Define client ----------------------------------------------------------------------------------------------
type PatientCombinationAppClient struct {
	client *titan.Client
}

func (srv *PatientCombinationAppClient) CombinePatients(ctx *titan.Context, request CombinePatientsRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CombinePatients).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}

func NewPatientCombinationAppClient(clients ...*titan.Client) *PatientCombinationAppClient {
	var client *titan.Client
	if len(clients) <= 0 {
		client = titan.GetDefaultClient()
	} else {
		client = clients[0]
	}
	return &PatientCombinationAppClient{client: client}
}

// Define event Notifier -----------------------------------------------------------------------------------------
type PatientCombinationNotifier struct {
	client *titan.Client
}

func NewPatientCombinationNotifier() *PatientCombinationNotifier {
	client := titan.GetDefaultClient()
	return &PatientCombinationNotifier{client}
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type PatientCombinationSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewPatientCombinationSocketNotifier(socket *socket_api.SocketServiceClient) *PatientCombinationSocketNotifier {
	return &PatientCombinationSocketNotifier{socket}
}

// -----------------------------------------------
// Test event listener
type PatientCombinationEventListener struct {
	mux sync.Mutex
}

func (listener *PatientCombinationEventListener) Reset() {
	listener.mux.Lock()
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *PatientCombinationEventListener) Subscribe(s *titan.MessageSubscriber) {
}
