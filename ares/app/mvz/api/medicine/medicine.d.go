// This code was autogenerated from app/mvz/medicine.proto, do not edit.

package medicine

import (
	"encoding/json"
	"errors"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	timeline "git.tutum.dev/medi/tutum/ares/app/mvz/api/timeline"

	bmp "git.tutum.dev/medi/tutum/ares/service/domains/api/bmp"

	common "git.tutum.dev/medi/tutum/ares/service/domains/api/common"

	common1 "git.tutum.dev/medi/tutum/ares/service/domains/form/common"

	common2 "git.tutum.dev/medi/tutum/ares/service/domains/qes/common"

	common3 "git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/medicine/common"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type DrugInformation struct {
	ATC            string      `json:"aTC"`
	ATCA           string      `json:"aTCA"`
	ATCName        string      `json:"aTCName"`
	Effectiveness  string      `json:"effectiveness"`
	NoOfSubstances int64       `json:"noOfSubstances"`
	Components     []Component `json:"components"`
}

type Component struct {
	Substances        []Substance `json:"substances"`
	ComponentNumber   int64       `json:"componentNumber"`
	ComponentName     string      `json:"componentName"`
	PhysicalForm      int64       `json:"physicalForm"`
	AbsoluteUnit      string      `json:"absoluteUnit"`
	AbsoluteAmount    string      `json:"absoluteAmount"`
	RelativeUnit      string      `json:"relativeUnit"`
	RelativeAmount    string      `json:"relativeAmount"`
	RelativeForm      string      `json:"relativeForm"`
	EthanolPercentage string      `json:"ethanolPercentage"`
	ReleaseBehavior   int64       `json:"releaseBehavior"`
	GalenicBasicForm  int64       `json:"galenicBasicForm"`
}

type Substance struct {
	SubstanceType       int64   `json:"substanceType"`
	Rank                int64   `json:"rank"`
	Unit                string  `json:"unit"`
	Amount              float64 `json:"amount"`
	EquivalentSubstance int64   `json:"equivalentSubstance"`
	Suffix              string  `json:"suffix"`
	Name                string  `json:"name"`
	GbaUrl              string  `json:"gbaUrl"`
	Id                  int64   `json:"id"`
}

type HintsAndWarning struct {
	StandardIndex string `json:"standardIndex"`
	Text          string `json:"text"`
}

type LegalNote struct {
	Type int64  `json:"type"`
	Text string `json:"text"`
}

type Medicine struct {
	Id                        string                     `json:"id"`
	Pzn                       string                     `json:"pzn"`
	Search                    string                     `json:"search"`
	ProductInformation        *ProductInformation        `json:"productInformation,omitempty"`
	HintsAndWarnings          []HintsAndWarning          `json:"hintsAndWarnings,omitempty"`
	DrugInformation           *DrugInformation           `json:"drugInformation,omitempty"`
	PackagingInformation      *PackagingInformation      `json:"packagingInformation,omitempty"`
	PriceInformation          *PriceInformation          `json:"priceInformation,omitempty"`
	TextInformation           *TextInformation           `json:"textInformation,omitempty"`
	ColorCategory             *ColorCategory             `json:"colorCategory,omitempty"`
	MedicationPlanInformation *MedicationPlanInformation `json:"medicationPlanInformation,omitempty"`
	PackageExtend             *common3.PackageExtend     `json:"packageExtend"`
}

type ColorCategory struct {
	Sorting         int32  `json:"sorting"`
	DrugCategory    string `json:"drugCategory"`
	IsInPriscusList *bool  `json:"isInPriscusList,inPriscusList,omitempty"`
}

type PackageSize struct {
	PackingComponent int64  `json:"packingComponent"`
	Classification   int64  `json:"classification"`
	Nop              string `json:"nop"`
}

type PackagingInformation struct {
	Quantity       string      `json:"quantity"`
	Unit           string      `json:"unit"`
	PackageSize    PackageSize `json:"packageSize"`
	AmountText     string      `json:"amountText"`
	QuantityNumber *int32      `json:"quantityNumber"`
	NameRecipe     string      `json:"nameRecipe"`
}

type PriceList struct {
	PriceType *string  `json:"priceType"`
	Value     *float64 `json:"value"`
}

type PriceInformation struct {
	PharmacySalePrice     float64                   `json:"pharmacySalePrice"`
	Discount              map[string]DiscountDetail `json:"discount"`
	CopaymentSorting      float64                   `json:"copaymentSorting"`
	Copayment             float64                   `json:"copayment"`
	TotalPayment          int64                     `json:"totalPayment"`
	TotalCoPayment        *float64                  `json:"totalCoPayment"`
	AdditionalPayment     int64                     `json:"additionalPayment"`
	AdditionalCost        *float64                  `json:"additionalCost"`
	PriceComparisonGroup1 int32                     `json:"priceComparisonGroup1"`
	PriceComparisonGroup2 int32                     `json:"priceComparisonGroup2"`
	IsFreeCopayment       *bool                     `json:"isFreeCopayment"`
	Pricelist             []PriceList               `json:"pricelist"`
	FixedAmount           *int64                    `json:"fixedAmount"`
}

type DiscountDetail struct {
	AdditionalPaymentIndicator int64 `json:"additionalPaymentIndicator"`
	DiscountFactor             int64 `json:"discountFactor"`
	PreferAutIdem              int64 `json:"preferAutIdem"`
}

type Divisible struct {
	DIVISIBILITYTYPECODE string `json:"dIVISIBILITYTYPECODE"`
	DIVISIBLE2_FLAG      int32  `json:"dIVISIBLE2_FLAG"`
	DIVISIBLE3_FLAG      int32  `json:"dIVISIBLE3_FLAG"`
	DIVISIBLE4_FLAG      int32  `json:"dIVISIBLE4_FLAG"`
	DIVISIBLE_FLAG       int32  `json:"dIVISIBLE_FLAG"`
}

type BENIFIT_ASSESSMENT_PATIENT_GROUP_LIST struct {
	ID           string `json:"iD"`
	DOCUMENT_ID  int64  `json:"dOCUMENT_ID"`
	DATUM_BE_VOM string `json:"dATUM_BE_VOM"`
	DATUM_BE_BIS string `json:"dATUM_BE_BIS"`
	NAME_PAT_GR  string `json:"nAME_PAT_GR"`
}

type GPA struct {
	AWG                                   string                                  `json:"aWG"`
	ID                                    int32                                   `json:"iD"`
	ID_BE_AKZ                             string                                  `json:"iD_BE_AKZ"`
	QS_ATMP                               int32                                   `json:"qS_ATMP"`
	REG_NB                                string                                  `json:"rEG_NB"`
	SOND_ZUL_ATMP                         int32                                   `json:"sOND_ZUL_ATMP"`
	SOND_ZUL_AUSN                         int32                                   `json:"sOND_ZUL_AUSN"`
	SOND_ZUL_BESOND                       int32                                   `json:"sOND_ZUL_BESOND"`
	SOND_ZUL_ORPHAN                       int32                                   `json:"sOND_ZUL_ORPHAN"`
	UES_BE                                string                                  `json:"uES_BE"`
	URL                                   string                                  `json:"uRL"`
	URL_QS_ATMP                           string                                  `json:"uRL_QS_ATMP"`
	URL_QS_ATMP_TEXT                      string                                  `json:"uRL_QS_ATMP_TEXT"`
	URL_TEXT                              string                                  `json:"uRL_TEXT"`
	DocumentIds                           []int32                                 `json:"documentIds"`
	BENIFIT_ASSESSMENT_PATIENT_GROUP_LIST []BENIFIT_ASSESSMENT_PATIENT_GROUP_LIST `json:"bENIFIT_ASSESSMENT_PATIENT_GROUP_LIST"`
}

type ARMDocument struct {
	FileName    string `json:"fileName"`
	AmrTypeCode string `json:"amrTypeCode"`
}

type ProductInformation struct {
	ShortName                              string       `json:"shortName"`
	Name                                   string       `json:"name"`
	Provider                               string       `json:"provider"`
	DosageForm                             string       `json:"dosageForm"`
	TReceipt                               int64        `json:"tReceipt"`
	NegativeList                           int64        `json:"negativeList"`
	ImportReimport                         bool         `json:"importReimport"`
	DocumentRequired                       int64        `json:"documentRequired"`
	LifeStyle                              int64        `json:"lifeStyle"`
	Prescribable                           int64        `json:"prescribable"`
	IsOTC                                  bool         `json:"isOTC"`
	ConditionalReimbursement               int64        `json:"conditionalReimbursement"`
	IsExcludedFromAppendixIII              bool         `json:"isExcludedFromAppendixIII"`
	IsExistingInGBA                        bool         `json:"isExistingInGBA"`
	PharmaciesAvailability                 int64        `json:"pharmaciesAvailability"`
	ProductGroup                           int64        `json:"productGroup"`
	CopaymentExemption                     int64        `json:"copaymentExemption"`
	CopaymentExemptionForBloodAndUrineTest int64        `json:"copaymentExemptionForBloodAndUrineTest"`
	LegalNotes                             []LegalNote  `json:"legalNotes"`
	FormType                               FormType     `json:"formType"`
	ActiveIngredients                      []string     `json:"activeIngredients"`
	OTXFlag                                int64        `json:"oTXFlag"`
	OrderComponents                        []string     `json:"orderComponents"`
	TransfusionlawFlag                     int64        `json:"transfusionlawFlag"`
	MedicineProductFlag                    int64        `json:"medicineProductFlag"`
	KReceipt                               bool         `json:"kReceipt"`
	AmrMessagesCount                       int32        `json:"amrMessagesCount"`
	ImportProductFlag                      int32        `json:"importProductFlag"`
	SampleProductFlag                      int32        `json:"sampleProductFlag"`
	PharmacyRequired                       bool         `json:"pharmacyRequired"`
	GPAs                                   []GPA        `json:"gPAs"`
	Divisible                              Divisible    `json:"divisible"`
	TechInformationId                      int64        `json:"techInformationId"`
	Id                                     int64        `json:"id"`
	ProviderID                             int64        `json:"providerID"`
	DosageFormCode                         string       `json:"dosageFormCode"`
	PharmFormCodeIFA                       string       `json:"pharmFormCodeIFA"`
	RegulationTypeCodes                    []string     `json:"regulationTypeCodes"`
	HasAmr3ConstraintFlag                  int64        `json:"hasAmr3ConstraintFlag"`
	HasAmr3ExclusionFlag                   int64        `json:"hasAmr3ExclusionFlag"`
	DispensingTypeCode                     string       `json:"dispensingTypeCode"`
	MedicineProductExceptionFlag           int64        `json:"medicineProductExceptionFlag"`
	PraxisbesonderheitDocument             *ARMDocument `json:"praxisbesonderheitDocument"`
	HasAmr1Flag                            int64        `json:"hasAmr1Flag"`
	IsDigaFlag                             bool         `json:"isDigaFlag"`
	IsNegativeList                         bool         `json:"isNegativeList"`
	IsLifeStyle                            bool         `json:"isLifeStyle"`
	IsBandage                              bool         `json:"isBandage"`
}

type Substances struct {
	SubstanceType       string  `json:"substanceType"`
	Rank                string  `json:"rank"`
	Unit                string  `json:"unit"`
	Amount              float64 `json:"amount"`
	EquivalentSubstance string  `json:"equivalentSubstance"`
	Suffix              string  `json:"suffix"`
	Name                string  `json:"name"`
}

type TextInfomationItem struct {
	CodeName string `json:"codeName"`
	Name     string `json:"name"`
	Content  string `json:"content"`
	Order    int64  `json:"order"`
}

type TextInformation struct {
	UpdatedDate string               `json:"updatedDate"`
	Items       []TextInfomationItem `json:"items"`
}

type Medicines struct {
	Medicines []Medicine `json:"medicines"`
}

type Sort struct {
	Field SortField    `json:"field"`
	Order common.Order `json:"order"`
}

type MedicationPlanInformation struct {
	MedicationPlanUnitCode string `json:"medicationPlanUnitCode"`
}

type AddToShoppingBagRequest struct {
	PatientId        *uuid.UUID              `json:"patientId"`
	DoctorId         *uuid.UUID              `json:"doctorId"`
	ContractId       *string                 `json:"contractId"`
	IkNumber         *int32                  `json:"ikNumber"`
	Medicine         MedicineShoppingBagInfo `json:"medicine" validate:"required"`
	Bsnr             *string                 `json:"bsnr"`
	AssignedToBsnrId *uuid.UUID              `json:"assignedToBsnrId"`
}

type MedicineShoppingBagInfo struct {
	Id                        *uuid.UUID                 `json:"id"`
	Type                      MedicineType               `json:"type" validate:"required"`
	Pzn                       *string                    `json:"pzn,omitempty"`
	Name                      string                     `json:"name"`
	Quantity                  int32                      `json:"quantity" validate:"min=1"`
	PackagingInformation      *PackagingInformation      `json:"packagingInformation,omitempty"`
	ProductInformation        *ProductInformation        `json:"productInformation,omitempty"`
	PatientId                 *uuid.UUID                 `json:"patientId,omitempty"`
	DoctorId                  *uuid.UUID                 `json:"doctorId,omitempty"`
	CurrentFormType           FormType                   `json:"currentFormType"`
	IntakeInterval            *IntakeInterval            `json:"intakeInterval,omitempty"`
	FormSetting               *string                    `json:"formSetting,omitempty"`
	AutIdem                   *bool                      `json:"autIdem,omitempty"`
	AsNeeded                  *bool                      `json:"asNeeded,omitempty"`
	FurtherInformation        *string                    `json:"furtherInformation,omitempty"`
	AvailableSizes            []AvailableSize            `json:"availableSizes,omitempty"`
	DrugInformation           *DrugInformation           `json:"drugInformation,omitempty"`
	TextInformation           *TextInformation           `json:"textInformation,omitempty"`
	PriceInformation          *PriceInformation          `json:"priceInformation,omitempty"`
	ColorCategory             *ColorCategory             `json:"colorCategory,omitempty"`
	SubstitutionPrescription  *bool                      `json:"substitutionPrescription"`
	SpecialExceedings         []SpecialExceeding         `json:"specialExceedings"`
	KBVMedicineId             *int32                     `json:"kBVMedicineId"`
	IsEPrescription           *bool                      `json:"isEPrescription"`
	MedicationPlanInformation *MedicationPlanInformation `json:"medicationPlanInformation,omitempty"`
	IsArtificialInsemination  *bool                      `json:"isArtificialInsemination,omitempty"`
	Bsnr                      *string                    `json:"bsnr"`
	Vaccinate                 *bool                      `json:"vaccinate"`
	DrugFormInformation       *string                    `json:"drugFormInformation"`
}

type AvailableSize struct {
	Pzn              string `json:"pzn"`
	Size             string `json:"size"`
	Unit             string `json:"unit"`
	Quantity         string `json:"quantity"`
	PackingComponent int64  `json:"packingComponent"`
	Classification   int64  `json:"classification"`
}

type IntakeInterval struct {
	Morning   *float64 `json:"morning,omitempty"`
	Evening   *float64 `json:"evening,omitempty"`
	Afternoon *float64 `json:"afternoon,omitempty"`
	Night     *float64 `json:"night,omitempty"`
	Freetext  *string  `json:"freetext,omitempty"`
	DJ        *bool    `json:"dJ,omitempty"`
}

type ShoppingBagResponse struct {
	Medicine MedicineShoppingBagInfo `json:"medicine"`
}

type UpdateShoppingBagQuantityRequest struct {
	PatientId  *uuid.UUID `json:"patientId"`
	DoctorId   *uuid.UUID `json:"doctorId"`
	MedicineId uuid.UUID  `json:"medicineId" validate:"required"`
	ContractId *string    `json:"contractId"`
	Quantity   int32      `json:"quantity" validate:"min=1"`
	Bsnr       *string    `json:"bsnr"`
}

type RemoveFromShoppingBagRequest struct {
	PatientId     *uuid.UUID `json:"patientId"`
	DoctorId      *uuid.UUID `json:"doctorId"`
	MedicineId    *uuid.UUID `json:"medicineId,omitempty"`
	ContractId    *string    `json:"contractId"`
	ShoppingBagId *uuid.UUID `json:"shoppingBagId,omitempty"`
	Bsnr          *string    `json:"bsnr,omitempty"`
}

type GetShoppingBagRequest struct {
	PatientId        *uuid.UUID `json:"patientId"`
	DoctorId         *uuid.UUID `json:"doctorId"`
	ContractId       *string    `json:"contractId"`
	Bsnr             *string    `json:"bsnr"`
	AssignedToBsnrId *uuid.UUID `json:"assignedToBsnrId"`
}

type GetShoppingBagRequestResponse struct {
	ShoppingBagId     uuid.UUID                 `json:"shoppingBagId"`
	PatientId         uuid.UUID                 `json:"patientId"`
	DoctorId          uuid.UUID                 `json:"doctorId"`
	TreatmentDoctorId uuid.UUID                 `json:"treatmentDoctorId"`
	Medicines         []MedicineShoppingBagInfo `json:"medicines"`
	Bsnr              string                    `json:"bsnr"`
	AssignedToBsnrId  *uuid.UUID                `json:"assignedToBsnrId"`
}

type GetSubsitutionResponse struct {
	Subsitutions []SubsitutionResponse `json:"subsitutions"`
}

type SubsitutionResponse struct {
	Key       string     `json:"key"`
	Total     int32      `json:"total"`
	ATCA      string     `json:"aTCA"`
	ATCName   string     `json:"aTCName"`
	Medicines []Medicine `json:"medicines"`
}

type EventShoppingBagRequest struct {
	Type               ShoppingBagEventType    `json:"type"`
	Payload            MedicineShoppingBagInfo `json:"payload"`
	TreatmentDoctorId  *uuid.UUID              `json:"treatmentDoctorId"`
	ContractId         *string                 `json:"contractId"`
	MedicineDeletedIds []uuid.UUID             `json:"medicineDeletedIds"`
	AssignedToBsnrId   *uuid.UUID              `json:"assignedToBsnrId"`
}

type CheckMissingDiagnoseResponse struct {
	ShowWarning bool `json:"showWarning"`
}

type AvailableSizeRequest struct {
	ShortName string `json:"shortName"`
	Provider  string `json:"provider"`
}

type UpdateFormRequest struct {
	PatientId       uuid.UUID   `json:"patientId" validate:"required"`
	DoctorId        uuid.UUID   `json:"doctorId" validate:"required"`
	MedicineIDs     []uuid.UUID `json:"medicineIDs" validate:"required"`
	CurrentFormType FormType    `json:"currentFormType" validate:"required"`
	ContractId      *string     `json:"contractId"`
}

type PrescribeRequest struct {
	PatientId                *uuid.UUID  `json:"patientId,omitempty"`
	DoctorId                 *uuid.UUID  `json:"doctorId,omitempty"`
	FormInfos                []FormInfo  `json:"formInfos" validate:"required"`
	ContractId               *string     `json:"contractId"`
	TreatmentDoctorId        *uuid.UUID  `json:"treatmentDoctorId"`
	EncounterCase            *string     `json:"encounterCase"`
	MedicineAutIdemIds       []uuid.UUID `json:"medicineAutIdemIds"`
	ScheinId                 *uuid.UUID  `json:"scheinId"`
	Bsnr                     *string     `json:"bsnr,omitempty"`
	HasSupportForm907        *bool       `json:"hasSupportForm907"`
	AssignedToBsnrId         *uuid.UUID  `json:"assignedToBsnrId"`
	PreventGetPatientProfile *bool       `json:"preventGetPatientProfile"`
}

type DateRange struct {
	StartDate int64  `json:"startDate"`
	EndDate   *int64 `json:"endDate"`
}

type FormInfo struct {
	MedicineIDs                  []uuid.UUID            `json:"medicineIDs" validate:"required"`
	FormSetting                  string                 `json:"formSetting" validate:"trimjson"`
	CurrentFormType              FormType               `json:"currentFormType"`
	PrescribeDate                *int64                 `json:"prescribeDate"`
	IsShowFavHint                bool                   `json:"isShowFavHint"`
	PrintDate                    *int64                 `json:"printDate"`
	EPrescription                *common3.EPrescription `json:"ePrescription"`
	PrintOption                  *common1.PrintOption   `json:"printOption"`
	BundleUrl                    *string                `json:"bundleUrl"`
	PdfUrl                       *string                `json:"pdfUrl"`
	HasChangedSprechsundenbedarf *bool                  `json:"hasChangedSprechsundenbedarf"`
}

type FormInfoResponse struct {
	Id                           uuid.UUID                 `json:"id"`
	FormInfoResponse             []MedicineShoppingBagInfo `json:"formInfoResponse"`
	FormSetting                  string                    `json:"formSetting"`
	CurrentFormType              FormType                  `json:"currentFormType"`
	PrescribeDate                *int64                    `json:"prescribeDate"`
	IsNotPicked                  bool                      `json:"isNotPicked"`
	IsShowFavHint                bool                      `json:"isShowFavHint"`
	EPrescription                *common3.EPrescription    `json:"ePrescription"`
	BundleUrl                    *string                   `json:"bundleUrl"`
	HasChangedSprechsundenbedarf *bool                     `json:"hasChangedSprechsundenbedarf"`
	ERezeptStatus                *common2.DocumentStatus   `json:"eRezeptStatus"`
}

type PrintResult struct {
	CurrentFormType FormType `json:"currentFormType"`
	FormUrl         string   `json:"formUrl"`
}

type PrescribeResponse struct {
	Id                uuid.UUID          `json:"id"`
	PatientId         uuid.UUID          `json:"patientId"`
	DoctorId          uuid.UUID          `json:"doctorId"`
	FormInfoResponses []FormInfoResponse `json:"formInfoResponses"`
	PrintDate         int64              `json:"printDate"`
	ContractId        string             `json:"contractId"`
	TreatmentDoctorId uuid.UUID          `json:"treatmentDoctorId"`
	CreatedDate       int64              `json:"createdDate"`
	EncounterId       uuid.UUID          `json:"encounterId"`
	PrintResults      []PrintResult      `json:"printResults"`
}

type UpdateShoppingBagInformationRequest struct {
	PatientId                *uuid.UUID          `json:"patientId"`
	DoctorId                 *uuid.UUID          `json:"doctorId"`
	ContractId               *string             `json:"contractId"`
	MedicineId               *uuid.UUID          `json:"medicineId" validate:"required_with=FurtherInformation IntakeInterval PackageSizeRequest AsNeeded"`
	FurtherInformation       *string             `json:"furtherInformation"`
	IntakeInterval           *IntakeInterval     `json:"intakeInterval"`
	PackageSizeRequest       *PackageSizeRequest `json:"packageSizeRequest"`
	TreatmentDoctorId        *uuid.UUID          `json:"treatmentDoctorId"`
	AsNeeded                 *bool               `json:"asNeeded"`
	FreeText                 *string             `json:"freeText"`
	SubstitutionPrescription *bool               `json:"substitutionPrescription"`
	SpecialExceedings        []SpecialExceeding  `json:"specialExceedings"`
	IsEPrescription          *bool               `json:"isEPrescription"`
	IsArtificialInsemination *bool               `json:"isArtificialInsemination"`
	Bsnr                     *string             `json:"bsnr"`
	Vaccinate                *bool               `json:"vaccinate"`
	DrugFormInformation      *string             `json:"drugFormInformation"`
	AssignedToBsnrId         *uuid.UUID          `json:"assignedToBsnrId"`
}

type PackageSizeRequest struct {
	Pzn string `json:"pzn"`
}

type GetMedicationPrescribeRequest struct {
	PatientId *uuid.UUID            `json:"patientId"`
	Bsnr      *string               `json:"bsnr"`
	SortField SortFieldConsultation `json:"sortField"`
	Order     common.Order          `json:"order"`
}

type MedicationPrescribeResponse struct {
	Id                        uuid.UUID                  `json:"id"`
	Type                      MedicineType               `json:"type"`
	Pzn                       *string                    `json:"pzn,omitempty"`
	Name                      string                     `json:"name"`
	PackagingInformation      *PackagingInformation      `json:"packagingInformation,omitempty"`
	ProductInformation        *ProductInformation        `json:"productInformation,omitempty"`
	IntakeInterval            IntakeInterval             `json:"intakeInterval"`
	FurtherInformation        string                     `json:"furtherInformation"`
	DrugInformation           *DrugInformation           `json:"drugInformation,omitempty"`
	PrescribeDate             int64                      `json:"prescribeDate"`
	TreatmentDoctorId         uuid.UUID                  `json:"treatmentDoctorId"`
	TextInformation           *TextInformation           `json:"textInformation,omitempty"`
	MedicationPlanId          *uuid.UUID                 `json:"medicationPlanId"`
	AsNeeded                  *bool                      `json:"asNeeded,omitempty"`
	ContractId                *string                    `json:"contractId"`
	PriceInformation          *PriceInformation          `json:"priceInformation,omitempty"`
	ColorCategory             *ColorCategory             `json:"colorCategory,omitempty"`
	AutIdem                   bool                       `json:"autIdem"`
	Quantity                  int32                      `json:"quantity"`
	CurrentFormType           FormType                   `json:"currentFormType"`
	FormInfoId                uuid.UUID                  `json:"formInfoId"`
	SubstitutionPrescription  *bool                      `json:"substitutionPrescription"`
	SpecialExceedings         []SpecialExceeding         `json:"specialExceedings"`
	KBVMedicineId             *int32                     `json:"kBVMedicineId"`
	MedicationPlanInformation *MedicationPlanInformation `json:"medicationPlanInformation,omitempty"`
	IsEPrescription           bool                       `json:"isEPrescription"`
	PrintDate                 *int64                     `json:"printDate"`
	FixedAmount               *int64                     `json:"fixedAmount"`
	IsFavourite               bool                       `json:"isFavourite"`
	FormInfo                  FormInfoResponse           `json:"formInfo"`
	AssignedToBsnrId          *uuid.UUID                 `json:"assignedToBsnrId"`
	IsArtificialInsemination  bool                       `json:"isArtificialInsemination"`
	Vaccinate                 bool                       `json:"vaccinate"`
}

type GetMedicationPrescribeResponse struct {
	PatientId                    uuid.UUID                     `json:"patientId"`
	Bsnr                         string                        `json:"bsnr"`
	MedicationPrescribeResponses []MedicationPrescribeResponse `json:"medicationPrescribeResponses"`
}

type EventMedicationPrescribe struct {
	EventType  EventMedicationPrescribeType `json:"eventType"`
	Payload    MedicationPrescribeResponse  `json:"payload"`
	PatientId  uuid.UUID                    `json:"patientId"`
	DoctorId   uuid.UUID                    `json:"doctorId"`
	ContractId *string                      `json:"contractId"`
	Bsnr       *string                      `json:"bsnr"`
}

type DeleteMedicationPrescribeRequest struct {
	Id        uuid.UUID `json:"id" validate:"required"`
	PatientId uuid.UUID `json:"patientId" validate:"required"`
}

type DeleteMedicationPrescribeResponse struct {
	Id        uuid.UUID `json:"id"`
	PatientId uuid.UUID `json:"patientId"`
}

type CreateMedicationPlanRequest struct {
	PrescribedMedicationId uuid.UUID `json:"prescribedMedicationId" validate:"required"`
	Hint                   string    `json:"hint"`
}

type EventMedicationPlanChanged struct {
	EventType     EventMedicationPlanChangedType `json:"eventType"`
	Payload       bmp.EntryResponse              `json:"payload"`
	EncounterCase string                         `json:"encounterCase"`
	ContractType  *common.ContractType           `json:"contractType"`
	PatientId     uuid.UUID                      `json:"patientId"`
}

type DeleteMedicationPlanRequest struct {
	MedicationPlanId uuid.UUID `json:"medicationPlanId" validate:"required"`
	PatientId        uuid.UUID `json:"patientId"`
	DoctorId         uuid.UUID `json:"doctorId"`
	ContractId       *string   `json:"contractId"`
	EncounterCase    string    `json:"encounterCase"`
}

type DeleteMedicationPlanResponse struct {
	Id        uuid.UUID `json:"id"`
	PatientId uuid.UUID `json:"patientId"`
}

type CheckMissingDiagnosesRequest struct {
	PatientId  uuid.UUID `json:"patientId" validate:"required"`
	DoctorId   uuid.UUID `json:"doctorId" validate:"required"`
	ContractId *string   `json:"contractId"`
	IkNumber   *int32    `json:"ikNumber"`
	Pzns       []string  `json:"pzns" validate:"required"`
}

type CheckMissingDiagnosesResponse struct {
	Pzns []string `json:"pzns"`
}

type EventRefillMedicine struct {
	PatientId     uuid.UUID                 `json:"patientId"`
	MedicineInfos []MedicineShoppingBagInfo `json:"medicineInfos"`
}

type ViewMedicationForm struct {
	TreatmentDoctorId uuid.UUID        `json:"treatmentDoctorId"`
	FormInfo          FormInfoResponse `json:"formInfo"`
	AssignedToBsnrId  uuid.UUID        `json:"assignedToBsnrId"`
}

type EventViewMedicationForm struct {
	PatientId          uuid.UUID              `json:"patientId"`
	ViewMedicationForm ViewMedicationForm     `json:"viewMedicationForm"`
	EventType          ViewMedicationFormType `json:"eventType"`
}

type UpdateTreatmentDoctorMedicationFormRequest struct {
	MedicationFormId  uuid.UUID  `json:"medicationFormId" validate:"required"`
	TreatmentDoctorId uuid.UUID  `json:"treatmentDoctorId" validate:"required"`
	PatientId         uuid.UUID  `json:"patientId" validate:"required"`
	AssignedToBsnrId  *uuid.UUID `json:"assignedToBsnrId"`
}

type PrintFormRequest struct {
	FormId                   uuid.UUID           `json:"formId" validate:"required"`
	PrintOption              common1.PrintOption `json:"printOption" validate:"required"`
	HasSupportForm907        *bool               `json:"hasSupportForm907"`
	PatientId                uuid.UUID           `json:"patientId"`
	PreventGetPatientProfile *bool               `json:"preventGetPatientProfile"`
}

type PrintFormResponse struct {
	PrintResults []PrintResult `json:"printResults"`
}

// enum definitions
type SortField string

const (
	Size         SortField = "Size"
	Price        SortField = "Price"
	TotalPayment SortField = "TotalPayment"
)

type SpecialExceeding string

const (
	A  SpecialExceeding = "A"
	N  SpecialExceeding = "N"
	S  SpecialExceeding = "S"
	SZ SpecialExceeding = "SZ"
	ST SpecialExceeding = "ST"
)

type FormType string

const (
	KREZ          FormType = "KREZ"
	GREZ          FormType = "GREZ"
	BTM           FormType = "BTM"
	TPrescription FormType = "TPrescription"
	Private       FormType = "Private"
	AOKNordwet    FormType = "AOKNordwet"
	AOKBremen     FormType = "AOKBremen"
	Muster16aBay  FormType = "Muster16aBay"
)

type MedicineType string

const (
	FreeText MedicineType = "FreeText"
	HPM      MedicineType = "HPM"
	KBV      MedicineType = "KBV"
)

type ShoppingBagEventType string

const (
	Add               ShoppingBagEventType = "Add"
	Update            ShoppingBagEventType = "Update"
	Delete            ShoppingBagEventType = "Delete"
	UpdateForm        ShoppingBagEventType = "UpdateForm"
	UpdateDoctorId    ShoppingBagEventType = "UpdateDoctorId"
	DeleteShoppingBag ShoppingBagEventType = "DeleteShoppingBag"
)

type SpecialIdentifier string

const (
	SpecialIdentifierA  SpecialIdentifier = "A - Überschreitung der Höchstverschreibungsmenge innerhalb von 30 Tagen"
	SpecialIdentifierN  SpecialIdentifier = "N - Notfall Verschreibung"
	SpecialIdentifierS  SpecialIdentifier = "S - Substitutionsmittel Verschreibung"
	SpecialIdentifierSZ SpecialIdentifier = "SZ - § 5 Absatz 8 BtMVV"
	SpecialIdentifierST SpecialIdentifier = "ST - § 5 Absatz 9 BtMVV"
)

type SortFieldConsultation string

const (
	PrescribeDate SortFieldConsultation = "PrescribeDate"
	Tradename     SortFieldConsultation = "Tradename"
	SizeMed       SortFieldConsultation = "SizeMed"
	PrescribedBy  SortFieldConsultation = "PrescribedBy"
	Status        SortFieldConsultation = "Status"
)

type EventMedicationPrescribeType string

const (
	Remove                        EventMedicationPrescribeType = "Remove"
	PrescribeSuccess              EventMedicationPrescribeType = "PrescribeSuccess"
	RemovePrescribeMedicationPlan EventMedicationPrescribeType = "RemovePrescribeMedicationPlan"
	CreateMedicationPlanSuccess   EventMedicationPrescribeType = "CreateMedicationPlanSuccess"
)

type EventMedicationPlanChangedType string

const (
	RemoveMedicationPlan EventMedicationPlanChangedType = "RemoveMedicationPlan"
	CreateMedicationPlan EventMedicationPlanChangedType = "CreateMedicationPlan"
	UpdateMedicationPlan EventMedicationPlanChangedType = "UpdateMedicationPlan"
)

type ViewMedicationFormType string

const (
	ViewForm              ViewMedicationFormType = "ViewForm"
	ChangeTreatmentDoctor ViewMedicationFormType = "ChangeTreatmentDoctor"
)

// Define constants
const NATS_SUBJECT = "api.app.mvz" // nats subject this service will listen to

// service event constants
const EVENT_HandleEventShoppingBagChanged = "api.app.mvz.MedicineApp.HandleEventShoppingBagChanged"
const LEGACY_TOPIC_HandleEventShoppingBagChanged = "/api/app/mvz/medicine/handleEventShoppingBagChanged"
const EVENT_HandleEventRefillMedicine = "api.app.mvz.MedicineApp.HandleEventRefillMedicine"
const LEGACY_TOPIC_HandleEventRefillMedicine = "/api/app/mvz/medicine/handleEventRefillMedicine"
const EVENT_GetShoppingBag = "api.app.mvz.MedicineApp.GetShoppingBag"
const LEGACY_TOPIC_GetShoppingBag = "/api/app/mvz/medicine/getShoppingBag"
const EVENT_AddToShoppingBag = "api.app.mvz.MedicineApp.AddToShoppingBag"
const LEGACY_TOPIC_AddToShoppingBag = "/api/app/mvz/medicine/addToShoppingBag"
const EVENT_CheckMissingDiagnose = "api.app.mvz.MedicineApp.CheckMissingDiagnose"
const LEGACY_TOPIC_CheckMissingDiagnose = "/api/app/mvz/medicine/checkMissingDiagnose"
const EVENT_UpdateShoppingBagQuantity = "api.app.mvz.MedicineApp.UpdateShoppingBagQuantity"
const LEGACY_TOPIC_UpdateShoppingBagQuantity = "/api/app/mvz/medicine/updateShoppingBagQuantity"
const EVENT_UpdateShoppingBagInformation = "api.app.mvz.MedicineApp.UpdateShoppingBagInformation"
const LEGACY_TOPIC_UpdateShoppingBagInformation = "/api/app/mvz/medicine/updateShoppingBagInformation"
const EVENT_RemoveFromShoppingBag = "api.app.mvz.MedicineApp.RemoveFromShoppingBag"
const LEGACY_TOPIC_RemoveFromShoppingBag = "/api/app/mvz/medicine/removeFromShoppingBag"
const EVENT_UpdateForm = "api.app.mvz.MedicineApp.UpdateForm"
const LEGACY_TOPIC_UpdateForm = "/api/app/mvz/medicine/updateForm"
const EVENT_Prescribe = "api.app.mvz.MedicineApp.Prescribe"
const LEGACY_TOPIC_Prescribe = "/api/app/mvz/medicine/prescribe"
const EVENT_GetMedicationPrescribe = "api.app.mvz.MedicineApp.GetMedicationPrescribe"
const LEGACY_TOPIC_GetMedicationPrescribe = "/api/app/mvz/medicine/getMedicationPrescribe"
const EVENT_DeleteMedicationPrescribe = "api.app.mvz.MedicineApp.DeleteMedicationPrescribe"
const LEGACY_TOPIC_DeleteMedicationPrescribe = "/api/app/mvz/medicine/deleteMedicationPrescribe"
const EVENT_CheckMissingDiagnoses = "api.app.mvz.MedicineApp.CheckMissingDiagnoses"
const LEGACY_TOPIC_CheckMissingDiagnoses = "/api/app/mvz/medicine/checkMissingDiagnoses"
const EVENT_HandleEventMedicationPrescribeChanged = "api.app.mvz.MedicineApp.HandleEventMedicationPrescribeChanged"
const LEGACY_TOPIC_HandleEventMedicationPrescribeChanged = "/api/app/mvz/medicine/handleEventMedicationPrescribeChanged"
const EVENT_CreateMedicationPlan = "api.app.mvz.MedicineApp.CreateMedicationPlan"
const LEGACY_TOPIC_CreateMedicationPlan = "/api/app/mvz/medicine/createMedicationPlan"
const EVENT_HandleEventMedicationPlanChanged = "api.app.mvz.MedicineApp.HandleEventMedicationPlanChanged"
const LEGACY_TOPIC_HandleEventMedicationPlanChanged = "/api/app/mvz/medicine/handleEventMedicationPlanChanged"
const EVENT_DeleteMedicationPlan = "api.app.mvz.MedicineApp.DeleteMedicationPlan"
const LEGACY_TOPIC_DeleteMedicationPlan = "/api/app/mvz/medicine/deleteMedicationPlan"
const EVENT_HandleEventViewMedicationForm = "api.app.mvz.MedicineApp.HandleEventViewMedicationForm"
const LEGACY_TOPIC_HandleEventViewMedicationForm = "/api/app/mvz/medicine/handleEventViewMedicationForm"
const EVENT_UpdateTreatmentDoctorMedicationForm = "api.app.mvz.MedicineApp.UpdateTreatmentDoctorMedicationForm"
const LEGACY_TOPIC_UpdateTreatmentDoctorMedicationForm = "/api/app/mvz/medicine/updateTreatmentDoctorMedicationForm"
const EVENT_PrintForm = "api.app.mvz.MedicineApp.PrintForm"
const LEGACY_TOPIC_PrintForm = "/api/app/mvz/medicine/printForm"
const EVENT_OnTimelineHardDelete = "api.app.mvz.MedicineApp.OnTimelineHardDelete"
const LEGACY_TOPIC_OnTimelineHardDelete = "/api/app/mvz/medicine/onTimelineHardDelete"

// message event constants
const EVENT_ShoppingBagRequest = "api.app.mvz.AppMvzMedicine.ShoppingBagRequest"
const EVENT_MedicationPrescribe = "api.app.mvz.AppMvzMedicine.MedicationPrescribe"
const EVENT_MedicationPlanChanged = "api.app.mvz.AppMvzMedicine.MedicationPlanChanged"
const EVENT_RefillMedicine = "api.app.mvz.AppMvzMedicine.RefillMedicine"
const EVENT_ViewMedicationForm = "api.app.mvz.AppMvzMedicine.ViewMedicationForm"

// Define service interface -------------------------------------------------------------
type MedicineApp interface {
	HandleEventShoppingBagChanged(ctx *titan.Context, request EventShoppingBagRequest) error
	HandleEventRefillMedicine(ctx *titan.Context, request EventRefillMedicine) error
	GetShoppingBag(ctx *titan.Context, request GetShoppingBagRequest) (*GetShoppingBagRequestResponse, error)
	AddToShoppingBag(ctx *titan.Context, request AddToShoppingBagRequest) (*ShoppingBagResponse, error)
	CheckMissingDiagnose(ctx *titan.Context, request AddToShoppingBagRequest) (*CheckMissingDiagnoseResponse, error)
	UpdateShoppingBagQuantity(ctx *titan.Context, request UpdateShoppingBagQuantityRequest) (*ShoppingBagResponse, error)
	UpdateShoppingBagInformation(ctx *titan.Context, request UpdateShoppingBagInformationRequest) (*ShoppingBagResponse, error)
	RemoveFromShoppingBag(ctx *titan.Context, request RemoveFromShoppingBagRequest) (*ShoppingBagResponse, error)
	UpdateForm(ctx *titan.Context, request UpdateFormRequest) error
	Prescribe(ctx *titan.Context, request PrescribeRequest) (*PrescribeResponse, error)
	GetMedicationPrescribe(ctx *titan.Context, request GetMedicationPrescribeRequest) (*GetMedicationPrescribeResponse, error)
	DeleteMedicationPrescribe(ctx *titan.Context, request DeleteMedicationPrescribeRequest) (*DeleteMedicationPrescribeResponse, error)
	CheckMissingDiagnoses(ctx *titan.Context, request CheckMissingDiagnosesRequest) (*CheckMissingDiagnosesResponse, error)
	HandleEventMedicationPrescribeChanged(ctx *titan.Context, request EventMedicationPrescribe) error
	CreateMedicationPlan(ctx *titan.Context, request CreateMedicationPlanRequest) error
	HandleEventMedicationPlanChanged(ctx *titan.Context, request EventMedicationPlanChanged) error
	DeleteMedicationPlan(ctx *titan.Context, request DeleteMedicationPlanRequest) (*DeleteMedicationPlanResponse, error)
	HandleEventViewMedicationForm(ctx *titan.Context, request EventViewMedicationForm) error
	UpdateTreatmentDoctorMedicationForm(ctx *titan.Context, request UpdateTreatmentDoctorMedicationFormRequest) error
	PrintForm(ctx *titan.Context, request PrintFormRequest) (*PrintFormResponse, error)
	OnTimelineHardDelete(ctx *titan.Context, request timeline.EventTimelineHardRemove) error
}

// Define service proxy -------------------------------------------------------------------
type MedicineAppProxy struct {
	service MedicineApp
}

func (srv *MedicineAppProxy) HandleEventShoppingBagChanged(ctx *titan.Context, request EventShoppingBagRequest) error {
	return srv.service.HandleEventShoppingBagChanged(ctx, request)
}
func (srv *MedicineAppProxy) HandleEventRefillMedicine(ctx *titan.Context, request EventRefillMedicine) error {
	return srv.service.HandleEventRefillMedicine(ctx, request)
}
func (srv *MedicineAppProxy) GetShoppingBag(ctx *titan.Context, request GetShoppingBagRequest) (*GetShoppingBagRequestResponse, error) {
	return srv.service.GetShoppingBag(ctx, request)
}
func (srv *MedicineAppProxy) AddToShoppingBag(ctx *titan.Context, request AddToShoppingBagRequest) (*ShoppingBagResponse, error) {
	return srv.service.AddToShoppingBag(ctx, request)
}
func (srv *MedicineAppProxy) CheckMissingDiagnose(ctx *titan.Context, request AddToShoppingBagRequest) (*CheckMissingDiagnoseResponse, error) {
	return srv.service.CheckMissingDiagnose(ctx, request)
}
func (srv *MedicineAppProxy) UpdateShoppingBagQuantity(ctx *titan.Context, request UpdateShoppingBagQuantityRequest) (*ShoppingBagResponse, error) {
	return srv.service.UpdateShoppingBagQuantity(ctx, request)
}
func (srv *MedicineAppProxy) UpdateShoppingBagInformation(ctx *titan.Context, request UpdateShoppingBagInformationRequest) (*ShoppingBagResponse, error) {
	return srv.service.UpdateShoppingBagInformation(ctx, request)
}
func (srv *MedicineAppProxy) RemoveFromShoppingBag(ctx *titan.Context, request RemoveFromShoppingBagRequest) (*ShoppingBagResponse, error) {
	return srv.service.RemoveFromShoppingBag(ctx, request)
}
func (srv *MedicineAppProxy) UpdateForm(ctx *titan.Context, request UpdateFormRequest) error {
	return srv.service.UpdateForm(ctx, request)
}
func (srv *MedicineAppProxy) Prescribe(ctx *titan.Context, request PrescribeRequest) (*PrescribeResponse, error) {
	return srv.service.Prescribe(ctx, request)
}
func (srv *MedicineAppProxy) GetMedicationPrescribe(ctx *titan.Context, request GetMedicationPrescribeRequest) (*GetMedicationPrescribeResponse, error) {
	return srv.service.GetMedicationPrescribe(ctx, request)
}
func (srv *MedicineAppProxy) DeleteMedicationPrescribe(ctx *titan.Context, request DeleteMedicationPrescribeRequest) (*DeleteMedicationPrescribeResponse, error) {
	return srv.service.DeleteMedicationPrescribe(ctx, request)
}
func (srv *MedicineAppProxy) CheckMissingDiagnoses(ctx *titan.Context, request CheckMissingDiagnosesRequest) (*CheckMissingDiagnosesResponse, error) {
	return srv.service.CheckMissingDiagnoses(ctx, request)
}
func (srv *MedicineAppProxy) HandleEventMedicationPrescribeChanged(ctx *titan.Context, request EventMedicationPrescribe) error {
	return srv.service.HandleEventMedicationPrescribeChanged(ctx, request)
}
func (srv *MedicineAppProxy) CreateMedicationPlan(ctx *titan.Context, request CreateMedicationPlanRequest) error {
	return srv.service.CreateMedicationPlan(ctx, request)
}
func (srv *MedicineAppProxy) HandleEventMedicationPlanChanged(ctx *titan.Context, request EventMedicationPlanChanged) error {
	return srv.service.HandleEventMedicationPlanChanged(ctx, request)
}
func (srv *MedicineAppProxy) DeleteMedicationPlan(ctx *titan.Context, request DeleteMedicationPlanRequest) (*DeleteMedicationPlanResponse, error) {
	return srv.service.DeleteMedicationPlan(ctx, request)
}
func (srv *MedicineAppProxy) HandleEventViewMedicationForm(ctx *titan.Context, request EventViewMedicationForm) error {
	return srv.service.HandleEventViewMedicationForm(ctx, request)
}
func (srv *MedicineAppProxy) UpdateTreatmentDoctorMedicationForm(ctx *titan.Context, request UpdateTreatmentDoctorMedicationFormRequest) error {
	return srv.service.UpdateTreatmentDoctorMedicationForm(ctx, request)
}
func (srv *MedicineAppProxy) PrintForm(ctx *titan.Context, request PrintFormRequest) (*PrintFormResponse, error) {
	return srv.service.PrintForm(ctx, request)
}
func (srv *MedicineAppProxy) OnTimelineHardDelete(ctx *titan.Context, request timeline.EventTimelineHardRemove) error {
	return srv.service.OnTimelineHardDelete(ctx, request)
}

// Define service router -----------------------------------------------------------------
type MedicineAppRouter struct {
	proxy *MedicineAppProxy
}

func (router *MedicineAppRouter) Register(r titan.Router) {
	r.RegisterTopic(EVENT_GetShoppingBag, router.proxy.GetShoppingBag, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetShoppingBag, router.proxy.GetShoppingBag, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_AddToShoppingBag, router.proxy.AddToShoppingBag, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_AddToShoppingBag, router.proxy.AddToShoppingBag, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_CheckMissingDiagnose, router.proxy.CheckMissingDiagnose, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_CheckMissingDiagnose, router.proxy.CheckMissingDiagnose, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_UpdateShoppingBagQuantity, router.proxy.UpdateShoppingBagQuantity, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_UpdateShoppingBagQuantity, router.proxy.UpdateShoppingBagQuantity, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_UpdateShoppingBagInformation, router.proxy.UpdateShoppingBagInformation, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_UpdateShoppingBagInformation, router.proxy.UpdateShoppingBagInformation, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_RemoveFromShoppingBag, router.proxy.RemoveFromShoppingBag, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_RemoveFromShoppingBag, router.proxy.RemoveFromShoppingBag, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_UpdateForm, router.proxy.UpdateForm, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_UpdateForm, router.proxy.UpdateForm, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_Prescribe, router.proxy.Prescribe, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_Prescribe, router.proxy.Prescribe, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetMedicationPrescribe, router.proxy.GetMedicationPrescribe, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetMedicationPrescribe, router.proxy.GetMedicationPrescribe, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_DeleteMedicationPrescribe, router.proxy.DeleteMedicationPrescribe, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_DeleteMedicationPrescribe, router.proxy.DeleteMedicationPrescribe, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_CheckMissingDiagnoses, router.proxy.CheckMissingDiagnoses, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_CheckMissingDiagnoses, router.proxy.CheckMissingDiagnoses, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_CreateMedicationPlan, router.proxy.CreateMedicationPlan, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_CreateMedicationPlan, router.proxy.CreateMedicationPlan, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_DeleteMedicationPlan, router.proxy.DeleteMedicationPlan, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_DeleteMedicationPlan, router.proxy.DeleteMedicationPlan, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_UpdateTreatmentDoctorMedicationForm, router.proxy.UpdateTreatmentDoctorMedicationForm, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_UpdateTreatmentDoctorMedicationForm, router.proxy.UpdateTreatmentDoctorMedicationForm, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_PrintForm, router.proxy.PrintForm, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_PrintForm, router.proxy.PrintForm, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
}

// Subscriber
func (router *MedicineAppRouter) Subscribe(s *titan.MessageSubscriber) {
	s.Register(EVENT_ShoppingBagRequest, "api.app.mvz_MedicineApp_HandleEventShoppingBagChanged_Queue", func(p *titan.Message) error {
		var resp EventShoppingBagRequest
		ctx, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		return router.proxy.HandleEventShoppingBagChanged(ctx, resp)
	})
	s.Register(EVENT_RefillMedicine, "api.app.mvz_MedicineApp_HandleEventRefillMedicine_Queue", func(p *titan.Message) error {
		var resp EventRefillMedicine
		ctx, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		return router.proxy.HandleEventRefillMedicine(ctx, resp)
	})
	s.Register(EVENT_MedicationPrescribe, "api.app.mvz_MedicineApp_HandleEventMedicationPrescribeChanged_Queue", func(p *titan.Message) error {
		var resp EventMedicationPrescribe
		ctx, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		return router.proxy.HandleEventMedicationPrescribeChanged(ctx, resp)
	})
	s.Register(EVENT_MedicationPlanChanged, "api.app.mvz_MedicineApp_HandleEventMedicationPlanChanged_Queue", func(p *titan.Message) error {
		var resp EventMedicationPlanChanged
		ctx, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		return router.proxy.HandleEventMedicationPlanChanged(ctx, resp)
	})
	s.Register(EVENT_ViewMedicationForm, "api.app.mvz_MedicineApp_HandleEventViewMedicationForm_Queue", func(p *titan.Message) error {
		var resp EventViewMedicationForm
		ctx, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		return router.proxy.HandleEventViewMedicationForm(ctx, resp)
	})
	s.Register("api.app.mvz.AppMvzTimeline.TimelineHardRemove", "api.app.mvz_MedicineApp_OnTimelineHardDelete_Queue", func(p *titan.Message) error {
		var resp timeline.EventTimelineHardRemove
		ctx, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		return router.proxy.OnTimelineHardDelete(ctx, resp)
	})
}

func NewMedicineAppRouter(s MedicineApp) *MedicineAppRouter {
	p := &MedicineAppProxy{s}
	return &MedicineAppRouter{p}
}

func NewServer(bff0 MedicineApp, options ...titan.Option) *titan.Server {
	opt := options

	router0 := NewMedicineAppRouter(bff0)
	opt = append(opt, titan.Routes(router0.Register), titan.Subscribe(router0.Subscribe))

	return titan.NewServer(NATS_SUBJECT, opt...)
}

// Define client ----------------------------------------------------------------------------------------------
type MedicineAppClient struct {
	client *titan.Client
}

func (srv *MedicineAppClient) GetShoppingBag(ctx *titan.Context, request GetShoppingBagRequest) (*GetShoppingBagRequestResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetShoppingBag).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetShoppingBagRequestResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *MedicineAppClient) AddToShoppingBag(ctx *titan.Context, request AddToShoppingBagRequest) (*ShoppingBagResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_AddToShoppingBag).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &ShoppingBagResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *MedicineAppClient) CheckMissingDiagnose(ctx *titan.Context, request AddToShoppingBagRequest) (*CheckMissingDiagnoseResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CheckMissingDiagnose).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &CheckMissingDiagnoseResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *MedicineAppClient) UpdateShoppingBagQuantity(ctx *titan.Context, request UpdateShoppingBagQuantityRequest) (*ShoppingBagResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UpdateShoppingBagQuantity).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &ShoppingBagResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *MedicineAppClient) UpdateShoppingBagInformation(ctx *titan.Context, request UpdateShoppingBagInformationRequest) (*ShoppingBagResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UpdateShoppingBagInformation).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &ShoppingBagResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *MedicineAppClient) RemoveFromShoppingBag(ctx *titan.Context, request RemoveFromShoppingBagRequest) (*ShoppingBagResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_RemoveFromShoppingBag).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &ShoppingBagResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *MedicineAppClient) UpdateForm(ctx *titan.Context, request UpdateFormRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UpdateForm).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *MedicineAppClient) Prescribe(ctx *titan.Context, request PrescribeRequest) (*PrescribeResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_Prescribe).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &PrescribeResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *MedicineAppClient) GetMedicationPrescribe(ctx *titan.Context, request GetMedicationPrescribeRequest) (*GetMedicationPrescribeResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetMedicationPrescribe).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetMedicationPrescribeResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *MedicineAppClient) DeleteMedicationPrescribe(ctx *titan.Context, request DeleteMedicationPrescribeRequest) (*DeleteMedicationPrescribeResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_DeleteMedicationPrescribe).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &DeleteMedicationPrescribeResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *MedicineAppClient) CheckMissingDiagnoses(ctx *titan.Context, request CheckMissingDiagnosesRequest) (*CheckMissingDiagnosesResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CheckMissingDiagnoses).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &CheckMissingDiagnosesResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *MedicineAppClient) CreateMedicationPlan(ctx *titan.Context, request CreateMedicationPlanRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CreateMedicationPlan).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *MedicineAppClient) DeleteMedicationPlan(ctx *titan.Context, request DeleteMedicationPlanRequest) (*DeleteMedicationPlanResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_DeleteMedicationPlan).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &DeleteMedicationPlanResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *MedicineAppClient) UpdateTreatmentDoctorMedicationForm(ctx *titan.Context, request UpdateTreatmentDoctorMedicationFormRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UpdateTreatmentDoctorMedicationForm).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *MedicineAppClient) PrintForm(ctx *titan.Context, request PrintFormRequest) (*PrintFormResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_PrintForm).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &PrintFormResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *MedicineAppClient) OnTimelineHardDelete(ctx *titan.Context, request timeline.EventTimelineHardRemove) error {
	return srv.client.Publish(ctx, "api.app.mvz.AppMvzTimeline.TimelineHardRemove", request)
}

func NewMedicineAppClient(clients ...*titan.Client) *MedicineAppClient {
	var client *titan.Client
	if len(clients) <= 0 {
		client = titan.GetDefaultClient()
	} else {
		client = clients[0]
	}
	return &MedicineAppClient{client: client}
}

// Define event Notifier -----------------------------------------------------------------------------------------
type MedicineNotifier struct {
	client *titan.Client
}

func NewMedicineNotifier() *MedicineNotifier {
	client := titan.GetDefaultClient()
	return &MedicineNotifier{client}
}
func (p *MedicineNotifier) NotifyShoppingBagRequest(ctx *titan.Context, event *EventShoppingBagRequest) error {
	return p.client.Publish(ctx, EVENT_ShoppingBagRequest, event)
}
func (p *MedicineNotifier) NotifyMedicationPrescribe(ctx *titan.Context, event *EventMedicationPrescribe) error {
	return p.client.Publish(ctx, EVENT_MedicationPrescribe, event)
}
func (p *MedicineNotifier) NotifyMedicationPlanChanged(ctx *titan.Context, event *EventMedicationPlanChanged) error {
	return p.client.Publish(ctx, EVENT_MedicationPlanChanged, event)
}
func (p *MedicineNotifier) NotifyRefillMedicine(ctx *titan.Context, event *EventRefillMedicine) error {
	return p.client.Publish(ctx, EVENT_RefillMedicine, event)
}
func (p *MedicineNotifier) NotifyViewMedicationForm(ctx *titan.Context, event *EventViewMedicationForm) error {
	return p.client.Publish(ctx, EVENT_ViewMedicationForm, event)
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type MedicineSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewMedicineSocketNotifier(socket *socket_api.SocketServiceClient) *MedicineSocketNotifier {
	return &MedicineSocketNotifier{socket}
}
func (n *MedicineSocketNotifier) NotifyCareProviderShoppingBagRequest(ctx *titan.Context, event *EventShoppingBagRequest) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_ShoppingBagRequest,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *MedicineSocketNotifier) NotifyUserShoppingBagRequest(ctx *titan.Context, event *EventShoppingBagRequest) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_ShoppingBagRequest,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *MedicineSocketNotifier) NotifyDeviceShoppingBagRequest(ctx *titan.Context, event *EventShoppingBagRequest) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_ShoppingBagRequest,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *MedicineSocketNotifier) NotifyClientShoppingBagRequest(ctx *titan.Context, event *EventShoppingBagRequest) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_ShoppingBagRequest,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}
func (n *MedicineSocketNotifier) NotifyCareProviderMedicationPrescribe(ctx *titan.Context, event *EventMedicationPrescribe) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_MedicationPrescribe,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *MedicineSocketNotifier) NotifyUserMedicationPrescribe(ctx *titan.Context, event *EventMedicationPrescribe) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_MedicationPrescribe,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *MedicineSocketNotifier) NotifyDeviceMedicationPrescribe(ctx *titan.Context, event *EventMedicationPrescribe) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_MedicationPrescribe,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *MedicineSocketNotifier) NotifyClientMedicationPrescribe(ctx *titan.Context, event *EventMedicationPrescribe) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_MedicationPrescribe,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}
func (n *MedicineSocketNotifier) NotifyCareProviderMedicationPlanChanged(ctx *titan.Context, event *EventMedicationPlanChanged) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_MedicationPlanChanged,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *MedicineSocketNotifier) NotifyUserMedicationPlanChanged(ctx *titan.Context, event *EventMedicationPlanChanged) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_MedicationPlanChanged,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *MedicineSocketNotifier) NotifyDeviceMedicationPlanChanged(ctx *titan.Context, event *EventMedicationPlanChanged) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_MedicationPlanChanged,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *MedicineSocketNotifier) NotifyClientMedicationPlanChanged(ctx *titan.Context, event *EventMedicationPlanChanged) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_MedicationPlanChanged,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}
func (n *MedicineSocketNotifier) NotifyCareProviderRefillMedicine(ctx *titan.Context, event *EventRefillMedicine) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_RefillMedicine,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *MedicineSocketNotifier) NotifyUserRefillMedicine(ctx *titan.Context, event *EventRefillMedicine) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_RefillMedicine,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *MedicineSocketNotifier) NotifyDeviceRefillMedicine(ctx *titan.Context, event *EventRefillMedicine) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_RefillMedicine,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *MedicineSocketNotifier) NotifyClientRefillMedicine(ctx *titan.Context, event *EventRefillMedicine) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_RefillMedicine,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}
func (n *MedicineSocketNotifier) NotifyCareProviderViewMedicationForm(ctx *titan.Context, event *EventViewMedicationForm) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_ViewMedicationForm,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *MedicineSocketNotifier) NotifyUserViewMedicationForm(ctx *titan.Context, event *EventViewMedicationForm) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_ViewMedicationForm,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *MedicineSocketNotifier) NotifyDeviceViewMedicationForm(ctx *titan.Context, event *EventViewMedicationForm) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_ViewMedicationForm,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *MedicineSocketNotifier) NotifyClientViewMedicationForm(ctx *titan.Context, event *EventViewMedicationForm) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_ViewMedicationForm,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}

// -----------------------------------------------
// Test event listener
type MedicineEventListener struct {
	mux                                sync.Mutex
	LastEventShoppingBagRequestList    []EventShoppingBagRequest
	LastEventMedicationPrescribeList   []EventMedicationPrescribe
	LastEventMedicationPlanChangedList []EventMedicationPlanChanged
	LastEventRefillMedicineList        []EventRefillMedicine
	LastEventViewMedicationFormList    []EventViewMedicationForm
}

func (listener *MedicineEventListener) Reset() {
	listener.mux.Lock()
	listener.LastEventShoppingBagRequestList = []EventShoppingBagRequest{}
	listener.LastEventMedicationPrescribeList = []EventMedicationPrescribe{}
	listener.LastEventMedicationPlanChangedList = []EventMedicationPlanChanged{}
	listener.LastEventRefillMedicineList = []EventRefillMedicine{}
	listener.LastEventViewMedicationFormList = []EventViewMedicationForm{}
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *MedicineEventListener) Subscribe(s *titan.MessageSubscriber) {
	s.Register(EVENT_ShoppingBagRequest, "api.app.mvz_AppMvzMedicine_ShoppingBagRequest_Queue_test", func(p *titan.Message) error {
		var resp EventShoppingBagRequest
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventShoppingBagRequestList) >= 100 {
			listener.LastEventShoppingBagRequestList = listener.LastEventShoppingBagRequestList[1:]
		}
		listener.LastEventShoppingBagRequestList = append(listener.LastEventShoppingBagRequestList, resp)
		listener.mux.Unlock()
		return nil
	})
	s.Register(EVENT_MedicationPrescribe, "api.app.mvz_AppMvzMedicine_MedicationPrescribe_Queue_test", func(p *titan.Message) error {
		var resp EventMedicationPrescribe
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventMedicationPrescribeList) >= 100 {
			listener.LastEventMedicationPrescribeList = listener.LastEventMedicationPrescribeList[1:]
		}
		listener.LastEventMedicationPrescribeList = append(listener.LastEventMedicationPrescribeList, resp)
		listener.mux.Unlock()
		return nil
	})
	s.Register(EVENT_MedicationPlanChanged, "api.app.mvz_AppMvzMedicine_MedicationPlanChanged_Queue_test", func(p *titan.Message) error {
		var resp EventMedicationPlanChanged
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventMedicationPlanChangedList) >= 100 {
			listener.LastEventMedicationPlanChangedList = listener.LastEventMedicationPlanChangedList[1:]
		}
		listener.LastEventMedicationPlanChangedList = append(listener.LastEventMedicationPlanChangedList, resp)
		listener.mux.Unlock()
		return nil
	})
	s.Register(EVENT_RefillMedicine, "api.app.mvz_AppMvzMedicine_RefillMedicine_Queue_test", func(p *titan.Message) error {
		var resp EventRefillMedicine
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventRefillMedicineList) >= 100 {
			listener.LastEventRefillMedicineList = listener.LastEventRefillMedicineList[1:]
		}
		listener.LastEventRefillMedicineList = append(listener.LastEventRefillMedicineList, resp)
		listener.mux.Unlock()
		return nil
	})
	s.Register(EVENT_ViewMedicationForm, "api.app.mvz_AppMvzMedicine_ViewMedicationForm_Queue_test", func(p *titan.Message) error {
		var resp EventViewMedicationForm
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventViewMedicationFormList) >= 100 {
			listener.LastEventViewMedicationFormList = listener.LastEventViewMedicationFormList[1:]
		}
		listener.LastEventViewMedicationFormList = append(listener.LastEventViewMedicationFormList, resp)
		listener.mux.Unlock()
		return nil
	})
}
func (listener *MedicineEventListener) GetLastEventShoppingBagRequestList(timeOutInMilliSeconds time.Duration) []EventShoppingBagRequest {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventShoppingBagRequestList
		default:
			// if any value
			if len(listener.LastEventShoppingBagRequestList) > 0 {
				return listener.LastEventShoppingBagRequestList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
func (listener *MedicineEventListener) GetLastEventMedicationPrescribeList(timeOutInMilliSeconds time.Duration) []EventMedicationPrescribe {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventMedicationPrescribeList
		default:
			// if any value
			if len(listener.LastEventMedicationPrescribeList) > 0 {
				return listener.LastEventMedicationPrescribeList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
func (listener *MedicineEventListener) GetLastEventMedicationPlanChangedList(timeOutInMilliSeconds time.Duration) []EventMedicationPlanChanged {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventMedicationPlanChangedList
		default:
			// if any value
			if len(listener.LastEventMedicationPlanChangedList) > 0 {
				return listener.LastEventMedicationPlanChangedList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
func (listener *MedicineEventListener) GetLastEventRefillMedicineList(timeOutInMilliSeconds time.Duration) []EventRefillMedicine {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventRefillMedicineList
		default:
			// if any value
			if len(listener.LastEventRefillMedicineList) > 0 {
				return listener.LastEventRefillMedicineList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
func (listener *MedicineEventListener) GetLastEventViewMedicationFormList(timeOutInMilliSeconds time.Duration) []EventViewMedicationForm {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventViewMedicationFormList
		default:
			// if any value
			if len(listener.LastEventViewMedicationFormList) > 0 {
				return listener.LastEventViewMedicationFormList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
