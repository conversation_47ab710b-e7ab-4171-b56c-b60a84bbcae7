// This code was autogenerated from app/mvz/document_management.proto, do not edit.

package document_management

import (
	"encoding/json"
	"errors"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	common1 "git.tutum.dev/medi/tutum/ares/service/domains/api/common"

	common2 "git.tutum.dev/medi/tutum/ares/service/domains/api/document_management/common"

	common "git.tutum.dev/medi/tutum/ares/service/domains/api/document_type/common"

	patient_profile_common "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type EventDocumentManagementChange struct {
}

type EventGdtExportResult struct {
	Result bool   `json:"result"`
	Error  string `json:"error"`
}

type EventGdtImportResult struct {
	Result      bool                             `json:"result"`
	PatientName string                           `json:"patientName"`
	Error       string                           `json:"error"`
	Type        common2.DocumentNotificationType `json:"type"`
}

type CreateDocumentManagementRequest struct {
	DocumentName      string                           `json:"documentName" validate:"required"`
	CompanionFileId   int64                            `json:"companionFileId" validate:"required"`
	CompanionFilePath string                           `json:"companionFilePath" validate:"required"`
	Status            common2.DocumentManagementStatus `json:"status" validate:"required"`
}

type CreateDocumentManagementResponse struct {
	Id                      uuid.UUID                       `json:"id"`
	DocumentManagementModel common2.DocumentManagementModel `json:"documentManagementModel"`
	PresignedUrl            string                          `json:"presignedUrl"`
}

type UpdateDocumentManagementStatusRequest struct {
	Id           uuid.UUID                        `json:"id" validate:"required"`
	Status       common2.DocumentManagementStatus `json:"status" validate:"required"`
	ImportedDate int64                            `json:"importedDate" validate:"required"`
}

type UpdateDocumentManagementStatusResponse struct {
	Id                      uuid.UUID                       `json:"id"`
	DocumentManagementModel common2.DocumentManagementModel `json:"documentManagementModel"`
}

type ListDocumentManagementRequest struct {
	Value         *string                           `json:"value"`
	IsNotAssigned bool                              `json:"isNotAssigned"`
	Pagination    *common1.PaginationRequest        `json:"pagination"`
	FromDate      *int64                            `json:"fromDate"`
	ToDate        *int64                            `json:"toDate"`
	Status        *common2.DocumentManagementStatus `json:"status"`
	PatientIds    []uuid.UUID                       `json:"patientIds"`
	SenderIds     []string                          `json:"senderIds"`
}

type ListDocumentManagementResponse struct {
	Data       []common2.DocumentManagementItem `json:"data"`
	Pagination *common1.PaginationResponse      `json:"pagination"`
}

type AssignPatientDocumentRequest struct {
	Id           uuid.UUID                         `json:"id" validate:"required"`
	PatientId    *uuid.UUID                        `json:"patientId"`
	Sender       *common2.DocumentManagementSender `json:"sender"`
	DocumentType *common.DocumentType              `json:"documentType"`
	Description  *string                           `json:"description"`
}

type GetDocumentManagementRequest struct {
	Id uuid.UUID `json:"id" validate:"required"`
}

type GetDocumentManagementResponse struct {
	Data common2.DocumentManagementItem `json:"data"`
}

type DeleteDocumentManagementRequest struct {
	Id        uuid.UUID  `json:"id" validate:"required"`
	PatientId *uuid.UUID `json:"patientId"`
}

type DeleteFailDocumentManagementRequest struct {
	Ids   []uuid.UUID `json:"ids" validate:"required"`
	IsAll bool        `json:"isAll"`
}

type MarkReadDocumentManagementRequest struct {
	Id        uuid.UUID  `json:"id" validate:"required"`
	PatientId *uuid.UUID `json:"patientId"`
}

type UploadFile struct {
	FileName string `json:"fileName" validate:"required"`
	ObjectId string `json:"objectId" validate:"required"`
}

type UploadDocumentManagementRequest struct {
	Files []UploadFile `json:"files" validate:"required"`
}

type GetDocumentBadgeResponse struct {
	UnassignedCount int64 `json:"unassignedCount"`
	FailCount       int64 `json:"failCount"`
	TotalCount      int64 `json:"totalCount"`
}

type ReImportFailDocumentRequest struct {
	Ids   []uuid.UUID `json:"ids" validate:"required"`
	IsAll bool        `json:"isAll"`
}

type ExportGdtDocumentRequest struct {
	GdtExportSettingId uuid.UUID  `json:"gdtExportSettingId" validate:"required"`
	TreatmentDate      *int64     `json:"treatmentDate"`
	ReadingDate        *int64     `json:"readingDate"`
	PatientId          uuid.UUID  `json:"patientId"`
	ScheinId           *uuid.UUID `json:"scheinId"`
	TreatmentTime      *int64     `json:"treatmentTime"`
	ReadingTime        *int64     `json:"readingTime"`
}

type LabParameter struct {
	Name string `json:"name"`
	Min  string `json:"min"`
	Max  string `json:"max"`
	Unit string `json:"unit"`
}

type LabResultItem struct {
	Name  string `json:"name"`
	Value string `json:"value"`
	Icon  string `json:"icon"`
}

type LabResultOverview struct {
	Date       int64           `json:"date"`
	LabOrderId string          `json:"labOrderId"`
	Items      []LabResultItem `json:"items"`
}

type GetLabResultsRequest struct {
	PatientId                 uuid.UUID `json:"patientId"`
	Results                   *int64    `json:"results"`
	FromDate                  *int64    `json:"fromDate"`
	ToDate                    *int64    `json:"toDate"`
	FieldNames                []string  `json:"fieldNames"`
	IsOnlyPathologicalResults *bool     `json:"isOnlyPathologicalResults"`
}

type GetLabResultsResponse struct {
	LabParameters          []LabParameter      `json:"labParameters"`
	AvailableLabParameters []LabParameter      `json:"availableLabParameters"`
	LabResults             []LabResultOverview `json:"labResults"`
}

type GetLabResultsPDFResponse struct {
	Pdf  []byte            `json:"pdf"`
	Mode LabResultsPDFMode `json:"mode"`
}

type ExportGdtDocumentResponse struct {
}

// enum definitions
type LabResultsPDFMode string

const (
	PORTRAIT  LabResultsPDFMode = "portrait"
	LANDSCAPE LabResultsPDFMode = "landscape"
)

// Define constants
const NATS_SUBJECT = "api.app.mvz" // nats subject this service will listen to

// service event constants
const EVENT_CreateDocumentManagement = "api.app.mvz.DocumentManagementApp.CreateDocumentManagement"
const LEGACY_TOPIC_CreateDocumentManagement = "/api/app/mvz/document/management/createDocumentManagement"
const EVENT_UpdateDocumentManagementStatus = "api.app.mvz.DocumentManagementApp.UpdateDocumentManagementStatus"
const LEGACY_TOPIC_UpdateDocumentManagementStatus = "/api/app/mvz/document/management/updateDocumentManagementStatus"
const EVENT_ListDocumentManagement = "api.app.mvz.DocumentManagementApp.ListDocumentManagement"
const LEGACY_TOPIC_ListDocumentManagement = "/api/app/mvz/document/management/listDocumentManagement"
const EVENT_AssignPatientDocument = "api.app.mvz.DocumentManagementApp.AssignPatientDocument"
const LEGACY_TOPIC_AssignPatientDocument = "/api/app/mvz/document/management/assignPatientDocument"
const EVENT_GetDocumentManagement = "api.app.mvz.DocumentManagementApp.GetDocumentManagement"
const LEGACY_TOPIC_GetDocumentManagement = "/api/app/mvz/document/management/getDocumentManagement"
const EVENT_MarkReadDocumentManagement = "api.app.mvz.DocumentManagementApp.MarkReadDocumentManagement"
const LEGACY_TOPIC_MarkReadDocumentManagement = "/api/app/mvz/document/management/markReadDocumentManagement"
const EVENT_DeleteDocumentManagement = "api.app.mvz.DocumentManagementApp.DeleteDocumentManagement"
const LEGACY_TOPIC_DeleteDocumentManagement = "/api/app/mvz/document/management/deleteDocumentManagement"
const EVENT_DeleteFailDocumentManagement = "api.app.mvz.DocumentManagementApp.DeleteFailDocumentManagement"
const LEGACY_TOPIC_DeleteFailDocumentManagement = "/api/app/mvz/document/management/deleteFailDocumentManagement"
const EVENT_HandleEventDocumentManagementChange = "api.app.mvz.DocumentManagementApp.HandleEventDocumentManagementChange"
const LEGACY_TOPIC_HandleEventDocumentManagementChange = "/api/app/mvz/document/management/handleEventDocumentManagementChange"
const EVENT_UploadDocumentManagement = "api.app.mvz.DocumentManagementApp.UploadDocumentManagement"
const LEGACY_TOPIC_UploadDocumentManagement = "/api/app/mvz/document/management/uploadDocumentManagement"
const EVENT_GetDocumentBadge = "api.app.mvz.DocumentManagementApp.GetDocumentBadge"
const LEGACY_TOPIC_GetDocumentBadge = "/api/app/mvz/document/management/getDocumentBadge"
const EVENT_ReImportFailDocument = "api.app.mvz.DocumentManagementApp.ReImportFailDocument"
const LEGACY_TOPIC_ReImportFailDocument = "/api/app/mvz/document/management/reImportFailDocument"
const EVENT_ProcessDocumentUpload = "api.app.mvz.DocumentManagementApp.ProcessDocumentUpload"
const LEGACY_TOPIC_ProcessDocumentUpload = "/api/app/mvz/document/management/processDocumentUpload"
const EVENT_ExportGdtDocument = "api.app.mvz.DocumentManagementApp.ExportGdtDocument"
const LEGACY_TOPIC_ExportGdtDocument = "/api/app/mvz/document/management/exportGdtDocument"
const EVENT_ProcessImportGdtDocuments = "api.app.mvz.DocumentManagementApp.ProcessImportGdtDocuments"
const LEGACY_TOPIC_ProcessImportGdtDocuments = "/api/app/mvz/document/management/processImportGdtDocuments"
const EVENT_ProcessImportLdtDocuments = "api.app.mvz.DocumentManagementApp.ProcessImportLdtDocuments"
const LEGACY_TOPIC_ProcessImportLdtDocuments = "/api/app/mvz/document/management/processImportLdtDocuments"
const EVENT_OnPatientUpdate = "api.app.mvz.DocumentManagementApp.OnPatientUpdate"
const LEGACY_TOPIC_OnPatientUpdate = "/api/app/mvz/document/management/onPatientUpdate"
const EVENT_GetLabResults = "api.app.mvz.DocumentManagementApp.GetLabResults"
const LEGACY_TOPIC_GetLabResults = "/api/app/mvz/document/management/getLabResults"
const EVENT_GetLabResultsPDF = "api.app.mvz.DocumentManagementApp.GetLabResultsPDF"
const LEGACY_TOPIC_GetLabResultsPDF = "/api/app/mvz/document/management/getLabResultsPDF"

// message event constants
const EVENT_DocumentManagementChange = "api.app.mvz.AppMvzDocumentManagement.DocumentManagementChange"
const EVENT_GdtExportResult = "api.app.mvz.AppMvzDocumentManagement.GdtExportResult"
const EVENT_GdtImportResult = "api.app.mvz.AppMvzDocumentManagement.GdtImportResult"

// Define service interface -------------------------------------------------------------
type DocumentManagementApp interface {
	CreateDocumentManagement(ctx *titan.Context, request CreateDocumentManagementRequest) (*CreateDocumentManagementResponse, error)
	UpdateDocumentManagementStatus(ctx *titan.Context, request UpdateDocumentManagementStatusRequest) (*UpdateDocumentManagementStatusResponse, error)
	ListDocumentManagement(ctx *titan.Context, request ListDocumentManagementRequest) (*ListDocumentManagementResponse, error)
	AssignPatientDocument(ctx *titan.Context, request AssignPatientDocumentRequest) error
	GetDocumentManagement(ctx *titan.Context, request GetDocumentManagementRequest) (*GetDocumentManagementResponse, error)
	MarkReadDocumentManagement(ctx *titan.Context, request MarkReadDocumentManagementRequest) error
	DeleteDocumentManagement(ctx *titan.Context, request DeleteDocumentManagementRequest) error
	DeleteFailDocumentManagement(ctx *titan.Context, request DeleteFailDocumentManagementRequest) error
	HandleEventDocumentManagementChange(ctx *titan.Context, request EventDocumentManagementChange) error
	UploadDocumentManagement(ctx *titan.Context, request UploadDocumentManagementRequest) error
	GetDocumentBadge(ctx *titan.Context) (*GetDocumentBadgeResponse, error)
	ReImportFailDocument(ctx *titan.Context, request ReImportFailDocumentRequest) error
	ProcessDocumentUpload(ctx *titan.Context) error
	ExportGdtDocument(ctx *titan.Context, request ExportGdtDocumentRequest) (*ExportGdtDocumentResponse, error)
	ProcessImportGdtDocuments(ctx *titan.Context) error
	ProcessImportLdtDocuments(ctx *titan.Context) error
	OnPatientUpdate(ctx *titan.Context, request patient_profile_common.EventPatientProfileChange) error
	GetLabResults(ctx *titan.Context, request GetLabResultsRequest) (*GetLabResultsResponse, error)
	GetLabResultsPDF(ctx *titan.Context, request GetLabResultsRequest) (*GetLabResultsPDFResponse, error)
}

// Define service proxy -------------------------------------------------------------------
type DocumentManagementAppProxy struct {
	service DocumentManagementApp
}

func (srv *DocumentManagementAppProxy) CreateDocumentManagement(ctx *titan.Context, request CreateDocumentManagementRequest) (*CreateDocumentManagementResponse, error) {
	return srv.service.CreateDocumentManagement(ctx, request)
}
func (srv *DocumentManagementAppProxy) UpdateDocumentManagementStatus(ctx *titan.Context, request UpdateDocumentManagementStatusRequest) (*UpdateDocumentManagementStatusResponse, error) {
	return srv.service.UpdateDocumentManagementStatus(ctx, request)
}
func (srv *DocumentManagementAppProxy) ListDocumentManagement(ctx *titan.Context, request ListDocumentManagementRequest) (*ListDocumentManagementResponse, error) {
	return srv.service.ListDocumentManagement(ctx, request)
}
func (srv *DocumentManagementAppProxy) AssignPatientDocument(ctx *titan.Context, request AssignPatientDocumentRequest) error {
	return srv.service.AssignPatientDocument(ctx, request)
}
func (srv *DocumentManagementAppProxy) GetDocumentManagement(ctx *titan.Context, request GetDocumentManagementRequest) (*GetDocumentManagementResponse, error) {
	return srv.service.GetDocumentManagement(ctx, request)
}
func (srv *DocumentManagementAppProxy) MarkReadDocumentManagement(ctx *titan.Context, request MarkReadDocumentManagementRequest) error {
	return srv.service.MarkReadDocumentManagement(ctx, request)
}
func (srv *DocumentManagementAppProxy) DeleteDocumentManagement(ctx *titan.Context, request DeleteDocumentManagementRequest) error {
	return srv.service.DeleteDocumentManagement(ctx, request)
}
func (srv *DocumentManagementAppProxy) DeleteFailDocumentManagement(ctx *titan.Context, request DeleteFailDocumentManagementRequest) error {
	return srv.service.DeleteFailDocumentManagement(ctx, request)
}
func (srv *DocumentManagementAppProxy) HandleEventDocumentManagementChange(ctx *titan.Context, request EventDocumentManagementChange) error {
	return srv.service.HandleEventDocumentManagementChange(ctx, request)
}
func (srv *DocumentManagementAppProxy) UploadDocumentManagement(ctx *titan.Context, request UploadDocumentManagementRequest) error {
	return srv.service.UploadDocumentManagement(ctx, request)
}
func (srv *DocumentManagementAppProxy) GetDocumentBadge(ctx *titan.Context) (*GetDocumentBadgeResponse, error) {
	return srv.service.GetDocumentBadge(ctx)
}
func (srv *DocumentManagementAppProxy) ReImportFailDocument(ctx *titan.Context, request ReImportFailDocumentRequest) error {
	return srv.service.ReImportFailDocument(ctx, request)
}
func (srv *DocumentManagementAppProxy) ProcessDocumentUpload(ctx *titan.Context) error {
	return srv.service.ProcessDocumentUpload(ctx)
}
func (srv *DocumentManagementAppProxy) ExportGdtDocument(ctx *titan.Context, request ExportGdtDocumentRequest) (*ExportGdtDocumentResponse, error) {
	return srv.service.ExportGdtDocument(ctx, request)
}
func (srv *DocumentManagementAppProxy) ProcessImportGdtDocuments(ctx *titan.Context) error {
	return srv.service.ProcessImportGdtDocuments(ctx)
}
func (srv *DocumentManagementAppProxy) ProcessImportLdtDocuments(ctx *titan.Context) error {
	return srv.service.ProcessImportLdtDocuments(ctx)
}
func (srv *DocumentManagementAppProxy) OnPatientUpdate(ctx *titan.Context, request patient_profile_common.EventPatientProfileChange) error {
	return srv.service.OnPatientUpdate(ctx, request)
}
func (srv *DocumentManagementAppProxy) GetLabResults(ctx *titan.Context, request GetLabResultsRequest) (*GetLabResultsResponse, error) {
	return srv.service.GetLabResults(ctx, request)
}
func (srv *DocumentManagementAppProxy) GetLabResultsPDF(ctx *titan.Context, request GetLabResultsRequest) (*GetLabResultsPDFResponse, error) {
	return srv.service.GetLabResultsPDF(ctx, request)
}

// Define service router -----------------------------------------------------------------
type DocumentManagementAppRouter struct {
	proxy *DocumentManagementAppProxy
}

func (router *DocumentManagementAppRouter) Register(r titan.Router) {
	r.RegisterTopic(EVENT_CreateDocumentManagement, router.proxy.CreateDocumentManagement, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_CreateDocumentManagement, router.proxy.CreateDocumentManagement, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_UpdateDocumentManagementStatus, router.proxy.UpdateDocumentManagementStatus, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_UpdateDocumentManagementStatus, router.proxy.UpdateDocumentManagementStatus, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_ListDocumentManagement, router.proxy.ListDocumentManagement, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_ListDocumentManagement, router.proxy.ListDocumentManagement, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_AssignPatientDocument, router.proxy.AssignPatientDocument, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_AssignPatientDocument, router.proxy.AssignPatientDocument, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetDocumentManagement, router.proxy.GetDocumentManagement, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetDocumentManagement, router.proxy.GetDocumentManagement, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_MarkReadDocumentManagement, router.proxy.MarkReadDocumentManagement, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_MarkReadDocumentManagement, router.proxy.MarkReadDocumentManagement, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_DeleteDocumentManagement, router.proxy.DeleteDocumentManagement, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_DeleteDocumentManagement, router.proxy.DeleteDocumentManagement, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_DeleteFailDocumentManagement, router.proxy.DeleteFailDocumentManagement, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_DeleteFailDocumentManagement, router.proxy.DeleteFailDocumentManagement, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_UploadDocumentManagement, router.proxy.UploadDocumentManagement, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_UploadDocumentManagement, router.proxy.UploadDocumentManagement, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetDocumentBadge, router.proxy.GetDocumentBadge, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetDocumentBadge, router.proxy.GetDocumentBadge, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_ReImportFailDocument, router.proxy.ReImportFailDocument, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_ReImportFailDocument, router.proxy.ReImportFailDocument, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_ProcessDocumentUpload, router.proxy.ProcessDocumentUpload, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_ProcessDocumentUpload, router.proxy.ProcessDocumentUpload, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_ExportGdtDocument, router.proxy.ExportGdtDocument, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_ExportGdtDocument, router.proxy.ExportGdtDocument, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_ProcessImportGdtDocuments, router.proxy.ProcessImportGdtDocuments, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_ProcessImportGdtDocuments, router.proxy.ProcessImportGdtDocuments, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_ProcessImportLdtDocuments, router.proxy.ProcessImportLdtDocuments, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_ProcessImportLdtDocuments, router.proxy.ProcessImportLdtDocuments, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetLabResults, router.proxy.GetLabResults, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetLabResults, router.proxy.GetLabResults, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetLabResultsPDF, router.proxy.GetLabResultsPDF, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetLabResultsPDF, router.proxy.GetLabResultsPDF, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
}

// Subscriber
func (router *DocumentManagementAppRouter) Subscribe(s *titan.MessageSubscriber) {
	s.Register(EVENT_DocumentManagementChange, "api.app.mvz_DocumentManagementApp_HandleEventDocumentManagementChange_Queue", func(p *titan.Message) error {
		var resp EventDocumentManagementChange
		ctx, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		return router.proxy.HandleEventDocumentManagementChange(ctx, resp)
	})
	s.Register("api.app.mvz.AppMvzPatientProfile.PatientProfileChange", "api.app.mvz_DocumentManagementApp_OnPatientUpdate_Queue", func(p *titan.Message) error {
		var resp patient_profile_common.EventPatientProfileChange
		ctx, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		return router.proxy.OnPatientUpdate(ctx, resp)
	})
}

func NewDocumentManagementAppRouter(s DocumentManagementApp) *DocumentManagementAppRouter {
	p := &DocumentManagementAppProxy{s}
	return &DocumentManagementAppRouter{p}
}

func NewServer(bff0 DocumentManagementApp, options ...titan.Option) *titan.Server {
	opt := options

	router0 := NewDocumentManagementAppRouter(bff0)
	opt = append(opt, titan.Routes(router0.Register), titan.Subscribe(router0.Subscribe))

	return titan.NewServer(NATS_SUBJECT, opt...)
}

// Define client ----------------------------------------------------------------------------------------------
type DocumentManagementAppClient struct {
	client *titan.Client
}

func (srv *DocumentManagementAppClient) CreateDocumentManagement(ctx *titan.Context, request CreateDocumentManagementRequest) (*CreateDocumentManagementResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_CreateDocumentManagement).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &CreateDocumentManagementResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *DocumentManagementAppClient) UpdateDocumentManagementStatus(ctx *titan.Context, request UpdateDocumentManagementStatusRequest) (*UpdateDocumentManagementStatusResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UpdateDocumentManagementStatus).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &UpdateDocumentManagementStatusResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *DocumentManagementAppClient) ListDocumentManagement(ctx *titan.Context, request ListDocumentManagementRequest) (*ListDocumentManagementResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_ListDocumentManagement).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &ListDocumentManagementResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *DocumentManagementAppClient) AssignPatientDocument(ctx *titan.Context, request AssignPatientDocumentRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_AssignPatientDocument).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *DocumentManagementAppClient) GetDocumentManagement(ctx *titan.Context, request GetDocumentManagementRequest) (*GetDocumentManagementResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetDocumentManagement).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetDocumentManagementResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *DocumentManagementAppClient) MarkReadDocumentManagement(ctx *titan.Context, request MarkReadDocumentManagementRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_MarkReadDocumentManagement).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *DocumentManagementAppClient) DeleteDocumentManagement(ctx *titan.Context, request DeleteDocumentManagementRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_DeleteDocumentManagement).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *DocumentManagementAppClient) DeleteFailDocumentManagement(ctx *titan.Context, request DeleteFailDocumentManagementRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_DeleteFailDocumentManagement).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *DocumentManagementAppClient) UploadDocumentManagement(ctx *titan.Context, request UploadDocumentManagementRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_UploadDocumentManagement).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *DocumentManagementAppClient) GetDocumentBadge(ctx *titan.Context) (*GetDocumentBadgeResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetDocumentBadge).
		Subject(NATS_SUBJECT).
		Build()
	var resp = &GetDocumentBadgeResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *DocumentManagementAppClient) ReImportFailDocument(ctx *titan.Context, request ReImportFailDocumentRequest) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_ReImportFailDocument).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *DocumentManagementAppClient) ProcessDocumentUpload(ctx *titan.Context) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_ProcessDocumentUpload).
		Subject(NATS_SUBJECT).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *DocumentManagementAppClient) ExportGdtDocument(ctx *titan.Context, request ExportGdtDocumentRequest) (*ExportGdtDocumentResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_ExportGdtDocument).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &ExportGdtDocumentResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *DocumentManagementAppClient) ProcessImportGdtDocuments(ctx *titan.Context) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_ProcessImportGdtDocuments).
		Subject(NATS_SUBJECT).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *DocumentManagementAppClient) ProcessImportLdtDocuments(ctx *titan.Context) error {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_ProcessImportLdtDocuments).
		Subject(NATS_SUBJECT).
		Build()
	_, err := srv.client.SendRequest(ctx, req)
	return err
}
func (srv *DocumentManagementAppClient) OnPatientUpdate(ctx *titan.Context, request patient_profile_common.EventPatientProfileChange) error {
	return srv.client.Publish(ctx, "api.app.mvz.AppMvzPatientProfile.PatientProfileChange", request)
}
func (srv *DocumentManagementAppClient) GetLabResults(ctx *titan.Context, request GetLabResultsRequest) (*GetLabResultsResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetLabResults).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetLabResultsResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *DocumentManagementAppClient) GetLabResultsPDF(ctx *titan.Context, request GetLabResultsRequest) (*GetLabResultsPDFResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetLabResultsPDF).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetLabResultsPDFResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func NewDocumentManagementAppClient(clients ...*titan.Client) *DocumentManagementAppClient {
	var client *titan.Client
	if len(clients) <= 0 {
		client = titan.GetDefaultClient()
	} else {
		client = clients[0]
	}
	return &DocumentManagementAppClient{client: client}
}

// Define event Notifier -----------------------------------------------------------------------------------------
type DocumentManagementNotifier struct {
	client *titan.Client
}

func NewDocumentManagementNotifier() *DocumentManagementNotifier {
	client := titan.GetDefaultClient()
	return &DocumentManagementNotifier{client}
}
func (p *DocumentManagementNotifier) NotifyDocumentManagementChange(ctx *titan.Context, event *EventDocumentManagementChange) error {
	return p.client.Publish(ctx, EVENT_DocumentManagementChange, event)
}
func (p *DocumentManagementNotifier) NotifyGdtExportResult(ctx *titan.Context, event *EventGdtExportResult) error {
	return p.client.Publish(ctx, EVENT_GdtExportResult, event)
}
func (p *DocumentManagementNotifier) NotifyGdtImportResult(ctx *titan.Context, event *EventGdtImportResult) error {
	return p.client.Publish(ctx, EVENT_GdtImportResult, event)
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type DocumentManagementSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewDocumentManagementSocketNotifier(socket *socket_api.SocketServiceClient) *DocumentManagementSocketNotifier {
	return &DocumentManagementSocketNotifier{socket}
}
func (n *DocumentManagementSocketNotifier) NotifyCareProviderDocumentManagementChange(ctx *titan.Context, event *EventDocumentManagementChange) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_DocumentManagementChange,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *DocumentManagementSocketNotifier) NotifyUserDocumentManagementChange(ctx *titan.Context, event *EventDocumentManagementChange) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_DocumentManagementChange,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *DocumentManagementSocketNotifier) NotifyDeviceDocumentManagementChange(ctx *titan.Context, event *EventDocumentManagementChange) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_DocumentManagementChange,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *DocumentManagementSocketNotifier) NotifyClientDocumentManagementChange(ctx *titan.Context, event *EventDocumentManagementChange) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_DocumentManagementChange,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}
func (n *DocumentManagementSocketNotifier) NotifyCareProviderGdtExportResult(ctx *titan.Context, event *EventGdtExportResult) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_GdtExportResult,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *DocumentManagementSocketNotifier) NotifyUserGdtExportResult(ctx *titan.Context, event *EventGdtExportResult) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_GdtExportResult,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *DocumentManagementSocketNotifier) NotifyDeviceGdtExportResult(ctx *titan.Context, event *EventGdtExportResult) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_GdtExportResult,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *DocumentManagementSocketNotifier) NotifyClientGdtExportResult(ctx *titan.Context, event *EventGdtExportResult) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_GdtExportResult,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}
func (n *DocumentManagementSocketNotifier) NotifyCareProviderGdtImportResult(ctx *titan.Context, event *EventGdtImportResult) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_GdtImportResult,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *DocumentManagementSocketNotifier) NotifyUserGdtImportResult(ctx *titan.Context, event *EventGdtImportResult) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_GdtImportResult,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *DocumentManagementSocketNotifier) NotifyDeviceGdtImportResult(ctx *titan.Context, event *EventGdtImportResult) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_GdtImportResult,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *DocumentManagementSocketNotifier) NotifyClientGdtImportResult(ctx *titan.Context, event *EventGdtImportResult) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_GdtImportResult,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}

// -----------------------------------------------
// Test event listener
type DocumentManagementEventListener struct {
	mux                                   sync.Mutex
	LastEventDocumentManagementChangeList []EventDocumentManagementChange
	LastEventGdtExportResultList          []EventGdtExportResult
	LastEventGdtImportResultList          []EventGdtImportResult
}

func (listener *DocumentManagementEventListener) Reset() {
	listener.mux.Lock()
	listener.LastEventDocumentManagementChangeList = []EventDocumentManagementChange{}
	listener.LastEventGdtExportResultList = []EventGdtExportResult{}
	listener.LastEventGdtImportResultList = []EventGdtImportResult{}
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *DocumentManagementEventListener) Subscribe(s *titan.MessageSubscriber) {
	s.Register(EVENT_DocumentManagementChange, "api.app.mvz_AppMvzDocumentManagement_DocumentManagementChange_Queue_test", func(p *titan.Message) error {
		var resp EventDocumentManagementChange
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventDocumentManagementChangeList) >= 100 {
			listener.LastEventDocumentManagementChangeList = listener.LastEventDocumentManagementChangeList[1:]
		}
		listener.LastEventDocumentManagementChangeList = append(listener.LastEventDocumentManagementChangeList, resp)
		listener.mux.Unlock()
		return nil
	})
	s.Register(EVENT_GdtExportResult, "api.app.mvz_AppMvzDocumentManagement_GdtExportResult_Queue_test", func(p *titan.Message) error {
		var resp EventGdtExportResult
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventGdtExportResultList) >= 100 {
			listener.LastEventGdtExportResultList = listener.LastEventGdtExportResultList[1:]
		}
		listener.LastEventGdtExportResultList = append(listener.LastEventGdtExportResultList, resp)
		listener.mux.Unlock()
		return nil
	})
	s.Register(EVENT_GdtImportResult, "api.app.mvz_AppMvzDocumentManagement_GdtImportResult_Queue_test", func(p *titan.Message) error {
		var resp EventGdtImportResult
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventGdtImportResultList) >= 100 {
			listener.LastEventGdtImportResultList = listener.LastEventGdtImportResultList[1:]
		}
		listener.LastEventGdtImportResultList = append(listener.LastEventGdtImportResultList, resp)
		listener.mux.Unlock()
		return nil
	})
}
func (listener *DocumentManagementEventListener) GetLastEventDocumentManagementChangeList(timeOutInMilliSeconds time.Duration) []EventDocumentManagementChange {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventDocumentManagementChangeList
		default:
			// if any value
			if len(listener.LastEventDocumentManagementChangeList) > 0 {
				return listener.LastEventDocumentManagementChangeList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
func (listener *DocumentManagementEventListener) GetLastEventGdtExportResultList(timeOutInMilliSeconds time.Duration) []EventGdtExportResult {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventGdtExportResultList
		default:
			// if any value
			if len(listener.LastEventGdtExportResultList) > 0 {
				return listener.LastEventGdtExportResultList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
func (listener *DocumentManagementEventListener) GetLastEventGdtImportResultList(timeOutInMilliSeconds time.Duration) []EventGdtImportResult {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventGdtImportResultList
		default:
			// if any value
			if len(listener.LastEventGdtImportResultList) > 0 {
				return listener.LastEventGdtImportResultList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
