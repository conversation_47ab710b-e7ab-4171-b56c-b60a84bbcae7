// This code was autogenerated from app/mvz/bg_billing.proto, do not edit.

package bg_billing

import (
	"encoding/json"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	common1 "git.tutum.dev/medi/tutum/ares/service/domains/api/common"

	patient_profile_common "git.tutum.dev/medi/tutum/ares/service/domains/api/patient_profile_common"

	common "git.tutum.dev/medi/tutum/ares/service/domains/bg_billing/common"

	common2 "git.tutum.dev/medi/tutum/ares/service/domains/timeline/common"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type CreateBgBillingRequest struct {
	Item *common.BillingRecord `json:"item"`
}

type GetBgBillingsRequest struct {
	Search     *string                    `json:"search"`
	Pagination *common1.PaginationRequest `json:"pagination"`
	Filter     *common.BgBillingFilter    `json:"filter" bson:"omitempty"`
}

type GetBgBillingsResponse struct {
	Items []*common.BgBillingItem `json:"items"`
	Total int64                   `json:"total"`
}

type GetBgBillingByScheinIdRequest struct {
	ScheinId *uuid.UUID `json:"scheinId"`
}

type GetBgBillingByScheinIdResponse struct {
	Item *common.BgBillingItem `json:"item"`
}

type GetBgBillingByIdRequest struct {
	Id *uuid.UUID `json:"id" validate:"required"`
}

type GetBgBillingByIdResponse struct {
	Item *common.BgBillingItem `json:"item"`
}

type GetUvGoaServiceCodeRequest struct {
	Id *uuid.UUID `json:"id" validate:"required"`
}

type GetUvGoaServiceCodeByIdsRequest struct {
	Ids []uuid.UUID `json:"ids" validate:"required"`
}

type UvGoaServiceTimelineData struct {
	BillingId      *uuid.UUID               `json:"billingId"`
	TimelineModels []*common2.TimelineModel `json:"timelineModels"`
	InvoiceInfo    *common.InvoiceInfo      `json:"invoiceInfo"`
}

type GetUvGoaServiceCodeResponse struct {
	Data *UvGoaServiceTimelineData `json:"data"`
}

type GetUvGoaServiceCodeByIdsResponse struct {
	Data []*UvGoaServiceTimelineData `json:"data"`
}

type GetPrintedInvoicesRequest struct {
	PatientId *uuid.UUID `json:"patientId" validate:"required"`
	BillingId *uuid.UUID `json:"billingId" validate:"required"`
}

type GetPrintedInvoicesResponse struct {
	TimelineModels []*common2.TimelineModel `json:"timelineModels"`
}

type MarkBgBillingPaidRequest struct {
	PatientId     *uuid.UUID `json:"patientId"`
	BillingId     *uuid.UUID `json:"billingId"`
	InvoiceNumber string     `json:"invoiceNumber"`
}

type MarkBgBillingPaidResponse struct {
	TimelineModel *common2.TimelineModel `json:"timelineModel"`
}

type MarkBgBillingUnpaidRequest struct {
	PatientId *uuid.UUID `json:"patientId"`
	BillingId *uuid.UUID `json:"billingId"`
}

type MarkBgBillingUnpaidResponse struct {
	Item *common.BgBillingItem `json:"item"`
}

type MarkBgBillingCancelledRequest struct {
	PatientId *uuid.UUID `json:"patientId"`
	BillingId *uuid.UUID `json:"billingId"`
}

type MarkBgBillingCancelledResponse struct {
	Item *common.BgBillingItem `json:"item"`
}

type GetListDoctorResponse struct {
	Doctors []*common.Doctor `json:"doctors"`
}

type GetListInsuranceResponse struct {
	Insurances []*patient_profile_common.InsuranceInfo `json:"insurances"`
}

// enum definitions

// Define constants
const NATS_SUBJECT = "api.app.mvz" // nats subject this service will listen to

// service event constants
const EVENT_GetBgBillingByScheinId = "api.app.mvz.BgBillingApp.GetBgBillingByScheinId"
const LEGACY_TOPIC_GetBgBillingByScheinId = "/api/app/mvz/bg/billing/getBgBillingByScheinId"
const EVENT_GetBgBilling = "api.app.mvz.BgBillingApp.GetBgBilling"
const LEGACY_TOPIC_GetBgBilling = "/api/app/mvz/bg/billing/getBgBilling"
const EVENT_GetUvGoaServiceCode = "api.app.mvz.BgBillingApp.GetUvGoaServiceCode"
const LEGACY_TOPIC_GetUvGoaServiceCode = "/api/app/mvz/bg/billing/getUvGoaServiceCode"
const EVENT_GetUvGoaServiceCodeByIds = "api.app.mvz.BgBillingApp.GetUvGoaServiceCodeByIds"
const LEGACY_TOPIC_GetUvGoaServiceCodeByIds = "/api/app/mvz/bg/billing/getUvGoaServiceCodeByIds"
const EVENT_GetBgBillingById = "api.app.mvz.BgBillingApp.GetBgBillingById"
const LEGACY_TOPIC_GetBgBillingById = "/api/app/mvz/bg/billing/getBgBillingById"
const EVENT_GetPrintedInvoices = "api.app.mvz.BgBillingApp.GetPrintedInvoices"
const LEGACY_TOPIC_GetPrintedInvoices = "/api/app/mvz/bg/billing/getPrintedInvoices"
const EVENT_MarkBgBillingPaid = "api.app.mvz.BgBillingApp.MarkBgBillingPaid"
const LEGACY_TOPIC_MarkBgBillingPaid = "/api/app/mvz/bg/billing/markBgBillingPaid"
const EVENT_MarkBgBillingUnpaid = "api.app.mvz.BgBillingApp.MarkBgBillingUnpaid"
const LEGACY_TOPIC_MarkBgBillingUnpaid = "/api/app/mvz/bg/billing/markBgBillingUnpaid"
const EVENT_MarkBgBillingCancelled = "api.app.mvz.BgBillingApp.MarkBgBillingCancelled"
const LEGACY_TOPIC_MarkBgBillingCancelled = "/api/app/mvz/bg/billing/markBgBillingCancelled"
const EVENT_GetListDoctor = "api.app.mvz.BgBillingApp.GetListDoctor"
const LEGACY_TOPIC_GetListDoctor = "/api/app/mvz/bg/billing/getListDoctor"
const EVENT_GetListInsurance = "api.app.mvz.BgBillingApp.GetListInsurance"
const LEGACY_TOPIC_GetListInsurance = "/api/app/mvz/bg/billing/getListInsurance"

// message event constants

// Define service interface -------------------------------------------------------------
type BgBillingApp interface {
	GetBgBillingByScheinId(ctx *titan.Context, request *GetBgBillingByScheinIdRequest) (*GetBgBillingByScheinIdResponse, error)
	GetBgBilling(ctx *titan.Context, request *GetBgBillingsRequest) (*GetBgBillingsResponse, error)
	GetUvGoaServiceCode(ctx *titan.Context, request *GetUvGoaServiceCodeRequest) (*GetUvGoaServiceCodeResponse, error)
	GetUvGoaServiceCodeByIds(ctx *titan.Context, request *GetUvGoaServiceCodeByIdsRequest) (*GetUvGoaServiceCodeByIdsResponse, error)
	GetBgBillingById(ctx *titan.Context, request *GetBgBillingByIdRequest) (*GetBgBillingByIdResponse, error)
	GetPrintedInvoices(ctx *titan.Context, request *GetPrintedInvoicesRequest) (*GetPrintedInvoicesResponse, error)
	MarkBgBillingPaid(ctx *titan.Context, request *MarkBgBillingPaidRequest) (*MarkBgBillingPaidResponse, error)
	MarkBgBillingUnpaid(ctx *titan.Context, request *MarkBgBillingUnpaidRequest) (*MarkBgBillingUnpaidResponse, error)
	MarkBgBillingCancelled(ctx *titan.Context, request *MarkBgBillingCancelledRequest) (*MarkBgBillingCancelledResponse, error)
	GetListDoctor(ctx *titan.Context) (*GetListDoctorResponse, error)
	GetListInsurance(ctx *titan.Context) (*GetListInsuranceResponse, error)
}

// Define service proxy -------------------------------------------------------------------
type BgBillingAppProxy struct {
	service BgBillingApp
}

func (srv *BgBillingAppProxy) GetBgBillingByScheinId(ctx *titan.Context, request *GetBgBillingByScheinIdRequest) (*GetBgBillingByScheinIdResponse, error) {
	return srv.service.GetBgBillingByScheinId(ctx, request)
}
func (srv *BgBillingAppProxy) GetBgBilling(ctx *titan.Context, request *GetBgBillingsRequest) (*GetBgBillingsResponse, error) {
	return srv.service.GetBgBilling(ctx, request)
}
func (srv *BgBillingAppProxy) GetUvGoaServiceCode(ctx *titan.Context, request *GetUvGoaServiceCodeRequest) (*GetUvGoaServiceCodeResponse, error) {
	return srv.service.GetUvGoaServiceCode(ctx, request)
}
func (srv *BgBillingAppProxy) GetUvGoaServiceCodeByIds(ctx *titan.Context, request *GetUvGoaServiceCodeByIdsRequest) (*GetUvGoaServiceCodeByIdsResponse, error) {
	return srv.service.GetUvGoaServiceCodeByIds(ctx, request)
}
func (srv *BgBillingAppProxy) GetBgBillingById(ctx *titan.Context, request *GetBgBillingByIdRequest) (*GetBgBillingByIdResponse, error) {
	return srv.service.GetBgBillingById(ctx, request)
}
func (srv *BgBillingAppProxy) GetPrintedInvoices(ctx *titan.Context, request *GetPrintedInvoicesRequest) (*GetPrintedInvoicesResponse, error) {
	return srv.service.GetPrintedInvoices(ctx, request)
}
func (srv *BgBillingAppProxy) MarkBgBillingPaid(ctx *titan.Context, request *MarkBgBillingPaidRequest) (*MarkBgBillingPaidResponse, error) {
	return srv.service.MarkBgBillingPaid(ctx, request)
}
func (srv *BgBillingAppProxy) MarkBgBillingUnpaid(ctx *titan.Context, request *MarkBgBillingUnpaidRequest) (*MarkBgBillingUnpaidResponse, error) {
	return srv.service.MarkBgBillingUnpaid(ctx, request)
}
func (srv *BgBillingAppProxy) MarkBgBillingCancelled(ctx *titan.Context, request *MarkBgBillingCancelledRequest) (*MarkBgBillingCancelledResponse, error) {
	return srv.service.MarkBgBillingCancelled(ctx, request)
}
func (srv *BgBillingAppProxy) GetListDoctor(ctx *titan.Context) (*GetListDoctorResponse, error) {
	return srv.service.GetListDoctor(ctx)
}
func (srv *BgBillingAppProxy) GetListInsurance(ctx *titan.Context) (*GetListInsuranceResponse, error) {
	return srv.service.GetListInsurance(ctx)
}

// Define service router -----------------------------------------------------------------
type BgBillingAppRouter struct {
	proxy *BgBillingAppProxy
}

func (router *BgBillingAppRouter) Register(r titan.Router) {
	r.RegisterTopic(EVENT_GetBgBillingByScheinId, router.proxy.GetBgBillingByScheinId, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetBgBillingByScheinId, router.proxy.GetBgBillingByScheinId, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetBgBilling, router.proxy.GetBgBilling, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetBgBilling, router.proxy.GetBgBilling, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetUvGoaServiceCode, router.proxy.GetUvGoaServiceCode, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetUvGoaServiceCode, router.proxy.GetUvGoaServiceCode, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetUvGoaServiceCodeByIds, router.proxy.GetUvGoaServiceCodeByIds, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetUvGoaServiceCodeByIds, router.proxy.GetUvGoaServiceCodeByIds, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetBgBillingById, router.proxy.GetBgBillingById, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetBgBillingById, router.proxy.GetBgBillingById, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetPrintedInvoices, router.proxy.GetPrintedInvoices, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetPrintedInvoices, router.proxy.GetPrintedInvoices, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_MarkBgBillingPaid, router.proxy.MarkBgBillingPaid, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_MarkBgBillingPaid, router.proxy.MarkBgBillingPaid, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_MarkBgBillingUnpaid, router.proxy.MarkBgBillingUnpaid, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_MarkBgBillingUnpaid, router.proxy.MarkBgBillingUnpaid, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_MarkBgBillingCancelled, router.proxy.MarkBgBillingCancelled, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_MarkBgBillingCancelled, router.proxy.MarkBgBillingCancelled, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetListDoctor, router.proxy.GetListDoctor, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetListDoctor, router.proxy.GetListDoctor, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(EVENT_GetListInsurance, router.proxy.GetListInsurance, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetListInsurance, router.proxy.GetListInsurance, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
}

// Subscriber
func (router *BgBillingAppRouter) Subscribe(s *titan.MessageSubscriber) {
}

func NewBgBillingAppRouter(s BgBillingApp) *BgBillingAppRouter {
	p := &BgBillingAppProxy{s}
	return &BgBillingAppRouter{p}
}

func NewServer(bff0 BgBillingApp, options ...titan.Option) *titan.Server {
	opt := options

	router0 := NewBgBillingAppRouter(bff0)
	opt = append(opt, titan.Routes(router0.Register), titan.Subscribe(router0.Subscribe))

	return titan.NewServer(NATS_SUBJECT, opt...)
}

// Define client ----------------------------------------------------------------------------------------------
type BgBillingAppClient struct {
	client *titan.Client
}

func (srv *BgBillingAppClient) GetBgBillingByScheinId(ctx *titan.Context, request *GetBgBillingByScheinIdRequest) (*GetBgBillingByScheinIdResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetBgBillingByScheinId).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetBgBillingByScheinIdResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *BgBillingAppClient) GetBgBilling(ctx *titan.Context, request *GetBgBillingsRequest) (*GetBgBillingsResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetBgBilling).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetBgBillingsResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *BgBillingAppClient) GetUvGoaServiceCode(ctx *titan.Context, request *GetUvGoaServiceCodeRequest) (*GetUvGoaServiceCodeResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetUvGoaServiceCode).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetUvGoaServiceCodeResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *BgBillingAppClient) GetUvGoaServiceCodeByIds(ctx *titan.Context, request *GetUvGoaServiceCodeByIdsRequest) (*GetUvGoaServiceCodeByIdsResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetUvGoaServiceCodeByIds).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetUvGoaServiceCodeByIdsResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *BgBillingAppClient) GetBgBillingById(ctx *titan.Context, request *GetBgBillingByIdRequest) (*GetBgBillingByIdResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetBgBillingById).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetBgBillingByIdResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *BgBillingAppClient) GetPrintedInvoices(ctx *titan.Context, request *GetPrintedInvoicesRequest) (*GetPrintedInvoicesResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetPrintedInvoices).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &GetPrintedInvoicesResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *BgBillingAppClient) MarkBgBillingPaid(ctx *titan.Context, request *MarkBgBillingPaidRequest) (*MarkBgBillingPaidResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_MarkBgBillingPaid).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &MarkBgBillingPaidResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *BgBillingAppClient) MarkBgBillingUnpaid(ctx *titan.Context, request *MarkBgBillingUnpaidRequest) (*MarkBgBillingUnpaidResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_MarkBgBillingUnpaid).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &MarkBgBillingUnpaidResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *BgBillingAppClient) MarkBgBillingCancelled(ctx *titan.Context, request *MarkBgBillingCancelledRequest) (*MarkBgBillingCancelledResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_MarkBgBillingCancelled).
		Subject(NATS_SUBJECT).
		BodyJSON(request).
		Build()
	var resp = &MarkBgBillingCancelledResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *BgBillingAppClient) GetListDoctor(ctx *titan.Context) (*GetListDoctorResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetListDoctor).
		Subject(NATS_SUBJECT).
		Build()
	var resp = &GetListDoctorResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *BgBillingAppClient) GetListInsurance(ctx *titan.Context) (*GetListInsuranceResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetListInsurance).
		Subject(NATS_SUBJECT).
		Build()
	var resp = &GetListInsuranceResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func NewBgBillingAppClient(clients ...*titan.Client) *BgBillingAppClient {
	var client *titan.Client
	if len(clients) <= 0 {
		client = titan.GetDefaultClient()
	} else {
		client = clients[0]
	}
	return &BgBillingAppClient{client: client}
}

// Define event Notifier -----------------------------------------------------------------------------------------
type BgBillingNotifier struct {
	client *titan.Client
}

func NewBgBillingNotifier() *BgBillingNotifier {
	client := titan.GetDefaultClient()
	return &BgBillingNotifier{client}
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type BgBillingSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewBgBillingSocketNotifier(socket *socket_api.SocketServiceClient) *BgBillingSocketNotifier {
	return &BgBillingSocketNotifier{socket}
}

// -----------------------------------------------
// Test event listener
type BgBillingEventListener struct {
	mux sync.Mutex
}

func (listener *BgBillingEventListener) Reset() {
	listener.mux.Lock()
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *BgBillingEventListener) Subscribe(s *titan.MessageSubscriber) {
}
