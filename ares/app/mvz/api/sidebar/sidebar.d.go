// This code was autogenerated from app/mvz/sidebar.proto, do not edit.

package sidebar

import (
	"encoding/json"
	"errors"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	infra "gitlab.com/silenteer-oss/hestia/infrastructure"
	socket_api "gitlab.com/silenteer-oss/hestia/socket_service/api"
	"gitlab.com/silenteer-oss/titan"

	goff "gitlab.com/silenteer-oss/goff"

	"sync"
	"time"
)

var _ = uuid.Nil
var _ = proto.Marshal
var _ goff.UUID
var _ socket_api.Topic
var _ json.Decoder
var _ time.Duration
var _ infra.Empty

// Type definitions
type GetDefaultInformationResponse struct {
	HasNotSubmittedEnrollment bool `json:"hasNotSubmittedEnrollment"`
	HasPendingPtvImport       bool `json:"hasPendingPtvImport"`
}

type EventSidebarRepsonse struct {
	UserId                    uuid.UUID `json:"userId"`
	HasNotSubmittedEnrollment bool      `json:"hasNotSubmittedEnrollment"`
	HasPendingPtvImport       bool      `json:"hasPendingPtvImport"`
}

// enum definitions

// Define constants
const NATS_SUBJECT = "api.app.mvz" // nats subject this service will listen to

// service event constants
const EVENT_GetDefaultInformation = "api.app.mvz.SidebarApp.GetDefaultInformation"
const LEGACY_TOPIC_GetDefaultInformation = "/api/app/mvz/sidebar/getDefaultInformation"
const EVENT_HandleEventSidebar = "api.app.mvz.SidebarApp.HandleEventSidebar"
const LEGACY_TOPIC_HandleEventSidebar = "/api/app/mvz/sidebar/handleEventSidebar"
const EVENT_HandleEventForPatientOverview = "api.app.mvz.SidebarApp.HandleEventForPatientOverview"
const LEGACY_TOPIC_HandleEventForPatientOverview = "/api/app/mvz/sidebar/handleEventForPatientOverview"

// message event constants
const EVENT_SidebarRepsonse = "api.app.mvz.AppMvzSidebar.SidebarRepsonse"

// Define service interface -------------------------------------------------------------
type SidebarApp interface {
	GetDefaultInformation(ctx *titan.Context) (*GetDefaultInformationResponse, error)
	HandleEventSidebar(ctx *titan.Context, request EventSidebarRepsonse) error
	HandleEventForPatientOverview(ctx *titan.Context, request EventSidebarRepsonse) error
}

// Define service proxy -------------------------------------------------------------------
type SidebarAppProxy struct {
	service SidebarApp
}

func (srv *SidebarAppProxy) GetDefaultInformation(ctx *titan.Context) (*GetDefaultInformationResponse, error) {
	return srv.service.GetDefaultInformation(ctx)
}
func (srv *SidebarAppProxy) HandleEventSidebar(ctx *titan.Context, request EventSidebarRepsonse) error {
	return srv.service.HandleEventSidebar(ctx, request)
}
func (srv *SidebarAppProxy) HandleEventForPatientOverview(ctx *titan.Context, request EventSidebarRepsonse) error {
	return srv.service.HandleEventForPatientOverview(ctx, request)
}

// Define service router -----------------------------------------------------------------
type SidebarAppRouter struct {
	proxy *SidebarAppProxy
}

func (router *SidebarAppRouter) Register(r titan.Router) {
	r.RegisterTopic(EVENT_GetDefaultInformation, router.proxy.GetDefaultInformation, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
	r.RegisterTopic(LEGACY_TOPIC_GetDefaultInformation, router.proxy.GetDefaultInformation, titan.IsAuthenticated(), titan.Secured(infra.CARE_PROVIDER_MEMBER))
}

// Subscriber
func (router *SidebarAppRouter) Subscribe(s *titan.MessageSubscriber) {
	s.Register(EVENT_SidebarRepsonse, "api.app.mvz_SidebarApp_HandleEventSidebar_Queue", func(p *titan.Message) error {
		var resp EventSidebarRepsonse
		ctx, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		return router.proxy.HandleEventSidebar(ctx, resp)
	})
	s.Register(EVENT_SidebarRepsonse, "api.app.mvz_SidebarApp_HandleEventForPatientOverview_Queue", func(p *titan.Message) error {
		var resp EventSidebarRepsonse
		ctx, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		return router.proxy.HandleEventForPatientOverview(ctx, resp)
	})
}

func NewSidebarAppRouter(s SidebarApp) *SidebarAppRouter {
	p := &SidebarAppProxy{s}
	return &SidebarAppRouter{p}
}

func NewServer(bff0 SidebarApp, options ...titan.Option) *titan.Server {
	opt := options

	router0 := NewSidebarAppRouter(bff0)
	opt = append(opt, titan.Routes(router0.Register), titan.Subscribe(router0.Subscribe))

	return titan.NewServer(NATS_SUBJECT, opt...)
}

// Define client ----------------------------------------------------------------------------------------------
type SidebarAppClient struct {
	client *titan.Client
}

func (srv *SidebarAppClient) GetDefaultInformation(ctx *titan.Context) (*GetDefaultInformationResponse, error) {
	req, _ := titan.NewReqBuilder().
		Post(EVENT_GetDefaultInformation).
		Subject(NATS_SUBJECT).
		Build()
	var resp = &GetDefaultInformationResponse{}
	err := srv.client.SendAndReceiveJson(ctx, req, &resp)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func NewSidebarAppClient(clients ...*titan.Client) *SidebarAppClient {
	var client *titan.Client
	if len(clients) <= 0 {
		client = titan.GetDefaultClient()
	} else {
		client = clients[0]
	}
	return &SidebarAppClient{client: client}
}

// Define event Notifier -----------------------------------------------------------------------------------------
type SidebarNotifier struct {
	client *titan.Client
}

func NewSidebarNotifier() *SidebarNotifier {
	client := titan.GetDefaultClient()
	return &SidebarNotifier{client}
}
func (p *SidebarNotifier) NotifySidebarRepsonse(ctx *titan.Context, event *EventSidebarRepsonse) error {
	return p.client.Publish(ctx, EVENT_SidebarRepsonse, event)
}

// Define socket event notifier -----------------------------------------------------------------------------------------
type SidebarSocketNotifier struct {
	socket *socket_api.SocketServiceClient
}

func NewSidebarSocketNotifier(socket *socket_api.SocketServiceClient) *SidebarSocketNotifier {
	return &SidebarSocketNotifier{socket}
}
func (n *SidebarSocketNotifier) NotifyCareProviderSidebarRepsonse(ctx *titan.Context, event *EventSidebarRepsonse) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_SidebarRepsonse,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToCareProviderMembersRequest{
		CareProviderId: ctx.UserInfo().CareProviderUUID(),
		MessageInfo:    messageInfo,
	}

	_, er := n.socket.SendToCareProviderMembers(ctx, message)
	return er
}
func (n *SidebarSocketNotifier) NotifyUserSidebarRepsonse(ctx *titan.Context, event *EventSidebarRepsonse) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_SidebarRepsonse,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToUserRequest{
		UserId:      ctx.UserInfo().UserUUID(),
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToUser(ctx, message)
	return er
}

func (n *SidebarSocketNotifier) NotifyDeviceSidebarRepsonse(ctx *titan.Context, event *EventSidebarRepsonse) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_SidebarRepsonse,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToDeviceRequest{
		DeviceId:    ctx.UserInfo().DeviceId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToDevice(ctx, message)
	return er
}
func (n *SidebarSocketNotifier) NotifyClientSidebarRepsonse(ctx *titan.Context, event *EventSidebarRepsonse) error {
	bytes, e := json.Marshal(event)
	if e != nil {
		return e
	}
	sessionId := ctx.SessionId()
	if sessionId == "" {
		return errors.New("sessionId is incorrect")
	}
	if len(sessionId) == 6 {
		return errors.New("sessionId is incorrect, you should pass global['KEY_GLOBAL_SESSION_ID'] to 'X-Session-Id' in headers request.")
	}

	messageInfo := &socket_api.SendRequestMessage{
		Id:          ctx.RequestId(),
		Topic:       EVENT_SidebarRepsonse,
		MessageBody: string(bytes),
		StatusCode:  200,
	}
	message := &socket_api.SendToClientRequest{
		SessionId:   sessionId,
		MessageInfo: messageInfo,
	}

	_, er := n.socket.SendToClient(ctx, message)
	return er
}

// -----------------------------------------------
// Test event listener
type SidebarEventListener struct {
	mux                          sync.Mutex
	LastEventSidebarRepsonseList []EventSidebarRepsonse
}

func (listener *SidebarEventListener) Reset() {
	listener.mux.Lock()
	listener.LastEventSidebarRepsonseList = []EventSidebarRepsonse{}
	listener.mux.Unlock()
}

// Test Subscribe to events
func (listener *SidebarEventListener) Subscribe(s *titan.MessageSubscriber) {
	s.Register(EVENT_SidebarRepsonse, "api.app.mvz_AppMvzSidebar_SidebarRepsonse_Queue_test", func(p *titan.Message) error {
		var resp EventSidebarRepsonse
		_, err := p.Parse(&resp)
		if err != nil {
			return err
		}
		listener.mux.Lock()
		if len(listener.LastEventSidebarRepsonseList) >= 100 {
			listener.LastEventSidebarRepsonseList = listener.LastEventSidebarRepsonseList[1:]
		}
		listener.LastEventSidebarRepsonseList = append(listener.LastEventSidebarRepsonseList, resp)
		listener.mux.Unlock()
		return nil
	})
}
func (listener *SidebarEventListener) GetLastEventSidebarRepsonseList(timeOutInMilliSeconds time.Duration) []EventSidebarRepsonse {
	timeout := time.After(timeOutInMilliSeconds * time.Millisecond)
	for {
		select {
		case <-timeout:
			return listener.LastEventSidebarRepsonseList
		default:
			// if any value
			if len(listener.LastEventSidebarRepsonseList) > 0 {
				return listener.LastEventSidebarRepsonseList
			}
			time.Sleep(50 * time.Millisecond)
		}
	}
}
