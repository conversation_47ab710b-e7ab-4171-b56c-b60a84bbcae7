package patient_search

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"

	"emperror.dev/errors"
	"git.tutum.dev/medi/tutum/ares/app/mvz/api/patient_search"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/mvz/schein"
	"git.tutum.dev/medi/tutum/ares/service/domains/repos/profile/patient"
	"git.tutum.dev/medi/tutum/pkg/slice"
	"git.tutum.dev/medi/tutum/pkg/util"
	"github.com/google/uuid"

	"gitlab.com/silenteer-oss/titan"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type PatientProfileRepository struct {
	patient.PatientProfileDefaultRepository
	scheinRepo schein.ScheinRepoDefaultRepository
}

func InitPatientProfileRepository() *PatientProfileRepository {
	return &PatientProfileRepository{
		patient.NewPatientProfileDefaultRepository(),
		schein.NewScheinRepoDefaultRepository(),
	}
}

// ExtractTimeFromString extracts the time from a string
// -> DD.MM.YYYY => Patients have this birthday
// -> DDMMYYYY => Patients have this birthday
// -> DDMMYY => Patients have this birthday
// -> DD.MM.YY => Patients have this birthday
// -> MM.YYYY => Patients have this birth month (regardless of day of the month)
// -> MM.YY => Patients have this birth month (regardless of day of the month)
// -> DD.MM => Patients have date and month regardless of year
// -> YYYY => Patients have this birth year (regardless of day or month)
// -> YY => Patients have this birth year (regardless of day or month)

func AddYearPrefix(yearYY, currentYearYY int) []int {
	res := []int{}

	if currentYearYY > 99 {
		return res
	}
	if yearYY > 999 {
		yearYY, _ = strconv.Atoi(fmt.Sprintf("%v", yearYY)[2:4])
	}
	if yearYY <= currentYearYY {
		res = []int{yearYY + 1900, yearYY + 2000} //	ex: yearYY = 23 -> 1923, 2023
	} else {
		res = []int{yearYY + 1900} //	ex: yearYY = 50 -> 1950
	}

	return res
}

var dobRegex = regexp.MustCompile(`^\d{2,8}$`)

func ExtractTimeFromString(ctx *titan.Context, dob string) [][3]int {
	res := [][3]int{}
	dob = strings.Trim(dob, " ")

	nowYearYYStr := fmt.Sprintf("%v", util.NowYear(ctx))[2:4]
	nowYearYY, _ := strconv.Atoi(nowYearYYStr)

	dobParts := strings.Split(dob, ".")

	if len(dobParts) == 3 {
		res = ExtractFullTime(dobParts, nowYearYY)
	} else if len(dobParts) == 2 {
		res = ExtractFromTwoElement(dobParts, nowYearYY)
	} else if len(dobParts) == 1 {
		res = ExtractFromOneElement(dobParts, nowYearYY)
	}
	return res
}

func toQueryDOB(dobs [][3]int) bson.M {
	filter := bson.M{}
	orQueries := []bson.M{}

	if len(dobs) == 0 {
		return filter
	}

	for _, dob := range dobs {
		day := dob[0]
		month := dob[1]
		year := dob[2]

		filterDob := bson.M{}

		if day == -1 {
			filterDob["patientInfo.personalinfo.dateofbirth.date"] = bson.M{"$eq": nil}
		} else if day > 0 {
			filterDob["patientInfo.personalinfo.dateofbirth.date"] = day
		}
		if month == -1 {
			filterDob["patientInfo.personalinfo.dateofbirth.month"] = bson.M{"$eq": nil}
		} else if month > 0 {
			filterDob["patientInfo.personalinfo.dateofbirth.month"] = month
		}
		if year == -1 {
			filterDob["patientInfo.personalinfo.dateofbirth.year"] = bson.M{"$eq": nil}
		} else if year > 0 {
			filterDob["patientInfo.personalinfo.dateofbirth.year"] = year
		}
		orQueries = append(orQueries, filterDob)
	}
	filter["$or"] = orQueries

	return filter
}

func (r *PatientProfileRepository) Search(ctx *titan.Context, request patient_search.PatientSearchingRequest) ([]patient.PatientProfileSearch, error) {
	filter := bson.M{}
	var (
		err                     error
		patientIdsWithFieldName []schein.PatientIdWithFieldValue
	)
	switch request.SearchingType {
	case patient_search.PatientName:
		names := strings.Split(request.SearchingKeyword, ",")
		if len(names) > 3 {
			return nil, nil
		}
		if len(names) == 1 {
			lastOrFirstNameRegex := createPrefixRegex(names[0])
			filter["$or"] = []bson.M{
				{
					patient.Field_LastName: bson.M{
						"$regex":   lastOrFirstNameRegex,
						"$options": "i",
					},
				},
				{
					patient.Field_FirstName: bson.M{
						"$regex":   lastOrFirstNameRegex,
						"$options": "i",
					},
				},
			}
		}

		if len(names) == 2 {
			regexLastName := createPrefixRegex(names[0])
			regexFirstName := createPrefixRegex(names[1])

			filter = bson.M{
				"$and": []bson.M{
					{
						patient.Field_LastName: bson.M{
							"$regex":   regexLastName,
							"$options": "i",
						},
					},
					{
						patient.Field_FirstName: bson.M{
							"$regex":   regexFirstName,
							"$options": "i",
						},
					},
				},
			}
		}
		// first name, last name, dob
		if len(names) == 3 {
			regexLastName := createPrefixRegex(names[0])
			regexFirstName := createPrefixRegex(names[1])

			filter = bson.M{
				"$and": []bson.M{
					{
						patient.Field_LastName: bson.M{
							"$regex":   regexLastName,
							"$options": "i",
						},
					},
					{
						patient.Field_FirstName: bson.M{
							"$regex":   regexFirstName,
							"$options": "i",
						},
					},
				},
			}

			dateTimes := ExtractTimeFromString(ctx, names[2])
			dobFilter := toQueryDOB(dateTimes)
			if len(dobFilter) > 0 {
				filter["$and"] = append(filter["$and"].([]bson.M), dobFilter)
			}
		}

	case patient_search.PatientNumber:
		patientNumber, err := strconv.ParseInt(strings.TrimSpace(request.SearchingKeyword), 10, 64)
		if err != nil {
			return nil, errors.Wrap(err, "Keyword wrong format. Should be number")
		}

		patientNumberField := fmt.Sprintf("%s.%s", patient.Field_PatientInfo, patient.Field_PatientInfo_PatientNumber)
		filter[patientNumberField] = patientNumber
	case patient_search.Birthdate:
		dateTimes := ExtractTimeFromString(ctx, strings.TrimSpace(request.SearchingKeyword))
		dobFilter := toQueryDOB(dateTimes)
		filter = dobFilter
	case patient_search.AdditionalSKTInformation:
		patientIdsWithFieldName, err = r.scheinRepo.GetPatientIdsByFieldName(ctx, schein.Field_Ad4124, request.SearchingKeyword)
		if err != nil {
			return nil, fmt.Errorf("get patient ids by field name return error: %w", err)
		}
		if len(patientIdsWithFieldName) == 0 {
			return nil, nil
		}
		filter = bson.M{
			patient.Field_Id: bson.M{
				"$in": slice.Map(patientIdsWithFieldName, func(t schein.PatientIdWithFieldValue) uuid.UUID {
					return t.PatientId
				}),
			},
		}
	default:
		return nil, nil
	}

	filter[repos.Field_IsDeleted] = bson.M{
		"$ne": true,
	}

	patientProfileResponse, err := r.Find(ctx, filter, &options.FindOptions{Limit: util.NewPointer(int64(30))})
	if err != nil {
		ctx.Logger().Error(fmt.Sprintf("Call DB Gateway service with sort condition return error %+v", err))
		return nil, err
	}

	if len(patientProfileResponse) == 0 {
		return nil, nil
	}

	if len(patientIdsWithFieldName) == 0 {
		return slice.Map(patientProfileResponse, func(t *patient.PatientProfile) patient.PatientProfileSearch {
			return patient.PatientProfileSearch{
				PatientProfile: *t,
				Ad4124:         "",
			}
		}), nil
	}

	return slice.Map(patientProfileResponse, func(t *patient.PatientProfile) patient.PatientProfileSearch {
		ad4124Value := slice.FindOne(patientIdsWithFieldName, func(v schein.PatientIdWithFieldValue) bool {
			return t.Id.String() == v.PatientId.String()
		})
		return patient.PatientProfileSearch{
			PatientProfile: *t,
			Ad4124:         util.GetPointerValue(ad4124Value).FieldValue,
		}
	}), nil
}

func createPrefixRegex(keyword string) string {
	searchKeyword := strings.ToLower(strings.TrimSpace(keyword))
	keywords := strings.Fields(searchKeyword)

	escapedKeywords := make([]string, len(keywords))
	for i, v := range keywords {
		escapedKeywords[i] = util.NormalizeKeyword(regexp.QuoteMeta(v))
	}

	return strings.Join(escapedKeywords, `\w*\s+`)
}

const Field_InsuranceInfos = "insuranceinfos"
const Field_InsuranceNumber = "insurancenumber"

func (r *PatientProfileRepository) GetPatientProfileByInsuranceNumber(ctx *titan.Context, insuranceNumber string) ([]patient.PatientProfileSearch, error) {
	insuranceField := fmt.Sprintf("%s.%s.%s", patient.Field_PatientInfo, Field_InsuranceInfos, Field_InsuranceNumber)
	filter := bson.M{
		insuranceField: bson.M{
			"$in": []string{insuranceNumber},
		},
		repos.Field_IsDeleted: bson.M{
			"$ne": true,
		},
	}

	patientProfiles, err := r.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	if len(patientProfiles) == 0 {
		return nil, nil
	}

	patientProfileSearch := make([]patient.PatientProfileSearch, 0, len(patientProfiles))
	for _, patientProfile := range patientProfiles {
		patientProfileSearch = append(patientProfileSearch, patient.PatientProfileSearch{
			PatientProfile: *patientProfile,
			Ad4124:         "",
		})
	}
	return patientProfileSearch, nil
}
