package jobs

import (
	"context"

	"git.tutum.dev/medi/tutum/ares/app/sync_mail/share"
	"github.com/robfig/cron/v3"
	"github.com/submodule-org/submodule.go/v2"
)

var JobRegisterMod = submodule.Make[*Register](func(self submodule.Self, jobCtx *share.JobContext) *Register {
	r := NewRegister(jobCtx)
	self.Scope.AppendMiddleware(
		submodule.WithContextScopeEnd(func(_ context.Context) error {
			jobCtx.Logger.Warn("stop job register")
			ctx := r.c.Stop()
			<-ctx.Done()
			jobCtx.Logger.Warn("job register stopped")
			return nil
		}),
	)
	return r
}, share.JobContextMod)
var JobRegister = JobRegisterMod.Resolve()

type Register struct {
	jobSpecs   []share.JobSpec
	c          *cron.Cron
	jobContext *share.JobContext
}

func NewRegister(jobContext *share.JobContext) *Register {
	return &Register{
		jobContext: jobContext,
		c: cron.New(
			cron.WithSeconds(),
			cron.With<PERSON>hain(
				cron.SkipIfStillRunning(cron.DefaultLogger),
			),
		),
	}
}

func (r *Register) Register(jobSpecs ...share.JobSpec) {
	r.jobSpecs = append(r.jobSpecs, jobSpecs...)
}

func (r *Register) Start() error {
	logger := r.jobContext.Logger
	for _, jobSpec := range r.jobSpecs {
		shouldSkip := jobSpec.Skip || jobSpec.Spec == ""
		if shouldSkip {
			logger.Info("skip job", "name", jobSpec.Name, "spec", jobSpec.Spec)
			continue
		}
		r.jobContext.Logger.Info("register job", "name", jobSpec.Name, "spec", jobSpec.Spec)
		logWithName := r.jobContext.Logger.With("job", jobSpec.Name)
		if _, err := r.c.AddFunc(jobSpec.Spec, func() {
			r.jobContext.Logger = logWithName
			jobSpec.Job(r.jobContext)
		}); err != nil {
			return err
		}
	}
	r.c.Start()
	return nil
}

func (r *Register) Stop() context.Context {
	return r.c.Stop()
}
